
# 说明
   本目录是jtopo软件发行版的npm本地安装目录

# 准备
    将该目录(jtopo_npm)放到项目工程的根目录（和package.json同级)

# 安装
    npm install ./jtopo_npm/core
    npm install ./jtopo_npm/editor

# 卸载
    npm uninstall @jtopo/core 
    npm uninstall @jtopo/editor

# 非ES模块引用方式
```html
    <!-- core模块, , 全局变量名称：jtopo -->
    <script src="jtopo_npm/core/index.umd.cjs"></script>
    <script>
        const stage = new jtopo.Stage('divId');
        const node = new jtopo.Node(null, 0,0, 20, 20);
        //...
    </script>

    <!-- editor模块 , 全局变量名称：jtopoEditor-->
    <script src="jtopo_npm/editor/index.umd.cjs"></script>
    <script>
        const editor = new jtopoEditor.Editor(stage);
        //...
    </script>
```

# 版权&许可
    软件含授权唯一水印，勿做其他用途, 详情请阅读《jtopo软件许可.pdf》