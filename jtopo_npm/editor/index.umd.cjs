(function(H,l){typeof exports=="object"&&typeof module<"u"?l(exports,require("@jtopo/core")):typeof define=="function"&&define.amd?define(["exports","@jtopo/core"],l):(H=typeof globalThis<"u"?globalThis:H||self,l(H.jtopoEditor={},H.jtopo))})(this,function(H,l){"use strict";(function(a,e){const t=O,n=a();for(;[];)try{if(parseInt(t(385))/1*(parseInt(t(394))/2)+parseInt(t(390))/3+parseInt(t(396))/4+-parseInt(t(393))/5*(parseInt(t(392))/6)+parseInt(t(384))/7+-parseInt(t(389))/8*(parseInt(t(387))/9)+parseInt(t(383))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(rt,159963);function O(a,e){const t=rt();return O=function(n,s){return n=n-383,t[n]},O(a,e)}class Qt extends l.EventTarget{constructor(e){const t=O;super(),this.stage=e,this[t(398)],this.initDom(),this.hide()}initDom(){const e=O;let t=document.createElement("div");return t.classList[e(388)]("jtopo_iconsPanel"),this.stage.layersContainer[e(391)](t),this.domElement=t,this}hide(){const e=O;return this.domElement.style[e(395)]="none",this}show(){return this.domElement.style.display="block",this}getDragItem(){return this.dargItem}setConfig(e){let t=this;return e.items.forEach(function(n){const s=O;let x=document.createElement(s(386));x.classList.add(s(397)),x.innerHTML=n.iconHtml,x.setAttribute("draggable",!![]),x.ondragstart=function(i){t.dargItem=n},t.domElement[s(391)](x)}),this}}function rt(){const a=["277904HSgRXR","item","domElement","1007980PeoNMX","1007790FWUsyd","313ebvHZu","div","9PxqtnO","add","1437960RkIWuQ","956829EWQibg","appendChild","883254mbHhIk","10qbmSoL","6PHjiqf","display"];return rt=function(){return a},rt()}const v=J;function J(a,e){const t=ot();return J=function(n,s){return n=n-186,t[n]},J(a,e)}(function(a,e){const t=J,n=a();for(;[];)try{if(-parseInt(t(186))/1*(-parseInt(t(248))/2)+parseInt(t(232))/3+parseInt(t(191))/4+parseInt(t(209))/5*(-parseInt(t(226))/6)+-parseInt(t(199))/7+parseInt(t(223))/8*(parseInt(t(242))/9)+-parseInt(t(193))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ot,320970);class $t{constructor(e,t){this.editor=e,this.dat=t,this.gui=new t.GUI,this.object,this.folders={}}setCurrentObject(e){const t=J,n=this.editor,s=this[t(239)][t(213)];s.inputSystem.target=e,s.selectedGroup.removeAll().add(e),e.isLink?n[t(222)].attachTo(e):e[t(189)]&&n[t(203)].attachTo(e),n.update(),this.showProperty(e)}[v(238)](e){const t=v;if(e==null)return;this.basic={id:e.id,name:"",x:1,y:1,imageSrc:"",width:1,height:1,text:"",rotation:0};const n=this[t(239)][t(213)],s=n.styleSystem[t(204)],x=s[t(249)](e[t(205)]);this.style={lineDash:null,backgroundColor:"",textAlign:x.textAlign||"center",textBaseline:x[t(200)]||t(228),strokeStyle:x[t(216)]||"gray",fillStyle:"",color:x[t(207)]||"",borderWidth:0,fontSize:"12px",fontFamily:"arial",lineWidth:x.lineWidth||0,lineCap:"butt",globalAlpha:1};const i=this.basic,r=this.style;Object.keys(i)[t(244)](function(o){if(e[o]!=null){let c=e[o];i[o]=c}}),Object.keys(r)[t(244)](function(o){let c=e.style[o];e.style[o]!=null&&(r[o]=c)}),this.object==null&&(this.object=e,this.init()),this.object=e,r.borderWidth=e.style.borderWidth||0,r[t(218)]=e[t(188)].fontWeight,r.fontSize=e.style.fontSize,r.fontFamily=e.style.fontFamily,this[t(217)](i,r),e.isNode&&this.getFolder(t(246))!=null?(this.getFolder("节点属性")[t(233)](),this.getFolder(t(215))[t(202)]()):this.getFolder("连线属性")!=null&&(this.getFolder("连线属性").show(),this.getFolder(t(246)).hide())}[v(198)](e){const t=this.gui.addFolder(e);return this.folders[e]=t,t}[v(206)](e){return this[v(247)][e]}getCtrollerValue(e,t){return this.getCtroller(e,t).getValue()}getCtroller(e,t){const n=v;return this.getFolder(e)[n(234)].find(x=>x.property==t)}setFolderValues(e,t){const n=v;Object[n(245)](this.gui[n(227)])[n(244)](x=>{const i=n;this[i(230)].__folders[x].__controllers.forEach(function(c){const h=i;let d=c.property;e[d]!=null?c.setValue(e[d]):t[d]!=null&&c[h(197)](t[d])})})}init(){const e=v,t=this,n=this.editor,s=n[e(213)],x=this[e(229)],i=this.style;let r={实线:"","虚线1,1":"1,1","虚线2,2":"2,2","虚线3,3":"3,3","虚线7,3":"7,3","虚线3,7":"3,7","虚线10,1":e(196),"虚线1,10":"1,10"},o=["Arial",e(240),"sans-serif"],c={默认:"butt",圆形:"round",矩形:"square"};function h(){const g=e;let k=this[g(208)],P=this.getValue(),M=t.object;M[g(225)]!=null&&(M[g(188)].backgroundColor=null),M[k]=P,n.update()}function d(){const g=e;let k=this[g(208)],P=this.getValue(),M=t.object;k=="lineDash"?P==null||P==""?P=null:typeof P=="string"&&(P=P.split(",")):k=="backgroundColor"&&t[g(219)][g(241)]&&(P=null),M.imageSrc!=null&&(M[g(188)][g(221)]=null),M.css(k,P),n.update()}const u=this.newFolder(e(231));u.add(x,"name").onFinishChange(h).name("name"),u[e(212)](x,e(210)).onFinishChange(h).name("文字"),u.add(i,"globalAlpha",0,1,.1)[e(236)](d).name("整体透明度"),u.add(i,"strokeStyle").onFinishChange(d).name("线条颜色"),u.open();const f=this.newFolder("节点属性");f.add(x,"x").onFinishChange(h),f.add(x,"y").onFinishChange(h),f.add(x,"width",1)[e(195)](h).name("宽度"),f.add(x,e(237),1).onFinishChange(h).name("高度"),f.add(i,"borderWidth",0,10)[e(236)](d).name("边框粗细"),f[e(212)](i,"lineWidth",0,10).onChange(d).name("线条粗细"),f.add(i,e(211),r).onChange(d).name("虚实"),f.add(i,"fillStyle")[e(195)](d).name("填充颜色"),f.add(x,e(225))[e(195)](h).name(e(194)),f[e(192)]();const b=this.newFolder("连线属性");b.add(i,e(190),0,100).onChange(d).name("线条粗细"),b.add(i,"lineDash",r)[e(236)](d)[e(187)]("虚实"),b.add(i,"lineCap",c)[e(236)](d).name("末端样式"),b[e(192)]();const S=this[e(198)]("文本");S[e(212)](i,e(224),o).onChange(d)[e(187)]("字体名称"),S.add(i,"fontSize",1,1e3).onChange(d).name("大小"),S[e(212)](i,"color").onChange(d).name("颜色"),S.open();let w=this.gui[e(220)];w.remove(),s[e(214)].appendChild(w),w[e(188)][e(235)]="absolute",w[e(188)].right="-15px",w.style.top=e(201),w.style.zIndex=1e3}open(){this.gui.open()}close(){const e=v;this[e(230)][e(243)]()}hide(){const e=v;this[e(230)].domElement[e(188)].display="none"}show(){const e=v;this[e(230)][e(220)].style.display="block"}}function ot(){const a=["linkCtrlBox","49528empaaV","fontFamily","imageSrc","2742GFEdsV","__folders","middle","basic","gui","基础属性","1209786ntYVbj","show","__controllers","position","onChange","height","showProperty","editor","serif","isLink","540QbJqmI","close","forEach","keys","节点属性","folders","505330uFVbAH","getStyle","1XmbuqE","name","style","isNode","lineWidth","1708336VrPNGj","open","4875700qSSjQE","图片路径","onFinishChange","10,1","setValue","newFolder","2765266YibvFJ","textAlign","0px","hide","nodeCtrlBox","currentTheme","className","getFolder","color","property","2745ylWqJr","text","lineDash","add","stage","layersContainer","连线属性","strokeStyle","setFolderValues","fontWeight","object","domElement","backgroundColor"];return ot=function(){return a},ot()}const Q=lt;function ct(){const a=["gray","1207500uuhNwm","1108SHBIAH","5903436dAtIPL","2170CNRspw","613291UFeaiI","14695duKeNH","5579056EHPIaO","10ZzPBCJ","#c8c8c8","topo_last_doc","3504VPIqiY","29123613saIjuP","orange"];return ct=function(){return a},ct()}(function(a,e){const t=lt,n=a();for(;[];)try{if(parseInt(t(126))/1+-parseInt(t(125))/2*(-parseInt(t(132))/3)+parseInt(t(137))/4*(parseInt(t(127))/5)+-parseInt(t(136))/6+parseInt(t(138))/7+parseInt(t(128))/8+-parseInt(t(133))/9*(parseInt(t(129))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ct,798197);function lt(a,e){const t=ct();return lt=function(n,s){return n=n-125,t[n]},lt(a,e)}const Gt=11,p={data:{localLastDocName:Q(131)},anchorPoint:{size:Gt,style:{strokeStyle:Q(135),fillStyle:"rgba(255,255,255,0.6)",lineWidth:1},activeStyle:{fillStyle:"red"},unActiveStyle:{fillStyle:"rgba(255,255,255,0.6)"},drawStartMinDistance:Gt,drawStartDelay:200},anchorBox:{anchorDist:Gt/2,nodeDist:12},nodeResizePoint:{width:12,height:12,style:{border:"1px solid black",backgroundColor:Q(134)}},nodeRotatePoint:{width:14,height:14,style:{lineWidth:1,strokeStyle:"black",fillStyle:"orange"},rotateLineStyle:{strokeStyle:"gray"},rotateLineLength:30},guildLine:{styleW:{strokeStyle:Q(130),lineDash:[1,1]},styleS:{strokeStyle:"#c8c8c8",lineDash:[1,1]}},linkCtrlPoint:{size:7,style:{lineWidth:1,strokeStyle:"gray",fillStyle:"pink"},activeStyle:{strokeStyle:"rgba(0,0,0,0.1)",fillStyle:"rgba(0,0,0,0.1)"},unactiveStyle:{strokeStyle:"rgba(0,0,0,0.9)",fillStyle:"pink"},adjustStyle:{strokeStyle:"gray",fillStyle:Q(134)},ctrlLinkStyle:{lineDash:[2,2]}},dropBox:{style:{border:"3px solid orange",lineDash:[5,3]}},align:{minDistance:10,alignLineStyle:{strokeStyle:"orange",lineDash:[5,3]}},operationTip:{enable:!![]}},N=ht;(function(a,e){const t=ht,n=a();for(;[];)try{if(parseInt(t(267))/1+-parseInt(t(280))/2*(parseInt(t(268))/3)+parseInt(t(284))/4*(parseInt(t(270))/5)+-parseInt(t(275))/6*(-parseInt(t(278))/7)+-parseInt(t(274))/8*(parseInt(t(285))/9)+parseInt(t(279))/10*(-parseInt(t(281))/11)+-parseInt(t(266))/12*(-parseInt(t(286))/13)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(dt,255088);const L=navigator[N(277)][N(269)]().indexOf("MAC OS")!=-1?N(283):"Control";function dt(){const a=["16489CfbNPv","shift","Meta","940356cqkeqi","1263429QGIcRH","13yuRoSN","4638264rjMiFI","258752BfsQPb","471651hhgxII","toUpperCase","5hQTYhH","Shift","Escape","Delete","16NERvYk","75138OCuRFc","Meta+Backspace","userAgent","273BnQqlR","2410qGDsxR","6sbGIoF"];return dt=function(){return a},dt()}function ht(a,e){const t=dt();return ht=function(n,s){return n=n-266,t[n]},ht(a,e)}const ut={CtrlOrCmd:L,CreateGroup:L,DropTo_leader:N(271),Delete:[N(273),N(276)],Select_all:[L+"+a"],Select_invert:[L+"+i"],Cut:[L+"+x"],Copy:[L+"+c"],Paste:[L+"+v"],Save:[L+"+s"],Open:[L+"+o"],Undo:[L+"+z"],Redo:[L+"+shift+z"],Copy_style:["Shift+c"],paste_Style:["Shift+v"],Move_up:["ArrowUp"],Move_down:["ArrowDown"],Move_left:["ArrowLeft"],Move_right:["ArrowRight"],Layout_tree:["t"],Layout_grid:["g"],LocalView:["/"],ResizeKeepAspectRatio:N(282),DrawLine:["l"],Cancel:[N(272)],DiableNodeAlign:"Alt"};function ft(){var a=["10LbdmAJ","2TeaGIa","54848vEnxZI","10874151OZmFSK","1601929aPQAXX","120gDJKXQ","1673580BRMfMb","3221368SKoOYh","395oAaMzl","2245606ZhjQqj","35oeIQkS","1116843rzrqkC"];return ft=function(){return a},ft()}function Kt(a,e){var t=ft();return Kt=function(n,s){n=n-397;var x=t[n];return x},Kt(a,e)}(function(a,e){for(var t=Kt,n=a();[];)try{var s=-parseInt(t(405))/1*(-parseInt(t(402))/2)+-parseInt(t(400))/3+parseInt(t(403))/4*(parseInt(t(397))/5)+-parseInt(t(407))/6*(-parseInt(t(399))/7)+parseInt(t(408))/8+parseInt(t(404))/9*(-parseInt(t(401))/10)+parseInt(t(398))/11*(-parseInt(t(406))/12);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ft,860518);class m{constructor(e){this.type=e}}const R=$;(function(a,e){const t=$,n=a();for(;[];)try{if(-parseInt(t(499))/1+parseInt(t(509))/2+parseInt(t(498))/3*(-parseInt(t(507))/4)+parseInt(t(500))/5+parseInt(t(508))/6*(parseInt(t(518))/7)+parseInt(t(515))/8*(parseInt(t(503))/9)+-parseInt(t(496))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(pt,655546);function $(a,e){const t=pt();return $=function(n,s){return n=n-495,t[n]},$(a,e)}class te extends l.Node{constructor(e,t){const n=$;super(),this.nodeCtrlBox=e,this.editor=e[n(512)],this.stage=this.editor.stage,this.selectedGroup=this.stage[n(511)],this[n(521)](p.nodeResizePoint.width,p.nodeResizePoint[n(497)]),this.css(p.nodeResizePoint.style),this[n(510)]=t}[R(506)](){}mousemoveHandler(){const e=R;let t,n=this.name;n==l.Position.lt?t="nw-resize":n==l.Position.ct?t="n-resize":n==l.Position.rt?t="ne-resize":n==l.Position.lm?t="w-resize":n==l.Position.rm?t=e(495):n==l.Position.lb?t="sw-resize":n==l.Position.cb?t="s-resize":n==l.Position.rb&&(t="se-resize"),this[e(512)][e(520)].setCursor(t)}mouseoutHandler(e){}mousedownHandler(e){e[R(516)]()}mouseupHandler(e){e.preventDefault()}[R(522)](e){const t=R;let n=this.nodeCtrlBox,s=n[t(512)],x=s.stage,i=this[t(510)];if(this.selectedGroup[t(514)]().length==0)throw new Error(t(505));let c=x.inputSystem.target,h=c.stageToLocalXY(e.x,e.y),d=c.positionToLocalPoint(i),u=h.x-d.x,f=h.y-d.y;if(i==l.Position.lt?(u=-u,f=-f):i==l.Position.ct?(u=0,f=-f):i==l.Position.rt?f=-f:i==l.Position.lm?(u=-u,f=0):i==l.Position.rm?f=0:i==l.Position.lb?u=-u:i==l.Position.cb?u=0:i==l.Position.rb,s[t(519)].isKeydown(ut.ResizeKeepAspectRatio)){let b=c[t(497)]/(c.width||1);f=u*b}if(this[t(517)](c,{dx:u,dy:f},i),u!=0&&f!=0){let b=new m("resize");b[t(502)]=c,b.details={dx:u,dy:f,ctrl:i},s.dispatchEvent(b)}}adjustFixedDirection(e,t,n){const s=R;let x=l.PositionInvertMap[n],i=e.positionToLocalPoint(x);i=e[s(513)](i),e.resizeWith(t.dx,t.dy);let r=e.positionToLocalPoint(x);r=e.transformPoint(r);let o=i.x-r.x,c=i.y-r.y;e[s(504)](o,c),this[s(501)].updateSize()}}function pt(){const a=["392266oFyQzP","name","selectedGroup","editor","transformPoint","getNoChildrensObjects","200creSTd","preventDefault","adjustFixedDirection","28gjHXia","keyboard","stage","resizeTo","dragHandler","e-resize","2177750rlABzt","height","6fPYCLy","999884wTVEZq","1366130mMhcoX","nodeCtrlBox","object","404406MSYGll","translateWith","selectedGroup.length is 0!","mouseenterHandler","485472kdqLeP","784848hvjDIB"];return pt=function(){return a},pt()}function G(a,e){const t=_t();return G=function(n,s){return n=n-128,t[n]},G(a,e)}const D=G;(function(a,e){const t=G,n=a();for(;[];)try{if(-parseInt(t(134))/1*(-parseInt(t(143))/2)+parseInt(t(150))/3*(parseInt(t(153))/4)+-parseInt(t(141))/5*(-parseInt(t(152))/6)+parseInt(t(131))/7+parseInt(t(136))/8*(-parseInt(t(129))/9)+-parseInt(t(128))/10*(-parseInt(t(158))/11)+-parseInt(t(142))/12===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(_t,269957);function qt(a,e){const t=G;let n=e[t(132)],s=a[t(144)](t(151));return s=a.getStageTransform().point(s),Math.atan2(n.y-s.y,n.x-s.x)}class ee extends l.CircleNode{constructor(e,t){const n=G;super(),this.editor=e[n(147)],this[n(139)]=this.editor.stage,this.selectedGroup=this.stage.selectedGroup,this[n(133)](p.nodeRotatePoint[n(135)]),this.resizeTo(p.nodeRotatePoint.width,p[n(149)].height),this[n(145)]=t}[D(138)](){const e=D;this[e(139)].setCursor(l.Cursor[e(130)])}[D(157)](e){const t=D;e.preventDefault();let n=this[t(139)];if(this.selectedGroup.getNoChildrensObjects().length==0)throw new Error(t(140));let x=n.inputSystem.target;this.elementInitAngle=x.rotation,this.mouseInitAngle=qt(x,n)}[D(137)](e){const t=D;this.stage.setCursor("auto"),e[t(155)]()}dragHandler(e){const t=D;e[t(155)]();let n=this.parent,s=n.editor,x=s.stage;if(s[t(139)].selectedGroup[t(156)]()[t(148)]==0)throw new Error("selectedGroup.length is 0!");let o=x.inputSystem.target,c=qt(o,x)-this[t(154)];if(o.rotate(this.elementInitAngle+c),n.updateSize(),c!=0){let h=new m("rotate");h.object=o,h[t(146)]={dAngle:c},s.dispatchEvent(h)}}}function _t(){const a=["8317CpvLDI","style","185672IXVhOt","mouseupHandler","mousemoveHandler","stage","selectedGroup.length is 0!","1333235IQFNkm","12284988PwSjma","26kkrmkw","positionToLocalPoint","name","details","editor","length","nodeRotatePoint","231126hkasOb","center","12yVFKCr","16bISARL","mouseInitAngle","preventDefault","getNoChildrensObjects","mousedownHandler","4949659yXoZzd","10WQTWqa","135PUSSbS","move","1696023ppNXMd","inputSystem","css"];return _t=function(){return a},_t()}function K(a,e){const t=bt();return K=function(n,s){return n=n-213,t[n]},K(a,e)}const C=K;(function(a,e){const t=K,n=a();for(;[];)try{if(parseInt(t(234))/1*(parseInt(t(229))/2)+parseInt(t(228))/3*(-parseInt(t(241))/4)+parseInt(t(239))/5*(-parseInt(t(218))/6)+parseInt(t(237))/7+parseInt(t(227))/8+parseInt(t(223))/9+parseInt(t(236))/10*(-parseInt(t(216))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(bt,473259);function bt(){const a=["6LEatJT","12VUvzDw","name","initCtrlPoint","currNode","show","138717KZIvtw","mouseoutStageHandler","6470QgUheV","1416744njovBs","attachTo","4525YRwZky","initPoints","1300496sceRAm","positionToLocalPoint","point","createCtrlPoint","18029eEEayu","update","3972JwDUXp","editor","updateSize","forEach","endArrow","8411715RxIHYy","rotate","zIndex","clearTarget","6509768tKjFlW"];return bt=function(){return a},bt()}function ne(a){const e=K;let t=a.parent;return t instanceof l.Link&&(t.beginArrow===a||t[e(222)]===a)}class se extends l.Node{constructor(e,t=0,n=0,s=1,x=1){const i=K;super(null,t,n,s,x),this[i(225)]=l.DefaultZIndexs.NodeCtrlBox,this[i(219)]=e,this.mouseEnabled=![],this.hide()}[C(217)](){const e=C;this.visible&&this[e(220)]()}[C(235)](){}mouseenterStageHandler(){}[C(231)](e){const t=C;this.removeAllChild();let n=e.getCtrlPoints(),s=this;n[t(221)](x=>{const i=t;if(s[i(215)](x),x=="rotate"){let r=function(){return s.positionToLocalPoint(l.Position.ct)},o=s[i(215)](i(224)),c=new l.Link(null,o,r);c.css(p.nodeRotatePoint.rotateLineStyle),c.mouseEnabled=![],s.addChild(c)}})}createCtrlPoint(e){let t;return e=="rotate"?t=new ee(this,e):t=new te(this,e),this.addChild(t),t}[C(226)](){this.currNode=null,this.hide()}[C(238)](e){const t=C;if(!ne(e)){if(e.editable!=!![])throw new Error("attach not Node or not editable");this.currNode!==null&&this.initCtrlPoint(e),this.currNode=e,this[t(220)](),this[t(233)]()}}[C(220)](){const e=C;let t=this.currNode;if(t==null||t.isSelected==![]||t.parent==null){this[e(232)]=null,this.hide();return}this.viewClone(t),this.initPoints()}[C(240)](){const e=C;let t=this.getChildren();for(var n=0;n<t.length;n++){let s=t[n];if(s instanceof l.Link)continue;let x;s.name=="rotate"?(x=this.positionToLocalPoint(l.Position.ct),x.y-=p.nodeRotatePoint.rotateLineLength):x=this.positionToLocalPoint(s[e(230)]),s instanceof l.Node&&s.translateTo(x.x,x.y)}}viewClone(e){const t=C;let n=e.getStageTransform(),s=n.point(e.positionToLocalPoint(l.Position.center)),x=n.point(e.positionToLocalPoint(l.Position.rm)),i=Math.atan2(x.y-s.y,x.x-s.x);n.rotate(-i);let r=n[t(214)](e[t(213)](l.Position.center)),o=n.point(e.positionToLocalPoint(l.Position.rb)),c=(o.x-r.x)*2,h=(o.y-r.y)*2;this.resizeTo(c,h),this.rotate(i),this.translateTo(s.x,s.y)}}const W=U;(function(a,e){const t=U,n=a();for(;[];)try{if(parseInt(t(275))/1*(parseInt(t(286))/2)+-parseInt(t(307))/3+-parseInt(t(284))/4+-parseInt(t(287))/5*(-parseInt(t(301))/6)+parseInt(t(267))/7*(-parseInt(t(272))/8)+parseInt(t(271))/9+parseInt(t(266))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(gt,842617);function U(a,e){const t=gt();return U=function(n,s){return n=n-263,t[n]},U(a,e)}class xe extends l.CircleNode{constructor(e){const t=U;super(),this.isConnectPoint=!![],this.isActive=![],this[t(281)](p.linkCtrlPoint.size),this.linkCtrlBox=e,this.editor=this.linkCtrlBox.editor,this.css(p[t(296)].style)}active(){this.css(p.linkCtrlPoint.activeStyle),this.isActive=!![]}unActive(){this[U(308)](p.linkCtrlPoint.unactiveStyle),this.isActive=![]}[W(304)](){const e=W;this[e(273)].anchorBox.hide(),this.editor.stage.setCursor(l.Cursor.move);let t=this.parent,n=this.editor.stage,s=t.attachedLink;if(s instanceof l.AutoFoldLink){let x=null,i=s.points[0],r=s.points[1],o=s.points[4],c=s.points[5];this.name==l.Position[e(285)]?l.isHorizontal(s[e(302)](0,.5))?x=r.x>i.x?l.Cursor[e(288)]:l.Cursor.e_resize:x=r.y>i.y?l.Cursor.s_resize:l.Cursor[e(279)]:this.name==l.Position[e(276)]?l.isHorizontal(s.getK(4,.5))?x=o.x>c.x?l.Cursor.w_resize:l.Cursor.e_resize:x=o.y>c.y?l.Cursor.s_resize:l.Cursor.n_resize:this.name==l.Position[e(270)]&&(l.isHorizontal(s.getK(2,.5))?x=l.Cursor.s_resize:x=l.Cursor.e_resize),x&&n.setCursor(x)}}[W(283)](e){const t=W;this[t(273)][t(289)].setCursor("crosshair"),this[t(292)]=null}dragHandler(e){const t=W;let n=this.parent,s=n.parent,x=s.stage;const i=this.editor;let r=n.attachedLink;i[t(295)].hide();const o=this[t(278)];this.editor.stage.setCursor(l.Cursor.crosshair);let c=r.parent.stageToLocalXY(x.inputSystem.x,x.inputSystem.y);if(e[t(291)]&&(o===l.Position.begin||o===l.Position.end)&&this.active(),o==l.Position.begin){r instanceof l.AutoFoldLink&&r.resetOffset(),r.setBegin({x:c.x,y:c.y}),r.updateMatrix(),this.canConnectEndpoint=i.anchorBox[t(280)]([r]);return}if(o==l.Position[t(293)]){r instanceof l.AutoFoldLink&&r[t(290)](),r[t(303)]({x:c.x,y:c.y}),r[t(299)](),this[t(292)]=i.anchorBox[t(280)]([r]);return}if(r instanceof l.CurveLink)this[t(278)]==l.Position[t(294)]&&(r[t(294)]==null?r.setCtrlPoint(r[t(305)](o)):(r.ctrlPoint.x+=c.x-r[t(294)].x,r.ctrlPoint.y+=c.y-r[t(294)].y,r[t(300)](r.ctrlPoint)));else if(r instanceof l.BezierLink)o==l.Position.ctrlPoint1?r.ctrlPoint1==null?r.setCtrlPoint1(r.positionToLocalPoint(o)):(r.ctrlPoint1.x+=c.x-r[t(269)].x,r.ctrlPoint1.y+=c.y-r.ctrlPoint1.y,r.setCtrlPoint1(r[t(269)])):o==l.Position.ctrlPoint2&&(r.ctrlPoint2==null?r.setCtrlPoint2(r.positionToLocalPoint(o)):(r.ctrlPoint2.x+=c.x-r.ctrlPoint2.x,r.ctrlPoint2.y+=c.y-r[t(274)].y,r[t(268)](r[t(274)])));else if(r instanceof l.AutoFoldLink){if(o==l.Position.fold1){let h=r[t(305)](l.Position.fold1);const d=c.x-h.x,u=c.y-h.y;r.setFold1Offset(d,u),r.updateMatrix()}else if(o==l.Position.center){let h=r.positionToLocalPoint(l.Position.center);const d=c.x-h.x,u=c.y-h.y;r[t(297)](d,u),r.updateMatrix()}else if(o==l.Position.fold2){let h=r.positionToLocalPoint(l.Position.fold2);const d=c.x-h.x,u=c.y-h.y;r.setFold2Offset(d,u),r.updateMatrix()}}}mouseupHandler(e){const t=W;this.editor.stage.setCursor(l.Cursor.auto),e.event.preventDefault(),this[t(263)]&&this[t(306)]();let n=this[t(264)],s=n.attachedLink;if(this[t(292)]!=null){let x=this[t(292)],i=x.target;if(i.isLink){let r=i;r.end.target!==s&&r.begin[t(282)]!==s&&(this[t(278)]==l.Position[t(265)]?s[t(277)](r,x):this.name==l.Position.end&&s[t(303)](r,x),s.upgradeParent(),s[t(299)]())}else this.name==l.Position.begin?s.setBegin(i,x):this[t(278)]==l.Position[t(293)]&&s.setEnd(i,x),s[t(298)](),s.updateMatrix();this.canConnectEndpoint=null}}}function gt(){const a=["findConnectableEndpoint","setRadius","target","mousedownHandler","5142120AgQnuK","fold1","1234226fFVfIF","5uvhDTu","w_resize","stage","resetOffset","isDragStart","canConnectEndpoint","end","ctrlPoint","anchorBox","linkCtrlPoint","setCenterOffset","upgradeParent","updateMatrix","setCtrlPoint","3664866MBGXsL","getK","setEnd","mousemoveHandler","positionToLocalPoint","unActive","2065626qQoPpC","css","isActive","parent","begin","21737720kbBttl","17808VBOedn","setCtrlPoint2","ctrlPoint1","center","2817225KincMq","2824HKwlue","editor","ctrlPoint2","1KQqlYc","fold2","setBegin","name","n_resize"];return gt=function(){return a},gt()}function tt(a,e){const t=yt();return tt=function(n,s){return n=n-134,t[n]},tt(a,e)}const T=tt;function yt(){const a=["setBegin","center","ctrlPoint","setEnd","background","attachedLink","parent","mouseenterStageHandler","Link","132EbTuuX","8669424fJozQc","clearTarget","ctrlPointInfo","21771sTWcJU","matrixDirty","show","217135rIHKtB","7pDLwjZ","fillColor","linkCtrlPoint","1503RxglPk","ctrlPoint1","updateCtrlPoints","150obAAbq","1828488vtOKyZ","css","20430cVXKJj","attachTo","addChild","452967NUyYNi","removeAllChild","getCtrlPoints","114sIbBSB","hideAllPoint","1116170XymyRT"];return yt=function(){return a},yt()}(function(a,e){const t=tt,n=a();for(;[];)try{if(-parseInt(t(155))/1+parseInt(t(149))/2*(parseInt(t(139))/3)+-parseInt(t(150))/4+-parseInt(t(142))/5*(-parseInt(t(158))/6)+parseInt(t(143))/7*(-parseInt(t(136))/8)+parseInt(t(146))/9*(parseInt(t(152))/10)+parseInt(t(160))/11*(parseInt(t(135))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(yt,832972);class ie extends l.Node{constructor(e){const t=tt;super(),this[t(138)]={},this.zIndex=l.DefaultZIndexs.LinkCtrlBox,this.editor=e,this.ctrlPointStyle=new l.Style({}),this.init()}mouseoutStageHandler(){}[T(168)](){}createNodeResizePoint(e){let t=new xe(this);return t.name=e,this.addChild(t),t}init(){this.ctrlPointInfo={},this.hide()}draw(e){this.visible!=![]&&this.updateSize()}updateSize(){const e=T,t=this.editor.stage.inputSystem.target;this.attachedLink?this[e(166)][e(167)]==null||t==null||this.attachedLink!=t?(this.attachedLink=null,this.hide()):this[e(153)](this[e(166)]):this.hide()}[T(148)](e){const t=T;this.attachedLink=e;let n=e.getCtrlPoints();this.anchorNameStr=n.join(",");for(let s=0;s<n.length;s++){let x=n[s],i=this.ctrlPointInfo[x];if(i==null){if(i=this.createNodeResizePoint(x),x!=l.Position.begin&&x!=l.Position.end&&(i[t(144)]=this.ctrlPointStyle[t(165)],i.css(p.linkCtrlPoint.adjustStyle),i.isConnectPoint=![]),x==t(147)){let r=new l.Link;r[t(151)](p[t(145)].ctrlLinkStyle),r.setBegin(i,t(162));let o=this.ctrlPointInfo.begin;r.setEnd(o,"center"),this[t(154)](r),this[t(138)][x+t(134)]=r}else if(x=="ctrlPoint2"){let r=new l.Link;r[t(151)](p.linkCtrlPoint.ctrlLinkStyle),r[t(161)](i,"center");let o=this.ctrlPointInfo.end;r[t(164)](o,"center"),this.addChild(r),this.ctrlPointInfo[x+"Link"]=r}else if(x==t(163)){let r=new l.Link;r.css(p.linkCtrlPoint.ctrlLinkStyle),r[t(161)](i,"center");let o=this.ctrlPointInfo.begin;r.setEnd(o,"center"),this[t(154)](r),this.ctrlPointInfo[x+t(134)]=r}this.ctrlPointInfo[x]=i}}}hideAllPoint(){let t=this[T(138)];for(var n in t)t[n].hide()}updateFllow(){const e=T,t=this.attachedLink;let n=t.getStageTransform(),s=t[e(157)]();this[e(159)]();for(let x=0;x<s.length;x++){const i=s[x];let r=this.ctrlPointInfo[i];if(r==null)continue;let o=t.positionToLocalPoint(i),c=n.point(o);r.translateTo(c.x,c.y),r.show();let h=this[e(138)][i+"Link"];h&&h[e(141)]()}}[T(153)](e){const t=T;if(this[t(140)]=!![],e[t(167)]!=null){if(this.attachedLink===e){e instanceof l.AutoFoldLink&&e[t(157)]().join(",")!=this.anchorNameStr&&this[t(148)](e),this.updateFllow(),this.show();return}this[t(138)]={},this[t(156)](),this[t(148)](e),this.updateFllow(),this[t(141)]()}}[T(137)](){const e=T;this[e(166)]=null,this.ctrlPointInfo={},this.removeAllChild(),this.hide()}}function It(a,e){const t=mt();return It=function(n,s){return n=n-259,t[n]},It(a,e)}const B=It;(function(a,e){const t=It,n=a();for(;[];)try{if(parseInt(t(267))/1*(-parseInt(t(259))/2)+parseInt(t(270))/3*(parseInt(t(272))/4)+parseInt(t(261))/5+parseInt(t(263))/6+-parseInt(t(273))/7*(parseInt(t(283))/8)+parseInt(t(266))/9*(parseInt(t(286))/10)+-parseInt(t(285))/11*(parseInt(t(275))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(mt,352583);var ae=Object.defineProperty,re=Object[B(264)],Zt=(a,e,t,n)=>{for(var s=n>1?void 0:n?re(e,t):e,x=a.length-1,i;x>=0;x--)(i=a[x])&&(s=(n?i(e,t,s):i(s))||s);return n&&s&&ae(e,t,s),s};function mt(){const a=["drawStartDelay","mousedownHandler","setEnd","isActive","13472oSBvZx","findConnectableEndpoint","7629677NXfPOb","6560TobZBn","drawStartInfo","activeStyle","setCursor","isDragStart","instanceManager","anchorPoint","isIntersectPoint","end","30826lXSVZU","intersect","1623280lJhZWZ","now","2925786UhCYLX","getOwnPropertyDescriptor","画线开始","9666ihIBca","1PASGeB","stageToLocalXY","link","57315CpsZPI","editor","76hbxhAq","518uvsOKg","lineDrawn","24JJCYJW","size","update","segIndex"];return mt=function(){return a},mt()}const oe="intersectNode";class et extends l.Node{constructor(e,t=0,n=0,s=1,x=1){const i=B;super(null,t,n,s,x),this[i(282)]=![],this[i(293)]=![],this.anchorBox=e,this.editor=e.editor,this.stage=this[i(271)].stage,this.resizeTo(p.anchorPoint[i(276)],p.anchorPoint.size),this.css(p[i(292)].style),this.unactive()}setIntersect(e){const t=B;this[t(260)]=e}mouseenterHandler(){this.active()}mouseoutHandler(){this.stage.setCursor("auto"),this.unactive()}mousemoveHandler(){}[B(280)](e){const t=B;e.preventDefault(),this[t(271)].stage[t(289)]("crosshair"),this.link=null}dragHandler(e){const t=B;if(e.buttons==2)return;const n=this.editor,s=n.stage;this.anchorBox.activedPoint&&this.anchorBox.activedPoint.unactive();const x=this.anchorBox.target;if(e[t(290)]){let i;if(this.isIntersectPoint){let r=this.anchorBox.intersect;i=new l.EndpointSegment(x,r.rate,r[t(278)])}else i=new l.EndpointFixedName(x,this.name);this[t(287)]={x:e.x,y:e.y,beginEndpoint:i,timeStamp:Date[t(262)]()};return}if(this.drawStartInfo!=null){let i=l.Point.distancePoint(this[t(287)],e);if(Date.now()-this.drawStartInfo.timeStamp>p.anchorPoint[t(279)]&&i>p[t(292)].drawStartMinDistance){this.editor.showTip(t(265)),console.assert(this.link==null,this.link),this.editor.record("划线");const c=n[t(291)][t(274)](null,x,null,this[t(287)].beginEndpoint);this[t(269)]=c;let h=c.parent[t(268)](s.inputSystem.x,s.inputSystem.y);c[t(281)](h),this.drawStartInfo=null}}if(this.link!=null&&this.link.end!=null){let i=this.link.parent[t(268)](s.inputSystem.x,s.inputSystem.y);this[t(269)].setEnd(i),this.endpoint=this.anchorBox.findConnectableEndpoint([this.link,this.link[t(294)].target])}}mouseupHandler(e){const t=B;this.show(),this.editor[t(277)](),this[t(271)].stage[t(289)]("auto"),this.link!=null&&(this.endpoint&&(this.link[t(281)](this.endpoint),this[t(269)].upgradeParent(),this[t(269)].updateMatrix()),this.editor[t(291)].lineDrawingFinished(this[t(269)]),this.anchorBox[t(284)]([]),this[t(271)].recordEnd("划线")),this[t(269)]=null}active(){const e=B;this.isActive=!![],this.css(p.anchorPoint[e(288)])}unactive(){this.isActive=![],this.css(p.anchorPoint.unActiveStyle)}}Zt([l.setProto(l.Shape.Circle)],et.prototype,"shape",2),Zt([l.setProto("shape")],et.prototype,"pickType",2);const I=V;(function(a,e){const t=V,n=a();for(;[];)try{if(parseInt(t(292))/1+parseInt(t(287))/2*(parseInt(t(289))/3)+-parseInt(t(264))/4+-parseInt(t(270))/5+parseInt(t(268))/6+parseInt(t(302))/7+-parseInt(t(278))/8===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ct,291898);function V(a,e){const t=Ct();return V=function(n,s){return n=n-263,t[n]},V(a,e)}function Ct(){const a=["3172603YIFMhW","findConnectableEndpoint","index","children","1295052WmIWfe","point","createAnchorPoint","anchorPoint","2362968dgslrY","active","1691080GaWezF","getConnectableEndpointFromObjects","update","show","length","showIntersectAnchor","keyboard","target","753616ByChLs","unactive","sort","hide","stage","canConnectable","visible","setTarget","anchorBox","3214ULaDmU","forEach","123LcnKPJ","ctrlIntersectPoint","dist","135135MfltlN","_findChildren","painted","clearTarget","object","indexOf","anchorName","filter","mouseY","mouseEnabled"];return Ct=function(){return a},Ct()}function ce(a){let e=a.parent;return e instanceof l.Link&&(e.beginArrow===a||e.endArrow===a)}class le extends l.Node{constructor(e,t=0,n=0,s=1,x=1){const i=V;super(null,t,n,s,x),this.editor=e,this.ctrlIntersectPoint=this.createAnchorPoint(oe),this[i(290)].isIntersectPoint=!![],this[i(290)].hide()}cancel(){}mouseoutStageHandler(){}mouseenterStageHandler(){}[I(295)](){this.target=null,this.hide()}[I(285)](e){const t=I;if(!this.canConnectable(e))return;if(this.target!==e){if(this.target=e,e!=null&&(e instanceof l.Node||e instanceof l.Link)){let x=e.getAnchorPoints();this.children.length=0;for(var n=0;n<x.length;n++){let i=x[n];this[t(266)](i)}}this[t(272)]()}this[t(263)][t(288)](x=>x[t(279)]()),this.ctrlIntersectPoint[t(281)](),this[t(273)]()}canConnectable(e){const t=I;return e==null||ce(e)?![]:e[t(284)]&&e[t(301)]&&e.connectable&&e[t(294)]}setTargetWithName(e,t){if(t==null)throw new Error("activeNameOrPoint is null");this.setTarget(e),this.activePointByName(t),this.show()}clearActived(){const e=I;this.activedPoint&&this.activedPoint.unactive(),this[e(290)][e(281)]()}activePointByName(e){const t=I;this.activedPoint&&this.activedPoint.unactive();let n=this.children,s=n[t(299)](x=>x.name==e)[0];this.activedPoint=s,s[t(269)](),this[t(290)].hide()}[I(275)](e){const t=I;if(this.intersect=e,e==null){this.ctrlIntersectPoint[t(281)]();return}let n=e.rate,s=e.segIndex;const x=this.target;let i=x.getLocalPoint(n,s),o=x.getStageTransform()[t(265)](i);this.ctrlIntersectPoint.translateTo(o.x,o.y),this[t(290)].css(p[t(267)].activeStyle),this.ctrlIntersectPoint.show()}update(){const e=I;if(this.target==null||this[e(277)].parent==null){this.clearTarget();return}const t=this.target,n=t.getStageTransform(),s=this[e(263)];for(let x=0;x<s[e(274)];x++){const i=s[x],r=i.name;if(!i.isIntersectPoint){let o=t.positionToLocalPoint(r),c=n[e(265)](o);i.translateTo(c.x,c.y)}}}createAnchorPoint(e){const t=new et(this);return t.name=e,t.anchorBox=this,this.addChild(t),t}getObjectsIntersect(e){const t=I;let n=this.editor[t(282)],s=this.editor.getCurrentLayer(),x={x:n.inputSystem.x,y:n.inputSystem.y};return l.getNearestPointOnObjectsOBB(s,x,e,p[t(286)].nodeDist)}[I(271)](e){const t=I,n=this.editor,s=this,x=n.stage,i=x.inputSystem,r={x:i.x,y:i.y},o=l.getNearestAnchorOnObjects(r,e,p.anchorPoint.size/2),c=n[t(276)].isControlDown();if(o.length==0&&!c){let d=de(x,100);return this.canConnectable(d)&&s.setTarget(d),null}if(o.length>0){o[t(280)]((S,w)=>{let g=S.object,k=w.object;if(c){let Ee=g instanceof l.Link?0:1,He=k instanceof l.Link?0:1;return Ee-He}let P=g instanceof l.Node?0:1,M=k instanceof l.Node?0:1;return P-M});const d=o[0],u=d[t(296)];let f=d[t(298)];const b=new l.EndpointFixedName(u,f);return s.setTargetWithName(u,f),b}const h=this.getObjectsIntersect(e);if(h!=null){const d=h.object;s.setTarget(d);const u=new l.EndpointSegment(d,h.rate,h.segIndex);return this.showIntersectAnchor(h),u}return s[t(295)](),null}[I(303)](e){const t=I,n=this.editor,s=this,x=n.stage.localView.getObject();function i(c){const h=V;return e[h(297)](c)==-1&&s[h(283)](c)}const r=x[t(293)](null,i,!![]);return this[t(271)](r)}}function de(a,e){const t=I,n=a.getCurrentLayer(),s={x:n.mouseX,y:n[t(300)]};let x=n.displayList[t(299)](o=>o.isNode),i=x.map((o,c)=>{let h=o._obb.aabb.getCenter();return{index:c,dist:l.Point.distancePoint(s,h)}});if(i=i.filter(o=>o.dist<=e),i[t(274)]==0)return null;i.sort((o,c)=>o.dist-c[t(291)]);let r=i[0][t(304)];return x[r]}function nt(a,e){const t=Pt();return nt=function(n,s){return n=n-396,t[n]},nt(a,e)}const Y=nt;(function(a,e){const t=nt,n=a();for(;[];)try{if(-parseInt(t(400))/1+parseInt(t(415))/2+-parseInt(t(410))/3*(-parseInt(t(407))/4)+parseInt(t(402))/5*(parseInt(t(401))/6)+parseInt(t(408))/7*(parseInt(t(398))/8)+parseInt(t(404))/9*(parseInt(t(414))/10)+parseInt(t(399))/11*(-parseInt(t(396))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Pt,560032);function Pt(){const a=["146536YhzPSz","5751878GydjOy","939117ZoiTYQ","3570PRrCJV","895PBmxZL","redoAll","9KCpAIZ","push","redoHistory","1084axmzxO","427kuJLof","redo","4803ZcXEbu","剪切粘贴","undoHistory","添加图元","8332910lBEqIr","1153678GJBXdA","editor","36rzoFdF","undo"];return Pt=function(){return a},Pt()}const he={cut:"cut",copy:"copy",delete:"删除",modify:"修改",addChild:Y(413),pasteCopy:"辅助粘贴",pasteCut:Y(411),resize:"尺寸修改",rotate:"旋转",dragNodeOrLink:"位置改变",modifyLink:"连线调整"};class ue{constructor(e,t,n){this.type=e,this.undoFn=n,this.redoFn=t}redo(){this.redoFn()}undo(){this.undoFn()}}class fe extends EventTarget{constructor(e){const t=Y;super(),this.editor=e,this[t(412)]=[],this[t(406)]=[]}clear(){this.undoHistory=[],this.redoHistory=[]}push(e,t,n){const s=Y;let x=new ue(e,t,n);return x.editor=this[s(416)],this.redoHistory.length=0,this.undoHistory.push(x),x}undo(){const e=Y;if(this.undoHistory.length==0)return null;let t=this.undoHistory.pop();return t[e(397)](),this[e(406)][e(405)](t),t}redo(){if(this.redoHistory.length==0)return null;let e=this.redoHistory.pop();return e.redo(),this.undoHistory.push(e),e}undoAll(e=500){let t=this;function n(){let s=t.undo();t.editor.update(),s!=null&&setTimeout(n,e)}n()}[Y(403)](e=500){let t=this;function n(){const s=nt;t.editor.update(),t[s(409)]()!=null&&setTimeout(n,e)}n()}}(function(a,e){const t=vt,n=a();for(;[];)try{if(parseInt(t(446))/1+parseInt(t(455))/2+parseInt(t(453))/3*(-parseInt(t(454))/4)+-parseInt(t(448))/5*(-parseInt(t(452))/6)+-parseInt(t(451))/7+-parseInt(t(449))/8+parseInt(t(450))/9*(parseInt(t(447))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(wt,429046);function wt(){const a=["1341045IbfHaL","6225296fNhawq","728397xppdgo","1198463aUhVsw","12JMNyOu","2094303PYQfbr","4ybYxZU","1695304CkDQdp","getItem","44984EZNbRn","80YBPfBJ"];return wt=function(){return a},wt()}function vt(a,e){const t=wt();return vt=function(n,s){return n=n-446,t[n]},vt(a,e)}const pe={getItem:function(a){const e=vt;return localStorage[e(456)](a)},setItem:function(a,e){localStorage.setItem(a,e)},saveWithVersion(a,e){a+=Date.now(),this.setItem(a,e)},getAllVersions(a){return Object.keys(localStorage).filter(t=>t.startsWith(a)).sort()},getLastVersion(a,e){let t=this.getAllVersions(a).reverse();if(t.length==0)return;e==null&&(e=0),e+1>=t.length&&(e=t.length-1);let n=t[e];return this.getItem(n)}};(function(a,e){const t=Lt,n=a();for(;[];)try{if(-parseInt(t(157))/1+parseInt(t(148))/2+-parseInt(t(152))/3+parseInt(t(150))/4+-parseInt(t(151))/5+parseInt(t(147))/6+-parseInt(t(143))/7*(-parseInt(t(156))/8)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(St,264629);function Lt(a,e){const t=St();return Lt=function(n,s){return n=n-143,t[n]},Lt(a,e)}function _e(a,e,t){const n=Lt,s=a[n(146)],x=a.KeysConfig.CreateGroup;let i={};function r(h){const d=n;i[h]=a.stage[d(153)][d(145)].slice(),a.showOpTooltip("编组-"+h)}function o(h){const d=n;let u=i[h];if(u)return u=u[d(155)](f=>f.parent!==null),u.length>0&&(a.showOpTooltip("选编组-"+h),a.stage.selectedGroup.removeAll().addAll(u),a.stage[d(158)][d(144)]=u[0]),u}function c(h){const d=n;o(h)&&t[d(149)](e.selectedGroup.objects)}for(let h=0;h<=9;h++)s.bindKey(x+"+"+h,function(d){d[n(159)](),r(h)}),s[n(154)](""+h,function(d){d.preventDefault(),o(h)}),s.bindKey(""+h+"+"+h,function(d){d[n(159)](),c(h)})}function St(){const a=["1678744LbrJNp","218873eEFOeC","inputSystem","preventDefault","7HwUllJ","target","objects","keyboard","1603266lKjxSp","955810dfkEzs","centerBy","1276868PucmHT","2613915rKUmuy","803673vHMcKx","selectedGroup","bindKey","filter"];return St=function(){return a},St()}(function(a,e){const t=Tt,n=a();for(;[];)try{if(parseInt(t(187))/1+-parseInt(t(198))/2+parseInt(t(183))/3+-parseInt(t(202))/4+-parseInt(t(180))/5+parseInt(t(201))/6+parseInt(t(173))/7*(parseInt(t(196))/8)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(kt,501009);function kt(){const a=["Cut","77qnFOQI","getNoChildrensObjects","clipboardManager","redoUndoSys","imageToBase64","stage","showOpTooltip","1903100lSJzzH","Undo","Move_left","1537812hSlZeJ","Move_right","forEach","toogleLocalView","254905Ivuosx","copyHandler","filter","keyup","Cancel","redo","doGridLayout","selectedGroup","displayList","599688ETiDmm","Select_invert","1053928zcMAMK","saveHandler","Save","4125168vUFjNi","3484060BjIuTx"];return kt=function(){return a},kt()}function Tt(a,e){const t=kt();return Tt=function(n,s){return n=n-172,t[n]},Tt(a,e)}class be{constructor(e){this.editor=e,this.init()}init(){const e=Tt,t=this.editor,n=t.stage,s=t.currentLayer;let x=t[e(178)].keyboard;const i=t.KeysConfig;function r(o,c,h){o.forEach(d=>{x.bindKey(d,function(u){c(u)},h)})}x.on("keydown",function(){t.update()}),x.on(e(190),function(o){t.update()}),r(i.Delete,o=>t.editorEventManager.deleteHandler(o)),r(i[e(172)],o=>t.clipboardManager.cutHandler(o)),r(i.Copy,o=>{const c=e;t[c(179)]("复制"),t[c(175)][c(188)](o)}),r(i.Paste,o=>t.clipboardManager.pasteHandler(o)),r(i[e(181)],o=>{t.showOpTooltip("撤销"),t.redoUndoSys.undo(),t.clearCtrlBoxs()}),r(i.Redo,o=>{const c=e;t.showOpTooltip("重做"),t[c(176)][c(192)](o),t.clearCtrlBoxs()}),r(i.Select_all,o=>{t.showOpTooltip("全选");let c=n.localView.getObject(),h=l.DisplayObject.flatten(c.children,d=>d.visible==!![]);n.select(h)}),r(i[e(197)],o=>{const c=e;t[c(179)]("反选");let h=n.selectedGroup.getNoChildrensObjects();n.select(s[c(195)].filter(d=>h.notContains(d)))}),r(i[e(200)],o=>{const c=e;t[c(179)]("保存"),t[c(199)](o,t[c(177)])},![]),r(i.Open,o=>{t.showOpTooltip("打开"),t.openLasted(o)},![]),r(i.LocalView,o=>{t[e(186)]()}),r(i.Copy_style,o=>{t.showOpTooltip("复制样式"),t.clipboardManager.styleCopyHandler(o)},![]),r(i.paste_Style,o=>{t.showOpTooltip("粘贴样式"),t.clipboardManager.stylePasteHandler(o)},![]),r(i[e(182)],o=>{const c=e;n[c(194)].getNoChildrensObjects()[c(189)](d=>d.isNode).forEach(d=>{d.x-=1})}),r(i[e(184)],o=>{const c=e;n[c(194)].getNoChildrensObjects()[c(189)](d=>d.isNode)[c(185)](d=>{d.x+=1})}),r(i.Move_up,o=>{const c=e;n.selectedGroup[c(174)]().filter(d=>d.isNode).forEach(d=>{d.y-=1})}),r(i.Move_down,o=>{n[e(194)].getNoChildrensObjects().filter(d=>d.isNode).forEach(d=>{d.y+=1})}),r(i.Layout_grid,o=>{const c=e;t.layoutManager[c(193)]()}),_e(t,n,s),r(i[e(191)],o=>{t.onEsc(o)})}}function st(a,e){const t=Et();return st=function(n,s){return n=n-241,t[n]},st(a,e)}const ge=st;function Et(){const a=["setZIndex","左对齐","左右等距","2AGgzgX","5873030uNTbdZ","5207034XKNCvU","垂直中心对齐","1943493riZPuk","inputSystem","18jvYQJe","cutHandler","876828oOOahz","clipboardManager","646301yepFCy","234UJDhSs","44YfNCVE","2547736dTnKQp","zIndex","4NweiiD","align",`
<div class="header">编辑</div>
<a>剪切</a>
<a>复制</a>
<a>粘贴</a>
<a>删除</a> 
<hr></hr>
<div class="header">前后</div>
<a>上移一层</a>
<a>下移一层</a>
<a>移至顶部</a>
<a>移至底部</a>
<div class="header">对齐</div>
<a>左对齐</a>
<a>右对齐</a>
<a>顶部对齐</a>
<a>底部对齐</a>
<a>水平中心对齐</a>
<a>垂直中心对齐</a>
`,"上移一层","60mMwIQf","顶部对齐","layoutManager"];return Et=function(){return a},Et()}(function(a,e){const t=st,n=a();for(;[];)try{if(-parseInt(t(247))/1*(parseInt(t(262))/2)+parseInt(t(241))/3*(-parseInt(t(252))/4)+parseInt(t(256))/5*(parseInt(t(248))/6)+parseInt(t(264))/7+-parseInt(t(250))/8*(-parseInt(t(243))/9)+parseInt(t(263))/10+parseInt(t(249))/11*(-parseInt(t(245))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Et,382159);let ye=ge(254);function Ie(a){const e=a.stage;let t=new l.PopupMenu(e,ye);return t.addEventListener("select",function(n){const s=st,x=n.item;let i=e[s(242)].target;x=="剪切"?a.clipboardManager[s(244)]():x=="复制"?a[s(246)].copyHandler():x=="粘贴"?a.clipboardManager.pasteHandler():x=="删除"&&a.editorEventManager.deleteHandler(),i!=null&&(x==s(255)?a.layoutManager.setZIndex(i,x):x=="下移一层"?a.layoutManager[s(259)](i,x):x=="移至顶部"?a[s(258)].setZIndex(i,x):x=="移至底部"?a.layoutManager[s(259)](i,x):x=="左对齐"?a.layoutManager.align(s(260)):x=="右对齐"?a[s(258)].align("右对齐"):x=="顶部对齐"?a.layoutManager[s(253)](s(257)):x=="底部对齐"?a.layoutManager.align("底部对齐"):x=="水平中心对齐"?a.layoutManager.align("水平中心对齐"):x==s(265)?a.layoutManager[s(253)]("垂直中心对齐"):x==s(261)?a[s(258)].evenSpacing(s(261)):x=="上下等距"&&a.layoutManager.evenSpacing("上下等距"),i[s(251)]<0?i.zIndex=0:i.zIndex>1e3&&(i[s(251)]=1e3),i.parent!=null&&i.parent.updateZIndex(),a.update())}),t}(function(a,e){const t=j,n=a();for(;[];)try{if(-parseInt(t(261))/1+-parseInt(t(256))/2+parseInt(t(249))/3+parseInt(t(254))/4+-parseInt(t(251))/5*(parseInt(t(263))/6)+-parseInt(t(260))/7+parseInt(t(259))/8===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ht,379046);function Ht(){const a=["905288OGRVxQ","_disabled","39778PPSqGw","setEnd","hide","7925592LmNPNP","3714676HtnuSR","710463gLEYXq","guildlineW","12YKagjt","1622139kosIcR","guildlineS","294170paoBtK","show","hideGuidLine"];return Ht=function(){return a},Ht()}function j(a,e){const t=Ht();return j=function(n,s){return n=n-249,t[n]},j(a,e)}class me{constructor(e){const t=j;this[t(255)]=!![],this.editor=e,this.init()}init(){const e=j,t=this.editor,n=new l.Link;n.mouseEnabled=![],n.css(p.guildLine.styleW),n.hide(),this[e(262)]=n,t.handlerLayer.addChild(this.guildlineW);const s=new l.Link;n.mouseEnabled=![],s.css(p.guildLine.styleS),s[e(258)](),this[e(250)]=s,t.handlerLayer.addChild(this[e(250)])}disable(){const e=j;this._disabled=!![],this[e(253)]()}enable(){this._disabled=![]}showGuildLine(e){const t=j;if(this._disabled)return;const n=this.editor,s=n.stage,x=this[t(262)],i=this.guildlineS;x.setBegin({x:0,y:e.y}),x.setEnd({x:s.width,y:e.y}),x[t(252)](),i.setBegin({x:e.x,y:0}),i[t(257)]({x:e.x,y:s.height}),i[t(252)]()}hideGuidLine(){const e=j;this.guildlineW[e(258)](),this.guildlineS.hide()}}(function(a,e){for(var t=q,n=a();[];)try{var s=-parseInt(t(212))/1+parseInt(t(218))/2+parseInt(t(217))/3+-parseInt(t(216))/4+-parseInt(t(219))/5+parseInt(t(214))/6+parseInt(t(209))/7*(-parseInt(t(215))/8);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Mt,146405);class Wt{constructor(){this.take=0}copyPut(e){var t=q;this[t(210)]=0,this.type="copy",this.source=e}cutPut(e){var t=q;this.take=-1,this[t(213)]="cut",this.source=e}takeSource(){var e=q;return this.take++,this[e(211)]}isFirstCutPaste(){return this.type==he.cut&&this.take==0}}function q(a,e){var t=Mt();return q=function(n,s){n=n-209;var x=t[n];return x},q(a,e)}function Mt(){var a=["630054XcKYTO","460318SiBKom","27085zCOEKZ","1675450qvKMLp","take","source","178023sYYllq","type","1036008ZXUiTd","8rEJSkI","174600omsZvv"];return Mt=function(){return a},Mt()}const Xt=E;function Nt(){const a=["linkCtrlBox","getNoChildrensObjects","36856810osIajn","408ehmljF","dispatchEvent","objects","cssClipBoard","copyAndToJSON","css","clearTarget","copySetStyle","stage","13609575qOIRIY","clipboard","12HMgHkr","753879cumCqG","editor","object","2075878JRNxUS","1YlxERb","record","className","913534ARAsxQ","copyPut","serializerSystem","6872464KuQqRo","map","selectedGroup","recordEnd","粘贴样式","isEmpty","76600YnNZPs"];return Nt=function(){return a},Nt()}(function(a,e){const t=E,n=a();for(;[];)try{if(parseInt(t(417))/1*(-parseInt(t(420))/2)+-parseInt(t(413))/3*(-parseInt(t(412))/4)+-parseInt(t(397))/5*(parseInt(t(401))/6)+parseInt(t(416))/7+-parseInt(t(391))/8+-parseInt(t(410))/9+parseInt(t(400))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Nt,866354);function E(a,e){const t=Nt();return E=function(n,s){return n=n-390,t[n]},E(a,e)}class Ce{constructor(e){const t=E;this.cssClipBoard=new Wt,this.clipboard=new Wt,this[t(404)]=new Wt,this.editor=e}copyHandler(){const e=E;let t=this[e(414)][e(409)],n=[].concat(t.selectedGroup[e(399)]());if(n.length==0)return;let s=n.map(i=>i.parent);this[e(411)][e(421)]([n,s]);let x=new m("copy");x[e(415)]=n,this.editor.dispatchEvent(x)}cutHandler(){const e=E;let t=this.editor,n=t.stage,s=[].concat(n[e(393)].getNoChildrensObjects());if(s.length==0)return;let x=s[e(392)](r=>r.parent);this.clipboard.cutPut([s,x]),t[e(418)]("剪切"),s.forEach((r,o)=>{x[o].removeChild(r),l.LinkHelper.disconnect(r,s)}),t[e(394)]("剪切"),n.inputSystem.target=null,t.anchorBox[e(407)](),t.nodeCtrlBox[e(407)](),t[e(398)][e(407)]();let i=new m("cut");i[e(415)]=s,this[e(414)].dispatchEvent(i)}pasteHandler(){const e=E;let t=this.editor,n=this[e(411)].takeSource();if(n==null)return;let s=t.currentLayer,x=t.stage,i=n[0],r=n[1],o=i.map(g=>g._obb.aabb),c=l.Rectangle.unionRects(o),h=c.getCenter(),d=s.stageToLocalXY(x.inputSystem.x,x.inputSystem.y),u=d.x-h.x,f=d.y-h.y;t.record("粘贴");let b=i,S=x.serializerSystem[e(405)](i);b=x[e(390)].jsonToObjects(S),b.forEach((g,k)=>{g.translateWith(u,f)}),b.forEach((g,k)=>{r[k].addChild(g)}),t.recordEnd("粘贴");let w=new m("paste");w.object=b,this.editor[e(402)](w)}styleCopyHandler(){const e=E;let t=this.editor,n=t.stage,s=n.inputSystem.target;s==null&&(!n.selectedGroup[e(396)]()&&(s=n.selectedGroup[e(403)][0]),s==null)||this[e(404)].copyPut(s)}stylePasteHandler(){const e=E;let t=this.editor,n=this[e(404)].takeSource();if(n==null)return;let s=t[e(409)].selectedGroup.objects,x=this;t[e(418)]("粘贴样式"),s.forEach(i=>{x.copySetStyle(i,n)}),t.recordEnd(e(395))}[Xt(408)](e,t){const n=Xt;e!==t&&e[n(419)]===t[n(419)]&&e[n(406)](t.style)}}(function(a,e){const t=z,n=a();for(;[];)try{if(-parseInt(t(136))/1*(parseInt(t(149))/2)+-parseInt(t(140))/3*(-parseInt(t(152))/4)+-parseInt(t(135))/5+parseInt(t(142))/6+-parseInt(t(145))/7*(parseInt(t(139))/8)+parseInt(t(147))/9*(-parseInt(t(153))/10)+-parseInt(t(134))/11*(-parseInt(t(148))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Bt,500916);function Bt(){const a=["NodeCtrlBox","positionToLocalPoint","112fSNMvz","3iQwGqh","show","4663260MLtkAb","zIndex","resizeTo","127743UYMPnm","rotate","18963uHLHEQ","145032YVkbHt","8690cpQEfJ","updateSize","currObject","1289636khToql","3670YVXjbA","946KoviYy","2199445IvIoRq","39XjTLkz"];return Bt=function(){return a},Bt()}function z(a,e){const t=Bt();return z=function(n,s){return n=n-134,t[n]},z(a,e)}class Pe extends l.Node{constructor(e,t=0,n=0,s=1,x=1){const i=z;super(null,t,n,s,x),this[i(143)]=l.DefaultZIndexs[i(137)],this.editor=e,this.css(p.dropBox.style),this.mouseEnabled=![],this.currObject,this.hide()}update(){const e=z;this.visible&&this[e(150)]()}attachTo(e){const t=z;if(e==null||e.editable!=!![]){this.currObject=null,this.hide();return}if(e.isLink)throw new Error("attach not Node");this[t(151)]=e,this.updateSize(),this[t(141)]()}updateSize(){this.currObject!=null&&this.viewClone(this.currObject)}viewClone(e){const t=z;let n=e.getStageTransform(),s=n.point(e.positionToLocalPoint(l.Position.center)),x=n.point(e.positionToLocalPoint(l.Position.rm)),i=Math.atan2(x.y-s.y,x.x-s.x);n[t(146)](-i);let r=n.point(e[t(138)](l.Position.center)),o=n.point(e.positionToLocalPoint(l.Position.rb)),c=(o.x-r.x)*2,h=(o.y-r.y)*2,d=4;this[t(144)](c+d*2,h+d*2),this.rotate(i),this.translateTo(s.x,s.y)}}function xt(a,e){const t=jt();return xt=function(n,s){return n=n-342,t[n]},xt(a,e)}function jt(){const a=["6NbyUcv","24xiLVtj","212664ravIrN","11WEvWUE","944240CMVPkZ","mouseEnabled","currentLayer","397649OkgqnA","3100030PaLXwy","279320ATICUI","assert","length","41qqjSwl","isAncestors","9rLGjBH","6126ofkKBC","1637964RdYroo"];return jt=function(){return a},jt()}(function(a,e){const t=xt,n=a();for(;[];)try{if(parseInt(t(349))/1*(parseInt(t(352))/2)+-parseInt(t(354))/3*(-parseInt(t(356))/4)+parseInt(t(346))/5*(parseInt(t(355))/6)+parseInt(t(344))/7+-parseInt(t(358))/8+-parseInt(t(351))/9*(parseInt(t(345))/10)+parseInt(t(357))/11*(parseInt(t(353))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(jt,220642);function Jt(a,e){const t=xt;let n=a[t(343)],s=a.currentLayer.getAllVisiable().filter(r=>{const o=t;return r.isNode&&r.isSelected!=!![]&&r[o(342)]&&r._cameraVisible}),x=e.filter(r=>r.isOutOfParent()||r.parent===n),i;for(let r=0;r<x[t(348)];r++){let o=x[r];if(i=we(o,s),i!=null)break}return i==null&&(i=n,x=x.filter(r=>r.parent!==n)),console[t(347)](i.isLink!=!![],!![],i),{parent:i,objects:x}}function we(a,e){const t=a.getAABB(),n=e.filter(s=>{const x=xt;return s===a.parent||s===a?![]:a[x(350)](s)?![]:!![]});for(let s=n.length-1;s>=0;s--){const x=n[s];if(x.getAABB().isIntersectRect(t))return x}return null}const y=it;(function(a,e){const t=it,n=a();for(;[];)try{if(parseInt(t(496))/1+parseInt(t(487))/2+parseInt(t(522))/3*(-parseInt(t(506))/4)+-parseInt(t(511))/5+parseInt(t(493))/6*(-parseInt(t(476))/7)+parseInt(t(525))/8*(parseInt(t(507))/9)+-parseInt(t(517))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(At,555915);function At(){const a=["object","isKeydown","event","removeAll","3dKdSas","changeParent","KeysConfig","7272OXAPFg","mouseenterStageHandler","update","selectedGroup","selectedGroupDragHandler","stage","nodeCtrlBox","guidlineManager","hide","forEach","findConnectableEndpoint","keyboard","isMouseOn","newMode","alignManager","控制点","attachTo","mousedownHandler","groupdrag","editor","84mshKsq","onlineDrawingFinishedHandler","setTarget","mousedown","record","button","addChild","drop","Shift","mouseOverTarget","controlTarget","1907992DLhOrh","toStageRect","mouseoutStageHandler","round","showAt","recordInterrupt","143772EdPpOO","init","inputSystem","1089789jIUyjq","mouseoutHandler","handlerLayer","clearTarget","dispatchEvent","target","isNode","selectedGroupDragEndHandler","linkCtrlBox","getCurrentLayer","2159556UpKidC","7335OYyjbl","zoom","pickUpChild","dropToBox","1241815lgcvaR","isDragEnd","canConnectable","anchorBox","mouseup","getNoChildrensObjects","11529090FMpMUe"];return At=function(){return a},At()}const ve=l.ResourceLoader.w!=null?l.ResourceLoader.w.charAt(3):"1";function it(a,e){const t=At();return it=function(n,s){return n=n-465,t[n]},it(a,e)}class Le{constructor(e){const t=it;this[t(475)]=e,this[t(494)]()}[y(494)](){const e=y;let t=this.editor,n=t.stage,s=n[e(495)],x=t.handlerLayer;n.localView.mode==null&&(n.camera.on(e(508),function(){t.update()}),n.on(l.EventNames.modeChange,function(i){const r=e;let o=i[r(469)],c=t[r(531)],h=t.linkCtrlBox,d=t.dropToBox,u=t.anchorBox;o==l.StageMode.edit?(x.addChild(c),x[r(482)](h),x.addChild(u),x.addChild(d)):(x.removeChild(c),x.removeChild(h),x.removeChild(u),x.removeChild(d)),t[r(527)]()}),s.on("dragover",function(i){i.preventDefault(),t.dispatchEvent(s)}),s.on(e(483),function(){s.event.defaultPrevented||t.dispatchEvent(s)}),s.on(e(479),function(i){s.event.defaultPrevented||t.popupMenu.hide()}),s.on(e(515),function(i){const r=e;s.button==2?!s[r(512)]&&t.popupMenu[r(491)](s.x,s.y):t.popupMenu.hide()}),s.on("mousemove",function(){}),n.selectedGroup.on(l.EventNames[e(474)],function(i){const r=e;t.selectedGroupDragHandler(i,n.selectedGroup[r(516)]())}),n.selectedGroup.on(l.EventNames.groupdragend,function(i){t[e(503)](i,n.selectedGroup.getNoChildrensObjects())}))}deleteHandler(){const e=y;let t=this.editor,n=t.stage,s=n.selectedGroup;if(ve!="1")return null;let x=s.getNoChildrensObjects();s[e(521)](),t.instanceManager.delete(x);let i=new m("delete");i.object=x,t[e(500)](i)}mousedownHandler(e){const t=y,n=this[t(475)],s=n[t(530)].inputSystem;let x=n[t(498)],i=n.nodeCtrlBox,r=n.linkCtrlBox;if(n.alignManager[t(473)](s),n.controlTarget=x[t(509)](),n.anchorBox.hide(),n[t(486)]!=null){let o=n.controlTarget.parent;o!==i?i.hide():o!==r&&r.hide(),n.controlTarget[t(473)](s),s[t(520)].preventDefault();return}r.hide(),i.hide()}mousewheelHandler(e){}dblclickHandler(e){}mouseupHandler(e){const t=y,n=this.editor,s=n.stage[t(495)];n.guidlineManager.hideGuidLine(),n[t(470)].mouseupHandler(s);let x=n.stage,i=n.nodeCtrlBox,r=n[t(504)],o=n.controlTarget;if(o!=null){s.isDragEnd&&!(o instanceof et)&&n.recordEnd("控制点"),o.mouseupHandler(s),s.event.preventDefault(),n.update();return}if(s[t(481)]==2)return;let c=x.inputSystem[t(501)];c!=null?(c.editable&&(c instanceof l.Link?r[t(472)](c):i[t(472)](c)),n[t(514)][t(513)](c)&&n.anchorBox[t(478)](c)):(n.anchorBox.clearTarget(),i[t(499)](),r.clearTarget());{if(n[t(510)].currObject!=null){let d=n[t(528)].getNoChildrensObjects(),u=Jt(n,d),f=u.parent;u.objects.forEach(S=>{S[t(523)](f),S.upgradeLinks()})}n.dropToBox.attachTo(null)}}mousedragHandler(e){const t=y,n=this.editor,s=n.stage.inputSystem;if(s.buttons==2)return;n[t(467)].isKeydown(t(484))&&n.stage.setCursor(l.Cursor.crosshair),n.guidlineManager.showGuildLine(s);let x=n[t(486)];if(x!=null){if(s.isDragStart&&!(x instanceof et)&&n.record(t(471)),x.dragHandler(s),s[t(520)]instanceof MouseEvent&&s[t(520)].defaultPrevented==!![])return;s[t(520)].preventDefault();return}n.alignManager.mousedragHandler(s),n[t(510)][t(533)]();const i=n[t(524)].DropTo_leader;if(n.stage.inputSystem.target&&n[t(467)].isKeydown(i)){let o=n.selectedGroup.getNoChildrensObjects(),c=Jt(n,o),h=c.parent;h!=null&&h.editable&&n[t(510)].attachTo(h)}}mousemoveHandler(e){const t=y,n=this.editor;let s=n[t(530)];const x=s.handlerLayer;if(s.localView.getObject(),s.setCursor("auto"),n.keyboard[t(519)]("Control")&&n.anchorBox[t(466)]([]))return;let i=x[t(509)]();if(i!==n[t(485)]&&(n.mouseOverTarget!=null&&n[t(485)].mouseoutHandler(e),i!=null&&i.mouseenterHandler(e)),n.mouseOverTarget=i,i!=null){i.mousemoveHandler(e);return}let r=s[t(505)]().pickUpChild();r!=null&&(n.stage.setCursor("move"),r.isNode&&n.anchorBox[t(513)](r)&&n.anchorBox[t(478)](r))}mouseenterHandler(e){const t=y,n=this.editor;n.linkCtrlBox[t(526)](),n[t(531)].mouseenterStageHandler(),n.anchorBox[t(526)]()}[y(497)](e){const t=y,n=this.editor;n.linkCtrlBox[t(489)](),n.nodeCtrlBox.mouseoutStageHandler(),n.anchorBox[t(489)]()}onLineDrawnHandler(e){const t=y,n=this.editor;if(n.onLinkCreate)n.onLinkCreate(e);else{let s=new m("lineDrawn");s.object=e,n[t(500)](s),s=new m("drawLineStart"),s.object=e,n.dispatchEvent(s)}}[y(477)](e){const t=y,n=this.editor;let s=new m("lineDrawingFinished");s.object=e,n.dispatchEvent(s),s=new m("drawLineEnd"),s[t(518)]=e,n[t(500)](s)}[y(529)](e,t){const n=y,s=this.editor;if(s.stage.inputSystem.isDragStart&&s[n(480)]("对象拖拽"),t.length==1){const i=t[0],r=s.getCurrentLayer()[n(488)](i._obb.aabb),o=r.getCenter();s[n(532)].showGuildLine(o)}}selectedGroupDragEndHandler(e,t){const n=y,s=this[n(475)];if(!s.stage.inputSystem[n(468)]){s[n(492)]();return}t[n(465)](i=>{const r=n;i[r(502)]&&(i.x=Math[r(490)](i.x),i.y=Math.round(i.y))}),s.recordEnd("对象拖拽")}}(function(a,e){const t=F,n=a();for(;[];)try{if(parseInt(t(147))/1+parseInt(t(151))/2+-parseInt(t(149))/3*(parseInt(t(152))/4)+parseInt(t(163))/5*(parseInt(t(144))/6)+parseInt(t(156))/7+-parseInt(t(150))/8*(parseInt(t(166))/9)+-parseInt(t(167))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ot,797776);function F(a,e){const t=Ot();return F=function(n,s){return n=n-144,t[n]},F(a,e)}class Se{constructor(e){this.editor=e}setZIndex(e,t){const n=F;t=="上移一层"?e.zIndex++:t=="下移一层"?e[n(158)]--:t=="移至顶部"?e.zIndex=1e3:t=="移至底部"&&(e.zIndex=0),e.parent[n(161)]()}evenSpacing(e){const t=F;let s=this.editor.stage.selectedGroup[t(154)]();s=s.filter(x=>x.isNode||x instanceof l.Link&&x[t(159)]()),s[t(165)]!=0}align(e){const t=F,n=this.editor;let s=n.stage.selectedGroup.getNoChildrensObjects();if(s=s.filter(r=>r.isNode||r instanceof l.Link&&r.isAlone()),s.length==0)return;let x=s.map(r=>r._obb.aabb),i=l.Rectangle.unionRects(x);for(let r=0;r<s[t(165)];r++){let o=s[r],c=o._obb.aabb;e=="左对齐"?o.translateWith(i.x-c.x,0):e=="右对齐"?o.translateWith(i.getRight()-c.getRight(),0):e=="顶部对齐"?o.translateWith(0,i.y-c.y):e==t(155)?o[t(148)](0,i.getBottom()-c[t(162)]()):e=="水平中心对齐"?o.translateWith(0,i.getCenter().y-c[t(146)]().y):e==t(160)&&o[t(148)](i.getCenter().x-c.getCenter().x,0)}n.update()}doGridLayout(){const e=F;let t=this.editor,n=t.stage,s=n.selectedGroup.getNoChildrensObjects().filter(d=>d instanceof l.Node);if(s[e(165)]<2)return;let x=Math.ceil(Math[e(157)](s.length)),i=l.NodeHelper.getUnionRect(s),r=i[e(146)](),o=l.Shape[e(145)](x,x),c=n[e(153)].shapeLayout(s,o),h=s[0][e(164)]*x;c.resizeTo(h,h),c.translate(r.x,r.y),c.doLayout({effect:"easeInQuart",duration:300}),t.showOpTooltip("网格布局")}}function Ot(){const a=["sqrt","zIndex","isAlone","垂直中心对齐","updatezIndex","getBottom","5zEaSXc","width","length","477SyoNui","20407470kujgIv","7773816SRTMyL","outerGrid","getCenter","330667NmiWUZ","translateWith","1123782AfqUhU","1584GdULue","2865320vMjMnQ","16mSWWyj","layoutSystem","getNoChildrensObjects","底部对齐","9019010ZbXRsf"];return Ot=function(){return a},Ot()}const Ut=Z;(function(a,e){const t=Z,n=a();for(;[];)try{if(parseInt(t(272))/1+parseInt(t(261))/2*(parseInt(t(269))/3)+parseInt(t(256))/4+parseInt(t(250))/5+-parseInt(t(252))/6+-parseInt(t(253))/7*(-parseInt(t(271))/8)+-parseInt(t(254))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Dt,389198);class ke{constructor(e){this.editor=e}delete(e){const t=Z;let n=this[t(265)];n.record("删除"),e[t(259)](function(s){l.LinkHelper.disconnect(s),s.removeFromParent()}),n.showOpTooltip("删除"),n.recordEnd("删除")}addNewInstance(e){const t=Z,n=this.editor;this.editor.stage[t(251)][t(264)]()[t(257)](e);let x=new m("create");x.object=e,n[t(255)](x)}[Ut(274)](e){const t=Ut;let x=this.editor[t(258)].localView.getMouseXY(),i=x.x,r=x.y,o=l.getClass(e);if(e.indexOf("Node")!=-1){let c=new o(null,i,r,64,64);return this[t(260)](c),c}if(e.indexOf("Link")!=-1){let c=new o(null,{x:i-40,y:r},{x:i+40,y:r+(e=="Link"?0:80)});return this[t(260)](c),c}throw new Error(t(268)+e)}lineDrawn(e,t,n,s){const x=Ut;let i=this.editor,r=l.getClass(i.LinkClassName);const o=new r(e,t,n,s),c=Object.assign({},i.newLinkProperties);c.css&&(o[x(266)](c.css),delete c[x(266)]),Object.keys(c)[x(259)](u=>{const f=x;let b=c[u];typeof b==f(273)?o[u]=b():o[u]=b});let d=t.isNode||t.isLink?t[x(262)]:i[x(270)];return o.zIndex=l.DefaultZIndexs[x(263)],d.addChild(o),i.editorEventManager[x(267)](o),o}lineDrawingFinished(e){this.editor.editorEventManager.onlineDrawingFinishedHandler(e)}}function Z(a,e){const t=Dt();return Z=function(n,s){return n=n-250,t[n]},Z(a,e)}function Dt(){const a=["forEach","addNewInstance","217882uMKbHA","parent","EditorNewLink","getObject","editor","css","onLineDrawnHandler","unknow classname:","15MLPBaK","currentLayer","8kUDUYg","229966GHIpgr","function","create","3579215JQAoMY","localView","2988942IhFHjk","2787575nIinyk","11834910kydKIO","dispatchEvent","1254424GUMAVv","addChild","stage"];return Dt=function(){return a},Dt()}const A=X;function X(a,e){const t=zt();return X=function(n,s){return n=n-357,t[n]},X(a,e)}(function(a,e){const t=X,n=a();for(;[];)try{if(parseInt(t(357))/1*(-parseInt(t(390))/2)+parseInt(t(365))/3+parseInt(t(388))/4+parseInt(t(382))/5*(parseInt(t(361))/6)+parseInt(t(372))/7+parseInt(t(386))/8+parseInt(t(368))/9*(-parseInt(t(376))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(zt,328109);function zt(){const a=["xType","label","top","2112936ofZEsy","minDistance","2098204qMFmaw","displayList","386bKukSF","left","setEnd","dxLine","show","stageToLocalXY","isNode","css","center","1221aaEtIS","init","_disabled","findAlignRectInfo","6UWItJu","right","yType","parent","1812051wvWwHj","mousedownHandler","alignLineStyle","234KTqIvt","middle","stage","hide","2405389weAryl","filter","abs","bottom","461410zRWfIb","alignInfo","beginArrow","dyLine","mouseEnabled","aabb","135580pFamWL"];return zt=function(){return a},zt()}class Te{constructor(e){const t=X;this[t(387)]=p.align[t(387)],this._disabled=![],this.visible=![],this.editor=e,this.init(e)}[A(358)](e){const t=A;let n=new l.Link(null,{x:0,y:0},{x:100,y:100});n.css(p.align[t(367)]),n.mouseEnabled=![],this.dxLine=n;let s=new l.Link(null,{x:0,y:0},{x:100,y:100});s[t(397)](p.align.alignLineStyle),s[t(380)]=![],this.dyLine=s,n.hide(),s[t(371)](),e.handlerLayer.addChild(n),e.handlerLayer.addChild(s)}disable(){this._disabled=!![],this.dxLine.hide(),this.dyLine.hide()}enable(){this._disabled=![]}getAlignInfo(e,t){const n=A,s=this.editor,x=s[n(370)];let i=e.target;if(x.selectedGroup.objects.length>1)return null;let r=this.editor.getCurrentLayer(),o=u=>u.isNode&&u!==i&&u!==u[n(364)][n(378)]&&u!==u.parent.endArrow&&u!==u.parent[n(384)];const c=x.getCurrentLayer()[n(389)][n(373)](o),h=c.map(u=>r.toStageRect(u._obb[n(381)]));return this[n(360)](r.toStageRect(i._obb.aabb),h,t)}[A(366)](e){}mouseupHandler(e){const t=A;if(this[t(359)])return;let n=!this.dxLine.visible&&!this.dyLine.visible;if(this[t(393)][t(371)](),this[t(379)][t(371)](),n)return;let s=e.target,x=this[t(377)];if(s&&s[t(396)]&&x!=null){if(this.editor.getCurrentLayer(),x.xType!=null){let i=x.xRect,r=x.xType,o=i[x.xType];o=s.parent.stageToLocalXY(o,0).x,r==t(391)?s[t(391)]=o:r=="center"?s.x=o:r=="right"&&(s.right=o)}if(x[t(363)]!=null){let i=x.yRect,r=x.yType,o=i[x.yType];o=s.parent[t(395)](0,o).y,r=="top"?s.top=o:r=="middle"?s.y=o:r==t(375)&&(s[t(375)]=o)}this.editor.update(),this[t(377)]=null}}mousedragHandler(e){const t=A;if(this._disabled)return;let n=e.target;if(n==null||!n.isNode||(this.dxLine.hide(),this.dyLine.hide(),this.editor.keyboard.isKeydown(ut.DiableNodeAlign)))return;let s=this.dxLine,x=this.dyLine;const i=this.getAlignInfo(e,this.minDistance);if(this[t(377)]=i,i==null)return;let r=i.rect;if(i.xType!=null){let o=i.xRect,c=o[i[t(383)]],h=(r.middle+o.middle)/2,d=Math[t(374)](r[t(369)]-o.middle);s.setBegin({x:c,y:h-d/2}),s[t(392)]({x:c,y:h+d/2}),s[t(394)]()}if(i.yType!=null){let o=i.yRect,c=(r[t(398)]+o.center)/2,h=o[i.yType],d=Math.abs(r.center-o[t(398)]);x.setBegin({x:c-d/2,y:h}),x[t(392)]({x:c+d/2,y:h}),x.show()}}[A(360)](e,t,n){const s=A;let x=Number.MAX_VALUE,i=Number.MAX_VALUE,r,o,c,h;t.sort((u,f)=>{const b=X;return l.Point.distance(e.center,e[b(369)],f[b(398)],f[b(369)])-l.Point.distance(e.center,e.middle,u.center,u.middle)});let d;for(let u=0;u<t.length;u++){let f=t[u];f.isIntersectRect(e)||(d=Math[s(374)](e.top-f[s(385)]),d<=i&&d<=n&&(o=s(385),i=d,h=f),d=Math.abs(e.bottom-f[s(375)]),d<=i&&d<=n&&(o="bottom",i=d,h=f),d=Math.abs(e[s(369)]-f[s(369)]),d<=i&&d<=n&&(o="middle",i=d,h=f),d=Math[s(374)](e.left-f.left),d<=x&&d<n&&(r="left",x=d,c=f),d=Math.abs(e[s(362)]-f.right),d<=x&&d<n&&(r=s(362),x=d,c=f),d=Math.abs(e.center-f.center),d<=x&&d<n&&(r=s(398),x=d,c=f))}return{x,y:i,rect:e,xRect:c,yRect:h,xType:r,yType:o}}}const _=at;(function(a,e){const t=at,n=a();for(;[];)try{if(-parseInt(t(372))/1*(parseInt(t(387))/2)+parseInt(t(376))/3+-parseInt(t(379))/4+-parseInt(t(394))/5*(parseInt(t(371))/6)+parseInt(t(378))/7+parseInt(t(374))/8+-parseInt(t(386))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ft,178806);function at(a,e){const t=Ft();return at=function(n,s){return n=n-364,t[n]},at(a,e)}function Ft(){const a=["record","mousedragHandler","mouseoutHandler","dblclickHandler","toogleLocalView","enable","setItem","mouseenterHandler","mouseupHandler","openJson","mousewheelHandler","getCurrentLayer","anchorBox","sendKey","isDraging","update","object","opTooltip","lastLayerState","78036dTRDzw","1GthJoO","restoreToJson","2494504uloPOt","inputSystem","592656DwUakh","redoUndoSys","1773366ZAJZgR","923604nCKgvn","getItem","newLink","getState","fadeOut","dispatchEvent","toFileJson","591021gRcCTZ","80426NptiaJ","keyboard","recordEnd","msg","editorEventManager","targetOnly","stage","95XekKxf","recordName","layoutManager","selectedGroupDragEndHandler","toJson","editor","newLinkProperties","mousedownHandler","create","push","data"];return Ft=function(){return a},Ft()}class Vt extends l.EventTarget{constructor(e){const t=at;super(),this.KeysConfig=ut,this.EditorConfig=p,this.LinkClassName="AutoFoldLink",this[t(400)]={},this.DataCenter=pe,this.imageToBase64=![],this[t(393)]=e,e[t(399)]=this,this.currentLayer=this.getCurrentLayer(),this.handlerLayer=e.handlerLayer,this.selectedGroup=this.stage.selectedGroup,this.keyboard=e[t(388)],this.clipboardManager=new Ce(this),this.instanceManager=new ke(this),this[t(391)]=new Le(this),this.keyManager=new be(this),this.nodeCtrlBox=new se(this),this.linkCtrlBox=new ie(this),this.anchorBox=new le(this),this.dropToBox=new Pe(this),this.popupMenu=Ie(this),this.stage[t(375)].target=null,this.controlTarget=null,this.mouseOverTarget=null,this.inputTextfield=new l.InputTextfield(this),this[t(377)]=new fe(this),this[t(396)]=new Se(this),this.guidlineManager=new me(this),this.alignManager=new Te(this),this[t(369)]=new l.Tooltip(e),Object.assign(this.opTooltip.domElement.style,{paddingLeft:"20px",paddingRight:"20px",color:"black"})}setLinkClassName(e){this.LinkClassName=e}[_(416)](){return this.stage.getCurrentLayer()}defineKeys(e){Object.assign(this.KeysConfig,e)}showOpTooltip(e){const t=_;p.operationTip[t(410)]&&(this[t(369)].setHtml(e),this.opTooltip.showAt(this.stage.width*.5,this[t(393)].height*.5),this.opTooltip[t(383)](80))}saveHandler(e,t=![]){const n=_;let s=this.getCurrentLayer(),x=s.toFileJson({imageToBase64:t});this.DataCenter[n(411)](p[n(404)].localLastDocName,x);const i=new m("save");i[n(368)]=x,i.imageToBase64=t,this[n(384)](i)}openLasted(){const e=_,t=this[e(416)](),n=this.DataCenter[e(380)](p.data.localLastDocName);return n!=null?t[e(414)](n):null}[_(408)](e){const t=_;this.editorEventManager[t(408)](e),this.update()}[_(415)](e){const t=_;this[t(391)].mousewheelHandler(e),this[t(367)]()}[_(401)](e){this.editorEventManager.mousedownHandler(e),this.update()}[_(413)](e){const t=_;this[t(391)][t(413)](e),this.update()}mousedragHandler(e){const t=_;this.editorEventManager[t(406)](e),this[t(367)]()}mousemoveHandler(e){const t=_;if(this.stage.inputSystem[t(366)])return this.mousedragHandler(e);this.editorEventManager.mousemoveHandler(e),this[t(367)]()}[_(412)](e){const t=_;this.editorEventManager.mouseenterHandler(e),this[t(367)]()}[_(407)](e){this.editorEventManager.mouseoutHandler(e),this.update()}selectedGroupDragHandler(e,t){this.editorEventManager.selectedGroupDragHandler(e,t)}selectedGroupDragEndHandler(e,t){const n=_;this.editorEventManager[n(397)](e,t)}recordInterrupt(){const e=_;this.recordName=null,this[e(370)]=null}[_(405)](e){const t=_;let n=this.currentLayer,s=n.stage.serializerSystem;this[t(395)]!=null&&console.warn("record和recordEnd没有成对出现",this.recordName+":"+e),this.recordName=e,this.lastLayerState=s.getState(this.currentLayer)}[_(389)](e){const t=_;e!=this[t(395)],this.recordName=null;let n=this.currentLayer,s=n.stage.serializerSystem,x=this.lastLayerState,i=s[t(382)](n);this.redoUndoSys[t(403)](e,function(){s[t(373)](n,i)},function(){s.restoreToJson(n,x)})}[_(381)](e,t,n,s){return this.instanceManager.lineDrawn(e,t,n,s)}[_(367)](){this.dropToBox.update(),this.nodeCtrlBox.update(),this.anchorBox.update(),this.stage.update()}[_(398)](e=![]){const t=_;return this[t(416)]()[t(385)](e)}[_(414)](e){const t=_;this.getCurrentLayer().openJson(e);let n=new m("open");n.object=e,this[t(384)](n)}showTip(e,t=""){const n=_;let s=new Event("log");s[n(390)]=e+t,this[n(384)](s)}[_(402)](e){return this.instanceManager.create(e)}clearCtrlBoxs(){this.anchorBox.clearTarget(),this.nodeCtrlBox.clearTarget(),this.linkCtrlBox.clearTarget()}[_(409)](){const e=_;let t=this[e(393)].inputSystem.target;this.stage.camera[e(392)](t),this.clearCtrlBoxs(),this.update()}[_(365)](e,t){this.keyboard.sendKey(e,t)}onEsc(){this[_(364)].cancel()}}Vt.KeysConfig=ut,l.jtopo.Editor=Vt,function(a,e){for(var t=Yt,n=a();[];)try{var s=parseInt(t(328))/1*(-parseInt(t(322))/2)+parseInt(t(329))/3*(-parseInt(t(323))/4)+-parseInt(t(324))/5+-parseInt(t(327))/6+parseInt(t(326))/7+parseInt(t(325))/8+-parseInt(t(321))/9;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}}(Rt,708224);function Rt(){var a=["296DpXGDM","1226560wwoMWp","7735112aHyezz","9364019nRdaKO","1610940CzLnte","63389nUEpBW","8697TxjxPN","4959981robFXo","10JrUKQf"];return Rt=function(){return a},Rt()}function Yt(a,e){var t=Rt();return Yt=function(n,s){n=n-321;var x=t[n];return x},Yt(a,e)}H.Editor=Vt,H.IconsPanel=Qt,H.PropertiesPanel=$t,Object.defineProperty(H,Symbol.toStringTag,{value:"Module"})});
