import { EventTarget as ot, Node as H, Position as f, PositionInvertMap as bt, CircleNode as ct, Cursor as C, DefaultZIndexs as Ue, Link as y, AutoFoldLink as ee, isHorizontal as Ye, CurveLink as gt, BezierLink as yt, Style as It, EndpointSegment as lt, EndpointFixedName as dt, Point as le, setProto as et, Shape as ht, getNearestPointOnObjectsOBB as mt, getNearestAnchorOnObjects as Ct, DisplayObject as wt, PopupMenu as vt, LinkHelper as ut, Rectangle as ft, ResourceLoader as tt, EventNames as qe, StageMode as St, NodeHelper as Pt, getClass as nt, InputTextfield as Lt, Tooltip as kt, jtopo as Tt } from "@jtopo/core";
(function(r, t) {
  const e = F, n = r();
  for (; []; )
    try {
      if (parseInt(e(385)) / 1 * (parseInt(e(394)) / 2) + parseInt(e(390)) / 3 + parseInt(e(396)) / 4 + -parseInt(e(393)) / 5 * (parseInt(e(392)) / 6) + parseInt(e(384)) / 7 + -parseInt(e(389)) / 8 * (parseInt(e(387)) / 9) + parseInt(e(383)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(de, 159963);
function F(r, t) {
  const e = de();
  return F = function(n, x) {
    return n = n - 383, e[n];
  }, F(r, t);
}
class o0 extends ot {
  constructor(t) {
    const e = F;
    super(), this.stage = t, this[e(398)], this.initDom(), this.hide();
  }
  initDom() {
    const t = F;
    let e = document.createElement("div");
    return e.classList[t(388)]("jtopo_iconsPanel"), this.stage.layersContainer[t(391)](e), this.domElement = e, this;
  }
  hide() {
    const t = F;
    return this.domElement.style[t(395)] = "none", this;
  }
  show() {
    return this.domElement.style.display = "block", this;
  }
  getDragItem() {
    return this.dargItem;
  }
  setConfig(t) {
    let e = this;
    return t.items.forEach(function(n) {
      const x = F;
      let s = document.createElement(x(386));
      s.classList.add(x(397)), s.innerHTML = n.iconHtml, s.setAttribute("draggable", !![]), s.ondragstart = function(i) {
        e.dargItem = n;
      }, e.domElement[x(391)](s);
    }), this;
  }
}
function de() {
  const r = ["277904HSgRXR", "item", "domElement", "1007980PeoNMX", "1007790FWUsyd", "313ebvHZu", "div", "9PxqtnO", "add", "1437960RkIWuQ", "956829EWQibg", "appendChild", "883254mbHhIk", "10qbmSoL", "6PHjiqf", "display"];
  return de = function() {
    return r;
  }, de();
}
const T = te;
function te(r, t) {
  const e = he();
  return te = function(n, x) {
    return n = n - 186, e[n];
  }, te(r, t);
}
(function(r, t) {
  const e = te, n = r();
  for (; []; )
    try {
      if (-parseInt(e(186)) / 1 * (-parseInt(e(248)) / 2) + parseInt(e(232)) / 3 + parseInt(e(191)) / 4 + parseInt(e(209)) / 5 * (-parseInt(e(226)) / 6) + -parseInt(e(199)) / 7 + parseInt(e(223)) / 8 * (parseInt(e(242)) / 9) + -parseInt(e(193)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(he, 320970);
class c0 {
  constructor(t, e) {
    this.editor = t, this.dat = e, this.gui = new e.GUI(), this.object, this.folders = {};
  }
  setCurrentObject(t) {
    const e = te, n = this.editor, x = this[e(239)][e(213)];
    x.inputSystem.target = t, x.selectedGroup.removeAll().add(t), t.isLink ? n[e(222)].attachTo(t) : t[e(189)] && n[e(203)].attachTo(t), n.update(), this.showProperty(t);
  }
  [T(238)](t) {
    const e = T;
    if (t == null)
      return;
    this.basic = { id: t.id, name: "", x: 1, y: 1, imageSrc: "", width: 1, height: 1, text: "", rotation: 0 };
    const n = this[e(239)][e(213)], x = n.styleSystem[e(204)], s = x[e(249)](t[e(205)]);
    this.style = { lineDash: null, backgroundColor: "", textAlign: s.textAlign || "center", textBaseline: s[e(200)] || e(228), strokeStyle: s[e(216)] || "gray", fillStyle: "", color: s[e(207)] || "", borderWidth: 0, fontSize: "12px", fontFamily: "arial", lineWidth: s.lineWidth || 0, lineCap: "butt", globalAlpha: 1 };
    const i = this.basic, a = this.style;
    Object.keys(i)[e(244)](function(o) {
      if (t[o] != null) {
        let c = t[o];
        i[o] = c;
      }
    }), Object.keys(a)[e(244)](function(o) {
      let c = t.style[o];
      t.style[o] != null && (a[o] = c);
    }), this.object == null && (this.object = t, this.init()), this.object = t, a.borderWidth = t.style.borderWidth || 0, a[e(218)] = t[e(188)].fontWeight, a.fontSize = t.style.fontSize, a.fontFamily = t.style.fontFamily, this[e(217)](i, a), t.isNode && this.getFolder(e(246)) != null ? (this.getFolder("节点属性")[e(233)](), this.getFolder(e(215))[e(202)]()) : this.getFolder("连线属性") != null && (this.getFolder("连线属性").show(), this.getFolder(e(246)).hide());
  }
  [T(198)](t) {
    const e = this.gui.addFolder(t);
    return this.folders[t] = e, e;
  }
  [T(206)](t) {
    return this[T(247)][t];
  }
  getCtrollerValue(t, e) {
    return this.getCtroller(t, e).getValue();
  }
  getCtroller(t, e) {
    const n = T;
    return this.getFolder(t)[n(234)].find((s) => s.property == e);
  }
  setFolderValues(t, e) {
    const n = T;
    Object[n(245)](this.gui[n(227)])[n(244)]((s) => {
      const i = n;
      this[i(230)].__folders[s].__controllers.forEach(function(c) {
        const d = i;
        let l = c.property;
        t[l] != null ? c.setValue(t[l]) : e[l] != null && c[d(197)](e[l]);
      });
    });
  }
  init() {
    const t = T, e = this, n = this.editor, x = n[t(213)], s = this[t(229)], i = this.style;
    let a = { 实线: "", "虚线1,1": "1,1", "虚线2,2": "2,2", "虚线3,3": "3,3", "虚线7,3": "7,3", "虚线3,7": "3,7", "虚线10,1": t(196), "虚线1,10": "1,10" }, o = ["Arial", t(240), "sans-serif"], c = { 默认: "butt", 圆形: "round", 矩形: "square" };
    function d() {
      const g = t;
      let k = this[g(208)], w = this.getValue(), O = e.object;
      O[g(225)] != null && (O[g(188)].backgroundColor = null), O[k] = w, n.update();
    }
    function l() {
      const g = t;
      let k = this[g(208)], w = this.getValue(), O = e.object;
      k == "lineDash" ? w == null || w == "" ? w = null : typeof w == "string" && (w = w.split(",")) : k == "backgroundColor" && e[g(219)][g(241)] && (w = null), O.imageSrc != null && (O[g(188)][g(221)] = null), O.css(k, w), n.update();
    }
    const h = this.newFolder(t(231));
    h.add(s, "name").onFinishChange(d).name("name"), h[t(212)](s, t(210)).onFinishChange(d).name("文字"), h.add(i, "globalAlpha", 0, 1, 0.1)[t(236)](l).name("整体透明度"), h.add(i, "strokeStyle").onFinishChange(l).name("线条颜色"), h.open();
    const u = this.newFolder("节点属性");
    u.add(s, "x").onFinishChange(d), u.add(s, "y").onFinishChange(d), u.add(s, "width", 1)[t(195)](d).name("宽度"), u.add(s, t(237), 1).onFinishChange(d).name("高度"), u.add(i, "borderWidth", 0, 10)[t(236)](l).name("边框粗细"), u[t(212)](i, "lineWidth", 0, 10).onChange(l).name("线条粗细"), u.add(i, t(211), a).onChange(l).name("虚实"), u.add(i, "fillStyle")[t(195)](l).name("填充颜色"), u.add(s, t(225))[t(195)](d).name(t(194)), u[t(192)]();
    const b = this.newFolder("连线属性");
    b.add(i, t(190), 0, 100).onChange(l).name("线条粗细"), b.add(i, "lineDash", a)[t(236)](l)[t(187)]("虚实"), b.add(i, "lineCap", c)[t(236)](l).name("末端样式"), b[t(192)]();
    const L = this[t(198)]("文本");
    L[t(212)](i, t(224), o).onChange(l)[t(187)]("字体名称"), L.add(i, "fontSize", 1, 1e3).onChange(l).name("大小"), L[t(212)](i, "color").onChange(l).name("颜色"), L.open();
    let P = this.gui[t(220)];
    P.remove(), x[t(214)].appendChild(P), P[t(188)][t(235)] = "absolute", P[t(188)].right = "-15px", P.style.top = t(201), P.style.zIndex = 1e3;
  }
  open() {
    this.gui.open();
  }
  close() {
    const t = T;
    this[t(230)][t(243)]();
  }
  hide() {
    const t = T;
    this[t(230)].domElement[t(188)].display = "none";
  }
  show() {
    const t = T;
    this[t(230)][t(220)].style.display = "block";
  }
}
function he() {
  const r = ["linkCtrlBox", "49528empaaV", "fontFamily", "imageSrc", "2742GFEdsV", "__folders", "middle", "basic", "gui", "基础属性", "1209786ntYVbj", "show", "__controllers", "position", "onChange", "height", "showProperty", "editor", "serif", "isLink", "540QbJqmI", "close", "forEach", "keys", "节点属性", "folders", "505330uFVbAH", "getStyle", "1XmbuqE", "name", "style", "isNode", "lineWidth", "1708336VrPNGj", "open", "4875700qSSjQE", "图片路径", "onFinishChange", "10,1", "setValue", "newFolder", "2765266YibvFJ", "textAlign", "0px", "hide", "nodeCtrlBox", "currentTheme", "className", "getFolder", "color", "property", "2745ylWqJr", "text", "lineDash", "add", "stage", "layersContainer", "连线属性", "strokeStyle", "setFolderValues", "fontWeight", "object", "domElement", "backgroundColor"];
  return he = function() {
    return r;
  }, he();
}
const $ = fe;
function ue() {
  const r = ["gray", "1207500uuhNwm", "1108SHBIAH", "5903436dAtIPL", "2170CNRspw", "613291UFeaiI", "14695duKeNH", "5579056EHPIaO", "10ZzPBCJ", "#c8c8c8", "topo_last_doc", "3504VPIqiY", "29123613saIjuP", "orange"];
  return ue = function() {
    return r;
  }, ue();
}
(function(r, t) {
  const e = fe, n = r();
  for (; []; )
    try {
      if (parseInt(e(126)) / 1 + -parseInt(e(125)) / 2 * (-parseInt(e(132)) / 3) + parseInt(e(137)) / 4 * (parseInt(e(127)) / 5) + -parseInt(e(136)) / 6 + parseInt(e(138)) / 7 + parseInt(e(128)) / 8 + -parseInt(e(133)) / 9 * (parseInt(e(129)) / 10) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(ue, 798197);
function fe(r, t) {
  const e = ue();
  return fe = function(n, x) {
    return n = n - 125, e[n];
  }, fe(r, t);
}
const Xe = 11, p = { data: { localLastDocName: $(131) }, anchorPoint: { size: Xe, style: { strokeStyle: $(135), fillStyle: "rgba(255,255,255,0.6)", lineWidth: 1 }, activeStyle: { fillStyle: "red" }, unActiveStyle: { fillStyle: "rgba(255,255,255,0.6)" }, drawStartMinDistance: Xe, drawStartDelay: 200 }, anchorBox: { anchorDist: Xe / 2, nodeDist: 12 }, nodeResizePoint: { width: 12, height: 12, style: { border: "1px solid black", backgroundColor: $(134) } }, nodeRotatePoint: { width: 14, height: 14, style: { lineWidth: 1, strokeStyle: "black", fillStyle: "orange" }, rotateLineStyle: { strokeStyle: "gray" }, rotateLineLength: 30 }, guildLine: { styleW: { strokeStyle: $(130), lineDash: [1, 1] }, styleS: { strokeStyle: "#c8c8c8", lineDash: [1, 1] } }, linkCtrlPoint: { size: 7, style: { lineWidth: 1, strokeStyle: "gray", fillStyle: "pink" }, activeStyle: { strokeStyle: "rgba(0,0,0,0.1)", fillStyle: "rgba(0,0,0,0.1)" }, unactiveStyle: { strokeStyle: "rgba(0,0,0,0.9)", fillStyle: "pink" }, adjustStyle: { strokeStyle: "gray", fillStyle: $(134) }, ctrlLinkStyle: { lineDash: [2, 2] } }, dropBox: { style: { border: "3px solid orange", lineDash: [5, 3] } }, align: { minDistance: 10, alignLineStyle: { strokeStyle: "orange", lineDash: [5, 3] } }, operationTip: { enable: !![] } }, A = _e;
(function(r, t) {
  const e = _e, n = r();
  for (; []; )
    try {
      if (parseInt(e(267)) / 1 + -parseInt(e(280)) / 2 * (parseInt(e(268)) / 3) + parseInt(e(284)) / 4 * (parseInt(e(270)) / 5) + -parseInt(e(275)) / 6 * (-parseInt(e(278)) / 7) + -parseInt(e(274)) / 8 * (parseInt(e(285)) / 9) + parseInt(e(279)) / 10 * (-parseInt(e(281)) / 11) + -parseInt(e(266)) / 12 * (-parseInt(e(286)) / 13) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(pe, 255088);
const Et = navigator[A(277)][A(269)]().indexOf("MAC OS") != -1, E = Et ? A(283) : "Control";
function pe() {
  const r = ["16489CfbNPv", "shift", "Meta", "940356cqkeqi", "1263429QGIcRH", "13yuRoSN", "4638264rjMiFI", "258752BfsQPb", "471651hhgxII", "toUpperCase", "5hQTYhH", "Shift", "Escape", "Delete", "16NERvYk", "75138OCuRFc", "Meta+Backspace", "userAgent", "273BnQqlR", "2410qGDsxR", "6sbGIoF"];
  return pe = function() {
    return r;
  }, pe();
}
function _e(r, t) {
  const e = pe();
  return _e = function(n, x) {
    return n = n - 266, e[n];
  }, _e(r, t);
}
const Ve = { CtrlOrCmd: E, CreateGroup: E, DropTo_leader: A(271), Delete: [A(273), A(276)], Select_all: [E + "+a"], Select_invert: [E + "+i"], Cut: [E + "+x"], Copy: [E + "+c"], Paste: [E + "+v"], Save: [E + "+s"], Open: [E + "+o"], Undo: [E + "+z"], Redo: [E + "+shift+z"], Copy_style: ["Shift+c"], paste_Style: ["Shift+v"], Move_up: ["ArrowUp"], Move_down: ["ArrowDown"], Move_left: ["ArrowLeft"], Move_right: ["ArrowRight"], Layout_tree: ["t"], Layout_grid: ["g"], LocalView: ["/"], ResizeKeepAspectRatio: A(282), DrawLine: ["l"], Cancel: [A(272)], DiableNodeAlign: "Alt" };
function be() {
  var r = ["10LbdmAJ", "2TeaGIa", "54848vEnxZI", "10874151OZmFSK", "1601929aPQAXX", "120gDJKXQ", "1673580BRMfMb", "3221368SKoOYh", "395oAaMzl", "2245606ZhjQqj", "35oeIQkS", "1116843rzrqkC"];
  return be = function() {
    return r;
  }, be();
}
function Qe(r, t) {
  var e = be();
  return Qe = function(n, x) {
    n = n - 397;
    var s = e[n];
    return s;
  }, Qe(r, t);
}
(function(r, t) {
  for (var e = Qe, n = r(); []; )
    try {
      var x = -parseInt(e(405)) / 1 * (-parseInt(e(402)) / 2) + -parseInt(e(400)) / 3 + parseInt(e(403)) / 4 * (parseInt(e(397)) / 5) + -parseInt(e(407)) / 6 * (-parseInt(e(399)) / 7) + parseInt(e(408)) / 8 + parseInt(e(404)) / 9 * (-parseInt(e(401)) / 10) + parseInt(e(398)) / 11 * (-parseInt(e(406)) / 12);
      if (x === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(be, 860518);
class S {
  constructor(t) {
    this.type = t;
  }
}
const K = ne;
(function(r, t) {
  const e = ne, n = r();
  for (; []; )
    try {
      if (-parseInt(e(499)) / 1 + parseInt(e(509)) / 2 + parseInt(e(498)) / 3 * (-parseInt(e(507)) / 4) + parseInt(e(500)) / 5 + parseInt(e(508)) / 6 * (parseInt(e(518)) / 7) + parseInt(e(515)) / 8 * (parseInt(e(503)) / 9) + -parseInt(e(496)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(ge, 655546);
function ne(r, t) {
  const e = ge();
  return ne = function(n, x) {
    return n = n - 495, e[n];
  }, ne(r, t);
}
class Ht extends H {
  constructor(t, e) {
    const n = ne;
    super(), this.nodeCtrlBox = t, this.editor = t[n(512)], this.stage = this.editor.stage, this.selectedGroup = this.stage[n(511)], this[n(521)](p.nodeResizePoint.width, p.nodeResizePoint[n(497)]), this.css(p.nodeResizePoint.style), this[n(510)] = e;
  }
  [K(506)]() {
  }
  mousemoveHandler() {
    const t = K;
    let e, n = this.name;
    n == f.lt ? e = "nw-resize" : n == f.ct ? e = "n-resize" : n == f.rt ? e = "ne-resize" : n == f.lm ? e = "w-resize" : n == f.rm ? e = t(495) : n == f.lb ? e = "sw-resize" : n == f.cb ? e = "s-resize" : n == f.rb && (e = "se-resize"), this[t(512)][t(520)].setCursor(e);
  }
  mouseoutHandler(t) {
  }
  mousedownHandler(t) {
    t[K(516)]();
  }
  mouseupHandler(t) {
    t.preventDefault();
  }
  [K(522)](t) {
    const e = K;
    let n = this.nodeCtrlBox, x = n[e(512)], s = x.stage, i = this[e(510)];
    if (this.selectedGroup[e(514)]().length == 0)
      throw new Error(e(505));
    let c = s.inputSystem.target, d = c.stageToLocalXY(t.x, t.y), l = c.positionToLocalPoint(i), h = d.x - l.x, u = d.y - l.y;
    if (i == f.lt ? (h = -h, u = -u) : i == f.ct ? (h = 0, u = -u) : i == f.rt ? u = -u : i == f.lm ? (h = -h, u = 0) : i == f.rm ? u = 0 : i == f.lb ? h = -h : i == f.cb ? h = 0 : i == f.rb, x[e(519)].isKeydown(Ve.ResizeKeepAspectRatio)) {
      let b = c[e(497)] / (c.width || 1);
      u = h * b;
    }
    if (this[e(517)](c, { dx: h, dy: u }, i), h != 0 && u != 0) {
      let b = new S("resize");
      b[e(502)] = c, b.details = { dx: h, dy: u, ctrl: i }, x.dispatchEvent(b);
    }
  }
  adjustFixedDirection(t, e, n) {
    const x = K;
    let s = bt[n], i = t.positionToLocalPoint(s);
    i = t[x(513)](i), t.resizeWith(e.dx, e.dy);
    let a = t.positionToLocalPoint(s);
    a = t.transformPoint(a);
    let o = i.x - a.x, c = i.y - a.y;
    t[x(504)](o, c), this[x(501)].updateSize();
  }
}
function ge() {
  const r = ["392266oFyQzP", "name", "selectedGroup", "editor", "transformPoint", "getNoChildrensObjects", "200creSTd", "preventDefault", "adjustFixedDirection", "28gjHXia", "keyboard", "stage", "resizeTo", "dragHandler", "e-resize", "2177750rlABzt", "height", "6fPYCLy", "999884wTVEZq", "1366130mMhcoX", "nodeCtrlBox", "object", "404406MSYGll", "translateWith", "selectedGroup.length is 0!", "mouseenterHandler", "485472kdqLeP", "784848hvjDIB"];
  return ge = function() {
    return r;
  }, ge();
}
function Y(r, t) {
  const e = ye();
  return Y = function(n, x) {
    return n = n - 128, e[n];
  }, Y(r, t);
}
const z = Y;
(function(r, t) {
  const e = Y, n = r();
  for (; []; )
    try {
      if (-parseInt(e(134)) / 1 * (-parseInt(e(143)) / 2) + parseInt(e(150)) / 3 * (parseInt(e(153)) / 4) + -parseInt(e(141)) / 5 * (-parseInt(e(152)) / 6) + parseInt(e(131)) / 7 + parseInt(e(136)) / 8 * (-parseInt(e(129)) / 9) + -parseInt(e(128)) / 10 * (-parseInt(e(158)) / 11) + -parseInt(e(142)) / 12 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(ye, 269957);
function xt(r, t) {
  const e = Y;
  let n = t[e(132)], x = r[e(144)](e(151));
  return x = r.getStageTransform().point(x), Math.atan2(n.y - x.y, n.x - x.x);
}
class Mt extends ct {
  constructor(t, e) {
    const n = Y;
    super(), this.editor = t[n(147)], this[n(139)] = this.editor.stage, this.selectedGroup = this.stage.selectedGroup, this[n(133)](p.nodeRotatePoint[n(135)]), this.resizeTo(p.nodeRotatePoint.width, p[n(149)].height), this[n(145)] = e;
  }
  [z(138)]() {
    const t = z;
    this[t(139)].setCursor(C[t(130)]);
  }
  [z(157)](t) {
    const e = z;
    t.preventDefault();
    let n = this[e(139)];
    if (this.selectedGroup.getNoChildrensObjects().length == 0)
      throw new Error(e(140));
    let s = n.inputSystem.target;
    this.elementInitAngle = s.rotation, this.mouseInitAngle = xt(s, n);
  }
  [z(137)](t) {
    const e = z;
    this.stage.setCursor("auto"), t[e(155)]();
  }
  dragHandler(t) {
    const e = z;
    t[e(155)]();
    let n = this.parent, x = n.editor, s = x.stage;
    if (x[e(139)].selectedGroup[e(156)]()[e(148)] == 0)
      throw new Error("selectedGroup.length is 0!");
    let o = s.inputSystem.target, c = xt(o, s) - this[e(154)];
    if (o.rotate(this.elementInitAngle + c), n.updateSize(), c != 0) {
      let d = new S("rotate");
      d.object = o, d[e(146)] = { dAngle: c }, x.dispatchEvent(d);
    }
  }
}
function ye() {
  const r = ["8317CpvLDI", "style", "185672IXVhOt", "mouseupHandler", "mousemoveHandler", "stage", "selectedGroup.length is 0!", "1333235IQFNkm", "12284988PwSjma", "26kkrmkw", "positionToLocalPoint", "name", "details", "editor", "length", "nodeRotatePoint", "231126hkasOb", "center", "12yVFKCr", "16bISARL", "mouseInitAngle", "preventDefault", "getNoChildrensObjects", "mousedownHandler", "4949659yXoZzd", "10WQTWqa", "135PUSSbS", "move", "1696023ppNXMd", "inputSystem", "css"];
  return ye = function() {
    return r;
  }, ye();
}
function q(r, t) {
  const e = Ie();
  return q = function(n, x) {
    return n = n - 213, e[n];
  }, q(r, t);
}
const v = q;
(function(r, t) {
  const e = q, n = r();
  for (; []; )
    try {
      if (parseInt(e(234)) / 1 * (parseInt(e(229)) / 2) + parseInt(e(228)) / 3 * (-parseInt(e(241)) / 4) + parseInt(e(239)) / 5 * (-parseInt(e(218)) / 6) + parseInt(e(237)) / 7 + parseInt(e(227)) / 8 + parseInt(e(223)) / 9 + parseInt(e(236)) / 10 * (-parseInt(e(216)) / 11) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ie, 473259);
function Ie() {
  const r = ["6LEatJT", "12VUvzDw", "name", "initCtrlPoint", "currNode", "show", "138717KZIvtw", "mouseoutStageHandler", "6470QgUheV", "1416744njovBs", "attachTo", "4525YRwZky", "initPoints", "1300496sceRAm", "positionToLocalPoint", "point", "createCtrlPoint", "18029eEEayu", "update", "3972JwDUXp", "editor", "updateSize", "forEach", "endArrow", "8411715RxIHYy", "rotate", "zIndex", "clearTarget", "6509768tKjFlW"];
  return Ie = function() {
    return r;
  }, Ie();
}
function Bt(r) {
  const t = q;
  let e = r.parent;
  return e instanceof y && (e.beginArrow === r || e[t(222)] === r);
}
class Ot extends H {
  constructor(t, e = 0, n = 0, x = 1, s = 1) {
    const i = q;
    super(null, e, n, x, s), this[i(225)] = Ue.NodeCtrlBox, this[i(219)] = t, this.mouseEnabled = ![], this.hide();
  }
  [v(217)]() {
    const t = v;
    this.visible && this[t(220)]();
  }
  [v(235)]() {
  }
  mouseenterStageHandler() {
  }
  [v(231)](t) {
    const e = v;
    this.removeAllChild();
    let n = t.getCtrlPoints(), x = this;
    n[e(221)]((s) => {
      const i = e;
      if (x[i(215)](s), s == "rotate") {
        let a = function() {
          return x.positionToLocalPoint(f.ct);
        }, o = x[i(215)](i(224)), c = new y(null, o, a);
        c.css(p.nodeRotatePoint.rotateLineStyle), c.mouseEnabled = ![], x.addChild(c);
      }
    });
  }
  createCtrlPoint(t) {
    let e;
    return t == "rotate" ? e = new Mt(this, t) : e = new Ht(this, t), this.addChild(e), e;
  }
  [v(226)]() {
    this.currNode = null, this.hide();
  }
  [v(238)](t) {
    const e = v;
    if (!Bt(t)) {
      if (t.editable != !![])
        throw new Error("attach not Node or not editable");
      this.currNode !== null && this.initCtrlPoint(t), this.currNode = t, this[e(220)](), this[e(233)]();
    }
  }
  [v(220)]() {
    const t = v;
    let e = this.currNode;
    if (e == null || e.isSelected == ![] || e.parent == null) {
      this[t(232)] = null, this.hide();
      return;
    }
    this.viewClone(e), this.initPoints();
  }
  [v(240)]() {
    const t = v;
    let e = this.getChildren();
    for (var n = 0; n < e.length; n++) {
      let x = e[n];
      if (x instanceof y)
        continue;
      let s;
      x.name == "rotate" ? (s = this.positionToLocalPoint(f.ct), s.y -= p.nodeRotatePoint.rotateLineLength) : s = this.positionToLocalPoint(x[t(230)]), x instanceof H && x.translateTo(s.x, s.y);
    }
  }
  viewClone(t) {
    const e = v;
    let n = t.getStageTransform(), x = n.point(t.positionToLocalPoint(f.center)), s = n.point(t.positionToLocalPoint(f.rm)), i = Math.atan2(s.y - x.y, s.x - x.x);
    n.rotate(-i);
    let a = n[e(214)](t[e(213)](f.center)), o = n.point(t.positionToLocalPoint(f.rb)), c = (o.x - a.x) * 2, d = (o.y - a.y) * 2;
    this.resizeTo(c, d), this.rotate(i), this.translateTo(x.x, x.y);
  }
}
const W = X;
(function(r, t) {
  const e = X, n = r();
  for (; []; )
    try {
      if (parseInt(e(275)) / 1 * (parseInt(e(286)) / 2) + -parseInt(e(307)) / 3 + -parseInt(e(284)) / 4 + -parseInt(e(287)) / 5 * (-parseInt(e(301)) / 6) + parseInt(e(267)) / 7 * (-parseInt(e(272)) / 8) + parseInt(e(271)) / 9 + parseInt(e(266)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(me, 842617);
function X(r, t) {
  const e = me();
  return X = function(n, x) {
    return n = n - 263, e[n];
  }, X(r, t);
}
class jt extends ct {
  constructor(t) {
    const e = X;
    super(), this.isConnectPoint = !![], this.isActive = ![], this[e(281)](p.linkCtrlPoint.size), this.linkCtrlBox = t, this.editor = this.linkCtrlBox.editor, this.css(p[e(296)].style);
  }
  active() {
    this.css(p.linkCtrlPoint.activeStyle), this.isActive = !![];
  }
  unActive() {
    this[X(308)](p.linkCtrlPoint.unactiveStyle), this.isActive = ![];
  }
  [W(304)]() {
    const t = W;
    this[t(273)].anchorBox.hide(), this.editor.stage.setCursor(C.move);
    let e = this.parent, n = this.editor.stage, x = e.attachedLink;
    if (x instanceof ee) {
      let s = null, i = x.points[0], a = x.points[1], o = x.points[4], c = x.points[5];
      this.name == f[t(285)] ? Ye(x[t(302)](0, 0.5)) ? s = a.x > i.x ? C[t(288)] : C.e_resize : s = a.y > i.y ? C.s_resize : C[t(279)] : this.name == f[t(276)] ? Ye(x.getK(4, 0.5)) ? s = o.x > c.x ? C.w_resize : C.e_resize : s = o.y > c.y ? C.s_resize : C.n_resize : this.name == f[t(270)] && (Ye(x.getK(2, 0.5)) ? s = C.s_resize : s = C.e_resize), s && n.setCursor(s);
    }
  }
  [W(283)](t) {
    const e = W;
    this[e(273)][e(289)].setCursor("crosshair"), this[e(292)] = null;
  }
  dragHandler(t) {
    const e = W;
    let n = this.parent, x = n.parent, s = x.stage;
    const i = this.editor;
    let a = n.attachedLink;
    i[e(295)].hide();
    const o = this[e(278)];
    this.editor.stage.setCursor(C.crosshair);
    let c = a.parent.stageToLocalXY(s.inputSystem.x, s.inputSystem.y);
    if (t[e(291)] && (o === f.begin || o === f.end) && this.active(), o == f.begin) {
      a instanceof ee && a.resetOffset(), a.setBegin({ x: c.x, y: c.y }), a.updateMatrix(), this.canConnectEndpoint = i.anchorBox[e(280)]([a]);
      return;
    }
    if (o == f[e(293)]) {
      a instanceof ee && a[e(290)](), a[e(303)]({ x: c.x, y: c.y }), a[e(299)](), this[e(292)] = i.anchorBox[e(280)]([a]);
      return;
    }
    if (a instanceof gt)
      this[e(278)] == f[e(294)] && (a[e(294)] == null ? a.setCtrlPoint(a[e(305)](o)) : (a.ctrlPoint.x += c.x - a[e(294)].x, a.ctrlPoint.y += c.y - a[e(294)].y, a[e(300)](a.ctrlPoint)));
    else if (a instanceof yt)
      o == f.ctrlPoint1 ? a.ctrlPoint1 == null ? a.setCtrlPoint1(a.positionToLocalPoint(o)) : (a.ctrlPoint1.x += c.x - a[e(269)].x, a.ctrlPoint1.y += c.y - a.ctrlPoint1.y, a.setCtrlPoint1(a[e(269)])) : o == f.ctrlPoint2 && (a.ctrlPoint2 == null ? a.setCtrlPoint2(a.positionToLocalPoint(o)) : (a.ctrlPoint2.x += c.x - a.ctrlPoint2.x, a.ctrlPoint2.y += c.y - a[e(274)].y, a[e(268)](a[e(274)])));
    else if (a instanceof ee) {
      if (o == f.fold1) {
        let d = a[e(305)](f.fold1);
        const l = c.x - d.x, h = c.y - d.y;
        a.setFold1Offset(l, h), a.updateMatrix();
      } else if (o == f.center) {
        let d = a.positionToLocalPoint(f.center);
        const l = c.x - d.x, h = c.y - d.y;
        a[e(297)](l, h), a.updateMatrix();
      } else if (o == f.fold2) {
        let d = a.positionToLocalPoint(f.fold2);
        const l = c.x - d.x, h = c.y - d.y;
        a.setFold2Offset(l, h), a.updateMatrix();
      }
    }
  }
  mouseupHandler(t) {
    const e = W;
    this.editor.stage.setCursor(C.auto), t.event.preventDefault(), this[e(263)] && this[e(306)]();
    let n = this[e(264)], x = n.attachedLink;
    if (this[e(292)] != null) {
      let s = this[e(292)], i = s.target;
      if (i.isLink) {
        let a = i;
        a.end.target !== x && a.begin[e(282)] !== x && (this[e(278)] == f[e(265)] ? x[e(277)](a, s) : this.name == f.end && x[e(303)](a, s), x.upgradeParent(), x[e(299)]());
      } else
        this.name == f.begin ? x.setBegin(i, s) : this[e(278)] == f[e(293)] && x.setEnd(i, s), x[e(298)](), x.updateMatrix();
      this.canConnectEndpoint = null;
    }
  }
}
function me() {
  const r = ["findConnectableEndpoint", "setRadius", "target", "mousedownHandler", "5142120AgQnuK", "fold1", "1234226fFVfIF", "5uvhDTu", "w_resize", "stage", "resetOffset", "isDragStart", "canConnectEndpoint", "end", "ctrlPoint", "anchorBox", "linkCtrlPoint", "setCenterOffset", "upgradeParent", "updateMatrix", "setCtrlPoint", "3664866MBGXsL", "getK", "setEnd", "mousemoveHandler", "positionToLocalPoint", "unActive", "2065626qQoPpC", "css", "isActive", "parent", "begin", "21737720kbBttl", "17808VBOedn", "setCtrlPoint2", "ctrlPoint1", "center", "2817225KincMq", "2824HKwlue", "editor", "ctrlPoint2", "1KQqlYc", "fold2", "setBegin", "name", "n_resize"];
  return me = function() {
    return r;
  }, me();
}
function xe(r, t) {
  const e = Ce();
  return xe = function(n, x) {
    return n = n - 134, e[n];
  }, xe(r, t);
}
const M = xe;
function Ce() {
  const r = ["setBegin", "center", "ctrlPoint", "setEnd", "background", "attachedLink", "parent", "mouseenterStageHandler", "Link", "132EbTuuX", "8669424fJozQc", "clearTarget", "ctrlPointInfo", "21771sTWcJU", "matrixDirty", "show", "217135rIHKtB", "7pDLwjZ", "fillColor", "linkCtrlPoint", "1503RxglPk", "ctrlPoint1", "updateCtrlPoints", "150obAAbq", "1828488vtOKyZ", "css", "20430cVXKJj", "attachTo", "addChild", "452967NUyYNi", "removeAllChild", "getCtrlPoints", "114sIbBSB", "hideAllPoint", "1116170XymyRT"];
  return Ce = function() {
    return r;
  }, Ce();
}
(function(r, t) {
  const e = xe, n = r();
  for (; []; )
    try {
      if (-parseInt(e(155)) / 1 + parseInt(e(149)) / 2 * (parseInt(e(139)) / 3) + -parseInt(e(150)) / 4 + -parseInt(e(142)) / 5 * (-parseInt(e(158)) / 6) + parseInt(e(143)) / 7 * (-parseInt(e(136)) / 8) + parseInt(e(146)) / 9 * (parseInt(e(152)) / 10) + parseInt(e(160)) / 11 * (parseInt(e(135)) / 12) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ce, 832972);
class Nt extends H {
  constructor(t) {
    const e = xe;
    super(), this[e(138)] = {}, this.zIndex = Ue.LinkCtrlBox, this.editor = t, this.ctrlPointStyle = new It({}), this.init();
  }
  mouseoutStageHandler() {
  }
  [M(168)]() {
  }
  createNodeResizePoint(t) {
    let e = new jt(this);
    return e.name = t, this.addChild(e), e;
  }
  init() {
    this.ctrlPointInfo = {}, this.hide();
  }
  draw(t) {
    this.visible != ![] && this.updateSize();
  }
  updateSize() {
    const t = M, e = this.editor.stage.inputSystem.target;
    this.attachedLink ? this[t(166)][t(167)] == null || e == null || this.attachedLink != e ? (this.attachedLink = null, this.hide()) : this[t(153)](this[t(166)]) : this.hide();
  }
  [M(148)](t) {
    const e = M;
    this.attachedLink = t;
    let n = t.getCtrlPoints();
    this.anchorNameStr = n.join(",");
    for (let x = 0; x < n.length; x++) {
      let s = n[x], i = this.ctrlPointInfo[s];
      if (i == null) {
        if (i = this.createNodeResizePoint(s), s != f.begin && s != f.end && (i[e(144)] = this.ctrlPointStyle[e(165)], i.css(p.linkCtrlPoint.adjustStyle), i.isConnectPoint = ![]), s == e(147)) {
          let a = new y();
          a[e(151)](p[e(145)].ctrlLinkStyle), a.setBegin(i, e(162));
          let o = this.ctrlPointInfo.begin;
          a.setEnd(o, "center"), this[e(154)](a), this[e(138)][s + e(134)] = a;
        } else if (s == "ctrlPoint2") {
          let a = new y();
          a[e(151)](p.linkCtrlPoint.ctrlLinkStyle), a[e(161)](i, "center");
          let o = this.ctrlPointInfo.end;
          a[e(164)](o, "center"), this.addChild(a), this.ctrlPointInfo[s + "Link"] = a;
        } else if (s == e(163)) {
          let a = new y();
          a.css(p.linkCtrlPoint.ctrlLinkStyle), a[e(161)](i, "center");
          let o = this.ctrlPointInfo.begin;
          a.setEnd(o, "center"), this[e(154)](a), this.ctrlPointInfo[s + e(134)] = a;
        }
        this.ctrlPointInfo[s] = i;
      }
    }
  }
  hideAllPoint() {
    let e = this[M(138)];
    for (var n in e)
      e[n].hide();
  }
  updateFllow() {
    const t = M, e = this.attachedLink;
    let n = e.getStageTransform(), x = e[t(157)]();
    this[t(159)]();
    for (let s = 0; s < x.length; s++) {
      const i = x[s];
      let a = this.ctrlPointInfo[i];
      if (a == null)
        continue;
      let o = e.positionToLocalPoint(i), c = n.point(o);
      a.translateTo(c.x, c.y), a.show();
      let d = this[t(138)][i + "Link"];
      d && d[t(141)]();
    }
  }
  [M(153)](t) {
    const e = M;
    if (this[e(140)] = !![], t[e(167)] != null) {
      if (this.attachedLink === t) {
        t instanceof ee && t[e(157)]().join(",") != this.anchorNameStr && this[e(148)](t), this.updateFllow(), this.show();
        return;
      }
      this[e(138)] = {}, this[e(156)](), this[e(148)](t), this.updateFllow(), this[e(141)]();
    }
  }
  [M(137)]() {
    const t = M;
    this[t(166)] = null, this.ctrlPointInfo = {}, this.removeAllChild(), this.hide();
  }
}
function we(r, t) {
  const e = ve();
  return we = function(n, x) {
    return n = n - 259, e[n];
  }, we(r, t);
}
const N = we;
(function(r, t) {
  const e = we, n = r();
  for (; []; )
    try {
      if (parseInt(e(267)) / 1 * (-parseInt(e(259)) / 2) + parseInt(e(270)) / 3 * (parseInt(e(272)) / 4) + parseInt(e(261)) / 5 + parseInt(e(263)) / 6 + -parseInt(e(273)) / 7 * (parseInt(e(283)) / 8) + parseInt(e(266)) / 9 * (parseInt(e(286)) / 10) + -parseInt(e(285)) / 11 * (parseInt(e(275)) / 12) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(ve, 352583);
var At = Object.defineProperty, Dt = Object[N(264)], st = (r, t, e, n) => {
  for (var x = n > 1 ? void 0 : n ? Dt(t, e) : t, s = r.length - 1, i; s >= 0; s--)
    (i = r[s]) && (x = (n ? i(t, e, x) : i(x)) || x);
  return n && x && At(t, e, x), x;
};
function ve() {
  const r = ["drawStartDelay", "mousedownHandler", "setEnd", "isActive", "13472oSBvZx", "findConnectableEndpoint", "7629677NXfPOb", "6560TobZBn", "drawStartInfo", "activeStyle", "setCursor", "isDragStart", "instanceManager", "anchorPoint", "isIntersectPoint", "end", "30826lXSVZU", "intersect", "1623280lJhZWZ", "now", "2925786UhCYLX", "getOwnPropertyDescriptor", "画线开始", "9666ihIBca", "1PASGeB", "stageToLocalXY", "link", "57315CpsZPI", "editor", "76hbxhAq", "518uvsOKg", "lineDrawn", "24JJCYJW", "size", "update", "segIndex"];
  return ve = function() {
    return r;
  }, ve();
}
const zt = "intersectNode";
class se extends H {
  constructor(t, e = 0, n = 0, x = 1, s = 1) {
    const i = N;
    super(null, e, n, x, s), this[i(282)] = ![], this[i(293)] = ![], this.anchorBox = t, this.editor = t.editor, this.stage = this[i(271)].stage, this.resizeTo(p.anchorPoint[i(276)], p.anchorPoint.size), this.css(p[i(292)].style), this.unactive();
  }
  setIntersect(t) {
    const e = N;
    this[e(260)] = t;
  }
  mouseenterHandler() {
    this.active();
  }
  mouseoutHandler() {
    this.stage.setCursor("auto"), this.unactive();
  }
  mousemoveHandler() {
  }
  [N(280)](t) {
    const e = N;
    t.preventDefault(), this[e(271)].stage[e(289)]("crosshair"), this.link = null;
  }
  dragHandler(t) {
    const e = N;
    if (t.buttons == 2)
      return;
    const n = this.editor, x = n.stage;
    this.anchorBox.activedPoint && this.anchorBox.activedPoint.unactive();
    const s = this.anchorBox.target;
    if (t[e(290)]) {
      let i;
      if (this.isIntersectPoint) {
        let a = this.anchorBox.intersect;
        i = new lt(s, a.rate, a[e(278)]);
      } else
        i = new dt(s, this.name);
      this[e(287)] = { x: t.x, y: t.y, beginEndpoint: i, timeStamp: Date[e(262)]() };
      return;
    }
    if (this.drawStartInfo != null) {
      let i = le.distancePoint(this[e(287)], t);
      if (Date.now() - this.drawStartInfo.timeStamp > p.anchorPoint[e(279)] && i > p[e(292)].drawStartMinDistance) {
        this.editor.showTip(e(265)), console.assert(this.link == null, this.link), this.editor.record("划线");
        const c = n[e(291)][e(274)](null, s, null, this[e(287)].beginEndpoint);
        this[e(269)] = c;
        let d = c.parent[e(268)](x.inputSystem.x, x.inputSystem.y);
        c[e(281)](d), this.drawStartInfo = null;
      }
    }
    if (this.link != null && this.link.end != null) {
      let i = this.link.parent[e(268)](x.inputSystem.x, x.inputSystem.y);
      this[e(269)].setEnd(i), this.endpoint = this.anchorBox.findConnectableEndpoint([this.link, this.link[e(294)].target]);
    }
  }
  mouseupHandler(t) {
    const e = N;
    this.show(), this.editor[e(277)](), this[e(271)].stage[e(289)]("auto"), this.link != null && (this.endpoint && (this.link[e(281)](this.endpoint), this[e(269)].upgradeParent(), this[e(269)].updateMatrix()), this.editor[e(291)].lineDrawingFinished(this[e(269)]), this.anchorBox[e(284)]([]), this[e(271)].recordEnd("划线")), this[e(269)] = null;
  }
  active() {
    const t = N;
    this.isActive = !![], this.css(p.anchorPoint[t(288)]);
  }
  unactive() {
    this.isActive = ![], this.css(p.anchorPoint.unActiveStyle);
  }
}
st([et(ht.Circle)], se.prototype, "shape", 2), st([et("shape")], se.prototype, "pickType", 2);
const m = J;
(function(r, t) {
  const e = J, n = r();
  for (; []; )
    try {
      if (parseInt(e(292)) / 1 + parseInt(e(287)) / 2 * (parseInt(e(289)) / 3) + -parseInt(e(264)) / 4 + -parseInt(e(270)) / 5 + parseInt(e(268)) / 6 + parseInt(e(302)) / 7 + -parseInt(e(278)) / 8 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Se, 291898);
function J(r, t) {
  const e = Se();
  return J = function(n, x) {
    return n = n - 263, e[n];
  }, J(r, t);
}
function Se() {
  const r = ["3172603YIFMhW", "findConnectableEndpoint", "index", "children", "1295052WmIWfe", "point", "createAnchorPoint", "anchorPoint", "2362968dgslrY", "active", "1691080GaWezF", "getConnectableEndpointFromObjects", "update", "show", "length", "showIntersectAnchor", "keyboard", "target", "753616ByChLs", "unactive", "sort", "hide", "stage", "canConnectable", "visible", "setTarget", "anchorBox", "3214ULaDmU", "forEach", "123LcnKPJ", "ctrlIntersectPoint", "dist", "135135MfltlN", "_findChildren", "painted", "clearTarget", "object", "indexOf", "anchorName", "filter", "mouseY", "mouseEnabled"];
  return Se = function() {
    return r;
  }, Se();
}
function Ft(r) {
  let t = r.parent;
  return t instanceof y && (t.beginArrow === r || t.endArrow === r);
}
class Rt extends H {
  constructor(t, e = 0, n = 0, x = 1, s = 1) {
    const i = J;
    super(null, e, n, x, s), this.editor = t, this.ctrlIntersectPoint = this.createAnchorPoint(zt), this[i(290)].isIntersectPoint = !![], this[i(290)].hide();
  }
  cancel() {
  }
  mouseoutStageHandler() {
  }
  mouseenterStageHandler() {
  }
  [m(295)]() {
    this.target = null, this.hide();
  }
  [m(285)](t) {
    const e = m;
    if (!this.canConnectable(t))
      return;
    if (this.target !== t) {
      if (this.target = t, t != null && (t instanceof H || t instanceof y)) {
        let s = t.getAnchorPoints();
        this.children.length = 0;
        for (var n = 0; n < s.length; n++) {
          let i = s[n];
          this[e(266)](i);
        }
      }
      this[e(272)]();
    }
    this[e(263)][e(288)]((s) => s[e(279)]()), this.ctrlIntersectPoint[e(281)](), this[e(273)]();
  }
  canConnectable(t) {
    const e = m;
    return t == null || Ft(t) ? ![] : t[e(284)] && t[e(301)] && t.connectable && t[e(294)];
  }
  setTargetWithName(t, e) {
    if (e == null)
      throw new Error("activeNameOrPoint is null");
    this.setTarget(t), this.activePointByName(e), this.show();
  }
  clearActived() {
    const t = m;
    this.activedPoint && this.activedPoint.unactive(), this[t(290)][t(281)]();
  }
  activePointByName(t) {
    const e = m;
    this.activedPoint && this.activedPoint.unactive();
    let n = this.children, x = n[e(299)]((s) => s.name == t)[0];
    this.activedPoint = x, x[e(269)](), this[e(290)].hide();
  }
  [m(275)](t) {
    const e = m;
    if (this.intersect = t, t == null) {
      this.ctrlIntersectPoint[e(281)]();
      return;
    }
    let n = t.rate, x = t.segIndex;
    const s = this.target;
    let i = s.getLocalPoint(n, x), o = s.getStageTransform()[e(265)](i);
    this.ctrlIntersectPoint.translateTo(o.x, o.y), this[e(290)].css(p[e(267)].activeStyle), this.ctrlIntersectPoint.show();
  }
  update() {
    const t = m;
    if (this.target == null || this[t(277)].parent == null) {
      this.clearTarget();
      return;
    }
    const e = this.target, n = e.getStageTransform(), x = this[t(263)];
    for (let s = 0; s < x[t(274)]; s++) {
      const i = x[s], a = i.name;
      if (!i.isIntersectPoint) {
        let o = e.positionToLocalPoint(a), c = n[t(265)](o);
        i.translateTo(c.x, c.y);
      }
    }
  }
  createAnchorPoint(t) {
    const e = new se(this);
    return e.name = t, e.anchorBox = this, this.addChild(e), e;
  }
  getObjectsIntersect(t) {
    const e = m;
    let n = this.editor[e(282)], x = this.editor.getCurrentLayer(), s = { x: n.inputSystem.x, y: n.inputSystem.y };
    return mt(x, s, t, p[e(286)].nodeDist);
  }
  [m(271)](t) {
    const e = m, n = this.editor, x = this, s = n.stage, i = s.inputSystem, a = { x: i.x, y: i.y }, o = Ct(a, t, p.anchorPoint.size / 2), c = n[e(276)].isControlDown();
    if (o.length == 0 && !c) {
      let l = Gt(s, 100);
      return this.canConnectable(l) && x.setTarget(l), null;
    }
    if (o.length > 0) {
      o[e(280)]((L, P) => {
        let g = L.object, k = P.object;
        if (c) {
          let pt = g instanceof y ? 0 : 1, _t = k instanceof y ? 0 : 1;
          return pt - _t;
        }
        let w = g instanceof H ? 0 : 1, O = k instanceof H ? 0 : 1;
        return w - O;
      });
      const l = o[0], h = l[e(296)];
      let u = l[e(298)];
      const b = new dt(h, u);
      return x.setTargetWithName(h, u), b;
    }
    const d = this.getObjectsIntersect(t);
    if (d != null) {
      const l = d.object;
      x.setTarget(l);
      const h = new lt(l, d.rate, d.segIndex);
      return this.showIntersectAnchor(d), h;
    }
    return x[e(295)](), null;
  }
  [m(303)](t) {
    const e = m, n = this.editor, x = this, s = n.stage.localView.getObject();
    function i(c) {
      const d = J;
      return t[d(297)](c) == -1 && x[d(283)](c);
    }
    const a = s[e(293)](null, i, !![]);
    return this[e(271)](a);
  }
}
function Gt(r, t) {
  const e = m, n = r.getCurrentLayer(), x = { x: n.mouseX, y: n[e(300)] };
  let s = n.displayList[e(299)]((o) => o.isNode), i = s.map((o, c) => {
    let d = o._obb.aabb.getCenter();
    return { index: c, dist: le.distancePoint(x, d) };
  });
  if (i = i.filter((o) => o.dist <= t), i[e(274)] == 0)
    return null;
  i.sort((o, c) => o.dist - c[e(291)]);
  let a = i[0][e(304)];
  return s[a];
}
function ie(r, t) {
  const e = Pe();
  return ie = function(n, x) {
    return n = n - 396, e[n];
  }, ie(r, t);
}
const U = ie;
(function(r, t) {
  const e = ie, n = r();
  for (; []; )
    try {
      if (-parseInt(e(400)) / 1 + parseInt(e(415)) / 2 + -parseInt(e(410)) / 3 * (-parseInt(e(407)) / 4) + parseInt(e(402)) / 5 * (parseInt(e(401)) / 6) + parseInt(e(408)) / 7 * (parseInt(e(398)) / 8) + parseInt(e(404)) / 9 * (parseInt(e(414)) / 10) + parseInt(e(399)) / 11 * (-parseInt(e(396)) / 12) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Pe, 560032);
function Pe() {
  const r = ["146536YhzPSz", "5751878GydjOy", "939117ZoiTYQ", "3570PRrCJV", "895PBmxZL", "redoAll", "9KCpAIZ", "push", "redoHistory", "1084axmzxO", "427kuJLof", "redo", "4803ZcXEbu", "剪切粘贴", "undoHistory", "添加图元", "8332910lBEqIr", "1153678GJBXdA", "editor", "36rzoFdF", "undo"];
  return Pe = function() {
    return r;
  }, Pe();
}
const Kt = { cut: "cut", copy: "copy", delete: "删除", modify: "修改", addChild: U(413), pasteCopy: "辅助粘贴", pasteCut: U(411), resize: "尺寸修改", rotate: "旋转", dragNodeOrLink: "位置改变", modifyLink: "连线调整" };
class Wt {
  constructor(t, e, n) {
    this.type = t, this.undoFn = n, this.redoFn = e;
  }
  redo() {
    this.redoFn();
  }
  undo() {
    this.undoFn();
  }
}
class Ut extends EventTarget {
  constructor(t) {
    const e = U;
    super(), this.editor = t, this[e(412)] = [], this[e(406)] = [];
  }
  clear() {
    this.undoHistory = [], this.redoHistory = [];
  }
  push(t, e, n) {
    const x = U;
    let s = new Wt(t, e, n);
    return s.editor = this[x(416)], this.redoHistory.length = 0, this.undoHistory.push(s), s;
  }
  undo() {
    const t = U;
    if (this.undoHistory.length == 0)
      return null;
    let e = this.undoHistory.pop();
    return e[t(397)](), this[t(406)][t(405)](e), e;
  }
  redo() {
    if (this.redoHistory.length == 0)
      return null;
    let t = this.redoHistory.pop();
    return t.redo(), this.undoHistory.push(t), t;
  }
  undoAll(t = 500) {
    let e = this;
    function n() {
      let x = e.undo();
      e.editor.update(), x != null && setTimeout(n, t);
    }
    n();
  }
  [U(403)](t = 500) {
    let e = this;
    function n() {
      const x = ie;
      e.editor.update(), e[x(409)]() != null && setTimeout(n, t);
    }
    n();
  }
}
(function(r, t) {
  const e = ke, n = r();
  for (; []; )
    try {
      if (parseInt(e(446)) / 1 + parseInt(e(455)) / 2 + parseInt(e(453)) / 3 * (-parseInt(e(454)) / 4) + -parseInt(e(448)) / 5 * (-parseInt(e(452)) / 6) + -parseInt(e(451)) / 7 + -parseInt(e(449)) / 8 + parseInt(e(450)) / 9 * (parseInt(e(447)) / 10) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Le, 429046);
function Le() {
  const r = ["1341045IbfHaL", "6225296fNhawq", "728397xppdgo", "1198463aUhVsw", "12JMNyOu", "2094303PYQfbr", "4ybYxZU", "1695304CkDQdp", "getItem", "44984EZNbRn", "80YBPfBJ"];
  return Le = function() {
    return r;
  }, Le();
}
function ke(r, t) {
  const e = Le();
  return ke = function(n, x) {
    return n = n - 446, e[n];
  }, ke(r, t);
}
const Vt = { getItem: function(r) {
  const t = ke;
  return localStorage[t(456)](r);
}, setItem: function(r, t) {
  localStorage.setItem(r, t);
}, saveWithVersion(r, t) {
  r += Date.now(), this.setItem(r, t);
}, getAllVersions(r) {
  return Object.keys(localStorage).filter((e) => e.startsWith(r)).sort();
}, getLastVersion(r, t) {
  let e = this.getAllVersions(r).reverse();
  if (e.length == 0)
    return;
  t == null && (t = 0), t + 1 >= e.length && (t = e.length - 1);
  let n = e[t];
  return this.getItem(n);
} };
(function(r, t) {
  const e = Te, n = r();
  for (; []; )
    try {
      if (-parseInt(e(157)) / 1 + parseInt(e(148)) / 2 + -parseInt(e(152)) / 3 + parseInt(e(150)) / 4 + -parseInt(e(151)) / 5 + parseInt(e(147)) / 6 + -parseInt(e(143)) / 7 * (-parseInt(e(156)) / 8) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ee, 264629);
function Te(r, t) {
  const e = Ee();
  return Te = function(n, x) {
    return n = n - 143, e[n];
  }, Te(r, t);
}
function Yt(r, t, e) {
  const n = Te, x = r[n(146)], s = r.KeysConfig.CreateGroup;
  let i = {};
  function a(d) {
    const l = n;
    i[d] = r.stage[l(153)][l(145)].slice(), r.showOpTooltip("编组-" + d);
  }
  function o(d) {
    const l = n;
    let h = i[d];
    if (h)
      return h = h[l(155)]((u) => u.parent !== null), h.length > 0 && (r.showOpTooltip("选编组-" + d), r.stage.selectedGroup.removeAll().addAll(h), r.stage[l(158)][l(144)] = h[0]), h;
  }
  function c(d) {
    const l = n;
    o(d) && e[l(149)](t.selectedGroup.objects);
  }
  for (let d = 0; d <= 9; d++)
    x.bindKey(s + "+" + d, function(l) {
      l[n(159)](), a(d);
    }), x[n(154)]("" + d, function(l) {
      l.preventDefault(), o(d);
    }), x.bindKey("" + d + "+" + d, function(l) {
      l[n(159)](), c(d);
    });
}
function Ee() {
  const r = ["1678744LbrJNp", "218873eEFOeC", "inputSystem", "preventDefault", "7HwUllJ", "target", "objects", "keyboard", "1603266lKjxSp", "955810dfkEzs", "centerBy", "1276868PucmHT", "2613915rKUmuy", "803673vHMcKx", "selectedGroup", "bindKey", "filter"];
  return Ee = function() {
    return r;
  }, Ee();
}
(function(r, t) {
  const e = Me, n = r();
  for (; []; )
    try {
      if (parseInt(e(187)) / 1 + -parseInt(e(198)) / 2 + parseInt(e(183)) / 3 + -parseInt(e(202)) / 4 + -parseInt(e(180)) / 5 + parseInt(e(201)) / 6 + parseInt(e(173)) / 7 * (parseInt(e(196)) / 8) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(He, 501009);
function He() {
  const r = ["Cut", "77qnFOQI", "getNoChildrensObjects", "clipboardManager", "redoUndoSys", "imageToBase64", "stage", "showOpTooltip", "1903100lSJzzH", "Undo", "Move_left", "1537812hSlZeJ", "Move_right", "forEach", "toogleLocalView", "254905Ivuosx", "copyHandler", "filter", "keyup", "Cancel", "redo", "doGridLayout", "selectedGroup", "displayList", "599688ETiDmm", "Select_invert", "1053928zcMAMK", "saveHandler", "Save", "4125168vUFjNi", "3484060BjIuTx"];
  return He = function() {
    return r;
  }, He();
}
function Me(r, t) {
  const e = He();
  return Me = function(n, x) {
    return n = n - 172, e[n];
  }, Me(r, t);
}
class qt {
  constructor(t) {
    this.editor = t, this.init();
  }
  init() {
    const t = Me, e = this.editor, n = e.stage, x = e.currentLayer;
    let s = e[t(178)].keyboard;
    const i = e.KeysConfig;
    function a(o, c, d) {
      o.forEach((l) => {
        s.bindKey(l, function(h) {
          c(h);
        }, d);
      });
    }
    s.on("keydown", function() {
      e.update();
    }), s.on(t(190), function(o) {
      e.update();
    }), a(i.Delete, (o) => e.editorEventManager.deleteHandler(o)), a(i[t(172)], (o) => e.clipboardManager.cutHandler(o)), a(i.Copy, (o) => {
      const c = t;
      e[c(179)]("复制"), e[c(175)][c(188)](o);
    }), a(i.Paste, (o) => e.clipboardManager.pasteHandler(o)), a(i[t(181)], (o) => {
      e.showOpTooltip("撤销"), e.redoUndoSys.undo(), e.clearCtrlBoxs();
    }), a(i.Redo, (o) => {
      const c = t;
      e.showOpTooltip("重做"), e[c(176)][c(192)](o), e.clearCtrlBoxs();
    }), a(i.Select_all, (o) => {
      e.showOpTooltip("全选");
      let c = n.localView.getObject(), d = wt.flatten(c.children, (l) => l.visible == !![]);
      n.select(d);
    }), a(i[t(197)], (o) => {
      const c = t;
      e[c(179)]("反选");
      let d = n.selectedGroup.getNoChildrensObjects();
      n.select(x[c(195)].filter((l) => d.notContains(l)));
    }), a(i[t(200)], (o) => {
      const c = t;
      e[c(179)]("保存"), e[c(199)](o, e[c(177)]);
    }, ![]), a(i.Open, (o) => {
      e.showOpTooltip("打开"), e.openLasted(o);
    }, ![]), a(i.LocalView, (o) => {
      e[t(186)]();
    }), a(i.Copy_style, (o) => {
      e.showOpTooltip("复制样式"), e.clipboardManager.styleCopyHandler(o);
    }, ![]), a(i.paste_Style, (o) => {
      e.showOpTooltip("粘贴样式"), e.clipboardManager.stylePasteHandler(o);
    }, ![]), a(i[t(182)], (o) => {
      const c = t;
      n[c(194)].getNoChildrensObjects()[c(189)]((l) => l.isNode).forEach((l) => {
        l.x -= 1;
      });
    }), a(i[t(184)], (o) => {
      const c = t;
      n[c(194)].getNoChildrensObjects()[c(189)]((l) => l.isNode)[c(185)]((l) => {
        l.x += 1;
      });
    }), a(i.Move_up, (o) => {
      const c = t;
      n.selectedGroup[c(174)]().filter((l) => l.isNode).forEach((l) => {
        l.y -= 1;
      });
    }), a(i.Move_down, (o) => {
      n[t(194)].getNoChildrensObjects().filter((l) => l.isNode).forEach((l) => {
        l.y += 1;
      });
    }), a(i.Layout_grid, (o) => {
      const c = t;
      e.layoutManager[c(193)]();
    }), Yt(e, n, x), a(i[t(191)], (o) => {
      e.onEsc(o);
    });
  }
}
function re(r, t) {
  const e = Be();
  return re = function(n, x) {
    return n = n - 241, e[n];
  }, re(r, t);
}
const Xt = re;
function Be() {
  const r = ["setZIndex", "左对齐", "左右等距", "2AGgzgX", "5873030uNTbdZ", "5207034XKNCvU", "垂直中心对齐", "1943493riZPuk", "inputSystem", "18jvYQJe", "cutHandler", "876828oOOahz", "clipboardManager", "646301yepFCy", "234UJDhSs", "44YfNCVE", "2547736dTnKQp", "zIndex", "4NweiiD", "align", `
<div class="header">编辑</div>
<a>剪切</a>
<a>复制</a>
<a>粘贴</a>
<a>删除</a> 
<hr></hr>
<div class="header">前后</div>
<a>上移一层</a>
<a>下移一层</a>
<a>移至顶部</a>
<a>移至底部</a>
<div class="header">对齐</div>
<a>左对齐</a>
<a>右对齐</a>
<a>顶部对齐</a>
<a>底部对齐</a>
<a>水平中心对齐</a>
<a>垂直中心对齐</a>
`, "上移一层", "60mMwIQf", "顶部对齐", "layoutManager"];
  return Be = function() {
    return r;
  }, Be();
}
(function(r, t) {
  const e = re, n = r();
  for (; []; )
    try {
      if (-parseInt(e(247)) / 1 * (parseInt(e(262)) / 2) + parseInt(e(241)) / 3 * (-parseInt(e(252)) / 4) + parseInt(e(256)) / 5 * (parseInt(e(248)) / 6) + parseInt(e(264)) / 7 + -parseInt(e(250)) / 8 * (-parseInt(e(243)) / 9) + parseInt(e(263)) / 10 + parseInt(e(249)) / 11 * (-parseInt(e(245)) / 12) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Be, 382159);
let Jt = Xt(254);
function Zt(r) {
  const t = r.stage;
  let e = new vt(t, Jt);
  return e.addEventListener("select", function(n) {
    const x = re, s = n.item;
    let i = t[x(242)].target;
    s == "剪切" ? r.clipboardManager[x(244)]() : s == "复制" ? r[x(246)].copyHandler() : s == "粘贴" ? r.clipboardManager.pasteHandler() : s == "删除" && r.editorEventManager.deleteHandler(), i != null && (s == x(255) ? r.layoutManager.setZIndex(i, s) : s == "下移一层" ? r.layoutManager[x(259)](i, s) : s == "移至顶部" ? r[x(258)].setZIndex(i, s) : s == "移至底部" ? r.layoutManager[x(259)](i, s) : s == "左对齐" ? r.layoutManager.align(x(260)) : s == "右对齐" ? r[x(258)].align("右对齐") : s == "顶部对齐" ? r.layoutManager[x(253)](x(257)) : s == "底部对齐" ? r.layoutManager.align("底部对齐") : s == "水平中心对齐" ? r.layoutManager.align("水平中心对齐") : s == x(265) ? r.layoutManager[x(253)]("垂直中心对齐") : s == x(261) ? r[x(258)].evenSpacing(x(261)) : s == "上下等距" && r.layoutManager.evenSpacing("上下等距"), i[x(251)] < 0 ? i.zIndex = 0 : i.zIndex > 1e3 && (i[x(251)] = 1e3), i.parent != null && i.parent.updateZIndex(), r.update());
  }), e;
}
(function(r, t) {
  const e = D, n = r();
  for (; []; )
    try {
      if (-parseInt(e(261)) / 1 + -parseInt(e(256)) / 2 + parseInt(e(249)) / 3 + parseInt(e(254)) / 4 + -parseInt(e(251)) / 5 * (parseInt(e(263)) / 6) + -parseInt(e(260)) / 7 + parseInt(e(259)) / 8 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Oe, 379046);
function Oe() {
  const r = ["905288OGRVxQ", "_disabled", "39778PPSqGw", "setEnd", "hide", "7925592LmNPNP", "3714676HtnuSR", "710463gLEYXq", "guildlineW", "12YKagjt", "1622139kosIcR", "guildlineS", "294170paoBtK", "show", "hideGuidLine"];
  return Oe = function() {
    return r;
  }, Oe();
}
function D(r, t) {
  const e = Oe();
  return D = function(n, x) {
    return n = n - 249, e[n];
  }, D(r, t);
}
class Qt {
  constructor(t) {
    const e = D;
    this[e(255)] = !![], this.editor = t, this.init();
  }
  init() {
    const t = D, e = this.editor, n = new y();
    n.mouseEnabled = ![], n.css(p.guildLine.styleW), n.hide(), this[t(262)] = n, e.handlerLayer.addChild(this.guildlineW);
    const x = new y();
    n.mouseEnabled = ![], x.css(p.guildLine.styleS), x[t(258)](), this[t(250)] = x, e.handlerLayer.addChild(this[t(250)]);
  }
  disable() {
    const t = D;
    this._disabled = !![], this[t(253)]();
  }
  enable() {
    this._disabled = ![];
  }
  showGuildLine(t) {
    const e = D;
    if (this._disabled)
      return;
    const n = this.editor, x = n.stage, s = this[e(262)], i = this.guildlineS;
    s.setBegin({ x: 0, y: t.y }), s.setEnd({ x: x.width, y: t.y }), s[e(252)](), i.setBegin({ x: t.x, y: 0 }), i[e(257)]({ x: t.x, y: x.height }), i[e(252)]();
  }
  hideGuidLine() {
    const t = D;
    this.guildlineW[t(258)](), this.guildlineS.hide();
  }
}
(function(r, t) {
  for (var e = V, n = r(); []; )
    try {
      var x = -parseInt(e(212)) / 1 + parseInt(e(218)) / 2 + parseInt(e(217)) / 3 + -parseInt(e(216)) / 4 + -parseInt(e(219)) / 5 + parseInt(e(214)) / 6 + parseInt(e(209)) / 7 * (-parseInt(e(215)) / 8);
      if (x === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(je, 146405);
class Je {
  constructor() {
    this.take = 0;
  }
  copyPut(t) {
    var e = V;
    this[e(210)] = 0, this.type = "copy", this.source = t;
  }
  cutPut(t) {
    var e = V;
    this.take = -1, this[e(213)] = "cut", this.source = t;
  }
  takeSource() {
    var t = V;
    return this.take++, this[t(211)];
  }
  isFirstCutPaste() {
    return this.type == Kt.cut && this.take == 0;
  }
}
function V(r, t) {
  var e = je();
  return V = function(n, x) {
    n = n - 209;
    var s = e[n];
    return s;
  }, V(r, t);
}
function je() {
  var r = ["630054XcKYTO", "460318SiBKom", "27085zCOEKZ", "1675450qvKMLp", "take", "source", "178023sYYllq", "type", "1036008ZXUiTd", "8rEJSkI", "174600omsZvv"];
  return je = function() {
    return r;
  }, je();
}
const it = B;
function Ne() {
  const r = ["linkCtrlBox", "getNoChildrensObjects", "36856810osIajn", "408ehmljF", "dispatchEvent", "objects", "cssClipBoard", "copyAndToJSON", "css", "clearTarget", "copySetStyle", "stage", "13609575qOIRIY", "clipboard", "12HMgHkr", "753879cumCqG", "editor", "object", "2075878JRNxUS", "1YlxERb", "record", "className", "913534ARAsxQ", "copyPut", "serializerSystem", "6872464KuQqRo", "map", "selectedGroup", "recordEnd", "粘贴样式", "isEmpty", "76600YnNZPs"];
  return Ne = function() {
    return r;
  }, Ne();
}
(function(r, t) {
  const e = B, n = r();
  for (; []; )
    try {
      if (parseInt(e(417)) / 1 * (-parseInt(e(420)) / 2) + -parseInt(e(413)) / 3 * (-parseInt(e(412)) / 4) + -parseInt(e(397)) / 5 * (parseInt(e(401)) / 6) + parseInt(e(416)) / 7 + -parseInt(e(391)) / 8 + -parseInt(e(410)) / 9 + parseInt(e(400)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ne, 866354);
function B(r, t) {
  const e = Ne();
  return B = function(n, x) {
    return n = n - 390, e[n];
  }, B(r, t);
}
class $t {
  constructor(t) {
    const e = B;
    this.cssClipBoard = new Je(), this.clipboard = new Je(), this[e(404)] = new Je(), this.editor = t;
  }
  copyHandler() {
    const t = B;
    let e = this[t(414)][t(409)], n = [].concat(e.selectedGroup[t(399)]());
    if (n.length == 0)
      return;
    let x = n.map((i) => i.parent);
    this[t(411)][t(421)]([n, x]);
    let s = new S("copy");
    s[t(415)] = n, this.editor.dispatchEvent(s);
  }
  cutHandler() {
    const t = B;
    let e = this.editor, n = e.stage, x = [].concat(n[t(393)].getNoChildrensObjects());
    if (x.length == 0)
      return;
    let s = x[t(392)]((a) => a.parent);
    this.clipboard.cutPut([x, s]), e[t(418)]("剪切"), x.forEach((a, o) => {
      s[o].removeChild(a), ut.disconnect(a, x);
    }), e[t(394)]("剪切"), n.inputSystem.target = null, e.anchorBox[t(407)](), e.nodeCtrlBox[t(407)](), e[t(398)][t(407)]();
    let i = new S("cut");
    i[t(415)] = x, this[t(414)].dispatchEvent(i);
  }
  pasteHandler() {
    const t = B;
    let e = this.editor, n = this[t(411)].takeSource();
    if (n == null)
      return;
    let x = e.currentLayer, s = e.stage, i = n[0], a = n[1], o = i.map((g) => g._obb.aabb), c = ft.unionRects(o), d = c.getCenter(), l = x.stageToLocalXY(s.inputSystem.x, s.inputSystem.y), h = l.x - d.x, u = l.y - d.y;
    e.record("粘贴");
    let b = i, L = s.serializerSystem[t(405)](i);
    b = s[t(390)].jsonToObjects(L), b.forEach((g, k) => {
      g.translateWith(h, u);
    }), b.forEach((g, k) => {
      a[k].addChild(g);
    }), e.recordEnd("粘贴");
    let P = new S("paste");
    P.object = b, this.editor[t(402)](P);
  }
  styleCopyHandler() {
    const t = B;
    let e = this.editor, n = e.stage, x = n.inputSystem.target;
    x == null && (!n.selectedGroup[t(396)]() && (x = n.selectedGroup[t(403)][0]), x == null) || this[t(404)].copyPut(x);
  }
  stylePasteHandler() {
    const t = B;
    let e = this.editor, n = this[t(404)].takeSource();
    if (n == null)
      return;
    let x = e[t(409)].selectedGroup.objects, s = this;
    e[t(418)]("粘贴样式"), x.forEach((i) => {
      s.copySetStyle(i, n);
    }), e.recordEnd(t(395));
  }
  [it(408)](t, e) {
    const n = it;
    t !== e && t[n(419)] === e[n(419)] && t[n(406)](e.style);
  }
}
(function(r, t) {
  const e = R, n = r();
  for (; []; )
    try {
      if (-parseInt(e(136)) / 1 * (parseInt(e(149)) / 2) + -parseInt(e(140)) / 3 * (-parseInt(e(152)) / 4) + -parseInt(e(135)) / 5 + parseInt(e(142)) / 6 + -parseInt(e(145)) / 7 * (parseInt(e(139)) / 8) + parseInt(e(147)) / 9 * (-parseInt(e(153)) / 10) + -parseInt(e(134)) / 11 * (-parseInt(e(148)) / 12) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ae, 500916);
function Ae() {
  const r = ["NodeCtrlBox", "positionToLocalPoint", "112fSNMvz", "3iQwGqh", "show", "4663260MLtkAb", "zIndex", "resizeTo", "127743UYMPnm", "rotate", "18963uHLHEQ", "145032YVkbHt", "8690cpQEfJ", "updateSize", "currObject", "1289636khToql", "3670YVXjbA", "946KoviYy", "2199445IvIoRq", "39XjTLkz"];
  return Ae = function() {
    return r;
  }, Ae();
}
function R(r, t) {
  const e = Ae();
  return R = function(n, x) {
    return n = n - 134, e[n];
  }, R(r, t);
}
class e0 extends H {
  constructor(t, e = 0, n = 0, x = 1, s = 1) {
    const i = R;
    super(null, e, n, x, s), this[i(143)] = Ue[i(137)], this.editor = t, this.css(p.dropBox.style), this.mouseEnabled = ![], this.currObject, this.hide();
  }
  update() {
    const t = R;
    this.visible && this[t(150)]();
  }
  attachTo(t) {
    const e = R;
    if (t == null || t.editable != !![]) {
      this.currObject = null, this.hide();
      return;
    }
    if (t.isLink)
      throw new Error("attach not Node");
    this[e(151)] = t, this.updateSize(), this[e(141)]();
  }
  updateSize() {
    this.currObject != null && this.viewClone(this.currObject);
  }
  viewClone(t) {
    const e = R;
    let n = t.getStageTransform(), x = n.point(t.positionToLocalPoint(f.center)), s = n.point(t.positionToLocalPoint(f.rm)), i = Math.atan2(s.y - x.y, s.x - x.x);
    n[e(146)](-i);
    let a = n.point(t[e(138)](f.center)), o = n.point(t.positionToLocalPoint(f.rb)), c = (o.x - a.x) * 2, d = (o.y - a.y) * 2, l = 4;
    this[e(144)](c + l * 2, d + l * 2), this.rotate(i), this.translateTo(x.x, x.y);
  }
}
function ae(r, t) {
  const e = De();
  return ae = function(n, x) {
    return n = n - 342, e[n];
  }, ae(r, t);
}
function De() {
  const r = ["6NbyUcv", "24xiLVtj", "212664ravIrN", "11WEvWUE", "944240CMVPkZ", "mouseEnabled", "currentLayer", "397649OkgqnA", "3100030PaLXwy", "279320ATICUI", "assert", "length", "41qqjSwl", "isAncestors", "9rLGjBH", "6126ofkKBC", "1637964RdYroo"];
  return De = function() {
    return r;
  }, De();
}
(function(r, t) {
  const e = ae, n = r();
  for (; []; )
    try {
      if (parseInt(e(349)) / 1 * (parseInt(e(352)) / 2) + -parseInt(e(354)) / 3 * (-parseInt(e(356)) / 4) + parseInt(e(346)) / 5 * (parseInt(e(355)) / 6) + parseInt(e(344)) / 7 + -parseInt(e(358)) / 8 + -parseInt(e(351)) / 9 * (parseInt(e(345)) / 10) + parseInt(e(357)) / 11 * (parseInt(e(353)) / 12) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(De, 220642);
function rt(r, t) {
  const e = ae;
  let n = r[e(343)], x = r.currentLayer.getAllVisiable().filter((a) => {
    const o = e;
    return a.isNode && a.isSelected != !![] && a[o(342)] && a._cameraVisible;
  }), s = t.filter((a) => a.isOutOfParent() || a.parent === n), i;
  for (let a = 0; a < s[e(348)]; a++) {
    let o = s[a];
    if (i = t0(o, x), i != null)
      break;
  }
  return i == null && (i = n, s = s.filter((a) => a.parent !== n)), console[e(347)](i.isLink != !![], !![], i), { parent: i, objects: s };
}
function t0(r, t) {
  const e = r.getAABB(), n = t.filter((x) => {
    const s = ae;
    return x === r.parent || x === r ? ![] : r[s(350)](x) ? ![] : !![];
  });
  for (let x = n.length - 1; x >= 0; x--) {
    const s = n[x];
    if (s.getAABB().isIntersectRect(e))
      return s;
  }
  return null;
}
const I = oe;
(function(r, t) {
  const e = oe, n = r();
  for (; []; )
    try {
      if (parseInt(e(496)) / 1 + parseInt(e(487)) / 2 + parseInt(e(522)) / 3 * (-parseInt(e(506)) / 4) + -parseInt(e(511)) / 5 + parseInt(e(493)) / 6 * (-parseInt(e(476)) / 7) + parseInt(e(525)) / 8 * (parseInt(e(507)) / 9) + -parseInt(e(517)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(ze, 555915);
function ze() {
  const r = ["object", "isKeydown", "event", "removeAll", "3dKdSas", "changeParent", "KeysConfig", "7272OXAPFg", "mouseenterStageHandler", "update", "selectedGroup", "selectedGroupDragHandler", "stage", "nodeCtrlBox", "guidlineManager", "hide", "forEach", "findConnectableEndpoint", "keyboard", "isMouseOn", "newMode", "alignManager", "控制点", "attachTo", "mousedownHandler", "groupdrag", "editor", "84mshKsq", "onlineDrawingFinishedHandler", "setTarget", "mousedown", "record", "button", "addChild", "drop", "Shift", "mouseOverTarget", "controlTarget", "1907992DLhOrh", "toStageRect", "mouseoutStageHandler", "round", "showAt", "recordInterrupt", "143772EdPpOO", "init", "inputSystem", "1089789jIUyjq", "mouseoutHandler", "handlerLayer", "clearTarget", "dispatchEvent", "target", "isNode", "selectedGroupDragEndHandler", "linkCtrlBox", "getCurrentLayer", "2159556UpKidC", "7335OYyjbl", "zoom", "pickUpChild", "dropToBox", "1241815lgcvaR", "isDragEnd", "canConnectable", "anchorBox", "mouseup", "getNoChildrensObjects", "11529090FMpMUe"];
  return ze = function() {
    return r;
  }, ze();
}
const n0 = tt.w != null ? tt.w.charAt(3) : "1";
function oe(r, t) {
  const e = ze();
  return oe = function(n, x) {
    return n = n - 465, e[n];
  }, oe(r, t);
}
class x0 {
  constructor(t) {
    const e = oe;
    this[e(475)] = t, this[e(494)]();
  }
  [I(494)]() {
    const t = I;
    let e = this.editor, n = e.stage, x = n[t(495)], s = e.handlerLayer;
    n.localView.mode == null && (n.camera.on(t(508), function() {
      e.update();
    }), n.on(qe.modeChange, function(i) {
      const a = t;
      let o = i[a(469)], c = e[a(531)], d = e.linkCtrlBox, l = e.dropToBox, h = e.anchorBox;
      o == St.edit ? (s.addChild(c), s[a(482)](d), s.addChild(h), s.addChild(l)) : (s.removeChild(c), s.removeChild(d), s.removeChild(h), s.removeChild(l)), e[a(527)]();
    }), x.on("dragover", function(i) {
      i.preventDefault(), e.dispatchEvent(x);
    }), x.on(t(483), function() {
      x.event.defaultPrevented || e.dispatchEvent(x);
    }), x.on(t(479), function(i) {
      x.event.defaultPrevented || e.popupMenu.hide();
    }), x.on(t(515), function(i) {
      const a = t;
      x.button == 2 ? !x[a(512)] && e.popupMenu[a(491)](x.x, x.y) : e.popupMenu.hide();
    }), x.on("mousemove", function() {
    }), n.selectedGroup.on(qe[t(474)], function(i) {
      const a = t;
      e.selectedGroupDragHandler(i, n.selectedGroup[a(516)]());
    }), n.selectedGroup.on(qe.groupdragend, function(i) {
      e[t(503)](i, n.selectedGroup.getNoChildrensObjects());
    }));
  }
  deleteHandler() {
    const t = I;
    let e = this.editor, n = e.stage, x = n.selectedGroup;
    if (n0 != "1")
      return null;
    let s = x.getNoChildrensObjects();
    x[t(521)](), e.instanceManager.delete(s);
    let i = new S("delete");
    i.object = s, e[t(500)](i);
  }
  mousedownHandler(t) {
    const e = I, n = this[e(475)], x = n[e(530)].inputSystem;
    let s = n[e(498)], i = n.nodeCtrlBox, a = n.linkCtrlBox;
    if (n.alignManager[e(473)](x), n.controlTarget = s[e(509)](), n.anchorBox.hide(), n[e(486)] != null) {
      let o = n.controlTarget.parent;
      o !== i ? i.hide() : o !== a && a.hide(), n.controlTarget[e(473)](x), x[e(520)].preventDefault();
      return;
    }
    a.hide(), i.hide();
  }
  mousewheelHandler(t) {
  }
  dblclickHandler(t) {
  }
  mouseupHandler(t) {
    const e = I, n = this.editor, x = n.stage[e(495)];
    n.guidlineManager.hideGuidLine(), n[e(470)].mouseupHandler(x);
    let s = n.stage, i = n.nodeCtrlBox, a = n[e(504)], o = n.controlTarget;
    if (o != null) {
      x.isDragEnd && !(o instanceof se) && n.recordEnd("控制点"), o.mouseupHandler(x), x.event.preventDefault(), n.update();
      return;
    }
    if (x[e(481)] == 2)
      return;
    let c = s.inputSystem[e(501)];
    c != null ? (c.editable && (c instanceof y ? a[e(472)](c) : i[e(472)](c)), n[e(514)][e(513)](c) && n.anchorBox[e(478)](c)) : (n.anchorBox.clearTarget(), i[e(499)](), a.clearTarget());
    {
      if (n[e(510)].currObject != null) {
        let l = n[e(528)].getNoChildrensObjects(), h = rt(n, l), u = h.parent;
        h.objects.forEach((L) => {
          L[e(523)](u), L.upgradeLinks();
        });
      }
      n.dropToBox.attachTo(null);
    }
  }
  mousedragHandler(t) {
    const e = I, n = this.editor, x = n.stage.inputSystem;
    if (x.buttons == 2)
      return;
    n[e(467)].isKeydown(e(484)) && n.stage.setCursor(C.crosshair), n.guidlineManager.showGuildLine(x);
    let s = n[e(486)];
    if (s != null) {
      if (x.isDragStart && !(s instanceof se) && n.record(e(471)), s.dragHandler(x), x[e(520)] instanceof MouseEvent && x[e(520)].defaultPrevented == !![])
        return;
      x[e(520)].preventDefault();
      return;
    }
    n.alignManager.mousedragHandler(x), n[e(510)][e(533)]();
    const i = n[e(524)].DropTo_leader;
    if (n.stage.inputSystem.target && n[e(467)].isKeydown(i)) {
      let o = n.selectedGroup.getNoChildrensObjects(), c = rt(n, o), d = c.parent;
      d != null && d.editable && n[e(510)].attachTo(d);
    }
  }
  mousemoveHandler(t) {
    const e = I, n = this.editor;
    let x = n[e(530)];
    const s = x.handlerLayer;
    if (x.localView.getObject(), x.setCursor("auto"), n.keyboard[e(519)]("Control") && n.anchorBox[e(466)]([]))
      return;
    let i = s[e(509)]();
    if (i !== n[e(485)] && (n.mouseOverTarget != null && n[e(485)].mouseoutHandler(t), i != null && i.mouseenterHandler(t)), n.mouseOverTarget = i, i != null) {
      i.mousemoveHandler(t);
      return;
    }
    let a = x[e(505)]().pickUpChild();
    a != null && (n.stage.setCursor("move"), a.isNode && n.anchorBox[e(513)](a) && n.anchorBox[e(478)](a));
  }
  mouseenterHandler(t) {
    const e = I, n = this.editor;
    n.linkCtrlBox[e(526)](), n[e(531)].mouseenterStageHandler(), n.anchorBox[e(526)]();
  }
  [I(497)](t) {
    const e = I, n = this.editor;
    n.linkCtrlBox[e(489)](), n.nodeCtrlBox.mouseoutStageHandler(), n.anchorBox[e(489)]();
  }
  onLineDrawnHandler(t) {
    const e = I, n = this.editor;
    if (n.onLinkCreate)
      n.onLinkCreate(t);
    else {
      let x = new S("lineDrawn");
      x.object = t, n[e(500)](x), x = new S("drawLineStart"), x.object = t, n.dispatchEvent(x);
    }
  }
  [I(477)](t) {
    const e = I, n = this.editor;
    let x = new S("lineDrawingFinished");
    x.object = t, n.dispatchEvent(x), x = new S("drawLineEnd"), x[e(518)] = t, n[e(500)](x);
  }
  [I(529)](t, e) {
    const n = I, x = this.editor;
    if (x.stage.inputSystem.isDragStart && x[n(480)]("对象拖拽"), e.length == 1) {
      const i = e[0], a = x.getCurrentLayer()[n(488)](i._obb.aabb), o = a.getCenter();
      x[n(532)].showGuildLine(o);
    }
  }
  selectedGroupDragEndHandler(t, e) {
    const n = I, x = this[n(475)];
    if (!x.stage.inputSystem[n(468)]) {
      x[n(492)]();
      return;
    }
    e[n(465)]((i) => {
      const a = n;
      i[a(502)] && (i.x = Math[a(490)](i.x), i.y = Math.round(i.y));
    }), x.recordEnd("对象拖拽");
  }
}
(function(r, t) {
  const e = G, n = r();
  for (; []; )
    try {
      if (parseInt(e(147)) / 1 + parseInt(e(151)) / 2 + -parseInt(e(149)) / 3 * (parseInt(e(152)) / 4) + parseInt(e(163)) / 5 * (parseInt(e(144)) / 6) + parseInt(e(156)) / 7 + -parseInt(e(150)) / 8 * (parseInt(e(166)) / 9) + -parseInt(e(167)) / 10 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Fe, 797776);
function G(r, t) {
  const e = Fe();
  return G = function(n, x) {
    return n = n - 144, e[n];
  }, G(r, t);
}
class s0 {
  constructor(t) {
    this.editor = t;
  }
  setZIndex(t, e) {
    const n = G;
    e == "上移一层" ? t.zIndex++ : e == "下移一层" ? t[n(158)]-- : e == "移至顶部" ? t.zIndex = 1e3 : e == "移至底部" && (t.zIndex = 0), t.parent[n(161)]();
  }
  evenSpacing(t) {
    const e = G;
    let x = this.editor.stage.selectedGroup[e(154)]();
    x = x.filter((s) => s.isNode || s instanceof y && s[e(159)]()), x[e(165)] != 0;
  }
  align(t) {
    const e = G, n = this.editor;
    let x = n.stage.selectedGroup.getNoChildrensObjects();
    if (x = x.filter((a) => a.isNode || a instanceof y && a.isAlone()), x.length == 0)
      return;
    let s = x.map((a) => a._obb.aabb), i = ft.unionRects(s);
    for (let a = 0; a < x[e(165)]; a++) {
      let o = x[a], c = o._obb.aabb;
      t == "左对齐" ? o.translateWith(i.x - c.x, 0) : t == "右对齐" ? o.translateWith(i.getRight() - c.getRight(), 0) : t == "顶部对齐" ? o.translateWith(0, i.y - c.y) : t == e(155) ? o[e(148)](0, i.getBottom() - c[e(162)]()) : t == "水平中心对齐" ? o.translateWith(0, i.getCenter().y - c[e(146)]().y) : t == e(160) && o[e(148)](i.getCenter().x - c.getCenter().x, 0);
    }
    n.update();
  }
  doGridLayout() {
    const t = G;
    let e = this.editor, n = e.stage, x = n.selectedGroup.getNoChildrensObjects().filter((l) => l instanceof H);
    if (x[t(165)] < 2)
      return;
    let s = Math.ceil(Math[t(157)](x.length)), i = Pt.getUnionRect(x), a = i[t(146)](), o = ht[t(145)](s, s), c = n[t(153)].shapeLayout(x, o), d = x[0][t(164)] * s;
    c.resizeTo(d, d), c.translate(a.x, a.y), c.doLayout({ effect: "easeInQuart", duration: 300 }), e.showOpTooltip("网格布局");
  }
}
function Fe() {
  const r = ["sqrt", "zIndex", "isAlone", "垂直中心对齐", "updatezIndex", "getBottom", "5zEaSXc", "width", "length", "477SyoNui", "20407470kujgIv", "7773816SRTMyL", "outerGrid", "getCenter", "330667NmiWUZ", "translateWith", "1123782AfqUhU", "1584GdULue", "2865320vMjMnQ", "16mSWWyj", "layoutSystem", "getNoChildrensObjects", "底部对齐", "9019010ZbXRsf"];
  return Fe = function() {
    return r;
  }, Fe();
}
const Ze = Z;
(function(r, t) {
  const e = Z, n = r();
  for (; []; )
    try {
      if (parseInt(e(272)) / 1 + parseInt(e(261)) / 2 * (parseInt(e(269)) / 3) + parseInt(e(256)) / 4 + parseInt(e(250)) / 5 + -parseInt(e(252)) / 6 + -parseInt(e(253)) / 7 * (-parseInt(e(271)) / 8) + -parseInt(e(254)) / 9 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Re, 389198);
class i0 {
  constructor(t) {
    this.editor = t;
  }
  delete(t) {
    const e = Z;
    let n = this[e(265)];
    n.record("删除"), t[e(259)](function(x) {
      ut.disconnect(x), x.removeFromParent();
    }), n.showOpTooltip("删除"), n.recordEnd("删除");
  }
  addNewInstance(t) {
    const e = Z, n = this.editor;
    this.editor.stage[e(251)][e(264)]()[e(257)](t);
    let s = new S("create");
    s.object = t, n[e(255)](s);
  }
  [Ze(274)](t) {
    const e = Ze;
    let s = this.editor[e(258)].localView.getMouseXY(), i = s.x, a = s.y, o = nt(t);
    if (t.indexOf("Node") != -1) {
      let c = new o(null, i, a, 64, 64);
      return this[e(260)](c), c;
    }
    if (t.indexOf("Link") != -1) {
      let c = new o(null, { x: i - 40, y: a }, { x: i + 40, y: a + (t == "Link" ? 0 : 80) });
      return this[e(260)](c), c;
    }
    throw new Error(e(268) + t);
  }
  lineDrawn(t, e, n, x) {
    const s = Ze;
    let i = this.editor, a = nt(i.LinkClassName);
    const o = new a(t, e, n, x), c = Object.assign({}, i.newLinkProperties);
    c.css && (o[s(266)](c.css), delete c[s(266)]), Object.keys(c)[s(259)]((h) => {
      const u = s;
      let b = c[h];
      typeof b == u(273) ? o[h] = b() : o[h] = b;
    });
    let l = e.isNode || e.isLink ? e[s(262)] : i[s(270)];
    return o.zIndex = Ue[s(263)], l.addChild(o), i.editorEventManager[s(267)](o), o;
  }
  lineDrawingFinished(t) {
    this.editor.editorEventManager.onlineDrawingFinishedHandler(t);
  }
}
function Z(r, t) {
  const e = Re();
  return Z = function(n, x) {
    return n = n - 250, e[n];
  }, Z(r, t);
}
function Re() {
  const r = ["forEach", "addNewInstance", "217882uMKbHA", "parent", "EditorNewLink", "getObject", "editor", "css", "onLineDrawnHandler", "unknow classname:", "15MLPBaK", "currentLayer", "8kUDUYg", "229966GHIpgr", "function", "create", "3579215JQAoMY", "localView", "2988942IhFHjk", "2787575nIinyk", "11834910kydKIO", "dispatchEvent", "1254424GUMAVv", "addChild", "stage"];
  return Re = function() {
    return r;
  }, Re();
}
const j = Q;
function Q(r, t) {
  const e = Ge();
  return Q = function(n, x) {
    return n = n - 357, e[n];
  }, Q(r, t);
}
(function(r, t) {
  const e = Q, n = r();
  for (; []; )
    try {
      if (parseInt(e(357)) / 1 * (-parseInt(e(390)) / 2) + parseInt(e(365)) / 3 + parseInt(e(388)) / 4 + parseInt(e(382)) / 5 * (parseInt(e(361)) / 6) + parseInt(e(372)) / 7 + parseInt(e(386)) / 8 + parseInt(e(368)) / 9 * (-parseInt(e(376)) / 10) === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ge, 328109);
function Ge() {
  const r = ["xType", "label", "top", "2112936ofZEsy", "minDistance", "2098204qMFmaw", "displayList", "386bKukSF", "left", "setEnd", "dxLine", "show", "stageToLocalXY", "isNode", "css", "center", "1221aaEtIS", "init", "_disabled", "findAlignRectInfo", "6UWItJu", "right", "yType", "parent", "1812051wvWwHj", "mousedownHandler", "alignLineStyle", "234KTqIvt", "middle", "stage", "hide", "2405389weAryl", "filter", "abs", "bottom", "461410zRWfIb", "alignInfo", "beginArrow", "dyLine", "mouseEnabled", "aabb", "135580pFamWL"];
  return Ge = function() {
    return r;
  }, Ge();
}
class r0 {
  constructor(t) {
    const e = Q;
    this[e(387)] = p.align[e(387)], this._disabled = ![], this.visible = ![], this.editor = t, this.init(t);
  }
  [j(358)](t) {
    const e = j;
    let n = new y(null, { x: 0, y: 0 }, { x: 100, y: 100 });
    n.css(p.align[e(367)]), n.mouseEnabled = ![], this.dxLine = n;
    let x = new y(null, { x: 0, y: 0 }, { x: 100, y: 100 });
    x[e(397)](p.align.alignLineStyle), x[e(380)] = ![], this.dyLine = x, n.hide(), x[e(371)](), t.handlerLayer.addChild(n), t.handlerLayer.addChild(x);
  }
  disable() {
    this._disabled = !![], this.dxLine.hide(), this.dyLine.hide();
  }
  enable() {
    this._disabled = ![];
  }
  getAlignInfo(t, e) {
    const n = j, x = this.editor, s = x[n(370)];
    let i = t.target;
    if (s.selectedGroup.objects.length > 1)
      return null;
    let a = this.editor.getCurrentLayer(), o = (h) => h.isNode && h !== i && h !== h[n(364)][n(378)] && h !== h.parent.endArrow && h !== h.parent[n(384)];
    const c = s.getCurrentLayer()[n(389)][n(373)](o), d = c.map((h) => a.toStageRect(h._obb[n(381)]));
    return this[n(360)](a.toStageRect(i._obb.aabb), d, e);
  }
  [j(366)](t) {
  }
  mouseupHandler(t) {
    const e = j;
    if (this[e(359)])
      return;
    let n = !this.dxLine.visible && !this.dyLine.visible;
    if (this[e(393)][e(371)](), this[e(379)][e(371)](), n)
      return;
    let x = t.target, s = this[e(377)];
    if (x && x[e(396)] && s != null) {
      if (this.editor.getCurrentLayer(), s.xType != null) {
        let i = s.xRect, a = s.xType, o = i[s.xType];
        o = x.parent.stageToLocalXY(o, 0).x, a == e(391) ? x[e(391)] = o : a == "center" ? x.x = o : a == "right" && (x.right = o);
      }
      if (s[e(363)] != null) {
        let i = s.yRect, a = s.yType, o = i[s.yType];
        o = x.parent[e(395)](0, o).y, a == "top" ? x.top = o : a == "middle" ? x.y = o : a == e(375) && (x[e(375)] = o);
      }
      this.editor.update(), this[e(377)] = null;
    }
  }
  mousedragHandler(t) {
    const e = j;
    if (this._disabled)
      return;
    let n = t.target;
    if (n == null || !n.isNode || (this.dxLine.hide(), this.dyLine.hide(), this.editor.keyboard.isKeydown(Ve.DiableNodeAlign)))
      return;
    let x = this.dxLine, s = this.dyLine;
    const i = this.getAlignInfo(t, this.minDistance);
    if (this[e(377)] = i, i == null)
      return;
    let a = i.rect;
    if (i.xType != null) {
      let o = i.xRect, c = o[i[e(383)]], d = (a.middle + o.middle) / 2, l = Math[e(374)](a[e(369)] - o.middle);
      x.setBegin({ x: c, y: d - l / 2 }), x[e(392)]({ x: c, y: d + l / 2 }), x[e(394)]();
    }
    if (i.yType != null) {
      let o = i.yRect, c = (a[e(398)] + o.center) / 2, d = o[i.yType], l = Math.abs(a.center - o[e(398)]);
      s.setBegin({ x: c - l / 2, y: d }), s[e(392)]({ x: c + l / 2, y: d }), s.show();
    }
  }
  [j(360)](t, e, n) {
    const x = j;
    let s = Number.MAX_VALUE, i = Number.MAX_VALUE, a, o, c, d;
    e.sort((h, u) => {
      const b = Q;
      return le.distance(t.center, t[b(369)], u[b(398)], u[b(369)]) - le.distance(t.center, t.middle, h.center, h.middle);
    });
    let l;
    for (let h = 0; h < e.length; h++) {
      let u = e[h];
      u.isIntersectRect(t) || (l = Math[x(374)](t.top - u[x(385)]), l <= i && l <= n && (o = x(385), i = l, d = u), l = Math.abs(t.bottom - u[x(375)]), l <= i && l <= n && (o = "bottom", i = l, d = u), l = Math.abs(t[x(369)] - u[x(369)]), l <= i && l <= n && (o = "middle", i = l, d = u), l = Math[x(374)](t.left - u.left), l <= s && l < n && (a = "left", s = l, c = u), l = Math.abs(t[x(362)] - u.right), l <= s && l < n && (a = x(362), s = l, c = u), l = Math.abs(t.center - u.center), l <= s && l < n && (a = x(398), s = l, c = u));
    }
    return { x: s, y: i, rect: t, xRect: c, yRect: d, xType: a, yType: o };
  }
}
const _ = ce;
(function(r, t) {
  const e = ce, n = r();
  for (; []; )
    try {
      if (-parseInt(e(372)) / 1 * (parseInt(e(387)) / 2) + parseInt(e(376)) / 3 + -parseInt(e(379)) / 4 + -parseInt(e(394)) / 5 * (parseInt(e(371)) / 6) + parseInt(e(378)) / 7 + parseInt(e(374)) / 8 + -parseInt(e(386)) / 9 === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(Ke, 178806);
function ce(r, t) {
  const e = Ke();
  return ce = function(n, x) {
    return n = n - 364, e[n];
  }, ce(r, t);
}
function Ke() {
  const r = ["record", "mousedragHandler", "mouseoutHandler", "dblclickHandler", "toogleLocalView", "enable", "setItem", "mouseenterHandler", "mouseupHandler", "openJson", "mousewheelHandler", "getCurrentLayer", "anchorBox", "sendKey", "isDraging", "update", "object", "opTooltip", "lastLayerState", "78036dTRDzw", "1GthJoO", "restoreToJson", "2494504uloPOt", "inputSystem", "592656DwUakh", "redoUndoSys", "1773366ZAJZgR", "923604nCKgvn", "getItem", "newLink", "getState", "fadeOut", "dispatchEvent", "toFileJson", "591021gRcCTZ", "80426NptiaJ", "keyboard", "recordEnd", "msg", "editorEventManager", "targetOnly", "stage", "95XekKxf", "recordName", "layoutManager", "selectedGroupDragEndHandler", "toJson", "editor", "newLinkProperties", "mousedownHandler", "create", "push", "data"];
  return Ke = function() {
    return r;
  }, Ke();
}
class at extends ot {
  constructor(t) {
    const e = ce;
    super(), this.KeysConfig = Ve, this.EditorConfig = p, this.LinkClassName = "AutoFoldLink", this[e(400)] = {}, this.DataCenter = Vt, this.imageToBase64 = ![], this[e(393)] = t, t[e(399)] = this, this.currentLayer = this.getCurrentLayer(), this.handlerLayer = t.handlerLayer, this.selectedGroup = this.stage.selectedGroup, this.keyboard = t[e(388)], this.clipboardManager = new $t(this), this.instanceManager = new i0(this), this[e(391)] = new x0(this), this.keyManager = new qt(this), this.nodeCtrlBox = new Ot(this), this.linkCtrlBox = new Nt(this), this.anchorBox = new Rt(this), this.dropToBox = new e0(this), this.popupMenu = Zt(this), this.stage[e(375)].target = null, this.controlTarget = null, this.mouseOverTarget = null, this.inputTextfield = new Lt(this), this[e(377)] = new Ut(this), this[e(396)] = new s0(this), this.guidlineManager = new Qt(this), this.alignManager = new r0(this), this[e(369)] = new kt(t), Object.assign(this.opTooltip.domElement.style, { paddingLeft: "20px", paddingRight: "20px", color: "black" });
  }
  setLinkClassName(t) {
    this.LinkClassName = t;
  }
  [_(416)]() {
    return this.stage.getCurrentLayer();
  }
  defineKeys(t) {
    Object.assign(this.KeysConfig, t);
  }
  showOpTooltip(t) {
    const e = _;
    p.operationTip[e(410)] && (this[e(369)].setHtml(t), this.opTooltip.showAt(this.stage.width * 0.5, this[e(393)].height * 0.5), this.opTooltip[e(383)](80));
  }
  saveHandler(t, e = ![]) {
    const n = _;
    let x = this.getCurrentLayer(), s = x.toFileJson({ imageToBase64: e });
    this.DataCenter[n(411)](p[n(404)].localLastDocName, s);
    const i = new S("save");
    i[n(368)] = s, i.imageToBase64 = e, this[n(384)](i);
  }
  openLasted() {
    const t = _, e = this[t(416)](), n = this.DataCenter[t(380)](p.data.localLastDocName);
    return n != null ? e[t(414)](n) : null;
  }
  [_(408)](t) {
    const e = _;
    this.editorEventManager[e(408)](t), this.update();
  }
  [_(415)](t) {
    const e = _;
    this[e(391)].mousewheelHandler(t), this[e(367)]();
  }
  [_(401)](t) {
    this.editorEventManager.mousedownHandler(t), this.update();
  }
  [_(413)](t) {
    const e = _;
    this[e(391)][e(413)](t), this.update();
  }
  mousedragHandler(t) {
    const e = _;
    this.editorEventManager[e(406)](t), this[e(367)]();
  }
  mousemoveHandler(t) {
    const e = _;
    if (this.stage.inputSystem[e(366)])
      return this.mousedragHandler(t);
    this.editorEventManager.mousemoveHandler(t), this[e(367)]();
  }
  [_(412)](t) {
    const e = _;
    this.editorEventManager.mouseenterHandler(t), this[e(367)]();
  }
  [_(407)](t) {
    this.editorEventManager.mouseoutHandler(t), this.update();
  }
  selectedGroupDragHandler(t, e) {
    this.editorEventManager.selectedGroupDragHandler(t, e);
  }
  selectedGroupDragEndHandler(t, e) {
    const n = _;
    this.editorEventManager[n(397)](t, e);
  }
  recordInterrupt() {
    const t = _;
    this.recordName = null, this[t(370)] = null;
  }
  [_(405)](t) {
    const e = _;
    let n = this.currentLayer, x = n.stage.serializerSystem;
    this[e(395)] != null && console.warn("record和recordEnd没有成对出现", this.recordName + ":" + t), this.recordName = t, this.lastLayerState = x.getState(this.currentLayer);
  }
  [_(389)](t) {
    const e = _;
    t != this[e(395)], this.recordName = null;
    let n = this.currentLayer, x = n.stage.serializerSystem, s = this.lastLayerState, i = x[e(382)](n);
    this.redoUndoSys[e(403)](t, function() {
      x[e(373)](n, i);
    }, function() {
      x.restoreToJson(n, s);
    });
  }
  [_(381)](t, e, n, x) {
    return this.instanceManager.lineDrawn(t, e, n, x);
  }
  [_(367)]() {
    this.dropToBox.update(), this.nodeCtrlBox.update(), this.anchorBox.update(), this.stage.update();
  }
  [_(398)](t = ![]) {
    const e = _;
    return this[e(416)]()[e(385)](t);
  }
  [_(414)](t) {
    const e = _;
    this.getCurrentLayer().openJson(t);
    let n = new S("open");
    n.object = t, this[e(384)](n);
  }
  showTip(t, e = "") {
    const n = _;
    let x = new Event("log");
    x[n(390)] = t + e, this[n(384)](x);
  }
  [_(402)](t) {
    return this.instanceManager.create(t);
  }
  clearCtrlBoxs() {
    this.anchorBox.clearTarget(), this.nodeCtrlBox.clearTarget(), this.linkCtrlBox.clearTarget();
  }
  [_(409)]() {
    const t = _;
    let e = this[t(393)].inputSystem.target;
    this.stage.camera[t(392)](e), this.clearCtrlBoxs(), this.update();
  }
  [_(365)](t, e) {
    this.keyboard.sendKey(t, e);
  }
  onEsc() {
    this[_(364)].cancel();
  }
}
at.KeysConfig = Ve, Tt.Editor = at;
(function(r, t) {
  for (var e = $e, n = r(); []; )
    try {
      var x = parseInt(e(328)) / 1 * (-parseInt(e(322)) / 2) + parseInt(e(329)) / 3 * (-parseInt(e(323)) / 4) + -parseInt(e(324)) / 5 + -parseInt(e(327)) / 6 + parseInt(e(326)) / 7 + parseInt(e(325)) / 8 + -parseInt(e(321)) / 9;
      if (x === t)
        break;
      n.push(n.shift());
    } catch {
      n.push(n.shift());
    }
})(We, 708224);
function We() {
  var r = ["296DpXGDM", "1226560wwoMWp", "7735112aHyezz", "9364019nRdaKO", "1610940CzLnte", "63389nUEpBW", "8697TxjxPN", "4959981robFXo", "10JrUKQf"];
  return We = function() {
    return r;
  }, We();
}
function $e(r, t) {
  var e = We();
  return $e = function(n, x) {
    n = n - 321;
    var s = e[n];
    return s;
  }, $e(r, t);
}
export {
  at as Editor,
  o0 as IconsPanel,
  c0 as PropertiesPanel
};
