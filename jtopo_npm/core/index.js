const Li = J0;
(function(n, e) {
  const t = J0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(278)) / 1 * (-parseInt(t(280)) / 2) + -parseInt(t(288)) / 3 + -parseInt(t(276)) / 4 + -parseInt(t(279)) / 5 + parseInt(t(287)) / 6 + -parseInt(t(283)) / 7 * (parseInt(t(284)) / 8) + -parseInt(t(277)) / 9 * (-parseInt(t(286)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Lx, 590047);
function Yr(n, e, t) {
  return n[0] = e[0] + t[0], n[1] = e[1] + t[1], n;
}
function Ui(n, e, t) {
  return n[0] = e[0] * t, n[1] = e[1] * t, n;
}
function Jr(n, e, t) {
  return n[0] = e[0] * t, n[1] = e[1] * t, n;
}
function J0(n, e) {
  const t = Lx();
  return J0 = function(x, s) {
    return x = x - 276, t[x];
  }, J0(n, e);
}
function Vr(n, e) {
  return n[0] = -e[0], n[1] = -e[1], n;
}
function Gr(n, e) {
  let t = Math.sqrt(e[0] * e[0] + e[1] * e[1]);
  return t == 0 ? (n[0] = 0, n[0] = 0, n) : (n[0] = e[0] / t, n[1] = e[1] / t, n);
}
function Lx() {
  const n = ["2546607gsayrX", "4521036rflthr", "33859629BveJUc", "39461xWArad", "4978775IxtGzL", "10qgZYTI", "multiplyC", "projection", "971782TnWOMI", "56jgmLTB", "sqrt", "10yVGOur", "3463356FKHLLU"];
  return Lx = function() {
    return n;
  }, Lx();
}
function qr(n) {
  return Math[J0(285)](n[0] * n[0] + n[1] * n[1]);
}
function Zi(n, e) {
  return n[0] * e[0] + n[1] * e[1];
}
function Ur(n, e, t) {
  let x = Zi(e, t);
  return Ui(n, t, x), n;
}
class B {
}
B[Li(281)] = Ui, B.scale = Jr, B.len = qr, B.negate = Vr, B.add = Yr, B.normalize = Gr, B.dot = Zi, B[Li(282)] = Ur;
const at = Ex;
(function(n, e) {
  const t = Ex, x = n();
  for (; []; )
    try {
      if (-parseInt(t(199)) / 1 + parseInt(t(212)) / 2 + parseInt(t(194)) / 3 * (parseInt(t(204)) / 4) + parseInt(t(208)) / 5 * (-parseInt(t(197)) / 6) + -parseInt(t(205)) / 7 + parseInt(t(216)) / 8 * (parseInt(t(217)) / 9) + -parseInt(t(215)) / 10 * (parseInt(t(201)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Cx, 201175);
const kt = { DefaultFont: at(196) }, Ve = { PointClosestEpsilon: 0.01, DefaultLightName: "DefaultLight", DefaultDarkName: "DefaultDark", flatten: !![] }, V0 = 2 * Math.PI, _i = "2.5.3_试用版";
function Cx() {
  const n = ["257579HxTnBK", "ctrlPoint1", "groupdragend", "4455dAFAZv", "edit", "ctrlPoint", "fullWindow", "753928zVWnRI", "s-resize", "zoomAfter", "100ceSyeu", "2170928eDpAtE", "9bkwdhc", "mid2", "12FUesWT", "move", "10px sans-serif", "738SxSoCx", "vertical", "147753IFDXuv", "begin", "266860ytvaTd", "drag", "zoom", "89588CgRpNm"];
  return Cx = function() {
    return n;
  }, Cx();
}
const Ct = { drag: at(202), edit: at(209), normal: "normal", select: "select", view: "view" };
function Ex(n, e) {
  const t = Cx();
  return Ex = function(x, s) {
    return x = x - 193, t[x];
  }, Ex(n, e);
}
const Zr = { auto: "auto", move: at(195), hand: "hand", crosshair: "crosshair", s_resize: at(213), n_resize: "n-resize", w_resize: "w-resize", e_resize: "e-resize" }, o0 = { horizontal: "horizontal", vertical: at(198) }, k = { lt: "lt", ct: "ct", rt: "rt", lm: "lm", center: "center", rm: "rm", lb: "lb", cb: "cb", rb: "rb", nearest: "nearest", begin: at(200), end: "end", ctrlPoint: at(210), ctrlPoint1: at(206), ctrlPoint2: "ctrlPoint2", fold1: "fold1", fold2: "fold2", mid: "mid", mid1: "mid1", mid2: at(193), up: "up", down: "down", left: "left", right: "right" }, Kr = { lt: k.rb, ct: k.cb, rt: k.lb, rm: k.lm, rb: k.lt, cb: k.ct, lb: k.rt, lm: k.rm }, cx = { HandlerLayerCanvas: 99, FullWindowDom: 1e3, Link: 1, Node: 2, EditorNewLink: 3, IntersectPoint: 999, NodeCtrlBox: 1e3, LinkCtrlBox: 1001 }, Ki = 0, Tx = 1, pt = { zoom: at(203), resize: "resize", zoomAfter: at(214), fullWindow: at(211), modeChange: "modeChange", groupdrag: "groupdrag", groupdragend: at(207), selectObject: "selectObject" };
function f(n) {
  return (e, t) => {
    e[t] = n;
  };
}
const Q = Ax;
(function(n, e) {
  const t = Ax, x = n();
  for (; []; )
    try {
      if (parseInt(t(377)) / 1 + parseInt(t(358)) / 2 + parseInt(t(373)) / 3 + -parseInt(t(365)) / 4 + -parseInt(t(367)) / 5 * (-parseInt(t(360)) / 6) + parseInt(t(374)) / 7 * (-parseInt(t(371)) / 8) + -parseInt(t(369)) / 9 * (parseInt(t(372)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Mx, 666317);
function Ax(n, e) {
  const t = Mx();
  return Ax = function(x, s) {
    return x = x - 353, t[x];
  }, Ax(n, e);
}
function Mx() {
  const n = ["concat", "26216jKJAlV", "7561270UHbPgd", "3791541HOEiPt", "2121jPOwTh", "assert error betweenPoints", "length", "1237224sKnCZP", "points.length < 2", "distance", "rotate", "atan2", "sqrt", "log", "offsetWithAngle", "669486zKHrjH", "forward", "6tBdIQJ", "toJSON", "sin", "interpolate", "normalize", "2523584SlcnVX", "cos", "4832920vEYmMm", "abs", "18pRsuxP"];
  return Mx = function() {
    return n;
  }, Mx();
}
class E {
  constructor(e = 0, t = 0) {
    this.x = e, this.y = t;
  }
  [Q(361)]() {
    return { x: this.x, y: this.y };
  }
  static isLikePoint(e) {
    const t = Q;
    return e instanceof E ? !![] : Object.keys(e)[t(376)] == 2 && e.x != null && e.y != null;
  }
  static looksSame(e, t, x) {
    if (e === t)
      return !![];
    let s = Math.abs(t.x - e.x), i = Math.abs(t.y - e.y);
    return x == null && (x = 0.01), s < x && i < x;
  }
  static middle(e, t) {
    return { x: (t.x + e.x) / 2, y: (t.y + e.y) / 2 };
  }
  static getAngle(e, t) {
    return Math[Q(354)](t.y - e.y, t.x - e.x);
  }
  static [Q(353)](e, t, x, s, i) {
    const r = Q;
    return { x: (e - x) * Math.cos(i) - (t - s) * Math.sin(i) + x, y: (e - x) * Math[r(362)](i) + (t - s) * Math.cos(i) + s };
  }
  static rotatePoint(e, t, x) {
    const s = Q;
    return { x: (e.x - t.x) * Math.cos(x) - (e.y - t.y) * Math.sin(x) + t.x, y: (e.x - t.x) * Math.sin(x) + (e.y - t.y) * Math[s(366)](x) + t.y };
  }
  static [Q(379)](e, t, x, s) {
    let i = x - e, r = s - t;
    return Math.sqrt(i * i + r * r);
  }
  static distancePoint(e, t) {
    const x = Q;
    let s = t.x - e.x, i = t.y - e.y;
    return Math[x(355)](s * s + i * i);
  }
  static mergeClosestPoints(e, t = Ve.PointClosestEpsilon) {
    const x = Q;
    let s = [e[0]];
    for (let i = 1; i < e.length - 1; i++) {
      let r = s[s.length - 1], o = e[i], a = e[i + 1];
      if (o.x === a.x && o.y === a.y)
        continue;
      let c = B.normalize([], [o.x - r.x, o.y - r.y]), l = B[x(364)]([], [a.x - o.x, a.y - o.y]);
      Math[x(368)](c[0] - l[0]) < t && Math[x(368)](c[1] - l[1]) < t || s.push(o);
    }
    return s.push(e[e.length - 1]), s;
  }
  static [Q(359)](e, t, x) {
    const s = Q;
    let i = Math[s(354)](t.y - e.y, t.x - e.x);
    return { x: e.x + x * Math.cos(i), y: e.y + x * Math[s(362)](i) };
  }
  static [Q(357)](e, t, x) {
    return typeof x == "number" && (x = { x: Math.cos(t) * x, y: Math.sin(t) * x }), { x: e.x + x.x, y: e.y + x.y };
  }
  static createPoints(e, t, x = 1, s = ![]) {
    const i = s ? [e] : [], r = s ? x - 1 : x;
    let o = e;
    for (var a = 0; a < r; a++) {
      const c = { x: o.x + t[0], y: o.y + t[1] };
      i.push(c), o = c;
    }
    return i;
  }
  static createPointsBidirectional(e, t, x) {
    const s = Q;
    if (x == 0)
      return [];
    const i = [-t[0], -t[1]];
    if (x % 2 == 0) {
      const a = [t[0] / 2, t[1] / 2], c = { x: e.x - a[0], y: e.y - a[1] }, l = { x: e.x + a[0], y: e.y + a[1] }, h = E.createPoints(c, i, x / 2, !![]), u = E.createPoints(l, t, x / 2, !![]);
      return h[s(370)](u);
    }
    const r = E.createPoints(e, i, (x - 1) / 2 + 1, !![]), o = E.createPoints(e, t, (x - 1) / 2, ![]);
    return r.concat(o);
  }
  static interpolate(e, t, x) {
    let s = (1 - x) * e.x, i = (1 - x) * e.y, r = x * t.x, o = x * t.y;
    return { x: s + r, y: i + o };
  }
  static getDistanceSum(e) {
    const t = Q;
    let x = e.length;
    if (x < 2)
      throw new Error(t(378));
    let s = e[0], i = e[x - 1];
    if (e[t(376)] == 2)
      return E.distancePoint(s, i);
    let r = 0;
    for (var o = 1; o < x; o++)
      r += E.distancePoint(e[o - 1], e[o]);
    return r;
  }
  static calculatePointOnMultiPointLine(e, t) {
    const x = Q;
    let s = e[x(376)];
    if (e.length < 2)
      throw new Error("points.length < 2");
    let i = e[0], r = e[e[x(376)] - 1];
    if (e.length == 2)
      return E.interpolate(i, r, t);
    if (t < 0)
      return E[x(363)](e[0], e[1], t);
    if (t > 1)
      return E.interpolate(e[e.length - 2], e[e.length - 1], t);
    let o = E.getDistanceSum(e), a = o * t, c = 0;
    for (var l = 1; l < s; l++) {
      let h = E.distancePoint(e[l - 1], e[l]);
      if (a >= c && a <= c + h) {
        let u = a - c, p = u / h;
        return E.interpolate(e[l - 1], e[l], p);
      }
      c += h;
    }
    throw console[x(356)](e, t), new Error(x(375));
  }
}
const yt = G0;
function G0(n, e) {
  const t = Dx();
  return G0 = function(x, s) {
    return x = x - 184, t[x];
  }, G0(n, e);
}
(function(n, e) {
  const t = G0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(201)) / 1 + parseInt(t(202)) / 2 + -parseInt(t(191)) / 3 * (parseInt(t(188)) / 4) + parseInt(t(204)) / 5 + parseInt(t(195)) / 6 * (parseInt(t(198)) / 7) + parseInt(t(205)) / 8 + -parseInt(t(189)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Dx, 441876);
function Dx() {
  const n = ["width", "18mEeRdD", "point", "scaleY", "1257263lvPeoY", "getTranslation", "translate", "555567vvAnsV", "1180852KKgrzZ", "rotate", "1635200lumOfU", "6584520moIHwz", "length", "getMatrix", "skewY", "setAbsolutePosition", "17552cywjJh", "7627527tiMaJc", "push", "297nrqiCk", "points", "identity"];
  return Dx = function() {
    return n;
  }, Dx();
}
class lx {
  constructor(e) {
    const t = G0;
    this[t(194)] = 1, this.height = 1, this.rotation = 0, this.m = e && e.slice() || [1, 0, 0, 1, 0, 0];
  }
  get skewX() {
    return this.m[1];
  }
  get [yt(186)]() {
    return this.m[2];
  }
  get scaleX() {
    return this.m[0];
  }
  get [yt(197)]() {
    return this.m[3];
  }
  get x() {
    return this.m[4];
  }
  get y() {
    return this.m[5];
  }
  copy() {
    const e = yt;
    let t = new lx(this.m);
    return t[e(194)] = this.width, t.height = this.height, t.rotation = this.rotation, t;
  }
  [yt(193)]() {
    this.m[0] = 1, this.m[1] = 0, this.m[2] = 0, this.m[3] = 1, this.m[4] = 0, this.m[5] = 0;
  }
  transform(e, t, x, s, i, r) {
    let o = [e, t, x, s, i, r], a = this.m[0] * o[0] + this.m[2] * o[1], c = this.m[1] * o[0] + this.m[3] * o[1], l = this.m[0] * o[2] + this.m[2] * o[3], h = this.m[1] * o[2] + this.m[3] * o[3], u = this.m[0] * o[4] + this.m[2] * o[5] + this.m[4], p = this.m[1] * o[4] + this.m[3] * o[5] + this.m[5];
    this.m[0] = a, this.m[1] = c, this.m[2] = l, this.m[3] = h, this.m[4] = u, this.m[5] = p;
  }
  [yt(196)](e) {
    let t = this.m;
    return { x: t[0] * e.x + t[2] * e.y + t[4], y: t[3] * e.y + t[1] * e.x + t[5] };
  }
  vec(e, t) {
    let x = this.m;
    return e[0] = x[0] * t[0] + x[2] * t[1], e[1] = x[3] * t[1] + x[1] * t[0], e;
  }
  [yt(192)](e) {
    const t = yt;
    let x = [];
    for (var s = 0; s < e[t(184)]; s++) {
      let i = e[s];
      x[t(190)](this[t(196)](i));
    }
    return x;
  }
  translate(e, t) {
    return this.m[4] += this.m[0] * e + this.m[2] * t, this.m[5] += this.m[1] * e + this.m[3] * t, this;
  }
  translateTo(e, t) {
    return this.m[4] = e, this.m[5] = t, this;
  }
  scale(e, t) {
    return this.m[0] *= e, this.m[1] *= e, this.m[2] *= t, this.m[3] *= t, this;
  }
  getScale() {
    return { x: this.m[0], y: this.m[3] };
  }
  [yt(203)](e) {
    let t = Math.cos(e), x = Math.sin(e), s = this.m[0] * t + this.m[2] * x, i = this.m[1] * t + this.m[3] * x, r = this.m[0] * -x + this.m[2] * t, o = this.m[1] * -x + this.m[3] * t;
    return this.m[0] = s, this.m[1] = i, this.m[2] = r, this.m[3] = o, this;
  }
  rotateTarget(e, t, x) {
    const s = yt;
    this[s(200)](t, x), this[s(203)](e), this.translate(-t, -x);
  }
  [yt(199)]() {
    return { x: this.m[4], y: this.m[5] };
  }
  multiply(e) {
    let t = this.m, x = e.m, s = t[0] * x[0] + t[2] * x[1], i = t[1] * x[0] + t[3] * x[1], r = t[0] * x[2] + t[2] * x[3], o = t[1] * x[2] + t[3] * x[3], a = t[0] * x[4] + t[2] * x[5] + t[4], c = t[1] * x[4] + t[3] * x[5] + t[5];
    return this.m[0] = s, this.m[1] = i, this.m[2] = r, this.m[3] = o, this.m[4] = a, this.m[5] = c, this;
  }
  invert() {
    let e = 1 / (this.m[0] * this.m[3] - this.m[1] * this.m[2]), t = this.m[3] * e, x = -this.m[1] * e, s = -this.m[2] * e, i = this.m[0] * e, r = e * (this.m[2] * this.m[5] - this.m[3] * this.m[4]), o = e * (this.m[1] * this.m[4] - this.m[0] * this.m[5]);
    return this.m[0] = t, this.m[1] = x, this.m[2] = s, this.m[3] = i, this.m[4] = r, this.m[5] = o, this;
  }
  [yt(185)]() {
    return this.m;
  }
  [yt(187)](e, t) {
    let x = this.m[0], s = this.m[1], i = this.m[2], r = this.m[3], o = this.m[4], a = this.m[5], c = (x * (t - a) - s * (e - o)) / (x * r - s * i), l = (e - o - i * c) / x;
    return this.translate(l, c);
  }
}
function Bx() {
  const n = ["max", "setTo", "abs", "bottom", "5kptpDH", "clone", "min", "union", " width:", "3463480qJdpGn", "612072qfAbGA", "703296tjygUB", "height", "center", "4206209FSzWwO", "468567tacyTz", "getRight", "370FupWUH", "28920hQwUXu", "getBottom", "width", "aabb", "42xxKirw", "left", "161622GXpvqk"];
  return Bx = function() {
    return n;
  }, Bx();
}
const ut = dt;
(function(n, e) {
  const t = dt, x = n();
  for (; []; )
    try {
      if (-parseInt(t(260)) / 1 + -parseInt(t(263)) / 2 * (parseInt(t(267)) / 3) + -parseInt(t(256)) / 4 + -parseInt(t(274)) / 5 * (parseInt(t(269)) / 6) + -parseInt(t(259)) / 7 + -parseInt(t(254)) / 8 + parseInt(t(255)) / 9 * (parseInt(t(262)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Bx, 608706);
function dt(n, e) {
  const t = Bx();
  return dt = function(x, s) {
    return x = x - 251, t[x];
  }, dt(n, e);
}
class z {
  constructor(e = 0, t = 0, x = 0, s = 0) {
    const i = dt;
    this.x = 0, this.y = 0, this[i(265)] = 0, this[i(257)] = 0, this.x = e, this.y = t, this.width = x, this[i(257)] = s;
  }
  setTo(e = 0, t = 0, x = 0, s = 0) {
    this.x = e, this.y = t, this.width = x, this.height = s;
  }
  getRect() {
    return this;
  }
  clone() {
    const e = dt;
    return new z(this.x, this.y, this.width, this[e(257)]);
  }
  toString() {
    const e = dt;
    return "[x: " + this.x + " y:" + this.y + e(253) + this[e(265)] + " height:" + this[e(257)] + "]";
  }
  equals(e) {
    const t = dt;
    return e.x == this.x && e.y == this.y && e[t(265)] == this.width && e.height == this[t(257)];
  }
  containsRect(e) {
    const t = dt;
    return e.x > this.x && e.getRight() < this[t(261)]() && e.y > this.y && e.getBottom() < this[t(264)]() ? !![] : ![];
  }
  contains(e, t) {
    const x = dt;
    return e >= this.x && e <= this.x + this.width && t >= this.y && t <= this.y + this[x(257)];
  }
  isIntersectRect(e) {
    const t = dt;
    return e.x > this[t(261)]() || e.y > this[t(264)]() ? ![] : e.getRight() < this.x || e.getBottom() < this.y ? ![] : !![];
  }
  getRight() {
    return this.x + this.width;
  }
  getBottom() {
    return this.y + this.height;
  }
  isEmpty() {
    return this.x === 0 && this.y === 0 && this.width === 0 && this.height === 0;
  }
  setToEmpty() {
    this[dt(271)](0, 0, 0, 0);
  }
  getCenter() {
    const e = dt;
    return { x: this.x + this.width / 2, y: this.y + this[e(257)] / 2 };
  }
  toPoints() {
    return [{ x: this.x, y: this.y }, { x: this.right, y: this.y }, { x: this.right, y: this.bottom }, { x: this.x, y: this.bottom }];
  }
  static [ut(252)](e, t) {
    const x = ut;
    let s = Math[x(251)](e.x, t.x), i = Math.min(e.y, t.y), r = Math[x(270)](e.getRight(), t[x(261)]()), o = Math[x(270)](e.getBottom(), t[x(264)]());
    return e.setTo(s, i, r - s, o - i), e;
  }
  static unionRects(e) {
    const t = ut;
    let x = e[0][t(275)]();
    for (let s = 1; s < e.length; s++)
      x = z.union(x, e[s]);
    return x;
  }
  static normal(e, t) {
    const x = ut;
    let s = t.x - e.x, i = t.y - e.y;
    return Math.abs(s) > Math[x(272)](i) ? [Math.sign(s), 0] : [0, Math.sign(i)];
  }
  get left() {
    return this.x;
  }
  set [ut(268)](e) {
    this.x = e;
  }
  get right() {
    return this.x + this.width;
  }
  set right(e) {
    this.x = e - this.width;
  }
  get top() {
    return this.y;
  }
  set top(e) {
    const t = ut;
    this.y = e + this[t(257)];
  }
  get [ut(273)]() {
    return this.y + this.height;
  }
  set bottom(e) {
    const t = ut;
    this.y = e - this[t(257)];
  }
  get [ut(258)]() {
    const e = ut;
    return this.x + this[e(265)] * 0.5;
  }
  set center(e) {
    this.x = e - this.width * 0.5;
  }
  get middle() {
    const e = ut;
    return this.y + this[e(257)] * 0.5;
  }
  set middle(e) {
    const t = ut;
    this.y = e - this[t(257)] * 0.5;
  }
}
class Zt {
  constructor() {
  }
  contains(e, t) {
    return this[ut(266)].contains(e, t) == ![] ? ![] : !![];
  }
  static toAABB(e, t) {
    let x = e[0], s = { x: x.x, y: x.y }, i = { x: x.x, y: x.y }, r = { x: x.x, y: x.y }, o = { x: x.x, y: x.y };
    for (let a = 1; a < e.length; a++) {
      let c = e[a];
      c.x < s.x && (s.x = c.x), c.x > i.x && (i.x = c.x), c.y < r.y && (r.y = c.y), c.y > o.y && (o.y = c.y);
    }
    return t == null || t <= 1 ? new z(s.x, r.y, i.x - s.x, o.y - r.y) : new z(s.x - t, r.y - t, i.x - s.x + t + t, o.y - r.y + t + t);
  }
}
function xi(n, e) {
  var t = jx();
  return xi = function(x, s) {
    x = x - 132;
    var i = t[x];
    return i;
  }, xi(n, e);
}
(function(n, e) {
  for (var t = xi, x = n(); []; )
    try {
      var s = -parseInt(t(142)) / 1 * (-parseInt(t(139)) / 2) + parseInt(t(135)) / 3 * (-parseInt(t(133)) / 4) + parseInt(t(141)) / 5 + -parseInt(t(140)) / 6 * (-parseInt(t(134)) / 7) + parseInt(t(138)) / 8 + parseInt(t(137)) / 9 * (parseInt(t(136)) / 10) + -parseInt(t(132)) / 11;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(jx, 829803);
function jx() {
  var n = ["8011sDYYwh", "27003834gFmmnW", "1808236hElGBq", "7wEDLvz", "3NBlKWK", "27790zNsURI", "2853RFEkrd", "258064QXXZEr", "354Ktwkhn", "1766754XLUAbq", "5555745QbmTCX"];
  return jx = function() {
    return n;
  }, jx();
}
function ni(n, e) {
  var t = zx();
  return ni = function(x, s) {
    x = x - 157;
    var i = t[x];
    return i;
  }, ni(n, e);
}
function zx() {
  var n = ["6672eokmdv", "4cXGkAU", "2749824zjqAfm", "2115miqRCW", "31624HtZXIY", "544128BvhZNd", "33073850EzVjdp", "211kVkySe", "5010865zRbtxC", "951663WYRTsk"];
  return zx = function() {
    return n;
  }, zx();
}
(function(n, e) {
  for (var t = ni, x = n(); []; )
    try {
      var s = parseInt(t(158)) / 1 * (-parseInt(t(161)) / 2) + parseInt(t(160)) / 3 + parseInt(t(162)) / 4 * (-parseInt(t(159)) / 5) + -parseInt(t(166)) / 6 + -parseInt(t(163)) / 7 + -parseInt(t(165)) / 8 * (parseInt(t(164)) / 9) + parseInt(t(157)) / 10;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(zx, 506062);
const qe = Nx;
(function(n, e) {
  const t = Nx, x = n();
  for (; []; )
    try {
      if (-parseInt(t(446)) / 1 * (-parseInt(t(453)) / 2) + -parseInt(t(458)) / 3 + parseInt(t(443)) / 4 * (-parseInt(t(447)) / 5) + parseInt(t(444)) / 6 * (-parseInt(t(449)) / 7) + parseInt(t(459)) / 8 * (-parseInt(t(456)) / 9) + parseInt(t(454)) / 10 * (-parseInt(t(457)) / 11) + parseInt(t(460)) / 12 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Rx, 113772);
function Nx(n, e) {
  const t = Rx();
  return Nx = function(x, s) {
    return x = x - 443, t[x];
  }, Nx(n, e);
}
function Rx() {
  const n = ["type", "4496drFlpe", "5000KmGHGd", "length", "18zuBtpU", "3047NJfEEl", "140604pjXuSO", "545080jfJwYO", "7462800PiYyyB", "4Zlkgax", "30Puowwf", "listeners", "1JSEJeK", "650365hWtSwn", "dispatchEvent", "82131rdMmNf", "removeEventListener", "addEventListener"];
  return Rx = function() {
    return n;
  }, Rx();
}
class Tt {
  constructor() {
    this.listeners = {};
  }
  hasListener(e) {
    return e in this.listeners;
  }
  [qe(451)](e, t) {
    !(e in this[qe(445)]) && (this.listeners[e] = []), this.listeners[e].push(t);
  }
  [qe(450)](e, t) {
    const x = qe;
    if (!(e in this[x(445)]))
      return;
    let s = this.listeners[e];
    for (var i = 0, r = s[x(455)]; i < r; i++)
      if (s[i] === t)
        return s.splice(i, 1), this.removeEventListener(e, t);
  }
  [qe(448)](e) {
    const t = qe;
    if (!(e.type in this.listeners))
      return;
    let x = this[t(445)][e[t(452)]];
    for (var s = 0, i = x[t(455)]; s < i; s++)
      x[s].call(this, e);
  }
  on(e, t) {
    return this.addEventListener(e, t);
  }
}
function Wx(n, e) {
  const t = Hx();
  return Wx = function(x, s) {
    return x = x - 229, t[x];
  }, Wx(n, e);
}
function Hx() {
  const n = ["min", "740yDdHSI", "rotateNormaledPoints", "MIN_SAFE_INTEGER", "12313XrTGmy", "length", "25374NYHUKW", "removeAt", "notContains", "360QIRtYo", "327501ZWcEsh", "getPointsRect", "getPointsNormalization", "1QWDpeC", "1609988KJEpoD", "union", "forEach", "width", "slice", "1384VJotaQ", "2131638rQvBUJ", "175835ezJduT", "remove", "splice", "2793354XbJgOR", "4092ZEhBZj"];
  return Hx = function() {
    return n;
  }, Hx();
}
const Ot = Wx;
(function(n, e) {
  const t = Wx, x = n();
  for (; []; )
    try {
      if (parseInt(t(230)) / 1 * (-parseInt(t(241)) / 2) + -parseInt(t(237)) / 3 + -parseInt(t(231)) / 4 + parseInt(t(244)) / 5 * (-parseInt(t(249)) / 6) + -parseInt(t(247)) / 7 * (parseInt(t(236)) / 8) + parseInt(t(253)) / 9 * (-parseInt(t(252)) / 10) + -parseInt(t(238)) / 11 * (-parseInt(t(242)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Hx, 700962);
class N {
  static hasChild(e, t) {
    return e.indexOf(t) != -1;
  }
  static [Ot(251)](e, t) {
    return e.indexOf(t) == -1;
  }
  static addAll(e, t) {
    for (var x = 0; x < t.length; x++)
      e.push(t[x]);
    return e;
  }
  static [Ot(250)](e, t) {
    return e.splice(t, 1);
  }
  static [Ot(239)](e, t) {
    const x = Ot;
    let s = e.indexOf(t);
    return s == -1 ? -1 : (e[x(240)](s, 1), s);
  }
  static [Ot(254)](e) {
    const t = Ot;
    let x = Number.MAX_SAFE_INTEGER, s = Number.MAX_SAFE_INTEGER, i = Number.MIN_SAFE_INTEGER, r = Number[t(246)];
    return e[t(233)](function(o) {
      o.x < x && (x = o.x), o.y < s && (s = o.y), o.x > i && (i = o.x), o.y > r && (r = o.y);
    }), new z(x, s, i - x, r - s);
  }
  static getRectsNormalization(e, t) {
    const x = Ot;
    let s = new z();
    s.setTo(e[0].x, e[0].y, e[0].width, e[0].height);
    for (let l = 1; l < e.length; l++)
      z[x(232)](s, e[l]);
    let i = s[x(234)], r = s.height, o = s.x, a = s.y;
    return e.map((l) => {
      let u = l[x(234)] / i, p = l.height / r, b = (l.x - o) / i, _ = (l.y - a) / r, m = u * t.width, g = p * t.height, w = t.x + b * t.width, O = t.y + _ * t.height;
      return new z(w, O, m, g);
    });
  }
  static getMinMax(e) {
    const t = Ot;
    let x = { x: e[0].x, y: e[0].y }, s = { x: e[0].x, y: e[0].y };
    for (let i = 1; i < e[t(248)]; i++) {
      let r = e[i];
      x.x = Math[t(243)](x.x, r.x), x.y = Math.min(x.y, r.y), s.x = Math.max(s.x, r.x), s.y = Math.max(s.y, r.y);
    }
    return { min: x, max: s };
  }
  static [Ot(229)](e, t = -0.5, x = -0.5, s = !![]) {
    let i = N.getMinMax(e), r = i.min, o = i.max, a = o.x - r.x, c = o.y - r.y;
    if (a == c || s == ![])
      return e.map((p) => ({ x: (p.x - r.x) / a + t, y: (p.y - r.y) / c + x }));
    if (a > c) {
      let u = (a - c) * 0.5;
      return c = a, e.map((b) => ({ x: (b.x - r.x) / a + t, y: (b.y + u - r.y) / c + x }));
    }
    let l = (c - a) * 0.5;
    return a = c, e.map((u) => ({ x: (u.x + l - r.x) / a + t, y: (u.y - r.y) / c + x }));
  }
  static [Ot(245)](e, t, x = 0, s = 0) {
    const i = Ot;
    if (t == 0 || t % 6.283185307179586 == 0)
      return e[i(235)]();
    let r = e.map((o) => E.rotate(o.x, o.y, x, s, t));
    return N.getPointsNormalization(r);
  }
}
const At = Ce;
function Fx() {
  const n = ["100092KBofWJ", "whenAllLoaded", "154rZWWkK", "imageCache", "addToCallbackList", "1021980hfbYOf", "callbackCache", "1214823ccwgOz", "allLoadedResolve", "loadImageWithObj", "naturalWidth", "set", "showImgLoadErrorLog", "1268924hxCmBx", "image load error: ", "canceled", "error", "loadImage", "onload", "objCache", "加载完成: ", "complete", "next", "1026711wdVjqI", "50EAHnRG", "712748RpMSXI", "cancelCallback", "onerror", "1llmQAw", "clear", "721632qpataj"];
  return Fx = function() {
    return n;
  }, Fx();
}
(function(n, e) {
  const t = Ce, x = n();
  for (; []; )
    try {
      if (-parseInt(t(365)) / 1 * (-parseInt(t(362)) / 2) + parseInt(t(375)) / 3 + parseInt(t(381)) / 4 + parseInt(t(373)) / 5 + parseInt(t(368)) / 6 * (-parseInt(t(370)) / 7) + -parseInt(t(367)) / 8 + parseInt(t(360)) / 9 * (-parseInt(t(361)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Fx, 255339);
class Qr extends Tt {
  constructor() {
    const e = Ce;
    super(), this[e(380)] = !![], this.imageCache = /* @__PURE__ */ new Map(), this.callbackCache = /* @__PURE__ */ new Map(), this.objCache = /* @__PURE__ */ new Map();
  }
  errorLog(e) {
    this.showImgLoadErrorLog && console.error(e);
  }
  [At(369)]() {
    let e = this;
    return new Promise((t, x) => {
      const s = Ce;
      e[s(376)] = t;
    });
  }
  whenAllImagesLoaded(e, t = ![]) {
    const x = At, s = this[x(371)], i = this;
    function r(a) {
      return new Promise((c, l) => {
        const h = Ce;
        let u = new Image();
        u.src = a, t && console.log("开始加载: ", a), u[h(355)] = function() {
          const p = h;
          t && console.log(p(357), a), u[p(358)] && u[p(378)] > 0 ? (s[p(379)](a, u), c(u)) : i.errorLog("图片加载失败: " + a);
        }, u[h(364)] = function() {
          const p = h;
          i.errorLog(p(351) + a), c(null);
        };
      });
    }
    let o = e.map(r);
    return Promise.all(o);
  }
  clearCache() {
    const e = At;
    this[e(371)].clear(), this.callbackCache[e(366)](), this.objCache.clear();
  }
  removeObject(e) {
    this.objCache.delete(e);
  }
  [At(363)](e) {
    let x = this[At(356)].get(e);
    x != null && (x.canceled = !![], this.objCache.delete(e));
  }
  onload(e, t) {
    const x = At, s = this, i = this[x(374)].get(e);
    if (i == null)
      return;
    for (let o = 0; o < i.length; o++) {
      let a = i[o];
      a[x(352)] != !![] && (a(t), a.cacheObj && this.objCache.delete(a.cacheObj), a.next && a[x(359)](t));
    }
    this.callbackCache.delete(e), t != null && this.imageCache[x(379)](e, t), this.hasLoaded = !![], this.dispatchEvent({ type: "loaded", resource: t }), this.callbackCache.size == 0 && this[x(376)] && (s.allLoadedResolve(t), s.allLoadedResolve = null);
  }
  [At(372)](e, t) {
    if (t == null)
      return null;
    let x = this.callbackCache.get(e);
    x == null && (x = [], this.callbackCache.set(e, x)), x.push(t);
  }
  [At(377)](e, t, x) {
    const s = At;
    let i = this[s(356)].get(e);
    return i != null && (i.canceled = !![]), x.cacheObj = e, x.canceled = null, this.objCache.set(e, x), this[s(354)](t, x);
  }
  loadImage(e, t) {
    const x = At;
    let s = this, i = this[x(374)].get(e), r = i != null;
    if (s.lastResource = e, this.addToCallbackList(e, t), r)
      return;
    let o = this.imageCache.get(e);
    if (o != null) {
      this.onload(e, o);
      return;
    }
    setTimeout(function() {
      const a = x;
      let c = new Image();
      c.src = e, c.addEventListener("load", (l) => {
        s.onload(e, c);
      }), c[a(364)] = function() {
        console[a(353)]("Image load error: " + e), s.onload(e, null);
      };
    }, 1);
  }
}
function Ce(n, e) {
  const t = Fx();
  return Ce = function(x, s) {
    return x = x - 351, t[x];
  }, Ce(n, e);
}
const et = new Qr();
function a0(n, e) {
  const t = Xx();
  return a0 = function(x, s) {
    return x = x - 352, t[x];
  }, a0(n, e);
}
(function(n, e) {
  const t = a0, x = n();
  for (; []; )
    try {
      if (parseInt(t(352)) / 1 + parseInt(t(366)) / 2 * (-parseInt(t(363)) / 3) + parseInt(t(356)) / 4 + -parseInt(t(354)) / 5 + parseInt(t(358)) / 6 * (parseInt(t(353)) / 7) + -parseInt(t(362)) / 8 + parseInt(t(355)) / 9 * (parseInt(t(364)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Xx, 679581);
function Xx() {
  const n = ["4049415dKkOAs", "715671xJraSW", "3292020iBxFrO", "name", "30oytray", "className", "concat", "ArrowNode", "4156352vYNJEm", "2991171osNyQz", "10MnQNov", "Node", "2vHcDvg", "842361kwUbLx", "1765652nbhUVh"];
  return Xx = function() {
    return n;
  }, Xx();
}
function $r(n, e, t) {
  return typeof n == "function" ? Ci(n[a0(357)], n, e) : Ci(n, e, t);
}
function Ci(n, e, t) {
  const x = a0;
  if (kt[n] != null)
    throw new Error("class already reg, name:" + n);
  const s = e.prototype;
  t != null && t.length > 0 && (s.serializers = s.serializers[x(360)](t)), Object.defineProperties(s, { className: { writable: !![] } }), s[x(359)] = n, Object.defineProperties(s, { className: { writable: ![] } }), kt[n] = e;
}
function mi(n) {
  const e = a0;
  let t = kt[n];
  if (t == null)
    throw (n == e(361) || n == "ShapeNode") && (t = kt[e(365)]), new Error("class not exist name:" + n);
  return t;
}
function hx(n, e) {
  let t, x = mi(n);
  return t = new x(), e && Object.assign(t, e), t;
}
const P = c0;
(function(n, e) {
  const t = c0, x = n();
  for (; []; )
    try {
      if (parseInt(t(375)) / 1 + parseInt(t(397)) / 2 * (parseInt(t(369)) / 3) + parseInt(t(367)) / 4 * (-parseInt(t(399)) / 5) + parseInt(t(378)) / 6 + -parseInt(t(406)) / 7 + -parseInt(t(400)) / 8 + parseInt(t(416)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Yx, 785454);
function Yx() {
  const n = ["Arrow", "Ellipse", "6224632oTgPbA", "isClosed", "3GTzheR", "TipShape", "width", "_computedStyle", "draw", "rect", "653331ObhUAg", "anchorPoints", "ctrlPoint1", "3593472tscJkj", "begin", "step", "height", "BezierCurve", "ArrowShape", "forEach", "points", "Polygon", "circlePoints", "atan2", "className", "tip", "moveTo", "pointCount", "CurveShape", "cos", "fromPoints", "scale", "1350084KGHoGD", "beginPath", "5iVPajQ", "8735856ZoCaFx", "end", "normalPoints", "Arc", "lineTo", "center is zero vector", "3277897gSEUlR", "Curve", "anticlockwise", "push", "CircleShape", "innerGrid", "length", "arc", "closePath", "rotate", "17771220nIfcbe", "distancePoint", "BezierCurveShape", "prototype", "dirty"];
  return Yx = function() {
    return n;
  }, Yx();
}
var to = Object.defineProperty, eo = Object.getOwnPropertyDescriptor, W = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? eo(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && to(e, t, s), s;
};
const st = class {
  constructor(n) {
    this.isClosed = !![], this.points = n;
  }
  updatePoints(n) {
    this.points = n, this.dirty = !![];
  }
  toJSON() {
    const n = c0;
    let e = { className: this[n(389)] };
    return e[n(385)] = this[n(385)], e.isClosed = this.isClosed, e;
  }
  clone() {
    return new st(this.points.slice());
  }
  static fromJSON(n) {
    const e = c0;
    let t = mi(n[e(389)]), x = new t(n[e(385)]);
    return x.isClosed = n.isClosed, x;
  }
  static [P(395)](n, e = !![]) {
    let t = N.getPointsNormalization(n, -0.5, -0.5, e);
    return new st(t);
  }
  static [P(402)](n, e = !![]) {
    return N.getPointsNormalization(n, -0.5, -0.5, e);
  }
  draw(n, e, t) {
    const x = P;
    t = t || this.points;
    let s = t[0];
    n.moveTo(s.x, s.y);
    for (let r = 1; r < t.length - 1; r++) {
      if (t[r].x === s.x && t[r].y === s.y) {
        s = t[r];
        continue;
      }
      n.lineTo(t[r].x, t[r].y), s = t[r];
    }
    let i = t[t[x(412)] - 1];
    n.lineTo(i.x, i.y), this.isClosed && n.closePath();
  }
  rotate(n) {
    const e = P;
    return this.points = N.rotateNormaledPoints(this.points, n), this[e(420)] = !![], this;
  }
  [P(396)](n, e) {
    const t = P;
    return this[t(385)].forEach((x) => {
      x.x *= n, x.y *= e;
    }), this[t(385)] = N.getPointsNormalization(this.points), this[t(420)] = !![], this;
  }
  static Scale(n, e) {
    return function(x) {
      return x.x *= n, x.y *= e, x;
    };
  }
  skew(n, e) {
    return this.points.forEach((t) => {
      let x = t.x, s = t.y;
      t.x = x + s * e, t.y = s + x * n;
    }), this.points = N.getPointsNormalization(this.points), this.dirty = !![], this;
  }
  static pointToSize(n, e, t, x = ![]) {
    return x == !![] && e != t && (e > t ? e = t : t = e), n.map((i) => ({ x: i.x * e, y: i.y * t }));
  }
  static polygon(n = 3, e = 0) {
    const t = P;
    let x = [], s = 2 * Math.PI / n;
    for (var i = 0; i < n; i++) {
      let o = Math[t(394)](e + i * s), a = Math.sin(e + i * s);
      x.push({ x: o, y: a });
    }
    return st[t(395)](x);
  }
  static parallelogram(n = 0.2) {
    let e = [{ x: n, y: 0 }, { x: 1, y: 0 }, { x: 1 - n, y: 1 }, { x: 0, y: 1 }];
    return st.fromPoints(e);
  }
  static cos(n) {
    const e = P;
    n[e(379)] = n.begin | 0, n.end = n[e(401)] | V0;
    let t = [];
    if (n.pointCount == null) {
      n.step = n.step | 0.2;
      for (let x = n.begin; x <= n.end; x += n.step) {
        let s = x;
        t.push({ x: s, y: Math.cos(s) });
      }
    } else {
      n.step = (n.end - n.begin) / n[e(392)];
      for (let x = 0; x < n[e(392)]; x++) {
        let s = (x + 1) * n.step;
        t.push({ x: s, y: Math.cos(s) });
      }
    }
    return st.fromPoints(t);
  }
  static circle(n) {
    const e = P;
    let t = st.circlePoints(n);
    return st[e(395)](t);
  }
  static [P(387)](n) {
    const e = P;
    n.begin = n.begin || 0, n[e(401)] = n.end || V0;
    let t = [];
    if (n.pointCount == null) {
      n.step = n.step | 0.2;
      for (let x = n.begin; x <= n.end; x += n[e(380)]) {
        let s = x;
        t[e(409)]({ x: Math.sin(s), y: Math.cos(s) });
      }
    } else {
      let x = (n.end - n[e(379)]) / n[e(392)];
      for (let s = 0; s < n[e(392)]; s++) {
        let i = s * x;
        t.push({ x: Math.sin(i), y: Math.cos(i) });
      }
    }
    return t;
  }
  static fn(n) {
    const e = P;
    let t = n();
    return st[e(395)](t);
  }
  static outerGrid(n, e) {
    const t = P;
    let x = [], s = 1 / (e - 1), i = 1 / (n - 1);
    for (let r = 0; r < n; r++)
      for (let o = 0; o < e; o++) {
        let a = { y: r * i - 0.5, x: o * s - 0.5 };
        x[t(409)](a);
      }
    return new st(x);
  }
  static [P(411)](n, e) {
    let t = [], x = 1 / (e + 1), s = 1 / (n + 1), i = x, r = s;
    for (let o = 0; o < n; o++)
      for (let a = 0; a < e; a++) {
        let c = { x: i + a * x - 0.5, y: r + o * s - 0.5 };
        t.push(c);
      }
    return new st(t);
  }
};
let T = st;
T.Damond = (() => {
  let n = [{ x: -0.5, y: 0 }, { x: 0, y: -0.5 }, { x: 0.5, y: 0 }, { x: 0, y: 0.5 }];
  return new st(n);
})(), T.Triangle = (() => {
  let n = [{ x: -0.5, y: -0.5 }, { x: 0.5, y: 0 }, { x: -0.5, y: 0.5 }];
  return new st(n);
})(), T[P(390)] = (() => {
  let n = [{ x: 0, y: 0 }, { x: 1, y: 0 }, { x: 1, y: 0.8 }, { x: 0.8, y: 0.8 }, { x: 0.5, y: 1 }, { x: 0.2, y: 0.8 }, { x: 0, y: 0.8 }, { x: 0, y: 0 }];
  return st.fromPoints(n);
})(), W([f("Shape")], T.prototype, "className", 2), W([f(["ct", "cb", "lm", "rm"])], T.prototype, "anchorPoints", 2), W([f([P(415), "lt", "lb", "rt", "rb"])], T.prototype, "ctrlPoints", 2), W([f(!![])], T.prototype, P(368), 2), W([f(!![])], T[P(419)], "isUnit", 2), W([f(![])], T.prototype, P(420), 2), T[P(386)] = T.polygon;
class D0 extends T {
  constructor(e = [{ x: -0.5, y: -0.5 }, { x: 0.4, y: 0 }, { x: -0.5, y: 0.5 }, { x: -0.5, y: 0 }]) {
    super(e);
  }
  draw(e, t, x) {
    const s = P;
    x = x || this[s(385)], e[s(391)](x[0].x, x[0].y), e.lineTo(x[1].x, x[1].y), e.lineTo(x[2].x, x[2].y), e.lineCap = "square";
    let i = t[s(372)].lineWidth || 1, r = i * 0.5 / t[s(371)];
    r > 1 && (r = 1), e[s(391)](x[3].x - r, x[3].y), e[s(404)](x[1].x - r, x[1].y);
  }
}
W([f(P(383))], D0[P(419)], "className", 2), W([f(![])], D0.prototype, P(368), 2), kt.ArrowShape = D0, T[P(421)] = new D0(), T.ArrowShape = T.Arrow;
function c0(n, e) {
  const t = Yx();
  return c0 = function(x, s) {
    return x = x - 367, t[x];
  }, c0(n, e);
}
class vx extends T {
  constructor(e = [{ x: -0.5, y: -0.5 }, { x: 0.5, y: -0.5 }, { x: 0.5, y: 0.5 }, { x: -0.5, y: 0.5 }]) {
    super(e);
  }
  [P(373)](e, t) {
    e[P(374)](-0.5, -0.5, 1, 1);
  }
}
W([f("RectShape")], vx.prototype, "className", 2), kt.RectShape = vx, T.Rect = new vx(), T.RectShape = T.Rect;
class si extends T {
  constructor(e = [{ x: 0, y: 0 }]) {
    super(e);
  }
  draw(e, t) {
    const x = P;
    let s = this.points[0];
    e[x(413)](s.x, s.y, 0.5, 0, V0);
  }
}
W([f(P(410))], si[P(419)], P(389), 2), T.Circle = new si();
class ii extends T {
  constructor(e = [{ x: 0, y: 0 }, { x: -0.5, y: 0 }, { x: -0.5, y: 0 }]) {
    super(e);
  }
  draw(e, t) {
    const x = P;
    let s = this[x(385)][0], i = this.points[1], r = this.points[2], o = E[x(417)](s, i), a = E.distancePoint(s, r);
    e.ellipse(s.x, s.y, o, a, 0, 0, V0);
  }
}
W([f("EllipseShape")], ii[P(419)], "className", 2), T[P(422)] = new ii();
class B0 extends T {
  constructor(e = []) {
    const t = P;
    super(e), this[t(368)] = ![];
  }
}
W([f("LineShape")], B0.prototype, "className", 2), W([f(["begin", "end"])], B0.prototype, "anchorPoints", 2), W([f(["begin", "end"])], B0[P(419)], "ctrlPoints", 2), T.Line = new B0();
class j0 extends T {
  constructor(e = []) {
    super(e), this.isClosed = ![];
  }
  draw(e, t, x) {
    let s = x[0], i = x[1], r = x[2];
    e.moveTo(s.x, s.y), e.quadraticCurveTo(i.x, i.y, r.x, r.y), this.isClosed && e.closePath();
  }
}
W([f(P(393))], j0.prototype, "className", 2), W([f(["begin", "end"])], j0[P(419)], "anchorPoints", 2), W([f(["begin", "end"])], j0.prototype, "ctrlPoints", 2), T[P(407)] = new j0();
class z0 extends T {
  constructor(e = []) {
    super(e), this.isClosed = ![];
  }
  draw(e, t, x) {
    const s = P;
    let i = x[0], r = x[1], o = x[3], a = x[4];
    e[s(398)](), e[s(391)](i.x, i.y), e.bezierCurveTo(r.x, r.y, o.x, o.y, a.x, a.y), this[s(368)] && e[s(414)]();
  }
}
W([f(P(418))], z0.prototype, P(389), 2), W([f([P(379), "end"])], z0.prototype, P(376), 2), W([f(["begin", "end", P(377), "ctrlPoint2"])], z0.prototype, "ctrlPoints", 2), T[P(382)] = new z0();
class N0 extends T {
  constructor(e = []) {
    super(e), this.isClosed = ![];
  }
  draw(e, t, x) {
    const s = P;
    let i = x[0], r = x[x.length - 1];
    if (t && t.direction == s(408)) {
      let o = Math[s(388)](i.y - r.y, i.x - r.x);
      e[s(413)](0, 0, 0.5, o, o + Math.PI, !![]);
    } else {
      let o = Math.atan2(i.y - r.y, i.x - r.x);
      e.arc(0, 0, 0.5, o, o + Math.PI, ![]);
    }
    this[s(368)] && e.closePath();
  }
}
W([f("ArcShape")], N0.prototype, P(389), 2), W([f(["begin", P(401)])], N0.prototype, P(376), 2), W([f(["begin", "end"])], N0.prototype, "ctrlPoints", 2), T[P(403)] = new N0();
const xo = T.outerGrid(3, 3)[P(385)], no = [k.lt, k.ct, k.rt, k.lm, k.center, k.rm, k.lb, k.cb, k.rb], de = {};
no[P(384)]((n, e) => {
  de[n] = xo[e];
}), de[k.nearest] = function() {
  return this.nearest;
};
function Qi(n) {
  const e = P;
  if (n == "center")
    throw new Error(e(405));
  let t = de[n];
  return B.normalize([], [t.x, t.y]);
}
function $i(n) {
  let e = de[n];
  return Math.atan2(e.y, e.x);
}
class $e extends T {
  constructor(e = []) {
    const t = P;
    super(e), this[t(368)] = !![];
  }
  draw(e, t) {
    const x = P;
    let s = t, i = -s.width * 0.5, r = -s[x(381)] * 0.5, o = s[x(371)], a = s.arrowsSize, c = s.height - a, l = 0;
    e.moveTo(i, r), e.lineTo(i + o, r), e.lineTo(i + o, r + c), e.lineTo(l + (a - 2), r + c), e.lineTo(l, r + c + a), e.lineTo(l - (a - 2), r + c), e.lineTo(i, r + c), e.lineTo(i, r);
  }
}
W([f(P(370))], $e.prototype, P(389), 2), W([f(![])], $e[P(419)], "isUnit", 2), W([f([])], $e.prototype, "anchorPoints", 2), W([f([])], $e.prototype, "ctrlPoints", 2);
(function(n, e) {
  const t = Vx, x = n();
  for (; []; )
    try {
      if (-parseInt(t(437)) / 1 + parseInt(t(445)) / 2 * (-parseInt(t(447)) / 3) + -parseInt(t(448)) / 4 + parseInt(t(440)) / 5 + -parseInt(t(443)) / 6 + parseInt(t(442)) / 7 + parseInt(t(438)) / 8 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Jx, 498349);
function Jx() {
  const n = ["_afterFromJSON", "push", "begin", "styles", "Shapes", "610749ijTHXu", "26450248tRDRFY", "forEach", "911825Lhvmwh", "get", "1222823hbHdRy", "5826096bABrUj", "DisplayObjects", "198634gmlJxV", "hasOwnProperty", "27bQboga", "2757472DiIycj", "userData", "fromJSON"];
  return Jx = function() {
    return n;
  }, Jx();
}
function Vx(n, e) {
  const t = Jx();
  return Vx = function(x, s) {
    return x = x - 435, t[x];
  }, Vx(n, e);
}
function qs(n, e) {
  const t = Vx;
  let x = {}, s = {}, i = {}, r = n.Roots || [0], o = n[t(444)] || [], a = n.Styles || [], c = n[t(436)] || [], l = n.Resources || [], h = n.CustomStyle || {};
  if (o.forEach((g) => {
    const w = t;
    if (g[w(446)](w(449)) && !g[w(446)]("data")) {
      let O = g.userData;
      delete g.userData, g.data = O;
    }
    g.isLink && g.path == null && (g.path = [g.begin, g.end], delete g[w(453)], delete g.end);
  }), h[t(435)] == null) {
    let g = {}, w = Object.keys(h);
    for (let O = 0; O < w.length; O++) {
      let A = w[O];
      A.startsWith(".") && (g[A] = h[A], delete h[A]);
    }
    h.styles = g;
  }
  a.forEach(function(g, w) {
    const O = t;
    s[w] = ct[O(450)](g, l);
  }), c[t(439)](function(g, w) {
    let A = T[t(450)](g);
    i[w] = A;
  });
  let u = [];
  function p(g, w) {
    const O = t;
    let A;
    return e && (A = e[O(441)](g.id)), A == null && (A = hx(g.className)), w < r.length && u[O(452)](A), A[O(451)](g, l, s, i), x[w] = A, A;
  }
  let b = o.map(p);
  b.forEach((g) => {
    g.removeAllChild();
  }), o.forEach((g, w) => {
    let O = b[w];
    if (g.parent != null) {
      let A = x[g.parent];
      O.parent != null && O.removeFromParent(), A.addChild(O);
    }
  });
  let _ = o.filter((g) => g.isLink), m = b.filter((g) => g.isLink);
  return _.forEach(function(g, w) {
    m[w]._updateBeginEndAfterJson(g, x);
  }), u;
}
const so = Gx;
(function(n, e) {
  const t = Gx, x = n();
  for (; []; )
    try {
      if (parseInt(t(217)) / 1 * (-parseInt(t(213)) / 2) + -parseInt(t(212)) / 3 + parseInt(t(219)) / 4 * (-parseInt(t(216)) / 5) + -parseInt(t(214)) / 6 * (parseInt(t(220)) / 7) + parseInt(t(211)) / 8 * (-parseInt(t(221)) / 9) + -parseInt(t(210)) / 10 + parseInt(t(215)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(qx, 859977);
function Gx(n, e) {
  const t = qx();
  return Gx = function(x, s) {
    return x = x - 210, t[x];
  }, Gx(n, e);
}
let L0 = 0, wt = {};
function qx() {
  const n = ["18891WRUkXU", "2304080OefJWQ", "1160SfeHxe", "4427916ebwpwm", "9554VCsqUw", "837006GSjTKw", "45528857laTQZQ", "406355KJsLBC", "88RmRpki", "getMax", "28lQCxZY", "14LxARtO"];
  return qx = function() {
    return n;
  }, qx();
}
wt.next = function() {
  return ++L0;
}, wt.back = function() {
  return --L0;
}, wt[so(218)] = function() {
  return L0;
}, wt.resetTo = function(n) {
  L0 = n;
}, wt.compare = function(n) {
  n != null && n > L0 && wt.resetTo(n + 1);
};
const Xt = q0;
(function(n, e) {
  const t = q0, x = n();
  for (; []; )
    try {
      if (parseInt(t(524)) / 1 * (parseInt(t(514)) / 2) + -parseInt(t(511)) / 3 + -parseInt(t(510)) / 4 + parseInt(t(501)) / 5 * (parseInt(t(515)) / 6) + parseInt(t(504)) / 7 + -parseInt(t(525)) / 8 * (-parseInt(t(498)) / 9) + -parseInt(t(506)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ux, 517462);
function q0(n, e) {
  const t = Ux();
  return q0 = function(x, s) {
    return x = x - 497, t[x];
  }, q0(n, e);
}
let io = /* @__PURE__ */ new Map();
function Ux() {
  const n = ["Resources", "catch", "3367784YyPtHs", "DisplayObjects", "6440610msuhoW", "styleSystem", "serializers", "compare", "1547620wAIAxI", "2005455VtosIP", "copyAndToJSON", "forEach", "113742wNVpck", "6PzAHYW", "restoreToJson", "get", "length", "CustomStyle", "img", "whenAllImagesLoaded", "objectsToJSON", "className", "11vurwcA", "2590232OSQbce", "getState", "9rqstmw", "fromJson", "stringify", "3932205nAvwDc"];
  return Ux = function() {
    return n;
  }, Ux();
}
const tr = class {
  constructor() {
  }
  setNumberFixed(n) {
    tr.numberFixed = n;
  }
  [Xt(522)](n, e = ![]) {
    return Us(n, e);
  }
  jsonToObjects(n, e) {
    return qs(n, e);
  }
  [Xt(512)](n) {
    const e = Xt;
    let t = Us(n);
    return t.DisplayObjects[e(513)]((x) => {
      x.id = void 0;
    }), t;
  }
  static getEmptyInstance(n) {
    let t = io[Xt(517)](n);
    return t == null && (t = hx(n)), t;
  }
  static getProtoDefaultProperties(n) {
    let t = n[Xt(508)], x = Object.getPrototypeOf(n), s = {};
    for (var i = 0; i < t.length; i++) {
      let r = t[i], o = x[r], a = n[r];
      a === o && (s[r] = a);
    }
    return s;
  }
  componentToObjects(n) {
    const e = Xt;
    let t = JSON.parse(n);
    return t[e(505)].forEach((i) => {
      delete i.id;
    }), qs(t);
  }
  objectsToComponent(n) {
    const e = Xt;
    let t = this.copyAndToJSON(n);
    return JSON[e(500)](t);
  }
  fillByJson(n, e, t = ![]) {
    const x = Xt;
    let s = e;
    typeof e == "string" && (s = JSON.parse(e));
    let i = this;
    function r() {
      const o = q0;
      let a = s.DisplayObjects, c = a[0];
      c[o(523)] == "Layer" && (n.id = c.id), a[o(513)]((l) => wt[o(509)](l.id)), s[o(519)] != null && n.stage[o(507)][o(499)](s), i[o(516)](n, s);
    }
    if (t) {
      let o = s[x(502)], a = o.filter((c) => c.type == x(520) && c.src != "canvas").map((c) => c.src);
      return new Promise((c, l) => {
        const h = x;
        if (a[h(518)] == 0) {
          r(), c(!![]);
          return;
        }
        et[h(521)](a).then(() => {
          r(), c(!![]);
        })[h(503)](() => {
          l(![]);
        });
      });
    }
    return r(), null;
  }
  [Xt(497)](n) {
    return Us([n]);
  }
  restoreToJson(n, e) {
    if (e != null) {
      let t = n.toIdMap();
      qs(e, t);
    }
    return e;
  }
};
let n0 = tr;
n0.numberFixed = 6;
const Mt = Zx;
(function(n, e) {
  const t = Zx, x = n();
  for (; []; )
    try {
      if (-parseInt(t(269)) / 1 + -parseInt(t(276)) / 2 * (-parseInt(t(258)) / 3) + -parseInt(t(260)) / 4 + -parseInt(t(282)) / 5 * (-parseInt(t(281)) / 6) + parseInt(t(267)) / 7 + -parseInt(t(265)) / 8 + parseInt(t(270)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Kx, 315383);
const Ei = document.createElement("canvas"), zt = class {
  static [Mt(284)](n, e, t, x, s = Mt(273), i = "#151515") {
    const r = Mt;
    let o = "<svg xmlns='http://www.w3.org/2000/svg' width='" + n + "' height='" + e + r(283) + n + "' height='" + e + "' style='fill:" + s + ";stroke:" + i + `;stroke-width:1;'/>
<g style='stroke:` + i + "; stroke-width:0.5;'>", a = e / t, c = n / x, l = "";
    for (let u = 1; u <= t; u++)
      l += "M 0 " + a * u + " H " + n + " ";
    o += "<path d='" + l + r(275);
    let h = "";
    for (let u = 1; u <= x; u++)
      h += "M " + c * u + " 0 V " + e + " ";
    return o += "<path d='" + h + "'/>", o += "</g></svg>", o = o.replace(/\n/g, ""), o;
  }
  static createGridImage(n = 100, e = 100, t = 5, x = 5, s, i) {
    let o = zt[Mt(284)](n, e, t, x, s, i);
    return 'url("' + zt.svgToImageUrl(o) + '")';
  }
  static createLightGridImg() {
    const n = Mt;
    return zt.createGridImage(100, 100, 5, 5, "rgb(255,255,255)", n(278));
  }
  static [Mt(261)]() {
    return zt.createGridImage(100, 100, 5, 5, "rgb(36,36,36)", "rgb(20,20,20)");
  }
  static svgToImageUrl(n) {
    return "data:image/svg+xml;charset=UTF-8," + n;
  }
  static canvasColorFilter(n, e) {
    const t = Mt, x = n.getContext("2d"), s = x[t(264)](0, 0, n.width, n.height);
    let i = (o, a, c, l) => [e[0] * o / 255, e[1] * a / 255, e[2] * c / 255];
    typeof e == "function" && (i = e);
    for (var r = 0; r < s[t(266)].length; r += 4) {
      let o = i(s.data[r], s.data[r + 1], s.data[r + 2], s.data[r + 3]);
      s.data[r] = o[0], s.data[r + 1] = o[1], s[t(266)][r + 2] = o[2], o[t(277)] > 3 && o[3] != null && (s.data[r + 3] = o[3]);
    }
    x.putImageData(s, 0, 0);
  }
  static colorFilter(n, e) {
    const t = Mt, x = zt.canvas, s = zt.ctx;
    x.width = n[t(259)], x[t(274)] = n.height, s.drawImage(n, 0, 0), zt.canvasColorFilter(x, e);
    const i = new Image();
    return i[t(268)] = x.toDataURL(t(279)), i;
  }
  static imageToBase64(n) {
    const e = Mt, t = zt.canvas, x = zt.ctx;
    return t[e(259)] = n[e(259)], t.height = n.height, x.drawImage(n, 0, 0), t.toDataURL();
  }
  static parseImgUrl(n) {
    const e = Mt;
    if (n.startsWith(e(280)))
      return n;
    if (n.startsWith("url(")) {
      let t = n.match(/url\((['"]?)(.*?)\1\)/);
      if (t)
        return t[2];
      throw new Error("Image url error: " + n);
    }
    return n;
  }
};
function Zx(n, e) {
  const t = Kx();
  return Zx = function(x, s) {
    return x = x - 258, t[x];
  }, Zx(n, e);
}
let Ht = zt;
Ht.canvas = Ei, Ht.ctx = Ei.getContext("2d", { willReadFrequently: !![] });
function Kx() {
  const n = ["1469280hcZrIT", `'>
<rect x='0' y='0' width='`, "bgGrid", "57ZpwXzk", "width", "2206284DdXdqe", "createDarkGridImg", "split", "charCodeAt", "getImageData", "492160aOkfpH", "data", "1923754uqduhW", "src", "179813FLOPjn", "1898010QsGYqv", "match", "onload", "#242424", "height", "'/>", "34602PkjqrP", "length", "rgb(240,240,240)", "image/png", "data:image/", "6QsbRVy"];
  return Kx = function() {
    return n;
  }, Kx();
}
(function(n, e) {
  const t = be, x = n();
  for (; []; )
    try {
      if (parseInt(t(477)) / 1 + -parseInt(t(478)) / 2 + -parseInt(t(473)) / 3 * (-parseInt(t(467)) / 4) + -parseInt(t(463)) / 5 * (-parseInt(t(471)) / 6) + -parseInt(t(476)) / 7 * (-parseInt(t(480)) / 8) + parseInt(t(470)) / 9 + -parseInt(t(461)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Qx, 535513);
function be(n, e) {
  const t = Qx();
  return be = function(x, s) {
    return x = x - 460, t[x];
  }, be(n, e);
}
function ro(n, e = ![]) {
  const t = be;
  let x = C[t(464)](n[0].children);
  for (let b = 1; b < n.length; b++) {
    let _ = C.flatten(n[b].children, (m) => m.serializeable);
    x = x.concat(_);
  }
  let s = n[t(466)](x), i = /* @__PURE__ */ new Map(), r = /* @__PURE__ */ new Map(), o = /* @__PURE__ */ new Map(), a = [], c = [], l = /* @__PURE__ */ new Map(), h = [];
  function u(b, _) {
    let m = l[b];
    if (m == null) {
      let g = co(b, _, e), w = { type: "img", src: g };
      h.push(w), m = h.length - 1, l[b] = m;
    }
    return m;
  }
  return s.forEach((b, _) => {
    const m = t;
    b.isNode && b[m(465)] != null && u(b.imageSrc, b[m(469)]);
    let g = b.style;
    if (r[m(484)](g) == null) {
      let O = a[m(462)];
      r.set(g, O);
      let A = g.toJSON(u);
      a.push(A);
    }
    let w = b[m(483)];
    if (b[m(479)] && o[m(484)](w) == null) {
      let O = c.length;
      o.set(w, O), c.push(w.toJSON());
    }
    i.set(b, _);
  }), { objects: s, objIndexMap: i, styleIndexMap: r, styles: a, resourcesIndexMap: l, resources: h, shapeIndexMap: o, shapes: c, indexImage: u };
}
function Us(n, e = ![]) {
  const t = be, x = {};
  x.version = _i;
  const s = ro(n, e);
  let i = s.objects;
  if (x.Roots = n[t(485)]((r, o) => o), x.Styles = s.styles, x.Shapes = s.shapes, x.Resources = s.resources, x.DisplayObjects = i[t(485)](function(r) {
    return r.toJSON(s);
  }), n.length == 1 && n[0][t(460)]) {
    let r = n[0];
    x.CustomStyle = r[t(474)][t(472)].customStyleToJSON(s.indexImage);
  }
  return x;
}
function Qx() {
  const n = ["4935hKDYJd", "380027aQdbYv", "1444468JJRoDx", "isNode", "11184UxXPCL", "number", "isArray", "shape", "get", "map", "isLayer", "15408600RaBNEk", "length", "170035nzscEX", "flatten", "imageSrc", "concat", "4pUKRCf", "startsWith", "image", "4727205vrjxzR", "6PJIgEY", "styleSystem", "2621214uAPFvi", "stage", "toJSON"];
  return Qx = function() {
    return n;
  }, Qx();
}
function er(n, e, t) {
  const x = be;
  let s = {}, i = n0.getEmptyInstance(n.className);
  if (t)
    for (let r = 0; r < t.length; r++) {
      let o = t[r], a = n[o];
      s[o] = a;
    }
  for (let r = 0; r < e[x(462)]; r++) {
    let o = e[r], a = n[o];
    if (a != i[o]) {
      if (Array.isArray(a) && Array[x(482)](i[o])) {
        let c = i[o];
        if (!ao(c, a))
          s[o] = a;
        else
          continue;
      }
      if (a == null) {
        s[o] = a;
        continue;
      }
      typeof a == x(481) && n0.numberFixed != null && (a = oo(a, n0.numberFixed)), a[x(475)] != null ? a = a.toJSON() : E.isLikePoint(a) && (a = new E(a.x, a.y)), s[o] = a;
    }
  }
  return s;
}
function oo(n, e) {
  if (Number.isInteger(n))
    return n;
  let t = n.toString();
  return t.length - t.indexOf(".") - 1 > e && (n = n.toFixed(e), n = parseFloat(n)), n;
}
function ao(n, e) {
  const t = be;
  if (n === e)
    return !![];
  if (n[t(462)] != e.length)
    return ![];
  for (let x = 0; x < n.length; x++)
    if (n[x] != e[x])
      return ![];
  return !![];
}
function co(n, e, t = ![]) {
  const x = be;
  return n == null ? null : t == ![] || n[x(468)]("data:image/") ? n : Ht.imageToBase64(e);
}
const $x = tn;
(function(n, e) {
  const t = tn, x = n();
  for (; []; )
    try {
      if (parseInt(t(456)) / 1 + parseInt(t(455)) / 2 + -parseInt(t(458)) / 3 * (parseInt(t(443)) / 4) + -parseInt(t(449)) / 5 * (parseInt(t(444)) / 6) + parseInt(t(442)) / 7 * (-parseInt(t(457)) / 8) + -parseInt(t(448)) / 9 + -parseInt(t(441)) / 10 * (-parseInt(t(459)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(en, 113323);
function tn(n, e) {
  const t = en();
  return tn = function(x, s) {
    return x = x - 441, t[x];
  }, tn(n, e);
}
const lo = document.createElement($x(450)), C0 = lo[$x(446)]("2d");
class ri {
  constructor() {
  }
  static measureTextSize(e, t) {
    const x = $x;
    C0.font = t;
    const s = C0[x(447)](e), i = s[x(451)] + s.actualBoundingBoxDescent;
    return { width: s[x(454)] + s[x(445)], height: i };
  }
  static measureTextArraySize(e, t) {
    const x = $x;
    C0.font = t;
    let s = C0[x(447)](e[0]);
    for (let o = 0; o < e[x(453)]; o++) {
      let a = C0[x(447)](e[o]);
      a[x(452)] > s.width && (s = a);
    }
    const i = s.actualBoundingBoxAscent + s.actualBoundingBoxDescent;
    return { width: s.actualBoundingBoxRight + s.actualBoundingBoxLeft, height: i };
  }
}
function en() {
  const n = ["1032fnFRXM", "726QwPuBk", "143ZjYLBo", "221660mjdGVE", "11333dxVtaX", "1692kBzbDK", "775596jdgreh", "actualBoundingBoxLeft", "getContext", "measureText", "62127knqDot", "5xkOCwf", "canvas", "actualBoundingBoxAscent", "width", "length", "actualBoundingBoxRight", "231624qZRJeb", "156739hNPnYS"];
  return en = function() {
    return n;
  }, en();
}
function xn() {
  const n = ["36TGyIJU", "1246982snFWVE", "1797793tZOYZW", "90zRAhCi", "7745456bITPLm", "271546xiIcLv", "7aXasjI", "normal", "parseFontDesc", "1394140lwIeup", "split", "bold", "694388QPIabM", "size", "14077557WWLfow", "italic", "3BvZEIv", "setWeight", "length", "italicWeight", "boldWeight"];
  return xn = function() {
    return n;
  }, xn();
}
const Ie = U0;
function U0(n, e) {
  const t = xn();
  return U0 = function(x, s) {
    return x = x - 414, t[x];
  }, U0(n, e);
}
(function(n, e) {
  const t = U0, x = n();
  for (; []; )
    try {
      if (parseInt(t(430)) / 1 + parseInt(t(429)) / 2 + parseInt(t(423)) / 3 * (-parseInt(t(419)) / 4) + parseInt(t(416)) / 5 * (-parseInt(t(428)) / 6) + -parseInt(t(434)) / 7 * (parseInt(t(432)) / 8) + parseInt(t(421)) / 9 + -parseInt(t(431)) / 10 * (parseInt(t(433)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(xn, 948536);
class Se {
  constructor(e) {
    const t = U0;
    this[t(427)] = t(414), this.italicWeight = t(414), this.size = "10px", this.family = "sans-serif", e != null && this.parseFontDesc(e);
  }
  [Ie(415)](e) {
    const t = Ie, x = e.split(" ");
    x[t(425)] > 3 ? (this[t(427)] = x[0], this[t(426)] = x[1], this.size = x[2], this.family = x[3], (x[0] === "italic" || x[1] === "bold") && (this.boldWeight = x[1], this.italicWeight = x[0])) : (this[t(427)] = x[0], this[t(420)] = x[1], this.family = x[2]);
  }
  getFontWeight() {
    return this[Ie(427)] + " " + this.italicWeight;
  }
  [Ie(424)](e) {
    const t = Ie, x = e[t(417)](" ");
    x[t(425)] > 1 ? (this[t(427)] = x[0], this.italicWeight = x[1], (this.boldWeight === t(422) || this[t(426)] === "bold") && (this[t(427)] = x[1], this.italicWeight = x[0])) : e === t(418) ? this.boldWeight = t(418) : e === "italic" ? this.italicWeight = "italic" : this.boldWeight = "normal";
  }
  setFamily(e) {
    e === null || e === "" || (this.family = e);
  }
  setSize(e) {
    e === null || e === "" || (this.size = e);
  }
  setBold(e) {
    e === null || e === "" || (this.boldWeight = e);
  }
  setItalic(e) {
    e === null || e === "" || (this.italicWeight = e);
  }
  toogleBold() {
    const e = Ie;
    this.boldWeight === e(418) ? this.boldWeight = "normal" : this.boldWeight = "bold";
  }
  toogleItalic() {
    this[Ie(426)] === "italic" ? this.italicWeight = "normal" : this.italicWeight = "italic";
  }
  toStyleFont() {
    return this.boldWeight + " " + this.italicWeight + " " + this.size + " " + this.family;
  }
}
(function(n, e) {
  const t = ye, x = n();
  for (; []; )
    try {
      if (parseInt(t(345)) / 1 + parseInt(t(349)) / 2 * (parseInt(t(348)) / 3) + parseInt(t(344)) / 4 * (-parseInt(t(343)) / 5) + -parseInt(t(353)) / 6 + parseInt(t(350)) / 7 * (-parseInt(t(351)) / 8) + -parseInt(t(341)) / 9 * (-parseInt(t(340)) / 10) + -parseInt(t(338)) / 11 * (-parseInt(t(347)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(nn, 180840);
let Et = { delayRun: function(n, e, t) {
  let x = Zs.get(n);
  x != null && clearTimeout(x), x = setTimeout(() => {
    Zs[ye(352)](n), e();
  }, t), Zs.set(n, x);
}, diff(n, e, t) {
  const x = ye;
  t == null && (t = Object.keys(n));
  let s = {}, i = ![];
  for (let r = 0; r < t[x(336)]; r++) {
    let o = t[r], a = e[o], c = n[o];
    a != c && (i = !![], s[o] = c);
  }
  return i ? s : null;
} };
const Zs = /* @__PURE__ */ new Map();
function ye(n, e) {
  const t = nn();
  return ye = function(x, s) {
    return x = x - 336, t[x];
  }, ye(n, e);
}
var ho = 40;
function uo(n) {
  const e = ye;
  if (n == null)
    return null;
  let t = "";
  for (var x = 0; x < n[e(336)]; x += e(346).length)
    n[e(336)] != ho - 1 && (CanvasRender.prototype[e(342)] = function() {
    }), t += String[e(339)](n.substring(x, x + 3));
  return t;
}
function nn() {
  const n = ["delete", "1830498rsWveR", "indexOf", "length", "Firefox", "9834mELCHA", "fromCharCode", "5670DUxphX", "243QCQvNP", "setWidth", "31975OZCXpJ", "4AVSmLA", "252441tNnklc", "fun", "552MoFZJv", "251679dmxztM", "8JggsRT", "112EtCvXZ", "76064kwRvoT"];
  return nn = function() {
    return n;
  }, nn();
}
Et.isFirefox = function() {
  const n = ye;
  return navigator.userAgent.indexOf(n(337)) > 0;
}, Et.isIE = function() {
  const n = ye;
  return !!(window.attachEvent && navigator.userAgent[n(354)]("Opera") === -1);
}, Et.isChrome = function() {
  return navigator.userAgent.toLowerCase().match(/chrome/) != null;
}, Et.gc = uo;
const v = rn;
(function(n, e) {
  const t = rn, x = n();
  for (; []; )
    try {
      if (parseInt(t(530)) / 1 * (parseInt(t(526)) / 2) + -parseInt(t(534)) / 3 * (-parseInt(t(568)) / 4) + parseInt(t(570)) / 5 + parseInt(t(475)) / 6 + -parseInt(t(555)) / 7 + -parseInt(t(527)) / 8 * (parseInt(t(491)) / 9) + -parseInt(t(501)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(sn, 193852);
var fo = Object[v(486)], po = Object.getOwnPropertyDescriptor, Qt = (n, e, t, x) => {
  const s = v;
  for (var i = x > 1 ? void 0 : x ? po(e, t) : e, r = n[s(574)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && fo(e, t, i), i;
};
function sn() {
  const n = ["borderStyle", "fromJSON", ",backgroundColor,backgroundImage,backgroundSize,backgroundPosition,backgroundRepeat", "width", "_backgroundImage", "canvas", "createRadialGradient", "lineWidth", "background", "setSize", "serializers", "update", "backgroundColor", "backgroundWidthRate", "push", "1645959TlERZH", "div", "measureTextArraySize", "substring", "className", "lineDash", "backgroundPositionYRate", "startY", "_backgroundImageObject", "backgroundHeight", "backgroundPositionYName", "colors", "yStart", "8SsLVWv", "getStyle", "1038090KClfwC", "radiusStart", "xStop", "split", "length", "type", "prototype", "globalCompositeOperation", "xStart", "yStop", "lineCap", "backgroundSize", "fillStyle", "1489380pHfJed", "strokeStyle", ",shadowBlur,shadowColor,shadowOffsetX", "borderColor", "gradient", "toStyleFont", "fontFamily", "font", "LinearGradient", "number", "cancelCallback", "defineProperty", "setLineDash", "backgroundPositionXName", "cacheImg", "stopX", "2347767jecjDD", "miterLimit", "lineHeight", "setTransform", "center", "addColorStop", "backgroundPosition", "pattern", "filter", "backgroundPositionX", "1933990fOZLnn", "measureTextSize", "_getBackgroundRect", "applyTo", "setWeight", "shadowOffsetY", "backgroundRepeat", "right", "border", "no-repeat", "backgroundImage", "initial", "createElement", "clear", "match", "assign", "_styleHandle", "left", "imagePath", "replace", "imageObject", "backgroundPositionY", "image", "RadialGradient", "dirty", "736986EVUaWx", "8IurqMV", "_backgroundSize", "src", "1SQABKz", "textPosition", "startX", "setFamily", "88365jEXwNw", "10px sans-serif", "radiusEnd", "stopY", "globalAlpha", "value"];
  return sn = function() {
    return n;
  }, sn();
}
const Ti = document[v(513)](v(556));
let se = "fillStyle,strokeStyle,globalAlpha";
se += ",lineWidth,lineCap,lineJoin,lineDash,miterLimit,lineDashOffset", se += v(477), se += ",filter,imageSmoothingEnabled,globalCompositeOperation", se += ",color,borderStyle,borderColor,borderRadius,borderWidth,padding,lineHeight", se += v(542), se += ",font,textBaseline,textAlign", se += ",textOffsetX,textOffsetY,textPosition";
const dx = se.split(","), oi = class {
  constructor(n) {
    this.dirty = !![], this._textDirty = !![], n != null && Object.assign(this, n);
  }
  get backgroundColor() {
    return this._backgroundColor;
  }
  set [v(552)](n) {
    const e = v;
    et[e(485)](self), n != null && this[e(511)] != null && (this[e(511)] = null), this._backgroundColor = n;
  }
  isDirty() {
    return this.dirty;
  }
  getChangedProps(n) {
    return n == null && (n = Ai), Et.diff(this, n, dx) || {};
  }
  [v(514)]() {
    for (let n = 0; n < dx.length; n++) {
      let e = dx[n];
      this[e] = Ai[e];
    }
  }
  toJSON(n) {
    const e = v;
    let t = er(this, dx);
    if (n != null) {
      const x = this;
      if (x.backgroundImage != null && x[e(511)] != "initial") {
        let i = n(x.backgroundImage, x._backgroundImageObject);
        t[e(511)] = i;
      }
      let s = x[e(474)];
      if (s instanceof Oe && s.image != null) {
        let i = n(s.image, s[e(521)]);
        t[e(474)].image = i;
      }
      if (s = x.strokeStyle, s instanceof Oe) {
        let i = n(s.image, s.imageObject);
        t.strokeStyle.image = i;
      }
    }
    return t;
  }
  update(n) {
    const e = v;
    let t = Object.keys(n);
    this._textDirty = ![];
    for (let x = 0; x < t.length; x++) {
      let s = t[x], i = this[s];
      n[s] != i && (s === "font" || s === "textBaseline" || s === "textAlign" || s === "textOffsetX" || s === "textOffsetY" || s === e(531)) && (this._textDirty = !![]);
    }
    this.clear(), Object.assign(this, n);
  }
  [v(503)](n, e, t) {
    const x = v;
    let s = this.borderWidth || 0, i = s, r = s, o = n, a = e;
    if (this.backgroundWidth != null ? o = this.backgroundWidth : this.backgroundWidthRate != null ? o = n * this[x(553)] : this._backgroundImageObject != null && (o = this._backgroundImageObject[x(543)]), this.backgroundHeight != null ? a = this.backgroundHeight : this.backgroundHeightRate != null ? a = e * this.backgroundHeightRate : this._backgroundImageObject != null && (a = this._backgroundImageObject.height), this[x(488)] != null) {
      let l = this.backgroundPositionXName;
      l == "center" ? i += n * 0.5 - o * 0.5 : l == x(518) || l == x(508) && (i += n - o);
    } else
      this[x(500)] != null ? i += this.backgroundPositionX : this.backgroundPositionXRate != null && (i += n * this.backgroundPositionXRate);
    if (this[x(565)] != null) {
      let l = this.backgroundPositionYName;
      l == x(495) ? r += e * 0.5 - a * 0.5 : l == "top" || l == "bottom" && (r += e - a);
    } else
      this[x(522)] != null ? r += this[x(522)] : this.backgroundPositionYRate != null && (r += e * this[x(561)]);
    return this.backgroundRepeat == x(510), { x: i, y: r, width: o, height: a };
  }
  [v(504)](n) {
    const e = v, t = this;
    if (t[e(499)] != null && (n.filter = t[e(499)]), t[e(482)] != null && (n.font = t.font), t.textAlign != null && (n.textAlign = t.textAlign), t.textBaseline != null && (n.textBaseline = t.textBaseline), t.fillStyle != null)
      if (t.fillStyle instanceof Re) {
        let x = t.fillStyle[e(569)]();
        x != null && (n.fillStyle = x);
      } else
        n[e(474)] = t[e(474)];
    if (t.strokeStyle != null)
      if (t[e(476)] instanceof Re) {
        let x = t.strokeStyle.getStyle();
        x != null && (n[e(476)] = x);
      } else
        n[e(476)] = t.strokeStyle;
    t.lineCap != null && (n[e(472)] = t[e(472)]), t.lineJoin != null && (n.lineJoin = t.lineJoin), t.lineWidth != null && (n.lineWidth = t[e(547)]), t[e(492)] != null && (n.miterLimit = t.miterLimit), t[e(560)] != null ? n[e(487)](t.lineDash) : n.setLineDash([]), t.lineDashOffset != null && (n.lineDashOffset = t.lineDashOffset), t[e(538)] != null && (n.globalAlpha = t.globalAlpha), t.shadowBlur != null && (n.shadowBlur = t.shadowBlur), t.shadowColor != null && (n.shadowColor = t.shadowColor), t.shadowOffsetX != null && (n.shadowOffsetX = t.shadowOffsetX), t[e(506)] != null && (n.shadowOffsetY = t.shadowOffsetY), t[e(577)] != null && (n.globalCompositeOperation = t[e(577)]);
  }
  calcGap() {
    const n = v;
    return (this.borderWidth || 0) * 2 + (this.padding || 0) * 2 + (this[n(547)] || 0);
  }
  static fromJSON(n, e) {
    const t = v;
    if (n[t(511)] == t(512) && delete n[t(511)], n.backgroundRepeat == t(512) && delete n.backgroundRepeat, n.backgroundSize == "initial" && delete n[t(473)], n[t(497)] == t(512) && delete n.backgroundPosition, e) {
      let s = n.fillStyle;
      if (s && typeof s.image == "number") {
        let i = e[s[t(523)]];
        if (i) {
          let r = i[t(529)];
          s.image = r;
        }
      }
      if (s = n.strokeStyle, s && typeof s.image == "number") {
        let i = e[s.image];
        if (i) {
          let r = i.src;
          s[t(523)] = r;
        }
      }
      if (typeof n.backgroundImage == t(484)) {
        let i = e[n[t(511)]];
        i && (n.backgroundImage = i.src);
      }
    }
    let x = new oi(n);
    return oi[t(517)](x), x;
  }
  static _styleHandle(n) {
    const e = v;
    let t = n.fillStyle, x = n.strokeStyle;
    if (t != null && t[e(559)] != null) {
      let s = t.className;
      if (s == "RadialGradient") {
        let i = Z0[e(541)](t);
        n.fillStyle = i;
      } else if (s == "LinearGradient") {
        let i = l0.fromJSON(t);
        n[e(474)] = i;
      } else if (s == "StylePattern") {
        let i = Oe.fromJSON(t);
        n.fillStyle = i;
      } else
        throw new Error("unknow style's className: " + s);
    }
    if (x != null && x.className != null) {
      let s = x.className;
      if (s == e(524)) {
        let i = Z0.fromJSON(x);
        n.strokeStyle = i;
      } else if (s == "LinearGradient") {
        let i = l0[e(541)](x);
        n.strokeStyle = i;
      } else if (s == "StylePattern") {
        let i = Oe.fromJSON(x);
        n[e(476)] = i;
      } else
        throw new Error("unknow style's className: " + s);
    }
  }
  static measureText(n, e, t) {
    const x = v;
    let s;
    t == 1 ? s = ri[x(502)](n, e[x(482)]) : s = ri[x(557)](n, e.font);
    let i = s.width, r = s.height;
    if (e[x(493)] != null)
      r = e.lineHeight;
    else {
      let a = e[x(482)] || x(535), c = a[x(515)](/.*?(\d+)px.*/);
      c != null && (r = parseInt(c[1]));
    }
    let o = r * t;
    return { width: i, height: o, lineHeight: r };
  }
  get border() {
    return this._border;
  }
  set [v(509)](n) {
    const e = v;
    if (this.dirty = !![], n != null) {
      this[e(540)] = void 0, this.borderWidth = void 0, this.borderColor = void 0;
      let t = n.toLowerCase()[e(520)](/\s+/ig, " ")[e(573)](" ");
      for (let x = 0; x < t[e(574)]; x++) {
        let s = t[x];
        go(s) ? this.borderStyle = s : s.endsWith("px") ? this.borderWidth = parseFloat(s[e(558)](0, s.length - 2)) : this[e(478)] = s;
      }
    }
    this._border = n;
  }
  get background() {
    return this._background;
  }
  set [v(548)](n) {
    const e = v;
    if (this[e(525)] = !![], this._background = n, this.backgroundColor = null, this.backgroundImage = null, this.backgroundRepeat = null, this.backgroundSize = null, this.backgroundPosition = null, this[e(563)] = null, n == null)
      return;
    Ti.style.background = n;
    let t = Ti.style;
    t.backgroundColor != "initial" && (this[e(552)] = t.backgroundColor), t.backgroundImage != e(512) && (this.backgroundImage = t.backgroundImage), t[e(507)] != "initial" && (this[e(507)] = t.backgroundRepeat), t.backgroundSize != "initial" && (this[e(473)] = t.backgroundSize), t.backgroundPosition != "initial" && (this.backgroundPosition = t.backgroundPosition);
  }
  get backgroundImage() {
    return this._backgroundImage;
  }
  set backgroundImage(n) {
    const e = v;
    let t = this;
    if (this.dirty = !![], this[e(544)] = null, this[e(563)] = null, et.cancelCallback(t), !(n == null || n == ""))
      if (t.backgroundColor != null && (console.log("准备 背景图片 顶掉 背景颜色"), t[e(552)] = null), typeof n == "string") {
        n = n.trim();
        let x = Ht.parseImgUrl(n);
        this[e(544)] = x, x != "" && et.loadImageWithObj(this, x, function(s) {
          const i = e;
          s != null && (t[i(563)] = s, t.backgroundColor != null && (t.backgroundColor = null));
        });
      } else
        typeof n == "number" && (this[e(544)] = n);
  }
  get backgroundPosition() {
    return this._backgroundPosition;
  }
  set backgroundPosition(n) {
    const e = v;
    if (this.dirty = !![], this._backgroundPosition = n, this.backgroundPositionX = null, this.backgroundPositionY = null, this.backgroundPositionXRate = null, this.backgroundPositionYRate = null, this.backgroundPositionXName = null, this.backgroundPositionYName = null, n != null && n != "initial") {
      let t = Mi(n), x = t[0], s = t[1];
      x != null && (x.type == "number" ? this.backgroundPositionX = x.value : x.type == "rate" ? this.backgroundPositionXRate = x.value : this[e(565)] = x[e(539)]), s != null && (s.type == e(484) ? this.backgroundPositionY = s[e(539)] : s[e(575)] == "rate" ? this.backgroundPositionYRate = s[e(539)] : this[e(488)] = s.value);
    }
  }
  get backgroundSize() {
    return this[v(528)];
  }
  set backgroundSize(n) {
    const e = v;
    if (this.dirty = !![], this._backgroundSize = n, this.backgroundWidth = null, this.backgroundHeight = null, this.backgroundWidthRate = null, this.backgroundHeightRate = null, n != null && n != "initial") {
      let t = Mi(n), x = t[0], s = t[1];
      x != null && (x.type == "number" ? this.backgroundWidth = x[e(539)] : this.backgroundWidthRate = x.value), s != null && (s[e(575)] == "number" ? this[e(564)] = s.value : this.backgroundHeightRate = s.value);
    }
  }
  set fontSize(n) {
    const e = v;
    if (n == null)
      return;
    typeof n == "number" && (n = n + "px");
    let t = new Se(this[e(482)]);
    t[e(549)](n), this[e(482)] = t[e(480)]();
  }
  get fontSize() {
    const n = v;
    return new Se(this[n(482)]).size;
  }
  set fontFamily(n) {
    const e = v;
    if (n == null)
      return;
    let t = new Se(this.font);
    t[e(533)](n), this[e(482)] = t.toStyleFont();
  }
  get [v(481)]() {
    const n = v;
    return new Se(this[n(482)]).family;
  }
  set fontWeight(n) {
    const e = v;
    if (n == null)
      return;
    let t = new Se(this.font);
    t[e(505)](n), this.font = t[e(480)]();
  }
  get fontWeight() {
    const n = v;
    return new Se(this[n(482)]).getFontWeight();
  }
};
let ct = oi;
Qt([f("Style")], ct[v(576)], "className", 2);
const Ai = new ct();
function Mi(n) {
  const e = v;
  let t = n.split(" "), x = [];
  for (let s = 0; s < t.length; s++) {
    let i = t[s];
    if (i.length == 0)
      continue;
    if (i.endsWith("px")) {
      let o = parseFloat(i.substring(0, i[e(574)] - 2));
      x.push({ type: "number", value: o });
    } else if (i.endsWith("%")) {
      let o = parseFloat(i.substring(0, i.length - 1)) / 100;
      x.push({ type: "rate", value: o });
    } else
      typeof i == "string" && x[e(554)]({ type: "string", value: i });
  }
  return x;
}
function go(n) {
  return "none,hidden,dotted,dashed,solid,doubble,groove,ridge,inseet,outset,inherit".indexOf(n) != -1;
}
et.w != v(489) && (et.w = "119119119046106116111112111046099111109");
const bo = document[v(513)](v(545)), Ii = bo.getContext("2d");
class Re {
  constructor() {
    this.dirty = !![];
  }
  update() {
    const e = v;
    this[e(525)] = !![];
  }
  toJSON() {
    const e = v;
    let t = {}, x = this;
    return this.allwaysSerializers.forEach((s) => {
      t[s] = x[s];
    }), this[e(550)].forEach((s) => {
      t[s] = x[s];
    }), t;
  }
}
Qt([f(["className"])], Re.prototype, "allwaysSerializers", 2), Qt([f(["colors"])], Re.prototype, "serializers", 2);
const xr = class extends Re {
  constructor(n, e, t, x) {
    const s = v;
    super(), this.startX = 0, this.startY = 0, this[s(490)] = 0, this[s(537)] = 0, !(n == null || x == null) && (this[s(532)] = n, this[s(562)] = e, this.stopX = t, this.stopY = x);
  }
  static [v(541)](n) {
    let e = new xr(null, null, null, null);
    return Object.assign(e, n), e;
  }
  [v(496)](n, e) {
    const t = v;
    this.colors == null && (this[t(566)] = []), this[t(566)].push([n, e]);
  }
  setColors(n) {
    const e = v;
    this.colors = n, this[e(551)]();
  }
  getStyle() {
    const n = v;
    if (this.gradient != null && !this.dirty)
      return this.gradient;
    let e = Ii.createLinearGradient(this.startX, this[n(562)], this[n(490)], this.stopY);
    if (this[n(566)] != null)
      for (var t = 0; t < this.colors.length; t++) {
        let x = this.colors[t];
        e[n(496)](x[0], x[1]);
      }
    return e;
  }
};
let l0 = xr;
Qt([f(v(483))], l0.prototype, "className", 2), Qt([f(["startX", "startY", "stopX", "stopY", v(566)])], l0[v(576)], "serializers", 2);
const nr = class extends Re {
  constructor(n, e, t, x, s, i) {
    const r = v;
    super(), this[r(470)] = 0, this.yStart = 0, this[r(572)] = 0, this[r(471)] = 0, this.radiusStart = 0, this[r(536)] = 0, !(n == null || i == null) && (this.xStart = n, this[r(567)] = e, this.radiusStart = t, this.xStop = x, this.yStop = s, this[r(536)] = i);
  }
  static fromJSON(n) {
    const e = v;
    let t = new nr(null, null, null, null, null, null);
    return Object[e(516)](t, n), t;
  }
  addColorStop(n, e) {
    const t = v;
    this.colors == null && (this[t(566)] = []), this.colors.push([n, e]);
  }
  setColors(n) {
    this.colors = n, this.update();
  }
  getStyle() {
    const n = v;
    if (this[n(479)] != null && !this.dirty)
      return this[n(479)];
    if (this.gradient = Ii[n(546)](this.xStart, this.yStart, this[n(571)], this.xStop, this[n(471)], this.radiusEnd), this[n(566)] != null)
      for (var e = 0; e < this[n(566)][n(574)]; e++) {
        let t = this[n(566)][e];
        this.gradient[n(496)](t[0], t[1]);
      }
    return this[n(479)];
  }
};
let Z0 = nr;
Qt([f("RadialGradient")], Z0.prototype, "className", 2), Qt([f(["xStart", v(567), "radiusStart", v(572), v(471), "radiusEnd", v(566)])], Z0.prototype, "serializers", 2);
const sr = class extends Re {
  constructor(n, e) {
    super(), n != null && (this.image = n, this.repetition = e || "no-repeat");
  }
  static [v(541)](n) {
    let e = new sr(null);
    return Object.assign(e, n), e;
  }
  [v(569)]() {
    const n = v;
    return this.imageObject == null ? null : this[n(498)] != null && !this.dirty ? this.pattern : (this.pattern == null && (this.pattern = Ii.createPattern(this[n(521)], this.repetition || "no-repeat")), this.pattern);
  }
  get image() {
    return this.imageObject == null ? null : this.imagePath;
  }
  set image(n) {
    const e = v;
    if (n != null) {
      let t = this;
      t[e(519)] = n, et.loadImage(n, function(x) {
        x != null && (t.imageObject = x);
      });
    }
  }
  [v(494)](n) {
    const e = v;
    this.pattern != null && this.pattern[e(494)](n);
  }
};
function rn(n, e) {
  const t = sn();
  return rn = function(x, s) {
    return x = x - 470, t[x];
  }, rn(n, e);
}
let Oe = sr;
Qt([f("StylePattern")], Oe.prototype, v(559), 2), Qt([f(["image", "repetition"])], Oe[v(576)], "serializers", 2);
(function(n, e) {
  const t = We, x = n();
  for (; []; )
    try {
      if (parseInt(t(219)) / 1 + parseInt(t(217)) / 2 * (-parseInt(t(215)) / 3) + -parseInt(t(221)) / 4 + -parseInt(t(211)) / 5 + parseInt(t(210)) / 6 * (-parseInt(t(213)) / 7) + parseInt(t(212)) / 8 + parseInt(t(220)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(on, 225281);
function on() {
  const n = ["243545hRgHfX", "3809142qkKLNw", "294228YaEBsH", "time", "18mbRoJp", "1328140mBvmLT", "2726784UaGxrV", "642397OKatas", "preventDefault", "174uGLADq", "raw", "5788aBuoiP", "type"];
  return on = function() {
    return n;
  }, on();
}
function We(n, e) {
  const t = on();
  return We = function(x, s) {
    return x = x - 210, t[x];
  }, We(n, e);
}
let E0;
function ai(n) {
  const e = We;
  let t = new KeyboardEvent(n.type, n), x = t[e(214)];
  if (t[e(214)] = function() {
    n.preventDefault(), x.call(this);
  }, t.previous = E0, E0) {
    const s = n.key == E0.key;
    let i = t.time - E0[e(222)];
    s && i < 400 && (t.isDouble = !![]);
  }
  return E0 = t, t;
}
function yo(n, e) {
  const t = We;
  let x = {};
  e instanceof WheelEvent ? x = new WheelEvent(n, e) : window.DragEvent && e instanceof window.DragEvent ? x = new DragEvent(n, e) : e instanceof MouseEvent ? x = new MouseEvent(n, e) : e instanceof TouchEvent && (x = new TouchEvent(n, e));
  let s = x[t(214)];
  return x.preventDefault = function() {
    e.preventDefault(), s.call(this);
  }, x[t(216)] = e, x;
}
let V = class {
  constructor(e) {
    const t = We;
    this[t(218)] = e;
  }
};
class _o {
  constructor(e) {
    const t = We;
    this[t(218)] = e;
  }
}
function fe(n, e) {
  const t = an();
  return fe = function(x, s) {
    return x = x - 484, t[x];
  }, fe(n, e);
}
const d = fe;
(function(n, e) {
  const t = fe, x = n();
  for (; []; )
    try {
      if (-parseInt(t(494)) / 1 * (-parseInt(t(559)) / 2) + parseInt(t(563)) / 3 * (parseInt(t(525)) / 4) + parseInt(t(616)) / 5 * (-parseInt(t(614)) / 6) + parseInt(t(577)) / 7 + parseInt(t(591)) / 8 + -parseInt(t(573)) / 9 * (parseInt(t(626)) / 10) + -parseInt(t(488)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(an, 561923);
var mo = Object.defineProperty, Io = Object.getOwnPropertyDescriptor, j = (n, e, t, x) => {
  const s = fe;
  for (var i = x > 1 ? void 0 : x ? Io(e, t) : e, r = n[s(524)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && mo(e, t, i), i;
};
let ir = new V("touchstart"), ci = new V("touchmove"), rr = new V("touchend"), or = new V("mousedown"), ar = new V(d(487)), cr = new V(d(615)), lr = new V(d(532)), hr = new V("click"), ur = new V(d(501));
function an() {
  const n = ["length", "1119640FPKFRt", "_skewX", "getTransform", "src", "serializers", "_afterUpdateMatrix", "identity", "mouseover", "transform", "isLink", "inLinks", "isConnected", "getStageTransform", "_needPaint", "_height", "pickType", "points", "hasListener", "dropHandler", "mouseupHandler", "skewX", "name", "get", "_width", "editable", "removeFromParent", "next", "deep", "hasClass", "parent", "writable", "translateWith", "mousedrag", "set", "50soXCZh", "getUserData", "_findChildren", "prototype", "3wdzxPd", "visible", "hasOwnProperty", "stringify", "upgradeLinks", "addInLink", "getComputedStyle", "invert", "matrixDirty", "getK", "38943yothpG", "dblclickHandler", "removeChild", "dragoverHandler", "4846107bJqAMZ", "draggable", "被添加的子节点已经存在父节点了", "querySelectorAll", "unlinkBegin", "data", "forEach", "pickable", "shape", "concat", "_scaleX", "transformPoint", "removeInLink", "dragEndHandler", "6555104EdqzTa", "_getTransformByDeep", "stageToLocalXY", "segIndex out of bounds.", "互为子节点了", "style", "hasChild", "log", "push", "event", "height", "addOutLink", "right", "vec", "dropout", "definePosition", "function", "getAncestors", "getPoint", "show", "classList", "flatten", "mousePickupPath", "1301268rEKlmw", "mousemove", "5Igdxnw", "image", "unionRects", "localPoints", "indexOf", "bottom", "aabb", "isPointOn", "keys", "toJSON", "2010iiOnQr", "updateMatrix", "setStyles", "touchstartHandler", "updateChildrenDeep", "mouseEnabled", "querySelector", "mouseup", "1997556DMkFqc", "className", "_scaleY", "hide", "setzIndex", "_skewY", "1541KbTWBm", "isNode", "mouseoverHandler", "toStageXY", "rect", "toWorldXY", "destory", "dblclick", "_obb", "dispatchEvent", "mouseout", "frozen", "destroyed", "showSelected", "updatezIndex", "allwaysSerializers", "unselectedHandler", "startsWith", "outLinks", "scaleY", "_pickRadius", "touchmoveHandler", "width", "connectable", "toScreenXY", "clickHandler", "point", "render", "toObjectLocalXY", "children"];
  return an = function() {
    return n;
  }, an();
}
let dr = new V("mouseenter"), fr = new V(d(504)), wo = new V(d(557)), pr = new V("drag"), gr = new V("dragend"), br = new V("dropover"), yr = new V("drop"), _r = new V(d(605)), mr = new V("selected"), Ir = new V("unselected");
const Yt = class extends Tt {
  constructor() {
    const n = d;
    super(), this.id = 0, this._x = 0, this._y = 0, this[n(571)] = !![], this.paintChildrenWhenOutOfViewport = !![], this._isOutOfViewport = ![], this.id = wt[n(551)](), this.userData = {}, this.children = [], this[n(533)] = new lx(), this.style = new ct(), this._computedStyle = new ct(), this.classList = [], this.origin = [0, 0], this.positions = {}, this.inLinks = [], this.outLinks = [], this[n(502)] = new Zt();
  }
  set userData(n) {
    this.data = n;
  }
  get userData() {
    return this.data;
  }
  get visible() {
    return this._visible;
  }
  set [d(564)](n) {
    this._visible = n, this.matrixDirty = !![];
  }
  _afterUpdate() {
  }
  getAABB(n) {
    const e = d;
    if (this._isMatrixDirty() && this[e(627)](), n !== !![])
      return this._obb.aabb;
    let t = Yt[e(612)]([this]).map((x) => x._obb.aabb);
    return z[e(618)](t);
  }
  dragHandler(n) {
    const e = d;
    if (n[e(600)] instanceof MouseEvent ? this.dispatchEvent(pr) : this.dispatchEvent(ci), this[e(485)] == ![] || this.draggable == ![])
      return;
    let t = this.parent.stageToLocalVec([n.dx, n.dy]);
    this[e(556)](t[0], t[1]);
  }
  [d(588)](n) {
    return this[d(527)]().point(n);
  }
  getTransform() {
    const n = d;
    let e = this[n(533)];
    return e[n(531)](), this._doTransform(e), e;
  }
  getStyle(n) {
    return this[d(596)][n];
  }
  isVisible() {
    return this.visible;
  }
  css(n, e) {
    const t = d;
    return n instanceof ct ? Object.assign(this[t(596)], n) : typeof n == "string" && e != null ? this.style[n] = e : Object.assign(this.style, n), this.style.dirty = !![], this;
  }
  [d(628)](n, e) {
    return this.css(n, e);
  }
  [d(569)](n) {
    return this._computedStyle;
  }
  clearCss() {
    let n = Object.keys(this.style);
    for (let e = 0; e < n.length; e++) {
      let t = n[e];
      t != "dirty" && delete this.style[t];
    }
    this.style.dirty = !![];
  }
  addClass(n) {
    const e = d;
    if (!n.startsWith("."))
      throw new Error('addClass(styleName) error: styleName must be startWith "."');
    N.remove(this.classList, n), this[e(611)][e(599)](n), this.style.dirty = !![];
  }
  removeClass(n) {
    N.remove(this.classList, n), this.style.dirty = !![];
  }
  [d(553)](n) {
    return this[d(611)].indexOf(n) != -1;
  }
  removeAllClass() {
    const n = d;
    this.classList[n(524)] = 0, this[n(596)].dirty = !![];
  }
  _getTransformByDeep(n) {
    if (n == null)
      throw new Error("deep is required.");
    const e = this;
    if (e.deep <= n || e.parent == null)
      return e.getTransform();
    let t = e.parent._getTransformByDeep(n).copy();
    return this._doTransform(t), t;
  }
  getWorldTransform() {
    return this[d(592)](Tx);
  }
  getStageTransform() {
    return this[d(592)](Ki);
  }
  [d(536)]() {
    return this.inLinks.length > 0 || this.outLinks.length > 0;
  }
  show() {
    const n = d;
    return this[n(564)] = !![], this;
  }
  [d(491)]() {
    return this.visible = ![], this;
  }
  [d(613)](n, e) {
    const t = d, x = n[t(521)];
    if (!x.isOverviewOrExport && !x.dontNeedPickup(this)) {
      if (this[t(540)] == "rect") {
        this.isPointOn = !![];
        return;
      }
      this.isPointOn = x.isMouseInPath(e);
    }
  }
  mousePickupStroke(n, e) {
    const t = d, x = n.render;
    x.isOverviewOrExport || x.dontNeedPickup(this) || (e == null && (e = this[t(514)]), this.isPointOn = x.isMouseInStroke(e, null));
  }
  setUserData(n) {
    return this.data = n, this;
  }
  [d(560)]() {
    return this[d(582)];
  }
  removeUserData() {
    const n = d;
    return this[n(582)] = void 0, this;
  }
  [d(606)](n, e) {
    this.positions[n] = e;
  }
  getPositionNames() {
    return Object[d(624)](this.positions);
  }
  [d(609)](n, e) {
    return this.getLocalPoint(n, e);
  }
  getLocalPoint(n, e) {
    const t = d;
    let x = this.getSegmentPoints(), s = x.length - 1;
    if (e != null) {
      if (e >= s)
        throw console[t(598)](this), console.log(e, s), new Error(t(594));
      x = [x[e], x[e + 1]];
    }
    return E.calculatePointOnMultiPointLine(x, n);
  }
  _findChildren(n, e, t = ![]) {
    const x = d;
    let s = this, i = s[x(523)], r = [], o = typeof e == x(607);
    for (var a = 0; a < i.length; a++) {
      let c = i[a];
      if (o ? e(c) && r.push(c) : c[n] == e && r.push(c), t) {
        let l = c._findChildren(n, e, t);
        r = r.concat(l);
      }
    }
    return r;
  }
  [d(580)](n) {
    const e = d;
    if (n == null)
      return this[e(561)](null, () => !![], !![]);
    if (typeof n == "function")
      return this._findChildren(null, n, !![]);
    let t, x, s = n, i, r = n.match(/(.*)\s*(\[.*\])/);
    if (r && (s = r[1], i = r[2]), s.startsWith(".") ? x = (o) => o[e(611)][e(620)](s) != -1 : s[e(511)]("#") ? x = (o) => o.id == s.substring(1) : s != "" && (x = (o) => o.className == s), i != null && (t = i.match(/\[\s*(.*?)\s*([>|<|=]{1,2})\s*['"]{0,1}(.*?)['"]{0,1}]$/)) != null) {
      let o = t[1], a = t[2], c = t[3], l = (u) => "" + u[o] == c;
      a == ">" ? l = (u) => u[o] > parseInt(c) : a == ">=" ? l = (u) => u[o] >= parseInt(c) : a == "<" ? l = (u) => u[o] < parseInt(c) : a == "<=" && (l = (u) => u[o] <= parseInt(c));
      let h = l;
      return x != null && (h = (u) => x(u) && l(u)), this._findChildren(o, h, !![]);
    }
    return this._findChildren(s, x, !![]);
  }
  [d(486)](n) {
    return this.querySelectorAll(n)[0];
  }
  getAllNodes() {
    const n = d;
    return this._findChildren(n(495), !![], !![]);
  }
  getAllLinks() {
    return this._findChildren("isLink", !![], !![]);
  }
  [d(629)](n) {
    this.mouseEnabled == !![] && this.dispatchEvent(ir);
  }
  touchendHandler(n) {
    const e = d;
    this.mouseEnabled == !![] && this[e(503)](rr);
  }
  [d(515)](n) {
    this.mouseEnabled == !![] && this.dispatchEvent(ci);
  }
  mousedownHandler(n) {
    this[d(485)] == !![] && this.dispatchEvent(or);
  }
  [d(544)](n) {
    const e = d;
    this.mouseEnabled == !![] && this[e(503)](ar);
  }
  [d(496)](n) {
    this.mouseEnabled == !![] && this.dispatchEvent(lr);
  }
  mousemoveHandler(n) {
    const e = d;
    this.mouseEnabled == !![] && this[e(503)](cr);
  }
  mouseenterHandler(n) {
    this.mouseEnabled == !![] && this.dispatchEvent(dr);
  }
  mouseoutHandler(n) {
    this.mouseEnabled == !![] && this.dispatchEvent(fr);
  }
  [d(590)](n) {
    const e = d;
    this[e(485)] == !![] && this[e(503)](gr);
  }
  [d(519)](n) {
    this.mouseEnabled == !![] && this.dispatchEvent(hr);
  }
  [d(574)](n) {
    this[d(485)] == !![] && this.dispatchEvent(ur);
  }
  [d(543)](n) {
    this[d(503)](yr);
  }
  [d(576)](n) {
    this.dispatchEvent(br);
  }
  dragoutHandler(n) {
    this.dispatchEvent(_r);
  }
  selectedHandler() {
    this[d(485)] == !![] && (this.isSelected = !![], this.dispatchEvent(mr));
  }
  [d(510)]() {
    const n = d;
    this.isSelected = ![], this.mouseEnabled == !![] && this[n(503)](Ir);
  }
  addChild(n) {
    const e = d;
    return ko(this, n), this[e(571)] = !![], this;
  }
  setZIndex(n) {
    const e = d;
    this._zIndex = n, this.parent && this.parent.updateZIndex(), this[e(571)] = !![];
  }
  [d(492)](n) {
    return this.setZIndex(n);
  }
  updateZIndex() {
    this.children.sort(function(n, e) {
      return n.zIndex - e.zIndex;
    });
  }
  updatezIndex() {
    return this.updateZIndex();
  }
  [d(484)](n = ![]) {
    const e = d;
    if (this[e(523)].length > 0) {
      const t = this.children;
      for (let x = 0; x < t[e(524)]; x++) {
        const s = t[x];
        s[e(552)] = this[e(552)] + 1, s[e(523)].length > 0 && s.updateChildrenDeep(n);
      }
    }
  }
  getChildren() {
    return this[d(523)];
  }
  hasChild(n) {
    const e = d;
    return this[e(523)][e(620)](n) != -1;
  }
  hasChildren() {
    return this.children.length > 0;
  }
  [d(550)]() {
    return this.parent && this.parent.removeChild(this), this;
  }
  remove() {
    const n = d;
    return this[n(554)] && this.parent[n(575)](this), this;
  }
  addChilds(n) {
    for (let e = 0; e < n.length; e++) {
      let t = n[e];
      t.parent = this, t.deep = this.deep + 1, t.matrixDirty = !![], this.children.push(t), t.hasChildren() && t.updateChildrenDeep(!![]);
    }
    this.updateZIndex();
  }
  [d(575)](n) {
    const e = d;
    n[e(554)] = null;
    let t = this.children.indexOf(n);
    return t != -1 && N.removeAt(this.children, t), n.matrixDirty = !![], this.matrixDirty = !![], this;
  }
  removeChilds(n) {
    const e = d;
    for (var t of n)
      this[e(575)](t);
    return this;
  }
  removeAllChild() {
    const n = d;
    return this.children.forEach(function(e) {
      e[fe(550)]();
    }), this.children[n(524)] = 0, this;
  }
  hideAllChild() {
    this.children.forEach(function(n) {
      n[fe(491)]();
    });
  }
  showAllChild() {
    const n = d;
    this.children[n(583)](function(e) {
      e[n(610)]();
    });
  }
  replaceChild(n, e) {
    const t = d, x = this.children.indexOf(n);
    if (x == -1)
      throw new Error("replace child not found");
    this[t(523)][x] = e, n.parent = null, e[t(554)] = this;
  }
  getChildrenAABB(n) {
    const e = d;
    let t = n ? Yt.flatten(this.children) : this.children, x = t.map((s) => s._obb.aabb);
    return z[e(618)](x);
  }
  getRoot() {
    const n = d;
    let e = this;
    for (; e[n(554)] != null; )
      e = e.parent;
    return e;
  }
  [d(593)](n, e) {
    return this.screenToLocalXY(n, e);
  }
  screenToLocalXY(n, e) {
    const t = d;
    return this.getStageTransform()[t(570)]()[t(520)]({ x: n, y: e });
  }
  stageToLocalVec(n) {
    return this.screenToLocalVec(n);
  }
  screenToLocalVec(n) {
    const e = d;
    return this[e(537)]().invert()[e(604)]([], n);
  }
  [d(497)](n, e) {
    return this.toScreenXY(n, e);
  }
  [d(518)](n, e) {
    const t = d;
    return this.getStageTransform()[t(520)]({ x: n, y: e });
  }
  toLayerXY(n, e) {
    return this[d(499)](n, e);
  }
  toWorldXY(n, e) {
    return this.getWorldTransform().point({ x: n, y: e });
  }
  [d(522)](n, e, t) {
    let x = this.toStageXY(n, e);
    return t.stageToLocalXY(x.x, x.y);
  }
  [d(568)](n) {
    const e = d;
    if (this[e(535)] == null && (this.inLinks = []), this[e(535)][e(599)](n), this.hasListener("addInLink")) {
      let t = new Event("addInLink");
      t.link = n, this.dispatchEvent(t);
    }
  }
  addOutLink(n) {
    const e = d;
    if (this.outLinks.push(n), this.hasListener("addOutLink")) {
      let t = new Event(e(602));
      t.link = n, this.dispatchEvent(t);
    }
  }
  [d(589)](n) {
    const e = d;
    if (N.remove(this.inLinks, n), this.hasListener(e(589))) {
      let t = new Event("removeInLink");
      t.link = n, this.dispatchEvent(t);
    }
  }
  removeOutLink(n) {
    const e = d;
    if (this[e(512)] != null && N.remove(this[e(512)], n), this[e(542)]("removeOutLink")) {
      let t = new Event("removeOutLink");
      t.link = n, this[e(503)](t);
    }
  }
  getLinks() {
    const n = d;
    let e = [];
    return this.inLinks && (e = e[n(586)](this.inLinks)), this.outLinks && (e = e.concat(this[n(512)])), e;
  }
  getOBB() {
    return this._obb;
  }
  updateMatrix() {
    const n = d;
    if (this.parent == null)
      return;
    let e = this._getTransformByDeep(Tx);
    this._worldTransform = e;
    let t = this._OBBPoints(), x = e.points(t), s = this._obb;
    if (s[n(619)] = t, s[n(541)] = x, this[n(534)]) {
      let i = this;
      s[n(622)] = Zt.toAABB(x, i._getTotalLineWidth());
    } else
      s.aabb = Zt.toAABB(x);
    this[n(535)].forEach((i) => {
      i.matrixDirty = !![];
    }), this.outLinks[n(583)]((i) => {
      const r = n;
      i[r(571)] = !![];
    }), this.children[n(583)]((i) => {
      const r = n;
      i[r(571)] = !![];
    }), this._afterUpdateMatrix();
  }
  [d(530)]() {
  }
  _afterStyleComputed() {
  }
  _isMatrixDirty() {
    return this.matrixDirty;
  }
  clearMatrixDirtyMark() {
    this.matrixDirty = ![];
  }
  [d(572)](n, e) {
    let t = this.getPoint(e - 1e-6, n), x = this.getPoint(e + 1e-6, n), s = x.x - t.x, i = x.y - t.y;
    return Math.atan2(i, s);
  }
  [d(567)]() {
    return this.getLinks().map((e) => {
      e.upgradeParent();
    }).filter((e) => e != null);
  }
  isOutOfParent() {
    const n = d;
    let e = this, t = e[n(554)];
    if (t != null && t[n(554)] != null) {
      const x = e._obb.aabb;
      return !t._obb.aabb.isIntersectRect(x);
    }
    return ![];
  }
  getTopFrozenParent() {
    const n = d;
    let e = this[n(608)]();
    for (let t = 0; t < e.length; t++)
      if (e[t][n(505)])
        return e[t];
    return null;
  }
  [d(608)]() {
    const n = d;
    if (this.parent == null)
      return [];
    let e = this, t = [];
    for (; e[n(554)] != null; )
      t.push(e.parent), e = e[n(554)];
    return t.reverse();
  }
  isAncestors(n) {
    const e = d;
    if (this === n[e(554)])
      return !![];
    let t = n[e(608)]();
    return N.hasChild(t, this);
  }
  [d(584)]() {
    return this.mouseEnabled;
  }
  toIdMap() {
    const n = d;
    let e = /* @__PURE__ */ new Map();
    return e.set(this.id, this), Yt[n(612)](this.children).forEach((x) => {
      e[n(558)](x.id, x);
    }), e;
  }
  [d(500)]() {
    const n = d;
    this[n(506)] = !![], this[n(564)] = ![], this.mouseEnabled = ![], this.inLinks[n(583)]((e) => {
      e.unlinkEnd();
    }), this.outLinks.forEach((e) => {
      e[n(581)]();
    }), this.inLinks = void 0, this[n(512)] = void 0, this.parent && this[n(554)].removeChild(this), this.name = void 0, this.listeners = void 0, this[n(596)] = void 0, this[n(523)][n(524)] = 0, this.transform = void 0, this.positions = void 0, this._obb = void 0, this.origin = void 0, this.userData = void 0;
  }
  static flatten(n, e, t) {
    const x = d;
    let s = [];
    if (t) {
      let i = [];
      for (let r = 0; r < n.length; r++) {
        let o = n[r];
        (e == null || e(o) == !![]) && (i.push(o), s.push(o));
      }
      for (let r = 0; r < i.length; r++) {
        let o = i[r];
        if (o.children && o.children.length > 0) {
          let a = Yt.flatten(o.children, e, t);
          s = s.concat(a);
        }
      }
      return s;
    }
    for (let i = 0; i < n.length; i++) {
      let r = n[i];
      if ((e == null || e(r) == !![]) && (s.push(r), r.children && r[x(523)][x(524)] > 0)) {
        let o = Yt.flatten(r[x(523)], e);
        s = s.concat(o);
      }
    }
    return s;
  }
  static getNoChildrensObjects(n) {
    let e = Yt.flatten(n);
    return e = Yt.flatten(n, (t) => {
      const x = fe;
      return N.notContains(e, t[x(554)]);
    }), e;
  }
  static _anyMatrixOrStyleDirty(n) {
    const e = d;
    if (n.matrixDirty)
      return !![];
    let t = n[e(523)];
    for (let x = 0; x < t.length; x++) {
      let s = t[x];
      if (s[e(571)] || n[e(596)].dirty)
        return !![];
      if (Yt._anyMatrixOrStyleDirty(s))
        return !![];
    }
    return ![];
  }
  [d(625)](n) {
    const e = d;
    let t = er(this, this.serializers, this[e(509)]);
    if (n != null) {
      let x = n.objIndexMap, s = n.styleIndexMap;
      if (n.shapeIndexMap, n.resourcesIndexMap, x) {
        let i = s.get(this.style);
        t.style = i;
      }
      if (x && this.parent != null) {
        let i = x[e(547)](this.parent);
        i != null && (t.parent = i);
      }
    }
    return t.data != null && (Object.keys(t.data).length == 0 ? delete t.data : t[e(582)] = JSON.parse(JSON[e(566)](t.data))), t;
  }
  _afterFromJSON(n, e, t, x) {
    const s = d, i = this;
    if (vo(n, i), n.style != null) {
      let r = t[n.style];
      i.style = r;
    }
    if (n.image != null) {
      let r = e[n[s(617)]];
      if (r) {
        let o = r[s(528)];
        i.imageSrc = o;
      }
    }
    if (n.shape != null) {
      let r = x[n[s(585)]];
      i.setShape(r);
    }
  }
  set zIndex(n) {
    this._zIndex = n, this.setZIndex(n);
  }
  get zIndex() {
    return this._zIndex;
  }
  get x() {
    return this._x;
  }
  set x(n) {
    const e = d;
    this._x !== n && (this[e(571)] = !![]), this._x = n;
  }
  get y() {
    return this._y;
  }
  set y(n) {
    const e = d;
    this._y !== n && (this[e(571)] = !![]), this._y = n;
  }
  get width() {
    return this._width;
  }
  set width(n) {
    const e = d;
    this[e(516)] !== n && (this.matrixDirty = !![]), this[e(548)] = n;
  }
  get height() {
    return this._height;
  }
  set height(n) {
    const e = d;
    this._height !== n && (this[e(571)] = !![]), this[e(539)] = n;
  }
  get rotation() {
    return this._rotation;
  }
  set rotation(n) {
    this._rotation !== n && (this.matrixDirty = !![]), this._rotation = n;
  }
  get scaleX() {
    return this[d(587)];
  }
  set scaleX(n) {
    this._scaleX !== n && (this.matrixDirty = !![]), this._scaleX = n;
  }
  get [d(513)]() {
    return this._scaleY;
  }
  set [d(513)](n) {
    this[d(490)] !== n && (this.matrixDirty = !![]), this._scaleY = n;
  }
  get [d(545)]() {
    return this[d(526)];
  }
  set skewX(n) {
    this[d(526)] !== n && (this.matrixDirty = !![]), this._skewX = n;
  }
  get skewY() {
    return this._skewY;
  }
  set skewY(n) {
    const e = d;
    this._skewY !== n && (this[e(571)] = !![]), this._skewY = n;
  }
  get left() {
    return this.x - this.width * 0.5;
  }
  set left(n) {
    this.x = n + this.width * 0.5;
  }
  get right() {
    return this.x + this.width * 0.5;
  }
  set [d(603)](n) {
    this.x = n - this.width * 0.5;
  }
  get top() {
    return this.y - this.height * 0.5;
  }
  set top(n) {
    this.y = n + this.height * 0.5;
  }
  get [d(621)]() {
    return this.y + this.height * 0.5;
  }
  set [d(621)](n) {
    const e = d;
    this.y = n - this[e(601)] * 0.5;
  }
};
let C = Yt;
j([f("DisplayObject")], C.prototype, "className", 2), j([f(0)], C.prototype, "_x", 2), j([f(0)], C.prototype, "_y", 2), j([f(1)], C.prototype, "_width", 2), j([f(1)], C.prototype, "_height", 2), j([f(0)], C[d(562)], "_rotation", 2), j([f(1)], C[d(562)], "_scaleX", 2), j([f(1)], C.prototype, "_scaleY", 2), j([f(0)], C.prototype, "_skewX", 2), j([f(0)], C.prototype, d(493), 2), j([f(d(498))], C.prototype, "pickType", 2), j([f(!![])], C.prototype, "_cameraVisible", 2), j([f(null)], C.prototype, "paintSelected", 2), j([f(![])], C.prototype, "painted", 2), j([f(![])], C.prototype, d(623), 2), j([f(![])], C.prototype, "_isMouseInAABB", 2), j([f(!![])], C[d(562)], d(538), 2), j([f(5)], C[d(562)], d(514), 2), j([f(![])], C.prototype, d(506), 2), j([f(["id", d(546), "type", "zIndex", d(505), d(549), "selectedStyle", "mouseEnabled", "connectable", "showSelected", d(578), "visible", "origin", "classList", "dropAllowed", "data"])], C.prototype, "serializers", 2), j([f(["className"])], C.prototype, "allwaysSerializers", 2), j([f(0)], C[d(562)], "_zIndex", 2), j([f(![])], C.prototype, "frozen", 2), j([f(0)], C.prototype, "deep", 2), j([f(!![])], C.prototype, "_visible", 2), j([f(!![])], C[d(562)], d(507), 2), j([f(!![])], C.prototype, "serializeable", 2), j([f(!![])], C.prototype, d(517), 2), j([f(!![])], C.prototype, d(485), 2), j([f(!![])], C.prototype, "draggable", 2), j([f(![])], C.prototype, "isSelected", 2), j([f(!![])], C.prototype, d(549), 2), j([f(![])], C[d(562)], "dropAllowed", 2);
function vo(n, e) {
  const t = d;
  let x = e[t(529)], s = Object.getPrototypeOf(e);
  return x.forEach((i) => {
    const r = t;
    if (n.hasOwnProperty(i)) {
      let o = Object.getOwnPropertyDescriptor(e, i);
      if (o == null && (o = Object.getOwnPropertyDescriptor(s, i)), o != null && o[r(555)] == ![])
        return;
      let a = n[i];
      a != null && a[r(489)] != null && (a = hx(a.className, a)), e[i] = a;
    } else
      s[r(565)](i) && (e[i] = s[i]);
  }), e.id == null && (e.id = wt[t(551)]()), e;
}
function ko(n, e) {
  const t = d;
  if (e.parent != null)
    throw console[t(598)](e), new Error(t(579));
  if (n === e)
    throw console.log(n), new Error("添加自己为子节点了2");
  if (N[t(597)](n.getAncestors(), e) && (console.log(t(595)), console.log(n, e)), n.parent === e && (console[t(598)]("互为子节点了2"), console.log(n, e)), e[t(554)] = n, e[t(552)] = n[t(552)] + 1, n.children.indexOf(e) != -1)
    throw console[t(598)](n, e), new Error("重复添加");
  return n.children[t(599)](e), e[t(523)].length > 0 && e.updateChildrenDeep(!![]), n[t(508)](), n;
}
function cn() {
  var n = ["2432815KCeVvq", "styleY", "315291eNsANe", "2491160gPgZeR", "40330NKudVJ", "rgba(0,255,0, 0.9)", "606897DqdgEA", "5lPggco", "2229688MFSdTk", "12TJfLNd", "rgba(255,0,0,0.3)", "92674TCfTHQ", "24pLLUue", "styleX"];
  return cn = function() {
    return n;
  }, cn();
}
(function(n, e) {
  for (var t = ln, x = n(); []; )
    try {
      var s = -parseInt(t(463)) / 1 * (-parseInt(t(453)) / 2) + -parseInt(t(458)) / 3 * (parseInt(t(451)) / 4) + -parseInt(t(460)) / 5 * (parseInt(t(454)) / 6) + parseInt(t(456)) / 7 + parseInt(t(464)) / 8 + -parseInt(t(462)) / 9 + -parseInt(t(459)) / 10;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(cn, 193837);
function ln(n, e) {
  var t = cn();
  return ln = function(x, s) {
    x = x - 451;
    var i = t[x];
    return i;
  }, ln(n, e);
}
class So {
  constructor() {
    var e = ln;
    this.visible = ![], this.lineDashScale = 1, this[e(455)] = new ct({ strokeStyle: e(452), fillStyle: "rgba(255,0,0,0.9)", textAlign: "right", textBaseline: "top", lineWidth: 1 }), this[e(457)] = new ct({ strokeStyle: "rgba(0,255,0, 0.4)", fillStyle: e(461), textBaseline: "bottom", lineWidth: 1 });
  }
  show() {
    this.visible = !![];
  }
  hide() {
    this.visible = ![];
  }
}
function hn(n, e) {
  const t = un();
  return hn = function(x, s) {
    return x = x - 127, t[x];
  }, hn(n, e);
}
const fx = hn;
(function(n, e) {
  const t = hn, x = n();
  for (; []; )
    try {
      if (parseInt(t(136)) / 1 * (parseInt(t(133)) / 2) + parseInt(t(138)) / 3 + parseInt(t(128)) / 4 + -parseInt(t(131)) / 5 * (-parseInt(t(137)) / 6) + -parseInt(t(139)) / 7 * (-parseInt(t(135)) / 8) + parseInt(t(130)) / 9 + -parseInt(t(132)) / 10 * (parseInt(t(134)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(un, 905338);
function un() {
  const n = ["3823884FaJDlb", "413TydSaL", "setItem", "true", "getItem", "1237760cGLSbD", "_jtopo_debug_mode", "4952484rKFhKU", "14695AAkYWz", "970japzvd", "50cAMBVy", "447271KDZjbP", "66736GPwOBs", "27551hoNggr", "3132OnOkeg"];
  return un = function() {
    return n;
  }, un();
}
let _e = { isDebug: localStorage[fx(127)](fx(129)) == fx(141), showFPS: ![], paintAABB: ![], debugInfo: null, debugMode: function() {
  const n = fx;
  let e = localStorage.getItem("_jtopo_debug_mode") == "true", t = !e;
  localStorage[n(140)]("_jtopo_debug_mode", "" + t), _e.isDebug = t;
} };
(function(n, e) {
  for (var t = li, x = n(); []; )
    try {
      var s = -parseInt(t(282)) / 1 + parseInt(t(287)) / 2 + -parseInt(t(281)) / 3 + parseInt(t(280)) / 4 + -parseInt(t(284)) / 5 * (parseInt(t(285)) / 6) + parseInt(t(279)) / 7 * (-parseInt(t(286)) / 8) + parseInt(t(283)) / 9;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(dn, 987672);
function li(n, e) {
  var t = dn();
  return li = function(x, s) {
    x = x - 279;
    var i = t[x];
    return i;
  }, li(n, e);
}
class Js {
  constructor(e) {
    this.length = 0, this.x = e.x, this.y = e.y;
  }
}
function dn() {
  var n = ["342UFEWDz", "5746448OBSZYB", "2777590rnlvHc", "14HvpuFh", "4530212gUQBtv", "3638001XBsdNI", "1941569Pqzcnr", "40230279rvSROD", "123935IBgpnb"];
  return dn = function() {
    return n;
  }, dn();
}
(function(n, e) {
  const t = bt, x = n();
  for (; []; )
    try {
      if (parseInt(t(401)) / 1 * (parseInt(t(407)) / 2) + -parseInt(t(380)) / 3 + -parseInt(t(400)) / 4 * (-parseInt(t(404)) / 5) + parseInt(t(410)) / 6 * (parseInt(t(394)) / 7) + -parseInt(t(409)) / 8 + parseInt(t(395)) / 9 + parseInt(t(387)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(pn, 893355);
function Po(n, e, t, x) {
  const s = bt, i = n.x, r = e.x, o = n.y, a = e.y, c = [r - i, a - o];
  B[s(402)](c, c);
  const l = [-c[1] * x, c[0] * x], h = E.createPointsBidirectional(n, l, t), u = E.createPointsBidirectional(e, l, t);
  return [h, u];
}
function Oo(n, e, t) {
  const x = bt, s = n.x, i = e.x, r = n.y, o = e.y, a = [i - s, o - r];
  B.normalize(a, a);
  const c = [-a[1] * t, a[0] * t], l = E[x(403)](n, c, 1), h = E[x(403)](e, c, 1);
  return [l[0], h[0]];
}
function Lo(n, e, t) {
  const x = bt, s = [n.x - e.x, n.y - e.y], i = [t.x - e.x, t.y - e.y], r = B.normalize([], i), o = B.dot(s, r), a = B[x(383)]([], r, o);
  return a[x(386)] = o, a;
}
function Co(n, e, t) {
  const x = bt, s = Lo(n, e, t), i = [t.x - e.x, t.y - e.y], r = B.len(i), o = s[x(386)] / r, a = new Js();
  return a.x = e.x + s[0], a.y = e.y + s[1], a.segLen = r, a.projectionLen = s[x(386)], a[x(381)] = o, a;
}
function Eo(n, e, t) {
  const x = bt, s = [n.x - e.x, n.y - e.y], i = [t.x - e.x, t.y - e.y], r = B.normalize([], i), o = B[x(392)](i);
  let a;
  const c = B.dot(s, r);
  if (c > o)
    a = t;
  else if (c < 0)
    a = e;
  else {
    let h = B.multiplyC([], r, c);
    a = { x: e.x + h[0], y: e.y + h[1] };
  }
  let l = new Js(a);
  return l[x(408)] = o, l.projectionLen = c, l.rate = c / o, l;
}
function wi(n, e) {
  const t = bt;
  if (e.length < 2)
    throw new Error(t(388));
  let x = new Js(e[0]), s = Number.MAX_SAFE_INTEGER;
  for (let i = 0; i < e[t(398)] - 1; i++) {
    const r = e[i], o = e[i + 1], a = Eo(n, r, o), c = E[t(389)](a, n);
    c < s && (x = a, x.seg = [r, o], x[t(379)] = c, x.segIndex = i, s = c);
  }
  return x;
}
function bt(n, e) {
  const t = pn();
  return bt = function(x, s) {
    return x = x - 379, t[x];
  }, bt(n, e);
}
function fn(n, e, t, x, s = ![]) {
  const i = [e.x - n.x, e.y - n.y], r = [x.x - t.x, x.y - t.y], o = B.normalize([], [-i[1], i[0]]), a = B.normalize([], [-r[1], r[0]]), c = o[0], l = o[1], h = a[0], u = a[1], p = c * u - h * l;
  if (p == 0)
    return null;
  const b = B.dot(o, [n.x, n.y]), _ = B.dot(a, [t.x, t.y]), m = { x: (u * b - l * _) / p, y: (c * _ - h * b) / p };
  return s == ![] && (!Di(m, n, e) || !Di(m, t, x)) ? null : m;
}
function wr(n, e, t, x = ![]) {
  const s = bt;
  if (t[s(398)] < 2)
    throw new Error(s(406));
  let i = [];
  for (var r = 0; r < t.length - 1; r++) {
    const o = t[r], a = t[r + 1];
    let c = fn(n, e, o, a, x);
    c != null && i[s(393)](c);
  }
  return i = To(i), i;
}
function Di(n, e, t) {
  let x = [t.x - e.x, t.y - e.y], s = B.len(x), i = { x: (e.x + t.x) / 2, y: (e.y + t.y) / 2 }, r = [n.x - i.x, n.y - i.y];
  return B.len(r) <= s * 0.5 + 1e-8;
}
function To(n) {
  let e = {}, t = [];
  for (var x = 0; x < n.length; x++) {
    let s = n[x], i = s.x.toFixed(6) + "-" + s.y.toFixed(6);
    e[i] == null && (t.push(s), e[i] = s);
  }
  return t;
}
function pn() {
  const n = ["isNode", "lenght of points less than 2", "16SGwohe", "segLen", "13279792YvHBMd", "4613694OZNjMG", "dist", "4102146fmwLkJ", "rate", "isLink", "multiplyC", "getEndPoint", "getStageTransform", "projectionLen", "5652520KiiJAU", "number of points is less than 2", "distancePoint", "points", "point", "len", "push", "7nSzHwq", "12431682QTadxO", "unkwnow object!", "abs", "length", "object", "472BuYwbh", "3181pUQNqS", "normalize", "createPointsBidirectional", "49990htDyox"];
  return pn = function() {
    return n;
  }, pn();
}
function Ao(n, e, t, x) {
  const s = bt;
  let i = Number.MAX_SAFE_INTEGER, r = null, o = n.getTransform();
  for (var a = 0; a < t.length; a++) {
    const c = t[a], l = o.points(c._obb[s(390)]);
    c[s(405)] && l.push(l[0]);
    let h = wi(e, l);
    h.dist < i && h.dist < x && (i = h.dist, h.object = c, r = h);
  }
  return r;
}
function Mo(n, e, t) {
  const x = bt;
  let s = [];
  for (let i = 0; i < e.length; i++) {
    const r = e[i], o = r[x(385)]();
    let a = r.getAnchorPoints();
    a == null && console.log(r);
    for (let c = 0; c < a[x(398)]; c++) {
      let l = a[c], h = r.positionToLocalPoint(l), u = o[x(391)](h), p = E[x(389)](u, n);
      if (p < t) {
        const b = { distance: p, object: r, anchorName: l };
        s.push(b);
      }
    }
  }
  return s.sort((i, r) => i.distance - r.distance), s;
}
function Do(n, e) {
  const t = bt;
  let x = n.segIndex, s = n.rate, i = n[t(399)], r = s >= 0.25 && s <= 0.75, o = s - 0.5, a = Math[t(397)](n[t(408)] * o);
  if (i.isNode) {
    if (a > e || !r)
      return null;
    if (x == 0)
      return k.ct;
    if (x == 1)
      return k.rm;
    if (x == 2)
      return k.cb;
    if (x == 3)
      return k.lm;
    throw new Error("error segIndex:" + x);
  } else if (i[t(382)]) {
    let c = i.stageToLocalXY(n.x, n.y);
    return E.distancePoint(c, i.getBeginPoint()) <= e ? k.begin : E[t(389)](c, i[t(384)]()) <= e ? k.end : null;
  } else
    throw new Error(t(396));
}
function kx(n) {
  return Math.abs(Math.abs(n) % Math.PI) < 0.5;
}
function hi(n, e, t) {
  return n < e ? e : n > t ? t : n;
}
const y = K0;
function gn() {
  const n = ["origin", "defineProperty", "positionToLocalPoint", "_text", "hasBackgroundColor", "end", "setXYOnLink", "nearest", "originAlignPosition", "restore", "endArrow", "beginArrow", "_updateText", "width", "3918924vXeEKt", "originOffset", "_textArr", "_computedStyle", "black", "resizeToFitImage", "dirty", "_originInParent", "get", "height", "rotate", "_calcTextPosition", "scaleBy", "textAlign", "translate", "hasImage", "准备删除的方法", "setXY", "backgroundColor", "no-repeat", "rotateCenter", "strokeStyle", "fillStyle", "parent", "resourcesIndexMap", "image", "center", "obbPoints", "rotateWithParent", "min", "_calcTextSize", "_textHeight", "_textDirty", "6iDkKiB", "scaleTo", "mousePickupPath", "1137252LFxOmt", "lineWidth", "drawImage", "resize", "_zIndex", "zoom", "11KnwPlo", "1394080jupyQQ", "getOwnPropertyDescriptor", "translatePositionTo", "borderWidth", "textRotation", "_drawContentDesc", "imageSmoothingEnabled", "positions", "_calc_originInParent", "setOrigin", "26776dEGgfz", "1023720pmJvKv", "pickType", "translateWith", "6NCxgNs", "push", "scaleX", "_isMatrixDirty", "_afterStyleComputed", "setText", "cos", "_updateShapeSize", "Rect", "getImage", "forEach", "hasBorder", "repeat-x", "_calcOriginInParentLink", "onlyText", "shape", "zoomOut", "originAutoRotate", "objCache", "skew", "_textWidth", "split", "fillRect", "66577EsYGKh", "_imageSrc", "updateMatrix", "prototype", "1494CjaLmF", "fillText", "borderColor", "isNode", "_textLineHeight", "getPoint", "_textPosition", "_image", "tagName", "_paintText", "serializers", "scale", "setLineDash", "rotation", "right", "position not exist:", "90lTqPFr", "rect", "196998yHWDqy", "textBaseline", "getCtrlPoints", "scaleY", "textOffsetY", "alignOriginToNode", "save"];
  return gn = function() {
    return n;
  }, gn();
}
function K0(n, e) {
  const t = gn();
  return K0 = function(x, s) {
    return x = x - 374, t[x];
  }, K0(n, e);
}
(function(n, e) {
  const t = K0, x = n();
  for (; []; )
    try {
      if (parseInt(t(395)) / 1 * (parseInt(t(449)) / 2) + parseInt(t(473)) / 3 * (parseInt(t(452)) / 4) + -parseInt(t(459)) / 5 + -parseInt(t(393)) / 6 * (parseInt(t(496)) / 7) + -parseInt(t(469)) / 8 * (parseInt(t(377)) / 9) + -parseInt(t(470)) / 10 + -parseInt(t(458)) / 11 * (-parseInt(t(416)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(gn, 406742);
var Bo = Object[y(403)], jo = Object[y(460)], J = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? jo(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && Bo(e, t, s), s;
};
const vr = class extends C {
  constructor(n, e = 0, t = 0, x = 1, s = 1) {
    const i = y;
    super(), this._drawContentDesc = { hasBorder: ![], hasBackgroundColor: ![], hasBackgroundImage: ![], hasShape: ![], hasImage: ![], onlyImage: ![], onlyText: ![], shapeSize: { width: 1, height: 1 } }, this[i(443)] = [{ x: 0, y: 0 }, { x: 0, y: 0 }, { x: 0, y: 0 }, { x: 0, y: 0 }], this[i(423)] = { x: 0, y: 0 }, n != null && (this.text = n, this._textDirty = !![]), this._x = e || 0, this._y = t || 0, this._width = x || 0, this._height = s || 0;
  }
  get [y(490)]() {
    return this.rotateWithParent;
  }
  set originAutoRotate(n) {
    this.rotateWithParent = n;
  }
  get text() {
    return this._text;
  }
  set text(n) {
    this[y(478)](n);
  }
  get image() {
    return this._image;
  }
  set image(n) {
    this.setImage(n);
  }
  get imageSrc() {
    return this._imageSrc;
  }
  set imageSrc(n) {
    n == "canvas" && (n = null), this.setImage(n), this._imageSrc = n;
  }
  setOrigin(n, e) {
    const t = y;
    return this.origin[0] = n, this[t(402)][1] = e, this.matrixDirty = !![], this;
  }
  [y(408)](n, e) {
    return this.origin[0] = n, this.origin[1] = e, this.matrixDirty = !![], this;
  }
  setShape(n) {
    this.shape = n, this.matrixDirty = !![];
  }
  alignOriginToLink(n, e, t, x) {
    const s = y;
    x != null && (this.originOffset = x), this[s(410)] = n, this.setRotateCenter(n), e != null && this.setXYOnLink(e, this.origin[1]), t != null && this.setXYOnLink(this.origin[0], t), this.matrixDirty = !![];
  }
  [y(400)](n, e = y(442)) {
    const t = y;
    let x = de[e];
    this[t(468)](x.x, x.y), this[t(410)] = n, this.matrixDirty = !![];
  }
  _OBBPoints() {
    let n = this.width * 0.5, e = this.height * 0.5, t = -n, x = -e;
    return [{ x: t, y: x }, { x: n, y: x }, { x: n, y: e }, { x: t, y: e }];
  }
  [y(477)]() {
    const n = y, e = this._computedStyle, t = this._drawContentDesc;
    t.hasBackgroundColor = e[n(434)] != null, t.hasBorder = e[n(462)] > 0, t.hasBackgroundImage = e._backgroundImageObject != null, t.hasImage = this[n(384)] != null, t.hasShape = e.lineWidth > 0 && e.strokeStyle != null || e.fillStyle != null, t.onlyImage = (this._image || t.hasBackgroundImage) && !t.hasBorder && !t.hasShape && !t.hasBackgroundColor, t[n(487)] = this._text != null && !t[n(431)] && !t.hasBackgroundImage && !t.hasBorder && !t.hasShape && !t[n(406)];
  }
  _afterUpdateMatrix() {
    this._updateShapeSize();
  }
  _afterUpdate() {
    const n = y;
    (this[n(476)]() || this._textDirty || this._computedStyle[n(448)]) && (this._updateText(), this._textDirty && (this[n(448)] = ![], this.updateMatrix()), this[n(448)] = ![], this._computedStyle._textDirty = ![]);
  }
  [y(467)]() {
    const n = y, e = this[n(439)];
    if (e.isLink)
      return this[n(486)]();
    let t = this.originAlignPosition;
    if (this.origin[0] == 0 && this.origin[1] == 0 && t == null)
      return this.origin;
    let s = [this.origin[0] * e.width, this.origin[1] * e.height];
    if (t != null) {
      let i = this.positionToLocalPoint(t);
      s[0] -= i.x, s[1] -= i.y;
    }
    return s;
  }
  _calcOriginInParentLink() {
    const n = y, e = this.parent, t = this;
    if (this === e.beginArrow || t === e[n(412)])
      return [0, 0];
    let x = t.origin[0], s = t[n(402)][1], i = e.getPoint(s, x);
    if (t.originOffset) {
      let o = this._getOriginRotation();
      i = { x: i.x + t.originOffset * Math[n(479)](o), y: i.y + t.originOffset * Math.sin(o) };
    }
    let r = t.originAlignPosition;
    if (r != null) {
      let o = t.positionToLocalPoint(r);
      i.x -= o.x, i.y -= o.y;
    }
    return [i.x, i.y];
  }
  _getOriginRotation() {
    const n = y, e = this.parent;
    if (e == null || e.isLayer || e[n(380)] || !this.rotateWithParent)
      return 0;
    let t = e;
    if (this === t[n(413)] || this === t.endArrow)
      return 0;
    let x = this[n(402)][0], s = this[n(402)][1];
    return e.getK(x, s);
  }
  _doTransform(n) {
    const e = y, t = this;
    if (t[e(380)] && t[e(439)] == null)
      return;
    if (t.isLink)
      throw new Error("link no transform");
    let x = t[e(467)](), s = x[0] + t.x, i = x[1] + t.y;
    n[e(430)](s, i), (t.skewX != 0 || t.skewY != 0) && n.transform(1, t.skewX, t.skewY, 1, 0, 0), (t.scaleX !== 1 || t.scaleY !== 1) && n[e(388)](t.scaleX, t[e(398)]);
    let r = t._getOriginRotation() + t[e(390)];
    if (r != 0)
      if (t[e(436)] == "center")
        n.rotate(r);
      else {
        let o = t.positionToLocalPoint(t.rotateCenter);
        n.translate(o.x, o.y), n.rotate(r), n.translate(-o.x, -o.y);
      }
  }
  [y(480)]() {
    const n = y, e = this[n(419)];
    let t = e.borderWidth || 0, x = e.lineWidth || 0, s = e.padding || 0;
    const i = this[n(415)] - t * 2 - x - s * 2, r = this.height - t * 2 - x - s * 2;
    this[n(464)].shapeSize.width = i, this._drawContentDesc.shapeSize.height = r;
  }
  getSegmentPoints() {
    const n = y, e = this._OBBPoints();
    return this.shape.isClosed && e[n(474)](e[0]), e;
  }
  [y(482)](n) {
    const e = this;
    return new Promise((t, x) => {
      const s = K0;
      if (e[s(384)] != null) {
        n ? n(e._image) : t(e._image);
        return;
      }
      let i = et[s(491)].get(e);
      if (i == null) {
        n ? n(null) : x(null);
        return;
      }
      n == null && (n = function() {
        t(e._image);
      }), i.next = n;
    });
  }
  setImage(n, e = ![]) {
    const t = y;
    let x = this, s = n;
    return et.cancelCallback(this), s == null || s == "" ? (this._imageSrc = null, this._image = null, x.style[t(422)] = !![], this) : s[t(385)] == "IMG" ? (this._imageSrc = s.getAttribute("src"), x._image = s, e == !![] && x.resizeTo(s.width, s.height), x.style.dirty = !![], this) : s[t(385)] == "CANVAS" ? (this._imageSrc = "canvas", this._image = s, e == !![] && this.resizeTo(s[t(415)], s.height), x.style.dirty = !![], this) : (et.loadImageWithObj(this, s, function(i) {
      const r = t;
      i != null && (x._image = i, x._imageSrc = i.getAttribute("src"), e == !![] && x.resizeTo(i[r(415)], i.height), x.style.dirty = !![]);
    }), this);
  }
  setText(n) {
    const e = y;
    if (n != this[e(405)] && (this._textDirty = !![], this.matrixDirty = !![]), n == null) {
      this._text = n, this[e(418)] = null;
      return;
    }
    if (typeof n == "number" && (n = "" + n), n.indexOf(`
`) != -1) {
      let t = n[e(494)](`
`);
      this._textArr = t;
    } else
      this[e(418)] = null;
    this._text = n;
  }
  [y(446)]() {
    const n = y;
    let e = this[n(418)] == null ? 1 : this[n(418)].length, t;
    e == 1 ? t = ct.measureText(this._text, this._computedStyle, e) : t = ct.measureText(this._textArr, this._computedStyle, e), this[n(493)] = t.width, this._textHeight = t[n(425)], this[n(381)] = t.lineHeight;
  }
  attr(n) {
    const e = y;
    throw new Error(e(432));
  }
  [y(421)]() {
    this._image != null && this._image.width != null && this.resizeTo(this._image.width, this._image.height);
  }
  drawShape(n) {
    const e = y;
    if (this.shape.isUnit) {
      let t = this._drawContentDesc.shapeSize;
      n.scale(t.width, t.height), this[e(488)].draw(n, this), n[e(388)](1 / t.width, 1 / t[e(425)]);
    } else
      this.shape.draw(n, this);
  }
  _drawBackgroundOrBorder(n) {
    const e = y, t = this[e(419)], x = this[e(464)];
    let s = t.borderWidth || 0, i = -this.width * 0.5 + s * 0.5, r = -this[e(425)] * 0.5 + s * 0.5, o = this.width - s, a = this[e(425)] - s, c = t.borderRadius || 0;
    n.beginPath(), c == 0 ? n[e(394)](i, r, o, a) : n.roundRect(i, r, o, a, c), x.hasBackgroundColor && (n.fillStyle = t[e(434)], n.fill()), x[e(484)] && (n.lineWidth = s, n.strokeStyle = t[e(379)] || e(420), (t.borderStyle == "dashed" || t.borderStyle == "dotted") && n[e(389)]([1, 1]), n.stroke());
  }
  _drawImage(n, e, t, x, s) {
    const i = y;
    if (this[i(374)] != null && this._imageSrc.toLowerCase().endsWith("svg")) {
      n[i(454)](e, -this[i(415)] * 0.5, -this.height * 0.5, this[i(415)], this.height);
      return;
    }
    const r = this._computedStyle, o = Math.min(this.width, this[i(415)] - s - s), a = Math.min(this.height, this.height - s - s), c = r._getBackgroundRect(o, a, this);
    let l = -this.width * 0.5, h = -this.height * 0.5, u = l + c.x, p = h + c.y;
    const b = Math.min(o, c.width), _ = Math[i(445)](a, c.height);
    let m = e[i(415)], g = e.height;
    const w = i(435);
    if (w == "no-repeat") {
      n[i(465)] = r[i(465)] == null ? !![] : r.imageSmoothingEnabled, n[i(454)](e, 0, 0, m, g, u, p, b, _);
      return;
    }
    let O = n.createPattern(e, w);
    n.fillStyle = O;
    let A = u % m, D = p % g;
    w == i(485) ? D = 0 : w == "repeat-y" && (A = 0), n.translate(A, D), n[i(495)](l - m, h - g, o + m + m, a + g + g), n.translate(-A, -D);
  }
  _strokeAndFill(n) {
    const e = y;
    let t = this._computedStyle, x = this._drawContentDesc;
    n.save(), (x.hasBorder || x.hasBackgroundColor) && (this._drawBackgroundOrBorder(n), this[e(451)](n)), x.hasShape && (n.beginPath(), this.drawShape(n), t.fillStyle != null && (n.fillStyle = t[e(438)], n.fill()), t.lineWidth > 0 && t.strokeStyle != null && (n[e(453)] = t.lineWidth, n[e(437)] = t.strokeStyle, n.stroke()), this.isPointOn == ![] && this.pickType == e(488) && (this.shape.isClosed ? this[e(451)](n) : this.mousePickupStroke(n, this._pickRadius)));
    const s = this[e(384)];
    if (s) {
      x.hasShape && n.clip();
      let i = t.borderWidth || 0;
      this._drawImage(n, s, x.hasBorder, x.hasBackgroundColor, i);
    }
    n[e(411)]();
  }
  draw(n) {
    const e = y;
    if (this.width <= 0 || this.height <= 0)
      return;
    let t = this._drawContentDesc;
    if (this._strokeAndFill(n), t.onlyImage || t.onlyText || t.hasShape && this.isPointOn == ![] && this[e(471)] == e(394)) {
      let x = -this.width * 0.5, s = -this.height * 0.5;
      n[e(401)](), n.beginPath(), n.rect(x, s, this[e(415)], this.height), n.closePath(), this[e(451)](n), n[e(411)]();
    }
    this[e(386)](n);
  }
  [y(414)]() {
    const n = y;
    this._calcTextSize(), this[n(427)]();
  }
  _calcTextPosition(n = 0, e = 0) {
    const t = y;
    let x = this._computedStyle, s = null;
    x.textPosition != null ? s = this.positionToLocalPoint(x.textPosition) : s = { x: 0, y: this.height * 0.5 };
    let i = 0, r = -(this[t(447)] - this._textLineHeight) * 0.5;
    return x[t(429)] == "left" ? i = n + e : x.textAlign == t(391) && (i = -(n + e)), x.textBaseline == "top" ? r = n + e : x[t(396)] == "bottom" && (r = -(n + e) - this._textHeight + this._textLineHeight), s.x += i, s.y += r, x.textOffsetX != null && (s.x += x.textOffsetX), x.textOffsetY != null && (s.y += x[t(399)]), this[t(383)] = s, s;
  }
  _paintText(n) {
    const e = y;
    let t = this._text;
    if (t == null)
      return null;
    let x = this._computedStyle, s = this._textPosition || { x: 0, y: 0 }, i = this[e(418)];
    if (n.fillStyle = x.color || "black", this[e(463)] == 0)
      if (i == null)
        n[e(378)](t, s.x, s.y);
      else {
        let o = this[e(381)];
        for (var r = 0; r < i.length; r++)
          n.fillText(i[r], s.x, s.y + r * o);
      }
    else {
      if (n.translate(s.x, s.y), n.rotate(this.textRotation), i == null)
        n.fillText(t, 0, 0);
      else {
        let o = this._textLineHeight;
        for (var r = 0; r < i.length; r++)
          n.fillText(i[r], 0, 0 + r * o);
      }
      n.rotate(-this.text), n.translate(-s.x, -s.y);
    }
    return s;
  }
  getLinkChildren(n) {
    const e = y;
    let t = [], x = this.outLinks;
    for (var s = 0; s < x.length; s++) {
      let i = x[s], r = i[e(407)].target;
      !N.hasChild(t, r) && t[e(474)](r), n && r instanceof vr && r.outLinks.length > 0 && N.addAll(t, r.getLinkChildren(n));
    }
    return t;
  }
  [y(409)](n, e) {
    const t = this.getSegmentPoints(), x = wr({ x: n, y: e }, { x: 0, y: 0 }, t);
    return x == null || x.length == 0 ? { x: n, y: e } : x[0];
  }
  setRotateCenter(n) {
    return this.rotateCenter = n, this;
  }
  translateWith(n, e) {
    const t = y;
    return this.x += n, this.y += e, this.inLinks[t(483)]((x) => x.updateMatrix()), this.outLinks.forEach((x) => x[t(375)]()), this;
  }
  translateTo(n, e) {
    return this.x = n, this.y = e, this;
  }
  translate(n, e) {
    return this.x = n, this.y = e, this;
  }
  setXY(n, e) {
    return this.x = n, this.y = e, this;
  }
  [y(461)](n, e, t) {
    const x = y;
    let s = this[x(404)](n), i = e - this.x - s.x, r = t - this.y - s.y;
    this[x(472)](i, r);
  }
  resizeTo(n, e) {
    return this.width = n, this.height = e, this;
  }
  [y(455)](n, e) {
    const t = y;
    return this.width = n, this[t(425)] = e, this;
  }
  [y(492)](n, e) {
    return this.skewX = n, this.skewY = e, this;
  }
  resizeWith(n, e) {
    const t = y;
    return this.width += n, this.height += e, this[t(415)] < 0 && (this[t(415)] = 0), this[t(425)] < 0 && (this.height = 0), this;
  }
  [y(428)](n, e) {
    return this.scaleX *= n, this.scaleY *= e, this;
  }
  [y(457)](n, e, t, x) {
    return this.width * this.scaleX, this.height * this.scaleY, this.scaleBy(n, e), this;
  }
  [y(489)]() {
    return this[y(457)](0.8, 0.8), this;
  }
  zoomIn() {
    return this[y(457)](1.25, 1.25), this;
  }
  [y(450)](n, e) {
    const t = y;
    return this[t(475)] = n, this.scaleY = e, this;
  }
  scale(n, e) {
    return this.scaleX = n, this.scaleY = e, this;
  }
  rotateTo(n) {
    return this.rotation = n, this;
  }
  [y(426)](n) {
    return this.rotation = n, this;
  }
  rotateWith(n) {
    const e = y;
    return this[e(390)] += n, this;
  }
  getRect() {
    const n = y;
    return new z(this.x - this.width * 0.5, this.y - this[n(425)] * 0.5, this.width, this.height);
  }
  [y(382)](n, e) {
    let t = this.getLocalPoint(n, e);
    return { x: -this.width * 0.5 + t.x, y: -this.height + t.y };
  }
  changeParent(n) {
    const e = y;
    if (this[e(439)] === n)
      throw new Error("same parent, dont need change");
    let t = this, x = t.toStageXY(0, 0), s = n.stageToLocalXY(x.x, x.y);
    return t.parent && t[e(439)].removeChild(t), t[e(433)](s.x, s.y), n.addChild(t), this;
  }
  setOriginToPosition(n) {
    const e = y;
    let t = de[n];
    return this.origin[0] = t.x, this[e(402)][1] = t.y, this;
  }
  positionToLocalPoint(n, e, t) {
    const x = y;
    let s = this[x(466)][n];
    if (s == null && (s = de[n]), s == null)
      throw Error(x(392) + n);
    return typeof s == "function" ? s.call(this, e, t) : { x: s.x * this.width, y: s.y * this.height };
  }
  getPositionNormal(n) {
    const e = y;
    let t = this.positionToLocalPoint(k.center), x = this[e(404)](n);
    return z.normal(t, x);
  }
  toJSON(n) {
    const e = y;
    let t = super.toJSON(n);
    if (n != null) {
      let x = n.shapeIndexMap, s = n[e(440)], i = s[this.imageSrc];
      if (i != null && (t[e(441)] = i), this.isNode) {
        let r = x[e(424)](this[e(488)]);
        t.shape = r;
      }
    }
    return t;
  }
  destory() {
    super.destory(), this.shape = void 0, this._text = void 0, this._textArr = void 0, this._image = void 0, this._shapePoints = void 0;
  }
};
let M = vr;
J([f("Node")], M[y(376)], "className", 2), J([f(T[y(481)])], M.prototype, y(488), 2), J([f(null)], M[y(376)], "originAlignPosition", 2), J([f(!![])], M[y(376)], "rotateWithParent", 2), J([f(y(442))], M.prototype, "rotateCenter", 2), J([f(0)], M.prototype, y(417), 2), J([f(null)], M.prototype, "_text", 2), J([f(null)], M[y(376)], "_textArr", 2), J([f(![])], M[y(376)], y(448), 2), J([f(0)], M[y(376)], "_textWidth", 2), J([f(0)], M.prototype, "_textHeight", 2), J([f(0)], M.prototype, "_textLineHeight", 2), J([f(!![])], M.prototype, y(380), 2), J([f(cx.Node)], M.prototype, y(456), 2), J([f(0)], M.prototype, "textRotation", 2), J([f(["className", y(380)])], M[y(376)], "allwaysSerializers", 2), J([f(C.prototype.serializers.concat(["text", "x", "y", y(415), "height", "shape", "scaleX", "scaleY", y(390), "originOffset", "textOffsetX", y(399), y(463), "originAlignPosition", "rotateCenter", y(444), "originAutoRotate"]))], M[y(376)], y(387), 2), J([f(function() {
  return this[y(488)].ctrlPoints;
})], M.prototype, y(397), 2), J([f(function() {
  return this.shape.anchorPoints;
})], M.prototype, "getAnchorPoints", 2), J([f(y(488))], M.prototype, "pickType", 2);
const tt = He;
(function(n, e) {
  const t = He, x = n();
  for (; []; )
    try {
      if (-parseInt(t(183)) / 1 * (-parseInt(t(182)) / 2) + -parseInt(t(177)) / 3 + -parseInt(t(190)) / 4 + parseInt(t(188)) / 5 + parseInt(t(196)) / 6 * (-parseInt(t(178)) / 7) + -parseInt(t(194)) / 8 * (-parseInt(t(179)) / 9) + parseInt(t(201)) / 10 * (-parseInt(t(199)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(bn, 503266);
var zo = Object.defineProperty, No = Object.getOwnPropertyDescriptor, P0 = (n, e, t, x) => {
  const s = He;
  for (var i = x > 1 ? void 0 : x ? No(e, t) : e, r = n[s(184)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && zo(e, t, i), i;
};
function He(n, e) {
  const t = bn();
  return He = function(x, s) {
    return x = x - 175, t[x];
  }, He(n, e);
}
class Ft {
  isDisplayObjectTarget() {
    return this.target instanceof C;
  }
  isNodeTarget() {
    return this instanceof Fe || this instanceof ux || this instanceof Xe ? this.target.isNode : ![];
  }
  hasTarget() {
    return this.target != null;
  }
  toJSON(e) {
    const t = He;
    let x = this.className, s = Object[t(191)]({ className: x }, this);
    if (this.target == null)
      delete s.target;
    else if (e != null && this[t(189)]()) {
      let i = e[t(176)](this.target);
      s[t(192)] = i;
    }
    return s;
  }
  static fromJSON(e, t) {
    const x = He;
    let s = hx(e.className, e);
    if (t != null && typeof e.target == "number") {
      let i = t[e.target];
      s[x(192)] = i;
    }
    return s;
  }
}
P0([f("Endpoint")], Ft.prototype, "className", 2);
class ux extends Ft {
  constructor(e, t, x = 0) {
    super(), this.target = e, this.t = t, this.segIndex = x;
  }
}
P0([f("EndpointSegment")], ux[tt(181)], tt(197), 2);
class Fe extends Ft {
  constructor(e, t) {
    const x = tt;
    super(), this[x(192)] = e, this.name = t;
  }
  getAngle() {
    return $i(this[tt(198)]) + Math.PI;
  }
  [tt(195)]() {
    return this[tt(192)].isNode ? Qi(this.name) : [0, 0];
  }
}
P0([f(tt(185))], Fe.prototype, "className", 2), Fe.prototype[tt(197)] = "EndpointFixedName";
class Xe extends Ft {
  constructor(e) {
    super(), this.target = e;
  }
  getAngle(e) {
    const t = tt;
    let x = 0;
    if (this.target.isNode) {
      let s = this[t(192)];
      x = Math.atan2(e.y - s.y, e.x - s.x);
    }
    return x;
  }
}
P0([f("EndpointNearest")], Xe.prototype, tt(197), 2), Xe[tt(181)].className = tt(187);
class Vt extends Ft {
  constructor(e, t, x) {
    super(), this.x = e, this.y = t, this.target = x;
  }
}
P0([f("EndpointFixedPoint")], Vt[tt(181)], "className", 2);
class Vs extends Ft {
  constructor(e) {
    super(), this.fn = e;
  }
  toJSON() {
    let e = this.className, t = this.fn();
    return Object.assign({ className: e, x: t.x, y: t.y }, this);
  }
}
P0([f("EndpointFunction")], Vs.prototype, "className", 2);
function Jt(n, e) {
  const t = tt;
  if (n == null)
    throw new Error("target is null");
  if (n instanceof Ft)
    return n;
  if (e instanceof Ft)
    return e;
  if (n instanceof C)
    return e == k.nearest ? new Xe(n) : new Fe(n, e || k[t(186)]);
  if (E.isLikePoint(n)) {
    let x = n;
    return new Vt(x.x, x.y);
  } else {
    if (typeof n == "function")
      return new Vs(n);
    throw console.log(n, e), new Error("error arguments");
  }
}
const Ro = { lm: [-1, 0], ct: [0, -1], rm: [1, 0], cb: [0, 1] };
function ui(n) {
  const e = tt;
  if (n == null)
    return;
  let t = n.target;
  if (typeof t != e(200)) {
    if (n instanceof Fe)
      return t.className == "ShapeNode" ? t[e(180)](n.name) : Ro[n.name];
    if (n instanceof ux) {
      let x = n.segIndex, s = n.t;
      if (t.className == "ShapeNode") {
        let i = t.positionToLocalPoint(k.center), r = t.getLocalPoint(s, x);
        return z.normal(i, r);
      } else if (t instanceof Node) {
        if (x == 0)
          return [0, -1];
        if (x == 1)
          return [1, 0];
        if (x == 2)
          return [0, 1];
        if (x == 3)
          return [-1, 0];
        throw new Error(e(193) + x);
      }
      if (t.isAutoFoldLink) {
        let i = t[e(175)][x], r = t.points[x + 1], o = [r.x - i.x, r.y - i.y];
        return B.normalize([], o);
      }
    }
  }
}
function bn() {
  const n = ["get", "1263210lHbcPz", "1547938dRdCZY", "9TzFKMr", "getPositionNormal", "prototype", "443052DHDZRU", "2qHtxuR", "length", "EndpointFixedName", "center", "EndpointNearest", "4762430lmRxMN", "isDisplayObjectTarget", "2218520xGmOut", "assign", "target", "unkow segIndex:", "6547184tIyMja", "getVec", "6oGuJaR", "className", "name", "418KXidOU", "function", "135220FzfcRv", "points"];
  return bn = function() {
    return n;
  }, bn();
}
const Ue = Ee;
(function(n, e) {
  const t = Ee, x = n();
  for (; []; )
    try {
      if (-parseInt(t(455)) / 1 + parseInt(t(440)) / 2 + -parseInt(t(451)) / 3 + parseInt(t(446)) / 4 + parseInt(t(443)) / 5 * (parseInt(t(452)) / 6) + parseInt(t(442)) / 7 * (parseInt(t(447)) / 8) + -parseInt(t(453)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(yn, 495292);
function yn() {
  const n = ["632328NVcLQX", "endpoints", "length", "splice", "975393SpaPFj", "1038BBlIMh", "2452419NdMMIg", "target", "887143FQicHq", "582050HuubEU", "getEnd", "14PGasVR", "22270ZwGDTM", "remove", "setEnd", "3041632wzxcTV"];
  return yn = function() {
    return n;
  }, yn();
}
function Ee(n, e) {
  const t = yn();
  return Ee = function(x, s) {
    return x = x - 440, t[x];
  }, Ee(n, e);
}
class Wo {
  constructor() {
    this.endpoints = [];
  }
  toJSON(e, t) {
    let x = t.getPoints();
    return this.endpoints.map((i, r) => {
      const o = Ee;
      let a = i.toJSON(e);
      if (a.hasOwnProperty(o(454)) && a.target == null) {
        let c = x[0];
        t instanceof Oi ? c = x[r] : r > 0 && (c = x[x.length - 1]);
        let l = { className: "EndpointFixedPoint", x: c.x, y: c.y };
        return console.log("rs", r, x, l), l;
      }
      return a;
    });
  }
  set(e) {
    const t = Ee, x = this[t(448)];
    x.length = 0;
    for (let s = 0; s < e.length; s++) {
      let i = Jt(e[s]);
      x.push(i);
    }
    return x;
  }
  insert(e, t) {
    const x = Ee;
    let s = Jt(e);
    return t == null && (t = this.endpoints[x(449)]), t + 1 >= this.endpoints.length ? this[x(448)].push(s) : this.endpoints.splice(t + 1, 0, s), s;
  }
  replace(e, t) {
    this.endpoints[t] != null && (this.endpoints[t] = Jt(e));
  }
  [Ue(444)](e) {
    const t = Ue;
    this[t(448)][t(449)] <= 2 || this.endpoints[t(450)](e, 1);
  }
  setBegin(e) {
    this.endpoints[0] = Jt(e);
  }
  [Ue(445)](e) {
    const t = Ue;
    if (this.endpoints.length < 2) {
      this[t(448)][1] = Jt(e);
      return;
    }
    let x = this.endpoints.length - 1;
    this.endpoints[x] = Jt(e);
  }
  getLength() {
    const e = Ue;
    return this[e(448)][e(449)];
  }
  getBegin() {
    return this.endpoints[0];
  }
  [Ue(441)]() {
    let e = this.endpoints.length - 1;
    return this.endpoints[e];
  }
}
const I = mn;
(function(n, e) {
  const t = mn, x = n();
  for (; []; )
    try {
      if (-parseInt(t(500)) / 1 * (parseInt(t(516)) / 2) + parseInt(t(509)) / 3 + -parseInt(t(553)) / 4 + -parseInt(t(530)) / 5 + parseInt(t(538)) / 6 * (parseInt(t(557)) / 7) + -parseInt(t(494)) / 8 + parseInt(t(562)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(_n, 514200);
var Ho = Object.defineProperty, Fo = Object[I(544)], Dt = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? Fo(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && Ho(e, t, s), s;
};
const Sx = {};
Sx[k.begin] = function() {
  return this.getPoints()[0];
}, Sx[k.end] = function() {
  const n = I;
  let e = this.getPoints();
  return e[e[n(529)] - 1];
}, Sx[k.center] = function() {
  const n = I;
  let e = this.getPoints();
  return E[n(578)](e[0], e[e.length - 1]);
};
class R extends C {
  constructor(e, t, x, s, i) {
    const r = I;
    if (super(), this._drawContentDesc = { hasBorder: ![], hasBackgroundColor: ![] }, this[r(539)] = e, this.path = new Wo(), t == null) {
      const o = new Vt(0, 0);
      this.path[r(540)][r(588)](o);
    } else
      this[r(506)](t, s);
    if (x == null) {
      const o = new Vt(0, 0);
      this.path[r(540)][r(588)](o);
    } else
      this.setEnd(x, i);
  }
  get begin() {
    return this[I(537)].getBegin();
  }
  get end() {
    return this.path.getEnd();
  }
  get [I(539)]() {
    const e = I;
    return this[e(551)] != null ? this.label[e(539)] : this._text;
  }
  set text(e) {
    const t = I;
    this[t(571)](e), this[t(526)] = e;
  }
  setLabel(e) {
    const t = I;
    return e == null && this.label == null ? this : e instanceof M ? (kt[t(535)][t(495)](this, e), e.mouseEnabled = ![], e) : this[t(551)] != null && typeof e == "string" ? (this.label.text = e, this) : (kt.LinkHelper[t(517)](this, e), this);
  }
  attr(e) {
    const t = I;
    throw new Error(t(528));
  }
  getLabel() {
    return this[I(551)];
  }
  getBeginArrow() {
    return this.beginArrow;
  }
  setBeginArrow(e) {
    const t = I;
    if (!(e instanceof M))
      throw new Error("arrow must be Node");
    let x = this;
    return x[t(582)] != null && x[t(523)](x.beginArrow), x.children.indexOf(e) == -1 && x.addChild(e), e.draggable = ![], e.connectable = ![], e.editable = ![], x[t(582)] = e, this.matrixDirty = !![], this;
  }
  getEndArrow() {
    return this[I(559)];
  }
  setEndArrow(e) {
    const t = I;
    if (!(e instanceof M))
      throw new Error("arrow must be Node");
    let x = this;
    return x[t(559)] != null && x[t(523)](x[t(559)]), x[t(503)].indexOf(e) == -1 && x.addChild(e), e.draggable = ![], e.connectable = ![], e.editable = ![], x.endArrow = e, this.matrixDirty = !![], this;
  }
  [I(548)]() {
    return this.getPoints();
  }
  setBegin(e, t) {
    const x = I;
    this.matrixDirty = !![], this[x(533)] && this.begin.isDisplayObjectTarget() && this[x(533)].target.removeOutLink(this);
    const s = Jt(e, t);
    this.path.setBegin(s), s[x(512)] instanceof C && s.target[x(545)](this);
  }
  [I(499)](e, t) {
    const x = I;
    this[x(519)] = !![], this.end && this[x(568)].isDisplayObjectTarget() && this.end[x(512)][x(532)](this);
    const s = Jt(e, t);
    this.path.setEnd(s), s.target instanceof C && s.target.addInLink(this);
  }
  getBeginPoint() {
    const e = I;
    return this.points == null && (this[e(564)] = this.updatePoints()), this[e(564)][0];
  }
  [I(522)]() {
    return this.points == null && (this.points = this.updatePoints()), this.points[this.points.length - 1];
  }
  drawPoints(e) {
    return this[I(576)](e);
  }
  drawShape(e) {
    const t = I;
    let x = this[t(564)];
    if (this._needCalcOffset() && (x = this.offsetPoints, x == null))
      throw new Error("offsetPoints is null");
    let s = Zt.toAABB(x), i = s.getCenter(), r = Math.max(1, Math[t(585)](s[t(515)], s.width));
    e.beginPath(), e.translate(i.x, i.y), e.scale(r, r);
    let o = N.getPointsNormalization(x);
    this[t(536)].draw(e, this, o), e.scale(1 / r, 1 / r), e[t(502)](-i.x, -i.y), e[t(525)]();
  }
  [I(556)]() {
    const e = I, t = this[e(547)], x = this._computedStyle;
    let s = x.lineWidth || 0;
    return s += x[e(555)] || 0, t.hasBorder && (s += x.borderWidth), s;
  }
  draw(e) {
    const t = I;
    if (this.begin == null || this.end == null)
      return this;
    if (this.drawShape(e), !this.isPointOn) {
      const x = this._pickRadius, s = this._computedStyle[t(508)] >= x ? 0 : x;
      this[t(572)](e, s);
    }
    return this;
  }
  _afterStyleComputed() {
    const e = I, t = this._computedStyle, x = this[e(547)];
    x.hasBackgroundColor = t[e(524)] != null, x[e(579)] = t.borderWidth > 0;
  }
  setBeginOffset(e) {
    const t = I;
    this[t(542)] = e, this[t(519)] = !![];
  }
  setEndOffset(e) {
    const t = I;
    this[t(514)] = e, this.matrixDirty = !![];
  }
  [I(586)](e, t) {
    const x = I;
    let s = this.path[x(540)];
    for (let i = 0; i < s[x(529)]; i++) {
      let r = s[i];
      r instanceof Vt && r.target == null && (r.x += e, r.y += t, this.matrixDirty = !![]);
    }
    return this[x(519)] && (this[x(590)](), this.inLinks.forEach((i) => i.updateMatrix()), this.outLinks.forEach((i) => i.updateMatrix())), this;
  }
  _calcAZ() {
    let e = s0(this, this.begin), t = s0(this, this.end);
    return [e, t];
  }
  _needCalcOffset() {
    const e = I;
    return this[e(542)] != null && this.beginOffset != 0 ? !![] : this.endOffset != null && this[e(514)] != 0 ? !![] : this[e(582)] != null && this.beginArrow.visible ? !![] : this.endArrow != null && this.endArrow.visible ? !![] : ![];
  }
  _offsetA(e, t) {
    let x = this.beginArrow, s = this.beginOffset || 0;
    if (x != null && x.visible && (s = s + x.width), s != 0) {
      let i = this.getBeginArrowDirection();
      this._setOffsetByVec(e, i, s);
    }
  }
  [I(574)](e, t) {
    const x = I;
    let s = this.endArrow, i = this[x(514)] || 0;
    if (s != null && s.visible && (i = i + s.width), i != 0) {
      let r = this.getEndArrowDirection();
      this._setOffsetByVec(t, r, i);
    }
  }
  _setOffsetByVec(e, t, x) {
    let s = [0, 0];
    B.multiplyC(s, t, -x), e.x += s[0], e.y += s[1];
  }
  _notUpdateYet() {
    console[I(534)]("not yet"), this.points = this.updatePoints();
  }
  _afterUpdateMatrix() {
    this.points = this.updatePoints(), this._offsetAndArrowHandle();
  }
  _offsetAndArrowHandle() {
    const e = I;
    if (this[e(582)] && this[e(582)].parent !== this && (this[e(582)] = null), this.endArrow && this.endArrow[e(520)] !== this && (this.endArrow = null), !this[e(505)]())
      return;
    let t = this.points[0], x = this[e(564)][this[e(564)].length - 1], s = { x: t.x, y: t.y }, i = { x: x.x, y: x.y };
    if (this._offsetA(s, i), this[e(574)](s, i), this.beginArrow) {
      let o = { x: (t.x + s.x) * 0.5, y: (t.y + s.y) * 0.5 }, a = Math[e(501)](t.y - o.y, t.x - o.x);
      this[e(582)].setXY(o.x, o.y), this.beginArrow.rotateTo(a);
    }
    if (this.endArrow) {
      let o = { x: (x.x + i.x) * 0.5, y: (x.y + i.y) * 0.5 }, a = Math.atan2(x.y - o.y, x.x - o.x);
      this.endArrow.setXY(o.x, o.y), this.endArrow.rotateTo(a);
    }
    let r = this._obb.localPoints[e(518)]();
    r = r.slice(), r[0] = s, r[r.length - 1] = i, this.offsetPoints = r;
  }
  updatePoints() {
    const e = I;
    let t = [];
    const x = this[e(537)][e(540)];
    for (let s = 0; s < x.length; s++) {
      let i = x[s], r = s0(this, i);
      t.push(r);
    }
    return t;
  }
  [I(580)]() {
    const e = I;
    return this.points == null && (this.points = this[e(531)]()), this.points;
  }
  getTransform() {
    return this[I(549)];
  }
  [I(583)]() {
  }
  nearest(e, t) {
    const x = this.getSegmentPoints(), s = wi({ x: e, y: t }, x);
    return s == null || s.length == 0 ? { x: e, y: t } : s;
  }
  getSegmentPoints() {
    return this[I(580)]();
  }
  upgradeParent() {
    const e = I;
    let t = this.begin.target, x = this.end.target;
    if (t == null || x == null)
      return;
    let s = Px(t, x);
    if (this[e(520)] !== s)
      return this[e(581)](s), s;
  }
  [I(511)]() {
    const e = I;
    let t = this[e(564)][0], x = this[e(510)](1e-7), s = [t.x - x.x, t.y - x.y];
    return B.normalize(s, s);
  }
  [I(575)]() {
    const e = I;
    let t = this.getPoint(0.999999), x = this[e(564)][this.points[e(529)] - 1], s = [x.x - t.x, x.y - t.y];
    return B.normalize(s, s);
  }
  [I(554)]() {
    return this.begin.isDisplayObjectTarget() || this.end.isDisplayObjectTarget() ? ![] : !![];
  }
  isBeginDisplayObject() {
    const e = I;
    return this[e(533)][e(493)]();
  }
  isEndDisplayObject() {
    const e = I;
    return this.end[e(493)]();
  }
  unlinkBegin() {
    this.begin.isDisplayObjectTarget() && this.begin.target.removeOutLink(this), this.setBegin(this.getBeginPoint());
  }
  unlinkEnd() {
    this.end.isDisplayObjectTarget() && this.end.target.removeInLink(this), this.setEnd(this.getEndPoint());
  }
  unlink() {
    const e = I;
    this.unlinkBegin(), this[e(550)](), this.matrixDirty = !![];
  }
  remove() {
    const e = I;
    return this.unlink(), super[e(584)](), this;
  }
  [I(581)](e) {
    const t = I;
    if (this.parent === e)
      throw new Error("same parent, dont need change");
    let x = this, s = this;
    function i(r, o, a) {
      let c = r[o];
      c != null && (c = r.toStageXY(c.x, c.y), r[o] = a.stageToLocalXY(c.x, c.y));
    }
    if (s.begin instanceof Vt && !s.begin[t(573)]()) {
      let r = s.toStageXY(s.begin.x, s[t(533)].y), o = e[t(565)](r.x, r.y);
      s[t(506)](o);
    }
    if (s.end instanceof Vt && !s.end.hasTarget()) {
      let r = s.toStageXY(s.end.x, s.end.y), o = e.stageToLocalXY(r.x, r.y);
      s[t(499)](o);
    }
    return s[t(560)] == "CurveLink" ? i(s, t(561), e) : s[t(560)] == "BezierLink" ? (i(s, "ctrlPoint1", e), i(s, "ctrlPoint2", e)) : s.className == t(567) && (i(s, "fold1Offset", e), i(s, "fold2Offset", e), i(s, t(570), e)), x.parent && x.parent.removeChild(x), e.addChild(x), this;
  }
  [I(541)](e, t, x) {
    let s = this.positions[e];
    if (s == null && (s = this.DefaultPositions[e]), s == null)
      throw Error("position not exist:" + e);
    return typeof s == "function" ? s.call(this, t, x) : s;
  }
  [I(587)](e) {
    const t = I;
    let x = super[t(587)](e);
    if (e != null) {
      let s = e.objIndexMap;
      x.path = this[t(537)][t(587)](s, this), this.label != null && (x[t(551)] = s[t(563)](this.label)), this.beginArrow != null && (x.beginArrow = s.get(this.beginArrow)), this[t(559)] != null && (x[t(559)] = s[t(563)](this[t(559)]));
    }
    return x;
  }
  [I(507)](e, t) {
    const x = I;
    if (e.label != null) {
      let r = t[e.label];
      this[x(551)] = r;
    }
    if (e.beginArrow != null) {
      let r = t[e.beginArrow];
      r[x(513)] = null, r.rotateCenter = x(552), this.setBeginArrow(r);
    }
    if (e.endArrow != null) {
      let r = t[e.endArrow];
      r.originAlignPosition = null, r.rotateCenter = "center", this.setEndArrow(r);
    }
    let s = e.path, i = s[x(569)]((r) => Ft.fromJSON(r, t));
    this._setEndpoints(i);
  }
  _setEndpoints(e) {
    const t = I;
    this.path.set(e);
    let x = this.path[t(521)]();
    x[t(493)]() && x[t(512)].addOutLink(this), x = this.path.getEnd(), x[t(493)]() && x[t(512)][t(589)](this), e = this[t(537)][t(540)];
    for (let s = 1; s < e.length - 1; s++)
      x = e[s], x.isDisplayObjectTarget() && x[t(512)].addInLink(this);
  }
  destory() {
    const e = I;
    super.destory(), this.unlinkBegin(), this[e(550)](), this.beginArrow = null, this[e(559)] = null, this.label = null, this[e(536)] = null, this.points = null, this[e(504)] = null, this.offsetPoints = null, this._text = null;
  }
}
Dt([f("Link")], R.prototype, "className", 2), Dt([f(T.Line)], R.prototype, "shape", 2), Dt([f(cx.Link)], R.prototype, "_zIndex", 2), Dt([f(!![])], R.prototype, I(546), 2), Dt([f(5)], R.prototype, "_pickRadius", 2), Dt([f(Sx)], R.prototype, "DefaultPositions", 2), Dt([f(["className", "isLink"])], R[I(498)], I(591), 2), Dt([f(C[I(498)].serializers.concat(["beginOffset", I(514)]))], R.prototype, I(492), 2), Dt([f(function() {
  const n = I;
  return this.shape[n(491)];
})], R.prototype, "getAnchorPoints", 2), Dt([f(function() {
  return this[I(536)].ctrlPoints;
})], R.prototype, "getCtrlPoints", 2);
function _n() {
  const n = ["target", "originAlignPosition", "endOffset", "height", "14dQHvzt", "createLabel", "slice", "matrixDirty", "parent", "getBegin", "getEndPoint", "removeChild", "backgroundColor", "stroke", "_text", "Invalid link's position arguments", "准备删除的方法", "length", "1729550QeBRuR", "updatePoints", "removeInLink", "begin", "log", "LinkHelper", "shape", "path", "4467714ukiPKf", "text", "endpoints", "positionToLocalPoint", "beginOffset", "invert", "getOwnPropertyDescriptor", "addOutLink", "isLink", "_drawContentDesc", "_OBBPoints", "transform", "unlinkEnd", "label", "center", "2473488lmaYsa", "isAlone", "padding", "_getTotalLineWidth", "7myaZzp", "name", "endArrow", "className", "ctrlPoint", "17583831bZviBT", "get", "points", "stageToLocalXY", "deep", "AutoFoldLink", "end", "map", "centerOffset", "setLabel", "mousePickupStroke", "hasTarget", "_offsetZ", "getEndArrowDirection", "drawShape", "point", "middle", "hasBorder", "getPoints", "changeParent", "beginArrow", "_doTransform", "remove", "max", "translateWith", "toJSON", "push", "addInLink", "updateMatrix", "allwaysSerializers", "anchorPoints", "serializers", "isDisplayObjectTarget", "5652256muEkak", "asLabel", "copy", "getWorldTransform", "prototype", "setEnd", "132272gNlsqk", "atan2", "translate", "children", "unitPoints", "_needCalcOffset", "setBegin", "_updateBeginEndAfterJson", "lineWidth", "1237620FseHUF", "getPoint", "getBeginArrowDirection"];
  return _n = function() {
    return n;
  }, _n();
}
function s0(n, e) {
  const t = I;
  let x;
  if (e instanceof Fe) {
    let s = e.target;
    x = Jo(n, s, e[t(558)]);
  } else if (e instanceof Xe)
    x = Vo(n, e);
  else if (e instanceof ux) {
    let s = e[t(512)], i = e.segIndex, r = e.t;
    x = Yo(n, s, i, r);
  } else if (e instanceof Vt) {
    let s = e.target;
    s == null ? x = { x: e.x, y: e.y } : x = Xo(n, s, e.x, e.y);
  } else if (e instanceof Vs)
    x = e.fn();
  else
    throw console.log(e), new Error(t(527));
  return x;
}
function Xo(n, e, t, x) {
  const s = I;
  let i = e.getWorldTransform().point({ x: t, y: x });
  return i = n.getWorldTransform()[s(543)]().point(i), i;
}
function mn(n, e) {
  const t = _n();
  return mn = function(x, s) {
    return x = x - 491, t[x];
  }, mn(n, e);
}
function Yo(n, e, t, x) {
  let s;
  return e.parent === n.parent ? (s = e.getLocalPoint(x, t), s = e.getTransform().point(s)) : (s = e.getLocalPoint(x, t), s = e.getWorldTransform().point(s), s = n.getWorldTransform().invert().point(s)), s;
}
function Jo(n, e, t) {
  const x = I;
  let s;
  return e[x(520)] === n.parent ? (s = e.positionToLocalPoint(t), e.isNode && (s = e.getTransform()[x(577)](s))) : (s = e.positionToLocalPoint(t), s = e[x(497)]().point(s), s = n.getWorldTransform()[x(543)]().point(s)), s;
}
function Vo(n, e) {
  const t = I;
  if (n.deep == 0)
    throw new Error("link's deep is 0!");
  const x = e.target, s = n[t(533)][t(512)] === x, i = s ? n.end : n[t(533)], r = i.target;
  let o = n.getWorldTransform()[t(496)](), a;
  i instanceof Xe ? a = r.toLayerXY(0, 0) : (s ? a = s0(n, n.end) : a = s0(n, n.begin), a = o[t(577)](a));
  let c = x[t(497)]();
  a = c.copy()[t(543)]().point(a);
  let l = x.nearest(a.x, a.y);
  return l = c[t(577)](l), l = o[t(543)]().point(l), l;
}
function Px(n, e) {
  const t = I;
  return n[t(520)] === e[t(520)] ? n.parent : n[t(566)] == e[t(566)] ? Px(n[t(520)], e.parent) : n[t(566)] > e.deep ? Px(n.parent, e) : Px(n, e[t(520)]);
}
var Go = Q0;
function In() {
  var n = ["14782059UQQrTB", "24cCiEfR", "5928708KddOIF", "102326JVOVcy", "1548MmBvHc", "2VEJYRX", "368GTfDPg", "582583FQPEDe", "3585144VHbScK", "stoped", "10tgpaxu", "dispose", "58145odlcbV", "58223Vtxqcy"];
  return In = function() {
    return n;
  }, In();
}
(function(n, e) {
  for (var t = Q0, x = n(); []; )
    try {
      var s = -parseInt(t(185)) / 1 * (-parseInt(t(183)) / 2) + -parseInt(t(186)) / 3 + -parseInt(t(194)) / 4 + parseInt(t(190)) / 5 * (parseInt(t(193)) / 6) + -parseInt(t(195)) / 7 * (-parseInt(t(184)) / 8) + parseInt(t(192)) / 9 * (parseInt(t(188)) / 10) + -parseInt(t(191)) / 11 * (-parseInt(t(182)) / 12);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(In, 949550);
function Q0(n, e) {
  var t = In();
  return Q0 = function(x, s) {
    x = x - 182;
    var i = t[x];
    return i;
  }, Q0(n, e);
}
class qo extends Tt {
  constructor() {
    var e = Q0;
    super(), this[e(187)] = ![];
  }
  [Go(189)]() {
  }
}
const H = $0;
(function(n, e) {
  const t = $0, x = n();
  for (; []; )
    try {
      if (parseInt(t(463)) / 1 * (parseInt(t(472)) / 2) + -parseInt(t(486)) / 3 + -parseInt(t(471)) / 4 * (-parseInt(t(469)) / 5) + -parseInt(t(481)) / 6 * (parseInt(t(488)) / 7) + -parseInt(t(474)) / 8 + -parseInt(t(482)) / 9 * (-parseInt(t(485)) / 10) + parseInt(t(483)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(wn, 308598);
function $0(n, e) {
  const t = wn();
  return $0 = function(x, s) {
    return x = x - 462, t[x];
  }, $0(n, e);
}
function wn() {
  const n = ["342BXQMpk", "225loqCUe", "2841575TdaOUk", "name", "117010dmmzKS", "419601BtBNIx", "length", "37303irzuDj", "setStyle", "#009A93", "54kwbBoB", "content", "1px solid rgba(255,255,255,0.5)", "top", "assign", "white", "5VImidC", "FlexionalLink", "863932vNVMBg", "2432exhkHh", "stringify", "642232gWjdYy", "black", "center", "RatioNode", "toJSON", " repeat", "rgba(0,0,236,0.1)"];
  return wn = function() {
    return n;
  }, wn();
}
class kr {
  constructor(e) {
    const t = $0;
    if (this.Layer = {}, this.SelectArea = { border: "1px rgba(0,0,0,0.5)", backgroundColor: "rgba(0,0,236,0.1)" }, this.SelectedStyle = { strokeStyle: t(462), shadowColor: "#009A93" }, this.LinkArrow = {}, this.LinkLabel = { textPosition: "center", textBaseline: "middle", textAlign: "center" }, this.Node = { textPosition: "cb", textAlign: "center", textBaseline: "top" }, this.TextNode = { textPosition: t(476), textAlign: "center", textBaseline: "middle" }, this.CircleNode = { textPosition: "cb", textAlign: "center", textBaseline: t(466) }, this.TipNode = { textPosition: "ct", textAlign: "center", textBaseline: t(466) }, this.ShapeNode = { lineWidth: 1, textPosition: "cb", textAlign: "center", textBaseline: "top" }, this.VideoNode = { textPosition: "cb", textAlign: "center", textBaseline: t(466) }, this[t(477)] = { textPosition: "center", textAlign: "center", textBaseline: "middle" }, this.Link = { lineWidth: 1 }, this.FoldLink = { lineWidth: 1 }, this.CurveLink = { lineWidth: 1 }, this.AutoFoldLink = { lineWidth: 1 }, this.BezierLink = { lineWidth: 1 }, this.ArcLink = { lineWidth: 1 }, this[t(470)] = { lineWidth: 1 }, e == null)
      return;
    let x = Object.keys(e);
    for (let s = 0; s < x.length; s++) {
      let i = x[s];
      Object[t(467)](this[i], e[i]);
    }
  }
  [H(478)]() {
    const e = H;
    let t = {}, x = Object.keys(this);
    for (let s = 0; s < x[e(487)]; s++) {
      let i = x[s], r = Uo[i], o = this[i], a = Et.diff(o, r);
      a != null && (t[i] = a);
    }
    return t;
  }
}
const Uo = new kr();
class O0 {
  constructor(e, t) {
    const x = H;
    this[x(484)] = e, this.content = new kr(t);
  }
  copy(e) {
    let x = JSON.parse(JSON[H(473)](this.content));
    return new O0(e, x);
  }
  [H(489)](e, t) {
    let x = this.content[e];
    Object.assign(x, t);
  }
  getStyle(e) {
    return this[H(464)][e];
  }
}
let Te = new O0(Ve.DefaultLightName, { Layer: { background: Ht.createLightGridImg() + H(479) }, SelectArea: { border: "1px solid rgba(0,0,0,0.5)", backgroundColor: H(480) }, LinkArrow: { strokeStyle: "black" }, Node: { strokeStyle: H(475) }, TextNode: { strokeStyle: "black" }, TipNode: { strokeStyle: "black" }, ShapeNode: { strokeStyle: "black" }, CircleNode: { strokeStyle: "black" }, VideoNode: { strokeStyle: "black" }, RatioNode: { strokeStyle: "black" }, Link: { strokeStyle: "black" }, AutoFoldLink: { strokeStyle: H(475) }, FoldLink: { strokeStyle: "black" }, FlexionalLink: { strokeStyle: H(475) }, CurveLink: { strokeStyle: H(475) }, BezierLink: { strokeStyle: "black" }, ArcLink: { strokeStyle: H(475) } }), Sr = new O0(Ve.DefaultDarkName, { Layer: { background: Ht.createDarkGridImg() + H(479) }, SelectArea: { border: H(465), backgroundColor: "rgba(255,255,255,0.2)" }, LinkArrow: { strokeStyle: H(468) }, LinkLabel: { strokeStyle: "white", color: "white" }, Node: { strokeStyle: H(468), color: H(468) }, TextNode: { strokeStyle: "white", color: "white" }, TipNode: { strokeStyle: H(468), color: "white" }, ShapeNode: { strokeStyle: "white", color: H(468) }, CircleNode: { strokeStyle: "white", color: "white" }, VideoNode: { strokeStyle: "white", color: "white" }, RatioNode: { strokeStyle: H(468), color: "white" }, Link: { strokeStyle: "white" }, AutoFoldLink: { strokeStyle: "white" }, FoldLink: { strokeStyle: H(468) }, FlexionalLink: { strokeStyle: H(468) }, CurveLink: { strokeStyle: H(468) }, BezierLink: { strokeStyle: H(468) }, ArcLink: { strokeStyle: "white" } });
const F = Ae;
function vn() {
  const n = ["transform", "toDataURL", "selectedStyle", "context", "300GmVcvf", "2218782fbGjhY", "styleSystem", "createElement", "1058029IpfLYF", "beginPath", "isPointInPath", "63rwxhiz", "children", "stroke", "1497bmPlBH", "isSelected", "56859BsgPWi", "isMouseInPath", "10172RbvDOx", "hide", "style", "length", "_needPaint", "canvas", "closePath", "clearAll", "SelectedStyle", "width", "isMouseInStroke", "restore", "isPointInStroke", "remove", "paintSelected", "lineTo", "_computedStyle", "render", "currentTheme", "height", "strokeStyle", "getStyle", "save", "paint", "roundRect", "getContext", "_isMouseInAABB", "980uuubbz", "parent", "inputSystem", "1537488ylSgGr", "stage", "30408TpChye", "painted", "quadraticCurveTo", "1317452MMEbYu"];
  return vn = function() {
    return n;
  }, vn();
}
(function(n, e) {
  const t = Ae, x = n();
  for (; []; )
    try {
      if (parseInt(t(362)) / 1 + parseInt(t(368)) / 2 + -parseInt(t(377)) / 3 * (parseInt(t(381)) / 4) + -parseInt(t(408)) / 5 * (-parseInt(t(359)) / 6) + -parseInt(t(371)) / 7 + parseInt(t(357)) / 8 * (-parseInt(t(374)) / 9) + parseInt(t(367)) / 10 * (parseInt(t(379)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(vn, 809835);
function Ae(n, e) {
  const t = vn();
  return Ae = function(x, s) {
    return x = x - 355, t[x];
  }, Ae(n, e);
}
class Pr extends qo {
  constructor(e) {
    const t = Ae;
    super(), this.destoryed = ![], this.layer = e, this.canvas = document[t(370)]("canvas"), Object.assign(this.canvas.style, { position: "absolute", left: "0px" }), this.context = this.canvas[t(406)]("2d"), this.context[t(398)] = this, window.devicePixelRatio && this[t(366)].scale(window.devicePixelRatio, window.devicePixelRatio), this.contextExtends();
  }
  _paintFlattenObjects(e) {
    const t = Ae;
    let x = this[t(366)];
    for (let s = 0; s < e[t(384)]; s++) {
      let i = e[s];
      if (x[t(403)](), i instanceof M) {
        let r = i._worldTransform, o = r.m;
        x.transform(o[0], o[1], o[2], o[3], o[4], o[5]);
      } else if (!(i[t(355)] instanceof ft)) {
        let o = i[t(355)]._worldTransform, a = o.m;
        x[t(363)](a[0], a[1], a[2], a[3], a[4], a[5]);
      }
      this[t(404)](i), x.restore();
    }
  }
  _paintObjects(e, t) {
    const x = Ae;
    let s = this.layer, i = this[x(366)];
    for (let r = 0; r < e.length; r++) {
      let o = e[r];
      if (o[x(385)] == ![] || o._isOutOfViewport && !t)
        continue;
      i.save(), o instanceof M && o._doTransform(i), !t && (o[x(360)] = !![]), this.paint(o), s && s.displayList.push(o), this._paintObjects(o[x(375)], t), i.restore();
    }
  }
  [F(404)](e) {
    const t = F;
    let x = this[t(366)];
    e[t(397)].applyTo(x);
    let s = e[t(378)] && e.showSelected == !![];
    s && (x.save(), this.paintSelected(e)), e.draw(x), s && x.restore();
  }
  [F(395)](e) {
    const t = F;
    let x = this.context;
    if (e[t(365)] != null && e.selectedStyle.applyTo(x), e[t(395)] != null) {
      e.paintSelected(x);
      return;
    }
    if (e instanceof M) {
      x.save(), x.beginPath();
      let s = 1;
      e.selectedStyle != null ? s = e.selectedStyle.lineWidth | s : (x.setLineDash([0, 0]), this.styleSystem ? x[t(401)] = this[t(369)][t(399)][t(402)]("SelectedStyle").strokeStyle : x[t(401)] = Te.getStyle(t(389))[t(401)]), x.lineWidth = s;
      let i = e.width + s + 3, r = e.height + s + 3;
      x.rect(-i * 0.5, -r * 0.5, i, r), x[t(376)](), x[t(387)](), x[t(392)]();
    } else
      this[t(369)] ? x.shadowColor = this[t(369)].currentTheme.getStyle(t(389)).strokeStyle : x.strokeStyle = Te.getStyle("SelectedStyle").shadowColor, x.shadowBlur = 5, x.shadowOffsetX = 3, x.shadowOffsetY = 3;
  }
  [F(388)]() {
    this.clear();
  }
  dontNeedPickup(e) {
    const t = F;
    let x = this[t(358)];
    return x[t(356)].isDraging && x[t(356)].target !== e ? !![] : x.inputSystem.event instanceof MouseEvent && !e[t(407)] ? !![] : ![];
  }
  [F(391)](e, t) {
    const x = F;
    let s = this.context, i = this.stage;
    if (e == null || e == 0)
      return t != null ? s[x(393)](t, i.inputSystem.x, i[x(356)].y) : s.isPointInStroke(i.inputSystem.x, i.inputSystem.y);
    let r = ![];
    for (var o = 0; o < e; o++) {
      if (t != null ? r = s.isPointInStroke(t, i.inputSystem.x + o, i.inputSystem.y + o) : r = s.isPointInStroke(i.inputSystem.x + o, i[x(356)].y + o), r)
        return !![];
      if (t != null ? r = s.isPointInStroke(t, i.inputSystem.x - o, i[x(356)].y - o) : r = s.isPointInStroke(i.inputSystem.x - o, i.inputSystem.y - o), r)
        return !![];
      if (t != null ? r = s.isPointInStroke(t, i.inputSystem.x - o, i[x(356)].y + o) : r = s.isPointInStroke(i.inputSystem.x - o, i[x(356)].y + o), r)
        return !![];
      if (t != null ? r = s[x(393)](t, i.inputSystem.x + o, i[x(356)].y - o) : r = s.isPointInStroke(i.inputSystem.x + o, i.inputSystem.y - o), r)
        return !![];
    }
    return ![];
  }
  [F(380)](e) {
    const t = F;
    let x = this.context, s = this.stage;
    return e ? x.isPointInPath(e, s.inputSystem.x, s[t(356)].y) : x[t(373)](s.inputSystem.x, s[t(356)].y);
  }
  contextExtends() {
    const e = F;
    this.context[e(405)] == null && (this[e(366)][e(405)] = function(t, x, s, i, r) {
      const o = e;
      this[o(372)](), this.moveTo(t + r, x), this.lineTo(t + s - r, x), this.quadraticCurveTo(t + s, x, t + s, x + r), this.lineTo(t + s, x + i - r), this[o(361)](t + s, x + i, t + s - r, x + i), this.lineTo(t + r, x + i), this.quadraticCurveTo(t, x + i, t, x + i - r), this[o(396)](t, x + r), this.quadraticCurveTo(t, x, t + r, x), this[o(387)]();
    });
  }
  show() {
    const e = F;
    this[e(386)].style.display = "block";
  }
  [F(382)]() {
    const e = F;
    this[e(386)].style.display = "none";
  }
  getWidth() {
    const e = F;
    return this[e(386)][e(390)];
  }
  getHeight() {
    const e = F;
    return this.canvas[e(400)];
  }
  setSize(e, t) {
    this.canvas.width = e, this.canvas.height = t;
  }
  getCursor() {
    return this.canvas.style.cursor;
  }
  setCursor(e) {
    const t = F;
    this[t(386)][t(383)].cursor = e;
  }
  [F(364)](e, t) {
    const x = F;
    return e = e || "image/png", this[x(386)].toDataURL(e, t);
  }
  getRectImageData(e, t, x, s) {
    return e == null && (e = 0), t == 0 && (t = 0), x == null && (x = this.getWidth()), s == null && (s = this.getHeight()), this.context.getImageData(e, t, x, s);
  }
  clear() {
    const e = F;
    this[e(386)].width = this.canvas.width;
  }
  dispose() {
    const e = F;
    this.canvas[e(394)]();
  }
}
const T0 = tx;
function kn() {
  const n = ["2853200gPmHMe", "1184343KvMgLp", "516WPKilB", "getHeight", "axis", "7863IxCjaN", "layer", "bold 16px arial", "scaleY", "width", "paintLayer", "paintAABB", "restore", "lineTo", "lineWidth", "moveTo", "getObject", "context", "2jkoEMv", "beginPath", "30261aWqrze", "stage", "5628177TsyhZU", "_paintObjects", "_computedStyle", "31738320ZlwbZz", "applyTo", "displayList", "styleX", "localView", "save", "4745690iIzekH", "1652wruGSL", "clear"];
  return kn = function() {
    return n;
  }, kn();
}
(function(n, e) {
  const t = tx, x = n();
  for (; []; )
    try {
      if (parseInt(t(322)) / 1 * (-parseInt(t(305)) / 2) + -parseInt(t(292)) / 3 * (-parseInt(t(319)) / 4) + -parseInt(t(318)) / 5 + parseInt(t(323)) / 6 * (-parseInt(t(307)) / 7) + -parseInt(t(321)) / 8 + -parseInt(t(309)) / 9 + parseInt(t(312)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(kn, 769043);
function tx(n, e) {
  const t = kn();
  return tx = function(x, s) {
    return x = x - 290, t[x];
  }, tx(n, e);
}
var Zo = Et.gc;
let Bi = et.w;
class Or extends Pr {
  constructor(e) {
    super(e);
  }
  renderLayer(e) {
    const t = tx;
    this[t(320)](), this.stage = this.layer[t(308)], this.styleSystem = this.stage.styleSystem, e[t(291)].visible && this.paintAxis(e), this.paintLayer(e, ![]), _e[t(298)] == !![] && e.displayList != null && this.paintAABB();
    {
      if (Bi == null)
        return;
      let x = this.context;
      x[t(317)](), x.globalAlpha = 0.8, x.font = t(294);
      let s = Zo(Bi);
      x.fillStyle = "gray", x.fillText(s, 14, this[t(290)]() - 14), x[t(299)]();
    }
  }
  [T0(297)](e, t) {
    const x = T0;
    let s = this.context;
    s.save();
    let i = e.getTransform(), r = i.m;
    return s.transform(r[0], r[1], r[2], r[3], r[4], r[5]), e[x(311)].applyTo(s), Ve.flatten ? (e[x(314)].forEach((o) => o.painted = !![]), this._paintFlattenObjects(e.displayList)) : this[x(310)](e.children, t), s[x(299)](), e;
  }
  [T0(298)]() {
    const e = T0;
    let t = this[e(293)], x = this[e(304)], s = t.displayList;
    for (let i = s.length - 1; i >= 0; i--) {
      let r = s[i], o = r._obb.aabb;
      o = t.toStageRect(o), x.save(), r instanceof ft ? x.strokeStyle = "rgba(0,0,255,0.3)" : r instanceof R ? x.strokeStyle = "pink" : x.strokeStyle = "green", x[e(306)](), x.rect(o.x, o.y, o.width, o.height), x.stroke(), x.closePath(), x.restore();
    }
  }
  paintAxis(e) {
    const t = T0, x = e[t(291)], s = e.stage;
    let i = this.context;
    const r = s[t(316)][t(303)](), o = s[t(296)], a = s.height, c = r.toScreenXY(0, 0), l = e.scaleX, h = e[t(295)];
    i.save(), c.x > 0 && c.x < o && (i.beginPath(), x.styleY[t(313)](i), i.moveTo(c.x, 0), i.lineTo(c.x, a), i[t(302)](c.x - 5, a - 8), i.lineTo(c.x, a), i[t(300)](c.x + 5, a - 8), i.stroke(), i.fillText("+y", c.x + 6, a - 5)), c.y > 0 && c.y < a && (i.beginPath(), x[t(315)][t(313)](i), i[t(302)](o * 0.5, c.y), i[t(300)](0, c.y), i.moveTo(o * 0.5, c.y), i[t(300)](o, c.y), i.moveTo(o - 8, c.y - 5), i[t(300)](o, c.y), i[t(300)](o - 8, c.y + 5), i.stroke(), i.fillText("+x", o - 5, c.y + 5));
    let u = x[t(315)], p = x.styleY;
    u[t(301)] = Math.max(1, l), p.lineWidth = Math.max(1, h), i[t(299)]();
  }
}
function Sn(n, e) {
  const t = Pn();
  return Sn = function(x, s) {
    return x = x - 122, t[x];
  }, Sn(n, e);
}
const L = Sn;
(function(n, e) {
  const t = Sn, x = n();
  for (; []; )
    try {
      if (parseInt(t(174)) / 1 * (-parseInt(t(129)) / 2) + parseInt(t(140)) / 3 * (-parseInt(t(148)) / 4) + -parseInt(t(181)) / 5 + -parseInt(t(177)) / 6 * (parseInt(t(180)) / 7) + parseInt(t(194)) / 8 + parseInt(t(136)) / 9 * (parseInt(t(151)) / 10) + parseInt(t(164)) / 11 * (parseInt(t(204)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Pn, 417913);
var Ko = Object.defineProperty, Qo = Object[L(137)], Ze = (n, e, t, x) => {
  const s = L;
  for (var i = x > 1 ? void 0 : x ? Qo(e, t) : e, r = n[s(125)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && Ko(e, t, i), i;
};
function Pn() {
  const n = ["show", "12598SmPJys", "mouseY", "flatten", "zoomLayer", "_layerIndex", "find", "_findChildren", "4419CAydWs", "getOwnPropertyDescriptor", "hideAxis", "style", "12843QqbgSJ", "updateMatrix", "forEach", "resetTo", "canvas", "backgroundPosition", "_dragDrawDelay", "undefined", "344McULiS", "filter", "parseImgUrl", "11260TyAlsf", "_tickLayer", "updateZIndex", "updateViewRect", "update", "fillByJson", "clone", "width", "_zIndex", "string", "inputSystem", "openJson", "addChild", "22mEglGd", "backgroundImage", "viewportRect", "toAABB", "flattenList", "_calcBackgroundPosition", "toJsonObject", "Layer has been destroyed already.", "mouseX", "toLayerRect", "31McJKNe", "hide", "script", "8952ltNlWb", "render", "stage", "854rJAzSH", "1288155eBJkMl", "camera", "removeChild", "getAABB", "height", "stringify", "updateSize", "removeFromParent", "true", 'url("', "displayList", "_requestReapint", "unionRects", "4735384Aaegwi", "prototype", "_frames", "0px", "aabb", "setSize", "backgroundColor", "isPointOn", "addChilds", "_cameraVisible", "1657284sGXFrT", "toPoints", "keys", "_bgInfo", "then", "length", "_resetBg", "background"];
  return Pn = function() {
    return n;
  }, Pn();
}
class ft extends M {
  constructor(e) {
    const t = L;
    super(), this.className = "Layer", this.renderTimes = 0, this[t(166)] = new z(0, 0, 1, 1), this.cuttingHide = !![], this[t(191)] = [], this.flattenList = [], this.wheelZoom = !![], this.axis = new So(), this.mouseX = -1, this.mouseY = -1, this._frames = 0, this.isFirstComputeBG = !![], this[t(192)] = ![], this._dragDrawDelay = ![], this[t(123)] = { sw: null, sh: null, x: null, y: null }, this.name = e, this.visible = ![], this[t(191)] = [], _e.isDebug && (kt.layer = this), this.render = new Or(this), this[t(144)] = this.render[t(144)];
  }
  get frames() {
    return this[L(196)];
  }
  set frames(e) {
    const t = L;
    this[t(196)] = e;
  }
  _onMound(e) {
    const t = L;
    this.stage = e, this[t(178)][t(179)] = e;
  }
  [L(155)]() {
    return this[L(169)](), this._requestReapint = !![], !![];
  }
  showAxis() {
    const e = L;
    this.axis[e(128)]();
  }
  [L(138)]() {
    const e = L;
    this.axis[e(175)]();
  }
  setRender(e) {
    const t = L;
    this.render != null && this[t(178)].dispose(), this.render = e, this.update();
  }
  _isMatrixDirty() {
    return !![];
  }
  [L(141)](e) {
    const t = L;
    let x = this._getTransformByDeep(Tx);
    this._worldTransform = x;
    let s = x.copy().invert(), i = s.point({ x: this.stage[t(161)].x || 0, y: this.stage.inputSystem.y || 0 });
    this[t(172)] = i.x, this[t(130)] = i.y, this.updateViewRect();
  }
  getViewportRectInLayer() {
    return this.viewportRect;
  }
  _calc_originInParent() {
    const e = this.stage;
    return [e.width * 0.5, e.height * 0.5];
  }
  toFileJson(e) {
    const t = L;
    let x = this[t(170)](e);
    return JSON[t(186)](x);
  }
  toJsonObject(e) {
    const t = L;
    e == null && (e = { imageToBase64: ![] }), typeof e == "boolean" && (e = { imageToBase64: e });
    let x = this.stage.serializerSystem.objectsToJSON([this], e.imageToBase64);
    return e.script != null && (x[t(176)] = e.script), e.info != null && (x.info = e.info), x;
  }
  [L(162)](e) {
    const t = L;
    wt[t(143)](100);
    let x = e;
    typeof e == t(160) && (x = JSON.parse(e)), this.removeAllChild();
    let s = this;
    return new Promise((i, r) => {
      const o = t;
      s.stage.serializerSystem[o(156)](this, x, !![])[o(124)](() => {
        s._whenJsonLoaded(), i(x);
      });
    });
  }
  openJsonAndWait(e) {
    return this.openJson(e);
  }
  _whenJsonLoaded() {
    const e = L;
    let t = this[e(179)], x = this;
    t.styleSystem.markDirty(), t.cancelZoom(), this[e(126)](), x.stage.forceUpdate();
  }
  _resetBg() {
    this._bgInfo = { sw: null, sh: null, x: null, y: null }, this._calcBackgroundPosition();
  }
  [L(154)]() {
    const e = L;
    this[e(166)].setTo(0, 0, this.stage[e(158)], this.stage[e(185)]);
    let t = this.getTransform().invert();
    const x = this[e(166)];
    let s = x[e(205)](), i = Zt[e(167)](t.points(s));
    return this[e(166)] = i, x;
  }
  [L(187)](e, t) {
    const x = L;
    (e != this.render.getWidth() || t != this[x(178)].getHeight()) && (this[x(178)][x(199)](e, t), this.resizeTo(e, t), this.update());
  }
  pickUpByRect(e) {
    const t = L, x = this;
    let s = [], i = x.displayList;
    if (i != null)
      for (var r = 0; r < i[t(125)]; r++) {
        let o = i[r], a = o._obb.aabb;
        o.pickable() && e.containsRect(a) && s.push(o);
      }
    return s;
  }
  pickUpChild() {
    const e = L;
    let t = this.displayList, x = null;
    for (let s = t.length - 1; s >= 0; s--) {
      let i = t[s];
      if (i[e(201)] && i.pickable()) {
        x = i;
        break;
      }
    }
    if (x != null) {
      let s = x.getTopFrozenParent();
      s != null && (x = s);
    }
    return x;
  }
  translateWith(e, t) {
    return super.translateWith(e, t), this._calcBackgroundPosition(), this;
  }
  _calcBackgroundPosition(e = ![]) {
    const t = L;
    if (!this[t(178)]) {
      console.log("render not exist");
      return;
    }
    const x = this.render.canvas;
    let s = this._computedStyle, i = this[t(123)], r = s.backgroundRepeat;
    if (r != null && r != "no-repeat") {
      let o = 100, a = 100, c = s._backgroundImageObject || this.style._backgroundImageObject;
      c != null && (o = c.width, a = c.height);
      let l = o * this.scaleX, h = a * this.scaleY, u = this[t(158)] * 0.5 % l + this.x, p = this.height * 0.5 % h + this.y;
      (e || i.sw != l || i.sh != h || i.x != u || i.y != p) && (x.style.backgroundSize = l + "px " + h + "px", x.style[t(145)] = u + "px " + p + "px"), i.sw = l, i.sh = h, i.x = u, i.y = p;
    } else
      x.style[t(145)] = "0px 0px";
  }
  _afterStyleComputed() {
    const e = L;
    let t = this._computedStyle.getChangedProps();
    Object[e(122)](t).filter((s) => s.startsWith(e(127))).length > 0 && this._updateDomStyle();
  }
  _updateDomStyle() {
    const e = L, t = this[e(144)], x = t.style, s = this._computedStyle;
    if (x.background = null, s.backgroundColor != null && (x[e(200)] = s[e(200)]), s[e(165)] != null) {
      let i = Ht[e(150)](s.backgroundImage);
      x.backgroundImage = e(190) + i + '")';
    }
    return s.backgroundRepeat != null && (x.backgroundRepeat = s.backgroundRepeat), s[e(145)] != null && (x[e(145)] = s[e(145)]), s.backgroundSize != null && (x.backgroundSize = s.backgroundSize), t.width = t[e(158)] + 1, t.width = t[e(158)] - 1, this._calcBackgroundPosition(!![]), this;
  }
  getAABB() {
    const e = L;
    let t = this.displayList;
    if (t.length == 0)
      return new z(0, 0, 1, 1);
    let x = t.map((i) => i._obb.aabb);
    return z[e(193)](x);
  }
  getExportAABB() {
    const e = L;
    let t = this[e(168)][e(149)]((i) => i[e(203)]);
    if (t.length == 0)
      return new z(0, 0, 1, 1);
    let x = t.map((i) => i._obb[e(198)]);
    return z.unionRects(x);
  }
  setZIndex(e) {
    const t = L;
    this[t(159)] = e, this.stage && this[t(179)][t(153)]();
  }
  [L(128)]() {
    return super.show(), this.render.show(), this._calcBackgroundPosition(), this;
  }
  hide() {
    return super.hide(), this.render.hide(), this;
  }
  _OBBPoints() {
    let e = this.width, t = this.height;
    return [{ x: 0, y: 0 }, { x: e, y: 0 }, { x: e, y: t }, { x: 0, y: t }];
  }
  getPoints() {
    return this._OBBPoints();
  }
  draw(e) {
  }
  zoom(e, t, x, s) {
    const i = L;
    return this.stage.camera[i(132)](this, e, x, s), this;
  }
  cancelZoom() {
    const e = L;
    this.stage[e(182)].cancelZoom();
  }
  forceUpdate() {
    const e = L;
    this.stage.renderSystem[e(152)](this, Date.now());
  }
  setBackground(e, t) {
    const x = L;
    this[x(178)][x(144)].style.background = e, this[x(178)].canvas[x(139)].backgroundSize = t;
  }
  [L(134)](e, t, x = ![]) {
    return this[L(135)](e, t, x);
  }
  toStageRect(e) {
    let x = e[L(157)](), s = this.getTransform(), i = x.toPoints();
    return Zt.toAABB(s.points(i));
  }
  [L(173)](e) {
    const t = L;
    let x = this.getTransform().invert(), s = e[t(205)]();
    return Zt.toAABB(x.points(s));
  }
  dragEndHandler() {
    const e = L;
    if (this[e(146)] == ![])
      return;
    let t = this.render.canvas, x = t.offsetLeft, s = t.offsetTop;
    this.translateWith(x, s), t.style.left = e(197), t.style.top = "0px";
  }
  addChild(e) {
    const t = L;
    return super[t(163)](e), this.update(), this;
  }
  [L(202)](e) {
    super.addChilds(e), this.update();
  }
  centerBy(e) {
    const t = L;
    let x = this, s = x.stage, i;
    if (e == null)
      i = x.getExportAABB();
    else if (e instanceof C)
      i = e[t(184)]();
    else {
      let l = e.map((h) => h.getAABB());
      i = z[t(193)](l);
    }
    i = x.toStageRect(i);
    let r = s.width / 2, o = s.height / 2, a = r - i.x, c = o - i.y;
    a -= i[t(158)] / 2, c -= i.height / 2, x.translateWith(a, c), x[t(155)]();
  }
  getAllVisiable() {
    return C[L(131)](this.children, (t) => t.visible == !![]);
  }
  loopRender(e = 60) {
    this._frames = e;
  }
  endLoopRender() {
    const e = L;
    this[e(196)] = 0;
  }
  destory() {
    const e = L;
    if (this.destroyed)
      throw new Error(e(171));
    this.destroyed = !![], this.stage && this[e(179)][e(183)](this), this.render.dispose(), this.displayList = void 0, this[e(168)] = void 0, this.listeners = void 0, this.style = void 0, this[e(166)] = void 0, this.classList.length = 0, this.render = void 0, this.stage = void 0, this.children[e(142)]((t) => t[e(188)]()), this.children.length = 0;
  }
}
Ze([f(L(189))], ft.prototype, "className", 2), Ze([f(!![])], ft[L(195)], "isLayer", 2), Ze([f(![])], ft.prototype, "isNode", 2), Ze([f(["className", "isLayer"])], ft.prototype, "allwaysSerializers", 2), Ze([f(["id", "name"])], ft.prototype, "serializers", 2), Ze([f(L(147))], ft.prototype, L(133), 2);
function On() {
  const n = ["className", "fillText", "flatten", "areaBox", "length", "clear", "_paintObjects", "2365120XOFsJG", "fillStyle", "renderLayer", "show", "defineProperty", "121494UBDXOa", "224239MVjTtm", "context", "getStyle", "125336PWOBVY", "8ekSIyA", "2CHUQNQ", "setRender", "175629NnRMcE", "1928772jWJZBZ", "min", "displayList", "HandlerLayerCanvas", "77KzbbhD", "height", "1231500syzvyJ", "layer", "stage", "translateTo", "hide", "draw", "visible", "styleSystem"];
  return On = function() {
    return n;
  }, On();
}
const Rt = Ln;
(function(n, e) {
  const t = Ln, x = n();
  for (; []; )
    try {
      if (parseInt(t(116)) / 1 * (-parseInt(t(121)) / 2) + -parseInt(t(123)) / 3 + -parseInt(t(119)) / 4 + parseInt(t(130)) / 5 + parseInt(t(115)) / 6 * (-parseInt(t(128)) / 7) + parseInt(t(120)) / 8 * (parseInt(t(124)) / 9) + parseInt(t(110)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(On, 160265);
var $o = Object[Rt(114)], ta = Object.getOwnPropertyDescriptor, ea = (n, e, t, x) => {
  const s = Rt;
  for (var i = x > 1 ? void 0 : x ? ta(e, t) : e, r = n[s(107)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && $o(e, t, i), i;
};
function Ln(n, e) {
  const t = On();
  return Ln = function(x, s) {
    return x = x - 106, t[x];
  }, Ln(n, e);
}
const xa = Et.gc, ji = et.w;
class na extends Pr {
  constructor(e, t) {
    const x = Rt;
    super(t), this[x(132)] = e;
  }
  [Rt(112)](e) {
    const t = Rt;
    this[t(108)](), this.stage = this[t(131)].stage, this.styleSystem = this.stage[t(137)];
    let x = this[t(117)];
    return x.save(), e._computedStyle.applyTo(x), e[t(135)](x), Ve[t(140)] ? (e.displayList.forEach((i) => i.painted = !![]), this._paintFlattenObjects(e[t(126)])) : this[t(109)](e.children, ![]), x.restore(), e;
  }
}
class Gs extends ft {
  constructor(e) {
    const t = Rt;
    super(), this._frames = 0, this._zIndex = cx[t(127)], wt.back(), this.stage = e, this.areaBox = new M(), wt.back(), this.addChild(this.areaBox), this.axis.visible = ![], this[t(136)] = !![], this[t(122)](new na(e, this)), this.areaBox.css(Te.getStyle("SelectArea")), this[t(106)].hide();
  }
  showAreaBox() {
    const e = Rt, t = this.stage.inputSystem, x = this.areaBox;
    let s = this.stage.styleSystem.currentTheme;
    x.css(s[e(118)]("SelectArea"));
    let i = t.mouseDownX, r = t.mouseDownY, o = t.x, a = t.y, c = Math.abs(i - o), l = Math.abs(r - a), h = Math.min(i, o), u = Math[e(125)](r, a);
    return x.resizeTo(c, l), x[e(133)](h, u), x.translateWith(c * 0.5, l * 0.5), this.areaBox[e(113)](), new z(h, u, x.width, x[e(129)]);
  }
  mouseoutHandler(e) {
    const t = Rt;
    this.areaBox[t(134)]();
  }
  _calc_originInParent() {
    return [0, 0];
  }
  update() {
    return this._requestReapint = !![], !![];
  }
  draw(e) {
    const t = Rt;
    if (ji == null)
      return;
    e.save(), e.globalAlpha = 0.6, e.font = "bold 16px arial";
    let x = xa(ji);
    e[t(111)] = "white", e[t(139)](x, 16, this.render.getHeight() - 16), e.restore();
  }
}
ea([f("HandlerLayer")], Gs.prototype, Rt(138), 2);
const t0 = Me;
function Me(n, e) {
  const t = Cn();
  return Me = function(x, s) {
    return x = x - 500, t[x];
  }, Me(n, e);
}
(function(n, e) {
  const t = Me, x = n();
  for (; []; )
    try {
      if (parseInt(t(527)) / 1 + parseInt(t(513)) / 2 + -parseInt(t(505)) / 3 + -parseInt(t(517)) / 4 + -parseInt(t(507)) / 5 * (-parseInt(t(514)) / 6) + parseInt(t(502)) / 7 + parseInt(t(530)) / 8 * (-parseInt(t(531)) / 9) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Cn, 410334);
function Cn() {
  const n = ["5MnilfS", "display", "appendChild", "offsetLeft", "setAttribute", "touches", "283502zjGBBK", "3022626jvlqqY", "oncanplay", "createElement", "1787944jXMghL", "getXYInDom", "substring", "style", "clientLeft", "isFirefox", "scrollTop", "isMobileDevice", "forEach", "src", "627248TyhZgJ", "fullScreen", "mousewheel", "26984CjBUig", "1494jQJjHo", "top", "fullWindow", "test", "4876186TIxPXk", "pageXOffset", "pageY", "1656390ODfgGn", "position"];
  return Cn = function() {
    return n;
  }, Cn();
}
const Lr = class {
  static addEventListener(n, e, t) {
    const x = Me;
    let s = n.attachEvent || n.addEventListener;
    Et[x(522)]() && e == x(529) ? e = "DOMMouseScroll" : window.attachEvent && e[x(519)](0, 2) !== "on" && (e = "on" + e), s.call(n, e, t);
  }
  static getOffsetPosition(n) {
    const e = Me;
    if (n == null)
      return { left: 0, top: 0 };
    let t = 0, x = 0;
    if (n.getBoundingClientRect != null) {
      let s = n.getBoundingClientRect(), i = n.ownerDocument, r = i.body, o = i.documentElement, a = o.clientTop || r.clientTop || 0, c = o.clientLeft || r[e(521)] || 0;
      t = s[e(532)] + (window.pageYOffset || o && o.scrollTop || r.scrollTop) - a, x = s.left + (window[e(503)] || o && o.scrollLeft || r.scrollLeft) - c;
    } else
      do
        t += n.offsetTop || 0, x += n[e(510)] || 0, n = n.offsetParent;
      while (n);
    return { left: x, top: t };
  }
  static createVideo(n, e) {
    const t = Me;
    if (typeof n == "string") {
      let x = document.createElement("video");
      x.muted = "muted", x.style[t(508)] = "none";
      let s = document[t(516)]("source");
      return s[t(511)]("_source", "jtopo"), s.type = "video/mp4", s[t(526)] = n, x[t(509)](s), document.body.appendChild(x), x[t(515)] = function() {
        e(x);
      }, x;
    } else
      return n;
  }
  static [t0(500)](n) {
    const e = t0;
    let t = "position,width,height,left,top,bottom,right,zIndex".split(",");
    if (n[e(528)] == !![]) {
      let x = n._backup;
      t.forEach((s) => {
        n.style[s] = x[s];
      }), n.fullScreen = ![];
    } else {
      let x = {};
      t[e(525)]((s) => {
        const i = e;
        x[s] = n[i(520)][s];
      }), n._backup = x, n.style[e(506)] = "fixed", n.style.left = 0, n[e(520)].top = 0, n.style.bottom = 0, n[e(520)].right = 0, n.style.zIndex = cx.FullWindowDom, n.fullScreen = !![];
    }
  }
  static fullScreen(n) {
    n.requestFullscreen ? n.requestFullscreen() : n.mozRequestFullScreen ? n.mozRequestFullScreen() : n.webkitRequestFullscreen ? n.webkitRequestFullscreen() : n.msRequestFullscreen && n.msRequestFullscreen();
  }
  static [t0(518)](n, e) {
    const t = t0;
    let x = e[t(512)][0].pageX, s = e.touches[0][t(504)];
    e[t(512)][0].pageX == null && (x = e.touches[0].clientX + document.body.scrollLeft - document.body[t(521)], s = e.touches[0].clientY + document.body[t(523)] - document.body.clientTop);
    let i = Lr.getOffsetPosition(n), r = x - i.left, o = s - i.top;
    return { x: r, y: o };
  }
};
let Ye = Lr;
Ye[t0(524)] = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i[t0(501)](navigator.userAgent);
(function(n, e) {
  const t = Tn, x = n();
  for (; []; )
    try {
      if (-parseInt(t(289)) / 1 * (parseInt(t(299)) / 2) + parseInt(t(294)) / 3 * (parseInt(t(297)) / 4) + -parseInt(t(293)) / 5 + parseInt(t(295)) / 6 + parseInt(t(290)) / 7 * (parseInt(t(298)) / 8) + parseInt(t(292)) / 9 + parseInt(t(296)) / 10 * (-parseInt(t(300)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(En, 580082);
function En() {
  const n = ["1160103wDOCNq", "6139674fLmLZs", "571530TRdPXV", "12yLrIMN", "1025624hTeuQl", "277868wTXYax", "275rljUpn", "1zeOaPR", "7SVynMs", "return ", "1468638dOPzjK", "1634630hrQCne"];
  return En = function() {
    return n;
  }, En();
}
function Tn(n, e) {
  const t = En();
  return Tn = function(x, s) {
    return x = x - 289, t[x];
  }, Tn(n, e);
}
const Cr = { execute(n, e) {
  const t = Tn;
  if (n == null)
    return;
  new Function(t(291) + n)()(e.stage, e, kt);
} };
function An(n, e) {
  const t = Mn();
  return An = function(x, s) {
    return x = x - 482, t[x];
  }, An(n, e);
}
const sa = An;
function Mn() {
  const n = ["536535EVtaPN", "5459223dGrqZF", "2201118UzeXfO", "2130762IotGQH", "14WIsGls", "28MnZpjh", "4yAsBrl", "11604016pbmnYd", "get", "1427067aHlTJm", "10IOaXja", "13441023LSqSxE"];
  return Mn = function() {
    return n;
  }, Mn();
}
(function(n, e) {
  const t = An, x = n();
  for (; []; )
    try {
      if (parseInt(t(490)) / 1 + -parseInt(t(487)) / 2 * (parseInt(t(483)) / 3) + parseInt(t(486)) / 4 * (-parseInt(t(493)) / 5) + -parseInt(t(484)) / 6 * (parseInt(t(485)) / 7) + parseInt(t(488)) / 8 + -parseInt(t(492)) / 9 * (-parseInt(t(491)) / 10) + -parseInt(t(482)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Mn, 945908);
let ia = `
<svg viewBox="0 0 24 24" id="zoom-in">
<path d="M4,20 L9.58788778,14.4121122"/>
<path d="M14,16 C10.6862915,16 8,13.3137085 8,10 C8,6.6862915 10.6862915,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,13.3137085 17.3137085,16 14,16 Z"/>
<path d="M16.6666667 10L11.3333333 10M14 7.33333333L14 12.6666667"/>
</svg>
<svg viewBox="0 0 24 24" id="zoom-out">
<path d="M14,16 C10.6862915,16 8,13.3137085 8,10 C8,6.6862915 10.6862915,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,13.3137085 17.3137085,16 14,16 Z"/>
<path d="M16.6666667 10L11.3333333 10M4 20L9.58788778 14.4121122"/>
</svg>
<svg viewBox="0 0 24 24" id="back-left">
<path d="M5,17 L5,15 C5,10.0294373 8.80557963,6 13.5,6 C18.1944204,6 22,10.0294373 22,15"/>
<polyline points="8 15 5 18 2 15"/>
</svg>
<svg viewBox="0 0 24 24" id="align-center">
<path d="M8 10L16 10M6 6L18 6M6 14L18 14M8 18L16 18"/>
</svg>
<svg viewBox="0 0 24 24" id="edit">
<path d="M18.4142136 4.41421356L19.5857864 5.58578644C20.366835 6.36683502 20.366835 7.63316498 19.5857864 8.41421356L8 20 4 20 4 16 15.5857864 4.41421356C16.366835 3.63316498 17.633165 3.63316498 18.4142136 4.41421356zM14 6L18 10"/>
</svg>
<svg viewBox="0 0 24 24" id="lock-alt">
<rect width="14" height="10" x="5" y="11"/>
<path d="M12,3 L12,3 C14.7614237,3 17,5.23857625 17,8 L17,11 L7,11 L7,8 C7,5.23857625 9.23857625,3 12,3 Z"/>
<circle cx="12" cy="16" r="1"/>
</svg>
<svg viewBox="0 0 24 24" id="lock-open">
<path d="M7,7.625 L7,7 C7,4.23857625 9.23857625,2 12,2 L12,2 C14.7614237,2 17,4.23857625 17,7 L17,11"/>
<rect width="14" height="10" x="5" y="11"/>
</svg>
<svg viewBox="0 0 24 24" id="pan">
<path d="M20,14 L20,17 C20,19.209139 18.209139,21 16,21 L10.0216594,21 C8.75045497,21 7.55493392,20.3957659 6.80103128,19.3722467 L3.34541668,14.6808081 C2.81508416,13.9608139 2.94777982,12.950548 3.64605479,12.391928 C4.35756041,11.8227235 5.38335813,11.8798792 6.02722571,12.5246028 L8,14.5 L8,13 L8.00393081,13 L8,11 L8.0174523,6.5 C8.0174523,5.67157288 8.68902517,5 9.5174523,5 C10.3458794,5 11.0174523,5.67157288 11.0174523,6.5 L11.0174523,11 L11.0174523,4.5 C11.0174523,3.67157288 11.6890252,3 12.5174523,3 C13.3458794,3 14.0174523,3.67157288 14.0174523,4.5 L14.0174523,11 L14.0174523,5.5 C14.0174523,4.67157288 14.6890252,4 15.5174523,4 C16.3458794,4 17.0174523,4.67157288 17.0174523,5.5 L17.0174523,11 L17.0174523,7.5 C17.0174523,6.67157288 17.6890252,6 18.5174523,6 C19.3458794,6 20.0174523,6.67157288 20.0174523,7.5 L20.0058962,14 L20,14 Z"/>
</svg>
<svg viewBox="0 0 24 24" id="apps-alt">
<rect x="5" y="5" width="2" height="2"/>
<rect x="11" y="5" width="2" height="2"/>
<rect x="17" y="5" width="2" height="2"/>
<rect x="5" y="11" width="2" height="2"/>
<rect x="11" y="11" width="2" height="2"/>
<rect x="17" y="11" width="2" height="2"/>
<rect x="5" y="17" width="2" height="2"/>
<rect x="11" y="17" width="2" height="2"/>
<rect x="17" y="17" width="2" height="2"/>
</svg>
<svg viewBox="0 0 24 24" id="maximise">
<polyline points="21 16 21 21 16 21"/>
<polyline points="8 21 3 21 3 16"/>
<polyline points="16 3 21 3 21 8"/>
<polyline points="3 8 3 3 8 3"/>
</svg>
<svg viewBox="0 0 24 24" id="minimise">
<polyline points="8 3 8 8 3 8"/>
<polyline points="21 8 16 8 16 3"/>
<polyline points="3 16 8 16 8 21"/>
<polyline points="16 21 16 16 21 16"/>
</svg>
<svg viewBox="0 0 24 24" id="download">
<path d="M12,3 L12,16"/>
<polyline points="7 12 12 17 17 12"/>
<path d="M20,21 L4,21"/>
</svg>
<svg viewBox="0 0 24 24" id="rectangle">
<rect width="18" height="18" x="3" y="3"/>
</svg>
<svg viewBox="0 0 24 24" id="cursor">
<polygon points="7 20 7 4 19 16 12 16 7 21"/>
</svg>
<svg viewBox="0 0 24 24" id="search">
<path d="M14.4121122,14.4121122 L20,20"/>
<circle cx="10" cy="10" r="6"/>
</svg>
<svg viewBox="0 0 24 24" id="eye">
<path d="M22 12C22 12 19 18 12 18C5 18 2 12 2 12C2 12 5 6 12 6C19 6 22 12 22 12Z"/>
<circle cx="12" cy="12" r="3"/>
</svg>
<svg viewBox="0 0 24 24" id="save">
<path d="M17.2928932,3.29289322 L21,7 L21,20 C21,20.5522847 20.5522847,21 20,21 L4,21 C3.44771525,21 3,20.5522847 3,20 L3,4 C3,3.44771525 3.44771525,3 4,3 L16.5857864,3 C16.8510029,3 17.1053568,3.10535684 17.2928932,3.29289322 Z"/>
<rect width="10" height="8" x="7" y="13"/>
<rect width="8" height="5" x="8" y="3"/>
</svg>
<svg viewBox="0 0 24 24" id="image">
<rect width="18" height="18" x="3" y="3"/>
<path stroke-linecap="round" d="M3 14l4-4 11 11"/>
<circle cx="13.5" cy="7.5" r="2.5"/>
<path stroke-linecap="round" d="M13.5 16.5L21 9"/>
</svg>
<svg viewBox="0 0 24 24" id="upload">
<path d="M12,4 L12,17"/>
<polyline points="7 8 12 3 17 8"/>
<path d="M20,21 L4,21"/>
</svg>
`, Er = document.createElement("div");
Er.innerHTML = ia;
let vi = {};
vi[sa(489)] = function(n) {
  return Er.querySelector("#" + n);
};
const Z = h0;
(function(n, e) {
  const t = h0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(438)) / 1 + parseInt(t(444)) / 2 * (parseInt(t(411)) / 3) + parseInt(t(424)) / 4 * (-parseInt(t(448)) / 5) + parseInt(t(430)) / 6 * (-parseInt(t(433)) / 7) + parseInt(t(400)) / 8 * (-parseInt(t(436)) / 9) + parseInt(t(425)) / 10 * (parseInt(t(402)) / 11) + parseInt(t(417)) / 12 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Dn, 431566);
function Dn() {
  const n = ["querySelector", "domObj", "12zzIViG", "drag", "clientHeight", "2763166eqJDTY", "iconId", `</button>
<button title="拖拽模式"  class="item" group='mode' iconId='pan'>`, "3843nrAUuw", "none", "264572KtSnny", "length", "imageToBase64", "initToolbar", "select", "addEventListener", "2792xuTAxT", "update", "stage", "add", "30JykGGC", "activeBtn", "248UBymen", "style", "11HuOCTj", "div", `</button>
<button title="取消缩放" class="item" iconId='back-left'>`, `</button>
</div>

<div class="group">
<button title="导出PNG" class="item" iconId='image'>`, "text", "hideOverview", `</button>
<button title="缩放至画布" class="item" iconId='minimise'>`, ' style="display:none" ', "overview", "168wFldRo", "removeAllActive", "input", "getAttribute", "eye", "button[iconid='edit']", "18016344TYFYDq", `</button>
<button title="浏览器全屏" class="item" iconId='maximise'>`, "group", "maximise", 'input[type="file"]', "toLocaleString", "indexOf", "242988tpUwAL", "2837950bNZNEM", "then", "pan"];
  return Dn = function() {
    return n;
  }, Dn();
}
function h0(n, e) {
  const t = Dn();
  return h0 = function(x, s) {
    return x = x - 399, t[x];
  }, h0(n, e);
}
function $(n) {
  return vi.get(n).outerHTML;
}
let Tr = Z(409);
Tr = "";
var ra = `
<div class="group">
<button title="默认" class="item active" group='mode' iconId='cursor'>` + $("cursor") + `</button>
<button title="编辑模式" edit="true" class="item" group='mode' iconId='edit' ` + Tr + ">" + $("edit") + `</button>
<button title="框选模式" class="item" group='mode' iconId='rectangle'>` + $("rectangle") + Z(435) + $(Z(427)) + `</button>
<button title="锁定模式" class="item" group='mode' iconId='lock-alt'>` + $("lock-alt") + `</button>
</div>

<div class="group">
<button title="放大"  class="item" iconId='zoom-in'>` + $("zoom-in") + `</button>
<button title="缩小"  class="item" iconId='zoom-out'>` + $("zoom-out") + `</button>
<button title="居中" class="item" iconId='align-center'>` + $("align-center") + Z(408) + $("minimise") + Z(404) + $("back-left") + `</button>
</div>

<div class="group">
<button title="缩略图" class="item" iconId='eye'>` + $(Z(415)) + Z(418) + $(Z(420)) + `</button>
<input title="查找" type="text" placeholder="查找" value=""></input>
<button class="item" iconId='search'>` + vi.get("search").outerHTML + Z(405) + $("image") + `</button>
<button title="打开本地文件" class="item" iconId='upload'>` + $("upload") + `</button>
<button title="保存到本地" class="item" iconId='save'>` + $("save") + `</button>
<div style="display:none;"><input type="file"/></div>
</div>
`;
class Ar {
  constructor(e) {
    this.imageToBase64 = !![], this.stage = e, this.initToolbar(e, ra);
    let t = this;
    setTimeout(function() {
      t.initActiveStatus();
    }, 200);
  }
  getDom() {
    return this[Z(429)];
  }
  show() {
    const e = Z;
    this[e(429)].style.display = "block";
  }
  hide() {
    const e = Z;
    this[e(429)][e(401)].display = "none";
  }
  remove() {
    this.domObj.remove();
  }
  getHeigth() {
    const e = Z;
    return this.domObj.style.display == e(437) ? 0 : this[e(429)][e(432)] + 1;
  }
  initActiveStatus() {
    const e = Z;
    if (this.stage.mode == "edit") {
      let x = document.querySelector(e(416));
      this.activeBtn(x);
    }
  }
  [Z(441)](e, t) {
    const x = Z;
    let s = this, i = document.createElement(x(403));
    this.domObj = i, i.classList[x(447)]("jtopo_toolbar"), i.innerHTML = t;
    let r = i.querySelectorAll("button");
    this.buttons = r;
    let o = i[x(428)](x(421)), a = o.parentNode;
    function c(_) {
      a.innerHTML = '<input type="file"/>', l();
      let m = _.target.files[0];
      const g = new FileReader();
      g.readAsText(m), g.onload = function() {
        const w = h0, O = s[w(446)].getCurrentLayer(), A = this.result;
        try {
          O.openJson(A), O.translate(0, 0), O.scaleTo(1, 1), document.title = m.name;
        } catch (D) {
          console.log(D), alert("加载出现错误");
        }
      };
    }
    function l() {
      const _ = x;
      o = i.querySelector(_(421)), o[_(443)]("change", c);
    }
    l(), this.fileInput = o;
    function h() {
      const _ = x;
      let m = e.getCurrentLayer(), g = i[_(428)]('input[type="text"]').value;
      if (g.length > 0) {
        let w = m.displayList;
        for (let O = 0; O < w.length; O++) {
          const A = w[O];
          if (A[_(406)] != null && A.text[_(423)](g) != -1) {
            m.centerBy(A, 10), e.effectSystem.flash(A).play(), e.editor != null ? e.editor[_(445)]() : e[_(445)]();
            return;
          }
        }
      }
    }
    let u = { cursor: function() {
      e.setMode("normal");
    }, rectangle: function() {
      const _ = x;
      e.setMode(_(442));
    }, pan: function() {
      const _ = x;
      e.setMode(_(431));
    }, edit: function() {
      e.setMode("edit");
    }, "lock-alt": function() {
      e.setMode("view");
    }, eye: function() {
      const _ = x;
      e.overview == null || e[_(410)].visible == ![] ? e.showOverview() : e[_(407)]();
    }, "zoom-in": function() {
      e.zoomIn();
    }, "zoom-out": function() {
      e.zoomOut();
    }, "back-left": function() {
      e.cancelZoom();
    }, minimise: function() {
      e.zoomFullStage();
    }, "align-center": function() {
      e.translateToCenter();
    }, maximise: function() {
      e.fullWindow();
    }, image: function() {
      e.saveImageInfo();
    }, save: function() {
      const _ = x;
      let m = prompt("要保存的文件名："), g = s[_(440)];
      if (m != null) {
        let w = e.getCurrentLayer(), O = w.toFileJson({ info: { date: (/* @__PURE__ */ new Date())[_(422)](), fileName: m }, imageToBase64: g });
        e.download(m + ".json", O);
      }
    }, upload: function() {
      o.click();
    }, search: h };
    i[x(428)]("input").onkeydown = function(_) {
      _.key == "Enter" && h();
    }, i.querySelector(x(413)).ondblclick = function(_) {
      const m = x;
      if (!_.shiftKey)
        return;
      console.log("reloadJsonTest");
      let g = e.getCurrentLayer();
      g.openJson(g.toFileJson())[m(426)]((w) => {
        Cr.execute(w.script, g);
      });
    };
    function p(_) {
      _.onclick = function() {
        const m = h0;
        let g = _[m(414)](m(434));
        u[g](), s[m(399)](_);
      };
    }
    for (var b = 0; b < r[x(439)]; b++) {
      let _ = r[b];
      p(_);
    }
  }
  activeBtn(e) {
    const t = Z;
    let x = e[t(414)](t(419));
    x != null && (this[t(412)](x), e.classList.add("active"));
  }
  removeAllActive(e) {
    const t = Z;
    let x = this.buttons;
    for (var s = 0; s < x.length; s++) {
      let i = x[s];
      e == i.getAttribute(t(419)) && i.classList.remove("active");
    }
  }
}
const xt = oe;
(function(n, e) {
  const t = oe, x = n();
  for (; []; )
    try {
      if (-parseInt(t(165)) / 1 + parseInt(t(162)) / 2 + parseInt(t(163)) / 3 + parseInt(t(159)) / 4 + parseInt(t(146)) / 5 + -parseInt(t(144)) / 6 * (parseInt(t(161)) / 7) + parseInt(t(150)) / 8 * (-parseInt(t(151)) / 9) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Bn, 757303);
function oe(n, e) {
  const t = Bn();
  return oe = function(x, s) {
    return x = x - 140, t[x];
  }, oe(n, e);
}
function Bn() {
  const n = ["observers", "length", "-#distinct#-", "489318CuVhph", "click", "3747935HkPLVq", "log", "clientX", "移动-x", "88ZKRNJR", "804987MTbXvn", "next", "publish", "complete", "distinct", "function", "map", "build", "2994676WDMSsq", "subscribe", "119MzpwHl", "2121314DzrUOE", "3987387NTJFTw", "forEach", "760465UvSYFU", "fromEvent", "debounceTime", "重复订阅", "filter", "error"];
  return Bn = function() {
    return n;
  }, Bn();
}
class De {
  constructor() {
    this.observers = [];
  }
  [xt(153)](e) {
    const t = xt;
    return this[t(141)][t(164)]((x) => {
      typeof x == "function" ? x(e) : x[t(152)] && x.next(e);
    }), this;
  }
  complete() {
    return this.observers.forEach((e) => {
      const t = oe;
      typeof e == t(156) || e[t(154)] && e.complete();
    }), this;
  }
  [xt(140)](e) {
    const t = xt;
    return this[t(141)].forEach((x) => {
      typeof x == t(156) || x.error && x.error(e);
    }), this;
  }
  subscribe(e) {
    const t = xt;
    if (this[t(141)].indexOf(e) != -1)
      throw new Error(t(168));
    return this.observers.push(e), this;
  }
  [xt(158)](e) {
    const t = xt;
    let x = new De();
    return this[t(160)]((s) => {
      e(x, s);
    }), x;
  }
  filter(e) {
    return this.build((t, x) => {
      e(x) && t.publish(x);
    });
  }
  map(e) {
    const t = xt;
    return this[t(158)]((x, s) => {
      x[t(153)](e(s));
    });
  }
  reduce(e, t) {
    const x = xt;
    let s = t, i = !![];
    return this[x(158)]((r, o) => {
      const a = x;
      i && t == null ? (i = ![], s = o) : s = e(o, s), r[a(153)](s);
    });
  }
  [xt(155)]() {
    const e = xt;
    let t = e(143);
    return this.build((x, s) => {
      const i = e;
      t !== s && (t = s, x[i(153)](s));
    });
  }
  debounceTime(e) {
    let t;
    return this.build((x, s) => {
      t != null && clearTimeout(t), t = setTimeout(() => {
        x.publish(s);
      }, e);
    });
  }
  delay(e) {
    return this.build((t, x) => {
      setTimeout(() => {
        t.publish(x);
      }, e);
    });
  }
  pairwise(e = 2) {
    return this.bufferCount(e, 1);
  }
  bufferCount(e, t = 1) {
    let x = [];
    return this.build((s, i) => {
      const r = oe;
      if (x.push(i), x[r(142)] == e) {
        s.publish(x);
        for (let o = 0; o < t; o++)
          x.shift();
      }
    });
  }
  unsubscribe() {
    const e = xt;
    this.observers[e(142)] = 0;
  }
  static [xt(166)](e, t) {
    let x = new De(), s = (i) => {
      x[oe(153)](i);
    };
    return e.addEventListener(t, s), x.unsubscribe = () => {
      e.removeEventListener(t, s);
    }, x;
  }
  static of(...e) {
    const t = xt;
    let x = new De(), s;
    return x[t(160)] = function(i) {
      return this.observers.push(i), s = setTimeout(() => {
        e[oe(164)]((o) => x.publish(o));
      }, 10), this;
    }, x.unsubscribe = () => {
      clearTimeout(s);
    }, x;
  }
}
const Pe = Nt;
(function(n, e) {
  const t = Nt, x = n();
  for (; []; )
    try {
      if (parseInt(t(444)) / 1 + parseInt(t(450)) / 2 * (parseInt(t(408)) / 3) + parseInt(t(422)) / 4 + -parseInt(t(406)) / 5 + -parseInt(t(447)) / 6 * (parseInt(t(453)) / 7) + -parseInt(t(411)) / 8 + -parseInt(t(432)) / 9 * (-parseInt(t(434)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(jn, 841863);
class ki extends Tt {
  constructor() {
    const e = Nt;
    super(), this.skipPointerMovePicking = ![], this.wheelZoom = !![], this.mouseDownX = 0, this[e(404)] = 0, this.x = -1, this.y = -1, this.isMouseDown = ![], this.isDragEnd = ![], this[e(441)] = ![], this.isMouseOn = ![], this.isDragStart = ![], this.idleTimeout = 50, this.buttons = 0, this.button = 0, this.touchesDistance = 0, this.distanceRatio = 1, this.eventObservable = new De();
  }
  get pickedObject() {
    return this.target;
  }
  _initEvent(e) {
    const t = Nt;
    this[t(436)] = e, oa(this), this[t(437)]();
  }
  _initObserve() {
    const e = this, t = this.stage;
    e.eventObservable.filter((i) => {
      const r = Nt, o = i.type;
      return t[r(438)] == Ct.view ? ![] : t[r(429)].mode == "painted" ? ![] : (o == "touchstart" && t.forceUpdate(), !![]);
    }).filter((i) => {
      const r = Nt;
      if (t.mode == Ct[r(449)] && t.editor != null) {
        let a = i.type + "Handler";
        if (t[r(419)][a] && (t.editor[a](i), i instanceof MouseEvent && i.defaultPrevented))
          return ![];
      }
      return !![];
    }).subscribe((i) => {
      const r = Nt;
      i.type == "mousewheel" && i.preventDefault();
      let a = i.type + "Handler";
      t[a] && t[a](i), t[r(442)] && (t.overview[r(424)] = !![]);
    }), e.eventObservable.subscribe((i) => {
      e.stage.update(), e.dispatchEvent(i);
    });
  }
  preventDefault() {
    this.event && this.event.preventDefault();
  }
  isRightButton() {
    return this.button == 2;
  }
  updateBaseInfo(e, t) {
    const x = Nt, s = Object.assign({}, this);
    this.previous = s, this.event = e, this.timeStamp = e[x(431)], this.type = t, this.isDragEnd = ![], this.isIdle = ![];
    let i = this;
    this.idleTimer != null && window.clearTimeout(this[x(403)]), this[x(403)] = setTimeout(function() {
      const r = x;
      i[r(448)] = !![];
    }, this[x(445)]);
  }
  [Pe(412)](e, t) {
    const x = Pe;
    this.updateBaseInfo(e, t), this[x(409)] = e.offsetX > 0 && e.offsetY > 0;
    const s = this.previous;
    this.buttons = e[x(427)], this.button = e.button, this.x = e.offsetX, this.y = e.offsetY, t == x(440) || t == "touchstart" ? (this.isMouseDown = !![], this.mouseDownX = this.x, this[x(404)] = this.y) : (t == "mouseup" || t == "click" || t == "mouseout" || t == "touchend") && (this[x(407)] = ![], s.type == x(417) && (this.isDragEnd = !![])), this.isDraging = this.isMouseDown == !![] && (t == x(417) || t == "touchmove" || t == "mousewheel" || t == "mouseenter" || t == "mouseout"), this[x(410)] = this.isDraging && s.isDraging != !![], this.dx = this.x - s.x, this.dy = this.y - s.y;
  }
  updateTouchInfo(e, t) {
    const x = Pe;
    this.updateBaseInfo(e, t);
    const s = this.previous, r = this[x(436)].handlerLayer.render.canvas.getBoundingClientRect(), o = r.left, a = r[x(433)];
    if (t != x(405)) {
      const c = e.touches[0], l = c.clientX - o, h = c.clientY - a;
      this.x = l, this.y = h;
    }
    if (t == "touchstart" ? (this.isMouseDown = !![], this.mouseDownX = this.x, this[x(404)] = this.y, this.touchesDistance = 0, this.distanceRatio = 1) : t == "touchend" && (this.isMouseDown = ![], s && s[x(439)] == x(430) && (this.isDragEnd = !![]), this[x(414)] = 0, this.distanceRatio = 1), this.isDraging = this[x(407)] == !![] && t == "touchmove", this[x(410)] = this.isDraging && s.isDraging != !![], this.dx = this.x - s.x, this.dy = this.y - s.y, t == "touchmove" && e.touches.length >= 2) {
      const c = e.touches[0], l = e.touches[1], h = aa(c, l);
      this.touchesDistance != 0 && (this.distanceRatio = h / this[x(414)]), this.touchesDistance = h;
    }
    e[x(452)]();
  }
  _onEvent(e) {
    const t = this, x = e.type;
    e instanceof MouseEvent ? t.updateMouseInfo(e, x) : t.updateTouchInfo(e, x), t.eventObservable.publish(e);
  }
  [Pe(443)](e) {
    const t = Pe, x = this.stage, s = this;
    let i = s[t(421)] || { type: t(440), ctrlKey: ![], metaKey: ![] };
    if (x.inputSystem.target = e, e == null)
      return;
    e.matrixDirty = !![], s.type == "mousedown" ? e.mousedownHandler.call(e, s) : i.type == "touchstart" && e.touchstartHandler.call(e, s);
    const r = i.ctrlKey || i[t(416)];
    x.selectedGroup[t(425)](e) ? r && x.selectedGroup[t(446)](e) : (!r && x.selectedGroup.removeAll(), x[t(413)][t(418)](e));
  }
}
function jn() {
  const n = ["touchstart", "buttons", "click", "localView", "touchmove", "timeStamp", "5706873tTlNiS", "top", "10IgximN", "mouseover", "stage", "_initObserve", "mode", "type", "mousedown", "isDraging", "overview", "pickObject", "1422907dOosUq", "idleTimeout", "remove", "7547106bScNyV", "isIdle", "edit", "2EulbRZ", "render", "preventDefault", "7YJSPXv", "idleTimer", "mouseDownY", "touchend", "6060475UWmaDQ", "isMouseDown", "4687818DscoFM", "isMouseOn", "isDragStart", "7700808fzQBmn", "updateMouseInfo", "selectedGroup", "touchesDistance", "pageX", "metaKey", "mousemove", "add", "editor", "drop", "event", "2619200ItVdel", "handlerLayer", "dirty", "has"];
  return jn = function() {
    return n;
  }, jn();
}
function Nt(n, e) {
  const t = jn();
  return Nt = function(x, s) {
    return x = x - 403, t[x];
  }, Nt(n, e);
}
function oa(n) {
  const e = Pe;
  let t = n.stage, x = t[e(423)][e(451)].canvas;
  ["mouseenter", "mouseout", e(435), "mousedown", "mouseup", e(417), "mousewheel", e(428), "dblclick", "dragstart", "dragend", "dragover", e(420), e(426), "touchmove", "touchend"].map(function(i) {
    Ye.addEventListener(x, i, function(r) {
      n._onEvent(r);
    });
  });
}
function aa(n, e) {
  const t = Pe, x = e[t(415)] - n.pageX, s = e.pageY - n.pageY;
  return Math.sqrt(x * x + s * s);
}
const lt = Be;
(function(n, e) {
  const t = Be, x = n();
  for (; []; )
    try {
      if (parseInt(t(481)) / 1 * (-parseInt(t(489)) / 2) + parseInt(t(473)) / 3 * (-parseInt(t(464)) / 4) + -parseInt(t(465)) / 5 + parseInt(t(466)) / 6 * (parseInt(t(467)) / 7) + parseInt(t(461)) / 8 + parseInt(t(476)) / 9 * (-parseInt(t(483)) / 10) + -parseInt(t(495)) / 11 * (-parseInt(t(477)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(zn, 549923);
function Be(n, e) {
  const t = zn();
  return Be = function(x, s) {
    return x = x - 461, t[x];
  }, Be(n, e);
}
function zn() {
  const n = ["3585195fmsoPJ", "1176kvXsKh", "32872TTHDtK", "bufferCount", "sort", "join", "inputSystem", "isMouseOn", "1059mQEtwM", "Meta", "map", "2882106nAWErK", "1199424YToLEV", "bindKey", "_disabled", "tagName", "137IqZLki", "activeElement", "10bIqyNW", "shift", "keyMap", "Alt", "push", "meta", "9140zeTZmj", "isKeydown", "stage", "split", "toLowerCase", "textInputMode", "110yLjntJ", "keyupHandler", "mock", "keys", "indexOf", "filter", "editor", "regMap", "preventDefault", "key", "fromEvent", "2524712JXuCOo", "shiftKey", "ctrlKey", "252CtnaAn"];
  return zn = function() {
    return n;
  }, zn();
}
class Mr extends Tt {
  constructor(e) {
    const t = Be;
    super(), this.debug = ![], this._disabled = ![], this.regMap = /* @__PURE__ */ new Map(), this.keyMap = /* @__PURE__ */ new Map(), this[t(491)] = e, this.init(), this.debug = ![];
  }
  disable() {
    this._disabled = !![];
  }
  enable() {
    this._disabled = ![];
  }
  isControlDown() {
    return this.isKeydown("Control");
  }
  isShiftDown() {
    return this.isKeydown("Shift");
  }
  isAltDown() {
    const e = Be;
    return this.isKeydown(e(486));
  }
  isMetaDown() {
    const e = Be;
    return this.isKeydown(e(474));
  }
  [lt(478)](e, t) {
    const x = lt;
    let s = e[x(493)]().split("+")[x(469)]().join("+");
    this.regMap.set(s, t);
  }
  isKeyRegistered(e) {
    return this.getKeyBinding(e) != null;
  }
  getKeyBinding(e) {
    const t = lt;
    let x = e[t(493)]().split("+").sort()[t(470)]("+");
    return this[t(502)].get(x);
  }
  unBind(e) {
    return this.unbind(e);
  }
  unbind(e) {
    const t = lt;
    let x = e[t(493)]()[t(492)]("+").sort().join("+");
    this[t(502)].delete(x);
  }
  [lt(490)](e) {
    const t = lt;
    return this.keyMap.get(e[t(493)]());
  }
  sendKey(e, t) {
    const x = lt;
    t == null && (t = new KeyboardEvent("keydown"), t[x(497)] = !![]), this.fireKey(e[x(493)]().split("+"), t, !![]);
  }
  checkValid() {
    const e = lt;
    let t = document[e(482)][e(480)];
    return this[e(479)] || t == "INPUT" || t == "TEXTAREA" ? ![] : !![];
  }
  fireKey(e, t, x) {
    const s = lt;
    if (x == ![])
      return;
    const i = this;
    let r = this.stage, o = r[s(501)], a = e[s(469)]().join("+").toLowerCase();
    this.debug && console.log("按下", a);
    let c = this[s(502)][s(498)]();
    for (let l of c) {
      if (l != a || o != null && o.textInputMode == !![])
        continue;
      t[s(503)]();
      let h = i[s(502)].get(l);
      h && h(t);
    }
  }
  keydownHandler(e) {
    const t = lt;
    let x = e[t(504)].toLowerCase(), s = this.stage, i = [];
    e[t(463)] && (this.keyMap.set("control", !![]), i.push("control")), e.altKey && (this.keyMap.set("alt", !![]), i.push("alt")), e[t(462)] && (this[t(485)].set(t(484), !![]), i.push(t(484))), e.metaKey && (this.keyMap.set("meta", !![]), i[t(487)](t(488))), i[t(499)](x) == -1 && i.push(x), this.keyMap.set(x, !![]);
    let r = ai(e);
    this.fireKey(i, r, s[t(471)][t(472)]), this.dispatchEvent(r);
  }
  [lt(496)](e) {
    const t = lt, x = this, s = e[t(504)].toLowerCase();
    x[t(485)].delete(s);
    let i = x.stage.editor;
    if (i != null && i[t(494)] == !![])
      return;
    let r = ai(e);
    x.dispatchEvent(r);
  }
  init() {
    const e = lt;
    let t = this, x = De.fromEvent(document, "keydown"), s = De[e(505)](document, "keyup"), i = x[e(500)](() => this.checkValid()), r = s.filter(() => this.checkValid());
    i.subscribe((o) => {
      t.keydownHandler(o);
    }), r.subscribe((o) => {
      t[e(496)](o);
    }), this.keydownOb = i, this.keyupOb = r, this.keyBufferOb = i[e(475)]((o) => o[e(504)])[e(468)](2, 1)[e(475)]((o) => o[0] + "" + o[1]);
  }
}
function Nn(n, e) {
  const t = Rn();
  return Nn = function(x, s) {
    return x = x - 388, t[x];
  }, Nn(n, e);
}
const K = Nn;
function Rn() {
  const n = ["45455RXcAtq", "push", "dispatchEvent", "24CWwNCb", "1094061TeyXaQ", "objects", "groupdrag", "433027goJEmx", "hasChild", "4961850oGzcJA", "4XxWgZT", "60ylBacz", "isEmpty", "groupdragend", "1308852qNrxwD", "draggable", "remove", "dragHandler", "addAll", "4MXFnng", "36cEqfBf", "dragEndHandler", "selectedHandler", "getNoChildrensObjects", "group", "defaultPrevented", "90142NANlZo", "has", "event", "1903693UnOsqj"];
  return Rn = function() {
    return n;
  }, Rn();
}
(function(n, e) {
  const t = Nn, x = n();
  for (; []; )
    try {
      if (parseInt(t(404)) / 1 * (parseInt(t(388)) / 2) + -parseInt(t(412)) / 3 * (-parseInt(t(397)) / 4) + parseInt(t(408)) / 5 + parseInt(t(398)) / 6 * (-parseInt(t(415)) / 7) + parseInt(t(411)) / 8 * (parseInt(t(392)) / 9) + parseInt(t(417)) / 10 + parseInt(t(407)) / 11 * (-parseInt(t(389)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Rn, 250050);
let zi = new InputEvent(pt[K(391)]), ca = new InputEvent(pt[K(414)]);
class Dr extends Tt {
  constructor() {
    const e = K;
    super(), this[e(413)] = [], this.isDraging = ![], this.noChildrensObjects;
  }
  [K(390)]() {
    return this.objects.length == 0;
  }
  mouseoutHandler(e) {
    const t = K;
    this.isDraging = ![], !this.isEmpty() && this[t(410)](zi);
  }
  [K(395)](e) {
    const t = K;
    if (this.dispatchEvent(ca), e[t(406)] instanceof MouseEvent && e[t(406)][t(403)] == !![])
      return;
    let x = this[t(401)]();
    for (var s = 0; s < x.length; s++) {
      let i = x[s];
      i[t(393)] && i[t(395)](e);
    }
  }
  [K(399)](e) {
    const t = K;
    this.dispatchEvent(zi);
    let x = this[t(401)]();
    for (var s = 0; s < x.length; s++) {
      let i = x[s];
      i.draggable && i.dragEndHandler(e);
    }
  }
  [K(401)]() {
    return C.getNoChildrensObjects(this.objects);
  }
  [K(396)](e) {
    const t = K;
    for (var x = 0; x < e.length; x++) {
      let s = e[x];
      s.group = this, !N[t(416)](this[t(413)], s) && (s.selectedHandler && s[t(400)](), this[t(413)].push(s));
    }
    return this;
  }
  add(e) {
    const t = K;
    return e.group = this, N.hasChild(this[t(413)], e) ? this : (e.selectedHandler.call(e), this.objects[t(409)](e), this);
  }
  remove(e) {
    const t = K;
    return e.group = void 0, e.unselectedHandler && e.unselectedHandler(e), N[t(394)](this.objects, e), this;
  }
  removeAll() {
    const e = K;
    let t = this.objects;
    for (var x = 0; x < t.length; x++) {
      let s = t[x];
      s[e(402)] = void 0, s.unselectedHandler && s.unselectedHandler();
    }
    return this.objects.length = 0, this;
  }
  [K(405)](e) {
    return N[K(416)](this.objects, e);
  }
}
const Ks = je;
function je(n, e) {
  const t = Wn();
  return je = function(x, s) {
    return x = x - 227, t[x];
  }, je(n, e);
}
(function(n, e) {
  const t = je, x = n();
  for (; []; )
    try {
      if (parseInt(t(230)) / 1 * (-parseInt(t(256)) / 2) + parseInt(t(259)) / 3 * (parseInt(t(258)) / 4) + parseInt(t(253)) / 5 + parseInt(t(234)) / 6 + parseInt(t(240)) / 7 + parseInt(t(264)) / 8 + -parseInt(t(239)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Wn, 772390);
function Wn() {
  const n = ["div", "toFixed", "4786565PpyUMU", "inputSystem", ", h:", "142HoICaD", "start", "4iztAaK", "4189101gnGhkh", "checkDom", "<li>Links: ", "_timer", "hide", "7513760EuInio", "</li>", "jtopo_debugPanel", "stage", "18782hayqsr", "getCurrentLayer", "getAllLinks", "domElement", "3862590HSxJTI", "]</li>", "innerHTML", " y: ", ", y: ", "27445347EgkFOf", "8531110fZOWeh", "<li>Mouse-Layer( x: ", "ceil", "length", "<li>Painted: ", "stageToLocalXY", "origin", "setContent", "layersContainer", "<li>", "<li>&nbsp;&nbsp;-Parent: (x: "];
  return Wn = function() {
    return n;
  }, Wn();
}
class la {
  constructor(e) {
    this.numberFixed = 0, this.stage = e;
  }
  init() {
    const e = je, t = document.createElement(e(251));
    t.classList.add(e(228)), this.stage[e(248)].appendChild(t), this[e(233)] = t, this[e(263)]();
  }
  start(e = 24) {
    const t = je, x = this, s = this[t(229)];
    function i() {
      const r = t;
      if (s[r(254)].isIdle)
        return;
      let o = s.getChildren(), a = 0, c = 0, l = 0;
      for (var h = 0; h < o[r(243)]; h++) {
        let w = o[h];
        a += w.displayList[r(243)], c += w.getAllNodes()[r(243)], l += w[r(232)]()[r(243)];
      }
      let u = Math[r(242)](s.inputSystem.x), p = Math.ceil(s.inputSystem.y), b = s[r(231)](), _ = { x: 0, y: 0 };
      b != null && (_ = b[r(245)](u, p));
      const m = s[r(254)].target;
      let g = "<li>Mouse-Canvas( x: " + u + " y: " + p + ")</li>";
      if (g += r(241) + _.x.toFixed(2) + r(237) + _.y.toFixed(2) + ")</li>", g += "<li>Nodes: " + c + "</li>", g += r(261) + l + "</li>", g += "<li>Total: " + (c + l) + "</li>", g += r(244) + a + "</li>", m) {
        g += "<li>Target: id: " + m.id;
        const w = m._obb.aabb, O = m.toStageXY(0, 0), A = m.toLayerXY(0, 0), D = this.numberFixed;
        g += "<li>_aabb:[x:" + w.x.toFixed(D) + ",y: " + w.y.toFixed(D) + ",w: " + w.width[r(252)](D) + r(255) + w.height.toFixed(D) + "] </li>", g += "<li>Origin: [" + m[r(246)][0].toFixed(D) + ", " + m.origin[1][r(252)](D) + r(235), g += "<li>&nbsp;&nbsp;-Canvas: (x: " + O.x.toFixed(D) + r(238) + O.y.toFixed(D) + ") </li>", m instanceof M && (g += r(250) + m.x.toFixed(D) + ", y: " + m.y.toFixed(D) + ") </li>"), g += "<li>&nbsp;&nbsp;-Layer:  (x: " + A.x[r(252)](D) + r(238) + A.y.toFixed(D) + ") </li>";
      }
      _e.debugInfo && (g += r(249) + _e.debugInfo + r(227)), x[r(247)](g);
    }
    this._timer = setInterval(i, e);
  }
  setContent(e) {
    const t = je;
    this.domElement[t(236)] = e;
  }
  [Ks(260)]() {
    this.domElement == null && this.init();
  }
  hide() {
    const e = Ks;
    return this.checkDom(), clearInterval(this[e(262)]), this.domElement.style.display = "none", this;
  }
  show(e) {
    const t = Ks;
    return this.checkDom(), this.domElement.style.display = "block", this[t(257)](e), this;
  }
}
const q = pe;
(function(n, e) {
  const t = pe, x = n();
  for (; []; )
    try {
      if (parseInt(t(378)) / 1 + parseInt(t(381)) / 2 + -parseInt(t(399)) / 3 * (parseInt(t(393)) / 4) + parseInt(t(410)) / 5 * (parseInt(t(394)) / 6) + -parseInt(t(372)) / 7 * (parseInt(t(373)) / 8) + -parseInt(t(397)) / 9 * (-parseInt(t(385)) / 10) + parseInt(t(408)) / 11 * (parseInt(t(417)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Hn, 350312);
class Br extends Or {
  constructor(e, t) {
    const x = pe;
    super(t), this[x(400)] = e;
  }
  dontNeedPickup(e) {
    return !![];
  }
  paintSelected(e) {
  }
  overviewPaint(e) {
    const t = pe;
    let x = this.context;
    if (x.save(), e._doTransform(x), e[t(406)][t(405)](x), Ve.flatten) {
      const s = e.flattenList.filter((i) => i._needPaint && i._cameraVisible);
      this._paintFlattenObjects(s);
    } else
      this[t(391)](e[t(404)], !![]);
    x.restore();
  }
  exportPaintObjects(e) {
    const t = pe;
    e[0] instanceof ft ? e.forEach((x) => this[t(380)](x)) : this[t(391)](e, !![]);
  }
}
class ha {
  constructor(e) {
    const t = pe;
    this[t(386)] = ![], this[t(388)] = !![], this.paintInterval = 500, this[t(400)] = e, this.inputSystem = new ki(), this[t(382)] = new Br(e);
    let x = this.render.canvas;
    x.style.backgroundColor = t(375), x[t(376)].border = t(367), x[t(376)][t(392)] = "absolute", x[t(376)][t(369)] = "" + (e.handlerLayer[t(369)] + 1), x[t(376)].opacity = "0.7", x.style.right = "0", x[t(376)][t(374)] = null, x.style.bottom = "0", e.layersContainer.appendChild(x), this[t(415)] = x, this.domElement = x, this.render.setSize(200, 200 * 0.618), this[t(402)] = new M(null, 0, 0), this[t(402)].hide(), this.initEvent(), this.hide();
  }
  [q(370)](e) {
    const t = q;
    if (e == null)
      return this;
    for (let x in e)
      this.canvas[t(376)][x] = e[x];
    return this;
  }
  initEvent() {
    const e = q;
    let t = this, x = [e(384), "mouseup", "mousemove", "mousewheel"], s = t.render, i = s.canvas, r = this.inputSystem;
    x.map(function(o) {
      Ye.addEventListener(i, o, function(a) {
        if (a.offsetX < 0 || a.offsetY < 0)
          return;
        r.updateMouseInfo(a, o);
        let c = o + "Handler";
        if (t[c] == null)
          throw new Error("Overview has no handler:" + o);
        t[c](a);
      });
    });
  }
  [q(368)]() {
    const e = q;
    this.visible = !![], this[e(382)][e(368)](), clearInterval(this._overviewTimer);
    let t = this;
    return this._overviewTimer = setInterval(function() {
      const x = e;
      t[x(388)] && t[x(398)]();
    }, this[e(379)]), this;
  }
  [q(418)]() {
    return this.visible = ![], this.render.hide(), clearInterval(this._overviewTimer), this;
  }
  getWidth() {
    const e = q;
    return this.render[e(383)]();
  }
  getHeight() {
    return this.render.getHeight();
  }
  [q(377)](e, t) {
    return this.render.setSize(e, t);
  }
  paint() {
    const e = q;
    this.dirty = ![];
    let t = this, x = t.stage, s = t.render, i = t.getWidth(), r = t.getHeight(), o = this.stage.getExportAABB(), a = Math.max(i, o[e(387)]), c = Math[e(416)](r, o.height), l = i / a, h = r / c;
    s.clearAll();
    let u = s.context;
    u.save(), u.scale(l, h), u.translate(-o.x, -o.y), x.getChildren().forEach(function(p) {
      s.overviewPaint(p);
    }), u.restore(), this.paintDragRect(u, o);
  }
  [q(413)](e, t) {
    const x = q;
    let s = this.rectObj, i = this.stage, r = this.render, o = Math.max(t[x(387)], i.width), a = Math.max(t.height, i.height), c = i.width / o, l = i.height / a;
    if (c == 1 && l == 1) {
      s.hide();
      return;
    }
    s.show();
    let h = r[x(383)]() * c, u = r[x(407)]() * l;
    s.resizeTo(h, u);
    let p = r.getWidth() / o, b = r.getHeight() / a, _ = -t.x * p, m = -t.y * b;
    _ < 0 && (_ = 0), m < 0 && (m = 0), _ + s.width > r.getWidth() && (_ = r.getWidth() - s.width), m + s.height > r.getHeight() && (m = r.getHeight() - s[x(371)]), s.translateTo(_, m), e.save(), e[x(414)] = 2, e.fillStyle = "rgba(0,250,50,0.2)", e[x(390)] = x(366), e.beginPath(), e.rect(s.x, s.y, s[x(387)], s.height), e.stroke(), e[x(409)](), e.restore();
  }
  update() {
    const e = q;
    this.visible && this.stage[e(404)].length > 0 && (this.stage.update(), this[e(396)]());
  }
  mousedownHandler() {
    const e = q;
    let t = this[e(402)][e(403)](), x = this.inputSystem.x, s = this.inputSystem.y;
    t.contains(x, s) && (this.inputSystem.target = this.rectObj);
    let i = x - (this[e(402)].x + this[e(402)].width / 2), r = s - (this[e(402)].y + this[e(402)].height / 2);
    this.moveWith(i, r);
  }
  mousedragHandler() {
    const e = q;
    let t = this.inputSystem.dx, x = this.inputSystem.dy;
    this.moveWith(t, x), this.stage[e(412)] && this.stage.editor.update();
  }
  [q(401)](e, t) {
    const x = q;
    if (!this.rectObj[x(386)])
      return;
    e < 0 && this[x(402)].x + e <= 0 && (e = -this.rectObj.x), e > 0 && this[x(402)].x + this[x(402)].width >= this.getWidth() && (e = this.getWidth() - this.rectObj.width - this.rectObj.x), t < 0 && this.rectObj.y <= 0 && (t = -this.rectObj.y), t > 0 && this.rectObj.y + this.rectObj.height >= this.getHeight() && (t = this.getHeight() - this.rectObj[x(371)] - this.rectObj.y), this.rectObj.translateWith(e, t);
    let i = this.stage.getExportAABB(), r = i[x(387)], o = i.height, a = r * (e / this.getWidth()), c = o * (t / this.getHeight());
    this[x(400)].getChildren().forEach(function(h, u) {
      h.translateWith(-a, -c);
    });
    let l = this;
    this[x(389)] = setTimeout(function() {
      l.update();
    }, 20), this[x(400)].update();
  }
  mousewheelHandler(e) {
    e[q(411)]();
  }
  [q(395)]() {
    this.inputSystem.isDraging && this.mousedragHandler();
  }
  mouseupHandler() {
    this.inputSystem.target = null;
  }
}
function pe(n, e) {
  const t = Hn();
  return pe = function(x, s) {
    return x = x - 366, t[x];
  }, pe(n, e);
}
function Hn() {
  const n = ["overviewPaint", "593478mihQnn", "render", "getWidth", "mousedown", "906890hhYwRq", "visible", "width", "dirty", "_updateTimer", "strokeStyle", "_paintObjects", "position", "16eCxAeY", "137106GCMYMy", "mousemoveHandler", "paint", "18RBLNiS", "update", "305718oqlIAj", "stage", "moveWith", "rectObj", "getRect", "children", "applyTo", "_computedStyle", "getHeight", "40986eLTLYg", "fill", "60vdBUNr", "preventDefault", "editor", "paintDragRect", "lineWidth", "canvas", "max", "192VHYfQE", "hide", "red", "solid 1px gray", "show", "zIndex", "css", "height", "68243IQJIko", "496gAyoul", "left", "rgba(255,255,255,0.5)", "style", "setSize", "550429xqXons", "paintInterval"];
  return Hn = function() {
    return n;
  }, Hn();
}
const ua = `.jtopo_popoupmenu{padding:10px;border-radius:5px;min-width:210px;background-color:#16436b;border:1px solid #16436b;position:absolute;z-index:10000}.jtopo_popoupmenu .header{font-size:14px;height:24px;padding-bottom:3px}.jtopo_popoupmenu a{text-rendering:optimizeLegibility;font-family:Open Sans,Helvetica Neue,Helvetica,Arial,sans-serif;padding-left:20px;display:block;height:25px;color:#fff;font-size:13px;cursor:pointer}.jtopo_popoupmenu a:hover{color:#fff}.jtopo_iconsPanel{opacity:.8;padding-left:5px;position:absolute;background-color:#e8e8e8;top:90px;width:52px;height:425px;z-index:1000;border-radius:5px}.jtopo_iconsPanel .item{border:1px solid white;width:42px;height:42px;margin-top:10px}.jtopo_toolbar{border-bottom:1px dotted;padding-bottom:2px;border-color:#e0e0e0;width:100%;min-height:33px;background-color:#e8e8e8}.jtopo_toolbar .group{float:left;margin-right:5px}.jtopo_toolbar .item{float:left;width:32px;height:32px;stroke:gray;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;fill:none;font-size:12px;color:gray}.jtopo_toolbar .active{background-color:#d3d3d3;border:1px solid black}.jtopo_toolbar input[type=text]{font-size:12px;color:gray;float:left;width:120px;height:26px;border:1px solid white;margin:2px 2px 2px 4px}.jtopo_input_textfield{position:absolute;display:none;font-size:smaller;z-index:10000}.jtopo_tooltip{pointer-events:none;opacity:.9;min-width:30px;min-height:30px;padding:10px;border-radius:5px;background-color:#0C387B;position:absolute;z-index:10000}.jtopo_historyPanel{position:absolute;left:0px;top:100%;width:879px;overflow-x:scroll;height:600px;z-index:1000}.jtopo_debugPanel{user-select:none;border:dashed 1px gray;padding:8px;position:absolute;left:0px;top:0%;width:300px;z-index:98;text-align:left;font-size:10px;color:gray}
`, rt = gt;
(function(n, e) {
  const t = gt, x = n();
  for (; []; )
    try {
      if (parseInt(t(336)) / 1 * (-parseInt(t(338)) / 2) + parseInt(t(331)) / 3 * (parseInt(t(322)) / 4) + parseInt(t(328)) / 5 * (parseInt(t(357)) / 6) + parseInt(t(354)) / 7 + -parseInt(t(348)) / 8 + -parseInt(t(362)) / 9 * (parseInt(t(355)) / 10) + -parseInt(t(314)) / 11 * (-parseInt(t(317)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Fn, 510624);
function Fn() {
  const n = ["isPause", "alternate", "2790976iWNUvP", "from", "animations", "delayed", "currentTime", "finished", "262374QEsVSk", "6509830ZLlGpM", "now", "1368nuYalk", "timeline", "pause", "alternate-reverse", "reject", "9yuCkiU", "remove", "465575CifuQm", "isArray", "direction", "336rfYYcW", "sqrt", "catch", "onEnd", "tick", "2805992epYfmu", "fillMode", "playState", "stepAction", "play", "system", "10075VZWMqH", "reverse", "asin", "3OeBMmV", "resolve", "setAttr", "cancel", "add", "291007NjMHIE", "length", "6uBWxPb", "sin", "playedTimes", "promise", "pow", "continue", "duration", "startTime"];
  return Fn = function() {
    return n;
  }, Fn();
}
function da(n, e, t, x) {
  return t * n / x + e;
}
function fa(n, e, t, x) {
  return t * (n /= x) * n + e;
}
function pa(n, e, t, x) {
  return -t * (n /= x) * (n - 2) + e;
}
function ga(n, e, t, x) {
  return (n /= x / 2) < 1 ? t / 2 * n * n + e : -t / 2 * (--n * (n - 2) - 1) + e;
}
function ba(n, e, t, x) {
  return -t * Math.cos(n / x * (Math.PI / 2)) + t + e;
}
function ya(n, e, t, x) {
  return t * Math[gt(339)](n / x * (Math.PI / 2)) + e;
}
function _a(n, e, t, x) {
  return -t / 2 * (Math.cos(Math.PI * n / x) - 1) + e;
}
function ma(n, e, t, x) {
  return n == 0 ? e : t * Math.pow(2, 10 * (n / x - 1)) + e;
}
function Ia(n, e, t, x) {
  return n == 0 ? e : n == x ? e + t : (n /= x / 2) < 1 ? t / 2 * Math.pow(2, 10 * (n - 1)) + e : t / 2 * (-Math.pow(2, -10 * --n) + 2) + e;
}
function wa(n, e, t, x) {
  return -t * (Math.sqrt(1 - (n /= x) * n) - 1) + e;
}
function va(n, e, t, x) {
  return t * Math[gt(318)](1 - (n = n / x - 1) * n) + e;
}
function ka(n, e, t, x) {
  const s = gt;
  return (n /= x / 2) < 1 ? -t / 2 * (Math[s(318)](1 - n * n) - 1) + e : t / 2 * (Math.sqrt(1 - (n -= 2) * n) + 1) + e;
}
function Sa(n, e, t, x) {
  return t * (n /= x) * n * n + e;
}
function Pa(n, e, t, x) {
  return t * ((n = n / x - 1) * n * n + 1) + e;
}
function Oa(n, e, t, x) {
  return (n /= x / 2) < 1 ? t / 2 * n * n * n + e : t / 2 * ((n -= 2) * n * n + 2) + e;
}
function La(n, e, t, x) {
  return t * (n /= x) * n * n * n + e;
}
function Ca(n, e, t, x) {
  return -t * ((n = n / x - 1) * n * n * n - 1) + e;
}
function Ea(n, e, t, x) {
  return (n /= x / 2) < 1 ? t / 2 * n * n * n * n + e : -t / 2 * ((n -= 2) * n * n * n - 2) + e;
}
function Ta(n, e, t, x) {
  return t * (n /= x) * n * n * n * n + e;
}
function Aa(n, e, t, x) {
  return t * ((n = n / x - 1) * n * n * n * n + 1) + e;
}
function Ma(n, e, t, x) {
  return (n /= x / 2) < 1 ? t / 2 * n * n * n * n * n + e : t / 2 * ((n -= 2) * n * n * n * n + 2) + e;
}
function Da(n, e, t, x) {
  const s = gt;
  var o = 1.70158, i = 0, r = t;
  if (n == 0)
    return e;
  if ((n /= x) == 1)
    return e + t;
  if (i || (i = x * 0.3), r < Math.abs(t)) {
    r = t;
    var o = i / 4;
  } else
    var o = i / (2 * Math.PI) * Math[s(330)](t / r);
  return -(r * Math.pow(2, 10 * (n -= 1)) * Math.sin((n * x - o) * (2 * Math.PI) / i)) + e;
}
function Ba(n, e, t, x) {
  const s = gt;
  var o = 1.70158, i = 0, r = t;
  if (n == 0)
    return e;
  if ((n /= x) == 1)
    return e + t;
  if (i || (i = x * 0.3), r < Math.abs(t)) {
    r = t;
    var o = i / 4;
  } else
    var o = i / (2 * Math.PI) * Math.asin(t / r);
  return r * Math[s(342)](2, -10 * n) * Math[s(339)]((n * x - o) * (2 * Math.PI) / i) + t + e;
}
function ja(n, e, t, x) {
  const s = gt;
  var o = 1.70158, i = 0, r = t;
  if (n == 0)
    return e;
  if ((n /= x / 2) == 2)
    return e + t;
  if (i || (i = x * (0.3 * 1.5)), r < Math.abs(t)) {
    r = t;
    var o = i / 4;
  } else
    var o = i / (2 * Math.PI) * Math.asin(t / r);
  return n < 1 ? -0.5 * (r * Math[s(342)](2, 10 * (n -= 1)) * Math.sin((n * x - o) * (2 * Math.PI) / i)) + e : r * Math[s(342)](2, -10 * (n -= 1)) * Math.sin((n * x - o) * (2 * Math.PI) / i) * 0.5 + t + e;
}
function za(n, e, t, x, s) {
  return s == null && (s = 1.70158), t * (n /= x) * n * ((s + 1) * n - s) + e;
}
function Na(n, e, t, x, s) {
  return s == null && (s = 1.70158), t * ((n = n / x - 1) * n * ((s + 1) * n + s) + 1) + e;
}
function Ra(n, e, t, x, s) {
  return s == null && (s = 1.70158), (n /= x / 2) < 1 ? t / 2 * (n * n * (((s *= 1.525) + 1) * n - s)) + e : t / 2 * ((n -= 2) * n * (((s *= 1.525) + 1) * n + s) + 2) + e;
}
function jr(n, e, t, x) {
  return t - Si(x - n, 0, t, x) + e;
}
function Si(n, e, t, x) {
  return (n /= x) < 1 / 2.75 ? t * (7.5625 * n * n) + e : n < 2 / 2.75 ? t * (7.5625 * (n -= 1.5 / 2.75) * n + 0.75) + e : n < 2.5 / 2.75 ? t * (7.5625 * (n -= 2.25 / 2.75) * n + 0.9375) + e : t * (7.5625 * (n -= 2.625 / 2.75) * n + 0.984375) + e;
}
function Wa(n, e, t, x) {
  return n < x / 2 ? jr(n * 2, 0, t, x) * 0.5 + e : Si(n * 2 - x, 0, t, x) * 0.5 + t * 0.5 + e;
}
function gt(n, e) {
  const t = Fn();
  return gt = function(x, s) {
    return x = x - 313, t[x];
  }, gt(n, e);
}
let Ha = { easeLinear: da, easeInQuad: fa, easeOutQuad: pa, easeInOutQuad: ga, easeInSine: ba, easeOutSine: ya, easeInOutSine: _a, easeInExpo: ma, easeInOutExpo: Ia, easeInCirc: wa, easeOutCirc: va, easeInOutCirc: ka, easeInCubic: Sa, easeOutCubic: Pa, easeInOutCubic: Oa, easeInQuart: La, easeOutQuart: Ca, easeInOutQuart: Ea, easeInQuint: Ta, easeOutQuint: Aa, easeInOutQuint: Ma, easeInElastic: Da, easeOutElastic: Ba, easeInOutElastic: ja, easeInBack: za, easeOutBack: Na, easeInOutBack: Ra, easeInBounce: jr, easeOutBounce: Si, easeInOutBounce: Wa };
class zr {
  constructor(e, t, x, s) {
    const i = gt;
    this.duration = 1e3, this.delay = 0, this[i(316)] = "normal", this[i(323)] = "none", this[i(324)] = "idle", this[i(346)] = ![], this[i(353)] = ![], this.delayed = ![], this.times = 1, this[i(340)] = 0, this.effect = "easeLinear", this.from = e, this.to = t, x != null && (this.duration = x), this.update = s;
  }
  set(e, t, x, s) {
    const i = gt;
    return this.from = e, this.to = t, this[i(344)] = x, this.update = s, this;
  }
  setFrom(e) {
    const t = gt;
    return this[t(349)] = e, this;
  }
  setTo(e) {
    return this.to = e, this;
  }
  [rt(333)](e) {
    return Object.assign(this, e), this;
  }
  onUpdate(e) {
    return this.update = e, this;
  }
  cancel() {
    const e = rt;
    return this.system && this.system.remove(this), this.reject && (this[e(361)](), this[e(361)] = null), this.playState = "finished", this;
  }
  [rt(359)]() {
    return this.playState = "paused", this.isPause = !![], this;
  }
  [rt(343)]() {
    const e = rt;
    return this.startTime = Date[e(356)]() - this[e(352)], this[e(324)] = "running", this.isPause = ![], this;
  }
  tick(e) {
    const t = rt;
    if (this.playState != "running")
      return ![];
    let x = e - this[t(345)];
    return this.currentTime = x, x >= this.duration ? (this.playState = "finished", this.system[t(313)](this), this.stepAction(this[t(344)]), this.playedTimes < this.times ? this[t(326)]() : (this[t(332)](), this[t(332)] = null, this.onEnd && this[t(320)]())) : this.stepAction(x), !![];
  }
  play() {
    const e = rt;
    let t = this;
    this[e(327)][e(335)](this), this.playedTimes++, this.isPause = ![], this.delay != 0 && this.delayed == ![] ? (setTimeout(function() {
      const r = e;
      t[r(345)] = Date.now(), t.playState = "running";
    }, this.delay), t[e(351)] = !![]) : (t[e(345)] = Date.now(), t.playState = "running");
    const x = this;
    let s = this._getTickAction();
    this[e(325)] = s;
    let i = this[e(341)];
    return i == null && (i = new Promise(function(r, o) {
      x[e(332)] == null && (x.resolve = r, x.reject = o);
    }), this[e(341)] = this.promise), i[e(319)]((r) => {
    });
  }
  _getTickAction() {
    const e = rt;
    let t = this.effect, x = this.from, s = this.to, i = this.duration, r = this.update, o = x, a = s;
    if (typeof x == "number" && (o = [x], a = [s]), this[e(316)] == e(329) || this.direction == "alternate-reverse") {
      let O = o;
      o = a, a = O;
    }
    let c = o[0];
    const l = Array[e(315)](x), h = typeof c == "number", u = c.x != null || c.y != null;
    let p = o.slice(), b, _ = Ha[t], m = this[e(316)] == e(347) || this.direction == e(360), g = this, w = this[e(344)] * 0.5;
    if (h)
      b = function(O) {
        const A = e;
        let D = O;
        m && (O > w ? D = g[A(344)] * 2 - D * 2 : D = O * 2);
        for (let St = 0; St < o.length; St++) {
          const Pt = o[St], Ge = a[St], $t = Ge - Pt;
          if ($t == 0)
            p[St] = Pt;
          else {
            let Xr = _(D, Pt, $t, i);
            p[St] = Xr;
          }
        }
        r(l ? p : p[0]);
      };
    else if (u)
      b = function(O) {
        for (let A = 0; A < o.length; A++) {
          const D = o[A], St = a[A], Pt = St.x - D.x, Ge = St.y - D.y;
          let $t = { x: D.x, y: D.y };
          Pt != 0 && ($t.x = _(O, D.x, Pt, i)), Ge != 0 && ($t.y = _(O, D.y, Ge, i)), p[A] = $t;
        }
        r(l ? p : p[0]);
      };
    else
      throw new Error("value format error.");
    return b;
  }
}
class Nr {
  constructor() {
    const e = rt;
    this.animations = [], this[e(358)] = { begin: 1735488e6, end: Date.now() };
  }
  add(e) {
    this[rt(350)].indexOf(e) == -1 && this.animations.push(e);
  }
  remove(e) {
    N.remove(this.animations, e);
  }
  cancelAll() {
    const e = rt;
    for (let t = 0; t < this.animations[e(337)]; t++)
      this.animations[t][e(334)]();
    this.animations.length = 0;
  }
  [rt(321)](e) {
    const t = rt;
    let x = ![];
    for (let s = 0; s < this.animations.length; s++)
      this[t(350)][s].tick(e) && x == ![] && (x = !![]);
    return this.animations = this.animations.filter((s) => s.playState != t(353)), x;
  }
  anime(e) {
    const t = rt;
    e[t(344)] == null && (e[t(344)] = 1e3);
    let x = new zr();
    return x.system = this, x.setAttr(e), x;
  }
}
const px = It;
(function(n, e) {
  const t = It, x = n();
  for (; []; )
    try {
      if (parseInt(t(365)) / 1 + parseInt(t(358)) / 2 + parseInt(t(356)) / 3 + -parseInt(t(351)) / 4 + -parseInt(t(352)) / 5 * (-parseInt(t(385)) / 6) + -parseInt(t(366)) / 7 + parseInt(t(377)) / 8 * (-parseInt(t(386)) / 9) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Xn, 583158);
function It(n, e) {
  const t = Xn();
  return It = function(x, s) {
    return x = x - 349, t[x];
  }, It(n, e);
}
function Xn() {
  const n = ["length", "currentTheme", "dirty", "8rfbjLP", "name", "className", "update", "stringify", "parent", "getStyle", "_afterStyleComputed", "1362eatEok", "7193394aKETxT", "Resources", "stage", "811768abEcCU", "16965cymKGH", "toJSON", "keys", "addTheme", "348354SxJrBa", "defStyles", "1246846KFHXBa", "setTheme", "assign", "LinkLabel", "markDirty", "_computedStyle", "getTheme", "526974fdbypz", "3159520PpwlQL", "getComputedStyle", "DefaultLight", "SelectedStyle", "filter", "styles", "CustomStyle", "themeName"];
  return Xn = function() {
    return n;
  }, Xn();
}
class Rr {
  constructor(e) {
    const t = It;
    this.themes = {}, this.defStyles = {}, this.selectedStyle = Te.getStyle(t(369)), this[t(350)] = e, this[t(355)](Te), this[t(355)](Sr), this.setTheme(Te.name);
  }
  addTheme(e) {
    this.themes[e.name] = e;
  }
  getTheme(e) {
    return this.themes[e];
  }
  customStyleToJSON(e) {
    const t = It;
    let x = JSON.parse(JSON[t(381)](this.themes));
    delete x[t(368)], delete x.DefaultDark;
    let s = {}, i = { themeName: this.currentTheme.name, themes: x, styles: s }, r = Object.keys(this.defStyles);
    for (let o = 0; o < r[t(374)]; o++) {
      let a = r[o], c = this.defStyles[a], l = c[t(353)](e);
      s[a] = l;
    }
    return i;
  }
  fromJson(e) {
    const t = It;
    let x = e[t(372)];
    if (x == null)
      return;
    let s = x.themes, i = x[t(371)] || {};
    if (s != null) {
      let a = Object[t(354)](s);
      for (let c = 0; c < a.length; c++) {
        let l = a[c], h = s[l], u = new O0(l, h.content);
        this.addTheme(u);
      }
    }
    let r = e[t(349)] || {};
    this[t(357)] = {};
    let o = Object.keys(i)[t(370)]((a) => a.startsWith("."));
    for (let a = 0; a < o.length; a++) {
      let c = o[a], l = i[c];
      this.defStyles[c] = ct.fromJSON(l, r);
    }
    x[t(373)] != null && this[t(359)](x.themeName);
  }
  defClass(e, t) {
    const x = It;
    if (t instanceof ct) {
      this.defStyles[e] = t;
      return;
    }
    let s = new ct();
    Object.assign(s, t), this.defStyles[e] = s, !e.startsWith(".") && this[x(362)]();
  }
  removeClass(e) {
    const t = It;
    delete this.defStyles[e], !e.startsWith(".") && this[t(362)]();
  }
  getClass(e) {
    return this.defStyles[e];
  }
  markDirty() {
    const e = It;
    this[e(350)].children.forEach((x) => {
      const s = e;
      x.style.dirty = !![], x.querySelectorAll().forEach((i) => i.style[s(376)] = !![]);
    });
  }
  start() {
    const e = It;
    this.setTheme(this[e(375)][e(378)]);
  }
  setTheme(e) {
    let x = this[It(364)](e);
    if (x == null)
      throw new Error("theme not exist.");
    this.currentTheme = x, this.markDirty(), this.stage.update();
  }
  [px(367)](e) {
    return e[px(363)];
  }
  computeStyle(e) {
    const t = px;
    let x = this, s = {}, i = this.currentTheme, r = e[t(379)];
    e[t(382)] instanceof R && (e === e.parent.label ? r = t(361) : (e === e.parent.beginArrow || e === e.parent.endArrow) && (r = "LinkArrow"));
    let o = i[t(383)](r);
    o != null && Object.assign(s, o);
    let a = x.getClass(r);
    a != null && Object[t(360)](s, a);
    let c = e.classList;
    if (c.length > 0)
      for (let l = 0; l < c[t(374)]; l++) {
        const h = c[l];
        let u = x.getClass(h);
        u != null && Object.assign(s, u);
      }
    return Object.assign(s, e.style.getChangedProps()), e._computedStyle[t(380)](s), e[t(384)](), e._computedStyle;
  }
  defTheme(e, t) {
    let s = this[px(364)](e);
    if (s == null)
      throw new Error("theme not exist:" + e);
    let i = s.copy(t);
    return this.addTheme(i), i;
  }
}
const we = ex;
function ex(n, e) {
  const t = Yn();
  return ex = function(x, s) {
    return x = x - 476, t[x];
  }, ex(n, e);
}
(function(n, e) {
  const t = ex, x = n();
  for (; []; )
    try {
      if (parseInt(t(516)) / 1 * (parseInt(t(504)) / 2) + -parseInt(t(476)) / 3 * (parseInt(t(518)) / 4) + parseInt(t(479)) / 5 * (parseInt(t(505)) / 6) + -parseInt(t(497)) / 7 + parseInt(t(485)) / 8 * (-parseInt(t(478)) / 9) + -parseInt(t(480)) / 10 + parseInt(t(494)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Yn, 357680);
function Yn() {
  const n = ["style", "destroyed", "_requestReapint", "painted", "132368CZVEUF", "_frames", "_renderLayerBefore", "animationSystem", "_cameraVisible", "currentTime", "mode", "visible", "isOutOfViewPort", "8955034wGDmtC", "updateMatrix", "now", "4281921zKhRAZ", "clone", "computeStyle", "_isOutOfViewport", "requestTimer", "length", "dirty", "4114OEVxNc", "696HbpgrZ", "localView", "stage", "_isMatrixDirty", "displayList", "width", "tick", "isLink", "onUpdate", "flatten", "_needPaint", "12TyjftT", "styleSystem", "2020WgWNQN", "975KbgvBF", "getViewportRectInLayer", "9EmWkVf", "14830GeGRQv", "327800tRqVqc"];
  return Yn = function() {
    return n;
  }, Yn();
}
class Fa {
  constructor(e) {
    const t = ex;
    this.timeline = { currentTime: Date[t(496)]() }, this.stage = e;
  }
  [we(487)](e) {
    const t = we;
    e.style.dirty && !(e instanceof Gs) && this[t(507)][t(517)][t(499)](e), e[t(508)]() && e[t(495)](), e.clearMatrixDirtyMark(), e.style.dirty = ![];
    let x = C[t(514)](e.children, null);
    e.flattenList = x, e[t(509)].length = 0;
    for (let s = 0; s < x[t(502)]; s++) {
      let i = x[s];
      i[t(481)][t(503)] && this.stage[t(517)][t(499)](i), i._isMatrixDirty() && i[t(495)](), i._afterUpdate(), i.clearMatrixDirtyMark(), i.style[t(503)] = ![], i.painted = ![], i.isPointOn = ![], i[t(500)] = this.isOutOfViewPort(i, e);
      let r = i.visible && i.parent._needPaint;
      i[t(515)] = r, i._isMouseInAABB = ![], r && !i._isOutOfViewport && (i._isMouseInAABB = this.isMouseInObjectAABB(i, e));
    }
    for (let s = 0; s < x[t(502)]; s++) {
      let i = x[s];
      (i._isMatrixDirty() || i.parent[t(512)]) && (i[t(495)](), i._afterUpdate(), i.clearMatrixDirtyMark(), i[t(500)] = this.isOutOfViewPort(i, e), i._isMouseInAABB = ![], i._needPaint && !i._isOutOfViewport && (i._isMouseInAABB = this.isMouseInObjectAABB(i, e))), i._needPaint && !i[t(500)] && i[t(489)] && e.displayList.push(i);
    }
  }
  [we(493)](e, t) {
    const x = we;
    if (t.cuttingHide == ![])
      return ![];
    let i = t[x(477)](), r = e._obb;
    return r.aabb == null ? ![] : !i.isIntersectRect(r.aabb);
  }
  isMouseInObjectAABB(e, t) {
    const x = we, s = t[x(507)];
    if (s.inputSystem.x < 0 || s.inputSystem.y < 0)
      return ![];
    let i = e._obb.aabb[x(498)]();
    return e instanceof R && (i = i[x(498)](), i.x -= 2, i.y -= 2, i[x(510)] += 4, i.height += 4), i.contains(t.mouseX, t.mouseY);
  }
  _tickLayer(e, t) {
    const x = we;
    let s = e.render;
    if (!(e[x(492)] == ![] || e[x(482)] || s.stoped || s.destoryed == !![])) {
      if (e[x(486)] == 0) {
        (e[x(483)] == !![] || C._anyMatrixOrStyleDirty(e)) && (e[x(483)] = ![], this._renderLayerBefore(e), s.renderLayer(e), e.renderTimes++);
        return;
      }
      this[x(487)](e), s.renderLayer(e), e.renderTimes++;
    }
  }
  pause() {
    cancelAnimationFrame(this.requestTimer);
  }
  start() {
    const e = we, t = this.stage, x = this, s = t.handlerLayer, i = t.children, r = this.timeline, o = t[e(488)], a = t.behaviourSystem;
    {
      let h = t[e(488)].timeline.begin;
      this.timeline[e(490)] >= h && (t.localView[e(491)] = e(484));
    }
    const c = t[e(506)][e(491)] == e(484);
    function l() {
      const h = e;
      if (t.destoryed)
        return;
      let u = Date.now();
      r.currentTime = u, a.tick(u);
      let p = ![];
      !c && (p = o[h(511)](u)), p == !![] && (s[h(483)] = !![]), x._tickLayer(s, u);
      for (let b = 0; b < i.length; b++) {
        let _ = i[b];
        p && (_._requestReapint = !![]), _[h(513)] != null && _.onUpdate(), x._tickLayer(_, u);
      }
      x[h(501)] = requestAnimationFrame(l);
    }
    l();
  }
}
const ve = u0;
function Jn() {
  const n = ["186680SiAZnc", "href", "882iyLFLp", "toDataURL", "revokeObjectURL", "download", "unionRects", "saveDataAsFile", "469568Gbntsf", "about:blank", "appendChild", "3465553vBULlV", "61750UUnymq", "60wSlZNz", "save", "saveAsLocalImage", "639012VqzOgi", "body", "9948037xKNHhk", "389SkCBQS", "_obb", "8xWLZma", "117vmeHRX"];
  return Jn = function() {
    return n;
  }, Jn();
}
(function(n, e) {
  const t = u0, x = n();
  for (; []; )
    try {
      if (parseInt(t(123)) / 1 * (parseInt(t(129)) / 2) + parseInt(t(120)) / 3 * (parseInt(t(125)) / 4) + -parseInt(t(116)) / 5 * (parseInt(t(117)) / 6) + parseInt(t(115)) / 7 + -parseInt(t(112)) / 8 + parseInt(t(126)) / 9 * (parseInt(t(127)) / 10) + -parseInt(t(122)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Jn, 248757);
function u0(n, e) {
  const t = Jn();
  return u0 = function(x, s) {
    return x = x - 112, t[x];
  }, u0(n, e);
}
class Xa {
  constructor(e) {
    this.stage = e, this.render = new Br(e);
  }
  [ve(130)](e) {
    const t = ve;
    let x = Array.isArray(e) ? e : [e];
    return this._exportPaint(x), this.render[t(130)]();
  }
  toImage(e) {
    let t = this.toDataURL(e), x = new Image();
    return x.src = t, x;
  }
  [ve(119)](e, t) {
    const x = ve;
    let s = this.toDataURL(e);
    t == null && (t = "jtopo_" + (/* @__PURE__ */ new Date()).getTime() + ".png"), this[x(134)](s, t);
  }
  saveImageInfo(e) {
    const t = ve;
    let x = this.toDataURL(e);
    window.open(t(113)).document.write("<img src='" + x + "' alt='from canvas'/>");
  }
  download(e, t) {
    const x = new File([t], e, { type: "text/json" });
    function s(i) {
      const r = u0, o = document.createElement("a"), a = URL.createObjectURL(i);
      o[r(128)] = a, o[r(132)] = i.name, document.body[r(114)](o), o.click(), document[r(121)].removeChild(o), URL[r(131)](a);
    }
    s(x);
  }
  _exportPaint(e) {
    const t = ve;
    let x = this.render, s = e.map((a) => {
      const c = u0;
      return a instanceof ft ? a.toStageRect(a.getExportAABB()) : a[c(124)].aabb;
    }), i = z[t(133)](s), r = Math.max(1, i.width), o = Math.max(1, i.height);
    x.setSize(r, o), x.context[t(118)](), x.context.translate(-i.x, -i.y), e.forEach(function(a) {
      x.exportPaintObjects([a]);
    }), x.context.restore();
  }
  saveDataAsFile(e, t) {
    const x = ve;
    let s = document.createElementNS("http://www.w3.org/1999/xhtml", "a");
    s.href = e, s[x(132)] = t;
    let i = document.createEvent("MouseEvents");
    i.initMouseEvent("click", !![], ![], window, 0, 0, 0, 0, 0, ![], ![], ![], ![], 0, null), s.dispatchEvent(i);
  }
}
(function(n, e) {
  for (var t = Gn, x = n(); []; )
    try {
      var s = parseInt(t(466)) / 1 * (-parseInt(t(472)) / 2) + parseInt(t(465)) / 3 * (parseInt(t(464)) / 4) + parseInt(t(468)) / 5 + -parseInt(t(473)) / 6 * (parseInt(t(470)) / 7) + -parseInt(t(474)) / 8 * (parseInt(t(471)) / 9) + -parseInt(t(467)) / 10 + parseInt(t(463)) / 11;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Vn, 842036);
function Vn() {
  var n = ["36901557VtZObZ", "1296jxzIBC", "13194JBqKNa", "203udgIVP", "7861870SrGCMh", "3547725untEUn", "writable", "8649844uBCdXw", "9fsdTJm", "13612lPOUpF", "6LkghqI", "9949208rrVrxA"];
  return Vn = function() {
    return n;
  }, Vn();
}
function Gn(n, e) {
  var t = Vn();
  return Gn = function(x, s) {
    x = x - 463;
    var i = t[x];
    return i;
  }, Gn(n, e);
}
function Ya(n, e) {
  for (var t in e) {
    let x = e[t];
    Ja(n, t, x);
  }
}
function Ja(n, e, t) {
  var x = Gn;
  t.writable == null && (t[x(469)] = !![]), t.enumerable == null && (t.enumerable = !![]), Object.defineProperty(n, e, t);
}
const vt = qn;
(function(n, e) {
  const t = qn, x = n();
  for (; []; )
    try {
      if (parseInt(t(379)) / 1 * (-parseInt(t(378)) / 2) + -parseInt(t(363)) / 3 * (-parseInt(t(385)) / 4) + -parseInt(t(392)) / 5 + -parseInt(t(389)) / 6 * (-parseInt(t(380)) / 7) + -parseInt(t(370)) / 8 * (parseInt(t(373)) / 9) + parseInt(t(390)) / 10 * (parseInt(t(368)) / 11) + parseInt(t(371)) / 12 * (-parseInt(t(387)) / 13) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Un, 452998);
function qn(n, e) {
  const t = Un();
  return qn = function(x, s) {
    return x = x - 362, t[x];
  }, qn(n, e);
}
var Va = Object[vt(372)], Ga = Object[vt(365)], Ni = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? Ga(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && Va(e, t, s), s;
};
class ge extends M {
  constructor(e, t = 0, x = 0, s = 1, i = 1, r = !![]) {
    super(e, t, x, s, i), this.autoDirection = !![], this._autoSize = !![], this.autoSize = r;
  }
  get autoSize() {
    return this[vt(382)];
  }
  set autoSize(e) {
    this._autoSize = e;
  }
  updateMatrix() {
    const e = vt, t = this;
    if (t.autoDirection == !![]) {
      let x = t._getOriginRotation();
      x < -Math.PI / 2 || x > Math.PI / 2 ? t.rotation !== Math.PI && (t.rotation = Math.PI) : t[e(376)] !== 0 && (t[e(376)] = 0);
    }
    super.updateMatrix();
  }
  [vt(384)]() {
    this._calcTextSize(), this._autoSize && this.resizeToFitText(), this._calcTextPosition();
  }
  resizeToFitText() {
    const e = vt;
    let x = this[e(362)][e(367)]();
    this.width = this._textWidth + x, this.height = this._textHeight + x;
    let s = this._OBBPoints(), i = this._worldTransform.points(s), r = this._obb;
    r.localPoints = s, r[e(381)] = i, r.aabb = Zt.toAABB(i);
  }
  _calcTextPosition() {
    const e = vt, t = this[e(362)];
    let x = t[e(386)] || 0, s = (t[e(364)] || 0) + (t.lineWidth | 0);
    return (t.textBaseline == "bottom" && (t.textPosition == "lt" || t.textPosition == "ct" || t.textPosition == "rt") || t.textBaseline == "top" && (t.textPosition == "lb" || t[e(383)] == "cb" || t[e(383)] == "rb") || t.textAlign == e(377) && (t.textPosition == "lt" || t[e(383)] == "lm" || t.textPosition == "lb") || t[e(366)] == "left" && (t[e(383)] == "rt" || t.textPosition == "rm" || t.textPosition == "rb")) && (x = 0, s = 0), super[e(388)](x, s);
  }
}
function Un() {
  const n = ["points", "_autoSize", "textPosition", "_updateText", "100UVOGQk", "borderWidth", "39SlVBPD", "_calcTextPosition", "6LPaoAa", "3847300Ihtgit", "prototype", "1512475nmDLLP", "_computedStyle", "24321zinHmF", "padding", "getOwnPropertyDescriptor", "textAlign", "calcGap", "22AzsEkJ", "serializers", "11040oPvtNT", "1586448iqacQF", "defineProperty", "2223KmdqKQ", "autoDirection", "concat", "rotation", "right", "1304xEeFTy", "76jWqRjZ", "3992674tzxjdV"];
  return Un = function() {
    return n;
  }, Un();
}
Ni([f("TextNode")], ge[vt(391)], "className", 2), Ni([f("rect")], ge.prototype, "pickType", 2), Ya(ge[vt(391)], { serializers: { value: M.prototype[vt(369)][vt(375)](["autoSize", vt(374)]) } });
var Qs = ze;
(function(n, e) {
  for (var t = ze, x = n(); []; )
    try {
      var s = -parseInt(t(496)) / 1 + -parseInt(t(501)) / 2 + parseInt(t(493)) / 3 + parseInt(t(495)) / 4 * (parseInt(t(489)) / 5) + -parseInt(t(503)) / 6 * (parseInt(t(498)) / 7) + -parseInt(t(490)) / 8 * (parseInt(t(502)) / 9) + parseInt(t(504)) / 10;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Zn, 125165);
var qa = Object.defineProperty, Ua = Object.getOwnPropertyDescriptor, Za = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? Ua(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && qa(e, t, s), s;
};
function ze(n, e) {
  var t = Zn();
  return ze = function(x, s) {
    x = x - 489;
    var i = t[x];
    return i;
  }, ze(n, e);
}
class xx extends M {
  constructor(e, t = 0, x = 0, s = 1, i = 1) {
    var r = ze;
    super(e, t, x, s, i), this.shape = T[r(491)], this[r(492)](s / 2);
  }
  setRadius(e = 1) {
    var t = ze;
    this.width = e * 2, this[t(500)] = e * 2;
  }
  getRadius() {
    return this.radius;
  }
  getPoint(e) {
    var t = ze;
    let x = Math[t(499)](this.width, this[t(500)]) * 0.5, s = e * (2 * Math.PI);
    return { x: this.x + x + x * Math.cos(s), y: this.y + x + x * Math[t(494)](s) };
  }
}
function Zn() {
  var n = ["6374010FgJYno", "982585APOYIH", "136lWFPCw", "Circle", "setRadius", "26052ITevxj", "sin", "4XDDrIc", "54094xqZfRz", "defineProperties", "21lVgQng", "max", "height", "429854XbUgpg", "111483YZkrQM", "475674moLkwG"];
  return Zn = function() {
    return n;
  }, Zn();
}
Za([f("CircleNode")], xx.prototype, "className", 2), Object[Qs(497)](xx.prototype, { radius: { get() {
  var n = Qs;
  return Math.max(this.width, this[n(500)]) * 0.5;
}, set(n) {
  var e = Qs;
  this[e(492)](n);
} } });
const Y = Je;
(function(n, e) {
  const t = Je, x = n();
  for (; []; )
    try {
      if (-parseInt(t(476)) / 1 * (-parseInt(t(503)) / 2) + -parseInt(t(481)) / 3 + parseInt(t(500)) / 4 * (parseInt(t(501)) / 5) + parseInt(t(485)) / 6 + -parseInt(t(493)) / 7 + parseInt(t(492)) / 8 + -parseInt(t(505)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Kn, 468574);
function Kn() {
  const n = ["matrixDirty", "looksSame", "centerOffset", "_calcFold2Vec", "35ZIrFYy", "resetOffset", "_calcFold1", "_calcFold2", "prototype", "1987647DVuxXH", "abs", "serializers", "_calcAZ", "3602820qwGNpk", "_afterUpdateMatrix", "end", "middle", "dot", "className", "getAngle", "1918216xThOzq", "5373438bWvYzc", "length", "_calcFold1Vec", "getK", "points", "AutoFoldLink", "absorb", "7240BdGQSK", "1265RiGsBV", "begin", "44152jSLwYs", "_needCalcOffset", "1548720GYrvJz", "setEndOffsetGap", "sign"];
  return Kn = function() {
    return n;
  }, Kn();
}
var Ka = Object.defineProperty, Qa = Object.getOwnPropertyDescriptor, A0 = (n, e, t, x) => {
  const s = Je;
  for (var i = x > 1 ? void 0 : x ? Qa(e, t) : e, r = n[s(494)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && Ka(e, t, i), i;
};
function Je(n, e) {
  const t = Kn();
  return Je = function(x, s) {
    return x = x - 470, t[x];
  }, Je(n, e);
}
class ie extends R {
  constructor(e, t, x, s, i) {
    const r = Je;
    super(e, t, x, s, i), this[r(499)] = 3;
  }
  setBeginOffsetGap(e) {
    const t = Je;
    this.beginOffsetGap = e, this[t(472)] = !![];
  }
  [Y(470)](e) {
    const t = Y;
    this.endOffsetGap = e, this[t(472)] = !![];
  }
  [Y(478)](e, t) {
    let x = this.beginOffsetGap, s = this._calcFold1Vec(e, t), i = { x: e.x + s[0] * x, y: e.y + s[1] * x }, r = this.fold1Offset;
    return r && (i.x += r.x, i.y += r.y), i;
  }
  [Y(479)](e, t) {
    const x = Y;
    let s = this.endOffsetGap, i = this[x(475)](e, t), r = { x: t.x + i[0] * s, y: t.y + i[1] * s }, o = this.fold2Offset;
    return o && (r.x += o.x, r.y += o.y), r;
  }
  _calcFold1Vec(e, t) {
    let x = ui(this.begin);
    if (x == null) {
      let s = 0, i = 0;
      t.x > e.x ? s = 1 : s = -1, x = [s, i];
    }
    return x;
  }
  _calcFold2Vec(e, t) {
    let x = ui(this.end);
    if (x == null) {
      let s = 0, i = -1;
      t.y > e.y ? i = -1 : i = 1, x = [s, i];
    }
    return x;
  }
  updatePoints() {
    const e = Y, t = this[e(484)](), x = t[0], s = t[1];
    return this._calcPointsByAZ(x, s);
  }
  _calcPointsByAZ(e, t) {
    const x = Y, s = this.absorb;
    let i = this[x(495)](e, t);
    if (i[0] == 0 && Math[x(482)](e.x - t.x) < s) {
      let u = (e.x + t.x) * 0.5;
      e.x = u, t.x = u;
    }
    if (i[1] == 0 && Math.abs(e.y - t.y) < s) {
      let u = (e.y + t.y) * 0.5;
      e.y = u, t.y = u;
    }
    let r = this._calcFold1(e, t), o = this._calcFold2(e, t);
    const a = $a(this, e, t, r, o);
    let c = a[0], l = a[1];
    {
      if (E[x(473)](c, l, 0.5) == ![]) {
        const b = Math.abs(E[x(491)](c, l)).toFixed(6);
        this._preAngle != null && this._preAngle != b && (this[x(474)] = null), this._preAngle = b;
      }
      let p = this.centerOffset;
      p && (c.x += p.x, c.y += p.y, l.x += p.x, l.y += p.y);
    }
    return [e, r, c, l, o, t];
  }
  [Y(486)]() {
    const e = Y;
    if (super[e(486)](), this[e(504)]()) {
      let t = this[e(497)][0], x = this[e(497)][this[e(497)][e(494)] - 1], s = this.offsetPoints[0], i = this.offsetPoints[this.offsetPoints[e(494)] - 1];
      this[e(497)] = this._calcPointsByAZ(s, i), this.points[0] = t, this[e(497)][this.points.length - 1] = x;
    }
  }
  getMergedPoints() {
    return E.mergeClosestPoints(this.points);
  }
  setFold1Offset(e, t) {
    const x = Y;
    let s = this.fold1Offset;
    s == null && (s = { x: 0, y: 0 }, this.fold1Offset = s), kx(this[x(496)](0, 0.5)) ? (s.y = 0, t = 0) : (s.x = 0, e = 0), s.x += e, s.y += t, this.matrixDirty = !![];
  }
  setFold2Offset(e, t) {
    let x = this.fold2Offset;
    x == null && (x = { x: 0, y: 0 }, this.fold2Offset = x), kx(this.getK(4, 0.5)) ? (x.y = 0, t = 0) : (x.x = 0, e = 0), x.x += e, x.y += t, this.matrixDirty = !![];
  }
  setCenterOffset(e, t) {
    let x = this.centerOffset;
    x == null && (x = { x: 0, y: 0 }, this.centerOffset = x), kx(this.getK(2, 0.5)) ? (x.x = 0, e = 0) : (x.y = 0, t = 0), x.x += e, x.y += t, this.matrixDirty = !![];
  }
  [Y(477)]() {
    this.centerOffset = void 0, this.fold1Offset = null, this.fold2Offset = null, this.matrixDirty = !![];
  }
}
A0([f(Y(498))], ie.prototype, Y(490), 2), A0([f(15)], ie.prototype, "beginOffsetGap", 2), A0([f(15)], ie.prototype, "endOffsetGap", 2), A0([f(function() {
  const n = Y;
  let e = this.getMergedPoints(), t = e.length;
  return t < 4 ? [n(502), "end"] : t == 4 || t == 5 ? ["begin", "end", "fold1", "fold2"] : [n(502), n(487), "fold1", "fold2", "center"];
})], ie.prototype, "getCtrlPoints", 2), A0([f(R[Y(480)][Y(483)].concat(["beginOffsetGap", "endOffsetGap", "fold1Offset", "fold2Offset", "centerOffset"]))], ie[Y(480)], "serializers", 2);
const te = {};
te[k.begin] = function() {
  return this.points[0];
}, te[k.fold1] = function() {
  return this.points[1];
}, te[k.mid1] = function() {
  return this[Y(497)][2];
}, te[k.mid2] = function() {
  return this.points[3];
}, te[k.fold2] = function() {
  return this.points[4];
}, te[k.end] = function() {
  return this.points[5];
}, te[k.center] = function() {
  return E[Y(488)](this.points[2], this.points[3]);
}, ie.prototype.DefaultPositions = te;
function Qn(n, e) {
  const t = Y;
  return Math.abs(e[0]) > Math.abs(e[1]) ? n.x * Math[t(471)](e[0]) : n.y * Math.sign(e[1]);
}
function Ri(n, e, t) {
  let x = Qn(n, e);
  return Qn(t, e) - x;
}
function $s(n, e, t, x) {
  let s = Qn(n, t), i = Qn(e, x), r = t[0] != 0;
  return s > i ? r ? { x: n.x, y: e.y } : { x: e.x, y: n.y } : r ? { x: e.x, y: n.y } : { x: n.x, y: e.y };
}
function $a(n, e, t, x, s) {
  const i = Y, r = n._calcFold1Vec(e, t), o = n._calcFold2Vec(e, t), a = B[i(489)](r, o);
  if (a == 1) {
    let u = $s(x, s, r, o);
    return [u, u];
  }
  if (a == -1) {
    const u = E[i(488)](x, s);
    let p = $s(x, u, r, r), b = $s(s, u, o, o);
    return [p, b];
  }
  let c = fn(e, x, t, s, !![]), l = Ri(e, r, c), h = Ri(t, o, c);
  if (l > 0 && h > 0)
    return [c, c];
  {
    const u = [-r[1], r[0]], p = { x: x.x + u[0], y: x.y + u[1] }, b = [-o[1], o[0]], _ = { x: s.x + b[0], y: s.y + b[1] };
    let m = fn(x, p, s, _, !![]);
    if (m != null)
      return [m, m];
  }
  throw new Error("assert failed getMid1AndMid2");
}
const Gt = d0;
(function(n, e) {
  const t = d0, x = n();
  for (; []; )
    try {
      if (parseInt(t(322)) / 1 + parseInt(t(321)) / 2 * (-parseInt(t(337)) / 3) + -parseInt(t(332)) / 4 * (parseInt(t(328)) / 5) + -parseInt(t(330)) / 6 * (-parseInt(t(331)) / 7) + parseInt(t(339)) / 8 * (-parseInt(t(336)) / 9) + parseInt(t(327)) / 10 + -parseInt(t(334)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})($n, 320254);
var tc = Object.defineProperty, ec = Object.getOwnPropertyDescriptor, gx = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? ec(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && tc(e, t, s), s;
};
function $n() {
  const n = ["14646cFBtqv", "1603jiOHJd", "164sHvxyo", "FlexionalLink", "4827460rgRLZp", "begin", "58977MvpDGV", "261asGCix", "direction", "128dZCLiq", "fold1", "prototype", "2846XSgdOx", "330223LFjDAr", "getPoints", "horizontal", "offsetGap", "center", "6236790eoJJcE", "64040nNMTCB", "getFold2"];
  return $n = function() {
    return n;
  }, $n();
}
class Le extends R {
  constructor(e, t, x, s, i) {
    super(e, t, x, s, i);
  }
  getFold1(e, t) {
    const x = d0;
    let s = (t.y - e.y) / 2, i = (t.x - e.x) / 2;
    return this.direction == o0[x(324)] ? { x: e.x + i, y: e.y } : { x: e.x, y: e.y + s };
  }
  getFold2(e, t) {
    let x = (t.y - e.y) / 2, s = (t.x - e.x) / 2;
    return this.direction == o0.horizontal ? { x: t.x - s, y: t.y } : { x: t.x, y: t.y - x };
  }
  updatePoints() {
    const e = d0, t = this._calcAZ(), x = t[0], s = t[1];
    let i = this.getFold1(x, s), r = this[e(329)](x, s), o = { x: (i.x + r.x) / 2, y: (i.y + r.y) / 2 };
    const a = [x, i, o, r, s];
    if (this.points = a, this.endArrow) {
      let c = a.length - 2;
      this.endArrow.origin[0] = c;
    }
    return a;
  }
}
function d0(n, e) {
  const t = $n();
  return d0 = function(x, s) {
    return x = x - 319, t[x];
  }, d0(n, e);
}
gx([f(Gt(333))], Le.prototype, "className", 2), gx([f(R.prototype.serializers.concat(["direction", "offsetGap"]))], Le.prototype, "serializers", 2), gx([f(o0[Gt(324)])], Le.prototype, Gt(338), 2), gx([f(44)], Le.prototype, Gt(325), 2);
const Ke = {};
Ke[k[Gt(335)]] = function() {
  return this.getPoints()[0];
}, Ke[k[Gt(319)]] = function() {
  return this.getPoints()[1];
}, Ke[k[Gt(326)]] = function() {
  return this.getPoints()[2];
}, Ke[k.fold2] = function() {
  return this.getPoints()[3];
}, Ke[k.end] = function() {
  return this[Gt(323)]()[4];
}, Le[Gt(320)].DefaultPositions = Ke;
var Kt = ts;
(function(n, e) {
  for (var t = ts, x = n(); []; )
    try {
      var s = -parseInt(t(458)) / 1 + -parseInt(t(463)) / 2 * (-parseInt(t(462)) / 3) + parseInt(t(466)) / 4 * (-parseInt(t(471)) / 5) + -parseInt(t(457)) / 6 * (-parseInt(t(460)) / 7) + parseInt(t(470)) / 8 * (-parseInt(t(468)) / 9) + -parseInt(t(453)) / 10 + parseInt(t(454)) / 11 * (parseInt(t(467)) / 12);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(es, 766957);
function ts(n, e) {
  var t = es();
  return ts = function(x, s) {
    x = x - 452;
    var i = t[x];
    return i;
  }, ts(n, e);
}
var xc = Object[Kt(461)], nc = Object[Kt(456)], Wi = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? nc(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && xc(e, t, s), s;
};
function es() {
  var n = ["serializers", "getOwnPropertyDescriptor", "6GVuOSU", "983772sJzJky", "DefaultPositions", "3513055UKcyez", "defineProperty", "1296111bDLZUZ", "4OKMSlC", "prototype", "getPoints", "140HEmEGr", "36qioAiP", "1202337MHPoyp", "end", "32VtEbxt", "208810plxrFB", "updatePoints", "4539550fkwaiE", "10394219pPWmAn"];
  return es = function() {
    return n;
  }, es();
}
class nx extends R {
  constructor(e, t, x, s, i) {
    super(e, t, x, s, i), this.direction = "horizontal";
  }
  [Kt(452)]() {
    const e = this._calcAZ(), t = e[0], x = e[1];
    let s;
    return t.x == x.x || t.y == x.y ? s = E.middle(t, x) : this.direction == o0.horizontal ? s = { x: x.x, y: t.y } : s = { x: t.x, y: x.y }, [t, s, x];
  }
}
Wi([f("FoldLink")], nx.prototype, "className", 2), Wi([f(R[Kt(464)][Kt(455)].concat(["direction"]))], nx[Kt(464)], "serializers", 2);
const bx = {};
bx[k.begin] = function() {
  return this.getPoints()[0];
}, bx[k[Kt(469)]] = function() {
  var n = Kt;
  return this[n(465)]()[2];
}, bx[k.center] = function() {
  return this.getPoints()[1];
}, nx.prototype[Kt(459)] = bx;
const it = f0;
function f0(n, e) {
  const t = xs();
  return f0 = function(x, s) {
    return x = x - 116, t[x];
  }, f0(n, e);
}
function xs() {
  const n = ["936zsRlXx", "children", "draggable", "inLinks", "2mvJWgM", "asLabel", "fromPoints", "polygon", "disconnect", "1528IFGouu", "32781MVoGJg", "2811504sKEjeg", "33bhIfok", "indexOf", "3846680yuAThE", "endArrow", "getRect", "4182324JseVlx", "height", "label", "createLabel", "setXYButChildFixed", "3364794ApmELl", "setXYOnLink", "length", "addChild", "parent", "988403ojghXm", "getEndPoint", "getMiddleOrigin", "editable", "removeChild", "travel", "3687LuFhVi", "createArrow", "outLinks", "5aSnHus", "getUnionRect"];
  return xs = function() {
    return n;
  }, xs();
}
(function(n, e) {
  const t = f0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(128)) / 1 * (-parseInt(t(143)) / 2) + parseInt(t(134)) / 3 * (-parseInt(t(148)) / 4) + -parseInt(t(137)) / 5 * (parseInt(t(118)) / 6) + parseInt(t(149)) / 7 * (-parseInt(t(139)) / 8) + parseInt(t(123)) / 9 + parseInt(t(153)) / 10 + -parseInt(t(151)) / 11 * (-parseInt(t(150)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(xs, 735370);
class qt {
  static createPologyNode(e, t = 1, x = 1) {
    const s = f0;
    let i = new M();
    return i.setShape(T[s(146)](e)), i.resizeTo(t, x), i;
  }
  static getUnionRect(e) {
    const t = f0;
    let x = e[0][t(117)]();
    for (let s = 1; s < e.length; s++)
      x = z.union(x, e[s][t(117)]());
    return x;
  }
  static [it(122)](e, t, x) {
    const s = it;
    let i = e.children;
    e.x += t, e.y += x;
    for (let r = 0; r < i[s(125)]; r++) {
      const o = i[r];
      o instanceof M && (o.x -= t, o.y -= x);
    }
  }
  static sizeFitToChildren(e, t) {
    const x = it;
    let s = e.getChildren();
    if (s.length == 0)
      return;
    let i = qt[x(138)](s);
    t == null && (t = 0);
    let r = t * 2;
    e.resizeTo(i.width + r, i[x(119)] + r);
    let o = i.x + e.width / 2, a = i.y + e.height / 2;
    qt[x(122)](e, o - t, a - t);
  }
  static translateObjectsCenterTo(e, t, x) {
    const s = it;
    let i = { x: t, y: x }, r = [];
    r = r.concat(e);
    let o = qt[s(138)](r), a = o.getCenter(), c = i.x - a.x, l = i.y - a.y;
    r.forEach((h) => {
      h.translateWith(c, l);
    });
  }
  static travel(e, t, x, s) {
    const i = it;
    if (s == null)
      s = [];
    else if (N.hasChild(s, e))
      return null;
    if (t && t(e, x), s.push(e), e instanceof M) {
      let o = e[i(136)];
      if (o != null)
        for (var r = 0; r < o.length; r++) {
          let a = o[r];
          qt.travel(a, t, e, s);
        }
    } else
      e instanceof R && e.end.isDisplayObjectTarget() && qt[i(133)](e.end.target, t, x, s);
    return s;
  }
}
class sx {
  static [it(147)](e, t) {
    const x = it;
    if (e instanceof R) {
      e.unlink();
      return;
    }
    let s = e[x(142)];
    s != null && (s.forEach((r) => {
      const o = x;
      r[o(127)] != null && (t != null && t[o(152)](r) != -1 || r.setEnd(r[o(129)]()));
    }), e[x(142)] = []);
    let i = e.outLinks;
    i != null && (i.forEach((r) => {
      const o = x;
      r.parent != null && (t != null && t[o(152)](r) != -1 || r.setBegin(r.getBeginPoint()));
    }), e[x(136)] = []);
  }
  static [it(121)](e, t) {
    const x = it;
    if (e[x(120)] == null) {
      const s = new ge(t);
      return s[x(131)] = ![], s[x(141)] = ![], s.connectable = ![], s.mouseEnabled = ![], s.autoSize = !![], s.alignOriginToLink("cb", 0, 0.5), sx[x(144)](e, s), s;
    }
    return e.label.text = t, e.label;
  }
  static asLabel(e, t) {
    const x = it;
    e.label != null && e.removeChild(e.label), e.label = t, e.children.indexOf(e[x(120)]) == -1 && e[x(126)](e.label);
    let s = sx[x(130)](e);
    return t[x(124)](s.segIndex, s.t), t;
  }
  static [it(130)](e) {
    let t = 0, x = 0.5;
    return e instanceof nx ? x = 1 : e instanceof Le ? t = 1 : e instanceof ie && (t = 2), { segIndex: t, t: x };
  }
  static [it(135)](e, t = 10, x = 10) {
    const s = it;
    let i = new M();
    return i.resizeTo(t, x), e == null ? i.setShape(T.Arrow) : i.setShape(T[s(145)](e)), i.editable = ![], i.draggable = ![], i.connectable = ![], i.mouseEnabled = ![], i;
  }
  static asBeginArrow(e, t) {
    return e.beginArrow != null && e.removeChild(e.beginArrow), e.children.indexOf(t) == -1 && e.addChild(t), e.beginArrow = t, t;
  }
  static asEndArrow(e, t) {
    const x = it;
    return e[x(116)] != null && e[x(132)](e.endArrow), e[x(140)].indexOf(t) == -1 && e.addChild(t), e.endArrow = t, t;
  }
}
const mt = ss;
function ns() {
  const n = ["css", "lineDash", "22ZZOAwC", "direction", "color", "lineWidth", "anime", "mouseEnabled", "animationList", "play", "428150IuWmrA", "height", "white", "count", "red", "760696kcMSXH", "text", "width", "199591UPgHrk", "3511008QKAorp", "times", "435EKDOJB", "segIndex", "beginWidth", "142682IHuvvP", "5VTvpra", "exportSystem", "setRadius", "center", "strokeStyle", "colorFilter", "2692iCDvMr", "minHeight", "clockwise", "animationSystem", "linkMark", "757728ZBspGt", "9wYvZsT", "setColors", "children"];
  return ns = function() {
    return n;
  }, ns();
}
(function(n, e) {
  const t = ss, x = n();
  for (; []; )
    try {
      if (parseInt(t(194)) / 1 + -parseInt(t(201)) / 2 * (-parseInt(t(191)) / 3) + parseInt(t(225)) / 4 + parseInt(t(195)) / 5 * (parseInt(t(206)) / 6) + -parseInt(t(188)) / 7 + parseInt(t(189)) / 8 * (-parseInt(t(207)) / 9) + parseInt(t(220)) / 10 * (parseInt(t(212)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ns, 272555);
function ss(n, e) {
  const t = ns();
  return ss = function(x, s) {
    return x = x - 187, t[x];
  }, ss(n, e);
}
class di {
  constructor(e, t) {
    this.objects = e, this.animationList = t;
  }
  remove() {
    this.objects.forEach((e) => e.remove()), this.animationList.forEach((e, t) => {
      e.cancel();
    });
  }
  [mt(219)]() {
    this[mt(218)].forEach((t, x) => {
      t.play();
    });
  }
}
class Wr {
  constructor(e, t) {
    this.stage = e, this.animationSystem = t;
  }
  xyToCenter(e, t = { x: 0, y: 0 }) {
    return this.lookAt(e, t);
  }
  lookAt(e, t = { x: 0, y: 0 }) {
    let x = t.x || 0, s = t.y || 0;
    return this.animationSystem.anime({ from: [e.x, e.y], to: [-x, -s], update: (r) => {
      e.x = r[0], e.y = r[1];
    }, effect: "easeInOutElastic" });
  }
  flash(e, t = {}) {
    let s = t[mt(190)] || 3, i = t.duration || 100;
    return this.animationSystem.anime({ from: [0], to: [1], update: (a) => {
      e.css({ globalAlpha: a[0] });
    }, times: s, effect: "easeOutBounce", duration: i });
  }
  expandScale(e, t = {}) {
    const x = mt;
    let s = t.position || "center", i = t[x(193)] || 0, r = t.beginHeight || 0, o = this.animationSystem, a = [i, r, e.x, e.y], c = [e[x(187)], e[x(221)], e.x, e.y];
    return s == x(198) ? a = [i, r, e.x, e.y] : s == "lt" ? a = [i, r, e.x - e.width * 0.5, e.y - e[x(221)] * 0.5] : s == "rt" ? a = [i, r, e.x + e.width * 0.5, e.y - e.height * 0.5] : s == "lb" ? a = [i, r, e.x - e.width * 0.5, e.y + e.height * 0.5] : s == "rb" ? a = [i, r, e.x + e.width * 0.5, e.y + e[x(221)] * 0.5] : s == "ct" ? a = [e.width, r, e.x, e.y - e.height * 0.5] : s == "cb" ? a = [e.height, r, e.x, e.y + e.height * 0.5] : s == "lm" ? a = [i, e.height, e.x - e.width * 0.5, e.y] : s == "rm" && (a = [i, e.height, e.x + e.width * 0.5, e.y]), o[x(216)]({ from: a, to: c, update: (h) => {
      const u = x;
      e.scaleTo(h[0] / e[u(187)], h[1] / e[u(221)]), e.translateTo(h[2], h[3]);
    }, effect: "easeOutCubic" });
  }
  unexpandScale(e, t = {}) {
    const x = mt;
    let s = t.position || "center", i = t.minWidth || 0, r = t[x(202)] || 0, o = this.animationSystem, a = [e[x(187)], e.height, e.x, e.y], c = [i, r, e.x, e.y];
    return s == "center" ? c = [i, r, e.x, e.y] : s == "lt" ? c = [i, r, e.x - e[x(187)] * 0.5, e.y - e[x(221)] * 0.5] : s == "rt" ? c = [i, r, e.x + e[x(187)] * 0.5, e.y - e.height * 0.5] : s == "lb" ? c = [i, r, e.x - e.width * 0.5, e.y + e.height * 0.5] : s == "rb" ? c = [i, r, e.x + e[x(187)] * 0.5, e.y + e.height * 0.5] : s == "ct" ? c = [i, r, e.x, e.y - e[x(221)] * 0.5] : s == "cb" ? c = [i, r, e.x, e.y + e.height * 0.5] : s == "lm" ? c = [i, r, e.x - e[x(187)] * 0.5, e.y] : s == "rm" && (c = [i, r, e.x + e.width * 0.5, e.y]), o.anime({ from: a, to: c, update: (h) => {
      const u = x;
      e.scaleTo(h[0] / e[u(187)], h[1] / e[u(221)]), e.translateTo(h[2], h[3]);
    }, effect: "easeOutCubic" });
  }
  flow(e, t = {}) {
    const x = mt;
    let s = t.lineDash || e.style[x(211)] || [3, 2], i = t[x(213)] || "clockwise", r = t.duration || 1e4, o = t.distance || 300, a = i == x(203) ? 1 : -1, c = this[x(204)];
    return e.css({ lineDash: s }), c[x(216)]({ from: 0, to: o, update: (h) => {
      e.css({ lineDashOffset: -h * a });
    }, times: 1 / 0, duration: r });
  }
  rippling(e = {}) {
    const t = mt;
    let x = e.radius || 50, s = e.color || "rgba(128,128,128,0.8)", i = e[t(223)] || 3, r = e[t(215)] || 8, o = this[t(204)], a = [], c = new xx(null, 0, 0);
    c[t(217)] = ![], c.setRadius(x);
    for (let b = 0; b < i; b++) {
      let _ = new xx(null, 0, 0);
      _.mouseEnabled = ![], _.css(t(199), s), _[t(197)](1), c.addChild(_);
    }
    let l = c[t(209)], h = x / i, u = o[t(216)]({ from: [1], to: [x], update: (b) => {
      let _ = b[0];
      for (let m = 0; m < l.length; m++) {
        let g = l[m], w = _ + m * h;
        w > x && (w = w % x);
        let O = w / x;
        g.setRadius(w), g.css({ lineWidth: O * r, globalAlpha: 0.1 });
      }
    }, times: 1 / 0, duration: 2e3 });
    return a.push(u), new di([c], a);
  }
  [mt(200)](e, t) {
    const x = mt;
    let s = this.stage[x(196)].toImage(e);
    s.onload = () => {
      s = Ht[x(200)](s, t), e.setImage(s, !![]);
    };
  }
  imageFilter(e, t) {
    e.getImage((x) => {
      if (x == null)
        return;
      let s = Ht.colorFilter(x, t);
      e.setImage(s);
    });
  }
  [mt(205)](e, t = { color: "red" }) {
    const x = mt;
    let s = t[x(226)] || "❌️", i = new ge(s, 0, 0);
    i.draggable = ![], i.editable = ![], i.connectable = ![], i[x(210)]({ textPosition: x(198), textBaseline: "middle", textAlign: "center", color: t[x(214)] || x(224) }), t.font != null && i.css({ font: t.font });
    let r = sx.getMiddleOrigin(e);
    return i.setXYOnLink(r[x(192)], r.t), e.addChild(i), i;
  }
  waterLike(e, t = 60, x = 60) {
    const s = mt;
    let i = new l0(0, 0, 0, x), r = new M(null, 0, 0, t, x);
    e == null && (e = [s(222), "green", "rgb(0,87,55)"]), i.setColors([[0, e[0]], [0.5, e[1]], [1, e[2]]]);
    let o = this.animationSystem.anime({ from: 0, to: 1, duration: 2500, update: (c) => {
      const l = s;
      c = c < 0 ? 0 : c, i.startX = Math.random() + Math.cos(c * 2 * Math.PI) * 10, i.startY = -10 + Math.cos(c * 2 * Math.PI) * 5, i[l(208)]([[0, e[0]], [0.2 * c, e[1]], [1, e[2]]]);
    } });
    return o[s(213)] = "alternate", o.times = 1 / 0, r.css({ fillStyle: i }), new di([r], [o]);
  }
}
const e0 = p0;
(function(n, e) {
  const t = p0, x = n();
  for (; []; )
    try {
      if (parseInt(t(440)) / 1 * (-parseInt(t(439)) / 2) + -parseInt(t(441)) / 3 * (-parseInt(t(438)) / 4) + -parseInt(t(431)) / 5 + parseInt(t(436)) / 6 + parseInt(t(428)) / 7 * (-parseInt(t(433)) / 8) + -parseInt(t(442)) / 9 + parseInt(t(427)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(is, 750466);
function is() {
  const n = ["11703064NeYNSu", "stoped", "map", "2115228ybGhVE", "regBehaviour", "16036rBqcvY", "894902FpijTO", "3zNmlHb", "930XcvHjJ", "2772657raHHvb", "prototype", "first", "36788880dMTNbS", "7coavta", "isNaN", "update", "7052205rYFMtr", "length"];
  return is = function() {
    return n;
  }, is();
}
function p0(n, e) {
  const t = is();
  return p0 = function(x, s) {
    return x = x - 427, t[x];
  }, p0(n, e);
}
class sc {
  constructor(e) {
    const t = p0;
    this[t(435)] = /* @__PURE__ */ new Map(), this.argMap = /* @__PURE__ */ new WeakMap(), this.behaviourMap = /* @__PURE__ */ new Map(), this[t(434)] = ![], this.stage = e;
  }
  tick(e) {
    const t = p0;
    if (this.stoped)
      return;
    let x = this.map.keys();
    for (let s of x)
      this.executeBehaviours(s, t(430));
  }
  defBehaviour(e, t) {
    let x = new rs(e);
    return Object.assign(x, t), this.regBehaviour(x), x;
  }
  [e0(437)](e) {
    this.behaviourMap.set(e.name, e);
  }
  addBehaviour(e, t, x) {
    const s = e0;
    let i = this.behaviourMap.get(t);
    if (i == null)
      throw new Error("behaviours not exist:" + t);
    let r = this.map.get(e);
    r == null ? (r = [], this.map.set(e, r), r.push(i)) : r.indexOf(i) == -1 && r.push(i), x != null && (r[t] = x), i[s(444)](e, x);
  }
  removeBehaviour(e, t) {
    const x = e0;
    let s = this[x(435)].get(e);
    if (s != null)
      for (let i = 0; i < s[x(432)]; i++) {
        let r = s[i];
        if (r.name == t) {
          N.remove(s, r);
          return;
        }
      }
  }
  executeBehaviours(e, t) {
    const x = e0;
    let s = this.map.get(e);
    if (s != null)
      for (let i = 0; i < s.length; i++) {
        let r = s[i];
        if (t == "update") {
          let o = s[r.name];
          if (r.update !== rs[x(443)].update && r.update(e, o) == ![])
            return ![];
        }
      }
    return null;
  }
}
class rs {
  constructor(e) {
    if (typeof e == "number" || !Number[e0(429)](parseInt(e)))
      throw new Error("behaviour name cannot be number:" + e);
    this.name = e;
  }
  first(e, t) {
  }
  [e0(430)](e, t) {
  }
  static fromJSON(e) {
    let t = new rs(e.name);
    return t.first = new Function("return " + t.first)(), t.update = new Function("return " + t.update)(), t;
  }
  toJSON() {
    return { name: this.name, first: this.first.toString(), update: this.update.toString() };
  }
}
const nt = g0;
(function(n, e) {
  const t = g0, x = n();
  for (; []; )
    try {
      if (parseInt(t(273)) / 1 + -parseInt(t(280)) / 2 + -parseInt(t(290)) / 3 + -parseInt(t(291)) / 4 * (-parseInt(t(289)) / 5) + parseInt(t(272)) / 6 * (-parseInt(t(277)) / 7) + parseInt(t(271)) / 8 * (parseInt(t(279)) / 9) + parseInt(t(274)) / 10 * (parseInt(t(286)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(os, 647220);
function os() {
  const n = ["height", "children", "push", "242KPtHjL", "object", "translateWithRecursive", "1819140EXCdWu", "3851532OBBOAS", "8ziqXJs", "parent", "addChild", "translateWith", ": 	", "setObject", "rect", "flatten", "join", "1616epJNBz", "6FJwzqa", "1114373cTacJQ", "189160wfatzR", "length", "getRect", "3849097yDAoRj", "union", "19746ofkzPb", "440868RjrnFD", "map", "setTo"];
  return os = function() {
    return n;
  }, os();
}
function g0(n, e) {
  const t = os();
  return g0 = function(x, s) {
    return x = x - 268, t[x];
  }, g0(n, e);
}
class i0 {
  constructor(e = 0, t = 0, x = 1, s = 1) {
    const i = g0;
    this.x = 0, this.y = 0, this.width = 1, this.height = 1, this[i(268)] = new z(0, 0, 1, 1), this.children = [], this.object = null, this.x = e, this.y = t, this.width = x, this[i(283)] = s;
  }
  fromObject(e) {
    const t = g0;
    this.object = e, this.x = e.x, this.y = e.y, this.width = e.width, this.height = e[t(283)];
  }
  [nt(296)](e) {
    const t = nt;
    this[t(287)] = e;
  }
  getRect(e = ![]) {
    const t = nt;
    return this.rect[t(282)](this.x, this.y, this.width, this.height), e ? z.union(this.rect, this.getChildrenRect(!![])) : this[t(268)];
  }
  getChildrenRect(e) {
    const t = nt;
    let x = this.children, s = x[0].getRect(e);
    for (let i = 1; i < x[t(275)]; i++)
      s = z.union(s, x[i][t(276)](e));
    return s;
  }
  [nt(294)](e, t) {
    this.x += e, this.y += t;
  }
  translateTo(e, t) {
    this.x = e, this.y = t;
  }
  [nt(293)](e) {
    const t = nt;
    e[t(292)] = this, this.children.push(e);
  }
  [nt(288)](e, t) {
    const x = nt;
    this[x(294)](e, t);
    let s = this.children;
    for (var i = 0; i < s[x(275)]; i++)
      s[i][x(288)](e, t);
  }
  [nt(269)](e) {
    const t = nt;
    let x = [];
    for (var s = 0; s < this.children[t(275)]; s++) {
      let i = this[t(284)][s];
      if ((e == null || e(i) == !![]) && (x[t(285)](i), i.children && i[t(284)].length > 0)) {
        let r = i0.flatten(i[t(284)], e);
        x = x.concat(r);
      }
    }
    return x;
  }
  toString() {
    const e = nt;
    return this.object.text + e(295) + i0.flatten(this.children)[e(281)]((x) => x.object.text)[e(270)](",");
  }
  static getVNodeUnionRect(e) {
    const t = nt;
    let x = e[0][t(276)]();
    for (let s = 1; s < e.length; s++)
      x = z[t(278)](x, e[s].getRect());
    return x;
  }
  static flatten(e, t) {
    const x = nt;
    let s = [];
    for (var i = 0; i < e.length; i++) {
      let r = e[i];
      if ((t == null || t(r) == !![]) && (s[x(285)](r), r.children && r.children.length > 0)) {
        let o = i0.flatten(r[x(284)], t);
        s = s.concat(o);
      }
    }
    return s;
  }
}
function as() {
  const n = ["8105900BSfqZu", "380984uiHsOh", "allVirtualNodes", "63ctyrrL", "6994449eQrEAc", "getRect", "root", "5BnzlBF", "centerTo", "children", "885046FfwjRt", "indexData", "allObjects", "3370803qDNbgH", "forEach", "length", "10624416uCJlgh", "index", "getLeafs", "4464804paQiyl", "2HMqBOm"];
  return as = function() {
    return n;
  }, as();
}
const ee = b0;
(function(n, e) {
  const t = b0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(200)) / 1 + -parseInt(t(210)) / 2 * (parseInt(t(203)) / 3) + parseInt(t(212)) / 4 + -parseInt(t(218)) / 5 * (parseInt(t(209)) / 6) + -parseInt(t(215)) / 7 + -parseInt(t(206)) / 8 + parseInt(t(214)) / 9 * (parseInt(t(211)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(as, 689336);
function b0(n, e) {
  const t = as();
  return b0 = function(x, s) {
    return x = x - 198, t[x];
  }, b0(n, e);
}
class ic {
  constructor(e) {
    const t = b0;
    this.deep = 0, this.root = e, this.descendants = e.flatten(), this.allVirtualNodes = [e].concat(this.descendants), this[t(202)] = this[t(213)].map((x) => x.object), this[t(201)] = [], this.index();
  }
  [ee(207)]() {
    const e = ee;
    let t = [];
    const x = this;
    function s(i, r) {
      const o = b0;
      x.deep < r && (x.deep = r);
      let a = i[o(199)], c = t[r];
      c == null && (c = [], t[r] = c), c.push(i);
      for (var l = 0; l < a[o(205)]; l++)
        s(a[l], r + 1);
    }
    s(this.root, 0), this[e(201)] = t;
  }
  getRect() {
    const e = ee, t = this[e(217)].flatten();
    if (t[e(205)] == 0)
      throw new Error("getRect() in empty tree");
    let x = t[0].getRect();
    for (let s = 1; s < t.length; s++) {
      const i = t[s];
      x = z.union(x, i[e(216)]());
    }
    return x;
  }
  [ee(198)](e, t) {
    const x = ee, s = this.allVirtualNodes;
    let i = this.root[x(216)]();
    s[x(204)]((c) => {
      const l = x;
      i = z.union(i, c[l(216)]());
    });
    let r = i.getCenter(), o = e - r.x, a = t - r.y;
    return s.forEach((c) => {
      c.translateWith(o, a);
    }), this;
  }
  translateTo(e, t) {
    const x = ee, s = this.allVirtualNodes;
    let i = this.root.getRect();
    s[x(204)]((a) => {
      const c = x;
      i = z.union(i, a[c(216)]());
    });
    let r = e - i.x, o = t - i.y;
    return s[x(204)]((a) => {
      a.translateWith(r, o);
    }), this;
  }
  translateWith(e, t) {
    return this[ee(213)].forEach((i) => {
      i.translateWith(e, t);
    }), this;
  }
  [ee(208)]() {
    return this.indexData[this.deep];
  }
}
const U = y0;
function y0(n, e) {
  const t = cs();
  return y0 = function(x, s) {
    return x = x - 341, t[x];
  }, y0(n, e);
}
(function(n, e) {
  const t = y0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(349)) / 1 * (parseInt(t(360)) / 2) + parseInt(t(362)) / 3 * (-parseInt(t(341)) / 4) + parseInt(t(363)) / 5 + -parseInt(t(344)) / 6 * (-parseInt(t(347)) / 7) + -parseInt(t(348)) / 8 * (parseInt(t(345)) / 9) + -parseInt(t(355)) / 10 + parseInt(t(343)) / 11 * (parseInt(t(352)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(cs, 903120);
class r0 {
  constructor() {
    this.inputs = [], this.outputs = [];
  }
  getDegree() {
    return this.getInDegree() + this.getOutDegree();
  }
  getInDegree() {
    return this.inputs.length;
  }
  getOutDegree() {
    return this[y0(346)].length;
  }
  [U(353)]() {
    const e = U;
    let t = this.outputs[e(365)]((s) => s.to), x = this.inputs.map((s) => s.from);
    return t.concat(x);
  }
}
function cs() {
  const n = ["isZero", "map", "push", "depth", "length", "filter", "170268CfeHoW", "inputs", "275JcxVJL", "54oskuBI", "351aRdKTK", "outputs", "642558TRXgmX", "72672iayuMh", "16yXqGpP", "getDegree", "vertexes", "1654284tfOFkh", "getAdjacentList", "forEach", "12686010JtKteb", "hasDirection", "from", "has", "edges", "134986OtGbtk", "isBridge", "117bPAGXG", "4967135IfDPEp"];
  return cs = function() {
    return n;
  }, cs();
}
class fi {
  constructor(e, t) {
    const x = U;
    this.weight = 0, this.from = e, this.to = t, e.outputs[x(366)](this), t.inputs.push(this);
  }
  isLoop() {
    return this.from === this.to;
  }
  isAdjacent(e) {
    const t = U;
    return this.from === e[t(357)] || this[t(357)] === e.to || this.to === e[t(357)] || this.to === e.to;
  }
}
class rc {
  constructor(e) {
    const t = U;
    this[t(351)] = e;
  }
  isClose(e) {
    return ![];
  }
  isTrace(e) {
    return ![];
  }
  isCircuit(e) {
    return this.isClose(e) && this.isTrace(e);
  }
  isTrack(e) {
    return ![];
  }
  isCycle(e) {
    return this.isClose(e) && this.isTrack(e);
  }
}
class ix {
  constructor(e, t) {
    const x = U;
    this[x(356)] = !![], this.vertexes = e, this[x(359)] = t;
  }
  [U(364)]() {
    return this[U(351)].length > 0 && this.edges.length == 0;
  }
  isAlone() {
    const e = U;
    return this[e(351)].length == 1 && this.edges[e(368)] == 0;
  }
  traverse(e, t = U(367), x = [], s = /* @__PURE__ */ new Set()) {
    const i = U;
    e == null && (e = this.vertexes[0]);
    let r = this;
    if (!s[i(358)](e) && (x.push(e), s.add(e)), t == "depth")
      e.getAdjacentList().filter((a) => !s.has(a)).map((a) => {
        !s.has(a) && (x.push(a), s.add(a)), r.traverse(a, t, x, s);
      });
    else {
      let o = e.getAdjacentList().filter((a) => !s.has(a));
      o[i(365)]((a) => {
        x.push(a), s.add(a);
      }), o[i(365)]((a) => {
        r.traverse(a, t, x, s);
      });
    }
    return x;
  }
  getMaxDegreeVertext() {
    const e = U;
    let t = this.vertexes[0];
    for (let x = 1; x < this[e(351)].length; x++) {
      const s = this.vertexes[x];
      s.getDegree() > t.getDegree() && (t = s);
    }
    return t;
  }
  getMinDegree() {
    const e = U;
    let t = this.vertexes[0].getDegree();
    for (let x = 1; x < this[e(351)].length; x++) {
      const s = this.vertexes[x];
      s.getDegree() < t && (t = s.getDegree());
    }
    return t;
  }
  getPathList(e, t, x = /* @__PURE__ */ new Set()) {
    return [];
  }
  getOrder() {
    const e = U;
    return this.vertexes[e(368)];
  }
  [U(361)]() {
  }
  isSubGraph(e) {
  }
  isTree() {
    const e = U;
    return this[e(351)].length != this[e(359)].length + 1 ? ![] : this.vertexes[e(368)] == 1 ? !![] : this.vertexes.filter((x) => x[e(350)]() == 1)[e(368)] > 0;
  }
  travelNext(e) {
    let t = [];
    function x(s) {
      const i = y0;
      t.push(s);
      let r = s[i(346)].map((o) => o.to);
      for (let o = 0; o < r.length; o++) {
        let a = r[o];
        if (a === e)
          return;
        x(a);
      }
    }
    return x(e), t;
  }
  clone() {
    const e = U;
    let t = this[e(351)].slice();
    t.forEach((i) => {
      const r = e;
      i.inputs = i[r(342)].slice(), i[r(346)] = i.outputs.slice();
    });
    let x = this.edges.slice(), s = new ix(t, x);
    return s.hasDirection = this.hasDirection, s;
  }
  check() {
    const e = U;
    let t = 0;
    this.vertexes[e(354)]((s) => t += s.getDegree()), console.assert(t == this.edges.length * 2);
    let x = this.vertexes[e(369)]((s) => s[e(350)]() % 2 != 0)[e(368)];
    console.assert(x % 2 == 0);
  }
}
const xe = _0;
(function(n, e) {
  const t = _0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(184)) / 1 * (parseInt(t(195)) / 2) + -parseInt(t(191)) / 3 + -parseInt(t(179)) / 4 + parseInt(t(172)) / 5 * (parseInt(t(197)) / 6) + parseInt(t(187)) / 7 + parseInt(t(173)) / 8 * (-parseInt(t(177)) / 9) + -parseInt(t(185)) / 10 * (-parseInt(t(199)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ls, 977323);
function ls() {
  const n = ["set", "5058176wZnoqw", "isNodeTarget", "concat", "edges", "target", "11IBBFDF", "790MmZzRF", "travelVertext", "5821389evFUDm", "begin", "length", "object", "3137649oIsprk", "isTree", "toGraphs", "createMinimalSpanningTree", "162366YAamOy", "filter", "6dQkdIN", "push", "290818DbeAuI", "get", "forEach", "fromObject", "8578315zTmEfj", "3641032dFWEOZ", "map", "has", "getInDegree", "9KFGGcY"];
  return ls = function() {
    return n;
  }, ls();
}
function _0(n, e) {
  const t = ls();
  return _0 = function(x, s) {
    return x = x - 172, t[x];
  }, _0(n, e);
}
class m0 {
  static [xe(194)](e) {
    const t = xe;
    let x = [], s = [], i = e.edges.sort((a, c) => {
      const l = _0;
      let h = a.weight - c.weight;
      return h == 0 && (h = a.from.getInDegree() - c.from[l(176)](), h == 0 && (h = c.to.getOutDegree() - a.to.getOutDegree())), h;
    }), r = /* @__PURE__ */ new WeakMap();
    for (let a = 0; a < i.length; a++) {
      const c = i[a];
      let l = c.from, h = c.to, u = r.get(l), p = r[t(200)](h);
      if (u != null && p != null)
        continue;
      u == null && (u = new r0(), u.object = l.object, x.push(u), r.set(l, u)), p == null && (p = new r0(), p.object = h.object, x.push(p), r.set(h, p));
      let b = new fi(u, p);
      b.object = c[t(190)], s.push(b);
    }
    return new ix(x, s);
  }
  getNodes(e) {
    return e.vertexes.map((t) => t.object);
  }
  getLinks(e) {
    const t = xe;
    return e[t(182)][t(174)]((x) => x.object);
  }
  objectsToGraphs(e) {
    const t = xe, x = e[t(196)]((c) => c instanceof M);
    let s = e[t(196)]((c) => c instanceof R);
    s.filter((c) => c[t(188)][t(180)]() && c.end.isNodeTarget());
    const i = /* @__PURE__ */ new WeakMap(), r = x.map((c) => {
      const l = t, h = new r0();
      return h.object = c, i[l(178)](c, h), h;
    });
    s.filter((c) => {
      const l = t;
      return i.get(c.begin.target) && i[l(200)](c.end[l(183)]);
    });
    const o = s.map((c) => {
      const l = t;
      let h = i.get(c[l(188)].target), u = i.get(c.end.target), p = new fi(h, u);
      return p.object = c, p;
    });
    return this.toGraphs(r, o);
  }
  [xe(193)](e, t) {
    const x = xe;
    let s = [];
    e.forEach((o) => {
      const a = _0;
      let c = o.inputs, l = o.outputs;
      s = s[a(181)](c), s = s.concat(l);
    });
    let i = [], r = /* @__PURE__ */ new Set();
    for (let o = 0; o < e[x(189)]; o++) {
      const a = e[o];
      if (r.has(a))
        continue;
      let c = [], l = [];
      this.travelVertext(a, c, l, r);
      let h = new ix(c, l);
      i.push(h);
    }
    return i;
  }
  travelVertext(e, t = [], x = [], s = /* @__PURE__ */ new Set()) {
    const i = xe;
    if (s.has(e))
      return;
    t.push(e), s.add(e);
    let r = e.inputs.filter((c) => !s[i(175)](c)), o = e.outputs[i(196)]((c) => !s.has(c));
    r[i(201)]((c) => {
      x.push(c), s.add(c);
    }), o[i(201)]((c) => {
      x[i(198)](c), s.add(c);
    });
    let a = this;
    r[i(201)]((c) => {
      a.travelVertext(c.from, t, x, s);
    }), o[i(201)]((c) => {
      a[i(186)](c.to, t, x, s);
    });
  }
  toTree(e) {
    const t = xe;
    !e[t(192)]() && (e = m0.createMinimalSpanningTree(e));
    let x = e.vertexes.filter((c) => c.getInDegree() == 0)[0][t(190)], s = e.vertexes.map((c) => c.object), i = e[t(182)].map((c) => c.object);
    const r = /* @__PURE__ */ new WeakMap();
    let o = new i0();
    return o.fromObject(x), r.set(x, o), s.forEach((c) => {
      const l = t;
      if (c === x)
        return;
      const h = new i0();
      h[l(202)](c), r.set(c, h);
    }), i.forEach((c) => {
      const l = t;
      let h = r.get(c.begin.target), u = r.get(c.end[l(183)]);
      h.addChild(u);
    }), new ic(o);
  }
}
const yx = I0;
function hs() {
  const n = ["265433UlWBVC", "width", "play", "2579759BPrgXS", "786237YvxVJd", "rotate", "scale", "10684487ailNEw", "positions", "scaleY", "6RXHOaX", "293520BKIKIk", "height", "4XaftDD", "4oAMYqk", "rotation", "3706310jTGFZv", "1580755bmonru", "9pEDpEt", "updateXY"];
  return hs = function() {
    return n;
  }, hs();
}
(function(n, e) {
  const t = I0, x = n();
  for (; []; )
    try {
      if (-parseInt(t(267)) / 1 * (parseInt(t(261)) / 2) + parseInt(t(271)) / 3 * (parseInt(t(260)) / 4) + -parseInt(t(264)) / 5 + -parseInt(t(257)) / 6 * (parseInt(t(270)) / 7) + -parseInt(t(258)) / 8 + -parseInt(t(265)) / 9 * (-parseInt(t(263)) / 10) + parseInt(t(274)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(hs, 351783);
function I0(n, e) {
  const t = hs();
  return I0 = function(x, s) {
    return x = x - 255, t[x];
  }, I0(n, e);
}
class Ox {
  constructor(e, t) {
    const x = I0;
    this.x = 0, this.y = 0, this.scaleX = 1, this[x(256)] = 1, this[x(268)] = 1, this[x(259)] = 1, this.rotation = 0, this.objects = e, this[x(255)] = t, this.positionNormals = N.getPointsNormalization(t);
    let s = N.getPointsRect(t);
    this.width = s[x(268)], this[x(259)] = s.height;
  }
  resizeTo(e, t) {
    this.width = e, this.height = t;
  }
  resize(e, t) {
    const x = I0;
    this[x(268)] = e, this.height = t;
  }
  translate(e, t) {
    this.x = e, this.y = t;
  }
  [yx(273)](e, t) {
    this.scaleX = e, this.scaleY = t;
  }
  [yx(272)](e) {
    const t = yx;
    this[t(262)] = e;
  }
  updateXY(e) {
    const t = this.objects;
    let x = Math.min(e.length, t.length);
    for (let s = 0; s < x; s++) {
      let i = e[s];
      t[s].setXY(i.x, i.y);
    }
  }
  doLayout(e) {
    const t = yx;
    let x = this, s = this.objects, i = this.positionNormals;
    this[t(262)] != 0 && (i = N.rotateNormaledPoints(this.positionNormals, this[t(262)]));
    let r = (o) => {
      const a = t;
      return { x: x.x + x.width * o.x * x.scaleX, y: x.y + x.height * o.y * x[a(256)] };
    };
    if (i = i.map(r), e != null) {
      let o = function(l) {
        return x.updateXY(l);
      }, a = s.map((l) => ({ x: l.x, y: l.y })), c = Object.assign({ from: a, to: i, update: o }, e);
      this.animationSystem.anime(c)[t(269)]();
    } else
      this[t(266)](i);
    return this;
  }
}
function us() {
  const n = ["translateTo", "36610SdYNvq", "352096JnHWpD", "indexData", "allVirtualNodes", "358591yddVbd", "2119860mNyWKx", "1037328iclrmV", "length", "70QMBYxF", "translateWithRecursive", "33SSkuXI", "540ytSRCo", "3SksTey", "5608116bZCyjM", "children", "50TuBUxP", "900076khsiYH"];
  return us = function() {
    return n;
  }, us();
}
function ds(n, e) {
  const t = us();
  return ds = function(x, s) {
    return x = x - 340, t[x];
  }, ds(n, e);
}
(function(n, e) {
  const t = ds, x = n();
  for (; []; )
    try {
      if (parseInt(t(348)) / 1 + parseInt(t(342)) / 2 * (-parseInt(t(356)) / 3) + -parseInt(t(355)) / 4 * (-parseInt(t(344)) / 5) + parseInt(t(357)) / 6 + -parseInt(t(352)) / 7 * (-parseInt(t(345)) / 8) + parseInt(t(349)) / 9 * (-parseInt(t(341)) / 10) + parseInt(t(354)) / 11 * (-parseInt(t(350)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(us, 834797);
function oc(n) {
  const e = ds;
  let t = n[e(346)], x = n.deep, s = n.getLeafs(), i = 60, r = 80;
  for (let c = 0; c < s.length; c++) {
    let l = s[c], h = (c + 1) * i, u = x * r;
    l[e(343)](h, u);
  }
  for (let c = x - 1; c >= 0; c--) {
    let l = t[c];
    for (let h = 0; h < l.length; h++) {
      let u = l[h], p = u[e(340)], b = u.x, _ = c * r;
      if (p[e(351)] > 0 ? b = (p[0].x + p[p.length - 1].x) / 2 : h > 0 && (b = l[h - 1].x + l[h - 1].width), u.translateTo(b, _), h > 0 && u.x < l[h - 1].x + l[h - 1].width) {
        let m = l[h - 1].x + l[h - 1].width, g = Math.abs(m - u.x);
        for (let w = h; w < l.length; w++)
          l[w][e(353)](g, 0);
      }
    }
  }
  let o = [];
  return n[e(347)].forEach((c) => {
    o.push(c);
  }), o;
}
const _x = ae;
(function(n, e) {
  const t = ae, x = n();
  for (; []; )
    try {
      if (-parseInt(t(378)) / 1 + -parseInt(t(395)) / 2 + -parseInt(t(375)) / 3 + parseInt(t(384)) / 4 + -parseInt(t(391)) / 5 + -parseInt(t(390)) / 6 * (parseInt(t(379)) / 7) + parseInt(t(376)) / 8 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(fs, 515996);
function ae(n, e) {
  const t = fs();
  return ae = function(x, s) {
    return x = x - 373, t[x];
  }, ae(n, e);
}
function fs() {
  const n = ["1751524lXFTKK", "update", "radiusScale", "add", "length", "getAdjacentList", "2058732LyJulK", "430005AOcfCz", "graphSystem", "endAngle", "map", "1087110rtOOSt", "push", "traverse", "travelTree", "848583NdLNpM", "11933528cXiODW", "animationSystem", "158037uwEGgO", "7VdGarI", "sizeFitToChildren", "points", "toTree", "isTree"];
  return fs = function() {
    return n;
  }, fs();
}
class Hr {
  constructor(e, t) {
    const x = ae;
    this[x(377)] = e, this[x(392)] = t;
  }
  shapeLayout(e, t) {
    const x = ae;
    let s = t;
    t instanceof T && (s = t[x(381)]);
    let i = new Ox(e, s);
    return i.animationSystem = this[x(377)], i;
  }
  circleLayout(e, t = {}) {
    const x = ae;
    !e.isTree() && (e = m0.createMinimalSpanningTree(e));
    let s = e[x(373)](null).filter((c) => c instanceof r0), i = s[0], r = this.getCircleLayoutPositions(i, t), o = s[x(394)]((c) => c.object), a = new Ox(o, r);
    return a.animationSystem = this.animationSystem, a;
  }
  treeLayout(e) {
    const t = ae;
    !e.isTree() && (e = m0.createMinimalSpanningTree(e));
    let x = e.traverse(null).filter((a) => a instanceof r0), s = this[t(392)][t(382)](e), i = oc(s), r = x.map((a) => a.object), o = new Ox(r, i);
    return o.animationSystem = this[t(377)], o;
  }
  [_x(374)](e, t) {
    if (!e[_x(383)]())
      throw new Error("graph is not a tree");
    return t == null && (t = e.vertexes[0]), e.travelNext(t);
  }
  getCircleLayoutPositions(e, t = {}) {
    const x = _x;
    t.cx = t.cx || 0, t.cy = t.cy || 0, t.radius = t.radius || 200, t.startAngle = t.startAngle || 0, t.endAngle = t.endAngle || 2 * Math.PI, t.radiusScale = t[x(386)] || 0.5, t.angleScale = t.angleScale || 1, t.endAngle > 2 * Math.PI && (t[x(393)] = t[x(393)] % (2 * Math.PI));
    let s = [], i = /* @__PURE__ */ new Set(), r = [];
    function o(a, c, l, h, u, p, b) {
      const _ = x;
      if (i.has(a))
        return;
      r[_(396)](a.object.id), i[_(387)](a), s[_(396)]({ x: c, y: l });
      let m = a[_(389)](), g = m[_(388)];
      if (b > 1 && (g -= 1), g <= 0)
        return;
      let w = u, O = p - u, A = O;
      O < 2 * Math.PI && (g -= 1), g != 0 && (A /= g), b > 1 && (O < 2 * Math.PI ? w -= O * 0.5 : O == 2 * Math.PI && g > 1 && g % 2 == 0 && (w += A * 0.5));
      for (let D = 0; D < m.length; D++) {
        let St = m[D], Pt = w + D * A, Ge = c + h * Math.cos(Pt), $t = l + h * Math.sin(Pt);
        t[_(385)] && t.update(t, e.object, b), o(St, Ge, $t, h * t.radiusScale, Pt, Pt + O, b + 1);
      }
    }
    return t[x(385)] && t[x(385)](t, e.object, 0), o(e, t.cx, t.cy, t.radius, t.startAngle, t.endAngle, 1), s;
  }
  [_x(380)](e, t) {
    return qt.sizeFitToChildren(e, t);
  }
}
const ke = rx;
(function(n, e) {
  const t = rx, x = n();
  for (; []; )
    try {
      if (parseInt(t(246)) / 1 * (parseInt(t(236)) / 2) + parseInt(t(245)) / 3 * (-parseInt(t(235)) / 4) + -parseInt(t(238)) / 5 + -parseInt(t(253)) / 6 + parseInt(t(249)) / 7 + -parseInt(t(255)) / 8 * (parseInt(t(254)) / 9) + -parseInt(t(237)) / 10 * (-parseInt(t(257)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ps, 473174);
function ps() {
  const n = ["stage", "2091075NrxZXn", "inputSystem", "getObject", "getCurrentLayer", "522630fvEtJV", "2440953bBLQnE", "24ekUlpJ", "hideList", "218581MVVFhp", "24356MAJXaa", "2PgHhGa", "960WUxTHM", "4610605oFqnGH", "flatten", "isNode", "mouseEnabledBak", "object", "getMouseXY", "painted", "129vrSSao", "351537wESMJB", "enter"];
  return ps = function() {
    return n;
  }, ps();
}
function rx(n, e) {
  const t = ps();
  return rx = function(x, s) {
    return x = x - 235, t[x];
  }, rx(n, e);
}
class ac {
  constructor(e) {
    const t = rx;
    this.mode = t(244), this.stage = e;
  }
  [ke(247)](e) {
    const t = ke;
    if (this.quit(), e == null || e === this.object || !e[t(240)]) {
      this[t(242)] = null;
      return;
    }
    this[t(242)] = e;
    let x = this.stage[t(252)]();
    this[t(256)] = C[t(239)](x.children, (s) => s !== e), this[t(241)] = e.mouseEnabled, e.mouseEnabled = ![], e.isSelected = ![], this.hideList.forEach(function(s, i) {
      s._cameraVisible = ![];
    });
  }
  quit() {
    const e = ke;
    this[e(256)] != null && (this.mouseEnabledBak != null && (this.object.mouseEnabled = this[e(241)]), this.hideList.forEach(function(t, x) {
      t._cameraVisible = !![];
    })), this.hideList = null, this.stage.update();
  }
  [ke(251)]() {
    return this[ke(242)] == null ? this.stage.getCurrentLayer() : this.object;
  }
  [ke(243)]() {
    const e = ke;
    return this.getObject().screenToLocalXY(this.stage[e(250)].x, this[e(248)].inputSystem.y);
  }
}
function gs(n, e) {
  var t = bs();
  return gs = function(x, s) {
    x = x - 406;
    var i = t[x];
    return i;
  }, gs(n, e);
}
(function(n, e) {
  for (var t = gs, x = n(); []; )
    try {
      var s = -parseInt(t(406)) / 1 + parseInt(t(414)) / 2 * (parseInt(t(419)) / 3) + parseInt(t(411)) / 4 * (-parseInt(t(408)) / 5) + parseInt(t(415)) / 6 * (-parseInt(t(418)) / 7) + parseInt(t(407)) / 8 * (-parseInt(t(417)) / 9) + -parseInt(t(413)) / 10 * (-parseInt(t(412)) / 11) + -parseInt(t(409)) / 12 * (-parseInt(t(416)) / 13);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(bs, 182241);
function bs() {
  var n = ["470OgOTIU", "36FMaZJy", "1729eZnRCD", "1472103TCNPdn", "288253VMSKCm", "447BNSzqi", "139753gJNAHC", "8bMtTpd", "15ZzhKzt", "47532wYMpeh", "type", "141028kIkkbM", "1521179qseXPE", "20NZqZWL"];
  return bs = function() {
    return n;
  }, bs();
}
class cc {
  constructor(e) {
    var t = gs;
    this[t(410)] = e;
  }
}
const Bt = ce;
(function(n, e) {
  const t = ce, x = n();
  for (; []; )
    try {
      if (parseInt(t(265)) / 1 + -parseInt(t(245)) / 2 * (parseInt(t(244)) / 3) + -parseInt(t(241)) / 4 * (-parseInt(t(266)) / 5) + -parseInt(t(258)) / 6 * (-parseInt(t(247)) / 7) + parseInt(t(243)) / 8 * (parseInt(t(259)) / 9) + -parseInt(t(242)) / 10 + -parseInt(t(269)) / 11 * (parseInt(t(257)) / 12) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ys, 336997);
function ys() {
  const n = ["2285087kJFszi", "isDragStart", "wheelZoom", "defaultPrevented", "cancelZoom", "dispatchEvent", "scaleY", "translateTo", "forEach", "zoom", "804wPWEsO", "6reinjK", "9BYFkKA", "width", "zoomIn", "getChildren", "scale", "dirty", "410310kJSDaq", "18695LpgyJC", "stage", "type", "20603shXKHR", "zoomOut", "scaleTo", "number", "grabbing", "4ozjrGo", "1987410hbaigM", "778040sOuIZl", "264774MtHcCy", "4XDZrOX", "transform"];
  return ys = function() {
    return n;
  }, ys();
}
function ce(n, e) {
  const t = ys();
  return ce = function(x, s) {
    return x = x - 238, t[x];
  }, ce(n, e);
}
let jt = new cc("zoom");
class lc extends Tt {
  constructor(e) {
    super(), this.x = 0, this.y = 0, this.scale = 1, this.dirty = !![], this.transform = new lx(), this.zoomMaxLimit = 10, this.zoomMinLimit = 0.1, this.stage = e;
  }
  lookAt(e, t) {
    const x = ce;
    typeof e != x(239) ? (this.x = e.x, this.y = e.y) : (this.x = e, this.y = t);
    const s = this[x(267)], i = this;
    s.getChildren().forEach(function(r) {
      const o = x;
      r.translate(-i.x * r.scaleX, -i.y * r[o(253)]);
    }), s.update(), jt.type = "lookAt", this.dispatchEvent(jt), this.dirty = !![];
  }
  zoomOut(e = 0.8) {
    const t = ce;
    return this.zoomBy(e), jt[t(268)] = t(270), this[t(252)](jt), this;
  }
  zoomIn(e = 1.25) {
    const t = ce;
    return this.zoomBy(e), jt[t(268)] = t(261), this.dispatchEvent(jt), this;
  }
  zoomBy(e, t, x) {
    const s = ce;
    let i = this[s(263)] * e;
    if (i > this.zoomMaxLimit || i < this.zoomMinLimit)
      return;
    this.scale = i;
    const r = this.stage;
    if (this.hasListener(pt.zoom)) {
      let a = new Event(pt.zoom, { cancelable: !![] });
      if (r[s(252)](a), a.defaultPrevented == !![])
        return;
    }
    const o = this;
    if (r.getChildren()[s(255)](function(a) {
      const c = s;
      a.mouseEnabled && a[c(249)] && o.zoomLayer(a, e, t, x);
    }), this.hasListener(pt.zoomAfter)) {
      let a = new Event(pt.zoomAfter, { cancelable: !![] });
      if (r.dispatchEvent(a), a[s(250)] == !![])
        return;
    }
    r.update(), this.dirty = !![], jt.type = "zoom", this.dispatchEvent(jt);
  }
  [Bt(256)](e) {
    const t = Bt;
    this[t(267)].getChildren().forEach(function(s) {
      s[t(238)](1, 1), s.translateTo(0, 0);
    }), this.scale = 1, this.zoomBy(e, this.x, this.y);
  }
  [Bt(251)]() {
    const e = Bt, t = this.stage;
    t[e(262)]().forEach(function(x) {
      const s = e;
      x.scaleTo(1, 1), x[s(254)](0, 0);
    }), this[e(263)] = 1, this[e(264)] = !![], t.update(), jt.type = "cancelZoom", this.dispatchEvent(jt);
  }
  zoomLayer(e, t, x, s) {
    const i = Bt;
    if (this.hasListener(i(256))) {
      let l = new Event("zoom", { cancelable: !![] });
      if (l.zoom = { x: t, y: t, cx: x, cy: s }, this.dispatchEvent(l), l.defaultPrevented == !![])
        return;
    }
    if (x != null && s != null) {
      e.updateMatrix();
      let l = e.toScreenXY(x, s);
      x = l.x, s = l.y;
    }
    e.scaleBy(t, t);
    let r = e[i(260)] * e.scaleX, o = e.height * e.scaleY, a = r * t - r, c = o * t - o;
    if (x != null && s != null) {
      let l = e.stage, h = { x: l.width * 0.5, y: l.height * 0.5 }, u = e.toScreenXY(h.x, h.y), p = (x - u.x) / 2, b = (s - u.y) / 2;
      t >= 1 ? (a += p, c += b) : (a -= p * t, c -= b * t);
    }
    e.translateWith(-(a / 2), -(c / 2));
  }
  translateWith(e, t) {
    this.x += e, this.y += t, this.dirty = !![];
  }
  _dragWith(e, t) {
    const x = Bt;
    this.translateWith(-e, -t);
    const s = this.stage;
    s.inputSystem[x(248)] && s.setCursor(x(240));
    const i = s[x(262)]().filter((r) => r.mouseEnabled && r.draggable);
    for (let r = 0; r < i.length; r++)
      i[r].translateWith(e, t);
  }
  targetOnly(e) {
    this[Bt(267)].localView.enter(e);
  }
  getTransform() {
    const e = Bt;
    return this.transform.translate(this.x, this.y), this[e(246)][e(263)](this[e(263)], this.scale), this[e(246)];
  }
  isDirty() {
    return this[Bt(264)];
  }
  clearDirty() {
    const e = Bt;
    this[e(264)] = ![];
  }
}
const S = me;
(function(n, e) {
  const t = me, x = n();
  for (; []; )
    try {
      if (parseInt(t(389)) / 1 + parseInt(t(461)) / 2 * (-parseInt(t(467)) / 3) + -parseInt(t(353)) / 4 * (-parseInt(t(372)) / 5) + -parseInt(t(388)) / 6 * (parseInt(t(421)) / 7) + parseInt(t(394)) / 8 * (parseInt(t(411)) / 9) + parseInt(t(385)) / 10 + -parseInt(t(367)) / 11 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(_s, 453536);
function me(n, e) {
  const t = _s();
  return me = function(x, s) {
    return x = x - 352, t[x];
  }, me(n, e);
}
function _s() {
  const n = ["getHeigth", "updateZIndex", "zoomIn", "domElement", "animationSystem", "editor", "getAABB", "isDragStart", "1912750SAivBu", "config", "position", "6dQYjZj", "227232LfociT", "resourceSystem", "dblclickHandler", "length", "filter", "6013320CFKUqi", "resize", "rand", "dropAllowed", "forceUpdate", "div", "handlerLayer", "dragHandler", "sort", "dispatchEvent", "mouseupHandler", "canvas", "px)", "hasListener", "touchendHandler", "_dragWith", "getChildren", "9kRLpyg", "dropHandler", "visible", "removeChild", "render", "dragoverHandler", "grab", "touchstartHandler", "draggable", "setCursor", "5168373BIIIZl", "width", "isMouseOn", "ctrlKey", "keyboard", "map", "metaKey", "previous", "flatten", "calc(100% - ", "mouseup", "selectedGroup", "toolbar", "isNode", "resizeTo", "pickUpViewLayers", "mouseenterHandler", "overview", "saveAsLocalImage", "_resizeObserver", "aabb", "height", "start", "Debug", "defaultPrevented", "_onMound", "edit", "toDataURL", "mode", "download", "context", "stage", "camera", "forEach", "layersContainer", "getExportAABB", "update", "innerHTML", "style", "zIndex", "9406OjFpKY", "contentRect", "unionRects", "toStageRect", "fullWindow", "name", "489lXjZoh", "fullScreen", "pause", "select", "mouseoutHandler", "translateToCenter", "data", "drag", "appendChild", "inputSystem", "8KQkmfw", "clickHandler", "_obb", "zoomOut", "isDraging", "updateSize", "children", "show", "now", "getCursor", "dragoutHandler", "offsetWidth", "getToolbarHeight", "getImageData", "628232TOmLfB", "layer_container", "distanceRatio", "createElement", "mousedragHandler", "2113510HfCycW", "css", "modeChange", "push", "_init"];
  return _s = function() {
    return n;
  }, _s();
}
let hc = Date[S(361)]();
class Pi extends Tt {
  constructor(e, t) {
    const x = S;
    super(), this.version = _i, this.children = [], this.visible = ![], this[x(449)] = "normal", this.destoryed = ![], this.config = { dropAllowed: !![] }, this[x(444)] = _e, t != null && Object.assign(this.config, t), this.camera = new lc(this), this.localView = new ac(this), this.localView[x(449)] = null, this[x(425)] = new Mr(this), this.inputSystem = new ki(), this.selectedGroup = new Dr(), this.resourceSystem = et, this._init(e), this.injectCss(), this.styleSystem = new Rr(this), this.behaviourSystem = new sc(this), this.animationSystem = new Nr(), this.effectSystem = new Wr(this, this[x(381)]), this.graphSystem = new m0(), this.layoutSystem = new Hr(this.animationSystem, this.graphSystem), this.renderSystem = new Fa(this), this.exportSystem = new Xa(this), this.serializerSystem = new n0(), this[x(390)].on("loaded", (s) => {
      this.update();
    }), this[x(352)]._initEvent(this);
  }
  injectCss() {
    const e = S;
    let t = document.head || document.getElementsByTagName("head")[0], x = document.getElementById("_jtopo_style_");
    x == null && (x = document[e(370)](e(459)), x.id = "_jtopo_style_", x.textContent = ua, t.appendChild(x));
  }
  [S(376)](e) {
    const t = S;
    let x = this;
    document.oncontextmenu = function() {
      const i = me;
      return !x.inputSystem[i(423)];
    }, x.domElement = fc(e);
    const s = document.createElement(t(399));
    x.layersContainer = s, x.debugPanel = new la(x), _e.isDebug && x.debugPanel[t(360)](), x.setToolbar(new Ar(x)), s.classList.add(t(368)), s.style.position = "relative", s.style.width = "100%", x.domElement[t(475)](s), x[t(422)] = s[t(364)], x.height = s.clientHeight;
    {
      const i = new Gs(x);
      x.handlerLayer = i;
      const r = i[t(415)].canvas;
      r.style.zIndex = "" + i.zIndex, this[t(455)].appendChild(r);
    }
    uc(x), x.on("dragover", function(i) {
      i.preventDefault();
    });
  }
  showDebugPanel() {
    this.debugPanel.show();
  }
  hideDebugPanel() {
    this.debugPanel.hide();
  }
  showOverview(e) {
    const t = S;
    let x = this;
    x.overview == null && (x[t(438)] = new ha(x)), x.overview[t(373)](e), x.overview[t(360)]();
  }
  _updateOverview() {
    const e = S;
    this.overview != null && this[e(438)][e(457)]();
  }
  hideOverview() {
    this.overview != null && this.overview.hide();
  }
  getToolbarHeight() {
    const e = S;
    return this.toolbar == null ? 0 : this[e(433)][e(377)]();
  }
  [S(356)](e = 0.8) {
    const t = S;
    return this[t(453)][t(356)](e), this;
  }
  [S(379)](e = 1.25) {
    return this.camera.zoomIn(e), this;
  }
  zoom(e) {
    this.camera.zoomBy(e);
  }
  cancelZoom() {
    this.camera.cancelZoom();
  }
  zoomFullStage() {
    const e = S, t = this;
    let x = t[e(383)](), s = t.width / x[e(422)], i = t[e(442)] / x[e(442)], r = Math.min(s, i), o = 0, a = 0;
    this.camera.zoomBy(r, o, a), t.translateToCenter();
  }
  [S(472)]() {
    if (!this.visible) {
      console.warn("stage.translateToCenter() should after stage.show()");
      return;
    }
    this.getChildren().forEach(function(e) {
      e.centerBy();
    });
  }
  createLayer(e) {
    let t = new ft(e);
    return this.addChild(t), t;
  }
  addChild(e) {
    const t = S;
    e[t(446)](this), e[t(460)] = this.children.length, e[t(435)](this.width, this.height), e[t(466)] == null && (e[t(466)] = "Layer_" + e.zIndex), this.children[t(375)](e);
    const x = e.render.canvas;
    x.style.zIndex = "" + e.zIndex, this.layersContainer[t(475)](x), this.updateZIndex();
  }
  [S(378)]() {
    const e = S;
    this.children[e(402)](function(t, x) {
      return t.zIndex - x.zIndex;
    }), this.children[e(454)]((t) => {
      const x = e, s = t[x(415)][x(405)];
      s[x(459)].zIndex = "" + t.zIndex;
    });
  }
  [S(410)]() {
    return this.children;
  }
  [S(414)](e) {
    const t = S;
    e.render[t(405)].remove();
    let x = this.children.indexOf(e);
    return x == -1 ? this : (N.removeAt(this.children, x), e[t(452)] = null, this);
  }
  show() {
    const e = S;
    this.visible = !![], this[e(410)]().forEach(function(t) {
      t.show();
    }), this.renderSystem[e(443)](), this.styleSystem.start();
  }
  hide() {
    const e = S;
    this[e(413)] = ![], this.renderSystem[e(469)](), this.getChildren().forEach(function(t) {
      t.hide();
    });
  }
  update() {
    const e = S;
    this[e(410)]().forEach(function(t) {
      t.update();
    }), this[e(400)][e(457)]();
  }
  forceUpdate() {
    this.handlerLayer.forceUpdate(), this.getChildren().forEach(function(e) {
      e[me(398)]();
    });
  }
  toDataURL() {
    const e = S;
    return this.exportSystem[e(448)](this[e(359)]);
  }
  saveImageInfo() {
    return this.exportSystem.saveImageInfo(this.children);
  }
  [S(439)]() {
    const e = S;
    this.exportSystem[e(439)](this.children);
  }
  on(e, t) {
    return this.on.index = hc, this.addEventListener(e, t);
  }
  [S(465)]() {
    const e = S;
    if (this.hasListener(pt.fullWindow)) {
      let t = new Event(pt[e(465)], { cancelable: !![] });
      if (this.dispatchEvent(t), t[e(445)] == !![])
        return;
    }
    Ye.fullWindow(this.domElement);
  }
  [S(468)]() {
    const e = S;
    Ye.fullScreen(this[e(380)]);
  }
  showToolbar() {
    const e = S;
    if (this.toolbar == null)
      return;
    this[e(433)][e(360)]();
    let t = "calc(100% - " + this[e(365)]() + e(406);
    this.layersContainer[e(459)].height = t;
  }
  hideToolbar() {
    const e = S;
    if (this.toolbar == null)
      return;
    this.toolbar.hide();
    let t = e(430) + this.getToolbarHeight() + e(406);
    this.layersContainer.style.height = t;
  }
  setToolbar(e) {
    this[S(433)] != null && this.toolbar.remove(), this.domElement.appendChild(e.getDom());
    let x = "calc(100% - " + this.getToolbarHeight() + "px)";
    this.layersContainer.style.height = x, this.toolbar = e;
  }
  setMode(e) {
    const t = S;
    if (this[t(407)](pt.modeChange)) {
      let x = new Event(pt[t(374)], { cancelable: !![] });
      if (x.mode = e, this.dispatchEvent(x), x.defaultPrevented == !![])
        return;
    }
    dc(this, e), e == Ct.drag ? this[t(420)](t(417)) : this.setCursor("default");
  }
  dropHandler(e) {
  }
  dragoverHandler() {
  }
  mouseoverHandler() {
  }
  mousedownHandler(e) {
    const t = S, x = this.inputSystem;
    if (this.mode == Ct.drag) {
      this[t(420)]("grabbing");
      return;
    }
    let s = this, i = s.pickUpViewLayers();
    s[t(352)].target = i;
    let r = s[t(449)] == t(447) && i != null && i.mouseEnabled == ![];
    if (i != null && !r) {
      x.pickObject(i);
      return;
    }
    !(e[t(424)] || e[t(427)]) && s.selectedGroup.removeAll();
  }
  static findDropToObjec(e, t) {
    const x = S;
    let s = e.getAncestors(), i = C[x(429)](e.children);
    for (let r = t.length - 1; r >= 0; r--) {
      let o = t[r];
      if (!(e === o || o === e.parent || !o.dropAllowed) && !(s.indexOf(o) != -1 || i.indexOf(o) != -1) && o._obb[x(441)].containsRect(e[x(355)].aabb))
        return o;
    }
    return null;
  }
  mousedragHandler(e) {
    const t = S, x = this.inputSystem, s = this.handlerLayer, i = this.mode, r = this.inputSystem.target, o = this.selectedGroup, a = this.pickUpViewLayers();
    x.mouseoverTarget = a;
    const c = e.buttons == 1, l = c && r != null && r.mouseEnabled && r[t(419)], h = this[t(410)]().filter((p) => p.mouseEnabled);
    if (c && this.config[t(397)] && l && r[t(419)]) {
      let p = null;
      for (let b = 0; b < h.length; b++) {
        const _ = h[b], m = _.flattenList[t(393)]((g) => g.painted && g[t(434)]);
        p = Pi.findDropToObjec(r, m), p !== x.dropTarget && x.dropTarget && x.dropTarget[t(363)](x), p != null && p[t(416)](this.inputSystem);
      }
      x.dropTarget = p;
    }
    if (l) {
      o[t(401)](x);
      return;
    }
    if (c && (i == Ct[t(470)] || i == Ct.edit)) {
      Hi(s);
      return;
    }
    this[t(453)]._dragWith(x.dx, x.dy);
  }
  mouseupHandler(e) {
    const t = S, x = this.inputSystem;
    let s = this;
    const i = this[t(410)](), r = x.isRightButton();
    for (let c = 0; c < i[t(392)]; c++) {
      const l = i[c];
      l.draggable && x.isDraging && l.dragEndHandler();
    }
    if (this[t(449)] == Ct[t(474)]) {
      this.setCursor("grab");
      return;
    }
    this.setCursor("default");
    let o = this.inputSystem.target, a = this.handlerLayer;
    a.areaBox.hide(), a.areaBox.resizeTo(0, 0), o && o.mouseEnabled && (x.type == t(431) ? o[t(404)](x) : x.type == "touchend" && o[t(408)](x), x[t(428)].isDraging && o.draggable && r == ![] && s[t(432)].dragEndHandler(x)), this[t(386)].dropAllowed && x.dropTarget && (x.dropTarget[t(412)](x), x.dropTarget = null);
  }
  mouseoutHandler(e) {
    const t = S, x = this[t(352)];
    this.handlerLayer.mouseoutHandler(x), this.selectedGroup[t(471)](x);
  }
  [S(354)](e) {
    const t = S, x = this.inputSystem;
    let s = x.target;
    s && !x.previous.isDragEnd && s[t(354)](x);
  }
  [S(391)](e) {
    const t = S, x = this[t(352)];
    let s = x.target;
    s && !x[t(428)].isDragEnd && s.dblclickHandler(x);
  }
  mousemoveHandler(e) {
    const t = S, x = this.inputSystem;
    if (x[t(357)])
      return this[t(371)](e);
    if (this.mode == Ct.drag) {
      this.setCursor("grab");
      return;
    }
    if (x.skipPointerMovePicking) {
      console.log("跳过");
      return;
    }
    let s = x.mouseoverTarget, i = this.pickUpViewLayers();
    s !== i && s != null && s[t(471)] && s.mouseoutHandler(x), i != null && (s !== i ? i.mouseenterHandler && i[t(437)](x) : i.mousemoveHandler && i.mousemoveHandler(x)), x.mouseoverTarget = i;
  }
  mousewheelHandler(e) {
    const t = this.getCurrentLayer();
    if (t == null || this.inputSystem.wheelZoom != !![])
      return;
    let i = t.mouseX, r = t.mouseY;
    (e.wheelDelta == null ? e.detail : e.wheelDelta) > 0 ? this.camera.zoomBy(1.25, i, r) : this.camera.zoomBy(0.8, i, r);
  }
  mouseenterHandler(e) {
  }
  [S(418)](e) {
    this.mousedownHandler(e);
  }
  touchmoveHandler(e) {
    const t = S, x = this.inputSystem, s = this.handlerLayer, i = this[t(449)], r = this.inputSystem.target, o = this[t(432)], a = this.pickUpViewLayers();
    this.inputSystem.mouseoverTarget = a, x.distanceRatio != 1 && this.getChildren().forEach((h) => {
      const u = t;
      h.zoom(x[u(369)], x[u(369)]);
    }), x[t(384)];
    const c = r != null && r.mouseEnabled && r[t(419)];
    if (this.getChildren()[t(393)]((h) => h.mouseEnabled), c) {
      o[t(401)](x);
      return;
    }
    if (i == Ct.select || i == Ct.edit) {
      Hi(s);
      return;
    }
    this.camera[t(409)](x.dx, x.dy);
  }
  touchwheelHandler(e) {
    throw new Error("touchwheelHandler");
  }
  touchendHandler(e) {
    this.mouseupHandler(e);
  }
  [S(436)]() {
    let e = this.getChildren();
    for (let t = e.length - 1; t >= 0; t--) {
      let x = e[t];
      if (!(x.visible && x.mouseEnabled))
        continue;
      let s = x.pickUpChild();
      if (s != null)
        return s;
    }
    return null;
  }
  getCursor() {
    const e = S;
    return this.handlerLayer.render[e(362)]();
  }
  setCursor(e) {
    const t = S;
    return this[t(400)].render[t(420)](e);
  }
  download(e, t) {
    const x = S;
    return this.exportSystem[x(450)](e, t);
  }
  select(e) {
    const t = S;
    this.selectedGroup.removeAll(), this[t(432)].addAll(e);
  }
  getCurrentLayer() {
    return this[S(359)].filter((t) => t.visible == !![])[0];
  }
  [S(396)](e, t) {
    return e == null && (e = -this.width * 0.5), t == null && (t = this.height * 0.5), Math.round(Math.random() * (t - e) + e);
  }
  [S(383)]() {
    const e = S;
    let t = this.getChildren()[e(426)]((s) => s[e(464)](s.getAABB()));
    return z.unionRects(t);
  }
  [S(456)]() {
    const e = S;
    let t = this.getChildren().map((s) => s[e(464)](s.getExportAABB()));
    return z[e(463)](t);
  }
  destory(e) {
    const t = S;
    if (this.destoryed)
      throw new Error("Stage has been destroyed already.");
    this.destoryed = !![], this[t(440)] && this._resizeObserver.disconnect(), this._resizeTimer && clearInterval(this._resizeTimer), this.handlerLayer.destory(), this.children.forEach((x) => {
      x.destory();
    }), this[t(380)][t(458)] = "", e != ![] && et.clearCache();
  }
}
function Hi(n, e) {
  const t = S;
  let x = n.stage, s = n.showAreaBox(), i = n[t(452)][t(410)]();
  for (var r = 0; r < i[t(392)]; r++) {
    let o = i[r];
    if (!o.hasChildren())
      continue;
    let a = o.toLayerRect(s), c = o.pickUpByRect(a);
    x.selectedGroup.addAll(c);
  }
}
function Fi(n, e, t) {
  const x = S;
  n.layersContainer.style[x(442)] = x(430) + n.getToolbarHeight() + "px)", n.width = e, n[x(442)] = t, n.handlerLayer[x(358)](e, t), n[x(410)]()[x(454)](function(i) {
    const r = x;
    i[r(358)](e, t), n.editor && n[r(382)].update && n.editor[r(457)]();
  });
  let s = new Event(pt[x(395)]);
  n.dispatchEvent(s);
}
function uc(n) {
  const e = n.layersContainer;
  if (e.style.height = "calc(100% - " + n.getToolbarHeight() + "px)", window.ResizeObserver) {
    const x = new ResizeObserver((s) => {
      const i = me;
      s[0][i(462)], Et.delayRun("stage_resize", function() {
        Fi(n, e.clientWidth, e.clientHeight);
      }, 30);
    });
    x.observe(e), n._resizeObserver = x;
    return;
  }
  let t = setInterval(function() {
    const x = me;
    let s = e[x(364)], i = e.offsetHeight;
    (n.width != s || n[x(442)] != i) && (e[x(459)].height = "calc(100% - " + n.getToolbarHeight() + x(406), Fi(n, s, i));
  }, 500);
  n._resizeTimer = t;
}
function dc(n, e) {
  const t = S;
  let x = n[t(449)];
  n[t(449)] = e;
  let s = { type: pt.modeChange, oldMode: x, newMode: e };
  n[t(403)](s);
}
function fc(n) {
  const e = S;
  if (typeof n == "string" && (n = document.getElementById(n), n == null))
    throw new Error("the dom element id is not found id:" + n);
  if (n == null)
    throw new Error("the dom element is null.");
  return n.style[e(387)] = "relative", n;
}
const X = Is;
(function(n, e) {
  const t = Is, x = n();
  for (; []; )
    try {
      if (-parseInt(t(299)) / 1 * (-parseInt(t(293)) / 2) + parseInt(t(318)) / 3 + parseInt(t(310)) / 4 * (parseInt(t(303)) / 5) + -parseInt(t(290)) / 6 + parseInt(t(308)) / 7 + -parseInt(t(294)) / 8 * (-parseInt(t(317)) / 9) + -parseInt(t(316)) / 10 * (parseInt(t(312)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ms, 789896);
var pc = Object[X(309)], gc = Object.getOwnPropertyDescriptor, mx = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? gc(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && pc(e, t, s), s;
};
function ms() {
  const n = ["defineProperty", "4614940mmRRMU", "getPoint", "286onyjmB", "translateWith", "getCtrlPoints", "prototype", "664730afFgZm", "101538IqDwHn", "3192633xASJHd", "serializers", "points", "7391364SywCLi", "getBeginArrowDirection", "normalize", "6CUyIoP", "248oBJRMU", "getPoints", "DefaultPositions", "horizontal", "begin", "315521roOMtM", "concat", "interpolate", "getCtrlPoint", "5lpRjOT", "ctrlPoint", "direction", "setCtrlPoint", "length", "1650859NerZdu"];
  return ms = function() {
    return n;
  }, ms();
}
class x0 extends R {
  constructor(e, t, x, s, i) {
    const r = X;
    super(e, t, x, s, i), this[r(305)] = "horizontal";
  }
  updatePoints() {
    const e = X;
    let t = this._calcAZ(), x = t[0], s = t[1], i = this[e(302)](x, s);
    return this.points = [x, i, s], this[e(289)];
  }
  translateWith(e, t) {
    const x = X;
    return super[x(313)](e, t), this.ctrlPoint != null && (this.ctrlPoint.x += e, this.ctrlPoint.y += t), this;
  }
  autoCalcCtrlPoint(e, t) {
    const x = X;
    let s = (e.x + t.x) / 2, i = (e.y + t.y) / 2;
    return this[x(305)] == o0[x(297)] ? i += (t.y - e.y) / 2 : i -= (t.y - e.y) / 2, { x: s, y: i };
  }
  getCtrlPoint(e, t) {
    const x = X;
    return this.ctrlPoint != null ? this[x(304)] : this.autoCalcCtrlPoint(e, t);
  }
  [X(306)](e) {
    const t = X;
    this[t(304)] = e, this.matrixDirty = !![];
  }
  resetCtrlPoint() {
    const e = X;
    this[e(304)] = void 0, this.matrixDirty = !![];
  }
  [X(311)](e) {
    const t = X;
    let x = this.getPoints(), s = x[0], i = x[1], r = x[2], o = E[t(301)](s, i, e), a = E.interpolate(i, r, e);
    return E.interpolate(o, a, e);
  }
  [X(291)]() {
    const e = X;
    let t = this.points[0], x = this[e(311)](0.01), s = [t.x - x.x, t.y - x.y];
    return B[e(292)](s, s);
  }
  getEndArrowDirection() {
    const e = X;
    let t = this.getPoint(0.9), x = this.points[this.points[e(307)] - 1], s = [x.x - t.x, x.y - t.y];
    return B.normalize(s, s);
  }
}
function Is(n, e) {
  const t = ms();
  return Is = function(x, s) {
    return x = x - 289, t[x];
  }, Is(n, e);
}
mx([f("CurveLink")], x0.prototype, "className", 2), mx([f(T.Curve)], x0.prototype, "shape", 2), mx([f(function() {
  return ["begin", "end", "ctrlPoint"];
})], x0.prototype, X(314), 2), mx([f(R.prototype.serializers[X(300)](["direction", "ctrlPoint"]))], x0.prototype, X(319), 2);
const M0 = {};
M0[k[X(298)]] = function() {
  return this.getPoints()[0];
}, M0[k.end] = function() {
  let n = this.getPoints();
  return n[n.length - 1];
}, M0[k.center] = function() {
  let e = this[X(295)]();
  return E.middle(e[0], e[1]);
}, M0[k.ctrlPoint] = function() {
  if (this.ctrlPoint != null)
    return this.ctrlPoint;
  let n = this.getPoints();
  return this.getCtrlPoint(n[0], n[1]);
}, x0[X(315)][X(296)] = M0;
var Ne = vs;
function ws() {
  var n = ["2896qxPZGp", "prototype", "14SwpRps", "defineProperty", "15811QqtUlI", "resizeToFitText", "arrowsSize", "length", "884934aVXexr", "10186792iXDoxW", "3ZxthiA", "162845fkrtEG", "1177724WGgwtO", "2204688NiGpWf", "20YjiVto", "1400lDqegd"];
  return ws = function() {
    return n;
  }, ws();
}
(function(n, e) {
  for (var t = vs, x = n(); []; )
    try {
      var s = parseInt(t(246)) / 1 * (parseInt(t(244)) / 2) + parseInt(t(236)) / 3 * (-parseInt(t(238)) / 4) + -parseInt(t(237)) / 5 + -parseInt(t(239)) / 6 + -parseInt(t(241)) / 7 * (-parseInt(t(242)) / 8) + parseInt(t(250)) / 9 * (-parseInt(t(240)) / 10) + parseInt(t(235)) / 11;
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ws, 218049);
function vs(n, e) {
  var t = ws();
  return vs = function(x, s) {
    x = x - 235;
    var i = t[x];
    return i;
  }, vs(n, e);
}
var bc = Object[Ne(245)], yc = Object.getOwnPropertyDescriptor, Ix = (n, e, t, x) => {
  for (var s = Ne, i = x > 1 ? void 0 : x ? yc(e, t) : e, r = n[s(249)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && bc(e, t, i), i;
};
class R0 extends ge {
  constructor(e, t = 0, x = 0, s = 1, i = 1) {
    super(e, t, x, s, i);
  }
  [Ne(247)]() {
    var e = Ne;
    let x = this._computedStyle.calcGap();
    this._width = this._textWidth + x, this._height = this._textHeight + x + this[e(248)];
  }
}
Ix([f("TipNode")], R0.prototype, "className", 2), Ix([f(8)], R0.prototype, Ne(248), 2), Ix([f(new $e())], R0[Ne(243)], "shape", 2), Ix([f(ge.prototype.serializers.concat(["arrowsSize"]))], R0[Ne(243)], "serializers", 2);
var re = Ut;
(function(n, e) {
  for (var t = Ut, x = n(); []; )
    try {
      var s = -parseInt(t(412)) / 1 + -parseInt(t(407)) / 2 + parseInt(t(404)) / 3 * (parseInt(t(402)) / 4) + -parseInt(t(417)) / 5 * (-parseInt(t(410)) / 6) + -parseInt(t(401)) / 7 * (parseInt(t(397)) / 8) + parseInt(t(416)) / 9 + -parseInt(t(405)) / 10 * (-parseInt(t(414)) / 11);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(ks, 333449);
var _c = Object.defineProperty, mc = Object.getOwnPropertyDescriptor, ti = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? mc(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && _c(e, t, s), s;
};
function Ut(n, e) {
  var t = ks();
  return Ut = function(x, s) {
    x = x - 397;
    var i = t[x];
    return i;
  }, Ut(n, e);
}
class W0 extends M {
  constructor(e, t = 0, x = 0, s = 1, i = 1) {
    var r = Ut;
    super(), this._isPlaying = ![], this.text = e, this.x = t || 0, this.y = x || 0, this[r(415)] = s || 0, this.height = i || 0;
  }
  showCover() {
    var e = Ut;
    this[e(422)] = ![];
  }
  play() {
    this._isPlaying = !![], this._video.play();
  }
  pause() {
    var e = Ut;
    this[e(423)].pause();
  }
  setVideo(e) {
    var t = Ut;
    const x = this;
    this._dom != null && (this._dom[t(409)](), this[t(413)] = null), typeof e == "string" ? (this._videoSrc = e, this[t(423)] = Ye[t(403)](e, function() {
      var s = t;
      x[s(408)] && x[s(398)]();
    }), this._dom = this._video) : (this[t(423)] = e, this._videoSrc = e[t(420)].getAttribute("src")), this._video[t(415)] = this[t(415)], this[t(423)][t(418)] = this.height;
  }
  onPlay(e) {
    var t = Ut;
    this._video[t(406)](t(398), e, ![]);
  }
  [re(411)](e) {
    this._video.addEventListener("pause", e, ![]);
  }
  onEnded(e) {
    var t = re;
    this._video[t(406)]("ended", e, ![]);
  }
  draw(e) {
    var t = re;
    e.beginPath();
    const x = -this.width * 0.5, s = -this.height * 0.5;
    if (e.rect(x, s, this.width, this.height), this._video != null) {
      this._video.width != this.width && (this._video.width = this[t(415)], this._video.height = this.height);
      let i = this[t(422)] ? this[t(423)] : this._image;
      i != null && e.drawImage(i, x, s, this.width, this[t(418)]);
    } else
      e.stroke();
    this[t(419)](e), this[t(399)](e);
  }
  destory() {
    super.destory(), this._dom && this._dom.remove();
  }
}
ti([f("VideoNode")], W0[re(400)], "className", 2), ti([f(![])], W0[re(400)], "autoplay", 2), ti([f(M.prototype[re(421)].concat([re(408), "videoSrc"]))], W0.prototype, re(421), 2), Object.defineProperties(W0.prototype, { videoSrc: { get() {
  return this._videoSrc;
}, set(n) {
  this.setVideo(n);
} } });
function ks() {
  var n = ["30QTNCoQ", "onPause", "612300GFoHvV", "_dom", "2838fBvvvt", "width", "954351KcItVN", "41140vkBZeT", "height", "mousePickupPath", "firstElementChild", "serializers", "_isPlaying", "_video", "8YRrrhP", "play", "_paintText", "prototype", "1739262irSycS", "312788xbhXOf", "createVideo", "21XfWBmy", "34490LqcrCe", "addEventListener", "780370vGCxHm", "autoplay", "remove"];
  return ks = function() {
    return n;
  }, ks();
}
const ht = w0;
(function(n, e) {
  const t = w0, x = n();
  for (; []; )
    try {
      if (parseInt(t(243)) / 1 + -parseInt(t(241)) / 2 + parseInt(t(254)) / 3 + parseInt(t(244)) / 4 * (parseInt(t(248)) / 5) + -parseInt(t(255)) / 6 + -parseInt(t(239)) / 7 * (parseInt(t(238)) / 8) + -parseInt(t(246)) / 9 * (parseInt(t(237)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ss, 462479);
var pi = function() {
};
pi.prototype = { calculate: function(n, e) {
  const t = w0;
  this.dx = n.x - e.x, this.dy = n.y - e.y, this.d2 = this.dx * this.dx + this.dy * this.dy, this.d = Math[t(260)](this.d2);
} };
class Ic {
  constructor(e, t, x) {
    const s = w0;
    this[s(242)] = [], this.frame_width = t, this[s(236)] = x, this.origin = e, this[s(256)](), this.initNodes(e);
  }
  [ht(261)](e) {
    const t = ht;
    let x = this;
    qt[t(259)](e, function(s, i) {
      const r = t;
      if (s.isNode && i != null) {
        let o = i, a = s;
        o == e && x.setOriginEdgeWeight(a, x.originWeight);
        let c = a.mass | 1;
        x.addNode(a, c);
        let l = 30;
        x[r(257)](o, a, l);
      }
    });
  }
  initialize() {
    const e = ht;
    this.originWeight = 48, this.speed = 12, this.gravity = 50, this[e(262)] = 512, this[e(252)] = new Array(), this.edges = new Array(), this.originEdges = new Array();
  }
  [ht(265)](e, t) {
    const x = ht;
    if (this.originEdges[e.id]) {
      if (e.id != this.selectedNode) {
        let s = this[x(242)][e.id], i = (t.d - s) / s;
        e.force.x += i * (t.dx / t.d), e.force.y += i * (t.dy / t.d);
      }
    } else if (e.id != this.selectedNode) {
      let s = this.gravity * e.mass * this[x(258)].mass / t.d2, i = this.maxForceDistance - t.d;
      i > 0 && (s *= Math.log(i)), s < 1024 && (e.force.x -= s * t.dx / t.d, e[x(251)].y -= s * t.dy / t.d);
    }
  }
  [ht(247)](e, t, x) {
    const s = ht;
    let i = this[s(240)][e.id][t.id];
    if (i += 3 * (e.neighbors + t[s(250)]), i) {
      let r = (x.d - i) / i;
      e.id != this.selectedNode && (e.force.x -= r * x.dx / x.d, e[s(251)].y -= r * x.dy / x.d), t.id != this[s(263)] && (t.force.x += r * x.dx / x.d, t.force.y += r * x.dy / x.d);
    }
  }
  repulsiveForce(e, t, x) {
    const s = ht;
    let i = this.gravity * e.mass * t.mass / x.d2, r = this[s(262)] - x.d;
    r > 0 && (i *= Math[s(253)](r)), i < 1024 && (e[s(251)].x += i * x.dx / x.d, e.force.y += i * x.dy / x.d);
  }
  [ht(249)]() {
    this.applyForce();
  }
  [ht(264)]() {
    const e = ht;
    for (var t = 0; t < this.nodes.length; t++) {
      let s = this.nodes[t];
      for (var x = 0; x < this.nodes.length; x++)
        if (t != x) {
          let r = this.nodes[x], o = new pi();
          o.calculate(s, r), this.getLink(s.id, r.id) != null && this.attractiveForce(s, r, o), t != this.selectedNode && this.repulsiveForce(s, r, o);
        }
      let i = new pi();
      i.calculate(this.origin, s), this.originForce(s, i), s.force.x *= this.speed, s[e(251)].y *= this.speed, s.x += s[e(251)].x, s.y += s.force.y, s.force.x = 0, s.force.y = 0;
    }
  }
  bounds(e) {
    const t = ht;
    let x = 12, s = x * 2 + 4, i = e.x, r = e.x + s, o = e.y, a = e.y + s;
    i < 0 && (e.x = 0), o < 0 && (e.y = 0), r > this[t(245)] && (e.x = this[t(245)] - s), a > this[t(236)] && (e.y = this[t(236)] - s);
  }
  setOriginEdgeWeight(e, t) {
    this.originEdges[e.id] = t;
  }
  addNode(e, t) {
    const x = ht;
    e.mass = t | 1, e.neighbors = e[x(250)] | 0, e.force = { x: 0, y: 0 }, this[x(252)].push(e);
  }
  getLink(e, t) {
    let x = this.edges[e];
    return x == null ? null : x[t];
  }
  addLink(e, t, x) {
    !this.edges[e.id] && (this.edges[e.id] = new Object()), this.edges[e.id][t.id] = x;
    try {
      e.neighbors++, t.neighbors++;
    } catch (s) {
      console.log("Error Adding Edge: " + s);
    }
  }
}
function w0(n, e) {
  const t = Ss();
  return w0 = function(x, s) {
    return x = x - 236, t[x];
  }, w0(n, e);
}
function Ss() {
  const n = ["14znfoDK", "edges", "471362ANnjFq", "originEdges", "710187pgocSx", "170012kvyiOq", "frame_width", "54738ltmMDm", "attractiveForce", "25KWrmue", "doLayout", "neighbors", "force", "nodes", "log", "1445913HyVvQA", "42750NqJVBx", "initialize", "addLink", "origin", "travel", "sqrt", "initNodes", "maxForceDistance", "selectedNode", "applyForce", "originForce", "frame_height", "230kmcKwD", "2238008gkkqFD"];
  return Ss = function() {
    return n;
  }, Ss();
}
const le = Os;
(function(n, e) {
  const t = Os, x = n();
  for (; []; )
    try {
      if (parseInt(t(294)) / 1 + parseInt(t(297)) / 2 * (-parseInt(t(302)) / 3) + -parseInt(t(301)) / 4 * (-parseInt(t(288)) / 5) + -parseInt(t(286)) / 6 * (parseInt(t(295)) / 7) + -parseInt(t(292)) / 8 + -parseInt(t(300)) / 9 + parseInt(t(289)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ps, 774723);
var wc = Object[le(290)], vc = Object.getOwnPropertyDescriptor, wx = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? vc(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && wc(e, t, s), s;
};
function Ps() {
  const n = ["4STWhJm", "21bJrvrF", "23676OnTLZf", "prototype", "401725mMkYiE", "42329490hHHmuK", "defineProperty", "direction", "6626936cmLVdz", "concat", "1019121XdpAGL", "2268EwJFvV", "length", "319810OXAgbR", "push", "getPoint", "11983374Lpmyfp"];
  return Ps = function() {
    return n;
  }, Ps();
}
class H0 extends R {
  constructor(e, t, x, s, i) {
    super(e, t, x, s, i);
  }
  [le(299)](e) {
    const t = le;
    let x = this.getPoints(), s = x[0], i = x[x[t(296)] - 1], r = (s.x + i.x) / 2, o = (s.y + i.y) / 2, a = s.x - i.x, c = s.y - i.y, l = Math.sqrt(a * a + c * c) / 2, h = Math.atan2(c, a), u = h + Math.PI * e;
    return this[t(291)] == "anticlockwise" && (u = h - Math.PI * e), { x: r + l * Math.cos(u), y: o + l * Math.sin(u) };
  }
  updatePoints() {
    const e = le;
    let t = this._calcAZ(), x = t[0], s = t[1], i = [x], r = { x: (x.x + s.x) * 0.5, y: (x.y + s.y) * 0.5 }, o = E.distancePoint(x, s) * 0.5;
    return i.push({ x: r.x, y: r.y - o }), i[e(298)]({ x: r.x, y: r.y + o }), i[e(298)]({ x: r.x - o, y: r.y }), i[e(298)]({ x: r.x + o, y: r.y }), i.push(s), i;
  }
}
wx([f("ArcLink")], H0[le(287)], "className", 2), wx([f(T.Arc)], H0[le(287)], "shape", 2), wx([f(R.prototype.serializers[le(293)](["direction"]))], H0[le(287)], "serializers", 2), wx([f("clockwise")], H0.prototype, "direction", 2);
function Os(n, e) {
  const t = Ps();
  return Os = function(x, s) {
    return x = x - 286, t[x];
  }, Os(n, e);
}
function Ls() {
  const n = ["2952378rikvWu", "161758gApsTR", "interpolate", "getPoint", "BezierLink", "399574GwinrJ", "ctrlPoint2", "getPoints", "shape", "3082170SBtJpe", "end", "1608027ovqJXD", "8XZzLaD", "prototype", "ctrlPoint1", "calcCtrlPoint2", "updatePoints", "48CkJevH", "matrixDirty", "799940jjgkIK", "begin", "161860AcLchZ"];
  return Ls = function() {
    return n;
  }, Ls();
}
const ot = v0;
(function(n, e) {
  const t = v0, x = n();
  for (; []; )
    try {
      if (parseInt(t(427)) / 1 + parseInt(t(445)) / 2 + parseInt(t(437)) / 3 + parseInt(t(443)) / 4 * (-parseInt(t(447)) / 5) + -parseInt(t(435)) / 6 + parseInt(t(431)) / 7 * (parseInt(t(438)) / 8) + parseInt(t(426)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ls, 580702);
function v0(n, e) {
  const t = Ls();
  return v0 = function(x, s) {
    return x = x - 426, t[x];
  }, v0(n, e);
}
var kc = Object.defineProperty, Sc = Object.getOwnPropertyDescriptor, ei = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? Sc(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && kc(e, t, s), s;
};
class Y0 extends R {
  constructor(e, t, x, s, i) {
    super(e, t, x, s, i);
  }
  translateWith(e, t) {
    const x = v0;
    return super.translateWith(e, t), this.ctrlPoint1 != null && (this.ctrlPoint1.x += e, this.ctrlPoint1.y += t), this[x(432)] != null && (this.ctrlPoint2.x += e, this.ctrlPoint2.y += t), this;
  }
  setCtrlPoint1(e) {
    this.ctrlPoint1 = e, this.matrixDirty = !![];
  }
  setCtrlPoint2(e) {
    const t = v0;
    this.ctrlPoint2 = e, this[t(444)] = !![];
  }
  autoCalcCtrlPoint(e, t, x) {
    let s = t.x - e.x, i = t.y - e.y, r = (t.x + e.x) / 2, o = (t.y + e.y) / 2, a = 1 - 0.618;
    return { x: r + s * a, y: o - i * a };
  }
  calcCtrlPoint1(e, t, x) {
    return this.ctrlPoint1 != null ? this.ctrlPoint1 : this.autoCalcCtrlPoint(e, x, t);
  }
  [ot(441)](e, t, x) {
    return this.ctrlPoint2 != null ? this.ctrlPoint2 : this.autoCalcCtrlPoint(t, x, e);
  }
  resetCtrlPoint() {
    const e = ot;
    this[e(440)] = void 0, this.ctrlPoint2 = void 0, this[e(444)] = !![];
  }
  getPoint(e) {
    const t = ot;
    let x = this.getPoints(), s = x[0], i = x[1];
    x[2];
    let r = x[3], o = x[4], a = i, c = r, l = E.interpolate(s, a, e), h = E.interpolate(a, c, e), u = E[t(428)](c, o, e), p = E[t(428)](l, h, e), b = E.interpolate(h, u, e);
    return E.interpolate(p, b, e);
  }
  [ot(442)]() {
    const e = ot, t = this._calcAZ(), x = t[0], s = t[1];
    let i = { x: (x.x + s.x) / 2, y: (x.y + s.y) / 2 }, r = this.calcCtrlPoint1(x, s, i), o = this[e(441)](x, s, i);
    return [x, r, i, o, s];
  }
}
ei([f(ot(430))], Y0[ot(439)], "className", 2), ei([f(T.BezierCurve)], Y0[ot(439)], ot(434), 2), ei([f(R.prototype.serializers.concat(["ctrlPoint1", "ctrlPoint2"]))], Y0.prototype, "serializers", 2);
const Qe = {};
Qe[k[ot(446)]] = function() {
  return this.getPoints()[0];
}, Qe[k[ot(436)]] = function() {
  let e = this[ot(433)]();
  return e[e.length - 1];
}, Qe[k.center] = function() {
  return this[ot(429)](0.5, 0);
}, Qe[k.ctrlPoint1] = function() {
  return this.ctrlPoint1 != null ? this.ctrlPoint1 : this.getPoints()[1];
}, Qe[k.ctrlPoint2] = function() {
  return this[ot(432)] != null ? this.ctrlPoint2 : this.getPoints()[3];
}, Y0.prototype.DefaultPositions = Qe;
var Pc = ox;
(function(n, e) {
  for (var t = ox, x = n(); []; )
    try {
      var s = -parseInt(t(122)) / 1 * (-parseInt(t(124)) / 2) + parseInt(t(120)) / 3 * (parseInt(t(123)) / 4) + -parseInt(t(126)) / 5 * (-parseInt(t(134)) / 6) + parseInt(t(128)) / 7 + -parseInt(t(125)) / 8 + parseInt(t(130)) / 9 * (parseInt(t(132)) / 10) + parseInt(t(133)) / 11 * (-parseInt(t(131)) / 12);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Cs, 125547);
var Oc = Object.defineProperty, Lc = Object.getOwnPropertyDescriptor, Cc = (n, e, t, x) => {
  for (var s = x > 1 ? void 0 : x ? Lc(e, t) : e, i = n.length - 1, r; i >= 0; i--)
    (r = n[i]) && (s = (x ? r(e, t, s) : r(s)) || s);
  return x && s && Oc(e, t, s), s;
};
function ox(n, e) {
  var t = Cs();
  return ox = function(x, s) {
    x = x - 120;
    var i = t[x];
    return i;
  }, ox(n, e);
}
class Oi extends R {
  constructor(e, t, x, s, i) {
    super(e, t, x, s, i);
  }
  getPath() {
    return this.path.endpoints;
  }
  clearPath() {
    var e = ox;
    const t = this.path.endpoints;
    for (let x = 1; x < t.length - 1; x++) {
      let s = t[x];
      s[e(129)]() && s.target.removeOutLink(this);
    }
    t[e(127)] = 0;
  }
  setPath(e) {
    if (e.length < 2)
      throw new Error("path length is less than 2");
    this.clearPath(), super._setEndpoints(e), this.matrixDirty = !![];
  }
}
function Cs() {
  var n = ["16820pUxNUD", "8393wNGhXn", "726jRgDMh", "39KLqkvj", "prototype", "317jpBKcB", "17372czAduj", "856uBIqJH", "1527632IuLowK", "4185TlAMhq", "length", "589680vtgaxt", "isDisplayObjectTarget", "918zwoEcu", "3660PmrfZO"];
  return Cs = function() {
    return n;
  }, Cs();
}
Cc([f("PathLink")], Oi[Pc(121)], "className", 2);
const F0 = Es;
(function(n, e) {
  const t = Es, x = n();
  for (; []; )
    try {
      if (parseInt(t(368)) / 1 * (-parseInt(t(370)) / 2) + -parseInt(t(372)) / 3 * (parseInt(t(375)) / 4) + parseInt(t(365)) / 5 * (-parseInt(t(362)) / 6) + -parseInt(t(369)) / 7 + parseInt(t(376)) / 8 * (parseInt(t(367)) / 9) + -parseInt(t(363)) / 10 * (-parseInt(t(371)) / 11) + parseInt(t(379)) / 12 * (parseInt(t(366)) / 13) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ts, 705683);
const Xi = ["#475164", F0(374), "#FA7E23", "#FF9900", "#FED71A", "#2bae85", F0(364), "#12A182", "#5e5314", "#1ba784", "#0f1423", F0(377), "#2474b5", "#2775B6", "#346c9c", "#61649f", "#C06f98", "#7e2065", "#681752", "#EE3f4d", F0(373)];
function Ec() {
  let e = Math[F0(378)]() * Xi.length | 0;
  return Xi[e];
}
function Es(n, e) {
  const t = Ts();
  return Es = function(x, s) {
    return x = x - 362, t[x];
  }, Es(n, e);
}
function Ts() {
  const n = ["2659581JgPnAP", "8oGuWzv", "6167245DCQtQP", "123866ixvhHV", "33cZmyAT", "2858286YLgHAa", "#C02c38", "#2d2e36", "4GhaUML", "16uYKFoD", "#4E7ca1", "random", "8005488qIqhYz", "43782gdhPfP", "1416210SwYNDU", "#248067", "445eLvnkh", "52qtKFcR"];
  return Ts = function() {
    return n;
  }, Ts();
}
const G = he;
(function(n, e) {
  const t = he, x = n();
  for (; []; )
    try {
      if (parseInt(t(108)) / 1 + parseInt(t(115)) / 2 * (-parseInt(t(109)) / 3) + parseInt(t(116)) / 4 * (parseInt(t(138)) / 5) + -parseInt(t(131)) / 6 * (-parseInt(t(114)) / 7) + parseInt(t(123)) / 8 * (-parseInt(t(127)) / 9) + parseInt(t(112)) / 10 * (-parseInt(t(136)) / 11) + -parseInt(t(132)) / 12 * (parseInt(t(117)) / 13) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(As, 942232);
const X0 = class {
  constructor() {
    const n = he;
    this.position = { x: 0, y: 0 }, this.direction = 0, this.stepSize = 1, this.dx = 1, this.dy = 1, this[n(121)]();
  }
  init() {
    const n = he;
    return this._position = { x: 0, y: 0 }, this._actions = [], this[n(125)] = {}, this;
  }
  addAction(n, e) {
    const t = he;
    let x = { name: n, args: e };
    return this._actions[t(129)](x), this;
  }
  mark(n) {
    const e = he;
    if (n == null)
      throw new Error("mark's name is required.");
    return this.marks[n] = { x: this._position.x, y: this[e(135)].y }, this;
  }
  [G(118)](n) {
    return this.marks[n];
  }
  faceToMark(n) {
    const e = G;
    let t = this.getMark(n);
    return this[e(130)](t);
  }
  moveToMark(n) {
    let t = this[G(118)](n);
    return this.moveTo(t);
  }
  forwardToMark(n) {
    let t = this[G(118)](n);
    return this.forwardTo(t);
  }
  [G(113)](n) {
    const e = G;
    for (let t = 0; t < n.length; t++) {
      const x = n[t], s = this[e(118)](x);
      this[e(120)](s);
    }
    return this;
  }
  updateDxy() {
    let n = this._position, e = this._direction, t = this._stepSize, x = n.x + t * Math.cos(e), s = n.y + t * Math.sin(e);
    return this.dx = x - n.x, this.dy = s - n.y, this;
  }
  faceTo(n) {
    const e = n.x, t = n.y;
    return this._direction = Math.atan2(t - this._position.y, e - this._position.x), this.updateDxy(), this;
  }
  [G(119)](n) {
    const e = G;
    return this[e(128)](n), this[e(135)].x += this.dx, this._position.y += this.dy, this.addAction(X0.OP.forward, [this._position.x, this._position.y]), this;
  }
  jump(n) {
    n == null && (n = 1);
    for (var e = 0; e < n; e++)
      this._position.x += this.dx, this._position.y += this.dy, this.addAction(X0.OP.jump, [this._position.x, this._position.y]);
    return this;
  }
  moveTo(n) {
    const e = G, t = n.x, x = n.y;
    return this._position.x = t, this[e(135)].y = x, this.addAction(X0.OP.moveTo, [this._position.x, this._position.y]), this;
  }
  moveToMiddle(n, e) {
    let t = { x: (n.x + e.x) / 2, y: (n.y + e.y) / 2 };
    return this.moveTo(t);
  }
  forwardTo(n) {
    const e = G, t = n.x, x = n.y;
    return this._position.x = t, this[e(135)].y = x, this.addAction(X0.OP[e(120)], [this._position.x, this[e(135)].y]), this;
  }
  [G(126)](n) {
    return n == null && (n = Math.PI / 2), this._direction = this._direction - n, this.updateDxy(), this;
  }
  [G(111)](n) {
    const e = G;
    return n == null && (n = -Math.PI / 2), this[e(137)] = this._direction + n, this.updateDxy(), this;
  }
  size(n) {
    return this._stepSize = n, this.updateDxy(), this;
  }
  sizeBy(n) {
    const e = G;
    return this[e(133)] *= n, this[e(124)](), this;
  }
  sizeWith(n) {
    return this._stepSize += n, this.updateDxy(), this;
  }
  getDistance(n) {
    const e = G, t = n.x, x = n.y;
    let s = t - this._position.x, i = x - this[e(135)].y;
    return Math[e(122)](s * s + i * i);
  }
  getAngle(n) {
    const e = G, t = n.x, x = n.y;
    return Math.atan2(x - this[e(135)].y, t - this._position.x);
  }
};
let Fr = X0;
function he(n, e) {
  const t = As();
  return he = function(x, s) {
    return x = x - 108, t[x];
  }, he(n, e);
}
Fr.OP = { forward: "forward", forwardTo: G(120), moveTo: G(134), jump: G(110) };
function As() {
  const n = ["push", "faceTo", "6nrffha", "500184KaVKQA", "_stepSize", "moveTo", "_position", "11gYaKnT", "_direction", "136265CWdRnc", "127899BSWBXP", "423Ebcljm", "jump", "turnRight", "1954270GWwwxA", "forwardToMarks", "7525931sFbZZr", "12590bHfSdP", "268DtkxDT", "39gZHkFJ", "getMark", "forward", "forwardTo", "init", "sqrt", "27352MkLSHM", "updateDxy", "marks", "turnLeft", "2313jLrAri", "size"];
  return As = function() {
    return n;
  }, As();
}
const _t = Ms;
(function(n, e) {
  const t = Ms, x = n();
  for (; []; )
    try {
      if (-parseInt(t(363)) / 1 + -parseInt(t(349)) / 2 * (-parseInt(t(353)) / 3) + -parseInt(t(372)) / 4 * (-parseInt(t(350)) / 5) + parseInt(t(361)) / 6 * (parseInt(t(373)) / 7) + parseInt(t(360)) / 8 + parseInt(t(357)) / 9 + -parseInt(t(369)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ds, 756762);
function Ms(n, e) {
  const t = Ds();
  return Ms = function(x, s) {
    return x = x - 346, t[x];
  }, Ms(n, e);
}
function Ds() {
  const n = ["applyTo's target has no method:", "toCmds", "style", "8014401NemNbu", "circle", "text", "3067520sDnYYT", "12DjZZdU", "restore", "1434205VRAWze", "_actions", "_position", "addAction", "name", "fillText", "15053990MfNdqn", "moveTo", "push", "34396PhbggB", "4886350uWEyEn", "ctx.", "join", "length", "166xmhGTV", "220ZwBapG", "apply", `
 return path;`, "23421qwjNDq"];
  return Ds = function() {
    return n;
  }, Ds();
}
var Yi = { forward: "lineTo", forwardTo: "lineTo", moveTo: _t(370), jump: "moveTo" };
class Tc extends Fr {
  constructor() {
    super();
  }
  applyTo(e) {
    const t = _t;
    let x = this[t(364)];
    for (let s = 0; s < x[t(348)]; s++) {
      const i = x[s];
      let r = Yi[i[t(367)]];
      const o = i.args;
      r == null && (r = i.name);
      let a = e[r];
      if (a == null)
        throw new Error(t(354) + r);
      if (o == null) {
        a.apply(e, []);
        continue;
      }
      o[t(348)] ? a[t(351)](e, o) : e[r] = o;
    }
    return this;
  }
  toFunction() {
    let e = this.toCmd();
    return new Function("ctx", e);
  }
  toPath2D() {
    const e = _t;
    let t = this.toCmd("path.");
    return t = `var path = new Path2D();
` + t, t = t + e(352), new Function("path", t)();
  }
  toCmd(e) {
    const t = _t;
    return this.toCmds(e)[t(347)](`
`);
  }
  [_t(355)](e) {
    const t = _t;
    let x = this._actions, s = [];
    e == null && (e = t(346));
    for (let i = 0; i < x[t(348)]; i++) {
      const r = x[i];
      let o = Yi[r[t(367)]];
      const a = r.args;
      if (o == null && (o = r[t(367)]), a == null) {
        s[t(371)](e + o + "();");
        continue;
      }
      a[t(348)] ? s[t(371)](e + o + "(" + Ji(a) + ");") : s.push(e + o + "=" + Ji(a) + ";");
    }
    return s;
  }
  [_t(358)](e) {
    return this.addAction("arc", [this._position.x, this._position.y, e, 0, Math.PI * 2]), this;
  }
  [_t(356)](e, t) {
    return this.addAction(e, t), this;
  }
  [_t(359)](e) {
    const t = _t;
    return this.addAction(t(368), [e, this[t(365)].x, this._position.y]), this;
  }
  lineTo(e) {
    return this.forwardTo(e), this;
  }
  beginPath() {
    return this.addAction("beginPath"), this;
  }
  stroke() {
    return this[_t(366)]("stroke"), this;
  }
  fill() {
    return this.addAction("fill"), this;
  }
  save() {
    return this.addAction("save"), this;
  }
  restore() {
    const e = _t;
    return this.addAction(e(362)), this;
  }
}
function Ji(n) {
  if (!Array.isArray(n) && typeof n == "string")
    return '"' + n + '"';
  let e = "";
  for (let t = 0; t < n.length; t++) {
    let x = n[t];
    typeof x == "string" ? e += '"' + x + '"' : e += "" + x, t + 1 < n.length && (e += ",");
  }
  return e;
}
(function(n, e) {
  const t = Wt, x = n();
  for (; []; )
    try {
      if (parseInt(t(363)) / 1 * (parseInt(t(373)) / 2) + parseInt(t(374)) / 3 * (-parseInt(t(357)) / 4) + parseInt(t(384)) / 5 * (parseInt(t(361)) / 6) + parseInt(t(381)) / 7 * (parseInt(t(369)) / 8) + -parseInt(t(370)) / 9 * (-parseInt(t(351)) / 10) + parseInt(t(378)) / 11 * (-parseInt(t(368)) / 12) + -parseInt(t(367)) / 13 * (parseInt(t(356)) / 14) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Bs, 394012);
function Wt(n, e) {
  const t = Bs();
  return Wt = function(x, s) {
    return x = x - 349, t[x];
  }, Wt(n, e);
}
function Ac(n, e, t) {
  const x = Wt;
  t = t | 1;
  const s = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  return s[x(372)]("xmlns", x(360)), s.style = "border:0px;position:absolute;top:0px;left:0px;text-align:center;z-index:10;width:" + n + x(362) + e + "px;opacity:" + t + ";", s.innerHTML = x(366) + t + x(379), s;
}
function Mc(n) {
  const e = Wt;
  let t = document.createElement(e(359));
  t.width = n[e(382)].width ? +n[e(382)][e(358)] : n.width, t.height = n.style[e(352)] ? +n[e(382)][e(352)] : n.height;
  let x = t[e(380)]("2d");
  return x.scale(t[e(358)] / n.naturalWidth, t.height / n[e(385)]), x[e(364)](n, 0, 0), e(383) + n[e(358)] + e(355) + n.height + e(354) + t.toDataURL(e(371)) + '" height="' + n[e(352)] + e(349) + n.width + 'px" /></svg>';
}
async function Dc(n) {
  const e = n.replace(/\<br\>/gi, "<div/>"), t = e.match(/<img .*?>/g);
  if (t == null || t.length == 0)
    return new Promise(function(s) {
      s([[], []]);
    });
  const x = [];
  return new Promise(function(s) {
    const i = Wt;
    t[i(350)](function(r) {
      const o = i, a = document.createElement("div");
      a[o(386)] = r;
      const c = a.querySelector(o(353));
      c.onload = function() {
        const l = o, h = Mc(c);
        x.push(h), x[l(377)] == t.length && s([t, x]);
      };
    });
  });
}
function Bs() {
  const n = ["12ojYvWI", "912ktbfnP", "9567OUiMvM", "image/png", "setAttribute", "10EXrVSB", "1605081LkuFoG", "opacity", "svg", "length", "6244403GKLOjA", `;">
        '</foreignObject>`, "getContext", "48069gNoVeN", "style", '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink= "http://www.w3.org/1999/xlink" width="', "8300WPfutO", "naturalHeight", "innerHTML", 'px" width="', "forEach", "3110cMGwaf", "height", "img", '" display="inline" style="vertical-align: middle;display: inline-block"><image xlink:href="', '" height="', "11606zxpNcU", "4apHTnS", "width", "canvas", "http://www.w3.org/2000/svg", "2388RehaKi", "px;height:", "127066GAqWwz", "drawImage", "html", '<foreignObject width="100%" height="100%" style="position:absolute;top:0;left:0; opacity: ', "14313JbDGSs"];
  return Bs = function() {
    return n;
  }, Bs();
}
class Bc {
  constructor(e, t, x, s = 1) {
    const i = Wt;
    this.isHtmlImage = !![], this[i(358)] = 1, this[i(352)] = 1, this.opacity = 1, this.isHtmlImage = !![], e !== null && e.startsWith("HtmlImage") && (e = e.substring(9)), this.html = e, this.width = t, this.height = x, this[i(375)] = s | 1, this.svg = Ac(t, x, this[i(375)]), this[i(359)] = document.createElement("canvas");
  }
  setSize(e, t) {
    this.width = e, this.height = t;
  }
  getAttribute(e) {
    if (e != "src")
      throw new Error("HtmlImage has only src attr");
    return "HtmlImage" + this.html;
  }
  setHtml(e) {
    const t = Wt;
    this[t(365)] = e;
  }
  getCanvas() {
    const e = Wt;
    let t = this[e(365)];
    const x = this[e(376)], s = this;
    let i = s[e(359)], r = i.getContext("2d");
    return i.style.width = s[e(358)] + "px", i[e(382)].height = s.height + "px", i[e(358)] = s[e(358)], i[e(352)] = s.height, new Promise(function(o) {
      Dc(t).then(function(a) {
        const c = Wt, l = a[0], h = a[1];
        for (let b = 0; b < l[c(377)]; b++)
          t = t.replace(l[b], h[b]);
        const u = x.querySelector("foreignObject");
        u.innerHTML = t;
        let p = new Image();
        p.src = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(s[c(376)].outerHTML), p.onload = function() {
          r.drawImage(p, 0, 0, s.width, s.height), o(i);
        };
      });
    });
  }
}
const k0 = zs;
(function(n, e) {
  const t = zs, x = n();
  for (; []; )
    try {
      if (parseInt(t(144)) / 1 * (parseInt(t(137)) / 2) + parseInt(t(142)) / 3 + -parseInt(t(136)) / 4 + parseInt(t(135)) / 5 * (-parseInt(t(131)) / 6) + parseInt(t(129)) / 7 * (parseInt(t(128)) / 8) + parseInt(t(143)) / 9 + -parseInt(t(134)) / 10 * (parseInt(t(141)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(js, 483632);
var jc = Object[k0(139)], zc = Object.getOwnPropertyDescriptor, Vi = (n, e, t, x) => {
  const s = k0;
  for (var i = x > 1 ? void 0 : x ? zc(e, t) : e, r = n[s(133)] - 1, o; r >= 0; r--)
    (o = n[r]) && (i = (x ? o(e, t, i) : o(i)) || i);
  return x && i && jc(e, t, i), i;
};
function js() {
  const n = ["prototype", "95094Rcrrjo", "left", "length", "10NTwObq", "35EWGltL", "2401984oceGLy", "36zfeZyI", "beginPath", "defineProperty", "direction", "9661817GlhzXm", "1215786XFKTDy", "4738428xdJBHV", "22913thOkIt", "height", "width", "583384uyHgiN", "70ZbGpJH"];
  return js = function() {
    return n;
  }, js();
}
class gi extends M {
  constructor(e, t = 0, x = 0, s = 1, i = 1) {
    const r = k0;
    super(e, t, x, s, i), this.ratio = 0.5, this[r(140)] = k.right;
  }
  draw(e) {
    const t = k0;
    let x = this._computedStyle, s = x.fillStyle;
    x.fillStyle = null, this._strokeAndFill(e), this.mousePickupPath(e), x.fillStyle = s, e[t(138)](), e.fillStyle = s;
    let i = x.borderWidth || 0, r = x.padding || 0, o = r * 2 + i * 2, a = -this.width * 0.5 + i + r, c = -this.height * 0.5 + i + r, l = (this.width - o) * this.ratio, h = (this.height - o) * this.ratio;
    if (this.direction == k.right)
      h = this.height - o;
    else if (this.direction == k.down)
      l = this[t(127)] - o;
    else if (this.direction == k[t(132)])
      a = -this.width * 0.5 + this[t(127)] - i - r - l, h = this.height - o;
    else if (this.direction == k.up)
      c = -this.height * 0.5 + this[t(126)] - i - r - h, l = this[t(127)] - o;
    else
      throw new Error("Unknow RatioNode's direction:" + this.direction);
    e.rect(a, c, l, h), e.fill(), this._paintText(e);
  }
}
Vi([f("RatioNode")], gi.prototype, "className", 2), Vi([f(M[k0(130)].serializers.concat(["ratio", "direction"]))], gi[k0(130)], "serializers", 2);
function zs(n, e) {
  const t = js();
  return zs = function(x, s) {
    return x = x - 126, t[x];
  }, zs(n, e);
}
const Gi = ue;
(function(n, e) {
  const t = ue, x = n();
  for (; []; )
    try {
      if (-parseInt(t(494)) / 1 * (-parseInt(t(475)) / 2) + -parseInt(t(478)) / 3 + -parseInt(t(480)) / 4 * (parseInt(t(483)) / 5) + -parseInt(t(487)) / 6 * (parseInt(t(482)) / 7) + -parseInt(t(490)) / 8 + parseInt(t(476)) / 9 + parseInt(t(491)) / 10 * (parseInt(t(500)) / 11) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ns, 167249);
function ue(n, e) {
  const t = Ns();
  return ue = function(x, s) {
    return x = x - 475, t[x];
  }, ue(n, e);
}
function Ns() {
  const n = ["classList", "jtopo_popoupmenu", "domElement", "setHtml", "352AozrQA", "style", "hide", "644692kRMQsr", "763794UwUuZo", "height", "493467ojYAOw", "appendChild", "212ilMZEw", "none", "84TvnDqS", "19850gPmBBc", "width", "defaultPrevented", "layersContainer", "41946qSupXz", "item", "block", "626592WxOMkg", "92860eozawW", "createElement", "stage", "1PCrKpA", "offsetHeight"];
  return Ns = function() {
    return n;
  }, Ns();
}
class Nc extends Tt {
  constructor(e, t) {
    const x = ue;
    super(), this.stage = e, this.domElement = this[x(499)](t);
  }
  remove() {
    this.domElement != null && this.domElement.remove();
  }
  setHtml(e) {
    const t = ue;
    this.html = e, this.remove();
    let x = document[t(492)]("div");
    return x[t(496)].add(t(497)), this[t(493)][t(486)][t(479)](x), x.innerHTML = e, this.initEvent(x), this.domElement = x, this.hide(), x;
  }
  initEvent(e) {
    let t = this;
    e.querySelectorAll("a").forEach(function(s) {
      s.addEventListener("click", function(i) {
        const r = ue;
        let o = new Event("select", { cancelable: !![] });
        o[r(488)] = this.innerHTML, t.dispatchEvent(o), !o[r(485)] && t[r(502)]();
      });
    });
  }
  showAt(e, t) {
    const x = ue;
    this.domElement.style.display = x(489), t + this.domElement[x(495)] >= this.stage.height && t > this[x(493)][x(477)] / 2 && (t -= this.domElement[x(495)]), e + this.domElement.offsetWidth >= this.stage[x(484)] && e > this.stage.width / 2 && (e -= this[x(498)].offsetWidth), this.domElement[x(501)].left = e + "px", this.domElement[x(501)].top = t + "px";
  }
  [Gi(502)]() {
    const e = Gi;
    this.domElement[e(501)].display = e(481);
  }
}
const Lt = S0;
(function(n, e) {
  const t = S0, x = n();
  for (; []; )
    try {
      if (parseInt(t(518)) / 1 + parseInt(t(510)) / 2 * (parseInt(t(513)) / 3) + parseInt(t(503)) / 4 + parseInt(t(501)) / 5 + parseInt(t(515)) / 6 * (-parseInt(t(508)) / 7) + -parseInt(t(523)) / 8 + -parseInt(t(509)) / 9 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Rs, 235293);
function Rs() {
  const n = ["6545853NgAFfr", "2fQsdPJ", "style", "domElement", "1026363DAZbYr", "fadeOut", "114aIcdrt", "showAt", "left", "460687PFlSFd", "fadeoutTimer", "innerHTML", "top", "hide", "187216OwJsrv", "stage", "1292805SdOOhZ", "enabled", "741520OpXFdX", "createElement", "opacity", "initEvent", "display", "96061vSYTXG"];
  return Rs = function() {
    return n;
  }, Rs();
}
class Rc extends Tt {
  constructor(e) {
    const t = S0;
    super(), this.stage = e, this.domElement = document[t(504)]("div"), this[t(512)].classList.add("jtopo_tooltip"), this.stage.layersContainer.appendChild(this.domElement), this.initEvent(), this.hide();
  }
  disable() {
    this[S0(512)] && this.domElement.remove();
  }
  [Lt(502)]() {
    const e = Lt;
    this[e(512)] && this[e(512)].parentNode == null && this[e(500)].layersContainer.appendChild(this.domElement);
  }
  setHtml(e) {
    const t = Lt;
    return this[t(512)][t(520)] = e, this.domElement;
  }
  [Lt(506)]() {
    const e = Lt, t = this, x = this[e(500)].inputSystem;
    x.on("mousedown", function() {
      t[e(522)]();
    }), x.on("mouseup", function() {
      t[e(522)]();
    }), x.on("mousemove", function() {
      x.mouseoverTarget == null && t.fadeOut();
    });
  }
  [Lt(516)](e, t) {
    const x = Lt;
    this.stopFade(), this[x(512)].style.display = "block", this[x(512)].style[x(517)] = e + "px", this[x(512)][x(511)][x(521)] = t + "px", this.domElement.style.opacity = "0.9";
  }
  [Lt(522)]() {
    const e = Lt;
    this.stopFade(), this.domElement[e(511)][e(507)] = "none";
  }
  stopFade() {
    this.fadeoutTimer != null && (clearInterval(this.fadeoutTimer), this.fadeoutTimer = null);
  }
  [Lt(514)](e = 50) {
    const t = Lt;
    if (this.fadeoutTimer != null)
      return;
    let x = this;
    this[t(519)] = setInterval(function() {
      const s = t;
      x.domElement[s(511)].opacity -= 0.1, x[s(512)].style[s(505)] <= 0.1 && x.hide();
    }, e);
  }
}
function S0(n, e) {
  const t = Rs();
  return S0 = function(x, s) {
    return x = x - 500, t[x];
  }, S0(n, e);
}
const ne = ax;
function Ws() {
  const n = ["editor", "2739420UEnpZG", "focus", "2188482BkXUoa", "textarea", "279thiTnZ", "text", "21kXizqC", "show", "setValue", "4259145TtCljY", "isNode", "setSize", "key", "hide", "stage", "toStageXY", "value", "createElement", "height", "style", "350lCRPMA", "50768weCFWP", "positionToLocalPoint", "7415uDYkgq", "attachTo", "22550200TzVuiZ", "724860RDaGhV"];
  return Ws = function() {
    return n;
  }, Ws();
}
(function(n, e) {
  const t = ax, x = n();
  for (; []; )
    try {
      if (-parseInt(t(462)) / 1 * (-parseInt(t(459)) / 2) + parseInt(t(465)) / 3 + -parseInt(t(467)) / 4 + parseInt(t(476)) / 5 + parseInt(t(469)) / 6 * (parseInt(t(473)) / 7) + -parseInt(t(460)) / 8 * (-parseInt(t(471)) / 9) + -parseInt(t(464)) / 10 === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ws, 742166);
function ax(n, e) {
  const t = Ws();
  return ax = function(x, s) {
    return x = x - 454, t[x];
  }, ax(n, e);
}
class Wc {
  constructor(e) {
    const t = ax;
    this[t(466)] = e, this.stage = e[t(481)];
    let x = document[t(456)](t(470));
    x.classList.add("jtopo_input_textfield"), this.stage.layersContainer.appendChild(x);
    let s = this;
    x.onkeydown = function(i) {
      s.onkeydown(i);
    }, this.textarea = x;
  }
  [ne(463)](e, t) {
    const x = ne;
    let s = { x: t.x - 50, y: t.y };
    if (e[x(477)]) {
      let i = e[x(461)](k.lt);
      s = e[x(454)](i.x, i.y);
      let r = hi(e.width, 60, 100), o = hi(e.height, 60, 100);
      this[x(478)](r, o);
    } else if (e.isLink)
      return;
    this.setValue(e.text), this[x(474)](s.x, s.y);
  }
  [ne(475)](e) {
    const t = ne;
    this.textarea[t(455)] = e;
  }
  setSize(e, t) {
    const x = ne;
    this[x(470)].style.width = e + "px", this[x(470)][x(458)][x(457)] = t + "px";
  }
  show(e, t) {
    const x = ne;
    this.textarea.style.display = "block", this.textarea[x(468)](), e != null && (e = Math.max(0, e), t = Math.max(0, t), this.textarea[x(458)].left = e, this.textarea.style.top = t);
  }
  [ne(480)]() {
    this.textarea.style.display = "none";
  }
  onkeydown(e) {
    const t = ne;
    let x = this.textarea;
    if (e[t(479)] == "Enter" && (e.ctrlKey || e.metaKey)) {
      let s = this.stage.inputSystem.target;
      if (s == null)
        return;
      s[t(472)] = x.value, x[t(458)].display = "none", this.hide();
    }
  }
}
(function(n, e) {
  for (var t = bi, x = n(); []; )
    try {
      var s = parseInt(t(469)) / 1 * (-parseInt(t(465)) / 2) + -parseInt(t(462)) / 3 * (parseInt(t(468)) / 4) + parseInt(t(460)) / 5 * (-parseInt(t(471)) / 6) + -parseInt(t(464)) / 7 * (parseInt(t(466)) / 8) + parseInt(t(461)) / 9 + -parseInt(t(459)) / 10 * (parseInt(t(463)) / 11) + parseInt(t(470)) / 12 * (parseInt(t(467)) / 13);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Hs, 319672);
function Hs() {
  var n = ["10pkecLW", "2313099knYzXA", "331494sdIdea", "97449hUjszy", "2704142uJPPay", "818EulFbj", "8KBsoli", "23723141uhtyEK", "16mETfhL", "923ejpkuJ", "12TCIjeC", "473208PNKlCw", "450wqLyPA"];
  return Hs = function() {
    return n;
  }, Hs();
}
function bi(n, e) {
  var t = Hs();
  return bi = function(x, s) {
    x = x - 459;
    var i = t[x];
    return i;
  }, bi(n, e);
}
(function(n, e) {
  for (var t = yi, x = n(); []; )
    try {
      var s = -parseInt(t(492)) / 1 + -parseInt(t(494)) / 2 * (parseInt(t(496)) / 3) + parseInt(t(497)) / 4 + parseInt(t(495)) / 5 * (parseInt(t(493)) / 6) + -parseInt(t(500)) / 7 + parseInt(t(499)) / 8 * (-parseInt(t(498)) / 9) + -parseInt(t(491)) / 10 * (-parseInt(t(501)) / 11);
      if (s === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Fs, 928467);
function yi(n, e) {
  var t = Fs();
  return yi = function(x, s) {
    x = x - 491;
    var i = t[x];
    return i;
  }, yi(n, e);
}
function Fs() {
  var n = ["173620qStqTv", "36AAIdGu", "10xLULWq", "788845GJftkS", "975993oZFJkQ", "984816eYeTee", "63aetZnH", "570824iHbrBj", "1988854UMlAyk", "11SlPENw", "23195170RFRTOX"];
  return Fs = function() {
    return n;
  }, Fs();
}
const Hc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  AENode: di,
  Animation: zr,
  AnimationSystem: Nr,
  ArcLink: H0,
  ArcShape: N0,
  ArrowShape: D0,
  AutoFoldLink: ie,
  BezierCurveShape: z0,
  BezierLink: Y0,
  CircleNode: xx,
  CircleShape: si,
  Collection: N,
  Config: Ve,
  Cursor: Zr,
  CurveLink: x0,
  CurveShape: j0,
  Debug: _e,
  DefaultDarkTheme: Sr,
  DefaultLightTheme: Te,
  DefaultZIndexs: cx,
  Direction: o0,
  DisplayObject: C,
  DomUtil: Ye,
  Edge: fi,
  EffectSystem: Wr,
  EllipseShape: ii,
  Endpoint: Ft,
  EndpointFixedName: Fe,
  EndpointFixedPoint: Vt,
  EndpointFunction: Vs,
  EndpointNearest: Xe,
  EndpointSegment: ux,
  EventNames: pt,
  EventTarget: Tt,
  FlexionalLink: Le,
  FoldLink: nx,
  FontInfo: Se,
  FontUtil: ri,
  ForceDirectLayout: Ic,
  Graph: ix,
  GraphSystem: m0,
  HandlerLayer: Gs,
  HtmlImage: Bc,
  ImageUtil: Ht,
  InputEvent: V,
  InputSystem: ki,
  InputTextfield: Wc,
  Intersect: Js,
  Keyboard: Mr,
  Layer: ft,
  LayerLocalDeep: Tx,
  Layout: Ox,
  LayoutSystem: Hr,
  LineShape: B0,
  LinearGradient: l0,
  Link: R,
  LinkHelper: sx,
  Node: M,
  NodeHelper: qt,
  OBB: Zt,
  PI2: V0,
  Path: rc,
  PathLink: Oi,
  Point: E,
  PopupMenu: Nc,
  Position: k,
  PositionInvertMap: Kr,
  RadialGradient: Z0,
  RatioNode: gi,
  RectDefaultPositions: de,
  RectShape: vx,
  Rectangle: z,
  ResourceLoader: et,
  Runtime: Cr,
  SelectedGroup: Dr,
  SerializerSystem: n0,
  Shape: T,
  Stage: Pi,
  StageLocalDeep: Ki,
  StageMode: Ct,
  Style: ct,
  StylePattern: Oe,
  StyleSystem: Rr,
  TextNode: ge,
  Theme: O0,
  TipNode: R0,
  TipShape: $e,
  Toolbar: Ar,
  Tooltip: Rc,
  TopoEvent: _o,
  TopoPainter: Tc,
  Transform: lx,
  VERSION: _i,
  Vertext: r0,
  VideoNode: W0,
  clickEvent: hr,
  convertToEndpoint: Jt,
  copyKeyboardEvent: ai,
  copyMouseEvent: yo,
  dblclickEvent: ur,
  dragEndEvent: gr,
  dragEvent: pr,
  dropEvent: yr,
  dropoutEvent: _r,
  dropoverEvent: br,
  getClass: mi,
  getEndpointNormal: ui,
  getLineIntersectPoint: fn,
  getLineIntersectPoints: wr,
  getNearestAnchorOnObjects: Mo,
  getNearestPointOnLines: wi,
  getNearestPointOnObjectsOBB: Ao,
  getNearestPositionName: Do,
  getParallelLine: Oo,
  getParallelLines: Po,
  getPointByEndpoint: s0,
  getRectPositionDirection: Qi,
  getRectPositionRotate: $i,
  isHorizontal: kx,
  jtopo: kt,
  mousedownEvent: or,
  mousedragEvent: wo,
  mouseenterEvent: dr,
  mousemoveEvent: cr,
  mouseoutEvent: fr,
  mouseoverEvent: lr,
  mouseupEvent: ar,
  newInstance: hx,
  pointProjectToLine: Co,
  randomColor: Ec,
  range: hi,
  regClass: $r,
  selectedEvent: mr,
  setProto: f,
  touchendEvent: rr,
  touchmoveEvent: ci,
  touchstartEvent: ir,
  unselectedEvent: Ir,
  util: Et,
  vec2: B
}, Symbol.toStringTag, { value: "Module" })), Fc = Xs;
(function(n, e) {
  const t = Xs, x = n();
  for (; []; )
    try {
      if (parseInt(t(423)) / 1 * (parseInt(t(428)) / 2) + parseInt(t(427)) / 3 + -parseInt(t(421)) / 4 + -parseInt(t(425)) / 5 * (parseInt(t(430)) / 6) + parseInt(t(426)) / 7 + -parseInt(t(422)) / 8 + parseInt(t(431)) / 9 * (parseInt(t(429)) / 10) === e)
        break;
      x.push(x.shift());
    } catch {
      x.push(x.shift());
    }
})(Ys, 607805);
function Xs(n, e) {
  const t = Ys();
  return Xs = function(x, s) {
    return x = x - 421, t[x];
  }, Xs(n, e);
}
let qi = kt;
Object[Fc(424)](qi, Hc), delete qi.jtopo;
function Ys() {
  const n = ["115585oNrIXb", "703724WwbPvJ", "2479491xkmJMr", "3218FsQidb", "26370ZnxpMz", "54qczXnL", "1125nsaOWy", "2803588TpAUcR", "4818584VkssrQ", "536MzINtE", "assign"];
  return Ys = function() {
    return n;
  }, Ys();
}
export {
  di as AENode,
  zr as Animation,
  Nr as AnimationSystem,
  H0 as ArcLink,
  N0 as ArcShape,
  D0 as ArrowShape,
  ie as AutoFoldLink,
  z0 as BezierCurveShape,
  Y0 as BezierLink,
  xx as CircleNode,
  si as CircleShape,
  N as Collection,
  Ve as Config,
  Zr as Cursor,
  x0 as CurveLink,
  j0 as CurveShape,
  _e as Debug,
  Sr as DefaultDarkTheme,
  Te as DefaultLightTheme,
  cx as DefaultZIndexs,
  o0 as Direction,
  C as DisplayObject,
  Ye as DomUtil,
  fi as Edge,
  Wr as EffectSystem,
  ii as EllipseShape,
  Ft as Endpoint,
  Fe as EndpointFixedName,
  Vt as EndpointFixedPoint,
  Vs as EndpointFunction,
  Xe as EndpointNearest,
  ux as EndpointSegment,
  pt as EventNames,
  Tt as EventTarget,
  Le as FlexionalLink,
  nx as FoldLink,
  Se as FontInfo,
  ri as FontUtil,
  Ic as ForceDirectLayout,
  ix as Graph,
  m0 as GraphSystem,
  Gs as HandlerLayer,
  Bc as HtmlImage,
  Ht as ImageUtil,
  V as InputEvent,
  ki as InputSystem,
  Wc as InputTextfield,
  Js as Intersect,
  Mr as Keyboard,
  ft as Layer,
  Tx as LayerLocalDeep,
  Ox as Layout,
  Hr as LayoutSystem,
  B0 as LineShape,
  l0 as LinearGradient,
  R as Link,
  sx as LinkHelper,
  M as Node,
  qt as NodeHelper,
  Zt as OBB,
  V0 as PI2,
  rc as Path,
  Oi as PathLink,
  E as Point,
  Nc as PopupMenu,
  k as Position,
  Kr as PositionInvertMap,
  Z0 as RadialGradient,
  gi as RatioNode,
  de as RectDefaultPositions,
  vx as RectShape,
  z as Rectangle,
  et as ResourceLoader,
  Cr as Runtime,
  Dr as SelectedGroup,
  n0 as SerializerSystem,
  T as Shape,
  Pi as Stage,
  Ki as StageLocalDeep,
  Ct as StageMode,
  ct as Style,
  Oe as StylePattern,
  Rr as StyleSystem,
  ge as TextNode,
  O0 as Theme,
  R0 as TipNode,
  $e as TipShape,
  Ar as Toolbar,
  Rc as Tooltip,
  _o as TopoEvent,
  Tc as TopoPainter,
  lx as Transform,
  _i as VERSION,
  r0 as Vertext,
  W0 as VideoNode,
  hr as clickEvent,
  Jt as convertToEndpoint,
  ai as copyKeyboardEvent,
  yo as copyMouseEvent,
  ur as dblclickEvent,
  gr as dragEndEvent,
  pr as dragEvent,
  yr as dropEvent,
  _r as dropoutEvent,
  br as dropoverEvent,
  mi as getClass,
  ui as getEndpointNormal,
  fn as getLineIntersectPoint,
  wr as getLineIntersectPoints,
  Mo as getNearestAnchorOnObjects,
  wi as getNearestPointOnLines,
  Ao as getNearestPointOnObjectsOBB,
  Do as getNearestPositionName,
  Oo as getParallelLine,
  Po as getParallelLines,
  s0 as getPointByEndpoint,
  Qi as getRectPositionDirection,
  $i as getRectPositionRotate,
  kx as isHorizontal,
  kt as jtopo,
  or as mousedownEvent,
  wo as mousedragEvent,
  dr as mouseenterEvent,
  cr as mousemoveEvent,
  fr as mouseoutEvent,
  lr as mouseoverEvent,
  ar as mouseupEvent,
  hx as newInstance,
  Co as pointProjectToLine,
  Ec as randomColor,
  hi as range,
  $r as regClass,
  mr as selectedEvent,
  f as setProto,
  rr as touchendEvent,
  ci as touchmoveEvent,
  ir as touchstartEvent,
  Ir as unselectedEvent,
  Et as util,
  B as vec2
};
