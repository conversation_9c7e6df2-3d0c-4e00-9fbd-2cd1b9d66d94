(function(p,x0){typeof exports=="object"&&typeof module<"u"?x0(exports):typeof define=="function"&&define.amd?define(["exports"],x0):(p=typeof globalThis<"u"?globalThis:p||self,x0(p.jtopo={}))})(this,function(p){"use strict";const x0=U0;(function(x,e){const t=U0,n=x();for(;[];)try{if(-parseInt(t(278))/1*(-parseInt(t(280))/2)+-parseInt(t(288))/3+-parseInt(t(276))/4+-parseInt(t(279))/5+parseInt(t(287))/6+-parseInt(t(283))/7*(parseInt(t(284))/8)+-parseInt(t(277))/9*(-parseInt(t(286))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(kn,590047);function lo(x,e,t){return x[0]=e[0]+t[0],x[1]=e[1]+t[1],x}function or(x,e,t){return x[0]=e[0]*t,x[1]=e[1]*t,x}function ho(x,e,t){return x[0]=e[0]*t,x[1]=e[1]*t,x}function U0(x,e){const t=kn();return U0=function(n,s){return n=n-276,t[n]},U0(x,e)}function uo(x,e){return x[0]=-e[0],x[1]=-e[1],x}function fo(x,e){let t=Math.sqrt(e[0]*e[0]+e[1]*e[1]);return t==0?(x[0]=0,x[0]=0,x):(x[0]=e[0]/t,x[1]=e[1]/t,x)}function kn(){const x=["2546607gsayrX","4521036rflthr","33859629BveJUc","39461xWArad","4978775IxtGzL","10qgZYTI","multiplyC","projection","971782TnWOMI","56jgmLTB","sqrt","10yVGOur","3463356FKHLLU"];return kn=function(){return x},kn()}function po(x){return Math[U0(285)](x[0]*x[0]+x[1]*x[1])}function ar(x,e){return x[0]*e[0]+x[1]*e[1]}function go(x,e,t){let n=ar(e,t);return or(x,t,n),x}class B{}B[x0(281)]=or,B.scale=ho,B.len=po,B.negate=uo,B.add=lo,B.normalize=fo,B.dot=ar,B[x0(282)]=go;const st=On;(function(x,e){const t=On,n=x();for(;[];)try{if(-parseInt(t(199))/1+parseInt(t(212))/2+parseInt(t(194))/3*(parseInt(t(204))/4)+parseInt(t(208))/5*(-parseInt(t(197))/6)+-parseInt(t(205))/7+parseInt(t(216))/8*(parseInt(t(217))/9)+-parseInt(t(215))/10*(parseInt(t(201))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Pn,201175);const dt={DefaultFont:st(196)},se={PointClosestEpsilon:.01,DefaultLightName:"DefaultLight",DefaultDarkName:"DefaultDark",flatten:!![]},s0=2*Math.PI,Sn="2.5.3_试用版";function Pn(){const x=["257579HxTnBK","ctrlPoint1","groupdragend","4455dAFAZv","edit","ctrlPoint","fullWindow","753928zVWnRI","s-resize","zoomAfter","100ceSyeu","2170928eDpAtE","9bkwdhc","mid2","12FUesWT","move","10px sans-serif","738SxSoCx","vertical","147753IFDXuv","begin","266860ytvaTd","drag","zoom","89588CgRpNm"];return Pn=function(){return x},Pn()}const mt={drag:st(202),edit:st(209),normal:"normal",select:"select",view:"view"};function On(x,e){const t=Pn();return On=function(n,s){return n=n-193,t[n]},On(x,e)}const cr={auto:"auto",move:st(195),hand:"hand",crosshair:"crosshair",s_resize:st(213),n_resize:"n-resize",w_resize:"w-resize",e_resize:"e-resize"},Te={horizontal:"horizontal",vertical:st(198)},S={lt:"lt",ct:"ct",rt:"rt",lm:"lm",center:"center",rm:"rm",lb:"lb",cb:"cb",rb:"rb",nearest:"nearest",begin:st(200),end:"end",ctrlPoint:st(210),ctrlPoint1:st(206),ctrlPoint2:"ctrlPoint2",fold1:"fold1",fold2:"fold2",mid:"mid",mid1:"mid1",mid2:st(193),up:"up",down:"down",left:"left",right:"right"},lr={lt:S.rb,ct:S.cb,rt:S.lb,rm:S.lm,rb:S.lt,cb:S.ct,lb:S.rt,lm:S.rm},i0={HandlerLayerCanvas:99,FullWindowDom:1e3,Link:1,Node:2,EditorNewLink:3,IntersectPoint:999,NodeCtrlBox:1e3,LinkCtrlBox:1001},ui=0,Z0=1,it={zoom:st(203),resize:"resize",zoomAfter:st(214),fullWindow:st(211),modeChange:"modeChange",groupdrag:"groupdrag",groupdragend:st(207),selectObject:"selectObject"};function f(x){return(e,t)=>{e[t]=x}}const tt=En;(function(x,e){const t=En,n=x();for(;[];)try{if(parseInt(t(377))/1+parseInt(t(358))/2+parseInt(t(373))/3+-parseInt(t(365))/4+-parseInt(t(367))/5*(-parseInt(t(360))/6)+parseInt(t(374))/7*(-parseInt(t(371))/8)+-parseInt(t(369))/9*(parseInt(t(372))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ln,666317);function En(x,e){const t=Ln();return En=function(n,s){return n=n-353,t[n]},En(x,e)}function Ln(){const x=["concat","26216jKJAlV","7561270UHbPgd","3791541HOEiPt","2121jPOwTh","assert error betweenPoints","length","1237224sKnCZP","points.length < 2","distance","rotate","atan2","sqrt","log","offsetWithAngle","669486zKHrjH","forward","6tBdIQJ","toJSON","sin","interpolate","normalize","2523584SlcnVX","cos","4832920vEYmMm","abs","18pRsuxP"];return Ln=function(){return x},Ln()}class C{constructor(e=0,t=0){this.x=e,this.y=t}[tt(361)](){return{x:this.x,y:this.y}}static isLikePoint(e){const t=tt;return e instanceof C?!![]:Object.keys(e)[t(376)]==2&&e.x!=null&&e.y!=null}static looksSame(e,t,n){if(e===t)return!![];let s=Math.abs(t.x-e.x),i=Math.abs(t.y-e.y);return n==null&&(n=.01),s<n&&i<n}static middle(e,t){return{x:(t.x+e.x)/2,y:(t.y+e.y)/2}}static getAngle(e,t){return Math[tt(354)](t.y-e.y,t.x-e.x)}static[tt(353)](e,t,n,s,i){const r=tt;return{x:(e-n)*Math.cos(i)-(t-s)*Math.sin(i)+n,y:(e-n)*Math[r(362)](i)+(t-s)*Math.cos(i)+s}}static rotatePoint(e,t,n){const s=tt;return{x:(e.x-t.x)*Math.cos(n)-(e.y-t.y)*Math.sin(n)+t.x,y:(e.x-t.x)*Math.sin(n)+(e.y-t.y)*Math[s(366)](n)+t.y}}static[tt(379)](e,t,n,s){let i=n-e,r=s-t;return Math.sqrt(i*i+r*r)}static distancePoint(e,t){const n=tt;let s=t.x-e.x,i=t.y-e.y;return Math[n(355)](s*s+i*i)}static mergeClosestPoints(e,t=se.PointClosestEpsilon){const n=tt;let s=[e[0]];for(let i=1;i<e.length-1;i++){let r=s[s.length-1],o=e[i],a=e[i+1];if(o.x===a.x&&o.y===a.y)continue;let c=B.normalize([],[o.x-r.x,o.y-r.y]),l=B[n(364)]([],[a.x-o.x,a.y-o.y]);Math[n(368)](c[0]-l[0])<t&&Math[n(368)](c[1]-l[1])<t||s.push(o)}return s.push(e[e.length-1]),s}static[tt(359)](e,t,n){const s=tt;let i=Math[s(354)](t.y-e.y,t.x-e.x);return{x:e.x+n*Math.cos(i),y:e.y+n*Math[s(362)](i)}}static[tt(357)](e,t,n){return typeof n=="number"&&(n={x:Math.cos(t)*n,y:Math.sin(t)*n}),{x:e.x+n.x,y:e.y+n.y}}static createPoints(e,t,n=1,s=![]){const i=s?[e]:[],r=s?n-1:n;let o=e;for(var a=0;a<r;a++){const c={x:o.x+t[0],y:o.y+t[1]};i.push(c),o=c}return i}static createPointsBidirectional(e,t,n){const s=tt;if(n==0)return[];const i=[-t[0],-t[1]];if(n%2==0){const a=[t[0]/2,t[1]/2],c={x:e.x-a[0],y:e.y-a[1]},l={x:e.x+a[0],y:e.y+a[1]},h=C.createPoints(c,i,n/2,!![]),u=C.createPoints(l,t,n/2,!![]);return h[s(370)](u)}const r=C.createPoints(e,i,(n-1)/2+1,!![]),o=C.createPoints(e,t,(n-1)/2,![]);return r.concat(o)}static interpolate(e,t,n){let s=(1-n)*e.x,i=(1-n)*e.y,r=n*t.x,o=n*t.y;return{x:s+r,y:i+o}}static getDistanceSum(e){const t=tt;let n=e.length;if(n<2)throw new Error(t(378));let s=e[0],i=e[n-1];if(e[t(376)]==2)return C.distancePoint(s,i);let r=0;for(var o=1;o<n;o++)r+=C.distancePoint(e[o-1],e[o]);return r}static calculatePointOnMultiPointLine(e,t){const n=tt;let s=e[n(376)];if(e.length<2)throw new Error("points.length < 2");let i=e[0],r=e[e[n(376)]-1];if(e.length==2)return C.interpolate(i,r,t);if(t<0)return C[n(363)](e[0],e[1],t);if(t>1)return C.interpolate(e[e.length-2],e[e.length-1],t);let o=C.getDistanceSum(e),a=o*t,c=0;for(var l=1;l<s;l++){let h=C.distancePoint(e[l-1],e[l]);if(a>=c&&a<=c+h){let u=a-c,g=u/h;return C.interpolate(e[l-1],e[l],g)}c+=h}throw console[n(356)](e,t),new Error(n(375))}}const It=K0;function K0(x,e){const t=Tn();return K0=function(n,s){return n=n-184,t[n]},K0(x,e)}(function(x,e){const t=K0,n=x();for(;[];)try{if(-parseInt(t(201))/1+parseInt(t(202))/2+-parseInt(t(191))/3*(parseInt(t(188))/4)+parseInt(t(204))/5+parseInt(t(195))/6*(parseInt(t(198))/7)+parseInt(t(205))/8+-parseInt(t(189))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Tn,441876);function Tn(){const x=["width","18mEeRdD","point","scaleY","1257263lvPeoY","getTranslation","translate","555567vvAnsV","1180852KKgrzZ","rotate","1635200lumOfU","6584520moIHwz","length","getMatrix","skewY","setAbsolutePosition","17552cywjJh","7627527tiMaJc","push","297nrqiCk","points","identity"];return Tn=function(){return x},Tn()}class r0{constructor(e){const t=K0;this[t(194)]=1,this.height=1,this.rotation=0,this.m=e&&e.slice()||[1,0,0,1,0,0]}get skewX(){return this.m[1]}get[It(186)](){return this.m[2]}get scaleX(){return this.m[0]}get[It(197)](){return this.m[3]}get x(){return this.m[4]}get y(){return this.m[5]}copy(){const e=It;let t=new r0(this.m);return t[e(194)]=this.width,t.height=this.height,t.rotation=this.rotation,t}[It(193)](){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}transform(e,t,n,s,i,r){let o=[e,t,n,s,i,r],a=this.m[0]*o[0]+this.m[2]*o[1],c=this.m[1]*o[0]+this.m[3]*o[1],l=this.m[0]*o[2]+this.m[2]*o[3],h=this.m[1]*o[2]+this.m[3]*o[3],u=this.m[0]*o[4]+this.m[2]*o[5]+this.m[4],g=this.m[1]*o[4]+this.m[3]*o[5]+this.m[5];this.m[0]=a,this.m[1]=c,this.m[2]=l,this.m[3]=h,this.m[4]=u,this.m[5]=g}[It(196)](e){let t=this.m;return{x:t[0]*e.x+t[2]*e.y+t[4],y:t[3]*e.y+t[1]*e.x+t[5]}}vec(e,t){let n=this.m;return e[0]=n[0]*t[0]+n[2]*t[1],e[1]=n[3]*t[1]+n[1]*t[0],e}[It(192)](e){const t=It;let n=[];for(var s=0;s<e[t(184)];s++){let i=e[s];n[t(190)](this[t(196)](i))}return n}translate(e,t){return this.m[4]+=this.m[0]*e+this.m[2]*t,this.m[5]+=this.m[1]*e+this.m[3]*t,this}translateTo(e,t){return this.m[4]=e,this.m[5]=t,this}scale(e,t){return this.m[0]*=e,this.m[1]*=e,this.m[2]*=t,this.m[3]*=t,this}getScale(){return{x:this.m[0],y:this.m[3]}}[It(203)](e){let t=Math.cos(e),n=Math.sin(e),s=this.m[0]*t+this.m[2]*n,i=this.m[1]*t+this.m[3]*n,r=this.m[0]*-n+this.m[2]*t,o=this.m[1]*-n+this.m[3]*t;return this.m[0]=s,this.m[1]=i,this.m[2]=r,this.m[3]=o,this}rotateTarget(e,t,n){const s=It;this[s(200)](t,n),this[s(203)](e),this.translate(-t,-n)}[It(199)](){return{x:this.m[4],y:this.m[5]}}multiply(e){let t=this.m,n=e.m,s=t[0]*n[0]+t[2]*n[1],i=t[1]*n[0]+t[3]*n[1],r=t[0]*n[2]+t[2]*n[3],o=t[1]*n[2]+t[3]*n[3],a=t[0]*n[4]+t[2]*n[5]+t[4],c=t[1]*n[4]+t[3]*n[5]+t[5];return this.m[0]=s,this.m[1]=i,this.m[2]=r,this.m[3]=o,this.m[4]=a,this.m[5]=c,this}invert(){let e=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),t=this.m[3]*e,n=-this.m[1]*e,s=-this.m[2]*e,i=this.m[0]*e,r=e*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),o=e*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=t,this.m[1]=n,this.m[2]=s,this.m[3]=i,this.m[4]=r,this.m[5]=o,this}[It(185)](){return this.m}[It(187)](e,t){let n=this.m[0],s=this.m[1],i=this.m[2],r=this.m[3],o=this.m[4],a=this.m[5],c=(n*(t-a)-s*(e-o))/(n*r-s*i),l=(e-o-i*c)/n;return this.translate(l,c)}}function Cn(){const x=["max","setTo","abs","bottom","5kptpDH","clone","min","union"," width:","3463480qJdpGn","612072qfAbGA","703296tjygUB","height","center","4206209FSzWwO","468567tacyTz","getRight","370FupWUH","28920hQwUXu","getBottom","width","aabb","42xxKirw","left","161622GXpvqk"];return Cn=function(){return x},Cn()}const ft=pt;(function(x,e){const t=pt,n=x();for(;[];)try{if(-parseInt(t(260))/1+-parseInt(t(263))/2*(parseInt(t(267))/3)+-parseInt(t(256))/4+-parseInt(t(274))/5*(parseInt(t(269))/6)+-parseInt(t(259))/7+-parseInt(t(254))/8+parseInt(t(255))/9*(parseInt(t(262))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Cn,608706);function pt(x,e){const t=Cn();return pt=function(n,s){return n=n-251,t[n]},pt(x,e)}class z{constructor(e=0,t=0,n=0,s=0){const i=pt;this.x=0,this.y=0,this[i(265)]=0,this[i(257)]=0,this.x=e,this.y=t,this.width=n,this[i(257)]=s}setTo(e=0,t=0,n=0,s=0){this.x=e,this.y=t,this.width=n,this.height=s}getRect(){return this}clone(){const e=pt;return new z(this.x,this.y,this.width,this[e(257)])}toString(){const e=pt;return"[x: "+this.x+" y:"+this.y+e(253)+this[e(265)]+" height:"+this[e(257)]+"]"}equals(e){const t=pt;return e.x==this.x&&e.y==this.y&&e[t(265)]==this.width&&e.height==this[t(257)]}containsRect(e){const t=pt;return e.x>this.x&&e.getRight()<this[t(261)]()&&e.y>this.y&&e.getBottom()<this[t(264)]()?!![]:![]}contains(e,t){const n=pt;return e>=this.x&&e<=this.x+this.width&&t>=this.y&&t<=this.y+this[n(257)]}isIntersectRect(e){const t=pt;return e.x>this[t(261)]()||e.y>this[t(264)]()?![]:e.getRight()<this.x||e.getBottom()<this.y?![]:!![]}getRight(){return this.x+this.width}getBottom(){return this.y+this.height}isEmpty(){return this.x===0&&this.y===0&&this.width===0&&this.height===0}setToEmpty(){this[pt(271)](0,0,0,0)}getCenter(){const e=pt;return{x:this.x+this.width/2,y:this.y+this[e(257)]/2}}toPoints(){return[{x:this.x,y:this.y},{x:this.right,y:this.y},{x:this.right,y:this.bottom},{x:this.x,y:this.bottom}]}static[ft(252)](e,t){const n=ft;let s=Math[n(251)](e.x,t.x),i=Math.min(e.y,t.y),r=Math[n(270)](e.getRight(),t[n(261)]()),o=Math[n(270)](e.getBottom(),t[n(264)]());return e.setTo(s,i,r-s,o-i),e}static unionRects(e){const t=ft;let n=e[0][t(275)]();for(let s=1;s<e.length;s++)n=z.union(n,e[s]);return n}static normal(e,t){const n=ft;let s=t.x-e.x,i=t.y-e.y;return Math.abs(s)>Math[n(272)](i)?[Math.sign(s),0]:[0,Math.sign(i)]}get left(){return this.x}set[ft(268)](e){this.x=e}get right(){return this.x+this.width}set right(e){this.x=e-this.width}get top(){return this.y}set top(e){const t=ft;this.y=e+this[t(257)]}get[ft(273)](){return this.y+this.height}set bottom(e){const t=ft;this.y=e-this[t(257)]}get[ft(258)](){const e=ft;return this.x+this[e(265)]*.5}set center(e){this.x=e-this.width*.5}get middle(){const e=ft;return this.y+this[e(257)]*.5}set middle(e){const t=ft;this.y=e-this[t(257)]*.5}}class Bt{constructor(){}contains(e,t){return this[ft(266)].contains(e,t)==![]?![]:!![]}static toAABB(e,t){let n=e[0],s={x:n.x,y:n.y},i={x:n.x,y:n.y},r={x:n.x,y:n.y},o={x:n.x,y:n.y};for(let a=1;a<e.length;a++){let c=e[a];c.x<s.x&&(s.x=c.x),c.x>i.x&&(i.x=c.x),c.y<r.y&&(r.y=c.y),c.y>o.y&&(o.y=c.y)}return t==null||t<=1?new z(s.x,r.y,i.x-s.x,o.y-r.y):new z(s.x-t,r.y-t,i.x-s.x+t+t,o.y-r.y+t+t)}}function di(x,e){var t=An();return di=function(n,s){n=n-132;var i=t[n];return i},di(x,e)}(function(x,e){for(var t=di,n=x();[];)try{var s=-parseInt(t(142))/1*(-parseInt(t(139))/2)+parseInt(t(135))/3*(-parseInt(t(133))/4)+parseInt(t(141))/5+-parseInt(t(140))/6*(-parseInt(t(134))/7)+parseInt(t(138))/8+parseInt(t(137))/9*(parseInt(t(136))/10)+-parseInt(t(132))/11;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(An,829803);function An(){var x=["8011sDYYwh","27003834gFmmnW","1808236hElGBq","7wEDLvz","3NBlKWK","27790zNsURI","2853RFEkrd","258064QXXZEr","354Ktwkhn","1766754XLUAbq","5555745QbmTCX"];return An=function(){return x},An()}function fi(x,e){var t=Mn();return fi=function(n,s){n=n-157;var i=t[n];return i},fi(x,e)}function Mn(){var x=["6672eokmdv","4cXGkAU","2749824zjqAfm","2115miqRCW","31624HtZXIY","544128BvhZNd","33073850EzVjdp","211kVkySe","5010865zRbtxC","951663WYRTsk"];return Mn=function(){return x},Mn()}(function(x,e){for(var t=fi,n=x();[];)try{var s=parseInt(t(158))/1*(-parseInt(t(161))/2)+parseInt(t(160))/3+parseInt(t(162))/4*(-parseInt(t(159))/5)+-parseInt(t(166))/6+-parseInt(t(163))/7+-parseInt(t(165))/8*(parseInt(t(164))/9)+parseInt(t(157))/10;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Mn,506062);const o0=Dn;(function(x,e){const t=Dn,n=x();for(;[];)try{if(-parseInt(t(446))/1*(-parseInt(t(453))/2)+-parseInt(t(458))/3+parseInt(t(443))/4*(-parseInt(t(447))/5)+parseInt(t(444))/6*(-parseInt(t(449))/7)+parseInt(t(459))/8*(-parseInt(t(456))/9)+parseInt(t(454))/10*(-parseInt(t(457))/11)+parseInt(t(460))/12===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Bn,113772);function Dn(x,e){const t=Bn();return Dn=function(n,s){return n=n-443,t[n]},Dn(x,e)}function Bn(){const x=["type","4496drFlpe","5000KmGHGd","length","18zuBtpU","3047NJfEEl","140604pjXuSO","545080jfJwYO","7462800PiYyyB","4Zlkgax","30Puowwf","listeners","1JSEJeK","650365hWtSwn","dispatchEvent","82131rdMmNf","removeEventListener","addEventListener"];return Bn=function(){return x},Bn()}class wt{constructor(){this.listeners={}}hasListener(e){return e in this.listeners}[o0(451)](e,t){!(e in this[o0(445)])&&(this.listeners[e]=[]),this.listeners[e].push(t)}[o0(450)](e,t){const n=o0;if(!(e in this[n(445)]))return;let s=this.listeners[e];for(var i=0,r=s[n(455)];i<r;i++)if(s[i]===t)return s.splice(i,1),this.removeEventListener(e,t)}[o0(448)](e){const t=o0;if(!(e.type in this.listeners))return;let n=this[t(445)][e[t(452)]];for(var s=0,i=n[t(455)];s<i;s++)n[s].call(this,e)}on(e,t){return this.addEventListener(e,t)}}function jn(x,e){const t=zn();return jn=function(n,s){return n=n-229,t[n]},jn(x,e)}function zn(){const x=["min","740yDdHSI","rotateNormaledPoints","MIN_SAFE_INTEGER","12313XrTGmy","length","25374NYHUKW","removeAt","notContains","360QIRtYo","327501ZWcEsh","getPointsRect","getPointsNormalization","1QWDpeC","1609988KJEpoD","union","forEach","width","slice","1384VJotaQ","2131638rQvBUJ","175835ezJduT","remove","splice","2793354XbJgOR","4092ZEhBZj"];return zn=function(){return x},zn()}const Lt=jn;(function(x,e){const t=jn,n=x();for(;[];)try{if(parseInt(t(230))/1*(-parseInt(t(241))/2)+-parseInt(t(237))/3+-parseInt(t(231))/4+parseInt(t(244))/5*(-parseInt(t(249))/6)+-parseInt(t(247))/7*(parseInt(t(236))/8)+parseInt(t(253))/9*(-parseInt(t(252))/10)+-parseInt(t(238))/11*(-parseInt(t(242))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(zn,700962);class R{static hasChild(e,t){return e.indexOf(t)!=-1}static[Lt(251)](e,t){return e.indexOf(t)==-1}static addAll(e,t){for(var n=0;n<t.length;n++)e.push(t[n]);return e}static[Lt(250)](e,t){return e.splice(t,1)}static[Lt(239)](e,t){const n=Lt;let s=e.indexOf(t);return s==-1?-1:(e[n(240)](s,1),s)}static[Lt(254)](e){const t=Lt;let n=Number.MAX_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,r=Number[t(246)];return e[t(233)](function(o){o.x<n&&(n=o.x),o.y<s&&(s=o.y),o.x>i&&(i=o.x),o.y>r&&(r=o.y)}),new z(n,s,i-n,r-s)}static getRectsNormalization(e,t){const n=Lt;let s=new z;s.setTo(e[0].x,e[0].y,e[0].width,e[0].height);for(let l=1;l<e.length;l++)z[n(232)](s,e[l]);let i=s[n(234)],r=s.height,o=s.x,a=s.y;return e.map(l=>{let u=l[n(234)]/i,g=l.height/r,y=(l.x-o)/i,m=(l.y-a)/r,I=u*t.width,b=g*t.height,k=t.x+y*t.width,E=t.y+m*t.height;return new z(k,E,I,b)})}static getMinMax(e){const t=Lt;let n={x:e[0].x,y:e[0].y},s={x:e[0].x,y:e[0].y};for(let i=1;i<e[t(248)];i++){let r=e[i];n.x=Math[t(243)](n.x,r.x),n.y=Math.min(n.y,r.y),s.x=Math.max(s.x,r.x),s.y=Math.max(s.y,r.y)}return{min:n,max:s}}static[Lt(229)](e,t=-.5,n=-.5,s=!![]){let i=R.getMinMax(e),r=i.min,o=i.max,a=o.x-r.x,c=o.y-r.y;if(a==c||s==![])return e.map(g=>({x:(g.x-r.x)/a+t,y:(g.y-r.y)/c+n}));if(a>c){let u=(a-c)*.5;return c=a,e.map(y=>({x:(y.x-r.x)/a+t,y:(y.y+u-r.y)/c+n}))}let l=(c-a)*.5;return a=c,e.map(u=>({x:(u.x+l-r.x)/a+t,y:(u.y-r.y)/c+n}))}static[Lt(245)](e,t,n=0,s=0){const i=Lt;if(t==0||t%6.283185307179586==0)return e[i(235)]();let r=e.map(o=>C.rotate(o.x,o.y,n,s,t));return R.getPointsNormalization(r)}}const jt=Ce;function Nn(){const x=["100092KBofWJ","whenAllLoaded","154rZWWkK","imageCache","addToCallbackList","1021980hfbYOf","callbackCache","1214823ccwgOz","allLoadedResolve","loadImageWithObj","naturalWidth","set","showImgLoadErrorLog","1268924hxCmBx","image load error: ","canceled","error","loadImage","onload","objCache","加载完成: ","complete","next","1026711wdVjqI","50EAHnRG","712748RpMSXI","cancelCallback","onerror","1llmQAw","clear","721632qpataj"];return Nn=function(){return x},Nn()}(function(x,e){const t=Ce,n=x();for(;[];)try{if(-parseInt(t(365))/1*(-parseInt(t(362))/2)+parseInt(t(375))/3+parseInt(t(381))/4+parseInt(t(373))/5+parseInt(t(368))/6*(-parseInt(t(370))/7)+-parseInt(t(367))/8+parseInt(t(360))/9*(-parseInt(t(361))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Nn,255339);class bo extends wt{constructor(){const e=Ce;super(),this[e(380)]=!![],this.imageCache=new Map,this.callbackCache=new Map,this.objCache=new Map}errorLog(e){this.showImgLoadErrorLog&&console.error(e)}[jt(369)](){let e=this;return new Promise((t,n)=>{const s=Ce;e[s(376)]=t})}whenAllImagesLoaded(e,t=![]){const n=jt,s=this[n(371)],i=this;function r(a){return new Promise((c,l)=>{const h=Ce;let u=new Image;u.src=a,t&&console.log("开始加载: ",a),u[h(355)]=function(){const g=h;t&&console.log(g(357),a),u[g(358)]&&u[g(378)]>0?(s[g(379)](a,u),c(u)):i.errorLog("图片加载失败: "+a)},u[h(364)]=function(){const g=h;i.errorLog(g(351)+a),c(null)}})}let o=e.map(r);return Promise.all(o)}clearCache(){const e=jt;this[e(371)].clear(),this.callbackCache[e(366)](),this.objCache.clear()}removeObject(e){this.objCache.delete(e)}[jt(363)](e){let n=this[jt(356)].get(e);n!=null&&(n.canceled=!![],this.objCache.delete(e))}onload(e,t){const n=jt,s=this,i=this[n(374)].get(e);if(i==null)return;for(let o=0;o<i.length;o++){let a=i[o];a[n(352)]!=!![]&&(a(t),a.cacheObj&&this.objCache.delete(a.cacheObj),a.next&&a[n(359)](t))}this.callbackCache.delete(e),t!=null&&this.imageCache[n(379)](e,t),this.hasLoaded=!![],this.dispatchEvent({type:"loaded",resource:t}),this.callbackCache.size==0&&this[n(376)]&&(s.allLoadedResolve(t),s.allLoadedResolve=null)}[jt(372)](e,t){if(t==null)return null;let n=this.callbackCache.get(e);n==null&&(n=[],this.callbackCache.set(e,n)),n.push(t)}[jt(377)](e,t,n){const s=jt;let i=this[s(356)].get(e);return i!=null&&(i.canceled=!![]),n.cacheObj=e,n.canceled=null,this.objCache.set(e,n),this[s(354)](t,n)}loadImage(e,t){const n=jt;let s=this,i=this[n(374)].get(e),r=i!=null;if(s.lastResource=e,this.addToCallbackList(e,t),r)return;let o=this.imageCache.get(e);if(o!=null){this.onload(e,o);return}setTimeout(function(){const a=n;let c=new Image;c.src=e,c.addEventListener("load",l=>{s.onload(e,c)}),c[a(364)]=function(){console[a(353)]("Image load error: "+e),s.onload(e,null)}},1)}}function Ce(x,e){const t=Nn();return Ce=function(n,s){return n=n-351,t[n]},Ce(x,e)}const $=new bo;function a0(x,e){const t=Rn();return a0=function(n,s){return n=n-352,t[n]},a0(x,e)}(function(x,e){const t=a0,n=x();for(;[];)try{if(parseInt(t(352))/1+parseInt(t(366))/2*(-parseInt(t(363))/3)+parseInt(t(356))/4+-parseInt(t(354))/5+parseInt(t(358))/6*(parseInt(t(353))/7)+-parseInt(t(362))/8+parseInt(t(355))/9*(parseInt(t(364))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Rn,679581);function Rn(){const x=["4049415dKkOAs","715671xJraSW","3292020iBxFrO","name","30oytray","className","concat","ArrowNode","4156352vYNJEm","2991171osNyQz","10MnQNov","Node","2vHcDvg","842361kwUbLx","1765652nbhUVh"];return Rn=function(){return x},Rn()}function hr(x,e,t){return typeof x=="function"?ur(x[a0(357)],x,e):ur(x,e,t)}function ur(x,e,t){const n=a0;if(dt[x]!=null)throw new Error("class already reg, name:"+x);const s=e.prototype;t!=null&&t.length>0&&(s.serializers=s.serializers[n(360)](t)),Object.defineProperties(s,{className:{writable:!![]}}),s[n(359)]=x,Object.defineProperties(s,{className:{writable:![]}}),dt[x]=e}function Wn(x){const e=a0;let t=dt[x];if(t==null)throw(x==e(361)||x=="ShapeNode")&&(t=dt[e(365)]),new Error("class not exist name:"+x);return t}function c0(x,e){let t,n=Wn(x);return t=new n,e&&Object.assign(t,e),t}const O=h0;(function(x,e){const t=h0,n=x();for(;[];)try{if(parseInt(t(375))/1+parseInt(t(397))/2*(parseInt(t(369))/3)+parseInt(t(367))/4*(-parseInt(t(399))/5)+parseInt(t(378))/6+-parseInt(t(406))/7+-parseInt(t(400))/8+parseInt(t(416))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Hn,785454);function Hn(){const x=["Arrow","Ellipse","6224632oTgPbA","isClosed","3GTzheR","TipShape","width","_computedStyle","draw","rect","653331ObhUAg","anchorPoints","ctrlPoint1","3593472tscJkj","begin","step","height","BezierCurve","ArrowShape","forEach","points","Polygon","circlePoints","atan2","className","tip","moveTo","pointCount","CurveShape","cos","fromPoints","scale","1350084KGHoGD","beginPath","5iVPajQ","8735856ZoCaFx","end","normalPoints","Arc","lineTo","center is zero vector","3277897gSEUlR","Curve","anticlockwise","push","CircleShape","innerGrid","length","arc","closePath","rotate","17771220nIfcbe","distancePoint","BezierCurveShape","prototype","dirty"];return Hn=function(){return x},Hn()}var yo=Object.defineProperty,_o=Object.getOwnPropertyDescriptor,H=(x,e,t,n)=>{for(var s=n>1?void 0:n?_o(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&yo(e,t,s),s};const rt=class{constructor(x){this.isClosed=!![],this.points=x}updatePoints(x){this.points=x,this.dirty=!![]}toJSON(){const x=h0;let e={className:this[x(389)]};return e[x(385)]=this[x(385)],e.isClosed=this.isClosed,e}clone(){return new rt(this.points.slice())}static fromJSON(x){const e=h0;let t=Wn(x[e(389)]),n=new t(x[e(385)]);return n.isClosed=x.isClosed,n}static[O(395)](x,e=!![]){let t=R.getPointsNormalization(x,-.5,-.5,e);return new rt(t)}static[O(402)](x,e=!![]){return R.getPointsNormalization(x,-.5,-.5,e)}draw(x,e,t){const n=O;t=t||this.points;let s=t[0];x.moveTo(s.x,s.y);for(let r=1;r<t.length-1;r++){if(t[r].x===s.x&&t[r].y===s.y){s=t[r];continue}x.lineTo(t[r].x,t[r].y),s=t[r]}let i=t[t[n(412)]-1];x.lineTo(i.x,i.y),this.isClosed&&x.closePath()}rotate(x){const e=O;return this.points=R.rotateNormaledPoints(this.points,x),this[e(420)]=!![],this}[O(396)](x,e){const t=O;return this[t(385)].forEach(n=>{n.x*=x,n.y*=e}),this[t(385)]=R.getPointsNormalization(this.points),this[t(420)]=!![],this}static Scale(x,e){return function(n){return n.x*=x,n.y*=e,n}}skew(x,e){return this.points.forEach(t=>{let n=t.x,s=t.y;t.x=n+s*e,t.y=s+n*x}),this.points=R.getPointsNormalization(this.points),this.dirty=!![],this}static pointToSize(x,e,t,n=![]){return n==!![]&&e!=t&&(e>t?e=t:t=e),x.map(i=>({x:i.x*e,y:i.y*t}))}static polygon(x=3,e=0){const t=O;let n=[],s=2*Math.PI/x;for(var i=0;i<x;i++){let o=Math[t(394)](e+i*s),a=Math.sin(e+i*s);n.push({x:o,y:a})}return rt[t(395)](n)}static parallelogram(x=.2){let e=[{x,y:0},{x:1,y:0},{x:1-x,y:1},{x:0,y:1}];return rt.fromPoints(e)}static cos(x){const e=O;x[e(379)]=x.begin|0,x.end=x[e(401)]|s0;let t=[];if(x.pointCount==null){x.step=x.step|.2;for(let n=x.begin;n<=x.end;n+=x.step){let s=n;t.push({x:s,y:Math.cos(s)})}}else{x.step=(x.end-x.begin)/x[e(392)];for(let n=0;n<x[e(392)];n++){let s=(n+1)*x.step;t.push({x:s,y:Math.cos(s)})}}return rt.fromPoints(t)}static circle(x){const e=O;let t=rt.circlePoints(x);return rt[e(395)](t)}static[O(387)](x){const e=O;x.begin=x.begin||0,x[e(401)]=x.end||s0;let t=[];if(x.pointCount==null){x.step=x.step|.2;for(let n=x.begin;n<=x.end;n+=x[e(380)]){let s=n;t[e(409)]({x:Math.sin(s),y:Math.cos(s)})}}else{let n=(x.end-x[e(379)])/x[e(392)];for(let s=0;s<x[e(392)];s++){let i=s*n;t.push({x:Math.sin(i),y:Math.cos(i)})}}return t}static fn(x){const e=O;let t=x();return rt[e(395)](t)}static outerGrid(x,e){const t=O;let n=[],s=1/(e-1),i=1/(x-1);for(let r=0;r<x;r++)for(let o=0;o<e;o++){let a={y:r*i-.5,x:o*s-.5};n[t(409)](a)}return new rt(n)}static[O(411)](x,e){let t=[],n=1/(e+1),s=1/(x+1),i=n,r=s;for(let o=0;o<x;o++)for(let a=0;a<e;a++){let c={x:i+a*n-.5,y:r+o*s-.5};t.push(c)}return new rt(t)}};let A=rt;A.Damond=(()=>{let x=[{x:-.5,y:0},{x:0,y:-.5},{x:.5,y:0},{x:0,y:.5}];return new rt(x)})(),A.Triangle=(()=>{let x=[{x:-.5,y:-.5},{x:.5,y:0},{x:-.5,y:.5}];return new rt(x)})(),A[O(390)]=(()=>{let x=[{x:0,y:0},{x:1,y:0},{x:1,y:.8},{x:.8,y:.8},{x:.5,y:1},{x:.2,y:.8},{x:0,y:.8},{x:0,y:0}];return rt.fromPoints(x)})(),H([f("Shape")],A.prototype,"className",2),H([f(["ct","cb","lm","rm"])],A.prototype,"anchorPoints",2),H([f([O(415),"lt","lb","rt","rb"])],A.prototype,"ctrlPoints",2),H([f(!![])],A.prototype,O(368),2),H([f(!![])],A[O(419)],"isUnit",2),H([f(![])],A.prototype,O(420),2),A[O(386)]=A.polygon;class l0 extends A{constructor(e=[{x:-.5,y:-.5},{x:.4,y:0},{x:-.5,y:.5},{x:-.5,y:0}]){super(e)}draw(e,t,n){const s=O;n=n||this[s(385)],e[s(391)](n[0].x,n[0].y),e.lineTo(n[1].x,n[1].y),e.lineTo(n[2].x,n[2].y),e.lineCap="square";let i=t[s(372)].lineWidth||1,r=i*.5/t[s(371)];r>1&&(r=1),e[s(391)](n[3].x-r,n[3].y),e[s(404)](n[1].x-r,n[1].y)}}H([f(O(383))],l0[O(419)],"className",2),H([f(![])],l0.prototype,O(368),2),dt.ArrowShape=l0,A[O(421)]=new l0,A.ArrowShape=A.Arrow;function h0(x,e){const t=Hn();return h0=function(n,s){return n=n-367,t[n]},h0(x,e)}class Q0 extends A{constructor(e=[{x:-.5,y:-.5},{x:.5,y:-.5},{x:.5,y:.5},{x:-.5,y:.5}]){super(e)}[O(373)](e,t){e[O(374)](-.5,-.5,1,1)}}H([f("RectShape")],Q0.prototype,"className",2),dt.RectShape=Q0,A.Rect=new Q0,A.RectShape=A.Rect;class Fn extends A{constructor(e=[{x:0,y:0}]){super(e)}draw(e,t){const n=O;let s=this.points[0];e[n(413)](s.x,s.y,.5,0,s0)}}H([f(O(410))],Fn[O(419)],O(389),2),A.Circle=new Fn;class Xn extends A{constructor(e=[{x:0,y:0},{x:-.5,y:0},{x:-.5,y:0}]){super(e)}draw(e,t){const n=O;let s=this[n(385)][0],i=this.points[1],r=this.points[2],o=C[n(417)](s,i),a=C.distancePoint(s,r);e.ellipse(s.x,s.y,o,a,0,0,s0)}}H([f("EllipseShape")],Xn[O(419)],"className",2),A[O(422)]=new Xn;class u0 extends A{constructor(e=[]){const t=O;super(e),this[t(368)]=![]}}H([f("LineShape")],u0.prototype,"className",2),H([f(["begin","end"])],u0.prototype,"anchorPoints",2),H([f(["begin","end"])],u0[O(419)],"ctrlPoints",2),A.Line=new u0;class d0 extends A{constructor(e=[]){super(e),this.isClosed=![]}draw(e,t,n){let s=n[0],i=n[1],r=n[2];e.moveTo(s.x,s.y),e.quadraticCurveTo(i.x,i.y,r.x,r.y),this.isClosed&&e.closePath()}}H([f(O(393))],d0.prototype,"className",2),H([f(["begin","end"])],d0[O(419)],"anchorPoints",2),H([f(["begin","end"])],d0.prototype,"ctrlPoints",2),A[O(407)]=new d0;class f0 extends A{constructor(e=[]){super(e),this.isClosed=![]}draw(e,t,n){const s=O;let i=n[0],r=n[1],o=n[3],a=n[4];e[s(398)](),e[s(391)](i.x,i.y),e.bezierCurveTo(r.x,r.y,o.x,o.y,a.x,a.y),this[s(368)]&&e[s(414)]()}}H([f(O(418))],f0.prototype,O(389),2),H([f([O(379),"end"])],f0.prototype,O(376),2),H([f(["begin","end",O(377),"ctrlPoint2"])],f0.prototype,"ctrlPoints",2),A[O(382)]=new f0;class p0 extends A{constructor(e=[]){super(e),this.isClosed=![]}draw(e,t,n){const s=O;let i=n[0],r=n[n.length-1];if(t&&t.direction==s(408)){let o=Math[s(388)](i.y-r.y,i.x-r.x);e[s(413)](0,0,.5,o,o+Math.PI,!![])}else{let o=Math.atan2(i.y-r.y,i.x-r.x);e.arc(0,0,.5,o,o+Math.PI,![])}this[s(368)]&&e.closePath()}}H([f("ArcShape")],p0.prototype,O(389),2),H([f(["begin",O(401)])],p0.prototype,O(376),2),H([f(["begin","end"])],p0.prototype,"ctrlPoints",2),A[O(403)]=new p0;const mo=A.outerGrid(3,3)[O(385)],Io=[S.lt,S.ct,S.rt,S.lm,S.center,S.rm,S.lb,S.cb,S.rb],qt={};Io[O(384)]((x,e)=>{qt[x]=mo[e]}),qt[S.nearest]=function(){return this.nearest};function pi(x){const e=O;if(x=="center")throw new Error(e(405));let t=qt[x];return B.normalize([],[t.x,t.y])}function gi(x){let e=qt[x];return Math.atan2(e.y,e.x)}class Ae extends A{constructor(e=[]){const t=O;super(e),this[t(368)]=!![]}draw(e,t){const n=O;let s=t,i=-s.width*.5,r=-s[n(381)]*.5,o=s[n(371)],a=s.arrowsSize,c=s.height-a,l=0;e.moveTo(i,r),e.lineTo(i+o,r),e.lineTo(i+o,r+c),e.lineTo(l+(a-2),r+c),e.lineTo(l,r+c+a),e.lineTo(l-(a-2),r+c),e.lineTo(i,r+c),e.lineTo(i,r)}}H([f(O(370))],Ae.prototype,O(389),2),H([f(![])],Ae[O(419)],"isUnit",2),H([f([])],Ae.prototype,"anchorPoints",2),H([f([])],Ae.prototype,"ctrlPoints",2),function(x,e){const t=Jn,n=x();for(;[];)try{if(-parseInt(t(437))/1+parseInt(t(445))/2*(-parseInt(t(447))/3)+-parseInt(t(448))/4+parseInt(t(440))/5+-parseInt(t(443))/6+parseInt(t(442))/7+parseInt(t(438))/8===e)break;n.push(n.shift())}catch{n.push(n.shift())}}(Yn,498349);function Yn(){const x=["_afterFromJSON","push","begin","styles","Shapes","610749ijTHXu","26450248tRDRFY","forEach","911825Lhvmwh","get","1222823hbHdRy","5826096bABrUj","DisplayObjects","198634gmlJxV","hasOwnProperty","27bQboga","2757472DiIycj","userData","fromJSON"];return Yn=function(){return x},Yn()}function Jn(x,e){const t=Yn();return Jn=function(n,s){return n=n-435,t[n]},Jn(x,e)}function bi(x,e){const t=Jn;let n={},s={},i={},r=x.Roots||[0],o=x[t(444)]||[],a=x.Styles||[],c=x[t(436)]||[],l=x.Resources||[],h=x.CustomStyle||{};if(o.forEach(b=>{const k=t;if(b[k(446)](k(449))&&!b[k(446)]("data")){let E=b.userData;delete b.userData,b.data=E}b.isLink&&b.path==null&&(b.path=[b.begin,b.end],delete b[k(453)],delete b.end)}),h[t(435)]==null){let b={},k=Object.keys(h);for(let E=0;E<k.length;E++){let D=k[E];D.startsWith(".")&&(b[D]=h[D],delete h[D])}h.styles=b}a.forEach(function(b,k){const E=t;s[k]=et[E(450)](b,l)}),c[t(439)](function(b,k){let D=A[t(450)](b);i[k]=D});let u=[];function g(b,k){const E=t;let D;return e&&(D=e[E(441)](b.id)),D==null&&(D=c0(b.className)),k<r.length&&u[E(452)](D),D[E(451)](b,l,s,i),n[k]=D,D}let y=o.map(g);y.forEach(b=>{b.removeAllChild()}),o.forEach((b,k)=>{let E=y[k];if(b.parent!=null){let D=n[b.parent];E.parent!=null&&E.removeFromParent(),D.addChild(E)}});let m=o.filter(b=>b.isLink),I=y.filter(b=>b.isLink);return m.forEach(function(b,k){I[k]._updateBeginEndAfterJson(b,n)}),u}const wo=Vn;(function(x,e){const t=Vn,n=x();for(;[];)try{if(parseInt(t(217))/1*(-parseInt(t(213))/2)+-parseInt(t(212))/3+parseInt(t(219))/4*(-parseInt(t(216))/5)+-parseInt(t(214))/6*(parseInt(t(220))/7)+parseInt(t(211))/8*(-parseInt(t(221))/9)+-parseInt(t(210))/10+parseInt(t(215))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Gn,859977);function Vn(x,e){const t=Gn();return Vn=function(n,s){return n=n-210,t[n]},Vn(x,e)}let $0=0,vt={};function Gn(){const x=["18891WRUkXU","2304080OefJWQ","1160SfeHxe","4427916ebwpwm","9554VCsqUw","837006GSjTKw","45528857laTQZQ","406355KJsLBC","88RmRpki","getMax","28lQCxZY","14LxARtO"];return Gn=function(){return x},Gn()}vt.next=function(){return++$0},vt.back=function(){return--$0},vt[wo(218)]=function(){return $0},vt.resetTo=function(x){$0=x},vt.compare=function(x){x!=null&&x>$0&&vt.resetTo(x+1)};const Ut=tn;(function(x,e){const t=tn,n=x();for(;[];)try{if(parseInt(t(524))/1*(parseInt(t(514))/2)+-parseInt(t(511))/3+-parseInt(t(510))/4+parseInt(t(501))/5*(parseInt(t(515))/6)+parseInt(t(504))/7+-parseInt(t(525))/8*(-parseInt(t(498))/9)+-parseInt(t(506))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(qn,517462);function tn(x,e){const t=qn();return tn=function(n,s){return n=n-497,t[n]},tn(x,e)}let vo=new Map;function qn(){const x=["Resources","catch","3367784YyPtHs","DisplayObjects","6440610msuhoW","styleSystem","serializers","compare","1547620wAIAxI","2005455VtosIP","copyAndToJSON","forEach","113742wNVpck","6PzAHYW","restoreToJson","get","length","CustomStyle","img","whenAllImagesLoaded","objectsToJSON","className","11vurwcA","2590232OSQbce","getState","9rqstmw","fromJson","stringify","3932205nAvwDc"];return qn=function(){return x},qn()}const dr=class{constructor(){}setNumberFixed(x){dr.numberFixed=x}[Ut(522)](x,e=![]){return yi(x,e)}jsonToObjects(x,e){return bi(x,e)}[Ut(512)](x){const e=Ut;let t=yi(x);return t.DisplayObjects[e(513)](n=>{n.id=void 0}),t}static getEmptyInstance(x){let t=vo[Ut(517)](x);return t==null&&(t=c0(x)),t}static getProtoDefaultProperties(x){let t=x[Ut(508)],n=Object.getPrototypeOf(x),s={};for(var i=0;i<t.length;i++){let r=t[i],o=n[r],a=x[r];a===o&&(s[r]=a)}return s}componentToObjects(x){const e=Ut;let t=JSON.parse(x);return t[e(505)].forEach(i=>{delete i.id}),bi(t)}objectsToComponent(x){const e=Ut;let t=this.copyAndToJSON(x);return JSON[e(500)](t)}fillByJson(x,e,t=![]){const n=Ut;let s=e;typeof e=="string"&&(s=JSON.parse(e));let i=this;function r(){const o=tn;let a=s.DisplayObjects,c=a[0];c[o(523)]=="Layer"&&(x.id=c.id),a[o(513)](l=>vt[o(509)](l.id)),s[o(519)]!=null&&x.stage[o(507)][o(499)](s),i[o(516)](x,s)}if(t){let o=s[n(502)],a=o.filter(c=>c.type==n(520)&&c.src!="canvas").map(c=>c.src);return new Promise((c,l)=>{const h=n;if(a[h(518)]==0){r(),c(!![]);return}$[h(521)](a).then(()=>{r(),c(!![])})[h(503)](()=>{l(![])})})}return r(),null}[Ut(497)](x){return yi([x])}restoreToJson(x,e){if(e!=null){let t=x.toIdMap();bi(e,t)}return e}};let Me=dr;Me.numberFixed=6;const zt=Un;(function(x,e){const t=Un,n=x();for(;[];)try{if(-parseInt(t(269))/1+-parseInt(t(276))/2*(-parseInt(t(258))/3)+-parseInt(t(260))/4+-parseInt(t(282))/5*(-parseInt(t(281))/6)+parseInt(t(267))/7+-parseInt(t(265))/8+parseInt(t(270))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Zn,315383);const fr=document.createElement("canvas"),Nt=class{static[zt(284)](x,e,t,n,s=zt(273),i="#151515"){const r=zt;let o="<svg xmlns='http://www.w3.org/2000/svg' width='"+x+"' height='"+e+r(283)+x+"' height='"+e+"' style='fill:"+s+";stroke:"+i+`;stroke-width:1;'/>
<g style='stroke:`+i+"; stroke-width:0.5;'>",a=e/t,c=x/n,l="";for(let u=1;u<=t;u++)l+="M 0 "+a*u+" H "+x+" ";o+="<path d='"+l+r(275);let h="";for(let u=1;u<=n;u++)h+="M "+c*u+" 0 V "+e+" ";return o+="<path d='"+h+"'/>",o+="</g></svg>",o=o.replace(/\n/g,""),o}static createGridImage(x=100,e=100,t=5,n=5,s,i){let o=Nt[zt(284)](x,e,t,n,s,i);return'url("'+Nt.svgToImageUrl(o)+'")'}static createLightGridImg(){const x=zt;return Nt.createGridImage(100,100,5,5,"rgb(255,255,255)",x(278))}static[zt(261)](){return Nt.createGridImage(100,100,5,5,"rgb(36,36,36)","rgb(20,20,20)")}static svgToImageUrl(x){return"data:image/svg+xml;charset=UTF-8,"+x}static canvasColorFilter(x,e){const t=zt,n=x.getContext("2d"),s=n[t(264)](0,0,x.width,x.height);let i=(o,a,c,l)=>[e[0]*o/255,e[1]*a/255,e[2]*c/255];typeof e=="function"&&(i=e);for(var r=0;r<s[t(266)].length;r+=4){let o=i(s.data[r],s.data[r+1],s.data[r+2],s.data[r+3]);s.data[r]=o[0],s.data[r+1]=o[1],s[t(266)][r+2]=o[2],o[t(277)]>3&&o[3]!=null&&(s.data[r+3]=o[3])}n.putImageData(s,0,0)}static colorFilter(x,e){const t=zt,n=Nt.canvas,s=Nt.ctx;n.width=x[t(259)],n[t(274)]=x.height,s.drawImage(x,0,0),Nt.canvasColorFilter(n,e);const i=new Image;return i[t(268)]=n.toDataURL(t(279)),i}static imageToBase64(x){const e=zt,t=Nt.canvas,n=Nt.ctx;return t[e(259)]=x[e(259)],t.height=x.height,n.drawImage(x,0,0),t.toDataURL()}static parseImgUrl(x){const e=zt;if(x.startsWith(e(280)))return x;if(x.startsWith("url(")){let t=x.match(/url\((['"]?)(.*?)\1\)/);if(t)return t[2];throw new Error("Image url error: "+x)}return x}};function Un(x,e){const t=Zn();return Un=function(n,s){return n=n-258,t[n]},Un(x,e)}let Tt=Nt;Tt.canvas=fr,Tt.ctx=fr.getContext("2d",{willReadFrequently:!![]});function Zn(){const x=["1469280hcZrIT",`'>
<rect x='0' y='0' width='`,"bgGrid","57ZpwXzk","width","2206284DdXdqe","createDarkGridImg","split","charCodeAt","getImageData","492160aOkfpH","data","1923754uqduhW","src","179813FLOPjn","1898010QsGYqv","match","onload","#242424","height","'/>","34602PkjqrP","length","rgb(240,240,240)","image/png","data:image/","6QsbRVy"];return Zn=function(){return x},Zn()}(function(x,e){const t=ie,n=x();for(;[];)try{if(parseInt(t(477))/1+-parseInt(t(478))/2+-parseInt(t(473))/3*(-parseInt(t(467))/4)+-parseInt(t(463))/5*(-parseInt(t(471))/6)+-parseInt(t(476))/7*(-parseInt(t(480))/8)+parseInt(t(470))/9+-parseInt(t(461))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Kn,535513);function ie(x,e){const t=Kn();return ie=function(n,s){return n=n-460,t[n]},ie(x,e)}function ko(x,e=![]){const t=ie;let n=L[t(464)](x[0].children);for(let y=1;y<x.length;y++){let m=L.flatten(x[y].children,I=>I.serializeable);n=n.concat(m)}let s=x[t(466)](n),i=new Map,r=new Map,o=new Map,a=[],c=[],l=new Map,h=[];function u(y,m){let I=l[y];if(I==null){let b=Oo(y,m,e),k={type:"img",src:b};h.push(k),I=h.length-1,l[y]=I}return I}return s.forEach((y,m)=>{const I=t;y.isNode&&y[I(465)]!=null&&u(y.imageSrc,y[I(469)]);let b=y.style;if(r[I(484)](b)==null){let E=a[I(462)];r.set(b,E);let D=b.toJSON(u);a.push(D)}let k=y[I(483)];if(y[I(479)]&&o[I(484)](k)==null){let E=c.length;o.set(k,E),c.push(k.toJSON())}i.set(y,m)}),{objects:s,objIndexMap:i,styleIndexMap:r,styles:a,resourcesIndexMap:l,resources:h,shapeIndexMap:o,shapes:c,indexImage:u}}function yi(x,e=![]){const t=ie,n={};n.version=Sn;const s=ko(x,e);let i=s.objects;if(n.Roots=x[t(485)]((r,o)=>o),n.Styles=s.styles,n.Shapes=s.shapes,n.Resources=s.resources,n.DisplayObjects=i[t(485)](function(r){return r.toJSON(s)}),x.length==1&&x[0][t(460)]){let r=x[0];n.CustomStyle=r[t(474)][t(472)].customStyleToJSON(s.indexImage)}return n}function Kn(){const x=["4935hKDYJd","380027aQdbYv","1444468JJRoDx","isNode","11184UxXPCL","number","isArray","shape","get","map","isLayer","15408600RaBNEk","length","170035nzscEX","flatten","imageSrc","concat","4pUKRCf","startsWith","image","4727205vrjxzR","6PJIgEY","styleSystem","2621214uAPFvi","stage","toJSON"];return Kn=function(){return x},Kn()}function pr(x,e,t){const n=ie;let s={},i=Me.getEmptyInstance(x.className);if(t)for(let r=0;r<t.length;r++){let o=t[r],a=x[o];s[o]=a}for(let r=0;r<e[n(462)];r++){let o=e[r],a=x[o];if(a!=i[o]){if(Array.isArray(a)&&Array[n(482)](i[o])){let c=i[o];if(!Po(c,a))s[o]=a;else continue}if(a==null){s[o]=a;continue}typeof a==n(481)&&Me.numberFixed!=null&&(a=So(a,Me.numberFixed)),a[n(475)]!=null?a=a.toJSON():C.isLikePoint(a)&&(a=new C(a.x,a.y)),s[o]=a}}return s}function So(x,e){if(Number.isInteger(x))return x;let t=x.toString();return t.length-t.indexOf(".")-1>e&&(x=x.toFixed(e),x=parseFloat(x)),x}function Po(x,e){const t=ie;if(x===e)return!![];if(x[t(462)]!=e.length)return![];for(let n=0;n<x.length;n++)if(x[n]!=e[n])return![];return!![]}function Oo(x,e,t=![]){const n=ie;return x==null?null:t==![]||x[n(468)]("data:image/")?x:Tt.imageToBase64(e)}const Qn=$n;(function(x,e){const t=$n,n=x();for(;[];)try{if(parseInt(t(456))/1+parseInt(t(455))/2+-parseInt(t(458))/3*(parseInt(t(443))/4)+-parseInt(t(449))/5*(parseInt(t(444))/6)+parseInt(t(442))/7*(-parseInt(t(457))/8)+-parseInt(t(448))/9+-parseInt(t(441))/10*(-parseInt(t(459))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ex,113323);function $n(x,e){const t=ex();return $n=function(n,s){return n=n-441,t[n]},$n(x,e)}const Eo=document.createElement(Qn(450)),en=Eo[Qn(446)]("2d");class tx{constructor(){}static measureTextSize(e,t){const n=Qn;en.font=t;const s=en[n(447)](e),i=s[n(451)]+s.actualBoundingBoxDescent;return{width:s[n(454)]+s[n(445)],height:i}}static measureTextArraySize(e,t){const n=Qn;en.font=t;let s=en[n(447)](e[0]);for(let o=0;o<e[n(453)];o++){let a=en[n(447)](e[o]);a[n(452)]>s.width&&(s=a)}const i=s.actualBoundingBoxAscent+s.actualBoundingBoxDescent;return{width:s.actualBoundingBoxRight+s.actualBoundingBoxLeft,height:i}}}function ex(){const x=["1032fnFRXM","726QwPuBk","143ZjYLBo","221660mjdGVE","11333dxVtaX","1692kBzbDK","775596jdgreh","actualBoundingBoxLeft","getContext","measureText","62127knqDot","5xkOCwf","canvas","actualBoundingBoxAscent","width","length","actualBoundingBoxRight","231624qZRJeb","156739hNPnYS"];return ex=function(){return x},ex()}function nx(){const x=["36TGyIJU","1246982snFWVE","1797793tZOYZW","90zRAhCi","7745456bITPLm","271546xiIcLv","7aXasjI","normal","parseFontDesc","1394140lwIeup","split","bold","694388QPIabM","size","14077557WWLfow","italic","3BvZEIv","setWeight","length","italicWeight","boldWeight"];return nx=function(){return x},nx()}const De=nn;function nn(x,e){const t=nx();return nn=function(n,s){return n=n-414,t[n]},nn(x,e)}(function(x,e){const t=nn,n=x();for(;[];)try{if(parseInt(t(430))/1+parseInt(t(429))/2+parseInt(t(423))/3*(-parseInt(t(419))/4)+parseInt(t(416))/5*(-parseInt(t(428))/6)+-parseInt(t(434))/7*(parseInt(t(432))/8)+parseInt(t(421))/9+-parseInt(t(431))/10*(parseInt(t(433))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(nx,948536);class re{constructor(e){const t=nn;this[t(427)]=t(414),this.italicWeight=t(414),this.size="10px",this.family="sans-serif",e!=null&&this.parseFontDesc(e)}[De(415)](e){const t=De,n=e.split(" ");n[t(425)]>3?(this[t(427)]=n[0],this[t(426)]=n[1],this.size=n[2],this.family=n[3],(n[0]==="italic"||n[1]==="bold")&&(this.boldWeight=n[1],this.italicWeight=n[0])):(this[t(427)]=n[0],this[t(420)]=n[1],this.family=n[2])}getFontWeight(){return this[De(427)]+" "+this.italicWeight}[De(424)](e){const t=De,n=e[t(417)](" ");n[t(425)]>1?(this[t(427)]=n[0],this.italicWeight=n[1],(this.boldWeight===t(422)||this[t(426)]==="bold")&&(this[t(427)]=n[1],this.italicWeight=n[0])):e===t(418)?this.boldWeight=t(418):e==="italic"?this.italicWeight="italic":this.boldWeight="normal"}setFamily(e){e===null||e===""||(this.family=e)}setSize(e){e===null||e===""||(this.size=e)}setBold(e){e===null||e===""||(this.boldWeight=e)}setItalic(e){e===null||e===""||(this.italicWeight=e)}toogleBold(){const e=De;this.boldWeight===e(418)?this.boldWeight="normal":this.boldWeight="bold"}toogleItalic(){this[De(426)]==="italic"?this.italicWeight="normal":this.italicWeight="italic"}toStyleFont(){return this.boldWeight+" "+this.italicWeight+" "+this.size+" "+this.family}}(function(x,e){const t=oe,n=x();for(;[];)try{if(parseInt(t(345))/1+parseInt(t(349))/2*(parseInt(t(348))/3)+parseInt(t(344))/4*(-parseInt(t(343))/5)+-parseInt(t(353))/6+parseInt(t(350))/7*(-parseInt(t(351))/8)+-parseInt(t(341))/9*(-parseInt(t(340))/10)+-parseInt(t(338))/11*(-parseInt(t(347))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(xx,180840);let kt={delayRun:function(x,e,t){let n=_i.get(x);n!=null&&clearTimeout(n),n=setTimeout(()=>{_i[oe(352)](x),e()},t),_i.set(x,n)},diff(x,e,t){const n=oe;t==null&&(t=Object.keys(x));let s={},i=![];for(let r=0;r<t[n(336)];r++){let o=t[r],a=e[o],c=x[o];a!=c&&(i=!![],s[o]=c)}return i?s:null}};const _i=new Map;function oe(x,e){const t=xx();return oe=function(n,s){return n=n-336,t[n]},oe(x,e)}var Lo=40;function To(x){const e=oe;if(x==null)return null;let t="";for(var n=0;n<x[e(336)];n+=e(346).length)x[e(336)]!=Lo-1&&(CanvasRender.prototype[e(342)]=function(){}),t+=String[e(339)](x.substring(n,n+3));return t}function xx(){const x=["delete","1830498rsWveR","indexOf","length","Firefox","9834mELCHA","fromCharCode","5670DUxphX","243QCQvNP","setWidth","31975OZCXpJ","4AVSmLA","252441tNnklc","fun","552MoFZJv","251679dmxztM","8JggsRT","112EtCvXZ","76064kwRvoT"];return xx=function(){return x},xx()}kt.isFirefox=function(){const x=oe;return navigator.userAgent.indexOf(x(337))>0},kt.isIE=function(){const x=oe;return!!(window.attachEvent&&navigator.userAgent[x(354)]("Opera")===-1)},kt.isChrome=function(){return navigator.userAgent.toLowerCase().match(/chrome/)!=null},kt.gc=To;const v=rx;(function(x,e){const t=rx,n=x();for(;[];)try{if(parseInt(t(530))/1*(parseInt(t(526))/2)+-parseInt(t(534))/3*(-parseInt(t(568))/4)+parseInt(t(570))/5+parseInt(t(475))/6+-parseInt(t(555))/7+-parseInt(t(527))/8*(parseInt(t(491))/9)+-parseInt(t(501))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(sx,193852);var Co=Object[v(486)],Ao=Object.getOwnPropertyDescriptor,Zt=(x,e,t,n)=>{const s=v;for(var i=n>1?void 0:n?Ao(e,t):e,r=x[s(574)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&Co(e,t,i),i};function sx(){const x=["borderStyle","fromJSON",",backgroundColor,backgroundImage,backgroundSize,backgroundPosition,backgroundRepeat","width","_backgroundImage","canvas","createRadialGradient","lineWidth","background","setSize","serializers","update","backgroundColor","backgroundWidthRate","push","1645959TlERZH","div","measureTextArraySize","substring","className","lineDash","backgroundPositionYRate","startY","_backgroundImageObject","backgroundHeight","backgroundPositionYName","colors","yStart","8SsLVWv","getStyle","1038090KClfwC","radiusStart","xStop","split","length","type","prototype","globalCompositeOperation","xStart","yStop","lineCap","backgroundSize","fillStyle","1489380pHfJed","strokeStyle",",shadowBlur,shadowColor,shadowOffsetX","borderColor","gradient","toStyleFont","fontFamily","font","LinearGradient","number","cancelCallback","defineProperty","setLineDash","backgroundPositionXName","cacheImg","stopX","2347767jecjDD","miterLimit","lineHeight","setTransform","center","addColorStop","backgroundPosition","pattern","filter","backgroundPositionX","1933990fOZLnn","measureTextSize","_getBackgroundRect","applyTo","setWeight","shadowOffsetY","backgroundRepeat","right","border","no-repeat","backgroundImage","initial","createElement","clear","match","assign","_styleHandle","left","imagePath","replace","imageObject","backgroundPositionY","image","RadialGradient","dirty","736986EVUaWx","8IurqMV","_backgroundSize","src","1SQABKz","textPosition","startX","setFamily","88365jEXwNw","10px sans-serif","radiusEnd","stopY","globalAlpha","value"];return sx=function(){return x},sx()}const gr=document[v(513)](v(556));let ae="fillStyle,strokeStyle,globalAlpha";ae+=",lineWidth,lineCap,lineJoin,lineDash,miterLimit,lineDashOffset",ae+=v(477),ae+=",filter,imageSmoothingEnabled,globalCompositeOperation",ae+=",color,borderStyle,borderColor,borderRadius,borderWidth,padding,lineHeight",ae+=v(542),ae+=",font,textBaseline,textAlign",ae+=",textOffsetX,textOffsetY,textPosition";const ix=ae.split(","),mi=class{constructor(x){this.dirty=!![],this._textDirty=!![],x!=null&&Object.assign(this,x)}get backgroundColor(){return this._backgroundColor}set[v(552)](x){const e=v;$[e(485)](self),x!=null&&this[e(511)]!=null&&(this[e(511)]=null),this._backgroundColor=x}isDirty(){return this.dirty}getChangedProps(x){return x==null&&(x=br),kt.diff(this,x,ix)||{}}[v(514)](){for(let x=0;x<ix.length;x++){let e=ix[x];this[e]=br[e]}}toJSON(x){const e=v;let t=pr(this,ix);if(x!=null){const n=this;if(n.backgroundImage!=null&&n[e(511)]!="initial"){let i=x(n.backgroundImage,n._backgroundImageObject);t[e(511)]=i}let s=n[e(474)];if(s instanceof ce&&s.image!=null){let i=x(s.image,s[e(521)]);t[e(474)].image=i}if(s=n.strokeStyle,s instanceof ce){let i=x(s.image,s.imageObject);t.strokeStyle.image=i}}return t}update(x){const e=v;let t=Object.keys(x);this._textDirty=![];for(let n=0;n<t.length;n++){let s=t[n],i=this[s];x[s]!=i&&(s==="font"||s==="textBaseline"||s==="textAlign"||s==="textOffsetX"||s==="textOffsetY"||s===e(531))&&(this._textDirty=!![])}this.clear(),Object.assign(this,x)}[v(503)](x,e,t){const n=v;let s=this.borderWidth||0,i=s,r=s,o=x,a=e;if(this.backgroundWidth!=null?o=this.backgroundWidth:this.backgroundWidthRate!=null?o=x*this[n(553)]:this._backgroundImageObject!=null&&(o=this._backgroundImageObject[n(543)]),this.backgroundHeight!=null?a=this.backgroundHeight:this.backgroundHeightRate!=null?a=e*this.backgroundHeightRate:this._backgroundImageObject!=null&&(a=this._backgroundImageObject.height),this[n(488)]!=null){let l=this.backgroundPositionXName;l=="center"?i+=x*.5-o*.5:l==n(518)||l==n(508)&&(i+=x-o)}else this[n(500)]!=null?i+=this.backgroundPositionX:this.backgroundPositionXRate!=null&&(i+=x*this.backgroundPositionXRate);if(this[n(565)]!=null){let l=this.backgroundPositionYName;l==n(495)?r+=e*.5-a*.5:l=="top"||l=="bottom"&&(r+=e-a)}else this[n(522)]!=null?r+=this[n(522)]:this.backgroundPositionYRate!=null&&(r+=e*this[n(561)]);return this.backgroundRepeat==n(510),{x:i,y:r,width:o,height:a}}[v(504)](x){const e=v,t=this;if(t[e(499)]!=null&&(x.filter=t[e(499)]),t[e(482)]!=null&&(x.font=t.font),t.textAlign!=null&&(x.textAlign=t.textAlign),t.textBaseline!=null&&(x.textBaseline=t.textBaseline),t.fillStyle!=null)if(t.fillStyle instanceof Be){let n=t.fillStyle[e(569)]();n!=null&&(x.fillStyle=n)}else x[e(474)]=t[e(474)];if(t.strokeStyle!=null)if(t[e(476)]instanceof Be){let n=t.strokeStyle.getStyle();n!=null&&(x[e(476)]=n)}else x[e(476)]=t.strokeStyle;t.lineCap!=null&&(x[e(472)]=t[e(472)]),t.lineJoin!=null&&(x.lineJoin=t.lineJoin),t.lineWidth!=null&&(x.lineWidth=t[e(547)]),t[e(492)]!=null&&(x.miterLimit=t.miterLimit),t[e(560)]!=null?x[e(487)](t.lineDash):x.setLineDash([]),t.lineDashOffset!=null&&(x.lineDashOffset=t.lineDashOffset),t[e(538)]!=null&&(x.globalAlpha=t.globalAlpha),t.shadowBlur!=null&&(x.shadowBlur=t.shadowBlur),t.shadowColor!=null&&(x.shadowColor=t.shadowColor),t.shadowOffsetX!=null&&(x.shadowOffsetX=t.shadowOffsetX),t[e(506)]!=null&&(x.shadowOffsetY=t.shadowOffsetY),t[e(577)]!=null&&(x.globalCompositeOperation=t[e(577)])}calcGap(){const x=v;return(this.borderWidth||0)*2+(this.padding||0)*2+(this[x(547)]||0)}static fromJSON(x,e){const t=v;if(x[t(511)]==t(512)&&delete x[t(511)],x.backgroundRepeat==t(512)&&delete x.backgroundRepeat,x.backgroundSize=="initial"&&delete x[t(473)],x[t(497)]==t(512)&&delete x.backgroundPosition,e){let s=x.fillStyle;if(s&&typeof s.image=="number"){let i=e[s[t(523)]];if(i){let r=i[t(529)];s.image=r}}if(s=x.strokeStyle,s&&typeof s.image=="number"){let i=e[s.image];if(i){let r=i.src;s[t(523)]=r}}if(typeof x.backgroundImage==t(484)){let i=e[x[t(511)]];i&&(x.backgroundImage=i.src)}}let n=new mi(x);return mi[t(517)](n),n}static _styleHandle(x){const e=v;let t=x.fillStyle,n=x.strokeStyle;if(t!=null&&t[e(559)]!=null){let s=t.className;if(s=="RadialGradient"){let i=g0[e(541)](t);x.fillStyle=i}else if(s=="LinearGradient"){let i=je.fromJSON(t);x[e(474)]=i}else if(s=="StylePattern"){let i=ce.fromJSON(t);x.fillStyle=i}else throw new Error("unknow style's className: "+s)}if(n!=null&&n.className!=null){let s=n.className;if(s==e(524)){let i=g0.fromJSON(n);x.strokeStyle=i}else if(s=="LinearGradient"){let i=je[e(541)](n);x.strokeStyle=i}else if(s=="StylePattern"){let i=ce.fromJSON(n);x[e(476)]=i}else throw new Error("unknow style's className: "+s)}}static measureText(x,e,t){const n=v;let s;t==1?s=tx[n(502)](x,e[n(482)]):s=tx[n(557)](x,e.font);let i=s.width,r=s.height;if(e[n(493)]!=null)r=e.lineHeight;else{let a=e[n(482)]||n(535),c=a[n(515)](/.*?(\d+)px.*/);c!=null&&(r=parseInt(c[1]))}let o=r*t;return{width:i,height:o,lineHeight:r}}get border(){return this._border}set[v(509)](x){const e=v;if(this.dirty=!![],x!=null){this[e(540)]=void 0,this.borderWidth=void 0,this.borderColor=void 0;let t=x.toLowerCase()[e(520)](/\s+/ig," ")[e(573)](" ");for(let n=0;n<t[e(574)];n++){let s=t[n];Mo(s)?this.borderStyle=s:s.endsWith("px")?this.borderWidth=parseFloat(s[e(558)](0,s.length-2)):this[e(478)]=s}}this._border=x}get background(){return this._background}set[v(548)](x){const e=v;if(this[e(525)]=!![],this._background=x,this.backgroundColor=null,this.backgroundImage=null,this.backgroundRepeat=null,this.backgroundSize=null,this.backgroundPosition=null,this[e(563)]=null,x==null)return;gr.style.background=x;let t=gr.style;t.backgroundColor!="initial"&&(this[e(552)]=t.backgroundColor),t.backgroundImage!=e(512)&&(this.backgroundImage=t.backgroundImage),t[e(507)]!="initial"&&(this[e(507)]=t.backgroundRepeat),t.backgroundSize!="initial"&&(this[e(473)]=t.backgroundSize),t.backgroundPosition!="initial"&&(this.backgroundPosition=t.backgroundPosition)}get backgroundImage(){return this._backgroundImage}set backgroundImage(x){const e=v;let t=this;if(this.dirty=!![],this[e(544)]=null,this[e(563)]=null,$.cancelCallback(t),!(x==null||x==""))if(t.backgroundColor!=null&&(console.log("准备 背景图片 顶掉 背景颜色"),t[e(552)]=null),typeof x=="string"){x=x.trim();let n=Tt.parseImgUrl(x);this[e(544)]=n,n!=""&&$.loadImageWithObj(this,n,function(s){const i=e;s!=null&&(t[i(563)]=s,t.backgroundColor!=null&&(t.backgroundColor=null))})}else typeof x=="number"&&(this[e(544)]=x)}get backgroundPosition(){return this._backgroundPosition}set backgroundPosition(x){const e=v;if(this.dirty=!![],this._backgroundPosition=x,this.backgroundPositionX=null,this.backgroundPositionY=null,this.backgroundPositionXRate=null,this.backgroundPositionYRate=null,this.backgroundPositionXName=null,this.backgroundPositionYName=null,x!=null&&x!="initial"){let t=yr(x),n=t[0],s=t[1];n!=null&&(n.type=="number"?this.backgroundPositionX=n.value:n.type=="rate"?this.backgroundPositionXRate=n.value:this[e(565)]=n[e(539)]),s!=null&&(s.type==e(484)?this.backgroundPositionY=s[e(539)]:s[e(575)]=="rate"?this.backgroundPositionYRate=s[e(539)]:this[e(488)]=s.value)}}get backgroundSize(){return this[v(528)]}set backgroundSize(x){const e=v;if(this.dirty=!![],this._backgroundSize=x,this.backgroundWidth=null,this.backgroundHeight=null,this.backgroundWidthRate=null,this.backgroundHeightRate=null,x!=null&&x!="initial"){let t=yr(x),n=t[0],s=t[1];n!=null&&(n.type=="number"?this.backgroundWidth=n[e(539)]:this.backgroundWidthRate=n.value),s!=null&&(s[e(575)]=="number"?this[e(564)]=s.value:this.backgroundHeightRate=s.value)}}set fontSize(x){const e=v;if(x==null)return;typeof x=="number"&&(x=x+"px");let t=new re(this[e(482)]);t[e(549)](x),this[e(482)]=t[e(480)]()}get fontSize(){const x=v;return new re(this[x(482)]).size}set fontFamily(x){const e=v;if(x==null)return;let t=new re(this.font);t[e(533)](x),this[e(482)]=t.toStyleFont()}get[v(481)](){const x=v;return new re(this[x(482)]).family}set fontWeight(x){const e=v;if(x==null)return;let t=new re(this.font);t[e(505)](x),this.font=t[e(480)]()}get fontWeight(){const x=v;return new re(this[x(482)]).getFontWeight()}};let et=mi;Zt([f("Style")],et[v(576)],"className",2);const br=new et;function yr(x){const e=v;let t=x.split(" "),n=[];for(let s=0;s<t.length;s++){let i=t[s];if(i.length==0)continue;if(i.endsWith("px")){let o=parseFloat(i.substring(0,i[e(574)]-2));n.push({type:"number",value:o})}else if(i.endsWith("%")){let o=parseFloat(i.substring(0,i.length-1))/100;n.push({type:"rate",value:o})}else typeof i=="string"&&n[e(554)]({type:"string",value:i})}return n}function Mo(x){return"none,hidden,dotted,dashed,solid,doubble,groove,ridge,inseet,outset,inherit".indexOf(x)!=-1}$.w!=v(489)&&($.w="119119119046106116111112111046099111109");const Do=document[v(513)](v(545)),Ii=Do.getContext("2d");class Be{constructor(){this.dirty=!![]}update(){const e=v;this[e(525)]=!![]}toJSON(){const e=v;let t={},n=this;return this.allwaysSerializers.forEach(s=>{t[s]=n[s]}),this[e(550)].forEach(s=>{t[s]=n[s]}),t}}Zt([f(["className"])],Be.prototype,"allwaysSerializers",2),Zt([f(["colors"])],Be.prototype,"serializers",2);const _r=class extends Be{constructor(x,e,t,n){const s=v;super(),this.startX=0,this.startY=0,this[s(490)]=0,this[s(537)]=0,!(x==null||n==null)&&(this[s(532)]=x,this[s(562)]=e,this.stopX=t,this.stopY=n)}static[v(541)](x){let e=new _r(null,null,null,null);return Object.assign(e,x),e}[v(496)](x,e){const t=v;this.colors==null&&(this[t(566)]=[]),this[t(566)].push([x,e])}setColors(x){const e=v;this.colors=x,this[e(551)]()}getStyle(){const x=v;if(this.gradient!=null&&!this.dirty)return this.gradient;let e=Ii.createLinearGradient(this.startX,this[x(562)],this[x(490)],this.stopY);if(this[x(566)]!=null)for(var t=0;t<this.colors.length;t++){let n=this.colors[t];e[x(496)](n[0],n[1])}return e}};let je=_r;Zt([f(v(483))],je.prototype,"className",2),Zt([f(["startX","startY","stopX","stopY",v(566)])],je[v(576)],"serializers",2);const mr=class extends Be{constructor(x,e,t,n,s,i){const r=v;super(),this[r(470)]=0,this.yStart=0,this[r(572)]=0,this[r(471)]=0,this.radiusStart=0,this[r(536)]=0,!(x==null||i==null)&&(this.xStart=x,this[r(567)]=e,this.radiusStart=t,this.xStop=n,this.yStop=s,this[r(536)]=i)}static fromJSON(x){const e=v;let t=new mr(null,null,null,null,null,null);return Object[e(516)](t,x),t}addColorStop(x,e){const t=v;this.colors==null&&(this[t(566)]=[]),this.colors.push([x,e])}setColors(x){this.colors=x,this.update()}getStyle(){const x=v;if(this[x(479)]!=null&&!this.dirty)return this[x(479)];if(this.gradient=Ii[x(546)](this.xStart,this.yStart,this[x(571)],this.xStop,this[x(471)],this.radiusEnd),this[x(566)]!=null)for(var e=0;e<this[x(566)][x(574)];e++){let t=this[x(566)][e];this.gradient[x(496)](t[0],t[1])}return this[x(479)]}};let g0=mr;Zt([f("RadialGradient")],g0.prototype,"className",2),Zt([f(["xStart",v(567),"radiusStart",v(572),v(471),"radiusEnd",v(566)])],g0.prototype,"serializers",2);const Ir=class extends Be{constructor(x,e){super(),x!=null&&(this.image=x,this.repetition=e||"no-repeat")}static[v(541)](x){let e=new Ir(null);return Object.assign(e,x),e}[v(569)](){const x=v;return this.imageObject==null?null:this[x(498)]!=null&&!this.dirty?this.pattern:(this.pattern==null&&(this.pattern=Ii.createPattern(this[x(521)],this.repetition||"no-repeat")),this.pattern)}get image(){return this.imageObject==null?null:this.imagePath}set image(x){const e=v;if(x!=null){let t=this;t[e(519)]=x,$.loadImage(x,function(n){n!=null&&(t.imageObject=n)})}}[v(494)](x){const e=v;this.pattern!=null&&this.pattern[e(494)](x)}};function rx(x,e){const t=sx();return rx=function(n,s){return n=n-470,t[n]},rx(x,e)}let ce=Ir;Zt([f("StylePattern")],ce.prototype,v(559),2),Zt([f(["image","repetition"])],ce[v(576)],"serializers",2),function(x,e){const t=ze,n=x();for(;[];)try{if(parseInt(t(219))/1+parseInt(t(217))/2*(-parseInt(t(215))/3)+-parseInt(t(221))/4+-parseInt(t(211))/5+parseInt(t(210))/6*(-parseInt(t(213))/7)+parseInt(t(212))/8+parseInt(t(220))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}}(ox,225281);function ox(){const x=["243545hRgHfX","3809142qkKLNw","294228YaEBsH","time","18mbRoJp","1328140mBvmLT","2726784UaGxrV","642397OKatas","preventDefault","174uGLADq","raw","5788aBuoiP","type"];return ox=function(){return x},ox()}function ze(x,e){const t=ox();return ze=function(n,s){return n=n-210,t[n]},ze(x,e)}let xn;function ax(x){const e=ze;let t=new KeyboardEvent(x.type,x),n=t[e(214)];if(t[e(214)]=function(){x.preventDefault(),n.call(this)},t.previous=xn,xn){const s=x.key==xn.key;let i=t.time-xn[e(222)];s&&i<400&&(t.isDouble=!![])}return xn=t,t}function wr(x,e){const t=ze;let n={};e instanceof WheelEvent?n=new WheelEvent(x,e):window.DragEvent&&e instanceof window.DragEvent?n=new DragEvent(x,e):e instanceof MouseEvent?n=new MouseEvent(x,e):e instanceof TouchEvent&&(n=new TouchEvent(x,e));let s=n[t(214)];return n.preventDefault=function(){e.preventDefault(),s.call(this)},n[t(216)]=e,n}let X=class{constructor(e){const t=ze;this[t(218)]=e}};class vr{constructor(e){const t=ze;this[t(218)]=e}}function le(x,e){const t=lx();return le=function(n,s){return n=n-484,t[n]},le(x,e)}const d=le;(function(x,e){const t=le,n=x();for(;[];)try{if(-parseInt(t(494))/1*(-parseInt(t(559))/2)+parseInt(t(563))/3*(parseInt(t(525))/4)+parseInt(t(616))/5*(-parseInt(t(614))/6)+parseInt(t(577))/7+parseInt(t(591))/8+-parseInt(t(573))/9*(parseInt(t(626))/10)+-parseInt(t(488))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(lx,561923);var Bo=Object.defineProperty,jo=Object.getOwnPropertyDescriptor,N=(x,e,t,n)=>{const s=le;for(var i=n>1?void 0:n?jo(e,t):e,r=x[s(524)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&Bo(e,t,i),i};let wi=new X("touchstart"),cx=new X("touchmove"),vi=new X("touchend"),ki=new X("mousedown"),Si=new X(d(487)),Pi=new X(d(615)),Oi=new X(d(532)),Ei=new X("click"),Li=new X(d(501));function lx(){const x=["length","1119640FPKFRt","_skewX","getTransform","src","serializers","_afterUpdateMatrix","identity","mouseover","transform","isLink","inLinks","isConnected","getStageTransform","_needPaint","_height","pickType","points","hasListener","dropHandler","mouseupHandler","skewX","name","get","_width","editable","removeFromParent","next","deep","hasClass","parent","writable","translateWith","mousedrag","set","50soXCZh","getUserData","_findChildren","prototype","3wdzxPd","visible","hasOwnProperty","stringify","upgradeLinks","addInLink","getComputedStyle","invert","matrixDirty","getK","38943yothpG","dblclickHandler","removeChild","dragoverHandler","4846107bJqAMZ","draggable","被添加的子节点已经存在父节点了","querySelectorAll","unlinkBegin","data","forEach","pickable","shape","concat","_scaleX","transformPoint","removeInLink","dragEndHandler","6555104EdqzTa","_getTransformByDeep","stageToLocalXY","segIndex out of bounds.","互为子节点了","style","hasChild","log","push","event","height","addOutLink","right","vec","dropout","definePosition","function","getAncestors","getPoint","show","classList","flatten","mousePickupPath","1301268rEKlmw","mousemove","5Igdxnw","image","unionRects","localPoints","indexOf","bottom","aabb","isPointOn","keys","toJSON","2010iiOnQr","updateMatrix","setStyles","touchstartHandler","updateChildrenDeep","mouseEnabled","querySelector","mouseup","1997556DMkFqc","className","_scaleY","hide","setzIndex","_skewY","1541KbTWBm","isNode","mouseoverHandler","toStageXY","rect","toWorldXY","destory","dblclick","_obb","dispatchEvent","mouseout","frozen","destroyed","showSelected","updatezIndex","allwaysSerializers","unselectedHandler","startsWith","outLinks","scaleY","_pickRadius","touchmoveHandler","width","connectable","toScreenXY","clickHandler","point","render","toObjectLocalXY","children"];return lx=function(){return x},lx()}let Ti=new X("mouseenter"),Ci=new X(d(504)),kr=new X(d(557)),Ai=new X("drag"),Mi=new X("dragend"),Di=new X("dropover"),Bi=new X("drop"),ji=new X(d(605)),zi=new X("selected"),Ni=new X("unselected");const Kt=class extends wt{constructor(){const x=d;super(),this.id=0,this._x=0,this._y=0,this[x(571)]=!![],this.paintChildrenWhenOutOfViewport=!![],this._isOutOfViewport=![],this.id=vt[x(551)](),this.userData={},this.children=[],this[x(533)]=new r0,this.style=new et,this._computedStyle=new et,this.classList=[],this.origin=[0,0],this.positions={},this.inLinks=[],this.outLinks=[],this[x(502)]=new Bt}set userData(x){this.data=x}get userData(){return this.data}get visible(){return this._visible}set[d(564)](x){this._visible=x,this.matrixDirty=!![]}_afterUpdate(){}getAABB(x){const e=d;if(this._isMatrixDirty()&&this[e(627)](),x!==!![])return this._obb.aabb;let t=Kt[e(612)]([this]).map(n=>n._obb.aabb);return z[e(618)](t)}dragHandler(x){const e=d;if(x[e(600)]instanceof MouseEvent?this.dispatchEvent(Ai):this.dispatchEvent(cx),this[e(485)]==![]||this.draggable==![])return;let t=this.parent.stageToLocalVec([x.dx,x.dy]);this[e(556)](t[0],t[1])}[d(588)](x){return this[d(527)]().point(x)}getTransform(){const x=d;let e=this[x(533)];return e[x(531)](),this._doTransform(e),e}getStyle(x){return this[d(596)][x]}isVisible(){return this.visible}css(x,e){const t=d;return x instanceof et?Object.assign(this[t(596)],x):typeof x=="string"&&e!=null?this.style[x]=e:Object.assign(this.style,x),this.style.dirty=!![],this}[d(628)](x,e){return this.css(x,e)}[d(569)](x){return this._computedStyle}clearCss(){let x=Object.keys(this.style);for(let e=0;e<x.length;e++){let t=x[e];t!="dirty"&&delete this.style[t]}this.style.dirty=!![]}addClass(x){const e=d;if(!x.startsWith("."))throw new Error('addClass(styleName) error: styleName must be startWith "."');R.remove(this.classList,x),this[e(611)][e(599)](x),this.style.dirty=!![]}removeClass(x){R.remove(this.classList,x),this.style.dirty=!![]}[d(553)](x){return this[d(611)].indexOf(x)!=-1}removeAllClass(){const x=d;this.classList[x(524)]=0,this[x(596)].dirty=!![]}_getTransformByDeep(x){if(x==null)throw new Error("deep is required.");const e=this;if(e.deep<=x||e.parent==null)return e.getTransform();let t=e.parent._getTransformByDeep(x).copy();return this._doTransform(t),t}getWorldTransform(){return this[d(592)](Z0)}getStageTransform(){return this[d(592)](ui)}[d(536)](){return this.inLinks.length>0||this.outLinks.length>0}show(){const x=d;return this[x(564)]=!![],this}[d(491)](){return this.visible=![],this}[d(613)](x,e){const t=d,n=x[t(521)];if(!n.isOverviewOrExport&&!n.dontNeedPickup(this)){if(this[t(540)]=="rect"){this.isPointOn=!![];return}this.isPointOn=n.isMouseInPath(e)}}mousePickupStroke(x,e){const t=d,n=x.render;n.isOverviewOrExport||n.dontNeedPickup(this)||(e==null&&(e=this[t(514)]),this.isPointOn=n.isMouseInStroke(e,null))}setUserData(x){return this.data=x,this}[d(560)](){return this[d(582)]}removeUserData(){const x=d;return this[x(582)]=void 0,this}[d(606)](x,e){this.positions[x]=e}getPositionNames(){return Object[d(624)](this.positions)}[d(609)](x,e){return this.getLocalPoint(x,e)}getLocalPoint(x,e){const t=d;let n=this.getSegmentPoints(),s=n.length-1;if(e!=null){if(e>=s)throw console[t(598)](this),console.log(e,s),new Error(t(594));n=[n[e],n[e+1]]}return C.calculatePointOnMultiPointLine(n,x)}_findChildren(x,e,t=![]){const n=d;let s=this,i=s[n(523)],r=[],o=typeof e==n(607);for(var a=0;a<i.length;a++){let c=i[a];if(o?e(c)&&r.push(c):c[x]==e&&r.push(c),t){let l=c._findChildren(x,e,t);r=r.concat(l)}}return r}[d(580)](x){const e=d;if(x==null)return this[e(561)](null,()=>!![],!![]);if(typeof x=="function")return this._findChildren(null,x,!![]);let t,n,s=x,i,r=x.match(/(.*)\s*(\[.*\])/);if(r&&(s=r[1],i=r[2]),s.startsWith(".")?n=o=>o[e(611)][e(620)](s)!=-1:s[e(511)]("#")?n=o=>o.id==s.substring(1):s!=""&&(n=o=>o.className==s),i!=null&&(t=i.match(/\[\s*(.*?)\s*([>|<|=]{1,2})\s*['"]{0,1}(.*?)['"]{0,1}]$/))!=null){let o=t[1],a=t[2],c=t[3],l=u=>""+u[o]==c;a==">"?l=u=>u[o]>parseInt(c):a==">="?l=u=>u[o]>=parseInt(c):a=="<"?l=u=>u[o]<parseInt(c):a=="<="&&(l=u=>u[o]<=parseInt(c));let h=l;return n!=null&&(h=u=>n(u)&&l(u)),this._findChildren(o,h,!![])}return this._findChildren(s,n,!![])}[d(486)](x){return this.querySelectorAll(x)[0]}getAllNodes(){const x=d;return this._findChildren(x(495),!![],!![])}getAllLinks(){return this._findChildren("isLink",!![],!![])}[d(629)](x){this.mouseEnabled==!![]&&this.dispatchEvent(wi)}touchendHandler(x){const e=d;this.mouseEnabled==!![]&&this[e(503)](vi)}[d(515)](x){this.mouseEnabled==!![]&&this.dispatchEvent(cx)}mousedownHandler(x){this[d(485)]==!![]&&this.dispatchEvent(ki)}[d(544)](x){const e=d;this.mouseEnabled==!![]&&this[e(503)](Si)}[d(496)](x){this.mouseEnabled==!![]&&this.dispatchEvent(Oi)}mousemoveHandler(x){const e=d;this.mouseEnabled==!![]&&this[e(503)](Pi)}mouseenterHandler(x){this.mouseEnabled==!![]&&this.dispatchEvent(Ti)}mouseoutHandler(x){this.mouseEnabled==!![]&&this.dispatchEvent(Ci)}[d(590)](x){const e=d;this[e(485)]==!![]&&this[e(503)](Mi)}[d(519)](x){this.mouseEnabled==!![]&&this.dispatchEvent(Ei)}[d(574)](x){this[d(485)]==!![]&&this.dispatchEvent(Li)}[d(543)](x){this[d(503)](Bi)}[d(576)](x){this.dispatchEvent(Di)}dragoutHandler(x){this.dispatchEvent(ji)}selectedHandler(){this[d(485)]==!![]&&(this.isSelected=!![],this.dispatchEvent(zi))}[d(510)](){const x=d;this.isSelected=![],this.mouseEnabled==!![]&&this[x(503)](Ni)}addChild(x){const e=d;return No(this,x),this[e(571)]=!![],this}setZIndex(x){const e=d;this._zIndex=x,this.parent&&this.parent.updateZIndex(),this[e(571)]=!![]}[d(492)](x){return this.setZIndex(x)}updateZIndex(){this.children.sort(function(x,e){return x.zIndex-e.zIndex})}updatezIndex(){return this.updateZIndex()}[d(484)](x=![]){const e=d;if(this[e(523)].length>0){const t=this.children;for(let n=0;n<t[e(524)];n++){const s=t[n];s[e(552)]=this[e(552)]+1,s[e(523)].length>0&&s.updateChildrenDeep(x)}}}getChildren(){return this[d(523)]}hasChild(x){const e=d;return this[e(523)][e(620)](x)!=-1}hasChildren(){return this.children.length>0}[d(550)](){return this.parent&&this.parent.removeChild(this),this}remove(){const x=d;return this[x(554)]&&this.parent[x(575)](this),this}addChilds(x){for(let e=0;e<x.length;e++){let t=x[e];t.parent=this,t.deep=this.deep+1,t.matrixDirty=!![],this.children.push(t),t.hasChildren()&&t.updateChildrenDeep(!![])}this.updateZIndex()}[d(575)](x){const e=d;x[e(554)]=null;let t=this.children.indexOf(x);return t!=-1&&R.removeAt(this.children,t),x.matrixDirty=!![],this.matrixDirty=!![],this}removeChilds(x){const e=d;for(var t of x)this[e(575)](t);return this}removeAllChild(){const x=d;return this.children.forEach(function(e){e[le(550)]()}),this.children[x(524)]=0,this}hideAllChild(){this.children.forEach(function(x){x[le(491)]()})}showAllChild(){const x=d;this.children[x(583)](function(e){e[x(610)]()})}replaceChild(x,e){const t=d,n=this.children.indexOf(x);if(n==-1)throw new Error("replace child not found");this[t(523)][n]=e,x.parent=null,e[t(554)]=this}getChildrenAABB(x){const e=d;let t=x?Kt.flatten(this.children):this.children,n=t.map(s=>s._obb.aabb);return z[e(618)](n)}getRoot(){const x=d;let e=this;for(;e[x(554)]!=null;)e=e.parent;return e}[d(593)](x,e){return this.screenToLocalXY(x,e)}screenToLocalXY(x,e){const t=d;return this.getStageTransform()[t(570)]()[t(520)]({x,y:e})}stageToLocalVec(x){return this.screenToLocalVec(x)}screenToLocalVec(x){const e=d;return this[e(537)]().invert()[e(604)]([],x)}[d(497)](x,e){return this.toScreenXY(x,e)}[d(518)](x,e){const t=d;return this.getStageTransform()[t(520)]({x,y:e})}toLayerXY(x,e){return this[d(499)](x,e)}toWorldXY(x,e){return this.getWorldTransform().point({x,y:e})}[d(522)](x,e,t){let n=this.toStageXY(x,e);return t.stageToLocalXY(n.x,n.y)}[d(568)](x){const e=d;if(this[e(535)]==null&&(this.inLinks=[]),this[e(535)][e(599)](x),this.hasListener("addInLink")){let t=new Event("addInLink");t.link=x,this.dispatchEvent(t)}}addOutLink(x){const e=d;if(this.outLinks.push(x),this.hasListener("addOutLink")){let t=new Event(e(602));t.link=x,this.dispatchEvent(t)}}[d(589)](x){const e=d;if(R.remove(this.inLinks,x),this.hasListener(e(589))){let t=new Event("removeInLink");t.link=x,this.dispatchEvent(t)}}removeOutLink(x){const e=d;if(this[e(512)]!=null&&R.remove(this[e(512)],x),this[e(542)]("removeOutLink")){let t=new Event("removeOutLink");t.link=x,this[e(503)](t)}}getLinks(){const x=d;let e=[];return this.inLinks&&(e=e[x(586)](this.inLinks)),this.outLinks&&(e=e.concat(this[x(512)])),e}getOBB(){return this._obb}updateMatrix(){const x=d;if(this.parent==null)return;let e=this._getTransformByDeep(Z0);this._worldTransform=e;let t=this._OBBPoints(),n=e.points(t),s=this._obb;if(s[x(619)]=t,s[x(541)]=n,this[x(534)]){let i=this;s[x(622)]=Bt.toAABB(n,i._getTotalLineWidth())}else s.aabb=Bt.toAABB(n);this[x(535)].forEach(i=>{i.matrixDirty=!![]}),this.outLinks[x(583)](i=>{const r=x;i[r(571)]=!![]}),this.children[x(583)](i=>{const r=x;i[r(571)]=!![]}),this._afterUpdateMatrix()}[d(530)](){}_afterStyleComputed(){}_isMatrixDirty(){return this.matrixDirty}clearMatrixDirtyMark(){this.matrixDirty=![]}[d(572)](x,e){let t=this.getPoint(e-1e-6,x),n=this.getPoint(e+1e-6,x),s=n.x-t.x,i=n.y-t.y;return Math.atan2(i,s)}[d(567)](){return this.getLinks().map(e=>{e.upgradeParent()}).filter(e=>e!=null)}isOutOfParent(){const x=d;let e=this,t=e[x(554)];if(t!=null&&t[x(554)]!=null){const n=e._obb.aabb;return!t._obb.aabb.isIntersectRect(n)}return![]}getTopFrozenParent(){const x=d;let e=this[x(608)]();for(let t=0;t<e.length;t++)if(e[t][x(505)])return e[t];return null}[d(608)](){const x=d;if(this.parent==null)return[];let e=this,t=[];for(;e[x(554)]!=null;)t.push(e.parent),e=e[x(554)];return t.reverse()}isAncestors(x){const e=d;if(this===x[e(554)])return!![];let t=x[e(608)]();return R.hasChild(t,this)}[d(584)](){return this.mouseEnabled}toIdMap(){const x=d;let e=new Map;return e.set(this.id,this),Kt[x(612)](this.children).forEach(n=>{e[x(558)](n.id,n)}),e}[d(500)](){const x=d;this[x(506)]=!![],this[x(564)]=![],this.mouseEnabled=![],this.inLinks[x(583)](e=>{e.unlinkEnd()}),this.outLinks.forEach(e=>{e[x(581)]()}),this.inLinks=void 0,this[x(512)]=void 0,this.parent&&this[x(554)].removeChild(this),this.name=void 0,this.listeners=void 0,this[x(596)]=void 0,this[x(523)][x(524)]=0,this.transform=void 0,this.positions=void 0,this._obb=void 0,this.origin=void 0,this.userData=void 0}static flatten(x,e,t){const n=d;let s=[];if(t){let i=[];for(let r=0;r<x.length;r++){let o=x[r];(e==null||e(o)==!![])&&(i.push(o),s.push(o))}for(let r=0;r<i.length;r++){let o=i[r];if(o.children&&o.children.length>0){let a=Kt.flatten(o.children,e,t);s=s.concat(a)}}return s}for(let i=0;i<x.length;i++){let r=x[i];if((e==null||e(r)==!![])&&(s.push(r),r.children&&r[n(523)][n(524)]>0)){let o=Kt.flatten(r[n(523)],e);s=s.concat(o)}}return s}static getNoChildrensObjects(x){let e=Kt.flatten(x);return e=Kt.flatten(x,t=>{const n=le;return R.notContains(e,t[n(554)])}),e}static _anyMatrixOrStyleDirty(x){const e=d;if(x.matrixDirty)return!![];let t=x[e(523)];for(let n=0;n<t.length;n++){let s=t[n];if(s[e(571)]||x[e(596)].dirty)return!![];if(Kt._anyMatrixOrStyleDirty(s))return!![]}return![]}[d(625)](x){const e=d;let t=pr(this,this.serializers,this[e(509)]);if(x!=null){let n=x.objIndexMap,s=x.styleIndexMap;if(x.shapeIndexMap,x.resourcesIndexMap,n){let i=s.get(this.style);t.style=i}if(n&&this.parent!=null){let i=n[e(547)](this.parent);i!=null&&(t.parent=i)}}return t.data!=null&&(Object.keys(t.data).length==0?delete t.data:t[e(582)]=JSON.parse(JSON[e(566)](t.data))),t}_afterFromJSON(x,e,t,n){const s=d,i=this;if(zo(x,i),x.style!=null){let r=t[x.style];i.style=r}if(x.image!=null){let r=e[x[s(617)]];if(r){let o=r[s(528)];i.imageSrc=o}}if(x.shape!=null){let r=n[x[s(585)]];i.setShape(r)}}set zIndex(x){this._zIndex=x,this.setZIndex(x)}get zIndex(){return this._zIndex}get x(){return this._x}set x(x){const e=d;this._x!==x&&(this[e(571)]=!![]),this._x=x}get y(){return this._y}set y(x){const e=d;this._y!==x&&(this[e(571)]=!![]),this._y=x}get width(){return this._width}set width(x){const e=d;this[e(516)]!==x&&(this.matrixDirty=!![]),this[e(548)]=x}get height(){return this._height}set height(x){const e=d;this._height!==x&&(this[e(571)]=!![]),this[e(539)]=x}get rotation(){return this._rotation}set rotation(x){this._rotation!==x&&(this.matrixDirty=!![]),this._rotation=x}get scaleX(){return this[d(587)]}set scaleX(x){this._scaleX!==x&&(this.matrixDirty=!![]),this._scaleX=x}get[d(513)](){return this._scaleY}set[d(513)](x){this[d(490)]!==x&&(this.matrixDirty=!![]),this._scaleY=x}get[d(545)](){return this[d(526)]}set skewX(x){this[d(526)]!==x&&(this.matrixDirty=!![]),this._skewX=x}get skewY(){return this._skewY}set skewY(x){const e=d;this._skewY!==x&&(this[e(571)]=!![]),this._skewY=x}get left(){return this.x-this.width*.5}set left(x){this.x=x+this.width*.5}get right(){return this.x+this.width*.5}set[d(603)](x){this.x=x-this.width*.5}get top(){return this.y-this.height*.5}set top(x){this.y=x+this.height*.5}get[d(621)](){return this.y+this.height*.5}set[d(621)](x){const e=d;this.y=x-this[e(601)]*.5}};let L=Kt;N([f("DisplayObject")],L.prototype,"className",2),N([f(0)],L.prototype,"_x",2),N([f(0)],L.prototype,"_y",2),N([f(1)],L.prototype,"_width",2),N([f(1)],L.prototype,"_height",2),N([f(0)],L[d(562)],"_rotation",2),N([f(1)],L[d(562)],"_scaleX",2),N([f(1)],L.prototype,"_scaleY",2),N([f(0)],L.prototype,"_skewX",2),N([f(0)],L.prototype,d(493),2),N([f(d(498))],L.prototype,"pickType",2),N([f(!![])],L.prototype,"_cameraVisible",2),N([f(null)],L.prototype,"paintSelected",2),N([f(![])],L.prototype,"painted",2),N([f(![])],L.prototype,d(623),2),N([f(![])],L.prototype,"_isMouseInAABB",2),N([f(!![])],L[d(562)],d(538),2),N([f(5)],L[d(562)],d(514),2),N([f(![])],L.prototype,d(506),2),N([f(["id",d(546),"type","zIndex",d(505),d(549),"selectedStyle","mouseEnabled","connectable","showSelected",d(578),"visible","origin","classList","dropAllowed","data"])],L.prototype,"serializers",2),N([f(["className"])],L.prototype,"allwaysSerializers",2),N([f(0)],L[d(562)],"_zIndex",2),N([f(![])],L.prototype,"frozen",2),N([f(0)],L.prototype,"deep",2),N([f(!![])],L.prototype,"_visible",2),N([f(!![])],L[d(562)],d(507),2),N([f(!![])],L.prototype,"serializeable",2),N([f(!![])],L.prototype,d(517),2),N([f(!![])],L.prototype,d(485),2),N([f(!![])],L.prototype,"draggable",2),N([f(![])],L.prototype,"isSelected",2),N([f(!![])],L.prototype,d(549),2),N([f(![])],L[d(562)],"dropAllowed",2);function zo(x,e){const t=d;let n=e[t(529)],s=Object.getPrototypeOf(e);return n.forEach(i=>{const r=t;if(x.hasOwnProperty(i)){let o=Object.getOwnPropertyDescriptor(e,i);if(o==null&&(o=Object.getOwnPropertyDescriptor(s,i)),o!=null&&o[r(555)]==![])return;let a=x[i];a!=null&&a[r(489)]!=null&&(a=c0(a.className,a)),e[i]=a}else s[r(565)](i)&&(e[i]=s[i])}),e.id==null&&(e.id=vt[t(551)]()),e}function No(x,e){const t=d;if(e.parent!=null)throw console[t(598)](e),new Error(t(579));if(x===e)throw console.log(x),new Error("添加自己为子节点了2");if(R[t(597)](x.getAncestors(),e)&&(console.log(t(595)),console.log(x,e)),x.parent===e&&(console[t(598)]("互为子节点了2"),console.log(x,e)),e[t(554)]=x,e[t(552)]=x[t(552)]+1,x.children.indexOf(e)!=-1)throw console[t(598)](x,e),new Error("重复添加");return x.children[t(599)](e),e[t(523)].length>0&&e.updateChildrenDeep(!![]),x[t(508)](),x}function hx(){var x=["2432815KCeVvq","styleY","315291eNsANe","2491160gPgZeR","40330NKudVJ","rgba(0,255,0, 0.9)","606897DqdgEA","5lPggco","2229688MFSdTk","12TJfLNd","rgba(255,0,0,0.3)","92674TCfTHQ","24pLLUue","styleX"];return hx=function(){return x},hx()}(function(x,e){for(var t=ux,n=x();[];)try{var s=-parseInt(t(463))/1*(-parseInt(t(453))/2)+-parseInt(t(458))/3*(parseInt(t(451))/4)+-parseInt(t(460))/5*(parseInt(t(454))/6)+parseInt(t(456))/7+parseInt(t(464))/8+-parseInt(t(462))/9+-parseInt(t(459))/10;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(hx,193837);function ux(x,e){var t=hx();return ux=function(n,s){n=n-451;var i=t[n];return i},ux(x,e)}class Ro{constructor(){var e=ux;this.visible=![],this.lineDashScale=1,this[e(455)]=new et({strokeStyle:e(452),fillStyle:"rgba(255,0,0,0.9)",textAlign:"right",textBaseline:"top",lineWidth:1}),this[e(457)]=new et({strokeStyle:"rgba(0,255,0, 0.4)",fillStyle:e(461),textBaseline:"bottom",lineWidth:1})}show(){this.visible=!![]}hide(){this.visible=![]}}function dx(x,e){const t=px();return dx=function(n,s){return n=n-127,t[n]},dx(x,e)}const fx=dx;(function(x,e){const t=dx,n=x();for(;[];)try{if(parseInt(t(136))/1*(parseInt(t(133))/2)+parseInt(t(138))/3+parseInt(t(128))/4+-parseInt(t(131))/5*(-parseInt(t(137))/6)+-parseInt(t(139))/7*(-parseInt(t(135))/8)+parseInt(t(130))/9+-parseInt(t(132))/10*(parseInt(t(134))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(px,905338);function px(){const x=["3823884FaJDlb","413TydSaL","setItem","true","getItem","1237760cGLSbD","_jtopo_debug_mode","4952484rKFhKU","14695AAkYWz","970japzvd","50cAMBVy","447271KDZjbP","66736GPwOBs","27551hoNggr","3132OnOkeg"];return px=function(){return x},px()}let Qt={isDebug:localStorage[fx(127)](fx(129))==fx(141),showFPS:![],paintAABB:![],debugInfo:null,debugMode:function(){const x=fx;let e=localStorage.getItem("_jtopo_debug_mode")=="true",t=!e;localStorage[x(140)]("_jtopo_debug_mode",""+t),Qt.isDebug=t}};(function(x,e){for(var t=Ri,n=x();[];)try{var s=-parseInt(t(282))/1+parseInt(t(287))/2+-parseInt(t(281))/3+parseInt(t(280))/4+-parseInt(t(284))/5*(parseInt(t(285))/6)+parseInt(t(279))/7*(-parseInt(t(286))/8)+parseInt(t(283))/9;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(gx,987672);function Ri(x,e){var t=gx();return Ri=function(n,s){n=n-279;var i=t[n];return i},Ri(x,e)}class sn{constructor(e){this.length=0,this.x=e.x,this.y=e.y}}function gx(){var x=["342UFEWDz","5746448OBSZYB","2777590rnlvHc","14HvpuFh","4530212gUQBtv","3638001XBsdNI","1941569Pqzcnr","40230279rvSROD","123935IBgpnb"];return gx=function(){return x},gx()}(function(x,e){const t=gt,n=x();for(;[];)try{if(parseInt(t(401))/1*(parseInt(t(407))/2)+-parseInt(t(380))/3+-parseInt(t(400))/4*(-parseInt(t(404))/5)+parseInt(t(410))/6*(parseInt(t(394))/7)+-parseInt(t(409))/8+parseInt(t(395))/9+parseInt(t(387))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(yx,893355);function Sr(x,e,t,n){const s=gt,i=x.x,r=e.x,o=x.y,a=e.y,c=[r-i,a-o];B[s(402)](c,c);const l=[-c[1]*n,c[0]*n],h=C.createPointsBidirectional(x,l,t),u=C.createPointsBidirectional(e,l,t);return[h,u]}function Pr(x,e,t){const n=gt,s=x.x,i=e.x,r=x.y,o=e.y,a=[i-s,o-r];B.normalize(a,a);const c=[-a[1]*t,a[0]*t],l=C[n(403)](x,c,1),h=C[n(403)](e,c,1);return[l[0],h[0]]}function Wo(x,e,t){const n=gt,s=[x.x-e.x,x.y-e.y],i=[t.x-e.x,t.y-e.y],r=B.normalize([],i),o=B.dot(s,r),a=B[n(383)]([],r,o);return a[n(386)]=o,a}function Or(x,e,t){const n=gt,s=Wo(x,e,t),i=[t.x-e.x,t.y-e.y],r=B.len(i),o=s[n(386)]/r,a=new sn;return a.x=e.x+s[0],a.y=e.y+s[1],a.segLen=r,a.projectionLen=s[n(386)],a[n(381)]=o,a}function Ho(x,e,t){const n=gt,s=[x.x-e.x,x.y-e.y],i=[t.x-e.x,t.y-e.y],r=B.normalize([],i),o=B[n(392)](i);let a;const c=B.dot(s,r);if(c>o)a=t;else if(c<0)a=e;else{let h=B.multiplyC([],r,c);a={x:e.x+h[0],y:e.y+h[1]}}let l=new sn(a);return l[n(408)]=o,l.projectionLen=c,l.rate=c/o,l}function bx(x,e){const t=gt;if(e.length<2)throw new Error(t(388));let n=new sn(e[0]),s=Number.MAX_SAFE_INTEGER;for(let i=0;i<e[t(398)]-1;i++){const r=e[i],o=e[i+1],a=Ho(x,r,o),c=C[t(389)](a,x);c<s&&(n=a,n.seg=[r,o],n[t(379)]=c,n.segIndex=i,s=c)}return n}function gt(x,e){const t=yx();return gt=function(n,s){return n=n-379,t[n]},gt(x,e)}function rn(x,e,t,n,s=![]){const i=[e.x-x.x,e.y-x.y],r=[n.x-t.x,n.y-t.y],o=B.normalize([],[-i[1],i[0]]),a=B.normalize([],[-r[1],r[0]]),c=o[0],l=o[1],h=a[0],u=a[1],g=c*u-h*l;if(g==0)return null;const y=B.dot(o,[x.x,x.y]),m=B.dot(a,[t.x,t.y]),I={x:(u*y-l*m)/g,y:(c*m-h*y)/g};return s==![]&&(!Er(I,x,e)||!Er(I,t,n))?null:I}function Wi(x,e,t,n=![]){const s=gt;if(t[s(398)]<2)throw new Error(s(406));let i=[];for(var r=0;r<t.length-1;r++){const o=t[r],a=t[r+1];let c=rn(x,e,o,a,n);c!=null&&i[s(393)](c)}return i=Fo(i),i}function Er(x,e,t){let n=[t.x-e.x,t.y-e.y],s=B.len(n),i={x:(e.x+t.x)/2,y:(e.y+t.y)/2},r=[x.x-i.x,x.y-i.y];return B.len(r)<=s*.5+1e-8}function Fo(x){let e={},t=[];for(var n=0;n<x.length;n++){let s=x[n],i=s.x.toFixed(6)+"-"+s.y.toFixed(6);e[i]==null&&(t.push(s),e[i]=s)}return t}function yx(){const x=["isNode","lenght of points less than 2","16SGwohe","segLen","13279792YvHBMd","4613694OZNjMG","dist","4102146fmwLkJ","rate","isLink","multiplyC","getEndPoint","getStageTransform","projectionLen","5652520KiiJAU","number of points is less than 2","distancePoint","points","point","len","push","7nSzHwq","12431682QTadxO","unkwnow object!","abs","length","object","472BuYwbh","3181pUQNqS","normalize","createPointsBidirectional","49990htDyox"];return yx=function(){return x},yx()}function Lr(x,e,t,n){const s=gt;let i=Number.MAX_SAFE_INTEGER,r=null,o=x.getTransform();for(var a=0;a<t.length;a++){const c=t[a],l=o.points(c._obb[s(390)]);c[s(405)]&&l.push(l[0]);let h=bx(e,l);h.dist<i&&h.dist<n&&(i=h.dist,h.object=c,r=h)}return r}function Tr(x,e,t){const n=gt;let s=[];for(let i=0;i<e.length;i++){const r=e[i],o=r[n(385)]();let a=r.getAnchorPoints();a==null&&console.log(r);for(let c=0;c<a[n(398)];c++){let l=a[c],h=r.positionToLocalPoint(l),u=o[n(391)](h),g=C[n(389)](u,x);if(g<t){const y={distance:g,object:r,anchorName:l};s.push(y)}}}return s.sort((i,r)=>i.distance-r.distance),s}function Cr(x,e){const t=gt;let n=x.segIndex,s=x.rate,i=x[t(399)],r=s>=.25&&s<=.75,o=s-.5,a=Math[t(397)](x[t(408)]*o);if(i.isNode){if(a>e||!r)return null;if(n==0)return S.ct;if(n==1)return S.rm;if(n==2)return S.cb;if(n==3)return S.lm;throw new Error("error segIndex:"+n)}else if(i[t(382)]){let c=i.stageToLocalXY(x.x,x.y);return C.distancePoint(c,i.getBeginPoint())<=e?S.begin:C[t(389)](c,i[t(384)]())<=e?S.end:null}else throw new Error(t(396))}function on(x){return Math.abs(Math.abs(x)%Math.PI)<.5}function _x(x,e,t){return x<e?e:x>t?t:x}const _=an;function mx(){const x=["origin","defineProperty","positionToLocalPoint","_text","hasBackgroundColor","end","setXYOnLink","nearest","originAlignPosition","restore","endArrow","beginArrow","_updateText","width","3918924vXeEKt","originOffset","_textArr","_computedStyle","black","resizeToFitImage","dirty","_originInParent","get","height","rotate","_calcTextPosition","scaleBy","textAlign","translate","hasImage","准备删除的方法","setXY","backgroundColor","no-repeat","rotateCenter","strokeStyle","fillStyle","parent","resourcesIndexMap","image","center","obbPoints","rotateWithParent","min","_calcTextSize","_textHeight","_textDirty","6iDkKiB","scaleTo","mousePickupPath","1137252LFxOmt","lineWidth","drawImage","resize","_zIndex","zoom","11KnwPlo","1394080jupyQQ","getOwnPropertyDescriptor","translatePositionTo","borderWidth","textRotation","_drawContentDesc","imageSmoothingEnabled","positions","_calc_originInParent","setOrigin","26776dEGgfz","1023720pmJvKv","pickType","translateWith","6NCxgNs","push","scaleX","_isMatrixDirty","_afterStyleComputed","setText","cos","_updateShapeSize","Rect","getImage","forEach","hasBorder","repeat-x","_calcOriginInParentLink","onlyText","shape","zoomOut","originAutoRotate","objCache","skew","_textWidth","split","fillRect","66577EsYGKh","_imageSrc","updateMatrix","prototype","1494CjaLmF","fillText","borderColor","isNode","_textLineHeight","getPoint","_textPosition","_image","tagName","_paintText","serializers","scale","setLineDash","rotation","right","position not exist:","90lTqPFr","rect","196998yHWDqy","textBaseline","getCtrlPoints","scaleY","textOffsetY","alignOriginToNode","save"];return mx=function(){return x},mx()}function an(x,e){const t=mx();return an=function(n,s){return n=n-374,t[n]},an(x,e)}(function(x,e){const t=an,n=x();for(;[];)try{if(parseInt(t(395))/1*(parseInt(t(449))/2)+parseInt(t(473))/3*(parseInt(t(452))/4)+-parseInt(t(459))/5+-parseInt(t(393))/6*(parseInt(t(496))/7)+-parseInt(t(469))/8*(parseInt(t(377))/9)+-parseInt(t(470))/10+-parseInt(t(458))/11*(-parseInt(t(416))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(mx,406742);var Xo=Object[_(403)],Yo=Object[_(460)],G=(x,e,t,n)=>{for(var s=n>1?void 0:n?Yo(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Xo(e,t,s),s};const Ar=class extends L{constructor(x,e=0,t=0,n=1,s=1){const i=_;super(),this._drawContentDesc={hasBorder:![],hasBackgroundColor:![],hasBackgroundImage:![],hasShape:![],hasImage:![],onlyImage:![],onlyText:![],shapeSize:{width:1,height:1}},this[i(443)]=[{x:0,y:0},{x:0,y:0},{x:0,y:0},{x:0,y:0}],this[i(423)]={x:0,y:0},x!=null&&(this.text=x,this._textDirty=!![]),this._x=e||0,this._y=t||0,this._width=n||0,this._height=s||0}get[_(490)](){return this.rotateWithParent}set originAutoRotate(x){this.rotateWithParent=x}get text(){return this._text}set text(x){this[_(478)](x)}get image(){return this._image}set image(x){this.setImage(x)}get imageSrc(){return this._imageSrc}set imageSrc(x){x=="canvas"&&(x=null),this.setImage(x),this._imageSrc=x}setOrigin(x,e){const t=_;return this.origin[0]=x,this[t(402)][1]=e,this.matrixDirty=!![],this}[_(408)](x,e){return this.origin[0]=x,this.origin[1]=e,this.matrixDirty=!![],this}setShape(x){this.shape=x,this.matrixDirty=!![]}alignOriginToLink(x,e,t,n){const s=_;n!=null&&(this.originOffset=n),this[s(410)]=x,this.setRotateCenter(x),e!=null&&this.setXYOnLink(e,this.origin[1]),t!=null&&this.setXYOnLink(this.origin[0],t),this.matrixDirty=!![]}[_(400)](x,e=_(442)){const t=_;let n=qt[e];this[t(468)](n.x,n.y),this[t(410)]=x,this.matrixDirty=!![]}_OBBPoints(){let x=this.width*.5,e=this.height*.5,t=-x,n=-e;return[{x:t,y:n},{x,y:n},{x,y:e},{x:t,y:e}]}[_(477)](){const x=_,e=this._computedStyle,t=this._drawContentDesc;t.hasBackgroundColor=e[x(434)]!=null,t.hasBorder=e[x(462)]>0,t.hasBackgroundImage=e._backgroundImageObject!=null,t.hasImage=this[x(384)]!=null,t.hasShape=e.lineWidth>0&&e.strokeStyle!=null||e.fillStyle!=null,t.onlyImage=(this._image||t.hasBackgroundImage)&&!t.hasBorder&&!t.hasShape&&!t.hasBackgroundColor,t[x(487)]=this._text!=null&&!t[x(431)]&&!t.hasBackgroundImage&&!t.hasBorder&&!t.hasShape&&!t[x(406)]}_afterUpdateMatrix(){this._updateShapeSize()}_afterUpdate(){const x=_;(this[x(476)]()||this._textDirty||this._computedStyle[x(448)])&&(this._updateText(),this._textDirty&&(this[x(448)]=![],this.updateMatrix()),this[x(448)]=![],this._computedStyle._textDirty=![])}[_(467)](){const x=_,e=this[x(439)];if(e.isLink)return this[x(486)]();let t=this.originAlignPosition;if(this.origin[0]==0&&this.origin[1]==0&&t==null)return this.origin;let s=[this.origin[0]*e.width,this.origin[1]*e.height];if(t!=null){let i=this.positionToLocalPoint(t);s[0]-=i.x,s[1]-=i.y}return s}_calcOriginInParentLink(){const x=_,e=this.parent,t=this;if(this===e.beginArrow||t===e[x(412)])return[0,0];let n=t.origin[0],s=t[x(402)][1],i=e.getPoint(s,n);if(t.originOffset){let o=this._getOriginRotation();i={x:i.x+t.originOffset*Math[x(479)](o),y:i.y+t.originOffset*Math.sin(o)}}let r=t.originAlignPosition;if(r!=null){let o=t.positionToLocalPoint(r);i.x-=o.x,i.y-=o.y}return[i.x,i.y]}_getOriginRotation(){const x=_,e=this.parent;if(e==null||e.isLayer||e[x(380)]||!this.rotateWithParent)return 0;let t=e;if(this===t[x(413)]||this===t.endArrow)return 0;let n=this[x(402)][0],s=this[x(402)][1];return e.getK(n,s)}_doTransform(x){const e=_,t=this;if(t[e(380)]&&t[e(439)]==null)return;if(t.isLink)throw new Error("link no transform");let n=t[e(467)](),s=n[0]+t.x,i=n[1]+t.y;x[e(430)](s,i),(t.skewX!=0||t.skewY!=0)&&x.transform(1,t.skewX,t.skewY,1,0,0),(t.scaleX!==1||t.scaleY!==1)&&x[e(388)](t.scaleX,t[e(398)]);let r=t._getOriginRotation()+t[e(390)];if(r!=0)if(t[e(436)]=="center")x.rotate(r);else{let o=t.positionToLocalPoint(t.rotateCenter);x.translate(o.x,o.y),x.rotate(r),x.translate(-o.x,-o.y)}}[_(480)](){const x=_,e=this[x(419)];let t=e.borderWidth||0,n=e.lineWidth||0,s=e.padding||0;const i=this[x(415)]-t*2-n-s*2,r=this.height-t*2-n-s*2;this[x(464)].shapeSize.width=i,this._drawContentDesc.shapeSize.height=r}getSegmentPoints(){const x=_,e=this._OBBPoints();return this.shape.isClosed&&e[x(474)](e[0]),e}[_(482)](x){const e=this;return new Promise((t,n)=>{const s=an;if(e[s(384)]!=null){x?x(e._image):t(e._image);return}let i=$[s(491)].get(e);if(i==null){x?x(null):n(null);return}x==null&&(x=function(){t(e._image)}),i.next=x})}setImage(x,e=![]){const t=_;let n=this,s=x;return $.cancelCallback(this),s==null||s==""?(this._imageSrc=null,this._image=null,n.style[t(422)]=!![],this):s[t(385)]=="IMG"?(this._imageSrc=s.getAttribute("src"),n._image=s,e==!![]&&n.resizeTo(s.width,s.height),n.style.dirty=!![],this):s[t(385)]=="CANVAS"?(this._imageSrc="canvas",this._image=s,e==!![]&&this.resizeTo(s[t(415)],s.height),n.style.dirty=!![],this):($.loadImageWithObj(this,s,function(i){const r=t;i!=null&&(n._image=i,n._imageSrc=i.getAttribute("src"),e==!![]&&n.resizeTo(i[r(415)],i.height),n.style.dirty=!![])}),this)}setText(x){const e=_;if(x!=this[e(405)]&&(this._textDirty=!![],this.matrixDirty=!![]),x==null){this._text=x,this[e(418)]=null;return}if(typeof x=="number"&&(x=""+x),x.indexOf(`
`)!=-1){let t=x[e(494)](`
`);this._textArr=t}else this[e(418)]=null;this._text=x}[_(446)](){const x=_;let e=this[x(418)]==null?1:this[x(418)].length,t;e==1?t=et.measureText(this._text,this._computedStyle,e):t=et.measureText(this._textArr,this._computedStyle,e),this[x(493)]=t.width,this._textHeight=t[x(425)],this[x(381)]=t.lineHeight}attr(x){const e=_;throw new Error(e(432))}[_(421)](){this._image!=null&&this._image.width!=null&&this.resizeTo(this._image.width,this._image.height)}drawShape(x){const e=_;if(this.shape.isUnit){let t=this._drawContentDesc.shapeSize;x.scale(t.width,t.height),this[e(488)].draw(x,this),x[e(388)](1/t.width,1/t[e(425)])}else this.shape.draw(x,this)}_drawBackgroundOrBorder(x){const e=_,t=this[e(419)],n=this[e(464)];let s=t.borderWidth||0,i=-this.width*.5+s*.5,r=-this[e(425)]*.5+s*.5,o=this.width-s,a=this[e(425)]-s,c=t.borderRadius||0;x.beginPath(),c==0?x[e(394)](i,r,o,a):x.roundRect(i,r,o,a,c),n.hasBackgroundColor&&(x.fillStyle=t[e(434)],x.fill()),n[e(484)]&&(x.lineWidth=s,x.strokeStyle=t[e(379)]||e(420),(t.borderStyle=="dashed"||t.borderStyle=="dotted")&&x[e(389)]([1,1]),x.stroke())}_drawImage(x,e,t,n,s){const i=_;if(this[i(374)]!=null&&this._imageSrc.toLowerCase().endsWith("svg")){x[i(454)](e,-this[i(415)]*.5,-this.height*.5,this[i(415)],this.height);return}const r=this._computedStyle,o=Math.min(this.width,this[i(415)]-s-s),a=Math.min(this.height,this.height-s-s),c=r._getBackgroundRect(o,a,this);let l=-this.width*.5,h=-this.height*.5,u=l+c.x,g=h+c.y;const y=Math.min(o,c.width),m=Math[i(445)](a,c.height);let I=e[i(415)],b=e.height;const k=i(435);if(k=="no-repeat"){x[i(465)]=r[i(465)]==null?!![]:r.imageSmoothingEnabled,x[i(454)](e,0,0,I,b,u,g,y,m);return}let E=x.createPattern(e,k);x.fillStyle=E;let D=u%I,j=g%b;k==i(485)?j=0:k=="repeat-y"&&(D=0),x.translate(D,j),x[i(495)](l-I,h-b,o+I+I,a+b+b),x.translate(-D,-j)}_strokeAndFill(x){const e=_;let t=this._computedStyle,n=this._drawContentDesc;x.save(),(n.hasBorder||n.hasBackgroundColor)&&(this._drawBackgroundOrBorder(x),this[e(451)](x)),n.hasShape&&(x.beginPath(),this.drawShape(x),t.fillStyle!=null&&(x.fillStyle=t[e(438)],x.fill()),t.lineWidth>0&&t.strokeStyle!=null&&(x[e(453)]=t.lineWidth,x[e(437)]=t.strokeStyle,x.stroke()),this.isPointOn==![]&&this.pickType==e(488)&&(this.shape.isClosed?this[e(451)](x):this.mousePickupStroke(x,this._pickRadius)));const s=this[e(384)];if(s){n.hasShape&&x.clip();let i=t.borderWidth||0;this._drawImage(x,s,n.hasBorder,n.hasBackgroundColor,i)}x[e(411)]()}draw(x){const e=_;if(this.width<=0||this.height<=0)return;let t=this._drawContentDesc;if(this._strokeAndFill(x),t.onlyImage||t.onlyText||t.hasShape&&this.isPointOn==![]&&this[e(471)]==e(394)){let n=-this.width*.5,s=-this.height*.5;x[e(401)](),x.beginPath(),x.rect(n,s,this[e(415)],this.height),x.closePath(),this[e(451)](x),x[e(411)]()}this[e(386)](x)}[_(414)](){const x=_;this._calcTextSize(),this[x(427)]()}_calcTextPosition(x=0,e=0){const t=_;let n=this._computedStyle,s=null;n.textPosition!=null?s=this.positionToLocalPoint(n.textPosition):s={x:0,y:this.height*.5};let i=0,r=-(this[t(447)]-this._textLineHeight)*.5;return n[t(429)]=="left"?i=x+e:n.textAlign==t(391)&&(i=-(x+e)),n.textBaseline=="top"?r=x+e:n[t(396)]=="bottom"&&(r=-(x+e)-this._textHeight+this._textLineHeight),s.x+=i,s.y+=r,n.textOffsetX!=null&&(s.x+=n.textOffsetX),n.textOffsetY!=null&&(s.y+=n[t(399)]),this[t(383)]=s,s}_paintText(x){const e=_;let t=this._text;if(t==null)return null;let n=this._computedStyle,s=this._textPosition||{x:0,y:0},i=this[e(418)];if(x.fillStyle=n.color||"black",this[e(463)]==0)if(i==null)x[e(378)](t,s.x,s.y);else{let o=this[e(381)];for(var r=0;r<i.length;r++)x.fillText(i[r],s.x,s.y+r*o)}else{if(x.translate(s.x,s.y),x.rotate(this.textRotation),i==null)x.fillText(t,0,0);else{let o=this._textLineHeight;for(var r=0;r<i.length;r++)x.fillText(i[r],0,0+r*o)}x.rotate(-this.text),x.translate(-s.x,-s.y)}return s}getLinkChildren(x){const e=_;let t=[],n=this.outLinks;for(var s=0;s<n.length;s++){let i=n[s],r=i[e(407)].target;!R.hasChild(t,r)&&t[e(474)](r),x&&r instanceof Ar&&r.outLinks.length>0&&R.addAll(t,r.getLinkChildren(x))}return t}[_(409)](x,e){const t=this.getSegmentPoints(),n=Wi({x,y:e},{x:0,y:0},t);return n==null||n.length==0?{x,y:e}:n[0]}setRotateCenter(x){return this.rotateCenter=x,this}translateWith(x,e){const t=_;return this.x+=x,this.y+=e,this.inLinks[t(483)](n=>n.updateMatrix()),this.outLinks.forEach(n=>n[t(375)]()),this}translateTo(x,e){return this.x=x,this.y=e,this}translate(x,e){return this.x=x,this.y=e,this}setXY(x,e){return this.x=x,this.y=e,this}[_(461)](x,e,t){const n=_;let s=this[n(404)](x),i=e-this.x-s.x,r=t-this.y-s.y;this[n(472)](i,r)}resizeTo(x,e){return this.width=x,this.height=e,this}[_(455)](x,e){const t=_;return this.width=x,this[t(425)]=e,this}[_(492)](x,e){return this.skewX=x,this.skewY=e,this}resizeWith(x,e){const t=_;return this.width+=x,this.height+=e,this[t(415)]<0&&(this[t(415)]=0),this[t(425)]<0&&(this.height=0),this}[_(428)](x,e){return this.scaleX*=x,this.scaleY*=e,this}[_(457)](x,e,t,n){return this.width*this.scaleX,this.height*this.scaleY,this.scaleBy(x,e),this}[_(489)](){return this[_(457)](.8,.8),this}zoomIn(){return this[_(457)](1.25,1.25),this}[_(450)](x,e){const t=_;return this[t(475)]=x,this.scaleY=e,this}scale(x,e){return this.scaleX=x,this.scaleY=e,this}rotateTo(x){return this.rotation=x,this}[_(426)](x){return this.rotation=x,this}rotateWith(x){const e=_;return this[e(390)]+=x,this}getRect(){const x=_;return new z(this.x-this.width*.5,this.y-this[x(425)]*.5,this.width,this.height)}[_(382)](x,e){let t=this.getLocalPoint(x,e);return{x:-this.width*.5+t.x,y:-this.height+t.y}}changeParent(x){const e=_;if(this[e(439)]===x)throw new Error("same parent, dont need change");let t=this,n=t.toStageXY(0,0),s=x.stageToLocalXY(n.x,n.y);return t.parent&&t[e(439)].removeChild(t),t[e(433)](s.x,s.y),x.addChild(t),this}setOriginToPosition(x){const e=_;let t=qt[x];return this.origin[0]=t.x,this[e(402)][1]=t.y,this}positionToLocalPoint(x,e,t){const n=_;let s=this[n(466)][x];if(s==null&&(s=qt[x]),s==null)throw Error(n(392)+x);return typeof s=="function"?s.call(this,e,t):{x:s.x*this.width,y:s.y*this.height}}getPositionNormal(x){const e=_;let t=this.positionToLocalPoint(S.center),n=this[e(404)](x);return z.normal(t,n)}toJSON(x){const e=_;let t=super.toJSON(x);if(x!=null){let n=x.shapeIndexMap,s=x[e(440)],i=s[this.imageSrc];if(i!=null&&(t[e(441)]=i),this.isNode){let r=n[e(424)](this[e(488)]);t.shape=r}}return t}destory(){super.destory(),this.shape=void 0,this._text=void 0,this._textArr=void 0,this._image=void 0,this._shapePoints=void 0}};let M=Ar;G([f("Node")],M[_(376)],"className",2),G([f(A[_(481)])],M.prototype,_(488),2),G([f(null)],M[_(376)],"originAlignPosition",2),G([f(!![])],M[_(376)],"rotateWithParent",2),G([f(_(442))],M.prototype,"rotateCenter",2),G([f(0)],M.prototype,_(417),2),G([f(null)],M.prototype,"_text",2),G([f(null)],M[_(376)],"_textArr",2),G([f(![])],M[_(376)],_(448),2),G([f(0)],M[_(376)],"_textWidth",2),G([f(0)],M.prototype,"_textHeight",2),G([f(0)],M.prototype,"_textLineHeight",2),G([f(!![])],M.prototype,_(380),2),G([f(i0.Node)],M.prototype,_(456),2),G([f(0)],M.prototype,"textRotation",2),G([f(["className",_(380)])],M[_(376)],"allwaysSerializers",2),G([f(L.prototype.serializers.concat(["text","x","y",_(415),"height","shape","scaleX","scaleY",_(390),"originOffset","textOffsetX",_(399),_(463),"originAlignPosition","rotateCenter",_(444),"originAutoRotate"]))],M[_(376)],_(387),2),G([f(function(){return this[_(488)].ctrlPoints})],M.prototype,_(397),2),G([f(function(){return this.shape.anchorPoints})],M.prototype,"getAnchorPoints",2),G([f(_(488))],M.prototype,"pickType",2);const nt=Ne;(function(x,e){const t=Ne,n=x();for(;[];)try{if(-parseInt(t(183))/1*(-parseInt(t(182))/2)+-parseInt(t(177))/3+-parseInt(t(190))/4+parseInt(t(188))/5+parseInt(t(196))/6*(-parseInt(t(178))/7)+-parseInt(t(194))/8*(-parseInt(t(179))/9)+parseInt(t(201))/10*(-parseInt(t(199))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(wx,503266);var Jo=Object.defineProperty,Vo=Object.getOwnPropertyDescriptor,b0=(x,e,t,n)=>{const s=Ne;for(var i=n>1?void 0:n?Vo(e,t):e,r=x[s(184)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&Jo(e,t,i),i};function Ne(x,e){const t=wx();return Ne=function(n,s){return n=n-175,t[n]},Ne(x,e)}class Ct{isDisplayObjectTarget(){return this.target instanceof L}isNodeTarget(){return this instanceof he||this instanceof y0||this instanceof ue?this.target.isNode:![]}hasTarget(){return this.target!=null}toJSON(e){const t=Ne;let n=this.className,s=Object[t(191)]({className:n},this);if(this.target==null)delete s.target;else if(e!=null&&this[t(189)]()){let i=e[t(176)](this.target);s[t(192)]=i}return s}static fromJSON(e,t){const n=Ne;let s=c0(e.className,e);if(t!=null&&typeof e.target=="number"){let i=t[e.target];s[n(192)]=i}return s}}b0([f("Endpoint")],Ct.prototype,"className",2);class y0 extends Ct{constructor(e,t,n=0){super(),this.target=e,this.t=t,this.segIndex=n}}b0([f("EndpointSegment")],y0[nt(181)],nt(197),2);class he extends Ct{constructor(e,t){const n=nt;super(),this[n(192)]=e,this.name=t}getAngle(){return gi(this[nt(198)])+Math.PI}[nt(195)](){return this[nt(192)].isNode?pi(this.name):[0,0]}}b0([f(nt(185))],he.prototype,"className",2),he.prototype[nt(197)]="EndpointFixedName";class ue extends Ct{constructor(e){super(),this.target=e}getAngle(e){const t=nt;let n=0;if(this.target.isNode){let s=this[t(192)];n=Math.atan2(e.y-s.y,e.x-s.x)}return n}}b0([f("EndpointNearest")],ue.prototype,nt(197),2),ue[nt(181)].className=nt(187);class Rt extends Ct{constructor(e,t,n){super(),this.x=e,this.y=t,this.target=n}}b0([f("EndpointFixedPoint")],Rt[nt(181)],"className",2);class cn extends Ct{constructor(e){super(),this.fn=e}toJSON(){let e=this.className,t=this.fn();return Object.assign({className:e,x:t.x,y:t.y},this)}}b0([f("EndpointFunction")],cn.prototype,"className",2);function Wt(x,e){const t=nt;if(x==null)throw new Error("target is null");if(x instanceof Ct)return x;if(e instanceof Ct)return e;if(x instanceof L)return e==S.nearest?new ue(x):new he(x,e||S[t(186)]);if(C.isLikePoint(x)){let n=x;return new Rt(n.x,n.y)}else{if(typeof x=="function")return new cn(x);throw console.log(x,e),new Error("error arguments")}}const Go={lm:[-1,0],ct:[0,-1],rm:[1,0],cb:[0,1]};function Ix(x){const e=nt;if(x==null)return;let t=x.target;if(typeof t!=e(200)){if(x instanceof he)return t.className=="ShapeNode"?t[e(180)](x.name):Go[x.name];if(x instanceof y0){let n=x.segIndex,s=x.t;if(t.className=="ShapeNode"){let i=t.positionToLocalPoint(S.center),r=t.getLocalPoint(s,n);return z.normal(i,r)}else if(t instanceof Node){if(n==0)return[0,-1];if(n==1)return[1,0];if(n==2)return[0,1];if(n==3)return[-1,0];throw new Error(e(193)+n)}if(t.isAutoFoldLink){let i=t[e(175)][n],r=t.points[n+1],o=[r.x-i.x,r.y-i.y];return B.normalize([],o)}}}}function wx(){const x=["get","1263210lHbcPz","1547938dRdCZY","9TzFKMr","getPositionNormal","prototype","443052DHDZRU","2qHtxuR","length","EndpointFixedName","center","EndpointNearest","4762430lmRxMN","isDisplayObjectTarget","2218520xGmOut","assign","target","unkow segIndex:","6547184tIyMja","getVec","6oGuJaR","className","name","418KXidOU","function","135220FzfcRv","points"];return wx=function(){return x},wx()}const _0=Re;(function(x,e){const t=Re,n=x();for(;[];)try{if(-parseInt(t(455))/1+parseInt(t(440))/2+-parseInt(t(451))/3+parseInt(t(446))/4+parseInt(t(443))/5*(parseInt(t(452))/6)+parseInt(t(442))/7*(parseInt(t(447))/8)+-parseInt(t(453))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(vx,495292);function vx(){const x=["632328NVcLQX","endpoints","length","splice","975393SpaPFj","1038BBlIMh","2452419NdMMIg","target","887143FQicHq","582050HuubEU","getEnd","14PGasVR","22270ZwGDTM","remove","setEnd","3041632wzxcTV"];return vx=function(){return x},vx()}function Re(x,e){const t=vx();return Re=function(n,s){return n=n-440,t[n]},Re(x,e)}class qo{constructor(){this.endpoints=[]}toJSON(e,t){let n=t.getPoints();return this.endpoints.map((i,r)=>{const o=Re;let a=i.toJSON(e);if(a.hasOwnProperty(o(454))&&a.target==null){let c=n[0];t instanceof qs?c=n[r]:r>0&&(c=n[n.length-1]);let l={className:"EndpointFixedPoint",x:c.x,y:c.y};return console.log("rs",r,n,l),l}return a})}set(e){const t=Re,n=this[t(448)];n.length=0;for(let s=0;s<e.length;s++){let i=Wt(e[s]);n.push(i)}return n}insert(e,t){const n=Re;let s=Wt(e);return t==null&&(t=this.endpoints[n(449)]),t+1>=this.endpoints.length?this[n(448)].push(s):this.endpoints.splice(t+1,0,s),s}replace(e,t){this.endpoints[t]!=null&&(this.endpoints[t]=Wt(e))}[_0(444)](e){const t=_0;this[t(448)][t(449)]<=2||this.endpoints[t(450)](e,1)}setBegin(e){this.endpoints[0]=Wt(e)}[_0(445)](e){const t=_0;if(this.endpoints.length<2){this[t(448)][1]=Wt(e);return}let n=this.endpoints.length-1;this.endpoints[n]=Wt(e)}getLength(){const e=_0;return this[e(448)][e(449)]}getBegin(){return this.endpoints[0]}[_0(441)](){let e=this.endpoints.length-1;return this.endpoints[e]}}const w=Px;(function(x,e){const t=Px,n=x();for(;[];)try{if(-parseInt(t(500))/1*(parseInt(t(516))/2)+parseInt(t(509))/3+-parseInt(t(553))/4+-parseInt(t(530))/5+parseInt(t(538))/6*(parseInt(t(557))/7)+-parseInt(t(494))/8+parseInt(t(562))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Sx,514200);var Uo=Object.defineProperty,Zo=Object[w(544)],Ht=(x,e,t,n)=>{for(var s=n>1?void 0:n?Zo(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Uo(e,t,s),s};const kx={};kx[S.begin]=function(){return this.getPoints()[0]},kx[S.end]=function(){const x=w;let e=this.getPoints();return e[e[x(529)]-1]},kx[S.center]=function(){const x=w;let e=this.getPoints();return C[x(578)](e[0],e[e.length-1])};class W extends L{constructor(e,t,n,s,i){const r=w;if(super(),this._drawContentDesc={hasBorder:![],hasBackgroundColor:![]},this[r(539)]=e,this.path=new qo,t==null){const o=new Rt(0,0);this.path[r(540)][r(588)](o)}else this[r(506)](t,s);if(n==null){const o=new Rt(0,0);this.path[r(540)][r(588)](o)}else this.setEnd(n,i)}get begin(){return this[w(537)].getBegin()}get end(){return this.path.getEnd()}get[w(539)](){const e=w;return this[e(551)]!=null?this.label[e(539)]:this._text}set text(e){const t=w;this[t(571)](e),this[t(526)]=e}setLabel(e){const t=w;return e==null&&this.label==null?this:e instanceof M?(dt[t(535)][t(495)](this,e),e.mouseEnabled=![],e):this[t(551)]!=null&&typeof e=="string"?(this.label.text=e,this):(dt.LinkHelper[t(517)](this,e),this)}attr(e){const t=w;throw new Error(t(528))}getLabel(){return this[w(551)]}getBeginArrow(){return this.beginArrow}setBeginArrow(e){const t=w;if(!(e instanceof M))throw new Error("arrow must be Node");let n=this;return n[t(582)]!=null&&n[t(523)](n.beginArrow),n.children.indexOf(e)==-1&&n.addChild(e),e.draggable=![],e.connectable=![],e.editable=![],n[t(582)]=e,this.matrixDirty=!![],this}getEndArrow(){return this[w(559)]}setEndArrow(e){const t=w;if(!(e instanceof M))throw new Error("arrow must be Node");let n=this;return n[t(559)]!=null&&n[t(523)](n[t(559)]),n[t(503)].indexOf(e)==-1&&n.addChild(e),e.draggable=![],e.connectable=![],e.editable=![],n.endArrow=e,this.matrixDirty=!![],this}[w(548)](){return this.getPoints()}setBegin(e,t){const n=w;this.matrixDirty=!![],this[n(533)]&&this.begin.isDisplayObjectTarget()&&this[n(533)].target.removeOutLink(this);const s=Wt(e,t);this.path.setBegin(s),s[n(512)]instanceof L&&s.target[n(545)](this)}[w(499)](e,t){const n=w;this[n(519)]=!![],this.end&&this[n(568)].isDisplayObjectTarget()&&this.end[n(512)][n(532)](this);const s=Wt(e,t);this.path.setEnd(s),s.target instanceof L&&s.target.addInLink(this)}getBeginPoint(){const e=w;return this.points==null&&(this[e(564)]=this.updatePoints()),this[e(564)][0]}[w(522)](){return this.points==null&&(this.points=this.updatePoints()),this.points[this.points.length-1]}drawPoints(e){return this[w(576)](e)}drawShape(e){const t=w;let n=this[t(564)];if(this._needCalcOffset()&&(n=this.offsetPoints,n==null))throw new Error("offsetPoints is null");let s=Bt.toAABB(n),i=s.getCenter(),r=Math.max(1,Math[t(585)](s[t(515)],s.width));e.beginPath(),e.translate(i.x,i.y),e.scale(r,r);let o=R.getPointsNormalization(n);this[t(536)].draw(e,this,o),e.scale(1/r,1/r),e[t(502)](-i.x,-i.y),e[t(525)]()}[w(556)](){const e=w,t=this[e(547)],n=this._computedStyle;let s=n.lineWidth||0;return s+=n[e(555)]||0,t.hasBorder&&(s+=n.borderWidth),s}draw(e){const t=w;if(this.begin==null||this.end==null)return this;if(this.drawShape(e),!this.isPointOn){const n=this._pickRadius,s=this._computedStyle[t(508)]>=n?0:n;this[t(572)](e,s)}return this}_afterStyleComputed(){const e=w,t=this._computedStyle,n=this[e(547)];n.hasBackgroundColor=t[e(524)]!=null,n[e(579)]=t.borderWidth>0}setBeginOffset(e){const t=w;this[t(542)]=e,this[t(519)]=!![]}setEndOffset(e){const t=w;this[t(514)]=e,this.matrixDirty=!![]}[w(586)](e,t){const n=w;let s=this.path[n(540)];for(let i=0;i<s[n(529)];i++){let r=s[i];r instanceof Rt&&r.target==null&&(r.x+=e,r.y+=t,this.matrixDirty=!![])}return this[n(519)]&&(this[n(590)](),this.inLinks.forEach(i=>i.updateMatrix()),this.outLinks.forEach(i=>i.updateMatrix())),this}_calcAZ(){let e=We(this,this.begin),t=We(this,this.end);return[e,t]}_needCalcOffset(){const e=w;return this[e(542)]!=null&&this.beginOffset!=0?!![]:this.endOffset!=null&&this[e(514)]!=0?!![]:this[e(582)]!=null&&this.beginArrow.visible?!![]:this.endArrow!=null&&this.endArrow.visible?!![]:![]}_offsetA(e,t){let n=this.beginArrow,s=this.beginOffset||0;if(n!=null&&n.visible&&(s=s+n.width),s!=0){let i=this.getBeginArrowDirection();this._setOffsetByVec(e,i,s)}}[w(574)](e,t){const n=w;let s=this.endArrow,i=this[n(514)]||0;if(s!=null&&s.visible&&(i=i+s.width),i!=0){let r=this.getEndArrowDirection();this._setOffsetByVec(t,r,i)}}_setOffsetByVec(e,t,n){let s=[0,0];B.multiplyC(s,t,-n),e.x+=s[0],e.y+=s[1]}_notUpdateYet(){console[w(534)]("not yet"),this.points=this.updatePoints()}_afterUpdateMatrix(){this.points=this.updatePoints(),this._offsetAndArrowHandle()}_offsetAndArrowHandle(){const e=w;if(this[e(582)]&&this[e(582)].parent!==this&&(this[e(582)]=null),this.endArrow&&this.endArrow[e(520)]!==this&&(this.endArrow=null),!this[e(505)]())return;let t=this.points[0],n=this[e(564)][this[e(564)].length-1],s={x:t.x,y:t.y},i={x:n.x,y:n.y};if(this._offsetA(s,i),this[e(574)](s,i),this.beginArrow){let o={x:(t.x+s.x)*.5,y:(t.y+s.y)*.5},a=Math[e(501)](t.y-o.y,t.x-o.x);this[e(582)].setXY(o.x,o.y),this.beginArrow.rotateTo(a)}if(this.endArrow){let o={x:(n.x+i.x)*.5,y:(n.y+i.y)*.5},a=Math.atan2(n.y-o.y,n.x-o.x);this.endArrow.setXY(o.x,o.y),this.endArrow.rotateTo(a)}let r=this._obb.localPoints[e(518)]();r=r.slice(),r[0]=s,r[r.length-1]=i,this.offsetPoints=r}updatePoints(){const e=w;let t=[];const n=this[e(537)][e(540)];for(let s=0;s<n.length;s++){let i=n[s],r=We(this,i);t.push(r)}return t}[w(580)](){const e=w;return this.points==null&&(this.points=this[e(531)]()),this.points}getTransform(){return this[w(549)]}[w(583)](){}nearest(e,t){const n=this.getSegmentPoints(),s=bx({x:e,y:t},n);return s==null||s.length==0?{x:e,y:t}:s}getSegmentPoints(){return this[w(580)]()}upgradeParent(){const e=w;let t=this.begin.target,n=this.end.target;if(t==null||n==null)return;let s=Ox(t,n);if(this[e(520)]!==s)return this[e(581)](s),s}[w(511)](){const e=w;let t=this[e(564)][0],n=this[e(510)](1e-7),s=[t.x-n.x,t.y-n.y];return B.normalize(s,s)}[w(575)](){const e=w;let t=this.getPoint(.999999),n=this[e(564)][this.points[e(529)]-1],s=[n.x-t.x,n.y-t.y];return B.normalize(s,s)}[w(554)](){return this.begin.isDisplayObjectTarget()||this.end.isDisplayObjectTarget()?![]:!![]}isBeginDisplayObject(){const e=w;return this[e(533)][e(493)]()}isEndDisplayObject(){const e=w;return this.end[e(493)]()}unlinkBegin(){this.begin.isDisplayObjectTarget()&&this.begin.target.removeOutLink(this),this.setBegin(this.getBeginPoint())}unlinkEnd(){this.end.isDisplayObjectTarget()&&this.end.target.removeInLink(this),this.setEnd(this.getEndPoint())}unlink(){const e=w;this.unlinkBegin(),this[e(550)](),this.matrixDirty=!![]}remove(){const e=w;return this.unlink(),super[e(584)](),this}[w(581)](e){const t=w;if(this.parent===e)throw new Error("same parent, dont need change");let n=this,s=this;function i(r,o,a){let c=r[o];c!=null&&(c=r.toStageXY(c.x,c.y),r[o]=a.stageToLocalXY(c.x,c.y))}if(s.begin instanceof Rt&&!s.begin[t(573)]()){let r=s.toStageXY(s.begin.x,s[t(533)].y),o=e[t(565)](r.x,r.y);s[t(506)](o)}if(s.end instanceof Rt&&!s.end.hasTarget()){let r=s.toStageXY(s.end.x,s.end.y),o=e.stageToLocalXY(r.x,r.y);s[t(499)](o)}return s[t(560)]=="CurveLink"?i(s,t(561),e):s[t(560)]=="BezierLink"?(i(s,"ctrlPoint1",e),i(s,"ctrlPoint2",e)):s.className==t(567)&&(i(s,"fold1Offset",e),i(s,"fold2Offset",e),i(s,t(570),e)),n.parent&&n.parent.removeChild(n),e.addChild(n),this}[w(541)](e,t,n){let s=this.positions[e];if(s==null&&(s=this.DefaultPositions[e]),s==null)throw Error("position not exist:"+e);return typeof s=="function"?s.call(this,t,n):s}[w(587)](e){const t=w;let n=super[t(587)](e);if(e!=null){let s=e.objIndexMap;n.path=this[t(537)][t(587)](s,this),this.label!=null&&(n[t(551)]=s[t(563)](this.label)),this.beginArrow!=null&&(n.beginArrow=s.get(this.beginArrow)),this[t(559)]!=null&&(n[t(559)]=s[t(563)](this[t(559)]))}return n}[w(507)](e,t){const n=w;if(e.label!=null){let r=t[e.label];this[n(551)]=r}if(e.beginArrow!=null){let r=t[e.beginArrow];r[n(513)]=null,r.rotateCenter=n(552),this.setBeginArrow(r)}if(e.endArrow!=null){let r=t[e.endArrow];r.originAlignPosition=null,r.rotateCenter="center",this.setEndArrow(r)}let s=e.path,i=s[n(569)](r=>Ct.fromJSON(r,t));this._setEndpoints(i)}_setEndpoints(e){const t=w;this.path.set(e);let n=this.path[t(521)]();n[t(493)]()&&n[t(512)].addOutLink(this),n=this.path.getEnd(),n[t(493)]()&&n[t(512)][t(589)](this),e=this[t(537)][t(540)];for(let s=1;s<e.length-1;s++)n=e[s],n.isDisplayObjectTarget()&&n[t(512)].addInLink(this)}destory(){const e=w;super.destory(),this.unlinkBegin(),this[e(550)](),this.beginArrow=null,this[e(559)]=null,this.label=null,this[e(536)]=null,this.points=null,this[e(504)]=null,this.offsetPoints=null,this._text=null}}Ht([f("Link")],W.prototype,"className",2),Ht([f(A.Line)],W.prototype,"shape",2),Ht([f(i0.Link)],W.prototype,"_zIndex",2),Ht([f(!![])],W.prototype,w(546),2),Ht([f(5)],W.prototype,"_pickRadius",2),Ht([f(kx)],W.prototype,"DefaultPositions",2),Ht([f(["className","isLink"])],W[w(498)],w(591),2),Ht([f(L[w(498)].serializers.concat(["beginOffset",w(514)]))],W.prototype,w(492),2),Ht([f(function(){const x=w;return this.shape[x(491)]})],W.prototype,"getAnchorPoints",2),Ht([f(function(){return this[w(536)].ctrlPoints})],W.prototype,"getCtrlPoints",2);function Sx(){const x=["target","originAlignPosition","endOffset","height","14dQHvzt","createLabel","slice","matrixDirty","parent","getBegin","getEndPoint","removeChild","backgroundColor","stroke","_text","Invalid link's position arguments","准备删除的方法","length","1729550QeBRuR","updatePoints","removeInLink","begin","log","LinkHelper","shape","path","4467714ukiPKf","text","endpoints","positionToLocalPoint","beginOffset","invert","getOwnPropertyDescriptor","addOutLink","isLink","_drawContentDesc","_OBBPoints","transform","unlinkEnd","label","center","2473488lmaYsa","isAlone","padding","_getTotalLineWidth","7myaZzp","name","endArrow","className","ctrlPoint","17583831bZviBT","get","points","stageToLocalXY","deep","AutoFoldLink","end","map","centerOffset","setLabel","mousePickupStroke","hasTarget","_offsetZ","getEndArrowDirection","drawShape","point","middle","hasBorder","getPoints","changeParent","beginArrow","_doTransform","remove","max","translateWith","toJSON","push","addInLink","updateMatrix","allwaysSerializers","anchorPoints","serializers","isDisplayObjectTarget","5652256muEkak","asLabel","copy","getWorldTransform","prototype","setEnd","132272gNlsqk","atan2","translate","children","unitPoints","_needCalcOffset","setBegin","_updateBeginEndAfterJson","lineWidth","1237620FseHUF","getPoint","getBeginArrowDirection"];return Sx=function(){return x},Sx()}function We(x,e){const t=w;let n;if(e instanceof he){let s=e.target;n=$o(x,s,e[t(558)])}else if(e instanceof ue)n=ta(x,e);else if(e instanceof y0){let s=e[t(512)],i=e.segIndex,r=e.t;n=Qo(x,s,i,r)}else if(e instanceof Rt){let s=e.target;s==null?n={x:e.x,y:e.y}:n=Ko(x,s,e.x,e.y)}else if(e instanceof cn)n=e.fn();else throw console.log(e),new Error(t(527));return n}function Ko(x,e,t,n){const s=w;let i=e.getWorldTransform().point({x:t,y:n});return i=x.getWorldTransform()[s(543)]().point(i),i}function Px(x,e){const t=Sx();return Px=function(n,s){return n=n-491,t[n]},Px(x,e)}function Qo(x,e,t,n){let s;return e.parent===x.parent?(s=e.getLocalPoint(n,t),s=e.getTransform().point(s)):(s=e.getLocalPoint(n,t),s=e.getWorldTransform().point(s),s=x.getWorldTransform().invert().point(s)),s}function $o(x,e,t){const n=w;let s;return e[n(520)]===x.parent?(s=e.positionToLocalPoint(t),e.isNode&&(s=e.getTransform()[n(577)](s))):(s=e.positionToLocalPoint(t),s=e[n(497)]().point(s),s=x.getWorldTransform()[n(543)]().point(s)),s}function ta(x,e){const t=w;if(x.deep==0)throw new Error("link's deep is 0!");const n=e.target,s=x[t(533)][t(512)]===n,i=s?x.end:x[t(533)],r=i.target;let o=x.getWorldTransform()[t(496)](),a;i instanceof ue?a=r.toLayerXY(0,0):(s?a=We(x,x.end):a=We(x,x.begin),a=o[t(577)](a));let c=n[t(497)]();a=c.copy()[t(543)]().point(a);let l=n.nearest(a.x,a.y);return l=c[t(577)](l),l=o[t(543)]().point(l),l}function Ox(x,e){const t=w;return x[t(520)]===e[t(520)]?x.parent:x[t(566)]==e[t(566)]?Ox(x[t(520)],e.parent):x[t(566)]>e.deep?Ox(x.parent,e):Ox(x,e[t(520)])}var ea=ln;function Ex(){var x=["14782059UQQrTB","24cCiEfR","5928708KddOIF","102326JVOVcy","1548MmBvHc","2VEJYRX","368GTfDPg","582583FQPEDe","3585144VHbScK","stoped","10tgpaxu","dispose","58145odlcbV","58223Vtxqcy"];return Ex=function(){return x},Ex()}(function(x,e){for(var t=ln,n=x();[];)try{var s=-parseInt(t(185))/1*(-parseInt(t(183))/2)+-parseInt(t(186))/3+-parseInt(t(194))/4+parseInt(t(190))/5*(parseInt(t(193))/6)+-parseInt(t(195))/7*(-parseInt(t(184))/8)+parseInt(t(192))/9*(parseInt(t(188))/10)+-parseInt(t(191))/11*(-parseInt(t(182))/12);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ex,949550);function ln(x,e){var t=Ex();return ln=function(n,s){n=n-182;var i=t[n];return i},ln(x,e)}class na extends wt{constructor(){var e=ln;super(),this[e(187)]=![]}[ea(189)](){}}const F=hn;(function(x,e){const t=hn,n=x();for(;[];)try{if(parseInt(t(463))/1*(parseInt(t(472))/2)+-parseInt(t(486))/3+-parseInt(t(471))/4*(-parseInt(t(469))/5)+-parseInt(t(481))/6*(parseInt(t(488))/7)+-parseInt(t(474))/8+-parseInt(t(482))/9*(-parseInt(t(485))/10)+parseInt(t(483))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Lx,308598);function hn(x,e){const t=Lx();return hn=function(n,s){return n=n-462,t[n]},hn(x,e)}function Lx(){const x=["342BXQMpk","225loqCUe","2841575TdaOUk","name","117010dmmzKS","419601BtBNIx","length","37303irzuDj","setStyle","#009A93","54kwbBoB","content","1px solid rgba(255,255,255,0.5)","top","assign","white","5VImidC","FlexionalLink","863932vNVMBg","2432exhkHh","stringify","642232gWjdYy","black","center","RatioNode","toJSON"," repeat","rgba(0,0,236,0.1)"];return Lx=function(){return x},Lx()}class Mr{constructor(e){const t=hn;if(this.Layer={},this.SelectArea={border:"1px rgba(0,0,0,0.5)",backgroundColor:"rgba(0,0,236,0.1)"},this.SelectedStyle={strokeStyle:t(462),shadowColor:"#009A93"},this.LinkArrow={},this.LinkLabel={textPosition:"center",textBaseline:"middle",textAlign:"center"},this.Node={textPosition:"cb",textAlign:"center",textBaseline:"top"},this.TextNode={textPosition:t(476),textAlign:"center",textBaseline:"middle"},this.CircleNode={textPosition:"cb",textAlign:"center",textBaseline:t(466)},this.TipNode={textPosition:"ct",textAlign:"center",textBaseline:t(466)},this.ShapeNode={lineWidth:1,textPosition:"cb",textAlign:"center",textBaseline:"top"},this.VideoNode={textPosition:"cb",textAlign:"center",textBaseline:t(466)},this[t(477)]={textPosition:"center",textAlign:"center",textBaseline:"middle"},this.Link={lineWidth:1},this.FoldLink={lineWidth:1},this.CurveLink={lineWidth:1},this.AutoFoldLink={lineWidth:1},this.BezierLink={lineWidth:1},this.ArcLink={lineWidth:1},this[t(470)]={lineWidth:1},e==null)return;let n=Object.keys(e);for(let s=0;s<n.length;s++){let i=n[s];Object[t(467)](this[i],e[i])}}[F(478)](){const e=F;let t={},n=Object.keys(this);for(let s=0;s<n[e(487)];s++){let i=n[s],r=xa[i],o=this[i],a=kt.diff(o,r);a!=null&&(t[i]=a)}return t}}const xa=new Mr;class He{constructor(e,t){const n=F;this[n(484)]=e,this.content=new Mr(t)}copy(e){let n=JSON.parse(JSON[F(473)](this.content));return new He(e,n)}[F(489)](e,t){let n=this.content[e];Object.assign(n,t)}getStyle(e){return this[F(464)][e]}}let de=new He(se.DefaultLightName,{Layer:{background:Tt.createLightGridImg()+F(479)},SelectArea:{border:"1px solid rgba(0,0,0,0.5)",backgroundColor:F(480)},LinkArrow:{strokeStyle:"black"},Node:{strokeStyle:F(475)},TextNode:{strokeStyle:"black"},TipNode:{strokeStyle:"black"},ShapeNode:{strokeStyle:"black"},CircleNode:{strokeStyle:"black"},VideoNode:{strokeStyle:"black"},RatioNode:{strokeStyle:"black"},Link:{strokeStyle:"black"},AutoFoldLink:{strokeStyle:F(475)},FoldLink:{strokeStyle:"black"},FlexionalLink:{strokeStyle:F(475)},CurveLink:{strokeStyle:F(475)},BezierLink:{strokeStyle:"black"},ArcLink:{strokeStyle:F(475)}}),Hi=new He(se.DefaultDarkName,{Layer:{background:Tt.createDarkGridImg()+F(479)},SelectArea:{border:F(465),backgroundColor:"rgba(255,255,255,0.2)"},LinkArrow:{strokeStyle:F(468)},LinkLabel:{strokeStyle:"white",color:"white"},Node:{strokeStyle:F(468),color:F(468)},TextNode:{strokeStyle:"white",color:"white"},TipNode:{strokeStyle:F(468),color:"white"},ShapeNode:{strokeStyle:"white",color:F(468)},CircleNode:{strokeStyle:"white",color:"white"},VideoNode:{strokeStyle:"white",color:"white"},RatioNode:{strokeStyle:F(468),color:"white"},Link:{strokeStyle:"white"},AutoFoldLink:{strokeStyle:"white"},FoldLink:{strokeStyle:F(468)},FlexionalLink:{strokeStyle:F(468)},CurveLink:{strokeStyle:F(468)},BezierLink:{strokeStyle:F(468)},ArcLink:{strokeStyle:"white"}});const Y=Fe;function Tx(){const x=["transform","toDataURL","selectedStyle","context","300GmVcvf","2218782fbGjhY","styleSystem","createElement","1058029IpfLYF","beginPath","isPointInPath","63rwxhiz","children","stroke","1497bmPlBH","isSelected","56859BsgPWi","isMouseInPath","10172RbvDOx","hide","style","length","_needPaint","canvas","closePath","clearAll","SelectedStyle","width","isMouseInStroke","restore","isPointInStroke","remove","paintSelected","lineTo","_computedStyle","render","currentTheme","height","strokeStyle","getStyle","save","paint","roundRect","getContext","_isMouseInAABB","980uuubbz","parent","inputSystem","1537488ylSgGr","stage","30408TpChye","painted","quadraticCurveTo","1317452MMEbYu"];return Tx=function(){return x},Tx()}(function(x,e){const t=Fe,n=x();for(;[];)try{if(parseInt(t(362))/1+parseInt(t(368))/2+-parseInt(t(377))/3*(parseInt(t(381))/4)+-parseInt(t(408))/5*(-parseInt(t(359))/6)+-parseInt(t(371))/7+parseInt(t(357))/8*(-parseInt(t(374))/9)+parseInt(t(367))/10*(parseInt(t(379))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Tx,809835);function Fe(x,e){const t=Tx();return Fe=function(n,s){return n=n-355,t[n]},Fe(x,e)}class Dr extends na{constructor(e){const t=Fe;super(),this.destoryed=![],this.layer=e,this.canvas=document[t(370)]("canvas"),Object.assign(this.canvas.style,{position:"absolute",left:"0px"}),this.context=this.canvas[t(406)]("2d"),this.context[t(398)]=this,window.devicePixelRatio&&this[t(366)].scale(window.devicePixelRatio,window.devicePixelRatio),this.contextExtends()}_paintFlattenObjects(e){const t=Fe;let n=this[t(366)];for(let s=0;s<e[t(384)];s++){let i=e[s];if(n[t(403)](),i instanceof M){let r=i._worldTransform,o=r.m;n.transform(o[0],o[1],o[2],o[3],o[4],o[5])}else if(!(i[t(355)]instanceof ot)){let o=i[t(355)]._worldTransform,a=o.m;n[t(363)](a[0],a[1],a[2],a[3],a[4],a[5])}this[t(404)](i),n.restore()}}_paintObjects(e,t){const n=Fe;let s=this.layer,i=this[n(366)];for(let r=0;r<e.length;r++){let o=e[r];if(o[n(385)]==![]||o._isOutOfViewport&&!t)continue;i.save(),o instanceof M&&o._doTransform(i),!t&&(o[n(360)]=!![]),this.paint(o),s&&s.displayList.push(o),this._paintObjects(o[n(375)],t),i.restore()}}[Y(404)](e){const t=Y;let n=this[t(366)];e[t(397)].applyTo(n);let s=e[t(378)]&&e.showSelected==!![];s&&(n.save(),this.paintSelected(e)),e.draw(n),s&&n.restore()}[Y(395)](e){const t=Y;let n=this.context;if(e[t(365)]!=null&&e.selectedStyle.applyTo(n),e[t(395)]!=null){e.paintSelected(n);return}if(e instanceof M){n.save(),n.beginPath();let s=1;e.selectedStyle!=null?s=e.selectedStyle.lineWidth|s:(n.setLineDash([0,0]),this.styleSystem?n[t(401)]=this[t(369)][t(399)][t(402)]("SelectedStyle").strokeStyle:n[t(401)]=de.getStyle(t(389))[t(401)]),n.lineWidth=s;let i=e.width+s+3,r=e.height+s+3;n.rect(-i*.5,-r*.5,i,r),n[t(376)](),n[t(387)](),n[t(392)]()}else this[t(369)]?n.shadowColor=this[t(369)].currentTheme.getStyle(t(389)).strokeStyle:n.strokeStyle=de.getStyle("SelectedStyle").shadowColor,n.shadowBlur=5,n.shadowOffsetX=3,n.shadowOffsetY=3}[Y(388)](){this.clear()}dontNeedPickup(e){const t=Y;let n=this[t(358)];return n[t(356)].isDraging&&n[t(356)].target!==e?!![]:n.inputSystem.event instanceof MouseEvent&&!e[t(407)]?!![]:![]}[Y(391)](e,t){const n=Y;let s=this.context,i=this.stage;if(e==null||e==0)return t!=null?s[n(393)](t,i.inputSystem.x,i[n(356)].y):s.isPointInStroke(i.inputSystem.x,i.inputSystem.y);let r=![];for(var o=0;o<e;o++){if(t!=null?r=s.isPointInStroke(t,i.inputSystem.x+o,i.inputSystem.y+o):r=s.isPointInStroke(i.inputSystem.x+o,i[n(356)].y+o),r)return!![];if(t!=null?r=s.isPointInStroke(t,i.inputSystem.x-o,i[n(356)].y-o):r=s.isPointInStroke(i.inputSystem.x-o,i.inputSystem.y-o),r)return!![];if(t!=null?r=s.isPointInStroke(t,i.inputSystem.x-o,i[n(356)].y+o):r=s.isPointInStroke(i.inputSystem.x-o,i[n(356)].y+o),r)return!![];if(t!=null?r=s[n(393)](t,i.inputSystem.x+o,i[n(356)].y-o):r=s.isPointInStroke(i.inputSystem.x+o,i.inputSystem.y-o),r)return!![]}return![]}[Y(380)](e){const t=Y;let n=this.context,s=this.stage;return e?n.isPointInPath(e,s.inputSystem.x,s[t(356)].y):n[t(373)](s.inputSystem.x,s[t(356)].y)}contextExtends(){const e=Y;this.context[e(405)]==null&&(this[e(366)][e(405)]=function(t,n,s,i,r){const o=e;this[o(372)](),this.moveTo(t+r,n),this.lineTo(t+s-r,n),this.quadraticCurveTo(t+s,n,t+s,n+r),this.lineTo(t+s,n+i-r),this[o(361)](t+s,n+i,t+s-r,n+i),this.lineTo(t+r,n+i),this.quadraticCurveTo(t,n+i,t,n+i-r),this[o(396)](t,n+r),this.quadraticCurveTo(t,n,t+r,n),this[o(387)]()})}show(){const e=Y;this[e(386)].style.display="block"}[Y(382)](){const e=Y;this[e(386)].style.display="none"}getWidth(){const e=Y;return this[e(386)][e(390)]}getHeight(){const e=Y;return this.canvas[e(400)]}setSize(e,t){this.canvas.width=e,this.canvas.height=t}getCursor(){return this.canvas.style.cursor}setCursor(e){const t=Y;this[t(386)][t(383)].cursor=e}[Y(364)](e,t){const n=Y;return e=e||"image/png",this[n(386)].toDataURL(e,t)}getRectImageData(e,t,n,s){return e==null&&(e=0),t==0&&(t=0),n==null&&(n=this.getWidth()),s==null&&(s=this.getHeight()),this.context.getImageData(e,t,n,s)}clear(){const e=Y;this[e(386)].width=this.canvas.width}dispose(){const e=Y;this.canvas[e(394)]()}}const un=dn;function Cx(){const x=["2853200gPmHMe","1184343KvMgLp","516WPKilB","getHeight","axis","7863IxCjaN","layer","bold 16px arial","scaleY","width","paintLayer","paintAABB","restore","lineTo","lineWidth","moveTo","getObject","context","2jkoEMv","beginPath","30261aWqrze","stage","5628177TsyhZU","_paintObjects","_computedStyle","31738320ZlwbZz","applyTo","displayList","styleX","localView","save","4745690iIzekH","1652wruGSL","clear"];return Cx=function(){return x},Cx()}(function(x,e){const t=dn,n=x();for(;[];)try{if(parseInt(t(322))/1*(-parseInt(t(305))/2)+-parseInt(t(292))/3*(-parseInt(t(319))/4)+-parseInt(t(318))/5+parseInt(t(323))/6*(-parseInt(t(307))/7)+-parseInt(t(321))/8+-parseInt(t(309))/9+parseInt(t(312))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Cx,769043);function dn(x,e){const t=Cx();return dn=function(n,s){return n=n-290,t[n]},dn(x,e)}var sa=kt.gc;let Br=$.w;class jr extends Dr{constructor(e){super(e)}renderLayer(e){const t=dn;this[t(320)](),this.stage=this.layer[t(308)],this.styleSystem=this.stage.styleSystem,e[t(291)].visible&&this.paintAxis(e),this.paintLayer(e,![]),Qt[t(298)]==!![]&&e.displayList!=null&&this.paintAABB();{if(Br==null)return;let n=this.context;n[t(317)](),n.globalAlpha=.8,n.font=t(294);let s=sa(Br);n.fillStyle="gray",n.fillText(s,14,this[t(290)]()-14),n[t(299)]()}}[un(297)](e,t){const n=un;let s=this.context;s.save();let i=e.getTransform(),r=i.m;return s.transform(r[0],r[1],r[2],r[3],r[4],r[5]),e[n(311)].applyTo(s),se.flatten?(e[n(314)].forEach(o=>o.painted=!![]),this._paintFlattenObjects(e.displayList)):this[n(310)](e.children,t),s[n(299)](),e}[un(298)](){const e=un;let t=this[e(293)],n=this[e(304)],s=t.displayList;for(let i=s.length-1;i>=0;i--){let r=s[i],o=r._obb.aabb;o=t.toStageRect(o),n.save(),r instanceof ot?n.strokeStyle="rgba(0,0,255,0.3)":r instanceof W?n.strokeStyle="pink":n.strokeStyle="green",n[e(306)](),n.rect(o.x,o.y,o.width,o.height),n.stroke(),n.closePath(),n.restore()}}paintAxis(e){const t=un,n=e[t(291)],s=e.stage;let i=this.context;const r=s[t(316)][t(303)](),o=s[t(296)],a=s.height,c=r.toScreenXY(0,0),l=e.scaleX,h=e[t(295)];i.save(),c.x>0&&c.x<o&&(i.beginPath(),n.styleY[t(313)](i),i.moveTo(c.x,0),i.lineTo(c.x,a),i[t(302)](c.x-5,a-8),i.lineTo(c.x,a),i[t(300)](c.x+5,a-8),i.stroke(),i.fillText("+y",c.x+6,a-5)),c.y>0&&c.y<a&&(i.beginPath(),n[t(315)][t(313)](i),i[t(302)](o*.5,c.y),i[t(300)](0,c.y),i.moveTo(o*.5,c.y),i[t(300)](o,c.y),i.moveTo(o-8,c.y-5),i[t(300)](o,c.y),i[t(300)](o-8,c.y+5),i.stroke(),i.fillText("+x",o-5,c.y+5));let u=n[t(315)],g=n.styleY;u[t(301)]=Math.max(1,l),g.lineWidth=Math.max(1,h),i[t(299)]()}}function Ax(x,e){const t=Mx();return Ax=function(n,s){return n=n-122,t[n]},Ax(x,e)}const T=Ax;(function(x,e){const t=Ax,n=x();for(;[];)try{if(parseInt(t(174))/1*(-parseInt(t(129))/2)+parseInt(t(140))/3*(-parseInt(t(148))/4)+-parseInt(t(181))/5+-parseInt(t(177))/6*(parseInt(t(180))/7)+parseInt(t(194))/8+parseInt(t(136))/9*(parseInt(t(151))/10)+parseInt(t(164))/11*(parseInt(t(204))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Mx,417913);var ia=Object.defineProperty,ra=Object[T(137)],m0=(x,e,t,n)=>{const s=T;for(var i=n>1?void 0:n?ra(e,t):e,r=x[s(125)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&ia(e,t,i),i};function Mx(){const x=["show","12598SmPJys","mouseY","flatten","zoomLayer","_layerIndex","find","_findChildren","4419CAydWs","getOwnPropertyDescriptor","hideAxis","style","12843QqbgSJ","updateMatrix","forEach","resetTo","canvas","backgroundPosition","_dragDrawDelay","undefined","344McULiS","filter","parseImgUrl","11260TyAlsf","_tickLayer","updateZIndex","updateViewRect","update","fillByJson","clone","width","_zIndex","string","inputSystem","openJson","addChild","22mEglGd","backgroundImage","viewportRect","toAABB","flattenList","_calcBackgroundPosition","toJsonObject","Layer has been destroyed already.","mouseX","toLayerRect","31McJKNe","hide","script","8952ltNlWb","render","stage","854rJAzSH","1288155eBJkMl","camera","removeChild","getAABB","height","stringify","updateSize","removeFromParent","true",'url("',"displayList","_requestReapint","unionRects","4735384Aaegwi","prototype","_frames","0px","aabb","setSize","backgroundColor","isPointOn","addChilds","_cameraVisible","1657284sGXFrT","toPoints","keys","_bgInfo","then","length","_resetBg","background"];return Mx=function(){return x},Mx()}class ot extends M{constructor(e){const t=T;super(),this.className="Layer",this.renderTimes=0,this[t(166)]=new z(0,0,1,1),this.cuttingHide=!![],this[t(191)]=[],this.flattenList=[],this.wheelZoom=!![],this.axis=new Ro,this.mouseX=-1,this.mouseY=-1,this._frames=0,this.isFirstComputeBG=!![],this[t(192)]=![],this._dragDrawDelay=![],this[t(123)]={sw:null,sh:null,x:null,y:null},this.name=e,this.visible=![],this[t(191)]=[],Qt.isDebug&&(dt.layer=this),this.render=new jr(this),this[t(144)]=this.render[t(144)]}get frames(){return this[T(196)]}set frames(e){const t=T;this[t(196)]=e}_onMound(e){const t=T;this.stage=e,this[t(178)][t(179)]=e}[T(155)](){return this[T(169)](),this._requestReapint=!![],!![]}showAxis(){const e=T;this.axis[e(128)]()}[T(138)](){const e=T;this.axis[e(175)]()}setRender(e){const t=T;this.render!=null&&this[t(178)].dispose(),this.render=e,this.update()}_isMatrixDirty(){return!![]}[T(141)](e){const t=T;let n=this._getTransformByDeep(Z0);this._worldTransform=n;let s=n.copy().invert(),i=s.point({x:this.stage[t(161)].x||0,y:this.stage.inputSystem.y||0});this[t(172)]=i.x,this[t(130)]=i.y,this.updateViewRect()}getViewportRectInLayer(){return this.viewportRect}_calc_originInParent(){const e=this.stage;return[e.width*.5,e.height*.5]}toFileJson(e){const t=T;let n=this[t(170)](e);return JSON[t(186)](n)}toJsonObject(e){const t=T;e==null&&(e={imageToBase64:![]}),typeof e=="boolean"&&(e={imageToBase64:e});let n=this.stage.serializerSystem.objectsToJSON([this],e.imageToBase64);return e.script!=null&&(n[t(176)]=e.script),e.info!=null&&(n.info=e.info),n}[T(162)](e){const t=T;vt[t(143)](100);let n=e;typeof e==t(160)&&(n=JSON.parse(e)),this.removeAllChild();let s=this;return new Promise((i,r)=>{const o=t;s.stage.serializerSystem[o(156)](this,n,!![])[o(124)](()=>{s._whenJsonLoaded(),i(n)})})}openJsonAndWait(e){return this.openJson(e)}_whenJsonLoaded(){const e=T;let t=this[e(179)],n=this;t.styleSystem.markDirty(),t.cancelZoom(),this[e(126)](),n.stage.forceUpdate()}_resetBg(){this._bgInfo={sw:null,sh:null,x:null,y:null},this._calcBackgroundPosition()}[T(154)](){const e=T;this[e(166)].setTo(0,0,this.stage[e(158)],this.stage[e(185)]);let t=this.getTransform().invert();const n=this[e(166)];let s=n[e(205)](),i=Bt[e(167)](t.points(s));return this[e(166)]=i,n}[T(187)](e,t){const n=T;(e!=this.render.getWidth()||t!=this[n(178)].getHeight())&&(this[n(178)][n(199)](e,t),this.resizeTo(e,t),this.update())}pickUpByRect(e){const t=T,n=this;let s=[],i=n.displayList;if(i!=null)for(var r=0;r<i[t(125)];r++){let o=i[r],a=o._obb.aabb;o.pickable()&&e.containsRect(a)&&s.push(o)}return s}pickUpChild(){const e=T;let t=this.displayList,n=null;for(let s=t.length-1;s>=0;s--){let i=t[s];if(i[e(201)]&&i.pickable()){n=i;break}}if(n!=null){let s=n.getTopFrozenParent();s!=null&&(n=s)}return n}translateWith(e,t){return super.translateWith(e,t),this._calcBackgroundPosition(),this}_calcBackgroundPosition(e=![]){const t=T;if(!this[t(178)]){console.log("render not exist");return}const n=this.render.canvas;let s=this._computedStyle,i=this[t(123)],r=s.backgroundRepeat;if(r!=null&&r!="no-repeat"){let o=100,a=100,c=s._backgroundImageObject||this.style._backgroundImageObject;c!=null&&(o=c.width,a=c.height);let l=o*this.scaleX,h=a*this.scaleY,u=this[t(158)]*.5%l+this.x,g=this.height*.5%h+this.y;(e||i.sw!=l||i.sh!=h||i.x!=u||i.y!=g)&&(n.style.backgroundSize=l+"px "+h+"px",n.style[t(145)]=u+"px "+g+"px"),i.sw=l,i.sh=h,i.x=u,i.y=g}else n.style[t(145)]="0px 0px"}_afterStyleComputed(){const e=T;let t=this._computedStyle.getChangedProps();Object[e(122)](t).filter(s=>s.startsWith(e(127))).length>0&&this._updateDomStyle()}_updateDomStyle(){const e=T,t=this[e(144)],n=t.style,s=this._computedStyle;if(n.background=null,s.backgroundColor!=null&&(n[e(200)]=s[e(200)]),s[e(165)]!=null){let i=Tt[e(150)](s.backgroundImage);n.backgroundImage=e(190)+i+'")'}return s.backgroundRepeat!=null&&(n.backgroundRepeat=s.backgroundRepeat),s[e(145)]!=null&&(n[e(145)]=s[e(145)]),s.backgroundSize!=null&&(n.backgroundSize=s.backgroundSize),t.width=t[e(158)]+1,t.width=t[e(158)]-1,this._calcBackgroundPosition(!![]),this}getAABB(){const e=T;let t=this.displayList;if(t.length==0)return new z(0,0,1,1);let n=t.map(i=>i._obb.aabb);return z[e(193)](n)}getExportAABB(){const e=T;let t=this[e(168)][e(149)](i=>i[e(203)]);if(t.length==0)return new z(0,0,1,1);let n=t.map(i=>i._obb[e(198)]);return z.unionRects(n)}setZIndex(e){const t=T;this[t(159)]=e,this.stage&&this[t(179)][t(153)]()}[T(128)](){return super.show(),this.render.show(),this._calcBackgroundPosition(),this}hide(){return super.hide(),this.render.hide(),this}_OBBPoints(){let e=this.width,t=this.height;return[{x:0,y:0},{x:e,y:0},{x:e,y:t},{x:0,y:t}]}getPoints(){return this._OBBPoints()}draw(e){}zoom(e,t,n,s){const i=T;return this.stage.camera[i(132)](this,e,n,s),this}cancelZoom(){const e=T;this.stage[e(182)].cancelZoom()}forceUpdate(){const e=T;this.stage.renderSystem[e(152)](this,Date.now())}setBackground(e,t){const n=T;this[n(178)][n(144)].style.background=e,this[n(178)].canvas[n(139)].backgroundSize=t}[T(134)](e,t,n=![]){return this[T(135)](e,t,n)}toStageRect(e){let n=e[T(157)](),s=this.getTransform(),i=n.toPoints();return Bt.toAABB(s.points(i))}[T(173)](e){const t=T;let n=this.getTransform().invert(),s=e[t(205)]();return Bt.toAABB(n.points(s))}dragEndHandler(){const e=T;if(this[e(146)]==![])return;let t=this.render.canvas,n=t.offsetLeft,s=t.offsetTop;this.translateWith(n,s),t.style.left=e(197),t.style.top="0px"}addChild(e){const t=T;return super[t(163)](e),this.update(),this}[T(202)](e){super.addChilds(e),this.update()}centerBy(e){const t=T;let n=this,s=n.stage,i;if(e==null)i=n.getExportAABB();else if(e instanceof L)i=e[t(184)]();else{let l=e.map(h=>h.getAABB());i=z[t(193)](l)}i=n.toStageRect(i);let r=s.width/2,o=s.height/2,a=r-i.x,c=o-i.y;a-=i[t(158)]/2,c-=i.height/2,n.translateWith(a,c),n[t(155)]()}getAllVisiable(){return L[T(131)](this.children,t=>t.visible==!![])}loopRender(e=60){this._frames=e}endLoopRender(){const e=T;this[e(196)]=0}destory(){const e=T;if(this.destroyed)throw new Error(e(171));this.destroyed=!![],this.stage&&this[e(179)][e(183)](this),this.render.dispose(),this.displayList=void 0,this[e(168)]=void 0,this.listeners=void 0,this.style=void 0,this[e(166)]=void 0,this.classList.length=0,this.render=void 0,this.stage=void 0,this.children[e(142)](t=>t[e(188)]()),this.children.length=0}}m0([f(T(189))],ot.prototype,"className",2),m0([f(!![])],ot[T(195)],"isLayer",2),m0([f(![])],ot.prototype,"isNode",2),m0([f(["className","isLayer"])],ot.prototype,"allwaysSerializers",2),m0([f(["id","name"])],ot.prototype,"serializers",2),m0([f(T(147))],ot.prototype,T(133),2);function Dx(){const x=["className","fillText","flatten","areaBox","length","clear","_paintObjects","2365120XOFsJG","fillStyle","renderLayer","show","defineProperty","121494UBDXOa","224239MVjTtm","context","getStyle","125336PWOBVY","8ekSIyA","2CHUQNQ","setRender","175629NnRMcE","1928772jWJZBZ","min","displayList","HandlerLayerCanvas","77KzbbhD","height","1231500syzvyJ","layer","stage","translateTo","hide","draw","visible","styleSystem"];return Dx=function(){return x},Dx()}const Ft=Bx;(function(x,e){const t=Bx,n=x();for(;[];)try{if(parseInt(t(116))/1*(-parseInt(t(121))/2)+-parseInt(t(123))/3+-parseInt(t(119))/4+parseInt(t(130))/5+parseInt(t(115))/6*(-parseInt(t(128))/7)+parseInt(t(120))/8*(parseInt(t(124))/9)+parseInt(t(110))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Dx,160265);var oa=Object[Ft(114)],aa=Object.getOwnPropertyDescriptor,ca=(x,e,t,n)=>{const s=Ft;for(var i=n>1?void 0:n?aa(e,t):e,r=x[s(107)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&oa(e,t,i),i};function Bx(x,e){const t=Dx();return Bx=function(n,s){return n=n-106,t[n]},Bx(x,e)}const la=kt.gc,zr=$.w;class ha extends Dr{constructor(e,t){const n=Ft;super(t),this[n(132)]=e}[Ft(112)](e){const t=Ft;this[t(108)](),this.stage=this[t(131)].stage,this.styleSystem=this.stage[t(137)];let n=this[t(117)];return n.save(),e._computedStyle.applyTo(n),e[t(135)](n),se[t(140)]?(e.displayList.forEach(i=>i.painted=!![]),this._paintFlattenObjects(e[t(126)])):this[t(109)](e.children,![]),n.restore(),e}}class fn extends ot{constructor(e){const t=Ft;super(),this._frames=0,this._zIndex=i0[t(127)],vt.back(),this.stage=e,this.areaBox=new M,vt.back(),this.addChild(this.areaBox),this.axis.visible=![],this[t(136)]=!![],this[t(122)](new ha(e,this)),this.areaBox.css(de.getStyle("SelectArea")),this[t(106)].hide()}showAreaBox(){const e=Ft,t=this.stage.inputSystem,n=this.areaBox;let s=this.stage.styleSystem.currentTheme;n.css(s[e(118)]("SelectArea"));let i=t.mouseDownX,r=t.mouseDownY,o=t.x,a=t.y,c=Math.abs(i-o),l=Math.abs(r-a),h=Math.min(i,o),u=Math[e(125)](r,a);return n.resizeTo(c,l),n[e(133)](h,u),n.translateWith(c*.5,l*.5),this.areaBox[e(113)](),new z(h,u,n.width,n[e(129)])}mouseoutHandler(e){const t=Ft;this.areaBox[t(134)]()}_calc_originInParent(){return[0,0]}update(){return this._requestReapint=!![],!![]}draw(e){const t=Ft;if(zr==null)return;e.save(),e.globalAlpha=.6,e.font="bold 16px arial";let n=la(zr);e[t(111)]="white",e[t(139)](n,16,this.render.getHeight()-16),e.restore()}}ca([f("HandlerLayer")],fn.prototype,Ft(138),2);const I0=Xe;function Xe(x,e){const t=jx();return Xe=function(n,s){return n=n-500,t[n]},Xe(x,e)}(function(x,e){const t=Xe,n=x();for(;[];)try{if(parseInt(t(527))/1+parseInt(t(513))/2+-parseInt(t(505))/3+-parseInt(t(517))/4+-parseInt(t(507))/5*(-parseInt(t(514))/6)+parseInt(t(502))/7+parseInt(t(530))/8*(-parseInt(t(531))/9)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(jx,410334);function jx(){const x=["5MnilfS","display","appendChild","offsetLeft","setAttribute","touches","283502zjGBBK","3022626jvlqqY","oncanplay","createElement","1787944jXMghL","getXYInDom","substring","style","clientLeft","isFirefox","scrollTop","isMobileDevice","forEach","src","627248TyhZgJ","fullScreen","mousewheel","26984CjBUig","1494jQJjHo","top","fullWindow","test","4876186TIxPXk","pageXOffset","pageY","1656390ODfgGn","position"];return jx=function(){return x},jx()}const Nr=class{static addEventListener(x,e,t){const n=Xe;let s=x.attachEvent||x.addEventListener;kt[n(522)]()&&e==n(529)?e="DOMMouseScroll":window.attachEvent&&e[n(519)](0,2)!=="on"&&(e="on"+e),s.call(x,e,t)}static getOffsetPosition(x){const e=Xe;if(x==null)return{left:0,top:0};let t=0,n=0;if(x.getBoundingClientRect!=null){let s=x.getBoundingClientRect(),i=x.ownerDocument,r=i.body,o=i.documentElement,a=o.clientTop||r.clientTop||0,c=o.clientLeft||r[e(521)]||0;t=s[e(532)]+(window.pageYOffset||o&&o.scrollTop||r.scrollTop)-a,n=s.left+(window[e(503)]||o&&o.scrollLeft||r.scrollLeft)-c}else do t+=x.offsetTop||0,n+=x[e(510)]||0,x=x.offsetParent;while(x);return{left:n,top:t}}static createVideo(x,e){const t=Xe;if(typeof x=="string"){let n=document.createElement("video");n.muted="muted",n.style[t(508)]="none";let s=document[t(516)]("source");return s[t(511)]("_source","jtopo"),s.type="video/mp4",s[t(526)]=x,n[t(509)](s),document.body.appendChild(n),n[t(515)]=function(){e(n)},n}else return x}static[I0(500)](x){const e=I0;let t="position,width,height,left,top,bottom,right,zIndex".split(",");if(x[e(528)]==!![]){let n=x._backup;t.forEach(s=>{x.style[s]=n[s]}),x.fullScreen=![]}else{let n={};t[e(525)](s=>{const i=e;n[s]=x[i(520)][s]}),x._backup=n,x.style[e(506)]="fixed",x.style.left=0,x[e(520)].top=0,x.style.bottom=0,x[e(520)].right=0,x.style.zIndex=i0.FullWindowDom,x.fullScreen=!![]}}static fullScreen(x){x.requestFullscreen?x.requestFullscreen():x.mozRequestFullScreen?x.mozRequestFullScreen():x.webkitRequestFullscreen?x.webkitRequestFullscreen():x.msRequestFullscreen&&x.msRequestFullscreen()}static[I0(518)](x,e){const t=I0;let n=e[t(512)][0].pageX,s=e.touches[0][t(504)];e[t(512)][0].pageX==null&&(n=e.touches[0].clientX+document.body.scrollLeft-document.body[t(521)],s=e.touches[0].clientY+document.body[t(523)]-document.body.clientTop);let i=Nr.getOffsetPosition(x),r=n-i.left,o=s-i.top;return{x:r,y:o}}};let fe=Nr;fe[I0(524)]=/Android|webOS|iPhone|iPad|iPod|BlackBerry/i[I0(501)](navigator.userAgent),function(x,e){const t=Nx,n=x();for(;[];)try{if(-parseInt(t(289))/1*(parseInt(t(299))/2)+parseInt(t(294))/3*(parseInt(t(297))/4)+-parseInt(t(293))/5+parseInt(t(295))/6+parseInt(t(290))/7*(parseInt(t(298))/8)+parseInt(t(292))/9+parseInt(t(296))/10*(-parseInt(t(300))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}}(zx,580082);function zx(){const x=["1160103wDOCNq","6139674fLmLZs","571530TRdPXV","12yLrIMN","1025624hTeuQl","277868wTXYax","275rljUpn","1zeOaPR","7SVynMs","return ","1468638dOPzjK","1634630hrQCne"];return zx=function(){return x},zx()}function Nx(x,e){const t=zx();return Nx=function(n,s){return n=n-289,t[n]},Nx(x,e)}const Fi={execute(x,e){const t=Nx;if(x==null)return;new Function(t(291)+x)()(e.stage,e,dt)}};function Rx(x,e){const t=Wx();return Rx=function(n,s){return n=n-482,t[n]},Rx(x,e)}const ua=Rx;function Wx(){const x=["536535EVtaPN","5459223dGrqZF","2201118UzeXfO","2130762IotGQH","14WIsGls","28MnZpjh","4yAsBrl","11604016pbmnYd","get","1427067aHlTJm","10IOaXja","13441023LSqSxE"];return Wx=function(){return x},Wx()}(function(x,e){const t=Rx,n=x();for(;[];)try{if(parseInt(t(490))/1+-parseInt(t(487))/2*(parseInt(t(483))/3)+parseInt(t(486))/4*(-parseInt(t(493))/5)+-parseInt(t(484))/6*(parseInt(t(485))/7)+parseInt(t(488))/8+-parseInt(t(492))/9*(-parseInt(t(491))/10)+-parseInt(t(482))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Wx,945908);let da=`
<svg viewBox="0 0 24 24" id="zoom-in">
<path d="M4,20 L9.58788778,14.4121122"/>
<path d="M14,16 C10.6862915,16 8,13.3137085 8,10 C8,6.6862915 10.6862915,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,13.3137085 17.3137085,16 14,16 Z"/>
<path d="M16.6666667 10L11.3333333 10M14 7.33333333L14 12.6666667"/>
</svg>
<svg viewBox="0 0 24 24" id="zoom-out">
<path d="M14,16 C10.6862915,16 8,13.3137085 8,10 C8,6.6862915 10.6862915,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,13.3137085 17.3137085,16 14,16 Z"/>
<path d="M16.6666667 10L11.3333333 10M4 20L9.58788778 14.4121122"/>
</svg>
<svg viewBox="0 0 24 24" id="back-left">
<path d="M5,17 L5,15 C5,10.0294373 8.80557963,6 13.5,6 C18.1944204,6 22,10.0294373 22,15"/>
<polyline points="8 15 5 18 2 15"/>
</svg>
<svg viewBox="0 0 24 24" id="align-center">
<path d="M8 10L16 10M6 6L18 6M6 14L18 14M8 18L16 18"/>
</svg>
<svg viewBox="0 0 24 24" id="edit">
<path d="M18.4142136 4.41421356L19.5857864 5.58578644C20.366835 6.36683502 20.366835 7.63316498 19.5857864 8.41421356L8 20 4 20 4 16 15.5857864 4.41421356C16.366835 3.63316498 17.633165 3.63316498 18.4142136 4.41421356zM14 6L18 10"/>
</svg>
<svg viewBox="0 0 24 24" id="lock-alt">
<rect width="14" height="10" x="5" y="11"/>
<path d="M12,3 L12,3 C14.7614237,3 17,5.23857625 17,8 L17,11 L7,11 L7,8 C7,5.23857625 9.23857625,3 12,3 Z"/>
<circle cx="12" cy="16" r="1"/>
</svg>
<svg viewBox="0 0 24 24" id="lock-open">
<path d="M7,7.625 L7,7 C7,4.23857625 9.23857625,2 12,2 L12,2 C14.7614237,2 17,4.23857625 17,7 L17,11"/>
<rect width="14" height="10" x="5" y="11"/>
</svg>
<svg viewBox="0 0 24 24" id="pan">
<path d="M20,14 L20,17 C20,19.209139 18.209139,21 16,21 L10.0216594,21 C8.75045497,21 7.55493392,20.3957659 6.80103128,19.3722467 L3.34541668,14.6808081 C2.81508416,13.9608139 2.94777982,12.950548 3.64605479,12.391928 C4.35756041,11.8227235 5.38335813,11.8798792 6.02722571,12.5246028 L8,14.5 L8,13 L8.00393081,13 L8,11 L8.0174523,6.5 C8.0174523,5.67157288 8.68902517,5 9.5174523,5 C10.3458794,5 11.0174523,5.67157288 11.0174523,6.5 L11.0174523,11 L11.0174523,4.5 C11.0174523,3.67157288 11.6890252,3 12.5174523,3 C13.3458794,3 14.0174523,3.67157288 14.0174523,4.5 L14.0174523,11 L14.0174523,5.5 C14.0174523,4.67157288 14.6890252,4 15.5174523,4 C16.3458794,4 17.0174523,4.67157288 17.0174523,5.5 L17.0174523,11 L17.0174523,7.5 C17.0174523,6.67157288 17.6890252,6 18.5174523,6 C19.3458794,6 20.0174523,6.67157288 20.0174523,7.5 L20.0058962,14 L20,14 Z"/>
</svg>
<svg viewBox="0 0 24 24" id="apps-alt">
<rect x="5" y="5" width="2" height="2"/>
<rect x="11" y="5" width="2" height="2"/>
<rect x="17" y="5" width="2" height="2"/>
<rect x="5" y="11" width="2" height="2"/>
<rect x="11" y="11" width="2" height="2"/>
<rect x="17" y="11" width="2" height="2"/>
<rect x="5" y="17" width="2" height="2"/>
<rect x="11" y="17" width="2" height="2"/>
<rect x="17" y="17" width="2" height="2"/>
</svg>
<svg viewBox="0 0 24 24" id="maximise">
<polyline points="21 16 21 21 16 21"/>
<polyline points="8 21 3 21 3 16"/>
<polyline points="16 3 21 3 21 8"/>
<polyline points="3 8 3 3 8 3"/>
</svg>
<svg viewBox="0 0 24 24" id="minimise">
<polyline points="8 3 8 8 3 8"/>
<polyline points="21 8 16 8 16 3"/>
<polyline points="3 16 8 16 8 21"/>
<polyline points="16 21 16 16 21 16"/>
</svg>
<svg viewBox="0 0 24 24" id="download">
<path d="M12,3 L12,16"/>
<polyline points="7 12 12 17 17 12"/>
<path d="M20,21 L4,21"/>
</svg>
<svg viewBox="0 0 24 24" id="rectangle">
<rect width="18" height="18" x="3" y="3"/>
</svg>
<svg viewBox="0 0 24 24" id="cursor">
<polygon points="7 20 7 4 19 16 12 16 7 21"/>
</svg>
<svg viewBox="0 0 24 24" id="search">
<path d="M14.4121122,14.4121122 L20,20"/>
<circle cx="10" cy="10" r="6"/>
</svg>
<svg viewBox="0 0 24 24" id="eye">
<path d="M22 12C22 12 19 18 12 18C5 18 2 12 2 12C2 12 5 6 12 6C19 6 22 12 22 12Z"/>
<circle cx="12" cy="12" r="3"/>
</svg>
<svg viewBox="0 0 24 24" id="save">
<path d="M17.2928932,3.29289322 L21,7 L21,20 C21,20.5522847 20.5522847,21 20,21 L4,21 C3.44771525,21 3,20.5522847 3,20 L3,4 C3,3.44771525 3.44771525,3 4,3 L16.5857864,3 C16.8510029,3 17.1053568,3.10535684 17.2928932,3.29289322 Z"/>
<rect width="10" height="8" x="7" y="13"/>
<rect width="8" height="5" x="8" y="3"/>
</svg>
<svg viewBox="0 0 24 24" id="image">
<rect width="18" height="18" x="3" y="3"/>
<path stroke-linecap="round" d="M3 14l4-4 11 11"/>
<circle cx="13.5" cy="7.5" r="2.5"/>
<path stroke-linecap="round" d="M13.5 16.5L21 9"/>
</svg>
<svg viewBox="0 0 24 24" id="upload">
<path d="M12,4 L12,17"/>
<polyline points="7 8 12 3 17 8"/>
<path d="M20,21 L4,21"/>
</svg>
`,Rr=document.createElement("div");Rr.innerHTML=da;let Xi={};Xi[ua(489)]=function(x){return Rr.querySelector("#"+x)};const U=w0;(function(x,e){const t=w0,n=x();for(;[];)try{if(-parseInt(t(438))/1+parseInt(t(444))/2*(parseInt(t(411))/3)+parseInt(t(424))/4*(-parseInt(t(448))/5)+parseInt(t(430))/6*(-parseInt(t(433))/7)+parseInt(t(400))/8*(-parseInt(t(436))/9)+parseInt(t(425))/10*(parseInt(t(402))/11)+parseInt(t(417))/12===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Hx,431566);function Hx(){const x=["querySelector","domObj","12zzIViG","drag","clientHeight","2763166eqJDTY","iconId",`</button>
<button title="拖拽模式"  class="item" group='mode' iconId='pan'>`,"3843nrAUuw","none","264572KtSnny","length","imageToBase64","initToolbar","select","addEventListener","2792xuTAxT","update","stage","add","30JykGGC","activeBtn","248UBymen","style","11HuOCTj","div",`</button>
<button title="取消缩放" class="item" iconId='back-left'>`,`</button>
</div>

<div class="group">
<button title="导出PNG" class="item" iconId='image'>`,"text","hideOverview",`</button>
<button title="缩放至画布" class="item" iconId='minimise'>`,' style="display:none" ',"overview","168wFldRo","removeAllActive","input","getAttribute","eye","button[iconid='edit']","18016344TYFYDq",`</button>
<button title="浏览器全屏" class="item" iconId='maximise'>`,"group","maximise",'input[type="file"]',"toLocaleString","indexOf","242988tpUwAL","2837950bNZNEM","then","pan"];return Hx=function(){return x},Hx()}function w0(x,e){const t=Hx();return w0=function(n,s){return n=n-399,t[n]},w0(x,e)}function xt(x){return Xi.get(x).outerHTML}let Wr=U(409);Wr="";var fa=`
<div class="group">
<button title="默认" class="item active" group='mode' iconId='cursor'>`+xt("cursor")+`</button>
<button title="编辑模式" edit="true" class="item" group='mode' iconId='edit' `+Wr+">"+xt("edit")+`</button>
<button title="框选模式" class="item" group='mode' iconId='rectangle'>`+xt("rectangle")+U(435)+xt(U(427))+`</button>
<button title="锁定模式" class="item" group='mode' iconId='lock-alt'>`+xt("lock-alt")+`</button>
</div>

<div class="group">
<button title="放大"  class="item" iconId='zoom-in'>`+xt("zoom-in")+`</button>
<button title="缩小"  class="item" iconId='zoom-out'>`+xt("zoom-out")+`</button>
<button title="居中" class="item" iconId='align-center'>`+xt("align-center")+U(408)+xt("minimise")+U(404)+xt("back-left")+`</button>
</div>

<div class="group">
<button title="缩略图" class="item" iconId='eye'>`+xt(U(415))+U(418)+xt(U(420))+`</button>
<input title="查找" type="text" placeholder="查找" value=""></input>
<button class="item" iconId='search'>`+Xi.get("search").outerHTML+U(405)+xt("image")+`</button>
<button title="打开本地文件" class="item" iconId='upload'>`+xt("upload")+`</button>
<button title="保存到本地" class="item" iconId='save'>`+xt("save")+`</button>
<div style="display:none;"><input type="file"/></div>
</div>
`;class Yi{constructor(e){this.imageToBase64=!![],this.stage=e,this.initToolbar(e,fa);let t=this;setTimeout(function(){t.initActiveStatus()},200)}getDom(){return this[U(429)]}show(){const e=U;this[e(429)].style.display="block"}hide(){const e=U;this[e(429)][e(401)].display="none"}remove(){this.domObj.remove()}getHeigth(){const e=U;return this.domObj.style.display==e(437)?0:this[e(429)][e(432)]+1}initActiveStatus(){const e=U;if(this.stage.mode=="edit"){let n=document.querySelector(e(416));this.activeBtn(n)}}[U(441)](e,t){const n=U;let s=this,i=document.createElement(n(403));this.domObj=i,i.classList[n(447)]("jtopo_toolbar"),i.innerHTML=t;let r=i.querySelectorAll("button");this.buttons=r;let o=i[n(428)](n(421)),a=o.parentNode;function c(m){a.innerHTML='<input type="file"/>',l();let I=m.target.files[0];const b=new FileReader;b.readAsText(I),b.onload=function(){const k=w0,E=s[k(446)].getCurrentLayer(),D=this.result;try{E.openJson(D),E.translate(0,0),E.scaleTo(1,1),document.title=I.name}catch(j){console.log(j),alert("加载出现错误")}}}function l(){const m=n;o=i.querySelector(m(421)),o[m(443)]("change",c)}l(),this.fileInput=o;function h(){const m=n;let I=e.getCurrentLayer(),b=i[m(428)]('input[type="text"]').value;if(b.length>0){let k=I.displayList;for(let E=0;E<k.length;E++){const D=k[E];if(D[m(406)]!=null&&D.text[m(423)](b)!=-1){I.centerBy(D,10),e.effectSystem.flash(D).play(),e.editor!=null?e.editor[m(445)]():e[m(445)]();return}}}}let u={cursor:function(){e.setMode("normal")},rectangle:function(){const m=n;e.setMode(m(442))},pan:function(){const m=n;e.setMode(m(431))},edit:function(){e.setMode("edit")},"lock-alt":function(){e.setMode("view")},eye:function(){const m=n;e.overview==null||e[m(410)].visible==![]?e.showOverview():e[m(407)]()},"zoom-in":function(){e.zoomIn()},"zoom-out":function(){e.zoomOut()},"back-left":function(){e.cancelZoom()},minimise:function(){e.zoomFullStage()},"align-center":function(){e.translateToCenter()},maximise:function(){e.fullWindow()},image:function(){e.saveImageInfo()},save:function(){const m=n;let I=prompt("要保存的文件名："),b=s[m(440)];if(I!=null){let k=e.getCurrentLayer(),E=k.toFileJson({info:{date:new Date()[m(422)](),fileName:I},imageToBase64:b});e.download(I+".json",E)}},upload:function(){o.click()},search:h};i[n(428)]("input").onkeydown=function(m){m.key=="Enter"&&h()},i.querySelector(n(413)).ondblclick=function(m){const I=n;if(!m.shiftKey)return;console.log("reloadJsonTest");let b=e.getCurrentLayer();b.openJson(b.toFileJson())[I(426)](k=>{Fi.execute(k.script,b)})};function g(m){m.onclick=function(){const I=w0;let b=m[I(414)](I(434));u[b](),s[I(399)](m)}}for(var y=0;y<r[n(439)];y++){let m=r[y];g(m)}}activeBtn(e){const t=U;let n=e[t(414)](t(419));n!=null&&(this[t(412)](n),e.classList.add("active"))}removeAllActive(e){const t=U;let n=this.buttons;for(var s=0;s<n.length;s++){let i=n[s];e==i.getAttribute(t(419))&&i.classList.remove("active")}}}const at=pe;(function(x,e){const t=pe,n=x();for(;[];)try{if(-parseInt(t(165))/1+parseInt(t(162))/2+parseInt(t(163))/3+parseInt(t(159))/4+parseInt(t(146))/5+-parseInt(t(144))/6*(parseInt(t(161))/7)+parseInt(t(150))/8*(-parseInt(t(151))/9)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Fx,757303);function pe(x,e){const t=Fx();return pe=function(n,s){return n=n-140,t[n]},pe(x,e)}function Fx(){const x=["observers","length","-#distinct#-","489318CuVhph","click","3747935HkPLVq","log","clientX","移动-x","88ZKRNJR","804987MTbXvn","next","publish","complete","distinct","function","map","build","2994676WDMSsq","subscribe","119MzpwHl","2121314DzrUOE","3987387NTJFTw","forEach","760465UvSYFU","fromEvent","debounceTime","重复订阅","filter","error"];return Fx=function(){return x},Fx()}class Ye{constructor(){this.observers=[]}[at(153)](e){const t=at;return this[t(141)][t(164)](n=>{typeof n=="function"?n(e):n[t(152)]&&n.next(e)}),this}complete(){return this.observers.forEach(e=>{const t=pe;typeof e==t(156)||e[t(154)]&&e.complete()}),this}[at(140)](e){const t=at;return this[t(141)].forEach(n=>{typeof n==t(156)||n.error&&n.error(e)}),this}subscribe(e){const t=at;if(this[t(141)].indexOf(e)!=-1)throw new Error(t(168));return this.observers.push(e),this}[at(158)](e){const t=at;let n=new Ye;return this[t(160)](s=>{e(n,s)}),n}filter(e){return this.build((t,n)=>{e(n)&&t.publish(n)})}map(e){const t=at;return this[t(158)]((n,s)=>{n[t(153)](e(s))})}reduce(e,t){const n=at;let s=t,i=!![];return this[n(158)]((r,o)=>{const a=n;i&&t==null?(i=![],s=o):s=e(o,s),r[a(153)](s)})}[at(155)](){const e=at;let t=e(143);return this.build((n,s)=>{const i=e;t!==s&&(t=s,n[i(153)](s))})}debounceTime(e){let t;return this.build((n,s)=>{t!=null&&clearTimeout(t),t=setTimeout(()=>{n.publish(s)},e)})}delay(e){return this.build((t,n)=>{setTimeout(()=>{t.publish(n)},e)})}pairwise(e=2){return this.bufferCount(e,1)}bufferCount(e,t=1){let n=[];return this.build((s,i)=>{const r=pe;if(n.push(i),n[r(142)]==e){s.publish(n);for(let o=0;o<t;o++)n.shift()}})}unsubscribe(){const e=at;this.observers[e(142)]=0}static[at(166)](e,t){let n=new Ye,s=i=>{n[pe(153)](i)};return e.addEventListener(t,s),n.unsubscribe=()=>{e.removeEventListener(t,s)},n}static of(...e){const t=at;let n=new Ye,s;return n[t(160)]=function(i){return this.observers.push(i),s=setTimeout(()=>{e[pe(164)](o=>n.publish(o))},10),this},n.unsubscribe=()=>{clearTimeout(s)},n}}const Je=Xt;(function(x,e){const t=Xt,n=x();for(;[];)try{if(parseInt(t(444))/1+parseInt(t(450))/2*(parseInt(t(408))/3)+parseInt(t(422))/4+-parseInt(t(406))/5+-parseInt(t(447))/6*(parseInt(t(453))/7)+-parseInt(t(411))/8+-parseInt(t(432))/9*(-parseInt(t(434))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Yx,841863);class Xx extends wt{constructor(){const e=Xt;super(),this.skipPointerMovePicking=![],this.wheelZoom=!![],this.mouseDownX=0,this[e(404)]=0,this.x=-1,this.y=-1,this.isMouseDown=![],this.isDragEnd=![],this[e(441)]=![],this.isMouseOn=![],this.isDragStart=![],this.idleTimeout=50,this.buttons=0,this.button=0,this.touchesDistance=0,this.distanceRatio=1,this.eventObservable=new Ye}get pickedObject(){return this.target}_initEvent(e){const t=Xt;this[t(436)]=e,pa(this),this[t(437)]()}_initObserve(){const e=this,t=this.stage;e.eventObservable.filter(i=>{const r=Xt,o=i.type;return t[r(438)]==mt.view?![]:t[r(429)].mode=="painted"?![]:(o=="touchstart"&&t.forceUpdate(),!![])}).filter(i=>{const r=Xt;if(t.mode==mt[r(449)]&&t.editor!=null){let a=i.type+"Handler";if(t[r(419)][a]&&(t.editor[a](i),i instanceof MouseEvent&&i.defaultPrevented))return![]}return!![]}).subscribe(i=>{const r=Xt;i.type=="mousewheel"&&i.preventDefault();let a=i.type+"Handler";t[a]&&t[a](i),t[r(442)]&&(t.overview[r(424)]=!![])}),e.eventObservable.subscribe(i=>{e.stage.update(),e.dispatchEvent(i)})}preventDefault(){this.event&&this.event.preventDefault()}isRightButton(){return this.button==2}updateBaseInfo(e,t){const n=Xt,s=Object.assign({},this);this.previous=s,this.event=e,this.timeStamp=e[n(431)],this.type=t,this.isDragEnd=![],this.isIdle=![];let i=this;this.idleTimer!=null&&window.clearTimeout(this[n(403)]),this[n(403)]=setTimeout(function(){const r=n;i[r(448)]=!![]},this[n(445)])}[Je(412)](e,t){const n=Je;this.updateBaseInfo(e,t),this[n(409)]=e.offsetX>0&&e.offsetY>0;const s=this.previous;this.buttons=e[n(427)],this.button=e.button,this.x=e.offsetX,this.y=e.offsetY,t==n(440)||t=="touchstart"?(this.isMouseDown=!![],this.mouseDownX=this.x,this[n(404)]=this.y):(t=="mouseup"||t=="click"||t=="mouseout"||t=="touchend")&&(this[n(407)]=![],s.type==n(417)&&(this.isDragEnd=!![])),this.isDraging=this.isMouseDown==!![]&&(t==n(417)||t=="touchmove"||t=="mousewheel"||t=="mouseenter"||t=="mouseout"),this[n(410)]=this.isDraging&&s.isDraging!=!![],this.dx=this.x-s.x,this.dy=this.y-s.y}updateTouchInfo(e,t){const n=Je;this.updateBaseInfo(e,t);const s=this.previous,r=this[n(436)].handlerLayer.render.canvas.getBoundingClientRect(),o=r.left,a=r[n(433)];if(t!=n(405)){const c=e.touches[0],l=c.clientX-o,h=c.clientY-a;this.x=l,this.y=h}if(t=="touchstart"?(this.isMouseDown=!![],this.mouseDownX=this.x,this[n(404)]=this.y,this.touchesDistance=0,this.distanceRatio=1):t=="touchend"&&(this.isMouseDown=![],s&&s[n(439)]==n(430)&&(this.isDragEnd=!![]),this[n(414)]=0,this.distanceRatio=1),this.isDraging=this[n(407)]==!![]&&t=="touchmove",this[n(410)]=this.isDraging&&s.isDraging!=!![],this.dx=this.x-s.x,this.dy=this.y-s.y,t=="touchmove"&&e.touches.length>=2){const c=e.touches[0],l=e.touches[1],h=ga(c,l);this.touchesDistance!=0&&(this.distanceRatio=h/this[n(414)]),this.touchesDistance=h}e[n(452)]()}_onEvent(e){const t=this,n=e.type;e instanceof MouseEvent?t.updateMouseInfo(e,n):t.updateTouchInfo(e,n),t.eventObservable.publish(e)}[Je(443)](e){const t=Je,n=this.stage,s=this;let i=s[t(421)]||{type:t(440),ctrlKey:![],metaKey:![]};if(n.inputSystem.target=e,e==null)return;e.matrixDirty=!![],s.type=="mousedown"?e.mousedownHandler.call(e,s):i.type=="touchstart"&&e.touchstartHandler.call(e,s);const r=i.ctrlKey||i[t(416)];n.selectedGroup[t(425)](e)?r&&n.selectedGroup[t(446)](e):(!r&&n.selectedGroup.removeAll(),n[t(413)][t(418)](e))}}function Yx(){const x=["touchstart","buttons","click","localView","touchmove","timeStamp","5706873tTlNiS","top","10IgximN","mouseover","stage","_initObserve","mode","type","mousedown","isDraging","overview","pickObject","1422907dOosUq","idleTimeout","remove","7547106bScNyV","isIdle","edit","2EulbRZ","render","preventDefault","7YJSPXv","idleTimer","mouseDownY","touchend","6060475UWmaDQ","isMouseDown","4687818DscoFM","isMouseOn","isDragStart","7700808fzQBmn","updateMouseInfo","selectedGroup","touchesDistance","pageX","metaKey","mousemove","add","editor","drop","event","2619200ItVdel","handlerLayer","dirty","has"];return Yx=function(){return x},Yx()}function Xt(x,e){const t=Yx();return Xt=function(n,s){return n=n-403,t[n]},Xt(x,e)}function pa(x){const e=Je;let t=x.stage,n=t[e(423)][e(451)].canvas;["mouseenter","mouseout",e(435),"mousedown","mouseup",e(417),"mousewheel",e(428),"dblclick","dragstart","dragend","dragover",e(420),e(426),"touchmove","touchend"].map(function(i){fe.addEventListener(n,i,function(r){x._onEvent(r)})})}function ga(x,e){const t=Je,n=e[t(415)]-x.pageX,s=e.pageY-x.pageY;return Math.sqrt(n*n+s*s)}const bt=Ve;(function(x,e){const t=Ve,n=x();for(;[];)try{if(parseInt(t(481))/1*(-parseInt(t(489))/2)+parseInt(t(473))/3*(-parseInt(t(464))/4)+-parseInt(t(465))/5+parseInt(t(466))/6*(parseInt(t(467))/7)+parseInt(t(461))/8+parseInt(t(476))/9*(-parseInt(t(483))/10)+-parseInt(t(495))/11*(-parseInt(t(477))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Jx,549923);function Ve(x,e){const t=Jx();return Ve=function(n,s){return n=n-461,t[n]},Ve(x,e)}function Jx(){const x=["3585195fmsoPJ","1176kvXsKh","32872TTHDtK","bufferCount","sort","join","inputSystem","isMouseOn","1059mQEtwM","Meta","map","2882106nAWErK","1199424YToLEV","bindKey","_disabled","tagName","137IqZLki","activeElement","10bIqyNW","shift","keyMap","Alt","push","meta","9140zeTZmj","isKeydown","stage","split","toLowerCase","textInputMode","110yLjntJ","keyupHandler","mock","keys","indexOf","filter","editor","regMap","preventDefault","key","fromEvent","2524712JXuCOo","shiftKey","ctrlKey","252CtnaAn"];return Jx=function(){return x},Jx()}class Ji extends wt{constructor(e){const t=Ve;super(),this.debug=![],this._disabled=![],this.regMap=new Map,this.keyMap=new Map,this[t(491)]=e,this.init(),this.debug=![]}disable(){this._disabled=!![]}enable(){this._disabled=![]}isControlDown(){return this.isKeydown("Control")}isShiftDown(){return this.isKeydown("Shift")}isAltDown(){const e=Ve;return this.isKeydown(e(486))}isMetaDown(){const e=Ve;return this.isKeydown(e(474))}[bt(478)](e,t){const n=bt;let s=e[n(493)]().split("+")[n(469)]().join("+");this.regMap.set(s,t)}isKeyRegistered(e){return this.getKeyBinding(e)!=null}getKeyBinding(e){const t=bt;let n=e[t(493)]().split("+").sort()[t(470)]("+");return this[t(502)].get(n)}unBind(e){return this.unbind(e)}unbind(e){const t=bt;let n=e[t(493)]()[t(492)]("+").sort().join("+");this[t(502)].delete(n)}[bt(490)](e){const t=bt;return this.keyMap.get(e[t(493)]())}sendKey(e,t){const n=bt;t==null&&(t=new KeyboardEvent("keydown"),t[n(497)]=!![]),this.fireKey(e[n(493)]().split("+"),t,!![])}checkValid(){const e=bt;let t=document[e(482)][e(480)];return this[e(479)]||t=="INPUT"||t=="TEXTAREA"?![]:!![]}fireKey(e,t,n){const s=bt;if(n==![])return;const i=this;let r=this.stage,o=r[s(501)],a=e[s(469)]().join("+").toLowerCase();this.debug&&console.log("按下",a);let c=this[s(502)][s(498)]();for(let l of c){if(l!=a||o!=null&&o.textInputMode==!![])continue;t[s(503)]();let h=i[s(502)].get(l);h&&h(t)}}keydownHandler(e){const t=bt;let n=e[t(504)].toLowerCase(),s=this.stage,i=[];e[t(463)]&&(this.keyMap.set("control",!![]),i.push("control")),e.altKey&&(this.keyMap.set("alt",!![]),i.push("alt")),e[t(462)]&&(this[t(485)].set(t(484),!![]),i.push(t(484))),e.metaKey&&(this.keyMap.set("meta",!![]),i[t(487)](t(488))),i[t(499)](n)==-1&&i.push(n),this.keyMap.set(n,!![]);let r=ax(e);this.fireKey(i,r,s[t(471)][t(472)]),this.dispatchEvent(r)}[bt(496)](e){const t=bt,n=this,s=e[t(504)].toLowerCase();n[t(485)].delete(s);let i=n.stage.editor;if(i!=null&&i[t(494)]==!![])return;let r=ax(e);n.dispatchEvent(r)}init(){const e=bt;let t=this,n=Ye.fromEvent(document,"keydown"),s=Ye[e(505)](document,"keyup"),i=n[e(500)](()=>this.checkValid()),r=s.filter(()=>this.checkValid());i.subscribe(o=>{t.keydownHandler(o)}),r.subscribe(o=>{t[e(496)](o)}),this.keydownOb=i,this.keyupOb=r,this.keyBufferOb=i[e(475)](o=>o[e(504)])[e(468)](2,1)[e(475)](o=>o[0]+""+o[1])}}function Vx(x,e){const t=Gx();return Vx=function(n,s){return n=n-388,t[n]},Vx(x,e)}const Q=Vx;function Gx(){const x=["45455RXcAtq","push","dispatchEvent","24CWwNCb","1094061TeyXaQ","objects","groupdrag","433027goJEmx","hasChild","4961850oGzcJA","4XxWgZT","60ylBacz","isEmpty","groupdragend","1308852qNrxwD","draggable","remove","dragHandler","addAll","4MXFnng","36cEqfBf","dragEndHandler","selectedHandler","getNoChildrensObjects","group","defaultPrevented","90142NANlZo","has","event","1903693UnOsqj"];return Gx=function(){return x},Gx()}(function(x,e){const t=Vx,n=x();for(;[];)try{if(parseInt(t(404))/1*(parseInt(t(388))/2)+-parseInt(t(412))/3*(-parseInt(t(397))/4)+parseInt(t(408))/5+parseInt(t(398))/6*(-parseInt(t(415))/7)+parseInt(t(411))/8*(parseInt(t(392))/9)+parseInt(t(417))/10+parseInt(t(407))/11*(-parseInt(t(389))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Gx,250050);let Hr=new InputEvent(it[Q(391)]),ba=new InputEvent(it[Q(414)]);class Vi extends wt{constructor(){const e=Q;super(),this[e(413)]=[],this.isDraging=![],this.noChildrensObjects}[Q(390)](){return this.objects.length==0}mouseoutHandler(e){const t=Q;this.isDraging=![],!this.isEmpty()&&this[t(410)](Hr)}[Q(395)](e){const t=Q;if(this.dispatchEvent(ba),e[t(406)]instanceof MouseEvent&&e[t(406)][t(403)]==!![])return;let n=this[t(401)]();for(var s=0;s<n.length;s++){let i=n[s];i[t(393)]&&i[t(395)](e)}}[Q(399)](e){const t=Q;this.dispatchEvent(Hr);let n=this[t(401)]();for(var s=0;s<n.length;s++){let i=n[s];i.draggable&&i.dragEndHandler(e)}}[Q(401)](){return L.getNoChildrensObjects(this.objects)}[Q(396)](e){const t=Q;for(var n=0;n<e.length;n++){let s=e[n];s.group=this,!R[t(416)](this[t(413)],s)&&(s.selectedHandler&&s[t(400)](),this[t(413)].push(s))}return this}add(e){const t=Q;return e.group=this,R.hasChild(this[t(413)],e)?this:(e.selectedHandler.call(e),this.objects[t(409)](e),this)}remove(e){const t=Q;return e.group=void 0,e.unselectedHandler&&e.unselectedHandler(e),R[t(394)](this.objects,e),this}removeAll(){const e=Q;let t=this.objects;for(var n=0;n<t.length;n++){let s=t[n];s[e(402)]=void 0,s.unselectedHandler&&s.unselectedHandler()}return this.objects.length=0,this}[Q(405)](e){return R[Q(416)](this.objects,e)}}const Gi=Ge;function Ge(x,e){const t=qx();return Ge=function(n,s){return n=n-227,t[n]},Ge(x,e)}(function(x,e){const t=Ge,n=x();for(;[];)try{if(parseInt(t(230))/1*(-parseInt(t(256))/2)+parseInt(t(259))/3*(parseInt(t(258))/4)+parseInt(t(253))/5+parseInt(t(234))/6+parseInt(t(240))/7+parseInt(t(264))/8+-parseInt(t(239))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(qx,772390);function qx(){const x=["div","toFixed","4786565PpyUMU","inputSystem",", h:","142HoICaD","start","4iztAaK","4189101gnGhkh","checkDom","<li>Links: ","_timer","hide","7513760EuInio","</li>","jtopo_debugPanel","stage","18782hayqsr","getCurrentLayer","getAllLinks","domElement","3862590HSxJTI","]</li>","innerHTML"," y: ",", y: ","27445347EgkFOf","8531110fZOWeh","<li>Mouse-Layer( x: ","ceil","length","<li>Painted: ","stageToLocalXY","origin","setContent","layersContainer","<li>","<li>&nbsp;&nbsp;-Parent: (x: "];return qx=function(){return x},qx()}class ya{constructor(e){this.numberFixed=0,this.stage=e}init(){const e=Ge,t=document.createElement(e(251));t.classList.add(e(228)),this.stage[e(248)].appendChild(t),this[e(233)]=t,this[e(263)]()}start(e=24){const t=Ge,n=this,s=this[t(229)];function i(){const r=t;if(s[r(254)].isIdle)return;let o=s.getChildren(),a=0,c=0,l=0;for(var h=0;h<o[r(243)];h++){let k=o[h];a+=k.displayList[r(243)],c+=k.getAllNodes()[r(243)],l+=k[r(232)]()[r(243)]}let u=Math[r(242)](s.inputSystem.x),g=Math.ceil(s.inputSystem.y),y=s[r(231)](),m={x:0,y:0};y!=null&&(m=y[r(245)](u,g));const I=s[r(254)].target;let b="<li>Mouse-Canvas( x: "+u+" y: "+g+")</li>";if(b+=r(241)+m.x.toFixed(2)+r(237)+m.y.toFixed(2)+")</li>",b+="<li>Nodes: "+c+"</li>",b+=r(261)+l+"</li>",b+="<li>Total: "+(c+l)+"</li>",b+=r(244)+a+"</li>",I){b+="<li>Target: id: "+I.id;const k=I._obb.aabb,E=I.toStageXY(0,0),D=I.toLayerXY(0,0),j=this.numberFixed;b+="<li>_aabb:[x:"+k.x.toFixed(j)+",y: "+k.y.toFixed(j)+",w: "+k.width[r(252)](j)+r(255)+k.height.toFixed(j)+"] </li>",b+="<li>Origin: ["+I[r(246)][0].toFixed(j)+", "+I.origin[1][r(252)](j)+r(235),b+="<li>&nbsp;&nbsp;-Canvas: (x: "+E.x.toFixed(j)+r(238)+E.y.toFixed(j)+") </li>",I instanceof M&&(b+=r(250)+I.x.toFixed(j)+", y: "+I.y.toFixed(j)+") </li>"),b+="<li>&nbsp;&nbsp;-Layer:  (x: "+D.x[r(252)](j)+r(238)+D.y.toFixed(j)+") </li>"}Qt.debugInfo&&(b+=r(249)+Qt.debugInfo+r(227)),n[r(247)](b)}this._timer=setInterval(i,e)}setContent(e){const t=Ge;this.domElement[t(236)]=e}[Gi(260)](){this.domElement==null&&this.init()}hide(){const e=Gi;return this.checkDom(),clearInterval(this[e(262)]),this.domElement.style.display="none",this}show(e){const t=Gi;return this.checkDom(),this.domElement.style.display="block",this[t(257)](e),this}}const Z=ge;(function(x,e){const t=ge,n=x();for(;[];)try{if(parseInt(t(378))/1+parseInt(t(381))/2+-parseInt(t(399))/3*(parseInt(t(393))/4)+parseInt(t(410))/5*(parseInt(t(394))/6)+-parseInt(t(372))/7*(parseInt(t(373))/8)+-parseInt(t(397))/9*(-parseInt(t(385))/10)+parseInt(t(408))/11*(parseInt(t(417))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ux,350312);class Fr extends jr{constructor(e,t){const n=ge;super(t),this[n(400)]=e}dontNeedPickup(e){return!![]}paintSelected(e){}overviewPaint(e){const t=ge;let n=this.context;if(n.save(),e._doTransform(n),e[t(406)][t(405)](n),se.flatten){const s=e.flattenList.filter(i=>i._needPaint&&i._cameraVisible);this._paintFlattenObjects(s)}else this[t(391)](e[t(404)],!![]);n.restore()}exportPaintObjects(e){const t=ge;e[0]instanceof ot?e.forEach(n=>this[t(380)](n)):this[t(391)](e,!![])}}class _a{constructor(e){const t=ge;this[t(386)]=![],this[t(388)]=!![],this.paintInterval=500,this[t(400)]=e,this.inputSystem=new Xx,this[t(382)]=new Fr(e);let n=this.render.canvas;n.style.backgroundColor=t(375),n[t(376)].border=t(367),n[t(376)][t(392)]="absolute",n[t(376)][t(369)]=""+(e.handlerLayer[t(369)]+1),n[t(376)].opacity="0.7",n.style.right="0",n[t(376)][t(374)]=null,n.style.bottom="0",e.layersContainer.appendChild(n),this[t(415)]=n,this.domElement=n,this.render.setSize(200,200*.618),this[t(402)]=new M(null,0,0),this[t(402)].hide(),this.initEvent(),this.hide()}[Z(370)](e){const t=Z;if(e==null)return this;for(let n in e)this.canvas[t(376)][n]=e[n];return this}initEvent(){const e=Z;let t=this,n=[e(384),"mouseup","mousemove","mousewheel"],s=t.render,i=s.canvas,r=this.inputSystem;n.map(function(o){fe.addEventListener(i,o,function(a){if(a.offsetX<0||a.offsetY<0)return;r.updateMouseInfo(a,o);let c=o+"Handler";if(t[c]==null)throw new Error("Overview has no handler:"+o);t[c](a)})})}[Z(368)](){const e=Z;this.visible=!![],this[e(382)][e(368)](),clearInterval(this._overviewTimer);let t=this;return this._overviewTimer=setInterval(function(){const n=e;t[n(388)]&&t[n(398)]()},this[e(379)]),this}[Z(418)](){return this.visible=![],this.render.hide(),clearInterval(this._overviewTimer),this}getWidth(){const e=Z;return this.render[e(383)]()}getHeight(){return this.render.getHeight()}[Z(377)](e,t){return this.render.setSize(e,t)}paint(){const e=Z;this.dirty=![];let t=this,n=t.stage,s=t.render,i=t.getWidth(),r=t.getHeight(),o=this.stage.getExportAABB(),a=Math.max(i,o[e(387)]),c=Math[e(416)](r,o.height),l=i/a,h=r/c;s.clearAll();let u=s.context;u.save(),u.scale(l,h),u.translate(-o.x,-o.y),n.getChildren().forEach(function(g){s.overviewPaint(g)}),u.restore(),this.paintDragRect(u,o)}[Z(413)](e,t){const n=Z;let s=this.rectObj,i=this.stage,r=this.render,o=Math.max(t[n(387)],i.width),a=Math.max(t.height,i.height),c=i.width/o,l=i.height/a;if(c==1&&l==1){s.hide();return}s.show();let h=r[n(383)]()*c,u=r[n(407)]()*l;s.resizeTo(h,u);let g=r.getWidth()/o,y=r.getHeight()/a,m=-t.x*g,I=-t.y*y;m<0&&(m=0),I<0&&(I=0),m+s.width>r.getWidth()&&(m=r.getWidth()-s.width),I+s.height>r.getHeight()&&(I=r.getHeight()-s[n(371)]),s.translateTo(m,I),e.save(),e[n(414)]=2,e.fillStyle="rgba(0,250,50,0.2)",e[n(390)]=n(366),e.beginPath(),e.rect(s.x,s.y,s[n(387)],s.height),e.stroke(),e[n(409)](),e.restore()}update(){const e=Z;this.visible&&this.stage[e(404)].length>0&&(this.stage.update(),this[e(396)]())}mousedownHandler(){const e=Z;let t=this[e(402)][e(403)](),n=this.inputSystem.x,s=this.inputSystem.y;t.contains(n,s)&&(this.inputSystem.target=this.rectObj);let i=n-(this[e(402)].x+this[e(402)].width/2),r=s-(this[e(402)].y+this[e(402)].height/2);this.moveWith(i,r)}mousedragHandler(){const e=Z;let t=this.inputSystem.dx,n=this.inputSystem.dy;this.moveWith(t,n),this.stage[e(412)]&&this.stage.editor.update()}[Z(401)](e,t){const n=Z;if(!this.rectObj[n(386)])return;e<0&&this[n(402)].x+e<=0&&(e=-this.rectObj.x),e>0&&this[n(402)].x+this[n(402)].width>=this.getWidth()&&(e=this.getWidth()-this.rectObj.width-this.rectObj.x),t<0&&this.rectObj.y<=0&&(t=-this.rectObj.y),t>0&&this.rectObj.y+this.rectObj.height>=this.getHeight()&&(t=this.getHeight()-this.rectObj[n(371)]-this.rectObj.y),this.rectObj.translateWith(e,t);let i=this.stage.getExportAABB(),r=i[n(387)],o=i.height,a=r*(e/this.getWidth()),c=o*(t/this.getHeight());this[n(400)].getChildren().forEach(function(h,u){h.translateWith(-a,-c)});let l=this;this[n(389)]=setTimeout(function(){l.update()},20),this[n(400)].update()}mousewheelHandler(e){e[Z(411)]()}[Z(395)](){this.inputSystem.isDraging&&this.mousedragHandler()}mouseupHandler(){this.inputSystem.target=null}}function ge(x,e){const t=Ux();return ge=function(n,s){return n=n-366,t[n]},ge(x,e)}function Ux(){const x=["overviewPaint","593478mihQnn","render","getWidth","mousedown","906890hhYwRq","visible","width","dirty","_updateTimer","strokeStyle","_paintObjects","position","16eCxAeY","137106GCMYMy","mousemoveHandler","paint","18RBLNiS","update","305718oqlIAj","stage","moveWith","rectObj","getRect","children","applyTo","_computedStyle","getHeight","40986eLTLYg","fill","60vdBUNr","preventDefault","editor","paintDragRect","lineWidth","canvas","max","192VHYfQE","hide","red","solid 1px gray","show","zIndex","css","height","68243IQJIko","496gAyoul","left","rgba(255,255,255,0.5)","style","setSize","550429xqXons","paintInterval"];return Ux=function(){return x},Ux()}const ma=`.jtopo_popoupmenu{padding:10px;border-radius:5px;min-width:210px;background-color:#fff;border:1px solid;position:absolute;z-index:10000}.jtopo_popoupmenu .header{font-size:14px;height:24px;padding-bottom:3px}.jtopo_popoupmenu a{text-rendering:optimizeLegibility;font-family:Open Sans,Helvetica Neue,Helvetica,Arial,sans-serif;padding-left:20px;display:block;height:25px;color:#00000080;font-size:13px;cursor:pointer}.jtopo_popoupmenu a:hover{color:#000}.jtopo_iconsPanel{opacity:.8;padding-left:5px;position:absolute;background-color:#e8e8e8;top:90px;width:52px;height:425px;z-index:1000;border-radius:5px}.jtopo_iconsPanel .item{border:1px solid white;width:42px;height:42px;margin-top:10px}.jtopo_toolbar{border-bottom:1px dotted;padding-bottom:2px;border-color:#e0e0e0;width:100%;min-height:33px;background-color:#e8e8e8}.jtopo_toolbar .group{float:left;margin-right:5px}.jtopo_toolbar .item{float:left;width:32px;height:32px;stroke:gray;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;fill:none;font-size:12px;color:gray}.jtopo_toolbar .active{background-color:#d3d3d3;border:1px solid black}.jtopo_toolbar input[type=text]{font-size:12px;color:gray;float:left;width:120px;height:26px;border:1px solid white;margin:2px 2px 2px 4px}.jtopo_input_textfield{position:absolute;display:none;font-size:smaller;z-index:10000}.jtopo_tooltip{pointer-events:none;opacity:.9;min-width:30px;min-height:30px;padding:10px;border-radius:5px;background-color:#f8f8f8;border:1px solid;position:absolute;z-index:10000}.jtopo_historyPanel{position:absolute;left:0px;top:100%;width:879px;overflow-x:scroll;height:600px;z-index:1000}.jtopo_debugPanel{user-select:none;border:dashed 1px gray;padding:8px;position:absolute;left:0px;top:0%;width:300px;z-index:98;text-align:left;font-size:10px;color:gray}
`,ct=yt;(function(x,e){const t=yt,n=x();for(;[];)try{if(parseInt(t(336))/1*(-parseInt(t(338))/2)+parseInt(t(331))/3*(parseInt(t(322))/4)+parseInt(t(328))/5*(parseInt(t(357))/6)+parseInt(t(354))/7+-parseInt(t(348))/8+-parseInt(t(362))/9*(parseInt(t(355))/10)+-parseInt(t(314))/11*(-parseInt(t(317))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Zx,510624);function Zx(){const x=["isPause","alternate","2790976iWNUvP","from","animations","delayed","currentTime","finished","262374QEsVSk","6509830ZLlGpM","now","1368nuYalk","timeline","pause","alternate-reverse","reject","9yuCkiU","remove","465575CifuQm","isArray","direction","336rfYYcW","sqrt","catch","onEnd","tick","2805992epYfmu","fillMode","playState","stepAction","play","system","10075VZWMqH","reverse","asin","3OeBMmV","resolve","setAttr","cancel","add","291007NjMHIE","length","6uBWxPb","sin","playedTimes","promise","pow","continue","duration","startTime"];return Zx=function(){return x},Zx()}function Ia(x,e,t,n){return t*x/n+e}function wa(x,e,t,n){return t*(x/=n)*x+e}function va(x,e,t,n){return-t*(x/=n)*(x-2)+e}function ka(x,e,t,n){return(x/=n/2)<1?t/2*x*x+e:-t/2*(--x*(x-2)-1)+e}function Sa(x,e,t,n){return-t*Math.cos(x/n*(Math.PI/2))+t+e}function Pa(x,e,t,n){return t*Math[yt(339)](x/n*(Math.PI/2))+e}function Oa(x,e,t,n){return-t/2*(Math.cos(Math.PI*x/n)-1)+e}function Ea(x,e,t,n){return x==0?e:t*Math.pow(2,10*(x/n-1))+e}function La(x,e,t,n){return x==0?e:x==n?e+t:(x/=n/2)<1?t/2*Math.pow(2,10*(x-1))+e:t/2*(-Math.pow(2,-10*--x)+2)+e}function Ta(x,e,t,n){return-t*(Math.sqrt(1-(x/=n)*x)-1)+e}function Ca(x,e,t,n){return t*Math[yt(318)](1-(x=x/n-1)*x)+e}function Aa(x,e,t,n){const s=yt;return(x/=n/2)<1?-t/2*(Math[s(318)](1-x*x)-1)+e:t/2*(Math.sqrt(1-(x-=2)*x)+1)+e}function Ma(x,e,t,n){return t*(x/=n)*x*x+e}function Da(x,e,t,n){return t*((x=x/n-1)*x*x+1)+e}function Ba(x,e,t,n){return(x/=n/2)<1?t/2*x*x*x+e:t/2*((x-=2)*x*x+2)+e}function ja(x,e,t,n){return t*(x/=n)*x*x*x+e}function za(x,e,t,n){return-t*((x=x/n-1)*x*x*x-1)+e}function Na(x,e,t,n){return(x/=n/2)<1?t/2*x*x*x*x+e:-t/2*((x-=2)*x*x*x-2)+e}function Ra(x,e,t,n){return t*(x/=n)*x*x*x*x+e}function Wa(x,e,t,n){return t*((x=x/n-1)*x*x*x*x+1)+e}function Ha(x,e,t,n){return(x/=n/2)<1?t/2*x*x*x*x*x+e:t/2*((x-=2)*x*x*x*x+2)+e}function Fa(x,e,t,n){const s=yt;var o=1.70158,i=0,r=t;if(x==0)return e;if((x/=n)==1)return e+t;if(i||(i=n*.3),r<Math.abs(t)){r=t;var o=i/4}else var o=i/(2*Math.PI)*Math[s(330)](t/r);return-(r*Math.pow(2,10*(x-=1))*Math.sin((x*n-o)*(2*Math.PI)/i))+e}function Xa(x,e,t,n){const s=yt;var o=1.70158,i=0,r=t;if(x==0)return e;if((x/=n)==1)return e+t;if(i||(i=n*.3),r<Math.abs(t)){r=t;var o=i/4}else var o=i/(2*Math.PI)*Math.asin(t/r);return r*Math[s(342)](2,-10*x)*Math[s(339)]((x*n-o)*(2*Math.PI)/i)+t+e}function Ya(x,e,t,n){const s=yt;var o=1.70158,i=0,r=t;if(x==0)return e;if((x/=n/2)==2)return e+t;if(i||(i=n*(.3*1.5)),r<Math.abs(t)){r=t;var o=i/4}else var o=i/(2*Math.PI)*Math.asin(t/r);return x<1?-.5*(r*Math[s(342)](2,10*(x-=1))*Math.sin((x*n-o)*(2*Math.PI)/i))+e:r*Math[s(342)](2,-10*(x-=1))*Math.sin((x*n-o)*(2*Math.PI)/i)*.5+t+e}function Ja(x,e,t,n,s){return s==null&&(s=1.70158),t*(x/=n)*x*((s+1)*x-s)+e}function Va(x,e,t,n,s){return s==null&&(s=1.70158),t*((x=x/n-1)*x*((s+1)*x+s)+1)+e}function Ga(x,e,t,n,s){return s==null&&(s=1.70158),(x/=n/2)<1?t/2*(x*x*(((s*=1.525)+1)*x-s))+e:t/2*((x-=2)*x*(((s*=1.525)+1)*x+s)+2)+e}function Xr(x,e,t,n){return t-qi(n-x,0,t,n)+e}function qi(x,e,t,n){return(x/=n)<1/2.75?t*(7.5625*x*x)+e:x<2/2.75?t*(7.5625*(x-=1.5/2.75)*x+.75)+e:x<2.5/2.75?t*(7.5625*(x-=2.25/2.75)*x+.9375)+e:t*(7.5625*(x-=2.625/2.75)*x+.984375)+e}function qa(x,e,t,n){return x<n/2?Xr(x*2,0,t,n)*.5+e:qi(x*2-n,0,t,n)*.5+t*.5+e}function yt(x,e){const t=Zx();return yt=function(n,s){return n=n-313,t[n]},yt(x,e)}let Ua={easeLinear:Ia,easeInQuad:wa,easeOutQuad:va,easeInOutQuad:ka,easeInSine:Sa,easeOutSine:Pa,easeInOutSine:Oa,easeInExpo:Ea,easeInOutExpo:La,easeInCirc:Ta,easeOutCirc:Ca,easeInOutCirc:Aa,easeInCubic:Ma,easeOutCubic:Da,easeInOutCubic:Ba,easeInQuart:ja,easeOutQuart:za,easeInOutQuart:Na,easeInQuint:Ra,easeOutQuint:Wa,easeInOutQuint:Ha,easeInElastic:Fa,easeOutElastic:Xa,easeInOutElastic:Ya,easeInBack:Ja,easeOutBack:Va,easeInOutBack:Ga,easeInBounce:Xr,easeOutBounce:qi,easeInOutBounce:qa};class Ui{constructor(e,t,n,s){const i=yt;this.duration=1e3,this.delay=0,this[i(316)]="normal",this[i(323)]="none",this[i(324)]="idle",this[i(346)]=![],this[i(353)]=![],this.delayed=![],this.times=1,this[i(340)]=0,this.effect="easeLinear",this.from=e,this.to=t,n!=null&&(this.duration=n),this.update=s}set(e,t,n,s){const i=yt;return this.from=e,this.to=t,this[i(344)]=n,this.update=s,this}setFrom(e){const t=yt;return this[t(349)]=e,this}setTo(e){return this.to=e,this}[ct(333)](e){return Object.assign(this,e),this}onUpdate(e){return this.update=e,this}cancel(){const e=ct;return this.system&&this.system.remove(this),this.reject&&(this[e(361)](),this[e(361)]=null),this.playState="finished",this}[ct(359)](){return this.playState="paused",this.isPause=!![],this}[ct(343)](){const e=ct;return this.startTime=Date[e(356)]()-this[e(352)],this[e(324)]="running",this.isPause=![],this}tick(e){const t=ct;if(this.playState!="running")return![];let n=e-this[t(345)];return this.currentTime=n,n>=this.duration?(this.playState="finished",this.system[t(313)](this),this.stepAction(this[t(344)]),this.playedTimes<this.times?this[t(326)]():(this[t(332)](),this[t(332)]=null,this.onEnd&&this[t(320)]())):this.stepAction(n),!![]}play(){const e=ct;let t=this;this[e(327)][e(335)](this),this.playedTimes++,this.isPause=![],this.delay!=0&&this.delayed==![]?(setTimeout(function(){const r=e;t[r(345)]=Date.now(),t.playState="running"},this.delay),t[e(351)]=!![]):(t[e(345)]=Date.now(),t.playState="running");const n=this;let s=this._getTickAction();this[e(325)]=s;let i=this[e(341)];return i==null&&(i=new Promise(function(r,o){n[e(332)]==null&&(n.resolve=r,n.reject=o)}),this[e(341)]=this.promise),i[e(319)](r=>{})}_getTickAction(){const e=ct;let t=this.effect,n=this.from,s=this.to,i=this.duration,r=this.update,o=n,a=s;if(typeof n=="number"&&(o=[n],a=[s]),this[e(316)]==e(329)||this.direction=="alternate-reverse"){let E=o;o=a,a=E}let c=o[0];const l=Array[e(315)](n),h=typeof c=="number",u=c.x!=null||c.y!=null;let g=o.slice(),y,m=Ua[t],I=this[e(316)]==e(347)||this.direction==e(360),b=this,k=this[e(344)]*.5;if(h)y=function(E){const D=e;let j=E;I&&(E>k?j=b[D(344)]*2-j*2:j=E*2);for(let Mt=0;Mt<o.length;Mt++){const Dt=o[Mt],q0=a[Mt],Le=q0-Dt;if(Le==0)g[Mt]=Dt;else{let Xc=m(j,Dt,Le,i);g[Mt]=Xc}}r(l?g:g[0])};else if(u)y=function(E){for(let D=0;D<o.length;D++){const j=o[D],Mt=a[D],Dt=Mt.x-j.x,q0=Mt.y-j.y;let Le={x:j.x,y:j.y};Dt!=0&&(Le.x=m(E,j.x,Dt,i)),q0!=0&&(Le.y=m(E,j.y,q0,i)),g[D]=Le}r(l?g:g[0])};else throw new Error("value format error.");return y}}class Zi{constructor(){const e=ct;this.animations=[],this[e(358)]={begin:1735488e6,end:Date.now()}}add(e){this[ct(350)].indexOf(e)==-1&&this.animations.push(e)}remove(e){R.remove(this.animations,e)}cancelAll(){const e=ct;for(let t=0;t<this.animations[e(337)];t++)this.animations[t][e(334)]();this.animations.length=0}[ct(321)](e){const t=ct;let n=![];for(let s=0;s<this.animations.length;s++)this[t(350)][s].tick(e)&&n==![]&&(n=!![]);return this.animations=this.animations.filter(s=>s.playState!=t(353)),n}anime(e){const t=ct;e[t(344)]==null&&(e[t(344)]=1e3);let n=new Ui;return n.system=this,n.setAttr(e),n}}const Kx=St;(function(x,e){const t=St,n=x();for(;[];)try{if(parseInt(t(365))/1+parseInt(t(358))/2+parseInt(t(356))/3+-parseInt(t(351))/4+-parseInt(t(352))/5*(-parseInt(t(385))/6)+-parseInt(t(366))/7+parseInt(t(377))/8*(-parseInt(t(386))/9)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Qx,583158);function St(x,e){const t=Qx();return St=function(n,s){return n=n-349,t[n]},St(x,e)}function Qx(){const x=["length","currentTheme","dirty","8rfbjLP","name","className","update","stringify","parent","getStyle","_afterStyleComputed","1362eatEok","7193394aKETxT","Resources","stage","811768abEcCU","16965cymKGH","toJSON","keys","addTheme","348354SxJrBa","defStyles","1246846KFHXBa","setTheme","assign","LinkLabel","markDirty","_computedStyle","getTheme","526974fdbypz","3159520PpwlQL","getComputedStyle","DefaultLight","SelectedStyle","filter","styles","CustomStyle","themeName"];return Qx=function(){return x},Qx()}class Ki{constructor(e){const t=St;this.themes={},this.defStyles={},this.selectedStyle=de.getStyle(t(369)),this[t(350)]=e,this[t(355)](de),this[t(355)](Hi),this.setTheme(de.name)}addTheme(e){this.themes[e.name]=e}getTheme(e){return this.themes[e]}customStyleToJSON(e){const t=St;let n=JSON.parse(JSON[t(381)](this.themes));delete n[t(368)],delete n.DefaultDark;let s={},i={themeName:this.currentTheme.name,themes:n,styles:s},r=Object.keys(this.defStyles);for(let o=0;o<r[t(374)];o++){let a=r[o],c=this.defStyles[a],l=c[t(353)](e);s[a]=l}return i}fromJson(e){const t=St;let n=e[t(372)];if(n==null)return;let s=n.themes,i=n[t(371)]||{};if(s!=null){let a=Object[t(354)](s);for(let c=0;c<a.length;c++){let l=a[c],h=s[l],u=new He(l,h.content);this.addTheme(u)}}let r=e[t(349)]||{};this[t(357)]={};let o=Object.keys(i)[t(370)](a=>a.startsWith("."));for(let a=0;a<o.length;a++){let c=o[a],l=i[c];this.defStyles[c]=et.fromJSON(l,r)}n[t(373)]!=null&&this[t(359)](n.themeName)}defClass(e,t){const n=St;if(t instanceof et){this.defStyles[e]=t;return}let s=new et;Object.assign(s,t),this.defStyles[e]=s,!e.startsWith(".")&&this[n(362)]()}removeClass(e){const t=St;delete this.defStyles[e],!e.startsWith(".")&&this[t(362)]()}getClass(e){return this.defStyles[e]}markDirty(){const e=St;this[e(350)].children.forEach(n=>{const s=e;n.style.dirty=!![],n.querySelectorAll().forEach(i=>i.style[s(376)]=!![])})}start(){const e=St;this.setTheme(this[e(375)][e(378)])}setTheme(e){let n=this[St(364)](e);if(n==null)throw new Error("theme not exist.");this.currentTheme=n,this.markDirty(),this.stage.update()}[Kx(367)](e){return e[Kx(363)]}computeStyle(e){const t=Kx;let n=this,s={},i=this.currentTheme,r=e[t(379)];e[t(382)]instanceof W&&(e===e.parent.label?r=t(361):(e===e.parent.beginArrow||e===e.parent.endArrow)&&(r="LinkArrow"));let o=i[t(383)](r);o!=null&&Object.assign(s,o);let a=n.getClass(r);a!=null&&Object[t(360)](s,a);let c=e.classList;if(c.length>0)for(let l=0;l<c[t(374)];l++){const h=c[l];let u=n.getClass(h);u!=null&&Object.assign(s,u)}return Object.assign(s,e.style.getChangedProps()),e._computedStyle[t(380)](s),e[t(384)](),e._computedStyle}defTheme(e,t){let s=this[Kx(364)](e);if(s==null)throw new Error("theme not exist:"+e);let i=s.copy(t);return this.addTheme(i),i}}const qe=pn;function pn(x,e){const t=$x();return pn=function(n,s){return n=n-476,t[n]},pn(x,e)}(function(x,e){const t=pn,n=x();for(;[];)try{if(parseInt(t(516))/1*(parseInt(t(504))/2)+-parseInt(t(476))/3*(parseInt(t(518))/4)+parseInt(t(479))/5*(parseInt(t(505))/6)+-parseInt(t(497))/7+parseInt(t(485))/8*(-parseInt(t(478))/9)+-parseInt(t(480))/10+parseInt(t(494))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})($x,357680);function $x(){const x=["style","destroyed","_requestReapint","painted","132368CZVEUF","_frames","_renderLayerBefore","animationSystem","_cameraVisible","currentTime","mode","visible","isOutOfViewPort","8955034wGDmtC","updateMatrix","now","4281921zKhRAZ","clone","computeStyle","_isOutOfViewport","requestTimer","length","dirty","4114OEVxNc","696HbpgrZ","localView","stage","_isMatrixDirty","displayList","width","tick","isLink","onUpdate","flatten","_needPaint","12TyjftT","styleSystem","2020WgWNQN","975KbgvBF","getViewportRectInLayer","9EmWkVf","14830GeGRQv","327800tRqVqc"];return $x=function(){return x},$x()}class Za{constructor(e){const t=pn;this.timeline={currentTime:Date[t(496)]()},this.stage=e}[qe(487)](e){const t=qe;e.style.dirty&&!(e instanceof fn)&&this[t(507)][t(517)][t(499)](e),e[t(508)]()&&e[t(495)](),e.clearMatrixDirtyMark(),e.style.dirty=![];let n=L[t(514)](e.children,null);e.flattenList=n,e[t(509)].length=0;for(let s=0;s<n[t(502)];s++){let i=n[s];i[t(481)][t(503)]&&this.stage[t(517)][t(499)](i),i._isMatrixDirty()&&i[t(495)](),i._afterUpdate(),i.clearMatrixDirtyMark(),i.style[t(503)]=![],i.painted=![],i.isPointOn=![],i[t(500)]=this.isOutOfViewPort(i,e);let r=i.visible&&i.parent._needPaint;i[t(515)]=r,i._isMouseInAABB=![],r&&!i._isOutOfViewport&&(i._isMouseInAABB=this.isMouseInObjectAABB(i,e))}for(let s=0;s<n[t(502)];s++){let i=n[s];(i._isMatrixDirty()||i.parent[t(512)])&&(i[t(495)](),i._afterUpdate(),i.clearMatrixDirtyMark(),i[t(500)]=this.isOutOfViewPort(i,e),i._isMouseInAABB=![],i._needPaint&&!i._isOutOfViewport&&(i._isMouseInAABB=this.isMouseInObjectAABB(i,e))),i._needPaint&&!i[t(500)]&&i[t(489)]&&e.displayList.push(i)}}[qe(493)](e,t){const n=qe;if(t.cuttingHide==![])return![];let i=t[n(477)](),r=e._obb;return r.aabb==null?![]:!i.isIntersectRect(r.aabb)}isMouseInObjectAABB(e,t){const n=qe,s=t[n(507)];if(s.inputSystem.x<0||s.inputSystem.y<0)return![];let i=e._obb.aabb[n(498)]();return e instanceof W&&(i=i[n(498)](),i.x-=2,i.y-=2,i[n(510)]+=4,i.height+=4),i.contains(t.mouseX,t.mouseY)}_tickLayer(e,t){const n=qe;let s=e.render;if(!(e[n(492)]==![]||e[n(482)]||s.stoped||s.destoryed==!![])){if(e[n(486)]==0){(e[n(483)]==!![]||L._anyMatrixOrStyleDirty(e))&&(e[n(483)]=![],this._renderLayerBefore(e),s.renderLayer(e),e.renderTimes++);return}this[n(487)](e),s.renderLayer(e),e.renderTimes++}}pause(){cancelAnimationFrame(this.requestTimer)}start(){const e=qe,t=this.stage,n=this,s=t.handlerLayer,i=t.children,r=this.timeline,o=t[e(488)],a=t.behaviourSystem;{let h=t[e(488)].timeline.begin;this.timeline[e(490)]>=h&&(t.localView[e(491)]=e(484))}const c=t[e(506)][e(491)]==e(484);function l(){const h=e;if(t.destoryed)return;let u=Date.now();r.currentTime=u,a.tick(u);let g=![];!c&&(g=o[h(511)](u)),g==!![]&&(s[h(483)]=!![]),n._tickLayer(s,u);for(let y=0;y<i.length;y++){let m=i[y];g&&(m._requestReapint=!![]),m[h(513)]!=null&&m.onUpdate(),n._tickLayer(m,u)}n[h(501)]=requestAnimationFrame(l)}l()}}const Ue=v0;function ts(){const x=["186680SiAZnc","href","882iyLFLp","toDataURL","revokeObjectURL","download","unionRects","saveDataAsFile","469568Gbntsf","about:blank","appendChild","3465553vBULlV","61750UUnymq","60wSlZNz","save","saveAsLocalImage","639012VqzOgi","body","9948037xKNHhk","389SkCBQS","_obb","8xWLZma","117vmeHRX"];return ts=function(){return x},ts()}(function(x,e){const t=v0,n=x();for(;[];)try{if(parseInt(t(123))/1*(parseInt(t(129))/2)+parseInt(t(120))/3*(parseInt(t(125))/4)+-parseInt(t(116))/5*(parseInt(t(117))/6)+parseInt(t(115))/7+-parseInt(t(112))/8+parseInt(t(126))/9*(parseInt(t(127))/10)+-parseInt(t(122))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ts,248757);function v0(x,e){const t=ts();return v0=function(n,s){return n=n-112,t[n]},v0(x,e)}class Ka{constructor(e){this.stage=e,this.render=new Fr(e)}[Ue(130)](e){const t=Ue;let n=Array.isArray(e)?e:[e];return this._exportPaint(n),this.render[t(130)]()}toImage(e){let t=this.toDataURL(e),n=new Image;return n.src=t,n}[Ue(119)](e,t){const n=Ue;let s=this.toDataURL(e);t==null&&(t="jtopo_"+new Date().getTime()+".png"),this[n(134)](s,t)}saveImageInfo(e){const t=Ue;let n=this.toDataURL(e);window.open(t(113)).document.write("<img src='"+n+"' alt='from canvas'/>")}download(e,t){const n=new File([t],e,{type:"text/json"});function s(i){const r=v0,o=document.createElement("a"),a=URL.createObjectURL(i);o[r(128)]=a,o[r(132)]=i.name,document.body[r(114)](o),o.click(),document[r(121)].removeChild(o),URL[r(131)](a)}s(n)}_exportPaint(e){const t=Ue;let n=this.render,s=e.map(a=>{const c=v0;return a instanceof ot?a.toStageRect(a.getExportAABB()):a[c(124)].aabb}),i=z[t(133)](s),r=Math.max(1,i.width),o=Math.max(1,i.height);n.setSize(r,o),n.context[t(118)](),n.context.translate(-i.x,-i.y),e.forEach(function(a){n.exportPaintObjects([a])}),n.context.restore()}saveDataAsFile(e,t){const n=Ue;let s=document.createElementNS("http://www.w3.org/1999/xhtml","a");s.href=e,s[n(132)]=t;let i=document.createEvent("MouseEvents");i.initMouseEvent("click",!![],![],window,0,0,0,0,0,![],![],![],![],0,null),s.dispatchEvent(i)}}(function(x,e){for(var t=ns,n=x();[];)try{var s=parseInt(t(466))/1*(-parseInt(t(472))/2)+parseInt(t(465))/3*(parseInt(t(464))/4)+parseInt(t(468))/5+-parseInt(t(473))/6*(parseInt(t(470))/7)+-parseInt(t(474))/8*(parseInt(t(471))/9)+-parseInt(t(467))/10+parseInt(t(463))/11;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(es,842036);function es(){var x=["36901557VtZObZ","1296jxzIBC","13194JBqKNa","203udgIVP","7861870SrGCMh","3547725untEUn","writable","8649844uBCdXw","9fsdTJm","13612lPOUpF","6LkghqI","9949208rrVrxA"];return es=function(){return x},es()}function ns(x,e){var t=es();return ns=function(n,s){n=n-463;var i=t[n];return i},ns(x,e)}function Qa(x,e){for(var t in e){let n=e[t];$a(x,t,n)}}function $a(x,e,t){var n=ns;t.writable==null&&(t[n(469)]=!![]),t.enumerable==null&&(t.enumerable=!![]),Object.defineProperty(x,e,t)}const Pt=xs;(function(x,e){const t=xs,n=x();for(;[];)try{if(parseInt(t(379))/1*(-parseInt(t(378))/2)+-parseInt(t(363))/3*(-parseInt(t(385))/4)+-parseInt(t(392))/5+-parseInt(t(389))/6*(-parseInt(t(380))/7)+-parseInt(t(370))/8*(parseInt(t(373))/9)+parseInt(t(390))/10*(parseInt(t(368))/11)+parseInt(t(371))/12*(-parseInt(t(387))/13)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ss,452998);function xs(x,e){const t=ss();return xs=function(n,s){return n=n-362,t[n]},xs(x,e)}var tc=Object[Pt(372)],ec=Object[Pt(365)],Yr=(x,e,t,n)=>{for(var s=n>1?void 0:n?ec(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&tc(e,t,s),s};class $t extends M{constructor(e,t=0,n=0,s=1,i=1,r=!![]){super(e,t,n,s,i),this.autoDirection=!![],this._autoSize=!![],this.autoSize=r}get autoSize(){return this[Pt(382)]}set autoSize(e){this._autoSize=e}updateMatrix(){const e=Pt,t=this;if(t.autoDirection==!![]){let n=t._getOriginRotation();n<-Math.PI/2||n>Math.PI/2?t.rotation!==Math.PI&&(t.rotation=Math.PI):t[e(376)]!==0&&(t[e(376)]=0)}super.updateMatrix()}[Pt(384)](){this._calcTextSize(),this._autoSize&&this.resizeToFitText(),this._calcTextPosition()}resizeToFitText(){const e=Pt;let n=this[e(362)][e(367)]();this.width=this._textWidth+n,this.height=this._textHeight+n;let s=this._OBBPoints(),i=this._worldTransform.points(s),r=this._obb;r.localPoints=s,r[e(381)]=i,r.aabb=Bt.toAABB(i)}_calcTextPosition(){const e=Pt,t=this[e(362)];let n=t[e(386)]||0,s=(t[e(364)]||0)+(t.lineWidth|0);return(t.textBaseline=="bottom"&&(t.textPosition=="lt"||t.textPosition=="ct"||t.textPosition=="rt")||t.textBaseline=="top"&&(t.textPosition=="lb"||t[e(383)]=="cb"||t[e(383)]=="rb")||t.textAlign==e(377)&&(t.textPosition=="lt"||t[e(383)]=="lm"||t.textPosition=="lb")||t[e(366)]=="left"&&(t[e(383)]=="rt"||t.textPosition=="rm"||t.textPosition=="rb"))&&(n=0,s=0),super[e(388)](n,s)}}function ss(){const x=["points","_autoSize","textPosition","_updateText","100UVOGQk","borderWidth","39SlVBPD","_calcTextPosition","6LPaoAa","3847300Ihtgit","prototype","1512475nmDLLP","_computedStyle","24321zinHmF","padding","getOwnPropertyDescriptor","textAlign","calcGap","22AzsEkJ","serializers","11040oPvtNT","1586448iqacQF","defineProperty","2223KmdqKQ","autoDirection","concat","rotation","right","1304xEeFTy","76jWqRjZ","3992674tzxjdV"];return ss=function(){return x},ss()}Yr([f("TextNode")],$t[Pt(391)],"className",2),Yr([f("rect")],$t.prototype,"pickType",2),Qa($t[Pt(391)],{serializers:{value:M.prototype[Pt(369)][Pt(375)](["autoSize",Pt(374)])}});var Qi=Ze;(function(x,e){for(var t=Ze,n=x();[];)try{var s=-parseInt(t(496))/1+-parseInt(t(501))/2+parseInt(t(493))/3+parseInt(t(495))/4*(parseInt(t(489))/5)+-parseInt(t(503))/6*(parseInt(t(498))/7)+-parseInt(t(490))/8*(parseInt(t(502))/9)+parseInt(t(504))/10;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(is,125165);var nc=Object.defineProperty,xc=Object.getOwnPropertyDescriptor,sc=(x,e,t,n)=>{for(var s=n>1?void 0:n?xc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&nc(e,t,s),s};function Ze(x,e){var t=is();return Ze=function(n,s){n=n-489;var i=t[n];return i},Ze(x,e)}class k0 extends M{constructor(e,t=0,n=0,s=1,i=1){var r=Ze;super(e,t,n,s,i),this.shape=A[r(491)],this[r(492)](s/2)}setRadius(e=1){var t=Ze;this.width=e*2,this[t(500)]=e*2}getRadius(){return this.radius}getPoint(e){var t=Ze;let n=Math[t(499)](this.width,this[t(500)])*.5,s=e*(2*Math.PI);return{x:this.x+n+n*Math.cos(s),y:this.y+n+n*Math[t(494)](s)}}}function is(){var x=["6374010FgJYno","982585APOYIH","136lWFPCw","Circle","setRadius","26052ITevxj","sin","4XDDrIc","54094xqZfRz","defineProperties","21lVgQng","max","height","429854XbUgpg","111483YZkrQM","475674moLkwG"];return is=function(){return x},is()}sc([f("CircleNode")],k0.prototype,"className",2),Object[Qi(497)](k0.prototype,{radius:{get(){var x=Qi;return Math.max(this.width,this[x(500)])*.5},set(x){var e=Qi;this[e(492)](x)}}});const J=Ke;(function(x,e){const t=Ke,n=x();for(;[];)try{if(-parseInt(t(476))/1*(-parseInt(t(503))/2)+-parseInt(t(481))/3+parseInt(t(500))/4*(parseInt(t(501))/5)+parseInt(t(485))/6+-parseInt(t(493))/7+parseInt(t(492))/8+-parseInt(t(505))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(rs,468574);function rs(){const x=["matrixDirty","looksSame","centerOffset","_calcFold2Vec","35ZIrFYy","resetOffset","_calcFold1","_calcFold2","prototype","1987647DVuxXH","abs","serializers","_calcAZ","3602820qwGNpk","_afterUpdateMatrix","end","middle","dot","className","getAngle","1918216xThOzq","5373438bWvYzc","length","_calcFold1Vec","getK","points","AutoFoldLink","absorb","7240BdGQSK","1265RiGsBV","begin","44152jSLwYs","_needCalcOffset","1548720GYrvJz","setEndOffsetGap","sign"];return rs=function(){return x},rs()}var ic=Object.defineProperty,rc=Object.getOwnPropertyDescriptor,gn=(x,e,t,n)=>{const s=Ke;for(var i=n>1?void 0:n?rc(e,t):e,r=x[s(494)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&ic(e,t,i),i};function Ke(x,e){const t=rs();return Ke=function(n,s){return n=n-470,t[n]},Ke(x,e)}class te extends W{constructor(e,t,n,s,i){const r=Ke;super(e,t,n,s,i),this[r(499)]=3}setBeginOffsetGap(e){const t=Ke;this.beginOffsetGap=e,this[t(472)]=!![]}[J(470)](e){const t=J;this.endOffsetGap=e,this[t(472)]=!![]}[J(478)](e,t){let n=this.beginOffsetGap,s=this._calcFold1Vec(e,t),i={x:e.x+s[0]*n,y:e.y+s[1]*n},r=this.fold1Offset;return r&&(i.x+=r.x,i.y+=r.y),i}[J(479)](e,t){const n=J;let s=this.endOffsetGap,i=this[n(475)](e,t),r={x:t.x+i[0]*s,y:t.y+i[1]*s},o=this.fold2Offset;return o&&(r.x+=o.x,r.y+=o.y),r}_calcFold1Vec(e,t){let n=Ix(this.begin);if(n==null){let s=0,i=0;t.x>e.x?s=1:s=-1,n=[s,i]}return n}_calcFold2Vec(e,t){let n=Ix(this.end);if(n==null){let s=0,i=-1;t.y>e.y?i=-1:i=1,n=[s,i]}return n}updatePoints(){const e=J,t=this[e(484)](),n=t[0],s=t[1];return this._calcPointsByAZ(n,s)}_calcPointsByAZ(e,t){const n=J,s=this.absorb;let i=this[n(495)](e,t);if(i[0]==0&&Math[n(482)](e.x-t.x)<s){let u=(e.x+t.x)*.5;e.x=u,t.x=u}if(i[1]==0&&Math.abs(e.y-t.y)<s){let u=(e.y+t.y)*.5;e.y=u,t.y=u}let r=this._calcFold1(e,t),o=this._calcFold2(e,t);const a=oc(this,e,t,r,o);let c=a[0],l=a[1];{if(C[n(473)](c,l,.5)==![]){const y=Math.abs(C[n(491)](c,l)).toFixed(6);this._preAngle!=null&&this._preAngle!=y&&(this[n(474)]=null),this._preAngle=y}let g=this.centerOffset;g&&(c.x+=g.x,c.y+=g.y,l.x+=g.x,l.y+=g.y)}return[e,r,c,l,o,t]}[J(486)](){const e=J;if(super[e(486)](),this[e(504)]()){let t=this[e(497)][0],n=this[e(497)][this[e(497)][e(494)]-1],s=this.offsetPoints[0],i=this.offsetPoints[this.offsetPoints[e(494)]-1];this[e(497)]=this._calcPointsByAZ(s,i),this.points[0]=t,this[e(497)][this.points.length-1]=n}}getMergedPoints(){return C.mergeClosestPoints(this.points)}setFold1Offset(e,t){const n=J;let s=this.fold1Offset;s==null&&(s={x:0,y:0},this.fold1Offset=s),on(this[n(496)](0,.5))?(s.y=0,t=0):(s.x=0,e=0),s.x+=e,s.y+=t,this.matrixDirty=!![]}setFold2Offset(e,t){let n=this.fold2Offset;n==null&&(n={x:0,y:0},this.fold2Offset=n),on(this.getK(4,.5))?(n.y=0,t=0):(n.x=0,e=0),n.x+=e,n.y+=t,this.matrixDirty=!![]}setCenterOffset(e,t){let n=this.centerOffset;n==null&&(n={x:0,y:0},this.centerOffset=n),on(this.getK(2,.5))?(n.x=0,e=0):(n.y=0,t=0),n.x+=e,n.y+=t,this.matrixDirty=!![]}[J(477)](){this.centerOffset=void 0,this.fold1Offset=null,this.fold2Offset=null,this.matrixDirty=!![]}}gn([f(J(498))],te.prototype,J(490),2),gn([f(15)],te.prototype,"beginOffsetGap",2),gn([f(15)],te.prototype,"endOffsetGap",2),gn([f(function(){const x=J;let e=this.getMergedPoints(),t=e.length;return t<4?[x(502),"end"]:t==4||t==5?["begin","end","fold1","fold2"]:[x(502),x(487),"fold1","fold2","center"]})],te.prototype,"getCtrlPoints",2),gn([f(W[J(480)][J(483)].concat(["beginOffsetGap","endOffsetGap","fold1Offset","fold2Offset","centerOffset"]))],te[J(480)],"serializers",2);const be={};be[S.begin]=function(){return this.points[0]},be[S.fold1]=function(){return this.points[1]},be[S.mid1]=function(){return this[J(497)][2]},be[S.mid2]=function(){return this.points[3]},be[S.fold2]=function(){return this.points[4]},be[S.end]=function(){return this.points[5]},be[S.center]=function(){return C[J(488)](this.points[2],this.points[3])},te.prototype.DefaultPositions=be;function os(x,e){const t=J;return Math.abs(e[0])>Math.abs(e[1])?x.x*Math[t(471)](e[0]):x.y*Math.sign(e[1])}function Jr(x,e,t){let n=os(x,e);return os(t,e)-n}function $i(x,e,t,n){let s=os(x,t),i=os(e,n),r=t[0]!=0;return s>i?r?{x:x.x,y:e.y}:{x:e.x,y:x.y}:r?{x:e.x,y:x.y}:{x:x.x,y:e.y}}function oc(x,e,t,n,s){const i=J,r=x._calcFold1Vec(e,t),o=x._calcFold2Vec(e,t),a=B[i(489)](r,o);if(a==1){let u=$i(n,s,r,o);return[u,u]}if(a==-1){const u=C[i(488)](n,s);let g=$i(n,u,r,r),y=$i(s,u,o,o);return[g,y]}let c=rn(e,n,t,s,!![]),l=Jr(e,r,c),h=Jr(t,o,c);if(l>0&&h>0)return[c,c];{const u=[-r[1],r[0]],g={x:n.x+u[0],y:n.y+u[1]},y=[-o[1],o[0]],m={x:s.x+y[0],y:s.y+y[1]};let I=rn(n,g,s,m,!![]);if(I!=null)return[I,I]}throw new Error("assert failed getMid1AndMid2")}const ee=S0;(function(x,e){const t=S0,n=x();for(;[];)try{if(parseInt(t(322))/1+parseInt(t(321))/2*(-parseInt(t(337))/3)+-parseInt(t(332))/4*(parseInt(t(328))/5)+-parseInt(t(330))/6*(-parseInt(t(331))/7)+parseInt(t(339))/8*(-parseInt(t(336))/9)+parseInt(t(327))/10+-parseInt(t(334))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(cs,320254);var ac=Object.defineProperty,cc=Object.getOwnPropertyDescriptor,as=(x,e,t,n)=>{for(var s=n>1?void 0:n?cc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&ac(e,t,s),s};function cs(){const x=["14646cFBtqv","1603jiOHJd","164sHvxyo","FlexionalLink","4827460rgRLZp","begin","58977MvpDGV","261asGCix","direction","128dZCLiq","fold1","prototype","2846XSgdOx","330223LFjDAr","getPoints","horizontal","offsetGap","center","6236790eoJJcE","64040nNMTCB","getFold2"];return cs=function(){return x},cs()}class ye extends W{constructor(e,t,n,s,i){super(e,t,n,s,i)}getFold1(e,t){const n=S0;let s=(t.y-e.y)/2,i=(t.x-e.x)/2;return this.direction==Te[n(324)]?{x:e.x+i,y:e.y}:{x:e.x,y:e.y+s}}getFold2(e,t){let n=(t.y-e.y)/2,s=(t.x-e.x)/2;return this.direction==Te.horizontal?{x:t.x-s,y:t.y}:{x:t.x,y:t.y-n}}updatePoints(){const e=S0,t=this._calcAZ(),n=t[0],s=t[1];let i=this.getFold1(n,s),r=this[e(329)](n,s),o={x:(i.x+r.x)/2,y:(i.y+r.y)/2};const a=[n,i,o,r,s];if(this.points=a,this.endArrow){let c=a.length-2;this.endArrow.origin[0]=c}return a}}function S0(x,e){const t=cs();return S0=function(n,s){return n=n-319,t[n]},S0(x,e)}as([f(ee(333))],ye.prototype,"className",2),as([f(W.prototype.serializers.concat(["direction","offsetGap"]))],ye.prototype,"serializers",2),as([f(Te[ee(324)])],ye.prototype,ee(338),2),as([f(44)],ye.prototype,ee(325),2);const P0={};P0[S[ee(335)]]=function(){return this.getPoints()[0]},P0[S[ee(319)]]=function(){return this.getPoints()[1]},P0[S[ee(326)]]=function(){return this.getPoints()[2]},P0[S.fold2]=function(){return this.getPoints()[3]},P0[S.end]=function(){return this[ee(323)]()[4]},ye[ee(320)].DefaultPositions=P0;var ne=ls;(function(x,e){for(var t=ls,n=x();[];)try{var s=-parseInt(t(458))/1+-parseInt(t(463))/2*(-parseInt(t(462))/3)+parseInt(t(466))/4*(-parseInt(t(471))/5)+-parseInt(t(457))/6*(-parseInt(t(460))/7)+parseInt(t(470))/8*(-parseInt(t(468))/9)+-parseInt(t(453))/10+parseInt(t(454))/11*(parseInt(t(467))/12);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(hs,766957);function ls(x,e){var t=hs();return ls=function(n,s){n=n-452;var i=t[n];return i},ls(x,e)}var lc=Object[ne(461)],hc=Object[ne(456)],Vr=(x,e,t,n)=>{for(var s=n>1?void 0:n?hc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&lc(e,t,s),s};function hs(){var x=["serializers","getOwnPropertyDescriptor","6GVuOSU","983772sJzJky","DefaultPositions","3513055UKcyez","defineProperty","1296111bDLZUZ","4OKMSlC","prototype","getPoints","140HEmEGr","36qioAiP","1202337MHPoyp","end","32VtEbxt","208810plxrFB","updatePoints","4539550fkwaiE","10394219pPWmAn"];return hs=function(){return x},hs()}class O0 extends W{constructor(e,t,n,s,i){super(e,t,n,s,i),this.direction="horizontal"}[ne(452)](){const e=this._calcAZ(),t=e[0],n=e[1];let s;return t.x==n.x||t.y==n.y?s=C.middle(t,n):this.direction==Te.horizontal?s={x:n.x,y:t.y}:s={x:t.x,y:n.y},[t,s,n]}}Vr([f("FoldLink")],O0.prototype,"className",2),Vr([f(W[ne(464)][ne(455)].concat(["direction"]))],O0[ne(464)],"serializers",2);const us={};us[S.begin]=function(){return this.getPoints()[0]},us[S[ne(469)]]=function(){var x=ne;return this[x(465)]()[2]},us[S.center]=function(){return this.getPoints()[1]},O0.prototype[ne(459)]=us;const lt=E0;function E0(x,e){const t=ds();return E0=function(n,s){return n=n-116,t[n]},E0(x,e)}function ds(){const x=["936zsRlXx","children","draggable","inLinks","2mvJWgM","asLabel","fromPoints","polygon","disconnect","1528IFGouu","32781MVoGJg","2811504sKEjeg","33bhIfok","indexOf","3846680yuAThE","endArrow","getRect","4182324JseVlx","height","label","createLabel","setXYButChildFixed","3364794ApmELl","setXYOnLink","length","addChild","parent","988403ojghXm","getEndPoint","getMiddleOrigin","editable","removeChild","travel","3687LuFhVi","createArrow","outLinks","5aSnHus","getUnionRect"];return ds=function(){return x},ds()}(function(x,e){const t=E0,n=x();for(;[];)try{if(-parseInt(t(128))/1*(-parseInt(t(143))/2)+parseInt(t(134))/3*(-parseInt(t(148))/4)+-parseInt(t(137))/5*(parseInt(t(118))/6)+parseInt(t(149))/7*(-parseInt(t(139))/8)+parseInt(t(123))/9+parseInt(t(153))/10+-parseInt(t(151))/11*(-parseInt(t(150))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ds,735370);class Yt{static createPologyNode(e,t=1,n=1){const s=E0;let i=new M;return i.setShape(A[s(146)](e)),i.resizeTo(t,n),i}static getUnionRect(e){const t=E0;let n=e[0][t(117)]();for(let s=1;s<e.length;s++)n=z.union(n,e[s][t(117)]());return n}static[lt(122)](e,t,n){const s=lt;let i=e.children;e.x+=t,e.y+=n;for(let r=0;r<i[s(125)];r++){const o=i[r];o instanceof M&&(o.x-=t,o.y-=n)}}static sizeFitToChildren(e,t){const n=lt;let s=e.getChildren();if(s.length==0)return;let i=Yt[n(138)](s);t==null&&(t=0);let r=t*2;e.resizeTo(i.width+r,i[n(119)]+r);let o=i.x+e.width/2,a=i.y+e.height/2;Yt[n(122)](e,o-t,a-t)}static translateObjectsCenterTo(e,t,n){const s=lt;let i={x:t,y:n},r=[];r=r.concat(e);let o=Yt[s(138)](r),a=o.getCenter(),c=i.x-a.x,l=i.y-a.y;r.forEach(h=>{h.translateWith(c,l)})}static travel(e,t,n,s){const i=lt;if(s==null)s=[];else if(R.hasChild(s,e))return null;if(t&&t(e,n),s.push(e),e instanceof M){let o=e[i(136)];if(o!=null)for(var r=0;r<o.length;r++){let a=o[r];Yt.travel(a,t,e,s)}}else e instanceof W&&e.end.isDisplayObjectTarget()&&Yt[i(133)](e.end.target,t,n,s);return s}}class L0{static[lt(147)](e,t){const n=lt;if(e instanceof W){e.unlink();return}let s=e[n(142)];s!=null&&(s.forEach(r=>{const o=n;r[o(127)]!=null&&(t!=null&&t[o(152)](r)!=-1||r.setEnd(r[o(129)]()))}),e[n(142)]=[]);let i=e.outLinks;i!=null&&(i.forEach(r=>{const o=n;r.parent!=null&&(t!=null&&t[o(152)](r)!=-1||r.setBegin(r.getBeginPoint()))}),e[n(136)]=[])}static[lt(121)](e,t){const n=lt;if(e[n(120)]==null){const s=new $t(t);return s[n(131)]=![],s[n(141)]=![],s.connectable=![],s.mouseEnabled=![],s.autoSize=!![],s.alignOriginToLink("cb",0,.5),L0[n(144)](e,s),s}return e.label.text=t,e.label}static asLabel(e,t){const n=lt;e.label!=null&&e.removeChild(e.label),e.label=t,e.children.indexOf(e[n(120)])==-1&&e[n(126)](e.label);let s=L0[n(130)](e);return t[n(124)](s.segIndex,s.t),t}static[lt(130)](e){let t=0,n=.5;return e instanceof O0?n=1:e instanceof ye?t=1:e instanceof te&&(t=2),{segIndex:t,t:n}}static[lt(135)](e,t=10,n=10){const s=lt;let i=new M;return i.resizeTo(t,n),e==null?i.setShape(A.Arrow):i.setShape(A[s(145)](e)),i.editable=![],i.draggable=![],i.connectable=![],i.mouseEnabled=![],i}static asBeginArrow(e,t){return e.beginArrow!=null&&e.removeChild(e.beginArrow),e.children.indexOf(t)==-1&&e.addChild(t),e.beginArrow=t,t}static asEndArrow(e,t){const n=lt;return e[n(116)]!=null&&e[n(132)](e.endArrow),e[n(140)].indexOf(t)==-1&&e.addChild(t),e.endArrow=t,t}}const Ot=ps;function fs(){const x=["css","lineDash","22ZZOAwC","direction","color","lineWidth","anime","mouseEnabled","animationList","play","428150IuWmrA","height","white","count","red","760696kcMSXH","text","width","199591UPgHrk","3511008QKAorp","times","435EKDOJB","segIndex","beginWidth","142682IHuvvP","5VTvpra","exportSystem","setRadius","center","strokeStyle","colorFilter","2692iCDvMr","minHeight","clockwise","animationSystem","linkMark","757728ZBspGt","9wYvZsT","setColors","children"];return fs=function(){return x},fs()}(function(x,e){const t=ps,n=x();for(;[];)try{if(parseInt(t(194))/1+-parseInt(t(201))/2*(-parseInt(t(191))/3)+parseInt(t(225))/4+parseInt(t(195))/5*(parseInt(t(206))/6)+-parseInt(t(188))/7+parseInt(t(189))/8*(-parseInt(t(207))/9)+parseInt(t(220))/10*(parseInt(t(212))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(fs,272555);function ps(x,e){const t=fs();return ps=function(n,s){return n=n-187,t[n]},ps(x,e)}class gs{constructor(e,t){this.objects=e,this.animationList=t}remove(){this.objects.forEach(e=>e.remove()),this.animationList.forEach((e,t)=>{e.cancel()})}[Ot(219)](){this[Ot(218)].forEach((t,n)=>{t.play()})}}class tr{constructor(e,t){this.stage=e,this.animationSystem=t}xyToCenter(e,t={x:0,y:0}){return this.lookAt(e,t)}lookAt(e,t={x:0,y:0}){let n=t.x||0,s=t.y||0;return this.animationSystem.anime({from:[e.x,e.y],to:[-n,-s],update:r=>{e.x=r[0],e.y=r[1]},effect:"easeInOutElastic"})}flash(e,t={}){let s=t[Ot(190)]||3,i=t.duration||100;return this.animationSystem.anime({from:[0],to:[1],update:a=>{e.css({globalAlpha:a[0]})},times:s,effect:"easeOutBounce",duration:i})}expandScale(e,t={}){const n=Ot;let s=t.position||"center",i=t[n(193)]||0,r=t.beginHeight||0,o=this.animationSystem,a=[i,r,e.x,e.y],c=[e[n(187)],e[n(221)],e.x,e.y];return s==n(198)?a=[i,r,e.x,e.y]:s=="lt"?a=[i,r,e.x-e.width*.5,e.y-e[n(221)]*.5]:s=="rt"?a=[i,r,e.x+e.width*.5,e.y-e.height*.5]:s=="lb"?a=[i,r,e.x-e.width*.5,e.y+e.height*.5]:s=="rb"?a=[i,r,e.x+e.width*.5,e.y+e[n(221)]*.5]:s=="ct"?a=[e.width,r,e.x,e.y-e.height*.5]:s=="cb"?a=[e.height,r,e.x,e.y+e.height*.5]:s=="lm"?a=[i,e.height,e.x-e.width*.5,e.y]:s=="rm"&&(a=[i,e.height,e.x+e.width*.5,e.y]),o[n(216)]({from:a,to:c,update:h=>{const u=n;e.scaleTo(h[0]/e[u(187)],h[1]/e[u(221)]),e.translateTo(h[2],h[3])},effect:"easeOutCubic"})}unexpandScale(e,t={}){const n=Ot;let s=t.position||"center",i=t.minWidth||0,r=t[n(202)]||0,o=this.animationSystem,a=[e[n(187)],e.height,e.x,e.y],c=[i,r,e.x,e.y];return s=="center"?c=[i,r,e.x,e.y]:s=="lt"?c=[i,r,e.x-e[n(187)]*.5,e.y-e[n(221)]*.5]:s=="rt"?c=[i,r,e.x+e[n(187)]*.5,e.y-e.height*.5]:s=="lb"?c=[i,r,e.x-e.width*.5,e.y+e.height*.5]:s=="rb"?c=[i,r,e.x+e[n(187)]*.5,e.y+e.height*.5]:s=="ct"?c=[i,r,e.x,e.y-e[n(221)]*.5]:s=="cb"?c=[i,r,e.x,e.y+e.height*.5]:s=="lm"?c=[i,r,e.x-e[n(187)]*.5,e.y]:s=="rm"&&(c=[i,r,e.x+e.width*.5,e.y]),o.anime({from:a,to:c,update:h=>{const u=n;e.scaleTo(h[0]/e[u(187)],h[1]/e[u(221)]),e.translateTo(h[2],h[3])},effect:"easeOutCubic"})}flow(e,t={}){const n=Ot;let s=t.lineDash||e.style[n(211)]||[3,2],i=t[n(213)]||"clockwise",r=t.duration||1e4,o=t.distance||300,a=i==n(203)?1:-1,c=this[n(204)];return e.css({lineDash:s}),c[n(216)]({from:0,to:o,update:h=>{e.css({lineDashOffset:-h*a})},times:1/0,duration:r})}rippling(e={}){const t=Ot;let n=e.radius||50,s=e.color||"rgba(128,128,128,0.8)",i=e[t(223)]||3,r=e[t(215)]||8,o=this[t(204)],a=[],c=new k0(null,0,0);c[t(217)]=![],c.setRadius(n);for(let y=0;y<i;y++){let m=new k0(null,0,0);m.mouseEnabled=![],m.css(t(199),s),m[t(197)](1),c.addChild(m)}let l=c[t(209)],h=n/i,u=o[t(216)]({from:[1],to:[n],update:y=>{let m=y[0];for(let I=0;I<l.length;I++){let b=l[I],k=m+I*h;k>n&&(k=k%n);let E=k/n;b.setRadius(k),b.css({lineWidth:E*r,globalAlpha:.1})}},times:1/0,duration:2e3});return a.push(u),new gs([c],a)}[Ot(200)](e,t){const n=Ot;let s=this.stage[n(196)].toImage(e);s.onload=()=>{s=Tt[n(200)](s,t),e.setImage(s,!![])}}imageFilter(e,t){e.getImage(n=>{if(n==null)return;let s=Tt.colorFilter(n,t);e.setImage(s)})}[Ot(205)](e,t={color:"red"}){const n=Ot;let s=t[n(226)]||"❌️",i=new $t(s,0,0);i.draggable=![],i.editable=![],i.connectable=![],i[n(210)]({textPosition:n(198),textBaseline:"middle",textAlign:"center",color:t[n(214)]||n(224)}),t.font!=null&&i.css({font:t.font});let r=L0.getMiddleOrigin(e);return i.setXYOnLink(r[n(192)],r.t),e.addChild(i),i}waterLike(e,t=60,n=60){const s=Ot;let i=new je(0,0,0,n),r=new M(null,0,0,t,n);e==null&&(e=[s(222),"green","rgb(0,87,55)"]),i.setColors([[0,e[0]],[.5,e[1]],[1,e[2]]]);let o=this.animationSystem.anime({from:0,to:1,duration:2500,update:c=>{const l=s;c=c<0?0:c,i.startX=Math.random()+Math.cos(c*2*Math.PI)*10,i.startY=-10+Math.cos(c*2*Math.PI)*5,i[l(208)]([[0,e[0]],[.2*c,e[1]],[1,e[2]]])}});return o[s(213)]="alternate",o.times=1/0,r.css({fillStyle:i}),new gs([r],[o])}}const T0=C0;(function(x,e){const t=C0,n=x();for(;[];)try{if(parseInt(t(440))/1*(-parseInt(t(439))/2)+-parseInt(t(441))/3*(-parseInt(t(438))/4)+-parseInt(t(431))/5+parseInt(t(436))/6+parseInt(t(428))/7*(-parseInt(t(433))/8)+-parseInt(t(442))/9+parseInt(t(427))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(bs,750466);function bs(){const x=["11703064NeYNSu","stoped","map","2115228ybGhVE","regBehaviour","16036rBqcvY","894902FpijTO","3zNmlHb","930XcvHjJ","2772657raHHvb","prototype","first","36788880dMTNbS","7coavta","isNaN","update","7052205rYFMtr","length"];return bs=function(){return x},bs()}function C0(x,e){const t=bs();return C0=function(n,s){return n=n-427,t[n]},C0(x,e)}class uc{constructor(e){const t=C0;this[t(435)]=new Map,this.argMap=new WeakMap,this.behaviourMap=new Map,this[t(434)]=![],this.stage=e}tick(e){const t=C0;if(this.stoped)return;let n=this.map.keys();for(let s of n)this.executeBehaviours(s,t(430))}defBehaviour(e,t){let n=new ys(e);return Object.assign(n,t),this.regBehaviour(n),n}[T0(437)](e){this.behaviourMap.set(e.name,e)}addBehaviour(e,t,n){const s=T0;let i=this.behaviourMap.get(t);if(i==null)throw new Error("behaviours not exist:"+t);let r=this.map.get(e);r==null?(r=[],this.map.set(e,r),r.push(i)):r.indexOf(i)==-1&&r.push(i),n!=null&&(r[t]=n),i[s(444)](e,n)}removeBehaviour(e,t){const n=T0;let s=this[n(435)].get(e);if(s!=null)for(let i=0;i<s[n(432)];i++){let r=s[i];if(r.name==t){R.remove(s,r);return}}}executeBehaviours(e,t){const n=T0;let s=this.map.get(e);if(s!=null)for(let i=0;i<s.length;i++){let r=s[i];if(t=="update"){let o=s[r.name];if(r.update!==ys[n(443)].update&&r.update(e,o)==![])return![]}}return null}}class ys{constructor(e){if(typeof e=="number"||!Number[T0(429)](parseInt(e)))throw new Error("behaviour name cannot be number:"+e);this.name=e}first(e,t){}[T0(430)](e,t){}static fromJSON(e){let t=new ys(e.name);return t.first=new Function("return "+t.first)(),t.update=new Function("return "+t.update)(),t}toJSON(){return{name:this.name,first:this.first.toString(),update:this.update.toString()}}}const ht=A0;(function(x,e){const t=A0,n=x();for(;[];)try{if(parseInt(t(273))/1+-parseInt(t(280))/2+-parseInt(t(290))/3+-parseInt(t(291))/4*(-parseInt(t(289))/5)+parseInt(t(272))/6*(-parseInt(t(277))/7)+parseInt(t(271))/8*(parseInt(t(279))/9)+parseInt(t(274))/10*(parseInt(t(286))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(_s,647220);function _s(){const x=["height","children","push","242KPtHjL","object","translateWithRecursive","1819140EXCdWu","3851532OBBOAS","8ziqXJs","parent","addChild","translateWith",": 	","setObject","rect","flatten","join","1616epJNBz","6FJwzqa","1114373cTacJQ","189160wfatzR","length","getRect","3849097yDAoRj","union","19746ofkzPb","440868RjrnFD","map","setTo"];return _s=function(){return x},_s()}function A0(x,e){const t=_s();return A0=function(n,s){return n=n-268,t[n]},A0(x,e)}class M0{constructor(e=0,t=0,n=1,s=1){const i=A0;this.x=0,this.y=0,this.width=1,this.height=1,this[i(268)]=new z(0,0,1,1),this.children=[],this.object=null,this.x=e,this.y=t,this.width=n,this[i(283)]=s}fromObject(e){const t=A0;this.object=e,this.x=e.x,this.y=e.y,this.width=e.width,this.height=e[t(283)]}[ht(296)](e){const t=ht;this[t(287)]=e}getRect(e=![]){const t=ht;return this.rect[t(282)](this.x,this.y,this.width,this.height),e?z.union(this.rect,this.getChildrenRect(!![])):this[t(268)]}getChildrenRect(e){const t=ht;let n=this.children,s=n[0].getRect(e);for(let i=1;i<n[t(275)];i++)s=z.union(s,n[i][t(276)](e));return s}[ht(294)](e,t){this.x+=e,this.y+=t}translateTo(e,t){this.x=e,this.y=t}[ht(293)](e){const t=ht;e[t(292)]=this,this.children.push(e)}[ht(288)](e,t){const n=ht;this[n(294)](e,t);let s=this.children;for(var i=0;i<s[n(275)];i++)s[i][n(288)](e,t)}[ht(269)](e){const t=ht;let n=[];for(var s=0;s<this.children[t(275)];s++){let i=this[t(284)][s];if((e==null||e(i)==!![])&&(n[t(285)](i),i.children&&i[t(284)].length>0)){let r=M0.flatten(i[t(284)],e);n=n.concat(r)}}return n}toString(){const e=ht;return this.object.text+e(295)+M0.flatten(this.children)[e(281)](n=>n.object.text)[e(270)](",")}static getVNodeUnionRect(e){const t=ht;let n=e[0][t(276)]();for(let s=1;s<e.length;s++)n=z[t(278)](n,e[s].getRect());return n}static flatten(e,t){const n=ht;let s=[];for(var i=0;i<e.length;i++){let r=e[i];if((t==null||t(r)==!![])&&(s[n(285)](r),r.children&&r.children.length>0)){let o=M0.flatten(r[n(284)],t);s=s.concat(o)}}return s}}function ms(){const x=["8105900BSfqZu","380984uiHsOh","allVirtualNodes","63ctyrrL","6994449eQrEAc","getRect","root","5BnzlBF","centerTo","children","885046FfwjRt","indexData","allObjects","3370803qDNbgH","forEach","length","10624416uCJlgh","index","getLeafs","4464804paQiyl","2HMqBOm"];return ms=function(){return x},ms()}const _e=D0;(function(x,e){const t=D0,n=x();for(;[];)try{if(-parseInt(t(200))/1+-parseInt(t(210))/2*(parseInt(t(203))/3)+parseInt(t(212))/4+-parseInt(t(218))/5*(parseInt(t(209))/6)+-parseInt(t(215))/7+-parseInt(t(206))/8+parseInt(t(214))/9*(parseInt(t(211))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ms,689336);function D0(x,e){const t=ms();return D0=function(n,s){return n=n-198,t[n]},D0(x,e)}class dc{constructor(e){const t=D0;this.deep=0,this.root=e,this.descendants=e.flatten(),this.allVirtualNodes=[e].concat(this.descendants),this[t(202)]=this[t(213)].map(n=>n.object),this[t(201)]=[],this.index()}[_e(207)](){const e=_e;let t=[];const n=this;function s(i,r){const o=D0;n.deep<r&&(n.deep=r);let a=i[o(199)],c=t[r];c==null&&(c=[],t[r]=c),c.push(i);for(var l=0;l<a[o(205)];l++)s(a[l],r+1)}s(this.root,0),this[e(201)]=t}getRect(){const e=_e,t=this[e(217)].flatten();if(t[e(205)]==0)throw new Error("getRect() in empty tree");let n=t[0].getRect();for(let s=1;s<t.length;s++){const i=t[s];n=z.union(n,i[e(216)]())}return n}[_e(198)](e,t){const n=_e,s=this.allVirtualNodes;let i=this.root[n(216)]();s[n(204)](c=>{const l=n;i=z.union(i,c[l(216)]())});let r=i.getCenter(),o=e-r.x,a=t-r.y;return s.forEach(c=>{c.translateWith(o,a)}),this}translateTo(e,t){const n=_e,s=this.allVirtualNodes;let i=this.root.getRect();s[n(204)](a=>{const c=n;i=z.union(i,a[c(216)]())});let r=e-i.x,o=t-i.y;return s[n(204)](a=>{a.translateWith(r,o)}),this}translateWith(e,t){return this[_e(213)].forEach(i=>{i.translateWith(e,t)}),this}[_e(208)](){return this.indexData[this.deep]}}const K=B0;function B0(x,e){const t=Is();return B0=function(n,s){return n=n-341,t[n]},B0(x,e)}(function(x,e){const t=B0,n=x();for(;[];)try{if(-parseInt(t(349))/1*(parseInt(t(360))/2)+parseInt(t(362))/3*(-parseInt(t(341))/4)+parseInt(t(363))/5+-parseInt(t(344))/6*(-parseInt(t(347))/7)+-parseInt(t(348))/8*(parseInt(t(345))/9)+-parseInt(t(355))/10+parseInt(t(343))/11*(parseInt(t(352))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Is,903120);class Qe{constructor(){this.inputs=[],this.outputs=[]}getDegree(){return this.getInDegree()+this.getOutDegree()}getInDegree(){return this.inputs.length}getOutDegree(){return this[B0(346)].length}[K(353)](){const e=K;let t=this.outputs[e(365)](s=>s.to),n=this.inputs.map(s=>s.from);return t.concat(n)}}function Is(){const x=["isZero","map","push","depth","length","filter","170268CfeHoW","inputs","275JcxVJL","54oskuBI","351aRdKTK","outputs","642558TRXgmX","72672iayuMh","16yXqGpP","getDegree","vertexes","1654284tfOFkh","getAdjacentList","forEach","12686010JtKteb","hasDirection","from","has","edges","134986OtGbtk","isBridge","117bPAGXG","4967135IfDPEp"];return Is=function(){return x},Is()}class ws{constructor(e,t){const n=K;this.weight=0,this.from=e,this.to=t,e.outputs[n(366)](this),t.inputs.push(this)}isLoop(){return this.from===this.to}isAdjacent(e){const t=K;return this.from===e[t(357)]||this[t(357)]===e.to||this.to===e[t(357)]||this.to===e.to}}class Gr{constructor(e){const t=K;this[t(351)]=e}isClose(e){return![]}isTrace(e){return![]}isCircuit(e){return this.isClose(e)&&this.isTrace(e)}isTrack(e){return![]}isCycle(e){return this.isClose(e)&&this.isTrack(e)}}class j0{constructor(e,t){const n=K;this[n(356)]=!![],this.vertexes=e,this[n(359)]=t}[K(364)](){return this[K(351)].length>0&&this.edges.length==0}isAlone(){const e=K;return this[e(351)].length==1&&this.edges[e(368)]==0}traverse(e,t=K(367),n=[],s=new Set){const i=K;e==null&&(e=this.vertexes[0]);let r=this;if(!s[i(358)](e)&&(n.push(e),s.add(e)),t=="depth")e.getAdjacentList().filter(a=>!s.has(a)).map(a=>{!s.has(a)&&(n.push(a),s.add(a)),r.traverse(a,t,n,s)});else{let o=e.getAdjacentList().filter(a=>!s.has(a));o[i(365)](a=>{n.push(a),s.add(a)}),o[i(365)](a=>{r.traverse(a,t,n,s)})}return n}getMaxDegreeVertext(){const e=K;let t=this.vertexes[0];for(let n=1;n<this[e(351)].length;n++){const s=this.vertexes[n];s.getDegree()>t.getDegree()&&(t=s)}return t}getMinDegree(){const e=K;let t=this.vertexes[0].getDegree();for(let n=1;n<this[e(351)].length;n++){const s=this.vertexes[n];s.getDegree()<t&&(t=s.getDegree())}return t}getPathList(e,t,n=new Set){return[]}getOrder(){const e=K;return this.vertexes[e(368)]}[K(361)](){}isSubGraph(e){}isTree(){const e=K;return this[e(351)].length!=this[e(359)].length+1?![]:this.vertexes[e(368)]==1?!![]:this.vertexes.filter(n=>n[e(350)]()==1)[e(368)]>0}travelNext(e){let t=[];function n(s){const i=B0;t.push(s);let r=s[i(346)].map(o=>o.to);for(let o=0;o<r.length;o++){let a=r[o];if(a===e)return;n(a)}}return n(e),t}clone(){const e=K;let t=this[e(351)].slice();t.forEach(i=>{const r=e;i.inputs=i[r(342)].slice(),i[r(346)]=i.outputs.slice()});let n=this.edges.slice(),s=new j0(t,n);return s.hasDirection=this.hasDirection,s}check(){const e=K;let t=0;this.vertexes[e(354)](s=>t+=s.getDegree()),console.assert(t==this.edges.length*2);let n=this.vertexes[e(369)](s=>s[e(350)]()%2!=0)[e(368)];console.assert(n%2==0)}}const me=z0;(function(x,e){const t=z0,n=x();for(;[];)try{if(-parseInt(t(184))/1*(parseInt(t(195))/2)+-parseInt(t(191))/3+-parseInt(t(179))/4+parseInt(t(172))/5*(parseInt(t(197))/6)+parseInt(t(187))/7+parseInt(t(173))/8*(-parseInt(t(177))/9)+-parseInt(t(185))/10*(-parseInt(t(199))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(vs,977323);function vs(){const x=["set","5058176wZnoqw","isNodeTarget","concat","edges","target","11IBBFDF","790MmZzRF","travelVertext","5821389evFUDm","begin","length","object","3137649oIsprk","isTree","toGraphs","createMinimalSpanningTree","162366YAamOy","filter","6dQkdIN","push","290818DbeAuI","get","forEach","fromObject","8578315zTmEfj","3641032dFWEOZ","map","has","getInDegree","9KFGGcY"];return vs=function(){return x},vs()}function z0(x,e){const t=vs();return z0=function(n,s){return n=n-172,t[n]},z0(x,e)}class $e{static[me(194)](e){const t=me;let n=[],s=[],i=e.edges.sort((a,c)=>{const l=z0;let h=a.weight-c.weight;return h==0&&(h=a.from.getInDegree()-c.from[l(176)](),h==0&&(h=c.to.getOutDegree()-a.to.getOutDegree())),h}),r=new WeakMap;for(let a=0;a<i.length;a++){const c=i[a];let l=c.from,h=c.to,u=r.get(l),g=r[t(200)](h);if(u!=null&&g!=null)continue;u==null&&(u=new Qe,u.object=l.object,n.push(u),r.set(l,u)),g==null&&(g=new Qe,g.object=h.object,n.push(g),r.set(h,g));let y=new ws(u,g);y.object=c[t(190)],s.push(y)}return new j0(n,s)}getNodes(e){return e.vertexes.map(t=>t.object)}getLinks(e){const t=me;return e[t(182)][t(174)](n=>n.object)}objectsToGraphs(e){const t=me,n=e[t(196)](c=>c instanceof M);let s=e[t(196)](c=>c instanceof W);s.filter(c=>c[t(188)][t(180)]()&&c.end.isNodeTarget());const i=new WeakMap,r=n.map(c=>{const l=t,h=new Qe;return h.object=c,i[l(178)](c,h),h});s.filter(c=>{const l=t;return i.get(c.begin.target)&&i[l(200)](c.end[l(183)])});const o=s.map(c=>{const l=t;let h=i.get(c[l(188)].target),u=i.get(c.end.target),g=new ws(h,u);return g.object=c,g});return this.toGraphs(r,o)}[me(193)](e,t){const n=me;let s=[];e.forEach(o=>{const a=z0;let c=o.inputs,l=o.outputs;s=s[a(181)](c),s=s.concat(l)});let i=[],r=new Set;for(let o=0;o<e[n(189)];o++){const a=e[o];if(r.has(a))continue;let c=[],l=[];this.travelVertext(a,c,l,r);let h=new j0(c,l);i.push(h)}return i}travelVertext(e,t=[],n=[],s=new Set){const i=me;if(s.has(e))return;t.push(e),s.add(e);let r=e.inputs.filter(c=>!s[i(175)](c)),o=e.outputs[i(196)](c=>!s.has(c));r[i(201)](c=>{n.push(c),s.add(c)}),o[i(201)](c=>{n[i(198)](c),s.add(c)});let a=this;r[i(201)](c=>{a.travelVertext(c.from,t,n,s)}),o[i(201)](c=>{a[i(186)](c.to,t,n,s)})}toTree(e){const t=me;!e[t(192)]()&&(e=$e.createMinimalSpanningTree(e));let n=e.vertexes.filter(c=>c.getInDegree()==0)[0][t(190)],s=e.vertexes.map(c=>c.object),i=e[t(182)].map(c=>c.object);const r=new WeakMap;let o=new M0;return o.fromObject(n),r.set(n,o),s.forEach(c=>{const l=t;if(c===n)return;const h=new M0;h[l(202)](c),r.set(c,h)}),i.forEach(c=>{const l=t;let h=r.get(c.begin.target),u=r.get(c.end[l(183)]);h.addChild(u)}),new dc(o)}}const ks=N0;function Ss(){const x=["265433UlWBVC","width","play","2579759BPrgXS","786237YvxVJd","rotate","scale","10684487ailNEw","positions","scaleY","6RXHOaX","293520BKIKIk","height","4XaftDD","4oAMYqk","rotation","3706310jTGFZv","1580755bmonru","9pEDpEt","updateXY"];return Ss=function(){return x},Ss()}(function(x,e){const t=N0,n=x();for(;[];)try{if(-parseInt(t(267))/1*(parseInt(t(261))/2)+parseInt(t(271))/3*(parseInt(t(260))/4)+-parseInt(t(264))/5+-parseInt(t(257))/6*(parseInt(t(270))/7)+-parseInt(t(258))/8+-parseInt(t(265))/9*(-parseInt(t(263))/10)+parseInt(t(274))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ss,351783);function N0(x,e){const t=Ss();return N0=function(n,s){return n=n-255,t[n]},N0(x,e)}class bn{constructor(e,t){const n=N0;this.x=0,this.y=0,this.scaleX=1,this[n(256)]=1,this[n(268)]=1,this[n(259)]=1,this.rotation=0,this.objects=e,this[n(255)]=t,this.positionNormals=R.getPointsNormalization(t);let s=R.getPointsRect(t);this.width=s[n(268)],this[n(259)]=s.height}resizeTo(e,t){this.width=e,this.height=t}resize(e,t){const n=N0;this[n(268)]=e,this.height=t}translate(e,t){this.x=e,this.y=t}[ks(273)](e,t){this.scaleX=e,this.scaleY=t}[ks(272)](e){const t=ks;this[t(262)]=e}updateXY(e){const t=this.objects;let n=Math.min(e.length,t.length);for(let s=0;s<n;s++){let i=e[s];t[s].setXY(i.x,i.y)}}doLayout(e){const t=ks;let n=this,s=this.objects,i=this.positionNormals;this[t(262)]!=0&&(i=R.rotateNormaledPoints(this.positionNormals,this[t(262)]));let r=o=>{const a=t;return{x:n.x+n.width*o.x*n.scaleX,y:n.y+n.height*o.y*n[a(256)]}};if(i=i.map(r),e!=null){let o=function(l){return n.updateXY(l)},a=s.map(l=>({x:l.x,y:l.y})),c=Object.assign({from:a,to:i,update:o},e);this.animationSystem.anime(c)[t(269)]()}else this[t(266)](i);return this}}function Ps(){const x=["translateTo","36610SdYNvq","352096JnHWpD","indexData","allVirtualNodes","358591yddVbd","2119860mNyWKx","1037328iclrmV","length","70QMBYxF","translateWithRecursive","33SSkuXI","540ytSRCo","3SksTey","5608116bZCyjM","children","50TuBUxP","900076khsiYH"];return Ps=function(){return x},Ps()}function Os(x,e){const t=Ps();return Os=function(n,s){return n=n-340,t[n]},Os(x,e)}(function(x,e){const t=Os,n=x();for(;[];)try{if(parseInt(t(348))/1+parseInt(t(342))/2*(-parseInt(t(356))/3)+-parseInt(t(355))/4*(-parseInt(t(344))/5)+parseInt(t(357))/6+-parseInt(t(352))/7*(-parseInt(t(345))/8)+parseInt(t(349))/9*(-parseInt(t(341))/10)+parseInt(t(354))/11*(-parseInt(t(350))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ps,834797);function fc(x){const e=Os;let t=x[e(346)],n=x.deep,s=x.getLeafs(),i=60,r=80;for(let c=0;c<s.length;c++){let l=s[c],h=(c+1)*i,u=n*r;l[e(343)](h,u)}for(let c=n-1;c>=0;c--){let l=t[c];for(let h=0;h<l.length;h++){let u=l[h],g=u[e(340)],y=u.x,m=c*r;if(g[e(351)]>0?y=(g[0].x+g[g.length-1].x)/2:h>0&&(y=l[h-1].x+l[h-1].width),u.translateTo(y,m),h>0&&u.x<l[h-1].x+l[h-1].width){let I=l[h-1].x+l[h-1].width,b=Math.abs(I-u.x);for(let k=h;k<l.length;k++)l[k][e(353)](b,0)}}}let o=[];return x[e(347)].forEach(c=>{o.push(c)}),o}const Es=Ie;(function(x,e){const t=Ie,n=x();for(;[];)try{if(-parseInt(t(378))/1+-parseInt(t(395))/2+-parseInt(t(375))/3+parseInt(t(384))/4+-parseInt(t(391))/5+-parseInt(t(390))/6*(parseInt(t(379))/7)+parseInt(t(376))/8===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ls,515996);function Ie(x,e){const t=Ls();return Ie=function(n,s){return n=n-373,t[n]},Ie(x,e)}function Ls(){const x=["1751524lXFTKK","update","radiusScale","add","length","getAdjacentList","2058732LyJulK","430005AOcfCz","graphSystem","endAngle","map","1087110rtOOSt","push","traverse","travelTree","848583NdLNpM","11933528cXiODW","animationSystem","158037uwEGgO","7VdGarI","sizeFitToChildren","points","toTree","isTree"];return Ls=function(){return x},Ls()}class er{constructor(e,t){const n=Ie;this[n(377)]=e,this[n(392)]=t}shapeLayout(e,t){const n=Ie;let s=t;t instanceof A&&(s=t[n(381)]);let i=new bn(e,s);return i.animationSystem=this[n(377)],i}circleLayout(e,t={}){const n=Ie;!e.isTree()&&(e=$e.createMinimalSpanningTree(e));let s=e[n(373)](null).filter(c=>c instanceof Qe),i=s[0],r=this.getCircleLayoutPositions(i,t),o=s[n(394)](c=>c.object),a=new bn(o,r);return a.animationSystem=this.animationSystem,a}treeLayout(e){const t=Ie;!e.isTree()&&(e=$e.createMinimalSpanningTree(e));let n=e.traverse(null).filter(a=>a instanceof Qe),s=this[t(392)][t(382)](e),i=fc(s),r=n.map(a=>a.object),o=new bn(r,i);return o.animationSystem=this[t(377)],o}[Es(374)](e,t){if(!e[Es(383)]())throw new Error("graph is not a tree");return t==null&&(t=e.vertexes[0]),e.travelNext(t)}getCircleLayoutPositions(e,t={}){const n=Es;t.cx=t.cx||0,t.cy=t.cy||0,t.radius=t.radius||200,t.startAngle=t.startAngle||0,t.endAngle=t.endAngle||2*Math.PI,t.radiusScale=t[n(386)]||.5,t.angleScale=t.angleScale||1,t.endAngle>2*Math.PI&&(t[n(393)]=t[n(393)]%(2*Math.PI));let s=[],i=new Set,r=[];function o(a,c,l,h,u,g,y){const m=n;if(i.has(a))return;r[m(396)](a.object.id),i[m(387)](a),s[m(396)]({x:c,y:l});let I=a[m(389)](),b=I[m(388)];if(y>1&&(b-=1),b<=0)return;let k=u,E=g-u,D=E;E<2*Math.PI&&(b-=1),b!=0&&(D/=b),y>1&&(E<2*Math.PI?k-=E*.5:E==2*Math.PI&&b>1&&b%2==0&&(k+=D*.5));for(let j=0;j<I.length;j++){let Mt=I[j],Dt=k+j*D,q0=c+h*Math.cos(Dt),Le=l+h*Math.sin(Dt);t[m(385)]&&t.update(t,e.object,y),o(Mt,q0,Le,h*t.radiusScale,Dt,Dt+E,y+1)}}return t[n(385)]&&t[n(385)](t,e.object,0),o(e,t.cx,t.cy,t.radius,t.startAngle,t.endAngle,1),s}[Es(380)](e,t){return Yt.sizeFitToChildren(e,t)}}const t0=yn;(function(x,e){const t=yn,n=x();for(;[];)try{if(parseInt(t(246))/1*(parseInt(t(236))/2)+parseInt(t(245))/3*(-parseInt(t(235))/4)+-parseInt(t(238))/5+-parseInt(t(253))/6+parseInt(t(249))/7+-parseInt(t(255))/8*(parseInt(t(254))/9)+-parseInt(t(237))/10*(-parseInt(t(257))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ts,473174);function Ts(){const x=["stage","2091075NrxZXn","inputSystem","getObject","getCurrentLayer","522630fvEtJV","2440953bBLQnE","24ekUlpJ","hideList","218581MVVFhp","24356MAJXaa","2PgHhGa","960WUxTHM","4610605oFqnGH","flatten","isNode","mouseEnabledBak","object","getMouseXY","painted","129vrSSao","351537wESMJB","enter"];return Ts=function(){return x},Ts()}function yn(x,e){const t=Ts();return yn=function(n,s){return n=n-235,t[n]},yn(x,e)}class pc{constructor(e){const t=yn;this.mode=t(244),this.stage=e}[t0(247)](e){const t=t0;if(this.quit(),e==null||e===this.object||!e[t(240)]){this[t(242)]=null;return}this[t(242)]=e;let n=this.stage[t(252)]();this[t(256)]=L[t(239)](n.children,s=>s!==e),this[t(241)]=e.mouseEnabled,e.mouseEnabled=![],e.isSelected=![],this.hideList.forEach(function(s,i){s._cameraVisible=![]})}quit(){const e=t0;this[e(256)]!=null&&(this.mouseEnabledBak!=null&&(this.object.mouseEnabled=this[e(241)]),this.hideList.forEach(function(t,n){t._cameraVisible=!![]})),this.hideList=null,this.stage.update()}[t0(251)](){return this[t0(242)]==null?this.stage.getCurrentLayer():this.object}[t0(243)](){const e=t0;return this.getObject().screenToLocalXY(this.stage[e(250)].x,this[e(248)].inputSystem.y)}}function Cs(x,e){var t=As();return Cs=function(n,s){n=n-406;var i=t[n];return i},Cs(x,e)}(function(x,e){for(var t=Cs,n=x();[];)try{var s=-parseInt(t(406))/1+parseInt(t(414))/2*(parseInt(t(419))/3)+parseInt(t(411))/4*(-parseInt(t(408))/5)+parseInt(t(415))/6*(-parseInt(t(418))/7)+parseInt(t(407))/8*(-parseInt(t(417))/9)+-parseInt(t(413))/10*(-parseInt(t(412))/11)+-parseInt(t(409))/12*(-parseInt(t(416))/13);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(As,182241);function As(){var x=["470OgOTIU","36FMaZJy","1729eZnRCD","1472103TCNPdn","288253VMSKCm","447BNSzqi","139753gJNAHC","8bMtTpd","15ZzhKzt","47532wYMpeh","type","141028kIkkbM","1521179qseXPE","20NZqZWL"];return As=function(){return x},As()}class gc{constructor(e){var t=Cs;this[t(410)]=e}}const Jt=we;(function(x,e){const t=we,n=x();for(;[];)try{if(parseInt(t(265))/1+-parseInt(t(245))/2*(parseInt(t(244))/3)+-parseInt(t(241))/4*(-parseInt(t(266))/5)+-parseInt(t(258))/6*(-parseInt(t(247))/7)+parseInt(t(243))/8*(parseInt(t(259))/9)+-parseInt(t(242))/10+-parseInt(t(269))/11*(parseInt(t(257))/12)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ms,336997);function Ms(){const x=["2285087kJFszi","isDragStart","wheelZoom","defaultPrevented","cancelZoom","dispatchEvent","scaleY","translateTo","forEach","zoom","804wPWEsO","6reinjK","9BYFkKA","width","zoomIn","getChildren","scale","dirty","410310kJSDaq","18695LpgyJC","stage","type","20603shXKHR","zoomOut","scaleTo","number","grabbing","4ozjrGo","1987410hbaigM","778040sOuIZl","264774MtHcCy","4XDZrOX","transform"];return Ms=function(){return x},Ms()}function we(x,e){const t=Ms();return we=function(n,s){return n=n-238,t[n]},we(x,e)}let Vt=new gc("zoom");class bc extends wt{constructor(e){super(),this.x=0,this.y=0,this.scale=1,this.dirty=!![],this.transform=new r0,this.zoomMaxLimit=10,this.zoomMinLimit=.1,this.stage=e}lookAt(e,t){const n=we;typeof e!=n(239)?(this.x=e.x,this.y=e.y):(this.x=e,this.y=t);const s=this[n(267)],i=this;s.getChildren().forEach(function(r){const o=n;r.translate(-i.x*r.scaleX,-i.y*r[o(253)])}),s.update(),Vt.type="lookAt",this.dispatchEvent(Vt),this.dirty=!![]}zoomOut(e=.8){const t=we;return this.zoomBy(e),Vt[t(268)]=t(270),this[t(252)](Vt),this}zoomIn(e=1.25){const t=we;return this.zoomBy(e),Vt[t(268)]=t(261),this.dispatchEvent(Vt),this}zoomBy(e,t,n){const s=we;let i=this[s(263)]*e;if(i>this.zoomMaxLimit||i<this.zoomMinLimit)return;this.scale=i;const r=this.stage;if(this.hasListener(it.zoom)){let a=new Event(it.zoom,{cancelable:!![]});if(r[s(252)](a),a.defaultPrevented==!![])return}const o=this;if(r.getChildren()[s(255)](function(a){const c=s;a.mouseEnabled&&a[c(249)]&&o.zoomLayer(a,e,t,n)}),this.hasListener(it.zoomAfter)){let a=new Event(it.zoomAfter,{cancelable:!![]});if(r.dispatchEvent(a),a[s(250)]==!![])return}r.update(),this.dirty=!![],Vt.type="zoom",this.dispatchEvent(Vt)}[Jt(256)](e){const t=Jt;this[t(267)].getChildren().forEach(function(s){s[t(238)](1,1),s.translateTo(0,0)}),this.scale=1,this.zoomBy(e,this.x,this.y)}[Jt(251)](){const e=Jt,t=this.stage;t[e(262)]().forEach(function(n){const s=e;n.scaleTo(1,1),n[s(254)](0,0)}),this[e(263)]=1,this[e(264)]=!![],t.update(),Vt.type="cancelZoom",this.dispatchEvent(Vt)}zoomLayer(e,t,n,s){const i=Jt;if(this.hasListener(i(256))){let l=new Event("zoom",{cancelable:!![]});if(l.zoom={x:t,y:t,cx:n,cy:s},this.dispatchEvent(l),l.defaultPrevented==!![])return}if(n!=null&&s!=null){e.updateMatrix();let l=e.toScreenXY(n,s);n=l.x,s=l.y}e.scaleBy(t,t);let r=e[i(260)]*e.scaleX,o=e.height*e.scaleY,a=r*t-r,c=o*t-o;if(n!=null&&s!=null){let l=e.stage,h={x:l.width*.5,y:l.height*.5},u=e.toScreenXY(h.x,h.y),g=(n-u.x)/2,y=(s-u.y)/2;t>=1?(a+=g,c+=y):(a-=g*t,c-=y*t)}e.translateWith(-(a/2),-(c/2))}translateWith(e,t){this.x+=e,this.y+=t,this.dirty=!![]}_dragWith(e,t){const n=Jt;this.translateWith(-e,-t);const s=this.stage;s.inputSystem[n(248)]&&s.setCursor(n(240));const i=s[n(262)]().filter(r=>r.mouseEnabled&&r.draggable);for(let r=0;r<i.length;r++)i[r].translateWith(e,t)}targetOnly(e){this[Jt(267)].localView.enter(e)}getTransform(){const e=Jt;return this.transform.translate(this.x,this.y),this[e(246)][e(263)](this[e(263)],this.scale),this[e(246)]}isDirty(){return this[Jt(264)]}clearDirty(){const e=Jt;this[e(264)]=![]}}const P=ve;(function(x,e){const t=ve,n=x();for(;[];)try{if(parseInt(t(389))/1+parseInt(t(461))/2*(-parseInt(t(467))/3)+-parseInt(t(353))/4*(-parseInt(t(372))/5)+-parseInt(t(388))/6*(parseInt(t(421))/7)+parseInt(t(394))/8*(parseInt(t(411))/9)+parseInt(t(385))/10+-parseInt(t(367))/11===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ds,453536);function ve(x,e){const t=Ds();return ve=function(n,s){return n=n-352,t[n]},ve(x,e)}function Ds(){const x=["getHeigth","updateZIndex","zoomIn","domElement","animationSystem","editor","getAABB","isDragStart","1912750SAivBu","config","position","6dQYjZj","227232LfociT","resourceSystem","dblclickHandler","length","filter","6013320CFKUqi","resize","rand","dropAllowed","forceUpdate","div","handlerLayer","dragHandler","sort","dispatchEvent","mouseupHandler","canvas","px)","hasListener","touchendHandler","_dragWith","getChildren","9kRLpyg","dropHandler","visible","removeChild","render","dragoverHandler","grab","touchstartHandler","draggable","setCursor","5168373BIIIZl","width","isMouseOn","ctrlKey","keyboard","map","metaKey","previous","flatten","calc(100% - ","mouseup","selectedGroup","toolbar","isNode","resizeTo","pickUpViewLayers","mouseenterHandler","overview","saveAsLocalImage","_resizeObserver","aabb","height","start","Debug","defaultPrevented","_onMound","edit","toDataURL","mode","download","context","stage","camera","forEach","layersContainer","getExportAABB","update","innerHTML","style","zIndex","9406OjFpKY","contentRect","unionRects","toStageRect","fullWindow","name","489lXjZoh","fullScreen","pause","select","mouseoutHandler","translateToCenter","data","drag","appendChild","inputSystem","8KQkmfw","clickHandler","_obb","zoomOut","isDraging","updateSize","children","show","now","getCursor","dragoutHandler","offsetWidth","getToolbarHeight","getImageData","628232TOmLfB","layer_container","distanceRatio","createElement","mousedragHandler","2113510HfCycW","css","modeChange","push","_init"];return Ds=function(){return x},Ds()}let yc=Date[P(361)]();class Bs extends wt{constructor(e,t){const n=P;super(),this.version=Sn,this.children=[],this.visible=![],this[n(449)]="normal",this.destoryed=![],this.config={dropAllowed:!![]},this[n(444)]=Qt,t!=null&&Object.assign(this.config,t),this.camera=new bc(this),this.localView=new pc(this),this.localView[n(449)]=null,this[n(425)]=new Ji(this),this.inputSystem=new Xx,this.selectedGroup=new Vi,this.resourceSystem=$,this._init(e),this.injectCss(),this.styleSystem=new Ki(this),this.behaviourSystem=new uc(this),this.animationSystem=new Zi,this.effectSystem=new tr(this,this[n(381)]),this.graphSystem=new $e,this.layoutSystem=new er(this.animationSystem,this.graphSystem),this.renderSystem=new Za(this),this.exportSystem=new Ka(this),this.serializerSystem=new Me,this[n(390)].on("loaded",s=>{this.update()}),this[n(352)]._initEvent(this)}injectCss(){const e=P;let t=document.head||document.getElementsByTagName("head")[0],n=document.getElementById("_jtopo_style_");n==null&&(n=document[e(370)](e(459)),n.id="_jtopo_style_",n.textContent=ma,t.appendChild(n))}[P(376)](e){const t=P;let n=this;document.oncontextmenu=function(){const i=ve;return!n.inputSystem[i(423)]},n.domElement=Ic(e);const s=document.createElement(t(399));n.layersContainer=s,n.debugPanel=new ya(n),Qt.isDebug&&n.debugPanel[t(360)](),n.setToolbar(new Yi(n)),s.classList.add(t(368)),s.style.position="relative",s.style.width="100%",n.domElement[t(475)](s),n[t(422)]=s[t(364)],n.height=s.clientHeight;{const i=new fn(n);n.handlerLayer=i;const r=i[t(415)].canvas;r.style.zIndex=""+i.zIndex,this[t(455)].appendChild(r)}_c(n),n.on("dragover",function(i){i.preventDefault()})}showDebugPanel(){this.debugPanel.show()}hideDebugPanel(){this.debugPanel.hide()}showOverview(e){const t=P;let n=this;n.overview==null&&(n[t(438)]=new _a(n)),n.overview[t(373)](e),n.overview[t(360)]()}_updateOverview(){const e=P;this.overview!=null&&this[e(438)][e(457)]()}hideOverview(){this.overview!=null&&this.overview.hide()}getToolbarHeight(){const e=P;return this.toolbar==null?0:this[e(433)][e(377)]()}[P(356)](e=.8){const t=P;return this[t(453)][t(356)](e),this}[P(379)](e=1.25){return this.camera.zoomIn(e),this}zoom(e){this.camera.zoomBy(e)}cancelZoom(){this.camera.cancelZoom()}zoomFullStage(){const e=P,t=this;let n=t[e(383)](),s=t.width/n[e(422)],i=t[e(442)]/n[e(442)],r=Math.min(s,i),o=0,a=0;this.camera.zoomBy(r,o,a),t.translateToCenter()}[P(472)](){if(!this.visible){console.warn("stage.translateToCenter() should after stage.show()");return}this.getChildren().forEach(function(e){e.centerBy()})}createLayer(e){let t=new ot(e);return this.addChild(t),t}addChild(e){const t=P;e[t(446)](this),e[t(460)]=this.children.length,e[t(435)](this.width,this.height),e[t(466)]==null&&(e[t(466)]="Layer_"+e.zIndex),this.children[t(375)](e);const n=e.render.canvas;n.style.zIndex=""+e.zIndex,this.layersContainer[t(475)](n),this.updateZIndex()}[P(378)](){const e=P;this.children[e(402)](function(t,n){return t.zIndex-n.zIndex}),this.children[e(454)](t=>{const n=e,s=t[n(415)][n(405)];s[n(459)].zIndex=""+t.zIndex})}[P(410)](){return this.children}[P(414)](e){const t=P;e.render[t(405)].remove();let n=this.children.indexOf(e);return n==-1?this:(R.removeAt(this.children,n),e[t(452)]=null,this)}show(){const e=P;this.visible=!![],this[e(410)]().forEach(function(t){t.show()}),this.renderSystem[e(443)](),this.styleSystem.start()}hide(){const e=P;this[e(413)]=![],this.renderSystem[e(469)](),this.getChildren().forEach(function(t){t.hide()})}update(){const e=P;this[e(410)]().forEach(function(t){t.update()}),this[e(400)][e(457)]()}forceUpdate(){this.handlerLayer.forceUpdate(),this.getChildren().forEach(function(e){e[ve(398)]()})}toDataURL(){const e=P;return this.exportSystem[e(448)](this[e(359)])}saveImageInfo(){return this.exportSystem.saveImageInfo(this.children)}[P(439)](){const e=P;this.exportSystem[e(439)](this.children)}on(e,t){return this.on.index=yc,this.addEventListener(e,t)}[P(465)](){const e=P;if(this.hasListener(it.fullWindow)){let t=new Event(it[e(465)],{cancelable:!![]});if(this.dispatchEvent(t),t[e(445)]==!![])return}fe.fullWindow(this.domElement)}[P(468)](){const e=P;fe.fullScreen(this[e(380)])}showToolbar(){const e=P;if(this.toolbar==null)return;this[e(433)][e(360)]();let t="calc(100% - "+this[e(365)]()+e(406);this.layersContainer[e(459)].height=t}hideToolbar(){const e=P;if(this.toolbar==null)return;this.toolbar.hide();let t=e(430)+this.getToolbarHeight()+e(406);this.layersContainer.style.height=t}setToolbar(e){this[P(433)]!=null&&this.toolbar.remove(),this.domElement.appendChild(e.getDom());let n="calc(100% - "+this.getToolbarHeight()+"px)";this.layersContainer.style.height=n,this.toolbar=e}setMode(e){const t=P;if(this[t(407)](it.modeChange)){let n=new Event(it[t(374)],{cancelable:!![]});if(n.mode=e,this.dispatchEvent(n),n.defaultPrevented==!![])return}mc(this,e),e==mt.drag?this[t(420)](t(417)):this.setCursor("default")}dropHandler(e){}dragoverHandler(){}mouseoverHandler(){}mousedownHandler(e){const t=P,n=this.inputSystem;if(this.mode==mt.drag){this[t(420)]("grabbing");return}let s=this,i=s.pickUpViewLayers();s[t(352)].target=i;let r=s[t(449)]==t(447)&&i!=null&&i.mouseEnabled==![];if(i!=null&&!r){n.pickObject(i);return}!(e[t(424)]||e[t(427)])&&s.selectedGroup.removeAll()}static findDropToObjec(e,t){const n=P;let s=e.getAncestors(),i=L[n(429)](e.children);for(let r=t.length-1;r>=0;r--){let o=t[r];if(!(e===o||o===e.parent||!o.dropAllowed)&&!(s.indexOf(o)!=-1||i.indexOf(o)!=-1)&&o._obb[n(441)].containsRect(e[n(355)].aabb))return o}return null}mousedragHandler(e){const t=P,n=this.inputSystem,s=this.handlerLayer,i=this.mode,r=this.inputSystem.target,o=this.selectedGroup,a=this.pickUpViewLayers();n.mouseoverTarget=a;const c=e.buttons==1,l=c&&r!=null&&r.mouseEnabled&&r[t(419)],h=this[t(410)]().filter(g=>g.mouseEnabled);if(c&&this.config[t(397)]&&l&&r[t(419)]){let g=null;for(let y=0;y<h.length;y++){const m=h[y],I=m.flattenList[t(393)](b=>b.painted&&b[t(434)]);g=Bs.findDropToObjec(r,I),g!==n.dropTarget&&n.dropTarget&&n.dropTarget[t(363)](n),g!=null&&g[t(416)](this.inputSystem)}n.dropTarget=g}if(l){o[t(401)](n);return}if(c&&(i==mt[t(470)]||i==mt.edit)){qr(s);return}this[t(453)]._dragWith(n.dx,n.dy)}mouseupHandler(e){const t=P,n=this.inputSystem;let s=this;const i=this[t(410)](),r=n.isRightButton();for(let c=0;c<i[t(392)];c++){const l=i[c];l.draggable&&n.isDraging&&l.dragEndHandler()}if(this[t(449)]==mt[t(474)]){this.setCursor("grab");return}this.setCursor("default");let o=this.inputSystem.target,a=this.handlerLayer;a.areaBox.hide(),a.areaBox.resizeTo(0,0),o&&o.mouseEnabled&&(n.type==t(431)?o[t(404)](n):n.type=="touchend"&&o[t(408)](n),n[t(428)].isDraging&&o.draggable&&r==![]&&s[t(432)].dragEndHandler(n)),this[t(386)].dropAllowed&&n.dropTarget&&(n.dropTarget[t(412)](n),n.dropTarget=null)}mouseoutHandler(e){const t=P,n=this[t(352)];this.handlerLayer.mouseoutHandler(n),this.selectedGroup[t(471)](n)}[P(354)](e){const t=P,n=this.inputSystem;let s=n.target;s&&!n.previous.isDragEnd&&s[t(354)](n)}[P(391)](e){const t=P,n=this[t(352)];let s=n.target;s&&!n[t(428)].isDragEnd&&s.dblclickHandler(n)}mousemoveHandler(e){const t=P,n=this.inputSystem;if(n[t(357)])return this[t(371)](e);if(this.mode==mt.drag){this.setCursor("grab");return}if(n.skipPointerMovePicking){console.log("跳过");return}let s=n.mouseoverTarget,i=this.pickUpViewLayers();s!==i&&s!=null&&s[t(471)]&&s.mouseoutHandler(n),i!=null&&(s!==i?i.mouseenterHandler&&i[t(437)](n):i.mousemoveHandler&&i.mousemoveHandler(n)),n.mouseoverTarget=i}mousewheelHandler(e){const t=this.getCurrentLayer();if(t==null||this.inputSystem.wheelZoom!=!![])return;let i=t.mouseX,r=t.mouseY;(e.wheelDelta==null?e.detail:e.wheelDelta)>0?this.camera.zoomBy(1.25,i,r):this.camera.zoomBy(.8,i,r)}mouseenterHandler(e){}[P(418)](e){this.mousedownHandler(e)}touchmoveHandler(e){const t=P,n=this.inputSystem,s=this.handlerLayer,i=this[t(449)],r=this.inputSystem.target,o=this[t(432)],a=this.pickUpViewLayers();this.inputSystem.mouseoverTarget=a,n.distanceRatio!=1&&this.getChildren().forEach(h=>{const u=t;h.zoom(n[u(369)],n[u(369)])}),n[t(384)];const c=r!=null&&r.mouseEnabled&&r[t(419)];if(this.getChildren()[t(393)](h=>h.mouseEnabled),c){o[t(401)](n);return}if(i==mt.select||i==mt.edit){qr(s);return}this.camera[t(409)](n.dx,n.dy)}touchwheelHandler(e){throw new Error("touchwheelHandler")}touchendHandler(e){this.mouseupHandler(e)}[P(436)](){let e=this.getChildren();for(let t=e.length-1;t>=0;t--){let n=e[t];if(!(n.visible&&n.mouseEnabled))continue;let s=n.pickUpChild();if(s!=null)return s}return null}getCursor(){const e=P;return this.handlerLayer.render[e(362)]()}setCursor(e){const t=P;return this[t(400)].render[t(420)](e)}download(e,t){const n=P;return this.exportSystem[n(450)](e,t)}select(e){const t=P;this.selectedGroup.removeAll(),this[t(432)].addAll(e)}getCurrentLayer(){return this[P(359)].filter(t=>t.visible==!![])[0]}[P(396)](e,t){return e==null&&(e=-this.width*.5),t==null&&(t=this.height*.5),Math.round(Math.random()*(t-e)+e)}[P(383)](){const e=P;let t=this.getChildren()[e(426)](s=>s[e(464)](s.getAABB()));return z.unionRects(t)}[P(456)](){const e=P;let t=this.getChildren().map(s=>s[e(464)](s.getExportAABB()));return z[e(463)](t)}destory(e){const t=P;if(this.destoryed)throw new Error("Stage has been destroyed already.");this.destoryed=!![],this[t(440)]&&this._resizeObserver.disconnect(),this._resizeTimer&&clearInterval(this._resizeTimer),this.handlerLayer.destory(),this.children.forEach(n=>{n.destory()}),this[t(380)][t(458)]="",e!=![]&&$.clearCache()}}function qr(x,e){const t=P;let n=x.stage,s=x.showAreaBox(),i=x[t(452)][t(410)]();for(var r=0;r<i[t(392)];r++){let o=i[r];if(!o.hasChildren())continue;let a=o.toLayerRect(s),c=o.pickUpByRect(a);n.selectedGroup.addAll(c)}}function Ur(x,e,t){const n=P;x.layersContainer.style[n(442)]=n(430)+x.getToolbarHeight()+"px)",x.width=e,x[n(442)]=t,x.handlerLayer[n(358)](e,t),x[n(410)]()[n(454)](function(i){const r=n;i[r(358)](e,t),x.editor&&x[r(382)].update&&x.editor[r(457)]()});let s=new Event(it[n(395)]);x.dispatchEvent(s)}function _c(x){const e=x.layersContainer;if(e.style.height="calc(100% - "+x.getToolbarHeight()+"px)",window.ResizeObserver){const n=new ResizeObserver(s=>{const i=ve;s[0][i(462)],kt.delayRun("stage_resize",function(){Ur(x,e.clientWidth,e.clientHeight)},30)});n.observe(e),x._resizeObserver=n;return}let t=setInterval(function(){const n=ve;let s=e[n(364)],i=e.offsetHeight;(x.width!=s||x[n(442)]!=i)&&(e[n(459)].height="calc(100% - "+x.getToolbarHeight()+n(406),Ur(x,s,i))},500);x._resizeTimer=t}function mc(x,e){const t=P;let n=x[t(449)];x[t(449)]=e;let s={type:it.modeChange,oldMode:n,newMode:e};x[t(403)](s)}function Ic(x){const e=P;if(typeof x=="string"&&(x=document.getElementById(x),x==null))throw new Error("the dom element id is not found id:"+x);if(x==null)throw new Error("the dom element is null.");return x.style[e(387)]="relative",x}const V=Ns;(function(x,e){const t=Ns,n=x();for(;[];)try{if(-parseInt(t(299))/1*(-parseInt(t(293))/2)+parseInt(t(318))/3+parseInt(t(310))/4*(parseInt(t(303))/5)+-parseInt(t(290))/6+parseInt(t(308))/7+-parseInt(t(294))/8*(-parseInt(t(317))/9)+-parseInt(t(316))/10*(parseInt(t(312))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(zs,789896);var wc=Object[V(309)],vc=Object.getOwnPropertyDescriptor,js=(x,e,t,n)=>{for(var s=n>1?void 0:n?vc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&wc(e,t,s),s};function zs(){const x=["defineProperty","4614940mmRRMU","getPoint","286onyjmB","translateWith","getCtrlPoints","prototype","664730afFgZm","101538IqDwHn","3192633xASJHd","serializers","points","7391364SywCLi","getBeginArrowDirection","normalize","6CUyIoP","248oBJRMU","getPoints","DefaultPositions","horizontal","begin","315521roOMtM","concat","interpolate","getCtrlPoint","5lpRjOT","ctrlPoint","direction","setCtrlPoint","length","1650859NerZdu"];return zs=function(){return x},zs()}class e0 extends W{constructor(e,t,n,s,i){const r=V;super(e,t,n,s,i),this[r(305)]="horizontal"}updatePoints(){const e=V;let t=this._calcAZ(),n=t[0],s=t[1],i=this[e(302)](n,s);return this.points=[n,i,s],this[e(289)]}translateWith(e,t){const n=V;return super[n(313)](e,t),this.ctrlPoint!=null&&(this.ctrlPoint.x+=e,this.ctrlPoint.y+=t),this}autoCalcCtrlPoint(e,t){const n=V;let s=(e.x+t.x)/2,i=(e.y+t.y)/2;return this[n(305)]==Te[n(297)]?i+=(t.y-e.y)/2:i-=(t.y-e.y)/2,{x:s,y:i}}getCtrlPoint(e,t){const n=V;return this.ctrlPoint!=null?this[n(304)]:this.autoCalcCtrlPoint(e,t)}[V(306)](e){const t=V;this[t(304)]=e,this.matrixDirty=!![]}resetCtrlPoint(){const e=V;this[e(304)]=void 0,this.matrixDirty=!![]}[V(311)](e){const t=V;let n=this.getPoints(),s=n[0],i=n[1],r=n[2],o=C[t(301)](s,i,e),a=C.interpolate(i,r,e);return C.interpolate(o,a,e)}[V(291)](){const e=V;let t=this.points[0],n=this[e(311)](.01),s=[t.x-n.x,t.y-n.y];return B[e(292)](s,s)}getEndArrowDirection(){const e=V;let t=this.getPoint(.9),n=this.points[this.points[e(307)]-1],s=[n.x-t.x,n.y-t.y];return B.normalize(s,s)}}function Ns(x,e){const t=zs();return Ns=function(n,s){return n=n-289,t[n]},Ns(x,e)}js([f("CurveLink")],e0.prototype,"className",2),js([f(A.Curve)],e0.prototype,"shape",2),js([f(function(){return["begin","end","ctrlPoint"]})],e0.prototype,V(314),2),js([f(W.prototype.serializers[V(300)](["direction","ctrlPoint"]))],e0.prototype,V(319),2);const _n={};_n[S[V(298)]]=function(){return this.getPoints()[0]},_n[S.end]=function(){let x=this.getPoints();return x[x.length-1]},_n[S.center]=function(){let e=this[V(295)]();return C.middle(e[0],e[1])},_n[S.ctrlPoint]=function(){if(this.ctrlPoint!=null)return this.ctrlPoint;let x=this.getPoints();return this.getCtrlPoint(x[0],x[1])},e0[V(315)][V(296)]=_n;var n0=Ws;function Rs(){var x=["2896qxPZGp","prototype","14SwpRps","defineProperty","15811QqtUlI","resizeToFitText","arrowsSize","length","884934aVXexr","10186792iXDoxW","3ZxthiA","162845fkrtEG","1177724WGgwtO","2204688NiGpWf","20YjiVto","1400lDqegd"];return Rs=function(){return x},Rs()}(function(x,e){for(var t=Ws,n=x();[];)try{var s=parseInt(t(246))/1*(parseInt(t(244))/2)+parseInt(t(236))/3*(-parseInt(t(238))/4)+-parseInt(t(237))/5+-parseInt(t(239))/6+-parseInt(t(241))/7*(-parseInt(t(242))/8)+parseInt(t(250))/9*(-parseInt(t(240))/10)+parseInt(t(235))/11;if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Rs,218049);function Ws(x,e){var t=Rs();return Ws=function(n,s){n=n-235;var i=t[n];return i},Ws(x,e)}var kc=Object[n0(245)],Sc=Object.getOwnPropertyDescriptor,Hs=(x,e,t,n)=>{for(var s=n0,i=n>1?void 0:n?Sc(e,t):e,r=x[s(249)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&kc(e,t,i),i};class R0 extends $t{constructor(e,t=0,n=0,s=1,i=1){super(e,t,n,s,i)}[n0(247)](){var e=n0;let n=this._computedStyle.calcGap();this._width=this._textWidth+n,this._height=this._textHeight+n+this[e(248)]}}Hs([f("TipNode")],R0.prototype,"className",2),Hs([f(8)],R0.prototype,n0(248),2),Hs([f(new Ae)],R0[n0(243)],"shape",2),Hs([f($t.prototype.serializers.concat(["arrowsSize"]))],R0[n0(243)],"serializers",2);var ke=xe;(function(x,e){for(var t=xe,n=x();[];)try{var s=-parseInt(t(412))/1+-parseInt(t(407))/2+parseInt(t(404))/3*(parseInt(t(402))/4)+-parseInt(t(417))/5*(-parseInt(t(410))/6)+-parseInt(t(401))/7*(parseInt(t(397))/8)+parseInt(t(416))/9+-parseInt(t(405))/10*(-parseInt(t(414))/11);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Fs,333449);var Pc=Object.defineProperty,Oc=Object.getOwnPropertyDescriptor,nr=(x,e,t,n)=>{for(var s=n>1?void 0:n?Oc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Pc(e,t,s),s};function xe(x,e){var t=Fs();return xe=function(n,s){n=n-397;var i=t[n];return i},xe(x,e)}class W0 extends M{constructor(e,t=0,n=0,s=1,i=1){var r=xe;super(),this._isPlaying=![],this.text=e,this.x=t||0,this.y=n||0,this[r(415)]=s||0,this.height=i||0}showCover(){var e=xe;this[e(422)]=![]}play(){this._isPlaying=!![],this._video.play()}pause(){var e=xe;this[e(423)].pause()}setVideo(e){var t=xe;const n=this;this._dom!=null&&(this._dom[t(409)](),this[t(413)]=null),typeof e=="string"?(this._videoSrc=e,this[t(423)]=fe[t(403)](e,function(){var s=t;n[s(408)]&&n[s(398)]()}),this._dom=this._video):(this[t(423)]=e,this._videoSrc=e[t(420)].getAttribute("src")),this._video[t(415)]=this[t(415)],this[t(423)][t(418)]=this.height}onPlay(e){var t=xe;this._video[t(406)](t(398),e,![])}[ke(411)](e){this._video.addEventListener("pause",e,![])}onEnded(e){var t=ke;this._video[t(406)]("ended",e,![])}draw(e){var t=ke;e.beginPath();const n=-this.width*.5,s=-this.height*.5;if(e.rect(n,s,this.width,this.height),this._video!=null){this._video.width!=this.width&&(this._video.width=this[t(415)],this._video.height=this.height);let i=this[t(422)]?this[t(423)]:this._image;i!=null&&e.drawImage(i,n,s,this.width,this[t(418)])}else e.stroke();this[t(419)](e),this[t(399)](e)}destory(){super.destory(),this._dom&&this._dom.remove()}}nr([f("VideoNode")],W0[ke(400)],"className",2),nr([f(![])],W0[ke(400)],"autoplay",2),nr([f(M.prototype[ke(421)].concat([ke(408),"videoSrc"]))],W0.prototype,ke(421),2),Object.defineProperties(W0.prototype,{videoSrc:{get(){return this._videoSrc},set(x){this.setVideo(x)}}});function Fs(){var x=["30QTNCoQ","onPause","612300GFoHvV","_dom","2838fBvvvt","width","954351KcItVN","41140vkBZeT","height","mousePickupPath","firstElementChild","serializers","_isPlaying","_video","8YRrrhP","play","_paintText","prototype","1739262irSycS","312788xbhXOf","createVideo","21XfWBmy","34490LqcrCe","addEventListener","780370vGCxHm","autoplay","remove"];return Fs=function(){return x},Fs()}const _t=H0;(function(x,e){const t=H0,n=x();for(;[];)try{if(parseInt(t(243))/1+-parseInt(t(241))/2+parseInt(t(254))/3+parseInt(t(244))/4*(parseInt(t(248))/5)+-parseInt(t(255))/6+-parseInt(t(239))/7*(parseInt(t(238))/8)+-parseInt(t(246))/9*(parseInt(t(237))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Xs,462479);var xr=function(){};xr.prototype={calculate:function(x,e){const t=H0;this.dx=x.x-e.x,this.dy=x.y-e.y,this.d2=this.dx*this.dx+this.dy*this.dy,this.d=Math[t(260)](this.d2)}};class Zr{constructor(e,t,n){const s=H0;this[s(242)]=[],this.frame_width=t,this[s(236)]=n,this.origin=e,this[s(256)](),this.initNodes(e)}[_t(261)](e){const t=_t;let n=this;Yt[t(259)](e,function(s,i){const r=t;if(s.isNode&&i!=null){let o=i,a=s;o==e&&n.setOriginEdgeWeight(a,n.originWeight);let c=a.mass|1;n.addNode(a,c);let l=30;n[r(257)](o,a,l)}})}initialize(){const e=_t;this.originWeight=48,this.speed=12,this.gravity=50,this[e(262)]=512,this[e(252)]=new Array,this.edges=new Array,this.originEdges=new Array}[_t(265)](e,t){const n=_t;if(this.originEdges[e.id]){if(e.id!=this.selectedNode){let s=this[n(242)][e.id],i=(t.d-s)/s;e.force.x+=i*(t.dx/t.d),e.force.y+=i*(t.dy/t.d)}}else if(e.id!=this.selectedNode){let s=this.gravity*e.mass*this[n(258)].mass/t.d2,i=this.maxForceDistance-t.d;i>0&&(s*=Math.log(i)),s<1024&&(e.force.x-=s*t.dx/t.d,e[n(251)].y-=s*t.dy/t.d)}}[_t(247)](e,t,n){const s=_t;let i=this[s(240)][e.id][t.id];if(i+=3*(e.neighbors+t[s(250)]),i){let r=(n.d-i)/i;e.id!=this.selectedNode&&(e.force.x-=r*n.dx/n.d,e[s(251)].y-=r*n.dy/n.d),t.id!=this[s(263)]&&(t.force.x+=r*n.dx/n.d,t.force.y+=r*n.dy/n.d)}}repulsiveForce(e,t,n){const s=_t;let i=this.gravity*e.mass*t.mass/n.d2,r=this[s(262)]-n.d;r>0&&(i*=Math[s(253)](r)),i<1024&&(e[s(251)].x+=i*n.dx/n.d,e.force.y+=i*n.dy/n.d)}[_t(249)](){this.applyForce()}[_t(264)](){const e=_t;for(var t=0;t<this.nodes.length;t++){let s=this.nodes[t];for(var n=0;n<this.nodes.length;n++)if(t!=n){let r=this.nodes[n],o=new xr;o.calculate(s,r),this.getLink(s.id,r.id)!=null&&this.attractiveForce(s,r,o),t!=this.selectedNode&&this.repulsiveForce(s,r,o)}let i=new xr;i.calculate(this.origin,s),this.originForce(s,i),s.force.x*=this.speed,s[e(251)].y*=this.speed,s.x+=s[e(251)].x,s.y+=s.force.y,s.force.x=0,s.force.y=0}}bounds(e){const t=_t;let n=12,s=n*2+4,i=e.x,r=e.x+s,o=e.y,a=e.y+s;i<0&&(e.x=0),o<0&&(e.y=0),r>this[t(245)]&&(e.x=this[t(245)]-s),a>this[t(236)]&&(e.y=this[t(236)]-s)}setOriginEdgeWeight(e,t){this.originEdges[e.id]=t}addNode(e,t){const n=_t;e.mass=t|1,e.neighbors=e[n(250)]|0,e.force={x:0,y:0},this[n(252)].push(e)}getLink(e,t){let n=this.edges[e];return n==null?null:n[t]}addLink(e,t,n){!this.edges[e.id]&&(this.edges[e.id]=new Object),this.edges[e.id][t.id]=n;try{e.neighbors++,t.neighbors++}catch(s){console.log("Error Adding Edge: "+s)}}}function H0(x,e){const t=Xs();return H0=function(n,s){return n=n-236,t[n]},H0(x,e)}function Xs(){const x=["14znfoDK","edges","471362ANnjFq","originEdges","710187pgocSx","170012kvyiOq","frame_width","54738ltmMDm","attractiveForce","25KWrmue","doLayout","neighbors","force","nodes","log","1445913HyVvQA","42750NqJVBx","initialize","addLink","origin","travel","sqrt","initNodes","maxForceDistance","selectedNode","applyForce","originForce","frame_height","230kmcKwD","2238008gkkqFD"];return Xs=function(){return x},Xs()}const Se=Vs;(function(x,e){const t=Vs,n=x();for(;[];)try{if(parseInt(t(294))/1+parseInt(t(297))/2*(-parseInt(t(302))/3)+-parseInt(t(301))/4*(-parseInt(t(288))/5)+-parseInt(t(286))/6*(parseInt(t(295))/7)+-parseInt(t(292))/8+-parseInt(t(300))/9+parseInt(t(289))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Js,774723);var Ec=Object[Se(290)],Lc=Object.getOwnPropertyDescriptor,Ys=(x,e,t,n)=>{for(var s=n>1?void 0:n?Lc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Ec(e,t,s),s};function Js(){const x=["4STWhJm","21bJrvrF","23676OnTLZf","prototype","401725mMkYiE","42329490hHHmuK","defineProperty","direction","6626936cmLVdz","concat","1019121XdpAGL","2268EwJFvV","length","319810OXAgbR","push","getPoint","11983374Lpmyfp"];return Js=function(){return x},Js()}class F0 extends W{constructor(e,t,n,s,i){super(e,t,n,s,i)}[Se(299)](e){const t=Se;let n=this.getPoints(),s=n[0],i=n[n[t(296)]-1],r=(s.x+i.x)/2,o=(s.y+i.y)/2,a=s.x-i.x,c=s.y-i.y,l=Math.sqrt(a*a+c*c)/2,h=Math.atan2(c,a),u=h+Math.PI*e;return this[t(291)]=="anticlockwise"&&(u=h-Math.PI*e),{x:r+l*Math.cos(u),y:o+l*Math.sin(u)}}updatePoints(){const e=Se;let t=this._calcAZ(),n=t[0],s=t[1],i=[n],r={x:(n.x+s.x)*.5,y:(n.y+s.y)*.5},o=C.distancePoint(n,s)*.5;return i.push({x:r.x,y:r.y-o}),i[e(298)]({x:r.x,y:r.y+o}),i[e(298)]({x:r.x-o,y:r.y}),i[e(298)]({x:r.x+o,y:r.y}),i.push(s),i}}Ys([f("ArcLink")],F0[Se(287)],"className",2),Ys([f(A.Arc)],F0[Se(287)],"shape",2),Ys([f(W.prototype.serializers[Se(293)](["direction"]))],F0[Se(287)],"serializers",2),Ys([f("clockwise")],F0.prototype,"direction",2);function Vs(x,e){const t=Js();return Vs=function(n,s){return n=n-286,t[n]},Vs(x,e)}function Gs(){const x=["2952378rikvWu","161758gApsTR","interpolate","getPoint","BezierLink","399574GwinrJ","ctrlPoint2","getPoints","shape","3082170SBtJpe","end","1608027ovqJXD","8XZzLaD","prototype","ctrlPoint1","calcCtrlPoint2","updatePoints","48CkJevH","matrixDirty","799940jjgkIK","begin","161860AcLchZ"];return Gs=function(){return x},Gs()}const ut=X0;(function(x,e){const t=X0,n=x();for(;[];)try{if(parseInt(t(427))/1+parseInt(t(445))/2+parseInt(t(437))/3+parseInt(t(443))/4*(-parseInt(t(447))/5)+-parseInt(t(435))/6+parseInt(t(431))/7*(parseInt(t(438))/8)+parseInt(t(426))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Gs,580702);function X0(x,e){const t=Gs();return X0=function(n,s){return n=n-426,t[n]},X0(x,e)}var Tc=Object.defineProperty,Cc=Object.getOwnPropertyDescriptor,sr=(x,e,t,n)=>{for(var s=n>1?void 0:n?Cc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Tc(e,t,s),s};class Y0 extends W{constructor(e,t,n,s,i){super(e,t,n,s,i)}translateWith(e,t){const n=X0;return super.translateWith(e,t),this.ctrlPoint1!=null&&(this.ctrlPoint1.x+=e,this.ctrlPoint1.y+=t),this[n(432)]!=null&&(this.ctrlPoint2.x+=e,this.ctrlPoint2.y+=t),this}setCtrlPoint1(e){this.ctrlPoint1=e,this.matrixDirty=!![]}setCtrlPoint2(e){const t=X0;this.ctrlPoint2=e,this[t(444)]=!![]}autoCalcCtrlPoint(e,t,n){let s=t.x-e.x,i=t.y-e.y,r=(t.x+e.x)/2,o=(t.y+e.y)/2,a=1-.618;return{x:r+s*a,y:o-i*a}}calcCtrlPoint1(e,t,n){return this.ctrlPoint1!=null?this.ctrlPoint1:this.autoCalcCtrlPoint(e,n,t)}[ut(441)](e,t,n){return this.ctrlPoint2!=null?this.ctrlPoint2:this.autoCalcCtrlPoint(t,n,e)}resetCtrlPoint(){const e=ut;this[e(440)]=void 0,this.ctrlPoint2=void 0,this[e(444)]=!![]}getPoint(e){const t=ut;let n=this.getPoints(),s=n[0],i=n[1];n[2];let r=n[3],o=n[4],a=i,c=r,l=C.interpolate(s,a,e),h=C.interpolate(a,c,e),u=C[t(428)](c,o,e),g=C[t(428)](l,h,e),y=C.interpolate(h,u,e);return C.interpolate(g,y,e)}[ut(442)](){const e=ut,t=this._calcAZ(),n=t[0],s=t[1];let i={x:(n.x+s.x)/2,y:(n.y+s.y)/2},r=this.calcCtrlPoint1(n,s,i),o=this[e(441)](n,s,i);return[n,r,i,o,s]}}sr([f(ut(430))],Y0[ut(439)],"className",2),sr([f(A.BezierCurve)],Y0[ut(439)],ut(434),2),sr([f(W.prototype.serializers.concat(["ctrlPoint1","ctrlPoint2"]))],Y0.prototype,"serializers",2);const J0={};J0[S[ut(446)]]=function(){return this.getPoints()[0]},J0[S[ut(436)]]=function(){let e=this[ut(433)]();return e[e.length-1]},J0[S.center]=function(){return this[ut(429)](.5,0)},J0[S.ctrlPoint1]=function(){return this.ctrlPoint1!=null?this.ctrlPoint1:this.getPoints()[1]},J0[S.ctrlPoint2]=function(){return this[ut(432)]!=null?this.ctrlPoint2:this.getPoints()[3]},Y0.prototype.DefaultPositions=J0;var Ac=mn;(function(x,e){for(var t=mn,n=x();[];)try{var s=-parseInt(t(122))/1*(-parseInt(t(124))/2)+parseInt(t(120))/3*(parseInt(t(123))/4)+-parseInt(t(126))/5*(-parseInt(t(134))/6)+parseInt(t(128))/7+-parseInt(t(125))/8+parseInt(t(130))/9*(parseInt(t(132))/10)+parseInt(t(133))/11*(-parseInt(t(131))/12);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Us,125547);var Mc=Object.defineProperty,Dc=Object.getOwnPropertyDescriptor,Bc=(x,e,t,n)=>{for(var s=n>1?void 0:n?Dc(e,t):e,i=x.length-1,r;i>=0;i--)(r=x[i])&&(s=(n?r(e,t,s):r(s))||s);return n&&s&&Mc(e,t,s),s};function mn(x,e){var t=Us();return mn=function(n,s){n=n-120;var i=t[n];return i},mn(x,e)}class qs extends W{constructor(e,t,n,s,i){super(e,t,n,s,i)}getPath(){return this.path.endpoints}clearPath(){var e=mn;const t=this.path.endpoints;for(let n=1;n<t.length-1;n++){let s=t[n];s[e(129)]()&&s.target.removeOutLink(this)}t[e(127)]=0}setPath(e){if(e.length<2)throw new Error("path length is less than 2");this.clearPath(),super._setEndpoints(e),this.matrixDirty=!![]}}function Us(){var x=["16820pUxNUD","8393wNGhXn","726jRgDMh","39KLqkvj","prototype","317jpBKcB","17372czAduj","856uBIqJH","1527632IuLowK","4185TlAMhq","length","589680vtgaxt","isDisplayObjectTarget","918zwoEcu","3660PmrfZO"];return Us=function(){return x},Us()}Bc([f("PathLink")],qs[Ac(121)],"className",2);const In=Zs;(function(x,e){const t=Zs,n=x();for(;[];)try{if(parseInt(t(368))/1*(-parseInt(t(370))/2)+-parseInt(t(372))/3*(parseInt(t(375))/4)+parseInt(t(365))/5*(-parseInt(t(362))/6)+-parseInt(t(369))/7+parseInt(t(376))/8*(parseInt(t(367))/9)+-parseInt(t(363))/10*(-parseInt(t(371))/11)+parseInt(t(379))/12*(parseInt(t(366))/13)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Ks,705683);const Kr=["#475164",In(374),"#FA7E23","#FF9900","#FED71A","#2bae85",In(364),"#12A182","#5e5314","#1ba784","#0f1423",In(377),"#2474b5","#2775B6","#346c9c","#61649f","#C06f98","#7e2065","#681752","#EE3f4d",In(373)];function Qr(){let e=Math[In(378)]()*Kr.length|0;return Kr[e]}function Zs(x,e){const t=Ks();return Zs=function(n,s){return n=n-362,t[n]},Zs(x,e)}function Ks(){const x=["2659581JgPnAP","8oGuWzv","6167245DCQtQP","123866ixvhHV","33cZmyAT","2858286YLgHAa","#C02c38","#2d2e36","4GhaUML","16uYKFoD","#4E7ca1","random","8005488qIqhYz","43782gdhPfP","1416210SwYNDU","#248067","445eLvnkh","52qtKFcR"];return Ks=function(){return x},Ks()}const q=Pe;(function(x,e){const t=Pe,n=x();for(;[];)try{if(parseInt(t(108))/1+parseInt(t(115))/2*(-parseInt(t(109))/3)+parseInt(t(116))/4*(parseInt(t(138))/5)+-parseInt(t(131))/6*(-parseInt(t(114))/7)+parseInt(t(123))/8*(-parseInt(t(127))/9)+parseInt(t(112))/10*(-parseInt(t(136))/11)+-parseInt(t(132))/12*(parseInt(t(117))/13)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(Qs,942232);const wn=class{constructor(){const x=Pe;this.position={x:0,y:0},this.direction=0,this.stepSize=1,this.dx=1,this.dy=1,this[x(121)]()}init(){const x=Pe;return this._position={x:0,y:0},this._actions=[],this[x(125)]={},this}addAction(x,e){const t=Pe;let n={name:x,args:e};return this._actions[t(129)](n),this}mark(x){const e=Pe;if(x==null)throw new Error("mark's name is required.");return this.marks[x]={x:this._position.x,y:this[e(135)].y},this}[q(118)](x){return this.marks[x]}faceToMark(x){const e=q;let t=this.getMark(x);return this[e(130)](t)}moveToMark(x){let t=this[q(118)](x);return this.moveTo(t)}forwardToMark(x){let t=this[q(118)](x);return this.forwardTo(t)}[q(113)](x){const e=q;for(let t=0;t<x.length;t++){const n=x[t],s=this[e(118)](n);this[e(120)](s)}return this}updateDxy(){let x=this._position,e=this._direction,t=this._stepSize,n=x.x+t*Math.cos(e),s=x.y+t*Math.sin(e);return this.dx=n-x.x,this.dy=s-x.y,this}faceTo(x){const e=x.x,t=x.y;return this._direction=Math.atan2(t-this._position.y,e-this._position.x),this.updateDxy(),this}[q(119)](x){const e=q;return this[e(128)](x),this[e(135)].x+=this.dx,this._position.y+=this.dy,this.addAction(wn.OP.forward,[this._position.x,this._position.y]),this}jump(x){x==null&&(x=1);for(var e=0;e<x;e++)this._position.x+=this.dx,this._position.y+=this.dy,this.addAction(wn.OP.jump,[this._position.x,this._position.y]);return this}moveTo(x){const e=q,t=x.x,n=x.y;return this._position.x=t,this[e(135)].y=n,this.addAction(wn.OP.moveTo,[this._position.x,this._position.y]),this}moveToMiddle(x,e){let t={x:(x.x+e.x)/2,y:(x.y+e.y)/2};return this.moveTo(t)}forwardTo(x){const e=q,t=x.x,n=x.y;return this._position.x=t,this[e(135)].y=n,this.addAction(wn.OP[e(120)],[this._position.x,this[e(135)].y]),this}[q(126)](x){return x==null&&(x=Math.PI/2),this._direction=this._direction-x,this.updateDxy(),this}[q(111)](x){const e=q;return x==null&&(x=-Math.PI/2),this[e(137)]=this._direction+x,this.updateDxy(),this}size(x){return this._stepSize=x,this.updateDxy(),this}sizeBy(x){const e=q;return this[e(133)]*=x,this[e(124)](),this}sizeWith(x){return this._stepSize+=x,this.updateDxy(),this}getDistance(x){const e=q,t=x.x,n=x.y;let s=t-this._position.x,i=n-this[e(135)].y;return Math[e(122)](s*s+i*i)}getAngle(x){const e=q,t=x.x,n=x.y;return Math.atan2(n-this[e(135)].y,t-this._position.x)}};let $r=wn;function Pe(x,e){const t=Qs();return Pe=function(n,s){return n=n-108,t[n]},Pe(x,e)}$r.OP={forward:"forward",forwardTo:q(120),moveTo:q(134),jump:q(110)};function Qs(){const x=["push","faceTo","6nrffha","500184KaVKQA","_stepSize","moveTo","_position","11gYaKnT","_direction","136265CWdRnc","127899BSWBXP","423Ebcljm","jump","turnRight","1954270GWwwxA","forwardToMarks","7525931sFbZZr","12590bHfSdP","268DtkxDT","39gZHkFJ","getMark","forward","forwardTo","init","sqrt","27352MkLSHM","updateDxy","marks","turnLeft","2313jLrAri","size"];return Qs=function(){return x},Qs()}const Et=$s;(function(x,e){const t=$s,n=x();for(;[];)try{if(-parseInt(t(363))/1+-parseInt(t(349))/2*(-parseInt(t(353))/3)+-parseInt(t(372))/4*(-parseInt(t(350))/5)+parseInt(t(361))/6*(parseInt(t(373))/7)+parseInt(t(360))/8+parseInt(t(357))/9+-parseInt(t(369))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ti,756762);function $s(x,e){const t=ti();return $s=function(n,s){return n=n-346,t[n]},$s(x,e)}function ti(){const x=["applyTo's target has no method:","toCmds","style","8014401NemNbu","circle","text","3067520sDnYYT","12DjZZdU","restore","1434205VRAWze","_actions","_position","addAction","name","fillText","15053990MfNdqn","moveTo","push","34396PhbggB","4886350uWEyEn","ctx.","join","length","166xmhGTV","220ZwBapG","apply",`
 return path;`,"23421qwjNDq"];return ti=function(){return x},ti()}var to={forward:"lineTo",forwardTo:"lineTo",moveTo:Et(370),jump:"moveTo"};class eo extends $r{constructor(){super()}applyTo(e){const t=Et;let n=this[t(364)];for(let s=0;s<n[t(348)];s++){const i=n[s];let r=to[i[t(367)]];const o=i.args;r==null&&(r=i.name);let a=e[r];if(a==null)throw new Error(t(354)+r);if(o==null){a.apply(e,[]);continue}o[t(348)]?a[t(351)](e,o):e[r]=o}return this}toFunction(){let e=this.toCmd();return new Function("ctx",e)}toPath2D(){const e=Et;let t=this.toCmd("path.");return t=`var path = new Path2D();
`+t,t=t+e(352),new Function("path",t)()}toCmd(e){const t=Et;return this.toCmds(e)[t(347)](`
`)}[Et(355)](e){const t=Et;let n=this._actions,s=[];e==null&&(e=t(346));for(let i=0;i<n[t(348)];i++){const r=n[i];let o=to[r[t(367)]];const a=r.args;if(o==null&&(o=r[t(367)]),a==null){s[t(371)](e+o+"();");continue}a[t(348)]?s[t(371)](e+o+"("+no(a)+");"):s.push(e+o+"="+no(a)+";")}return s}[Et(358)](e){return this.addAction("arc",[this._position.x,this._position.y,e,0,Math.PI*2]),this}[Et(356)](e,t){return this.addAction(e,t),this}[Et(359)](e){const t=Et;return this.addAction(t(368),[e,this[t(365)].x,this._position.y]),this}lineTo(e){return this.forwardTo(e),this}beginPath(){return this.addAction("beginPath"),this}stroke(){return this[Et(366)]("stroke"),this}fill(){return this.addAction("fill"),this}save(){return this.addAction("save"),this}restore(){const e=Et;return this.addAction(e(362)),this}}function no(x){if(!Array.isArray(x)&&typeof x=="string")return'"'+x+'"';let e="";for(let t=0;t<x.length;t++){let n=x[t];typeof n=="string"?e+='"'+n+'"':e+=""+n,t+1<x.length&&(e+=",")}return e}(function(x,e){const t=Gt,n=x();for(;[];)try{if(parseInt(t(363))/1*(parseInt(t(373))/2)+parseInt(t(374))/3*(-parseInt(t(357))/4)+parseInt(t(384))/5*(parseInt(t(361))/6)+parseInt(t(381))/7*(parseInt(t(369))/8)+-parseInt(t(370))/9*(-parseInt(t(351))/10)+parseInt(t(378))/11*(-parseInt(t(368))/12)+-parseInt(t(367))/13*(parseInt(t(356))/14)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ei,394012);function Gt(x,e){const t=ei();return Gt=function(n,s){return n=n-349,t[n]},Gt(x,e)}function jc(x,e,t){const n=Gt;t=t|1;const s=document.createElementNS("http://www.w3.org/2000/svg","svg");return s[n(372)]("xmlns",n(360)),s.style="border:0px;position:absolute;top:0px;left:0px;text-align:center;z-index:10;width:"+x+n(362)+e+"px;opacity:"+t+";",s.innerHTML=n(366)+t+n(379),s}function zc(x){const e=Gt;let t=document.createElement(e(359));t.width=x[e(382)].width?+x[e(382)][e(358)]:x.width,t.height=x.style[e(352)]?+x[e(382)][e(352)]:x.height;let n=t[e(380)]("2d");return n.scale(t[e(358)]/x.naturalWidth,t.height/x[e(385)]),n[e(364)](x,0,0),e(383)+x[e(358)]+e(355)+x.height+e(354)+t.toDataURL(e(371))+'" height="'+x[e(352)]+e(349)+x.width+'px" /></svg>'}async function Nc(x){const e=x.replace(/\<br\>/gi,"<div/>"),t=e.match(/<img .*?>/g);if(t==null||t.length==0)return new Promise(function(s){s([[],[]])});const n=[];return new Promise(function(s){const i=Gt;t[i(350)](function(r){const o=i,a=document.createElement("div");a[o(386)]=r;const c=a.querySelector(o(353));c.onload=function(){const l=o,h=zc(c);n.push(h),n[l(377)]==t.length&&s([t,n])}})})}function ei(){const x=["12ojYvWI","912ktbfnP","9567OUiMvM","image/png","setAttribute","10EXrVSB","1605081LkuFoG","opacity","svg","length","6244403GKLOjA",`;">
        '</foreignObject>`,"getContext","48069gNoVeN","style",'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink= "http://www.w3.org/1999/xlink" width="',"8300WPfutO","naturalHeight","innerHTML",'px" width="',"forEach","3110cMGwaf","height","img",'" display="inline" style="vertical-align: middle;display: inline-block"><image xlink:href="','" height="',"11606zxpNcU","4apHTnS","width","canvas","http://www.w3.org/2000/svg","2388RehaKi","px;height:","127066GAqWwz","drawImage","html",'<foreignObject width="100%" height="100%" style="position:absolute;top:0;left:0; opacity: ',"14313JbDGSs"];return ei=function(){return x},ei()}class xo{constructor(e,t,n,s=1){const i=Gt;this.isHtmlImage=!![],this[i(358)]=1,this[i(352)]=1,this.opacity=1,this.isHtmlImage=!![],e!==null&&e.startsWith("HtmlImage")&&(e=e.substring(9)),this.html=e,this.width=t,this.height=n,this[i(375)]=s|1,this.svg=jc(t,n,this[i(375)]),this[i(359)]=document.createElement("canvas")}setSize(e,t){this.width=e,this.height=t}getAttribute(e){if(e!="src")throw new Error("HtmlImage has only src attr");return"HtmlImage"+this.html}setHtml(e){const t=Gt;this[t(365)]=e}getCanvas(){const e=Gt;let t=this[e(365)];const n=this[e(376)],s=this;let i=s[e(359)],r=i.getContext("2d");return i.style.width=s[e(358)]+"px",i[e(382)].height=s.height+"px",i[e(358)]=s[e(358)],i[e(352)]=s.height,new Promise(function(o){Nc(t).then(function(a){const c=Gt,l=a[0],h=a[1];for(let y=0;y<l[c(377)];y++)t=t.replace(l[y],h[y]);const u=n.querySelector("foreignObject");u.innerHTML=t;let g=new Image;g.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(s[c(376)].outerHTML),g.onload=function(){r.drawImage(g,0,0,s.width,s.height),o(i)}})})}}const V0=si;(function(x,e){const t=si,n=x();for(;[];)try{if(parseInt(t(144))/1*(parseInt(t(137))/2)+parseInt(t(142))/3+-parseInt(t(136))/4+parseInt(t(135))/5*(-parseInt(t(131))/6)+parseInt(t(129))/7*(parseInt(t(128))/8)+parseInt(t(143))/9+-parseInt(t(134))/10*(parseInt(t(141))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ni,483632);var Rc=Object[V0(139)],Wc=Object.getOwnPropertyDescriptor,so=(x,e,t,n)=>{const s=V0;for(var i=n>1?void 0:n?Wc(e,t):e,r=x[s(133)]-1,o;r>=0;r--)(o=x[r])&&(i=(n?o(e,t,i):o(i))||i);return n&&i&&Rc(e,t,i),i};function ni(){const x=["prototype","95094Rcrrjo","left","length","10NTwObq","35EWGltL","2401984oceGLy","36zfeZyI","beginPath","defineProperty","direction","9661817GlhzXm","1215786XFKTDy","4738428xdJBHV","22913thOkIt","height","width","583384uyHgiN","70ZbGpJH"];return ni=function(){return x},ni()}class xi extends M{constructor(e,t=0,n=0,s=1,i=1){const r=V0;super(e,t,n,s,i),this.ratio=.5,this[r(140)]=S.right}draw(e){const t=V0;let n=this._computedStyle,s=n.fillStyle;n.fillStyle=null,this._strokeAndFill(e),this.mousePickupPath(e),n.fillStyle=s,e[t(138)](),e.fillStyle=s;let i=n.borderWidth||0,r=n.padding||0,o=r*2+i*2,a=-this.width*.5+i+r,c=-this.height*.5+i+r,l=(this.width-o)*this.ratio,h=(this.height-o)*this.ratio;if(this.direction==S.right)h=this.height-o;else if(this.direction==S.down)l=this[t(127)]-o;else if(this.direction==S[t(132)])a=-this.width*.5+this[t(127)]-i-r-l,h=this.height-o;else if(this.direction==S.up)c=-this.height*.5+this[t(126)]-i-r-h,l=this[t(127)]-o;else throw new Error("Unknow RatioNode's direction:"+this.direction);e.rect(a,c,l,h),e.fill(),this._paintText(e)}}so([f("RatioNode")],xi.prototype,"className",2),so([f(M[V0(130)].serializers.concat(["ratio","direction"]))],xi[V0(130)],"serializers",2);function si(x,e){const t=ni();return si=function(n,s){return n=n-126,t[n]},si(x,e)}const io=Oe;(function(x,e){const t=Oe,n=x();for(;[];)try{if(-parseInt(t(494))/1*(-parseInt(t(475))/2)+-parseInt(t(478))/3+-parseInt(t(480))/4*(parseInt(t(483))/5)+-parseInt(t(487))/6*(parseInt(t(482))/7)+-parseInt(t(490))/8+parseInt(t(476))/9+parseInt(t(491))/10*(parseInt(t(500))/11)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ii,167249);function Oe(x,e){const t=ii();return Oe=function(n,s){return n=n-475,t[n]},Oe(x,e)}function ii(){const x=["classList","jtopo_popoupmenu","domElement","setHtml","352AozrQA","style","hide","644692kRMQsr","763794UwUuZo","height","493467ojYAOw","appendChild","212ilMZEw","none","84TvnDqS","19850gPmBBc","width","defaultPrevented","layersContainer","41946qSupXz","item","block","626592WxOMkg","92860eozawW","createElement","stage","1PCrKpA","offsetHeight"];return ii=function(){return x},ii()}class ro extends wt{constructor(e,t){const n=Oe;super(),this.stage=e,this.domElement=this[n(499)](t)}remove(){this.domElement!=null&&this.domElement.remove()}setHtml(e){const t=Oe;this.html=e,this.remove();let n=document[t(492)]("div");return n[t(496)].add(t(497)),this[t(493)][t(486)][t(479)](n),n.innerHTML=e,this.initEvent(n),this.domElement=n,this.hide(),n}initEvent(e){let t=this;e.querySelectorAll("a").forEach(function(s){s.addEventListener("click",function(i){const r=Oe;let o=new Event("select",{cancelable:!![]});o[r(488)]=this.innerHTML,t.dispatchEvent(o),!o[r(485)]&&t[r(502)]()})})}showAt(e,t){const n=Oe;this.domElement.style.display=n(489),t+this.domElement[n(495)]>=this.stage.height&&t>this[n(493)][n(477)]/2&&(t-=this.domElement[n(495)]),e+this.domElement.offsetWidth>=this.stage[n(484)]&&e>this.stage.width/2&&(e-=this[n(498)].offsetWidth),this.domElement[n(501)].left=e+"px",this.domElement[n(501)].top=t+"px"}[io(502)](){const e=io;this.domElement[e(501)].display=e(481)}}const At=G0;(function(x,e){const t=G0,n=x();for(;[];)try{if(parseInt(t(518))/1+parseInt(t(510))/2*(parseInt(t(513))/3)+parseInt(t(503))/4+parseInt(t(501))/5+parseInt(t(515))/6*(-parseInt(t(508))/7)+-parseInt(t(523))/8+-parseInt(t(509))/9===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ri,235293);function ri(){const x=["6545853NgAFfr","2fQsdPJ","style","domElement","1026363DAZbYr","fadeOut","114aIcdrt","showAt","left","460687PFlSFd","fadeoutTimer","innerHTML","top","hide","187216OwJsrv","stage","1292805SdOOhZ","enabled","741520OpXFdX","createElement","opacity","initEvent","display","96061vSYTXG"];return ri=function(){return x},ri()}class oo extends wt{constructor(e){const t=G0;super(),this.stage=e,this.domElement=document[t(504)]("div"),this[t(512)].classList.add("jtopo_tooltip"),this.stage.layersContainer.appendChild(this.domElement),this.initEvent(),this.hide()}disable(){this[G0(512)]&&this.domElement.remove()}[At(502)](){const e=At;this[e(512)]&&this[e(512)].parentNode==null&&this[e(500)].layersContainer.appendChild(this.domElement)}setHtml(e){const t=At;return this[t(512)][t(520)]=e,this.domElement}[At(506)](){const e=At,t=this,n=this[e(500)].inputSystem;n.on("mousedown",function(){t[e(522)]()}),n.on("mouseup",function(){t[e(522)]()}),n.on("mousemove",function(){n.mouseoverTarget==null&&t.fadeOut()})}[At(516)](e,t){const n=At;this.stopFade(),this[n(512)].style.display="block",this[n(512)].style[n(517)]=e+"px",this[n(512)][n(511)][n(521)]=t+"px",this.domElement.style.opacity="0.9"}[At(522)](){const e=At;this.stopFade(),this.domElement[e(511)][e(507)]="none"}stopFade(){this.fadeoutTimer!=null&&(clearInterval(this.fadeoutTimer),this.fadeoutTimer=null)}[At(514)](e=50){const t=At;if(this.fadeoutTimer!=null)return;let n=this;this[t(519)]=setInterval(function(){const s=t;n.domElement[s(511)].opacity-=.1,n[s(512)].style[s(505)]<=.1&&n.hide()},e)}}function G0(x,e){const t=ri();return G0=function(n,s){return n=n-500,t[n]},G0(x,e)}const Ee=vn;function oi(){const x=["editor","2739420UEnpZG","focus","2188482BkXUoa","textarea","279thiTnZ","text","21kXizqC","show","setValue","4259145TtCljY","isNode","setSize","key","hide","stage","toStageXY","value","createElement","height","style","350lCRPMA","50768weCFWP","positionToLocalPoint","7415uDYkgq","attachTo","22550200TzVuiZ","724860RDaGhV"];return oi=function(){return x},oi()}(function(x,e){const t=vn,n=x();for(;[];)try{if(-parseInt(t(462))/1*(-parseInt(t(459))/2)+parseInt(t(465))/3+-parseInt(t(467))/4+parseInt(t(476))/5+parseInt(t(469))/6*(parseInt(t(473))/7)+-parseInt(t(460))/8*(-parseInt(t(471))/9)+-parseInt(t(464))/10===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(oi,742166);function vn(x,e){const t=oi();return vn=function(n,s){return n=n-454,t[n]},vn(x,e)}class ao{constructor(e){const t=vn;this[t(466)]=e,this.stage=e[t(481)];let n=document[t(456)](t(470));n.classList.add("jtopo_input_textfield"),this.stage.layersContainer.appendChild(n);let s=this;n.onkeydown=function(i){s.onkeydown(i)},this.textarea=n}[Ee(463)](e,t){const n=Ee;let s={x:t.x-50,y:t.y};if(e[n(477)]){let i=e[n(461)](S.lt);s=e[n(454)](i.x,i.y);let r=_x(e.width,60,100),o=_x(e.height,60,100);this[n(478)](r,o)}else if(e.isLink)return;this.setValue(e.text),this[n(474)](s.x,s.y)}[Ee(475)](e){const t=Ee;this.textarea[t(455)]=e}setSize(e,t){const n=Ee;this[n(470)].style.width=e+"px",this[n(470)][n(458)][n(457)]=t+"px"}show(e,t){const n=Ee;this.textarea.style.display="block",this.textarea[n(468)](),e!=null&&(e=Math.max(0,e),t=Math.max(0,t),this.textarea[n(458)].left=e,this.textarea.style.top=t)}[Ee(480)](){this.textarea.style.display="none"}onkeydown(e){const t=Ee;let n=this.textarea;if(e[t(479)]=="Enter"&&(e.ctrlKey||e.metaKey)){let s=this.stage.inputSystem.target;if(s==null)return;s[t(472)]=n.value,n[t(458)].display="none",this.hide()}}}(function(x,e){for(var t=ir,n=x();[];)try{var s=parseInt(t(469))/1*(-parseInt(t(465))/2)+-parseInt(t(462))/3*(parseInt(t(468))/4)+parseInt(t(460))/5*(-parseInt(t(471))/6)+-parseInt(t(464))/7*(parseInt(t(466))/8)+parseInt(t(461))/9+-parseInt(t(459))/10*(parseInt(t(463))/11)+parseInt(t(470))/12*(parseInt(t(467))/13);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ai,319672);function ai(){var x=["10pkecLW","2313099knYzXA","331494sdIdea","97449hUjszy","2704142uJPPay","818EulFbj","8KBsoli","23723141uhtyEK","16mETfhL","923ejpkuJ","12TCIjeC","473208PNKlCw","450wqLyPA"];return ai=function(){return x},ai()}function ir(x,e){var t=ai();return ir=function(n,s){n=n-459;var i=t[n];return i},ir(x,e)}(function(x,e){for(var t=rr,n=x();[];)try{var s=-parseInt(t(492))/1+-parseInt(t(494))/2*(parseInt(t(496))/3)+parseInt(t(497))/4+parseInt(t(495))/5*(parseInt(t(493))/6)+-parseInt(t(500))/7+parseInt(t(499))/8*(-parseInt(t(498))/9)+-parseInt(t(491))/10*(-parseInt(t(501))/11);if(s===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(ci,928467);function rr(x,e){var t=ci();return rr=function(n,s){n=n-491;var i=t[n];return i},rr(x,e)}function ci(){var x=["173620qStqTv","36AAIdGu","10xLULWq","788845GJftkS","975993oZFJkQ","984816eYeTee","63aetZnH","570824iHbrBj","1988854UMlAyk","11SlPENw","23195170RFRTOX"];return ci=function(){return x},ci()}const Hc=Object.freeze(Object.defineProperty({__proto__:null,AENode:gs,Animation:Ui,AnimationSystem:Zi,ArcLink:F0,ArcShape:p0,ArrowShape:l0,AutoFoldLink:te,BezierCurveShape:f0,BezierLink:Y0,CircleNode:k0,CircleShape:Fn,Collection:R,Config:se,Cursor:cr,CurveLink:e0,CurveShape:d0,Debug:Qt,DefaultDarkTheme:Hi,DefaultLightTheme:de,DefaultZIndexs:i0,Direction:Te,DisplayObject:L,DomUtil:fe,Edge:ws,EffectSystem:tr,EllipseShape:Xn,Endpoint:Ct,EndpointFixedName:he,EndpointFixedPoint:Rt,EndpointFunction:cn,EndpointNearest:ue,EndpointSegment:y0,EventNames:it,EventTarget:wt,FlexionalLink:ye,FoldLink:O0,FontInfo:re,FontUtil:tx,ForceDirectLayout:Zr,Graph:j0,GraphSystem:$e,HandlerLayer:fn,HtmlImage:xo,ImageUtil:Tt,InputEvent:X,InputSystem:Xx,InputTextfield:ao,Intersect:sn,Keyboard:Ji,Layer:ot,LayerLocalDeep:Z0,Layout:bn,LayoutSystem:er,LineShape:u0,LinearGradient:je,Link:W,LinkHelper:L0,Node:M,NodeHelper:Yt,OBB:Bt,PI2:s0,Path:Gr,PathLink:qs,Point:C,PopupMenu:ro,Position:S,PositionInvertMap:lr,RadialGradient:g0,RatioNode:xi,RectDefaultPositions:qt,RectShape:Q0,Rectangle:z,ResourceLoader:$,Runtime:Fi,SelectedGroup:Vi,SerializerSystem:Me,Shape:A,Stage:Bs,StageLocalDeep:ui,StageMode:mt,Style:et,StylePattern:ce,StyleSystem:Ki,TextNode:$t,Theme:He,TipNode:R0,TipShape:Ae,Toolbar:Yi,Tooltip:oo,TopoEvent:vr,TopoPainter:eo,Transform:r0,VERSION:Sn,Vertext:Qe,VideoNode:W0,clickEvent:Ei,convertToEndpoint:Wt,copyKeyboardEvent:ax,copyMouseEvent:wr,dblclickEvent:Li,dragEndEvent:Mi,dragEvent:Ai,dropEvent:Bi,dropoutEvent:ji,dropoverEvent:Di,getClass:Wn,getEndpointNormal:Ix,getLineIntersectPoint:rn,getLineIntersectPoints:Wi,getNearestAnchorOnObjects:Tr,getNearestPointOnLines:bx,getNearestPointOnObjectsOBB:Lr,getNearestPositionName:Cr,getParallelLine:Pr,getParallelLines:Sr,getPointByEndpoint:We,getRectPositionDirection:pi,getRectPositionRotate:gi,isHorizontal:on,jtopo:dt,mousedownEvent:ki,mousedragEvent:kr,mouseenterEvent:Ti,mousemoveEvent:Pi,mouseoutEvent:Ci,mouseoverEvent:Oi,mouseupEvent:Si,newInstance:c0,pointProjectToLine:Or,randomColor:Qr,range:_x,regClass:hr,selectedEvent:zi,setProto:f,touchendEvent:vi,touchmoveEvent:cx,touchstartEvent:wi,unselectedEvent:Ni,util:kt,vec2:B},Symbol.toStringTag,{value:"Module"})),Fc=li;(function(x,e){const t=li,n=x();for(;[];)try{if(parseInt(t(423))/1*(parseInt(t(428))/2)+parseInt(t(427))/3+-parseInt(t(421))/4+-parseInt(t(425))/5*(parseInt(t(430))/6)+parseInt(t(426))/7+-parseInt(t(422))/8+parseInt(t(431))/9*(parseInt(t(429))/10)===e)break;n.push(n.shift())}catch{n.push(n.shift())}})(hi,607805);function li(x,e){const t=hi();return li=function(n,s){return n=n-421,t[n]},li(x,e)}let co=dt;Object[Fc(424)](co,Hc),delete co.jtopo;function hi(){const x=["115585oNrIXb","703724WwbPvJ","2479491xkmJMr","3218FsQidb","26370ZnxpMz","54qczXnL","1125nsaOWy","2803588TpAUcR","4818584VkssrQ","536MzINtE","assign"];return hi=function(){return x},hi()}p.AENode=gs,p.Animation=Ui,p.AnimationSystem=Zi,p.ArcLink=F0,p.ArcShape=p0,p.ArrowShape=l0,p.AutoFoldLink=te,p.BezierCurveShape=f0,p.BezierLink=Y0,p.CircleNode=k0,p.CircleShape=Fn,p.Collection=R,p.Config=se,p.Cursor=cr,p.CurveLink=e0,p.CurveShape=d0,p.Debug=Qt,p.DefaultDarkTheme=Hi,p.DefaultLightTheme=de,p.DefaultZIndexs=i0,p.Direction=Te,p.DisplayObject=L,p.DomUtil=fe,p.Edge=ws,p.EffectSystem=tr,p.EllipseShape=Xn,p.Endpoint=Ct,p.EndpointFixedName=he,p.EndpointFixedPoint=Rt,p.EndpointFunction=cn,p.EndpointNearest=ue,p.EndpointSegment=y0,p.EventNames=it,p.EventTarget=wt,p.FlexionalLink=ye,p.FoldLink=O0,p.FontInfo=re,p.FontUtil=tx,p.ForceDirectLayout=Zr,p.Graph=j0,p.GraphSystem=$e,p.HandlerLayer=fn,p.HtmlImage=xo,p.ImageUtil=Tt,p.InputEvent=X,p.InputSystem=Xx,p.InputTextfield=ao,p.Intersect=sn,p.Keyboard=Ji,p.Layer=ot,p.LayerLocalDeep=Z0,p.Layout=bn,p.LayoutSystem=er,p.LineShape=u0,p.LinearGradient=je,p.Link=W,p.LinkHelper=L0,p.Node=M,p.NodeHelper=Yt,p.OBB=Bt,p.PI2=s0,p.Path=Gr,p.PathLink=qs,p.Point=C,p.PopupMenu=ro,p.Position=S,p.PositionInvertMap=lr,p.RadialGradient=g0,p.RatioNode=xi,p.RectDefaultPositions=qt,p.RectShape=Q0,p.Rectangle=z,p.ResourceLoader=$,p.Runtime=Fi,p.SelectedGroup=Vi,p.SerializerSystem=Me,p.Shape=A,p.Stage=Bs,p.StageLocalDeep=ui,p.StageMode=mt,p.Style=et,p.StylePattern=ce,p.StyleSystem=Ki,p.TextNode=$t,p.Theme=He,p.TipNode=R0,p.TipShape=Ae,p.Toolbar=Yi,p.Tooltip=oo,p.TopoEvent=vr,p.TopoPainter=eo,p.Transform=r0,p.VERSION=Sn,p.Vertext=Qe,p.VideoNode=W0,p.clickEvent=Ei,p.convertToEndpoint=Wt,p.copyKeyboardEvent=ax,p.copyMouseEvent=wr,p.dblclickEvent=Li,p.dragEndEvent=Mi,p.dragEvent=Ai,p.dropEvent=Bi,p.dropoutEvent=ji,p.dropoverEvent=Di,p.getClass=Wn,p.getEndpointNormal=Ix,p.getLineIntersectPoint=rn,p.getLineIntersectPoints=Wi,p.getNearestAnchorOnObjects=Tr,p.getNearestPointOnLines=bx,p.getNearestPointOnObjectsOBB=Lr,p.getNearestPositionName=Cr,p.getParallelLine=Pr,p.getParallelLines=Sr,p.getPointByEndpoint=We,p.getRectPositionDirection=pi,p.getRectPositionRotate=gi,p.isHorizontal=on,p.jtopo=dt,p.mousedownEvent=ki,p.mousedragEvent=kr,p.mouseenterEvent=Ti,p.mousemoveEvent=Pi,p.mouseoutEvent=Ci,p.mouseoverEvent=Oi,p.mouseupEvent=Si,p.newInstance=c0,p.pointProjectToLine=Or,p.randomColor=Qr,p.range=_x,p.regClass=hr,p.selectedEvent=zi,p.setProto=f,p.touchendEvent=vi,p.touchmoveEvent=cx,p.touchstartEvent=wi,p.unselectedEvent=Ni,p.util=kt,p.vec2=B,Object.defineProperty(p,Symbol.toStringTag,{value:"Module"})});
