const execSync = require('child_process').execSync;
const modules = require('./modules').names || [];

console.log(`开始打包全部项目，总计(${modules.length})个：${modules.join(',')}`);

for( let [ i, item ] of new Map( modules.map( ( item, i ) => [ i, item ] ) )){
  console.log(`\n开始打包第${i + 1}个项目: ${item}`);
  execSync(`npm run build ${item}`);
  console.log(`第${i + 1}个项目打包完成`);
  if(i + 1 === modules.length){
    console.log(`所有项目打包成功successfully`);
  }
}
