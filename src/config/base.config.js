let userType = 0;
if (sessionStorage.hasOwnProperty('accessToken')) {
   userType =JSON.parse(sessionStorage.getItem('accessToken')).userType;
}else{
   userType =0;
}
const base = {
  //处理输入框前后为空数据
  dispose(val) {
    return val.replace(/(^\s*)|(\s*$)/g, "");
  },
  //信息提示框去重
  isShowPrompt() {
    let flag = false;
    if (!this.latestTime || new Date() - this.latestTime > 2000) {
      this.latestTime = new Date();
      flag = true;
    }
    return flag;
  },
  //当前账号权限
  power: userType, //0、集团账号，1、分支账号
  //当前分支机构账号ID
  current_client_id: ""
};
export default base;
