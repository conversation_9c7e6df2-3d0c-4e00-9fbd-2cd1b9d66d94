import Vue from "vue";

Vue.directive("page", {
  bind(el) {

    const firstPage = new Vue({
      render(h) {
        return h(
          "a",
          {
            class: "page-btn",
            on: {
              click: () => {
                el.__vue__.changePage(1);
              }
            }
          },
          el.__vue__.$t('server_home_page')
        );
      }
    }).$mount();
    el.insertBefore(firstPage.$el, el.firstChild);
    let pageOptions = el.querySelector(".ivu-page-options");
    const lastPage = new Vue({
      render(h) {
        let last = h(
          "a",
          {
            class: "page-btn",
            on: {
              click: () => {
                el.__vue__.changePage(el.__vue__.allPages);
              }
            }
          },
          el.__vue__.$t('server_home_last_page')
        );
        let total = h(
          "span",
          {
            class: "page-total"
          },
          el.__vue__.$t('server_total') + " " + el.__vue__.total + " " + el.__vue__.$t('server_strip')
        );
        return h("span", [last, total]);
      }
    }).$mount();
    el.insertBefore(lastPage.$el, pageOptions);
  }
});
