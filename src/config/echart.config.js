const econfig = {
  // 0 浅色 ， 1 深色
  dataZoom:{
    fillerColor:["#E4E7ED","rgba(2, 29, 54, 1)"],
    borderColor:["#DCDFE6","rgba(22, 67, 107, 1)"],
    handleStyle: {
        color: ["#C0C4CC" ,  "rgba(2, 67, 107, 1)"]
    },
    textStyle:{
        color:["#808695" , "#617ca5"]
    }
  },
  // 图列的颜色 
  legend:{
    // 时延
    delayColor:["#03B999","#00FFEE"],
    // 丢包
    lossColor:["#0290FD","#0290FD"],
    // 入流速
    flowInColor:["#9DD871","#478EE9"],
    // 出流速
    flowOutColor:["#AEBBFE","#F19149"],
    // 入流速利用率
    flowInRateColor:["#3CDE66","#00FFEE"],
    // 出流速利用率
    flowOutRateColor:["#88C4FF","#FE5C5C"],
  },
  // 趋势图的图形背景颜色
  areaStyle:{
    // 时延
    delayColor_0:["rgba(3,185,153,0.6)","rgba(0, 255, 238, 0.6)"],
    delayColor_8:["rgba(3,185,153,0.2)","rgba(0, 255, 238, 0.2)"],
    // 丢包
    lossColor_0:["rgba(2,144,253,0.6)","rgba(2, 144, 253, 0.6)"],
    lossColor_8:["rgba(2,144,253,0.2)","rgba(2, 144, 253, 0.2)"],
    // 入流速
    flowInColor_0:["rgba(157,216,113,0.8)","rgba(71,142,233, 0.8)"],
    flowInColor_8:["rgba(157,216,113,0.2)","rgba(71,142,233, 0.2)"],
    // 出流速
    flowOutColor_0:["rgba(174,187,254,0.8)","rgba(241,145,73, 0.8)"],
    flowOutColor_8:["rgba(174,187,254,0.2)","rgba(241,145,73, 0.2)"],
    // 入流速利用率
    flowInRateColor_0:["rgba(60, 222, 102, 0.8)","rgba(0,255,238, 0.8)"],
    flowInRateColor_8:["rgba(60, 222, 102, 0.2)","rgba(0,255,238, 0.2)"],
    // 出流速利用率
    flowOutRateColor_0:["rgba(136, 196, 255, 0.8)","rgba(254,92,92, 0.8)"],
    flowOutRateColor_8:["rgba(136, 196, 255, 0.2)","rgba(254,92,92, 0.2)"],
  },
  //echarts tooltip 配置
  tip: function(type, format) {
    let tip = {
      trigger: type === "axis" ? "axis" : type,
      backgroundColor: top.window.isdarkSkin==1 ? 'rgba(18,55,127,.8)':"rgba(255,255,255,1)",
      // axisPointer: {
      //    type: 'shadow',
      //    shadowStyle:{
      //        color: "rgba(234,237,243,0.7)",
      //    }
      // },
      borderColor: top.window.isdarkSkin==1 ? 'rgba(18,55,127,.8)':"#b4b9cc",
      borderRadius: 10,
      borderWidth: 1,
      confine: true,
      padding: 16,
      textStyle: {
        color: top.window.isdarkSkin==1 ? '#d1e4ff':"#515566",
        fontSize: 16,
        fontFamily: "微软雅黑",
        align: "left"
      }
    };
    if (format) {
      tip.formatter = format;
    }
    return tip;
  },
  //echarts axis  配置
  axis: function(fontSize, axisLine, splitLine, textColor, axisColor) {
    return {
      nameTextStyle: {
        color: textColor,
        fontSize: fontSize
      },
      axisLine: {
        show: axisLine,
        lineStyle: {
          type: "dashed",
          color: axisColor, //左边线的颜色
          width: "1" //坐标线的宽度
        }
      },
      splitLine: {
        show: splitLine,
        lineStyle: {
          type: "dashed",
          color: axisColor
        }
      },
      axisLabel: {
        show: true,
        margin: 20,
        textStyle: {
          color: textColor,
          fontSize: fontSize
        }
      },
      axisTick: {
        show: false
      }
    };
  },
  //
  placeHolderStyle: {
    normal: {
      label: {
        show: false,
        position: "outside"
      },
      labelLine: {
        show: false
      },
      color: top.window.isdarkSkin == 1 ? "#032A4D":"#F5F6FA",
      borderColor: top.window.isdarkSkin == 1 ? "#032A4D":"#F5F6FA",
      borderWidth: 0
    },
    emphasis: {
      color: "#032A4D",
      borderColor: "#032A4D",
      borderWidth: 0
    }
  }
};
export default econfig;
