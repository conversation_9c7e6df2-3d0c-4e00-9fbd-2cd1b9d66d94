.ivu-modal {
    .ivu-select-multiple .ivu-tag {
        max-width: 70%;
    }
    .sectionBox  {
        background: transparent !important;
        .section-body {
            background: transparent !important;
        }
        .section-top {
            background: unset !important;
        }
    }
    .ivu-input {
        height: 38px;
        line-height: 38px;
    }
    textarea.ivu-input {
        height: auto;
    }
    .ivu-date-picker .ivu-icon {
        line-height: 38px;
    }
    .ivu-input-prefix i, .ivu-input-suffix i {
        color: var(--time_picker_suffix_color,#5CA0D5) ;
    }
    .ivu-table-header thead tr th {
        padding: 0 !important;
    }
    .ivu-table td {
        height: 50px !important;
    }
    .ivu-page {
        padding: 16px 0;
    }
    .ivu-radio-wrapper-checked {
        .ivu-radio-inner {
            border-color:var(--reset_export_del_button_font_color,#05EEFF) !important;
        }

    }
    .btnChange {
        margin-bottom: 16px;
        margin-left: 10px;
    }
    .index-query-r {
        display: flex;
        height: 38px;
        margin-bottom: 16px;
        align-items: center;
        margin-left: 10px;
        .query-btn {
            margin-left: 10px;
        }

    }
    .title-lookBox {
        .title-name {
            margin-right: 20px;
        }
    }
    .ivu-modal-body {
        padding: 10px 20px 20px 20px !important;
    }
    .tab-page {
        padding: unset;
    }
    .daoChu-btn {
        width: 60px !important;
        height: 38px !important;
    }
    .title-name {
        margin-right: 20px;
    }
    .ivu-btn-info {
        background-color: var(--scrollbar_track_bg_color);
        border-color:var(--reset_export_del_button_font_color,#05EEFF) !important;
        color: var(--reset_export_del_button_font_color,#05EEFF) !important;
    }
    .ivu-btn-info:hover {
        background-color: var(--scrollbar_track_bg_color);
        border-color:var(--reset_export_del_button_font_color,#05EEFF) !important;
        color: var(--reset_export_del_button_font_color,#05EEFF) !important;
    }
}
.no-padding-modal {
    .ivu-modal {
        .ivu-modal-body {
            padding-top: unset !important;
        }

    }
  
   

    
  
   
}
.pading-40-modal {
    .ivu-form {
        margin-right: 24px;
    }
   
    
    
}
.map-import-modal {
    .import-item {
        display: flex;
        margin-bottom: 24px;
        align-items: center;
        .import-item-label {
           
            text-align: right;
        }
        .import-btn {
            color: var(--reset_export_del_button_font_color,#05EEFF);
            cursor: pointer;
            
            text-decoration: underline;  // 添加下划线
            text-underline-offset: 3px;
            text-decoration-thickness: 2px;
            cursor: pointer;
        }
    }
}

.section-modal {
    .sectionBox {
        
        padding: 0;
        .fn_box  {
            margin-bottom: 20px;
        }
        .section-body {
            .section-body-content {
                padding: 0;
            }
        }
       
        .ivu-select {
            height: 38px !important;
        }
        .ivu-select-input {
            height:34px;
            line-height: 34px;
        }
        .ivu-select-single .ivu-select-selection {
            height: unset;
        }
        .section-top .fn_item .fn_item_label {
           line-height: 1.5;
        }
    }
    .tool-btn button {
        margin-right: 0;
    }
    
   
}
.operation-modal {
    .ivu-modal-body {
        padding: 10px 40px 20px 20px !important;
    }
}
.msg-modal {
    .ivu-btn-info , .ivu-btn-info:hover {
        border-width: 1px;

    }
}
.checked-tag-modal {
    .ivu-modal .ivu-select-multiple .ivu-tag {
        max-width: 100px;
    }
}