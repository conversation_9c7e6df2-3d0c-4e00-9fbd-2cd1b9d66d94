<template>
  <div class="Fs-main" v-clickoutside="handleClose">
    <Dropdown
      trigger="custom"
      :visible="dropdownShow && searchList.length > 0"
      :placement="optionsData.placement"
      style="left:0px;width: 100%"
      @on-click="selectClick"
    >
      <i-input
        v-model="selectValue"
        type="text"
        placeholder="多型号用、号分割"
        :icon="focusShow ? 'ios-arrow-up' : 'ios-arrow-down'"
        @on-focus="inputFocus"
        @on-change="valueChange"
      >
      </i-input>
      <DropdownMenu slot="list">
        <DropdownItem
          v-for="(item, index) in searchList"
          :key="index"
          :name="item"
          :selected="selectArray.includes(item)"
          >{{ item }}</DropdownItem
        >
      </DropdownMenu>
    </Dropdown>
    <div class="select-no-data" v-show="searchList.length < 1">无匹配数据</div>
  </div>
</template>

<script>
const clickoutside = {
  // 初始化指令
  bind(el, binding, vnode) {
    function documentHandler(e) {
      // 这里判断点击的元素是否是本身，是本身，则返回
      if (el.contains(e.target)) {
        return false;
      }
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
        binding.value(e);
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.__vueClickOutside__ = documentHandler;
    document.addEventListener("click", documentHandler);
  },
  update() {},
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener("click", el.__vueClickOutside__);
    delete el.__vueClickOutside__;
  }
};
export default {
  name: "f-select",
  directives: { clickoutside },
  props: ["options", "multiple"],
  created() {
    this.optionsData = this.options;
    this.searchList = this.sourceList = this.optionsData.list;
  },
  data() {
    return {
      modelName: "f-select",
      optionsData: {},
      row: {
        contract_no: ""
      },
      selectArray: [],
      selectValue: "",
      focusShow: false, //是否获取焦点
      dropdownShow: false, //显示下拉框
      sourceList: [],
      searchList: []
    };
  },

  methods: {
    handleClose() {
      this.focusShow = false;
      this.dropdownShow = false;
    },
    //input获取焦点
    inputFocus() {
      this.focusShow = true;
      this.dropdownShow = true;
    },
    onClick() {
      this.classStyle = "Fs-root-show";
    },
    selectClick(item, index) {
      this.selectValue = "";
      if (this.multiple === "true") {
        if (this.selectArray.includes(item)) {
          this.selectArray.forEach(function(ArriItem, index, arr) {
            if (ArriItem == item) {
              arr.splice(index, 1);
            }
          });
        } else {
          this.selectArray.push(item);
        }
      } else {
        if (this.selectArray.includes(item)) {
          this.selectArray.forEach(function(ArriItem, index, arr) {
            if (ArriItem == item) {
              arr.splice(index, 1);
            }
          });
        } else {
          this.selectArray = [item];
        }

        this.handleClose();
      }

      for (let i = 0; i < this.selectArray.length; i++) {
        if (i === 0) {
          this.selectValue += this.selectArray[0];
        } else {
          this.selectValue += "、" + this.selectArray[i];
        }
      }
    },
    valueChange(value) {
      console.log(this.selectValue);
    }
  },
  beforeDestroy() {
    this.selectArray = [];
  }
};
</script>

<style scoped lang="less">
.Fs-main {
  width: 100%;
  position: relative;
  .select-no-data {
    width: 100%;
    margin: 5px 0;
    padding: 5px 0;
    background-color: #fff;
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    position: absolute;
    z-index: 900;
    text-align: center;
  }
  .ivu-dropdown-item-selected,
  .ivu-dropdown-item.ivu-dropdown-item-selected:hover {
    background: #f3f3f3;
  }
}
</style>
