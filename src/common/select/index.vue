<template>
  <section class="select-content" v-clickoutside="handleClose">
    <div class="select-input">
      <input
        type="text"
        :value="currentValue"
        :placeholder="placeholder"
        :class="[{ focus: focusShow },isbigscreen?'isbigscreenClass':'']"
        :readonly="readonly"
        :title="title"
        @input="inputChange"
        @focus="inputFocus"
        style="border-radius: 4px;"
      />
      <i class="ivu-icon ivu-icon-ios-arrow-down ivu-select-arrow"></i>
    </div>
    <div 
     class="select-dropdown" 
     :class="isbigscreen?'isbigscreenClass':''" 
     v-if="dropdownShow"
     >
      <div v-if="list.length > 0">
        <p
          v-for="(item, index) in list"
          :key="index"
          :class="styleIndex === index ? 'active' : ''"
          @click="dropdownClick(item, index)"
        >
          {{ item.label }}
        </p>
      </div>
      <div class="select-no-data" v-else>无匹配数据</div>
    </div>
  </section>
</template>

<script>
const clickoutside = {
  // 初始化指令
  bind(el, binding, vnode) {
    function documentHandler(e) {
      // 这里判断点击的元素是否是本身，是本身，则返回
      if (el.contains(e.target)) {
        return false;
      }
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
        binding.value(e);
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.__vueClickOutside__ = documentHandler;
    document.addEventListener("click", documentHandler);
  },
  update() {},
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener("click", el.__vueClickOutside__);
    delete el.__vueClickOutside__;
  }
};
export default {
  name: "index",
  directives: { clickoutside },
  props: {
    title: {
      type: String,
      default: ""
    },
    readonly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: "请选择"
    },
    list: {
      type: Array,
      default: function() {
        return [];
      }
    },
    isbigscreen:{
      type: Boolean,
      default: false
    },
    value: [Number, String, Array]
  },
  data() {
    return {
      currentValue: "",
      focusShow: false, //是否获取焦点
      dropdownShow: false, //显示下拉框
      styleIndex: null
    };
  },
  watch: {
    value: {
      handler(val) {
        if (typeof val === "string" && val === "") {
          this.styleIndex = null;
          this.currentValue = "";
          this.$emit("on-change", "");
        } else {
          if (val.length === 0) {
            this.styleIndex = null;
            this.currentValue = "";
            this.$emit("on-change", ["", ""]);
          } else {
            if (this.value.toString() != "") {
              let index = this.list
                .map(item => item.value.toString())
                .indexOf(val.toString());
              this.styleIndex = index;
              if (index > -1) {
                this.currentValue = this.list[index].label;
              }
            }
          }
        }
      },
      deep: true
    }
  },
  methods: {
    handleClose() {
      this.focusShow = false;
      this.dropdownShow = false;
    },
    //input获取焦点
    inputFocus() {
      this.focusShow = true;
      this.dropdownShow = true;
    },
    inputChange(event) {
      this.styleIndex = null;
      this.currentValue = event.target.value;
      this.$emit("on-input-change", event.target.value);
    },
    dropdownClick(item, index) {
      this.styleIndex = index;
      this.currentValue = item.label;
      this.$emit("on-change", item.value);
      this.focusShow = false;
      this.dropdownShow = false;
    }
  },
  mounted() {
    if (typeof this.value === "string" && this.value === "") {
      this.styleIndex = null;
      this.currentValue = "";
      this.$emit("on-change", "");
    } else {
      if (this.value.length === 0) {console.log(1)
        this.styleIndex = null;
        this.currentValue = "";
        this.$emit("on-change", ["", ""]);
      } else {
        if (this.value.toString() != "") {
          let index = this.list
            .map(item => item.value.toString())
            .indexOf(this.value.toString());
          this.styleIndex = index;
          if (index > -1) {
            this.currentValue = this.list[index].label;
          }
        }
      }
    }
  }
};
</script>
<style scoped lang="less">
.select-content .isbigscreenClass{
  color: rgb(209, 228, 255) !important;
  background: rgb(6, 50, 77) !important;
}
.select-content {
    height: inherit;
    border: 1px solid transparent !important;
    border-radius: 3px !important;
    background-clip: padding-box, border-box !important;
    background-origin: padding-box, border-box !important;
    background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
</style>
