export const getRandomColor = function () {
    var str="#";
    var arr=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];
    for(var i=0;i<6;i++){
        var num=parseInt(Math.random()*16);
        str+=arr[num];
    }
    return str;
}


export const getRandomRgbaColor = function () {
    const r = Math.floor(Math.random() * 256); // 红色通道，较低值
    const g = Math.floor(Math.random() * 256); // 绿色通道，较低值
    const b = Math.floor(Math.random() * 256); // 蓝色通道，较低值
    const a = (Math.random() * 0.4 + 0.2).toFixed(2); // 透明度在0.2到0.6之间
  
    // var str= `rgba(${r}, ${g}, ${b}, ${a})`;
    var str= `rgba(${r}, ${g}, ${b}, 0.2)`;
    console.log("color==" , str)
    return  str;
}

