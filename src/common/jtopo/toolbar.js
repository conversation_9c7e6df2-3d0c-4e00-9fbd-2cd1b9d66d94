const lan = require('../../language')
/**
 * jtopo工具栏
 */
import $ from "./jquery.min.js";
import "./jtopo-s.js";

export function creatCanvas(thisId){


}

/**
 * 页面工具栏
 * 必须调取
 * 参数：stage对象，容器id，是否显示工具栏
 */
export function showJTopoToobar(stage,dataList){

	var toobarDiv = ''
		+'&nbsp;&nbsp;<input type="button" id="shuxingButton" value=lan.getLabel("src.treeShape")/>'
		+'&nbsp;&nbsp;<input type="button" id="xingxingButton" value=lan.getLabel("src.star")/>'
		+'&nbsp;&nbsp;<input type="button" id="centerButton" value=lan.getLabel("src.CenterDisplay")/>'
		+'<input type="button" id="fullScreenButton" value=lan.getLabel("src.full-screenDisplay")/>'
		+'<input type="button" id="zoomOutButton" value=lan.getLabel("src.magnify") />'
		+'<input type="button" id="zoomInButton" value=lan.getLabel("src.zoomOut") />'
		+'&nbsp;&nbsp;<input type="radio" name="modeRadio" value="select" id="r2"/><label for="r2"> 框选</label>'
		+'&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" id="zoomCheckbox" checked="checked"/><label for="zoomCheckbox">鼠标缩放</label>'
		+'&nbsp;&nbsp;<input type="text" id="findText" style="width: 100px;" value="" onkeydown="enterPressHandler(event)">'
		+ '<input type="button" id="findButton" value=lan.getLabel("src.Inquire")>'
		+'&nbsp;&nbsp;<input type="button" id="exportButton" value=lan.getLabel("src.ExportPNG")>';
	$('#jtopo_toolbar').html(toobarDiv);
	// 工具栏按钮处理
	$("input[name='modeRadio']").click(function(){
		stage.mode = $("input[name='modeRadio']:checked").val();
		if($(`input[name='modeRadio']`).is(':checked')){
			stage.mode = 1.2; // 设置鼠标缩放比例
		}else{
			stage.mode = null; // 取消鼠标缩放比例
		}
	});
	$('#shuxingButton').click(function(){
		stage.shuxingButton();
	});
	$('#xingxingButton').click(function(){
		stage.xingxingButton();
	});
	$('#centerButton').click(function(){
		stage.centerAndZoom(); //缩放并居中显示
	});
	$('#zoomOutButton').click(function(){
		stage.zoomOut();
	});
	$('#zoomInButton').click(function(){
		stage.zoomIn();
	});
	$('#cloneButton').click(function(){
		stage.saveImageInfo();
	});
	$('#exportButton').click(function() {
	    stage.saveImageInfo();
	});
	$('#printButton').click(function() {
	    stage.saveImageInfo();
	});
	$('#zoomCheckbox').click(function(){
		if($('#zoomCheckbox').is(':checked')){
			stage.wheelZoom = 1.2; // 设置鼠标缩放比例
		}else{
			stage.wheelZoom = null; // 取消鼠标缩放比例
		}
	});
	$('#fullScreenButton').click(function(){
		runPrefixMethod(stage.canvas, "RequestFullScreen")
	});

	window.enterPressHandler = function (event){
		if(event.keyCode == 13 || event.which == 13){
			$('#findButton').click();
		}
	};

	// 查询
	$('#findButton').click(function(){
		var text = $('#findText').val().trim();
		//var nodes = stage.find('node[text="'+text+'"]');
		var scene = stage.childs[0];
		var nodes = scene.childs.filter(function(e){
			return e instanceof JTopo.Node;
		});
		nodes = nodes.filter(function(e){
			if(e.text == null) return false;
			return e.text.indexOf(text) != -1;
		});

		if(nodes.length > 0){
			var node = nodes[0];
			node.selected = true;
			var location = node.getCenterLocation();
			// 查询到的节点居中显示
			stage.setCenter(location.x, location.y);

			function nodeFlash(node, n){
				if(n == 0) {
					node.selected = false;
					return;
				};
				node.selected = !node.selected;
				setTimeout(function(){
					nodeFlash(node, n-1);
				}, 300);
			}
			// 闪烁几下
			nodeFlash(node, 6);
		}
	});
	return stage;
}

var runPrefixMethod = function(element, method) {
	var usablePrefixMethod;
	["webkit", "moz", "ms", "o", ""].forEach(function(prefix) {
		if (usablePrefixMethod) return;
		if (prefix === "") {
			// 无前缀，方法首字母小写
			method = method.slice(0,1).toLowerCase() + method.slice(1);
		}
		var typePrefixMethod = typeof element[prefix + method];
		if (typePrefixMethod + "" !== "undefined") {
			if (typePrefixMethod === "function") {
				usablePrefixMethod = element[prefix + method]();
			} else {
				usablePrefixMethod = element[prefix + method];
			}
		}
	}
);

return usablePrefixMethod;
};
