const lan = require('./language')
const sm2 = require('sm-crypto').sm2;
// 密码解密
function decrypt(val){
    if (val){
        // 秘钥解密
        console.log(lan.getLabel("src.CBD")+val);
        const privateKey ="00e7b2c4ee0fde539b5edd401032e1df70885c5f2831211caca3cea3549d59be32";
        let decryptPassw = sm2.doDecrypt(val,privateKey,1); // 解密结果
        console.log(lan.getLabel("src.DecryptedContent:")+decryptPassw);
        return decryptPassw;
    }else{
        return null
    }

}

// 密码加密
function encryption(val){
    if (!val){
        return null
    }
    // 公钥加密
    const publicKey = "047d6d03c6dc9d812f0f5178a0e037adf834d968ebefcbf97a97c24233927130fde6398b8436c18f38210b0cbf8313f081dbbe764d26807549faa2536c27f9f4ef";
    let encryptionPassw = sm2.doEncrypt(val, publicKey, 1); // 加密结果 
    return encryptionPassw;
}

export default {
    decrypt,
    encryption
}
