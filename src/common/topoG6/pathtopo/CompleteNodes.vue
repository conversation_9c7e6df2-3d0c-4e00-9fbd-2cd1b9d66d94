<template>
  <div
    ref="modalBox"
    :class="[currentSkin == 1 ? 'complete-nodes' : 'complete-nodes-light']"
  >
    <div class="modal-container">
      <!-- 图形盒子 -->
      <div class="modal-title">
        <div>
          <span class="title-text">{{ $t("topo_modal_title") }}</span>
          <span class="title-ip">{{ "(" + sourceIp + ")" }}</span>
        </div>
        <div @click="closeNodeModal">
          <Icon type="ios-close" size="32" color="#00FFEE" />
        </div>
      </div>
      <div id="completeContainer"></div>

      <!-- /图形盒子 -->
    </div>
  </div>
</template>

<script>
import G6 from '@antv/g6'
export default {
  props: ['completeNodeData','containerWidth','containerHeight'],
  data() {
    return {
      currentSkin:sessionStorage.getItem('dark') || 1,
      graph:null,
      topWidth:0,
      topHeight:0,
       nodeData: {
          nodes:[ {
            id: 'node1',
            x: 100,
            y: 200,
          },
          {
            id: 'node2',
            x: 300,
            y: 200,
          },],
          edges:[
             // 表示一条从 node1 节点连接到 node2 节点的边
          {
            source: 'node1',
            target: 'node2',
          },

          ],
 

  },
      
      
    }
  },
  watch: {
    completeNodeData: {
      handler(newVal) {
        console.log(newVal,'newVal')
         if (newVal) {
        this.handleNodes()
        this.handleLink()
        if (this.graph) {
          this.graph.data(this.nodeData)
          this.graph.render()
        }
      }
      
        
       
        
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    sourceIp() {
      return this.completeNodeData?.sourceIp || '--'
    }
  },
  methods: {
     // 初始化G6（横向纵向）
  initG6() {
     // Wait for container to be ready
      const container = document.getElementById('completeContainer');
      if (!container) return;
       const box = this.$refs.modalBox;
    if (box) {
      this.topWidth = box.offsetWidth;
      this.topHeight = box.offsetHeight;
    }
     
    
    let obj = {
      
      container: container,
      width:this.topWidth - 32,
      height:this.topHeight  - 42 - 46,
      // width:500,
      // height:500,
      
        defaultEdge: { // Add default edge config
          type: 'line',
        },
     
        modes: {
             default: ['drag-canvas', 'zoom-canvas', 'drag-node'], // 同时开启画布拖拽、缩放和节点拖拽
           
                  },
         fitView: true,
          fitViewPadding: 10,
          animate: true,

    }
   
     this.graph = new G6.Graph(obj)
    //  console.log(this.nodeData,'this.nodeData')
    // 监听节点点击事件
     this.graph.on('node:click', (evt) => {
    const node = evt.item;
    const data = node.getModel().data;
    debugger
    // console.log('Clicked node:', model);
     let params = {
          target:data.id,
          targetIp:data.nodeId,
          sourceIp:this.completeNodeData.nodeList[0].nodeId,
          curIp:data.nodeId,
          preIp:this.completeNodeData.nodeList[0].nodeId,
          linkIds:data.linkIds,
        }

    // You can emit an event or handle the click however you need
    this.$emit('getTaskList', params);
  });

   

         this.graph.data(this.nodeData) // 数据映射
        this.graph.render() // 渲染
       

  },
        // 处理节点
    handleNodes() {
    
       this.nodeData.nodes = []
        let nodeList = this.completeNodeData?.nodeList || []; // 节点数据
        nodeList.forEach((item, index) => {
          // 区分横向布局竖向布局节点位置
          let newX = item.locX
          let newY = item.locY
          if(index === 0) {
            newX = 0
            newY = 0
          }
          let postion = [ [0, 0.5], [1, 0.5]]
          
            // 区分横向布局竖向布局节点位置/
            // 区分横向竖向节点与边练节点位置

        
            let idx = item.id.indexOf('_') + 1
          
            
          
            let text = ''
            let showType = this.completeNodeData.showType
            
            // 判断如果有aliases就两排显示label
            if (showType === "all") {
          text = `\n${item.value}\n${item.aliases || ""}`;
        } else if (showType === this.$t("snmp_task_device_name")) {
          text = `\n${item.aliases || ""}`;
        } else if (showType === this.$t("comm_topo_device_ip")) {
          text = `\n${item.value || ""}`;
        }
            // if(item.aliases != '' && item.aliases != null) {
            //   text = item.value + '\n' + item.aliases
               
            // }else {
            //   text = item.value
            // }
            let obj = {}
          
                    // 匹配图片
            let imgUrl = this.handleImage(item)
              obj = {
                id:item.id.slice(idx),
                label:text,
                type:'image',
                img: imgUrl,
                 x:newX,
                y:newY,
                // anchorPoints:postion,
                size:[15,15],
                anchorPoints: [
                    [0.5, 0.5],   // 中心点
                ],
                 
                data:item,
                labelCfg: {           // 标签配置属性
                position: 'bottom',
                offset:5,
                // 标签的属性，标签在元素中的位置
                style: {            // 包裹标签样式属性的字段 style 与标签其他属性在数据结构上并行
                fontSize: 8,      // 标签的样式属性，文字字体大小
                fill:this.currentSkin == 1 ? '#fff' : '#515A6E'
                }
                }

            }
            
              
            // console.log(index)
            this.nodeData.nodes.push(obj)
        })
        // console.log(this.nodeData,'节点数据id')

    },
        // 处理连线
    handleLink() {
        // 用来装连线数组
        this.nodeData.edges = []
        let arr = []
        let linkList = this.completeNodeData?.linkList || []
       linkList.forEach((item,index) => {
            // console.log(item,index)
            let linkColor = this.handleLinkColor(item)
            // let linkLabel = this.handleLinkLabel(item)
          
             let idx = item.source.indexOf('_') + 1
             let obj = {
                source: item.source.slice(idx),
                target:item.curIp,
                data:item,
                style: {
                  stroke: linkColor,
                  lineWidth: 0.5
                   
                  
                },
                 
              
                
            }
            // 虚线逻辑
            if(item.destType !=null &&  item.destType == 0) {
             obj.style.lineDash = [4, 4]

            }
            // 缩略虚线逻辑
            if(item.scalingSplicingPoint === 1) {
               obj.style.lineDash = [4,1, 4]

            }
            arr.push(obj)
           
              
        })
        // console.log(arr,this.nodeData)
        this.nodeData.edges = arr
    

},
    handleImage(data) {
        let {
        deviceManageInconDeviceType,
        twoDeviceType,
        inconType,
        inconDeviceType,
        locX,
        locY,
        width = 15,
        height = 15,
        aliases = "",
        value,
        type,
        state,
        devType,
        interruptShowGrayNode,
        id,
        faultLink,
        faultIds,
        faultDesc,
        virtualNode,
        linkIds,
        despLinkIds,
        childList = []
      } = data;
      let showWarning = this.completeNodeData.showWarning
      let configData = this.completeNodeData.configData
         const showState = showWarning ? state : null;
        // 节点状态字段对应
      let statusType = 0;
      if (showState == null || showState == "") {
        statusType = 0;
      } else if (showState == 1) {
        statusType = 1;
      } else if (showState == 2 || showState == 3) {
        statusType = 2;
      } else {
        statusType = 3;
      }
       let url = null
         if (inconDeviceType) {
        configData.deviceTypeIconManageConfigures.forEach((item) => {
          if (item.deviceTypeId == inconDeviceType) {
            if (interruptShowGrayNode && interruptShowGrayNode == 1 && showWarning) {
              //    debugger
              item.types.forEach((item3) => {
                if (item3.type === 3) {
                  url = item3.image;
                }
              });
            } else {
              item.types.forEach((item2) => {
                if (item2.type == statusType) {
                  url = item2.image;
                  // // console.log(url);
                }
              });
            }
          }
        });

        if (url === null) {
          // 先判断deviM。。。。 与设备匹配，没有，匹配上为空往下进行
          if (deviceManageInconDeviceType) {
            configData.deviceTypeIconManageConfigures.forEach((item) => {
              if (item.deviceTypeId == deviceManageInconDeviceType) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  showWarning
                ) {
                  //    debugger
                  item.types.forEach((item3) => {
                    if (item3.type === 3) {
                      url = item3.image;
                    }
                  });
                } else {
                  item.types.forEach((item2) => {
                    if (item2.type == statusType) {
                      url = item2.image;
                      // // console.log(url);
                    }
                  });
                }
              }
            });
            if (url === null) {
              if (twoDeviceType) {
                let findR = configData.inUseIconManageConfigures.find(
                  (item) => item.code === "SLN"
                );
                if (findR) {
                  if (
                    interruptShowGrayNode &&
                    interruptShowGrayNode == 1 &&
                    showWarning
                  ) {
                    let findR2 = findR.types.find((item) => item.type === 3);
                    url = findR2.image;
                  } else {
                    let findR3 = findR.types.find((item) => item.type === statusType);
                    url = findR3.image;
                  }
                }
              } else {
                configData.inUseIconManageConfigures.forEach((item) => {
                  if (item.deviceTypeId == inconType) {
                    if (
                      interruptShowGrayNode &&
                      interruptShowGrayNode == 1 &&
                      showWarning
                    ) {
                      item.types.forEach((item3) => {
                        if (item3.type === 3) {
                          url = item3.image;
                        }
                      });
                    } else {
                      item.types.forEach((item2) => {
                        if (item2.type == statusType) {
                          url = item2.image;
                        }
                      });
                    }
                  }
                });
              }
            }
          } else {
            if (twoDeviceType) {
              let findR = configData.inUseIconManageConfigures.find(
                (item) => item.code === "SLN"
              );
              if (findR) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  showWarning
                ) {
                  let findR2 = findR.types.find((item) => item.type === 3);
                  url = findR2.image;
                } else {
                  let findR3 = findR.types.find((item) => item.type === statusType);
                  url = findR3.image;
                }
              }
            } else {
              configData.inUseIconManageConfigures.forEach((item) => {
                if (item.deviceTypeId == inconType) {
                  if (
                    interruptShowGrayNode &&
                    interruptShowGrayNode == 1 &&
                    showWarning
                  ) {
                    item.types.forEach((item3) => {
                      if (item3.type === 3) {
                        url = item3.image;
                      }
                    });
                  } else {
                    item.types.forEach((item2) => {
                      if (item2.type == statusType) {
                        url = item2.image;
                      }
                    });
                  }
                }
              });
            }
          }

          // 如果没有  二层节点不为空 二层节点   为空下面的逻辑

     
        }
      } else {
        // debugger
        // 先判断deviM。。。。 与设备匹配，没有，匹配上为空往下进行
        if (deviceManageInconDeviceType) {
          configData.deviceTypeIconManageConfigures.forEach((item) => {
            if (item.deviceTypeId == deviceManageInconDeviceType) {
              if (
                interruptShowGrayNode &&
                interruptShowGrayNode == 1 &&
                showWarning
              ) {
                //    debugger
                item.types.forEach((item3) => {
                  if (item3.type === 3) {
                    url = item3.image;
                  }
                });
              } else {
                item.types.forEach((item2) => {
                  if (item2.type == statusType) {
                    url = item2.image;
                    // // console.log(url);
                  }
                });
              }
            }
          });

          if (url == null) {
            configData.inUseIconManageConfigures.forEach((item) => {
              if (item.deviceTypeId == inconType) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  showWarning
                ) {
                  item.types.forEach((item3) => {
                    if (item3.type === 3) {
                      url = item3.image;
                    }
                  });
                } else {
                  item.types.forEach((item2) => {
                    if (item2.type == statusType) {
                      url = item2.image;
                    }
                  });
                }
              }
            });
          }
        } else {
          configData.inUseIconManageConfigures.forEach((item) => {
            if (item.deviceTypeId == inconType) {
              if (
                interruptShowGrayNode &&
                interruptShowGrayNode == 1 &&
                showWarning
              ) {
                item.types.forEach((item3) => {
                  if (item3.type === 3) {
                    url = item3.image;
                  }
                });
              } else {
                item.types.forEach((item2) => {
                  if (item2.type == statusType) {
                    url = item2.image;
                  }
                });
              }
            }
          });
        }
      }
      return "data:image/png;base64," + url

    },
     handleLinkColor(item) {
      let { state,interruptShowGrayNode } = item;
      let showWarning = this.completeNodeData.showWarning
      let configData = this.completeNodeData.configData
      // 中断颜色
      let breakLineColor = configData.breakLineColor;
      // 正常颜色
      let normalLineColor = configData.normalLineColor;
      // 裂化颜色
      let degradationLineColor = configData.degradationLineColor;
      // 未知颜色
      let unknownLineColor = configData.unknownLineColor;
       let color = normalLineColor;
       if (showWarning) {
        if (interruptShowGrayNode == 1) {
          // 未知
          color = unknownLineColor;
        }else{
          if (state == 1) {
            // 中断
            color = breakLineColor;
          } else if (state == 2 || state == 3) {
            // 劣化
            color = degradationLineColor;
          } else if (interruptShowGrayNode == 1) {
            // 未知
            color = unknownLineColor;
          }
        }
      }

      return color

    },
  
   
    closeNodeModal() {
      this.$emit('closeNodeModal')

    }
  },
  created() {
    console.log('created')
  },
  mounted() {
    console.log(this.completeNodeData,'completeNodeData')
    this.$nextTick(() => {
    this.handleNodes()
    this.handleLink()
    this.initG6()
  })
  },
  beforeDestroy() {
    this.graph.destroy()
  }
  

}
</script>

<style scoped lang='less'>
.complete-nodes {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9000;
  background-color: rgba(55, 55, 55, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  .modal-container {
    width: 95%;
    height: 95%;
    padding: 0 16px 16px 16px;
    z-index: 9001;
    background-color: #08101a;
    display: flex;
    flex-direction: column;

    .modal-title {
      height: 42px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-text {
        font-size: 16px;
        color: #00ffee;
        font-weight: bold;
        margin-right: 10px;
      }
    }
    #container {
      width: 100%;
      flex: 1;
      // background-color: pink;
    }
  }
}
.complete-nodes-light {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9000;
  background-color: rgba(55, 55, 55, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  .modal-container {
    width: 95%;
    height: 95%;
    padding: 0 16px 16px 16px;
    z-index: 9001;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    .modal-title {
      height: 42px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-text {
        font-size: 16px;
        color: #0290fd;
        font-weight: bold;
        margin-right: 10px;
      }
    }
    #container {
      width: 100%;
      flex: 1;
      // background-color: pink;
    }
  }
}
</style>
