<template>
  <Modal
    sticky
    :value="linkDetailShow"
    :width="modalWidth"
    class="index-modal"
    :styles="{ top: '100px' }"
    :title="$t('peertopeer_path_information')"
    draggable
    :mask="true"
    @on-cancel="cancel"
    @on-visible-change="visiblechange"
    :footer-hide="true"
  >
    <!-- 标题 -->
    <div slot="header">
      <span class="title-name">{{ $t("nodequality_Link_details") }}</span>
      <span class="title-content"> {{ modalTitle }}</span>
    </div>
    <!-- /标题 -->
    <!-- 表格 -->
    <Table
      ref="tableListModal"
      stripe
      max-height="250"
      :columns="columnsModal"
      :loading="ModalTableLoading"
      :data="filtabListModal"
      class="modalTable modalTableover"
      :no-data-text="
        ModalTableLoading
          ? ''
          : tabListModal.length > 0
          ? ''
          : currentSkin == 1
          ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
            $t('common_No_data') +
            '</p></div>'
          : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
            $t('common_No_data') +
            '</p></div>'
      "
      size="small"
    >
    </Table>
    <!-- /表格 -->
    <!-- 分页 -->
    <div class="tab-page">
      <Page
        v-page
        class="infoListPage"
        :current="page.pageNum"
        :page-size="page.pageSize"
        :total="page.total"
        @on-page-size-change="tabParamPageSizeChange"
        :page-size-opts="[10, 50, 100, 200, 500, 1000]"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChangeModal"
        show-total
        show-sizer
      >
      </Page>
    </div>

    <!-- /分页 -->
    <section ref="indexEvent">
      <!-- 路径tuopu -->

      <div class="index-title">
        {{ $t("probetask_path_topology_tip") }}（{{
          this.routeCode ? this.routeCode : "--"
        }})
      </div>
      <!-- <div class="index-flowChart"> -->
      <topology-item
        :data="topologyList"
        :linkData="linkData"
        ref="topologyChart"
        @on-click="topologyClick"
      ></topology-item>
      <!-- </div> -->
      <div v-show="pathShow" class="index-query-r">
        <label>{{ $t("probetask_date") }}</label>

        <DatePicker
          format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          :options="timeOptions"
          v-model="timeRange"
          :editable="false"
          :clearable="false"
          style="width: 330px"
          :confirm="false"
          @on-change="dateChange"
        >
        </DatePicker>

        <Button
          class="query-btn"
          icon="ios-search"
          type="primary"
          @click="indexQueryClick"
        ></Button>
      </div>

      <div class="index-title">
        {{ $t("probetask_indicator_trends_tip") }}
      </div>
      <div class="index-line-box">
        <!--趋势图-->
        <div class="lookBox" style="position: relative; margin-top: 10px">
          <div
            class="title"
            style="position: absolute; left: 0; right: 0; top: -10px"
          >
            {{
              this.echartLookParama2.preNodeIp.includes("-9999")
                ? this.echartLookParama2.preNodeIp.split("_")[1]
                : this.echartLookParama2.preNodeIp
            }}————>{{
              this.echartLookParama2.nodeIp.includes("-9999")
                ? this.echartLookParama2.nodeIp.split("_")[2]
                : this.echartLookParama2.nodeIp
            }}
          </div>
          <div
            class="title"
            v-if="echart2.show"
            style="position: absolute; left: 0; right: 0; top: 45%"
          >
            {{ flowData.devName
            }}{{ flowData.interfaceName ? "—" + flowData.interfaceName : ""
            }}{{ flowData.interfaceIp ? "—" + flowData.interfaceIp : "" }}
          </div>
          <div class="contain" v-if="!topoShow">
            <div
              ref="probetask-delayLoss"
              id="probetask-delayLoss"
              class="echartStyle"
              :style="'height:' + height + 'px'"
            ></div>
          </div>
          <div
            :class="{
              table_empty: currentSkin == 1,
              table2_empty: currentSkin == 0,
            }"
            v-else
          >
            <p class="emptyText">{{ $t("common_No_data") }}</p>
          </div>
        </div>
      </div>

      <div v-show="!pathShow">
        <PathList
          ref="pathList"
          :taskId="indexData.id"
          :rowData="rowData"
          :startTime="startDay"
          :endTime="endDay"
        >
        </PathList>
      </div>
    </section>
  </Modal>
</template>

<script>
const pointGreen =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC";
const pointRed =
  "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC";
import axios from "axios";
import Qs from "qs";
import topologyItem from "@/common/flowChart/topologyX.vue";
import timeLine from "@/common/echarts/time-line.vue";
import echartFn from "@/common/mixins/echartFun";
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/dataZoom");
require("echarts/lib/component/markLine");
import moment from "moment";
import eConfig from "@/config/echart.config.js";
import ipv6Format from "@/common/ipv6Format";
export default {
  name: "indexItem",
  mixins: [echartFn],
  components: {
    topologyItem,
    PathList: () => import("./pathList.vue"),
    PathChart: () => import("./PathChart.vue"),
    // timeLine
  },
  props: {
    tabListModal: {
      type: Array,
      required: true,
    },
    indexData: {
      type: Object,
      default: function () {
        return {};
      },


    },
    rowData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    linkDetailShow: {
      type: Boolean,
      default: false,
    },
    isSpecial: Boolean,
    High: Boolean,
    preIp:String,
    tabsId:{
      type:[String, Number],
      default:''
    },
  },
  computed: {
    modalTitle() {
      // 处理虚拟节点
      let preNodeIp = this.usea.preNodeIp;
      // let nodeIp = this.usea.nodeIp;
      // 趋势图专用参数
      let nodeIp = this.usea.nodeIpTrend ? this.usea.nodeIpTrend : this.usea.nodeIp;
      console.log("this.usea------------" , this.usea)
      if (preNodeIp.includes('-9999')) {
        const parts = preNodeIp.split("_");
        preNodeIp = parts[1];
      }
      if (nodeIp.includes('-9999')) {
        const parts = nodeIp.split("_");
        nodeIp = parts[2];
      }
      return "(" + preNodeIp + "--" + nodeIp + ")";
    },
    linkData() {
      let obj = {
        linkId: this.tabsId,
      };
      console.log("thisLInkData:::::" + this.tabsId)
      return obj;
    },
    btnImg() {
      if (this.pathShow) {
        return {
          img1: "@/assets/btn-img/icon-tu01.png",
          img2: "@/assets/btn-img/icon-b02.png",
        };
      } else {
        return {
          img1: "@/assets/btn-img/icon-tu02.png",
          img2: "@/assets/btn-img/icon-b01.png",
        };
      }
    },
  },
  data() {
    return {
        currentSkin: sessionStorage.getItem('dark') || 1,
       modalWidth:1290,

      page: {
        pageSize: 10,
        pageNum: 1,
        total: null,
      },
      // 路径图查询参数
      selectPingQuery: {},
      usea: {
        preNodeIp: "",
        nodeIp: "",
        // 趋势图专用参数
        nodeIpTrend: "",
        sourceIp: "",
        topoId: "",
        targetIp: "",
      },
      filtabListModal: [],
      deviceTypeList: [],
      // 表格loading效果
      ModalTableLoading: false,
      // 表格数据
      columnsModal: [
        // 选择
        {
          title: this.$t("but_choose"),
          width: 70,
          className: "bgColor",
          // slot: 'action'
          render: (h, params) => {
            let id = params.row.taskId;
            let flag = false;
            if (this.taskId === id) {
              flag = true;
            } else {
              flag = false;
            }
            let self = this;

            return h("div", [
              h("Radio", {
                style: {
                  marginRight: 0,
                },
                props: {
                  value: flag,
                },
                on: {
                  "on-change": () => {
                    // console.log('params==>',params.row);
                    this.taskId = params.row.taskId;
                    this.selectPingQuery = params.row;
                    // 单击查询设置为空
                    this.usea.nodeIpTrend = "";
                    // this.usea.nodeIp = this.selectPingQuery.showDestIp;
                    console.log(" this.usea.linkNodePairVos--------------" ,  this.usea.linkNodePairVos );
                    // let linkNodePairVo = this.usea.linkNodePairVos.find(item => item.linkId == params.row.routeId);
                    let linkNodePairVo = this.querylinkNodePairVos(params.row.routeId);
                    // debugger
                    this.usea.nodeIp = linkNodePairVo.preIp;
                    console.log();
                    // this.routeCode = this.routeCode;
                    this.getPathChartData(2);
                  },
                },
              }),
            ]);
          },
        },
        // 路径编号
        {
          title: this.$t("probetask_num_no"),
          key: "displayId",
        },
        // 探针IP
        {
          title: this.$t("comm_probe_ip"),
          key: "showSourceIp",
          width:250,
          render: (h, params) => {
            let str = params.row.showSourceIp;
             str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;

                 let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },

        },
        // 拨测类型
        {
          title: this.$t("task_dial_type"),
          key: "taskType",
          render: (h, params) => {
            let str = params.row.configType,
              text = "--";
            let routePath = params.row.routePath || "";
            switch (str * 1) {
              // case 0:
              //     text=' 集团遍历';
              //     break;
              case 1:
                text = " UDP Trace";
                break;
              case 2:
                text = "ICMP Ping";
                break;
              case 3:
                text =
                  routePath.indexOf("-") >= 0
                    ? routePath.split("-")[1] == 1
                      ? "UDP Ping"
                      : routePath.split("-")[1] == 2
                      ? "ICMP Ping"
                      : routePath.split("-")[1] == 3
                      ? "TCP Ping"
                      : "--"
                    : "--";
                break;
              case 4:
                text = "UDP Trace";
                break;
              case 5:
                text = "ICMP Trace";
                break;
              case 6:
                text = "TCP Trace";
                break;
              case 7:
                text = "TCP Trace";
                break;
              case 8:
                text =
                  routePath.indexOf("-") >= 0
                    ? routePath.split("-")[1] == 1
                      ? "UDP Trace"
                      : routePath.split("-")[1] == 2
                      ? "ICMP Trace"
                      : routePath.split("-")[1] == 3
                      ? "TCP Trace"
                      : "--"
                    : "--";
                break;
              default:
                text = "--";
                break;
            }
            return h("div", text);
          },
        },
        // 目标IP
        {
          title: this.$t("comm_target_ip"),
          key: "showDestIp",
          width:250,
          render: (h, params) => {
            let str = params.row.showDestIp;
             str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;

                 let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },
        },
        // 目标名称
        {
          title: this.$t("comm_target_name"),
          key: "destIpName",
          render: (h, params) => {
            let str = params.row.destIpName;
            str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;
            return h(
              "div",
              {
                style: {
                  textAlign: "left",
                  width: "100%",
                  textIndent: "0px",
                  overflow: "hidden", //超出的文本隐藏
                  textOverflow: "ellipsis", //溢出用省略号显示
                  whiteSpace: "nowrap", //溢出不换行
                },
                attrs: {
                  title: str,
                },
              },
              str
            );
          },
        },
        // 目标类型

        {
          title: this.$t("comm_target_type"),
          key: "destType",
          render: (h, params) => {
            let str = params.row.destType;
            let typeList = this.deviceTypeList;

            for (var i = 0; i < typeList.length; i++) {
              if (parseInt(typeList[i].value) == str) {
                str = typeList[i].lable;
                break;
              }
            }
            return h(
              "span",
              str === undefined || str === null || str === "" || str == 0 ? "--" : str
            );
          },
        },
      ],
      row: {},
      pathShow: true,
      taskId: null,
      topoShow: false,
      //是否是不可信节点
      suspect: false,
      loading1: true,
      srcAlias: "",
      destAlias: "",
      startIp: "",
      endIp: "",
      trendParam: {
        pre_ip: "",
        cur_ip: "",
      },
      timeRange: [
        new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
      ],
      timeOptions: {
        shortcuts: [
          {
            text: this.$t("comm_half_hour"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return [
                start.format("yyyy-MM-dd HH:mm:ss"),
                end.format("yyyy-MM-dd HH:mm:ss"),
              ];
            },
            onClick: () => {},
          },
          {
            text: this.$t("comm_today"),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [
                new Date().format("yyyy-MM-dd 00:00:00"),
                new Date().format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_yesterday"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_7"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_30"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_curr_month"),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_preced_month"),
            value() {
              let date = new Date();
              let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
        ],
      },
      buttonList: [
        {
          value: 1,
          label: this.$t("dashboard_delay"),
        },
        {
          value: 2,
          label: this.$t("comm_packet_loss"),
        },
      ],
      indexQuery: {},

      buttonVal: 1,
      day: "",
      startDay: "",
      endDay: "",
      exportUrl: "sys/getPingTrace",
      exportQuery: {},
      topologyList: [],
      event_number: 0,
      delay_line: [], //时延走势数据
      loss_line: [], //丢包走势数据
      timeLine: {
        data: [],
      },
      //以下为趋势图有关参数
      echartLookParama2: {
        preNodeIp: "",
        nodeIp: "",
        linkId: "",
        startTime: "",
        endTime: "",
        queryType: "",
        special: false,
        High: false,
        level: "",
        taskFlag: 1,
      },
      height: 300,
      echart1: {
        show: true,
      },
      echart2: {
        show: false,
      },
      query: {},
      query2: {},
      startTime: 0,
      endTime: 0,
      preAlias: {
        name: "",
        port: "",
      },
      zAlias: {
        name: "",
        port: "",
      },
      delayLossChart1: null,
      delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "B",
        出流速: "B",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%",
      },
      flowColor: ["#478EE9", "#F19149"],
      flowUnit: "B",
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: [],
      },
      flowData: {
        devName: "",
        interfaceIp: "",
        interfaceName: "",
        enter: [],
        issue: [],
      },
      delayLossScale: true,
      startScale: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayEnd: 100,
      startValue: "",
      endValue: "",
      scale: "",
      hoverDelayTime: "",
      hoverFlowTime: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {},
      },
      //以上为趋势图有关参数
      markPoint: "",
      markPointGreen: "",
      routeId: "",
      routeCode: "",
    };
  },
  mounted() {
    this.modalWidth = localStorage.getItem('modalWidth') *  0.98
      // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        // 设置颜色
    this.delayLossColor = [eConfig.legend.delayColor[this.currentSkin] ,eConfig.legend.lossColor[this.currentSkin] ];
    this.flowColor = [eConfig.legend.flowInColor[this.currentSkin] ,eConfig.legend.flowOutColor[this.currentSkin] ];
    // 监听 storage 事件

  },
  created() {
    this.getDeviceTypeList();

    let delayLossUnitTemp = {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] = "ms";
    // 丢包率
    delayLossUnitTemp[this.$t("comm_packet_loss")] = "%";
    // 入流速
    delayLossUnitTemp[this.$t("specquality_incoming_velocity")] = "bps";
    // 出流速
    delayLossUnitTemp[this.$t("specquality_exit_velocity")] = "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("specquality_incoming_rate")] = "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("specquality_output_rate")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t("alarm_disclaimer")] = "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t("specquality_availability_exemption")] = "%";
    // 可用率
    delayLossUnitTemp[this.$t("specquality_availability")] = "%";
    // 优良率
    delayLossUnitTemp[this.$t("specquality_rate_exemption")] = "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit, delayLossUnitTemp);

    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
  watch: {
    indexData: {
      handler(val) {
        this.topoShow = val.topoShow;
        this.buttonVal = 1;
        this.markPoint = val.eventStart;
        this.markPointGreen = val.eventEnd;
        this.trendParam.pre_ip = val.showSourceIp;
        this.trendParam.cur_ip = val.showDestIp;
        this.indexQuery = Object.assign({}, val);
        this.getLastHour();
        // this.getTopologyData(val);
        this.echartLookParama2.preNodeIp = val.preNodeIp || val.showSourceIp;
        this.echartLookParama2.nodeIp = val.nodeIp || val.showDestIp;
        this.echartLookParama2.linkId = val.sysLinkId;
        this.echartLookParama2.queryType = 2;
        this.echartLookParama2.special = this.isSpecial;
        this.echartLookParama2.High = val.isHigh || this.High;
        this.echartLookParama2.startTime = this.startDay;
        this.echartLookParama2.endTime = this.endDay;
        this.echartLookParama2.level = "";
        // this.getTrend(this.echartLookParama2);
      },
      deep: true,
    },
    height() {
      if (this.delayLossChart1) {
        // this.delayLossChart1.clear();
        this.delayLossChart1.dispose();
      }
      this.initEchart();
    },
    // pathShow(newVal) {
    //   if(newVal) {
    //     console.log(newVal,'newVal.....')
    //     if (!this.delayLossChart1) {
    //   return;
    //    }
    //      this.delayLossChart1.dispose();
    //      this.delayLossChart1 = null;
    //      this.initEchart();

    //   }
    // }
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 分页切换条数
    tabParamPageSizeChange(val) {
      this.page.pageNum = 1;
      this.page.pageSize = val;
      this.getTaskList();
      // debugger
    },
    // 分页页码变化
    pageChangeModal(page) {
      this.page.pageNum = page;
      this.getTaskList();
    },
    // 获取表格数据
    async getTaskList() {
      let obj = {
        pageNo: this.page.pageNum,
        pageSize: this.page.pageSize,
        sourceIp: this.usea.sourceIp,
        topoId: this.usea.topoId,
        targetIp: this.usea.targetIp,
        linkIds:this.usea.linkIds,
        preIp:this.preIp
      };
      console.log(obj,'表格查询参数')
      const res = await this.$http.PostJson("/pathTopo/getTaskList", obj);
      this.filtabListModal = res.data.records;
    },
    // 获取目标类型列表
    async getDeviceTypeList() {
      this.deviceTypeList = [];
      await this.$http
         .wisdomPost("/deviceType/queryList")
        .then(({ code, data, msg }) => {
          if (code === 1) {
            // this.deviceTypeList = data ?? [];
            var newdeviceTypeList = [];
            for (let index = 0; index < data.length; index++) {
              var newdeviceType = {"value":data[index].id,"lable":data[index].name}
              // console.log('this.newdeviceType==>',newdeviceType);
              newdeviceTypeList.push(newdeviceType);
            }
            this.deviceTypeList = newdeviceTypeList ?? [];
          } else {
            this.deviceTypeList = [];
            this.$Message.warning(msg);
          }
        })
        .catch((err) => {
          this.deviceTypeList = [];
          throw new Error(err);
        });
    },

    querylinkNodePairVos(linkId) {
       var showLinkNodePairVo = this.usea.linkNodePairVos.find(item => item.linkId == linkId);
       if(showLinkNodePairVo != null){
        var preIp = showLinkNodePairVo.preIp,
        curIp = showLinkNodePairVo.curIp ;
          if(preIp == null){
             showLinkNodePairVo = this.usea.linkNodePairVos.find(item => item.preIp != null && item.preIp == curIp);

             console.log("showLinkNodePairVo-----" , showLinkNodePairVo);
          }
       }

       return showLinkNodePairVo;

    },

    // 获取路径分析图表数据
    async getPathChartData(val, obj = {}) {
      // debugger
      // val 1 ==> 父组件点击进来的时候调用，参数2，表格切换时候调用
      // console.log(this.indexData.taskId,'11111111')
      // console.log(obj,'obj')
      // debugger
      //  this.echartLookParama2.preNodeIp = val.sourceIp;
      //     this.echartLookParama2.nodeIp = val.destIp;
      debugger
      if (val == 1) {
        this.filtabListModal = this.tabListModal;
        console.log(" this.filtabListModal-----" ,  this.filtabListModal)
        this.routeCode = this.filtabListModal[0].taskNum;
        // 路径图查询参数赋值
        this.selectPingQuery = this.indexData;

        this.taskId = this.indexData.taskId;
        this.usea = obj;
        this.page.total = obj.total;
        // 为趋势图参数赋值
        // 赋值 为 用户点击的这一段的IP。
        // 注意这个参数： queryType 必须是2
        console.log(this.usea,'this.usea');
        // 防止 列表（this.tabListModal）返回的数据和 this.usea.linkIds 里面的顺序不一致。详情里面线路高亮显示不正确的问题。
        //
        let linkId = this.filtabListModal[0].routeId;
        if(this.usea.linkIds && this.usea.linkIds.length > 0){
            linkId = this.usea.linkIds[0];
        }
        let showLinkNodePairVo ="";
        if(this.usea.linkNodePairVos){
          // showLinkNodePairVo = this.usea.linkNodePairVos.find(item => item.linkId == linkId);
          showLinkNodePairVo = this.querylinkNodePairVos(linkId);

          this.usea.preNodeIp = showLinkNodePairVo.curIp;
          this.usea.nodeIp = showLinkNodePairVo.preIp; 
          this.usea.nodeIpTrend = showLinkNodePairVo.preIp; 
        }
        // this.usea.preNodeIp = showLinkNodePairVo.curIp;
        // this.usea.nodeIp = showLinkNodePairVo.preIp; 
        // this.usea.nodeIpTrend = showLinkNodePairVo.preIp; 
        // this.echartLookParama2.preNodeIp = this.usea.preNodeIp;
        this.echartLookParama2.preNodeIp = this.usea.preNodeIp;
        // this.echartLookParama2.nodeIp =  this.usea.nodeIpTrend ? this.usea.nodeIpTrend : this.usea.nodeIp;
        this.echartLookParama2.nodeIp =  this.usea.nodeIpTrend ? this.usea.nodeIpTrend : this.usea.nodeIp;

      } else {
        this.echartLookParama2.preNodeIp = this.usea.preNodeIp;
        // this.echartLookParama2.nodeIp = this.usea.nodeIpTrend;
        // this.echartLookParama2.nodeIp = this.usea.nodeIp;
         this.echartLookParama2.nodeIp =  this.usea.nodeIpTrend ? this.usea.nodeIpTrend : this.usea.nodeIp;
      }

      let endTime = moment(new Date().getTime()).format("YYYY-MM-DD HH:mm:ss");
      let startTime = moment(new Date().getTime() - 1 * 60 * 60 * 1000).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      try {
        const { code, data, msg } = await this.$http.wisdomPost(
          "/probetask/findRouteBarChartByTaskId",
          {
            taskId: this.taskId,
            startTime,
            endTime,
          }
        );
        if (code === 1) {
          // console.log('111111111111111111111111111111111111',data);

          this.chartData = [];
          let routeId = "";
          let routeCode = "";
          if (data.length > 0) {
            if (data.length > 10) {
              this.startValue = data[data.length - 10].stratTime * 1000;
              this.endValue = data[data.length - 1].endTime * 1000;
            } else {
              this.startValue = data[0].stratTime * 1000;
              this.endValue = data[data.length - 1].endTime * 1000;
            }

            // console.log(
            //   "this.startValue",
            //   this.startValue,
            //   "this.endValue",
            //   this.endValue
            // );
            data.forEach((item) => {
              item.activeRoute ? (routeId = item.routeId) : "";
              item.activeRoute ? (routeCode = item.routeCode) : "";
              this.routeCode = item.routeCode;
              this.chartData.push([
                item.stratTime * 1000,
                item.endTime * 1000,
                item.percentage,
                item.routeId,
                item.routeCode,
              ]);
            });
            this.sysLinkId = routeId;
            // alert(this.sysLinkId)
            await this.getTopologyData(this.selectPingQuery, routeId);
            // this.echartsLookParama2.endTime=this.endTime
            // this.echartLookParama2.startTime=this.startTime
            // this.echartLookParama2.nodeIp=this.indexData.nodeIp
            // this.echartLookParama2.preNodeIp=this.indexData.preNodeIp

            await this.getTrend(this.echartLookParama2, routeId);
            //  this.initEchart()
          } else {
          }
        } else {
          this.$Message.warning(msg);
        }
      } catch (err) {
      } finally {
        this.$emit("closeLoading");
      }
      // .finally(() => {
      // 		this.$emit("closeLoading");
      // 	});

      // .then(({ code, data, msg }) => {
      //
      // })
      // .catch((error) => {})
      // .finally(() => {
      // 	this.$emit("closeLoading");
      // });
    },
    dateChange(val, type) {
      if (type == "date") {
        this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")];
      }
    },
    cancel() {
      (this.pathShow = true), (this.topologyList = []);
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      // this.$emit('closeModal')
      this.$emit("closeLinkModal");
      // this.$emit('update:linkDetailShow',false)
    },
    visiblechange(val) {
      if (val) {
        this.$nextTick(() => {
          // this.initEchart();
          //   this.$refs.PathChart.getPathChartData(this.startDay, this.endDay)
        });
      } else {
        // this.$emit('update:linkDetailShow',false)
        this.page= {
        pageSize: 10,
        pageNum: 1,
        total: null,
      }
      }
    },

    getAlias(ip, type) {
      //获取别名
      this.$http.wisdomPost("/link/getByIpName", { ip: ip }).then((res) => {
        if (res.code == 1) {
          if (type == 1) {
            this.srcAlias = res.data[0] || "";
          } else if (type == 2) {
            this.destAlias = res.data[0] || "";
          }
        }
      });
    },
    // setRoutId(routeId, routeCode) {
    //   this.routeId = routeId
    //   this.routeCode = routeCode
    //   this.getTopologyData(this.indexData, routeId) // 获取路径拓扑数据
    //   this.getTrend(this.echartLookParama2, routeId) // 获取指标趋势数据
    // },
    //获取数据
    getTopologyData(param, routeId = "") {
      this.tabsId = routeId;
      let _self = this;
      _self.$refs["topologyChart"].resetData();
      _self.$http
        .wisdomPost("/trendData/selectGongHangIndexPing", {
          ...param,
          sysLinkId: routeId,
        })
        .then((res) => {
          let data = res.data || {},
            list = data.listl || [];
          if (list.length > 0) {
            list.unshift({
              ip1: data.sourceIp ||  this.indexQuery.showSourceIp,
              isBuquan: 0,
              isBroken: 0,
            });
          }
          /*2021年4月25为改中断和任务策略任务显示一致，但数据不一致的bug而增加的代码*/
          let brokenNum = 0;
          if (list.length > 0) {
            let isBrokenF = false,
              isIpduan = false,
              isDelay = false,
              borkenIndex = 0,
              delayIndex = 0;
            list.forEach((item, index) => {
              if (item.isBroken === 1 || item.isBroken === 3 || item.isBroken === 7) {
                isBrokenF = true;
                borkenIndex = index;
              }
              if (item.isBroken === 4) {
                isIpduan = true;
              }
              if (item.isBroken === 2) {
                isDelay = true;
                delayIndex = index;
              }
            });
            for (let i = 0, len = list.length; i < len; i++) {
              /*重新组装数据start*/
              /*nodeColor:r,y,b;lineColor:r,y,b,linkPoint:true*/
              if (list[i].isBroken != 0) {
                brokenNum = list[i].isBroken;
              }
              list[i].nodeColor =
                list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
              list[i].lineColor =
                list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
              list[i].linkPoint = list[i].isBroken == 1 ? true : false;
              list[i].deviceTypeCss = "";
              /*重新组装数据end*/
            }

            //处理中断ip段 前的ip为*的处理
            let thisIndex = true;
            if (isBrokenF) {
              for (let index = borkenIndex; index > 0; index--) {
                if (thisIndex && list[index - 1].ip1 == "*") {
                  list[index - 1].nodeColor = "r";
                  list[index - 1].lineColor = "r";
                  thisIndex = true;
                } else if (thisIndex && list[index - 1].suspect === true) {
                  list[index - 1].nodeColor = "r";
                  list[index - 1].lineColor = "r";
                  thisIndex = true;
                } else {
                  thisIndex = false;
                }
              }
            }
            //处理时延ip段 前的ip为*的处理
            let delayFlag = true;
            if (isDelay) {
              for (let index = delayIndex; index > 0; index--) {
                if (delayFlag && list[index - 1].ip1 == "*") {
                  list[index - 1].nodeColor = "y";
                  list[index - 1].lineColor = "y";
                  delayFlag = true;
                } else if (delayFlag && list[index - 1].suspect === true) {
                  list[index - 1].nodeColor = "y";
                  list[index - 1].lineColor = "y";
                  delayFlag = true;
                } else {
                  delayFlag = false;
                }
              }
            }
            let indexBroken = list.map((item) => item.isBroken != 0).indexOf(true),
              indexX = 0;
            for (let i = indexBroken - 1; i >= 0; i--) {
              if (i === indexBroken - 1 && list[i].ip1 === "*" && i - 1 >= 0) {
                indexX = i - 1;
                list[i].lineColor = brokenNum === 2 ? "y" : "r";
              } else if (indexX === i && list[i].ip1 === "*" && i - 1 >= 0) {
                indexX = i - 1;
                list[i].lineColor = brokenNum === 2 ? "y" : "r";
              }
            }

            if (list.length === 2 && (list[0].isBroken === 1 || list[1].isBroken === 1)) {
              list[1].lineColor = "r";
              list[1].nodeColor = "r";
              list[1].linkPoint = true;
            }
            // for (let i = borkenIndex, len = list.length; i < len; i++) {
            //   if (isBrokenF && borkenIndex<i) {
            //     list[i].nodeColor = 'g';
            //     list[i].lineColor = 'g';
            //   }
            // }
            list.forEach((item, index) => {
              if (index > borkenIndex && borkenIndex != 0) {
                item.nodeColor = "g";
                item.lineColor = "g";
              }
            });
            this.topologyList = list;
            // getLineClickIndex
            // 解决链路默认高亮代码
            // 拓扑图线条变粗的需求
            // console.log('list===>',list);
            list.forEach((itemA, index) => {
              // console.log("itemA.ip1",itemA.ip1);
              let isReturn = false;
              // 虚拟节点需要拿到拼接的前节点
              if (this.usea.preNodeIp.includes('-9999')) {
                this.usea.preNodeIp = this.usea.preNodeIp.split("_")[1]
              }
              // debugger
              console.log( this.$refs.topologyChart,'自组建封装方法')
              // debugger
              console.log(this.usea , "----------------------");
              if (itemA.ip1 == this.usea.preNodeIp) {
                this.$refs.topologyChart.getLineClickIndex(index);
                for(var i=++index;i< list.length ; i++){
                  console.log("itemB.ip1===>",i+'-'+list[i]);
                  var nodeTemp = this.usea.nodeIp;
                  if(this.usea.nodeIpTrend){
                      nodeTemp = this.usea.nodeIpTrend;
                  }
                  if (list[i].ip1 == nodeTemp) {
                       isReturn = true;
                      break;
                  }
                  console.log("高亮===>",i);
                  this.$refs.topologyChart.getLineClickIndex(i);
                }

              }
              if (isReturn) {
                return;
              }
            });
            // debugger
          } else {
            this.topologyList = [];
          }
          if (data.broken_num == null) {
            _self.event_number = 0;
          } else {
            _self.event_number = data.broken_num;
          }
          _self.delay_line = data.delay || [];
          _self.timeLine.data = data.delay || [];
          _self.loss_line = data.las_vlue || [];
        });
    },
    //获取虚线数据
    getLineData(param) {
      let _self = this;
      _self.$http.wisdomPost("/sys/selectGongHangIndexTrace", param).then((res) => {
        let data = res.data || {};
        if (data.broken_num == null) {
          _self.event_number = 0;
        } else {
          _self.event_number = data.broken_num;
        }
        _self.delay_line = data.delay || [];
        _self.timeLine.data = data.delay || [];
        _self.loss_line = data.las_vlue || [];
      });
    },
    //获取当前最近一小时时间
    getLastHour() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000);
      this.startDay = this.indexQuery.startTime = new Date(start).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      this.endDay = this.indexQuery.endTime = new Date(end).format("yyyy-MM-dd HH:mm:ss");
      this.timeRange = [this.startDay, this.endDay];
      // this.day=[start,end];
    },
    //获取当前数据之前N月
    monthDate(val) {
      let start = new Date(),
        monthNumber = 2, //当前天数之前第几个月
        year = start.getFullYear(),
        month = start.getMonth() + 1,
        date = start.getDate(),
        newYear = "",
        newMonth = "",
        startDate = "",
        endDate = "";
      if (month - monthNumber < 1) {
        newYear = year - 1;
        newMonth = 12 - monthNumber + month;
      } else {
        newYear = year;
        newMonth = month - monthNumber;
      }
      startDate = newYear + "-" + newMonth + "-" + date + " 00:00:00";
      endDate = year + "-" + month + "-" + date + " 00:00:00";
      // if(new Date(val).getTime()<new Date(startDate).getTime()){
      //     if(this.$base.isShowPrompt()){
      //         this.$Message.error("请选择最近两个月内的时间段");
      //     }
      //    return []
      // }
      return [startDate, endDate];
    },
    //查询   负责 pathShow 和  topoShow

    indexQueryClick() {
      this.startDay = new Date(this.timeRange[0]).format("yyyy-MM-dd HH:mm:ss");
      this.endDay = new Date(this.timeRange[1]).format("yyyy-MM-dd HH:mm:ss");
      let startVal = moment(this.startDay, "YYYY-MM-DD hh:mm:ss").valueOf();
      let endVal = moment(this.endDay, "YYYY-MM-DD hh:mm:ss").valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning({
          content: this.$t("warning_time_not_exceed_62"),
          background: true,
        });
        return;
      }

      this.indexQuery.startTime = this.startDay;
      this.indexQuery.endTime = this.endDay;
      if (!this.startDay || !this.endDay) {
        this.$Message.warning({ content: "请选择起始日期", background: true });
        return;
      } else if (this.startDay > this.endDay) {
        this.$Message.warning({ content: "开始日期不能大于结束日期", background: true });
        return;
      }
      if (!this.pathShow) {
        // pathList页面查询
        console.log("pathList页面查询");
        this.$refs.pathList.getList(this.startDay, this.endDay);
      } else {
        //detailModal查询
        console.log("detailModal查询");
        this.threndQuery();
      }
    },
    threndQuery() {
      if (this.topoShow) {
        return;
      }
      let date = this.monthDate(this.startDay);
      this.buttonVal = 1;
      this.delayStart = 0;
      this.delayEnd = 100;
      if (date.length > 0) {
        this.echartLookParama2.startTime = this.indexQuery.startTime;
        this.echartLookParama2.endTime = this.indexQuery.endTime;
        this.echartLookParama2.level = "";
        // this.$refs.PathChart.getPathChartData(this.indexQuery.startTime, this.indexQuery.endTime)
        // this.getTopologyData(this.indexData,this.routeId)
        this.getTrend(this.echartLookParama2, this.sysLinkId);
      }
    },
    pathListQuery() {
      this.$refs.pathList.getList(
        this.row,
        this.indexQuery.startTime,
        this.indexQuery.endTime
      );
    },
    //导出
    exportClick(type) {
      // type:1、导出当前 ，2、导出全部
      let _self = this,
        user = JSON.parse(sessionStorage.getItem("accessToken")),
        baseURL = this.$baseUrl + "/sys/getPingTrace",
        a = null,
        param = {
          token_id: user.token_id,
          sys_link_id: this.indexQuery.sysLinkId,
          source_ip: this.indexQuery.sourceIp,
          dest_ip: this.indexQuery.destIp,
        };
      let date = _self.monthDate(_self.indexQuery.startTime);
      if (date.length > 0) {
        if (type === 1) {
          param.startTime = _self.indexQuery.startTime;
          param.endTime = _self.indexQuery.endTime;
          a = document.getElementById("current");
        } else {
          param.startTime = date[0];
          param.endTime = date[1].split(" ")[0] + " 23:59:59";
          a = document.getElementById("all");
        }
        if (_self.timeLine.data.length > 0) {
          a.href =
            baseURL + (baseURL.indexOf("?") === -1 ? "?" : "&") + Qs.stringify(param);
        }
      }
    },
    //按钮切换
    buttonClick(val) {
      let _self = this;
      _self.buttonVal = val;
      _self.timeLine.data = [];
      if (val === 1) {
        _self.timeLine.data = _self.delay_line;
      } else {
        _self.timeLine.data = _self.loss_line;
      }
    },
    //流程图事件
    topologyClick(row) {
      // debugger
      if (!this.High) {
        this.indexQuery.flowChartType = row.type;
        this.indexQuery.sourceIp = row.data[0].ip1;
        this.indexQuery.destIp = row.data[1].ip1;
        this.trendParam.preIp = row.data[0].ip1;
        this.trendParam.curIp = row.data[1].ip1;

        this.echartLookParama2.level = "";
        this.buttonVal = 1;
        // this.getAlias(this.trendParam.pre_ip,1);
        // this.getAlias(this.trendParam.cur_ip,2);
        if (row.type == 1 && row.index != 0) {
          this.echartLookParama2.queryType = 1;
          this.echartLookParama2.preNodeIp = row.data[0].ip1;
          this.echartLookParama2.nodeIp = row.data[1].ip1;
          if (this.echartLookParama2.nodeIp != "*") {
            this.topoShow = false;
            this.getTrend(this.echartLookParama2, this.sysLinkId);
          } else {
            this.topoShow = true;
          }
        } else if (row.type == 2) {
          this.topoShow = false;
          this.echartLookParama2.queryType = 2;
          this.echartLookParama2.preNodeIp = row.data[0].ip1;
          this.echartLookParama2.nodeIp = row.data[1].ip1;

          if (row.suspect) {
            this.suspect = true;
            // return
          } else {
            this.suspect = false;
          }
          this.getTrend(this.echartLookParama2, this.sysLinkId);
        }
      }
    },

    /*趋势图有关*/
    async getTrend(param, routeId = "") {
      // debugger

      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.loading1 = true;
      param = { ...param, linkId: routeId };
      const res = await this.$http.PostJson("/trend/getDelayAndLostTrend", param);

      //时延丢包趋势数据
      if (res.code === 1) {
        if (res.data) {
          this.preAlias.name = res.data.preName;
          this.preAlias.port = res.data.prePort;
          this.zAlias.name = res.data.name;
          this.zAlias.port = res.data.port;
          this.delayLossLevel = res.data.level;
          const delayData = res.data.lineOne,
            lossData = res.data.lineTwo;
          let hasData = true;
          if (!delayData && !lossData) {
            hasData = false;
            this.echart1.show = false;
          }
          if (hasData) {
            this.echart1.show = true;
            this.delayStart = 0;
            if (delayData) {
              // if (delayData.length <= 300) {
              this.delayEnd = 100;
              this.startValue = delayData[0][0];
              this.endValue = delayData[delayData.length - 1][0];
              // } else {
              //   this.delayEnd = (300 * 100) / delayData.length;
              //   this.startValue = delayData[0][0];
              //   this.endValue = delayData[299][0];
              // }
              this.delayLossScale = true;
              this.delayLossData.delay = delayData;
            } else if (lossData) {
              // if (lossData.length <= 300) {
              this.delayEnd = 100;
              this.startValue = lossData[0][0];
              this.endValue = lossData[lossData.length - 1][0];
              // } else {
              //   this.delayEnd = (300 * 100) / lossData.length;
              //   this.startValue = lossData[0][0];
              //   this.endValue = lossData[299][0];
              // }
              this.delayLossScale = true;
              this.delayLossData.loss = lossData;
            }
          }

          // if (res.data.lineOne && res.data.lineOne.length > 0) {
          //   this.echart1.show = true;
          //   this.delayStart = 0;
          //   if (res.data.lineOnetotal <= 300) {
          //     this.delayEnd = 100;
          //     this.startValue = res.data.lineOne[0][0];
          //     this.endValue =
          //       res.data.lineOne[res.data.lineOne.length - 1][0];
          //   } else {
          //     this.delayEnd = (300 * 100) / res.data.lineOnetotal;
          //     this.startValue = res.data.lineOne[0][0];
          //     this.endValue = res.data.lineOne[299][0];
          //   }
          //   this.delayLossScale = true;
          //   this.delayLossData.delay = res.data.lineOne;
          // }
          if (lossData && lossData.length > 0) {
            this.delayLossData.loss = lossData;
          }
          this.$store.commit("updateDelayLossHistory", {
            level: res.data.level,
            datas: [this.delayLossData.delay, this.delayLossData.loss],
          }); //保存数据
          this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
          this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
        }
      }

      let trendParam = JSON.parse(JSON.stringify(param));
      trendParam = Object.assign(trendParam, { snmp: false });
      this.getDataTrend(trendParam);
    },
    async getDataTrend(trendParam) {
      const res = await this.$http.PostJson("/trend/getDataTrend", trendParam);

      //流速趋势数据
      if (res.code === 1) {

        this.flowData.devName = res.data.devName ?? "";
        this.flowData.interfaceIp = res.data.interfaceIp ?? "";
        this.flowData.interfaceName = res.data.interfaceName ?? "";
        let flowUnit = 1;
        this.flowLevel = res.data.level ?? "";
        if (!res.data.lineOne || res.data.lineOne.length < 1) {
          this.echart2.show = false;
          this.flowLevel = 99;
        }
        if (res.data.lineTwo && res.data.lineTwo.length > 0) {
          this.echart2.show = true;
          let unitChange = this.getFlowUnit(res.data.lineTwo);
          this.flowUnit = unitChange[0];
          this.delayLossUnit[this.$t("specquality_incoming_velocity")] = unitChange[0];
          this.delayLossUnit[this.$t("specquality_exit_velocity")] = unitChange[0];
          flowUnit = unitChange[1];
          this.flowData.enter = res.data.lineTwo.map((item) => {
            return [item[0], item[1]];
          });
        }
        if (res.data.lineOne && res.data.lineOne.length > 0) {
          this.flowData.issue = res.data.lineOne.map((item) => {
            return [item[0], item[1]];
          });
        }

        this.$store.commit("updateFlowHistory", {
          level: res.data.level,
          datas: [this.flowData.enter, this.flowData.issue],
        }); //保存数据
        this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
        this.$store.commit("setflowUnit", {
          level: res.data.level,
          unit: this.flowUnit,
        }); //保存单位
      }

      this.startScale = false;
      this.loading1 = false;
      if (this.echart2.show) {
        this.height = 500;
      } else if (!this.echart2.show) {
        this.height = 300;
      }
       top.document.getElementById("probetask-delayLoss").style.height =
        this.height + "px";
        this.$nextTick(() => {
          this.initEchart();
          
        })

      
    },

    setDelayLossTooltip() {
      let _self = this;
      return eConfig.tip("axis", function (param) {
        _self.scale = param[0].data[0];
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex] ? "" : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : _self.flowSize(param[i].value[1], true, true)) +
              // param[i].value[1]
              // _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip;
      });
    },
    Option() {
      console.log(this.delayLossData,this.echart2,'delayLossData............')

      let optionArr = [
        {
          title: {
            show:
              this.delayLossData.delay.length === 0 &&
              this.delayLossData.loss.length === 0,
            text: this.$t("common_No_data"),
            left: "center",
            top: "90px",
            textStyle: {
              color: "#465b7a",
              fontFamily: "serif",
              fontWeigth: "400",
              fontSize: 18,
            },
          },
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "5%",
              top: "40px",
              width: "90%",
              height: "140px",
            },
            {
              // show:this.echart2.show,
              left: "9%",
              top: "300px",
              width: "90%",
              height: this.echart2.show ? "140px" : "0px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "40%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: this.currentSkin == 1 ? "#FFFFFF" : "#515A6E",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t("speed_delay"), this.$t("comm_packet_loss")],
            },
            {
              show: this.echart2.show,
              top: "250px",
              right: "40%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , //"#FFFFFF",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.flowColor,
              data: [
                this.$t("specquality_incoming_velocity"),
                this.$t("specquality_exit_velocity"),
              ],
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color:  top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2", // "rgba(42, 56, 64, 1)",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , //"rgba(42, 56, 64, 1)",
                },
              },
              axisLabel: {
                textStyle: {
                  color:top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2", // "#5CA0D5",
                },
              },
              axisLine: {
                lineStyle: {
                  color:top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: this.$t("comm_delay(ms)"),
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "left",
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#2A3840",
                },
              },
            },
            {
              name: this.$t("comm_loss_rate"),
              type: "value",
              scale: true,
              gridIndex: 0,
              min: 0,
              max: 100,
              position: "right",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#2A3840",
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t("server_flow_rate"),
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color:  top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#2A3840",
                },
              },
              axisLabel: {
                show: true,
                formatter: (value) => {
                  return this.flowSize(value, true, true);
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue == this.endValue ? "" : this.startValue,
              endValue: this.startValue == this.endValue ? "" : this.endValue,
              // start:this.delayStart,
              // end:this.delayEnd
            },
            {
              type: "slider",
              left: "5%",
              right: "5%",
              height: 20,
              top: this.echart2.show ? "480px" : "250px",
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue == this.endValue ? "" : this.startValue,
              endValue: this.startValue == this.endValue ? "" : this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin]  ,//"rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin]  ,//"rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin]  ,//"rgba(2, 67, 107, 1)",
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin]  ,// top.window.isdarkSkin == 1 ? "#617ca5" : "#e3e7f2",
              },
            },
          ],
          series: [
            {
              type: "line",
              name: this.$t("speed_delay"),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              // symbol: this.delayLossData.delay.length>1?"none":"none",
              // seriesLayoutBy:'row',
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.delayColor_0[this.currentSkin] ,//"rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.delayColor_8[this.currentSkin] ,//"rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.delay,
              // markLine: {
              //   data: [
              //     {
              //       symbol: 'image://' + pointRed,
              //       symbolSize: 10,
              //       xAxis: this.markPoint,
              //       symbolRotate: '0',
              //       // symbolOffset:[0,'70px'],
              //       lineStyle: {
              //         color: "rgba(0,0,0,0)",
              //         width: 0,
              //         opacity: 1
              //       },
              //       label: {
              //         show: true,
              //         position: "start",
              //         color: "red",
              //         fontSize: 10,
              //         formatter: () => {
              //           return this.markPoint
              //         },
              //         backgroundColor: top.window.isdarkSkin == 1 ? "#617ca5" : "#fff",
              //         // padding: [0]
              //       }
              //     },
              //     {
              //       symbol: 'image://' + pointGreen,
              //       symbolSize: 10,
              //       xAxis: this.markPointGreen,
              //       symbolRotate: '0',
              //       // symbolOffset:[0,'70px'],
              //       lineStyle: {
              //         color: "rgba(0,0,0,0)",
              //         width: 0,
              //         opacity: 1
              //       },
              //       label: {
              //         show: true,
              //         position: "start",
              //         color: "#33cc33",
              //         fontSize: 10,
              //         backgroundColor: "#fff",
              //         formatter: () => {
              //           return this.markPointGreen
              //         },
              //         // padding: [0]
              //       }
              //     }
              //   ],
              //   emphasis: {
              //     lineStyle: {
              //       color: "red",
              //       width: 0,
              //       opacity: 1
              //     }
              //   }
              // },
            },
            {
              type: "line",
              name: this.$t("comm_packet_loss"),
              xAxisIndex: 0,
              yAxisIndex: 1,
              // seriesLayoutBy:'row',
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              // symbol: this.delayLossData.loss.length>1?"none":"none",
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.lossColor_0[this.currentSkin] ,//"rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.lossColor_8[this.currentSkin] ,//"rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.loss,
            },
            {
              show: this.echart2.show,
              name: this.$t("specquality_incoming_velocity"),
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              // symbol: this.flowData.enter.length>1?"none":"none",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowInColor_0[this.currentSkin] ,//"rgba(71,142,233, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowInColor_8[this.currentSkin] ,//"rgba(71,142,233, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t("specquality_exit_velocity"),
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              //symbol: this.flowData.issue.length>1?"none":"none",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart() {
      // debugger

      // let that = this;
      if (this.delayLossChart1) {
        // alert("销毁");
        // this.delayLossChart1.clear();
        this.delayLossChart1.dispose();
      }

      // top.document.getElementById("probetask-delayLoss").style.height =
      //   this.height + "px";
      this.delayLossChart1 = echarts.init(this.$refs["probetask-delayLoss"]);

      /*设置时延丢包率echart图*/

      const option = this.Option();
      setTimeout(() => {
        this.delayLossChart1.setOption(option);
      }, 500);
      // const option = this.Option();

      // this.delayLossChart1.setOption(this.Option());

      // debugger

      // this.delayLossChart1.getZr().on("mousewheel", (params) => {
      //   let getTlevel = this.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
      //   let getflowTlevel = this.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
      //   let getgoodTlevel = this.$store.state.goodRateTlevel; //获取delay保存的顶层粒度级别
      //   let getSaveData = this.$store.state.delayLossHistory; //获取保存的数据
      //   let getflowSaveData = this.$store.state.flowHistory; //获取保存的数据
      //   let getflowSaveUnit = this.$store.state.flowUnit; //获取流速单位数据
      //   let getgoodSaveData = this.$store.state.goodRateHistory; //获取保存的数据

      //   var pointInPixel = [params.offsetX, params.offsetY];
      //   if (
      //     this.delayLossChart1.containPixel(
      //       { gridIndex: [0, 1, 2] },
      //       pointInPixel
      //     )
      //   ) {
      //     let startValue =
      //       this.delayLossChart1.getModel().option.dataZoom[0].startValue;
      //     let endValue =
      //       this.delayLossChart1.getModel().option.dataZoom[0].endValue;
      //     this.echartLookParama2.startTime = new Date(startValue).format2(
      //       "yyyy-MM-dd HH:mm:ss"
      //     );
      //     this.echartLookParama2.endTime = new Date(endValue).format2(
      //       "yyyy-MM-dd HH:mm:ss"
      //     );
      //     this.setPs(this.delayLossLevel, [
      //       this.timeChange(startValue),
      //       this.timeChange(endValue),
      //     ]);
      //     let differ = endValue - startValue;
      //     let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
      //     let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
      //     let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
      //     var levelNum = "";
      //     let start = this.delayLossChart1.getModel().option.dataZoom[0].start;
      //     let end = this.delayLossChart1.getModel().option.dataZoom[0].end;

      //     if (params.wheelDelta >= 0) {
      //       if (this.delayLossLevel == 1) {
      //         // 小时粒度
      //         if (differ < minute) {
      //           if (!this.startScale) {
      //             this.startScale = true;
      //             levelNum = 2;
      //             let delayParam = Object.assign(this.echartLookParama2, {
      //               level: levelNum,
      //               linkId:this.routeId
      //             });
      //             let flowParam = Object.assign(delayParam, {
      //               snmp: this.dataSource == 1 ? true : false,
      //             });
      //             this.getDelayLoss(delayParam, flowParam);
      //           }
      //         }
      //       } else if (this.delayLossLevel == 2) {
      //         // 分钟粒度
      //         if (differ < original) {
      //           if (!this.startScale) {
      //             this.startScale = true;
      //             if (this.High) {
      //               levelNum = 4;
      //               let delayParam = Object.assign(this.echartLookParama2, {
      //                 level: levelNum,
      //                 linkId:this.routeId
      //               });
      //               let flowParam = Object.assign(delayParam, {
      //                 snmp: this.dataSource == 1 ? true : false,
      //               });
      //               this.getDelayLoss(delayParam, flowParam);
      //             }
      //           }
      //         }
      //       }
      //     } else {
      //       if (start == 0 && end == 100) {
      //         //是否处在缩放过程中
      //         if (this.delayLossLevel == getTlevel) {
      //           this.setPs(this.delayLossLevel, [
      //             this.timeChange(startValue),
      //             this.timeChange(endValue),
      //           ]);
      //           this.startScale = false;
      //         } else {
      //           let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
      //           if (this.flowLevel == getflowTlevel) {
      //             this.startScale = false;
      //           } else {
      //             if (this.flowLevel == 2) {
      //               this.flowLevel = 1;
      //               this.startScale = true;
      //               this.flowUnit = getflowSaveUnit.HoursUnit;
      //               this.flowData.enter = getflowSaveData.HoursData.enter;
      //               this.flowData.issue = getflowSaveData.HoursData.issue;
      //             } else if (this.flowLevel == 3 || this.flowLevel == 4) {
      //               this.flowLevel = 2;
      //               this.startScale = true;
      //               this.flowUnit = getflowSaveUnit.minuteUnit;
      //               this.flowData.enter = getflowSaveData.minuteData.enter;
      //               this.flowData.issue = getflowSaveData.minuteData.issue;
      //             }
      //           }
      //           if (this.goodRateLevel == getgoodTlevel) {
      //             this.startScale = false;
      //           } else {
      //             if (this.goodRateLevel == 2) {
      //               this.goodRateLevel = 1;
      //               this.startScale = true;
      //               this.goodRateData.nrUseRateList =
      //                 getgoodSaveData.HoursUnit.nrUseRateList;
      //               this.goodRateData.nrGoodRateList =
      //                 getgoodSaveData.HoursUnit.nrGoodRateList;
      //               this.goodRateData.useRateList =
      //                 getgoodSaveData.HoursUnit.useRateList;
      //               this.goodRateData.goodRateList =
      //                 getgoodSaveData.HoursUnit.goodRateList;
      //             } else if (
      //               this.delayLossLevel == 3 ||
      //               this.delayLossLevel == 4
      //             ) {
      //               this.delayLossLevel = 2;
      //               this.startScale = true;
      //               this.goodRateData.nrUseRateList =
      //                 getgoodSaveData.minuteUnit.nrUseRateList;
      //               this.goodRateData.nrGoodRateList =
      //                 getgoodSaveData.minuteUnit.nrGoodRateList;
      //               this.goodRateData.useRateList =
      //                 getgoodSaveData.minuteUnit.useRateList;
      //               this.goodRateData.goodRateList =
      //                 getgoodSaveData.minuteUnit.goodRateList;
      //             }
      //           }

      //           if (this.delayLossLevel == getTlevel) {
      //             this.startScale = false;
      //           } else if (this.delayLossLevel == 2) {
      //             this.delayLossLevel = 1;
      //             this.startScale = true;
      //             this.delayLossData.delay = getSaveData.HoursData.delay;
      //             this.delayLossData.loss = getSaveData.HoursData.loss;
      //             this.delayStart = getSite.psH.start;
      //             this.startValue = getSite.psH.start;
      //             this.delayEnd = getSite.psH.end;
      //             this.endValue = getSite.psH.end;
      //           } else if (
      //             this.delayLossLevel == 3 ||
      //             this.delayLossLevel == 4
      //           ) {
      //             this.delayLossLevel = 2;
      //             this.startScale = true;
      //             this.delayLossData.delay = getSaveData.minuteData.delay;
      //             this.delayLossData.loss = getSaveData.minuteData.loss;
      //             this.delayStart = getSite.psM.start;
      //             this.startValue = getSite.psM.start;
      //             this.delayEnd = getSite.psM.end;
      //             this.endValue = getSite.psM.end;
      //           }

      //           setTimeout(() => {
      //             this.startScale = false;
      //             this.initEchart();
      //           }, 300);
      //         }
      //       }
      //     }
      //   }
      // });
    },
    async getDelayLoss(
      delayParam,
      flowParam,
      goodRateParam,
      cachD,
      cachF,
      cachG,
      hoverDelayTime,
      flowTime,
      useTime
    ) {
      let isUpdate = false;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        this.loading1 = true;
        await this.$http
          .PostJson("/trend/getDelayAndLostTrend", delayParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.delayLossLevel = res.data.level;
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              let index = this.closest(lineData, hoverDelayTime);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.delayStart = 0;
                // if (res.data.lineOnetotal <= 300) {
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
                // } else {
                //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
                //   if (index <= 150) {
                //     this.startValue = res.data.lineOne[0][0];
                //     if (index + 150 < res.data.lineOnetotal - 1) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //           res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   } else {
                //     this.startValue = res.data.lineOne[index - 150][0];
                //     if (index + 150 < res.data.lineOnetotal) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //           res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   }
                // }
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            }
          });
      }

      if (flowParam.level != 99) {
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, {});
        await this.$http.PostJson("/trend/getDataTrend", trendParam).then((res) => {
          //流速趋势数据
          if (res.code === 1) {
            let flowUnit = 1;
            this.flowLevel = res.data.level;
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              let unitChange = this.getFlowUnit(res.data.lineOne);
              this.flowUnit = unitChange[0];
              this.delayLossUnit[this.$t("specquality_incoming_velocity")] =
                unitChange[0];
              this.delayLossUnit[this.$t("specquality_exit_velocity")] = unitChange[0];
              flowUnit = unitChange[1];
              this.flowData.enter = res.data.lineTwo.map((item) => {
                // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                return [item[0], item[1]];
              });
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.flowData.issue = res.data.lineOne.map((item) => {
                // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                return [item[0], item[1]];
              });
            }
            if (cachF) {
              this.$store.commit("updateFlowHistory", {
                level: res.data.level,
                datas: [this.flowData.enter, this.flowData.issue],
              }); //保存数据
              this.$store.commit("setflowUnit", {
                level: res.data.level,
                unit: this.flowUnit,
              }); //保存单位
            }
          }
        });
      }

      this.delayLoading = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      this.loading1 = false;
      if (isUpdate) {
        this.initEchart();
      }
    },
    //时间推算
    setTime(level, time) {
      let Interval = 0,
        start = new Date(this.echartLookParama2.startTime).getTime(),
        end = new Date(this.echartLookParama2.endTime).getTime(),
        newStart = 0,
        newEnd = 0;
      if (level == 0) {
        Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 1) {
        Interval = 12 * 60 * 60 * 1000;
      } else if (level == 2) {
        Interval = 30 * 60 * 1000;
      }
      newStart = time - Interval;
      newEnd = time + Interval;

      if (newStart < start) {
        newStart = start;
      }
      if (newEnd > end) {
        newEnd = end;
      }
      newStart = this.timeChange(newStart);
      newEnd = this.timeChange(newEnd);
      return [newStart, newEnd];
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.clear();
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
};
</script>
<style>
.index-modal
  .ivu-modal-body
  .index-query
  .index-query-box
  .ivu-picker-confirm
  button {
  width: auto !important;
  height: auto !important;
}

.index-modal .ivu-modal-body {
  height: auto !important;
}
</style>
