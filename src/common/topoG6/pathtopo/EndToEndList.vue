<template>
    <section class="sectionBox">
        <!-- 端到端拓扑图页面 -->
        <div class="section-top">
            <Row class="fn_box">
                <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{ $t("comm_org") }}{{ $t("comm_colon") }}</label>
                    <div class="fn_item_box">
                        <TreeSelect v-model="treeValue" ref="TreeSelect" :data="treeData"
                            :placeholder="$t('comm_please_select')" :loadData="loadData" @onSelectChange="setOrg"
                            @onClear="onClear" @onFocus="focusFn"></TreeSelect>
                    </div>
                </div>
                </Col>
                <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{ $t("peertopeer_monitoring") }}{{ $t("comm_colon") }}</label>
                    <div class="fn_item_box">
                        <Select clearable filterable :only-filter-with-text="true" v-model="query.monitorStatus"
                            :placeholder="$t('comm_please_select')">
                            <Option v-for="item in runStateList" :value="item.value" :key="item.value">{{ item.label }}
                            </Option>
                        </Select>
                    </div>
                </div>
                </Col>
                <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label>
                    <div class="fn_item_box">
                        <Input v-model="query.keyWord" :placeholder="$t('peertopeer_tip')" :title="$t('peertopeer_tip')"
                            style="width: 440px" />
                    </div>
                </div>
                </Col>
            </Row>
            <div class="tool-btn" style="margin-top: -64px;">
                <div>
                    <Button class="jiaHao-btn" type="primary" @click="queryClick" :title="$t('common_query')">
                        <i class="iconfont icon-icon-query" />
                    </Button>
                </div>
            </div>
        </div>
        <div class="section-body fault-tab contentBox_bg">
            <div class="section-body-content">
                <div class="dialTest-tab-content" style="padding: 0">
                    <Loading :loading="pageLoading"></Loading>
                    <Table ref="tableList" stripe :columns="columns" :data="tableList" :no-data-text="pageLoading
                            ? ''
                            : tableList.length > 0
                                ? ''
                                 : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                        " @on-select="handleSelect" @on-select-cancel="handleCancel" @on-select-all="handleSelectAll"
                        @on-select-all-cancel="handleSelectAll">
                    </Table>
                </div>
                <div class="tab-page" v-if="tableList.length > 0">
                    <Page v-page :current.sync="query.pageNo" :page-size="pageSize" :page-size-opts="pageSizeOpts"
                        :total="totalCount" show-sizer show-elevator :prev-text="$t('common_previous')"
                        :next-text="$t('common_next_page')" @on-change="pageChange"
                        @on-page-size-change="pageSizeChange">
                    </Page>
                </div>
            </div>
        </div>
        <!-- 新增/修改表单 -->
        <FormModal v-bind:formModalShow.sync="formModalShow" ref="addForm" :rowData="rowData" :formType="formType"
            :modalTitle="modalTitle" @getList="getList(query)">
        </FormModal>
        <!-- 趋势图弹窗 -->
        <DetailModal :detailModalShow="detailModalShow" :indexData="indexData" @closeModal="detailModalShow = false">
        </DetailModal>
    </section>
</template>

<script>
import global from "@/common/global.js";
import locationreload from "@/common/locationReload";
import "@/config/page.js";
import "@/timechange.js";
import ipv6Format from "@/common/ipv6Format";
export default {
    name: 'index',
    components: {
        TreeSelect: () => import('@/common/treeSelect/treeSelect.vue'),
        FormModal: () => import('./FormModal.vue'),
        DetailModal: () => import('./DetailModal2.vue'),
    },
    props:['operationType','topologyId','filterTaskId'],
    data() {
        return {
             currentSkin: sessionStorage.getItem('dark') || 1,
            formModalShow: false,
            detailModalShow: false,
            pageLoading: false,
            formType: 'Add',
            modalTitle: this.$t('common_new'),
            treeValue: '',
            indexData: {},
            rowData: {},
            //权限对象
            permissionObj: {},
            //机构参数
            treeData: [],
            orgTree: false,
            orgLists: [],
            runStateList: [
                { label: this.$t('common_Normal'), value: 1 },
                { label: this.$t('phytopo_deterioration'), value: 2 },
                { label: this.$t('phytopo_interrupt'), value: 3 },
                { label: this.$t('comm_unknown'), value: 4 }
            ],
            //搜索字段
            query: {
                monitorStatus: '', //状态
                orgId: '', //组织机构
                keyWord: '', //关键字
                pageNo: 1, //页数
                pageSize: 10, //每页展示多少数据
            },
            columns: [
                {
                    type: "selection",
                    width: 35,
                    align: "left",
                },
                {
                    title: this.$t('comm_org'),
                    key: "orgName",
                    align: "left",
                    width: 200,
                },
                // {
                //     title: this.$t('comm_probe_ip'),
                //     key: "probeIp",
                //     align: "left",
                //     minWidth: 145,
                //     render: (h, params) => {
                //         let str = params.row.probeIp;
                //         str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                //         return h(
                //             "div",
                //             {
                //                 style: {
                //                     textAlign: "left",
                //                     width: "100%",
                //                     textIndent: "0px",
                //                     overflow: "hidden", //超出的文本隐藏
                //                     textOverflow: "ellipsis", //溢出用省略号显示
                //                     whiteSpace: "nowrap", //溢出不换行
                //                 },
                //                 attrs: {
                //                     title: str,
                //                 },
                //             },
                //             str
                //         );
                //     },
                // },
                // 路径名称
                {
                    title: this.$t('peertopeer_path_name'),
                    key: "name",
                    align: "left",
                    minWidth: 150,
                    render: (h, params) => {
                        let str = params.row.name;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        let str2 = ''
                        if (str.length > 8) {
                            str2 = str.slice(0, 7) + '...'
                            return h('Tooltip', {
                                props: {
                                    placement: 'bottom-start',
                                    transfer: true,
                                    maxWidth: 400
                                }
                            },
                                [str2, h(
                                    'span', {
                                    slot: 'content',


                                }, str
                                )]);
                        } else {
                            str2 = str
                            return h('span', str2)
                        }
                    }
                },
                {
                    title: this.$t('peertopeer_start_IP'),
                    key: "startTaskIp",
                    align: "left",
                    width: 250,
                    render: (h, params) => {
                        let str = params.row.startTaskIp;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                         let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                        // return h(
                        //     "div",
                        //     {
                        //         style: {
                        //             textAlign: "left",
                        //             width: "100%",
                        //             textIndent: "0px",
                        //             overflow: "hidden", //超出的文本隐藏
                        //             textOverflow: "ellipsis", //溢出用省略号显示
                        //             whiteSpace: "nowrap", //溢出不换行
                        //         },
                        //         attrs: {
                        //             title: str,
                        //         },
                        //     },
                        //     str
                        // );
                    }
                },
                {
                    title: this.$t('peertopeer_start_name'),
                    key: "startTaskName",
                    align: "left",
                    minWidth: 150,
                    render: (h, params) => {
                        let str = params.row.startTaskName;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        let str2 = ''
                        if (str.length > 8) {
                            str2 = str.slice(0, 6) + '...'
                            return h('Tooltip', {
                                props: {
                                    placement: 'bottom-start'
                                }
                            },
                                [str2, h(
                                    'span', {
                                    slot: 'content',


                                }, str
                                )]);
                        } else {
                            str2 = str
                            return h('span', str2)
                        }
                        // return h(
                        //     "div",
                        //     {
                        //     style: {
                        //         textAlign: "left",
                        //         width: "100%",
                        //         textIndent: "0px",
                        //         overflow: "hidden", //超出的文本隐藏
                        //         textOverflow: "ellipsis", //溢出用省略号显示
                        //         whiteSpace: "nowrap", //溢出不换行
                        //     },
                        //     attrs: {
                        //         title: str,
                        //     },
                        //     },
                        //     str
                        // );
                    }
                },
                {
                    title: this.$t('comm_target_ip'),
                    key: "endTaskIp",
                    width: 250,
                    align: "left",
                    render: (h, params) => {
                        let str = params.row.endTaskIp;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;

                        let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                        // return h(
                        //     "div",
                        //     {
                        //         style: {
                        //             textAlign: "left",
                        //             width: "100%",
                        //             textIndent: "0px",
                        //             overflow: "hidden", //超出的文本隐藏
                        //             textOverflow: "ellipsis", //溢出用省略号显示
                        //             whiteSpace: "nowrap", //溢出不换行
                        //         },
                        //         attrs: {
                        //             title: str,
                        //         },
                        //     },
                        //     str
                        // );
                    }
                },
                // 需要改
                {
                    title: this.$t('comm_target_name'),
                    key: "endTaskName",
                    align: "left",
                    width: 120,
                    render: (h, params) => {
                        let str = params.row.endTaskName;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        let str2 = ''
                        if (str.length > 6) {
                            str2 = str.slice(0, 5) + '...'
                            return h('Tooltip', {
                                props: {
                                    placement: 'bottom-start'
                                }
                            },
                                [str2, h(
                                    'span', {
                                    slot: 'content',


                                }, str
                                )]);
                        } else {
                            str2 = str
                            return h('span', str2)
                        }
                        // return h(
                        //     "div",
                        //     {
                        //     style: {
                        //         textAlign: "left",
                        //         width: "100%",
                        //         textIndent: "0px",
                        //         overflow: "hidden", //超出的文本隐藏
                        //         textOverflow: "ellipsis", //溢出用省略号显示
                        //         whiteSpace: "nowrap", //溢出不换行
                        //     },
                        //     attrs: {
                        //         title: str,
                        //     },
                        //     },
                        //     str
                        // );
                    },
                },
                {
                    title: this.$t('peertopeer_monitoring'),
                    key: "monitorStatus",
                    align: "center",
                    width: 160,
                    render: (h, params) => {
                        let str = params.row.monitorStatus,
                            text = "";
                        switch (str) {
                            case 1:
                                text = h(
                                    "span",
                                    {
                                        class: "runState runGreen"
                                    }
                                );
                                break;
                            case 2:
                                text = h(
                                    "span",
                                    {
                                        class: "runState runOrange"
                                    }
                                );
                                break;
                            case 3:
                                text = h(
                                    "span",
                                    {
                                        class: "runState runRed"
                                    }
                                );
                                break;
                            case 4:
                                text = h(
                                    "span",
                                    {
                                        class: "runState runGrey"
                                    }
                                );
                                break;
                        }
                        return h("div", [text]);
                    }
                }
            ],
            tableList: [],
            originalList: [], //保存原始列表数据
            orderTabList: [],
            totalCount: 0,
            //当前页数
            // currentNum: 1,
            pageNo: 1,
            pageSize: 10,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            selectedIds: [],
        }
    },
    methods: {
         handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        focusFn() {
            this.getTreeOrg()
        },
        queryClick() {
            //点击搜索
            // this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.keyWord = this.query.keyWord.trim();
            this.getList(this.query);
        },
        actionClick(rowData, type) {
            if (type === 'show') {
                // 详情
                this.detailModalShow = true
                this.indexData = { ...rowData }
            } else {
                this.formModalShow = true
                this.modalTitle = this.$t('common_update')
                this.formType = 'Edit'
                this.rowData = { ...rowData }
                this.$refs.addForm.getUpdateRouteTopology(rowData.id)
            }
        },
        // deleteClick() {
        //     let selectedIdsArrary = Array.from(this.selectedIds);
        //     let param = {
        //         ids: selectedIdsArrary.join(',')
        //     };
        //     if (selectedIdsArrary.length > 0) {
        //         top.window.$iviewModal.confirm({
        //             title: this.$t('common_delete_prompt'),
        //             content: this.$t('server_sure_delete'),
        //             onOk: () => {
        //                 this.$http.wisdomPost('/peertopeertopology/newDelete', param).then(res => {
        //                     if (res.code === 1) {
        //                         this.$Message.success(this.$t('comm_deleted_success'));
        //                     } else {
        //                         this.$Message.error(res.msg);
        //                     }
        //                     // this.currentNum = this.query.pageNo = 1;
        //                     this.getList(this.query);
        //                     this.selectedIds = new Set();
        //                 });
        //             }
        //         });
        //     } else {
        //         this.$Message.warning(this.$t('specquality_select'));
        //     }
        // },
        addClick() {
            this.formModalShow = true
            this.rowData = {}
            this.formType = 'Add'
            this.modalTitle = this.$t('common_new')
            // 新增数据回显
            this.$refs.addForm.setData()

        },
        pageChange(val) {
            // // 表格页码切换
            // this.currentNum = val;
            // this.query.pageNo = this.currentNum;
            this.getList(this.query);
        },
        pageSizeChange(e) {
            // 表格每页展示多少条数据切换
            // this.currentNum = 1;
            // this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList(this.query);
        },

        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
			// this.selectedDatas = [...new Set(this.selectedDatas.map(item=>JSON.stringify(item)))].map(i=>JSON.parse(i))// 数组去重
			if (slection.length === 0) {
				let data = this.$refs.tableList.data;
				data.forEach((item) => {
					if (this.selectedIds.includes(item.id)) {

						this.selectedIds.splice(
							this.selectedIds.findIndex((id) => id === item.id),
							1
						);
						this.selectedDatas.splice(
							this.selectedDatas.findIndex((current) => item.id === current.id),
							1
						);
					}
				});
			} else {
				slection.forEach((item) => {
					if (!this.selectedIds.includes(item.id)) {
						this.selectedIds.push(item.id);
					}
				});
			}
        },
        handleSelect(slection, row) {
			if (!this.selectedIds.includes(row.id)) {
				this.selectedIds.push(row.id);
			}

			let arrays = this.selectedDatas.filter(current=>{
				return row.id === current.id
			});
			if(arrays.length == 0){
				this.selectedDatas.push(row);
			}
        },
        handleCancel(slection, row) {
			this.selectedDatas =	[...new Set(this.selectedDatas.map(item=>JSON.stringify(item)))].map(i=>JSON.parse(i))// 数组去重
			this.selectedIds.splice(
				this.selectedIds.findIndex((id) => id === row.id),
				1
			);
			this.selectedDatas.splice(
				this.selectedDatas.findIndex((item) => item.id === row.id),
				1
			);
        },
        getList(param) {
            this.pageLoading = true;
            console.log('param',param);
            let params = {...param}
            // params.groupIds = params.groupIds.join(',')
            console.log('params',params);
            console.log('this.operationType=>',this.operationType);
            console.log('this.filterTaskId==>',this.filterTaskId);
            this.$http
                .wisdomPost('/peertopeertopology/newList', {...params,'topologyId':this.topologyId,'filterTaskId':this.filterTaskId, 
                'opType':this.operationType== 'add'?0:1})
                .then(res => {
                    if (res.code === 1) {
                        if (res.data) {
                            this.tableList = res.data.records;
                            this.totalCount = res.data.total || 0;
                        } else {
                            this.tableList = [];
                        }
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.pageLoading = false;
                });
        },
        getTreeOrg(param = null) {
            let _self = this;
            _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item
                    });
                    _self.treeData = treeNodeList;
                }
            })
        },
        loadData(item, callback) {
            // this.$refs.TreeSelect.$refs.Vinput.focus({ preventScroll: true})
            this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item
                    });
                    callback(childrenOrgList);
                }
            })
        },
        setOrg(item) {
            console.log(item);
            this.treeValue = item[0].name
            this.query.orgId = item[0] ? item[0].id : null;
        },
        onClear() {
            this.query.orgId = ''
            this.treeValue = ''
        },

    },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
     },
    mounted() {
        // 监听 storage 事件
         window.addEventListener('storage', this.handleStorageChange);
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        this.getList(this.query);
        this.getTreeOrg()
    }

}
</script>

<style>
.runState {
    display: inline-block;
    vertical-align: middle;
    width: 18px;
    height: 18px;
    border-radius: 50%;
}

.runRed {
    background: #ff0000;
}

.runGrey {
    background: darkgrey;
}

.runGreen {
    background: #07c5a3;
}

.runOrange {
    background: #f79232;
}

.sectionBox .section-top .fn_item .fn_item_label {
    min-width: 120px;
}
</style>