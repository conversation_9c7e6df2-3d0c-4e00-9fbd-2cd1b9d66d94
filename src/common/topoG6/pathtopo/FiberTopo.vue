<!-- 纤维图组件 -->
<template>
  <div class="container">
    <div class="fiber-topo" id="fiberTopo"></div>
    <!-- 遮图标 -->
    <div class="logo-box">
      <div class="text">{{ topoTitle }}</div>
    </div>
    <!-- /遮图标 -->
  </div>
</template>

<script>
import {
  
  Stage,
  Layer,
  Node,
  TextNode,
  Link,
  CurveLink,
  BezierLink,
  jtopo,

  Tooltip,
  CircleNode,
} from "@jtopo/core";
 //   fibreType 0 横向   1竖向

export default {
name: '',
components: {},
props:['fiberData','fiberTopoHeight','fiberTopoWidth','configData','fibreType','topoTabList',"tabsId",'fibreTopoLineLengthPercentage'],
data() {
return {
    fiberStage:null,
    fiberLayer:null,
    fiberTotoData:{
        nodeList:[],
        linkList:[]
    },
    handlerNodeData:{},
    allNode:[],
    topoTitle:''


}
},
computed: {},
watch: {
    tabsId() {
        this.getTopoTitle()
    }
    
},
methods: {
     getTopoTitle() {
      
     
        this.topoTabList.forEach(item => {
          if(item.id == this.tabsId) {
            this.topoTitle = item.topologyName
          }
        })
       

                
      },
     // s曲线控制点修改
  handleBezierLink() {
          // 贝塞尔曲线的控制点计算方法
      BezierLink.prototype.autoCalcCtrlPoint =  (a, z) =>{
        //判断控制点1和2，哪个在上面
        let zMax = z.y >= a.y;
        if(zMax){
          //// console.log(z.y-a.y)
        }
          let dx = z.x - a.x;
          let dy = z.y - a.y;

          // 设置平滑过渡的阈值
          const threshold = 10;
          const factor = Math.min(1, Math.abs(dy) / threshold);

          let cx = (z.x + a.x) / 2;
          let cy = (z.y + a.y) / 2;
           let len = null
           let angle = null
          //  console.log(this.layoutModel,'11111111111111111111111')
        //   console.log(this.layoutModel,'节点布局模式')
          //  debugger
        //   fibreType 0 横向   1竖向
          if(this.fibreType == 0) {
               len = Math.sqrt(dx * dx + dy * dy) / 2 * factor*1.1;//弧度
               angle = (Math.atan2(dy, dx) ) * -1;

          }else {
              len = Math.sqrt(dx * dx + dy * dy) / 2 * factor * 1.25; // 弧度
               angle = (Math.atan2(dy, dx) + Math.PI) * -1; // 调整角度计算来改变弯曲方向


          }

        
        // // console.log(angle)
      let controlPoint = {
              x: cx + len * Math.cos(angle),
              y: cy + len * Math.sin(angle)
          };

          // 如果控制点在左侧，将其向右移动20%
          // if (controlPoint.x > Math.min(a.x, z.x)) {
          //   // // console.log(1);
          //     controlPoint.x += Math.abs(a.x - z.x) *0.55;//左侧控制点位置
          // } else {
          //   controlPoint.x += Math.abs(a.x - z.x) *0; //右侧控制点位置
          // }

          return controlPoint;
};

  },
    init() {
    
        this.fiberStage = new Stage('fiberTopo');
        this.fiberLayer = new Layer()
        this.fiberStage.addChild(this.fiberLayer);
        this.fiberStage.hideToolbar();
        this.fiberStage.show()
       
        this.fiberLayer.css({
            backgroundColor:'transparent',
           
        })
       
        this.fiberLayer.translateTo(-this.fiberTopoWidth/2,-this.fiberTopoHeight / 2)
       
        //设置纤维图线长度
        if(this.fibreTopoLineLengthPercentage){
            if(this.fibreType == 0) {
                this.fiberTopoWidth = this.fiberTopoWidth*(this.fibreTopoLineLengthPercentage/100);
            }else{
                this.fiberTopoHeight = this.fiberTopoHeight*(this.fibreTopoLineLengthPercentage/100);
            }
        }
        

    },
    handleFiberData() {
        // debugger
        // 计算y轴间距
        // (总高-上下距离- 2个节点高度的一半)/个数
        let childCount = 0
       
        this.fiberData.children.forEach((item,i) => {
            if(item.children && item.children.length > 0) {
                childCount += item.children.length
            }else {
                childCount += 1

            }
            
            
        })

        
       let itemY = this.fiberTopoHeight / childCount 
        console.log(childCount,itemY,this.fiberTopoHeight,'childCount')
         let locY = itemY/2
         let locX = this.fiberTopoWidth - 300
         let firstOne = true
        this.fiberData.children.forEach((item,index) => {
           
           if(item.children && item.children.length > 0) {
            // 有子节点
            item.children.forEach((item2,index) => {
                console.log(item2,'item2')

                if(index === 0 &&    firstOne ) {
                    locY = itemY/2
                }else {
                     locY += itemY

                }
                
               
               item2.locY = locY
               item2.locX = locX
               item2.nodeColor = this.handleColor(item2.state)
               item2.textColor = this.handleColor(item2.state)
               console.log(item2,'item2...................')

                 
                this.fiberTotoData.nodeList.push(item2)
                this.fiberTotoData.linkList.push({
                    source:item.key,
                    target:item2.key,
                    color:this.handleColor(item2.state)

                })
            })
            firstOne = false

           }else {
            // 没有子节点
            if(index === 0 && firstOne) {
            
            }else {
                  locY += itemY

            }
            item.nodeColor = this.handleColor(item.state)
            item.textColor = this.handleColor(item.state)
          
            console.log(item,'item....................')
            this.fiberTotoData.nodeList.push({...item,locY,locX})
            this.fiberTotoData.linkList.push({
                    source:this.fiberData.key,
                    target:item.key,
                    color:this.handleColor(item.state)

                })
            firstOne = false

           }

        })
      
    
    // 处理第二层节点
    let sorceY = {
        start:null,
        end:null,
    }
    this.fiberData.children.forEach((item,index) => {
        if(item.children && item.children.length > 0) {
         
             let x = 300 + (this.fiberTopoWidth -600)/2
        let y = item.children[0].locY + (item.children[item.children.length - 1].locY - item.children[0].locY)/2
        this.fiberTotoData.nodeList.push({
            name:item.name,
            locY:y,
            locX:x,
            key:item.key,
            childSecond:true, //二层节点
             nodeColor:'#fff',
             textColor:this.configData.normalLineColor

        })
        if(index === 0) {
            sorceY.start = y
        }
        sorceY.end = y
        this.fiberTotoData.linkList.push({
            source:this.fiberData.key,
            target:item.key,
            color:'#fff'

        })

        }else {
            sorceY.start = 0
            sorceY.end = this.fiberTopoHeight 

        }
       
    })
       
       //    处理第一个节点
    this.fiberTotoData.nodeList.unshift({
        name:this.fiberData.name,
        sorce:true,   //源节点
        locY: sorceY.start + (sorceY.end - sorceY.start) / 2,
        locX: 300,
        key: this.fiberData.key,
        nodeColor:'#fff',
        textColor:'#fff'

    })
       this.handleJtopo()

        

        console.log(this.fiberTotoData,this.fiberData,'this.fiberTotoData')
    },
    // 竖向数据处理
    handleVerticalData() {
         // debugger
        // 计算y轴间距
        // (总高-上下距离- 2个节点高度的一半)/个数
        let childCount = 0
       
        this.fiberData.children.forEach((item,i) => {
            if(item.children && item.children.length > 0) {
                childCount += item.children.length
            }else {
                childCount += 1

            }
            
            
        })
        
       let itemX = this.fiberTopoWidth / childCount 
        // console.log(childCount,itemY,this.fiberTopoHeight,'childCount')
         let locX = itemX/2
         let locY = this.fiberTopoHeight - 150
         let firstOne = true
        this.fiberData.children.forEach((item,index) => {
           
           if(item.children && item.children.length > 0) {
            // 有子节点
            item.children.forEach((item2,index) => {
                console.log(item2,'item2')

                if(index === 0 &&    firstOne ) {
                    locX = itemX/2
                }else {
                     locX += itemX

                }
                
               
               item2.locY = locY
               
               item2.locX = locX
               item2.nodeColor = this.handleColor(item2.state)
               item2.textColor = this.handleColor(item2.state)
               console.log(item2,'item2...................')

                 
                this.fiberTotoData.nodeList.push(item2)
                this.fiberTotoData.linkList.push({
                    source:item.key,
                    target:item2.key,
                    color:this.handleColor(item2.state)

                })
            })
            firstOne = false

           }else {
            // 没有子节点
            if(index === 0 && firstOne) {
            
            }else {
                  locX += itemX

            }

            item.nodeColor = this.handleColor(item.state)
            item.textColor = this.handleColor(item.state)
          
            console.log(item,'item....................')
            this.fiberTotoData.nodeList.push({...item,locY,locX})
            this.fiberTotoData.linkList.push({
                    source:this.fiberData.key,
                    target:item.key,
                    color:this.handleColor(item.state)

                })
            firstOne = false

           }

        })
      
    
    // 处理第二层节点
    let sorceX = {
        start:null,
        end:null,
    }
    this.fiberData.children.forEach((item,index) => {
        if(item.children && item.children.length > 0) {
         
             let y = 50 + (this.fiberTopoHeight -100)/2
        let x = item.children[0].locX + (item.children[item.children.length - 1].locX - item.children[0].locX)/2
        this.fiberTotoData.nodeList.push({
            name:item.name,
            locY:y,
            locX:x,
            key:item.key,
            childSecond:true, //二层节点
             nodeColor:'#fff',
             textColor:this.configData.normalLineColor

        })
        if(index === 0) {
            sorceX.start = x
        }
        sorceX.end =x
        this.fiberTotoData.linkList.push({
            source:this.fiberData.key,
            target:item.key,
            color:'#fff'

        })

        }else {
            sorceX.start = 0
            sorceX.end = this.fiberTopoWidth 

        }
       
    })
       
       //    处理第一个节点
    this.fiberTotoData.nodeList.unshift({
        name:this.fiberData.name,
        key:this.fiberData.key,
        sorce:true,   //源节点
        locY:20 ,
        locX: sorceX.start + (sorceX.end - sorceX.start) / 2,
        nodeColor:'#fff',
        textColor:'#fff'

    })
       this.handleJtopo()
    },

    handleJtopo() {
        this.fiberTotoData.nodeList.forEach(item => {
            let node = null
            if(this.fibreType == 0) {
                 node = this.createNode(item)

            }else {
                node = this.createVerticalNode(item)
            }
            
            this.allNode.push(node)
             if (!this.handlerNodeData.key) {
            this.handlerNodeData[item.key] = node;
          }
            this.fiberLayer.addChild(node)
        })
        console.log(this.allNode,'this.fiberTotoData.linkList')
        this.fiberTotoData.linkList.forEach(item => {
            let link = this.createLink(item)
            this.fiberLayer.addChild(link)
        })

    },
    handleLinkData(source,target){
        
        let sourceNode = {}
        let targetNode = {}
        // this.allNode.forEach(item => {
        //     if(item.source)
        // })

    },
    
    // 创建节点
    createNode(data) {
        let {locX,locY,name,nodeColor,textColor} = data
        // value.data.name.length > 30 ? value.data.name.substring(0, 30) + '...' : value.data.name;
        let name2 = name.length > 30 ? name.substring(0, 30) + '...' : name 
        let node = new CircleNode( '',locX,locY)
        // 文字位置，一级二级节点左边，最后一级节点右边
         let offestX = 110
         let textP = 'left'
        if(data.sorce || data.childSecond) {
            offestX = -110
            textP = 'right'
            
        }
          let textNode = new Node('',offestX,0,200,16)
       

        let htmlImage = new jtopo.HtmlImage(`
            <div xmlns="http://www.w3.org/1999/xhtml"
            style="height:100%;width:100%;background-color:transparent;transform;">
            <div style= "height:100%;width:100%;background-color:transparent;line-height:16px;color:${textColor};font-size:10px;text-align:${textP};"><span id="tooltip" >${name2}</span></div>

          `, textNode.width, textNode.height
                        
        )
        htmlImage.getCanvas().then(function(canvas){
            textNode.setImage(canvas);
            });
             textNode.draggable = false
        textNode.showSelected = false
        node.addChild(textNode)
        // 如果缩略鼠标悬浮显示全部信息
        if(name.length > 30) {
            console.log(locX,locY,'节点坐标')
            // 鼠标指向小提示
            var tooltip = new Tooltip(this.fiberStage);
            tooltip.setHtml(name);
            textNode.on('mousemove', (e) => {
                // console.log(e,'eeeeeeeeeeeeeeeeeeeeeeeeee')
                let is = this.fiberStage.inputSystem;
                console.log(is.x,locX,'鼠标位置鼠标位置')
                console.log(this.fiberLayer.scaleX,'画布缩放系数')
                tooltip.showAt(is.x + 20, is.y + 20);
               
           
            })
            
        }
        
        node.setRadius(6)
       
        if(data.sorce || data.childSecond) {
              node.css({
            fillStyle:nodeColor,
            color:textColor,
            textPosition:'lm',
            textAlign:'right',
            textBaseline:'middle',


        })
             
        }else {
           
        node.css({
            fillStyle:nodeColor,
            color:textColor,
            textPosition:'rm',
            textAlign:'left',
            textBaseline:'middle',
            transform: 'rotate(90deg)'

        })

            
        }
         node.on('click', (e) => {
            if(data.sorce || data.childSecond) {

            }else {
                let param = {
                    data:data
                }
                this.$emit('nodeClick',param)
            }
        })
        node.data = data
        node.zIndex = 3
        return node

    },
    // 竖向节点
    createVerticalNode(data) {
        
         let {locX,locY,name,nodeColor,textColor} = data
          let name2 = name.length > 30 ? name.substring(0, 30) + '...' : name 
          console.log(name2,name,'数据前后对比')
        let node = new CircleNode( '',locX,locY)
        // 文字位置，一级二级节点左边，最后一级节点右边
        
        node.setRadius(6)
           node.css({
            fillStyle:nodeColor,
           


        })
        // 如果是一层二层节点，位置偏移
        let offestX = 0
        if(data.sorce || data.childSecond) {
            offestX = -10
        }
        let textNode = new Node('',offestX,110,200,200)
       

        let htmlImage = new jtopo.HtmlImage(`
            <div xmlns="http://www.w3.org/1999/xhtml"
            style="height:100%;width:100%;background-color:transparent;transform;">
            <div style= "height:100%;width:100%;background-color:transparent; transform: rotate(90deg);line-height:200px;color:${textColor};font-size:10px;text-align: left;"><span id="tooltip" >${name2}</span></div>

          `, textNode.width, textNode.height
                        
        )
        htmlImage.getCanvas().then(function(canvas){
            textNode.setImage(canvas);
            });
             textNode.draggable = false
        textNode.showSelected = false
        node.addChild(textNode)
        // 如果缩略鼠标悬浮显示全部信息
        if(name.length > 30) {
            console.log(locX,locY,'节点坐标')
            // 鼠标指向小提示
            var tooltip = new Tooltip(this.fiberStage);
            tooltip.setHtml(name);
            textNode.on('mousemove', (e) => {
                // console.log(e,'eeeeeeeeeeeeeeeeeeeeeeeeee')
                let is = this.fiberStage.inputSystem;
                console.log(is.x,locX,'鼠标位置鼠标位置')
                console.log(this.fiberLayer.scaleX,'画布缩放系数')
                if(data.sorce || data.childSecond) {
                     if(is.x > locX -20*this.fiberLayer.scaleX && is.x < locX  + 10*this.fiberLayer.scaleX) {
                    console.log('范围内')
                     tooltip.showAt(is.x + 20, is.y + 20);

                }

                }else {
                     tooltip.showAt(is.x + 20, is.y + 20);
                }
               
           
            })
            
        }

        node.on('click', (e) => {
            if(data.sorce || data.childSecond) {

            }else {
                let param = {
                    data:data
                }
                this.$emit('nodeClick',param)
            }
        })
       

       
      
        node.data = data
        node.zIndex = 3
        return node


    },
    // 创建连线
    createLink(data) {
        console.log(this.handlerNodeData[data.source],
        this.handlerNodeData[data.target],)
        let link =  null
        if(this.fibreType == 0) {
            link = new BezierLink(
         '',
        this.handlerNodeData[data.source],
        this.handlerNodeData[data.target],
        'rm','lm'
      );

        }else {
             link = new BezierLink(
         '',
        this.handlerNodeData[data.source],
        this.handlerNodeData[data.target],
        'cb','ct'
      );
        }
      
       
          
       
      
      link.css({
        strokeStyle:data.color,
      })

      return link
        


    },
    // 处理颜色
    handleColor(state) {
        let color = ''
        if(state === 1000) {
            color = this.configData.normalLineColor
        }else if(state == 1) {
            color = this.configData.breakLineColor
        }else {
            color = this.configData.degradationLineColor
        }

        return color
    }

},
created() {
    console.log('纤维图组件渲染了')
   

},
mounted() {
    console.log(this.topoTabList,this.tabsId,'纤维图组件渲染了数据遮挡')
    this.getTopoTitle()
    this.handleBezierLink()
      this.init()
    if(this.fibreType == 0) {
        this.handleFiberData()

    }else {
       this.handleVerticalData()
    }
    
    
    

},
}
</script>
<style scoped lang='less'>
.container {
  width: 100%;
  height: 100%;
}
.fiber-topo {
  width: 100%;
  height: 100%;
}
.logo-box {
  width: 140px;
  height: 28px;
  background-color: #061824;
  // border: 1px solid pink;
  position: fixed;
  left: 70px;
  bottom: 0;
  z-index: 666;
  display: flex;
  align-items: center;
  justify-content: center;
  .text {
    width: 90%;
    height: 28px;
    line-height: 28px;
    color: #5ca0d5;
    font-weight: 700;
    overflow: hidden; /* 确保超出容器的文本被裁剪 */
    white-space: nowrap; /* 防止文本换行 */
    text-overflow: ellipsis; /* 在文本末端显示省略号 */
  }
}
</style>