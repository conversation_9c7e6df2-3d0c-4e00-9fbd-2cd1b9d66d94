<template>
  <Modal
    sticky
    :value="formModalShow"
    width="1100"
    class="peertopeer-modal"
    :title="modalTitle"
    draggable
    :mask="true"
    :mask-closable="false"
    @on-visible-change="visiblechange"
    @on-cancel="editCancel"
  >
    <Form :model="formData" :rules="ruleValidate" ref="editForm" @submit.native.prevent>
      <!-- <div style="margin-bottom: 20px;">
                    
                        <Row class="fn_box">
                            <Col span="12">
                                <FormItem :label="$t('comm_org')+$t('comm_colon')" prop="orgId">
                                    <TreeSelect 
                                     v-model="treeValue" 
                                     ref="TreeSelect" 
                                     :data='treeData' 
                                     :placeholder="$t('snmp_pl_man')" 
                                     :loadData="loadOrgTreeData"
                                     @onSelectChange="setOrg" 
                                     @onClear='onClear'  
                                     @onFocus="focusFn">
                                    </TreeSelect>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem :label="$t('peertopeer_path_name2')" prop="name">
                                    <Input   v-model.trim="formData.name" :title="formData.name" maxlength="50"/>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem :label="$t('peertopeer_probe')" prop="probeId">
                                    <Select   v-model="formData.probeId" filterable :filterByLabel='true' :placeholder="$t('comm_select_probe')" @on-select='selectProbeId'>
                                        <Option v-for="(item) in probeList" :value=" String(item.id) " :tag="item.id" :label='item.labelName'
                                            :key="item.id">{{ item.labelName }}</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem :label="$t('peertopeer_initial_node')" prop="startTaskId">
                                    <Select   v-model="formData.startTaskId" filterable :filterByLabel='true'  :placeholder="$t('peertopeer_select_initial')" @on-select='selectStartTaskId'>
                                        <Option v-for="item in taskList" :value="item.value" :key="item.value" :label='item.label' style="width:410px">{{ item.label }}</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem :label="$t('peertopeer_target_node')" prop="endTaskId">
                                    <Select   v-model="formData.endTaskId" filterable :placeholder="$t('peertopeer_select_target')" :filterByLabel='true' @on-select='selectEndTaskId'>
                                        <Option v-for="item in taskList" :value="item.value" :key="item.value" :label='item.label' style="width:410px">{{ item.label }}</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                        <Row class="fn_box">
                            <Col span="24">
                                <FormItem :label="$t('phytopo_desc')+$t('comm_colon')" prop="marks">
                                    <Input  v-model.trim="formData.marks" :title="formData.marks" type="textarea" :placeholder="$t('group_description_content')" maxlength="200" show-word-limit :rows="6"/>
                                </FormItem>
                            </Col>
                        </Row>
                    </div> -->
      <Row>
        <Col span="12">
          <!-- 机构 -->
          <FormItem
            :label-width="110"
            :label="$t('comm_org') + $t('comm_colon')"
            prop="orgId"
          >
            <TreeSelect
              v-model="treeValue"
              ref="TreeSelect"
              :data="treeData"
              :placeholder="$t('snmp_pl_man')"
              :loadData="loadOrgTreeData"
              @onSelectChange="setOrg"
              @onClear="onClear"
              @onFocus="focusFn"
            >
            </TreeSelect>
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem :label-width="110" :label="$t('peertopeer_path_name2')" prop="name">
            <Input v-model.trim="formData.name" :title="formData.name" maxlength="50" />
          </FormItem>
        </Col>
      </Row>

      <!-- 路径名称 -->

      <Row :gutter="16" style="margin-left: 100px">
        <Col span="8">{{ $t("comm_probe") }}</Col>
        <Col span="8">{{ $t("comm_task") }}</Col>
        <Col span="6">{{ $t("prob_direction") }}</Col>
        <Col span="2">
          <Icon
            v-if="formData.peerToPeerTopologySubtasks.length < 10"
            @click="addTask"
            type="ios-add-circle-outline"
            color="#05EEFF"
            size="22"
          />
        </Col>
      </Row>
      <Row
        :gutter="16"
        style="margin-left: 100px"
        v-for="(item, i) in formData.peerToPeerTopologySubtasks"
        :key="i"
      >
        <!-- 探针 -->
        <Col span="8">
          <FormItem>
            <Select
              clearable
              filterable
              v-model="formData.peerToPeerTopologySubtasks[i].probeId"
              @on-select="selectProbeId(i)"
              @on-clear="clearProbeId(i)"
            >
              <Option
                v-for="item in probeList"
                :key="item.id"
                :value="String(item.id)"
                :label="item.labelName"
              ></Option>
            </Select>
          </FormItem>
          <!-- 校验文字 -->
          <span v-if="item.validProbeId" class="validate-text">{{
            formMessage.probe
          }}</span>
        </Col>
        <!-- 任务 -->
        <Col span="8">
          <FormItem>
            <Select
              clearable
              filterable
              v-model="formData.peerToPeerTopologySubtasks[i].sysDialConfigId"
              @on-open-change="(isOpen) => taskOpen(isOpen, i)"
              @on-clear="clearTask(i)"
              @on-select="selectTask(i)"
            >
              <Option
                v-for="item in taskList[i]"
                :value="item.value"
                :key="item.value"
                :label="item.label"
                >{{ item.label }}</Option
              >
            </Select>
            <Spin v-if="taskSelectLoading" fix></Spin>
          </FormItem>
          <!-- 校验文字 -->
          <span v-if="item.validTask" class="validate-text">{{ formMessage.task }}</span>
        </Col>
        <!-- 方向 -->
        <Col span="6">
          <FormItem>
            <Select
              v-model="formData.peerToPeerTopologySubtasks[i].direction"
              @on-select="selectDirection(i)"
            >
              <Option v-for="item in directionValue" :key="item.id" :value="item.id">{{
                item.label
              }}</Option>
            </Select>
          </FormItem>
          <!-- 校验文字 -->
          <span v-if="item.validDirection" class="validate-text">{{
            formMessage.direction
          }}</span>
        </Col>
        <Col span="2">
          <Icon
            @click="deleteTask(i)"
            v-if="formData.peerToPeerTopologySubtasks.length > 2"
            type="ios-remove-circle-outline"
            color="#FE5C5C"
            size="22"
          />
        </Col>
      </Row>
      <Row class="fn_box">
        <Col span="24">
          <FormItem
            :label-width="110"
            :label="$t('phytopo_desc') + $t('comm_colon')"
            prop="marks"
          >
            <Input
              v-model.trim="formData.marks"
              :title="formData.marks"
              type="textarea"
              :placeholder="$t('group_description_content')"
              maxlength="200"
              show-word-limit
              :rows="6"
            />
          </FormItem>
        </Col>
      </Row>
    </Form>

    <div slot="footer">
      <Button class="cancel-btn" @click="editCancel()">{{ $t("common_cancel") }}</Button>
      <Button class="primary-btn" @click="editOk()">{{ $t("common_verify") }}</Button>
    </div>
  </Modal>
</template>

<script>
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
export default {
  name: "FormModal",
  props: {
    formModalShow: {
      typeof: Boolean,
      default: false,
    },
    modalTitle: {
      typeof: String,
      default: "",
    },
    formType: {
      typeof: String,
      default: "Add",
    },
    rowData: {
      typeof: Object,
      default: null,
    },
  },
  components: {
    TreeSelect,
  },
  data() {
    return {
      formrules: [
        { required: true, message: "请输入名称", trigger: "blur" },
        { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
      ],
      formMessage: {
        probe: this.$t("comm_select_probe"),
        task: this.$t("testspeed_select_task"),
        direction: this.$t("topo_pl_dire"),
      },
      directionValue: [
        { id: 0, label: this.$t("prob_direction_Backward") },
        { id: 1, label: this.$t("prob_direction_Forward") },
      ],
      // formData:{
      //     orgId:'',
      //     name:'',
      //     probeId:'',
      //     probeName:'',
      //     startTaskId:'',
      //     startTaskName:'',
      //     endTaskId:'',
      //     endTaskName:'',
      //     marks:'',
      // },
      selectLoading: false,
      formData: {
        orgId: "",
        name: "",
        marks: "",
        peerToPeerTopologySubtasks: [
          { direction: "", sysDialConfigId: "", probeId: "" },
          { direction: "", sysDialConfigId: "", probeId: "" },
        ],
      },
      totalTaskList: [],
      treeValue: "",
      treeData: [],
      probeList: [],
      taskList: [],
      taskCount: 0,
      taskSelectLoading : false,// 开始加载，禁用下拉框

      //验证规则
      ruleValidate: {
        orgId: [{ required: true, message: this.$t("comm_select_org") }],
        name: [{ required: true, message: this.$t("peertopeer_select_name") }],
        // probeId: [
        //     { required: true, message: this.$t('peertopeer_select_probe') },
        // ],
        // startTaskId: [{ required: true, message: this.$t('peertopeer_select_initial') }],
        // endTaskId: [{ required: true, message: this.$t('peertopeer_select_target') }],
      },
    };
  },
  created() {},
  methods: {
    // 删除探针，校验提示
    clearProbeId(i) {
      this.formData.peerToPeerTopologySubtasks[i].validProbeId = true;
    },
    clearTask(i) {
      this.formData.peerToPeerTopologySubtasks[i].validTask = true;
    },
    selectDirection(i) {
      this.formData.peerToPeerTopologySubtasks[i].validDirection = false;
    },
    selectTask(i) {
      console.log("执行selectTask");
      this.formData.peerToPeerTopologySubtasks[i].validTask = false;
    },
    // // 选择任务
    // taskOpen(i) {
    //   this.taskList[i] = [];

    // },
    async taskOpen( openStatus,i) {
      // 只有在打开的时候才需要执行
      console.log("index",i);
      console.log("openStatus",openStatus);
      if(!openStatus){
        return
      }
      console.log("执行taksopen");
      this.taskList[i] = [];
      this.taskSelectLoading=true
      try {
        await this.getNodeData(
          this.formData.orgId,
          this.formData.peerToPeerTopologySubtasks[i].probeId,
          i
        );
      } catch (error) {
        // 处理错误
        console.error("Failed to fetch tasks:", error);
      }finally{
        this.taskSelectLoading=false
      }
    },
    // 获取编辑详情
    async getUpdateRouteTopology(id) {
      const res = await this.$http.post("/peertopeertopology/getUpdateRouteTopology", {
        id,
      });
      console.log(res);

      this.formData = res.data;
      //  this.processArrayConcurrently(res.data.peerToPeerTopologySubtasks)
      // this.formData.peerToPeerTopologySubtasks.forEach(item=>{
      //   item.isEditStatus = true

      // })
      console.log(this.formData.peerToPeerTopologySubtasks);
      // debugger
      await this.getNodeData(this.formData.orgId);
    },
    async processArrayConcurrently(arr) {
      console.log(arr);
      // debugger
      arr.map(async (item, index) => {
        await this.getNodeData(this.formData.orgId, item.probeId, index);
      });
      console.log(this.formData.peerToPeerTopologySubtasks);
    },
    // 新增任务
    addTask() {
      // this.taskCount ++
      if (this.formData.peerToPeerTopologySubtasks.length < 10) {
        let obj = {
          direction: "",
          sysDialConfigId: "",
          probeId: "",
        };
        this.formData.peerToPeerTopologySubtasks.push(obj);
      }
    },
    // 删除任务
    deleteTask(i) {
      if (this.formData.peerToPeerTopologySubtasks.length > 2) {
        this.formData.peerToPeerTopologySubtasks.splice(i, 1);
      }
      //  删除应该把绑定的任务列表对应删除
      this.taskList.splice(i, 1);
    },
    // 新增 机构数据回显
    setData() {
      console.log("新增编辑");
      let token = JSON.parse(sessionStorage.getItem("accessToken"));
      if (token && token.user) {
        // this.loginUserOrgId = token.user.orgId;
        this.treeValue = token.user.orgName;
        this.formData.orgId = token.user.orgId;
        this.getGetherList(this.formData.orgId);
      }
    },
    focusFn() {
      this.getTreeOrg();
    },
    visiblechange(val) {
      if (val) {
        this.getTreeOrg();
        if (this.formType === "Edit") {
          this.treeValue = this.rowData.orgName;
          console.log("this.rowData", this.rowData);
          // this.formData = {...this.rowData}
          this.formData.orgId = this.rowData.orgId;
          // this.formData.marks = this.rowData.marks
          // this.formData.name = this.rowData.name
          this.getGetherList(this.formData.orgId);
          // this.getNodeData(this.formData.orgId,this.formData.probeId)
        }
      } else {
        // 关闭模态框，重置数据，重置校验
        this.formData = {
          orgId: "",
          name: "",
          marks: "",
          peerToPeerTopologySubtasks: [
            { direction: "", sysDialConfigId: "", probeId: "" },
            { direction: "", sysDialConfigId: "", probeId: "" },
          ],
        };
        this.$refs.editForm.resetFields();
      }
    },
    setOrg(item) {
      this.treeValue = item[0].name;
      this.formData.orgId = item[0] ? item[0].id : null;
      console.log(this.formData);
      this.probeList = [];
      this.formData.probeId = "";
      this.taskList = [];
      this.formData.startTaskId = "";
      this.formData.endTaskId = "";
      this.getGetherList(this.formData.orgId);
    },
    onClear() {
      this.probeList = [];
      this.formData.probeId = "";
      this.taskList = [];
      this.formData.startTaskId = "";
      this.formData.endTaskId = "";
      this.treeValue = "";
      this.formData.orgId = "";
    },
    getGetherList(orgId) {
      this.$http
        .PostJson("/sys/gether/findSysGetherVo", { type: 1, orgId: orgId })
        .then(({ code, data, msg }) => {
          if (code === 1 && typeof data === "object") {
            this.probeList = data
              .filter((item) => {
                return item && typeof item.ip === "string";
              })
              .map(({ ip, code, labelName, id }) => {
                return { value: ip, label: code, labelName: labelName, id: id };
              });
          } else {
            throw new Error(String(msg));
          }
        });
    },
    // 获取任务列表
    async getNodeData(orgId, probeId = "", i = "") {
      let obj = {
        orgId: orgId,
      };
      if (probeId) {
        obj.probeId = probeId;
        this.selectLoading = true;
      }
      console.log(orgId, probeId, i);

      try {
        const res = await this.$http.post(
          "/peertopeertopology/findPeertopeerSysDialConfigVo",
          obj
        );
        if (res.code === 1) {
          const taskData = res.data;
          let arr = taskData.map((item) => {
            let label = item.destPort
              ? `${item.destIpName ?? "--"}(${item.destIp ?? "--"}:${item.destPort})`
              : `${item.destIpName ?? "--"}(${item.destIp ?? "--"})`;
            return {
              value: item.id,
              label: label,
            };
          });

          if (probeId) {
            // 直接更新对应的任务列表
            this.$set(this.taskList, i, arr);
          } else {
            // 如果没有提供探针 ID，可能是编辑回显，更新所有任务列表
            this.formData.peerToPeerTopologySubtasks.forEach((item, index) => {
              this.$set(this.taskList, index, arr);
            });
          }
          this.selectLoading = false;
        }
      } catch (error) {
        console.error("Failed to fetch tasks:", error);
        this.selectLoading = false;
      }
    },
    async selectProbeId(i) {
      // 赋值探针ip
      //  this.formData.probeId = val.tag;
      //  this.taskList = [];
      //  this.formData.startTaskId = '';
      //  this.formData.endTaskId = '';
      // 选择探针ip后，再获取开始和结束节点
      // 选择探针后，校验关闭
      this.formData.peerToPeerTopologySubtasks[i].validProbeId = false;
      setTimeout(() => {
        this.getNodeData(
          this.formData.orgId,
          this.formData.peerToPeerTopologySubtasks[i].probeId,
          i
        );
      }, 500);
    },
    selectStartTaskId(val) {
      this.formData.startTaskId = val.value;
    },
    selectEndTaskId(val) {
      this.formData.endTaskId = val.value;
    },
    getTreeOrg(param = null) {
      this.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            return item;
          });
          this.treeData = treeNodeList;
        }
      });
    },
    loadOrgTreeData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    editOk() {
      // debugger
      // 先校验任务队
      // debugger
      console.log(this.formData.peerToPeerTopologySubtasks);
      this.formData.peerToPeerTopologySubtasks.forEach((item) => {
        // debugger
        // if(item.probeId  == '' || item.probeId == undefined) {
        //   this.$set(item,'validProbeId',true)
        // } else if(item.sysDialConfigId == '' || item.sysDialConfigId == undefined) {
        //   this.$set(item,'validTask',true)

        // } else if(item.direction == '' || item.direction == undefined) {
        //   this.$set(item,'validDirection',true)
        // }
        item.probeId === ""
          ? this.$set(item, "validProbeId", true)
          : item.probeId === undefined
          ? this.$set(item, "validProbeId", true)
          : this.$set(item, "validProbeId", false);
        item.sysDialConfigId === ""
          ? this.$set(item, "validTask", true)
          : item.sysDialConfigId === undefined
          ? this.$set(item, "validTask", true)
          : this.$set(item, "validTask", false);
        item.direction === ""
          ? this.$set(item, "validDirection", true)
          : item.direction === undefined
          ? this.$set(item, "validDirection", true)
          : this.$set(item, "validDirection", false);
      });
      const isOk = this.formData.peerToPeerTopologySubtasks.every((item) => {
        return (
          item.validProbeId == false &&
          item.validTask == false &&
          item.validDirection == false
        );
      });

      console.log(this.formData.peerToPeerTopologySubtasks);
      this.$refs["editForm"].validate((valid) => {
        if (valid && isOk) {
          let requestUrl = "";
          let params = null;
          if (this.formType === "Add") {
            requestUrl = "/peertopeertopology/newAdd";
            params = { ...this.formData };
          } else if (this.formType === "Edit") {
            requestUrl = "/peertopeertopology/newUpdate";
            params = { ...this.formData, id: this.rowData.id };
          }
          this.$http.PostJson(requestUrl, params).then((res) => {
            if (res.code === 1) {
              this.$Message.success(res.msg);
              this.clearForm();
              this.$emit("update:formModalShow", false);

              this.$emit("getList");
            } else {
              this.$Message.error(res.msg);
            }
          });
        }
      });
    },
    // 清空表单
    clearForm() {
      this.treeValue = "";
      this.probeList = [];
      this.taskList = [];
      this.$refs.editForm.resetFields();
    },
    editCancel() {
      this.clearForm();
      this.$emit("update:formModalShow", false);
    },
  },
};
</script>

<style></style>
