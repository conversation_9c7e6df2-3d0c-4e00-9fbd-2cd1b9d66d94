<template>
  <div>
    <Table
      ref="pathList"
      stripe
      :height="getTableHeight"
      :columns="columns"
      :data="tabList"
      size="small"
      :no-data-text="
        tabList.length > 0
          ? ''
          : currentSkin == 1
          ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
            $t('common_No_data') +
            '</p></div>'
          : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
            $t('common_No_data') +
            '</p></div>'
      "
    >
    </Table>
    <div class="tab-page" v-if="tabList.length > 0">
      <Page
        v-page
        :current.sync="currentNum"
        :page-size="pageSize"
        :total="totalCount"
        :page-size-opts="pageSizeOpts"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-elevator
        show-sizer
      >
      </Page>
    </div>
    <!--指标-->
    <Modal
      :styles="{ top: '140px' }"
      sticky
      v-model="indexModal.show"
      :width="modalWidth"
      class="index-modal probetaskModal"
      :title="$t('probetask_two_details') + displayId + ')'"
      draggable
      :mask="true"
      :footer-hide="true"
    >
      <indexItem
        ref="indexEvent"
        :indexData="indexModal.data"
        :isSpecial="echartIsSpecial"
        :High="High"
      ></indexItem>
    </Modal>
  </div>
</template>

<script>
import Q from 'q';
 import langFn  from '@/common/mixins/langFn';
import global from "@/common/global.js";

export default {
    name: 'pathList',
      mixins: [langFn],
    props: ['taskId', 'startTime', 'endTime', 'rowData'],
    components: {
        indexItem: () => import('./indexItemCopy.vue')
    },
    computed: {
         getTableHeight() {
            let height = 820
            if(this.tabList.length > 0) {
            height = this.tabList.length * 50 + 50
            }
            if(height > 820) {
                height = 820
            }
            return height


        },
    },
    data() {
        return {
              currentSkin: sessionStorage.getItem('dark') || 1,
              modalWidth:localStorage.getItem('modalWidth') * 0.98 || '99%',
            query: {
                taskId: '',
                pageNo: 1,
                pageSize: 10
            },
            tabList: [],
            currentNum: 1,
            pageNo: 1,
            pageSize: 10,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            columns: [
                {
                    title: this.$t('server_view'),
                    key: "taskNum",
                    align: "center",
                    width:80,
                    render: (h, params) => {
                        let str = params.row.taskNum,
                            i = h("i", {
                                class: "icon-box icon-index myIconClass",
                                style: 'cursor: pointer;',
                                on: {
                                    click: () => {
                                        this.actionClick(params.row, "index", event);
                                    },
                                },
                            }),
                            array = [i];
                        return h("div", array);
                    },
                },
                {
                    title: this.$t('snmp_task_code'),
                    key: "displayId",
                    align: "center",
                      width:300,
                    render: (h, params) => {
                        let str = params.row.displayId,
                            span = h(
                                "span",
                                {
                                    class: "icon-index-name",
                                    style: {
                                        color: params.row.status ? "#5ce959" :"var(--task_path_list_table_color,#ffffff)",
                                    },
                                },
                                str === undefined || str === null || str === "" ? "--" : str
                            ),
                            array = [span];
                        return h("div", array);
                    },
                },
                {
                    title: this.$t('gether_last_time'),
                    key: "reportTimeFormat",
                    align: "center",
                       width:200,
                    render: (h, params) => {
                        let str = params.row.reportTimeFormat;
                        return h(
                            "div",
                            {
                                style: {
                                    textAlign: "left",
                                    color: params.row.status ? "#5ce959" : "var(--task_path_list_table_color,#ffffff)",
                                    width: "100%",
                                    textIndent: "0px",
                                    overflow: "hidden", //超出的文本隐藏
                                    textOverflow: "ellipsis", //溢出用省略号显示
                                    whiteSpace: "nowrap", //溢出不换行
                                },
                                attrs: {
                                    title: str,
                                },
                            },
                            str
                        );

                    },
                },
                {
                    title: this.$t('comm_probe_ip'),
                    key: "sourceIp",
                    align: "center",
                    render: (h, params) => {
                        let str = params.row.sourceIp;
                        return h(
                            "span",
                            {
                                style: {
                                    color: params.row.status ? "#5ce959" : "var(--task_path_list_table_color,#ffffff)",
                                },
                            },
                            str === undefined || str === null || str === "" ? "--" : str
                        );
                    },
                },
                {
                    title: this.$t('comm_probe_port'),
                    key: "sourcePort",
                    align: "center",
                     width:200,
                    render: (h, params) => {
                        let str = params.row.sourcePort;
                        let routePath = params.row.routePath || "";
                        let routePathType =
                            routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
                        let routePathType2 =
                            routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
                        let text = "";
                        if (str === undefined || str === null || str === "") {
                            text = "--";
                        } else if (
                            params.row.configType == 1 ||
                            params.row.configType == 2 ||
                            params.row.configType == 5 ||
                            params.row.configType == 6
                        ) {
                            text = "--";
                        } else if (
                            (params.row.configType == 3 && routePathType == 2) ||
                            (params.row.configType == 3 && routePathType == 1 && str < 20) ||
                            (params.row.configType == 3 && routePathType == 3 && str < 20) ||
                            (params.row.configType == 8 && routePathType2 == 2)
                        ) {
                            text = "--";
                        } else if (str < 20) {
                            text = "--";
                        } else {
                            text = str;
                        }
                        return h("span",
                            {
                                style: {
                                    color: params.row.status ? "#5ce959" :"var(--task_path_list_table_color,#ffffff)",
                                },
                            },
                            text);
                    },
                },
                {
                    title: this.$t('comm_target_ip'),
                    key: "destIp",
                    align: "center",
                    ellipsis: true,
                    render: (h, params) => {
                        let config_type = params.row.configType;
                        let text = "";
                        let str = params.row.destIp;
                        if (str == undefined || str == null || str == "") {
                            text = "--";
                        } else if (str.length > 16) {
                            text = str.substring(0, 16) + "...";
                        } else {
                            text = str;
                        }
                        let dest_ip_name = params.row.destIpName;
                        let nameText = "";
                        if (
                            dest_ip_name == undefined ||
                            dest_ip_name == null ||
                            dest_ip_name == ""
                        ) {
                            nameText = "--";
                        } else {
                            nameText = dest_ip_name;
                        }
                        let title =
                            (text != "--"
                                ? (config_type != 8 ? this.$t('access_destination_ip')+"：" : this.$t('access_destination_acc')+"：") + str
                                : "")
                        return h(
                            "span",
                            {
                                class: {
                                    "text-ellipsis": true,
                                },

                                style: {
                                    color: params.row.status ? "#5ce959" : "var(--task_path_list_table_color,#ffffff)",
                                },

                                domProps: {
                                    title: text == "--" && nameText == "--" ? "" : title,
                                },
                            },
                            text
                        );
                    },
                },
                // {
                //     title: this.$t('comm_target_name'),
                //     key: "destIpName",
                //     align: "center",
                //     width: 120,
                //     render: (h, params) => {
                //         let str = params.row.destIpName;
                //         str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                //         return h(
                //             "div",
                //             {
                //                 style: {
                //                     textAlign: "left",
                //                     color: params.row.status ? "#5ce959" : "#ffffff",
                //                     width: "100%",
                //                     textIndent: "0px",
                //                     overflow: "hidden", //超出的文本隐藏
                //                     textOverflow: "ellipsis", //溢出用省略号显示
                //                     whiteSpace: "nowrap", //溢出不换行
                //                 },
                //                 attrs: {
                //                     title: str,
                //                 },
                //             },
                //             str
                //         );

                //     },
                // },
                {
                    title: this.$t('access_des_port'),
                    key: "destPort",
                    align: "center",
                     width:180,
                    render: (h, params) => {
                        let str = this.rowData.destPort;
                        let routePath = this.rowData.routePath || "";
                        let routePathType =
                            routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
                        let routePathType2 =
                            routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
                        // console.log(params.row)
                        let text = "";
                        if (str === undefined || str === null || str === "") {
                            text = "--";
                        } else if (
                            this.rowData.configType == 1 ||
                            this.rowData.configType == 2 ||
                            this.rowData.configType == 5
                        ) {
                            text = "--";
                        } else if (this.rowData.configType == 3 && routePathType == 2) {
                            text = "--";
                        } else if (this.rowData.configType == 8 && routePathType2 == 2) {
                            text = "--";
                        } else {
                            text = str;
                        }
                        return h("span",
                            {
                                style: {
                                    color: params.row.status ? "#5ce959" : "var(--task_path_list_table_color,#ffffff)",
                                },
                            },
                            text);
                    },
                },
                 // 拨测类型
                    {
                    title: this.$t("task_dial_type"),
                    key: "taskType",
                     width:180,
                    render: (h, params) => {
                         return h("span", {
                                style: {
                                    color: params.row.status ? "#5ce959" : "var(--task_path_list_table_color,#ffffff)",
                                },
                            }, global.convertTaskType(params.row.taskType));
                    },
                    },
                
            ],
            indexModal: {
                show: false,
                data: {},
            },
            taskNum: '',
            displayId:'',
            echartIsSpecial: false,
            High: false,
        }
    },
    methods: {
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        actionClick(row) {
            console.log('row==>',row.reportTimeFormat);
            console.log('eventStart==>',new Date(row.reportTimeFormat));
            console.log('eventEnd==>',new Date(new Date(row.reportTimeFormat).getTime()- 3600 * 1000 * 24 * 6));
            this.$store.commit("setdelayLTlevel", "");
            this.$store.commit("updateDelayLossHistory", -1);
            this.$store.commit("setflowTlevel", "");
            this.$store.commit("updateFlowHistory", -1);
            this.indexModal.data = [];
            this.indexModal.show = true
            this.displayId = row.displayId;
            this.indexModal.data = {
                flowChartType: 1,
                sysLinkId: row.id,
                sourceIp: row.sourceIp,
                destIp: row.destIp,
                respondIp: row.respondIp,
                startTime: "",
                endTime: "",
                eventStart: row.eventStart || "",
                eventEnd: row.eventEnd || "",
                id: row.id,
                reportTimeFormat: row.reportTimeFormat
            };
            this.echartIsSpecial = row.isSpecial == 1 ? true : false;
            this.High = row.isHigh;

        },



        getList() {
            this.query.taskId = this.taskId
            this.$http.wisdomPost("/probetask/setChildren", this.query).then(res => {
                if (res.code) {
                    let data = res.data
                    this.tabList = data.records ?? []
                    // 因为页面探针端口 目标名称  目标端口 是一样的 
                    // 从传参中获取
                    this.tabList.forEach(temp => {
                        temp.sourcePort = this.rowData?.sourcePort,
                            temp.destIpName = this.rowData?.destIpName,
                            temp.destPort = this.rowData?.destPort
                    })
                    this.totalCount = data.total
                }
            });
        },
        pageChange(val) {
            //表格页码切换
            this.currentNum = val;
            this.query.pageNo = this.currentNum;
            this.getList();
            // if (this.query.startTime != null && this.query.startTime != "") {
            //     this.query.startTime = new Date(this.query.startTime).format(
            //         "yyyy-MM-dd HH:mm:ss"
            //     );
            // }
            // if (this.query.endTime != null && this.query.endTime != "") {
            //     this.query.endTime = new Date(this.query.endTime).format(
            //         "yyyy-MM-dd HH:mm:ss"
            //     );
            // }
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList();
            // if (this.query.startTime != null && this.query.startTime != "") {
            //     this.query.startTime = new Date(this.query.startTime).format(
            //         "yyyy-MM-dd HH:mm:ss"
            //     );
            // }
            // if (this.query.endTime != null && this.query.endTime != "") {
            //     this.query.endTime = new Date(this.query.endTime).format(
            //         "yyyy-MM-dd 23:59:59"
            //     );
            // }
        },
    },
    mounted() {
        this.getList();
         // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
     beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
}
</script>

<style></style>