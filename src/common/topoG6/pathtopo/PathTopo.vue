<!-- G6拓扑图 -->
<template>
  <div>
    <div
      id="container"
      :style="{
        width: containerWidth,
        height: containerHeight,
        backgroundImage: `url(${backgroundImage})`,
      }"
      class="topo-contaienr"
    ></div>
    <div>
      <Modal
        sticky
        :value="dialogVisible"
        :width="modalWidth"
        class="index-modal probetask-modal"
        :styles="{ top: 100 + 'px' }"
        :title="$t('pathtopo_tip_alarm_info')"
        draggable
        :mask="true"
        @on-cancel="customModalCancel"
        @on-visible-change="visiblechange"
        :footer-hide="true"
      >
        <iframe
          :src="dialogContent"
          width="100%"
          :height="iframeHeight + 'px'"
          frameborder="0"
        ></iframe>
        <!-- <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{ $t("but_confirm") }}</Button>
        </div> -->
      </Modal>
    </div>
  </div>
</template>

<script>

import G6 from '@antv/g6'
// import { FullScreen } from '@antv/g6-plugin'
import ipv6Format from "@/common/ipv6Format";



export default {
name: '',
components: {},
props:[
  "pathTopoCoorType",
  'pathTopoData',
  "topoTabList",
  'configData',
  'showWarning',
  "containerWidth",
  "containerHeight",
  'layoutModel',
  'backgroundImage',
  "showPeerToPeerLinkDetail",
  'tabsId',
  "forbidUpdate",
  "showType",
   "pathTopologyLocXLocYMinMax"],
  //  pathTopoCoorType 0 路径拓扑  2仪表盘路径拓扑构件
// layoutModel：布局类型 0 横向 1 纵向  2 星形
data() {
return {
  modalWidth:0,
  iframeHeight: 0, // 用于存储动态计算的高度
  dialogVisible: false, // 控制对话框的显示状态
  dialogContent: '', // 用于存储要显示的 URL 信息

  // containerWidth:null,
  // containerHeight:null,
  currentSkin: sessionStorage.getItem('dark') || 1,
  nodeData: {
    nodes:[],
    edges:[],



  },
   isBigScreen:false,
  graph:null,
  timer:null,
  viewCenter:{},
  // 被点击的节点
  selectedNode: null,
  // 框选选中的节点
  selectedNodes:[],
  boxSelectMode:false,
  successed:true,
  tooltip:null,
   fullScreenPlugin: null,
  selectionBox: null,  // 添加用于存储选择框的属性
  dragStartPos: null,  // 添加用于存储拖动起始位置的属性
  // 这个 showType 属性用上一个页面传过来的值
  //  showType: 'all',



}
},
computed: {},
watch: {
  pathTopoData: {

    handler() {
     
        if(this.layoutModel == 2) {
    this.handleStarNodes()


  }else {
     this.handleNodes()

  }
         this.handleLink()
         if(this.graph) {
          this.graph.destroy();
          this.initG6()
         }else {
          this.$nextTick(() => {
            this.initG6()
          })
          //  this.initG6()

         }

         this.toggleBoxSelectMode(this.boxSelectMode)




    },
    deep: true
  },
  showType: {
        handler(val) {
          debugger
          console.log(val,'showType',this.showType)
          this.showTypeChange();
        },
        deep: true,
      },
       // 是否展示告警信息
       showWarning: {
        handler(val) {
          // debugger
          this.showTypeChange();
        },
        deep: true,
      },
    boxSelectMode(val) {
      if(val) {
        //
        this.graph.setMode('custom');
      } else {
        this.removeSelectionBox()
        this.graph.setMode('default');
      }
    }

},
methods: {
  clearData() {
    // 1. 清理图实例及其相关资源
    if (this.graph) {
      debugger
      // 清理所有事件监听
      this.graph.off();
      // 清空画布
      this.graph.clear();
      // 销毁图实例
      this.graph.destroy();
      this.graph = null;
    }

    // 2. 清理定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    // 3. 清理数据引用
   this. nodeData= {
    nodes:[],
    edges:[],



  }

    // 4. 使用防抖处理数据更新
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }
    
    this.updateTimer = setTimeout(() => {
      // 处理节点数据
      if(this.layoutModel == 2) {
        this.handleStarNodes();
      } else {
        this.handleNodes();
      }
      
      // 处理连线数据
      this.handleLink();

      // 初始化新的图实例
      this.$nextTick(() => {
        this.initG6();
        this.toggleBoxSelectMode(this.boxSelectMode);
      });
    }, 100); 

  },

  // 初始化G6（横向纵向）
  initG6() {
    // debugger
     this.$emit('update:forbidUpdate',true)
    // console.log(this.containerWidth,this.containerHeight,'宽高================')

    // 区分竖向横向边样式
    // console.log(this.backgroundImage)
    let linkType = 'cubic-horizontal'
    if(this.layoutModel == 1) {
      // 竖向
      linkType = 'cubic-vertical'


    }
    let tooltip = this.createTooltip()

    let obj = {
      container: document.getElementById('container'),
      width:this.containerWidth,
      height:this.containerHeight,
      plugins: [tooltip],

      nodeStateStyles: {
      // highlight: {
      //   stroke: '#91d5ff',
      //   lineWidth: 3,
      // },
      selected:{
        stroke: '#91d5ff',
          lineWidth: 3,
      }
    },
    edgeStateStyles: {
      highlight: {

        lineWidth: 4,
      },
    },




      defaultNode: {
          type: 'domNode', // 默认节点类型
        },

      defaultEdge: {
        type:linkType,
        style: {
            lineWidth:1, // 默认线宽
          },



      },

        modes: {
             default: [
              'drag-canvas',
              'zoom-canvas',
              'drag-node'
              ], // 同时开启画布拖拽、缩放和节点拖拽
             custom:[
               {
            type: 'drag-node',
            shouldBegin: (e) => {
             // 修改判断逻辑，允许拖动选中的节点或单个节点
              if (this.selectionBox && this.selectedNodes.includes(e.item)) {
                this.dragStartPos = { x: e.x, y: e.y };
                return true;
              }
              // 允许拖动单个节点，即使在框选模式下
              if (!this.selectedNodes.includes(e.item)) {
                this.selectedNode = e.item;
                return true;
              }
              return true; // 允许所有节点拖动
            },
            shouldUpdate: (e) => {
              if (this.selectionBox) {
                // 更新选择框位置
                const dx = e.x - this.dragStartPos.x;
                const dy = e.y - this.dragStartPos.y;
                this.updateSelectionBoxPosition(dx, dy);
              }
              return true;
            },
            shouldEnd: () => {
              // 拖动结束时移除选择框
              // this.removeSelectionBox();
              return true;
            }
          },
          'zoom-canvas',
             {
              type:'brush-select',
              brushStyle: {
              fill: '#16436B',
              fillOpacity: 0.2,
              // stroke: '#666',
              // lineWidth: 0.75,
              },
              onSelect: (nodes) => {
                this.selectedNodes = nodes
                 if (nodes.length > 0) {
                // 获取选中节点的包围盒
                const bbox = this.getNodesBBox(nodes);
                // 创建选择框
                this.createSelectionBox(bbox);
              }

              console.log('Selected nodes:', nodes);

              // 在这里处理选中的节点
            },
            onDeselect: (nodes) => {
              console.log('Deselected nodes:', nodes);
               this.removeSelectionBox();
              this.selectedNodes = [];
            },
            selectedState: 'selected',
            includeEdges: false,
            trigger: 'drag'


             }]
                  },

    }
    if(this.layoutModel == 2) {
      obj.layout = {
        type:'force',
        linkDistance: 120,
        nodeStrength: -80,
        edgeStrength: 5,
        // animate: false
      }
      // obj.nodeStrength = -100;
      obj.defaultEdge = {
        type:'line'
      }
    }
     this.graph = new G6.Graph(obj)
    // 调用事件监听
    this.addEventListenerGraph()


         this.graph.data(this.nodeData) // 数据映射
          this.graph.on('afterrender', () => {
             this.$emit('update:forbidUpdate',false)



    })
    // if(this.layoutModel == 2 && this.pathTopoCoorType == 2) {
    //   debugger
    //             this.graph.fitView(10)
    //          }
        this.graph.render() // 渲染
    var width = this.graph.get('width');
    var height = this.graph.get('height');
    // 找到视口中心
    this.viewCenter = {
      x: width / 2,
      y: height / 2
    };
        // 初步渲染还原画布位置
         let pathTopologyCoordinate = this.pathTopoData?.pathTopologyCoordinate || {}; // 节点数据

        if (pathTopologyCoordinate.coordinateHeight !== 1 && pathTopologyCoordinate.coordinateHeight !== 1.0 ) {
          // 拖动之后


           var coordinateX = pathTopologyCoordinate.coordinateX;
          var coordinateY = pathTopologyCoordinate.coordinateY;
          var zoomXMultiple = pathTopologyCoordinate.zoomXMultiple || 1;
          var zoomYMultiple = pathTopologyCoordinate.zoomYMultiple || 1;

          if (zoomXMultiple && zoomYMultiple) {


              this.graph.zoom(zoomXMultiple,{x:0,y:0})
              this.graph.translate(coordinateX,coordinateY)
              this.graph.refresh();

          }





        } else {
          if(this.pathTopoCoorType == 2) {
            // debuggers

            // 仪表盘
            // if(this.layoutModel != 2) {
            this.graph.fitView(10)
            // }


          }else {
               // 未拖动 路径拓扑
          if(this.layoutModel == 1 || this.layoutModel == 0) {

           this.graph.zoom(this.pathTopologyLocXLocYMinMax.zoomXMultiple,{x:0,y:0})
              this.graph.translate(this.pathTopologyLocXLocYMinMax.defaultLocX,this.pathTopologyLocXLocYMinMax.defaultLocY)
              this.graph.refresh();




          }else {
            this.graph.zoom(
                this.pathTopologyLocXLocYMinMax.zoomXMultiple
                ,this.viewCenter
            )
              this.graph.translate(this.pathTopologyLocXLocYMinMax.defaultLocX,this.pathTopologyLocXLocYMinMax.defaultLocY)
            // this.graph.fitCenter();
              this.graph.refresh();


          }




          }



        }



  },
  // 事件监听合集
  addEventListenerGraph() {
    this.graph.on('node:dragend', (evt) => {
      debugger
      const node = evt.item;
        const model = node.getModel();
        let newX = model.x
        let newY = model.y

         // if(this.layoutModel == 1) {
         //  newX = this.saveCoordinate(this.containerWidth,this.containerHeight,newX,newY).x
         //  newY = this.saveCoordinate(this.containerWidth,this.containerHeight,newX,newY).y
         //
         // }
      if(this.boxSelectMode) {

        let arr = []
        if(this.selectedNodes.length > 0) {
           this.selectedNodes.forEach(item => {
         console.log(item.getModel(),'拖动的节点')
         let data = item.getModel()
         // if(this.layoutModel == 1) {
         //  let posiztionObj = this.saveCoordinate(this.containerWidth,this.containerHeight,data.x,data.y)
         //  data.x = posiztionObj.x
         //  data.y = posiztionObj.y
         // }
         arr.push({
          pathTopologyId: this.tabsId,
            zoomXMultiple: this.graph.getZoom(),
            zoomYMultiple: this.graph.getZoom(),
            coordinateX: this.graph.getCanvasByPoint(0, 0).x,
            coordinateY:this.graph.getCanvasByPoint(0, 0).y,
            coordinateWidth: this.graph.getWidth(),
            coordinateHeight: this.graph.getHeight(),
            nodeId: data.data.id,
            nodeX: data.x,
            nodeY: data.y,
            pathTopoCoorType: 0

         })

        })
         this.savePathTopologyCoordinateMode(arr)


        }else {
           this.savePathTopologyCoordinate({
          nodeId: model.data.id,
          locX: newX,
          locY: newY
        });

        }


      }else {

        // console.log('Node dragged:', model);
        // // Save the new node position
        this.savePathTopologyCoordinate({
          nodeId: model.data.id,
          locX: newX,
          locY: newY
        });

      }

      })

     this.graph.on('edge:click', (evt) => {

      if(this.isBigScreen) {
        return
      }

      this.handleLinkClick(evt)
      })
       this.graph.on('edge:mouseover', (evt) => {

      this.handleLinkOver(evt)
      })
       this.graph.on('edge:mouseleave', (evt) => {

      this.handleLinkLeave(evt)
      })

    this.graph.on('node:click', (evt) => {
      // console.log('节点的点击事件')
       if(this.isBigScreen) {
        return
      }
      const clickedNode = evt.item;
      this.handleNodeSelection(clickedNode);
      })
      // 节点悬浮高亮
      this.graph.on('node:mouseover',(evt) => {
        // console.log(evt,'mouseover')
        this.handleLightNode(evt)
      })
      // 节点离开取消高亮
      this.graph.on('node:mouseleave',(evt) => {
        // console.log(evt,'mouseleave')
        this.handleLightLeave(evt)
      })

      // 鼠标滚轮缩放
      this.graph.on('wheel', ev => {
      clearTimeout(this.timer)
      // console.log('Canvas 画布的 x/y 坐标分别为:',this.graph.getCanvasByPoint(0, 0))
      // console.log('图中心点坐标',this.graph.getGraphCenterPoint())
      // 节流防止高频率请求
    this.timer =  setTimeout(() => {

      this.savePathTopologyCoordinate({})
        // this.saveDashboardComponentCoordinates(ev,'canvas')
      }, 500)


    })
     // 画布拖拽
  this.graph.on('canvas:dragend',ev => {
    console.log(ev,'画布被拖拽了')
    if(ev.dx && ev.dy) {

      this.savePathTopologyCoordinate({})

    }

  })


  },
  createTooltip() {
      return new G6.Tooltip({
        offsetX: 10,
        offsetY: 10,
        // The container to render the tooltip
        container: document.getElementById('container'),
        // Only show tooltip for nodes
        itemTypes: ['node'],
        // Fix position relative to node
        fixToNode: [1, 0],
         trigger: 'click',
         // 添加触发条件判断
        shouldBegin: (e) => {
          if (!e.item) return false;
          const model = e.item.getModel();
          const data = model.data;
          // 只有当 faultLink > 0 时才显示 tooltip
          return data.faultLink > 0 && !this.isBigScreen;
        },
        // Customize tooltip content
        getContent: (e) => {
          if (!e.item) return '';
          const model = e.item.getModel();
          const data = model.data;
          console.log(data,'data....')
          const outDiv = document.createElement('div');

         let infoArry = [];


        // 将数据序列化并转义引号
        const serializedData = JSON.stringify(data).replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
        let bgColor = this.currentSkin == 1 ? '#16436b' : '#fff'
          let fontColor = this.currentSkin == 1 ? '#fff' : '#515A6E'
          let boxShadow = this.currentSkin == 1 ? 'unset' : '0px 0px 8px 1px rgba(0,0,0,0.16)'
          let borderColor = this.currentSkin == 1 ? '#16436b' : '#e2e2e2'
          outDiv.style.cssText = `
            min-width: 80px;
            background-color: ${bgColor};
            border: 1px solid ${borderColor};
            border-radius: 4px;
            box-shadow: ${boxShadow};
            padding: 10px 8px;
          `
        let titleColor = this.currentSkin == 1 ? '#0fe' : '#0290FD'
        let fontColorMain = this.currentSkin == 1 ? '#fff' : '#515A6E'
        let borderLineColor = this.currentSkin == 1 ? '#045b8e' : '#E4E7ED'
      let html = `
        <div class="header" style="font-size: 14px;color: ${fontColorMain};text-align:left; height:24px;line-height:24px;">
          ${this.$t("alarm_details")}
          <span id='goDetail' style="color:${titleColor};cursor: pointer"
                onclick='window.pathTopoInstance.handleGoDetail(${serializedData})'>
            ${this.$t("go_and_check")} >
          </span>
        </div>
        <div style="font-size: 14px;color:${fontColorMain};text-align:left;height:24px;line-height:24px;margin-bottom:5px;">
          ${this.$t("number_of_alarms")}: ${this.$t(data.faultLink)}
        </div>
        <div class=content>`;
        let info = data.faultDesc
      if (info) {
        infoArry = info.split("#$");
      }
      infoArry.forEach((item) => {
        let content = item.split(",");

        html = html +`<hr style="border:1px solid ${borderLineColor}"></hr>`;
        for (let index = 0; index < content.length; index++) {
          let element = content[index];
          html = html +`<div style="font-size: 14px;color: ${fontColorMain};text-align:left;height:24px;line-height:24px;white-space:nowrap;">${element ?? ""}</div>`;
        }
      });

          outDiv.innerHTML = html + '</div>'
          return outDiv;
        },
      })
    },

handleGoDetail(data) {
    console.log(data, 'data....');

    let ids = data.faultIds.join(",");
    let nodeIp = data.value;
    let url = window.location.hostname === "localhost"
      ? "/anpm-plug-alarmlist-ui.html?faultIds=" + ids + "&days=62&keyword=" + nodeIp
      : "/alarmlist?faultIds=" + ids + "&days=62&keyword=" + nodeIp;

    this.dialogContent = url; // 保存生成的 URL 信息
    this.dialogVisible = true; // 打开对话框

    // this.$emit('go-detail', data);
  },
  // customModalOk() {
  //   window.location.href = this.dialogContent; // 执行页面跳转
  // },
  customModalCancel() {
    this.dialogVisible = false; // 关闭对话框
  },


// 获取节点组的包围盒
  getNodesBBox(nodes) {
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    nodes.forEach(node => {
      const { x, y } = node.getModel();
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    });

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  },

  // 创建选择框
  createSelectionBox(bbox) {
    this.removeSelectionBox(); // 确保之前的选择框被移除

    const group = this.graph.get('group');
    this.selectionBox = group.addShape('rect', {
      attrs: {
        x: bbox.x - 10,
        y: bbox.y - 10,
        width: bbox.width + 20,
        height: bbox.height + 20,
        fill: '#16436B',
        fillOpacity: 0.2,
        // stroke: '#666',
        // lineWidth: 1,
        radius: 4
      },
      capture: false // 确保选择框不会捕获事件
    });
    this.graph.paint();
  },

  // 更新选择框位置
  updateSelectionBoxPosition(dx, dy) {
    if (this.selectionBox) {
      const matrix = this.selectionBox.getMatrix() || [1, 0, 0, 0, 1, 0, 0, 0, 1];
      const newMatrix = [1, 0, 0, 0, 1, 0, dx, dy, 1];
      this.selectionBox.setMatrix(newMatrix);
      this.graph.paint();
    }
  },

  // 移除选择框
  removeSelectionBox() {
    if (this.selectionBox) {
      this.selectionBox.remove();
      this.selectionBox = null;
      this.graph.paint();
    }
  },
  showTypeChange() {
    // debugger
    // this.graph.destroy();
    this.handleDomeNode()
    if(this.layoutModel == 2) {
    this.handleStarNodes()



  }else {
     this.handleNodes()

  }
   this.handleLink()

  this.graph.changeData(this.nodeData) // 数据映射
  // this.graph.render();




    // this.initG6()

  },
  handleNodeSelection(clickedNode) {
    const nodeModel = clickedNode.getModel();
    console.log(nodeModel,'nodeModel')
    if(nodeModel.data.childList && nodeModel.data.childList.length > 0) {
      // 收缩节点，点击显示弹框
      let obj = {}
      obj.sourceIp = nodeModel.data.value
      obj.nodeList =[ nodeModel.data,...nodeModel.data.childList]
      obj.linkList = this.pathTopoData.nodeRouteList

      obj.configData = this.configData
      obj.showWarning = this.showWarning
      obj.showType = this.showType
      this.$emit('updateCompleteNodesShow',obj)

    }
  },


  // 处理离开高亮节点样式恢复
  handleLightLeave(evt) {
    let data = evt.item._cfg.model.data
    if(data.inconType === 6) {
      const graph = this.graph;

  // 清除节点的高亮效果
  graph.getNodes().forEach(node => {
    const nodeModel = node.getModel();
    const nodeSize = Array.isArray(nodeModel.size) ? nodeModel.size[0] : nodeModel.size;

      // 只有当节点大小为32时才改变尺寸
      if (nodeSize === 32) {
    graph.updateItem(node, {
        size: 24,
      });
    }

  });

  // 清除边的高亮效果
  graph.getEdges().forEach(edge => {
    const edgeModel = edge.getModel();
    const lineWidth = edgeModel.style?.lineWidth;

      // 只有当边宽为4时才改变尺寸
      if (lineWidth === 4) {
    graph.updateItem(edge, {
        style: {

          lineWidth: 0.75,
        },
      });
    }

  });


    }

  },

  // 处理节点悬浮高亮
  handleLightNode(evt) {
    let data = evt.item._cfg.model.data

    if(data.inconType === 6) {
      console.log('目标节点高亮')
      let despLinkIds = data.despLinkIds
      const graph = this.graph;
      // 高亮匹配的节点
    graph.getNodes().forEach(node => {
      const nodeModel = node.getModel()
      if (nodeModel.data && nodeModel.data.linkIds) {
        const hasMatchingLink = nodeModel.data.linkIds.some(id => despLinkIds.includes(id))
        if (hasMatchingLink) {
          // 获取原始大小
        const originalSize = nodeModel.size || graph.getDefaultNodeSize();



        // 设置高亮状态和新的大小

        graph.updateItem(node, {
          size: 32,
        });
        }
      }
    })

    // 高亮匹配的边
    graph.getEdges().forEach(edge => {
      const edgeModel = edge.getModel()
      if (edgeModel.data && edgeModel.data.linkIds) {
        const hasMatchingLink = edgeModel.data.linkIds.some(id => despLinkIds.includes(id))
        if (hasMatchingLink) {
          graph.updateItem(edge, {
          style: {
            ...edgeModel.style,
            lineWidth: 4, // 宽度变为原来的 2 倍
          },
        });
        }
      }
    })

    }

  },
      // 框选保存节点信息
      savePathTopologyCoordinateMode(arr) {
        // console.log(arr,'传过来的参数。。。。。。')
        if(this.forbidTimer) {
          clearTimeout(this.forbidTimer)
        }
       this.$emit('update:forbidUpdate',true)

        if (this.successed) {
          // // console.log(222222)
          this.successed = false;

          // this.clearUpdatePathResult()
          this.$http
            .PostJson("/pathTopo/savePathTopologyCoordinates", arr)
            .then((res) => {
              if (res.code == 1) {
                // this.$Message.success(res.msg);
              }
            })
            .finally(() => {
              // this.loading = false
              this.successed = true;
              // this.dataList = [];
              this.selectedNodes = []
              this.forbidTimer = setTimeout(() => {
                // this.forbidUpdate = false
                this.$emit('update:forbidUpdate',false)
               },1000 * 60)
            });
        }
      },
      // 保存节点恢复
saveCoordinate(width,height,newX,newY) {
  // debugger
  let height2 = height * 0.9;
  let x = newY/2.5/height2*width
  let y = newX/0.5/width*height2
  return { x: x, y: y }


},

   // 保存备注节点信息
   savePathTopologyCoordinate(obj) {
  let type =  Object.keys(obj).length === 0 ? this.pathTopoCoorType : 0
  // this.forbidUpdate = true
  this.$emit('update:forbidUpdate',true)
  if(this.forbidTimer) {
    clearTimeout(this.forbidTimer)
  }


      let params = {};
         let nodeX = obj.locX
         let nodeY = obj.locY
       //   if(this.layoutModel == 1) {
       //   let posiztionObj = this.adjustCoordinate(this.containerWidth,this.containerHeight,obj.locX,obj.locY)
       //   nodeX = posiztionObj.x
       //   nodeY = posiztionObj.y
       // }



          params = {
            pathTopologyId: this.tabsId,
            zoomXMultiple: this.graph.getZoom(),
            zoomYMultiple: this.graph.getZoom(),
            coordinateX: this.graph.getCanvasByPoint(0, 0).x,
            coordinateY:this.graph.getCanvasByPoint(0, 0).y,
            coordinateWidth: this.graph.getWidth(),
            coordinateHeight: this.graph.getHeight(),
            nodeId: obj.nodeId,
            nodeX: nodeX,
            nodeY: nodeY,
            pathTopoCoorType: type
          };





          // // console.log(222222)
          // this.clearUpdatePathResult()


          // this.loading = true
          // this.forbidUpdate = true
          this.$http
            .PostJson("/pathTopo/savePathTopologyCoordinate", params)
            .then((res) => {
              if (res.code == 1) {
                // this.$Message.success(res.msg);
                this.forbidUpdate = false
              }
            })
            .finally(() => {
              // this.loading = false
            this.forbidTimer = setTimeout(() => {
              // this.forbidUpdate = false
              this.$emit('update:forbidUpdate',false)
             },1000 * 60)
            });

      },
       // 处理节点
    handleNodes() {
      console.log(this.layoutModel,'布局类型布局类型')
       this.nodeData.nodes = []
        let nodeList = this.pathTopoData?.nodeList || []; // 节点数据
        nodeList.forEach((item, index) => {
          // 区分横向布局竖向布局节点位置
          let newX = item.locX
          let newY = item.locY
          let postion = [ [0, 0.5], [1, 0.5]]
          if(this.layoutModel == 1) {
          //   let posiztionObj = this.adjustCoordinate(this.containerWidth,this.containerHeight,item.locX,item.locY)
          //   newX = posiztionObj.x
          //   newY = posiztionObj.y
            postion = [ [0.5,1],[0.5,0],]

          }
            // 区分横向布局竖向布局节点位置/
            // 区分横向竖向节点与边练节点位置


            let idx = item.id.indexOf('_') + 1



// TODO 处理节点
            let text = ''
            let text2 = ''
            let value2 = JSON.parse(JSON.stringify(item.value))
            // 判断如果有aliases就两排显示label
            let value = ipv6Format.abbreviateIPv6(item.value);
         

            let existsDeviceName = this.existShowType(this.$t("snmp_task_device_name"));
            let existsDeviceIp = this.existShowType(this.$t("comm_topo_device_ip"));
            let existsAll = this.existShowType("all");

            // if (this.showType === "all") {
            if ((existsDeviceName && existsDeviceIp) || existsAll) {
              console.log('all',this.showType)
          text = `${item.aliases || ""}\n${value}`;
          text2 = `${item.aliases || ""}\n${value2}`;
      

        // } else if (this.showType === this.$t("snmp_task_device_name")) {
        } else if (existsDeviceName) {
          // debugger
          text = `${item.aliases || ""}`;
          text2 = `${item.aliases || ""}`;
        // } else if (this.showType === this.$t("comm_topo_device_ip")) {
        } else if (existsDeviceIp) {
          text = `${value || ""}`;
          text2 = `${value2 || ""}`;
        }

          let isSingleLine = text2.startsWith('\n')|| !text2.includes('\n');
       text2 = text2.replace(/^\n+/, '');
        item.label = text2
            // if(item.aliases != '' && item.aliases != null) {
            //   text = item.value + '\n' + item.aliases

            // }else {
            //   text = item.value
            // }
            let obj = {}
            // console.log(item,'item===============================>')
            if(item.childList && item.childList.length > 0) {
              // console.log(item.childList,'=================================子集')
            // this.handleDomeNode(item)
            postion = [ [0.2, 0.3], [0.4, 0.3]]
            if(this.layoutModel == 1) {
              postion = [ [0.28,0.2],[0.28,0.2],]
            }

               obj = {
                id:item.id,
                label:text,
                type:'domNode',

                 x:newX,
                y:newY,
                size:[120,40],
                data:item,
                anchorPoints:postion


            }


            }else {
                    // 匹配图片
            let imgUrl = this.handleImage(item)
              obj = {
                id:item.id,
                label:text,
                type:'image',
                img: imgUrl,
                 x:newX,
                y:newY,
                anchorPoints:postion,
                size:[24,24],
                data:item,
                
                labelCfg: {           // 标签配置属性
                position: 'bottom',// 标签的属性，标签在元素中的位置
                offset: 8,
                style: {            // 包裹标签样式属性的字段 style 与标签其他属性在数据结构上并行
                fontSize: 12,      // 标签的样式属性，文字字体大小
                fill: this.currentSkin == 1 ? '#5CA0D5' : '#515A6E',
                textBaseline: isSingleLine ? 'middle' : 'top',
                textAlign: 'center',
                }
                }

            }

            }

            // console.log(index)
            this.nodeData.nodes.push(obj)
        })
        // console.log(this.nodeData,'节点数据id')

    },
    // 星形节点
    handleStarNodes() {
      console.log(this.layoutModel,'布局类型布局类型')
       this.nodeData.nodes = []
        let nodeList = this.pathTopoData?.nodeList || []; // 节点数据
        nodeList.forEach((item, index) => {

            let idx = item.id.indexOf('_') + 1
            let x = item.locX
            let y = item.locY



            let text = ''
            let text2 = ''
            let value2 = JSON.parse(JSON.stringify(item.value))
            // 判断如果有aliases就两排显示label
            let value = ipv6Format.abbreviateIPv6(item.value);


          let existsDeviceName = this.existShowType(this.$t("snmp_task_device_name"));
          let existsDeviceIp = this.existShowType(this.$t("comm_topo_device_ip"));
          let existsAll = this.existShowType("all");
        


        // if (this.showType === "all") {
        if ((existsDeviceName && existsDeviceIp) || existsAll) {
              console.log('all',this.showType)
           text = `${item.aliases || ""}\n${value}`;
          text2 = `${item.aliases || ""}\n${value2}`;
        
        // } else if (this.showType === this.$t("snmp_task_device_name")) {
        } else if (existsDeviceName) {
          // debugger
          text = `${item.aliases || ""}`;
          text2 = `${item.aliases || ""}`;
          console.log(this.$t("snmp_task_device_name"),this.showType)
        // } else if (this.showType === this.$t("comm_topo_device_ip")) {
        } else if (existsDeviceIp) {
          text = `${value || ""}`;
          text2 = `${value2 || ""}`;
          console.log(this.$t("comm_topo_device_ip"),this.showType)
        }
         let isSingleLine = text2.startsWith('\n')|| !text2.includes('\n');
       text2 = text2.replace(/^\n+/, '');
        item.label = text2
            let obj = {}
            // console.log(item,'item===============================>')
            if(item.childList && item.childList.length > 0) {
              // console.log(item.childList,'=================================子集')
            // this.handleDomeNode(item)
          let  postion = [ [0, 0.3], [1, 0.3]]
               obj = {
                id:item.id,
                label:text,
                type:'domNode',
                // x:x,
                // y:y,

                size:[120,40],
                 anchorPoints:postion,
                data:item,



            }


            }else {
                    // 匹配图片
            let imgUrl = this.handleImage(item)
              obj = {
                id:item.id,
                label:text,
                type:'image',
                img: imgUrl,
                data:item,
                 anchorPoints:[[0.5, 0.5]],


                x:null ,
                y:null,
                size:[24,24],
                labelCfg: {           // 标签配置属性
                position: 'bottom',// 标签的属性，标签在元素中的位置
                offset: 8,

                style: {            // 包裹标签样式属性的字段 style 与标签其他属性在数据结构上并行
                fontSize: 12,      // 标签的样式属性，文字字体大小
                fill:this.currentSkin == 1 ? '#5CA0D5' : '#515A6E',
                textBaseline: isSingleLine ? 'middle' : 'top',
                textAlign: 'center',
                }
                }

            }
              if(index === 0){
                obj.x = this.viewCenter.x ;
                obj.y = this.viewCenter.y ;
              }

            }

            // console.log(index)
            this.nodeData.nodes.push(obj)
        })
        // console.log(this.nodeData,'节点数据id')


    },

     // 处理连线
    handleLink() {
        // 用来装连线数组
        this.nodeData.edges = []
        let arr = []
        let linkList = this.pathTopoData?.nodeRouteList || []
         const edgeCount = new Map()
       linkList.forEach((item,index) => {
            console.log(item.right,'item.right...............................................')
            let linkColor = this.handleLinkColor(item)
            let linkLabel = this.handleLinkLabel(item)
            let linkLabelColor = this.handleLinkLabelColor(item)
              // 创建双向的key来统计边数
              const nodeKey1 = `${item.source}-${item.target}`
              const nodeKey2 = `${item.target}-${item.source}`
              // 检查两个方向的边数
              const count = (edgeCount.get(nodeKey1) || 0) + (edgeCount.get(nodeKey2) || 0)
              // 更新边计数
              edgeCount.set(nodeKey1, (edgeCount.get(nodeKey1) || 0) + 1)

             let idx = item.source.indexOf('_') + 1
             let obj = {
                source: item.source,
                target:item.target,
                data:item,
                style: {
                  stroke: linkColor,
                  lineWidth:1,


                },
                label:linkLabel,
               
                labelCfg: {
                  autoRotate:true,
                   
                  style: {
                    fill:linkLabelColor,
                    fontSize:12,
                    
                    // stroke:linkColor,


                  }
                }

            }
            if(item.right == 1) {
              obj.labelCfg.position = 'end'
              // obj.labelCfg.offset = -150; // 标签整体偏移10像素
              obj.labelCfg.refX = -20
              // obj.autoRotate = true
              console.log('obj...............................................999')
            }
            
            
        if (count > 0) {
            obj.type = 'cubic'
            // 0 横向20， 1 竖向-20
            if(this.layoutModel == 1) {
              obj.curveOffset = -20
            }else {
              obj.curveOffset = 20
            }

        }
            // 虚线逻辑
            if(item.destType !=null &&  item.destType == 0) {
             obj.style.lineDash = [4, 4]

            }
            // 缩略虚线逻辑
            if(item.scalingSplicingPoint === 1) {
               obj.style.lineDash = [4,1, 4]

            }
            arr.push(obj)


        })
        // console.log(arr,this.nodeData)
        this.nodeData.edges = arr


},
    handleImage(data) {
        let {
        deviceManageInconDeviceType,
        twoDeviceType,
        inconType,
        inconDeviceType,
        locX,
        locY,
        width = 24,
        height = 24,
        aliases = "",
        value,
        type,
        state,
        devType,
        interruptShowGrayNode,
        id,
        faultLink,
        faultIds,
        faultDesc,
        virtualNode,
        linkIds,
        despLinkIds,
        childList = []
      } = data;
         const showState = this.showWarning ? state : null;
        // 节点状态字段对应
      let statusType = 0;
      if (showState == null || showState == "") {
        statusType = 0;
      } else if (showState == 1) {
        statusType = 1;
      } else if (showState == 2 || showState == 3) {
        statusType = 2;
      } else {
        statusType = 3;
      }
       let url = null
         if (inconDeviceType) {
        this.configData.deviceTypeIconManageConfigures.forEach((item) => {
          if (item.deviceTypeId == inconDeviceType) {
            if (interruptShowGrayNode && interruptShowGrayNode == 1 && this.showWarning) {
              //    debugger
              item.types.forEach((item3) => {
                if (item3.type === 3) {
                  url = item3.image;
                }
              });
            } else {
              item.types.forEach((item2) => {
                if (item2.type == statusType) {
                  url = item2.image;
                  // // console.log(url);
                }
              });
            }
          }
        });

        if (url === null) {
          // 先判断deviM。。。。 与设备匹配，没有，匹配上为空往下进行
          if (deviceManageInconDeviceType) {
            this.configData.deviceTypeIconManageConfigures.forEach((item) => {
              if (item.deviceTypeId == deviceManageInconDeviceType) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  this.showWarning
                ) {
                  //    debugger
                  item.types.forEach((item3) => {
                    if (item3.type === 3) {
                      url = item3.image;
                    }
                  });
                } else {
                  item.types.forEach((item2) => {
                    if (item2.type == statusType) {
                      url = item2.image;
                      // // console.log(url);
                    }
                  });
                }
              }
            });
            if (url === null) {
              if (twoDeviceType) {
                let findR = this.configData.inUseIconManageConfigures.find(
                  (item) => item.code === "SLN"
                );
                if (findR) {
                  if (
                    interruptShowGrayNode &&
                    interruptShowGrayNode == 1 &&
                    this.showWarning
                  ) {
                    let findR2 = findR.types.find((item) => item.type === 3);
                    url = findR2.image;
                  } else {
                    let findR3 = findR.types.find((item) => item.type === statusType);
                    url = findR3.image;
                  }
                }
              } else {
                this.configData.inUseIconManageConfigures.forEach((item) => {
                  if (item.deviceTypeId == inconType) {
                    if (
                      interruptShowGrayNode &&
                      interruptShowGrayNode == 1 &&
                      this.showWarning
                    ) {
                      item.types.forEach((item3) => {
                        if (item3.type === 3) {
                          url = item3.image;
                        }
                      });
                    } else {
                      item.types.forEach((item2) => {
                        if (item2.type == statusType) {
                          url = item2.image;
                        }
                      });
                    }
                  }
                });
              }
            }
          } else {
            if (twoDeviceType) {
              let findR = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "SLN"
              );
              if (findR) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  this.showWarning
                ) {
                  let findR2 = findR.types.find((item) => item.type === 3);
                  url = findR2.image;
                } else {
                  let findR3 = findR.types.find((item) => item.type === statusType);
                  url = findR3.image;
                }
              }
            } else {
              this.configData.inUseIconManageConfigures.forEach((item) => {
                if (item.deviceTypeId == inconType) {
                  if (
                    interruptShowGrayNode &&
                    interruptShowGrayNode == 1 &&
                    this.showWarning
                  ) {
                    item.types.forEach((item3) => {
                      if (item3.type === 3) {
                        url = item3.image;
                      }
                    });
                  } else {
                    item.types.forEach((item2) => {
                      if (item2.type == statusType) {
                        url = item2.image;
                      }
                    });
                  }
                }
              });
            }
          }

          // 如果没有  二层节点不为空 二层节点   为空下面的逻辑


        }
      } else {
        // debugger
        // 先判断deviM。。。。 与设备匹配，没有，匹配上为空往下进行
        if (deviceManageInconDeviceType) {
          this.configData.deviceTypeIconManageConfigures.forEach((item) => {
            if (item.deviceTypeId == deviceManageInconDeviceType) {
              if (
                interruptShowGrayNode &&
                interruptShowGrayNode == 1 &&
                this.showWarning
              ) {
                //    debugger
                item.types.forEach((item3) => {
                  if (item3.type === 3) {
                    url = item3.image;
                  }
                });
              } else {
                item.types.forEach((item2) => {
                  if (item2.type == statusType) {
                    url = item2.image;
                    // // console.log(url);
                  }
                });
              }
            }
          });

          if (url == null) {
            this.configData.inUseIconManageConfigures.forEach((item) => {
              if (item.deviceTypeId == inconType) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  this.showWarning
                ) {
                  item.types.forEach((item3) => {
                    if (item3.type === 3) {
                      url = item3.image;
                    }
                  });
                } else {
                  item.types.forEach((item2) => {
                    if (item2.type == statusType) {
                      url = item2.image;
                    }
                  });
                }
              }
            });
          }
        } else {
          this.configData.inUseIconManageConfigures.forEach((item) => {
            if (item.deviceTypeId == inconType) {
              if (
                interruptShowGrayNode &&
                interruptShowGrayNode == 1 &&
                this.showWarning
              ) {
                item.types.forEach((item3) => {
                  if (item3.type === 3) {
                    url = item3.image;
                  }
                });
              } else {
                item.types.forEach((item2) => {
                  if (item2.type == statusType) {
                    url = item2.image;
                  }
                });
              }
            }
          });
        }
      }
      return "data:image/png;base64," + url

    },
     handleLinkLabelColor(item) {
      let { state,interruptShowGrayNode } = item;
      // 中断颜色
      let breakLineColor = this.configData.breakLineColor;
      // 正常颜色
      let normalLineColor = '#fff';
      // 裂化颜色
      let degradationLineColor = this.configData.degradationLineColor;
      // 未知颜色
      let unknownLineColor = this.configData.unknownLineColor;
       let color = normalLineColor;
       if (this.showWarning) {
        if (interruptShowGrayNode == 1) {
          // 未知
          color = unknownLineColor;
        }else{
          if (state == 1) {
            // 中断
            color = breakLineColor;
          } else if (state == 2 || state == 3) {
            // 劣化
            color = degradationLineColor;
          } else if (interruptShowGrayNode == 1) {
            // 未知
            color = unknownLineColor;
          }
        }
      }

      return color

    },
    handleLinkColor(item) {
      let { state,interruptShowGrayNode } = item;
      // 中断颜色
      let breakLineColor = this.configData.breakLineColor;
      // 正常颜色
      let normalLineColor = this.configData.normalLineColor;
      // 裂化颜色
      let degradationLineColor = this.configData.degradationLineColor;
      // 未知颜色
      let unknownLineColor = this.configData.unknownLineColor;
       let color = normalLineColor;
       if (this.showWarning) {
        if (interruptShowGrayNode == 1) {
          // 未知
          color = unknownLineColor;
        }else{
          if (state == 1) {
            // 中断
            color = breakLineColor;
          } else if (state == 2 || state == 3) {
            // 劣化
            color = degradationLineColor;
          } else if (interruptShowGrayNode == 1) {
            // 未知
            color = unknownLineColor;
          }
        }
      }

      return color

    },
    // 判断是否存在
    existShowType(type){
      if(type == "" || type == null){
          return false;
      }
      // 是否是数组
      if(Array.isArray(this.showType)){
          return this.showType.indexOf(type) > -1;
      }else if(/.*string/ig.test(Object.prototype.toString.call(this.showType).toString())){
          if(/all/ig.test(this.showType)){
              return true;
          }else if(this.showType == ''){
              return false;
          }
          // 是否是字符串类型
          return this.showType == type;
      }
      return false;
    },
    // 处理边文字
    handleLinkLabel(data) {
        let {
        state,
        destType,
        delay,
        lossRate,
        flowIntoSpeed,
        flowOutSpeed,
        faultDesc,
        faultLink,
        faultIds,
        interruptShowGrayNode,
        specialLine,
      } = data;
      let text = "";
      let textOffsetY =  0


      // debugger

      // TODO 处理显示指标的问题。
      // 线路延时
      let existsDelay = this.existShowType(this.$t("comm_topo_device_delay"));
      // 线路丢包率
      let existsLoss = this.existShowType(this.$t("comm_topo_device_loss"));
      // 线路流量 
      let existsflow = this.existShowType(this.$t("comm_topo_device_flow"));
      let existsAll = this.existShowType("all");

      // TODO 处理节点显示问题,如果不显示就设置成一个无效的值，方便判断。
      if ((existsDelay && existsLoss && existsflow) || existsAll) {
          // 
      } else {
          if (!existsDelay) {
            // 时延
            delay = "";
          }
          if (!existsLoss) {
            // 丢包
            lossRate = "";
          }
          if (!existsflow) {
              // 入流速
            flowIntoSpeed = "";
              // 出流速
            flowOutSpeed = "";
          }
      }
      if (delay) {
        // 提取数字部分并转换为浮点数
        const delayNum = parseFloat(delay.replace('ms', ''));
        if (delayNum < 1) {
          delay = '<1ms';
        }
      }
      if (!existsDelay && !existsLoss && !existsflow) {
        text = "";

      } else{ 
        
        if (!existsflow) {
          // text = "\n\n";
          if(existsDelay && existsLoss){
            if(delay || lossRate){
              text = `${delay || "--"}/${lossRate || "--"}`;
            }
          }else if(existsDelay){
               text = `${delay || ""}`;
          }else if(existsLoss){
               text = `${lossRate || ""}`;
          }
            text += "\n";
           if(existsflow){
              if(specialLine == 1) {
                text +=  "[" + this.$t("dash_Special_line") + "]";
              }

           }
          textOffsetY = 0
        
        } else if (!existsDelay && !existsLoss) {
          text += "\n";
          if(existsflow){
          
            if(specialLine == 1) {
                text +=  "[" + this.$t("dash_Special_line") + "]";
            }

            if(flowIntoSpeed || flowOutSpeed){
               text += `<${flowIntoSpeed || "--"}/${flowOutSpeed || "--"}>`;
            }

          }

          textOffsetY = 0
        } else if (existsDelay && existsLoss && existsflow) {

           if(existsDelay  && existsLoss){

             if(delay || lossRate){
                text = `${delay || "--"}/${lossRate || "--"}`;
             }
          }else if(existsDelay){
               text = `${delay || ""}`;
          }else if(existsLoss){
               text = `${lossRate || ""}`;
          }

          text += "\n";
          if(existsflow){
          
              if(specialLine == 1) {
                text += "[" + this.$t("dash_Special_line") + "]";
              }

            if(flowIntoSpeed || flowOutSpeed){
              text += `<${flowIntoSpeed || "--"}/${flowOutSpeed || "--"}>`;
            }
          }

            textOffsetY = 0
        } else {

          if(existsDelay  && existsLoss){
            if(delay || lossRate){
              text = `${delay || "--"}/${lossRate || "--"}`;
            }
          }else if(existsDelay){
               text = `${delay || ""}`;
          }else if(existsLoss){
               text = `${lossRate || ""}`;
          }

          text += "\n";
          if(existsflow){
             
              if(specialLine == 1) {
                text +=  "[" + this.$t("dash_Special_line") + "]";
              }

              if(flowIntoSpeed || flowOutSpeed){
                  text += `<${flowIntoSpeed || "--"}/${flowOutSpeed || "--"}>`;
              }
          }
          textOffsetY = 0
        }


      }
      if(data.scalingSplicingPoint === 1) {
        text = ''
      }
      return text

    },
    // 自定义缩略节点
    handleDomeNode() {
      console.log('自定义缩略节点')
      // 中间圆点

       G6.registerNode('domNode', {
        // 添加状态处理
        setState(name, value, item) {
          const group = item.getContainer();
          const shape = group.get('children')[0]; // 获取圆形背景

          if (name === 'hover') {
            if (value) {
              shape.attr('fillOpacity', 0.8);
            } else {
              shape.attr('fillOpacity', 1);
            }
          }
        },

        draw:(cfg, group) => {
          let data = cfg.data
          let color = this.handleLinkColor(data)
          let container = group.addGroup()

          container.addShape('circle', {
            attrs: {
              x: 0,
              y: 0,
              r: 13,
              fill: '#5b8ff9',
              stroke: color,
              lineWidth: 1,
            },
          draggable: true, // 启用拖拽
         name: 'circle-shape' // 添加名称以便识别
          });
          // 中间文字
          container.addShape('text', {
            attrs: {
              x: 0,
              y: 0,
              text: data.allChildCount,
              textAlign: 'center',
              textBaseline: 'middle',
              fontWeight:'bold',
              fill: '#060D15',
              fontSize: 12
            },
            draggable: true, // 启用拖拽
            name: 'text-shape' // 添加名称以便识别
          });
          // 底部label
           container.addShape('text', {
            attrs: {
              x: 0,
              y: 26,
              text: cfg.label,
              textAlign: 'center',
              textBaseline: 'middle',
              fill: this.currentSkin == 1 ? '#5CA0D5' : '#515A6E',
              fontSize: 10
            },
          });
          // 右侧文字
            // 顺序：中断/劣化/正常/未知


        let formatCount = [
   {id:1,value:data.breakChildCount,color:this.configData.breakLineColor},
                            {id:2,value:data.lossChildCount,color:this.configData.degradationLineColor},
                            {id:0,value:data.normalChildCount,color:this.configData.normalLineColor},
                             {id:3,value:data.unknownchildcount,color:this.configData.unknownLineColor},
].filter(item => item.value > 0);
console.log(formatCount,'formatCount....................')
// 如果只有正常则不显示右侧文字
if(formatCount.length === 1 && formatCount[0].id === 0) {
  return container
}

   let locX = 25

        formatCount.forEach((item,index) => {


             container.addShape('text', {
              attrs:{
                x:locX,
                y:0,
                text:item.value,
                textAlign:'center',
                textBaseline:'middle',
                fill:item.color,
                fontSize:10
              }

          })
         locX  = locX +  String(item.value).length * 6
          if(index !== formatCount.length - 1) {
            container.addShape('text', {
              attrs:{
                x:locX,
                y:0,
                text:'/',
                textAlign:'center',
                textBaseline:'middle',
                fill:'#02B8FD',
                fontSize:16
              }



          })
            locX  = locX + 8


          }



        })



          return container;
        },
      });
//
    },
      // 竖向坐标处理
  adjustCoordinate(width, height, x,y) {
    let height2 = height * 0.9;
    const normalizedX = x / width;
    const normalizedY = y / height2;
    const newX = normalizedY * width * 0.5;
    const newY = normalizedX * height2 * 2.5;

    return { x: newX, y: newY };
},
handleLinkClick(evt) {
  // console.log(evt.item._cfg.model.data,'边的点击事件')
   this.getTaskList(this.tabsId, evt);

},
// 边线鼠标移入事件
handleLinkOver(evt) {
  // console.log(evt,'鼠标移入的事件')
  this.graph.updateItem(evt.item, {
    style: {
      lineWidth:4
    }
  });
},
// 边线鼠标移除事件
handleLinkLeave(evt) {
  this.graph.updateItem(evt.item, {
    style: {
      lineWidth:1
    }
  });

},
    // 请求获链路详情数据
    async getTaskList(pathId, evt) {

    // 这里要单独处理不可用节点的显示方式

    let data = evt.item._cfg.model.data;

    let suspectNodeData =
    this.handleNotAvailableNodeToPreNodeIp(data , evt.currentTarget.cfg.data.nodes);

debugger
      // debugger
      console.log(pathId, data,'pathId, data');

      const arr = data.target.split("_");
      let obj = {
        pageNo: 1,
        pageSize: 10,
        sourceIp: arr[0],
        topoId: pathId,
        targetIp: data.targetIp,
        preIp: data.sourceIp,
        linkIds:data.linkIds,
      };
      let url = '/pathTopo/getTaskList';
      if(this.showPeerToPeerLinkDetail){
          obj.sourceIp= data.sourceIp;
          obj.targetIp= data.curIp;
          obj.preIp= data.preIp;
          url = '/pathTopo/getPeerToPeerTaskList';
      }
      try {
        const res = await this.$http.PostJson(url, obj);
        const linkTable = res.data.records;
        let usea = {
          // preNodeIp: data.preIp,
          // targetIp: data.targetIp,
          preNodeIp: suspectNodeData.preIp,
          targetIp: suspectNodeData.targetIp,
          // 趋势图专用参数
          nodeIpTrend: suspectNodeData.curIp,
          nodeIp: data.curIp,
          sourceIp: arr[0],
          topoId: pathId,
          total: res.data.total,
          linkIds:data.linkIds,
          linkNodePairVos:data.linkNodePairVos
        };
        this.$emit('setPreIp',data.sourceIp)
        this.$emit("getLinkTable", linkTable, usea);
      } catch (error) {
     ;
      }
    },
    // 处理不可用节点方法
    // 处理BUG: 22403 【V4.6.1】【路径拓扑】不可用节点与其后可用节点之间没有显示数据，且点击不可用节点与前后节点之间的线路时没有正确加载链路
    handleNotAvailableNodeToPreNodeIp(nodeData , nodes){

      var resultData = {
        targetIp: nodeData.targetIp,
        preIp: nodeData.sourceIp,
        preNodeIp:nodeData.preIp,
        curIp:nodeData.curIp
      };
      // 判断是否有不可用节点
      var notAvailableArrays =  nodes.filter((item)=>{
         return item.data.suspect == true;
      });

      if(notAvailableArrays == null && notAvailableArrays.length == 0){
          return resultData;
      }
      var size = nodes.length;
      // 不可用节点的数据
      if(nodeData.suspect){
        var targetIp = nodeData.preIp;
        // 找到当前节点之前的是不可用节点
          var prevNodeIndex = -1;
           for(var index = size-1 ; index >=0 ; index--){
              if(nodes[index].data.nodeId == targetIp){
                prevNodeIndex = index;
                break;
              }
          }
          var prevNodeData = null;
          if(prevNodeIndex > 0 && prevNodeIndex <= size){
              prevNodeData = nodes[prevNodeIndex];
          }

        // 前面第一个节点是否是不可用节点
        if(prevNodeData  && prevNodeData.data.suspect){
          // 找到不可用节点后面的不是不可用节点的第一个节点数据
          for(var index = prevNodeIndex-1 ; index > 0 ; index -- ){
            if(nodes[index].data.suspect == false){
                prevNodeIndex = index;
                break;
            }
          }
          prevNodeData = (prevNodeIndex > 0 && prevNodeIndex <= size)? nodes[prevNodeIndex] : null;
          if(prevNodeData){
            resultData.preNodeIp = prevNodeData.data.value;
            resultData.preIp = prevNodeData.data.value;
          }

        }
        return resultData;

      }

      resultData.preIp = nodeData.preIp;
      resultData.preNodeIp = nodeData.preIp;
      // 当前节点的下一个节点是否是不可用节点
       var nextNodeIndex = -1;
       var targetIp = nodeData.targetIp;
      for(var index = size-1 ; index >=0 ; index--){
          if(nodes[index].data.nodeId == targetIp){
            nextNodeIndex = index;
            break;
          }
      }
      var nextNodeData = null;
      if(nextNodeIndex > 0 && nextNodeIndex <= size){
          nextNodeData = nodes[nextNodeIndex];
      }

      // 后面的第一个节点是否是不可用节点
      if(nextNodeData && nextNodeData.data.suspect){

        // 找到不可用节点后面的不是不可用节点的第一个节点数据
        for(var index = nextNodeIndex+1 ; index < size ; index ++ ){
          if(nodes[index].data.suspect == false){
              nextNodeIndex = index;
              break;
          }
        }
        nextNodeData = (nextNodeIndex > 0 && nextNodeIndex <= size)? nodes[nextNodeIndex] : null;
        if(nextNodeData){
          resultData.targetIp = nextNodeData.data.value;
          resultData.curIp = nextNodeData.data.value;
        }
      }
      return resultData;
    },
    search(searchVal) {
      this.clearHighlight()
      console.log(searchVal,'search')
      const graph = this.graph;
      // 搜索匹配的节点
  const matchedNodes = graph.getNodes().filter(node => {
    const nodeModel = node.getModel();
    console.log(nodeModel,'nodeModel')
    // 假设节点的标签存储在 label 字段中，您可能需要根据实际情况调整
    return nodeModel.data.label && nodeModel.data.label.toLowerCase().includes(searchVal.toLowerCase());
    // return nodeModel.label && nodeModel.label.toLowerCase().includes(searchVal.toLowerCase());
  });

  // 高亮匹配的节点
  matchedNodes.forEach(node => {
    const nodeModel = node.getModel();
    const originalSize = nodeModel.size ||24;
    let targetSize;

    if (Array.isArray(originalSize)) {
      targetSize = originalSize.map(s => s * 3);
    } else {
      targetSize = originalSize * 3;
    }
     // 使用动画效果更新节点大小
     this.animateNodeSize(node, originalSize, targetSize, 1250, true);
  });

  // 如果有匹配的节点，将视图居中到第一个匹配的节点
  if (matchedNodes.length > 0) {
    graph.focusItem(matchedNodes[0]);
    // 有匹配的节点保存画布位置
    this.savePathTopologyCoordinate({})
  }


    },
   // 搜索动画
    animateNodeSize(node, startSize, endSize, duration, shouldShrink = false) {
      const startTime = Date.now();
      const graph = this.graph;
      const originalSize = startSize;

      const animate = () => {
        const currentTime = Date.now();
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);

        // 使用 easeInOutCubic 缓动函数
        const easeProgress = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        let currentSize;
        if (Array.isArray(startSize)) {
          currentSize = startSize.map((start, index) =>
            start + (endSize[index] - start) * easeProgress
          );
        } else {
          currentSize = startSize + (endSize - startSize) * easeProgress;
        }

        graph.updateItem(node, { size: currentSize });

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else if (shouldShrink) {
          // 当放大动画完成后，开始缩小动画
          this.animateNodeSize(node, endSize, originalSize, duration);
        }
      };

      animate();
    },
    // 框选切换
    toggleBoxSelectMode(val) {
      this.boxSelectMode = val;
      // if(val) {
      //   //
      //   this.graph.setMode('custom');
      // } else {
      //   this.removeSelectionBox()
      //   this.graph.setMode('default');
      // }

    },


// 清除高亮的方法保持不变
clearHighlight() {
  const graph = this.graph;
  graph.getNodes().forEach(node => {
    const nodeModel = node.getModel();
    const originalSize = nodeModel.originSize || 24;
    graph.updateItem(node, {
      size: originalSize,
    });
  });
},
     // 全屏
     fullScreen() {
      // stage.fullScreen();
      //  穿参数
      this.toggleFullScreen()
      return

      // let obj = {
      //   backgroundImage:this.backgroundImage,
      //   pathTopoData:this.pathTopoData,
      //   showType:this.showType,
      //   showWarning:this.showWarning,
      //   tabsId:this.tabsId,
      //   configData:this.configData,
      //   layoutModel:this.layoutModel,
      //   topoTitle:this.topoTitle,
      //   pathTopologyLocXLocYMinMax:this.pathTopologyLocXLocYMinMax


      // }
      // sessionStorage.setItem("fullScreen", JSON.stringify(obj));

      //  var big = top.open(window.location.hostname === 'localhost' ? '/anpm-plug-pathtopofullscreen-ui.html' : '/pathtopofullscreen' );
    },
     // 全屏切换方法
    toggleFullScreen() {
      const container = document.getElementById('container');

      if (!document.fullscreenElement) {
        // 进入全屏
        if (container.requestFullscreen) {
          container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
          container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
          container.msRequestFullscreen();
        }

        this.isFullscreen = true;
        // 调整图表大小为屏幕尺寸
        this.graph.changeSize(window.screen.width, window.screen.height);
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }

        this.isFullscreen = false;
        // 恢复原始大小
        this.graph.changeSize(this.containerWidth, this.containerHeight);
      }

      // 重新渲染以适应新的尺寸
      this.graph.fitView();
    },
     // 处理全屏变化
    handleFullscreenChange() {
      if (!document.fullscreenElement) {
        this.isFullscreen = false;
        // 退出全屏时恢复原始大小
        this.graph.changeSize(this.containerWidth, this.containerHeight);
        this.graph.fitView();
      }
    },
     zoomIn() {
    const currentZoom = this.graph.getZoom();
    const newZoom = Math.min(currentZoom * 1.2, 5); // 限制最大缩放比例为5
    this.graph.zoomTo(newZoom,this.viewCenter);
    this.savePathTopologyCoordinate({}); // 保存画布状态
  },

  zoomOut() {
    const currentZoom = this.graph.getZoom();
    const newZoom = Math.max(currentZoom * 0.8, 0.2); // 限制最小缩放比例为0.2
    this.graph.zoomTo(newZoom,this.viewCenter);
    this.savePathTopologyCoordinate({}); // 保存画布状态
  },
    export() {
      let name = this.topoTabList.find(item => item.id == this.tabsId).name;
      let bgColor = this.currentSkin == 1 ? '#060D15' : '#fff';
      
      // 创建临时canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const graph = this.graph;
      
      // 获取当前视图的尺寸和位置信息
      const container = document.getElementById('container');
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      // 设置canvas尺寸为当前容器尺寸
      canvas.width = width;
      canvas.height = height;
      
      // 绘制背景色
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, width, height);
      
      if (this.backgroundImage) {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        img.onload = () => {
          // 绘制背景图片，保持与当前视图一致的尺寸和位置
          ctx.drawImage(img, 0, 0, width, height);
          
          // 直接将当前图形canvas内容复制到新canvas
          const graphCanvas = graph.get('canvas').get('el');
          ctx.drawImage(graphCanvas, 0, 0, width, height);
          
          // 导出
          const link = document.createElement('a');
          link.download = `${name}.png`;
          link.href = canvas.toDataURL('image/png');
          link.click();
        };
        
        img.src = this.backgroundImage;
      } else {
        // 没有背景图片时直接导出当前视图
        const graphCanvas = graph.get('canvas').get('el');
        ctx.drawImage(graphCanvas, 0, 0, width, height);
        
        const link = document.createElement('a');
        link.download = `${name}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
      }
    }
},
created() {
  console.log(window.name,'123')
  if(window.name.indexOf('bigscreen') > -1) {
    this.isBigScreen = true
  }

    // this.containerWidth = document.documentElement.clientWidth - 60 //实时屏幕宽度
    // this.containerHeight = document.documentElement.clientHeight //实时屏幕高度


},
mounted() {
  this.modalWidth = localStorage.getItem('modalWidth') * 0.98
  this.iframeHeight = window.innerHeight * 0.8; // 根据需要调整比例

   // 添加全屏变化监听
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
  window.pathTopoInstance = this;
  //  console.log(top.document.getElementById('container'),document.getElementById('container'),123)
  if(this.layoutModel == 2) {
    this.handleStarNodes()


  }else {
     this.handleNodes()

  }

  this.handleLink()
  // 自定义节点
  this. handleDomeNode()

  this.initG6()

},
beforeDestroy() {
    if (this.graph) {
    this.graph.off();
    this.graph.clear();
    this.graph.destroy();
    this.graph = null;
  }
     // 3. 清理数据引用
   this. nodeData= {
    nodes:[],
    edges:[],



  }
  
  // 清理定时器
  if (this.timer) {
    clearTimeout(this.timer);
    this.timer = null;
  }
  // Clean up global reference when component is destroyed
  delete window.pathTopoInstance;
  // 移除全屏变化监听
  document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);
}
}
</script>
<style scoped lang="less">
.topo-contaienr {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.mask-topo {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(55, 55, 55, 0.6);
  z-index: 1000;
}
.container {
  position: relative;

  .ellipsis-modal {
    position: absolute;
    top: 40px;
    left: 5%;
    width: 90%;
    height: 90%;
    padding: 42px 16px 0 16px;
    z-index: 1001;
    background-color: #08101a;
    .detail-logo-box {
      width: 150px;
      height: 28px;
      background-color: #08101a;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 9999;
    }
    .jtopo-box {
      width: 100%;
      height: 100%;
    }
    .modal-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 42px;
      line-height: 42px;
      text-align: left;
      display: flex;
      padding: 0 12px;
      justify-content: space-between;

      .title-text {
        font-size: 18px;
        color: #00ffee;
        font-weight: 700;
        margin-right: 20px;
      }
    }
  }
}
.logo-box {
  width: 160px;
  height: 28px;
  // background-color: #061824;
  background-color: var(--pathtopo_logo_box_bg_color, #061824);
  // border: 1px solid pink;
  position: fixed;
  left: 60px;
  bottom: 0;
  z-index: 666;
  display: flex;
  align-items: center;
  justify-content: center;
  .text {
    width: 90%;
    height: 28px;
    line-height: 28px;
    // color: #5ca0d5;
    color: var(--pathtopo_logo_box_font_color, #5ca0d5);
    font-weight: 700;
    overflow: hidden; /* 确保超出容器的文本被裁剪 */
    white-space: nowrap; /* 防止文本换行 */
    text-overflow: ellipsis; /* 在文本末端显示省略号 */
  }
}
</style>
