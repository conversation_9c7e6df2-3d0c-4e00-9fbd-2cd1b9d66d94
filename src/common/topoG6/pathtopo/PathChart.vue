<template>
  <section class="contain"> 
      
      <div class="echartStyle" ref="pathChart"></div>
  </section>
</template>

<script>
import "@/timechange.js";
import {getRandomColor,getRandomRgbaColor} from "@/common/util.js";
import moment from "moment";
let echarts = require('echarts/lib/echarts')
require('echarts/lib/chart/custom')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/title')
require('echarts/lib/component/legend')
require('echarts/lib/component/dataZoom');
require('echarts/lib/component/markLine');
import eConfig from "@/config/echart.config.js";
export default {
    name:'PathChart',
    props:['taskId','startTime','endTime'],
    data(){
        return {
             currentSkin: sessionStorage.getItem('dark') || 1,
            pathChart:null,
            chartData:[],
            startValue: '',
            endValue: '',
            // chartData:[[1679287786000, 1679323786000, 3], [1679374186000, 1679381386000, 8], [1679467786000, 1679471386000, 12]],
            colorList:['#4f81bd', '#c0504d', '#9bbb59', '#604a7b', '#948a54', '#e46c0b']
        }
    },
    mounted(){
        // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    },
 beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    methods:{
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        initChart(){
            let _this = this
            if (this.pathChart) {
                // echarts.dispose(this.pathChart)
                this.pathChart.clear();
            }
            this.pathChart = echarts.init(this.$refs["pathChart"]);
            this.pathChart.setOption(this.Option(), true);
            this.pathChart.on('click', params=> {
                 this.$emit('setRoutId',params.value[3],params.value[4])
            });
        },
        Option(){
            let _this = this
            return {
                title: {
                    show:this.chartData.length<1,
                    text: this.$t("comm_no_data"),
                    top: "40%",
                    left:"center",
                    textStyle:{
                        color:top.window.isdarkSkin == 1 ? '#617ca5':"#333",
                        fontFamily:"serif",
                        fontWeigth:"400",
                        fontSize:18
                    }
                },
                tooltip: this.setDelayLossTooltip(),
                xAxis: {
                    scale: true,
                     type: "time",
                    gridIndex: 0,
                    splitLine: {
                        lineStyle: {
                        type: "dotted",
                        color: top.window.isdarkSkin == 1 ?"rgba(42, 56, 64, 1)" : "#e3e7f2"
                        }
                    },
                    axisLabel:{
                        textStyle: {
                        color: top.window.isdarkSkin == 1 ?"#5CA0D5" :"#0e2a5f",
                        }
                    },
                    axisLine: {

                        lineStyle: {
                        color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                        width: 1,
                        },
                    },
                    axisLabel:{
                        formatter:(value,index)=>{
                            let date = new Date(value);
                            return date.format("yyyy-MM-dd HH:mm:ss")
                        }
                    },
                },
                yAxis: {
                    name: this.$t("comm_proportion") + "（%）",
                    axisLine: {
                        symbol: ["none", "arrow"],
                        symbolSize: [6, 10],
                        lineStyle: {
                        color: top.window.isdarkSkin == 1 ?"#5CA0D5" : "#676f82",
                        width: "1" //坐标线的宽度
                        }
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                        type: "dashed",
                        color: top.window.isdarkSkin == 1 ?"#5CA0D5" : "#e3e7f2"
                        }
                    }
                },
                dataZoom: [
                    {
                    type: "inside",
                    startValue: this.startValue,
                    endValue: this.endValue,
                    },
                    {
                    type: "slider",
                    left: "5%",
                    right: "5%",
                    height:20,
                    realtime: true,
                    startValue: this.startValue,
                    endValue: this.endValue,
                    fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin]  ,//"rgba(2, 29, 54, 1)",
                    borderColor: eConfig.dataZoom.borderColor[this.currentSkin]  ,//"rgba(22, 67, 107, 1)",
                    handleStyle: {
                        color: eConfig.dataZoom.handleStyle.color[this.currentSkin]  ,//"rgba(2, 67, 107, 1)"
                    },
                    textStyle:{
                        color: eConfig.dataZoom.textStyle.color[this.currentSkin]  ,//top.window.isdarkSkin == 1 ? '#617ca5' :"#e3e7f2"
                        }
                    }
                ],
                series: [{
                    type: 'custom',
                    renderItem: function(params, api){
                        let yValue = api.value(2);
                        let start = api.coord([api.value(0), yValue]);
                        let size = api.size([api.value(1) - api.value(0), yValue]);
                        let style = api.style();
                        return {
                            type: 'rect',
                            shape: {
                                x: start[0],
                                y: start[1],
                                width: size[0],
                                height: size[1]
                            },
                            style: style
                        };
                    },
                    label: {
                        show: true,
                        position: 'top',
                         textStyle:{
                            color: eConfig.dataZoom.textStyle.color[this.currentSkin]
                        }
                    },
                    dimensions: [ this.$t("specquality_start_time"), this.$t('specquality_end_time')],
                    encode: {
                        x: [0, 1],
                        y: 2,
                        tooltip: [0, 1],
                        itemName: 3
                    },
                    data: echarts.util.map(_this.chartData, function (item, index) {
                        console.log(getRandomRgbaColor());
                        return {
                            value: item,
                            itemStyle: {
                                color: getRandomRgbaColor()
                            }
                        };
                    })
                }]
            }
        },
        setDelayLossTooltip() {
            var _this = this;
            return eConfig.tip("axis", function(param) {
                let startTime =  new Date(param[0].value[0]).format("yyyy-MM-dd HH:mm:ss") 
                let endTime =  new Date(param[0].value[1]).format("yyyy-MM-dd HH:mm:ss") 
                let rate = param[0].value[2]
                let routeCode = param[0].value[4]

                var obj = {};
                param = param.reduce(function(item, next) {
                obj[next.seriesIndex]
                    ? ""
                    : (obj[next.seriesIndex] = true && item.push(next));
                return item;
                }, []);
                let delayTime = "",
                    delayTip = ""
                for (let i = 0, len = param.length; i < len; i++) {
                if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
                    delayTip = _this.$t('probetask_num_no')+':' +routeCode + "<br />";
                    delayTip +=  _this.$t('comm_proportion')+':' +rate +'%'+ "<br />";
                    delayTime +=
                        '<span class="tooltip-round" style="background-color:' +
                        param[i].color +
                        '"></span>';
                        delayTip += _this.$t('specquality_start_time') + '：'+startTime+"<br />"
                                + _this.$t('specquality_end_time') + '：'+endTime+"<br />"
                        "<br />";
                }

                }
                return delayTime + delayTip;
            });
        },
		// 获取路径分析图表数据
		getPathChartData(paramStartTime,paramEndTime) {
            this.$http
				.wisdomPost("/probetask/findRouteBarChartByTaskId", { taskId:this.taskId,startTime:paramStartTime,endTime:paramEndTime })
				.then(({ code, data, msg }) => {
					if (code === 1) {
                        this.chartData = []
                        let routeId = ''
                        let routeCode='';
                        if (data.length>0) {
                            if (data.length>10) {
                                this.startValue = data[data.length-10].stratTime*1000
                                this.endValue = data[data.length-1].endTime*1000
                            }else {
                                this.startValue = data[0].stratTime*1000
                                this.endValue = data[data.length-1].endTime*1000
                            }

                            console.log('this.startValue',this.startValue,'this.endValue',this.endValue);
                            data.forEach(item => {
                                item.activeRoute?routeId=item.routeId:''
                                item.activeRoute?routeCode=item.routeCode:''
                                this.chartData.push(
                                    [item.stratTime*1000,item.endTime*1000,item.percentage,item.routeId,item.routeCode]
                                )
                            });
                            console.log(routeId);
                            this.$emit('setRoutId',routeId,routeCode)
                        }else{
                            this.$emit('setRoutId',null,null)
                        }
                        
						this.initChart();
					} else {
						this.$Message.warning(msg);
					}
				})
				.catch((error) => {})
				.finally(() => {
					this.$emit("closeLoading");
				});
		},
    },
    beforeDestroy(){
        echarts.dispose(this.pathChart)
    }


}
</script>

<style>

</style>