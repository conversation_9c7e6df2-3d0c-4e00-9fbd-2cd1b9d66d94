<template>
  <Modal
    sticky
    :value="detailShow"
    :width="modalWidth"
    class="index-modal probetask-modal"
    :styles="{ top: '100px' }"
    :title="$t('peertopeer_path_information')"
    draggable
    :mask="true"
    @on-cancel="cancel"
    @on-visible-change="visiblechange"
    :footer-hide="true"
  >
    <section ref="indexEvent">
      <div v-if="checkedTab !== 'pathView'" class="context">
        <label>{{ $t("probetask_date") }}</label>
        <div class="time-content">
          <DatePicker
            format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :options="timeOptions"
            v-model="timeRange"
            :editable="false"
            :clearable="false"
            style="width: 330px"
            :confirm="false"
            @on-change="dateChange"
          >
          </DatePicker>
        </div>
        <Button
          class="query-btn"
          icon="ios-search"
          type="primary"
          @click="indexQueryClick"
        ></Button>
      </div>
      <div
        :class="
          currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin'
        "
      >
        <Tabs
          :type="currentSkin == 1 ? 'card' : 'line'"
          v-model="checkedTab"
          @on-click="tabClick"
          :class="{ 'tabs-card-content-black': currentSkin == 1 }"
        >
          <TabPane name="echartsView" :label="$t('performance_index')">
            <div class="index-title">
              {{ $t("probetask_path_analysis_tip") }}
            </div>

            <div class="lookBox" style="position: relative">
              <div class="contain">
                <PathChart
                  ref="PathChart"
                  :taskId="indexData.id"
                  :startTime="startDay"
                  :endTime="endDay"
                  @setRoutId="setRoutId"
                >
                </PathChart>
              </div>
            </div>

            <div class="index-title">
              {{ $t("probetask_path_topology_tip") }}（{{
                this.routeCode ? this.routeCode : "--"
              }}） {{ $t("task_dial_type") }}（{{
                this.taskConfigType || "--"
              }}）
            </div>
            <topology-item
              :data="topologyList"
              :linkData="linkData"
              ref="topologyChart"
              @on-click="topologyClick"
            ></topology-item>
            <div class="index-title">
              {{ $t("probetask_indicator_trends_tip") }}
            </div>
            <div class="index-line-box">
              <!--趋势图-->
              <div class="lookBox" style="position: relative; margin-top: 14px">
                <div
                  class="title"
                  style="position: absolute; left: 0; right: 0; top: -14px"
                >
                  {{ this.echartLookParama2.preNodeIp }}————>{{
                    this.echartLookParama2.nodeIp
                  }}
                </div>
                <div
                  class="title"
                  v-if="echart2.show"
                  style="position: absolute; left: 0; right: 0; top: 45%"
                >
                  {{ flowData.devName
                  }}{{
                    flowData.interfaceName ? "—" + flowData.interfaceName : ""
                  }}{{ flowData.interfaceIp ? "—" + flowData.interfaceIp : "" }}
                </div>
                <div class="contain" v-if="!topoShow">
                  <div
                    ref="probetask-delayLoss"
                    id="probetask-delayLoss"
                    class="echartStyle"
                    :style="'height:' + height + 'px'"
                  ></div>
                </div>
                <div
                  :class="{
                    table_empty: currentSkin == 1,
                    table2_empty: currentSkin == 0,
                  }"
                  v-else
                >
                  <p class="emptyText">{{ $t("common_No_data") }}</p>
                </div>
              </div>
            </div>
          </TabPane>

          <TabPane name="pathView" :label="$t('path_info')">
            <PathList
              ref="pathList"
              :taskId="indexData.id"
              :rowData="rowData"
              :startTime="startDay"
              :endTime="endDay"
            >
            </PathList>
          </TabPane>
        </Tabs>
      </div>
    </section>
  </Modal>
</template>

<script>
const pointGreen =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC";
const pointRed =
  "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC";
import axios from "axios";
import Qs from "qs";
import topologyItem from "@/common/flowChart/topologyX.vue";
import timeLine from "@/common/echarts/time-line.vue";
import echartFn from "@/common/mixins/echartFun";
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/dataZoom");
require("echarts/lib/component/markLine");
import moment from "moment";
import eConfig from "@/config/echart.config.js";
import global from "@/common/global.js";
export default {
  name: "indexItem",
  mixins: [echartFn],
  components: {
    topologyItem,
    PathList: () => import("./pathList.vue"),
    PathChart: () => import("./PathChart.vue"),
    // timeLine
  },
  props: {
    indexData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    rowData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    detailShow: {
      type: Boolean,
      default: false,
    },
    isSpecial: Boolean,
    High: Boolean,
  },
  computed: {
    linkData() {
      let obj = {
        linkId: this.routeId,
      };

      return obj;
    },
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      taskConfigType:"",
      modalWidth:0,
      checkedTab: "echartsView",
      row: {},
      topoShow: false,
      //是否是不可信节点
      suspect: false,
      loading1: true,
      srcAlias: "",
      destAlias: "",
      startIp: "",
      endIp: "",
      trendParam: {
        pre_ip: "",
        cur_ip: "",
      },
      timeRange: [],
      timeOptions: {
        shortcuts: [
          {
            text: this.$t("comm_half_hour"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return [
                start.format("yyyy-MM-dd HH:mm:ss"),
                end.format("yyyy-MM-dd HH:mm:ss"),
              ];
            },
            onClick: () => {},
          },
          {
            text: this.$t("comm_today"),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [
                new Date().format("yyyy-MM-dd 00:00:00"),
                new Date().format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_yesterday"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_7"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_30"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_curr_month"),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_preced_month"),
            value() {
              let date = new Date();
              let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
        ],
      },
      buttonList: [
        {
          value: 1,
          label: this.$t("dashboard_delay"),
        },
        {
          value: 2,
          label: this.$t("comm_packet_loss"),
        },
      ],
      indexQuery: {},
      buttonVal: 1,
      day: "",
      startDay: "",
      endDay: "",
      exportUrl: "sys/getPingTrace",
      exportQuery: {},
      topologyList: [],
      event_number: 0,
      delay_line: [], //时延走势数据
      loss_line: [], //丢包走势数据
      timeLine: {
        data: [],
      },
      //以下为趋势图有关参数
      echartLookParama2: {
        preNodeIp: "",
        nodeIp: "",
        linkId: "",
        startTime: "",
        endTime: "",
        queryType: "",
        special: false,
        High: false,
        level: "",
        taskFlag: 1,
      },
      height: 300,
      echart1: {
        show: true,
      },
      echart2: {
        show: false,
      },
      query: {},
      query2: {},
      startTime: 0,
      endTime: 0,
      preAlias: {
        name: "",
        port: "",
      },
      zAlias: {
        name: "",
        port: "",
      },
      delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "B",
        出流速: "B",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%",
      },
      flowColor: ["#478EE9", "#F19149"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: [],
      },
      flowData: {
        devName: "",
        interfaceIp: "",
        interfaceName: "",
        enter: [],
        issue: [],
      },
      delayLossScale: true,
      startScale: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayEnd: 100,
      startValue: "",
      endValue: "",
      scale: "",
      hoverDelayTime: "",
      hoverFlowTime: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {},
      },
      //以上为趋势图有关参数
      markPoint: "",
      markPointGreen: "",
      routeId: "",
      routeCode: "",
    };
  },
  mounted() {
    this.modalWidth = localStorage.getItem('modalWidth') * 0.98
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
  created() {
    let delayLossUnitTemp = {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] = "ms";
    // 丢包率
    delayLossUnitTemp[this.$t("comm_packet_loss")] = "%";
    // 入流速
    delayLossUnitTemp[this.$t("specquality_incoming_velocity")] = "bps";
    // 出流速
    delayLossUnitTemp[this.$t("specquality_exit_velocity")] = "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("specquality_incoming_rate")] = "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("specquality_output_rate")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t("alarm_disclaimer")] = "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t("specquality_availability_exemption")] = "%";
    // 可用率
    delayLossUnitTemp[this.$t("specquality_availability")] = "%";
    // 优良率
    delayLossUnitTemp[this.$t("specquality_rate_exemption")] = "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit, delayLossUnitTemp);

    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
  watch: {
    indexData: {
      handler(val) {
        console.log("val22222", val);
        // 重置滚动条
        this.$nextTick(() => {
            this.resetScroll();
          });
        this.topoShow = val.topoShow;
        this.buttonVal = 1;
        this.markPoint = val.eventStart;
        this.markPointGreen = val.eventEnd;
        this.trendParam.pre_ip = val.sourceIp;
        this.trendParam.cur_ip = val.destIp;
        this.indexQuery = Object.assign({}, val);
        this.taskConfigType =  global.convertTaskType(val.taskType);
        this.getLastHour();
        // this.getTopologyData(val);
        this.echartLookParama2.preNodeIp = val.sourceIp;
        this.echartLookParama2.nodeIp = val.destIp;
        this.echartLookParama2.linkId = val.sysLinkId;
        this.echartLookParama2.queryType = 1;
        this.echartLookParama2.special = this.isSpecial;
        this.echartLookParama2.High = val.isHigh || this.High;
        this.echartLookParama2.startTime = this.startDay;
        this.echartLookParama2.endTime = this.endDay;
        this.echartLookParama2.level = "";
        // this.getTrend(this.echartLookParama2);
      },
      deep: true,
    },
    height() {
      if (this.delayLossChart1) {
        this.delayLossChart1.clear();
        this.delayLossChart1.dispose();
      }
      top.document.getElementById("probetask-delayLoss").style.height = this.height + "px"
      this.$nextTick(() => {
        this.initEchart();
      })
    },
    // pathShow(newVal) {
    //   if(newVal) {
    //     console.log(newVal,'newVal.....')
    //     if (!this.delayLossChart1) {
    //   return;
    //    }
    //      this.delayLossChart1.dispose();
    //      this.delayLossChart1 = null;
    //      this.initEchart();

    //   }
    // }
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    /**
     * 重置滚动条函数
     */
     resetScroll(){
      if (this.$refs.firstDiv) {
        this.$refs.firstDiv.scrollTop = 0;
      }
     },
    dateChange(val, type) {
      console.log(val, type, "什么东西");
      console.log(this.timeRange, "timeRange....");
      if (type == "date") {
        this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")];
      }
    },
    cancel() {
      (this.pathShow = true), (this.topologyList = []);
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.$emit("closeModal");
    },
    visiblechange(val) {
      if (val) {
        this.$nextTick(() => {
          this.initEchart();
          this.$refs.PathChart.getPathChartData(this.startDay, this.endDay);
        });
      }
    },
    getPathList() {
      this.$nextTick(() => {
        this.$refs.pathList.currentNum = 1;
        this.$refs.pathList.query.pageNo = 1;
        this.$refs.pathList.query.pageSize = 10;
        this.$refs.pathList.getList();
      });
    },
    getAlias(ip, type) {
      //获取别名
      this.$http.wisdomPost("/link/getByIpName", { ip: ip }).then((res) => {
        if (res.code == 1) {
          if (type == 1) {
            this.srcAlias = res.data[0] || "";
          } else if (type == 2) {
            this.destAlias = res.data[0] || "";
          }
        }
      });
    },
    setRoutId(routeId, routeCode) {
      this.routeId = routeId;
      this.routeCode = routeCode;
      this.getTopologyData(this.indexData, routeId); // 获取路径拓扑数据
      this.getTrend(this.echartLookParama2, routeId); // 获取指标趋势数据
    },
    //获取数据
    getTopologyData(param, routeId = "") {
      let _self = this;
      _self.$refs["topologyChart"].resetData();
      _self.$http
        .wisdomPost("/trendData/selectGongHangIndexPing", {
          ...param,
          sysLinkId: routeId,
        })
        .then((res) => {
          let data = res.data || {},
            list = data.listl || [];
          if (list.length > 0) {
            list.unshift({
              ip1: data.sourceIp ||  this.indexQuery.sourceIp,
              isBuquan: 0,
              isBroken: 0,
            });
          }
          /*2021年4月25为改中断和任务策略任务显示一致，但数据不一致的bug而增加的代码*/
          let brokenNum = 0;
          if (list.length > 0) {
            let isBrokenF = false,
              isIpduan = false,
              isDelay = false,
              borkenIndex = 0,
              delayIndex = 0;
            list.forEach((item, index) => {
              if (item.isBroken === 1 || item.isBroken === 3 || item.isBroken === 7) {
                isBrokenF = true;
                borkenIndex = index;
              }
              if (item.isBroken === 4) {
                isIpduan = true;
              }
              if (item.isBroken === 2) {
                isDelay = true;
                delayIndex = index;
              }
            });
            for (let i = 0, len = list.length; i < len; i++) {
              /*重新组装数据start*/
              /*nodeColor:r,y,b;lineColor:r,y,b,linkPoint:true*/
              if (list[i].isBroken != 0) {
                brokenNum = list[i].isBroken;
              }
              list[i].nodeColor =
                list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
              list[i].lineColor =
                list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
              list[i].linkPoint = list[i].isBroken == 1 ? true : false;
              list[i].deviceTypeCss = "";
              /*重新组装数据end*/
            }

            //处理中断ip段 前的ip为*的处理
            let thisIndex = true;
            if (isBrokenF) {
              for (let index = borkenIndex; index > 0; index--) {
                if (thisIndex && list[index - 1].ip1 == "*") {
                  list[index - 1].nodeColor = "r";
                  list[index - 1].lineColor = "r";
                  thisIndex = true;
                } else if (thisIndex && list[index - 1].suspect === true) {
                  list[index - 1].nodeColor = "r";
                  list[index - 1].lineColor = "r";
                  thisIndex = true;
                } else {
                  thisIndex = false;
                }
              }
            }
            //处理时延ip段 前的ip为*的处理
            let delayFlag = true;
            if (isDelay) {
              for (let index = delayIndex; index > 0; index--) {
                if (delayFlag && list[index - 1].ip1 == "*") {
                  list[index - 1].nodeColor = "y";
                  list[index - 1].lineColor = "y";
                  delayFlag = true;
                } else if (delayFlag && list[index - 1].suspect === true) {
                  list[index - 1].nodeColor = "y";
                  list[index - 1].lineColor = "y";
                  delayFlag = true;
                } else {
                  delayFlag = false;
                }
              }
            }
            let indexBroken = list.map((item) => item.isBroken != 0).indexOf(true),
              indexX = 0;
            for (let i = indexBroken - 1; i >= 0; i--) {
              if (i === indexBroken - 1 && list[i].ip1 === "*" && i - 1 >= 0) {
                indexX = i - 1;
                list[i].lineColor = brokenNum === 2 ? "y" : "r";
              } else if (indexX === i && list[i].ip1 === "*" && i - 1 >= 0) {
                indexX = i - 1;
                list[i].lineColor = brokenNum === 2 ? "y" : "r";
              }
            }

            if (list.length === 2 && (list[0].isBroken === 1 || list[1].isBroken === 1)) {
              list[1].lineColor = "r";
              list[1].nodeColor = "r";
              list[1].linkPoint = true;
            }
            // for (let i = borkenIndex, len = list.length; i < len; i++) {
            //   if (isBrokenF && borkenIndex<i) {
            //     list[i].nodeColor = 'g';
            //     list[i].lineColor = 'g';
            //   }
            // }
            list.forEach((item, index) => {
              if (index > borkenIndex && borkenIndex != 0) {
                item.nodeColor = "g";
                item.lineColor = "g";
              }
            });
            this.topologyList = list;
          } else {
            this.topologyList = [];
          }
          if (data.broken_num == null) {
            _self.event_number = 0;
          } else {
            _self.event_number = data.broken_num;
          }
          _self.delay_line = data.delay || [];
          _self.timeLine.data = data.delay || [];
          _self.loss_line = data.las_vlue || [];
        });
    },
    //获取虚线数据
    getLineData(param) {
      let _self = this;
      _self.$http.wisdomPost("/sys/selectGongHangIndexTrace", param).then((res) => {
        let data = res.data || {};
        if (data.broken_num == null) {
          _self.event_number = 0;
        } else {
          _self.event_number = data.broken_num;
        }
        _self.delay_line = data.delay || [];
        _self.timeLine.data = data.delay || [];
        _self.loss_line = data.las_vlue || [];
      });
    },
    //获取当前最近一小时时间
    getLastHour() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000);
      this.startDay = this.indexQuery.startTime = new Date(start).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      this.endDay = this.indexQuery.endTime = new Date(end).format("yyyy-MM-dd HH:mm:ss");
      this.timeRange = [this.startDay, this.endDay];
      // this.day=[start,end];
    },
    //获取当前数据之前N月
    monthDate(val) {
      let start = new Date(),
        monthNumber = 2, //当前天数之前第几个月
        year = start.getFullYear(),
        month = start.getMonth() + 1,
        date = start.getDate(),
        newYear = "",
        newMonth = "",
        startDate = "",
        endDate = "";
      if (month - monthNumber < 1) {
        newYear = year - 1;
        newMonth = 12 - monthNumber + month;
      } else {
        newYear = year;
        newMonth = month - monthNumber;
      }
      startDate = newYear + "-" + newMonth + "-" + date + " 00:00:00";
      endDate = year + "-" + month + "-" + date + " 00:00:00";
      // if(new Date(val).getTime()<new Date(startDate).getTime()){
      //     if(this.$base.isShowPrompt()){
      //         this.$Message.error("请选择最近两个月内的时间段");
      //     }
      //    return []
      // }
      return [startDate, endDate];
    },
    //查询   负责 pathShow 和  topoShow
    indexQueryClick() {
      this.startDay = new Date(this.timeRange[0]).format("yyyy-MM-dd HH:mm:ss");
      this.endDay = new Date(this.timeRange[1]).format("yyyy-MM-dd HH:mm:ss");
      let startVal = moment(this.startDay, "YYYY-MM-DD hh:mm:ss").valueOf();
      let endVal = moment(this.endDay, "YYYY-MM-DD hh:mm:ss").valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning({
          content: this.$t("warning_time_not_exceed_62"),
          background: true,
        });
        return;
      }

      this.indexQuery.startTime = this.startDay;
      this.indexQuery.endTime = this.endDay;
      if (!this.startDay || !this.endDay) {
        this.$Message.warning({ content: "请选择起始日期", background: true });
        return;
      } else if (this.startDay > this.endDay) {
        this.$Message.warning({ content: "开始日期不能大于结束日期", background: true });
        return;
      }
      // if (!this.pathShow) {
      //   // pathList页面查询
      //   this.$refs.pathList.getList(this.startDay, this.endDay);
      // } else {
      //   //detailModal查询
      //   this.threndQuery();
      // }


      if(this.checkedTab === 'echartsView') {
        // detailModal查询
        this.threndQuery()

      }else {
        // pathList页面查询
        this.$refs.pathList.getList(this.startDay, this.endDay);
      }

    },
    threndQuery() {
      // debugger;
      if (this.topoShow) {
        return;
      }
      let date = this.monthDate(this.startDay);
      this.buttonVal = 1;
      this.delayStart = 0;
      this.delayEnd = 100;
      if (date.length > 0) {
        this.echartLookParama2.startTime = this.indexQuery.startTime;
        this.echartLookParama2.endTime = this.indexQuery.endTime;
        this.echartLookParama2.level = "";
        this.$refs.PathChart.getPathChartData(
          this.indexQuery.startTime,
          this.indexQuery.endTime
        );
      }
    },
    pathListQuery() {
      this.$refs.pathList.getList(
        this.row,
        this.indexQuery.startTime,
        this.indexQuery.endTime
      );
    },
    tabClick(val) {
      if(val === "echartsView"){
        // 指标信息
      }else {
        // 路径信息
          this.pathListQuery();
      }
    },
    //导出
    exportClick(type) {
      // type:1、导出当前 ，2、导出全部
      let _self = this,
        user = JSON.parse(sessionStorage.getItem("accessToken")),
        baseURL = this.$baseUrl + "/sys/getPingTrace",
        a = null,
        param = {
          token_id: user.token_id,
          sys_link_id: this.indexQuery.sysLinkId,
          source_ip: this.indexQuery.sourceIp,
          dest_ip: this.indexQuery.destIp,
        };
      let date = _self.monthDate(_self.indexQuery.startTime);
      if (date.length > 0) {
        if (type === 1) {
          param.startTime = _self.indexQuery.startTime;
          param.endTime = _self.indexQuery.endTime;
          a = document.getElementById("current");
        } else {
          param.startTime = date[0];
          param.endTime = date[1].split(" ")[0] + " 23:59:59";
          a = document.getElementById("all");
        }
        if (_self.timeLine.data.length > 0) {
          a.href =
            baseURL + (baseURL.indexOf("?") === -1 ? "?" : "&") + Qs.stringify(param);
        }
      }
    },
    //按钮切换
    buttonClick(val) {
      let _self = this;
      _self.buttonVal = val;
      _self.timeLine.data = [];
      if (val === 1) {
        _self.timeLine.data = _self.delay_line;
      } else {
        _self.timeLine.data = _self.loss_line;
      }
    },
    //流程图事件
    topologyClick(row) {
      // debugger;
      if (!this.High) {
        this.indexQuery.flowChartType = row.type;
        this.indexQuery.sourceIp = row.data[0].ip1;
        this.indexQuery.destIp = row.data[1].ip1;
        this.trendParam.preIp = row.data[0].ip1;
        this.trendParam.curIp = row.data[1].ip1;

        this.echartLookParama2.level = "";
        this.buttonVal = 1;
        // this.getAlias(this.trendParam.pre_ip,1);
        // this.getAlias(this.trendParam.cur_ip,2);
        if (row.type == 1 && row.index != 0) {
          this.echartLookParama2.queryType = 1;
          this.echartLookParama2.preNodeIp = row.data[0].ip1;
          this.echartLookParama2.nodeIp = row.data[1].ip1;
          if (this.echartLookParama2.nodeIp != "*") {
            this.topoShow = false;
            this.getTrend(this.echartLookParama2, this.routeId);
          } else {
            this.topoShow = true;
          }
        } else if (row.type == 2) {
          this.topoShow = false;
          this.echartLookParama2.queryType = 2;
          this.echartLookParama2.preNodeIp = row.data[0].ip1;
          this.echartLookParama2.nodeIp = row.data[1].ip1;

          if (row.suspect) {
            this.suspect = true;
            // return
          } else {
            this.suspect = false;
          }
          this.getTrend(this.echartLookParama2, this.routeId);
        }
      }
    },

    /*趋势图有关*/
    async getTrend(param, routeId = "") {
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.loading1 = true;
      param = { ...param, linkId: routeId };
      await this.$http.PostJson("/trend/getDelayAndLostTrend", param).then((res) => {
        //时延丢包趋势数据
        if (res.code === 1) {
          if (res.data) {
            this.preAlias.name = res.data.preName;
            this.preAlias.port = res.data.prePort;
            this.zAlias.name = res.data.name;
            this.zAlias.port = res.data.port;
            this.delayLossLevel = res.data.level;
            const delayData = res.data.lineOne,
              lossData = res.data.lineTwo;
            let hasData = true;
            if (!delayData && !lossData) {
              hasData = false;
              this.echart1.show = false;
            }
            if (hasData) {
              this.echart1.show = true;
              this.delayStart = 0;
              if (delayData) {
                // if (delayData.length <= 300) {
                this.delayEnd = 100;
                this.startValue = delayData[0][0];
                this.endValue = delayData[delayData.length - 1][0];
                // } else {
                //   this.delayEnd = (300 * 100) / delayData.length;
                //   this.startValue = delayData[0][0];
                //   this.endValue = delayData[299][0];
                // }
                this.delayLossScale = true;
                this.delayLossData.delay = delayData;
              } else if (lossData) {
                // if (lossData.length <= 300) {
                this.delayEnd = 100;
                this.startValue = lossData[0][0];
                this.endValue = lossData[lossData.length - 1][0];
                // } else {
                //   this.delayEnd = (300 * 100) / lossData.length;
                //   this.startValue = lossData[0][0];
                //   this.endValue = lossData[299][0];
                // }
                this.delayLossScale = true;
                this.delayLossData.loss = lossData;
              }
            }

            // if (res.data.lineOne && res.data.lineOne.length > 0) {
            //   this.echart1.show = true;
            //   this.delayStart = 0;
            //   if (res.data.lineOnetotal <= 300) {
            //     this.delayEnd = 100;
            //     this.startValue = res.data.lineOne[0][0];
            //     this.endValue =
            //       res.data.lineOne[res.data.lineOne.length - 1][0];
            //   } else {
            //     this.delayEnd = (300 * 100) / res.data.lineOnetotal;
            //     this.startValue = res.data.lineOne[0][0];
            //     this.endValue = res.data.lineOne[299][0];
            //   }
            //   this.delayLossScale = true;
            //   this.delayLossData.delay = res.data.lineOne;
            // }
            if (lossData && lossData.length > 0) {
              this.delayLossData.loss = lossData;
            }
            this.$store.commit("updateDelayLossHistory", {
              level: res.data.level,
              datas: [this.delayLossData.delay, this.delayLossData.loss],
            }); //保存数据
            this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
          }
        }
      });

      let trendParam = JSON.parse(JSON.stringify(param));
      trendParam = Object.assign(trendParam, { snmp: false });
      await this.$http.PostJson("/trend/getDataTrend", trendParam).then((res) => {
        //流速趋势数据
        if (res.code === 1) {
          this.flowData.devName = res.data.devName ?? "";
          this.flowData.interfaceIp = res.data.interfaceIp ?? "";
          this.flowData.interfaceName = res.data.interfaceName ?? "";
          let flowUnit = 1;
          this.flowLevel = res.data.level ?? "";
          if (!res.data.lineOne || res.data.lineOne.length < 1) {
            this.echart2.show = false;
            this.flowLevel = 99;
          }
          if (res.data.lineTwo && res.data.lineTwo.length > 0) {
            this.echart2.show = true;
            let unitChange = this.getFlowUnit(res.data.lineTwo);
            this.flowUnit = unitChange[0];
            this.delayLossUnit[this.$t("specquality_incoming_velocity")] = unitChange[0];
            this.delayLossUnit[this.$t("specquality_exit_velocity")] = unitChange[0];
            flowUnit = unitChange[1];
            this.flowData.enter = res.data.lineTwo.map((item) => {
              return [item[0], item[1]];
            });
          }
          if (res.data.lineOne && res.data.lineOne.length > 0) {
            this.flowData.issue = res.data.lineOne.map((item) => {
              return [item[0], item[1]];
            });
          }

          this.$store.commit("updateFlowHistory", {
            level: res.data.level,
            datas: [this.flowData.enter, this.flowData.issue],
          }); //保存数据
          this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
          this.$store.commit("setflowUnit", {
            level: res.data.level,
            unit: this.flowUnit,
          }); //保存单位
        }
      });
      this.startScale = false;
      this.loading1 = false;
      if (this.echart2.show) {
        this.height = 500;
      } else if (!this.echart2.show) {
        this.height = 300;
      }
      
      top.document.getElementById("probetask-delayLoss").style.height = this.height + "px";
      this.$nextTick(() => {
          this.initEchart();
      });
    },

    setDelayLossTooltip() {
      let _self = this;
      return eConfig.tip("axis", function (param) {
        _self.scale = param[0].data[0];
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex] ? "" : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : _self.flowSize(param[i].value[1], true, true)) +
              // param[i].value[1]
              // _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip;
      });
    },
    Option() {
      let optionArr = [
        {
          title: {
            show:
              this.delayLossData.delay.length === 0 &&
              this.delayLossData.loss.length === 0,
            text: this.$t("common_No_data"),
            left: "center",
            top: "90px",
            textStyle: {
              color: top.window.isdarkSkin == 1 ? "#465b7a" : "grey",
              fontFamily: "serif",
              fontWeigth: "400",
              fontSize: 18,
            },
          },
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "5%",
              top: "40px",
              width: "90%",
              height: "140px",
            },
            {
              // show:this.echart2.show,
              left: "5%",
              top: "300px",
              width: "90%",
              height: this.echart2.show ? "140px" : "0px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "40%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t("speed_delay"), this.$t("comm_packet_loss")],
            },
            {
              show: this.echart2.show,
              top: "250px",
              right: "40%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              data: [
                this.$t("specquality_incoming_velocity"),
                this.$t("specquality_exit_velocity"),
              ],
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            // 时延
            {
              name: this.$t("comm_delay(ms)"),
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 ? "#2A3840" : "#e3e7f2",
                },
              },
            },
            // 丢包
            {
              name: this.$t("comm_loss_rate"),
              type: "value",
              scale: true,
              gridIndex: 0,
              min: 0,
              max: 100,
              position: "right",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 ? "#2A3840" : "#e3e7f2",
                },
              },
            },
            // 流速
            {
              show: this.echart2.show,
              name: this.$t("server_flow_rate"),
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 ? "#2A3840" : "#e3e7f2",
                },
              },
              axisLabel: {
                show: true,
                 interval: 'auto',
                formatter: (value) => {
                  return this.flowSize(value, true, true);
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue == this.endValue ? "" : this.startValue,
              endValue: this.startValue == this.endValue ? "" : this.endValue,
              // start:this.delayStart,
              // end:this.delayEnd
            },
            {
              type: "slider",
              left: "5%",
              right: "5%",
              height: 20,
              top: this.echart2.show ? "480px" : "250px",
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue == this.endValue ? "" : this.startValue,
              endValue: this.startValue == this.endValue ? "" : this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin]  ,// "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin]  ,// "rgba(22, 67, 107, 1)",
              handleStyle: {
                color:eConfig.dataZoom.handleStyle.color[this.currentSkin]  ,//  "rgba(2, 67, 107, 1)",
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin]  ,// top.window.isdarkSkin == 1 ? "#617ca5" : "#e3e7f2",
              },
            },
          ],
          series: [
            {
              type: "line",
              name: this.$t("speed_delay"),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              // symbol: this.delayLossData.delay.length>1?"none":"none",
              // seriesLayoutBy:'row',
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.delay,
              markLine: {
                data: [
                  {
                    symbol: "image://" + pointRed,
                    symbolSize: 10,
                    xAxis: this.markPoint,
                    symbolRotate: "0",
                    // symbolOffset:[0,'70px'],
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint;
                      },
                      backgroundColor: top.window.isdarkSkin == 1 ? "#617ca5" : "#fff",
                      // padding: [0]
                    },
                  },
                  {
                    symbol: "image://" + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen,
                    symbolRotate: "0",
                    // symbolOffset:[0,'70px'],
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      backgroundColor: "#fff",
                      formatter: () => {
                        return this.markPointGreen;
                      },
                      // padding: [0]
                    },
                  },
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1,
                  },
                },
              },
            },
            {
              type: "line",
              name: this.$t("comm_packet_loss"),
              xAxisIndex: 0,
              yAxisIndex: 1,
              // seriesLayoutBy:'row',
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              // symbol: this.delayLossData.loss.length>1?"none":"none",
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.loss,
            },
            {
              show: this.echart2.show,
              name: this.$t("specquality_incoming_velocity"),
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              // symbol: this.flowData.enter.length>1?"none":"none",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(71,142,233, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(71,142,233, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t("specquality_exit_velocity"),
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              //symbol: this.flowData.issue.length>1?"none":"none",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart1) {
        this.delayLossChart1.clear();
      }
      that.delayLossChart1 = echarts.init(this.$refs["probetask-delayLoss"]);

      /*设置时延丢包率echart图*/
      that.delayLossChart1.setOption(this.Option());
      that.delayLossChart1.getZr().on("mousewheel", (params) => {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getgoodTlevel = that.$store.state.goodRateTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据
        let getgoodSaveData = that.$store.state.goodRateHistory; //获取保存的数据

        var pointInPixel = [params.offsetX, params.offsetY];
        if (that.delayLossChart1.containPixel({ gridIndex: [0, 1, 2] }, pointInPixel)) {
          let startValue = that.delayLossChart1.getModel().option.dataZoom[0].startValue;
          let endValue = that.delayLossChart1.getModel().option.dataZoom[0].endValue;
          this.echartLookParama2.startTime = new Date(startValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          this.echartLookParama2.endTime = new Date(endValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);
          let differ = endValue - startValue;
          let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
          let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
          let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
          var levelNum = "";
          let start = that.delayLossChart1.getModel().option.dataZoom[0].start;
          let end = that.delayLossChart1.getModel().option.dataZoom[0].end;

          if (params.wheelDelta >= 0) {
            if (this.delayLossLevel == 1) {
              // 小时粒度
              if (differ < minute) {
                if (!that.startScale) {
                  that.startScale = true;
                  levelNum = 2;
                  let delayParam = Object.assign(this.echartLookParama2, {
                    level: levelNum,
                    linkId: this.routeId,
                  });
                  let flowParam = Object.assign(delayParam, {
                    snmp: this.dataSource == 1 ? true : false,
                  });
                  this.getDelayLoss(delayParam, flowParam);
                }
              }
            } else if (this.delayLossLevel == 2) {
              // 分钟粒度
              if (differ < original) {
                if (!that.startScale) {
                  that.startScale = true;
                  if (this.High) {
                    levelNum = 4;
                    let delayParam = Object.assign(this.echartLookParama2, {
                      level: levelNum,
                      linkId: this.routeId,
                    });
                    let flowParam = Object.assign(delayParam, {
                      snmp: this.dataSource == 1 ? true : false,
                    });
                    this.getDelayLoss(delayParam, flowParam);
                  }
                }
              }
            }
          } else {
            if (start == 0 && end == 100) {
              //是否处在缩放过程中
              if (that.delayLossLevel == getTlevel) {
                that.setPs(that.delayLossLevel, [
                  that.timeChange(startValue),
                  that.timeChange(endValue),
                ]);
                that.startScale = false;
              } else {
                let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                if (that.flowLevel == getflowTlevel) {
                  that.startScale = false;
                } else {
                  if (that.flowLevel == 2) {
                    that.flowLevel = 1;
                    that.startScale = true;
                    that.flowUnit = getflowSaveUnit.HoursUnit;
                    that.flowData.enter = getflowSaveData.HoursData.enter;
                    that.flowData.issue = getflowSaveData.HoursData.issue;
                  } else if (that.flowLevel == 3 || that.flowLevel == 4) {
                    that.flowLevel = 2;
                    that.startScale = true;
                    that.flowUnit = getflowSaveUnit.minuteUnit;
                    that.flowData.enter = getflowSaveData.minuteData.enter;
                    that.flowData.issue = getflowSaveData.minuteData.issue;
                  }
                }
                if (that.goodRateLevel == getgoodTlevel) {
                  that.startScale = false;
                } else {
                  if (that.goodRateLevel == 2) {
                    that.goodRateLevel = 1;
                    that.startScale = true;
                    that.goodRateData.nrUseRateList =
                      getgoodSaveData.HoursUnit.nrUseRateList;
                    that.goodRateData.nrGoodRateList =
                      getgoodSaveData.HoursUnit.nrGoodRateList;
                    that.goodRateData.useRateList = getgoodSaveData.HoursUnit.useRateList;
                    that.goodRateData.goodRateList =
                      getgoodSaveData.HoursUnit.goodRateList;
                  } else if (that.delayLossLevel == 3 || that.delayLossLevel == 4) {
                    that.delayLossLevel = 2;
                    that.startScale = true;
                    that.goodRateData.nrUseRateList =
                      getgoodSaveData.minuteUnit.nrUseRateList;
                    that.goodRateData.nrGoodRateList =
                      getgoodSaveData.minuteUnit.nrGoodRateList;
                    that.goodRateData.useRateList =
                      getgoodSaveData.minuteUnit.useRateList;
                    that.goodRateData.goodRateList =
                      getgoodSaveData.minuteUnit.goodRateList;
                  }
                }

                if (that.delayLossLevel == getTlevel) {
                  that.startScale = false;
                } else if (that.delayLossLevel == 2) {
                  that.delayLossLevel = 1;
                  that.startScale = true;
                  that.delayLossData.delay = getSaveData.HoursData.delay;
                  that.delayLossData.loss = getSaveData.HoursData.loss;
                  that.delayStart = getSite.psH.start;
                  that.startValue = getSite.psH.start;
                  that.delayEnd = getSite.psH.end;
                  that.endValue = getSite.psH.end;
                } else if (that.delayLossLevel == 3 || that.delayLossLevel == 4) {
                  that.delayLossLevel = 2;
                  that.startScale = true;
                  that.delayLossData.delay = getSaveData.minuteData.delay;
                  that.delayLossData.loss = getSaveData.minuteData.loss;
                  that.delayStart = getSite.psM.start;
                  that.startValue = getSite.psM.start;
                  that.delayEnd = getSite.psM.end;
                  that.endValue = getSite.psM.end;
                }

                setTimeout(() => {
                  that.startScale = false;
                  that.initEchart();
                }, 300);
              }
            }
          }
        }
      });
    },
    async getDelayLoss(
      delayParam,
      flowParam,
      goodRateParam,
      cachD,
      cachF,
      cachG,
      hoverDelayTime,
      flowTime,
      useTime
    ) {
      let isUpdate = false;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        this.loading1 = true;
        await this.$http
          .PostJson("/trend/getDelayAndLostTrend", delayParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.delayLossLevel = res.data.level;
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              let index = this.closest(lineData, hoverDelayTime);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.delayStart = 0;
                // if (res.data.lineOnetotal <= 300) {
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
                // } else {
                //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
                //   if (index <= 150) {
                //     this.startValue = res.data.lineOne[0][0];
                //     if (index + 150 < res.data.lineOnetotal - 1) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //           res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   } else {
                //     this.startValue = res.data.lineOne[index - 150][0];
                //     if (index + 150 < res.data.lineOnetotal) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //           res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   }
                // }
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            }
          });
      }

      if (flowParam.level != 99) {
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, {});
        await this.$http.PostJson("/trend/getDataTrend", trendParam).then((res) => {
          //流速趋势数据
          if (res.code === 1) {
            let flowUnit = 1;
            this.flowLevel = res.data.level;
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              let unitChange = this.getFlowUnit(res.data.lineOne);
              this.flowUnit = unitChange[0];
              this.delayLossUnit[this.$t("specquality_incoming_velocity")] =
                unitChange[0];
              this.delayLossUnit[this.$t("specquality_exit_velocity")] = unitChange[0];
              flowUnit = unitChange[1];
              this.flowData.enter = res.data.lineTwo.map((item) => {
                // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                return [item[0], item[1]];
              });
              console.log(this.flowData.enter);
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.flowData.issue = res.data.lineOne.map((item) => {
                // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                return [item[0], item[1]];
              });
            }
            if (cachF) {
              this.$store.commit("updateFlowHistory", {
                level: res.data.level,
                datas: [this.flowData.enter, this.flowData.issue],
              }); //保存数据
              this.$store.commit("setflowUnit", {
                level: res.data.level,
                unit: this.flowUnit,
              }); //保存单位
            }
          }
        });
      }

      this.delayLoading = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      this.loading1 = false;
      if (isUpdate) {
        this.initEchart();
      }
    },
    //时间推算
    setTime(level, time) {
      let Interval = 0,
        start = new Date(this.echartLookParama2.startTime).getTime(),
        end = new Date(this.echartLookParama2.endTime).getTime(),
        newStart = 0,
        newEnd = 0;
      if (level == 0) {
        Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 1) {
        Interval = 12 * 60 * 60 * 1000;
      } else if (level == 2) {
        Interval = 30 * 60 * 1000;
      }
      newStart = time - Interval;
      newEnd = time + Interval;

      if (newStart < start) {
        newStart = start;
      }
      if (newEnd > end) {
        newEnd = end;
      }
      newStart = this.timeChange(newStart);
      newEnd = this.timeChange(newEnd);
      return [newStart, newEnd];
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },
  },
  beforeDestroy() {
        // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.clear();
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
};
</script>
<style>
.index-modal
  .ivu-modal-body
  .index-query
  .index-query-box
  .ivu-picker-confirm
  button {
  width: auto !important;
  height: auto !important;
}

.index-modal .ivu-modal-body {
  height: auto !important;
}
</style>
