<template>
  <Modal
    sticky
    :value="detailShow"
    width="99%"
    class="index-modal"
    :title="title"
    draggable
    :mask="true"
    @on-cancel="cancel"
    @on-visible-change="visiblechange"
    :footer-hide="true"
  >
    <div class="index-line-box">
      <div class="index-query">
        <div class="index-query-box">
          <label>{{ $t("probetask_date") }}</label>
          <div class="time-content">
            <DatePicker
              type="datetime"
              :value="startDay"
              :placeholder="$t('comm_choose_time')"
              :options="startOptions"
              format="yyyy-MM-dd HH:mm:ss"
              @on-change="startDayChange"
            >
            </DatePicker>
          </div>
        </div>
        <div class="index-query-box">
          <label style="text-align: center">{{ $t("comm_to") }}</label>
          <div class="time-content">
            <DatePicker
              type="datetime"
              :value="endDay"
              :placeholder="$t('comm_choose_time')"
              :options="endOptions"
              format="yyyy-MM-dd HH:mm:ss"
              @on-change="endDayChange"
            >
            </DatePicker>
          </div>
        </div>
        <div class="index-query-box">
          <Button
            class="query-btn"
            icon="ios-search"
            type="primary"
            @click="indexQueryClick"
          ></Button>
        </div>
      </div>
    </div>
    <div class="firstDiv">
      <div class="index-line-box" style="height: 100%">
        <!--趋势图-->
        <div
          class="lookBox"
          style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100%;
          "
        >
          <Loading :loading="loading"></Loading>
          <div class="contain" v-if="!loading && !topoShow">
            <div
              ref="indexChart"
              id="indexChart"
              class="echartStyle"
              :style="'height:' + height + 'px'"
            ></div>
          </div>
          <div
            :class="{
              table_empty: currentSkin == 1,
              table2_empty: currentSkin == 0,
            }"
            v-if="topoShow"
          >
            <p class="emptyText">{{ $t("common_No_data") }}</p>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
const pointGreen =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC';
const pointRed =
    'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC';
import echartFn from '@/common/mixins/echartFun';
import '@/timechange.js';
let echarts = require('echarts/lib/echarts');
require('echarts/lib/chart/line');
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
require('echarts/lib/component/dataZoom');
require('echarts/lib/component/markLine');
import eConfig from '@/config/echart.config.js';
import moment from 'moment';
export default {
    name: 'indexItem',
    mixins: [echartFn],
    props: {
        deailData: {
            type: Object,
            default: function () {
                return {};
            }
        },
        detailShow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            indexChart: null,
            loading1: true,
            title: '',
            loading: true,
            topoShow: false,
            startOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            this.indexQuery.endTime = this.endDay = new Date().format('yyyy-MM-dd HH:mm:ss');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd HH:mm:ss');
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const start = new Date();
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            this.indexQuery.endTime = this.endDay = new Date().format('yyyy-MM-dd HH:mm:ss');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            this.indexQuery.endTime = this.endDay = start.format('yyyy-MM-dd 23:59:59');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            end.setTime(end.getTime());
                            this.indexQuery.endTime = this.endDay = end.format('yyyy-MM-dd 23:59:59');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            end.setTime(end.getTime());
                            this.indexQuery.endTime = this.endDay = end.format('yyyy-MM-dd 23:59:59');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    }
                ],
                disabledDate(date) {
                    return date && date.valueOf() > Date.now();
                }
            },
            endOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            this.indexQuery.endTime = this.endDay = new Date().format('yyyy-MM-dd HH:mm:ss');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd HH:mm:ss');
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const start = new Date();
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            this.indexQuery.endTime = this.endDay = new Date().format('yyyy-MM-dd HH:mm:ss');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            this.indexQuery.endTime = this.endDay = start.format('yyyy-MM-dd 23:59:59');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            end.setTime(end.getTime());
                            this.indexQuery.endTime = this.endDay = end.format('yyyy-MM-dd 23:59:59');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return start;
                        },
                        onClick: picker => {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            end.setTime(end.getTime());
                            this.indexQuery.endTime = this.endDay = end.format('yyyy-MM-dd 23:59:59');
                            this.indexQuery.startTime = this.startDay = start.format('yyyy-MM-dd 00:00:00');
                        }
                    }
                ],
                disabledDate(date) {
                    return date && date.valueOf() > Date.now();
                }
            },
            indexQuery: {},
            buttonVal: 1,
            day: '',
            startDay: '',
            endDay: '',
            event_number: 0,
            delay_line: [], //时延走势数据
            loss_line: [], //丢包走势数据
            timeLine: {
                data: []
            },
            //以下为趋势图有关参数
            echartParam: {
                beginTime: '',
                endTime: '',
                pageNo: 1,
                pageSize: 1,
                phyLinkId: '',
                level:''
            },
            height: 600,
            echart1: {
                show: true
            },
            echart2: {
                show: true
            },
            query: {},
            query2: {},
            startTime: 0,
            endTime: 0,
            preAlias: {
                name: '',
                port: ''
            },
            zAlias: {
                name: '',
                port: ''
            },
            delayLossColor: ['#00FFEE', '#0290FD'],
            delayLossUnit: {
                时延: 'ms',
                丢包率: '%',
                入流速: 'B',
                出流速: 'B',
                入利用率: '%',
                出利用率: '%'
            },
            delayLossUnitEn: {
                'Delay ': 'ms',
                'Packet loss rate': '%',
                'Inflow velocity': 'B',
                'Outflow velocity': 'B',
                'Ingress Rate': '%',
                'Outbound Rate': '%'
            },
            flowColor: ['#81c8ff', '#9bfbae', '#00FFEE', '#0290FD'],
            flowUnit: 'B',
            goodRateColor: ['#478EE9', '#46B759', '#F19149', '#015197'],
            goodRateUnit: '%',
            delayLossData: {
                delay: [],
                loss: []
            },
            flowData: {
                intoBandwithRates: [],
                outBandwithRates: [],
                enter: [],
                issue: []
            },
            delayPs: {
                //记录滚动条位置的对象，保存在sessionStorage中
                psD: {},
                psH: {},
                psM: {},
                psS: {}
            },
            startScale: false,
            level:0,
            delayLossLevel: 0,
            flowLevel: 0,
            goodRateLevel: 0,
            delayStart: 0,
            delayEnd: 100,
            startValue: '',
            endValue: '',
            scale: '',
            //以上为趋势图有关参数
            markPoint: '',
            markPointGreen: ''
        };
    },
    watch: {
        deailData: {
            handler(val) {
                this.title = `${val.sourceName}(${val.sourceIp})-${val.targetName}(${val.targetIp})`;
            },
            deep: true
        }
    },
    computed: {
        // topoShow() {
        //     let show = false;
        //     // 趋势图所有指标都无数据
        //     if (this.delayLossData.delay.length == 0 && this.delayLossData.loss.length == 0 && this.flowData.intoBandwithRates.length == 0 && this.flowData.outBandwithRates.length == 0 && this.flowData.enter.length == 0 && this.flowData.issue.length == 0) {
        //         show = true;
        //     }
        //     return show;
        // }
    },
    methods: {
          handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        flowSizeOnlyKbps (size, unit = true, local = false)  {
      if (typeof size !== 'number' && size < +0) {
        throw new TypeError('The flow must be a positive number.');
      }
      size = Math.max(size, 0);
      const result = size > 1000
        ? size / 1000 > 1000
          ? size / (1000 * 1000) > 1000
            ? size / (1000 * 1000 * 1000) > 1000
              ? size / (1000 * 1000 * 1000 * 1000) > 1000
              ? size / (1000 * 1000 * 1000 * 1000  * 1000) > 1000
                ?[(size / (1000 * 1000 * 1000 * 1000 * 1000 * 1000)), 'Ebps']
                  : [size / (1000 * 1000 * 1000 * 1000 * 1000), 'Pbps']
                : [size / (1000 * 1000 * 1000 * 1000), 'Tbps']
              : [size / (1000 * 1000 * 1000), 'Gbps']
            : [size / (1000 * 1000), 'Mbps']
          : [size / 1000, 'Kbps']
        : [(size), 'bps'];
      result[ 0 ] = Number.parseFloat(result[ 0 ]).toFixed(2);
      if (local) {
        result[ 0 ] = result[ 0 ].toLocaleString();
      }
      return unit ? result.join('') : result[ 0 ];
    },
        cancel() {
            this.delayLossData.delay = [];
            this.delayLossData.loss = [];
            this.$emit('closeModal');
        },
        visiblechange(val) {
            if (val) {
                this.$nextTick(() => {
                    this.getLastHour();
                    this.getTrendData();
                });
            }else {
                this.topoShow = false
                this.$emit('closeModal');
            }
        },
        //获取当前最近一小时时间
        getLastHour() {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000);
            this.startDay = this.indexQuery.startTime = new Date(start).format('yyyy-MM-dd HH:mm:ss');
            this.endDay = this.indexQuery.endTime = new Date(end).format('yyyy-MM-dd HH:mm:ss');
        },
        startDayChange(val) {
            let _self = this;
            if (val === '') {
                _self.endDay = '';
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000);
                this.indexQuery.startTime = new Date(start).format('yyyy-MM-dd HH:mm:ss');
                this.indexQuery.endTime = new Date(end).format('yyyy-MM-dd HH:mm:ss');
                _self.endOptions.disabledDate = date => {
                    return date && date.valueOf() > Date.now();
                };
            } else {
                _self.startDay = _self.indexQuery.startTime = val;
                _self.endOptions.disabledDate = date => {
                    let endTime = val ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000 : '';
                    return (date && date.valueOf() > (endTime > Date.now() ? Date.now() : endTime)) || (date && date.valueOf() < new Date(val));
                };
            }
        },
        endDayChange(val) {
            let _self = this,
                endArray = val.split(' ');
            if (val === '') {
                _self.startDay = '';
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000);
                this.indexQuery.startTime = new Date(start).format('yyyy-MM-dd HH:mm:ss');
                this.indexQuery.endTime = new Date(end).format('yyyy-MM-dd HH:mm:ss');
                _self.endOptions.disabledDate = date => {
                    return date && date.valueOf() > Date.now();
                };
            } else {
                if (endArray[1] === '00:00:00') {
                    val = endArray[0] + ' 23:59:59';
                }
                _self.endDay = _self.indexQuery.endTime = val;
                _self.startOptions.disabledDate = date => {
                    let startTime = val ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000 : '';
                    return (date && date.valueOf() <= startTime) || (date && date.valueOf() > (Date.now() > new Date(val) ? new Date(val) : Date.now()));
                };
            }
        },
        //获取当前数据之前N月
        monthDate(val) {
            let start = new Date(),
                monthNumber = 2, //当前天数之前第几个月
                year = start.getFullYear(),
                month = start.getMonth() + 1,
                date = start.getDate(),
                newYear = '',
                newMonth = '',
                startDate = '',
                endDate = '';
            if (month - monthNumber < 1) {
                newYear = year - 1;
                newMonth = 12 - monthNumber + month;
            } else {
                newYear = year;
                newMonth = month - monthNumber;
            }
            startDate = newYear + '-' + newMonth + '-' + date + ' 00:00:00';
            endDate = year + '-' + month + '-' + date + ' 00:00:00';
            // if(new Date(val).getTime()<new Date(startDate).getTime()){
            //     if(this.$base.isShowPrompt()){
            //         this.$Message.error("请选择最近两个月内的时间段");
            //     }
            //    return []
            // }
            return [startDate, endDate];
        },
        indexQueryClick() {
            if(!this.startDay){
                this.$Message.error({ content: this.$t("phystopo_startTime_error_tip") });
                return ; 
            }
             if(!this.endDay){
                this.$Message.error({ content: this.$t("phystopo_endTime_error_tip") });
                return ; 
            }

            let startVal = moment(this.indexQuery.startTime,"YYYY-MM-DD hh:mm:ss").valueOf();
            let endVal = moment(this.indexQuery.endTime,"YYYY-MM-DD hh:mm:ss").valueOf();
            if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
                this.$Message.warning(this.$t('warning_time_not_exceed_62'));
                return;
            }
            this.threndQuery();
        },
        threndQuery() {
            let date = this.monthDate(this.indexQuery.startTime);
            this.buttonVal = 1;
            this.delayStart = 0;
            this.delayEnd = 100;
            if (date.length > 0) {
                this.echartParam.beginTime = this.indexQuery.startTime;
                this.echartParam.endTime = this.indexQuery.endTime;
                this.getTrendData();
            }
        },
        /*获取趋势图数据*/
        getTrendData() {
            this.delayLossData.delay = [];
            this.delayLossData.loss = [];
            this.flowData.enter = [];
            this.flowData.issue = [];
            this.flowData.outBandwithRates = [];
            this.flowData.intoBandwithRates = [];
            let param = {
                ...this.echartParam,
                phyLinkId: this.deailData.id,
                beginTime: this.startDay,
                endTime: this.endDay
            };
            this.loading = true;
            this.$http.wisdomPost('/rppath/getTrendListByLinkId', param).then(res => {
                //时延丢包趋势数据
                let data = res.data;
                if (res.code === 1 && data!=null) {
                    this.startValue = data.delays[0]?.[0] || data.lossRates[0]?.[0] || data.flowIntos[0]?.[0] || data.flowOuts[0]?.[0] || data.outBandwithRates[0]?.[0] || data.intoBandwithRates[0]?.[0];
                    this.endValue = data.delays[data.delays.length - 1]?.[0] || data.lossRates[data.lossRates.length - 1]?.[0] || data.flowIntos[data.flowIntos.length - 1]?.[0] || data.flowOuts[data.flowOuts.length - 1]?.[0] || data.outBandwithRates[data.outBandwithRates.length - 1]?.[0] || data.intoBandwithRates[data.intoBandwithRates.length - 1]?.[0];
                    console.log('this.startValue', this.startValue);
                    console.log('this.endValue', this.endValue);
                    this.delayLossData.delay = data.delays;
                    this.delayLossData.loss = data.lossRates;
                    this.flowData.enter = data.flowIntos;
                    this.flowData.issue = data.flowOuts;
                    this.flowData.outBandwithRates = data.outBandwithRates;
                    this.flowData.intoBandwithRates = data.intoBandwithRates;
                    this.delayLossLevel = data.level
                    this.loading = false;
                    if (this.delayLossData.delay.length == 0 && this.delayLossData.loss.length == 0 && this.flowData.intoBandwithRates.length == 0 && this.flowData.outBandwithRates.length == 0 && this.flowData.enter.length == 0 && this.flowData.issue.length == 0) {
                        debugger
                        this.topoShow = true;
                    }else {
                        this.topoShow = false
                    }

                    this.$store.commit("updateDelayLossHistory", {
                        level: res.data?.level || '',
                        datas: [this.delayLossData.delay, this.delayLossData.loss]
                    }); 
                    //保存数据
                    this.$store.commit("setdelayLTlevel", res.data?.level || ''); //保存首次加载的顶层数据粒度级别

                    this.$store.commit("updateFlowHistory", {
                        level: res.data.level,
                        datas: [this.flowData.enter, this.flowData.issue]
                    });
                     //保存数据
                    this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
                    this.$store.commit("setflowUnit", {
                        level: res.data.level,
                        unit: this.flowUnit
                    }); //保存单位

                    this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);

                } else if (res.code != 1){
                    this.$Message.error(res.msg);
                }
                console.log(this.flowData);
            }) .finally(() => {
                this.loading = false; 
                this.$nextTick(() => {
                    this.initEchart();
                });            
             });
        },

        setDelayLossTooltip() {
            
            let _self = this;
            return eConfig.tip('axis', function (param) {
                _self.scale = param[0].data[0];
                var obj = {};
                param = param.reduce(function (item, next) {
                    obj[next.seriesIndex] ? '' : (obj[next.seriesIndex] = true && item.push(next));
                    return item;
                }, []);
                let delayTime = '',
                    delayTip = '',
                    flowTime = '',
                    flowTip = '',
                    rateTip = '';
                for (let i = 0, len = param.length; i < len; i++) {
                    if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
                        delayTime = param[i].data[0] + '<br />';
                        delayTip += '<span class="tooltip-round" style="background-color:' + param[i].color + '"></span>';
                        if (localStorage.getItem('locale') === 'en') {
                        delayTip += param[i].seriesName + '：' + (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] == -1 ? '--' : param[i].value[1]) + _self.delayLossUnitEn[param[i].seriesName] + '<br />';
                        }else{
                        delayTip += param[i].seriesName + '：' + (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] == -1 ? '--' : param[i].value[1]) + _self.delayLossUnit[param[i].seriesName] + '<br />';
                        }
                    }
                    if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
                        flowTime = param[i].data[0] + '<br />';
                        flowTip += '<span class="tooltip-round" style="background-color:' + param[i].color + '"></span>';
                        flowTip += param[i].seriesName + '：' + (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] == -1 ? '--' : _self.flowSizeOnlyKbps(param[i].value[1], true, true)) + '<br />';
                    }

                    if (param[i].seriesIndex === 4 || param[i].seriesIndex === 5) {
                        rateTip += '<span class="tooltip-round" style="background-color:' + param[i].color + '"></span>';
                        if (localStorage.getItem('locale') === 'en') {
                        rateTip += param[i].seriesName + '：' + (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] == -1 ? '--' : param[i].value[1]) + _self.delayLossUnitEn[param[i].seriesName] + '<br />';
                        }else{
                        rateTip += param[i].seriesName + '：' + (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] == -1 ? '--' : param[i].value[1]) + _self.delayLossUnit[param[i].seriesName] + '<br />';
                        }
                    }
                }
                return delayTime + delayTip + flowTime + flowTip + rateTip;
            });
        },
        Option() {
            let optionArr = [
                {
                    title: [
                        {
                            show: this.delayLossData.delay.length === 0 && this.delayLossData.loss.length === 0,
                            textStyle: {
                                color: top.window.isdarkSkin == 1 ? '#465b7a' : 'grey',
                                fontSize: 16
                            },
                            text: this.$t('alarm_no_data'),
                            left: 'center',
                            top: '90px'
                        },
                        {
                            show: this.flowData.intoBandwithRates.length === 0 && this.flowData.outBandwithRates.length === 0 && this.flowData.enter.length === 0 && this.flowData.issue.length === 0,
                            textStyle: {
                                color: top.window.isdarkSkin == 1 ? '#465b7a' : 'grey',
                                fontSize: 16
                            },
                            text: this.$t('alarm_no_data'),
                            left: 'center',
                            top: '370px'
                        }
                    ],
                    tooltip: this.setDelayLossTooltip(),
                    axisPointer: {
                        type: 'line',
                        link: {
                            xAxisIndex: [0, 1]
                        }
                    },
                    grid: [
                        {
                            left: '6%',
                            top: '40px',
                            width: '89%',
                            height: '140px'
                        },
                        {
                            // show:this.echart2.show,
                            left: '6%',
                            top: '300px',
                            width: '89%',
                            height: this.echart2.show ? '140px' : '0px'
                        }
                    ],
                    legend: [
                        {
                            show: true,
                            top: '0%',
                            right: '46%',
                            gridIndex: 0,
                            icon: 'roundRect',
                            itemWidth: 16,
                            itemHeight: 12,
                            textStyle: {
                                color: top.window.isdarkSkin == 1 ? '#FFFFFF' : '#484b56',
                                fontSize: 12,
                                fontFamily: 'MicrosoftYaHei-Bold'
                            },
                            color: this.delayLossColor,
                            data: [this.$t('dashboard_delay'), this.$t('comm_packet_loss')]
                        },
                        {
                            show: this.echart2.show,
                            top: '250px',
                            right: '41%',
                            icon: 'roundRect',
                            gridIndex: 1,
                            itemWidth: 16,
                            itemHeight: 12,
                            textStyle: {
                                color: top.window.isdarkSkin == 1 ? '#FFFFFF' : '#484b56',
                                fontSize: 12,
                                fontFamily: 'MicrosoftYaHei-Bold'
                            },
                            data: [this.$t('dashboard_inlet_velocity'), this.$t('dashboard_outflow_velocity'), this.$t('view_component_type_7'), this.$t('view_component_type_8')]
                        }
                    ],
                    xAxis: [
                        {
                            type: 'time',
                            gridIndex: 0,
                            splitLine: {
                                lineStyle: {
                                    type: 'dotted',
                                    color: top.window.isdarkSkin == 1 ? 'rgba(42, 56, 64, 1)' : '#e3e7f2'
                                }
                            },
                            axisLabel: {
                                textStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#0e2a5f'
                                }
                            },
                            axisLine: {
                                lineStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#676f82',
                                    width: 1
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            type: 'time',
                            gridIndex: 1,
                            splitLine: {
                                lineStyle: {
                                    type: 'dotted',
                                    color: top.window.isdarkSkin == 1 ? 'rgba(42, 56, 64, 1)' : '#e3e7f2'
                                }
                            },
                            axisLabel: {
                                textStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#0e2a5f'
                                }
                            },
                            axisLine: {
                                lineStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#676f82',
                                    width: 1
                                }
                            }
                        }
                    ],
                    yAxis: [
                        {
                            name: this.$t('comm_time_delay')+'（ms）',
                            type: 'value',
                            scale: true,
                            gridIndex: 0,
                            position: 'left',
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dotted',
                                    color: top.window.isdarkSkin == 1 ? '#2A3840' : '#e3e7f2'
                                }
                            }
                        },
                        {
                            name: this.$t('comm_packet_loss')+'（%）',
                            type: 'value',
                            scale: true,
                            gridIndex: 0,
                            position: 'right',
                            min:0,
                            max:100,
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dotted',
                                    color: top.window.isdarkSkin == 1 ? '#2A3840' : '#e3e7f2'
                                }
                            }
                        },
                        // 流速
                        {
                            show: this.echart2.show,
                            name: this.$t('specquality_velocity_flow'),
                            type: 'value',
                            scale: true,
                            position: 'left',
                            gridIndex: 1,
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dotted',
                                    color: top.window.isdarkSkin == 1 ? '#2A3840' : '#e3e7f2'
                                }
                            },
                            axisLabel: {
                                show: true,
                                interval: 'auto',
                                // formatter: '{value}.00000'
                                formatter: value => {
                                    // this.flowSize(value, false, true)
                                    // let val = value/1000
                                    return this.flowSizeOnlyKbps(value, true, true);
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('comm_port_utilization')+'（%）',
                            type: 'value',
                            scale: true,
                            position: 'right',
                            gridIndex: 1,
                            axisTick: {
                                show: false
                            },
                            min:0,
                            max:100,
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: top.window.isdarkSkin == 1 ? '#5CA0D5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dotted',
                                    color: top.window.isdarkSkin == 1 ? '#2A3840' : '#e3e7f2'
                                }
                            }
                            // axisLabel: {
                            //   show: true,
                            //   formatter: value => {
                            //     return this.getUnit(value, true, true);
                            //   }
                            // }
                        }
                    ],
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: [0, 1],
                            start: 0,
                            end: 100,
                            startValue: this.startValue==this.endValue?'':this.startValue,
                            endValue: this.startValue==this.endValue?'':this.endValue
                            // start:this.delayStart,
                            // end:this.delayEnd
                        },
                        {
                            type: 'slider',
                            left: '6%',
                            right: '5%',
                            height: 25,
                            top: this.echart2.show ? '490px' : '250px',
                            xAxisIndex: [0, 1],
                            realtime: true,
                            start: 0,
                            end: 100,
                            startValue: this.startValue==this.endValue?'':this.startValue,
                            endValue: this.startValue==this.endValue?'':this.endValue,
                            fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , //'rgba(2, 29, 54, 1)',
                            borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , //'rgba(22, 67, 107, 1)',
                            handleStyle: {
                                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , //'rgba(2, 67, 107, 1)'
                            },
                            textStyle: {
                                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , //top.window.isdarkSkin == 1 ? '#617ca5' : '#676f82'
                            }
                        }
                    ],
                    series: [
                        {
                            type: 'line',
                            name: this.$t('dashboard_delay'),
                            xAxisIndex: 0,
                            yAxisIndex: 0,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 1,
                            // symbol: this.delayLossData.delay.length>1?"none":"circle",
                            // seriesLayoutBy:'row',
                            color: this.delayLossColor[0],
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(0, 255, 238, 0.60)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(0, 255, 238, 0.2)'
                                        }
                                    ]
                                }
                            },
                            data: this.delayLossData.delay,
                            markLine: {
                                data: [
                                    {
                                        symbol: 'image://' + pointRed,
                                        symbolSize: 10,
                                        xAxis: this.markPoint,
                                        symbolRotate: '0',
                                        // symbolOffset:[0,'70px'],
                                        lineStyle: {
                                            color: 'rgba(0,0,0,0)',
                                            width: 0,
                                            opacity: 1
                                        },
                                        label: {
                                            show: true,
                                            position: 'start',
                                            color: 'red',
                                            fontSize: 10,
                                            formatter: () => {
                                                return this.markPoint;
                                            },
                                            backgroundColor: top.window.isdarkSkin == 1 ? '#617ca5' : '#fff'
                                            // padding: [0]
                                        }
                                    },
                                    {
                                        symbol: 'image://' + pointGreen,
                                        symbolSize: 10,
                                        xAxis: this.markPointGreen,
                                        symbolRotate: '0',
                                        // symbolOffset:[0,'70px'],
                                        lineStyle: {
                                            color: 'rgba(0,0,0,0)',
                                            width: 0,
                                            opacity: 1
                                        },
                                        label: {
                                            show: true,
                                            position: 'start',
                                            color: '#33cc33',
                                            fontSize: 10,
                                            backgroundColor: '#fff',
                                            formatter: () => {
                                                return this.markPointGreen;
                                            }
                                            // padding: [0]
                                        }
                                    }
                                ],
                                emphasis: {
                                    lineStyle: {
                                        color: 'red',
                                        width: 0,
                                        opacity: 1
                                    }
                                }
                            }
                        },
                        {
                            type: 'line',
                            name: this.$t('comm_packet_loss'),
                            xAxisIndex: 0,
                            yAxisIndex: 1,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 1,
                            // symbol: this.delayLossData.loss.length>1?"none":"circle",
                            color: this.delayLossColor[1],
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(2,144,253, 0.8)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(2,144,253, 0.2)'
                                        }
                                    ]
                                }
                            },
                            data: this.delayLossData.loss
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('dashboard_inlet_velocity'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 1,
                            xAxisIndex: 1,
                            yAxisIndex: 2, // 指定y轴
                            color: this.flowColor[0],
                            data: this.flowData.enter,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(71,142,233, 0.8)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(71,142,233, 0.2)'
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('dashboard_outflow_velocity'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 1,
                            xAxisIndex: 1,
                            yAxisIndex: 2,
                            color: this.flowColor[1],
                            data: this.flowData.issue,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(155,251,174, 0.8)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(155,251,174, 0.2)'
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('view_component_type_7'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 1,
                            xAxisIndex: 1,
                            yAxisIndex: 3,
                            color: this.flowColor[2],
                            data: this.flowData.intoBandwithRates,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(0,255,255, 0.8)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(0,255,255, 0.2)'
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('view_component_type_8'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 1,
                            xAxisIndex: 1,
                            yAxisIndex: 3,
                            color: this.flowColor[3],
                            data: this.flowData.outBandwithRates,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(22,144,253, 0.8)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(2,144,253, 0.2)'
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            ];
            return optionArr[0];
        },
        initEchart() {
            // let this = this
            if (this.indexChart) {
                this.indexChart.clear();
            }
            this.indexChart = echarts.init(this.$refs['indexChart']);
            this.indexChart.setOption(this.Option(), true);
            this.indexChart.getZr().on("mousewheel", (params) => {
                console.log('this.delayLossLevel',this.delayLossLevel);
                console.log('this.delayLossLevel',this);
                let getTlevel = this.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
                let getflowTlevel = this.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
                let getgoodTlevel = this.$store.state.goodRateTlevel; //获取delay保存的顶层粒度级别
                let getSaveData = this.$store.state.delayLossHistory; //获取保存的数据
                let getflowSaveData = this.$store.state.flowHistory; //获取保存的数据
                let getflowSaveUnit = this.$store.state.flowUnit; //获取流速单位数据
                let getgoodSaveData = this.$store.state.goodRateHistory; //获取保存的数据

                var pointInPixel = [params.offsetX, params.offsetY];
                if (
                    this.indexChart.containPixel(
                        { gridIndex: [0, 1, 2] },
                        pointInPixel
                    )
                ) {
                let startValue =
                    this.indexChart.getModel().option.dataZoom[0].startValue;
                let endValue =
                    this.indexChart.getModel().option.dataZoom[0].endValue;
                let startDay = new Date(startValue).format2(
                    "yyyy-MM-dd HH:mm:ss"
                );
                let endDay = new Date(endValue).format2(
                    "yyyy-MM-dd HH:mm:ss"
                );
                this.setPs(this.delayLossLevel, [
                    this.timeChange(startValue),
                    this.timeChange(endValue),
                ]);
                // debugger
                let differ = endValue - startValue;
                let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
                let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
                let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
                var levelNum = "";
                let start = this.indexChart.getModel().option.dataZoom[0].start;
                let end = this.indexChart.getModel().option.dataZoom[0].end;


                if (params.wheelDelta >= 0 ) {
                    console.log("放大");
                    if (this.delayLossLevel == 1) {
                        // 小时粒度
                        if (differ < minute) {
                            if (!this.startScale) {
                            this.startScale = true;
                            levelNum = 2;
                            let delayParam = Object.assign(this.echartParam, {
                                level: levelNum,
                            });
                            let flowParam = Object.assign(delayParam, {
                                snmp: this.dataSource == 1 ? true : false,
                            });
                            this.getDelayLoss(delayParam, startDay,endDay,flowParam);
                            }
                        }
                    } else if (this.delayLossLevel == 2) {
                    // 分钟粒度
                    if (differ < original) {
                        if (!this.startScale) {
                        this.startScale = true;
                        if (this.High) {
                            levelNum = 4;
                            let delayParam = Object.assign(this.echartParam, {
                            level: levelNum,
                            });
                            let flowParam = Object.assign(delayParam, {
                            snmp: this.dataSource == 1 ? true : false,
                            });
                            this.getDelayLoss(delayParam, startDay,endDay,flowParam);
                        }
                        }
                    }
                    }
                } else {
                    console.log("缩小--");
                    // debugger
                    if (start == 0 && end == 100) {
                        //是否处在缩放过程中
                        if (this.delayLossLevel == getTlevel) {
                        this.setPs(this.delayLossLevel, [
                            this.timeChange(startValue),
                            this.timeChange(endValue),
                        ]);
                        this.startScale = false;
                        } else {
                        let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                        if (this.flowLevel == getflowTlevel) {
                            this.startScale = false;
                        } else {
                            if (this.flowLevel == 2) {
                            this.flowLevel = 1;
                            this.startScale = true;
                            this.flowUnit = getflowSaveUnit.HoursUnit;
                            this.flowData.enter = getflowSaveData.HoursData.enter;
                            this.flowData.issue = getflowSaveData.HoursData.issue;
                            } else if (this.flowLevel == 3 || this.flowLevel == 4) {
                            this.flowLevel = 2;
                            this.startScale = true;
                            this.flowUnit = getflowSaveUnit.minuteUnit;
                            this.flowData.enter = getflowSaveData.minuteData.enter;
                            this.flowData.issue = getflowSaveData.minuteData.issue;
                            }
                        }
                        if (this.goodRateLevel == getgoodTlevel) {
                            this.startScale = false;
                        } else {
                            if (this.goodRateLevel == 2) {
                            this.goodRateLevel = 1;
                            this.startScale = true;
                            this.goodRateData.nrUseRateList =
                                getgoodSaveData.HoursUnit.nrUseRateList;
                            this.goodRateData.nrGoodRateList =
                                getgoodSaveData.HoursUnit.nrGoodRateList;
                            this.goodRateData.useRateList =
                                getgoodSaveData.HoursUnit.useRateList;
                            this.goodRateData.goodRateList =
                                getgoodSaveData.HoursUnit.goodRateList;
                            } else if (
                                this.delayLossLevel == 3 ||
                                this.delayLossLevel == 4
                            ) {
                            this.delayLossLevel = 2;
                            this.startScale = true;
                            this.goodRateData.nrUseRateList =
                                getgoodSaveData.minuteUnit.nrUseRateList;
                            this.goodRateData.nrGoodRateList =
                                getgoodSaveData.minuteUnit.nrGoodRateList;
                            this.goodRateData.useRateList =
                                getgoodSaveData.minuteUnit.useRateList;
                            this.goodRateData.goodRateList =
                                getgoodSaveData.minuteUnit.goodRateList;
                            }
                        }

                        if (this.delayLossLevel == getTlevel) {
                            this.startScale = false;
                        } else if (this.delayLossLevel == 2) {
                            this.delayLossLevel = 1;
                            this.startScale = true;
                            this.delayLossData.delay = getSaveData.HoursData.delay;
                            this.delayLossData.loss = getSaveData.HoursData.loss;
                            this.delayStart = getSite.psH.start;
                            this.startValue = getSite.psH.start;
                            this.delayEnd = getSite.psH.end;
                            this.endValue = getSite.psH.end;
                        } else if (
                            this.delayLossLevel == 3 ||
                            this.delayLossLevel == 4
                        ) {
                            this.delayLossLevel = 2;
                            this.startScale = true;
                            this.delayLossData.delay = getSaveData.minuteData.delay;
                            this.delayLossData.loss = getSaveData.minuteData.loss;
                            this.delayStart = getSite.psM.start;
                            this.startValue = getSite.psM.start;
                            this.delayEnd = getSite.psM.end;
                            this.endValue = getSite.psM.end;
                        }

                            setTimeout(() => {
                                this.startScale = false;
                                this.initEchart();
                            }, 300);
                        }
                    }
                }
                }
             });
   
        },
        // 切换颗粒度加载趋势图
        async getDelayLoss( delayParam,startDay,endDay,) {
            let param = {
                ...delayParam,
                phyLinkId: this.deailData.id,
                beginTime: startDay,
                endTime: endDay
            };
            let isUpdate = false;
            if (delayParam.level != 99) {
                this.delayLossData.delay = [];
                this.delayLossData.loss = [];
                this.flowData.enter = [];
                this.flowData.issue = [];
                this.flowData.outBandwithRates = [];
                this.flowData.intoBandwithRates = [];
                isUpdate = true;
                this.loading = true;
                await this.$http
                    .wisdomPost("/rppath/getTrendListByLinkId", param)
                    .then((res) => {
                        //时延丢包趋势数据
                        let data = res.data
                        if (res.code === 1 && data!=null) {
                            this.startValue = data.delays[0]?.[0] || data.lossRates[0]?.[0] || data.flowIntos[0]?.[0] || data.flowOuts[0]?.[0] || data.outBandwithRates[0]?.[0] || data.intoBandwithRates[0]?.[0];
                            this.endValue = data.delays[data.delays.length - 1]?.[0] || data.lossRates[data.lossRates.length - 1]?.[0] || data.flowIntos[data.flowIntos.length - 1]?.[0] || data.flowOuts[data.flowOuts.length - 1]?.[0] || data.outBandwithRates[data.outBandwithRates.length - 1]?.[0] || data.intoBandwithRates[data.intoBandwithRates.length - 1]?.[0];
                            this.delayLossData.delay = data.delays;
                            this.delayLossData.loss = data.lossRates;
                            this.flowData.enter = data.flowIntos;
                            this.flowData.issue = data.flowOuts;
                            this.flowData.outBandwithRates = data.outBandwithRates;
                            this.flowData.intoBandwithRates = data.intoBandwithRates;
                            this.delayLossLevel = data.level
                            this.loading = false;
                            if (this.delayLossData.delay.length == 0 && this.delayLossData.loss.length == 0 && this.flowData.intoBandwithRates.length == 0 && this.flowData.outBandwithRates.length == 0 && this.flowData.enter.length == 0 && this.flowData.issue.length == 0) {
                                debugger
                                this.topoShow = true;
                            }else {
                                this.topoShow = true;
                            }

                            this.$store.commit("updateDelayLossHistory", {
                                level: res.data?.level || '',
                                datas: [this.delayLossData.delay, this.delayLossData.loss]
                            }); 

                            this.$store.commit("updateFlowHistory", {
                                level: res.data.level,
                                datas: [this.flowData.enter, this.flowData.issue]
                            });
                            //保存数据
                            this.$store.commit("setflowUnit", {
                                level: res.data.level,
                                unit: this.flowUnit
                            }); //保存单位

                            this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
                        }
                    });
            }
            this.startScale = false;
            this.loading = false;
            if (isUpdate) {
                this.initEchart();
            }
        },
        //时间推算
        setTime(level, time) {
            let Interval = 0,
                start = new Date(this.echartParam.startTime).getTime(),
                end = new Date(this.echartParam.endTime).getTime(),
                newStart = 0,
                newEnd = 0;
            if (level == 0) {
                Interval = 15 * 24 * 60 * 60 * 1000;
            } else if (level == 1) {
                Interval = 12 * 60 * 60 * 1000;
            } else if (level == 2) {
                Interval = 30 * 60 * 1000;
            }
            newStart = time - Interval;
            newEnd = time + Interval;

            if (newStart < start) {
                newStart = start;
            }
            if (newEnd > end) {
                newEnd = end;
            }
            newStart = this.timeChange(newStart);
            newEnd = this.timeChange(newEnd);
            return [newStart, newEnd];
        },
        //记录滑块的位置
        setPs(level, position) {
            //记录滚动条的位置
            if (level == 0) {
                this.delayPs.psD.start = position[0];
                this.delayPs.psD.end = position[1];
                sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
            } else if (level == 1) {
                this.delayPs.psH.start = position[0];
                this.delayPs.psH.end = position[1];
                sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
            } else if (level == 2) {
                this.delayPs.psM.start = position[0];
                this.delayPs.psM.end = position[1];
                sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
            }
        }
    },
     mounted() {
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
    beforeDestroy() {
         // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
        if (!this.indexChart) {
            return;
        }
        this.indexChart.clear();
        this.indexChart.dispose();
        this.indexChart = null;
    }
};
</script>
<style>
</style>
