<template>
  <!-- 物理拓扑图  -->
  <div class="phytopo" id="phytopo"></div>
</template>

<script>
import G6 from '@antv/g6';

import ipv6Format from "@/common/ipv6Format";
export default {
    props: {
        msgModal: {
            type: Array,
             default: () => [this.$t('phytopo_device_ip'), this.$t('phytopo_device_Name'), this.$t('comm_line_flow'), this.$t('comm_line_delay'), this.$t('comm_line_packet_loss_rate')],
        },
        tabNlist: {
          type: Array,
          default: () => []
        },
        checkBoxSelect: {
          type:Boolean,
          default: false
        },
        // 1物理拓扑，3仪表盘物理拓扑
        pathTopoCoorType: {
          type: Number,
          default: 1
        }

    },
    watch: {
      checkBoxSelect(val) {
        this.graph.setMode(val ? 'custom' : 'default');
      },
      msgModal: {
        handler(val) {
          this.showTypeChange()
        },
        deep: true
      },
     
  currentSkin: {
    handler(val) {
      document.documentElement.setAttribute('data-theme', val == 1 ? 'dark' : 'light');
    },
    immediate: true
  }

},
    
    data() {

        return {
          linkCount: {},
          currentSkin: sessionStorage.getItem('dark') || 1,
          viewCenter:{},
          isBigScreen:false,
            topoData:{},
            graph:null,
            phytopoData: {
                nodes: [],
             edges: [],
             combos:[]

            },
            timerId:null,
            selectedNodes:[],
            selectedCombos:[],
            widthRatio:0,
            selectionBox: null,  // 添加用于存储选择框的属性
            dragStartPos: null,  // 添加用于存储拖动起始位置的属性
            circleFormat:[
              [{idx:4,curveOffset:0.001}],
              [{idx:4,curveOffset:-10},{idx:4,curveOffset:10}],
              [{idx:4,curveOffset:0.001},{idx:4,curveOffset:-30},{idx:4,curveOffset:30}],
              [{idx:4,curveOffset:-15},{idx:4,curveOffset:15},{idx:4,curveOffset:-40},{idx:4,curveOffset:40}],
              [{idx:4,curveOffset:0.001},{idx:4,curveOffset:20},{idx:4,curveOffset:-20},{idx:4,curveOffset:40},{idx:4,curveOffset:-40}],
              [{idx:4,curveOffset:10},{idx:4,curveOffset:-10},{idx:4,curveOffset:30},{idx:4,curveOffset:-30},{idx:4,curveOffset:50},{idx:4,curveOffset:-50}],
              [{idx:4,curveOffset:0.001},{idx:4,curveOffset:-20},{idx:4,curveOffset:20},{idx:4,curveOffset:-40},{idx:4,curveOffset:40},{idx:4,curveOffset:-55},{idx:4,curveOffset:55}],
              [{idx:4,curveOffset:-8},{idx:4,curveOffset:8},{idx:4,curveOffset:-25},{idx:4,curveOffset:25},{idx:4,curveOffset:-45},{idx:4,curveOffset:45},{idx:4,curveOffset:-60},{idx:4,curveOffset:60}]
          ]
        }

    },
    methods: {
        // 初始化G6
        initG6() {
          this.registerEdges()
          console.log(this.topoData,'canvas尺寸')
            let obj = {

                container: 'phytopo',
                width: this.topoData.with,
                height: this.topoData.height,
                 renderer: 'canvas', // 使用 canvas 渲染
                  minZoom: 0.2,      // 最小缩放比例
                  maxZoom: 5,        // 最大缩放比例
                   // 添加防抖配置
                enableDragCanvas: true,
                enableOptimize: true,  // 全局开启优化
                pixelRatio: window.devicePixelRatio, // 设置像素比
                // 添加动画配置
                animate: false, // 关闭默认动画
                 buffered: true,
        bufferedContexts: ['node', 'edge'],
         // 添加缩放配置
        wheelSensitivity: 0.2, // 降低滚轮灵敏度
        optimizeZoom: true,    // 优化缩放性能
                animateCfg: {
                    duration: 0,  // 动画时长设为0
                    easing: 'linearEasing'
                },
                modes: {
                    default: [
                       {
                    type: 'drag-canvas',
                    direction: 'both',
                    enableOptimize: false,  // 开启拖拽优化
                },
                {
                    type: 'zoom-canvas',
                    enableOptimize: false,  // 开启缩放优化
                    sensitivity: 1.5
                },

                        'drag-combo',
                        {
                             type: 'drag-node',
                            enableDelegate: false,
                            shouldBegin: (e) => {
                                // 如果节点属于combo，则不允许拖拽
                                const node = e.item;
                                return !node.getModel().comboId;
                    }

                        }
                    ],
                     custom:[ {
                             type: 'drag-node',
                            enableDelegate: false,
                            shouldUpdate: () => true,
                            hideSelectedNodes: false, // 防止节点隐藏
                            shouldBegin: (e) => {
                const node = e.item;
                const model = node.getModel();
                // 如果节点属于combo，获取父combo并开始拖拽整个combo
                if (model.comboId) {
                    const parentCombo = e.target.get('graph').findById(model.comboId);
                    if (parentCombo) {
                        // 触发combo的拖拽
                        const comboKeyShape = parentCombo.get('keyShape');
                        const graph = e.target.get('graph');
                        graph.emit('combo:dragstart', { item: parentCombo, target: comboKeyShape });
                    }
                    return false; // 阻止节点拖拽
                }
                return true; // 允许非combo内节点拖拽
            }

                        },'zoom-canvas', 'drag-combo',
             {
              type:'brush-select',
              brushStyle: {
              fill: '#16436B',
              fillOpacity: 0.3,
              // stroke: '#666',
              // lineWidth: 0.5,
              },
              onSelect: (nodes,combos) => {
                 // 清除之前的选中状态
        this.selectedNodes.forEach(node => {
            this.graph.setItemState(node, 'selected', false);
        });
        this.selectedCombos.forEach(combo => {
            this.graph.setItemState(combo, 'selected', false);
        });
        

             this.selectedNodes = nodes;
        this.selectedCombos = combos;
         // 设置新的选中状态
        nodes.forEach(node => {
            this.graph.setItemState(node, 'selected', true);
        });
        combos.forEach(combo => {
            this.graph.setItemState(combo, 'selected', true);
        });

               if (nodes.length > 0 || combos.length > 0) {
          this.createSelectionBox(nodes, combos);
        }

              console.log('Selected nodes:', this.selectedCombos,this.selectedNodes);

              // 在这里处理选中的节点
            },
            onDeselect: (nodes) => {
               // 清除选中状态
        this.selectedNodes.forEach(node => {
            this.graph.setItemState(node, 'selected', false);
        });
        this.selectedCombos.forEach(combo => {
            this.graph.setItemState(combo, 'selected', false);
        });
               this.selectedNodes = [];
                this.selectedCombos = [];
                this.removeSelectionBox();
              // 在这里处理取消选中的节点
            },
            selectedState: 'selected',
            includeEdges: false,

            trigger: 'drag',
              autoPaint: true,
            dragOnCanvas: true,
             hideSelectedNodes: false,
            // 添加清理方法
            shouldUpdate: () => {
                return true;
            },
            shouldEnd: () => {
                return true;
            }


             }]
            },
            defaultCombo: {
                type: 'rect',
                style: {
                    fill: 'rgba(1,27,45,0.7)',
                    stroke: '#015197',
                    // opacity: 0.6,

                    radius: [4, 4],
                    lineDash: [2, 2]
                },
                labelCfg: {
                    position: 'top',
                    style: {
                        fill: '#02B8FD',
                        fontSize: 14,
                    }
                }
            },
            defaultEdge: {
            type: 'new-line'  // 使用自定义边类型
          },
            defaultNode: {
                type: 'image',
                // 添加节点裁剪配置
                clipCfg: {
                    show: true,
                    type: 'circle'
                }
            } ,
             plugins: [this.createNoteTooltip() ],




            }

            this.graph = new G6.Graph(obj);
            // 事件监听
            this.addEventListenerGraph()
            // 读取数据
            this.graph.data(this.phytopoData);
             var width = this.graph.get('width');
            var height = this.graph.get('height');
            // 找到视口中心
            this.viewCenter = {
              x: width / 2,
              y: height / 2
            };
            // 渲染图
            this.graph.render();
             this.graph.setMode(this.checkBoxSelect ? 'custom' : 'default');
            // 初步渲染还原画布位置
             let zoomXMultiple = this.topoData.pathTopologyLocXLocYMinMax?.zoomXMultiple || 1
            let coordinateX = this.topoData.pathTopologyLocXLocYMinMax?.defaultLocX || 0
            let coordinateY = this.topoData.pathTopologyLocXLocYMinMax?.defaultLocY || 0
            console.log(zoomXMultiple,coordinateX,coordinateY,123)

              this.graph.zoom(zoomXMultiple,{x:0,y:0})
              this.graph.translate(coordinateX,coordinateY)
              this.graph.refresh();


              // 处理背景图片
              let backgroundImag = this.graph.getGroup().addShape('image',{
              attrs:{
                // width: imageSize[0],
                // height: imageSize[1],
                width: 1920,
                height: 1920*this.widthRatio,
                img: this.topoData.backgroundImage,
              },
              capture:false
            });
            backgroundImag.toBack();



        },
         addEventListenerGraph() {
          this.graph.on('combo:click', (evt) => {
            const combo = evt.item;
            const model = combo.getModel();

            console.log('Clicked combo:', model);
            this.$emit('combo-click',model.id)



          })
          // 节点点击事件
          this.graph.on('node:click', (evt) => {
            if(this.isBigScreen) {
              return
            }
            const node = evt.item;
            const model = node.getModel();
            console.log('Clicked node:', model);
            // 区分备注节点和普通节点
            if(model.id && model.id.includes('note')) {
              // this.$emit('saveNoteInfo',model.label,model.x,model.y)
              return
            }
            this.$emit('node-click', model.data)
          });
     // 修改 node:drag 事件处理
     this.graph.on('node:drag', (evt) => {
                  if (this.checkBoxSelect && (this.selectedNodes.length > 0 || this.selectedCombos.length > 0)) {
                      const { deltaX, deltaY } = evt;
                      
                      // 创建一个 Map 来存储需要更新的 combos
                      const comboUpdates = new Map();
                      
                      // 更新选中节点的位置
                      this.selectedNodes.forEach(node => {
                          const model = node.getModel();
                          const newX = model.x + deltaX;
                          const newY = model.y + deltaY;
                          
                          this.graph.updateItem(node, {
                              x: newX,
                              y: newY
                          });
                          
                          // 如果节点属于某个 combo，记录该 combo
                          if (model.comboId) {
                              if (!comboUpdates.has(model.comboId)) {
                                  const parentCombo = this.graph.findById(model.comboId);
                                  if (parentCombo) {
                                      comboUpdates.set(model.comboId, parentCombo);
                                  }
                              }
                          }
                      });
                      
                      // 更新选中的 combos 位置
                      this.selectedCombos.forEach(combo => {
                          const model = combo.getModel();
                          const newX = model.x + deltaX;
                          const newY = model.y + deltaY;
                          
                          // 更新 combo 位置
                          this.graph.updateItem(combo, {
                              x: newX,
                              y: newY
                          });
                          
                          // 更新该 combo 内的所有节点
                          const comboNodes = this.graph.getNodes().filter(node => 
                              node.getModel().comboId === model.id
                          );
                          
                          comboNodes.forEach(node => {
                              if (!this.selectedNodes.includes(node)) { // 避免重复更新已选中的节点
                                  const nodeModel = node.getModel();
                                  this.graph.updateItem(node, {
                                      x: nodeModel.x + deltaX,
                                      y: nodeModel.y + deltaY
                                  });
                              }
                          });
                          
                          // 将当前 combo 添加到更新列表
                          comboUpdates.set(model.id, combo);
                      });
                      
                      // 批量更新所有受影响的 combos
                      comboUpdates.forEach(combo => {
                          this.graph.updateCombo(combo);
                      });
                      
                      // 更新选择框
                      if (this.selectionBox) {
                          this.createSelectionBox(this.selectedNodes, this.selectedCombos);
                      }
                      
                      // 使用 requestAnimationFrame 优化渲染
                      requestAnimationFrame(() => {
                          this.graph.refresh();
                      });
                  }
              });
              this.graph.on('node:dragend', (evt) => {
             let arr = []

            if(this.checkBoxSelect) {
              // debugger
              console.log(this.selectedNodes,'拖动的节点')
               this.selectedNodes.forEach(item => {
         console.log(item.getModel(),'拖动的节点')
         let data = item.getModel()
        if (!data.comboId) {
        arr.push({
          pathTopologyId: this.topoData.topoId,
          nodeId: data.id,
          nodeX: data.x,
          nodeY: data.y,
          zoomXMultiple: this.graph.getZoom(),
          zoomYMultiple: this.graph.getZoom(),
          coordinateX: this.graph.getCanvasByPoint(0, 0).x,
          coordinateY: this.graph.getCanvasByPoint(0, 0).y,
          coordinateWidth: this.graph.getWidth(),
          coordinateHeight: this.graph.getHeight(),
          pathTopoCoorType: 1,
        });
        this.selectedNodes.forEach(node => {
            this.graph.setItemState(node, 'selected', false);
        });
        this.selectedCombos.forEach(combo => {
            this.graph.setItemState(combo, 'selected', false);
        });
        this.selectedNodes = [];
        this.selectedCombos = [];
        this.removeSelectionBox();
      }else {

        const combo = this.graph.findById(data.comboId);
  const comboModel = combo.getModel();
  
  arr.push({
    childTopoId: comboModel.id,
    nodeX: comboModel.x,
    nodeY: comboModel.y,
    pathTopologyId: this.topoData.topoId,
    pathTopoCoorType: 1,
  });
        
      }

        })
         // 处理选中的combos
    this.selectedCombos.forEach(combo => {
      const model = combo.getModel();
      arr.push({
        childTopoId: model.id,
        nodeX: model.x,
        nodeY: model.y,
        pathTopologyId: this.topoData.topoId,
        pathTopoCoorType: 1,
      });
    });
     // 更新选择框位置
      if (this.selectionBox) {
        this.createSelectionBox(this.selectedNodes, this.selectedCombos);
      }




            }else {
               const node = evt.item;
            const model = node.getModel();
            // 区分备注节点和普通节点

            if(model.id && model.id.includes('note')) {
              this.$emit('saveNoteInfo',model.label,model.x,model.y)
              return
            }

              let obj = {
              pathTopologyId: this.topoData.topoId,
              nodeId: model.id,
              nodeX: model.x,
            nodeY: model.y,
            zoomXMultiple: this.graph.getZoom(),
            zoomYMultiple: this.graph.getZoom(),
            coordinateX: this.graph.getCanvasByPoint(0, 0).x,
            coordinateY:this.graph.getCanvasByPoint(0, 0).y,
            coordinateWidth: this.graph.getWidth(),
            coordinateHeight: this.graph.getHeight(),
            pathTopoCoorType: 1,

            }
               arr.push(obj)

            }





            this.$emit('node-dragend', arr);

            });
            // combo拖拽事件
          this.graph.on('combo:dragend', (evt) => {
            console.log(evt,'combo拖拽事件')
            const combo = evt.item;
            const model = combo.getModel();
            console.log(model,'combo拖拽事件')
             const nodes = this.graph.findAll('node', (node) => node.getModel().comboId === model.id);
            let arr = []
            let locX = model.x - model.size[0]/2
            let locY = model.y - model.size[1]/2
            if(model.data.nodeList.length == 0) {
              locX = model.x
              locY = model.y
            }
            let obj = {
              childTopoId: model.id,
              nodeX: locX,
              nodeY: locY,
              // topId: this.topoData.topId,
              pathTopologyId: this.topoData.topoId,
              pathTopoCoorType: 1,

            }
            arr.push(obj)

            this.$emit('node-dragend', arr);

            console.log('End dragging combo:', model);
          });


       
       // 画布缩放
          this.graph.on('wheelzoom', (evt) => {
              // 取消之前的定时器
              if (this.timerId) {
                  clearTimeout(this.timerId);
                  this.timerId = null;
              }

              // 禁用局部刷新
              // this.graph.get('canvas').set('localRefresh', false);

              // 强制刷新画布
              // requestAnimationFrame(() => {
                  // this.graph.refresh();
              // });

                  this.timerId = setTimeout(() => {
                    // 恢复局部刷新
                  this.graph.get('canvas').set('localRefresh', true);
                      this.$emit('node-dragend', [{
                          pathTopologyId: this.topoData.topoId,
                          coordinateX: this.graph.getCanvasByPoint(0, 0).x,
                          coordinateY: this.graph.getCanvasByPoint(0, 0).y,
                          coordinateWidth: this.graph.getWidth(),
                          coordinateHeight: this.graph.getHeight(),
                          zoomXMultiple: this.graph.getZoom(),
                          zoomYMultiple: this.graph.getZoom(),
                          pathTopoCoorType: this.pathTopoCoorType
                      }]);

                      // 操作结束后再次刷新
                      requestAnimationFrame(() => {
                          this.graph.refresh();
                      });
                  }, 800);
          });

    // 画布拖拽
            this.graph.on('canvas:dragstart', () => {
                // 开始拖拽时禁用局部刷新
                this.graph.get('canvas').set('localRefresh', false);
            });

            this.graph.on('canvas:dragend', (evt) => {
                // 取消之前的定时器
                if (this.timerId) {
                    clearTimeout(this.timerId);
                    this.timerId = null;
                }

                // 恢复局部刷新
                this.graph.get('canvas').set('localRefresh', true);

                this.timerId = setTimeout(() => {
                    this.$emit('node-dragend', [{
                        pathTopologyId: this.topoData.topoId,
                        coordinateX: this.graph.getCanvasByPoint(0, 0).x,
                        coordinateY: this.graph.getCanvasByPoint(0, 0).y,
                        coordinateWidth: this.graph.getWidth(),
                        coordinateHeight: this.graph.getHeight(),
                        zoomXMultiple: this.graph.getZoom(),
                        zoomYMultiple: this.graph.getZoom(),
                        pathTopoCoorType: this.pathTopoCoorType
                    }]);

                    // 操作结束后刷新
                    requestAnimationFrame(() => {
                        this.graph.refresh();
                    });
                }, 800);
            });

        // 连线点击事件
        this.graph.on('edge:click', (evt) => {
          if(this.isBigScreen) {
            return
          }
        
          const edge = evt.item;
          console.log(edge,'连线点击事件')
          const model = edge.getModel();
          console.log(model,'连线点击事件')
          if(model.data.disableState == 1 && this.topoData.showWarning == true) {
            return
          }
          this.$emit('link-click', model.data);
        });
         },
        //  修改备注的tooltip
          createNoteTooltip() {
                const that = this; // 保存组件实例的引用
            let isEditing = false;
            let isOverTooltip = false;
            let isOverNode = false;
            let currentValue = '';

            const updateNodeSize = (node, text) => {
                // 创建临时 div 计算文本尺寸
                const tempDiv = document.createElement('div');
                tempDiv.style.position = 'absolute';
                tempDiv.style.visibility = 'hidden';
                tempDiv.style.whiteSpace = 'nowrap';
                tempDiv.style.fontSize = '12px';
                tempDiv.innerText = text;
                document.body.appendChild(tempDiv);

                // 获取文本尺寸并移除临时元素
                const textWidth = tempDiv.offsetWidth;
                const textHeight = tempDiv.offsetHeight;
                document.body.removeChild(tempDiv);

                // 添加内边距计算最终盒子尺寸
                const padding = 15;
                const boxWidth = textWidth + (padding * 2);
                const boxHeight = textHeight + (padding * 2 * 2);

                // 更新节点大小和标签
                this.graph.updateItem(node, {
                    size: [boxWidth, boxHeight],
                    label: text
                });
            };

            const hideTooltipAndSave = (textarea, model, tooltipEl) => {

            // 添加防抖，避免多次触发
            if (that.saveTimeout) {
                clearTimeout(that.saveTimeout);
            }

            that.saveTimeout = setTimeout(() => {
                if (!tooltipEl) {
                    tooltipEl = document.querySelector('.g6-tooltip');
                }
                // debugger
                // if (tooltipEl) {
                    // tooltipEl.style.display = 'none';

                    // 只有当值发生变化且不为空时才保存
                    if (textarea.value !== currentValue) {
                        console.log('Saving note:', textarea.value, model.x, model.y);
                        that.$emit('saveNoteInfo', textarea.value, model.x, model.y);
                        currentValue = textarea.value;
                    }
                // }
            }, 100); // 添加少量延迟确保事件处理的正确顺序
        };

            return new G6.Tooltip({
                getContent: (e) => {
                    const node = e.item;
                    const model = node.getModel();
                    console.log(model,'model')
                  if (!model.id || !model.id.includes('note')) {
                        return  '';
                    }

                    const container = document.createElement('div');
                    container.className = 'note-tooltip-container';
                    // container.style.width = '200px';
                    // container.style.padding = '10px';
                    container.style.border = this.currentSkin == 1 ? '4px solid #16436b' : '2px solid #DCDFE6';
                    

                    const textarea = document.createElement('textarea');
                    textarea.value = model.label || '';
                    textarea.maxLength = 200;
                  
                    currentValue = textarea.value;
                            textarea.addEventListener('input', (e) => {
            if (e.target.value.length > 200) {
                e.target.value = e.target.value.slice(0, 200);
            }
        });
                    // 添加鼠标按下事件监听器
                    document.addEventListener('mousedown', (event) => {
                        const tooltipEl = document.querySelector('.g6-tooltip');
                        if (tooltipEl && !tooltipEl.contains(event.target)) {
                            tooltipEl.style.display = 'none';
                            if (textarea.value !== currentValue) {
                                that.$emit('saveNoteInfo', textarea.value, model.x, model.y);
                                currentValue = textarea.value;
                            }
                        }
                    });

                    textarea.addEventListener('focus', () => {
                        isEditing = true;
                    });

                    textarea.addEventListener('blur', () => {
                        isEditing = false;
                      const tooltipEl = document.querySelector('.g6-tooltip');
                                hideTooltipAndSave(textarea, model, tooltipEl);
                    });

                    textarea.addEventListener('input', (event) => {
                        // 实时更新节点大小和标签
                        updateNodeSize(node, event.target.value);
                    });

                    container.appendChild(textarea);

                    container.addEventListener('mouseenter', () => {
                        isOverTooltip = true;
                    });

                  container.addEventListener('mouseleave', () => {
                    isOverTooltip = false;
                    if (!isEditing) {
                        const tooltipEl = document.querySelector('.g6-tooltip');
                        if (tooltipEl) {
                            hideTooltipAndSave(textarea, model, tooltipEl);
                        }
                    }
                });

                  // 修改全局点击事件监听器
                const handleClickOutside = (event) => {
                    const tooltipEl = document.querySelector('.g6-tooltip');
                    if (tooltipEl && !tooltipEl.contains(event.target) && !isOverNode && !isEditing) {
                        hideTooltipAndSave(textarea, model, tooltipEl);
                        document.removeEventListener('click', handleClickOutside);
                    }
                };
                    document.addEventListener('click', handleClickOutside);

                    // 添加节点事件监听器
                    const observer = new MutationObserver((mutations) => {
                        mutations.forEach((mutation) => {
                            if (mutation.addedNodes.length) {
                                const tooltipEl = document.querySelector('.g6-tooltip');
                                if (tooltipEl) {
                                    const nodeGroup = node.get('group');
                                    if (nodeGroup) {
                                        const nodeElement = nodeGroup.get('children')[0];
                                        if (nodeElement && nodeElement.get('element')) {
                                            nodeElement.get('element').addEventListener('mouseenter', () => {
                                                isOverNode = true;
                                            });

                                            nodeElement.get('element').addEventListener('mouseleave', () => {
                                                isOverNode = false;
                                                setTimeout(() => {
                                                    if (!isOverTooltip && !isEditing) {
                                                        hideTooltipAndSave(textarea, model, tooltipEl);
                                                    }
                                                }, 100);
                                            });
                                        }
                                    }
                                    observer.disconnect();
                                }
                            }
                        });
                    });

                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });

                    setTimeout(() => {
                        textarea.focus();
                    }, 0);

                    return container;
                },
                style: {
                    background: this.currentSkin == 1 ? '#021625' : '#DCDFE6',
                    // border: '2px solid #16436b',
                    // borderRadius: '4px'
                },
                shouldUpdate: () => false,
                // offsetX: -2,  // 水平偏移
                // offsetY: -2,  // 垂直偏移
            });
        },
         // 添加新方法
  createSelectionBox(nodes, combos) {
    this.removeSelectionBox();
    
    // 计算所有选中元素的边界
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    
    // 处理节点边界
    nodes.forEach(node => {
        const bbox = node.getBBox();
        minX = Math.min(minX, bbox.minX);
        minY = Math.min(minY, bbox.minY);
        maxX = Math.max(maxX, bbox.maxX);
        maxY = Math.max(maxY, bbox.maxY);
    });
    
    // 处理 combo 边界
    combos.forEach(combo => {
        const bbox = combo.getBBox();
        const model = combo.getModel();
        // 使用模型中的实际位置
        const comboMinX = model.x - bbox.width / 2;
        const comboMinY = model.y - bbox.height / 2;
        const comboMaxX = model.x + bbox.width / 2;
        const comboMaxY = model.y + bbox.height / 2;
        
        minX = Math.min(minX, comboMinX);
        minY = Math.min(minY, comboMinY);
        maxX = Math.max(maxX, comboMaxX);
        maxY = Math.max(maxY, comboMaxY);
    });
    
    // 添加 padding
    const padding = 10;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;
    
    // 创建选择框
    const group = this.graph.getGroup();
    this.selectionBox = group.addShape('rect', {
        attrs: {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY,
            fill: '#16436B',
            opacity: 0.2,
        },
        capture: false,
        name: 'selection-box'
    });
    
    this.graph.paint();
},
  
  removeSelectionBox() {
    if (this.selectionBox) {
      this.selectionBox.remove();
      this.selectionBox = null;
      this.graph.paint();
    }
  },

        //  备注信息
        createNoteNode(value, coordinateX, coordinateY) {
          // 设置备注文本
        let text = value ? value : this.$t('comm_remarks');

        // 创建临时 div 计算文本尺寸
        const tempDiv = document.createElement('div');
        tempDiv.style.position = 'absolute';
        tempDiv.style.visibility = 'hidden';
        tempDiv.style.whiteSpace = 'nowrap';
        tempDiv.style.fontSize = '12px';
        tempDiv.innerText = text;
        document.body.appendChild(tempDiv);

        // 获取文本尺寸并移除临时元素
        const textWidth = tempDiv.offsetWidth;
        const textHeight = tempDiv.offsetHeight;
        document.body.removeChild(tempDiv);

        // 添加内边距计算最终盒子尺寸
        const padding = 15;
        const boxWidth = textWidth + (padding * 2);
        const boxHeight = textHeight + (padding * 2 * 0.8);

        // 添加自定义节点
        const noteId = `note-${Date.now()}`;
              this.graph.addItem('node', {
                id: noteId,
                x: coordinateX,
                y: coordinateY,
                type: 'rect',
                size: [boxWidth, boxHeight],
                style: {
                  fill: this.currentSkin == 1 ? 'transparent' : '#F5F7FA',
                  stroke: this.currentSkin == 1 ? '#06324D' : '#DCDFE6',
                   lineWidth:1,
                  cursor: 'move',

                },
                label: text,
                labelCfg: {
                  style: {
                    fill: this.currentSkin == 1 ? 'rgba(0, 154, 147,1)' : '#515A6E',
                    fontSize: 12,
                    fontWeight: 'normal'
                  }
                }
              });


        },
         getBgSize(bgSrc) {

    if(bgSrc !== '') {
      let image = new Image();
      image.src = bgSrc;
      image.onload = () => {
      // this.bgImage.width = image.width
      // this.bgImage.height = image.height
        // console.log(this.bgImage.width, this.bgImage.height,'图片的宽高')
        // console.log(this.containerSize.width, this.containerSize.height,'容器宽高')
        // 图片宽高比
      // this.aspectRatio = image.width / image.height
        this.widthRatio = image.height/image.width;
      // 构件宽高比
      // this.componentAspectRatio = this.containerSize.width / this.containerSize.height
      // console.log(this.aspectRatio,'图片',this.componentAspectRatio,'构件')
      // this.handleNodes()
      // this.initG6()
    }
    }
         },
        // 初始化父组件传过来的数据
        initData(data) {
          if(this.graph) {
            this.graph.destroy()
          }
            console.log(data,'phytopo')
            this.topoData = data
            console.log(this.topoData,'数据。。。。。。。。。。。')
            this.getBgSize(data.backgroundImage)
            // 处理数据
            this.handleData()
            this.initG6()

        },
        showTypeChange() {
          this.handleData()
           this.graph.changeData(this.phytopoData) // 数据映射
        },
        // 修改连线处理函数，确保连线正确连接到节点
handleMoreLink(link) {
    let totalCount = 0
    
    const pairKey = `${link.source}-${link.target}`;
    console.log(pairKey,'pairKey')
    this.topoData.childrenLink.forEach(item => {
        let pairKey2 = `${item.source}-${item.target}`;
        let pairKey3 = `${item.target}-${item.source}`;
        console.log(pairKey2,pairKey3,'pairKey2,pairKey3')
        if(pairKey3 == pairKey) {
            item.source = item.target
            item.target = pairKey2.split('-')[0]
        }

        if(pairKey2 == pairKey || pairKey3 == pairKey) {
            totalCount++
        }
    })
    
    if (!this.linkCount[pairKey]) {
        this.linkCount[pairKey] = 0;
    }
    this.linkCount[pairKey]++;
    const count = this.linkCount[pairKey];
    
    // 根据连线数量选择合适的锚点
   
    let curveOffset = 0.001
    
    console.log(totalCount,link.id,'总数')
    if(totalCount > 8) {
     
      
        
        if(totalCount % 2 === 0) {
            if(count % 2 === 0) {
                curveOffset = count * 8
            } else {
                curveOffset = count * -8
            }
        } else {
            if(count === 1) {
                curveOffset = 0.001
            } else if(count % 2 === 0) {
                curveOffset = count * 8
            } else {
                curveOffset = count * -8
            }
        }
    } else {
        // 使用预定义的锚点配置
        let itemData = this.circleFormat[Math.min(totalCount-1, this.circleFormat.length-1)]
      
        curveOffset = itemData[Math.min(count-1, itemData.length-1)].curveOffset;
    }
    
    return {
      
        curveOffset,
    };
},


        // 处理数据
        handleData() {
          this.phytopoData = {
            nodes:[],
            edges:[],
            combos:[]
          }
            // 处理节点
            let nodeList = this.topoData.dataList || []
            nodeList.forEach(element => {
                let obj = this.handleNode(element)

                this.phytopoData.nodes.push(obj)
            });
        //   处理节点逻辑结束
        // 处理连线
        let linkList = this.topoData.childrenLink || []
          this. linkCount = {};
        linkList.forEach(link => {

          let text = this.handleLinkText(link);
          let color = this.handleLinkColor(link);
          let linkStyle = this.handleLinkStyle(link);
         let {  curveOffset } = this.handleMoreLink(link)
        
          let obj = {
            source: link.source,
            target: link.target,
            label: text,
            curveOffset: curveOffset,
            type: "animated-line",
            data: link,
            style: {
              stroke: color,
              ...linkStyle,
              lineAppendWidth: 20
            },
            labelCfg: {
              autoRotate: true,
              style: {
                fontSize: 7,
                fill: color,
              },
            },
             // 确保使用中心锚点
      sourceAnchor: 0,
      targetAnchor: 0
           
          };
          // x位置不对问题
          if(link.disableState == 1 && this.topoData.showWarning == true) {
            obj.labelCfg.refY = 1
          }
         
            this.phytopoData.edges.push(obj)
        });
        // 处理连线逻辑结束
        // 处理子图
           let groupList = JSON.parse(JSON.stringify(this.topoData.groupeList || []))
        groupList.forEach(group => {

            let arr = []
            
            group.nodeList.forEach(node => {
                node.locX = group.locX + node.locX
                node.locY = group.locY + node.locY
                let nodeObj = this.handleNode(node)
                arr.push(nodeObj.id)
                this.phytopoData.nodes.push(nodeObj)

            })
            let borderColor = ''
            let degradationLineColor = this.topoData.configData.degradationLineColor
            let normalLineColor = this.topoData.configData.normalLineColor
            let breakLineColor = this.topoData.configData.breakLineColor
            // 中断
            if (group.colorType == 0) {
              this.topoData.showWarning == true ? (borderColor = breakLineColor) : (borderColor = normalLineColor);
              } else if (group.colorType == 1) {
              // 劣化
              this.topoData.showWarning == true ? (borderColor = degradationLineColor) : (borderColor = normalLineColor);
            } else {
              // 正常
              borderColor = normalLineColor;
            }
            // 创建 combo
            if(group.nodeList.length > 0) {
              this.phytopoData.combos.push({
            id: group.id,
            label: group.name,
            data: group,
            size:[group.width,group.height],
            style: {
                fill: this.currentSkin == 1 ? 'rgba(1,27,45,0.7)' : '#FAFBFC',
                stroke: borderColor,
            }
        })

            }else {
              this.phytopoData.combos.push({
              id: group.id,
              label: group.name,
              size: [group.width, group.height],
              x: group.locX,  // 添加x坐标
              y: group.locY,  // 添加y坐标
              data: group,
              style: {
                fill: this.currentSkin == 1 ? 'rgba(1,27,45,0.7)' : '#FAFBFC',
                  stroke: borderColor,
              }
          });
              
            }
        
        // 将节点添加到 combo 中
        arr.forEach(nodeId => {
            let node = this.phytopoData.nodes.find(n => n.id === nodeId)
            if (node) {
                node.comboId = group.id
            }
        })

        })
        // 处理子图逻辑结束
        },
         // 注册自定义边
    registerEdges() {
                  // 注册自定义边
                G6.registerEdge('animated-line', {
                  // 暂时注释飞线
              // afterDraw(cfg, group) {
              //     // 获取路径图形
              //     const shape = group.get('children')[0];
              //     const startPoint = shape.getPoint(0);
              //     const endPoint = shape.getPoint(1);

              //     // 创建两个圆点
              //     const dot1 = group.addShape('circle', {
              //         attrs: {
              //             x: startPoint.x,
              //             y: startPoint.y,
              //             fill: cfg.style.stroke,
              //             r: 4,
              //         },
              //         name: 'dot1',
              //     });

              //     const dot2 = group.addShape('circle', {
              //         attrs: {
              //             x: endPoint.x,  // 从终点开始
              //             y: endPoint.y,
              //             fill: cfg.style.stroke,
              //             r: 4,
              //         },
              //         name: 'dot2',
              //     });

              //     // 创建动画 - dot1从起点到终点
              //     dot1.animate(
              //         (ratio) => {
              //             const point = shape.getPoint(ratio);
              //             return {
              //                 x: point.x,
              //                 y: point.y,
              //             };
              //         },
              //         {
              //             repeat: true,
              //             duration: 8000,
              //         },
              //     );

              //     // 创建动画 - dot2从终点到起点
              //     dot2.animate(
              //         (ratio) => {
              //             const point = shape.getPoint(1 - ratio); // 使用1-ratio让动画反向运动
              //             return {
              //                 x: point.x,
              //                 y: point.y,
              //             };
              //         },
              //         {
              //             repeat: true,
              //             duration: 8000,
              //         },
              //     );
              // },
          }, 'arc');
    },



        // 节点方法封装
        handleNode(element) {
                let image = this.handleImage(element)
                let labelText = this.handleNodeLabel(element)
                let nodeObj = {
                    id: element.id,
                    x: element.locX,
                    y: element.locY,
                    type: 'image',  // 设置节点类型为图片
                    img: image, // 图片URL
                    size: [30, 30], // 图片大小
                    label: labelText, // 节点标签
                    data: element,
                    labelCfg: {
                        position: 'bottom', // 标签位置
                        offset: 5 ,// 标签偏移量
                        style: {
                            fontSize: 8,
                            fill: this.currentSkin == 1 ? '#fff' : '#515A6E'
                        }
                    },
                    anchorPoints:[[0.5, 0.5]]
                
                }

                return nodeObj


        },
        // 处理连线样式虚线实线线粗细
        handleLinkStyle(item) {
            let type = {

            }
             const bandwidth = Math.max(item.bandwidth, item.bandwidth);
      if (bandwidth >= 1000000000) {
        // 流速大于1000M

        type.linWidth = 1.3
      }
      else if (bandwidth >= 1000000 && bandwidth < 1000000000) {
        // 流速1M~1000M
        type.linWidth = 0.7
      } else {
        // 流速小于1M
        type.linWidth = 0.7
        type.lineDash = [6, 2]

      };
      return type

        },
        // 处理连线颜色
        handleLinkColor(item) {
            let color = '';
            let showWarning = this.topoData.showWarning
            let configData = this.topoData.configData
       let breakLineColor = configData.breakLineColor
      // 正常颜色
      let normalLineColor = configData.normalLineColor
      // 裂化颜色
      let degradationLineColor = configData.degradationLineColor
      // disableState == 1 处理的是线路上标红叉的逻辑
      if(item.disableState == 1){
           // 中断
        showWarning == true ? (color = breakLineColor) : (color = normalLineColor);
      }else{
      if (item.devType == 0) {
        // 中断
       showWarning == true ? (color = breakLineColor) : (color = normalLineColor);
      } else if (item.devType == 1) {
        // 劣化
        showWarning == true ? (color = degradationLineColor) : (color = normalLineColor);
      } else {
        color = normalLineColor;
      }
      }
      return color

        },
        // 处理连线text
        handleLinkText(item) {
             let fianlText = '';
             let text = item.text
             console.log(item,'item.......................')
          if(item.disableState == 1 && this.topoData.showWarning == true) {
           return 'X'
          }
      if (text !== null && text !== '') {
        let test1 = text.split('\n')
        let flow = test1[0];
        if (this.msgModal.includes(this.$t('comm_line_flow'))) {
          fianlText = flow;
        }
        let delay = '';
        let loss = '';
        if (test1.length === 2) {
          delay = test1[1].split('/')[0];
          loss = test1[1].split('/')[1];
          if (this.msgModal.includes(this.$t('comm_line_delay'))) {
            fianlText = (fianlText === null ? '' : fianlText) + '\n' + delay;
            if (this.msgModal.includes(this.$t('comm_line_packet_loss_rate'))) {
              fianlText = fianlText + '/';
            }
          }
          if (this.msgModal.includes(this.$t('comm_line_packet_loss_rate'))) {
            if (!this.msgModal.includes(this.$t('comm_line_delay')) && !this.msgModal.includes(this.$t('comm_line_flow'))) {
              fianlText = fianlText + '\n' + loss;
            }
            else if (!this.msgModal.includes(this.$t('comm_line_delay')) && this.msgModal.includes(this.$t('comm_line_flow'))) {
              fianlText = fianlText + '\n' + loss;
            } else {
              fianlText = (fianlText === null ? '\n' : fianlText) + loss
            }
          }

        }else {
          fianlText = fianlText + '\n'
        }
      }
       return fianlText

        },
        // 处理图片
        handleImage(item) {
            let dev0 = item.devType.split(',')[0]; //设备类型
            let dev1 = item.devType.split(',')[1];  //状态类型
            // 0：正常，1：中断，2：劣化


      let configData = this.topoData.configData
      let url = ''
      let showWarning = this.topoData.showWarning
      configData.inUseIconManageConfigures.forEach(item => {

        if (item.deviceTypeId == dev0) {
          if(showWarning == true) {
            item.types.forEach(item2 => {
            if (item2.type == dev1) {
              // console.log(item2.image, 123)

              url = item2.image

            }
          })

          }else {
            // debugger
            let findResult = item.types.find(item3 => item3.type === 0)

            url = findResult.image
          }

        }


      })
      return 'data:image/png;base64,' + url
        },
        handleNodeLabel(item) {
            let value2 = ipv6Format.abbreviateIPv6(item.value)
            let labelText = ''
            let finName = item.aliases
      if (item.aliases == '' || item.aliases == null || item.aliases == undefined || item.aliases == '--') {
        finName = item.devName
        if (item.devName == '' || item.devName == null || item.devName == undefined || item.devName == '--') {
          finName = '--'

        }
      }

      if (this.msgModal.includes(this.$t('phytopo_device_ip'))) {
        if (this.msgModal.includes(this.$t('phytopo_device_Name'))) {


          labelText = value2 + '\n' + finName
        } else {

          labelText = value2

        }
      } else if (this.msgModal.includes(this.$t('phytopo_device_Name'))) {
        if (this.msgModal.includes(this.$t('phytopo_device_ip'))) {
          labelText = value2 + '\n' + finName
        } else {
            labelText = finName
        }
      } else {
        labelText = ''
      }
      return labelText
        },
        search(searchVal) {
           this.clearHighlight()
      console.log(searchVal,'search')
      const graph = this.graph;
      // 搜索匹配的节点
  const matchedNodes = graph.getNodes().filter(node => {
    const nodeModel = node.getModel();
    // 假设节点的标签存储在 label 字段中，您可能需要根据实际情况调整
    return nodeModel.label && nodeModel.label.toLowerCase().includes(searchVal.toLowerCase());
  });

  // 高亮匹配的节点
  matchedNodes.forEach(node => {
    const nodeModel = node.getModel();
    const originalSize = nodeModel.size ||30;
    let targetSize;

    if (Array.isArray(originalSize)) {
      targetSize = originalSize.map(s => s * 3);
    } else {
      targetSize = originalSize * 3;
    }
     // 使用动画效果更新节点大小
     this.animateNodeSize(node, originalSize, targetSize, 1250, true);
  });

  // 如果有匹配的节点，将视图居中到第一个匹配的节点
  if (matchedNodes.length > 0) {
    graph.focusItem(matchedNodes[0]);
    // 有匹配的节点保存画布位置
      this.$emit('node-dragend',[
               {
               pathTopologyId: this.topoData.topoId,
                coordinateX: this.graph.getCanvasByPoint(0, 0).x,
                coordinateY: this.graph.getCanvasByPoint(0, 0).y,
                coordinateWidth: this.graph.getWidth(),
                coordinateHeight: this.graph.getHeight(),
                zoomXMultiple: this.graph.getZoom(),
                zoomYMultiple: this.graph.getZoom(),
                pathTopoCoorType: this.pathTopoCoorType
            }
            ]);
  }


        },
      // 修改动画方法以支持放大后缩小
    animateNodeSize(node, startSize, endSize, duration, shouldShrink = false) {
      const startTime = Date.now();
      const graph = this.graph;
      const originalSize = startSize;

      const animate = () => {
        const currentTime = Date.now();
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        // 使用 easeInOutCubic 缓动函数
        const easeProgress = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        let currentSize;
        if (Array.isArray(startSize)) {
          currentSize = startSize.map((start, index) =>
            start + (endSize[index] - start) * easeProgress
          );
        } else {
          currentSize = startSize + (endSize - startSize) * easeProgress;
        }

        graph.updateItem(node, { size: currentSize });

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else if (shouldShrink) {
          // 当放大动画完成后，开始缩小动画
          this.animateNodeSize(node, endSize, originalSize, duration);
        }
      };

      animate();
    },
    // 清除高亮的方法保持不变
clearHighlight() {
  const graph = this.graph;
  graph.getNodes().forEach(node => {
    const nodeModel = node.getModel();
                // Use the stored original size for restoration
                if (nodeModel.originalSize) {
                    graph.updateItem(node, {
                        size: nodeModel.originalSize,
                    });
                }
  });
},
zoomIn() {
  const currentZoom = this.graph.getZoom();
    const newZoom = Math.max(currentZoom * 0.8, 0.2); // 限制最小缩放比例为0.2
    // 禁用局部刷新
    this.graph.get('canvas').set('localRefresh', false);
    this.graph.zoomTo(newZoom,this.viewCenter);
      clearTimeout(this.timerId)
   this.timerId = setTimeout(() => {
        // 恢复局部刷新
        this.graph.get('canvas').set('localRefresh', true);

        this.$emit('node-dragend',[{
            pathTopologyId: this.topoData.topoId,
            coordinateX: this.graph.getCanvasByPoint(0, 0).x,
            coordinateY: this.graph.getCanvasByPoint(0, 0).y,
            coordinateWidth: this.graph.getWidth(),
            coordinateHeight: this.graph.getHeight(),
            zoomXMultiple: this.graph.getZoom(),
            zoomYMultiple: this.graph.getZoom(),
            pathTopoCoorType: this.pathTopoCoorType
        }]);

        // 操作结束后刷新
        requestAnimationFrame(() => {
            this.graph.refresh();
        });
    }, 800);
    // this.savePathTopologyCoordinate({}); // 保
},
 zoomOut() {
    const currentZoom = this.graph.getZoom();
    const newZoom = Math.min(currentZoom * 1.2, 5);

    // 禁用局部刷新
    this.graph.get('canvas').set('localRefresh', false);

    this.graph.zoomTo(newZoom,this.viewCenter);
    clearTimeout(this.timerId);
    this.timerId = setTimeout(() => {
        // 恢复局部刷新
        this.graph.get('canvas').set('localRefresh', true);

        this.$emit('node-dragend',[{
            pathTopologyId: this.topoData.topoId,
            coordinateX: this.graph.getCanvasByPoint(0, 0).x,
            coordinateY: this.graph.getCanvasByPoint(0, 0).y,
            coordinateWidth: this.graph.getWidth(),
            coordinateHeight: this.graph.getHeight(),
            zoomXMultiple: this.graph.getZoom(),
            zoomYMultiple: this.graph.getZoom(),
            pathTopoCoorType: this.pathTopoCoorType
        }]);

        // 操作结束后刷新
        requestAnimationFrame(() => {
            this.graph.refresh();
        });
    }, 800);
},

   // 导出图片功能（飞线版本）
//  export() {
//   let name = this.tabNlist.find(item => item.id == this.topoData.topId).topologyName;
  
//   // 临时存储所有边的动画圆点
//   // const tempDots = [];
  
  
//   // 在导出前清除所有现有的动画圆点
//   // this.graph.getEdges().forEach(edge => {
//   //   const group = edge.get('group');
//   //   // 找到并移除所有名为 'dot1' 和 'dot2' 的圆点
//   //   const dots = group.findAllByName('dot1').concat(group.findAllByName('dot2'));
//   //   dots.forEach(dot => dot.remove());
//   // });

//   // 在导出前为每条边添加静态圆点
//   // this.graph.getEdges().forEach(edge => {
//   //   const shape = edge.get('keyShape');
//   //   const color = edge.getModel().style.stroke;
//   //   const group = edge.get('group');
    
//   //   // 在连线上添加多个点来模拟动画效果
//   //   for (let i = 0; i < 10; i++) {
//   //     const position = i * 0.1;
//   //     const point = shape.getPoint(position);
//   //     tempDots.push(
//   //       group.addShape('circle', {
//   //         attrs: {
//   //           x: point.x,
//   //           y: point.y,
//   //           fill: color,
//   //           r: 4
//   //         },
//   //         capture: false
//   //       })
//   //     );
//   //   }
//   // });
//  let bgColor = this.currentSkin == 1 ? '#060D15' : '#fff'

//   // 导出图片
//   this.graph.downloadImage(name, 'image/png', bgColor);
//   // 暂时注释飞线

//   // // 导出后移除临时添加的静态圆点
//   // tempDots.forEach(dot => dot.remove());
  
//   // // 重新渲染图以恢复动画
//   // this.graph.getEdges().forEach(edge => {
//   //   const group = edge.get('group');
//   //   const shape = group.get('children')[0];
//   //   const color = edge.getModel().style.stroke;
    
//   //   // 重新创建动画点1
//   //   const dot1 = group.addShape('circle', {
//   //     attrs: {
//   //       x: shape.getPoint(0).x,
//   //       y: shape.getPoint(0).y,
//   //       fill: color,
//   //       r: 4,
//   //     },
//   //     name: 'dot1',
//   //   });

//   //   // 重新创建动画点2
//   //   const dot2 = group.addShape('circle', {
//   //     attrs: {
//   //       x: shape.getPoint(1).x,
//   //       y: shape.getPoint(1).y,
//   //       fill: color,
//   //       r: 4,
//   //     },
//   //     name: 'dot2',
//   //   });

//   //   // 重新启动动画
//   //   dot1.animate(
//   //     (ratio) => {
//   //       const point = shape.getPoint(ratio);
//   //       return {
//   //         x: point.x,
//   //         y: point.y,
//   //       };
//   //     },
//   //     {
//   //       repeat: true,
//   //       duration: 8000,
//   //     },
//   //   );

//   //   dot2.animate(
//   //     (ratio) => {
//   //       const point = shape.getPoint(1 - ratio);
//   //       return {
//   //         x: point.x,
//   //         y: point.y,
//   //       };
//   //     },
//   //     {
//   //       repeat: true,
//   //       duration: 8000,
//   //     },
//   //   );
//   // });

//   // 刷新图
//   this.graph.refresh();
// },
// 导出图片功能（不包括飞线版本）
// 导出图片功能
export() {
  // 获取拓扑图名称
  let name = '';
  try {
    name = this.tabNlist.find(item => item.id === this.topoData.topId)?.topologyName || '物理拓扑图';
  } catch (e) {
    name = '物理拓扑图';
  }
  
  // 设置背景颜色
  const bgColor = this.currentSkin == 1 ? '#060D15' : '#fff';
  
  // 禁用局部刷新以确保完整渲染
  this.graph.get('canvas').set('localRefresh', false);
  
  // 强制刷新图形以确保所有元素都被渲染
  this.graph.refresh();
  
  // 创建临时canvas以便更好地控制导出过程
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  // 获取图形尺寸
  const width = this.graph.get('width');
  const height = this.graph.get('height');
  
  // 设置canvas尺寸
  canvas.width = width;
  canvas.height = height;
  
  // 绘制背景
  ctx.fillStyle = bgColor;
  ctx.fillRect(0, 0, width, height);
  
  // 如果有背景图片，先绘制背景图片
  if (this.topoData.backgroundImage) {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      // 绘制背景图片
      ctx.drawImage(img, 0, 0, width, height);
      
      // 然后绘制G6图形
      const graphCanvas = this.graph.get('canvas').get('el');
      ctx.drawImage(graphCanvas, 0, 0, width, height);
      
      // 导出图片
      this.downloadImage(canvas.toDataURL('image/png'), name);
      
      // 恢复局部刷新
      this.graph.get('canvas').set('localRefresh', true);
    };
    
    img.onerror = () => {
      // 如果背景图片加载失败，只导出图形
      const graphCanvas = this.graph.get('canvas').get('el');
      ctx.drawImage(graphCanvas, 0, 0, width, height);
      
      this.downloadImage(canvas.toDataURL('image/png'), name);
      this.graph.get('canvas').set('localRefresh', true);
    };
    
    img.src = this.topoData.backgroundImage;
  } else {
    // 没有背景图片，直接绘制图形
    const graphCanvas = this.graph.get('canvas').get('el');
    ctx.drawImage(graphCanvas, 0, 0, width, height);
    
    this.downloadImage(canvas.toDataURL('image/png'), name);
    this.graph.get('canvas').set('localRefresh', true);
  }
},

// 下载图片的辅助方法
downloadImage(dataUrl, fileName) {
  const link = document.createElement('a');
  link.download = `${fileName}.png`;
  link.href = dataUrl;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // 刷新图形以恢复原始状态
  setTimeout(() => {
    this.graph.refresh();
  }, 100);
},
   // 全屏切换方法
    fullScreen() {
      const container = document.getElementById('phytopo');

      if (!document.fullscreenElement) {
        // 进入全屏
        if (container.requestFullscreen) {
          container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
          container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
          container.msRequestFullscreen();
        }

        this.isFullscreen = true;
        // 调整图表大小为屏幕尺寸
        this.graph.changeSize(window.screen.width, window.screen.height);
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }

        this.isFullscreen = false;
        // 恢复原始大小
        this.graph.changeSize(this.topoData.width, this.topoData.height);
      }

      // 重新渲染以适应新的尺寸
      this.graph.fitView();
    },






    },
    created() {
      if(window.name.indexOf('bigscreen') > -1) {
        this.isBigScreen = true
  }
    }

}
</script>

<style lang="less">
@dark-bg: #021625;
@light-bg: #fafbfc;
@dark-text: #5ca0d5;
@light-text: #515a6e;
@light-bg-color: #fff;
@dark-bg-color: #060d15;
.g6-component-tooltip {
  background-color: unset !important;
  padding: 0 !important;
  border: unset !important;

  /* 其他样式 */
}
.phytopo {
  background-color: var(--path_body_b_color, #fff);
}
textarea {
  background-color: var(--textarea-bg, @dark-bg) !important;
  color: var(--textarea-text, @dark-text) !important;
  width: 400px;
  height: 200px;
  border: unset !important;
}
textarea:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
:root {
  --textarea-bg: @dark-bg;
  --textarea-text: @dark-text;
  --path_body_b_color: @dark-bg-color;
}

:root[data-theme="light"] {
  --textarea-bg: @light-bg;
  --textarea-text: @light-text;
  --path_body_b_color: @light-bg-color;
}
</style>
