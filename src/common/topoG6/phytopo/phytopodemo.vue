<template>
  <!-- 物理拓扑图  -->
  <div class="phytopo" id="phytopo"></div>
</template>

  <script>
  import G6 from '@antv/g6';



  export default {


      data() {

          return {
            nodeData : {
            nodes: [
                {
                id: '0',
                x: 150,
                y: 50,
                },
                {
                id: '1',
                x: 350,
                y: 250,
                },
            ],
            edges: [
                // Built-in arc edges
                {
                id: 'edge0',
                source: '0',
                target: '1',
                label: 'curveOffset = 15',
                curveOffset: 15,
                },
                {
                id: 'edge1',
                source: '0',
                target: '1',
                label: 'curveOffset = -15', // the bending degree
                curveOffset: -15,
                },
                {
                id: 'edge2',
                source: '0',
                target: '1',
                label: 'curveOffset = -30', // the bending degree
                curveOffset: -30,
                },
                {
                id: 'edge3',
                source: '0',
                target: '1',
                label: 'curveOffset = 30', // the bending degree
                curveOffset: 30,
                },
                {
                id: 'edge4',
                source: '0',
                target: '1',
                label: 'curveOffset = 45', // the bending degree
                curveOffset: 45,
                },
                {
                id: 'edge5',
                source: '0',
                target: '1',
                label: 'curveOffset = -45', // the bending degree
                curveOffset: -45,
                },
                {
                id: 'edge6',
                source: '0',
                target: '1',
                label: 'curveOffset = 60', // the bending degree
                curveOffset: 60,
                },
                {
                id: 'edge7',
                source: '0',
                target: '1',
                label: 'curveOffset = -60', // the bending degree
                curveOffset: -60,
                },
            ],
            }

          }

      },
      methods: {
        initG6 () {
            const graph = new G6.Graph({
                container: 'phytopo', // String | HTMLElement，必须，在 Step 1 中创建的容器 id 或容器本身
                width: 800, // Number，必须，图的宽度
                height: 600, // Number，必须，图的高度
                defaultEdge: {
                type: 'arc',

                style: {
                  stroke: 'pink',
                },
                labelCfg: {
                  autoRotate: true,
                  style: {
                    fill: '#FFFFFF',  // 设置边文本颜色为白色
                  },
                  refY: -10,
    },
  },

             })
             graph.data(this.nodeData); // 读取 Step 2 中的数据源到图上
             graph.render(); // 渲染图
        }
      },
      mounted() {
          this.initG6()
      }


  }
  </script>

  <style lang="less">
@dark-bg: #021625;
@light-bg: #fafbfc;
@dark-text: #5ca0d5;
@light-text: #515a6e;
@light-bg-color: #fff;
@dark-bg-color: #060d15;
.g6-component-tooltip {
  background-color: unset !important;
  padding: 0 !important;
  border: unset !important;

  /* 其他样式 */
}
.phytopo {
  background-color: var(--path_body_b_color, #fff);
}
textarea {
  background-color: var(--textarea-bg, @dark-bg) !important;
  color: var(--textarea-text, @dark-text) !important;
  width: 400px;
  height: 200px;
  border: unset !important;
}
textarea:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
:root {
  --textarea-bg: @dark-bg;
  --textarea-text: @dark-text;
  --path_body_b_color: @dark-bg-color;
}

:root[data-theme="light"] {
  --textarea-bg: @light-bg;
  --textarea-text: @light-text;
  --path_body_b_color: @light-bg-color;
}
</style>
