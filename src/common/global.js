const lan = require('../common/language')
const faultList= [
  { value: 1,label: lan.getLabel("src.interrupt")},
  { value: 2,label: lan.getLabel("src.LatencyDegradation")},
  { value: 3,label: lan.getLabel("src.PLD")},
  // { value: 4,label: '劣化'},
  { value: 7,label: lan.getLabel("src.RouteFluctuation")},
  // { value: 8,label: '紧急通知'},
  // 专线拥塞
  { value: 9,label: lan.getLabel("src.DLC")},
  // 端口拥塞
  { value: 10,label: lan.getLabel("src.PortCongestion")},
  // { value: 9,label: lan.getLabel("src.trafficCongestion")},
  { value: 11,label: lan.getLabel("src.CollectorOffline")},
  // { value: 12,label: lan.getLabel("src.IllegalDeviceAccess")},
  // 端口状态改变
  { value: 13,label: lan.getLabel("src.PortStatusChange")},
];
const networkfaultList= [
  { value: 2,label: lan.getLabel("src.LatencyDegradation")},
  { value: 3,label: lan.getLabel("src.PLD")},
  { value: 1,label: lan.getLabel("src.interrupt")},
  // { value: 4,label: '劣化'},
];
const flowFaultList= [
    // 专线拥塞
  { value: 9,label: lan.getLabel("src.DLC")},
   // 端口拥塞
  // { value: 9,label: lan.getLabel("src.trafficCongestion")},
  { value: 10,label: lan.getLabel("src.PortCongestion")}
];
const factoryList = [
  //厂商列表List
  { id: 2, name: lan.getLabel("src.Huawei") },
  { id: 1, name: lan.getLabel("src.Cisco") },
  { id: 3, name: lan.getLabel("src.H3C") },
  { id: 4, name: lan.getLabel("src.RG") },
  { id: 5, name: lan.getLabel("src.MP") },
  { id: 30, name: lan.getLabel("src.SF") }
];
const snmpVersionList = [
  //snmp版本列表List
  { id: 0, name: "SNMP V1" },
  { id: 1, name: "SNMP V2" },
  { id: 3, name: "SNMP V3" }
];
const devModelArray = {
  //各厂商型号
  huawei: ["5000E", "6000E"],
  sike: ["4000E", "7000E"],
  huasan: ["4000E", "5000E", "6000E", "8000E"]
};
const unitLists = [
  //时间单位List
  //{ id: 1, label: "秒" },
  { id: 2, label: lan.getLabel("src.point") },
  { id: 3, label: lan.getLabel("src.Hour") },
  { id: 4, label: lan.getLabel("src.sky") },
  { id: 5, label: lan.getLabel("src.week") }
];
const indicatorLists = [
  //各指标项
  // { id: 1, label: "时延+丢包率", disabled: false },
  // { id: 2, label: "流速", disabled: false },
  { id: 1, label: lan.getLabel("src.RelayInformation"), disabled: false },
  { id: 2, label: lan.getLabel("src.PortIndicators"), disabled: false },
  { id: 3, label: lan.getLabel("src.DeviceInformation"), disabled: false },
  { id: 4, label: lan.getLabel("src.LinkInformation"), disabled: false },
  { id: 5, label: lan.getLabel("src.ARPInformation"), disabled: false },
  { id: 6, label: lan.getLabel("src.MACInformation"), disabled: false },
  { id: 7, label: lan.getLabel("src.PortInformation"), disabled: false }
];
// 消息模板-消息类型集合
const messageTypeList = [
  {value: 1, label: lan.getLabel("src.AlarmTrigger")},
  {value: 2, label: lan.getLabel("src.AlarmRecovery")}
];
// 消息模板-模板类型集合
const templateTypeList = [
  {value: 1, label: lan.getLabel("src.mail")},
  {value: 2, label: lan.getLabel("src.ShortMessage")},
  // {value: 3, label: lan.getLabel("src.ChatMessage")},
  {value: 4, label: lan.getLabel("src.DingTalk")},
  {value: 5, label: lan.getLabel("src.voice")},
  {value: 6, label: lan.getLabel("src.EnterpriseWechat")}
];
// 运营商集合
const operatorList = [
  {value: 0, label: lan.getLabel("src.all")},
  {value: 1, label: lan.getLabel("src.ChinaMobile")},
  {value: 2, label: lan.getLabel("src.ChinaUnicom")},
  {value: 3, label: lan.getLabel("src.ChinaTelecom")},
  {value: 4, label: lan.getLabel("src.CRAT")},
  {value: 6, label: lan.getLabel("src.other")}
];
// 统计角度
const statisticalAngleList = [
  {value: 1, label: lan.getLabel("src.event")},
  {value: 2, label: lan.getLabel("src.Fault")}
];



// 发包个数
const packList = {
  // 10s
  '10':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10}
  ],
  // 30s
  '30':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10},
    {label:15,value:15,id:15},
    {label:30,value:30,id:30},
  ],
  // 1min
  '60':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10},
    {label:15,value:15,id:15},
    {label:20,value:20,id:20},
    {label:30,value:30,id:30},
    {label:60,value:60,id:60},
  ],
  // 5min
  '300':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10},
    {label:15,value:15,id:15},
    {label:20,value:20,id:20},
    {label:25,value:25,id:25},
    {label:30,value:30,id:30},
    {label:50,value:50,id:50},
  ],
  // 10min
  '600':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10},
    {label:15,value:15,id:15},
    {label:20,value:20,id:20},
    {label:25,value:25,id:25},
    {label:30,value:30,id:30},
    {label:40,value:40,id:40},
    {label:50,value:50,id:50},
    {label:60,value:60,id:60},
    {label:100,value:100,id:100},
  ],
  // 30min
  '1800':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10},
    {label:15,value:15,id:15},
    {label:20,value:20,id:20},
    {label:25,value:25,id:25},
    {label:30,value:30,id:30},
    {label:40,value:40,id:40},
    {label:45,value:45,id:45},
    {label:50,value:50,id:50},
    {label:60,value:60,id:60},
    {label:75,value:75,id:75},
    {label:90,value:90,id:90},
    {label:100,value:100,id:100},
    {label:120,value:120,id:120},
    {label:150,value:150,id:150},
    {label:180,value:180,id:180},
    {label:200,value:200,id:200},
    {label:225,value:225,id:225},
    {label:300,value:300,id:300},
  ],
  // 1h
  '3600':[
    {label:5,value:5,id:5},
    {label:10,value:10,id:10},
    {label:15,value:15,id:15},
    {label:20,value:20,id:20},
    {label:25,value:25,id:25},
    {label:30,value:30,id:30},
    {label:40,value:40,id:40},
    {label:45,value:45,id:45},
    {label:50,value:50,id:50},
    {label:60,value:60,id:60},
    {label:75,value:75,id:75},
    {label:80,value:80,id:80},
    {label:90,value:90,id:90},
    {label:100,value:100,id:100},
    {label:120,value:120,id:120},
    {label:150,value:150,id:150},
    {label:180,value:180,id:180},
    {label:200,value:200,id:200},
    {label:240,value:240,id:240},
    {label:300,value:300,id:300},
    {label:360,value:360,id:360},
    {label:400,value:400,id:400},
    {label:450,value:450,id:450},
    {label:600,value:600,id:600},
  ]
}

// 包大小
const packSizeList = {
  // 10s
  '10':[ 
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1}
  ],
  // 30s
  '30':[
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1}
  ],
  // 1min
  '60':[
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1},
    {label:'500',value:500,id:2},
    {label:'1000',value:1000,id:3},
    {label:'1500',value:1500,id:4},
  ],
  // 5min
  '300':[
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1},
    {label:'500',value:500,id:2},
    {label:'1000',value:1000,id:3},
    {label:'1500',value:1500,id:4},
  ],
  // 10min
  '600':[
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1},
    {label:'500',value:500,id:2},
    {label:'1000',value:1000,id:3},
    {label:'1500',value:1500,id:4},
  ],
  // 30min
  '1800':[
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1},
    {label:'500',value:500,id:2},
    {label:'1000',value:1000,id:3},
    {label:'1500',value:1500,id:4},
  ],
  // 1h
  '3600':[
    {label:lan.getLabel("src.MinimumBytes"),value:5,id:1},
    {label:'500',value:500,id:2},
    {label:'1000',value:1000,id:3},
    {label:'1500',value:1500,id:4},
  ]
}
const allDevModelArray = getAllDevModel(devModelArray); //所有厂商不重复型号
function getAllDevModel(devModelArray) {
  //获取所有厂商不重复型号Fn
  let devModelNewArr = Object.values(devModelArray);
  let allDataArr = [];
  for (let i = 0; i < devModelNewArr.length; i++) {
    for (let j = 0, len = devModelNewArr[i].length; j < len; j++) {
      allDataArr.push(devModelNewArr[i][j]);
    }
  }
  return Array.from(new Set(allDataArr));
}
function getDevModel(param) {
  //获取不同厂商型号，param为devModelArray属性值
  return devModelArray[param];
}
function fluxUnit(data) {
  //流量单位切换
  let num = data;
  let PB = Math.pow(1024, 5),
    TB = Math.pow(1024, 4),
    GB = Math.pow(1024, 3),
    MB = Math.pow(1024, 2),
    KB = Math.pow(1024, 1),
    B = Math.pow(1024, 0);
  if (num / PB >= 1) {
    return (num / PB).toFixed(2) + "PB";
  } else if (num / TB >= 1) {
    return (num / TB).toFixed(2) + "TB";
  } else if (num / GB >= 1) {
    return (num / GB).toFixed(2) + "GB";
  } else if (num / MB >= 1) {
    return (num / MB).toFixed(2) + "MB";
  } else if (num / KB >= 1) {
    return (num / KB).toFixed(2) + "KB";
  } else {
    return (num / B).toFixed(2) + "B";
  }
}
function fluxUnits(data) {
  //流速单位切换
  let num = data;
  let Pbps = Math.pow(1000, 5),
    Tbps = Math.pow(1000, 4),
    Gbps = Math.pow(1000, 3),
    Mbps = Math.pow(1000, 2),
    Kbps = Math.pow(1000, 1),
    bps = Math.pow(1000, 0);
  if (num / Pbps >= 1) {
    return (num / Pbps).toFixed(2) + "Pbps";
  } else if (num / Tbps >= 1) {
    return (num / Tbps).toFixed(2) + "Tbps";
  } else if (num / Gbps >= 1) {
    return (num / Gbps).toFixed(2) + "Gbps";
  } else if (num / Mbps >= 1) {
    return (num / Mbps).toFixed(2) + "Mbps";
  } else if (num / Kbps >= 1) {
    return (num / Kbps).toFixed(2) + "Kbps";
  } else {
    return (num / bps).toFixed(2) + "bps";
  }
}
const getPermission=()=>{
  if (sessionStorage.hasOwnProperty('accessToken')) {
    let accessToken =  JSON.parse(sessionStorage.getItem('accessToken'));
    if (accessToken.funcs) {
      const funcs = accessToken.funcs;
      let hash = window.location.hash.split('/')[ 1 ].split('-')[0],modulesName = hash.toLowerCase();
      // console.log(window.location)
      const  optEventArr = funcs.filter((item)=>{
        return item.routeUrl && item.routeUrl.toLowerCase() === '/'+modulesName
      });
      return optEventArr[ 0 ][ 'btn-'+modulesName ]
    }
  }
};

const getPermissionAll=(modulesName)=>{
  if (sessionStorage.hasOwnProperty('accessToken')) {
    let accessToken =  JSON.parse(sessionStorage.getItem('accessToken'));
    if (accessToken.funcs) {
      const funcs = accessToken.funcs;
      if(modulesName == null || modulesName == undefined){
        let hash = window.location.hash.split('/')[ 1 ].split('-')[0],
        modulesName = hash.toLowerCase();
      }else{
        modulesName = modulesName.toLowerCase();
      }
      const  optEventArr = funcs.filter((item)=>{
        return item.routeUrl && item.routeUrl.toLowerCase() === '/'+modulesName
      });
      return optEventArr;
    }
  }
};

// 转换拨测任务类型
const convertTaskType=(taskType)=>{
  var str = taskType , text = "--";
  switch (str) {
    case 1:
      text = " UDP Trace";
      break;
    case 2:
      text = "ICMP Ping";
      break;
    case 3:
      text = "--";
      break;
    case 4:
      text = "UDP Trace";
      break;
    case 5:
      text = "ICMP Trace";
      break;
    case 6:
      text = "TCP Trace";
      break;
    case 7:
      text = "TCP Trace";
      break;
    case 8:
      text ="--";
      break;
    default:
      text = "--";
      break;
  }
  return text;
};


export default {
  //暴露&返回所有公共参数
  faultList,
  networkfaultList,
  flowFaultList,
  factoryList,
  snmpVersionList,
  unitLists,
  indicatorLists,
  allDevModelArray,
  getDevModel,
  fluxUnit,
  fluxUnits,
  getPermission,
  getPermissionAll,
  messageTypeList,
  templateTypeList,
  operatorList,
  statisticalAngleList,
  packList,
  packSizeList,
  convertTaskType
};

