function loactionReload(link){
    const localstorageMenus = JSON.parse(sessionStorage.getItem('accessToken')).funcs;
    if (localstorageMenus) {
        let toPathMenu = localstorageMenus.filter(item=>item.routeUrl && item.routeUrl.split('/')[1].toLowerCase() == link)[0];let parentMenu = toPathMenu.parentFnId == 0 ? null : localstorageMenus.filter(item=>item.fnId == toPathMenu.parentFnId)[0];
        let level = toPathMenu.fnLevel;
        let topMenu = level==3 ? localstorageMenus.filter(item=>item.fnId == parentMenu.parentFnId)[0]:[];
        let activeParam = {
            parentactiveId:toPathMenu.parentFnId == 0 ? toPathMenu.fnId : level == 3 ? parentMenu.parentFnId : toPathMenu.parentFnId,
            activeId:toPathMenu.parentFnId == 0 ? null : level == 3  ? parentMenu.fnId :toPathMenu.fnId,
            parentFunctionName:parentMenu?(level == 3 ? topMenu.fnName :parentMenu.fnName) : '',
            navName:level == 3   ? parentMenu.fnName : toPathMenu.fnName,
            isdarkSkin:top.window.isdarkSkin
        };
        top.window.vm.$data.preMenu = activeParam.parentFunctionName;
        top.window.vm.$data.activeName = activeParam.navName;
        top.window.vm.$data.parentactiveId = activeParam.parentactiveId;
        top.window.vm.$data.activeId = activeParam.activeId;
        window.parent.postMessage(activeParam,window.location.origin);//window.postMessage() 方法可以安全地实现跨源通信
        let menu = {
            activeId: activeParam.activeId,
            functionUrl: '/'+link,
            navName: activeParam.navName,
            node1: "",
            parentFunctionName: activeParam.parentFunctionName,
            parentactiveId: activeParam.parentactiveId,
            subMenuName: "",
        };
        sessionStorage.setItem("menu",
            JSON.stringify(menu)
        );
    }
}
export default {
    loactionReload
}