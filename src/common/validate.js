const lan = require('./language')

// ip 正侧表达式
const _regexUtils = {
    // ipv4Regex: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
    ipv4Regex: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    ipv6Regex: /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,
};


/*得到ipv6校验表达式*/
function getIpv6Regex() {
    return _regexUtils.ipv6Regex;
}

/*得到ipv4校验表达式*/
function getIpv4Regex() {
    return _regexUtils.ipv4Regex;
}

/*得到natIp校验表达式*/
function validateNatIpRegex(value) {
    if(value.length <= 0){
        return false;
    }
    // 获取第一个下划线的位置
    var result = false,index = value.indexOf("_");
    if(index > 0){
      var str  = value.substring(index+1);
      if(str){
        var strSplit = str.split("_");
        if(strSplit.length != 2){
            result = false;
            return  result; 
        }
        var regex  = /^[1-9]\d*$/;
        try{
            var portStr = strSplit[0];
            if(!regex .test(portStr)){
                result = false;
                return  result; 
            }
            // 提取数字部分并转换为数字
            var port = Number(portStr);
            if(port >= 20 && port <= 60000){
                result = true;
            }else{
                result = false;
                return  result; 
            }
        }catch(e){
            result = false;
            return result;
        }
        var endStr = strSplit[1];
        if(endStr == "T"){
            result = true;
        }else{
            try{
                if(!regex .test(endStr)){
                    result = false;
                    return  result; 
                }
                // 提取数字部分并转换为数字
                var port = Number(endStr);
                if(port > 0){
                    result = true;
                }else{
                    result = false;
                    return  result; 
                }
            }catch(e){
                result = false;
                return result;
            }
        }

      }
    }
    return result;
}

/*时间校验*/
function validateDate(rule, value, callback) {
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if (!value[0]) {
            callback(new Error(lan.getLabel("src.PleaseSelect")));
        } else {
            callback();
        }
    }
}
/*IP校验*/
function validateIp(rule, value, callback) {
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if (_regexUtils.ipv4Regex.test(value) || _regexUtils.ipv6Regex.test(value)) {
            callback();
        } else {
            callback(new Error(lan.getLabel("src.PETCIA")));
        }
    }
}

/*IP校验,必须填写*/
function validateIpMust(rule, value, callback) {

    if (value == '' || value == undefined || value == null) {
        callback(new Error(lan.getLabel("src.PETCIA")));
    } else {
        if (_regexUtils.ipv4Regex.test(value) || _regexUtils.ipv6Regex.test(value)) {
            callback();
        } else {
            callback(new Error(lan.getLabel("src.PETCIA")));
        }
    }
}

/*手机/固定电话校验*/
function validatePhoneTwo(rule, value, callback) {
    const reg = /^((0\d{2,3}-\d{7,8})|(1[34578]\d{9}))$/;;
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if ((!reg.test(value)) && value != '') {
            callback(new Error(lan.getLabel("src.PECPNN")));
        } else {
            callback();
        }
    }
}
/*固定电话校验*/
function validateTelphone(rule, value, callback) {
    const reg = /0\d{2,3}-\d{7,8}/;
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if ((!reg.test(value)) && value != '') {
            callback(new Error(lan.getLabel("src.PETCLN")));
        } else {
            callback();
        }
    }
}
/*传真校验*/
function validatefax(rule, value, callback) {
    const reg = /0\d{2,3}-\d{7,8}/;
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if ((!reg.test(value)) && value != '') {
            callback(new Error(lan.getLabel("src.PETCFN")));
        } else {
            callback();
        }
    }
}
/*手机电话校验*/
function validatePhone(rule, value, callback) {
    const reg = /^1[3|5|7|8]\d{9}$/gi;
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if ((!reg.test(value)) && value != '') {
            callback(new Error(lan.getLabel("src.PETCPN")));
        } else {
            callback();
        }
    }
}
/*身份证校验*/
function validateIdNo(rule, value, callback) {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if ((!reg.test(value)) && value != '') {
            callback(new Error(lan.getLabel("src.PEAVIN")));
        } else {
            callback();
        }
    }
}
/*邮箱校验*/
function validateEMail(rule, value, callback) {
    const reg = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
    if (value == '' || value == undefined || value == null) {
        callback();
    } else {
        if (!reg.test(value)) {
            callback(new Error(lan.getLabel("src.PEYVE")));
        } else {
            callback();
        }
    }
}
/*合法URL校验*/
function validateURL(url) {
    try {
        new URL(url);
    return true; // 如果没有抛出异常，返回true表示URL合法
    } catch (e) {
    console.log(e);
    return false; // 如果抛出异常，返回false表示URL不合法
    }
    return false;
}
/*检验数值的范围校验*/
function checkMax(rule, value, callback) {
    if (value == '' || value == undefined || value == null) {
        callback();
    } else if (!Number(value)) {
        var _msg = lan.getLabel('src.checkMax');
        _msg = _msg.replaceAll(/min/g, rule.min)
        _msg = _msg.replaceAll(/max/g, rule.max)
        callback(new Error(_msg));
    } else if (value < rule.min || value > rule.max) {
        var _msg = lan.getLabel('src.checkMax');
        _msg = _msg.replaceAll(/min/g, rule.min)
        _msg = _msg.replaceAll(/max/g, rule.max)
        callback(new Error(_msg));
    } else {
        callback();
    }
}
/*校验长度*/
function checkLength(rule, value, callback) {
    if (value) {
        if (value.length < rule.minLength || value.length > rule.maxLength) {
            var _msg = lan.getLabel('src.checkLength');
        _msg = _msg.replaceAll(/min/g,rule.minLength || 1)
        _msg = _msg.replaceAll(/max/g, rule.maxLength)
            callback(new Error(_msg));
        } else {
            callback();
        }
    }
    callback();
}
/*数字输入框最大数值*/
function checkMaxVal(rule, value, callback) {
    if (value < 0 || value > 最大值) {
        callback(new Error(lan.getLabel("src.PEANB[MV")));
    } else {
        callback();
    }
}
/*验证是否1-99之间*/
function isOneToNinetyNine(rule, value, callback) {
    if (!value) {
        return callback(new Error(lan.getLabel("src.InputCannotBe")));
    }
    setTimeout(() => {
        if (!Number(value)) {
            callback(new Error(lan.getLabel("src.PEAPI")));
        } else {
            const re = /^[1-9][0-9]{0,1}$/;
            const rsCheck = re.test(value);
            if (!rsCheck) {
                callback(new Error(lan.getLabel("src.PEAPITVI[")));
            } else {
                callback();
            }
        }
    }, 0);
}
/*是否整数*/
function isInteger(rule, value, callback) {
    if (!value) {
        return callback(new Error(lan.getLabel("src.InputCannotBe")));
    }
    setTimeout(() => {
        if (!Number(value)) {
            callback(new Error(lan.getLabel("src.PEAPI")));
        } else {
            const re = /^[0-9]*[1-9][0-9]*$/;
            const rsCheck = re.test(value);
            if (!rsCheck) {
                callback(new Error(lan.getLabel("src.PEAPI")));
            } else {
                callback();
            }
        }
    }, 0);
}
/*是否非负整数*/
function isNonnegativeNumber(rule, value, callback) {
    if (!value) {
        return callback(new Error(lan.getLabel("src.InputCannotBe")));
    }
    setTimeout(() => {
        const reg = /^(0|\+?[1-9][0-9]*)$/;
        if (!reg.test(value)) {
            callback(new Error(lan.getLabel("src.PEANI")));
        } else {
            callback();
        }
    }, 0);
}
/*验证是否整数,非必填*/
function isIntegerNotMust(rule, value, callback) {
    if (!value) {
        callback();
    }
    setTimeout(() => {
        if (!Number(value)) {
            callback(new Error(lan.getLabel("src.PEAPI")));
        } else {
            const re = /^[0-9]*[1-9][0-9]*$/;
            const rsCheck = re.test(value);
            if (!rsCheck) {
                callback(new Error(lan.getLabel("src.PEAPI")));
            } else {
                callback();
            }
        }
    }, 1000);
}
/*验证是否是[0-1]的小数*/
function isDecimal(rule, value, callback) {
    if (!value) {
        return callback(new Error(lan.getLabel("src.InputCannotBe")));
    }
    setTimeout(() => {
        if (!Number(value)) {
            callback(new Error(lan.getLabel("src.PEANB[")));
        } else {
            if (value < 0 || value > 1) {
                callback(new Error(lan.getLabel("src.PEANB[")));
            } else {
                callback();
            }
        }
    }, 100);
}
/* 验证是否是[1-10]的小数,即不可以等于0*/
function isBtnOneToTen(rule, value, callback) {
    if (typeof value == 'undefined') {
        return callback(new Error(lan.getLabel("src.InputCannotBe")));
    }
    setTimeout(() => {
        if (!Number(value)) {
            callback(new Error(lan.getLabel("src.PEAPITVI[")));
        } else {
            if (!(value == '1' || value == '2' || value == '3' || value == '4' || value == '5' || value == '6' || value == '7' || value == '8' || value == '9' || value == '10')) {
                callback(new Error(lan.getLabel("src.PEAPITVI[")));
            } else {
                callback();
            }
        }
    }, 100);
}
/* 密码校验*/
function validatePsdReg(rule, value, callback) {
    if (!value) {
        return callback(new Error(lan.getLabel("src.PleaseEnterPassword")))
    }
    if (!/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){6,20}$/.test(value)) {
        callback(new Error(lan.getLabel("src.PE6ELNOS(SATSBALTTOLNAPM")))
    } else {
        callback()
    }
}
/* 中文校验*/
function validateContacts(rule, value, callback) {
    if (!value) {
        return callback()
    }
    if (rule.maxLength) {
        if (value.length > rule.maxLength) {
            var _msg = lan.getLabel("src.Cannot.exceed") + rule.maxLength + lan.getLabel("src.characters") ;
            callback(new Error(_msg));
        }
    }
    callback()
}
/* 纯数字校验*/
function validateNumber(rule, value, callback) {
    let numberReg = /^\d+$|^\d+[.]?\d+$/
    if (value !== '') {
        if (!numberReg.test(value)) {
            callback(new Error(lan.getLabel("src.PleaseEnterThe")))
        } else {
            callback()
        }
    } else {
        callback(new Error(lan.getLabel("src.PleaseEnterA")))
    }
}

/* 字符串校验   长度  不含特殊字符 */
function validateString(rule, value, callback) {
    // 是否必填
    if (rule.required) {
        if (!value || value === '') {
            callback(new Error(lan.getLabel("src.pleaseEnter") + rule.name))
        }
    }
    // 是否符合规则
    if (value) {
        //  var containSpecial = RegExp(/[(\)(\~)(\!)(\@)(\#) (\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\,)(\.)(\/) (\·)(\`)(\<)(\>)(\?)(\)]+/);
         var containSpecial = RegExp(/[\~\!\@\#\$\%\^\&\*\+\=\|\\\;\:\'\"\,\.\/\·\`\<\>\?\[\]\{\}]+/);
        if (containSpecial.test(value)){
            callback(new Error( rule.name+lan.getLabel("src.CCSC")))
        }
        //是否符合长度
        if (value.length > rule.maxLength) {
            var _msg = lan.getLabel("src.O'+R+'+R+'CAATBE");
            _msg = _msg.replaceAll(/min/g, rule.minLength)
            _msg = _msg.replaceAll(/max/g, rule.maxLength)
            callback(new Error(rule.name + _msg ));
        }
    }
    callback();
}


/* 字符串校验   长度  不含特殊字符 */
function validateStrContainsSpecialChars(rule, value, callback) {
    // 是否必填
    if (rule.required) {
        if (!value || value === '') {
            callback(new Error(lan.getLabel("src.pleaseEnter") + rule.name))
        }
    }
    // 是否符合规则
    if (value) {
        // var containSpecial = RegExp(/[\~\!\@\#\$\%\^\&\*\+\=\|\\\;\:\'\"\,\.\/\·\`\<\>\?\[\]\{\}]+/);
        // if (containSpecial.test(value)){
        //     callback(new Error( rule.name+'不可含有特殊字符'))
        // }
        //是否符合长度
        if (value.length > rule.maxLength) {
            var _msg = lan.getLabel("src.O'+R+'+R+'CAATBE");
            _msg = _msg.replaceAll(/min/g, rule.minLength)
            _msg = _msg.replaceAll(/max/g, rule.maxLength)
            callback(new Error(rule.name + _msg));
        }
    }
    callback();
}
//校验 是否符合code规则 数字 字母只包含
function     vaildCode (rule, value, callback) {

    // 是否必填
    if (rule.required) {
        if (!value || value === '') {
            callback(new Error(lan.getLabel("src.pleaseEnter")  + rule.name))
        }
    }
    // 是否符合规则
    if (value) {
        let formatVal = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{12}$/;
        if (!formatVal.test(value)){
            callback(new Error(lan.getLabel("src.TFCOBA1COLAN")))
        }
        //是否符合长度
        if (value.length > rule.maxLength) {
            callback(new Error(rule.name + lan.getLabel("src.OL'+R+'+R+'CA")));
        }
    }
    callback();
}
//校验 是否符合除了中文的字符
function validateOnlyEnAndNum(rule, value, callback) {
    // 是否必填
    if (rule.required) {
        if (!value || value === '') {
            callback(new Error(lan.getLabel("src.pleaseEnter") + rule.name))
        }
    }
    // 是否符合规则
    if (value) {
        let formatVal = /^[^\u4e00-\u9fa5]*$/;
        if (!formatVal.test(value)){
            callback(new Error(lan.getLabel("src.ONLYNUMLetter")))
        }
        if (value.indexOf(" ") >= 0){
            callback(new Error(lan.getLabel("src.NOTALLOWEDNOSPACE")))
        }
        //是否符合长度
        if (value.length > rule.maxLength) {
            var _msg = lan.getLabel("src.O'+R+'+R+'CAATBE");
            _msg = _msg.replaceAll(/min/g, rule.minLength)
            _msg = _msg.replaceAll(/max/g, rule.maxLength)
            callback(new Error(rule.name + _msg ));
        }
    }
    callback();
}
// 校验 是否有空格
function validBlank(rule,value,callback) {
    console.log(value,'输入的值')
    if(value.length === 0) {
        return
    }
    
    // value.split(" ").join("").length === 0
    // console.log(str)
    let pattern = /\s/
    let str = pattern.test(value)
    if(str) {
        console.log('进入这里')
        callback(new Error(lan.getLabel('src.HostName')))

    }else {
        callback()
    }
}
// 校验是否包含中文
function validChinese(rule,value,callback) {
    console.log(value,rule)
    let reg = /[\u4e00-\u9fa5]/
   const res = reg.test(value)
   console.log(res,'res')
   if(!res) {
    callback()
   }else {
    console.log('有中文')
    callback(new Error(lan.getLabel("src.noChinese")))

   }
// callback()
}

/*mac校验*/
function validatemac(rule, value, callback) {
    if (value == '' || value == undefined || value == null) {
        callback(new Error(lan.getLabel("src.pleaseEnter") + rule.name))
    } else {
        const reg = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
        if ((!reg.test(value)) && value != '') {
            callback(new Error(lan.getLabel("src.PICMAC")));
        } else {
            callback();
        }
    }
}
// 中文两个字符，英文一个字符
function countCharactersWithChineseWeight(value) {
    let count = 0
    for(let char of value) {
       // 判断字符是否为中文
    if (/[\u4e00-\u9fa5]/u.test(char)) {
        // 中文字符，计数加2
        count += 2;
      } else {
        // 其他字符，计数加1
        count += 1;
      }
    }
    return count
}

// 返回之前字符长度的索引值
function getSizeIndexToStr(value , length) {
    let count = 0
    let index = -1;
    for(let char of value) {
       // 判断字符是否为中文
        if (/[\u4e00-\u9fa5]/u.test(char)) {
            // 中文字符，计数加2
            count += 1;
        } else {
            // 其他字符，计数加1
            count += 0.5;
        }

        // 下标加1
        index ++;

        if(count >= length){
            break;
        }
    }

    if(index >= 0){
        return  value.substring(0,index + 1);
    }
    return value
}

/**
 * ipV6 展开
 * @param {*} ipv6Address 
 * @returns 
 */
function expandIpv6(ipv6Address) {
    let expandedAddress = ipv6Address;

    // If the address is compressed, expand it
    if (ipv6Address.includes('::')) {
        let parts = ipv6Address.split('::');
        let firstHalf = parts[0].split(':');
        let secondHalf = parts[1].split(':');

        // Calculate number of missing parts in compressed address
        let missingParts = 8 - (firstHalf.length + secondHalf.length);
        let middle = [];

        for (let i = 0; i < missingParts; i++) {
            middle.push('0000');
        }

        expandedAddress = firstHalf.concat(middle, secondHalf).join(':');
    }

    return expandedAddress;
}

// 校验中文
// function validChinese(rule, value, callback) {
//     // 使用正则表达式匹配中文字符
//     const reg = /[\u4E00-\u9FFF]/;
//     const containsChinese = reg.test(value);

//     if (!containsChinese) {
//         callback();
//     } else {
//         console.log('含有中文字符');
//         callback(new Error(lan.getLabel("src.noChinese")));
//     }
// }



export default {
    validBlank,
    validateDate,
    validateIp,
    validatePhoneTwo,
    validatefax,
    validateTelphone,
    validatePhone,
    validateIdNo,
    validateEMail,
    validateURL,
    checkMax,
    checkLength,
    checkMaxVal,
    isOneToNinetyNine,
    isInteger,
    isNonnegativeNumber,
    isIntegerNotMust,
    isDecimal,
    isBtnOneToTen,
    validatePsdReg,
    validateContacts,
    validateNumber,
    validateString,
    validateStrContainsSpecialChars,
    vaildCode,
    validateOnlyEnAndNum,
    validChinese,
    validatemac,
    expandIpv6,
    countCharactersWithChineseWeight,
    getIpv6Regex,
    getIpv4Regex,
    validateNatIpRegex,
    getSizeIndexToStr
}
