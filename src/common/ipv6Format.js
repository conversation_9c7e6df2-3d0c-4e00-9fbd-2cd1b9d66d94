const lan = require('./language')

//处理ipv6展示换行问题,超过宽度则以当前那个字符做分割，如果是:冒号则分割，如果不是冒号则以前面最近一个:冒号分割
function formatIPv6Address(str, maxWidthTemp) {
    if (str === undefined || str === null || str === '' || str === 'null') {
        return str;
    }
    let maxWidth = maxWidthTemp / 10;
    if (!str || str.length <= maxWidth) {
        return str;
    }
    let result = '';
    let currentWidth = 0;
    let lastColonIndex = -1;
    let segmentStart = 0;

    for (let i = 0; i < str.length; i++) {
        currentWidth++;
        if (str[i] === ':') {
            lastColonIndex = i;
        }
        if (currentWidth > maxWidth) {
            if (str[i] === ':') {
                result += str.slice(segmentStart, i + 1) + '\n';
                segmentStart = i + 1;
                currentWidth = 0;
            } else if (lastColonIndex !== -1 && lastColonIndex > segmentStart) {
                result += str.slice(segmentStart, lastColonIndex + 1) + '\n';
                segmentStart = lastColonIndex + 1;
                currentWidth = i - lastColonIndex;
                lastColonIndex = -1;
            } else {
                result += str.slice(segmentStart, i) + '\n';
                segmentStart = i;
                currentWidth = 1;
            }
        }
    }
    result += str.slice(segmentStart);
    return result;
}

//缩写IPv6格式保留IPv6地址的起始和结束部分（前后各两段），缩写后小于等于22字符的不省略，省略中间的一段
function abbreviateIPv6(ipv6) {
    // debugger
    if (ipv6 === undefined || ipv6 === null || ipv6 === '' || ipv6 === 'null') {
        return ipv6;
    }
    // 如果IPv6地址的长度小于等于22字符，则不省略
    if (ipv6.length <= 22) {
        return ipv6;
    }
    if (ipv6.indexOf(":") < 0) {
        return ipv6;
    }
    // 拆分IPv6地址为段
    let segments = ipv6.split(':');

    // 保留前两段和后两段，省略中间的段
    let abbreviated = `${segments[0]}:${segments[1]}...${segments[segments.length - 2]}:${segments[segments.length - 1]}`;

    return abbreviated;
}


// 处理ipv6展示换行问题，两条链路使用-做分割
function formatIpv6Double(str, maxWidthTemp) {
    // console.log(maxWidthTemp, '最大宽度')
    if (str === undefined || str === null || str === '' || str === 'null') {
        return str;
    }

    let maxWidth = maxWidthTemp / 10;
    if (!str || str.length <= maxWidth) {
        return str;
    }

    let result = ''
    if (str.indexOf('-')) {
        let strArr = str.split('-');
        let str1 = strArr[0];
        let str2 = strArr[1];

        result = str1 + '\n' + '-' + str2;

    } else {
        result = str

    }


    return result;


}
// Mac 列展示
function formatIPv6MAC(str, maxWidthTemp) {
    // console.log(str, maxWidthTemp, '数据')
    if (str === undefined || str === null || str === '' || str === 'null') {
        return str;
    }
    let maxWidth = maxWidthTemp / 7;
    if (!str || str.length <= maxWidth) {
        return str;
    }
    let result = ''
    if (str.indexOf('-')) {
        let strArr = str.split('(');
        let str1 = strArr[0];
        let str2 = strArr[1];

        result = str1 + '\n' + '(' + str2;

    } else {
        result = str

    }
    return result



}


export default {
    formatIPv6Address,
    abbreviateIPv6,
    formatIpv6Double,
    formatIPv6MAC
}