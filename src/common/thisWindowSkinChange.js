//二级iframe页面改变颜色函数
 window.skinChange = function (obj) {
    // console.log(obj)
    const property = Object.keys(obj);
    const color = Object.keys(obj).map(function (i) {
        return obj[i]
    });
    let root = document.documentElement;
    for (let i = 0; i < property.length; i++) {
        root.style.setProperty(property[i], color[i]);
    }
     // window.thisVm.$data.isdarkSkin = top.window.isdarkSkin;
}
