(function skinChange(skinObj){console.log(parent.window.name.indexOf('bigscreen')>-1)
    if (skinObj && typeof skinObj === 'object'){
        if (parent.window.name.indexOf('bigscreen')===-1){
            const property = Object.keys(skinObj);
            const color = Object.keys(skinObj).map(function (i) {
                return skinObj[i]
            });
            let root = document.documentElement;
            for (let i = 0; i < property.length; i++) {
                root.style.setProperty(property[i], color[i]);
            }
        }
    }
})(top.window.skin)
