<template>
  <section class="tree-table">
    <table cellspacing="0" cellpadding="0" border="0" ref="treeTable" style="width: 100%">
      <thead>
        <tr>
          <th
            v-for="(row, index) in columns"
            :style="{ textAlign: row.align, width: row.width + 'px' }"
            :key="index"
          >
            <div v-if="row.type === 'selection'" class="selection">
              <label class="ivu-checkbox-wrapper" @click="handleCheckAll">
                <span
                  class="ivu-checkbox"
                  :class="[checks ? 'ivu-checkbox-checked' : '']"
                >
                  <input
                    type="checkbox"
                    class="ivu-checkbox-input"
                    :class="[checks ? 'ivu-checkbox-wrapper-checked' : '']"
                  />
                  <span class="ivu-checkbox-inner"></span>
                </span>
              </label>
            </div>
            <div v-else class="ivu-table-cell">
              <span v-if="row.sortable" class="ivu-table-cell-sort">
                {{ row.title }}
              </span>
              <span
                v-if="row.sortable"
                class="ivu-table-sort"
                id="treeTablHIcon"
              >
                <i
                  class="ivu-icon ivu-icon-md-arrow-dropup"
                  @click="sortClick($event,row)"
                ></i>
                <i
                  class="ivu-icon ivu-icon-md-arrow-dropdown"
                  @click="sortClick($event,row)"
                ></i>
              </span>
              <span v-else> {{ row.title }} </span>
            </div>
          </th>
        </tr>
      </thead>
      <tbody id="treeTablIcon" ref="treeTablIcon">
        <tr
          v-for="(item, index) in initItems"
          :key="item.key"
          v-show="show(item)"
          :class="{
            'child-tr': item.parent,
            currentPath: item.currentPath == 1
          }"
        >
          <td
            v-for="row in columns"
            :key="row.key"
            :style="{ textAlign: row.align }"
          >
            <div
              class="selection"
              v-if="row.type === 'selection'"
              v-show="item.children"
            >
              <label
                class="ivu-checkbox-wrapper"
                @click="checkedSingle(item.id, item)"
              >
                <span
                  class="ivu-checkbox"
                  :class="[
                    checkGroup.indexOf(item.id) >= 0
                      ? 'ivu-checkbox-checked'
                      : ''
                  ]"
                >
                  <input
                    type="checkbox"
                    class="ivu-checkbox-input"
                    :class="[
                      checkGroup.indexOf(item.id) >= 0
                        ? 'ivu-checkbox-wrapper-checked'
                        : ''
                    ]"
                  />
                  <span class="ivu-checkbox-inner"></span>
                </span>
              </label>
            </div>
            <table-expand
              v-else-if="row.render"
              :row="item"
              :column="row"
              :index="index"
              :render="row.render"
            ></table-expand>
            <div
              v-else
              v-show="item.children"
              @click="toggle(index, item)"
              class="icon-tree"
            >
              <i
                class="ivu-icon "
                :class="{
                  'ivu-icon-ios-remove': item.expanded,
                  'ivu-icon-ios-add': !item.expanded
                }"
              ></i>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="no-data-text" style="line-height: 22px;height: auto" v-if="initItems.length === 0">
      <div :class="{'table_empty':currentSkin==1, 'table2_empty':currentSkin == 0}" ><p class='emptyText' >{{$t('common_No_data')}}</p></div>
    </div>
  </section>
</template>

<script>
import tableExpand from "./expand.js";
export default {
  name: "index",
  props: {
    columns: {
      type: Array,
      default: function() {
        return [];
      }
    },
    data: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  components: {
    tableExpand
  },
  data() {
    return {
       currentSkin: sessionStorage.getItem('dark') || 1,
      copyData: [],
      checks: false,
      checkedObj: {},
      checkGroup: [],
      dataGroup: [],
      checkedState: [],
      checkAll: false,
      initItems: [] //处理后数据数组
    };
  },
  watch: {
    data: {
      handler(val) {
        this.copyData = val;
        this.initData(this.deepCopy(val), 1, null);
      },
      deep: true
    }
  },
  mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
   },
   beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  methods: {
      handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 数据处理 增加自定义属性监听
    initData(items, level, parent) {
      this.initItems = [];
      items.forEach(item => {
        item = Object.assign({}, item, {
          parent: parent,
          level: level
        });
        if (typeof item.expanded == "undefined") {
          item = Object.assign({}, item, {
            expanded: false
          });
        }
        if (typeof item.show == "undefined") {
          item = Object.assign({}, item, {
            isShow: false
          });
        }
        // if (item.children) {
        //     item.children = item.children.map((childrenItem,index)=>{
        //         if (index === 0) {
        //             childrenItem = Object.assign({}, childrenItem, {
        //                 "currentPath": true
        //             })
        //         }else{
        //             childrenItem = Object.assign({}, childrenItem, {
        //                 "currentPath": false
        //             })
        //         }
        //         return childrenItem
        //     })
        // }
        this.initItems.push(item);
        if (item.children && item.expanded) {
          this.initData(item.children, level + 1, item);
        }
      });
    },
    //  隐藏显示
    show(item) {
      return (
        item.level == 1 || (item.parent && item.parent.expanded && item.isShow)
      );
    },
    toggle(index, item) {
      let level = item.level + 1;
      let childrenData = []
      if(!item.expanded){
        this.$http.wisdomPost('/probetask/setChildren',{taskType:item.taskType,parentId:item.id}).then(res=>{
          if(res.code == 1){
            childrenData = res.data
            if (item.children) {
              if (item.expanded) {
                item.expanded = !item.expanded;
                this.close(index, item, childrenData);
              } else {
                item.expanded = !item.expanded;
                if (item.load) {
                  this.open(index, item, childrenData);
                } else {
                  item.load = true;
                  // item.children.forEach
                  childrenData.forEach((child, childIndex) => {
                    this.initItems.splice(index + childIndex + 1, 0, child);
                    //设置监听属性
                    this.$set(this.initItems[index + childIndex + 1], "parent", item);
                    this.$set(this.initItems[index + childIndex + 1], "level", level);
                    this.$set(
                      this.initItems[index + childIndex + 1],
                      "isSpecial",
                      item.isSpecial == true &&
                        (item.configType == 4 ||
                          item.configType == 5 ||
                          item.configType == 7)
                        ? 1
                        : 0
                    );
                    this.$set(
                      this.initItems[index + childIndex + 1],
                      "High",
                      item.configType == 3 ? true : false
                    );
                    this.$set(this.initItems[index + childIndex + 1], "isShow", true);
                    this.$set(
                      this.initItems[index + childIndex + 1],
                      "expanded",
                      false
                    );
                    let routePath = item.routePath || "";
                    let routePathType =
                      routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
                    let routePathType2 =
                      routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
                    if (
                      item.configType == 2 ||
                      item.configType == 5 ||
                      (item.configType == 3 && routePathType == 2) ||
                      (item.configType == 8 && routePathType2 == 2)
                    ) {
                      this.$set(
                        this.initItems[index + childIndex + 1],
                        "sourcePort",
                        ""
                      );
                      this.$set(
                        this.initItems[index + childIndex + 1],
                        "destPort",
                        ""
                      );
                    }
                  });
                }
              }
            }
          }
        })
      }else{
        item.expanded = !item.expanded;
      }
    },
    open(index, item, chilData) {
      if (item.children) {
        chilData.forEach((child, childIndex) => {
          child.isShow = true;
          if (child.children && child.expanded) {
            this.open(index + childIndex + 1, child);
          }
        });
        // item.children.forEach((child, childIndex) => {
        //   child.isShow = true;
        //   if (child.children && child.expanded) {
        //     this.open(index + childIndex + 1, child);
        //   }
        // });
      }
    },
    close(index, item, chilData) {
      if (item.children) {
        chilData.forEach((child, childIndex) => {
          child.isShow = false;
          child.expanded = false;
          if (child.children) {
            this.close(index + childIndex + 1, child);
          }
        });
        // item.children.forEach((child, childIndex) => {
        //   child.isShow = false;
        //   child.expanded = false;
        //   if (child.children) {
        //     this.close(index + childIndex + 1, child);
        //   }
        // });
      }
    },
    // 深度拷贝函数
    deepCopy(data) {
      var t = this.type(data),
        o,
        i,
        ni;
      if (t === "array") {
        o = [];
      } else if (t === "object") {
        o = {};
      } else {
        return data;
      }
      if (t === "array") {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]));
        }
        return o;
      } else if (t === "object") {
        for (i in data) {
          o[i] = this.deepCopy(data[i]);
        }
        return o;
      }
    },
    type(obj) {
      var toString = Object.prototype.toString;
      var map = {
        "[object Boolean]": "boolean",
        "[object Number]": "number",
        "[object String]": "string",
        "[object Function]": "function",
        "[object Array]": "array",
        "[object Date]": "date",
        "[object RegExp]": "regExp",
        "[object Undefined]": "undefined",
        "[object Null]": "null",
        "[object Object]": "object"
      };
      return map[toString.call(obj)];
    },
    checkISAll() {
      this.$nextTick(() => {
        let thisPageID = [];
        let isAll = true;
        for (let i = 0, len = this.copyData.length; i < len; i++) {
          thisPageID.push(this.copyData[i].id);
          if (this.checkGroup.indexOf(this.copyData[i].id) < 0) {
            isAll = false;
          }
        }
        if (isAll) {
          this.checks = true;
        } else {
          this.checks = false;
        }
      });
    },
    //单选事件
    checkedSingle(id, item) {
      let _self = this,
        parentArray = [],
        idIndex = _self.checkGroup.indexOf(id),
        indexChildren = null;
      if (idIndex >= 0) {
        // 如果已经包含了该id, 则去除(单选按钮由选中变为非选中状态)
        _self.checkGroup.splice(idIndex, 1);
        _self.dataGroup.splice(idIndex, 1);
        _self.checkedState.splice(idIndex, 1);
        // if(item.children){
        //     let array=this.All(item.children);
        //     for(let i=0;i<array.length;i++){
        //         indexChildren=_self.checkGroup.indexOf(array[i]);
        //         _self.checkGroup.splice(indexChildren, 1);
        //         _self.checkedState.splice(indexChildren, 1);
        //     }
        // }
        // if(item.parent !=null){
        //     let parentIndex=_self.checkGroup.indexOf(item.parent.id);
        //     if(parentIndex>=0){
        //         _self.checkGroup.splice(parentIndex, 1);
        //         _self.checkedState.splice(parentIndex, 1);
        //     }
        // }
      } else {
        // 选中该checkbox
        // if(item.children){
        //     this.checkGroup = this.getArray(this.checkGroup.concat(this.All(item.children)));
        //     this.checkedState = this.getArray(this.checkedState.concat(this.All(item.children)));
        // }
        _self.checkGroup.push(id);
        _self.dataGroup.push(item);
        _self.checkedState.push(id);
        // if(item.parent !=null){
        //     for(let i=0;i<item.parent.children.length;i++){
        //         if(this.checkGroup.indexOf(item.parent.children[i].id)>=0){
        //             parentArray.push(item.parent.children[i].id)
        //         }
        //     }
        //     if(parentArray.length===item.parent.children.length){
        //         _self.checkGroup.push(item.parent.id);
        //         _self.checkedState.push(item.parent.id);
        //     }
        // }
      }
      this.$emit('boxClick');
      this.checkISAll();
    },
    //checkbox 全选 选择事件
    handleCheckAll() {
      this.checks = !this.checks;
      if (this.checks) {
        this.checkGroup = this.getArray(
          this.checkGroup.concat(this.All(this.initItems))
        );
        this.dataGroup = this.getArrayData(
          this.dataGroup.concat(this.AllData(this.initItems))
        );
        this.checkedState = this.getArray(
          this.checkedState.concat(this.All(this.initItems))
        );
      } else {
        this.checkGroup = [];
        this.dataGroup = [];
        this.checkedState = [];
        this.checkedObj = {};
      }
      this.$emit('boxClick');
    },
    // 数组去重
    getArray(a) {
      let hash = {},
        len = a.length,
        result = [];
      for (let i = 0; i < len; i++) {
        if (!hash[a[i]]) {
          hash[a[i]] = true;
          result.push(a[i]);
        }
      }
      return result;
    },
    getArrayData(a) {
      let hash = {},
        len = a.length,
        result = [];
      for (let i = 0; i < len; i++) {
        if (!hash[a[i].id]) {
          hash[a[i].id] = true;
          result.push(a[i]);
        }
      }
      return result;
    },
    All(data) {
      let arr = [];
      data.forEach(item => {
        arr.push(item.id);
        // if (item.children && item.children.length > 0) {
        //     arr = arr.concat(this.All(item.children));
        // }
      });
      return arr;
    },
    AllData(data) {
      let arr = [];
      data.forEach(item => {
        arr.push(item);
        // if (item.children && item.children.length > 0) {
        //     arr = arr.concat(this.All(item.children));
        // }
      });
      return arr;
    },
    getIds() {
      return this.checkGroup;
    },
    getDatas() {
      return this.dataGroup;
    },
    cancelIcon() {
      let iconTbody = document.getElementById("treeTablHIcon");
      let myIconClass = iconTbody.getElementsByTagName("i");
      myIconClass[0].className = "ivu-icon ivu-icon-md-arrow-dropup";
      myIconClass[1].className = "ivu-icon ivu-icon-md-arrow-dropdown";
    },
    sortClick(event,z) {
      const oldClass = event.srcElement.className
      let allSortcolum = document.getElementsByClassName('ivu-table-sort');
     
      for(let i = 0;i<allSortcolum.length;i++){
        allSortcolum[ i ].childNodes[ 0 ].className = 'ivu-icon ivu-icon-md-arrow-dropup';
        allSortcolum[ i ].childNodes[ 1 ].className = 'ivu-icon ivu-icon-md-arrow-dropdown';
      }
      let newClass = "",
      orderBy = "";
      if (oldClass.lastIndexOf(" on") >= 0) {
        // let onIndex = event.srcElement.className.lastIndexOf(' on');
        // newClass = event.srcElement.className.substring(0,onIndex);
        this.cancelIcon();
        orderBy = "normal";
      } else {
        this.cancelIcon();
        newClass = oldClass + " on";
        if (
          event.srcElement.className.indexOf("ivu-icon-md-arrow-dropup") > -1
        ) {
          orderBy = "asc";
        }
        if (
          event.srcElement.className.indexOf("ivu-icon-md-arrow-dropdown") > -1
        ) {
          orderBy = "desc";
        }
        event.srcElement.className = newClass;
      }

      /*url_num*/
      this.$emit("sortParent", { order: orderBy, key: z.key });
    },
    clearData() {
      this.checks = false;
      this.checkGroup = [];
      this.dataGroup = [];
    }
  }
};
</script>
<style scoped>
  .tree-table th,.tree-table td{
    height: 50px;
  }
.tree-table th .ivu-table-cell {
  padding: 0 !important;
}
.tree-table table .currentPath td {
  color: #00cc66!important;
}
.tree-table .currentPath {
}
  .no-data-text{
    line-height: 50px;
  }
</style>
