/**
 *  @mousedown.native="move"
 *  move(e){
        drag(e);
      },
 * 鼠标按下事件 处理程序
 */ 
export function drag(e){
    selectstart();
    let element = document.querySelectorAll('.ivu-modal-content')[1]
    let dX = getComputedStyle(element).getPropertyValue('left')
    let dY = getComputedStyle(element).getPropertyValue('top')
    let disX = parseFloat(e.clientX) - parseFloat(dX);
    let disY = parseFloat(e.clientY)  - parseFloat(dY);

    document.onmousemove = (e)=>{       //鼠标按下并移动的事件
        //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        let left = e.clientX - disX;    
        let top = e.clientY - disY;
        //移动当前元素
        element.style.left = left+ 'px';
        element.style.top = top + 'px';
    };

    document.onmouseup = (e) => {
        document.onmousemove = null;
        document.onmouseup = null;
    };
    
}

let selectstart = function (){
    document.onselectstart = function() { return false; };//解决拖动会选中文字的问题
    document.ondragstart = function() { return false; };
}

//模态框拖拽 不超出top
export function addDraggable() {
    let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver
    let elementAll = document.querySelectorAll('.ivu-modal-content');
    for(let i = 0; i < elementAll.length; i++){
        let element = document.querySelectorAll('.ivu-modal-content')[i]
        let observer = new MutationObserver((mutationList) => {
            let top = getComputedStyle(element).getPropertyValue('top')
            let left = getComputedStyle(element).getPropertyValue('left')
            if(top[0] == '-'){
              element.style.top = '2px'
            }
            // if(left[0] == '-'){
            //   element.style.left = '2px'
            // }
        })
        observer.observe(element, { attributes: true, attributeFilter: ['style'], attributeOldValue: true })
    }
}