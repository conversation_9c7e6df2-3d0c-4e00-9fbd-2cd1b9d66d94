<!--
*@avg_delay             时延
*@avg_loss_value        丢包
*@isSpecial             0、不是专线，1、是专线
*@isBroken              0、正常 ，1、中断
*@isBuquan              0、不是补全，1、是补全
*@ip1                   ip地址
-->
<template>
  <section class="flowChart-content" ref="flowChartItem">
    <div class="flowChart-noData" v-if="flow_chart.length === 0">
      拓扑图暂无数据
    </div>
    <div class="flowChart-overflow" v-else :style="{ width: flowChartWidth }">
      <div
        class="flowChart-box"
        v-for="(item, index) in flow_chart"
        :class="[index < flow_chart.length - 1 ? 'flowChart-margin' : '']"
        :key="index"
      >
        <div class="flowChart-content-box flowChart-img-box">
          <div @mouseenter="enter(index)" @mouseleave="leave()">
            <div
              class="flowChart-img"
              :class="[!lastType ? 'flowChart-pointer' : '']"
              @click="
                iconClick(
                  item,
                  index,
                  flow_chart[0],
                  flow_chart[flow_chart.length - 1]
                )
              "
              v-if="index === 0"
            >
              <img src="../../assets/wisdom/zhongduan_1.png" />
            </div>
            <div
              class="flowChart-img"
              :class="[
                !lastType && item.isBuquan === 0 ? 'flowChart-pointer' : ''
              ]"
              @click="
                iconClick(
                  item,
                  index,
                  flow_chart[0],
                  flow_chart[flow_chart.length - 1]
                )
              "
              v-else-if="item.isSpecial === 1"
            >
              <img
                v-if="item.isBuquan === 0"
                src="../../assets/wisdom/luyouqi.png"
              />
              <img v-else src="../../assets/wisdom/luyouqi_2.png" />
            </div>
            <div
              class="flowChart-img"
              :class="[
                lastType || index === flow_chart.length - 1
                  ? 'flowChart-pointer'
                  : ''
              ]"
              @click="
                iconClick(
                  item,
                  index,
                  flow_chart[0],
                  flow_chart[flow_chart.length - 1]
                )
              "
              v-else-if="index === flow_chart.length - 1"
            >
              <img
                v-if="item.isBuquan === 0"
                src="../../assets/wisdom/fuwuqi_1.png"
              />
              <img v-else src="../../assets/llt/end_invalid.png" />
            </div>
            <div
              class="flowChart-img"
              :class="[
                !lastType && item.isBuquan === 0 ? 'flowChart-pointer' : ''
              ]"
              @click="
                iconClick(
                  item,
                  index,
                  flow_chart[0],
                  flow_chart[flow_chart.length - 1]
                )
              "
              v-else
            >
              <img
                v-if="item.isBuquan === 0"
                src="../../assets/wisdom/luyouqi_1.png"
              />
              <img v-else src="../../assets/wisdom/luyouqi_2.png" />
            </div>
            <div class="flowChart-ip">{{ item.ip1 }}</div>
            <div class="flowChart-title" v-if="index === 0">源</div>
            <div
              class="flowChart-title"
              v-else-if="index === flow_chart.length - 1"
            >
              目的
            </div>
            <div class="flowChart-title" v-else-if="item.isSpecial === 1">
              专线
            </div>
          </div>
          <div
            class="flowChart-img-mask"
            v-if="item.ip1 === '*' && img_mask === index"
          >
            <div class="flowChart-mask-box">
              <label>节点1_IP：</label>
              <div class="flowChart-mask-text">
                {{ flow_chart_data[0].ip1 }}
              </div>
            </div>
            <div class="flowChart-mask-box">
              <label>节点2_IP：</label>
              <div class="flowChart-mask-text">
                {{ flow_chart_data[1].ip1 }}
              </div>
            </div>
            <div class="flowChart-mask-box">
              <label>平均时延：</label>
              <div class="flowChart-mask-text">
                {{
                  flow_chart_data[1].avg_delay &lt; 1
                    ? "&lt;1"
                    : Number(flow_chart_data[1].avg_delay).toFixed(2)
                }}ms
              </div>
            </div>
            <div class="flowChart-mask-box loss-mask-box" v-if="lossShow">
              <label>平均丢包率：</label>
              <div class="flowChart-mask-text">
                {{ flow_chart[index + 1].avg_loss_value }}%
              </div>
            </div>
          </div>
        </div>
        <div
          class="flowChart-content-box flowChart-line-box"
          v-if="index < flow_chart.length - 1"
        >
          <div
            class="flowChart-delay flowChart-index"
            v-if="
              flow_chart[index + 1].ip1 != '*' &&
                item.ip1 != '*' &&
                flow_chart[index + 1].avg_delay != null &&
                flow_chart[index + 1].isBuquan != 1
            "
          >
            <!--:class="[flow_chart[index+1].isBroken===2?'color-yellow':'']"-->
            {{
              parseFloat(flow_chart[index + 1].avg_delay) &lt; 1
                ? "&lt;1ms"
                : parseFloat(flow_chart[index + 1].avg_delay) + "ms"
            }}
          </div>
          <div
            class="flowChart-ban flowChart-pointer"
            v-if="flow_chart[index + 1].isBroken === 1"
            @click="lineClick(index, flow_chart[index + 1])"
            @mouseenter="lineEnter(index, flow_chart[index + 1])"
            @mouseleave="lineLeave()"
          ></div>
          <div
            class="flowChart-line"
            @click="lineClick(index, flow_chart[index + 1])"
            :class="[
              line_index.indexOf(index) > -1 ? 'flowChart-line-hover' : '',
              line_click_index.indexOf(index) > -1
                ? 'flowChart-line-hover'
                : '',
              flow_chart[index + 1].isBuquan === 0 ? 'flowChart-pointer' : '',
              flow_chart[index + 1].isBuquan === 1 ? 'flowChart-line-gray' : ''
            ]"
            @mouseenter="lineEnter(index, flow_chart[index + 1])"
            @mouseleave="lineLeave()"
          ></div>
          <div
            class="flowChart-loss flowChart-index"
            v-if="
              lossShow &&
                flow_chart[index + 1].ip1 != '*' &&
                item.ip1 != '*' &&
                flow_chart[index + 1].avg_loss_value != null &&
                flow_chart[index + 1].isBuquan != 1
            "
          >
            <!-- :class="[flow_chart[index+1].type===2?'color-yellow':'']"-->
            {{ flow_chart[index + 1].avg_loss_value }}%
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "index",
  props: {
    lastType: {
      //最后一个移入
      type: Boolean,
      default: false
    },
    lossShow: {
      //是否显示丢包率
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      flow_chart: [],
      img_mask: null,
      flow_chart_data: [],
      line_index: [],
      line_click_index: []
    };
  },
  watch: {
    data: {
      handler(val) {
        this.flow_chart = [...val];
      },
      deep: true
    }
  },
  computed: {
    //计算流程图总宽度
    flowChartWidth() {
      return (110 + 96) * (this.flow_chart.length - 1) + 110 + "px";
    }
  },
  methods: {
    //图标事件
    iconClick(currentData, index, startRow, endRow) {
      this.line_click_index = [];
      let obj = {
        type: 1,
        data: [startRow, endRow],
        index: index,
        currentData: currentData
      };
      this.$emit("on-click", obj);
      // if(currentData.isBuquan===0){
      //     this.line_click_index=[];
      //     let obj={
      //         type:1,
      //         data:[startRow,endRow],
      //         index:index,
      //     };
      //     this.$emit("on-click",obj)
      // }
    },
    //虚线事件
    lineClick(index, data) {
      if (data.isBuquan === 0) {
        let array = this.flow_chart,
          data = [];
        this.line_click_index = [];
        if (array[index].ip1 === "*" || array[index + 1].ip1 === "*") {
          data = this.flowChartNodeId(index);
          this.line_click_index = this.lineHover(index);
        } else {
          data = [array[index], array[index + 1]];
          this.line_click_index.push(index);
        }
        let obj = {
          type: 2,
          data: data
        };
        this.$emit("on-click", obj);
      }
    },
    //图标移入移出事件
    enter(index) {
      let _self = this;
      this.img_mask = index;
      if (_self.flow_chart[index].ip1 === "*") {
        this.flowChartNodeId(index);
      }
    },
    leave() {
      this.img_mask = null;
      this.flow_chart_data = [];
    },
    //获取ip为*的开始及结束数据
    flowChartNodeId(index) {
      let _self = this,
        array = _self.flow_chart,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].ip1 === "*") {
          if (i < index && array[i].ip1 != "*") {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*") {
            end.push(array[i]);
          }
        } else if (array[index + 1].ip1 === "*") {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*") {
            end.push(array[i]);
          }
        }
      }
      this.flow_chart_data = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //横线移入移出事件
    lineEnter(index, data) {
      if (this.hoverType && data.isBuquan === 0) {
        let array = this.flow_chart;
        if (array[index].ip1 === "*" || array[index + 1].ip1 === "*") {
          this.lineHover(index);
        } else {
          this.line_index.push(index);
        }
      }
    },
    lineLeave() {
      this.line_index = [];
    },
    //渲染横线移入数据
    lineHover(index) {
      let array = this.flow_chart;
      let lineData = this.flowChartNodeId(index);
      let startIndex = array.indexOf(lineData[0]),
        endIndex = array.indexOf(lineData[1]),
        indexArray = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (i >= startIndex && i < endIndex) {
          indexArray.push(i);
        }
      }
      this.line_index = indexArray;
      return indexArray;
    },
    //清除数据
    resetData() {
      this.flow_chart = [];
      this.flow_chart_data = [];
      this.line_index = [];
      this.line_click_index = [];
      this.img_mask = null;
    }
  },
  mounted() {
    this.flow_chart = [...this.data];
  }
};
</script>
