<!--
*@props       @iconType   {Boolean}       图标是否显示手指样式(默认为true)
              @lastType   {Boolean}       是否最后一个图标显示手指样式（如何iconType存在，lastType无效,默认false）
              @delayShow  {Boolean}       是否显示时延(默认为true)
              @lossShow   {Boolean}       是否显示丢包率(默认为true)
              @data       {Array}         返回数据参数\
*@data        @avgDelay                  时延
              @avgLossValue             丢包
              @isSpecial                  0、不是专线，1、是专线
              @isBroken                   0、正常 ，1、中断，2、时延劣化，3、丢包劣化 4 、中断才有的ip段
              @isBuquan                   0、不是补全，1、是补全
              @ip1                        ip地址
              @deviceType                 二层设备图标类型（服务器	1，交换机	2，路由器	3，防火墙	4，未知	5）
              @deviceTypeCss              二层设备图标类型（服务器	two-icon-1，交换机	two-icon-2，路由器	two-icon-3，防火墙	two-icon-4，未知	two-icon-5 ， 虚拟交换机  virtual-node-icon）
*@events      @on-click                   图标、虚线点击事件
*@on-click    @type                      1、图标，2、虚线
              @index                     当前位置下标
              @column                    当前数据（图标当前，虚线开头）
              @data                      返回数据
              @suspect                   不可信节点
              2021/2/24 19:05 劣化颜色标注去掉补全判断:
              （32行）：(item.isBroken === 2 || item.isBroken === 3) && item.isBuquan === 0
              (81-83行)：(topology[index + 1].isBroken === 2 ||
              topology[index + 1].isBroken === 3) &&
            topology[index + 1].isBuquan === 0
-->
<template>
  <section class="topology-x" style="min-height: 150px">
    <div class="topology-noData" v-if="topology.length === 0">
      <!-- 拓扑图暂无数据 -->
      {{$t('phytopo_No_data')}}
      
    </div>
    <div class="topology-content" :style="{ width: flowChartWidth ,margin:'15 auto' }" v-else>

      <div
        class="topology-box"
        v-for="(item, index) in topology"
        :class="[
          item.nodeColor === 'r'
            ? 'error-state'
            : '',
          item.nodeColor === 'y'
            ? 'inferior-state'
            : '',
            item.nodeColor === 'g'
            ? 'repair-state'
            : ''
        ]"
        :key="index"
      >
        <div
          class="icon-text"
          @mouseenter="iconMoveIn(index)"
          @mouseleave="iconMoveOut()"
        >
          <i
            :style="{
              'border-style':dashShow?'dashed':'none'
            }"
            :class="[
              index === 0 ? 'icon-route' : '',
              index === topology.length - 1 ? 'icon-route' : '',
              index > 0 && index < topology.length - 1 ? (item.deviceTypeCss || 'icon-route') : '',
              iconType && index != 0 ? 'icon-hover' : '',
              lastType && index != 0 ? 'icon-hover' : '',
              icon_click_index===index ? 'icon-click' : ''
            ]"
            @click="
              iconClick(item, index, topology[0], topology[index])
            "
          ></i>
          <div class="text">{{ item.ip1 }}</div>
          <div class="text fontHidden" :title="item.deviceName+ (index === 0
                ? '' 
                 : index === topology.length - 1
                ? item.isSpecial === 1
                  ? ''
                  : ''
                : item.isSpecial === 1
                ? ''
                : '') ">
   
         
            <div v-if="item.isShow">
                <span v-html="item.deviceName?item.deviceName:''"></span>               
            {{
              index === 0
                ? ''
                : index === topology.length - 1
                ? item.isSpecial === 1
                  ? ''
                  : ''
                : item.isSpecial === 1
                ? ""
                : ""   
            }}
            </div>
          </div>
           <topologyEditVue 
         
            v-show="item.ip1 != '*' && index !== 0 && index !== topology.length - 1"
            :nodeItem="item" 
            ref="topologyRef" 
            @addOk="addNameOk"
            @editName="editName"
            :isPoToPo="isPoToPo"
            :nodeIndex = "index"
            :linkData="linkData"></topologyEditVue>
        </div>
        <div
          class="line-box"
          v-if="index < topology.length - 1"
          :class="[
            topology[item.peertopeerType == 0? index :index + 1].lineColor === 'r'
              ? 'error-line-state'
              : '',
            topology[item.peertopeerType == 0? index :index + 1].lineColor === 'y'
              ? 'inferior-line-state'
              : '',
              topology[item.peertopeerType == 0? index :index + 1].lineColor === 'g'
              ? 'repair-line-state'
              : '',
            line_click_index.indexOf(index) > -1 ? 'ine-click' : ''
          ]"
          @click="lineClick(item, index ,topology[index + 1])"
        >
          <div class="text delay">
            <span
              v-if="
                (delayShow &&
                  topology[index + 1].lineColor != 'r' &&
                  topology[index + 1].suspect != true) &&
                 !(topology[index].suspect == true && topology[index + 1].suspect != true)
              "
              v-show="topology[index + 1].ip1 != '*' && item.ip1 != '*'"
            >
              {{
                topology[index + 1].lineColor === 'g' ||
                topology[index + 1].avgDelay === null ||
                topology[index + 1].avgDelay === "" ||
                topology[index + 1].avgDelay === undefined
                  ? topology[index].peertopeerShowDelayLoss != true ? "" :""
                  : (topology[index + 1].avgDelay &lt; 1)
                  ? ""
                  : ""
              }}
            </span>
          </div>
          <div class="line"></div>
          <div class="text loss">
            <span
              v-if="
                lossShow &&
                  topology[index + 1].lineColor != 'r' &&
                  topology[index + 1].suspect != true &&
                 !(topology[index].suspect == true && topology[index + 1].suspect != true)
              "
              v-show="topology[index + 1].ip1 != '*' && item.ip1 != '*'"
            >
              {{
                topology[index + 1].lineColor === 'g' ||
                topology[index + 1].avgLossValue === null ||
                topology[index + 1].avgLossValue === "" ||
                topology[index + 1].avgLossValue === undefined
                  ? topology[index].peertopeerShowDelayLoss != true ? "" :""
                  : topology[index + 1].avgLossValue+""
              }}
            </span>
          </div>
          <div
            class="icon-error"
            v-if="
              (topology[index + 1].peertopeerLineType !=0 && topology[index + 1].linkPoint) || ((topology[index+1].peertopeerLineType ==0 ||topology[index].peertopeerLineType ==0) && topology[index].linkPoint) === true
            "
          ></div>
          <div
              v-if="
              topology[index + 1].isSpecial == 1
            "
              class="text"
              style="margin-top:9px;"
          >
            <span>{{$t('flowChart_srl')}}</span>
          </div>
          <div style="color:#fff !important;height:5px"> {{(item.ip1 === '*' || item.suspect)  }} {{iconIndex === index}}</div>
         

          <div class="mask" v-if="(item.ip1 === '*' || item.suspect) && iconIndex === index ">

            <div class="mask-box">
              <label>{{$t('flowChart_n1')}}：</label>
              <div class="mask-text">{{ topology_mask[0].ip1 || "" }}</div>
            </div>
            <div class="mask-box">
              <label>{{$t('flowChart_n2')}}：</label>
              <div class="mask-text">{{ topology_mask[1].ip1 || ""  }}</div>
            </div>
            <div class="mask-box" v-if="delayShow">
              <label>{{$t('dash_average_delay')}}：</label>
              <div class="mask-text">
                {{
                  topology_mask[1].avgDelay === null
                    ? ""
                    : topology_mask[1].avgDelay &lt; 1
                    ? "&lt;1"
                    : parseFloat(topology_mask[1].avgDelay).toFixed(2)
                }}
              </div>
            </div>
            <div class="mask-box" v-if="lossShow">
              <label>{{$t('dash_average_packet_loss')}}：</label>
              <div class="mask-text loss-mask-test">
                {{
                  topology_mask[1].avgLossValue === null
                    ? "--"
                    : topology_mask[1].avgLossValue
                }}
              </div>
            </div>
        </div>
        
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import topologyEditVue from './topologyEdit.vue';
export default {
  name: "topology",
  components: {
    topologyEditVue
  },
  props: {
    iconType: {
      type: Boolean,
      default: true
    },
    lastType: {
      type: Boolean,
      default: false
    },
    delayShow: {
      type: Boolean,
      default: true
    },
    lossShow: {
      type: Boolean,
      default: true
    },
    dashShow: {
      type: Boolean,
      default: true
    },
    degradation_type: {},
    data_source: {},
    data: {
      type: Array,
      default: function() {
        return [];
      }
    },
    linkData: {
      type:Array,
      default: function() {
        return []
      }
    },
    // 区分端对端
    isPoToPo: {
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      topology: [],
      topology_mask: [],
      iconIndex: null,
      line_click_index: [],
      icon_click_index: null,
      // 节点编辑
      isEdit:false,
      nodeObj:{},
     
    };
  },
 
  watch: {
    data: {
      handler(val) {
        if (val.length === 0) {
          this.resetData();
        } else {
          let dataJson = JSON.parse(JSON.stringify(val));
          for (let index = 0 , size = dataJson.length; index < size; index++) {
            const element = dataJson[index];
            element.deviceTypeCss = "";
            // 设置二层设备的图标
            if(element.deviceType != null ){
                element.deviceTypeCss = "two-icon";
            }
            // 设置虚拟主机的图标
            if(element.virtualNode == 1 ){
              element.deviceTypeCss = "virtual-node-icon";
            }
          }
          this.topology = dataJson;
          console.log(this.topology,'数据。。。。。。。。。')
          this.topology.forEach(item => {return this.$set(item,'isClick', false)})
           this.topology.forEach(item => this.$set(item,'isShow', true))
        }
      },
      deep: true
    },
    degradation_type: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    },
    data_source: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    }
  },
  computed: {
    
    linkDataFin(){
      if(this.linkData.length === 0) {
        return this.linkDatas
      }else {
        return this.linkData
      }

    },
    //计算流程图总宽度
    flowChartWidth() {
      return (100 + 98) * (this.topology.length - 1) + "px";
    }
  },
  methods: {
    //图标事件
    iconClick(column, index, startRow, endRow) {
     console.log(column, index, startRow, endRow)
    //  this.$refs.topologyRef[0].linkId = this.linkId
    if(this.linkData.length == 0) {
      console.log(this.$refs.topologyRef[index])
    
    
    }
    this.$refs.topologyRef[index].changeEdit()
    console.log(this.$refs.topologyRef[index],'111111111')
    this.topology.forEach(item => item.isShow = true)
     this.topology.forEach(item => {
      if(item.ip1 === column.ip1) {
        item.isClick = true
      }else {
        item.isClick = false
      }
     })
    //  console.log(this.topology,'为什么没变化')
      this.line_click_index = [];
      this.icon_click_index = index;
      let array = this.topology;
      var data = [startRow, endRow];
      if(array[index].suspect == true){
         data = this.handleSuspectClick(index)
      }
      let obj = {
        type: 1,
        index: index,
        column: column,
        data: data
      };
      this.$emit("on-click", obj);
    },
    //图标移入移出事件
    iconMoveIn(index) {
      let _self = this;
      this.iconIndex = index;
      if (_self.topology[index].ip1 === "*") {
        console.log('------------');
        this.topology_mask = this.topologyNodeId(index);
        console.log('this.topology_mask',this.topology_mask);
      }else if(_self.topology[index].suspect == true){
        this.topology_mask  = this.handleSuspectClick(index)
      }
    },
    iconMoveOut() {
      this.iconIndex = null;
      this.topology_mask = [];
    },
    //虚线事件
    lineClick(column, index,columnlater) {
      this.line_click_index = [];
      this.icon_click_index = null;
      let array = this.topology,
        data = [];
        var pointIndex = null,pointArr = [];
        if(array[index].ip1 === "*" || array[index].suspect == true){
          pointIndex = index;
        }
        if(array[index+1].ip1 === "*" || array[index+1].suspect == true){
          pointIndex = index+1;
        }
        if(pointIndex && pointIndex>=0){
         for (let indexs = pointIndex; indexs < array.length; indexs++) {
            if(array[indexs].ip1!='*'&&array[indexs].suspect!=true){
              break
            }
            if (array[indexs].ip1=='*'||array[indexs].suspect==true) {
              pointArr.push(indexs);
            }
          }
          for (let indexs = pointIndex; indexs > 0; indexs--) {
            if(array[indexs].ip1!='*'&&array[indexs].suspect!=true){
              break
            }
            if (array[indexs].ip1=='*'||array[indexs].suspect==true) {
              pointArr.push(indexs);
            }
          }
        }
        if(pointArr.length>0){
          pointArr.sort((a, b)=>{return a - b}); 
          pointArr.push(pointArr[0]-1);
          pointArr = Array.from(new Set(pointArr));
          this.line_click_index = pointArr;
        }
        
      if (array[index].ip1 === "*" || array[index].suspect == true ) {
        data = this.topologyNodeIdIndex(index);
      }else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
        data = this.topologyNodeIdIndex(index);
      } else {
        data = [array[index], array[index + 1]];
        this.line_click_index.push(index);
      }
      let obj = {
        type: 2,
        index: index,
        column: column,
        data: data,
        suspect: columnlater.suspect
      };
      this.$emit("on-click", obj);
    },
    //获取ip为*，不可信节点的开始及结束数据
    topologyNodeIdIndex(index) {
      let _self = this,
        array = _self.topology,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].ip1 === "*" || array[index].suspect == true) {
          if (i < index && array[i].ip1 != "*"  && array[i].suspect != true) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*"  && array[i].suspect != true) {
            end.push(array[i]);
          }
        } else if (array[index + 1].ip1 === "*"  || array[index + 1].suspect == true) {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
            end.push(array[i]);
          }
        }
      }
      this.topology_mask = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //获取ip为*的开始及结束数据
    topologyNodeId(index) {
      // debugger
      let _self = this,
        array = _self.topology,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].ip1 === "*") {
          if (i < index && array[i].ip1 != "*" && array[i].suspect != true) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
            end.push(array[i]);
          }
        } else if (array[index + 1].ip1 === "*" ) {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*") {
            end.push(array[i]);
          }
        }else if ( array[index].suspect){
          if (i <= index) {
            start.push(array[i-1]);
          }
          if (i > index ) {
            end.push(array[i]);
          }
        }
      }
      this.topology_mask = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //渲染虚线移入数据
    lineHover(index) {
      let array = this.topology;
      let lineData = this.topologyNodeId(index);
      let startIndex = array.indexOf(lineData[0]),
        endIndex = array.indexOf(lineData[1]),
        indexArray = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (i >= startIndex && i < endIndex) {
          indexArray.push(i);
        }
      }
      return indexArray;
    },
    //清除数据
    resetData() {
      this.topology = [];
      this.topology_mask = [];
      this.iconIndex = null;
      this.line_click_index = [];
      this.icon_click_index = null;
    },

    handleSuspectClick(index){
      let array = this.topology;
      var data = [];   
      if (array[index].suspect == true ) {
        data = this.topologyNodeIdIndex(index);
      }else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
        data = this.topologyNodeIdIndex(index);
      } else {
        data = [array[index], array[index + 1]];
      }
      return data;
    },
    // 新增名字成功
    addNameOk(obj) {
      console.log('成功了',obj)
      this.topology.forEach(item => item.isShow = true)
      this.topology.forEach(item => {
        if(item.ip1 === obj.ip1) {
        item.deviceName = obj.text
          
        }   
        item.isClick = false

        }
      )
        console.log(this.topology)


    },
    editName(obj) {
      this.nodeObj = obj
      this.topology.forEach(item => {
       if(item.ip1 === obj.ip1) {
        item.isShow = false
       }
      })
      

    }
  },
  mounted() {
    
    
    if (this.data.length === 0) {
      this.resetData();
    } else {
      this.topology = JSON.parse(JSON.stringify(this.data));
      console.log(this.topology)
     
      
    }
  }
};
</script>
<style scoped lang="less">

</style>
