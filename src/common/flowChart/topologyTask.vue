<!--
*@props       @iconType   {Boolean}       图标是否显示手指样式(默认为true)
              @lastType   {Boolean}       是否最后一个图标显示手指样式（如何iconType存在，lastType无效,默认false）
              @delayShow  {Boolean}       是否显示时延(默认为true)
              @lossShow   {Boolean}       是否显示丢包率(默认为true)
              @data       {Array}         返回数据参数\
*@data        @avg_delay                  时延
              @avg_loss_value             丢包
              @isSpecial                  0、不是专线，1、是专线
              @isBroken                   0、正常 ，1、中断，2、时延劣化，3、丢包劣化
              @isBuquan                   0、不是补全，1、是补全
              @ip1                        ip地址
*@events      @on-click                   图标、虚线点击事件
*@on-click    @type                      1、图标，2、虚线
              @index                     当前位置下标
              @column                    当前数据（图标当前，虚线开头）
              @data                      返回数据
-->
<template>
  <section class="topology">
    <div class="topology-noData" v-if="topology.length === 0">
      拓扑图暂无数据
    </div>
    <div class="topology-content" :style="{ width: flowChartWidth }" v-else>
      <div
        class="topology-box"
        v-for="(item, index) in topology"
        :class="[
          item.isBroken === 1 && degradation_type != '丢包劣化'
            ? 'error-state'
            : '',
          (item.isBroken === 2 || item.isBroken === 3) && item.isBuquan === 0
            ? 'inferior-state'
            : '',
          item.isBuquan === 1 && item.isBroken === 0 ? 'repair-state' : ''
        ]"
        :key="index"
      >
        {{ data_source }}
        <div
          class="icon-text"
          @mouseenter="iconMoveIn(index)"
          @mouseleave="iconMoveOut()"
        >
          <i
            :class="[
              index === 0 ? 'icon-source' : '',
              index === topology.length - 1 ? 'icon-server' : '',
              index > 0 && index < topology.length - 1 ? 'icon-route' : '',
              iconType ? 'icon-hover' : '',
              lastType && index === topology.length - 1 ? 'icon-hover' : '',
              icon_click_index===index ? 'icon-click' : ''
            ]"
            @click="
              iconClick(item, index, topology[0], topology[topology.length - 1])
            "
          ></i>
          <div class="text">{{ item.ip1 }}</div>
          <div class="text">
            {{
              index === 0
                ? "源"
                : index === topology.length - 1
                ? item.isSpecial === 1
                  ? "专线/目的"
                  : "目的"
                : item.isSpecial === 1
                ? this.$t('spec_line')
                : ""
            }}
          </div>
        </div>
        <div
          class="line-box"
          v-if="index < topology.length - 1"
          :class="[
            topology[index + 1].isBroken === 1 && degradation_type != '丢包劣化'
              ? 'error-line-state'
              : '',
            (topology[index + 1].isBroken === 2 ||
              topology[index + 1].isBroken === 3) &&
            topology[index + 1].isBuquan === 0
              ? 'inferior-line-state'
              : '',
            topology[index + 1].isBuquan === 1 &&
            topology[index + 1].isBroken === 0
              ? 'repair-line-state'
              : '',
            line_click_index.indexOf(index) > -1 ? 'ine-click' : ''
          ]"
          @click="lineClick(item, index)"
        >
          <div class="text delay">
            <span
              v-if="
                delayShow &&
                  topology[index + 1].isBroken != 1 &&
                  topology[index + 1].isBuquan != 1
              "
              v-show="topology[index + 1].ip1 != '*' && item.ip1 != '*'"
            >
              {{
                (topology[index + 1].isBuquan === 1 && topology[index + 1].isBroken === 0) ||
                topology[index + 1].avg_delay === null ||
                topology[index + 1].avg_delay === "" ||
                topology[index + 1].avg_delay === undefined
                  ? "--"
                  : topology[index + 1].avg_delay &lt; 1
                  ? "&lt;1"
                  : topology[index + 1].avg_delay
              }}ms
            </span>
          </div>
          <div class="line"></div>
          <div class="text loss">
            <span
              v-if="
                lossShow &&
                  topology[index + 1].isBroken != 1 &&
                  topology[index + 1].isBuquan != 1
              "
              v-show="topology[index + 1].ip1 != '*' && item.ip1 != '*'"
            >
              {{
                topology[index + 1].avg_loss_value === null ||
                topology[index + 1].avg_loss_value === "" ||
                topology[index + 1].avg_loss_value === undefined
                  ? "--"
                  : topology[index + 1].avg_loss_value
              }}%
            </span>
          </div>
          <div
            class="icon-error"
            v-if="
              topology[index + 1].isBroken === 1 &&
                degradation_type != '丢包劣化'
            "
          ></div>
        </div>
        <div class="mask" v-if="item.ip1 === '*' && iconIndex === index">
          <div class="mask-box">
            <label>节点1_IP：</label>
            <div class="mask-text">{{ topology_mask[0].ip1 }}</div>
          </div>
          <div class="mask-box">
            <label>节点2_IP：</label>
            <div class="mask-text">{{ topology_mask[1].ip1 }}</div>
          </div>
          <div class="mask-box" v-if="delayShow">
            <label>平均时延：</label>
            <div class="mask-text">
              {{
                topology_mask[1].avg_delay === null
                  ? "--"
                  : topology_mask[1].avg_delay &lt; 1
                  ? "&lt;1"
                  : parseFloat(topology_mask[1].avg_delay).toFixed(2)
              }}ms
            </div>
          </div>
          <div class="mask-box" v-if="lossShow">
            <label>平均丢包率：</label>
            <div class="mask-text loss-mask-test">
              {{
                topology_mask[1].avg_loss_value === null
                  ? "--"
                  : topology_mask[1].avg_loss_value
              }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "topology",
  props: {
    iconType: {
      type: Boolean,
      default: true
    },
    lastType: {
      type: Boolean,
      default: false
    },
    delayShow: {
      type: Boolean,
      default: true
    },
    lossShow: {
      type: Boolean,
      default: true
    },
    degradation_type: {},
    data_source: {},
    data: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      topology: [],
      topology_mask: [],
      iconIndex: null,
      line_click_index: [],
      icon_click_index : null,
    };
  },
  watch: {
    data: {
      handler(val) {
        if (val.length === 0) {
          this.resetData();
        } else {
          this.topology = JSON.parse(JSON.stringify(val));
        }
      },
      deep: true
    },
    degradation_type: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    },
    data_source: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    }
  },
  computed: {
    //计算流程图总宽度
    flowChartWidth() {
      return (110 + 98) * (this.topology.length - 1) + 110 + "px";
    }
  },
  methods: {
    //图标事件
    iconClick(column, index, startRow, endRow) {
      this.line_click_index = [];
      this.icon_click_index = index;
      let obj = {
        type: 1,
        index: index,
        column: column,
        data: [startRow, endRow]
      };
      this.$emit("on-click", obj);
    },
    //图标移入移出事件
    iconMoveIn(index) {
      let _self = this;
      this.iconIndex = index;
      if (_self.topology[index].ip1 === "*") {
        this.topology_mask = this.topologyNodeId(index);
      }
    },
    iconMoveOut() {
      this.iconIndex = null;
      this.topology_mask = [];
    },
    //虚线事件
    lineClick(column, index) {
      this.line_click_index = [];
      this.icon_click_index = null;
      let array = this.topology,
        data = [];
      if (array[index].ip1 === "*" || array[index + 1].ip1 === "*") {
        this.line_click_index = this.lineHover(index);
        data = this.topologyNodeId(index);
      } else {
        data = [array[index], array[index + 1]];
        this.line_click_index.push(index);
      }
      let obj = {
        type: 2,
        index: index,
        column: column,
        data: data
      };
      this.$emit("on-click", obj);
    },
    //获取ip为*的开始及结束数据
    topologyNodeId(index) {
      let _self = this,
        array = _self.topology,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].ip1 === "*") {
          if (i < index && array[i].ip1 != "*") {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*") {
            end.push(array[i]);
          }
        } else if (array[index + 1].ip1 === "*") {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*") {
            end.push(array[i]);
          }
        }
      }
      this.topology_mask = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //渲染虚线移入数据
    lineHover(index) {
      let array = this.topology;
      let lineData = this.topologyNodeId(index);
      let startIndex = array.indexOf(lineData[0]),
        endIndex = array.indexOf(lineData[1]),
        indexArray = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (i >= startIndex && i < endIndex) {
          indexArray.push(i);
        }
      }
      return indexArray;
    },
    //清除数据
    resetData() {
      this.topology = [];
      this.topology_mask = [];
      this.iconIndex = null;
      this.line_click_index = [];
      this.icon_click_index = null;
    }
  },
  mounted() {
    if (this.data.length === 0) {
      this.resetData();
    } else {
      this.topology = JSON.parse(JSON.stringify(this.data));
      console.log(this.topology)
    }
  }
};
</script>
