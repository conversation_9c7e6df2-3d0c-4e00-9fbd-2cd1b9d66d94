
  <template>
  <!-- u型 topology -->
  <div
    :class="[
      'uTopology-container',
      uTopologyList.length === 1 ? 'uTopology-container-single' : '',
    ]"
  >
    <div class="topology-noData" v-if="topology.length === 0">
      <!-- 拓扑图暂无数据 -->
      <img :src="getNodataSrc" alt="" />

      <div class="noData-text">{{ $t("phytopo_No_data") }}</div>
    </div>
    <div class="uTopology-content" id="uTopology" v-else>
      <div
        class="uTopology-box"
        v-for="(item, index) in uTopologyList"
        :key="index"
      >
        <!-- 正向行 -->
        <div
          :class="[
            'uTopology-odd',
            item.length < 8 && uTopologyList.length === 1
              ? 'uTopology-odd-one'
              : '',
          ]"
          v-if="(index + 1) % 2 !== 0"
        >
          <div class="uTopology-item-box" v-for="(odd, idx) in item" :key="idx">
            <!-- 左侧u型 -->
            <div
              :class="[
                'uTopology-border-right',
                line_click_index.indexOf(index * 8 + idx) > -1
                  ? 'uTopology-right-click'
                  : '',
              ]"
              :style="{ borderColor: getBorderColor(odd, index, idx) }"
              v-if="idx === 7 && index * 8 + idx < topology.length - 1"
              @click.self="
                lineClick(odd, index * 8 + idx, topology[index * 8 + idx + 1])
              "
            >
              <div
                class="text udelay"
                :style="{ color: getBorderColor(odd, index, idx) }"
                v-show="
                  uTopologyList[index + 1][0].ip1 != '*' && odd.ip1 != '*'
                "
              >
                <span
                  v-if="
                    delayShow &&
                    uTopologyList[index + 1][0].lineColor != 'r' &&
                    uTopologyList[index + 1][0] != true &&
                    !(
                      item[idx].suspect == true &&
                      uTopologyList[index + 1][0].suspect != true
                    )
                  "
                >
                  {{
                  uTopologyList[index+1][0].lineColor === 'g' ||
                  uTopologyList[index+1][0].avgDelay === null ||
                  uTopologyList[index+1][0].avgDelay === "" ||
                  uTopologyList[index+1][0].avgDelay === undefined
                    ? item[idx].peertopeerShowDelayLoss != true ? "--ms" :""
                    : (uTopologyList[index+1][0].avgDelay &lt; 1)
                    ? "&lt;1ms"
                    : uTopologyList[index+1][0].avgDelay+"ms"
                  }}
                </span>
              </div>
              <div
                class="text uloss"
                :style="{ color: getBorderColor(odd, index, idx) }"
                v-if="
                  lossShow &&
                  uTopologyList[index + 1][0].lineColor != 'r' &&
                  uTopologyList[index + 1][0].suspect != true &&
                  !(
                    item[idx].suspect == true &&
                    uTopologyList[index + 1][0].suspect != true
                  )
                "
                v-show="
                  uTopologyList[index + 1][0].ip1 != '*' && odd.ip1 != '*'
                "
              >
                <span>
                  {{
                    uTopologyList[index + 1][0].lineColor === "g" ||
                    uTopologyList[index + 1][0].avgLossValue === null ||
                    uTopologyList[index + 1][0].avgLossValue === "" ||
                    uTopologyList[index + 1][0].avgLossValue === undefined
                      ? item[idx].peertopeerShowDelayLoss != true
                        ? "--%"
                        : ""
                      : uTopologyList[index + 1][0].avgLossValue + "%"
                  }}
                </span>
              </div>
              <!-- 专线标识 -->

              <div
                v-if="topology[index * 8 + idx + 1].isSpecial == 1"
                class="text flowChart"
                :style="getUtypeMargin(item, idx, odd, index)"
              >
                <span>{{ $t("flowChart_srl") }}</span>
              </div>
              <!-- /专线标识 -->

              <!-- x标识 -->
              <div
                class="text ulink-error"
                v-if="
                  (topology[index * 8 + idx + 1].peertopeerLineType != 0 &&
                    topology[index * 8 + idx + 1].linkPoint) ||
                  ((topology[index * 8 + idx + 1].peertopeerLineType == 0 ||
                    topology[index * 8 + idx].peertopeerLineType == 0) &&
                    topology[index * 8 + idx].linkPoint) === true
                "
              ></div>
              <!-- /x标识 -->
            </div>
            <!-- 左侧u型 -->

            <div class="left-img">
              <!-- 图标 -->
              <div
                :class="[
                  'left-img',
                  nodeActive == index * 8 + idx ? 'icon-active' : '',
                ]"
                @click.self="
                  iconClick(
                    odd,
                    index * 8 + idx,
                    topology[0],
                    topology[index * 8 + idx]
                  )
                "
                @mouseenter.self="iconMoveIn(index * 8 + idx)"
                @mouseleave.self="iconMoveOut()"
                :style="{
                  'background-image': getBackgroundImg(odd, index, idx),
                }"
              ></div>

              <!-- 图标 -->
              <!-- 星号提示 -->

              <div
                class="mask"
                v-if="
                  ((odd.ip1 === '*' && odd.isStarNodeMontage != 1) ||
                    odd.suspect) &&
                  iconIndex === index * 8 + idx
                "
                :style="{
                  left: idx === 7 ? '-80px' : '21px',
                }"
              >
                <div class="mask-box">
                  <div class="tip-label">{{ $t("flowChart_n1") }}：</div>
                  <div class="mask-text">{{ topology_mask[0].ip1 || "" }}</div>
                </div>
                <div class="mask-box">
                  <div class="tip-label">{{ $t("flowChart_n2") }}：</div>
                  <div class="mask-text">{{ topology_mask[1].ip1 || "" }}</div>
                </div>
                <div class="mask-box" v-if="delayShow">
                  <div class="tip-label">{{ $t("dash_average_delay") }}：</div>
                  <div class="mask-text">
                    {{
                  topology_mask[1].avgDelay === null
                    ? "--"
                    : topology_mask[1].avgDelay &lt; 1
                    ? "&lt;1"
                    : parseFloat(topology_mask[1].avgDelay).toFixed(2)








                    }}ms
                  </div>
                </div>
                <div class="mask-box" v-if="lossShow">
                  <div class="tip-label">
                    {{ $t("dash_average_packet_loss") }}：
                  </div>
                  <div class="mask-text loss-mask-test">
                    {{
                      topology_mask[1].avgLossValue === null
                        ? "--"
                        : topology_mask[1].avgLossValue
                    }}%
                  </div>
                </div>
              </div>
              <!-- /星号提示 -->
              <!-- 节点label -->
              <div class="uTopology-node-label">
                <Tooltip :content="odd.ip1" placement="top-start">
                  <div class="text">{{ odd.ip2 }}</div>
                </Tooltip>
                <div
                  class="text fontHidden"
                  :title="
                    odd.deviceName +
                    (index === 0 && idx === 0
                      ? $t('flowChart_sou1')
                      : index === uTopologyList.length - 1 &&
                        idx === item.length - 1
                      ? odd.isSpecial === 1
                        ? $t('flowChart_aim3')
                        : $t('flowChart_aim3')
                      : odd.isSpecial === 1
                      ? ''
                      : '')
                  "
                >
                  <div v-if="odd.isShow">
                    {{ odd.deviceName || "" }}
                    {{
                      index === 0 && idx === 0
                        ? $t("flowChart_sou1")
                        : index === uTopologyList.length - 1 &&
                          idx === item.length - 1
                        ? odd.isSpecial === 1
                          ? $t("flowChart_aim3")
                          : $t("flowChart_aim3")
                        : odd.isSpecial === 1
                        ? ""
                        : ""
                    }}
                  </div>
                </div>
                <topologyEditVue
                  v-show="checkEditShow(index, idx, odd)"
                  :nodeItem="odd"
                  ref="topologyRef"
                  @addOk="addNameOk"
                  @editName="editName"
                  :isPoToPo="isPoToPo"
                  :nodeIndex="getEditIndex(index, idx)"
                  :linkData="linkData"
                ></topologyEditVue>
              </div>
              <!-- /节点label -->
            </div>
            <div
              class="right-link"
              v-if="idx !== item.length - 1"
              :style="getItemStyle(item, odd, idx)"
              @click="
                lineClick(odd, index * 8 + idx, topology[index * 8 + idx + 1])
              "
            >
              <div
                class="text delay"
                :style="{ color: getBorderColor(odd, index, idx) }"
              >
                <span
                  v-if="
                    delayShow &&
                    item[idx + 1].lineColor != 'r' &&
                    item[idx + 1].suspect != true &&
                    !(
                      item[idx].suspect == true && item[idx + 1].suspect != true
                    )
                  "
                  v-show="item[idx + 1].ip1 != '*' && odd.ip1 != '*'"
                >
                  {{
                  item[idx + 1].lineColor === 'g' ||
                  item[idx + 1].avgDelay === null ||
                  item[idx + 1].avgDelay === "" ||
                  item[idx + 1].avgDelay === undefined
                    ? item[idx].peertopeerShowDelayLoss != true ? "--ms" :""
                    : (item[idx + 1].avgDelay &lt; 1)
                    ? "&lt;1ms"
                    : item[idx + 1].avgDelay+"ms"
                  }}
                </span>
              </div>
              <!--  @click="lineClick(item, index, topology[index + 1])" -->
              <div
                :class="[
                  'uTopology-link',
                  line_click_index.indexOf(index * 8 + idx) > -1
                    ? 'ine-click'
                    : '',
                ]"
                :style="{ backgroundColor: getBorderColor(odd, index, idx) }"
              ></div>
              <div
                class="text loss"
                :style="{ color: getBorderColor(odd, index, idx) }"
              >
                <span
                  v-if="
                    lossShow &&
                    item[idx + 1].lineColor != 'r' &&
                    item[idx + 1].suspect != true &&
                    !(
                      item[idx].suspect == true && item[idx + 1].suspect != true
                    )
                  "
                  v-show="item[idx + 1].ip1 != '*' && odd.ip1 != '*'"
                >
                  {{
                    item[idx + 1].lineColor === "g" ||
                    item[idx + 1].avgLossValue === null ||
                    item[idx + 1].avgLossValue === "" ||
                    item[idx + 1].avgLossValue === undefined
                      ? item[idx].peertopeerShowDelayLoss != true
                        ? "--%"
                        : ""
                      : item[idx + 1].avgLossValue + "%"
                  }}
                </span>
              </div>
              <!-- 专线标识 -->
              <div
                v-if="
                  index * 8 + idx < topology.length - 1 &&
                  topology[index * 8 + idx + 1].isSpecial == 1
                "
                class="text"
                :style="getMargin(item, idx, odd, index)"
              >
                <span>{{ $t("flowChart_srl") }}</span>
              </div>
              <!-- /专线标识 -->
              <!-- x标识 -->
              <div
                class="text link-error"
                v-if="
                  (topology[index * 8 + idx + 1].peertopeerLineType != 0 &&
                    topology[index * 8 + idx + 1].linkPoint) ||
                  ((topology[index * 8 + idx + 1].peertopeerLineType == 0 ||
                    topology[index * 8 + idx].peertopeerLineType == 0) &&
                    topology[index * 8 + idx].linkPoint) === true
                "
              ></div>
              <!-- /x标识 -->
            </div>
          </div>
        </div>
        <!-- 反向行 -->
        <div class="uTopology-even" v-if="(index + 1) % 2 === 0">
          <div class="uTopology-item-box" v-for="(odd, idx) in item" :key="idx">
            <!-- 左侧侧u型 -->
            <div
              :class="[
                'uTopology-border-left',
                line_click_index.indexOf(index * 8 + idx) > -1
                  ? 'uTopology-left-click'
                  : '',
              ]"
              :style="{ borderColor: getBorderColor(odd, index, idx) }"
              v-if="idx === 7 && index * 8 + idx < topology.length - 1"
              @click.self="
                lineClick(odd, index * 8 + idx, topology[index * 8 + idx + 1])
              "
            >
              <div
                class="text udelay"
                :style="{ color: getBorderColor(odd, index, idx) }"
                v-show="
                  uTopologyList[index + 1][0].ip1 != '*' && odd.ip1 != '*'
                "
              >
                <span
                  v-if="
                    delayShow &&
                    uTopologyList[index + 1][0].lineColor != 'r' &&
                    uTopologyList[index + 1][0] != true &&
                    !(
                      item[idx].suspect == true &&
                      uTopologyList[index + 1][0].suspect != true
                    )
                  "
                >
                  {{
                  uTopologyList[index+1][0].lineColor === 'g' ||
                  uTopologyList[index+1][0].avgDelay === null ||
                  uTopologyList[index+1][0].avgDelay === "" ||
                  uTopologyList[index+1][0].avgDelay === undefined
                    ? item[idx].peertopeerShowDelayLoss != true ? "--ms" :""
                    : (uTopologyList[index+1][0].avgDelay &lt; 1)
                    ? "&lt;1ms"
                    : uTopologyList[index+1][0].avgDelay+"ms"
                  }}
                </span>
              </div>
              <div
                class="text uloss"
                :style="{ color: getBorderColor(odd, index, idx) }"
                v-if="
                  lossShow &&
                  uTopologyList[index + 1][0].lineColor != 'r' &&
                  uTopologyList[index + 1][0].suspect != true &&
                  !(
                    item[idx].suspect == true &&
                    uTopologyList[index + 1][0].suspect != true
                  )
                "
                v-show="
                  uTopologyList[index + 1][0].ip1 != '*' && odd.ip1 != '*'
                "
              >
                <span>
                  {{
                    uTopologyList[index + 1][0].lineColor === "g" ||
                    uTopologyList[index + 1][0].avgLossValue === null ||
                    uTopologyList[index + 1][0].avgLossValue === "" ||
                    uTopologyList[index + 1][0].avgLossValue === undefined
                      ? item[idx].peertopeerShowDelayLoss != true
                        ? "--%"
                        : ""
                      : uTopologyList[index + 1][0].avgLossValue + "%"
                  }}
                </span>
              </div>
              <!-- 专线标识 -->

              <div
                v-if="topology[index * 8 + idx + 1].isSpecial == 1"
                class="text flowChart"
                :style="getUtypeMarginLeft(item, idx, odd, index)"
              >
                <span>{{ $t("flowChart_srl") }}</span>
              </div>
              <!-- /专线标识 -->
              <!-- x标识 -->
              <div
                class="text ulink-error"
                v-if="
                  (topology[index * 8 + idx + 1].peertopeerLineType != 0 &&
                    topology[index * 8 + idx + 1].linkPoint) ||
                  ((topology[index * 8 + idx + 1].peertopeerLineType == 0 ||
                    topology[index * 8 + idx].peertopeerLineType == 0) &&
                    topology[index * 8 + idx].linkPoint) === true
                "
              ></div>
              <!-- /x标识 -->
            </div>
            <!-- 右侧侧u型 -->
            <!-- 右侧连线 -->
            <div
              class="right-link"
              :style="getItemStyle(item, odd, idx)"
              @click="
                lineClick(odd, index * 8 + idx, topology[index * 8 + idx + 1])
              "
            >
              <!-- 时延 -->
              <div
                class="text delay"
                v-if="idx !== item.length - 1"
                :style="{ color: getBorderColor(odd, index, idx) }"
              >
                <span
                  v-if="
                    delayShow &&
                    item[idx + 1].lineColor != 'r' &&
                    item[idx + 1].suspect != true &&
                    !(
                      item[idx].suspect == true && item[idx + 1].suspect != true
                    )
                  "
                  v-show="item[idx + 1].ip1 != '*' && odd.ip1 != '*'"
                >
                  {{
                  item[idx + 1].lineColor === 'g' ||
                  item[idx + 1].avgDelay === null ||
                  item[idx + 1].avgDelay === "" ||
                  item[idx + 1].avgDelay === undefined
                    ? item[idx].peertopeerShowDelayLoss != true ? "--ms" :""
                    : (item[idx + 1].avgDelay &lt; 1)
                    ? "&lt;1ms"
                    : item[idx + 1].avgDelay+"ms"
                  }}
                </span>
              </div>
              <!--   @click="lineClick(item, index, topology[index + 1])" -->
              <div
                :class="[
                  'uTopology-link',
                  line_click_index.indexOf(index * 8 + idx) > -1
                    ? 'ine-click'
                    : '',
                ]"
                v-if="idx !== item.length - 1"
                :style="{ backgroundColor: getBorderColor(odd, index, idx) }"
              ></div>
              <div
                class="text loss"
                v-if="idx !== item.length - 1"
                :style="{ color: getBorderColor(odd, index, idx) }"
              >
                <span
                  v-if="
                    lossShow &&
                    item[idx + 1].lineColor != 'r' &&
                    item[idx + 1].suspect != true &&
                    !(
                      item[idx].suspect == true && item[idx + 1].suspect != true
                    )
                  "
                  v-show="item[idx + 1].ip1 != '*' && odd.ip1 != '*'"
                >
                  {{
                    item[idx + 1].lineColor === "g" ||
                    item[idx + 1].avgLossValue === null ||
                    item[idx + 1].avgLossValue === "" ||
                    item[idx + 1].avgLossValue === undefined
                      ? item[idx].peertopeerShowDelayLoss != true
                        ? "--%"
                        : ""
                      : item[idx + 1].avgLossValue + "%"
                  }}
                </span>
              </div>
              <!-- 专线标识 -->
              <div
                v-if="
                  index * 8 + idx < topology.length - 1 &&
                  topology[index * 8 + idx + 1].isSpecial == 1
                "
                class="text"
                :style="getMargin(item, idx, odd, index)"
              >
                <span>{{ $t("flowChart_srl") }}</span>
              </div>
              <!-- /专线标识 -->
              <!--反向行 x标识 -->
              <div
                class="text link-error"
                v-if="
                  index * 8 + idx + 1 < topology.length &&
                  ((topology[index * 8 + idx + 1].peertopeerLineType != 0 &&
                    topology[index * 8 + idx + 1].linkPoint) ||
                    ((topology[index * 8 + idx + 1].peertopeerLineType == 0 ||
                      topology[index * 8 + idx].peertopeerLineType == 0) &&
                      topology[index * 8 + idx].linkPoint) === true)
                "
              ></div>
              <!-- /x标识 -->
            </div>
            <div class="left-img">
              <!-- 图标 -->
              <div
                :class="[
                  'left-img',
                  nodeActive == index * 8 + idx ? 'icon-active' : '',
                ]"
                @click.self="
                  iconClick(
                    odd,
                    index * 8 + idx,
                    topology[0],
                    topology[index * 8 + idx]
                  )
                "
                @mouseenter.self="iconMoveIn(index * 8 + idx)"
                @mouseleave.self="iconMoveOut()"
                :style="{
                  'background-image': getBackgroundImg(odd, index, idx),
                }"
              ></div>

              <!-- 图标 -->
              <!-- 星号提示 -->
              <div
                class="mask"
                v-if="
                  ((odd.ip1 === '*' && odd.isStarNodeMontage != 1) ||
                    odd.suspect) &&
                  iconIndex === index * 8 + idx
                "
                :style="{
                  left: idx === 0 ? '-80px' : '21px',
                }"
              >
                <div class="mask-box">
                  <div class="tip-label">{{ $t("flowChart_n1") }}：</div>
                  <div class="mask-text">{{ topology_mask[0].ip1 || "" }}</div>
                </div>
                <div class="mask-box">
                  <div class="tip-label">{{ $t("flowChart_n2") }}：</div>
                  <div class="mask-text">{{ topology_mask[1].ip1 || "" }}</div>
                </div>
                <div class="mask-box" v-if="delayShow">
                  <div class="tip-label">{{ $t("dash_average_delay") }}：</div>
                  <div class="mask-text">
                    {{
                  topology_mask[1].avgDelay === null
                    ? "--"
                    : topology_mask[1].avgDelay &lt; 1
                    ? "&lt;1"
                    : parseFloat(topology_mask[1].avgDelay).toFixed(2)






                    }}ms
                  </div>
                </div>
                <div class="mask-box" v-if="lossShow">
                  <div class="tip-label">
                    {{ $t("dash_average_packet_loss") }}：
                  </div>
                  <div class="mask-text loss-mask-test">
                    {{
                      topology_mask[1].avgLossValue === null
                        ? "--"
                        : topology_mask[1].avgLossValue
                    }}%
                  </div>
                </div>
              </div>
              <!-- /星号提示 -->
              <!-- 节点label -->
              <div class="uTopology-node-label">
                <Tooltip :content="odd.ip1" placement="top-start">
                  <div class="text">{{ odd.ip2 }}</div>
                </Tooltip>
                <div
                  class="text fontHidden"
                  :title="
                    odd.deviceName +
                    (index === 0 && idx === 0
                      ? $t('flowChart_sou1')
                      : index === uTopologyList.length - 1 &&
                        idx === item.length - 1
                      ? odd.isSpecial === 1
                        ? $t('flowChart_aim3')
                        : $t('flowChart_aim3')
                      : odd.isSpecial === 1
                      ? ''
                      : '')
                  "
                >
                  <div v-if="odd.isShow">
                    {{ odd.deviceName || "" }}
                    {{
                      index === 0 && idx === 0
                        ? $t("flowChart_sou1")
                        : index === uTopologyList.length - 1 &&
                          idx === item.length - 1
                        ? odd.isSpecial === 1
                          ? $t("flowChart_aim3")
                          : $t("flowChart_aim3")
                        : odd.isSpecial === 1
                        ? ""
                        : ""
                    }}
                  </div>
                </div>
                <topologyEditVue
                  v-show="checkEditShow(index, idx, odd)"
                  :nodeItem="odd"
                  ref="topologyRef"
                  @addOk="addNameOk"
                  @editName="editName"
                  :isPoToPo="isPoToPo"
                  :nodeIndex="getEditIndex(index, idx)"
                  :linkData="linkData"
                ></topologyEditVue>
              </div>
              <!-- /节点label -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  import topologyEditVue from './topologyEdit.vue';
  import ipv6Format from "@/common/ipv6Format";
  
  export default {
  name: '',
   components: {
      topologyEditVue,
    },
    props: {
      browserWidth: {
        type: Number,
        default: 0,
      },
      iconType: {
        type: Boolean,
        default: true,
      },
      lastType: {
        type: Boolean,
        default: false,
      },
      delayShow: {
        type: Boolean,
        default: true,
      },
      lossShow: {
        type: Boolean,
        default: true,
      },
      dashShow: {
        type: Boolean,
        default: true,
      },
      degradation_type: {},
      data_source: {},
      data: {
        type: Array,
        default: function () {
          return [];
        },
      },
      linkData: {
        type: [Array, Object],
        default: function () {
          return [];
        },
      },
      // 区分端对端
      isPoToPo: {
        type: Boolean,
        default: false,
      },
    },
  data() {
  return {
    currentSkin: sessionStorage.getItem('dark') || 1,
    // 
    nodeActive: null,
    uTopology:[],
    uTopologyList:[],
    contentWidth:0,
     configData: {},
        topology: [],
        topology_mask: [],
        iconIndex: null,
        line_click_index: [],
        icon_click_index: null,
        // 节点编辑
        isEdit: false,
        nodeObj: {},
        rowNodeNum:8,  //每行显示的节点数
        topologyBoxWidth:"14%"// //每个 节点宽度
  
  }
  },
  computed: {
     linkDataFin() {
        if (this.linkData.length === 0) {
          return this.linkDatas;
        } else {
          return this.linkData;
        }
      },
      //计算流程图总宽度
      flowChartWidth() {
        return (100 + 50) * (this.topology.length - 1) + "px";
      },
       getNodataSrc() {
      let src = ''
    src =  this.currentSkin == 1 ?  require('../../assets/dashboard/fileempty.png') : require('../../assets/dashboard/fileempty2.png')
    return src

    },
  },
  watch: {
     data: {
        handler(val) {
          this.findInUseIconManageConfigureVoByType();
          this.nodeActive = null
          // this.topology = []
          this.uTopologyList = []
          if (val.length === 0) {
            this.resetData();
          } else {
            let dataJson = JSON.parse(JSON.stringify(val));
            for (let index = 0, size = dataJson.length; index < size; index++) {
              const element = dataJson[index];
              element.deviceTypeCss = "";
              // 设置二层设备的图标
              if (element.deviceType != null) {
                element.deviceTypeCss = "two-icon";
              }
              // 设置虚拟主机的图标
              if (element.virtualNode == 1) {
                element.deviceTypeCss = "virtual-node-icon";
              }
            }
            this.topology = dataJson;
            // console.log(this.topology, "数据。。。。。。。。。");
            this.topology.forEach((item) => {
              return this.$set(item, "isClick", false);
            });
            this.topology.forEach((item) => this.$set(item, "isShow", true));
            this.topology.forEach((item) => 
              item.ip2 = ipv6Format.abbreviateIPv6(item.ip1)
            );
            // this.generateSShapeStyles('topology-content',8)
            this.handleUTopology()
          }
        },
        deep: true,
      },
      degradation_type: {
        handler(val) {
          // console.log(val)
        },
        deep: true,
      },
      data_source: {
        handler(val) {
          // console.log(val)
        },
        deep: true,
      },
  },
  methods: {
   
    handleStorageChange(event) {
        if (event.key === 'dark') {
            this.currentSkin = event.newValue; // 更新肤色
        }
    },
    getUtypeMarginLeft(item,idx,odd,index) {
       let obj= {}
      
      this.lossShow &&
      this.uTopologyList[index + 1][0].lineColor != 'r' &&
      !(item[idx].suspect == true && this.uTopologyList[index + 1][0].suspect != true) &&
      this.uTopologyList[index + 1][0].ip1 != '*' &&
      odd.ip1 != '*' ? obj.left = '19px' : obj.left = '-3px'
      // 文字颜色
      // 文字颜色
      let borderColor = "";
        let indexTotal = index * 8 + idx
        
        // 原先逻辑
        let i = odd.peertopeerType == 0 ? indexTotal : indexTotal + 1;
        // console.log(item,i,'获取边框颜色')
        if (this.topology[i].lineColor === "r") {
          // 中断
          borderColor = this.configData.breakLineColor;
        } else if (this.topology[i].lineColor === "g") {
          // 未知
          borderColor = this.configData.unknownLineColor;
        } else if (this.topology[i].lineColor === "y") {
          borderColor = this.configData.degradationLineColor;
        } else {
          borderColor = this.configData.normalLineColor;
        }
        obj.color = borderColor;
  
      
      // obj.right = '19px'
      return obj
  
    },
    // 专线标识的位置
    getMargin(item,idx,odd,index) {
      let obj= {}
      
      this.lossShow &&
      item[idx + 1].lineColor != 'r' &&
      !(item[idx].suspect == true && item[idx + 1].suspect != true) &&
      item[idx + 1].ip1 != '*' &&
      odd.ip1 != '*' ? obj.top = '46px' : obj.top = '26px'
      // 文字颜色
      let borderColor = "";
        let indexTotal = index * 8 + idx
        
        // 原先逻辑
        let i = odd.peertopeerType == 0 ? indexTotal : indexTotal + 1;
        // console.log(item,i,'获取边框颜色')
        if (this.topology[i].lineColor === "r") {
          // 中断
          borderColor = this.configData.breakLineColor;
        } else if (this.topology[i].lineColor === "g") {
          // 未知
          borderColor = this.configData.unknownLineColor;
        } else if (this.topology[i].lineColor === "y") {
          borderColor = this.configData.degradationLineColor;
        } else {
          borderColor = this.configData.normalLineColor;
        }
        obj.color = borderColor;
  
      
  
      return obj
  
    },
    getUtypeMargin(item,idx,odd,index) {
       let obj= {}
      
      this.lossShow &&
      this.uTopologyList[index + 1][0].lineColor != 'r' &&
      !(item[idx].suspect == true && this.uTopologyList[index + 1][0].suspect != true) &&
      this.uTopologyList[index + 1][0].ip1 != '*' &&
      odd.ip1 != '*' ? obj.right = '19px' : obj.right = '-3px'
      // 文字颜色
      // 文字颜色
      let borderColor = "";
        let indexTotal = index * 8 + idx
        
        // 原先逻辑
        let i = odd.peertopeerType == 0 ? indexTotal : indexTotal + 1;
        // console.log(item,i,'获取边框颜色')
        if (this.topology[i].lineColor === "r") {
          // 中断
          borderColor = this.configData.breakLineColor;
        } else if (this.topology[i].lineColor === "g") {
          // 未知
          borderColor = this.configData.unknownLineColor;
        } else if (this.topology[i].lineColor === "y") {
          borderColor = this.configData.degradationLineColor;
        } else {
          borderColor = this.configData.normalLineColor;
        }
        obj.color = borderColor;
  
      
      // obj.right = '19px'
      return obj
  
    },
    // 判断是否可以编辑
    checkEditShow(index, idx, odd) {
      // item.ip1 != '*' && index !== 0 && index !== topology.length - 1
      let i = index * 8 + idx
      if(odd.ip1 != "*" && i !== 0 && i !== this.topology.length - 1 ) {
        return true
      }else {
        return false
      }
      },
  
  
    // 处理item样式
    getItemStyle(item,odd,idx) {
      console.log(this.contentWidth,'宽度')
      this.contentWidth = this.browserWidth - 40
  
      let width = (this.contentWidth - this.contentWidth * 0.16 - 42*8)/7
      console.log(width,'连线宽度')
      return {
        width: width + 'px'
      }
  
    },
    // 将数据处理成u型数据
    handleUTopology() {
      console.log(this.topology,'世纪数据')
      let element = null
      this.$nextTick(() => {
          element = top.document.getElementById('uTopology')
      console.log(element.offsetWidth,'元素内部')
          this.contentWidth = element.offsetWidth
          // this.contentWidth = 1000
  
      })
       
      let arrCount = Math.ceil(this.topology.length/8)
      console.log(arrCount,'arrCount')
      for(let i = 1; i <= arrCount;i++) {
        this.uTopologyList.push([])
      }
      console.log(this.uTopologyList,'第一步')
      this.topology.forEach((item,index) => {
        if(index+ 1 <= 8) {
          this.uTopologyList[0].push(item)
        }else if(index+ 1 > 8) {
           let num = Math.ceil((index+ 1 - 8)/8) 
            this.uTopologyList[num].push(item)
  
        }
       
       
       
      })
      console.log(this.uTopologyList,'第二步')
     
  
  
    },
    // 链路详情默认高亮
       getLineClickIndex(index) {
        this.line_click_index.push(index);
       
       },
      getTopologyBoxStyle(index) {
        let style= {
          'float': this.isLeftOrRight(index) ? 'left' : 'right',
          'width': (index==this.topology.length-1)?'108px':(index+1)%(this.rowNodeNum)===0?'-1%':this.topologyBoxWidth,
          'position':(index + 1) % this.rowNodeNum === 0 ? 'relative' : '',
          'margin-right':(index!==0&&(index==this.rowNodeNum||index%(this.rowNodeNum*2)===this.rowNodeNum))? '160px':'',
        }
     
        return style;
      },
      getCurveStyle(item, index){
        let style={};
        // borderColor颜色
        let borderColor = "";
        // 原先逻辑
        let i = item.peertopeerType == 0 ? index : index + 1;
        if (this.topology[i].lineColor === "r") {
          // 中断
          borderColor = this.configData.breakLineColor;
        } else if (this.topology[i].lineColor === "g") {
          // 未知
          borderColor = this.configData.unknownLineColor;
        } else if (this.topology[i].lineColor === "y") {
          borderColor = this.configData.degradationLineColor;
        } else {
          borderColor = this.configData.normalLineColor;
        }
        // 设置高度
        style.height="108px";
        // 设置弧度
        style.borderRadius="0 50% 50% 0";
        // 设置边界
        style.borderTop="2px solid "+borderColor
        style.borderRight="2px solid "+borderColor
        style.borderBottom="2px solid "+borderColor;
        style.borderLeft="none";
        // 如果取余等于0后，又是偶数倍，证明是向右开口的U型 翻转一下'
        if((index+1)/this.rowNodeNum%2===0){
          style.transform="rotate(180deg)"; /* 旋转180度 */
        }
        // console.log("弧线样式：",style);
        return style;
      },
      isLeftOrRight(index){
        return Math.floor(index / this.rowNodeNum) % 2 === 0 ;
      },
      getIconStyle(index){
         return { 
          order: this.isLeftOrRight(index) ? 0 : 1,
         }
      },
      getLineStyle(index) {
        let isLast=(index+1)%this.rowNodeNum===0
        let isDouble=(index+1)%(this.rowNodeNum*2)===0
        return {
          // 'margin-right': this.isLeftOrRight(index) ? '0px' : '-30px',
          // 'margin-left':isDouble?"-25px":isLast?"78px": this.isLeftOrRight(index) ? '-30px' : '0px',
          // 'position':(index + 1) % this.rowNodeNum === 0 ? 'absolute' : '',
          // 'width':isLast?'50%':'100%'
        };
      },
      getBackgroundImg(item, rowIdx,idx) {
        let index =  rowIdx * 8 + idx
        
        // debugger
        // console.log(item, 99999);
        // debugger
        let url = "";
        // 0 正常 1中断 2 劣化 3 未知
        let status = 0;
        // 先判断出节点状态
        if (item.nodeColor === "r") {
          status = 1;
        } else if (item.nodeColor === "y") {
          status = 2;
        } else if (item.nodeColor === "g") {
          status = 3;
        }
        // 虚拟节点 "VS"
        if (index === 0) {
          // 探针节点  探针 8 code 'PB'
          let findResult = this.configData.inUseIconManageConfigures.find(
            (item) => item.code === "PB"
          );
          let findResult2 = findResult.types.find((item) => item.type === status);
          url = findResult2.image;
        } else if (index === this.topology.length - 1) {
          // 目标节点  code: "GO"
          if (item.virtualNode) {
            // 虚拟节点
            //  this.this.configData.inUseIconManageConfigures
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item1) => item1.code === "VS"
            );
            let findResult2 = findResult.types.find((item2) => item2.type === status);
            url = findResult2.image;
          } else if (item.deviceType) {
            let findResults4 = this.configData.deviceTypeIconManageConfigures.find(
              (obj) => obj.deviceTypeId === item.deviceType
            );
            if (findResults4) {
              // debugger
              findResults4.types.forEach((item4) => {
                if (item4.type === status) {
                  // debugger
  
                  url = item4.image;
                }
              });
              if (!url) {
                let findResult = this.configData.inUseIconManageConfigures.find(
                  (item) => item.code === "GO"
                );
                let findResult2 = findResult.types.find((item) => item.type === status);
                url = findResult2.image;
              }
            } else {
              let findResult = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "GO"
              );
              let findResult2 = findResult.types.find((item) => item.type === status);
              url = findResult2.image;
            }
          } else {
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item) => item.code === "GO"
            );
            let findResult2 = findResult.types.find((item) => item.type === status);
            url = findResult2.image;
          }
        } else {
          // 中间节点 code: "IN"
          if (item.virtualNode) {
            // 虚拟节点
            //  this.this.configData.inUseIconManageConfigures
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item1) => item1.code === "VS"
            );
            let findResult2 = findResult.types.find((item2) => item2.type === status);
            url = findResult2.image;
          } else if (item.deviceType) {
            let findResults4 = this.configData.deviceTypeIconManageConfigures.find(
              (obj) => obj.deviceTypeId === item.deviceType
            );
            if (findResults4) {
              // debugger
              findResults4.types.forEach((item4) => {
                if (item4.type === status) {
                  // debugger
  
                  url = item4.image;
                }
              });
              if (!url) {
                // twoDeviceType区分二层节点
                if (item.twoDeviceType) {
                  let findResult = this.configData.inUseIconManageConfigures.find(
                    (item) => item.code === "SLN"
                  );
                  let findResult2 = findResult.types.find((item) => item.type === status);
                  url = findResult2.image;
                } else {
                  let findResult = this.configData.inUseIconManageConfigures.find(
                    (item) => item.code === "IN"
                  );
                  let findResult2 = findResult.types.find((item) => item.type === status);
                  url = findResult2.image;
                }
              }
            } else {
              // debugger
              let findResult = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "IN"
              );
              let findResult2 = findResult.types.find((item) => item.type === status);
              url = findResult2.image;
            }
          } else {
            // 判断是否是信号节点，如果不是走下面
            if (item.ip1 == "*") {
              let findResult = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "UN"
              );
              let findResult2 = findResult.types.find((item) => item.type === status);
              url = findResult2.image;
            } else {
              let findResult = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "IN"
              );
              let findResult2 = findResult.types.find((item) => item.type === status);
              url = findResult2.image;
            }
  
            // debugger
          }
        }
  
        // console.log(url, "url");
  
        return "url(data:image/png;base64," + url + ")";
      },
      getBorderColor(item, rowIdx,idx) {
        let borderColor = "";
        let index = rowIdx * 8 + idx
        
        // 原先逻辑
        let i = item.peertopeerType == 0 ? index : index + 1;
        console.log(item,i,'获取边框颜色')
        if (this.topology[i].lineColor === "r") {
          // 中断
          borderColor = this.configData.breakLineColor;
        } else if (this.topology[i].lineColor === "g") {
          // 未知
          borderColor = this.configData.unknownLineColor;
        } else if (this.topology[i].lineColor === "y") {
          borderColor = this.configData.degradationLineColor;
        } else {
          borderColor = this.configData.normalLineColor;
        }
  
        return borderColor;
      },
      // 获取图标配置
      async findInUseIconManageConfigureVoByType() {
        // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标
  
        try {
          const res = await this.$http.post(
            "/iconmanage/findInUseIconManageConfigureVoByType",
            { type: 2,currentSkin:this.currentSkin }
          );
          if (res.code === 1) {
            this.configData = res.data;
          }
        } catch (err) {}
      },
      getEditIndex(index,idx) {
        return index * 8 + idx
  
      },
      //图标事件
      iconClick(column, index, startRow, endRow) {
        // debugger
        this.nodeActive = index
        console.log(column, index, startRow, endRow);
        //  this.$refs.topologyRef[0].linkId = this.linkId
        if (this.linkData.length == 0) {
          // console.log(this.$refs.topologyRef[index]);
        }
        this.$refs.topologyRef[index].changeEdit();
        this.topology.forEach((item) => (item.isShow = true));
        this.topology.forEach((item) => {
          if (item.ip1 === column.ip1) {
            item.isClick = true;
          } else {
            item.isClick = false;
          }
        });
        //  console.log(this.topology,'为什么没变化')
        this.line_click_index = [];
        this.icon_click_index = index;
        let array = this.topology;
        var data = [startRow, endRow];
        if (array[index].suspect == true) {
          data = this.handleSuspectClick(index);
        }
        let obj = {
          type: 1,
          index: index,
          column: column,
          data: data,
        };
        this.$emit("on-click", obj);
      },
      //图标移入移出事件
      iconMoveIn(index) {
        // debugger
        let _self = this;
        this.iconIndex = index;
        if (_self.topology[index].ip1 === "*") {
          console.log("------------");
          this.topology_mask = this.topologyNodeId(index);
          // this.topology_mask[0].ip1 =  ipv6Format.formatIPv6Address(this.topology_mask[0].ip1,120);
          // console.log('start',this.topology_mask[0].ip1);
          
          // this.topology_mask[1].ip1 =  ipv6Format.formatIPv6Address(this.topology_mask[1].ip1,120);
          // console.log("this.topology_mask", this.topology_mask);
        } else if (_self.topology[index].suspect == true) {
          this.topology_mask = this.handleSuspectClick(index);
        }
      },
      iconMoveOut() {
        this.iconIndex = null;
        this.topology_mask = [];
      },
      //虚线事件
      lineClick(column, index, columnlater) {
        console.log(arguments , arguments.length , "===========================");
        this.line_click_index = [];
        this.icon_click_index = null;
        let array = this.topology,
          data = [];
        var pointIndex = null,
          pointArr = [];
        if (array[index].ip1 === "*" || array[index].suspect == true) {
          pointIndex = index;
        }
        if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
          pointIndex = index + 1;
        }
        if (pointIndex && pointIndex >= 0) {
          for (let indexs = pointIndex; indexs < array.length; indexs++) {
            if (array[indexs].ip1 != "*" && array[indexs].suspect != true) {
              break;
            }
            if (array[indexs].ip1 == "*" || array[indexs].suspect == true) {
              pointArr.push(indexs);
            }
          }
          for (let indexs = pointIndex; indexs > 0; indexs--) {
            if (array[indexs].ip1 != "*" && array[indexs].suspect != true) {
              break;
            }
            if (array[indexs].ip1 == "*" || array[indexs].suspect == true) {
              pointArr.push(indexs);
            }
          }
        }
        if (pointArr.length > 0) {
          pointArr.sort((a, b) => {
            return a - b;
          });
          pointArr.push(pointArr[0] - 1);
          pointArr = Array.from(new Set(pointArr));
          this.line_click_index = pointArr;
        }
  
        if (array[index].ip1 === "*" || array[index].suspect == true) {
          data = this.topologyNodeIdIndex(index);
        } else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
          data = this.topologyNodeIdIndex(index);
        } else {
          data = [array[index], array[index + 1]];
          this.line_click_index.push(index);
        }
        let obj = {
          type: 2,
          index: index,
          column: column,
          data: data,
          suspect: columnlater.suspect,
        };
        console.log("event-lineClick -----")
        this.$emit("on-click", obj);
      },
      //获取ip为*，不可信节点的开始及结束数据
      topologyNodeIdIndex(index) {
        let _self = this,
          array = _self.topology,
          start = [],
          end = [];
        for (let i = 0, len = array.length; i < len; i++) {
          if (array[index].ip1 === "*" || array[index].suspect == true) {
            if (i < index && array[i].ip1 != "*" && array[i].suspect != true) {
              start.push(array[i]);
            }
            if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
              end.push(array[i]);
            }
          } else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
            if (i <= index) {
              start.push(array[i]);
            }
            if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
              end.push(array[i]);
            }
          }
        }
        this.topology_mask = [start[start.length - 1], end[0]];
        return [start[start.length - 1], end[0]];
      },
      //获取ip为*的开始及结束数据
      topologyNodeId(index) {
        // debugger
        let _self = this,
          array = _self.topology,
          start = [],
          end = [];
        for (let i = 0, len = array.length; i < len; i++) {
          if (array[index].ip1 === "*") {
            if (i < index && array[i].ip1 != "*" && array[i].suspect != true) {
              start.push(array[i]);
            }
            if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
              end.push(array[i]);
            }
          } else if (array[index + 1].ip1 === "*") {
            if (i <= index) {
              start.push(array[i]);
            }
            if (i > index && array[i].ip1 != "*") {
              end.push(array[i]);
            }
          } else if (array[index].suspect) {
            if (i <= index) {
              start.push(array[i - 1]);
            }
            if (i > index) {
              end.push(array[i]);
            }
          }
        }
        this.topology_mask = [start[start.length - 1], end[0]];
        return [start[start.length - 1], end[0]];
      },
      //渲染虚线移入数据
      lineHover(index) {
        let array = this.topology;
        let lineData = this.topologyNodeId(index);
        let startIndex = array.indexOf(lineData[0]),
          endIndex = array.indexOf(lineData[1]),
          indexArray = [];
        for (let i = 0, len = array.length; i < len; i++) {
          if (i >= startIndex && i < endIndex) {
            indexArray.push(i);
          }
        }
        return indexArray;
      },
      //清除数据
      resetData() {
        this.topology = [];
        this.topology_mask = [];
        this.iconIndex = null;
        this.line_click_index = [];
        this.icon_click_index = null;
      },
  
      handleSuspectClick(index) {
        let array = this.topology;
        var data = [];
        if (array[index].suspect == true) {
          data = this.topologyNodeIdIndex(index);
        } else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
          data = this.topologyNodeIdIndex(index);
        } else {
          data = [array[index], array[index + 1]];
        }
        return data;
      },
      // 新增名字成功
      addNameOk(obj) {
        console.log("成功了", obj);
        this.topology.forEach((item) => (item.isShow = true));
        this.topology.forEach((item) => {
          if (item.ip1 === obj.ip1) {
            item.deviceName = obj.text;
          }
          item.isClick = false;
        });
        console.log(this.topology);
      },
      editName(obj) {
        console.log("编辑", obj);
        this.nodeObj = obj;
        this.topology.forEach((item) => {
          if (item.ip1 === obj.ip1) {
            item.isShow = false;
          }
        });
      },
  
  },
   mounted() {
       // 监听 storage 事件
      window.addEventListener('storage', this.handleStorageChange);
      if (this.data.length === 0) {
        this.resetData();
      } else {
        this.topology = JSON.parse(JSON.stringify(this.data));
        console.log(this.topology);
      }
    },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
    },
    created() {
      // this.findInUseIconManageConfigureVoByType();
    },
  }
  </script>
  <style scoped lang='less'>
</style>