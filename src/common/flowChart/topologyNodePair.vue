<!--
*@props       @iconType   {Boolean}       图标是否显示手指样式(默认为true)
              @lastType   {Boolean}       是否最后一个图标显示手指样式（如何iconType存在，lastType无效,默认false）
              @delayShow  {Boolean}       是否显示时延(默认为true)
              @lossShow   {Boolean}       是否显示丢包率(默认为true)
              @data       {Array}         返回数据参数\
*@data        @avg_delay                  时延
              @avg_loss_value             丢包
              @isSpecial                  0、不是专线，1、是专线
              @nodeType                   1、正常 ，2、中断，3、劣化，
              @repaired                   0、不是补全，1、是补全
              @nodeIp                        ip地址
*@events      @on-click                   图标、虚线点击事件
*@on-click    @type                      1、图标，2、虚线
              @index                     当前位置下标
              @column                    当前数据（图标当前，虚线开头）
              @data                      返回数据
-->
<template>
  <section class="topology">
    <div class="topology-noData" v-if="topology.length === 0">
      拓扑图暂无数据
    </div>
    <div class="topology-content" :style="{ width: flowChartWidth }" v-else>
      <div
        class="topology-box"
        v-for="(item, index) in topology"
        :class="[
          item.nodeType === 2 && degradation_type != '丢包劣化'
            ? 'error-state'
            : '',
          item.nodeType === 3 && item.repaired === false
            ? 'inferior-state'
            : '',
          item.repaired === true && item.nodeType === 1 ? 'repair-state' : ''
        ]"
        :key="index"
      >
        <div
          class="icon-text"
          @mouseenter="iconMoveIn(index)"
          @mouseleave="iconMoveOut()"
        >
          <i
            :class="[
              index === 0 ? 'icon-source' : '',
              index === topology.length - 1 ? 'icon-server' : '',
              index > 0 && index < topology.length - 1 ? 'icon-route' : '',
              iconType ? 'icon-hover' : '',
              lastType && index === topology.length - 1 ? 'icon-hover' : ''
            ]"
            @click="
              iconClick(item, index, topology[0], topology[topology.length - 1])
            "
          ></i>
          <div class="text">{{ item.nodeIp }}</div>
          <div class="text">
            {{
              index === 0
                ? "源"
                : index === topology.length - 1
                ? item.isSpecial === true
                  ? "专线/目的"
                  : "目的"
                : item.isSpecial === true
                ? this.$t('spec_line')
                : ""
            }}
          </div>
        </div>
        <div
          class="line-box"
          v-if="index < topology.length - 1"
          :class="[
            topology[index + 1].nodeType === 2 && degradation_type != '丢包劣化'
              ? 'error-line-state'
              : '',
            topology[index + 1].nodeType === 3 &&
            topology[index + 1].repaired === false
              ? 'inferior-line-state'
              : '',
            topology[index + 1].repaired === true &&
            topology[index + 1].nodeType === 1
              ? 'repair-line-state'
              : '',
            line_click_index.indexOf(index) > -1 ? 'ine-click' : ''
          ]"
          @click="lineClick(item, index)"
        >
          <div class="text delay">
            <span
              v-if="
                delayShow &&
                  topology[index + 1].nodeType != 2 &&
                  topology[index + 1].repaired != true
              "
              v-show="topology[index + 1].nodeIp != '*' && item.nodeIp != '*'"
            >
              {{
                (topology[index + 1].repaired === true && topology[index + 1].nodeType === 1) ||
                topology[index + 1].delay === null ||
                topology[index + 1].delay === "" ||
                topology[index + 1].delay === undefined ||
                topology[index + 1].delay === "-1"
                  ? "--"
                  : topology[index + 1].delay &lt; 1
                  ? "&lt;1"
                  : topology[index + 1].delay
              }}ms
            </span>
          </div>
          <div class="line"></div>
          <div class="text loss">
            <span
              v-if="
                lossShow &&
                  topology[index + 1].nodeType != 2 &&
                  topology[index + 1].repaired != true
              "
              v-show="topology[index + 1].nodeIp != '*' && item.nodeIp != '*'"
            >
              {{
                topology[index + 1].loseRate === null ||
                topology[index + 1].loseRate === "" ||
                topology[index + 1].loseRate === undefined
                  ? "--"
                  : topology[index + 1].loseRate
              }}%
            </span>
          </div>
          <div
            class="icon-error"
            v-if="
              topology[index + 1].nodeType === 2 &&
                degradation_type != '丢包劣化'
            "
          ></div>
        </div>
        <div class="mask" v-if="item.nodeIp === '*' && iconIndex === index">
          <div class="mask-box">
            <label>节点1_IP：</label>
            <div class="mask-text">{{ topology_mask[0].nodeIp }}</div>
          </div>
          <div class="mask-box">
            <label>节点2_IP：</label>
            <div class="mask-text">{{ topology_mask[1].nodeIp }}</div>
          </div>
          <div class="mask-box" v-if="delayShow">
            <label>平均时延：</label>
            <div class="mask-text">
              {{
                topology_mask[1].delay === null
                  ? "--"
                  : topology_mask[1].delay &lt; 1
                  ? "&lt;1"
                  : parseFloat(topology_mask[1].delay).toFixed(2)
              }}ms
            </div>
          </div>
          <div class="mask-box" v-if="lossShow">
            <label>平均丢包率：</label>
            <div class="mask-text loss-mask-test">
              {{
                topology_mask[1].loseRate === null
                  ? "--"
                  : topology_mask[1].loseRate
              }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "topology",
  props: {
    iconType: {
      type: Boolean,
      default: true
    },
    lastType: {
      type: Boolean,
      default: false
    },
    delayShow: {
      type: Boolean,
      default: true
    },
    lossShow: {
      type: Boolean,
      default: true
    },
    degradation_type: {},
    data: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      topology: [],
      topology_mask: [],
      iconIndex: null,
      line_click_index: []
    };
  },
  watch: {
    data: {
      handler(val) {
        if (val.length === 0) {
          this.resetData();
        } else {
          // console.log(val)
          this.topology = JSON.parse(JSON.stringify(val));
          // console.log(this.topology)
        }
      },
      deep: true
    },
    degradation_type: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    }
  },
  computed: {
    //计算流程图总宽度
    flowChartWidth() {
      return (110 + 98) * (this.topology.length - 1) + 110 + "px";
    }
  },
  methods: {
    //图标事件
    iconClick(column, index, startRow, endRow) {
      this.line_click_index = [];
      let obj = {
        type: 1,
        index: index,
        column: column,
        data: [startRow, endRow]
      };
      this.$emit("on-click", obj);
    },
    //图标移入移出事件
    iconMoveIn(index) {
      let _self = this;
      this.iconIndex = index;
      if (_self.topology[index].nodeIp === "*") {
        this.topology_mask = this.topologyNodeId(index);
      }
    },
    iconMoveOut() {
      this.iconIndex = null;
      this.topology_mask = [];
    },
    //虚线事件
    lineClick(column, index) {
      this.line_click_index = [];
      let array = this.topology,
        data = [];
      if (array[index].nodeIp === "*" || array[index + 1].nodeIp === "*") {
        this.line_click_index = this.lineHover(index);
        data = this.topologyNodeId(index);
      } else {
        data = [array[index], array[index + 1]];
        this.line_click_index.push(index);
      }
      let obj = {
        type: 2,
        index: index,
        column: column,
        data: data
      };
      this.$emit("on-click", obj);
    },
    //获取ip为*的开始及结束数据
    topologyNodeId(index) {
      let _self = this,
        array = _self.topology,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].nodeIp === "*") {
          if (i < index && array[i].nodeIp != "*") {
            start.push(array[i]);
          }
          if (i > index && array[i].nodeIp != "*") {
            end.push(array[i]);
          }
        } else if (array[index + 1].nodeIp === "*") {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].nodeIp != "*") {
            end.push(array[i]);
          }
        }
      }
      this.topology_mask = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //渲染虚线移入数据
    lineHover(index) {
      let array = this.topology;
      let lineData = this.topologyNodeId(index);
      let startIndex = array.indexOf(lineData[0]),
        endIndex = array.indexOf(lineData[1]),
        indexArray = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (i >= startIndex && i < endIndex) {
          indexArray.push(i);
        }
      }
      return indexArray;
    },
    //清除数据
    resetData() {
      this.topology = [];
      this.topology_mask = [];
      this.iconIndex = null;
      this.line_click_index = [];
    }
  },
  mounted() {
    if (this.data.length === 0) {
      this.resetData();
    } else {
      this.topology = JSON.parse(JSON.stringify(this.data));
    }
  }
};
</script>
