<!--
*@props       @iconType   {Boolean}       图标是否显示手指样式(默认为true)
              @lastType   {Boolean}       是否最后一个图标显示手指样式（如何iconType存在，lastType无效,默认false）
              @delayShow  {Boolean}       是否显示时延(默认为true)
              @lossShow   {Boolean}       是否显示丢包率(默认为true)
              @data       {Array}         返回数据参数\
*@data        @avgDelay                  时延
              @avgLossValue             丢包
              @isSpecial                  0、不是专线，1、是专线
              @isBroken                   0、正常 ，1、中断，2、时延劣化，3、丢包劣化 4 、中断才有的ip段
              @isBuquan                   0、不是补全，1、是补全
              @ip1                        ip地址
              @deviceType                 二层设备图标类型（服务器	1，交换机	2，路由器	3，防火墙	4，未知	5）
              @deviceTypeCss              二层设备图标类型（服务器	two-icon-1，交换机	two-icon-2，路由器	two-icon-3，防火墙	two-icon-4，未知	two-icon-5 ， 虚拟交换机  virtual-node-icon）
*@events      @on-click                   图标、虚线点击事件
*@on-click    @type                      1、图标，2、虚线
              @index                     当前位置下标
              @column                    当前数据（图标当前，虚线开头）
              @data                      返回数据
              @suspect                   不可信节点
              2021/2/24 19:05 劣化颜色标注去掉补全判断:
              （32行）：(item.isBroken === 2 || item.isBroken === 3) && item.isBuquan === 0
              (81-83行)：(topology[index + 1].isBroken === 2 ||
              topology[index + 1].isBroken === 3) &&
            topology[index + 1].isBuquan === 0
-->
<template>
  <section
    class="topology"
    style="
      min-height: 130px;
      max-height: 178px;
      overflow-x: hidden;
      overflow-y: auto;
    "
  >
    <div class="topology-noData" v-if="topology.length === 0">
      <!-- 拓扑图暂无数据 -->
      {{ $t("phytopo_No_data") }}
    </div>
    <div
      class="topology-content"
      :style="{
        width: '99%',
        margin: '0 auto',
        display: 'block',
        marginLeft: '93px',
      }"
      v-else
    >
      <!-- 动态添加topology的style，如果index除以8取整等于奇数，则浮动为left，否则为right -->
      <div
        class="topology-box"
        v-for="(item, index) in topology"
        :style="getTopologyBoxStyle(index)"
        :class="[
          item.nodeColor === 'r' ? 'error-state' : '',
          item.nodeColor === 'y' ? 'inferior-state' : '',
          item.nodeColor === 'g' ? 'repair-state' : '',
        ]"
        :key="index"
      >
        <!-- 如果index除以8取整等于奇数 则icon-text在左边 line-box在右边，如果index除以8取整等于偶数 则icon-text在右边 line-box在左边 -->
        <div
          class="icon-text"
          :style="getIconStyle(index)"
          @mouseenter="iconMoveIn(index)"
          @mouseleave="iconMoveOut()"
        >
          <i
            :style="{
              'border-style': dashShow ? 'dashed' : 'none',
              'background-image': getBackgroundImg(item, index),
            }"
            :class="[
              index === 0 ? 'icon-source' : '',
              index === topology.length - 1 ? 'icon-server' : '',
              index > 0 && index < topology.length - 1
                ? item.deviceTypeCss || 'icon-route'
                : '',
              iconType && index != 0 ? 'icon-hover' : '',
              lastType && index != 0 ? 'icon-hover' : '',
              icon_click_index === index ? 'icon-click' : '',
            ]"
            @click="iconClick(item, index, topology[0], topology[index])"
          ></i>

          <!-- <div class="text">{{ item.ip1 }}</div> -->

          <Tooltip :content="item.ip1" placement="top-start">
            <div class="text">{{ item.ip2 }}</div>
          </Tooltip>
          <div
            class="text fontHidden"
            :title="
              item.deviceName +
              (index === 0
                ? $t('flowChart_sou1')
                : index === topology.length - 1
                ? item.isSpecial === 1
                  ? $t('flowChart_aim3')
                  : $t('flowChart_aim3')
                : item.isSpecial === 1
                ? ''
                : '')
            "
          >
            <div v-if="item.isShow">
              {{ item.deviceName || "" }}
              {{
                index === 0
                  ? $t("flowChart_sou1")
                  : index === topology.length - 1
                  ? item.isSpecial === 1
                    ? $t("flowChart_aim3")
                    : $t("flowChart_aim3")
                  : item.isSpecial === 1
                  ? ""
                  : ""
              }}
            </div>
          </div>
          <topologyEditVue
            v-show="
              item.ip1 != '*' && index !== 0 && index !== topology.length - 1
            "
            :nodeItem="item"
            ref="topologyRef"
            @addOk="addNameOk"
            @editName="editName"
            :isPoToPo="isPoToPo"
            :nodeIndex="index"
            :linkData="linkData"
          ></topologyEditVue>
        </div>
        <div
          class="line-box"
          :style="getLineStyle(index)"
          v-if="index < topology.length - 1"
          :class="[
            topology[item.peertopeerType == 0 ? index : index + 1].lineColor ===
            'r'
              ? 'error-line-state'
              : '',
            topology[item.peertopeerType == 0 ? index : index + 1].lineColor ===
            'y'
              ? 'inferior-line-state'
              : '',
            topology[item.peertopeerType == 0 ? index : index + 1].lineColor ===
            'g'
              ? 'repair-line-state'
              : '',
            line_click_index.indexOf(index) > -1 ? 'ine-click' : '',
          ]"
          @click="lineClick(item, index, topology[index + 1])"
        >
          <div
            class="text delay"
            :style="{ color: getBorderColor(item, index) }"
          >
            <span
              v-if="
                delayShow &&
                topology[index + 1].lineColor != 'r' &&
                topology[index + 1].suspect != true &&
                !(
                  topology[index].suspect == true &&
                  topology[index + 1].suspect != true
                )
              "
              v-show="topology[index + 1].ip1 != '*' && item.ip1 != '*'"
            >
              {{
                topology[index + 1].lineColor === 'g' ||
                topology[index + 1].avgDelay === null ||
                topology[index + 1].avgDelay === "" ||
                topology[index + 1].avgDelay === undefined
                  ? topology[index].peertopeerShowDelayLoss != true ? "--ms" :""
                  : (topology[index + 1].avgDelay &lt; 1)
                  ? "&lt;1ms"
                  : topology[index + 1].avgDelay+"ms"
              }}
            </span>
          </div>
          <!-- 普通的直线 -->
          <div
            v-if="(index + 1) % rowNodeNum !== 0"
            class="line"
            :style="{ borderColor: getBorderColor(item, index) }"
          ></div>
          <!-- 转折点的曲线 -->
          <div
            v-if="(index + 1) % rowNodeNum === 0"
            class="line"
            :style="getCurveStyle(item, index)"
          ></div>

          <div
            class="text loss"
            :style="{ color: getBorderColor(item, index) }"
          >
            <span
              v-if="
                lossShow &&
                topology[index + 1].lineColor != 'r' &&
                topology[index + 1].suspect != true &&
                !(
                  topology[index].suspect == true &&
                  topology[index + 1].suspect != true
                )
              "
              v-show="topology[index + 1].ip1 != '*' && item.ip1 != '*'"
            >
              {{
                topology[index + 1].lineColor === "g" ||
                topology[index + 1].avgLossValue === null ||
                topology[index + 1].avgLossValue === "" ||
                topology[index + 1].avgLossValue === undefined
                  ? topology[index].peertopeerShowDelayLoss != true
                    ? "--%"
                    : ""
                  : topology[index + 1].avgLossValue + "%"
              }}
            </span>
          </div>
          <div
            class="icon-error"
            v-if="
              (topology[index + 1].peertopeerLineType != 0 &&
                topology[index + 1].linkPoint) ||
              ((topology[index + 1].peertopeerLineType == 0 ||
                topology[index].peertopeerLineType == 0) &&
                topology[index].linkPoint) === true
            "
          ></div>
          <div
            v-if="topology[index + 1].isSpecial == 1"
            class="text"
            style="margin-top: 9px"
          >
            <span>{{ $t("flowChart_srl") }}</span>
          </div>
          <div style="color: #fff !important; height: 5px">
            {{ item.ip1 === "*" || item.suspect }} {{ iconIndex === index }}
          </div>

          <div
            class="mask"
            v-if="
              ((item.ip1 === '*' && item.isStarNodeMontage != 1) ||
                item.suspect) &&
              iconIndex === index
            "
          >
            <div class="mask-box">
              <label>{{ $t("flowChart_n1") }}：</label>
              <div class="mask-text">{{ topology_mask[0].ip1 || "" }}</div>
            </div>
            <div class="mask-box">
              <label>{{ $t("flowChart_n2") }}：</label>
              <div class="mask-text">{{ topology_mask[1].ip1 || "" }}</div>
            </div>
            <div class="mask-box" v-if="delayShow">
              <label>{{ $t("dash_average_delay") }}：</label>
              <div class="mask-text">
                {{
                  topology_mask[1].avgDelay === null
                    ? "--"
                    : topology_mask[1].avgDelay &lt; 1
                    ? "&lt;1"
                    : parseFloat(topology_mask[1].avgDelay).toFixed(2)










                }}ms
              </div>
            </div>
            <div class="mask-box" v-if="lossShow">
              <label>{{ $t("dash_average_packet_loss") }}：</label>
              <div class="mask-text loss-mask-test">
                {{
                  topology_mask[1].avgLossValue === null
                    ? "--"
                    : topology_mask[1].avgLossValue
                }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import topologyEditVue from './topologyEdit.vue';
import ipv6Format from "@/common/ipv6Format";

export default {
  name: "topology",
  components: {
    topologyEditVue,
  },
  props: {
    iconType: {
      type: Boolean,
      default: true,
    },
    lastType: {
      type: Boolean,
      default: false,
    },
    delayShow: {
      type: Boolean,
      default: true,
    },
    lossShow: {
      type: Boolean,
      default: true,
    },
    dashShow: {
      type: Boolean,
      default: true,
    },
    degradation_type: {},
    data_source: {},
    data: {
      type: Array,
      default: function () {
        return [];
      },
    },
    linkData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    // 区分端对端
    isPoToPo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      configData: {},
      topology: [],
      topology_mask: [],
      iconIndex: null,
      line_click_index: [],
      icon_click_index: null,
      // 节点编辑
      isEdit: false,
      nodeObj: {},
      rowNodeNum:8,  //每行显示的节点数
      topologyBoxWidth:"14%"// //每个 节点宽度
    };
  },

  watch: {
    data: {
      handler(val) {
        if (val.length === 0) {
          this.resetData();
        } else {
          let dataJson = JSON.parse(JSON.stringify(val));
          for (let index = 0, size = dataJson.length; index < size; index++) {
            const element = dataJson[index];
            element.deviceTypeCss = "";
            // 设置二层设备的图标
            if (element.deviceType != null) {
              element.deviceTypeCss = "two-icon";
            }
            // 设置虚拟主机的图标
            if (element.virtualNode == 1) {
              element.deviceTypeCss = "virtual-node-icon";
            }
          }
          this.topology = dataJson;
          console.log(this.topology, "数据。。。。。。。。。");
          this.topology.forEach((item) => {
            return this.$set(item, "isClick", false);
          });
          this.topology.forEach((item) => this.$set(item, "isShow", true));
          this.topology.forEach((item) => 
            item.ip2 = ipv6Format.abbreviateIPv6(item.ip1)
          );
          // this.generateSShapeStyles('topology-content',8)
        }
      },
      deep: true,
    },
    degradation_type: {
      handler(val) {
        // console.log(val)
      },
      deep: true,
    },
    data_source: {
      handler(val) {
        // console.log(val)
      },
      deep: true,
    },
  },
  computed: {
    linkDataFin() {
      if (this.linkData.length === 0) {
        return this.linkDatas;
      } else {
        return this.linkData;
      }
    },
    //计算流程图总宽度
    flowChartWidth() {
      return (100 + 50) * (this.topology.length - 1) + "px";
    },
  },
  methods: {
      handleStorageChange(event) {
          if (event.key === 'dark') {
              this.currentSkin = event.newValue; // 更新肤色
          }
      },
    // 链路详情默认高亮
     getLineClickIndex(index) {
      this.line_click_index.push(index);
     
     },
    getTopologyBoxStyle(index) {
      let style= {
        'float': this.isLeftOrRight(index) ? 'left' : 'right',
        'width': (index==this.topology.length-1)?'108px':(index+1)%(this.rowNodeNum)===0?'-1%':this.topologyBoxWidth,
        'position':(index + 1) % this.rowNodeNum === 0 ? 'relative' : '',
        'margin-right':(index!==0&&(index==this.rowNodeNum||index%(this.rowNodeNum*2)===this.rowNodeNum))? '160px':'',
      }
   
      return style;
    },
    getCurveStyle(item, index){
      let style={};
      // borderColor颜色
      let borderColor = "";
      // 原先逻辑
      let i = item.peertopeerType == 0 ? index : index + 1;
      if (this.topology[i].lineColor === "r") {
        // 中断
        borderColor = this.configData.breakLineColor;
      } else if (this.topology[i].lineColor === "g") {
        // 未知
        borderColor = this.configData.unknownLineColor;
      } else if (this.topology[i].lineColor === "y") {
        borderColor = this.configData.degradationLineColor;
      } else {
        borderColor = this.configData.normalLineColor;
      }
      // 设置高度
      style.height="108px";
      // 设置弧度
      style.borderRadius="0 50% 50% 0";
      // 设置边界
      style.borderTop="2px solid "+borderColor
      style.borderRight="2px solid "+borderColor
      style.borderBottom="2px solid "+borderColor;
      style.borderLeft="none";
      // 如果取余等于0后，又是偶数倍，证明是向右开口的U型 翻转一下'
      if((index+1)/this.rowNodeNum%2===0){
        style.transform="rotate(180deg)"; /* 旋转180度 */
      }
      console.log("弧线样式：",style);
      return style;
    },
    isLeftOrRight(index){
      return Math.floor(index / this.rowNodeNum) % 2 === 0 ;
    },
    getIconStyle(index){
       return { 
        order: this.isLeftOrRight(index) ? 0 : 1,
       }
    },
    getLineStyle(index) {
      let isLast=(index+1)%this.rowNodeNum===0
      let isDouble=(index+1)%(this.rowNodeNum*2)===0
      return {
        'margin-right': this.isLeftOrRight(index) ? '0px' : '-30px',
        'margin-left':isDouble?"-25px":isLast?"78px": this.isLeftOrRight(index) ? '-30px' : '0px',
        'position':(index + 1) % this.rowNodeNum === 0 ? 'absolute' : '',
        'width':isLast?'50%':'100%'
      };
    },
    getBackgroundImg(item, index) {
      // debugger
      console.log(item, 99999);
      // debugger
      let url = "";
      // 0 正常 1中断 2 劣化 3 未知
      let status = 0;
      // 先判断出节点状态
      if (item.nodeColor === "r") {
        status = 1;
      } else if (item.nodeColor === "y") {
        status = 2;
      } else if (item.nodeColor === "g") {
        status = 3;
      }
      // 虚拟节点 "VS"
      if (index === 0) {
        // 探针节点  探针 8 code 'PB'
        let findResult = this.configData.inUseIconManageConfigures.find(
          (item) => item.code === "PB"
        );
        let findResult2 = findResult.types.find((item) => item.type === status);
        url = findResult2.image;
      } else if (index === this.topology.length - 1) {
        // 目标节点  code: "GO"
        if (item.virtualNode) {
          // 虚拟节点
          //  this.this.configData.inUseIconManageConfigures
          let findResult = this.configData.inUseIconManageConfigures.find(
            (item1) => item1.code === "VS"
          );
          let findResult2 = findResult.types.find((item2) => item2.type === status);
          url = findResult2.image;
        } else if (item.deviceType) {
          let findResults4 = this.configData.deviceTypeIconManageConfigures.find(
            (obj) => obj.deviceTypeId === item.deviceType
          );
          if (findResults4) {
            // debugger
            findResults4.types.forEach((item4) => {
              if (item4.type === status) {
                // debugger

                url = item4.image;
              }
            });
            if (!url) {
              let findResult = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "GO"
              );
              let findResult2 = findResult.types.find((item) => item.type === status);
              url = findResult2.image;
            }
          } else {
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item) => item.code === "GO"
            );
            let findResult2 = findResult.types.find((item) => item.type === status);
            url = findResult2.image;
          }
        } else {
          let findResult = this.configData.inUseIconManageConfigures.find(
            (item) => item.code === "GO"
          );
          let findResult2 = findResult.types.find((item) => item.type === status);
          url = findResult2.image;
        }
      } else {
        // 中间节点 code: "IN"
        if (item.virtualNode) {
          // 虚拟节点
          //  this.this.configData.inUseIconManageConfigures
          let findResult = this.configData.inUseIconManageConfigures.find(
            (item1) => item1.code === "VS"
          );
          let findResult2 = findResult.types.find((item2) => item2.type === status);
          url = findResult2.image;
        } else if (item.deviceType) {
          let findResults4 = this.configData.deviceTypeIconManageConfigures.find(
            (obj) => obj.deviceTypeId === item.deviceType
          );
          if (findResults4) {
            // debugger
            findResults4.types.forEach((item4) => {
              if (item4.type === status) {
                // debugger

                url = item4.image;
              }
            });
            if (!url) {
              // twoDeviceType区分二层节点
              if (item.twoDeviceType) {
                let findResult = this.configData.inUseIconManageConfigures.find(
                  (item) => item.code === "SLN"
                );
                let findResult2 = findResult.types.find((item) => item.type === status);
                url = findResult2.image;
              } else {
                let findResult = this.configData.inUseIconManageConfigures.find(
                  (item) => item.code === "IN"
                );
                let findResult2 = findResult.types.find((item) => item.type === status);
                url = findResult2.image;
              }
            }
          } else {
            // debugger
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item) => item.code === "IN"
            );
            let findResult2 = findResult.types.find((item) => item.type === status);
            url = findResult2.image;
          }
        } else {
          // 判断是否是信号节点，如果不是走下面
          if (item.ip1 == "*") {
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item) => item.code === "UN"
            );
            let findResult2 = findResult.types.find((item) => item.type === status);
            url = findResult2.image;
          } else {
            let findResult = this.configData.inUseIconManageConfigures.find(
              (item) => item.code === "IN"
            );
            let findResult2 = findResult.types.find((item) => item.type === status);
            url = findResult2.image;
          }

          // debugger
        }
      }

      console.log(url, "url");

      return "url(data:image/png;base64," + url + ")";
    },
    getBorderColor(item, index) {
      let borderColor = "";
      // 原先逻辑
      let i = item.peertopeerType == 0 ? index : index + 1;
      if (this.topology[i].lineColor === "r") {
        // 中断
        borderColor = this.configData.breakLineColor;
      } else if (this.topology[i].lineColor === "g") {
        // 未知
        borderColor = this.configData.unknownLineColor;
      } else if (this.topology[i].lineColor === "y") {
        borderColor = this.configData.degradationLineColor;
      } else {
        borderColor = this.configData.normalLineColor;
      }

      return borderColor;
    },
    // 获取图标配置
    async findInUseIconManageConfigureVoByType() {
      // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标

      try {
        const res = await this.$http.post(
          "/iconmanage/findInUseIconManageConfigureVoByType",
          { type: 2,currentSkin:this.currentSkin }
        );
        if (res.code === 1) {
          this.configData = res.data;
        }
      } catch (err) {}
    },
    //图标事件
    iconClick(column, index, startRow, endRow) {
      console.log(column, index, startRow, endRow);
      //  this.$refs.topologyRef[0].linkId = this.linkId
      if (this.linkData.length == 0) {
        console.log(this.$refs.topologyRef[index]);
      }
      this.$refs.topologyRef[index].changeEdit();
      this.topology.forEach((item) => (item.isShow = true));
      this.topology.forEach((item) => {
        if (item.ip1 === column.ip1) {
          item.isClick = true;
        } else {
          item.isClick = false;
        }
      });
      //  console.log(this.topology,'为什么没变化')
      this.line_click_index = [];
      this.icon_click_index = index;
      let array = this.topology;
      var data = [startRow, endRow];
      if (array[index].suspect == true) {
        data = this.handleSuspectClick(index);
      }
      let obj = {
        type: 1,
        index: index,
        column: column,
        data: data,
      };
      this.$emit("on-click", obj);
    },
    //图标移入移出事件
    iconMoveIn(index) {
      let _self = this;
      this.iconIndex = index;
      if (_self.topology[index].ip1 === "*") {
        console.log("------------");
        this.topology_mask = this.topologyNodeId(index);
        // this.topology_mask[0].ip1 =  ipv6Format.formatIPv6Address(this.topology_mask[0].ip1,120);
        // console.log('start',this.topology_mask[0].ip1);
        
        // this.topology_mask[1].ip1 =  ipv6Format.formatIPv6Address(this.topology_mask[1].ip1,120);
        // console.log("this.topology_mask", this.topology_mask);
      } else if (_self.topology[index].suspect == true) {
        this.topology_mask = this.handleSuspectClick(index);
      }
    },
    iconMoveOut() {
      this.iconIndex = null;
      this.topology_mask = [];
    },
    //虚线事件
    lineClick(column, index, columnlater) {
      this.line_click_index = [];
      this.icon_click_index = null;
      let array = this.topology,
        data = [];
      var pointIndex = null,
        pointArr = [];
      if (array[index].ip1 === "*" || array[index].suspect == true) {
        pointIndex = index;
      }
      if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
        pointIndex = index + 1;
      }
      if (pointIndex && pointIndex >= 0) {
        for (let indexs = pointIndex; indexs < array.length; indexs++) {
          if (array[indexs].ip1 != "*" && array[indexs].suspect != true) {
            break;
          }
          if (array[indexs].ip1 == "*" || array[indexs].suspect == true) {
            pointArr.push(indexs);
          }
        }
        for (let indexs = pointIndex; indexs > 0; indexs--) {
          if (array[indexs].ip1 != "*" && array[indexs].suspect != true) {
            break;
          }
          if (array[indexs].ip1 == "*" || array[indexs].suspect == true) {
            pointArr.push(indexs);
          }
        }
      }
      if (pointArr.length > 0) {
        pointArr.sort((a, b) => {
          return a - b;
        });
        pointArr.push(pointArr[0] - 1);
        pointArr = Array.from(new Set(pointArr));
        this.line_click_index = pointArr;
      }

      if (array[index].ip1 === "*" || array[index].suspect == true) {
        data = this.topologyNodeIdIndex(index);
      } else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
        data = this.topologyNodeIdIndex(index);
      } else {
        data = [array[index], array[index + 1]];
        this.line_click_index.push(index);
      }
      let obj = {
        type: 2,
        index: index,
        column: column,
        data: data,
        suspect: columnlater.suspect,
      };
      this.$emit("on-click", obj);
    },
    //获取ip为*，不可信节点的开始及结束数据
    topologyNodeIdIndex(index) {
      let _self = this,
        array = _self.topology,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].ip1 === "*" || array[index].suspect == true) {
          if (i < index && array[i].ip1 != "*" && array[i].suspect != true) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
            end.push(array[i]);
          }
        } else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
            end.push(array[i]);
          }
        }
      }
      this.topology_mask = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //获取ip为*的开始及结束数据
    topologyNodeId(index) {
      // debugger
      let _self = this,
        array = _self.topology,
        start = [],
        end = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (array[index].ip1 === "*") {
          if (i < index && array[i].ip1 != "*" && array[i].suspect != true) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*" && array[i].suspect != true) {
            end.push(array[i]);
          }
        } else if (array[index + 1].ip1 === "*") {
          if (i <= index) {
            start.push(array[i]);
          }
          if (i > index && array[i].ip1 != "*") {
            end.push(array[i]);
          }
        } else if (array[index].suspect) {
          if (i <= index) {
            start.push(array[i - 1]);
          }
          if (i > index) {
            end.push(array[i]);
          }
        }
      }
      this.topology_mask = [start[start.length - 1], end[0]];
      return [start[start.length - 1], end[0]];
    },
    //渲染虚线移入数据
    lineHover(index) {
      let array = this.topology;
      let lineData = this.topologyNodeId(index);
      let startIndex = array.indexOf(lineData[0]),
        endIndex = array.indexOf(lineData[1]),
        indexArray = [];
      for (let i = 0, len = array.length; i < len; i++) {
        if (i >= startIndex && i < endIndex) {
          indexArray.push(i);
        }
      }
      return indexArray;
    },
    //清除数据
    resetData() {
      this.topology = [];
      this.topology_mask = [];
      this.iconIndex = null;
      this.line_click_index = [];
      this.icon_click_index = null;
    },

    handleSuspectClick(index) {
      let array = this.topology;
      var data = [];
      if (array[index].suspect == true) {
        data = this.topologyNodeIdIndex(index);
      } else if (array[index + 1].ip1 === "*" || array[index + 1].suspect == true) {
        data = this.topologyNodeIdIndex(index);
      } else {
        data = [array[index], array[index + 1]];
      }
      return data;
    },
    // 新增名字成功
    addNameOk(obj) {
      console.log("成功了", obj);
      this.topology.forEach((item) => (item.isShow = true));
      this.topology.forEach((item) => {
        if (item.ip1 === obj.ip1) {
          item.deviceName = obj.text;
        }
        item.isClick = false;
      });
      console.log(this.topology);
    },
    editName(obj) {
      this.nodeObj = obj;
      this.topology.forEach((item) => {
        if (item.ip1 === obj.ip1) {
          item.isShow = false;
        }
      });
    },
  },
  mounted() {
     // 监听 storage 事件
     window.addEventListener('storage', this.handleStorageChange);
    if (this.data.length === 0) {
      this.resetData();
    } else {
      this.topology = JSON.parse(JSON.stringify(this.data));
      console.log(this.topology);
    }
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  created() {
    this.findInUseIconManageConfigureVoByType();
  },
};
</script>
<style scoped >
</style>
