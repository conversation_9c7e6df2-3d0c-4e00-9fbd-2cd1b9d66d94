<!--  -->
<template>
  <div class="w-edit-container">
    <div class="w-edit-btn" v-if="nodeItem.isClick">
      <img
        src="@/assets/btn-img/btn-edit.png"
        v-if="isEdit"
        alt=""
        @click.self="editClick"
      />
      <Form ref="form" v-else :model="formValidate" :rules="ruleValidate">
        <FormItem prop="text">
          <Input
            size="small"
            v-model="formValidate.text"
            @on-enter="addName"
            @on-blur="addName"
            :maxlength="50"
            style="width: 100px"
          />
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>

export default {
name: '',
props: {
    nodeItem: {
        type:Object
    },
    linkData: {
        type:[Array,Object]
    },
    isPoToPo: {
        type:Boolean,
        default:false
    },
    // 节点id
    nodeIndex: {
        type:Number,
    }
},
components: {},
data() {
return {
    formValidate:{
         text:'',


    },
  
    linkId:null,
    isEdit:true,
    isCheck:false,
    ruleValidate: {
        text:[{ max:50,message:this.$t('comm_length50'),trigger: 'change'}]
    }
  

}
},
computed: {},
watch: {
    nodeItem: {
        handler() {
            // console.log('变化了变化了')
            // this.isEdit = true
            this.formValidate.text = this.nodeItem.deviceName

        },
        deep:true,
        immediate:true
    }

},
methods: {
    changeEdit() {
        this.isEdit = true
    },
    editClick() {
        debugger
        this.isEdit = false
        this.$emit('editName',this.nodeItem)
    },
  
    addName() {
         console.log(this.linkData,this.isPoToPo)
        //  debugger
      
      console.log(this.linkData,'最终最终最终')
        let linkId;
        if (this.linkData instanceof Array && this.linkData.length !== 0) {
            // 区分端对端
            if(this.isPoToPo) {
                console.log(this.nodeItem,this.nodeIndex,'节点信息') 
            
                linkId = this.linkData[this.nodeIndex].linkId
            }else {
                linkId = this.linkData[0].linkId

            }
            
        } else if (this.linkData instanceof Object &&  this.linkData.length !== 0){
            console.log('elif')
            linkId = this.linkData.linkId
        } 
        this.$refs.form.validate(isOk => {
            if(isOk) {
                // 校验通过
                let param = {
                    ip:this.nodeItem.ip1,
                    linkId:linkId,
                    name:this.formValidate.text
                }
             
          this.$http.PostJson("trend/api/saveOrUpdateInterface", param).then(res => {
            if(res.code === 1) {
                this.$Message.success(this.$t(this.$t('comm_operate') + this.$t('comm_success1')))
               
               let obj = {...this.nodeItem,text:this.formValidate.text}
                this.$emit('addOk',obj)
            }
                //  console.log(res,'1111111')
               })
            // this.isEdit = true
            }
        })
       

    }
   
},
created() {

    

},
mounted() {
    // console.log(this.nodeItem,'节点信息。。。。。。')
   

},
beforeDestroy() {
    console.log('销毁前。。。。。')
}

}
</script>
<style scoped lang='less'>
</style>