
var englishMap = { "src.flowAnalysis": "Flow Analysis", "src.monitorTargetSpreadMap": "Real time monitoring of target", "src.pathTopologyfullscreen": "Path Topo", "src.iconManage": "Icon Management", "src.HostName": "The host name cannot contain spaces", "src.FillInMacMatching": "Fill in mac matching IP", "src.T5CBK": "Task ******** created by kk", "src.StartingTime": "Starting time", "src.EditView": "Edit view", "src.three": "three", "src.CenterDisplay": "Center display", "src.NVRPANC": "Numeric validation rule parameters are not configured.", "src.PETCPN": "Please enter the correct phone number", "src.PEANI": "Please enter a non-negative integer", "src.TCPDestination": "TCP destination port", "src.ExportPNG": "Export PNG", "src.DLQDA": "LL Analysis", "src.selectionPeriod": "selection period", "src.AlarmPeriodT": "Alarm period T", "src.OSTSTT": "Online speed test>PC speed test terminal", "src.sourceList": "source list", "src.UserManagement": "Users", "src.one": "one", "src.StartingTTL": "Starting TTL", "src.five": "five", "src.SMSService": "SMS service", "src.DDService": "DingTalk Service", "src.EnterpriseWechatService": "Enterprise wechat Service", "src.Article/Page": "Article / Page", "src.filter": "filter", "src.LogIn": "Log in", "src.Page": "Page", "src.NoData": "No data", "src.dedicatedLine": "dedicated line", "src.DHR": "LL Records", "src.two": "two", "src.DTQR": "Dial test quality report", "src.CCSC": "Cannot contain special characters", "src.IRT": "Interrupt recovery threshold", "src.RV": "Reachability", "src.event": "event", "src.EM": "Equipment manufacturer", "src.DM": "Equipment model", "src.DOA21SLF(FD1H3MA4S": "Deterioration occurred at 2023-04-27 14:50:00, suspected link failure (*********-**********), failure duration: 1 hour, 3 minutes and 44 seconds", "src.October": "October", "src.star": "star", "src.PEAPI": "Please enter a positive integer", "src.TrunkLineMonitoring": "RL Monitor", "src.July": "July", "src.AlarmTrigger": "Alarm trigger", "src.testingReport": "POC Reports", "src.testSpeedServer": "Test Speed Server", "src.legalresourcepool": "Legal Resource Pool", "src.dfDialTestProbe": "df dial test probe", "src.lyjSmokeTask": "lyj smoke task 22", "src.probePort": "probe port", "src.SM3M": "Smoke mission 30 minutes", "src.[yyyyYear][m": "[yyyy year] [m month]", "src.other": "other", "src.DGT": "Degradation generation threshold", "src.pathTopology": "Path Topo", "src.magnify": "magnify", "src.QIRD": "Quality inspection report diagram", "src.DOA21SLF(FD5MA4S": "Deterioration occurred at 2023-04-27 15:02:00, suspected link failure (********-6.1.37.6), failure duration: 51 minutes and 44 seconds", "src.DeviceManagement": "Device", "src.permissionDenied": "permission denied", "src.June": "June", "src.GroupManagement": "Groups", "src.DeviceInformation": "Device Information", "src.RelayStatistics": "Relay statistics", "src.Year": "Year", "src.ChinaUnicom": "China Unicom", "src.OnlineSpeedTest": "Speed Test", "src.fiberDiagram": "fiber diagram", "src.lyjSmokeMission": "lyj smoke mission 3", "src.full-screenDisplay": "full-screen display", "src.PortInformation": "Port information", "src.TRTOPRATAL": "The request timed out, please refresh and try again later.", "src.TFCOBA1COLAN": "12-digit letters or numbers", "src.RQDA": "RL Analysis", "src.pleaseChoose": "please choose", "src.operationFailed": "operation failed", "src.NumberOfAlarms": "Number of alarms N", "src.IGT": "Interrupt generation threshold", "src.RIM": "Relay Lines", "src.moon": "moon", "src.PositioningTimes": "Positioning times M", "src.TIVMBGTOETAI(": "The input value must be greater than or equal to an integer (0)", "src.DTTM": "Probe Tasks", "src.FaultList": "Alerts", "src.associatedDisplay": "IP Notes", "src.OM": "Organizations", "src.MessagingService": "Messaging service","src.speciallinereport": "LL Report",  "src.purposeList": "purpose list", "src.systemMessage": "Syslnfo", "src.NAPCTA": "Network abnormality, please contact the administrator", "src.PleaseSelect": "Please select a date", "src.six": "six", "src.SystemConfiguration": "SysConfig", "src.congestionThreshold": "congestion threshold", "src.DingTalk": "DingTalk", "src.securityAudit": "Audit Log", "src.summaryGraph": "summary graph", "src.PathAlarmTrend": "Path alarm trend", "src.flow": "flow", "src.MessageTemplate": "Message Tmp", "src.PleaseEnterA": "Please enter a value", "src.NotLoggedIn": "Not logged in", "src.CollectorManagement": "Collector", "src.PLAT": "Private line alarm trends", "src.SystemLog": "System Log", "src.NAPRATA": "Network abnormality, please refresh and try again", "src.5EISE": "500 error, internal server error", "src.Speed​​record": "Speed ​​record", "src.forward": "forward", "src.MinimumBytes": "Minimum bytes", "src.PoorQualityTop": "Poor quality top", "src.PoorQualityTOP": "Poor quality TOP", "src.LogQuery": "API Log", "src.PTM": "Path Topo", "src.PYM": "Physical Topo", "src.SmokeMission": "Smoke mission 1 hour", "src.dataExists": "data exists", "src.LST1S": "lyj smoke task 10 seconds", "src.PLADOA21SLF(FD1H1MA4S": "Packet loss and deterioration occurred at 2023-04-27 14:52:00, suspected link failure (6.1.52.5-6.1.52.6), failure duration: 1 hour, 1 minute and 44 seconds", "src.DeviceDiscovery": "Device Find", "src.DLM": "LL Monitor", "src.RGS": "Strategies", "src.DLC": "Leased Line Congestion", "src.H3C": "H3C", "src.RG": "RG", "src.MP": "MP", "src.SF": "SF", "src.O'+R+'+R+'CAATBE": " only permit min-max characters", "src.PushRules": "Push rules", "src.clearList": "Cleare list", "src.wifiAnalysis": "WiFi", "src.PEAPITVI[": "Please enter a positive integer, the value is [1,10]", "src.TTNASOCP": "Test the number and size of captured packets", "src.just": "just", "src.back": "back", "src.IllegalOperation": "Illegal operation", "src.SelectDate": "Select date", "src.roleManagement": "Roles", "src.CRAT": "China Radio and Television", "src.FITIAMTM": "Fill in the ip and match the MAC", "src.RelayHealthRecords": "RL Records", "src.WindowSizeW": "Window size W", "src.January": "January", "src.PECPNN": "Please enter correct phone number/landline number", "src.ChinaTelecom": "China Telecom", "src.TraceLimited": "Trace limited hop count", "src.November": "November", "src.I|':L|'": "i.time.before') || 'before' : locale('i.time.after') || 'after", "src.PortCongestion": "Port Congestion", "src.MACInformation": "MAC information", "src.autodiscover": "Discovery", "src.ListIsEmpty": "List is empty", "src.DRT": "Deterioration recovery threshold", "src.RelayAlarmTrend": "Relay alarm trend", "src.OST​R": "Online speed test>Speed ​​test record", "src.EquipmentType": "Equipment type", "src.GoBack5Pages": "Go back 5 pages", "src.PLDM": "Leased Lines", "src.GoForward5Pages": "Go forward 5 pages", "src.pleaseEnter": "Please enter ", "src.CRFPN": "Cisco router first port number", "src.LatencyDegradation": "Delay deterioration", "src.ETD": "End to End", "src.PortIndicators": "Port indicators", "src.NextPage": "Next page", "src.August": "August", "src.common": "common", "src.DTHR": "PT Records", "src.ParameterError": "Parameter error", "src.Cisco": "CS", "src.IOA21SLF(FD1H1MA4S": "Interruption occurred at 2023-04-27 14:43:00, suspected link failure (********-********), failure duration: 1 hour, 10 minutes and 44 seconds", "src.TIVMBAN": "The input value must be a number", "src.PNPIS": "Proactive Network Performance Insight System", "src.TTPVRPANC": "The time period validation rule parameters are not configured.", "src.PHT": "Probe heartbeat threshold", "src.Cancel": "Cancel", "src.DataDoesNotExist": "Data does not exist", "src.Huawei": "HW", "src.September": "September", "src.ARPInformation": "ARP information", "src.RouteFluctuation": "Route instability", "src.NoFilterResults": "No filter results yet", "src.PEYVE": "Please enter your vaild email", "src.PE6ELNOS(SATSBALTTOLNAPM": "Please enter 6-20 English letters, numbers or symbols (excluding spaces), and there should be at least two types of letters, numbers and punctuation marks.", "src.PLDGT": "Packet loss deterioration generation threshold", "src.PleaseEnterThe": "Please enter the number", "src.May": "May", "src.cisco'sSecond": "cisco's second port", "src.OCIIA": "Only Chinese input is allowed", "src.Delay/packet": "Delay/packet loss", "src.interrupt": "Interrupt", "src.Taobao": "Taobao", "src.realTimeMap": "real time map", "src.Four": "Four", "src.PETCFN": "Please enter the correct fax number)", "src.DialTestFrequency": "Dial test frequency", "src.UTTTB": "Used to test TTL2 beginning", "src.processInformation": "Processes", "src.RFQE": "Reports", "src.loading": "loading", "src.PEANB[": "Please enter a number between [0,1]", "src.ChatMessage": "wechat", "src.ShortMessage": "SMS", "src.CredentialManagement": "Credential management", "src.BigScreen": "Big screen", "src.DOM": "OIDs", "src.JumpTo": "Jump to", "src.LinkInformation": "Link information", "src.sky": "sky", "src.IOA1O2SLF(FD1H9MA4S": "Interruption occurred at 14:44:00 on 2023-04-27, suspected link failure (*********-10.20.40.204), failure duration: 1 hour, 9 minutes and 44 seconds", "src.March": "March", "src.DecryptedContent:": "Decrypted content:", "src.DDGT": "Delay deterioration generation threshold", "src.PEANB[MV": "Please enter a number between [0, maximum value]", "src.ServerMonitoring": "Server", "src.AIOA21SLF(FD4MA4S": "An interruption occurred at 2023-04-27 15:49:00, suspected link failure (10.20.10.2-10.20.40.2), failure duration: 4 minutes and 44 seconds", "src.physicalTopology": "Physical Topo", "src.RelayInformation": "Relay information", "src.LinkQualityAnalysis": "Link Analysis", "src.PEAVIN": "Please enter a valid ID number", "src.Real-timeAlarm": "Real-time alarm", "config.PETCPNTPNITFNUS": "Please enter the correct project name. The project name is the folder name under src/modules", "src.April": "April", "src.dashBoard": "DashBoard", "src.Hour": "Hour", "src.InputCannotBe": "Input cannot be empty", "src.CTM": "SNMP Tasks", "src.DVM": "Default Value", "src.PETCLN": "Please enter the correct landline number)", "src.Trend": "Trend", "src.Second": "Second", "src.TLMD": "Monitor", "src.voice": "Voice", "src.EnterpriseWechat": "Enterprise wechat", "src.total": "total", "src.strip": "strip", "src.PortDetails": "Port details", "src.dataBackup": "Data Backup", "src.OL'+R+'+R+'CA": "Only length ' + rule.minLength + '-' + rule.maxLength + ' characters allowed", "src.December": "December", "src.Inquire": "Inquire", "src.EGI": "Error getting institution", "src.TIVMBAIN": "The input value must be an integer number", "src.EGE": "Error getting enterprise", "src.aggregateGraph": "aggregate graph", "src.PreviousPage": "Previous page", "src.SM1M": "Smoke mission 10 minutes", "src.realTimeMonitoring": "real time monitoring", "src.Sure": "Sure", "src.NoMatchingData": "No matching data", "src.path": "path", "src.MessageRecord": "Message Logs", "src.ViewList": "View list", "src.AlarmRecovery": "Alarm recovery", "src.minute": "minute", "src.zoomOut": "zoom out", "src.PLD": "Packet loss deterioration", "src.PCSpeedTestTerminal": "PC speed test terminal", "src.MailService": "MsgSrv", "src.DOA21SLF(FD4MA4S#21PLADO": "Deterioration occurred at 2023-04-27 15:04:00, suspected link failure (**********-**********), failure duration: 49 minutes and 44 seconds #$ 2023-04-27 14:46:00 Packet loss and deterioration occurred, ", "src.reset": "reset", "src.CBD": "Content before decryption:", "src.TestImportDevice": "Test import device 2", "src.lineManagement": "line management", "src.PleaseEnterPassword": "Please enter password", "src.Clear": "Clear", "src.EnterSearchContent": "Enter search content", "src.treeShape": "tree shape", "src.TIVMBGTAI(": "The input value must be greater than an integer (0)", "src.DQDA": "PT Analysis", "src.ITDNE": "interface that does not exist", "src.CollectorOffline": "Collector offline", "src.IllegalDeviceAccess": "Illegal access alarm", "src.PortStatusChange": "Port monitoring", "src.EndTime": "End Time", "src.mail": "Mail", "src.IOA21SLF(FD1H9MA4S": "Interruption occurred at 2023-04-27 14:44:00, suspected link failure (*********-************), failure duration: 1 hour, 9 minutes and 44 seconds", "src.I|'I(<6R=P/1+(|'+D": "i.time.just') || 'just';else if (diff < 60000) resStr = parseInt(diff / 1000) + (locale('i.time.seconds') || 'seconds') + dirStr;else ", "src.UDPDestination": "UDP destination port", "src.week": "week", "src.TIVRPINC": "The integer validation rule parameter is not configured.", "src.PETCIA": "Please enter the correct IP address", "src.PICMAC": "Please enter the correct MAC", "src.all": "all", "src.Fault": "Fault", "src.day": "day", "src.point": "point", "src.ChinaMobile": "China Mobile", "src.PLDGTES": "Packet loss deterioration generation threshold enabled status", "src.February": "February", "src.Cannot.exceed": "Cannot exceed", "src.characters": "characters", "src.checkMax": "Please enter a number between min-max", "src.checkLength": "The input length should be between [min, max]", "virt.host.name": "Host Manage" }


var chineseMap = { "src.flowAnalysis": "流量分析", "src.monitorTargetSpreadMap": "实时监控目标分布", "src.pathTopologyfullscreen": "路径拓扑", "src.iconManage": "图标管理", "src.HostName": "宿主机名称不能包含空格", "src.FillInMacMatching": "填写mac匹配IP", "src.T5CBK": "kk创建的任务********", "src.StartingTime": "开始时间", "src.EditView": "编辑视图", "src.three": "三", "src.CenterDisplay": "居中显示", "src.NVRPANC": "数值验证规则参数未配置.", "src.PETCPN": "请输入正确的电话号码", "src.PEANI": "请输入非负整数", "src.TCPDestination": "TCP目标端口", "src.ExportPNG": "导出PNG", "src.DLQDA": "专线质差分析", "src.selectionPeriod": "选择时间", "src.AlarmPeriodT": "告警周期T", "src.OSTSTT": "在线测速>PC测速端", "src.sourceList": "源列表", "src.UserManagement": "用户管理", "src.one": "一", "src.StartingTTL": "起始TTL", "src.five": "五", "src.SMSService": "短信服务", "src.DDService": "钉钉服务", "src.EnterpriseWechatService": "企业微信服务", "src.Article/Page": "条/页", "src.filter": "筛选", "src.LogIn": "登录", "src.Page": "页", "src.NoData": "暂无数据", "src.dedicatedLine": "专线", "src.DHR": "专线健康档案", "src.two": "二", "src.DTQR": "拨测质量报告", "src.CCSC": "不可含有特殊字符", "src.IRT": "中断恢复阈值", "src.RV": "可达性验证", "src.event": "事件", "src.EM": "设备厂商", "src.DM": "设备型号", "src.DOA21SLF(FD1H3MA4S": "2023-04-27 14:50:00发生劣化，疑似故障链路（*********-**********），故障历时：1小时3分44秒", "src.October": "10 月", "src.star": "星型", "src.PEAPI": "请输入正整数", "src.TrunkLineMonitoring": "中继线路监测", "src.July": "7 月", "src.AlarmTrigger": "告警触发", "src.testingReport": "测试报告", "src.testSpeedServer": "测速服务器", "src.legalresourcepool": "合法资源池", "src.dfDialTestProbe": "df拨测探针", "src.lyjSmokeTask": "lyj冒烟任务22", "src.probePort": "探针端口", "src.SM3M": "冒烟任务30分钟", "src.[yyyyYear][m": "[yyyy年] [m月]", "src.other": "其他", "src.DGT": "劣化生成阈值", "src.pathTopology": "路径拓扑", "src.magnify": " 放 大 ", "src.QIRD": "质量检测报告图", "src.DOA21SLF(FD5MA4S": "2023-04-27 15:02:00发生劣化，疑似故障链路（********-6.1.37.6），故障历时：51分44秒", "src.DeviceManagement": "设备管理", "src.permissionDenied": "没有权限", "src.June": "6 月", "src.GroupManagement": "分组管理", "src.DeviceInformation": "设备信息", "src.RelayStatistics": "中继统计", "src.Year": "年", "src.ChinaUnicom": "中国联通", "src.OnlineSpeedTest": "在线测速", "src.fiberDiagram": "纤维图", "src.lyjSmokeMission": "lyj冒烟任务3", "src.full-screenDisplay": "全屏显示", "src.PortInformation": "端口信息", "src.TRTOPRATAL": "请求超时，请稍后刷新重试", "src.TFCOBA1COLAN": "格式只能为12位的字母和数字的组合", "src.RQDA": "中继质差分析", "src.pleaseChoose": "请选择", "src.operationFailed": "操作失败", "src.NumberOfAlarms": "告警次数N", "src.IGT": "中断生成阈值", "src.RIM": "中继信息管理", "src.moon": "月", "src.PositioningTimes": "定位次数M", "src.TIVMBGTOETAI(": "输入值必需大于等于整数（0）", "src.DTTM": "拨测任务管理", "src.FaultList": "故障清单", "src.associatedDisplay": "关联显示", "src.OM": "机构管理", "src.MessagingService": "消息服务", "src.speciallinereport": "专线线路报表", "src.purposeList": "目的列表", "src.systemMessage": "系统信息", "src.NAPCTA": "网络异常，请联系管理员", "src.PleaseSelect": "请选择日期", "src.six": "六", "src.SystemConfiguration": "系统配置", "src.congestionThreshold": "拥塞阈值", "src.DingTalk": "钉钉", "src.securityAudit": "安全审计", "src.summaryGraph": "统计图", "src.PathAlarmTrend": "路径告警趋势", "src.flow": "流量", "src.MessageTemplate": "消息模板", "src.PleaseEnterA": "请输入值", "src.NotLoggedIn": "未登录", "src.CollectorManagement": "采集器管理", "src.PLAT": "专线告警趋势", "src.SystemLog": "系统日志", "src.NAPRATA": "网络异常，请刷新重试", "src.5EISE": "500错误，服务器内部错误", "src.Speed​​record": "测速记录", "src.forward": "前", "src.MinimumBytes": "最小字节", "src.PoorQualityTop": "质差top", "src.PoorQualityTOP": "质差TOP", "src.LogQuery": "日志查询", "src.PTM": "路径拓扑管理", "src.PYM": "物理拓扑管理", "src.SmokeMission": "冒烟任务1个小时", "src.dataExists": "数据存在", "src.LST1S": "lyj冒烟任务10秒", "src.PLADOA21SLF(FD1H1MA4S": "2023-04-27 14:52:00发生丢包劣化，疑似故障链路（6.1.52.5-6.1.52.6），故障历时：1小时1分44秒", "src.DeviceDiscovery": "设备发现规则", "src.DLM": "专线线路监测", "src.RGS": "报告生成策略", "src.DLC": "专线拥塞", "src.H3C": "华三", "src.RG": "锐捷", "src.MP": "迈普", "src.SF": "深信服", "src.O'+R+'+R+'CAATBE": "只允许输入 min-max 个字符", "src.PushRules": "推送规则", "src.clearList": "清除列表", "src.wifiAnalysis": "wifi分析", "src.PEAPITVI[": "请输入正整数，值为[1,10]", "src.TTNASOCP": "测试抓包个数和大小", "src.just": "刚刚", "src.back": "后", "src.IllegalOperation": "非法操作", "src.SelectDate": "选择日期", "src.roleManagement": "角色管理", "src.CRAT": "中国广电", "src.FITIAMTM": "填写ip，匹配MAC", "src.RelayHealthRecords": "中继健康档案", "src.WindowSizeW": "窗口大小W", "src.January": "1 月", "src.PECPNN": "请输入正确的电话号码/固话号码", "src.ChinaTelecom": "中国电信", "src.TraceLimited": "Trace受限跳数", "src.November": "11 月", "src.I|':L|'": "i.time.before') || '前' : locale('i.time.after') || '后", "src.PortCongestion": "端口拥塞", "src.MACInformation": "MAC信息", "src.autodiscover": "自动发现", "src.ListIsEmpty": "列表为空", "src.DRT": "劣化恢复阈值", "src.RelayAlarmTrend": "中继告警趋势", "src.OST​R": "在线测速>测速记录", "src.EquipmentType": "设备类型", "src.GoBack5Pages": "向后 5 页", "src.PLDM": "专线资料管理", "src.null": "12月", "src.GoForward5Pages": "向前 5 页", "src.pleaseEnter": "请输入", "src.CRFPN": "思科路由器第一个端口号", "src.LatencyDegradation": "时延劣化", "src.ETD": "端到端拓扑图", "src.PortIndicators": "端口指标", "src.NextPage": "下一页", "src.August": "8 月", "src.common": "共", "src.DTHR": "拨测健康档案", "src.ParameterError": "参数错误", "src.Cisco": "思科", "src.IOA21SLF(FD1H1MA4S": "2023-04-27 14:43:00发生中断，疑似故障链路（********-********），故障历时：1小时10分44秒", "src.TIVMBAN": "输入值必需是数字", "src.PNPIS": "主动式网络性能透视系统", "src.TTPVRPANC": "时间周期验证规则参数未配置.", "src.PHT": "探针心跳阈值", "src.Cancel": "取消", "src.DataDoesNotExist": "数据不存在", "src.Huawei": "华为", "src.September": "9 月", "src.ARPInformation": "ARP信息", "src.RouteFluctuation": "路由波动", "src.NoFilterResults": "暂无筛选结果", "src.PEYVE": "请输入正确的邮箱", "src.PE6ELNOS(SATSBALTTOLNAPM": "请输入6-20位英文字母、数字或者符号（除空格），且字母、数字和标点符号至少包含两种", "src.PLDGT": "丢包劣化生成阈值", "src.PleaseEnterThe": "请输入数字", "src.May": "5 月", "src.cisco'sSecond": "思科的第二个端口", "src.OCIIA": "仅允许输入中文", "src.Delay/packet": "时延/丢包", "src.interrupt": "中断", "src.Taobao": "淘宝", "src.realTimeMap": "及时地图", "src.Four": "四", "src.PETCFN": "请输入正确的传真号码）", "src.DialTestFrequency": "拨测频率", "src.UTTTB": "用于测试TTL2开头", "src.processInformation": "进程信息", "src.RFQE": "质量检测报告", "src.loading": "加载中", "src.PEANB[": "请输入[0,1]之间的数字", "src.ChatMessage": "微信", "src.ShortMessage": "短信", "src.CredentialManagement": "凭证管理", "src.BigScreen": "大屏", "src.DOM": "字典OID管理", "src.JumpTo": "跳至", "src.LinkInformation": "链路信息", "src.sky": "天", "src.IOA1O2SLF(FD1H9MA4S": "2023-04-27 14:44:00发生中断，疑似故障链路（*********-10.20.40.204），故障历时：1小时9分44秒", "src.March": "3 月", "src.DecryptedContent:": "解密后内容:", "src.DDGT": "时延劣化生成阈值", "src.PEANB[MV": "请输入[0,最大值]之间的数字", "src.ServerMonitoring": "服务器监控", "src.AIOA21SLF(FD4MA4S": "2023-04-27 15:49:00发生中断，疑似故障链路（10.20.10.2-10.20.40.2），故障历时：4分44秒", "src.physicalTopology": "物理拓扑", "src.RelayInformation": "中继信息", "src.LinkQualityAnalysis": "链路质差分析", "src.PEAVIN": "请输入正确的身份证号码", "src.Real-timeAlarm": "实时告警", "config.PETCPNTPNITFNUS": "请输入正确的项目名称，项目名称是 src/modules 下的文件夹名", "src.April": "4 月", "src.dashBoard": "仪表盘", "src.Hour": "小时", "src.InputCannotBe": "输入不可以为空", "src.CTM": "采集任务管理", "src.DVM": "默认值管理", "src.PETCLN": "请输入正确的固定电话）", "src.Trend": "趋势图", "src.Second": "秒", "src.TLMD": "中继线路监测详情", "src.voice": "语音", "src.EnterpriseWechat": "企业微信", "src.total": "合计", "src.strip": "条", "src.PortDetails": "端口详情", "src.dataBackup": "数据备份", "src.OL'+R+'+R+'CA": "只允许长度' + rule.minLength + '-' + rule.maxLength + '个字符", "src.December": "12 月", "src.Inquire": " 查 询 ", "src.EGI": "获取机构出错", "src.TIVMBAIN": "输入值必需是整数数字", "src.EGE": "获取企业出错", "src.aggregateGraph": "聚合图形", "src.PreviousPage": "上一页", "src.SM1M": "冒烟任务10分钟", "src.realTimeMonitoring": "实时监控", "src.Sure": "确定", "src.NoMatchingData": "无匹配数据", "src.path": "路径", "src.MessageRecord": "消息记录", "src.ViewList": "视图列表", "src.AlarmRecovery": "告警恢复", "src.minute": "分钟", "src.zoomOut": " 缩 小 ", "src.PLD": "丢包劣化", "src.PCSpeedTestTerminal": "PC测速端", "src.MailService": "邮件服务", "src.DOA21SLF(FD4MA4S#21PLADO": "2023-04-27 15:04:00发生劣化，疑似故障链路（**********-**********），故障历时：49分44秒#$2023-04-27 14:46:00发生丢包劣化，疑似故障链路（**********-**********），故障历时：1小时7分44秒", "src.reset": "重置", "src.CBD": "解密前内容:", "src.TestImportDevice": "测试导入设备2", "src.lineManagement": "线路管理", "src.PleaseEnterPassword": "请输入密码", "src.Clear": "清空", "src.EnterSearchContent": "请输入搜索内容", "src.treeShape": "树形", "src.TIVMBGTAI(": "输入值必需大于整数（0）", "src.DQDA": "拨测质差分析", "src.ITDNE": "不存在的接口", "src.CollectorOffline": "采集器离线", "src.IllegalDeviceAccess": "非法接入告警", "src.PortStatusChange": "端口监控", "src.EndTime": "结束时间", "src.mail": "邮件", "src.IOA21SLF(FD1H9MA4S": "2023-04-27 14:44:00发生中断，疑似故障链路（*********-************），故障历时：1小时9分44秒", "src.I|'I(<6R=P/1+(|'+D": "i.time.just') || '刚刚';else if (diff < 60000) resStr = parseInt(diff / 1000) + (locale('i.time.seconds') || '秒') + dirStr;else if (diff >= 60000 && diff < 3600000) resStr = Math.floor(diff / 60000) + (locale('i.time.minutes') || '分钟') + dirStr;else if (diff >= 3600000 && diff < 86400000) resStr = Math.floor(diff / 3600000) + (locale('i.time.hours') || '小时') + dirStr;else if (diff >= 86400000 && diff < 2623860000) resStr = Math.floor(diff / 86400000) + (locale('i.time.days') || '天') + dirStr;else if (diff >= 2623860000 && diff <= 31567860000 && IS_EARLY) resStr = getDate(timeStamp);else resStr = getDate(timeStamp, 'year", "src.UDPDestination": "UDP目标端口", "src.week": "周", "src.TIVRPINC": "整数验证规则参数未配置.", "src.PETCIA": "请输入正确的IP地址", "src.PICMAC": "请输入正确的MAC", "src.all": "全部", "src.Fault": "故障", "src.day": "日", "src.point": "分", "src.ChinaMobile": "中国移动", "src.PLDGTES": "丢包劣化生成阈值启用状态", "src.February": "2 月", "src.Cannot.exceed": "不能超过", "src.characters": "个字符", "src.checkMax": "请输入[min,max]之间的数字", "src.checkLength": "输入长度应在 min-max 之间", "virt.host.name": "宿主机管理" }

var getLabel = function getLabel(code) {
	if ("zh" === localStorage.getItem('locale')) {
		return chineseMap[code];
	} else {
		return englishMap[code];
	}
}

exports.getLabel = getLabel;