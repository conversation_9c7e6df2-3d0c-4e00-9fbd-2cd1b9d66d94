<template>
    <div class="Treeselect" v-bind="$attrs" v-on='$listeners' :id="id">
        <Input 
         :icon="iconType" 
         :readonly='true' 
         :disabled='$attrs.disabled' 
         ref="Vinput" 
         :placeholder="$t('snmp_pl_man')" 
         @on-click="onClick" 
         @on-focus='onFocus' 
         @on-blur='onBlur' 
         @on-clear='onClear' 
         v-model="inputValue"
         clearable 
         style="width: 100%" />
        <div class="treeDiv" v-show="treeShow">
            <Tree 
             :ref="treeRef" 
             :data="data" 
             :multiple='$attrs.multiple||false' 
             :show-checkbox='$attrs.checkbox||false' 
             :load-data='$attrs.loadData' 
             @on-select-change="onSelectChange" 
             @on-check-change="onCheckChange" ></Tree>
        </div>
    </div>
</template>
<script>
export default {
    props: ['value', 'data'],
    // props: {
    //     value: {
    //         type: Object,
    //         default: ''
    //     },
    //     data: {
    //         type: Object,
    //         default: ''
    //     },
    //     dis: {
    //         type: Boolean,
    //         default: false
    //     }
    // },
    computed:{
        inputValue:{
            get(){
                return this.value
            },
            set(val){
               
            },
        }
    },
    watch:{
        treeShow:{
            handler(val){
                this.iconType =  val? 'ios-arrow-up': 'ios-arrow-down'
            }
        }
    },
    data() {
        return {
            treeRef:"tree_" +Date.now(),
            id:"orgBox_" +Date.now(),
            dis: false,
            treeValue: '',
            treeShow: false,
            iconType: 'ios-arrow-down'
        };
    },
    methods: {
        onFocus() {
            this.iconType = 'ios-arrow-up';
            this.treeShow = true;
            console.log('聚焦了')
            this.$emit('onFocus')
        },
        onBlur() {
            this.iconType = 'ios-arrow-down';
        },
        onDisTrue() {
            this.dis = true;
        },
        onDisFalse() {
            this.dis = false;
        },
        onSelectChange(item) {
            // 单选才会执行
            var multiple = this.$attrs.multiple || false;
            if(!multiple){
                this.$emit('onSelectChange', item);
                this.treeShow = false;
            }
        },
        onCheckChange(item){
             var checkbox = this.$attrs.checkbox;
            if(checkbox){
                var items = this.$refs[this.treeRef].getCheckedNodes();
                this.$emit('onSelectChange', items);
            }else{

            }
        },
        onClear(){
            this.$emit('onClear');
        },
        onClick() {
            var disabled = this.$attrs.multiple;
            if(!disabled){
                !this.$attrs.disabled && (this.treeShow = !this.treeShow);
            }
            this.$emit('onFocus')
            
        },
       
    },
    mounted() {
        var _that = this;
        document.addEventListener('click', e => {
            var box = document.getElementById(_that.id);
            //  console.log('box',box);
            if (box && box.contains(e.target)) {
            } else {
                this.treeShow = false;
            }
        });
        top.document.addEventListener('click', e => {
            
            var box = top.document.getElementById(_that.id);
            // console.log('top.box',box);
            if (box && box.contains(e.target)) {
            } else {
                this.treeShow = false;
            }
        });
    }
};
</script>

<style lang="less" scoped>
.Treeselect {
    position: relative;
    cursor: pointer;
    width: 100%;
    .treeDiv {
        position: absolute;
        max-height: 150px;
        width: 100%;
        border: 1px solid var(--border-color,#04478e);
        border-radius: 5px;
        background-color: var(--input_b_color, #fff);
        z-index: 1000;
        padding: 1px 10px;
        top: 33px;
        overflow: auto;
        overscroll-behavior: none;
        z-index: 2000;
    }
}
/deep/ .ivu-input-icon {
    // color: #05eeff;
      color: var(--ivu_select_arrow , #05EEFF);
}
/deep/.ivu-tree-empty{
    padding: 5px 0;
}
</style>