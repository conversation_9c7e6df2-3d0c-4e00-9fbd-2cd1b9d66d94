export default {
    data() {
        return {

        }
    },
    methods: {
        handleResizeTable() {
            console.log('resize')
            this.screenWidth = window.innerWidth;
            this.setCustomFieldsColumnsKeyWidth
            try{
            this.setCustomFieldsColumnsKeyWidth()
            this.getAllocationResize()
            }catch(e){
                console.log(e)
            }
        },

        getColumnWidth(zh, en) {
            let width = 0
            if (localStorage.getItem('locale') == 'zh') {
                width = zh
            } else {
                width = en
            }

            return width
        },
        calculateStringLength(str) {
            let length = 0
            for (let i = 0; i < str.length; i++) {
                const chartCode = str.charCodeAt(i)
                if (chartCode >= 0x4e00 && chartCode <= 0x9fff) {
                    length += 2
                } else {
                    length += 1
                }
            }
            return length
        },
        getAllocationResize() {
            //key值

            let screenWidthTemp = this.screenWidth - 20;
            // this.allocationListFields = res.data.allocationListFields;
            this.fieldsJsonObjArr = [];
            this.allocationListFields.forEach(item => {
                if (item.showField === true) {
                    this.fieldsJsonObjArr.push(item.parameterName);
                }
            });
            // 获取显示表头
            this.fieldsJsonObjArr.push("action");
            this.columns = [];
            let customColumnsWidth = 0;//回显自定义字段总宽度
            let customColumnsAvgWidth = 0;//回显自定义字段平均宽度
            //将显示的表头拿到函数放到columns中
            if (this.fieldsJsonObjArr.length > 0) {
                this.fieldsJsonObjArr.forEach(item => {
                    this.fixedColumns.forEach(item2 => {
                        if (item === item2.key) {
                            //计算需要展示自定义字段项总长度
                            let customFieldsColumnsKeyWidthTemp = this.customFieldsColumnsKeyWidth.find(item3 => item3.key === item2.key);
                            if (customFieldsColumnsKeyWidthTemp) {
                                customColumnsWidth = customColumnsWidth + customFieldsColumnsKeyWidthTemp.width;
                                item2.width = customFieldsColumnsKeyWidthTemp.width;
                            }
                            this.columns.push(item2);
                            return;
                        }
                    });
                });
                //赋值标头名称
                this.allocationListFields.forEach(item => {
                    this.fixedColumns.forEach(item2 => {
                        if (item.parameterName === item2.key) {
                            if (item.parameterName === 'affectEventNum') {
                                let affectEventNumTitle = this.query.type == "1" ? this.$t('comm_relevant_event') : this.$t('comm_relevant_fault')
                                item.parameterTitle = affectEventNumTitle;
                            } else {
                                item.parameterTitle = item2.title;
                            }
                            return;
                        }
                    });
                });
                //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
                if (screenWidthTemp > customColumnsWidth) {
                    if (this.columns.length > 1) {
                        let columnsLength = this.columns.length - 1;
                        customColumnsAvgWidth = Math.floor((screenWidthTemp - customColumnsWidth) / columnsLength);
                    }
                }
                this.columns.forEach(item => {
                    if (item.key != "action") {
                        item.width = item.width + customColumnsAvgWidth;
                    }
                });
            } else {
                this.columns = this.fixedColumns;
            }


        },

    },

    created() {
        window.addEventListener('resize', this.handleResizeTable);


    },
    beforeDestory() {
        window.removeEventListener('resize', this.handleResizeTable);
    },
}