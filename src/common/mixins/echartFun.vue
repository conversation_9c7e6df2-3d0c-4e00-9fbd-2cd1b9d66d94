<template>
  <div></div>
</template>

<script>
export default {
  name: "echartFun",
  methods: {

    // 处理时延y轴坐标的方法
     handleYcoordinate(data) {
    // data需要处理的数据，对象形式
   
    
    console.log(data,'需要处理的数据') 
    let maxNum = 0
    let minNum = 900000
    // 最大值
      data.forEach(item => {
        // console.log(item[1],'数据数据数据')
      
        if(Number(item[1]) > maxNum) {
          maxNum = Number(item[1])
        }
        if(Number(item[1]) < minNum) {
          minNum = Number(item[1])
        }
      })
      // console.log(maxNum,minNum,'最大值，最小值')
     
      // 0<=最大值<10，起止坐标值[0-11] 
      // 10<=最大值<50，起止坐标值[0-55] 
      // 50<=最大值<=100，起止坐标值[10/最小值-110]，起坐标取小值（10/最小值） 
      // 100<=最大值<200，起止坐标值[50/最小值-220]，起坐标取小值（50/最小值）
      //  200<=最大值<500，起止坐标值[100/最小值-550]，起坐标取小值（100/最小值） 
      // 500<=最大值<1000，起止坐标值[200/最小值-1100]，起坐标取小值（200/最小值） 
      // 1000<=最大值，起止坐标值[500/最小值-最大]，起坐标取小值（500/最小值） 坐标粒度自动适配
      let obj = {
        maxNum: maxNum,
        minNum: minNum
      }
      if(maxNum < 10 && maxNum >= 0) {
       obj.maxNum = 11
       obj.minNum = 0

      }else if( maxNum < 50 && maxNum >= 10) {
        obj.maxNum = 55
        obj.minNum = 0

      }else if( maxNum < 100 && maxNum >= 50) {
        
       obj.maxNum = 110
       minNum < 10 ? obj.minNum = minNum : obj.minNum = 10

      }else if( maxNum < 200 && maxNum >= 100) {
        obj.maxNum = 220
        minNum < 50 ? obj.minNum = minNum : obj.minNum = 50

      }else if( maxNum < 500 && maxNum >= 200) {
        obj.maxNum = 550
        minNum < 100 ? obj.minNum = minNum : obj.minNum = 100

      }else if( maxNum < 1000 && maxNum >= 500) {
        obj.maxNum = 1100
        minNum < 200 ? obj.minNum = minNum : obj.minNum = 200

      }else if( maxNum >= 1000) {
        obj.maxNum = maxNum 
      minNum < 500 ? obj.minNum = minNum : obj.minNum = 500
      

      }
      //  console.log(obj,'y轴坐标最大值最小值obj................')
       
      return obj
      

      
     
  

  },
  // 处理专线线路检测y坐标方法
    handleYspectendency(data) {
    // data需要处理的数据，对象形式
   
    
    console.log(data,'需要处理的数据') 
    let maxNum = 0
    let minNum = 900000
    // 最大值
      data.forEach(item => {
        // console.log(item[1],'数据数据数据')
      
        if(Number(item) > maxNum) {
          maxNum = Number(item)
        }
        if(Number(item) < minNum) {
          minNum = Number(item)
        }
      })
      // console.log(maxNum,minNum,'最大值，最小值')
     
      // 0<=最大值<10，起止坐标值[0-11] 
      // 10<=最大值<50，起止坐标值[0-55] 
      // 50<=最大值<=100，起止坐标值[10/最小值-110]，起坐标取小值（10/最小值） 
      // 100<=最大值<200，起止坐标值[50/最小值-220]，起坐标取小值（50/最小值）
      //  200<=最大值<500，起止坐标值[100/最小值-550]，起坐标取小值（100/最小值） 
      // 500<=最大值<1000，起止坐标值[200/最小值-1100]，起坐标取小值（200/最小值） 
      // 1000<=最大值，起止坐标值[500/最小值-最大]，起坐标取小值（500/最小值） 坐标粒度自动适配
      let obj = {
        maxNum: maxNum,
        minNum: minNum
      }
      if(maxNum < 10 && maxNum >= 0) {
       obj.maxNum = 11
       obj.minNum = 0

      }else if( maxNum < 50 && maxNum >= 10) {
        obj.maxNum = 55
        obj.minNum = 0

      }else if( maxNum < 100 && maxNum >= 50) {
        
       obj.maxNum = 110
       minNum < 10 ? obj.minNum = minNum : obj.minNum = 10

      }else if( maxNum < 200 && maxNum >= 100) {
        obj.maxNum = 220
        minNum < 50 ? obj.minNum = minNum : obj.minNum = 50

      }else if( maxNum < 500 && maxNum >= 200) {
        obj.maxNum = 550
        minNum < 100 ? obj.minNum = minNum : obj.minNum = 100

      }else if( maxNum < 1000 && maxNum >= 500) {
        obj.maxNum = 1100
        minNum < 200 ? obj.minNum = minNum : obj.minNum = 200

      }else if( maxNum >= 1000) {
        obj.maxNum = maxNum 
      minNum < 500 ? obj.minNum = minNum : obj.minNum = 500
      

      }
       console.log(obj,'y轴坐标最大值最小值obj................')
       
      return obj
      

      
     
  

  },

    closest(arr, num) {
      //获取数组中某个元素与给定的值最接近的数
      if (arr.length<1) {
        return 'noPoint'
      }else{
        let left = 0,
          right = arr.length - 1;
        while (left <= right) {
          let middle = Math.floor((right + left) / 2);
          if (right - left <= 1) {
            break;
          }
          let val = new Date(arr[middle][0]).valueOf();
          if (val === new Date(num).valueOf()) {
            return middle;
          } else if (val > new Date(num).valueOf()) {
            right = middle;
          } else {
            left = middle;
          }
        }
        let leftValue = new Date(arr[left][0]).valueOf();
        let rightValue = new Date(arr[right][0]).valueOf();
        return rightValue - new Date(num).valueOf() >
        new Date(num).valueOf() - leftValue
          ? left
          : right;
      }
    },
    isDefaultData(delayLoss){
      let lineOne = delayLoss.lineOne;
      let lineTwo = delayLoss.lineTwo;
      if(!lineOne&&!lineTwo){
        let lineone0= [];
        let linetwo0= [];
        var now = new Date();
        var year = now.getFullYear(); //得到年份
        var month = now.getMonth()+1;//得到月份
        var date = now.getDate();//得到日期
        for(let i=0;i<24;i++){
          let dates = [year+"-"+month+"-"+date+" "+(i<10?'0'+i:i+"")+":00:00",""];
          lineone0.push(dates);
          linetwo0.push(dates);
        }
        delayLoss.lineOne =lineone0;
        delayLoss.lineTwo =linetwo0;
        delayLoss.lineOnetotal =24;
      }
      return delayLoss;
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        now.toTimeString().substr(0, 8)
      );
    },
    getFlowUnit(data) {
      let dataArr = data,
        len = data.length,
        count = 0;
      for (let i = 0; i < len; i++) {
        count += parseInt(
          dataArr[i][1] === "" || dataArr[i][1] === null ? 0 : dataArr[i][1]
        );
      }
      let average = parseInt(count / len);
      if (average / Math.pow(1000, 5) >= 1) {
        return ["PB", Math.pow(1000, 5)];
      } else if (average / Math.pow(1000, 4) >= 1) {
        return ["TB", Math.pow(1000, 4)];
      } else if (average / Math.pow(1000, 3) >= 1) {
        return ["GB", Math.pow(1000, 3)];
      } else if (average / Math.pow(1000, 2) >= 1) {
        return ["MB", Math.pow(1000, 2)];
      } else if (average / Math.pow(1000, 1) >= 1) {
        return ["KB", Math.pow(1000, 1)];
      } else {
        return ["B", Math.pow(1000, 0)];
      }
    },
    getUnit (size, unit = true, local = false)  {
      if (typeof size !== 'number' && size < +0) {
        throw new TypeError('The flow must be a positive number.');
      }
      size = Math.max(size, 0);
      const result = size > 1000
        ? size / 1000 > 1000
          ? size / (1000 * 1000) > 1000
            ? size / (1000 * 1000 * 1000) > 1000
              ? size / (1000 * 1000 * 1000 * 1000) > 1000
              ? size / (1000 * 1000 * 1000 * 1000  * 1000) > 1000
                ?[(size / (1000 * 1000 * 1000 * 1000 * 1000 * 1000)), 'Ebps']
                  : [size / (1000 * 1000 * 1000 * 1000 * 1000), 'Pbps']
                : [size / (1000 * 1000 * 1000 * 1000), 'Tbps']
              : [size / (1000 * 1000 * 1000), 'Gbps']
            : [size / (1000 * 1000), 'Mbps']
          : [size / 1000, 'Kbps']
        : [(size), 'bps'];
      result[ 0 ] = Number.parseFloat(result[ 0 ]).toFixed(2);
      if (local) {
        result[ 0 ] = result[ 0 ].toLocaleString();
      }
      // console.log(unit ? result.join('') : result[ 0 ],'y坐标')
      return unit ? result.join('') : result[ 0 ];
    },
    getUnits (size, unit = true, local = false)  {
      if (typeof size !== 'number' && size < +0) {
        throw new TypeError('The flow must be a positive number.');
      }
      size = Math.max(size, 0);
      const result = size > 1000
        ? size / 1000 > 1000
          ? size / (1000 * 1000) > 1000
            ? size / (1000 * 1000 * 1000) > 1000
              ? size / (1000 * 1000 * 1000 * 1000) > 1000
               ? size / (1000 * 1000 * 1000 * 1000  * 1000) > 1000
                ?[(size / (1000 * 1000 * 1000 * 1000 * 1000 * 1000)), 'Ebps',1000000000000000000]
                  : [size / (1000 * 1000 * 1000 * 1000 * 1000), 'Pbps',1000000000000000]
                : [size / (1000 * 1000 * 1000 * 1000), 'Tbps',1000000000000]
              : [size / (1000 * 1000 * 1000), 'Gbps',1000000000]
            : [size / (1000 * 1000), 'Mbps',1000000]
          : [size / 1000, 'Kbps',1000]
        : [(size), 'bps',1];
      result[ 0 ] = Number.parseFloat(result[ 0 ]).toFixed(2);
      if (local) {
        result[ 0 ] = result[ 0 ].toLocaleString();
      }
      return  result;
    },
    flowSize (size, unit = true, local = false)  {
      if (size == undefined || size == null || size == '--'){
        return '--'
      }
      // if (typeof size !== 'number' && size < +0) {
      //   throw new TypeError('The flow must be a positive number.');
      // }
      size = Math.max(size, 0);
      const result = size > 1000
        ? size / 1000 > 1000
          ? size / (1000 * 1000) > 1000
            ? size / (1000 * 1000 * 1000) > 1000
              ? size / (1000 * 1000 * 1000 * 1000) > 1000
                ? size / (1000 * 1000 * 1000 * 1000  * 1000) > 1000
                ?[(size / (1000 * 1000 * 1000 * 1000 * 1000 * 1000)), 'Ebps']
                  : [(size / (1000 * 1000 * 1000 * 1000 * 1000)), 'Pbps']
                : [(size / (1000 * 1000 * 1000 * 1000)), 'Tbps']
              : [(size / (1000 * 1000 * 1000)), 'Gbps']
            : [(size / (1000 * 1000)), 'Mbps']
          : [(size / 1000), 'Kbps']
        : [(size), 'bps'];
      result[ 0 ] = Number.parseFloat(result[ 0 ]).toFixed(2);
      if (local) {
        result[ 0 ] = result[ 0 ].toLocaleString();
      }
      return unit ? result.join(' ') : Number.parseFloat(result[ 0 ]).toFixed(2);
    },


  flowSizeOnlyKbps (size, unit = true, local = false)  {
      if (size == undefined || size == null || size == '--'){
        return '--'
      }
      // if (typeof size !== 'number' && size < +0) {
      //   throw new TypeError('The flow must be a positive number.');
      // }
      size = Math.max(size, 0);
      const result = [(size / 1000), 'Kbps'];
      result[ 0 ] = Number.parseFloat(result[ 0 ]).toFixed(2);
      if (local) {
        result[ 0 ] = result[ 0 ].toLocaleString();
      }
      return unit ? result.join(' ') : Number.parseFloat(result[ 0 ]).toFixed(2);
    },
  }
};
</script>

<style scoped></style>
