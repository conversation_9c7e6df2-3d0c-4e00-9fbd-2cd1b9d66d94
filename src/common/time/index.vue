<!--周数组件
@props     @setYear             设置年份（默认当前年份）
           @setDay            设置某一天（默认当天）


-->

<template>
  <section class="time" v-clickoutside="handleClose">
    <div class="time-input">
      <input
        type="text"
        v-model="dateValue"
        :placeholder="$t('comm_week_tip')"
        :class="{ focus: focusShow }"
        readonly
        @focus="inputFocus"
      />
      <i class="ivu-icon ivu-icon-ios-arrow-down ivu-select-arrow"></i>
    </div>
    <div class="time-dropdown" v-show="dropdownShow">
      <div class="time-dropdown-header">
        <i
          class="icon-left ivu-icon ivu-icon-ios-arrow-back "
          @click="arrowClick('left')"
        ></i>
        <i
          v-show="showright"
          class="icon-right ivu-icon ivu-icon-ios-arrow-forward"
          @click="arrowClick('right')"
        ></i>
        <span>{{ title }}</span>
      </div>
      <div class="time-dropdown-content">
        <p
          v-for="(item, index) in weekList"
          :key="index"
          :class="(styleIndex === index ? 'active' : '')"
          :style="new Date(item.value[0]).valueOf()-new Date().valueOf()>24*3600000 ? 'cursor:no-drop;background:var(--time_disabled_b_color,#ffffff)!important;':''"
          @click="choiceClick(item, index)"
        >
          {{ item.label }}
        </p>
      </div>
    </div>
  </section>
</template>

<script>
  import  '../../timechange'
  import  '../../style/time.less'
  

const clickoutside = {
  // 初始化指令
  bind(el, binding, vnode) {
    function documentHandler(e) {
      // 这里判断点击的元素是否是本身，是本身，则返回
      if (el.contains(e.target)) {
        return false;
      }
      // 判断指令中是否绑定了函数
      if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
        binding.value(e);
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.__vueClickOutside__ = documentHandler;
    document.addEventListener("click", documentHandler);
  },
  update() {},
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener("click", el.__vueClickOutside__);
    delete el.__vueClickOutside__;
  }
};
export default {
  name: "Timeindex",
  directives: { clickoutside },
  props: {
    setYear: {
      type: [String, Number],
      default: new Date().format("yyyy")
    },
    setDay: {
      type: [String, Number],
      default: new Date().format("yyyy-MM-dd")
    }
  },
  data() {
    return {
      timeChange: "", //时间切换
      dateValue: "", //展示周数
      focusShow: false, //是否获取焦点
      dropdownShow: false, //显示下拉框
      title: "", //下拉框标题
      weekList: [], //下拉框数据
      styleIndex: null, //样式下标
      storageIndex: null, //缓存下标
      storageTime: "", //缓存时间
      isDisabled:false,
      showright:false,//是否显示右切换
    };
  },

  watch: {
    setYear: {
      handler(val) {
        let val1 = val
        if(val.length === 4) {
          val1 = val
          
        }else {
          val1 = val.slice(0,4)
        }
   
   
        // this.getWeekList(val)
        let oldDay = this.setDay.split("-"),day = val1 + "-" + oldDay[1] + "-" + oldDay[2];
        const thisDate = new Date(),thisYear = thisDate.getFullYear(),thisMonth = thisDate.getMonth()+1,thisDay = thisDate.getDate();
       
        if (val1 < thisYear) {
          this.showright = true;
        }else if (thisYear  == val1) {
          if (oldDay[1]<thisMonth) {
            this.showright = true;
          }else{
            this.showright = false;
          }
        }
        this.getWeekNum(val1, day);
        this.ifWeek(day);
        this.timeChange = day;
      },
      deep: true,
      // immediate:true
    }
  },
  methods: {


    // 确定选择周的数据
    getWeekList(val) {
      let oldDay = this.setDay.split("-"),
          day = val + "-" + oldDay[1] + "-" + oldDay[2];
        const thisDate = new Date(),thisYear = thisDate.getFullYear(),thisMonth = thisDate.getMonth()+1,thisDay = thisDate.getDate();
       
        if (val < thisYear) {
          this.showright = true;
        }else if (thisYear  == val) {
          if (oldDay[1]<thisMonth) {
            this.showright = true;
          }else{
            this.showright = false;
          }
        }
        this.getWeekNum(val, day);
        this.ifWeek(day);
        this.timeChange = day;

    },
    //获取周数
    getWeekNum(year, timeParam) {
      let time = timeParam.split("-"),
        month = time[1],
        firstWeek = new Date(year, month - 1, 1).getDay(), //当月第一天是星期几
        lastWeek = new Date(year, month, 0).getDay(), //一当月第一天是最后一天是星期几
        firstDay = "", //第一周第一天
        day = "",
        lastDay = ""; // 最后一周的最后一天
      if (firstWeek != 1) {
        firstDay = new Date(
          year,
          month - 1,
          -(firstWeek === 0 ? 7 : firstWeek) + 2
        );
        day = new Date(year, month - 1, -(firstWeek === 0 ? 7 : firstWeek) + 2);
      } else {
        firstDay = new Date(year, month - 1, 1);
        day = new Date(year, month - 1, 1);
      }
      if (lastWeek != 0) {
        lastDay = new Date(year, month, 7 - lastWeek);
      } else {
        lastDay = new Date(year, month, 0);
      }
      let index = 1,
        array = [];
      function formatDate(d) {
        return d
          .toLocaleDateString("zh-cn", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit"
          })
          .replace(/\//g, "-");
      }
      while (day.getTime() < lastDay.getTime()) {
        day.setDate(day.getDate() + 1);
        if (day.getDay() === 0) {
          
          array.push({
            value: [formatDate(firstDay), formatDate(day)],
            label:
              this.$t('org_the') +" "+
              index++ + " "+
              this.$t('comm_week') + "  " +
              formatDate(firstDay) + "  " +
              this.$t('comm_to') + "  " +
              formatDate(day)
          });
          firstDay = new Date(day.getTime() + 86400000);
        }
      }
      this.weekList = array;
      // this.setWeekList(array)
      let titleMonth = "";
      if (parseInt(time[1]) < 10) {
        titleMonth = "0" + parseInt(time[1]);
      } else {
        titleMonth = time[1];
      }
      this.title = time[0] + "-" + titleMonth;
    },
    //判断当前时间属于第几周
    ifWeek(timeParam) {
      let date = new Date(timeParam),
        w = date.getDay(),
        d = date.getDate();
      if (w === 0) {
        w = 7;
      }
      let index = Math.ceil((d + 7 - w) / 7) - 1;
      this.styleIndex = index;
      this.storageIndex = index;
   
   

      this.dateValue = this.weekList[index].label;
    },
    //input获取焦点
    inputFocus() {
      this.dropdownShow = true;
    },
    //方向切换
    arrowClick(type) {
      let current = this.timeChange.split("-"),
        year = "",
        month = "",
        newTime = "";
      this.styleIndex = null;
      if (type === "left") {
        this.showright = true;
        if (parseInt(current[1]) - 1 < 1) {
          year = parseInt(current[0]) - 1;
          month = 12;
        } else {
          year = parseInt(current[0]);
          if (parseInt(current[1]) - 1 < 10) {
            month = "0" + (parseInt(current[1]) - 1);
          } else {
            month = parseInt(current[1]) - 1;
          }
        }
        newTime = year + "-" + month + "-" + "01";
      } else {
        
        const thisDate = new Date(),thisYear = thisDate.getFullYear(),thisMonth = thisDate.getMonth()+1,thisDay = thisDate.getDate();
        
        if (thisYear <= current[0] && thisMonth <= Number(current[1])+1) {
          this.showright = false;
        }else{
          this.showright = true;
        }
        if (parseInt(current[1]) + 1 > 12) {
          year = parseInt(current[0]) + 1;
          month = "01";
        } else {
          year = parseInt(current[0]);
          if (parseInt(current[1]) + 1 < 10) {
            month = "0" + (parseInt(current[1]) + 1);
          } else {
            month = parseInt(current[1]) + 1;
          }
        }
        newTime = year + "-" + month + "-" + "01";
      }
      if (new Date(newTime).format("yyyy-MM") === this.storageTime) {
        this.styleIndex = this.storageIndex;
      }
      this.timeChange = newTime;
      this.getWeekNum(year, newTime);
    },
    //选择
    choiceClick(item, index) {
    
      let itemFirstTime = new Date(item.value[0]).valueOf(),nowTime = new Date().valueOf();
      if (itemFirstTime > nowTime && itemFirstTime - nowTime>24*3600000) {
        return
      }
 
      this.storageTime = this.title;
    
      this.dateValue = item.label;
  
      this.styleIndex = index;

      this.storageIndex = index;
    
      this.dropdownShow = false;

      let label = item.label.split(" ")[0];
     
      this.$emit("on-change", {
        title: this.title,
        label: label,
        value: item.value
      });
    },
    handleClose() {
      this.dropdownShow = false;
    },
    //返回当前数据信息
    getParams() {
      let data = this.weekList[this.styleIndex],
        label = data.label.split(" "),
        obj = {
          title: this.title,
          label: label[0],
          value: data.value
        };
      return obj;
    }
  },
  mounted() {


    this.getWeekNum(this.setYear, this.setDay);
    this.ifWeek(this.setDay);
    this.timeChange = this.setDay;
  }
};
</script>
<style lang="less" scoped>
p.isDisabled{
  cursor: not-allowed;
}
.time-dropdown-content p:hover{
  background: var(--time_hover_b_color,#e1f0fe)!important;
}

.time .time-dropdown .time-dropdown-content p{
  font-size: 14px;
}
</style>
