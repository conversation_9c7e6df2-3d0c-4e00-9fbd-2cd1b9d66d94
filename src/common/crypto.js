import CryptoJS from 'crypto-js'
let pwd = '0102030405060708';
export default {
     encrypt(word) {
         var key = CryptoJS.enc.Utf8.parse(pwd);
         var result = CryptoJS.AES.encrypt(word, key, {
             mode: CryptoJS.mode.ECB,
             padding: CryptoJS.pad.Pkcs7
         });
         // console.log("加密后====>：" + result);
         return result;
     },
    decrypt(word) {
        var key= CryptoJS.enc.Utf8.parse(pwd);
        var decryptedData = CryptoJS.AES.decrypt(word, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
        var decryptedStr = decryptedData.toString(CryptoJS.enc.Utf8);
        // console.log("解密后====>：" + decryptedStr);
        return decryptedStr;
    }
}