<template>
  <div style="width: 100%; height: 100%">
    <div class="brokenLine" :id="node"></div>
  </div>
</template>

<script>
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
import eConfig from "@/config/echart.config.js";
export default {
  props: {
    node: {
      type: String,
      default: "line",
    },
    degradationIndicators: {
      type: Number,
      default: 0,
    },
    isbigscreen: {
      type: Boolean,
      default: false,
    },
    isdarkSkin: {
      type: Number,
      default: 0,
    },
    height: {
      type: Number,
      default: 260,
    },
    lineData: {
      type: Object,
      default: function () {
        return {
          data: [],
        };
      },
    },
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 0,
    };
  },
  watch: {
    height: {
      handler(val) {
        this.height = val;
      },
      deep: true,
    },
    isdarkSkin: {
      handler(val) {
        console.log("spec");
        this.ceshi();
      },
      deep: true,
    },
  },
  mounted() {
    this.ceshi();
  },
  computed: {
    legendPosizion() {
      if(localStorage.getItem('locale') === 'zh') {
        return 'center'
      }else {
        return '15%'
      }

    }
  },
  methods: {
    ceshi() {
      let that = this;
      that.myChart1 = echarts.init(document.getElementById(that.node));
      if (that.myChart === undefined) {
        that.myChart1 = echarts.init(document.getElementById(that.node));
      }
      that.myChart1.clear();
      that.myChart1.setOption({
        // color: isbigscreen ? ["#1252C8", "#ED921C", "#F01C1C"] : ["#4e7bff", "#ffa212", "#fb204d"],
        // color: ["#00FFEE", "#FEA31B", "#FE5C5C"],
        color:that.currentSkin == 1 ? ["#00FFEE", "#FEA31B", "#FE5C5C"] : ["#03B999", "#FEA31B", "#FE5C5C"],
        // title: {
        //   text: "最近30天" + that.lineData.title + "告警走势",
        //   top: "3%"
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
            snap: true,
            shadowStyle: {
              // color: isbigscreen ? "rgba(18,55,127,.8)" :"rgba(0,0,0,1)",
              color: "rgba(0,0,0,1)",
              width: 30,
            },
            // lineStyle:isbigscreen ? {
            //   color:'rgba(143,215,227,.8)',
            // } : {},
            lineStyle: {},
          },
          // backgroundColor:isbigscreen ? "rgba(18,55,127,.8)" :"rgba(255,255,255,1)",
          // backgroundColor: "rgba(20,20,20,.6)",
          confine: true,
          backgroundColor: 'var(--dashboard_tooltip_bg_color,rgba(18,55,127,.8))',//"rgba(18,55,127,.8)",
          padding: [10, 18],
          textStyle: {
            align: "left",
            // color:isbigscreen ? '#8fd4ff' :'#d1e4ff'
            color: this.currentSkin == 1 ? '#fff': '#515A6E' ,// , "#d1e4ff",
          },
          // formatter:'{a0}:{c}<br/>{a1}:{c}<br/>{a2}:{c}'
        },
        legend: {
          right: this.legendPosizion,
          orient: 'horizontal',
          icon: "roundRect",
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30 ,
          textStyle: {
            color: isbigscreen
              ? "#FFFFFF"
              : this.isdarkSkin == 1
              ? "#FFFFFF"
              : "#0e2a5f",
            fontSize: 12,
            fontFamily: "MicrosoftYaHei-Bold",
          },
          data: that.getLegendData(),
        },
        grid: {
          top: "15%",
          left: "5%",
          right: "0%",
          bottom: "15%",
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisTick: {
              show: false
            },
              splitLine: {
                show:false,
                lineStyle: {
                  type: "dotted",
                  color: this.isdarkSkin == 1 ? 'rgba(42, 56, 64, 1)' :"#C0C4CC"
                }
              },
            axisLine: {
              show: false,
              lineStyle: {
                color: isbigscreen
                  ? "#617ca5"
                  : this.isdarkSkin == 1
                  ? "#617ca5"
                  : "#0e2a5f",
                width: 1,
              },
            },
            axisLabel: { color: this.isdarkSkin == 1 ? "#5CA0D5" : "" },
            data: that.xAxis(),
          },
        ],
        yAxis: [
          {
            name: this.$t('comm_unit_one'),
            nameLocation: "end",
            nameTextStyle: {
              color: isbigscreen
                ? "#5CA0D5"
                : this.isdarkSkin == 1
                ? "#5CA0D5"
                : "#0e2a5f",
            },
            type: "value",
            scale: true,
            // axisLine:{show:isbigscreen ? false : true},
            axisLine: { show: false },
            splitLine: {
              show: true,
              // lineStyle: isbigscreen ? {
              //   type: "solid",
              //   width: 0.5,
              //   color:'#0e2a5f',
              // } : {type: "dashed",width: 0.5,color:this.isdarkSkin == 1 ? '#617ca5':''}
              lineStyle: {
                type: "solid",
                width: 0.5,
                color: isbigscreen
                  ? "#2A3840"
                  : this.isdarkSkin == 1
                  ? "#2A3840"
                  : "#C0C4CC",
              },
            },
            // axisLabel:isbigscreen ? {
            //   fontSize:12,
            //   color:'#8fd4ff'
            // } :{color:this.isdarkSkin == 1 ? '#617ca5':''},
            axisLabel: {
              color: isbigscreen
                ? "#5CA0D5"
                : this.isdarkSkin == 1
                ? "#5CA0D5"
                : "",
            //    formatter: function(value) {
            //   // 将数值转换为整数显示
            //   return Math.floor(value);
            // }
            },
            minInterval: 1,
            // min: 'dataMin'
          },
        ],
        series: that.seriesData(),
      });
    },
    seriesData() {
      let that = this,seriesDataArrays = [
        {
          name: this.$t('common_degradation'),
          type: "line",
          symbol: "emptyCircle",
          symbolSize: 0.1,
          smooth: true,
          itemStyle: {
            normal: {
              lineStyle: {
                width: 1,
              },
            },
          },

          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops:[
                {
                  offset: 0,
                  color: "rgba(0, 255, 238, 0.4)"
                },
                {
                  offset: 0.8,
                  color: "rgba(0, 255, 238, 0)"
                }
              ]
            }
          },
          data: that.series1(),
        },
        {
          name: this.$t('common_loss_degradation'),
          type: "line",
          smooth: true,
          symbol: "emptyCircle",
          symbolSize: 0.1,
          data: that.series2(),
          itemStyle: {
            normal: {
              lineStyle: {
                width: 1,
              },
            },
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops:[
                {
                  offset: 0,
                  color: "rgba(254, 163, 27, 0.4)"
                },
                {
                  offset: 0.8,
                  color: "rgba(254, 163, 27, 0)"
                }
              ]
            }
          },
        },
        {
          name: this.$t('dash_interrupt'),
          type: "line",
          smooth: true,
          symbol: "emptyCircle",
          symbolSize: 0.1,
          data: that.series3(),
          itemStyle: {
            normal: {
              lineStyle: {
                width: 1,
              },
            },
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops:[
                {
                  offset: 0,
                  color: "rgba(254, 92, 94, 0.4)"
                },
                {
                  offset: 0.8,
                  color: "rgba(254, 92, 94,0)"
                }
              ]
            }
          },
        },
      ];
      if (this.degradationIndicators == 1) {
        seriesDataArrays.push({
          name: this.$t('dash_deterioration'),
          type: "line",
          smooth: true,
          symbol: "emptyCircle",
          symbolSize: 0.1,
          data: that.series4(),
          itemStyle: {
            normal: {
              lineStyle: {
                width: 1,
              },
            },
          },
        });
      }
      return seriesDataArrays;
    },
    getLegendData() {
      let dataArrays = [this.$t('common_degradation'), this.$t('common_loss_degradation'), this.$t('dash_interrupt')];
      if (this.degradationIndicators == 1) {
        dataArrays.push(this.$t('dash_deterioration'));
      }
      return dataArrays;
    },
    xAxis() {
      let time = [];
      this.lineData.data.forEach((e) => {
        time.push(e.date);
      });
      return time;
    },
    // 时延劣化
    series1() {
      let arry = [];
      this.lineData.data.forEach((e) => {
        arry.push(Number(e.degrCount));
      });
      return arry;
    },
    // 丢包劣化
    series2() {
      let arry = [];
      this.lineData.data.forEach((e) => {
        arry.push(Number(e.lossDegrCount));
      });
      return arry;
    },
    // 中断
    series3() {
      let arry = [];
      this.lineData.data.forEach((e) => {
        arry.push(Number(e.interruptCount));
      });
      return arry;
    },
    // 劣化
    series4() {
      let arry = [];
      this.lineData.data.forEach((e) => {
        arry.push(Number(e.lossDegrTotalCount));
      });
      return arry;
    },
  },
  beforeDestroy() {
    this.myChart1.clear();
    echarts.dispose(this.myChart1);
    this.myChart1 = void 0;
  },
};
</script>

<style scoped>
.brokenLine {
  height: 100%;
  min-height: 160px;
}
</style>
