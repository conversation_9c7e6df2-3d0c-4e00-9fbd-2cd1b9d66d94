<template>
  <section style="width: 100%;height: 100%" ref="timeEcharts">
    <div :id="node" style="width: 100%; height:100%"></div>
  </section>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
  require('echarts/lib/component/dataZoom')
import eConfig from "@/config/echart.config.js";
export default {
  name: "index-line",
  props: {
    node: {
      type: String,
      default: "line"
    },
    buttonType: {
      type: [String, Number],
      default: 1
    },
    dateType: {
      type: [String, Number],
      default: 1
    },
    lineData: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    }
  },
  data() {
    return {
      color_obj: {
        1: ["#4e7bff", "78,123,255"],
        2: ["#ffa212", "255,162,18"]
      },
      color_obj2: {
        可用率: ["#4e7bff", "78,123,255"],
        优良率: ["#ffa212", "255,162,18"]
      },
      name_obj: {
        1: ["时延", "ms"],
        2: ["丢包率", "%"]
      },
      name_obj2: {
        可用率: "%",
        优良率: "%"
      },
      color: [],
      colorRgb: [],
      color2: [],
      colorRgb2: [],
      line_data: {},
      dataZoomEnd: 0,
      date_type: this.dateType
    };
  },
  watch: {
    lineData: {
      handler(val) {
        if (!this.myChart) {
          return;
        }
        this.myChart.dispose();
        this.myChart = null;
        this.line_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    buttonType: {
      handler() {
        if (!this.myChart) {
          return;
        }
        this.myChart.dispose();
        this.myChart = null;
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    dateType: {
      handler(val) {
        if (val === 1) {
          this.dataZoomEnd = 100;
        } else if (val === 2) {
          this.dataZoomEnd = 100 / 7;
        } else if (val === 3) {
          this.dataZoomEnd = 100 / 62;
        }
      },
      deep: true
    }
  },
  methods: {
    init() {
      let _self = this;
      _self.myChart = echarts.init(document.getElementById(_self.node));
      let option = Object.assign(
        {},
        {
          color: _self.setColor(),
          tooltip: _self.setTooltip(),
          grid: _self.grid(),
          xAxis: _self.xAxis(),
          yAxis: _self.yAxis(),
          dataZoom: _self.setDataZoom(),
          series: _self.series(),
          legend: _self.buttonType == 3 ? _self.legend() : []
        }
      );
      if (option && typeof option === "object") {
        _self.myChart.clear();
        _self.myChart.setOption(option, true);
        window.onresize = function() {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        };
      }
    },
    setColor() {
      let _self = this;
      if (_self.buttonType != 3) {
        _self.color = _self.color_obj[_self.buttonType][0];
        _self.colorRgb = _self.color_obj[_self.buttonType][1];
        return _self.color;
      }
      if (_self.buttonType == 3) {
        let _self = this,
          legend = _self.line_data.legend;
        for (let i = 0, len = legend.length; i < len; i++) {
          _self.color2.push(_self.color_obj2[legend[i]][0]);
          _self.colorRgb2.push(_self.color_obj2[legend[i]][1]);
        }
        return _self.color2;
      }
    },
    setTooltip() {
      let _self = this;
      if (_self.buttonType != 3) {
        return eConfig.tip("axis", function(param) {
          let src = "";
          src +=
            '<span class="tooltip-round" style="background-color:' +
            param[0].color +
            '"></span>';
          src += param[0].data[0] + "<br />";
          src +=
            _self.name_obj[_self.buttonType][0] +
            "：" +
            (param[0].data[1] === "" ? "--" : param[0].data[1]) +
            _self.name_obj[_self.buttonType][1];
          return src;
        });
      }
      if (_self.buttonType == 3) {
        return eConfig.tip("axis", function(param) {
          let src = "";
          src += param[0].data[0] + "<br />";
          for (let i = 0, len = param.length; i < len; i++) {
            src +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            src +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === ""
                ? "--"
                : param[i].value[1]) +
              _self.name_obj2[param[i].seriesName] +
              "<br />";
          }
          return src;
        });
      }
    },
    grid() {
      return {
        top: "10%",
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true
      };
    },
    legend() {
      let obj = {};
      obj.right = "10%";
      obj.icon = "circle";
      obj.data = this.line_data.legend;
      obj.textStyle = {
        color: "#484b56",
        fontSize: 14,
        fontFamily: "MicrosoftYaHei-Bold"
      };
      return obj;
    },
    xAxis() {
      let obj = eConfig.axis(12, true, false, "#676f82", "#e3e7f2");
      obj.type = "time";
      return obj;
    },
    yAxis() {
      let _self = this;
      if (_self.buttonType != 3) {
        let obj = eConfig.axis(12, false, true, "#676f82", "#e3e7f2");
        obj.type = "value";
        obj.name =
          "平均" +
          _self.name_obj[_self.buttonType][0] +
          "（" +
          _self.name_obj[_self.buttonType][1] +
          "）";
        return obj;
      }
      if (_self.buttonType == 3) {
        let _self = this,
          legend = this.line_data.legend,
          array = [];
        for (let i = 0, len = legend.length; i < len; i++) {
          let obj = {
            nameTextStyle: {
              color: "#676f82",
              fontSize: 12
            },
            axisLine: {
              lineStyle: {
                color: "#676f82",
                width: "1" //坐标线的宽度
              }
            },
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#e3e7f2"
              }
            },
            axisLabel: {
              show: true,
              margin: 20,
              textStyle: {
                color: "#676f82",
                fontSize: 12
              }
            },
            axisTick: {
              show: false
            }
          };
          obj.name = legend[i] + _self.name_obj2[legend[i]];
          obj.type = "value";
          array.push(obj);
        }
        return array;
      }
    },
    setDataZoom() {
      let _self = this;
      return {
        show: true,
        start: 0,
        end: _self.dataZoomEnd
      };
    },
    series() {
      if (this.buttonType != 3) {
        let array = [],
          data = this.line_data.data[0];
        let obj = {
          type: "line"
        };
        obj.data = data;
        obj.smooth = true;
        obj.areaStyle = {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(" + this.colorRgb + ", 0.25)"
              },
              {
                offset: 0.9,
                color: "rgba(" + this.colorRgb + ", 0)"
              }
            ]
          }
        };
        array.push(obj);
        return array;
      }
      if (this.buttonType == 3) {
        let array = [],
          legend = this.line_data.legend,
          data = this.line_data.data;
        for (let i = 0, len = legend.length; i < len; i++) {
          let obj = {
            name: legend[i],
            type: "line",
            lineStyle: {
              width: 1
            },
            // connectNulls: true,
            data: data[i],
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(" + this.colorRgb2[i] + ", 0.25)"
                  },
                  {
                    offset: 0.9,
                    color: "rgba(" + this.colorRgb2[i] + ", 0)"
                  }
                ]
              }
            }
          };
          if (i > 0) {
            obj.yAxisIndex = i;
          }
          array.push(obj);
        }
        return array;
      }
    }
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.dispose();
    this.myChart = null;
  },
  mounted() {
    this.line_data = Object.assign({}, this.lineData);
    if (this.dateType === 1) {
      this.dataZoomEnd = 100;
    } else if (this.dateType === 2) {
      this.dataZoomEnd = 100 / 7;
    } else if (this.dateType === 3) {
      this.dataZoomEnd = 100 / 62;
    }
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
