<template>
  <div style="position: relative; height: 100%; overflow: hidden">
    <div class="cake" :id="node" v-if="echartShow"></div>
    <div
      :style="
        'position: absolute;left:' + styleProp.width * 0.4 + 'px;top:50%;'
      "
    >
      <div
        class="flexBox"
        :style="
          'margin-left:-50%;margin-top:-50%;width:' +
          ((minSize * 2) / 3 - (((3 * minSize * 2) / 3) * 4) / 5 / 4) +
          'px;height:' +
          ((minSize * 2) / 3 - (((3 * minSize * 2) / 3) * 4) / 5 / 4) +
          'px;'
        "
      >
        <p
          :style="
            'flex:1.5;font-size:24px;font-weight: 600;letter-spacing: 3px;line-height:' +
            (((minSize * 2) / 3 - (((3 * minSize * 2) / 3) * 4) / 5 / 4) * 3) /
              5 +
            'px;color:' +
            (isbigscreen ? '#d1e4ff' : isdarkSkin == 1 ? '#d1e4ff' : '#182f74')
          "
        >
          {{ datas.totalValue }}
        </p>
        <p
          style="font-size: 14px; font-weight: 600; letter-spacing: 3px"
          :style="
            'color:' +
            (isbigscreen ? '#465b7a' : isdarkSkin == 1 ? '#465b7a' : '#182f74')
          "
        >
          {{ datas.title }}
        </p>
      </div>
    </div>
    <div
      v-if="echartShow"
      class="cake"
      :style="{ position: 'absolute', width: '100%', left: '0', top: '0' }"
    >
      <!-- :class="isbigscreen ? 'bigscreen' : ''" -->
      <div
        class="legend"
        style="
          width: 160px;
          position: relative;
          left: 40%;
          top: 50%;
          margin-top: 0px;
        "
        :style="'margin-left:' + minSize / 2 + 'px'"
      >
        <div
          class="zc"
          :style="
            'color:' +
            (isbigscreen ? '#617ca5' : isdarkSkin == 1 ? '#617ca5' : '#0e2a5f')
          "
        >
          正常:{{ datas.data[0].value }}，{{ datas.data[0].percent + "%" }}
        </div>
        <div
          class="lh"
          :style="
            'color:' +
            (isbigscreen ? '#d1e4ff' : isdarkSkin == 1 ? '#d1e4ff' : '#1A80C0')
          "
          @click.stop="goPath(datas.data[1].link, 2)"
        >
          劣化:{{ datas.data[1].value }}，{{ datas.data[1].percent + "%" }}
        </div>
        <div
          class="zd"
          :style="
            'color:' +
            (isbigscreen ? '#d1e4ff' : isdarkSkin == 1 ? '#d1e4ff' : '#1A80C0')
          "
          @click.stop="goPath(datas.data[2].link, 1)"
        >
          中断:{{ datas.data[2].value }}，{{ datas.data[2].percent + "%" }}
        </div>
      </div>
    </div>
    <div v-if="!echartShow" style="width: 100%; height: 100%; display: table">
      <span style="display: table-cell; vertical-align: middle; width: 100%"
        >暂无统计数据</span
      >
    </div>
  </div>
</template>

<script>
  let echarts = require('echarts/lib/echarts');
  require('echarts/lib/chart/pie');
  require('echarts/lib/component/tooltip');
  require('echarts/lib/component/title');
  require('echarts/lib/component/legend');
import eConfig from "@/config/echart.config.js";
export default {
  props: {
    node: {
      type: String,
      default: "pie"
    },
    isbigscreen:{
      type: Boolean,
      default: false
    },
    isdarkSkin:{
      type: Number,
      default: 0
    },
    height: {
      type: Number,
      default: 260
    },
    styleProp:{
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    },
    r: {
      type: Number,
      default: 75
    },
    datas: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    },
    time: {
      type: Object,
      default: function() {
        return {
          startTime:new Date().format2('yyyy-MM-dd 00:00:00'),
          endTime:new Date().format2('yyyy-MM-dd 23:59:59'),
        };
      }
    }
  },
  data() {
    return {
      minSize:0,
      echartShow:true,
    };
  },
  watch: {
    height: {
      handler(val) {
        this.height = val;
      },
      deep: true
    },
    styleProp: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    },
    isdarkSkin:{
      handler(val) {console.log(val)
        this.drawPie(this.minSize/2);
      },
      deep: true
    }
  },
  mounted() {
    this.minSize = Math.min(this.styleProp.width,this.styleProp.height);
    if (this.datas.data.length > 0) {
      this.echartShow = true;
    }else if (this.datas.data.length < 1) {
      this.echartShow = false;
    }
    if (this.echartShow === true) {
      // this.drawPie(this.minSize*0.97/3);
      this.drawPie(this.minSize/2);
    }
  },
  methods: {
    // 绘制环形图
    drawPie(minSize) {console.log(this.isdarkSkin)
      let that = this;
      that.myChart = echarts.init(document.getElementById(that.node));
      if(that.myChart === undefined){
        that.myChart = echarts.init(document.getElementById(that.node));
      }
      that.myChart.clear();
      var color = this.isbigscreen ? ["#07c5a3", "#feae3c", "#ff4d4f"]: this.isdarkSkin == 1 ? ["#07c5a3", "#feae3c", "#ff4d4f"] :["#33CC33", "#ffa212", "#fb204d"];
      that.myChart.setOption({
        color: color,
        // legend:{
        //   icon:'circle',
        //   right:0,
        //   bottom:0,
        //   formatter:(param)=>{
        //     console.log(param)
        //   }
        // },
        title: {
          show:false,
          text: that.datas.totalValue,
          subtext: that.datas.title,
          left: "39%",
          top: "40%",
          textAlign:"center",
          textStyle: {
            fontSize:24,
            lineHeight:30,
            color:this.isbigscreen ? '#465b7a' : (this.isdarkSkin == 1 ? '#465b7a' :'#182f74'),
            fontFamily: "MicrosoftYaHei",
            // width: '600',
            // height: minSize*2/3*4/5/4*2,
            textAlign:"center",
          },
          subtextStyle: {
            fontSize:14,
            color:this.isbigscreen ? '#465b7a' : (this.isdarkSkin == 1 ? '#465b7a':'#182f74'),
            fontFamily: "MicrosoftYaHei",
            fontWeight:"bold",
            rich: {
              background:"red",
              width:"120px",
              height:"80px",
              color:"red",
              textAlign:"center",

            }
          }
        },
        series: that.series(minSize)
      });
      // window.addEventListener("resize", () => {
      //   if (that.myChart != null) {
      //     that.myChart.resize();
      //   }
      // });
    },
    series(minSize) {
      let that = this,
        data = that.datas.data,
        array = [];
      for (let i = 0, len = data.length; i < len; i++) {
        array.push({
          type: "pie",
          silent: true,
          // radius: [(85) - i * (minSize>125 ? 25 : 15), (85) + (minSize>125 ? 10 : 7) - i * (minSize>125 ? 25 : 15)],
          radius: [(minSize*2/3) - i * minSize*2/3*4/5/4, (minSize*2/3) + (Math.ceil(minSize*2/3*4/5/6)) - i * minSize*2/3*4/5/4],
          center:['40%','50%'],
          minAngle: 0.1, //最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
          // avoidLabelOverlap: true,
          // minShowLabelAngle: 1,
          // itemStyle: {
          // 	normal: {
          // 		labelLine: {
          // 			show: true,
          // 			position:'outside',
          // 			length: 30,
          // 			length2: 40,
          // 			smooth: 0
          // 		},
          // 		borderWidth: 5
          // 	}
          // },
          label: {
            show: false,
            position: "center",
            align: "center",
            // verticalAlign: "bottom",
            formatter: ['{a|'+that.datas.totalValue+'}','{b|}','{c|'+that.datas.title+'}'].join('\n'),
            rich:{
              a:{
                fontSize:24,
                lineHeight:30,
                color:this.isbigscreen||this.isdarkSkin == 1 ? '#A3DCFF' : '#182f74',
              },
              b:{
                fontSize:12,
                lineHeight:10,
                color:this.isbigscreen||this.isdarkSkin == 1 ? '#A3DCFF' : '#182f74',
              },
              c:{
                fontSize:14,
                color:this.isbigscreen||this.isdarkSkin == 1 ? '#A3DCFF' : '#182f74',
              },
            },
            // formatter:'{b}:{c}\n'
          },
          // clockWise:(i===1?true:false),
          // startAngle:(i===1?90:-45),
          data: [
            {
              value: data[i].value,
              // name: data[i].name + '：' + data[i].value + '%'
              name: data[i].name
            },
            {
              value:
                parseFloat(that.datas.totalValue) === 0
                  ? 0
                  : parseFloat(that.datas.totalValue) -
                    parseFloat(data[i].value),
              name: "",
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              // itemStyle: eConfig.placeHolderStyle
              itemStyle: this.isbigscreen ? {color:'#465b7a'} : this.isdarkSkin == 1 ? {color:'#465b7a'}: eConfig.placeHolderStyle
            }
          ]

        });
      }
      return array;
    },
    //跳转
    goPath(link,state){
      // console.log(link);
      const localstorageMenus = JSON.parse(sessionStorage.getItem('accessToken')).funcs;
      if (localstorageMenus && !this.isbigscreen) {
        let toPathMenu = localstorageMenus.filter(item=>item.routeUrl == '/'+link)[0];
        // console.log(toPathMenu);
        let activeParam = {
          parentactiveId:toPathMenu.parentFnId,
          activeId:toPathMenu.fnId,
          parentFunctionName:link==='pathquality'? '拨测分析' : link==='specquality' ? '专线管理' : link === 'rpquality' ? '中继分析' :'',
          navName:toPathMenu.fnName, 
          isdarkSkin:top.window.isdarkSkin
        };  
        top.window.vm.$data.preMenu = activeParam.parentFunctionName;
        top.window.vm.$data.activeName = activeParam.navName;
        top.window.vm.$data.parentactiveId = activeParam.parentactiveId;
        top.window.vm.$data.activeId = activeParam.activeId;
        window.parent.postMessage(activeParam,window.location.origin);//window.postMessage() 方法可以安全地实现跨源通信
        let menu = {
          activeId: activeParam.activeId,
          functionUrl: "/"+link,
          navName: activeParam.navName,
          node1: "",
          parentFunctionName: activeParam.parentFunctionName,
          parentactiveId: activeParam.parentactiveId,
          parentFunctionId:activeParam.parentFunctionId,
          subMenuName: "",
        };
        sessionStorage.setItem("menu",
        JSON.stringify(menu)
      );
        top.document.getElementById('sub-content-page').src = (window.location.hostname==='localhost' ? '/anpm-plug-'+link+'-ui.html?start='+this.time.startTime+'&end='+this.time.endTime+'&type=2&screening='+state : '/'+link+'?start='+this.time.startTime+'&end='+this.time.endTime+'&type=2&screening='+state);
      } 
      },
  },
  beforeDestroy() {
    this.myChart.clear();
    echarts.dispose(this.myChart);
    this.myChart = void 0
  }
};
</script>

<style scoped>
.cake,
.brokenLine {
  /*min-height: 160px;*/
  height: 100%;
}
.cake .piePCont {
  position: absolute;
  line-height: 24px;
  width: 120px;
  height: 24px;
}
.cake .piePCont.normal {
  color: #07c5a3;
  top: -75px;
  left: 115px;
}
.cake .piePCont.normal:before {
  content: "";
  width: 85px;
  height: 0;
  display: block;
  position: absolute;
  left: -75px;
  border-top: 1px solid #07c5a3;
  transform: translate(0px, 10px) rotate(0deg);
}
.cake .piePCont.degradation {
  color: #feae3c;
  top: 75px;
  left: 115px;
}
.cake .piePCont.degradation:before {
  content: "";
  width: 140px;
  height: 0;
  display: block;
  position: absolute;
  left: -85px;
  border-top: 1px solid #feae3c;
  -webkit-transform: translate(15px, 10px) rotate(0deg);
  transform: translate(0px, -55px) rotate(50deg);
}
.cake .piePCont.break {
  color: #ff4d4f;
  top: -75px;
  right: 115px;
  text-align: right;
}
.cake .piePCont.break:before {
  content: "";
  width: 110px;
  height: 0;
  display: block;
  position: absolute;
  right: -120px;
  border-top: 1px solid #ff4d4f;
  -webkit-transform: translate(15px, 10px) rotate(0deg);
  transform: translate(0px, 20px) rotate(10deg);
}
.legend > div {
  line-height: 30px;
  text-align: left;
  cursor: default;
}
.legend > div:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 8px;
  margin-top: -2px;
}
.legend > div.zc:before {
  background: #07c5a3;
  border: 1px solid #07c5a3;
}
.legend > div.lh:before {
  background: #feae3c;
  border: 1px solid #feae3c;
}
.legend > div.zd:before {
  background: #ff4d4f;
  border: 1px solid #ff4d4f;
}
.legend > div.zc {
  color: #617ca5;
}
.legend > div.lh,
.legend > div.zd {
  cursor: pointer;
  color: #d1e4ff;
}

.legend.bigscreen > div:before {
  content: "";
  display: inline-block;
  width: 26px;
  height: 8px;
  border-radius: 4px;
  vertical-align: middle;
  margin-right: 8px;
  margin-top: -2px;
}
.legend.bigscreen > div.zc:before {
  background: #1252c8;
  border: 1px solid #1252c8;
}
.legend.bigscreen > div.lh:before {
  background: #ed921c;
  border: 1px solid #ed921c;
}
.legend.bigscreen > div.zd:before {
  background: #f01c1c;
  border: 1px solid #f01c1c;
}
.legend.bigscreen {
  color: #1a80c0;
}
.legend.bigscreen > div.lh,
.legend > div.zd {
  cursor: pointer;
  /* color: #57a3f3; */
}
.flexBox {
  display: flex;
  flex-direction: column;
  align-content: center;
}
.flexBox p {
  text-align: center;
  -webkit-flex: 1;
  flex: 1;
}
</style>
