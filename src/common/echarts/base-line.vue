<template>
  <section style="width: 100%; height: 100%" ref="line">
    <div
      :id="node"
      style="height: 100%"
      :style="{ width: line_width + 'px' }"
    ></div>
  </section>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
  require('echarts/lib/component/dataZoom')
import eConfig from "@/config/echart.config.js";
export default {
  name: "base-line",
  props: {
    pageType: {
      type: [String, Number],
      default: 1 //1、首页，2、报告
    },
    print: {
      type: Boolean,
      default: false
    },
    node: {
      type: String,
      default: "line"
    },
    unit: {
      type: String,
      default: ""
    },
    lineData: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    },
    isdarkSkin:{
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      line_width: "",
      color_obj: {
        // '时延劣化':['#58a6e8','88,166,232'],
        中断: ["#fb204d", "251,32,77"],
        丢包劣化: ["#4e7bff", "78,123,255"],
        时延劣化: ["#ffa212", "255,162,18"],
        时延: ["#4e7bff", "78,123,255"],
        丢包率: ["#fb204d", "251,32,77"],
        可用率: ["#4e7bff", "78,123,255"],
        优良率: ["#ffa212", "255,162,18"]
      },
      color: [],
      colorRgb: [],
      line_data: {},
      name_obj: {
        时延: "ms",
        丢包率: "%",
        可用率: "%",
        优良率: "%"
      }
    };
  },
  watch: {
    lineData: {
      handler(val) {
        this.line_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    print: {
      handler() {
        this.getWidth();
      },
      deep: true
    },
    isdarkSkin:{
      handler(val) {console.log(val)
        this.init();
      },
      deep: true
    }
  },
  methods: {
    init() {
      let _self = this;
      _self.myChart = echarts.init(document.getElementById(_self.node));
      let option = Object.assign(
        {},
        {
          color: _self.setColor(),
          tooltip: _self.setTooltip(),
          legend: _self.legend(),
          grid: _self.grid(),
          xAxis: _self.xAxis(),
          yAxis: _self.yAxis(),
          series: _self.series()
        }
      );
      if (option && typeof option === "object") {
        _self.myChart.clear();
        _self.myChart.setOption(option, true);
        window.addEventListener("resize", () => {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        });
      }
    },
    setColor() {
      let _slef = this,
        legend = this.line_data.legend,
        color = [],
        colorRgb = [];
      for (let i = 0, len = legend.length; i < len; i++) {
        color.push(_slef.color_obj[legend[i]][0]);
        colorRgb.push(_slef.color_obj[legend[i]][1]);
      }
      this.color = color;
      this.colorRgb = colorRgb;
      return color;
    },
    setTooltip() {
      if (this.pageType === 1) {
        let obj = {};
        let _self = this;
        obj.trigger = "axis";
        obj.axisPointer = {
          type: "shadow",
          shadowStyle: {
            color: "rgba(238,240,246,0)",
            width: 30
          }
        };
        obj.formatter = function() {};
        return (
          obj,
          eConfig.tip("axis", function(param) {
            let src = "";
            src += param[0].name + "<br />";
            for (let i = 0, len = param.length; i < len; i++) {
              src +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
              src +=
                param[i].seriesName +
                "：" +
                (param[i].value === undefined ||
                param[i].value === null ||
                param[i].value === ""
                  ? "--"
                  : param[i].value) +
                "<br />";
            }
            return src;
          })
        );
      } else {
        let _self = this;
        return eConfig.tip("axis", function(param) {
          let src = "";
          src += param[0].name + "<br />";
          for (let i = 0, len = param.length; i < len; i++) {
            src +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            src +=
              param[i].seriesName +
              "：" +
              (param[i].value === undefined ||
              param[i].value === null ||
              param[i].value === ""
                ? "--"
                : param[i].value) +
              _self.name_obj[param[i].seriesName] +
              "<br />";
          }
          return src;
        });
      }
    },
    legend() {
      let obj = {};
      obj.right = "4%";
      obj.icon = "circle";
      obj.data = this.line_data.legend;
      obj.textStyle = {
        color: this.isdarkSkin == 1 ?"#617ca5" : "#484b56",
        fontSize: 14,
        fontFamily: "MicrosoftYaHei-Bold"
      };
      return obj;
    },
    grid() {
      return {
        left: "5%",
        right: "5%",
        bottom: "3%",
        containLabel: true
      };
    },
    xAxis() {
      let obj = eConfig.axis(12, true, false, this.isdarkSkin == 1 ? '#617ca5':"#676f82", this.isdarkSkin == 1 ? '#617ca5':"#e3e7f2");
      obj.type = "category";
      obj.boundaryGap = false;
      obj.data = this.line_data.xAxis;
      return obj;
    },
    yAxis() {
      let obj = eConfig.axis(12, false, true, this.isdarkSkin == 1 ? '#617ca5':"#676f82", this.isdarkSkin == 1 ? '#617ca5':"#e3e7f2");
      obj.type = "value";
      obj.scale = true;
      obj.max = this.line_data.type === "loss" ? 100 : null;
      return obj;
    },
    series() {
      let array = [],
        legend = this.line_data.legend,
        data = this.line_data.data;
      for (let i = 0, len = legend.length; i < len; i++) {
        let obj = {
          type: "line"
        };
        obj.name = legend[i];
        obj.data = data[i];
        obj.smooth = true;
        if (this.pageType === 1) {
          obj.symbol = "circle";
          obj.symbolSize = 4;
          obj.itemStyle = {
            normal: {
              borderWidth: 0
            }
          };
          obj.emphasis = {
            // 鼠标经过时：
            label: {
              show: true,
              position: "bottom",
              fontSize: 20,
              fontFamily: "MicrosoftYaHei",
              fontWeight: "bold"
            },
            itemStyle: {
              borderWidth: 2,
              borderColor: "#fff"
            }
          };
        }
        obj.areaStyle = {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(" + this.colorRgb[i] + ", 0.25)"
              },
              {
                offset: 0.9,
                color: "rgba(" + this.colorRgb[i] + ", 0)"
              }
            ]
          }
        };
        array.push(obj);
      }
      return array;
    },
    getWidth() {
      let node = this.$refs["line"];
      if (node != undefined) {
        let offsetWidth = node.offsetWidth;
        this.line_width = offsetWidth;
        if (this.myChart != undefined) {
          this.myChart.dispose();
          this.myChart = null;
        }
        this.$nextTick(() => {
          this.init();
        });
      }
    },
    resize() {
      let _self = this;
      window.addEventListener("resize", () => {
        _self.getWidth();
      });
    }
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.dispose();
    this.myChart = null;
    
    window.removeEventListener("resize");
  },
  mounted() {
    this.line_data = Object.assign({}, this.lineData);
    this.getWidth();
    this.resize();
  }
};
</script>
