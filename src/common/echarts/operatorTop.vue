<template>
  <section style="width: 100%; height: 100%" ref="line">
    <div
      :id="node"
      style="height: 100%"
      :style="{ width: line_width + 'px' }"
    ></div>
  </section>
</template>

<script>
import echarts from "echarts";
export default {
  name: "operatorRanking-ecahrt",
  props: {
    node: {
      type: String,
      default: "line"
    },
    lineData: {
      type: Object,
      default: function() {}
    },
    operatorData:{
      type: Object,
      default: function() {return [];}
    },
    isdarkSkin:{
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      line_width: "",
      operator_list:[],
      dataArray:[],
    };
  },
  methods: {

     // 获取运营商类型国际化信息
   async getListInternationaControlOperator() {
      await this.$http.wisdomPost('/internationaControl/queryCode',{key:"internation_operator_type"}).then(res => {
          if (res.code == 1) {
            if(res.data){
                this.internationOperatorListType = res.data.value;
            }
          }
      });
     //获取运营商
      var operator_list = [];
      var internationOperatorTypeTemp = 'chinaOperator';
      if(this.internationOperatorListType == '1'){
          internationOperatorTypeTemp = 'philippinesOperator';
      }
      await this.$http
        .wisdomPost("/dataTable/queryCode",{key:internationOperatorTypeTemp})
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if(data){
              data.map(item => {
                  operator_list.push({value:parseInt(item.value),label:item.lable});
              });
            }
          } else {
            operator_list = [];
        }
      })
      return operator_list;
   },
    
    init() {
      let _self = this;
      if (_self.myChart) {
        _self.myChart.clear();
      }

    this.dataArray = [];
      for(var i = 0; i< this.operator_list.length ; i++){
          this.dataArray.push(100);
      }

      _self.myChart = echarts.init(document.getElementById(_self.node));
      let option = Object.assign(
        {},
        {
          title: {
            show:this.line_data.length<1,
            text: this.$t('common_No_data'),
            left:"center",
            textStyle:{
              color:this.isdarkSkin == 1 ? '#617ca5':"#333",
              fontFamily:"serif",
              fontWeigth:"400",
              fontSize:18
            }
          },
        },
        {
          grid: _self.grid(),
          xAxis: _self.xAxis(),
          yAxis: _self.yAxis(),
          series: _self.series()
        }
      );
      if (option && typeof option === "object") {
        _self.myChart.clear();
        _self.myChart.setOption(option, true);
        window.addEventListener("resize", () => {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        });
      }
    },
    grid() {
      return {
        left: "5%",
        right: "5%",
        bottom: "3%",
        top: "10",
        containLabel: true
      };
    },
    xAxis() {
      return {
        show: false
      };
    },
    yAxis() {
      let obj = {};
      obj.type = "category";
      obj.splitLine = {
        show: false
      };
      obj.axisTick = {
        show: false,
        interval: 0,
        alignWithLabel: true
      };
      obj.axisLabel = {
        fontSize: 14,
        color:this.isdarkSkin == 1 ? '#617ca5':''
      };
      obj.axisLine = {
        show: false
      };
      obj.splitLine = {
        show: false
      };
      obj.axisTick = {
        show: false
      };
      obj.boundaryGap = true;
      obj.inverse = true;
      // obj.interval = 5;
      obj.max = value => {
        // return value.max - (5-this.line_data.yAxis.length)
        return value.max;
      };
      obj.data = this.line_data.yAxis;
      return obj;
    },
    series() {
      let obj = {
        type: "bar",
        barWidth: 20,
        yAxisIndex: 0,
        cursor: "auto",
        label: {
          show: true,
          position: "insideRight",
          color: this.isdarkSkin == 1 ? '#617ca5':"#fff",
          distance: -50,
          formatter: function(param) {
            if (Number(param.data) > 10) {
              return "{percentIn|" + param.data + "%}{percentOut|}";
            } else {
              return "{percentIn|}{percentOut|" + param.data + "%}";
            }
          },
          rich: {
            percentIn: {
              width: 50,
              color: "#fff",
              align: "right",
              padding: [0, 10]
            },
            percentOut: {
              width: 50,
              color: "#000",
              align: "left"
            }
          }
        }
      };
      (obj.itemStyle = {
        normal: {
          // 定制显示（按顺序）
          color: params => {
                return this.getInternationaControlOperator(params);
          },
          barBorderRadius: 30
        },
        emphasis: {
          color: "#7EBEF4"
        }
      }),
        // obj.name=legend[i];
        (obj.data = this.line_data.xAxis);

      let obj2 = {
        type: "bar",
        barWidth: 20,
        xAxisIndex: 0,
        barGap: "-100%",
        // data: [100, 100, 100, 100, 100],
        data: this.dataArray,
        itemStyle: {
          normal: {
            color: this.isdarkSkin == 1 ? '#465b7a':"#eee",
            barBorderRadius: 30
          },
          emphasis: {
            color: "#eee"
          }
        },

        zlevel: -1
      };
      return [obj, obj2];
    },
    getInternationaControlOperator(params){

      let items = this.operator_list.filter((item)=>{
          return item.label = params.name;
      });

      if(items.length > 0){
          return "#7EBEF4";
      }

            //  if (params.name === this.$t("server_China_Unicom")) {
            //   return "#7EBEF4";
            // } else if (params.name === this.$t("server_China_Mobile")) {
            //   return "#7EBEF4";
            // } else if (params.name ===this.$t("server_China_Telecom")) {
            //   return "#7EBEF4";
            // } else if (params.name === this.$t("server_China_Broadcasting")) {
            //   return "#7EBEF4";
            // } else if (params.name === this.$t("server_China_other")  || params.name === this.$t("server_China_other2") ) {
            //   return "#7EBEF4";
            // }
    },
    getWidth() {
      let node = this.$refs["line"];
      if (node != undefined) {
        let offsetWidth = node.offsetWidth;
        this.line_width = offsetWidth;
        if (this.myChart != undefined) {
          this.myChart.clear();
          this.myChart.dispose();
          this.myChart = null;
        }
        this.$nextTick(() => {
          this.init();
        });
      }
    },
    resize() {
      let _self = this;
      window.addEventListener("resize", () => {
        _self.getWidth();
      });
    }
  },
  watch: {
    lineData: {
      handler(val) {
        this.line_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    isdarkSkin:{
      handler(val) {
        this.init();
      },
      deep: true
    }
  },
 mounted() {
    this.operator_list = this.operatorData;
    this.line_data = Object.assign({}, this.lineData);
    this.getWidth();
    // this.resize();
  },
  created(){
    this.operator_list = this.operatorData;
    // this.operator_list = await this.getListInternationaControlOperator();
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.clear();
    this.myChart.dispose();
    this.myChart = null;
    window.removeEventListener("resize");
  }
};
</script>

<style scoped></style>
