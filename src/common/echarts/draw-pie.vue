<template>
  <div style="position: relative">
    <div class="cake" :id="node"></div>
    <div
      class="cake"
      :style="{ position: 'absolute', width: '100%', left: '0', top: '0' }"
    >
      <div
        class="legend"
        style="
          width: 160px;
          position: relative;
          left: 50%;
          top: 50%;
          margin-left: 100px;
          margin-top: -15px;
        "
      >
        <div class="zc">
          正常:{{ datas.data[0].value }}，{{ datas.data[0].percent + "%" }}
        </div>
        <div class="lh" @click.stop="goPath(datas.data[1].link)">
          劣化:{{ datas.data[1].value }}，{{ datas.data[1].percent + "%" }}
        </div>
        <div class="zd" @click.stop="goPath(datas.data[2].link)">
          中断:{{ datas.data[2].value }}，{{ datas.data[2].percent + "%" }}
        </div>
      </div>
      <!--<div-->
      <!--style="width:1px;height:1px;position: relative;left:50%;top:50%;margin-left:-.5px;margin-top:-.5px"-->
      <!--&gt;-->
      <!--<div class="piePCont normal" style="">-->
      <!--正常:{{ datas.data[0].percent + "%" }}-->
      <!--</div>-->
      <!--<div class="piePCont degradation" style="">-->
      <!--劣化:{{ datas.data[1].percent + "%" }}-->
      <!--</div>-->
      <!--<div class="piePCont break" style="">-->
      <!--中断:{{ datas.data[2].percent + "%" }}-->
      <!--</div>-->
      <!--</div>-->
    </div>
  </div>
</template>

<script>
  let echarts = require('echarts/lib/echarts');
  require('echarts/lib/chart/pie');
  require('echarts/lib/component/tooltip');
  require('echarts/lib/component/title');
  require('echarts/lib/component/legend');
import eConfig from "@/config/echart.config.js";
export default {
  props: {
    node: {
      type: String,
      default: "pie"
    },
    height: {
      type: Number,
      default: 260
    },
    r: {
      type: Number,
      default: 75
    },
    datas: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    },
    time: {
      type: Object,
      default: function() {
        return {
          startTime:new Date().format2('yyyy-MM-dd 00:00:00'),
          endTime:new Date().format2('yyyy-MM-dd 23:59:59'),
        };
      }
    }
  },
  data() {
    return {};
  },
  watch: {
    height: {
      handler(val) {
        this.height = val;
      },
      deep: true
    },
    r: {
      handler(val) {
        this.r = val;
      },
      deep: true
    }
  },
  mounted() {
    this.drawPie();
  },
  methods: {
    // 绘制环形图
    drawPie() {
      let that = this;
      that.myChart = echarts.init(document.getElementById(that.node));
      var color = ["#33CC33", "#ffa212", "#fb204d"];
      that.myChart.setOption({
        color: color,
        // legend:{
        //   icon:'circle',
        //   right:0,
        //   bottom:0,
        //   formatter:(param)=>{
        //     console.log(param)
        //   }
        // },
        // title: {
        //   subtext: that.datas.totalValue,
        //   text: that.datas.title,
        //   x: "center",
        //   y: "40%",
        //   textStyle: {
        //     color: "#182f74",
        //     fontSize: 24,
        //     fontFamily: "MicrosoftYaHei",
        //     fontWeight: "bold"
        //   },
        //   subtextStyle: {
        //     color: "#182f74",
        //     fontSize: 16,
        //     fontFamily: "MicrosoftYaHei",
        //     fontWeight: "bold"
        //   }
        // },
        series: that.series()
      });
      window.addEventListener("resize", () => {
        if (that.myChart != null) {
          that.myChart.resize();
        }
      });
    },
    series() {
      let that = this,
        data = that.datas.data,
        array = [];
      for (let i = 0, len = data.length; i < len; i++) {
        array.push({
          type: "pie",
          silent: true,
          radius: [75 - i * 15, 75 + 5 - i * 15],
          center:['40%','50%'],
          minAngle: 0.1, //最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
          avoidLabelOverlap: true,
          minShowLabelAngle: 1,
          // itemStyle: {
          // 	normal: {
          // 		labelLine: {
          // 			show: false,
          // 			position:'outside',
          // 			length: 30,
          // 			length2: 40,
          // 			smooth: 0
          // 		},
          // 		borderWidth: 5
          // 	}
          // },
          label: {
            show: true,
            position: "center",
            align: "center",
            // verticalAlign: "bottom",
            // formatter: "{c}",
            formatter: ['{a|'+that.datas.totalValue+'}','{b|'+that.datas.title+'}'].join('\n'),
            rich:{
              a:{
                fontSize:18,
                lineHeight:30,
                color:'#182f74',
              },
              b:{
                fontSize:22,
                color:'#182f74',
              },
            },
            // formatter:'{b}:{c}\n'
          },
          // clockWise:(i===1?true:false),
          // startAngle:(i===1?90:-45),
          data: [
            {
              value: data[i].value,
              // name: data[i].name + '：' + data[i].value + '%'
              name: data[i].name
            },
            {
              value:
                parseFloat(that.datas.totalValue) === 0
                  ? 0
                  : parseFloat(that.datas.totalValue) -
                    parseFloat(data[i].value),
              name: "",
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              itemStyle: eConfig.placeHolderStyle
            }
          ]
        });
      }
      return array;
    },
    //跳转
    goPath(link){

      const pathquality = {
        activeId: 8,
        functionCode: "7",
        functionUrl: "/pathquality",
        navName: "拨测质差分析",
        node1: "3",
        parentFunctionName: "拨测分析",
        parentactiveId:4,
        subMenuName: "7"};
      const rpquality = {
        activeId: 19,
        parentactiveId:5,
        functionCode: "17",
        functionUrl: "/rpquality",
        navName: "中继质差分析",
        node1: "4",
        parentFunctionName: "中继分析",
        subMenuName: "17"};
      sessionStorage.setItem("menu",
        JSON.stringify(link==='pathquality'? pathquality : rpquality)
      );
      let activeParam = {
        parentactiveId:link==='pathquality'? pathquality.parentactiveId : rpquality.parentactiveId,
        activeId:link==='pathquality'? pathquality.activeId : rpquality.activeId,
        parentFunctionName:link==='pathquality'? pathquality.parentFunctionName : rpquality.parentFunctionName,
        navName:link==='pathquality'? pathquality.navName : rpquality.navName,
      };
      window.parent.postMessage(activeParam,window.location.origin);//window.postMessage() 方法可以安全地实现跨源通信
      top.document.getElementById('sub-content-page').src = (window.location.hostname==='localhost' ? '/anpm-plug-'+link+'-ui.html?start='+this.time.startTime+'&end='+this.time.endTime: '/'+link+'?start='+this.time.startTime+'&end='+this.time.endTime);
    },
  },
  beforeDestory() {
    window.removeEventListener("resize");

  }
};
</script>

<style scoped>
.cake,
.brokenLine {
  height: 260px;
  border-bottom: 1px solid #e7e7e7;
}
.cake .piePCont {
  position: absolute;
  line-height: 24px;
  width: 120px;
  height: 24px;
}
.cake .piePCont.normal {
  color: #33cc33;
  top: -75px;
  left: 115px;
}
.cake .piePCont.normal:before {
  content: "";
  width: 85px;
  height: 0;
  display: block;
  position: absolute;
  left: -75px;
  border-top: 1px solid #33cc33;
  transform: translate(0px, 10px) rotate(0deg);
}
.cake .piePCont.degradation {
  color: #ffa212;
  top: 75px;
  left: 115px;
}
.cake .piePCont.degradation:before {
  content: "";
  width: 140px;
  height: 0;
  display: block;
  position: absolute;
  left: -85px;
  border-top: 1px solid #ffa212;
  -webkit-transform: translate(15px, 10px) rotate(0deg);
  transform: translate(0px, -55px) rotate(50deg);
}
.cake .piePCont.break {
  color: #fb204d;
  top: -75px;
  right: 115px;
  text-align: right;
}
.cake .piePCont.break:before {
  content: "";
  width: 110px;
  height: 0;
  display: block;
  position: absolute;
  right: -120px;
  border-top: 1px solid #fb204d;
  -webkit-transform: translate(15px, 10px) rotate(0deg);
  transform: translate(0px, 20px) rotate(10deg);
}
.legend > div {
  line-height: 30px;
  text-align: left;
  cursor: default;
}
.legend > div:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 8px;
  margin-top: -2px;
}
.legend > div.zc:before {
  background: #33cc33;
  border: 1px solid #33cc33;
}
.legend > div.lh:before {
  background: #ffa212;
  border: 1px solid #ffa212;
}
.legend > div.zd:before {
  background: #fb204d;
  border: 1px solid #fb204d;
}
.legend > div.lh,
.legend > div.zd {
  cursor: pointer;
  color: #57a3f3;
}
</style>
