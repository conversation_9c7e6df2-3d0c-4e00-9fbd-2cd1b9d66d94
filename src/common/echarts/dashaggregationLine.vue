<template>
  <div style="width: 100%; height: 100%">
    <div class="brokenLine" :id="node" :ref="node" style="'height:100%"></div>
  </div>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
import echartFn from "@/common/mixins/echartFun";
export default {
  mixins: [echartFn],
  props: {
    node: {
      type: String,
      default: "line"
    },
    isbigscreen:{
      type: Boolean,
      default: false
    },
    isdarkSkin:{
      type: [Number, String],
      default: 0
    },
    height: {
      type: Number,
      default: 260
    },
    lineData: {
      type: Object,
      default: function() {
        return {
          data: {}
        };
      }
    }
  },
  data() {
    return {
      myChart1:null,
      //指标标题
      // indictorHead:["最新","最小","最大","平均"],
      indictorHead:[this.$t("common_latest_value"),this.$t("common_min"),this.$t("common_max"),this.$t("common_avg")],
      color:[],
      dataObj:{"ms":{color:[],data:[]},"%":{color:[],data:[]},"bps":{color:[],data:[]}},
      time:[],
      //y坐标轴位置
      delayYAxis:'left',
      useYAxis:'right',
      flowYAxis:'left',
      flowYshow:false,
      //获取流速单位
      getflownum:1,
      getflowUnit:'bps',
      //三种指标
      delayflag:false,
      useflag:true,
      flowflag:true,
      minDelay:0,
      maxDelay:0,
    };
  },
  watch: {
    height: {
      handler(val) {
        this.height = val;
      },
      deep: true
    },
    lineData: {
      handler(val) {
        // console.log(val)
        // console.log(val['specdelay'])
      },
      deep: true,
      immediate:true
    },
    isdarkSkin:{
      handler(val) {console.log(11)
        this.handleData(this.lineData);
        // console.log(this.dataObj)
        this.useYAxis = 'right';
        this.flowYshow = false;
        this.useflag = false;
        this.flowflag = false;
        let series = [];
        if (this.dataObj['Mbps'].data.length <1 && this.dataObj['ms'].data.length <1) {
          this.useYAxis = 'left';
        }
        this.flowYshow = false;
        if (this.dataObj['ms'].data.length <1 && this.dataObj['Mbps'].data.length > 0 ) {
          this.flowYshow = true;
        }
        for (let index = 0; index < this.dataObj['ms'].data.length; index++) {
          const itemData = this.dataObj['ms'].data[index];
          const itemColor = this.dataObj['ms'].color[index];
          this.delayflag = true;
          this.maxDelay = Math.max(Math.max.apply(null,itemData),this.maxDelay);
          this.minDelay = Math.min(Math.min.apply(null,itemData),this.minDelay);
          // console.log(itemColor)
          series.push(
              {
                name: "ms",
                type: "line",
                yAxisIndex:0,
                symbol: "circle",
                symbolSize:3,
                smooth: true,
                z: 1,
                itemStyle: {
                  normal: {
                    color:itemColor,
                    lineStyle: {
                      width: 1,
                      corlor:itemColor
                    }
                  }
                },
                lineStyle:{
                  width: 1,
                  color:itemColor
                },
                data: itemData,
              }
          )
        }
        for (let index = 0; index < this.dataObj['%'].data.length; index++) {
          this.useflag = true;
          const itemData = this.dataObj['%'].data[index];
          const itemColor = this.dataObj['%'].color[index];
          series.push(
              {
                name: "%",
                type: "line",
                yAxisIndex:1,
                z: 2,
                smooth: true,
                symbol: "circle",
                symbolSize:3,
                data: itemData,
                itemStyle: {
                  normal: {
                    color:itemColor,
                    lineStyle: {
                      width: 1,
                      color:itemColor
                    }
                  }
                },
                lineStyle:{
                  width: 1,
                  color:itemColor
                }

              },
          )
        }
        for (let index = 0; index < this.dataObj['Mbps'].data.length; index++) {
          this.flowflag = true;
          const itemData = this.dataObj['Mbps'].data[index];
          const itemColor = this.dataObj['Mbps'].color[index];
          series.push(
              {
                name: "bps",
                type: "line",
                yAxisIndex:2,
                z: 3,
                smooth: true,
                symbol: "circle",
                symbolSize:3,
                data: itemData,
                itemStyle: {
                  normal: {
                    color:itemColor,
                    lineStyle: {
                      width: 1,
                      color:itemColor
                    }
                  }
                },
                lineStyle:{
                  width: 1,
                  color:itemColor
                }
              },
          )
        }
        this.ceshi(series);
      },
      deep: true,
    }
  },
  computed:{

  },
  mounted() {console.log(this.isdarkSkin)
    // console.log(this.lineData);
    // this.lineData.specoutflow = {aip: "*********", zip: "*******",color:"red",dataArr:[29830.90,123487324,79328795423,7878322],time:["2021-10-15 12:05:00","2021-10-15 12:10:00","2021-10-15 12:15:00","2021-10-15 12:20:00"]}
    this.handleData(this.lineData);
    // console.log(this.dataObj)
    this.useYAxis = 'right';
    this.flowYshow = false;
    this.useflag = false;
    this.flowflag = false;
    let series = [];
    if (this.dataObj['Mbps'].data.length <1 && this.dataObj['ms'].data.length <1) {
      this.useYAxis = 'left';
    }
    this.flowYshow = false;
    if (this.dataObj['ms'].data.length <1 && this.dataObj['Mbps'].data.length > 0 ) {
      this.flowYshow = true;
    }
    for (let index = 0; index < this.dataObj['ms'].data.length; index++) {
      const itemData = this.dataObj['ms'].data[index];
      const itemColor = this.dataObj['ms'].color[index];
      this.delayflag = true;
      this.maxDelay = Math.max(Math.max.apply(null,itemData),this.maxDelay);
      this.minDelay = Math.min(Math.min.apply(null,itemData),this.minDelay);
      // console.log(itemColor)
      series.push(
        {
          name: "ms",
          type: "line",
          yAxisIndex:0,
          symbol: "circle",
          symbolSize:3,
          smooth: true,
          z: 1,
          itemStyle: {
            normal: {
              color:itemColor,
              lineStyle: {
                width: 1,
                corlor:itemColor
              }
            }
          },
          lineStyle:{
            width: 1,
            color:itemColor
          },
          data: itemData,
        }
      )
    }
    for (let index = 0; index < this.dataObj['%'].data.length; index++) {
      this.useflag = true;
      const itemData = this.dataObj['%'].data[index];
      const itemColor = this.dataObj['%'].color[index];
      series.push(
        {
          name: "%",
          type: "line",
          yAxisIndex:1,
          z: 2,
          smooth: true,
          symbol: "circle",
          symbolSize:3,
          data: itemData,
          itemStyle: {
            normal: {
              color:itemColor,
              lineStyle: {
                width: 1,
                color:itemColor
              }
            }
          },
          lineStyle:{
            width: 1,
            color:itemColor
          }

        },
      )
    }
    for (let index = 0; index < this.dataObj['Mbps'].data.length; index++) {
      this.flowflag = true;
      const itemData = this.dataObj['Mbps'].data[index];
      const itemColor = this.dataObj['Mbps'].color[index];
      series.push(
        {
          name: "bps",
          type: "line",
          yAxisIndex:2,
          z: 3,
          smooth: true,
          symbol: "circle",
          symbolSize:3,
          data: itemData,
          itemStyle: {
            normal: {
              color:itemColor,
              lineStyle: {
                width: 1,
                color:itemColor
              }
            }
          },
          lineStyle:{
            width: 1,
            color:itemColor
          }
        },
      )
    }
    this.ceshi(series);
  },
  methods: {
    //echart高度获取
    divheight(){
      let height = Object.keys(this.lineData).length*22+22+8 + 'px';
      return `height:calc((100% - ${height})`
    },
    //流量数据处理
    flowDataHandle (val,key) {
      let newVal = val;
      if (key.search("specentryflow")!=-1 || key.search("specoutflow")!=-1 || key.search("rpentryflow")!=-1 || key.search("rpoutflow")!=-1 || key.search("pathentryflow")!=-1 || key.search("pathoutflow")!=-1) {
        newVal = val;
      }

      return  newVal!=null && newVal!=undefined  && newVal>=0  ? Number.parseFloat(newVal).toFixed(2) : '--'
      // return newVal>=0 ? Math.ceil(newVal*100)/100 : '--'
    },
    //处理传递过来的数据
    handleData(data){
      // console.log(data)
      this.dataObj = {"ms":{color:[],data:[]},"%":{color:[],data:[]},"Mbps":{color:[],data:[]}};
      let currentFlowUnit = 1;
      Object.keys(data).forEach(key=>{
        if (key && Object.keys(data[key]).length>0) {
          this.time = this.time.length < data[key].time.length ? data[key].time : this.time;
          let itemFirstTime = data[key].time[0],itemLastTime = data[key].time[data[key].time.length -1 ];
          if (itemFirstTime) {
            if (new Date(itemFirstTime).valueOf()<new Date(this.time[0]).valueOf()) {
              this.time.splice(0,0,itemFirstTime)
            }
          }
          if (itemLastTime) {
            if (new Date(itemLastTime).valueOf()>new Date(this.time[this.time.length-1]).valueOf()) {
              this.time.concat(itemLastTime)
            }
          }
          if (key.search("specdelay")!=-1 || key.search("rpdelay")!=-1 || key.search("pathdelay")!=-1) {
            this.dataObj['ms'].color.push(data[key].color);
            this.dataObj['ms'].data.push(data[key].dataArr);
            this.color.push(data[key].color);
          }else if (key.search("specentryflow")!=-1 || key.search("specoutflow")!=-1 || key.search("rpentryflow")!=-1 || key.search("rpoutflow")!=-1 || key.search("pathentryflow")!=-1 || key.search("pathoutflow")!=-1) {
            this.dataObj['Mbps'].color.push(data[key].color);
            if (key.search("entryflow")!=-1) {
              currentFlowUnit = data[key].target.valAvg > currentFlowUnit ? data[key].target.valAvg : currentFlowUnit;
            }else if (key.search("outflow")!=-1) {
              currentFlowUnit = data[key].target.valAvg > currentFlowUnit ? data[key].target.valAvg : currentFlowUnit;
            }
            data[key].dataArr = data[key].dataArr.map(itemVal=>{
              return this.flowDataHandle(itemVal,'specentryflow');
            })
            this.dataObj['Mbps'].data.push(data[key].dataArr);
            this.color.push(data[key].color);
          }else{
            data[key].dataArr = data[key].dataArr.map(itemVal=>{
              if (itemVal < 0) {
                itemVal = "";
              }
              return itemVal
            })
            this.dataObj['%'].color.push(data[key].color);
            this.dataObj['%'].data.push(data[key].dataArr);
            this.color.push(data[key].color);
          }
        }
      })
      let flowMsg = this.getUnits(currentFlowUnit, true, true);
      let getflownum = flowMsg[2];
      this.getflowUnit = flowMsg[1];
      this.getflownum = getflownum<=0 ? 1 : getflownum;
    },
    ceshi(series) {
      let that = this;
      console.log(that.myChart1)
      that.myChart1 = echarts.init(document.getElementById(that.node));
      that.myChart1.clear();
      that.myChart1.setOption({
        // color: isbigscreen ? ["#1252C8", "#ED921C", "#F01C1C"] : ["#4e7bff", "#ffa212", "#fb204d"],
        // color: this.color,
        // title: {
        //   text: "最近30天" + that.lineData.title + "告警走势",
        //   top: "3%"
        // },
        tooltip: {
          trigger: "axis",
          confine: true,
          axisPointer: {
            type: "line",
            snap: true,
            shadowStyle: {
              color: "rgba(0,0,0,1)",
              width: 30,
            },
             lineStyle: {},
          },
         
          backgroundColor: this.isdarkSkin == 1 ? 'rgba(12, 56, 123,0.7)' : '#FFFFFF',//"rgba(18,55,127,.8)",
          padding:[10,18],
          textStyle: {
            align: "left",
            color: this.isdarkSkin == 1 ? '#fff' : '#515A6E',// , "#d1e4ff",
          },
          extraCssText:this.isdarkSkin == 1 ? '':'box-shadow:  0px 0px 8px 1px rgba(0,0,0,0.16);' ,
          formatter:(param)=>{
            const paramArr = param;
            let msgDom = "<div>";
            if (paramArr.length>0) {
              msgDom +='<span>'+paramArr[0].axisValue+'</span><br/>';
              for (let index = 0; index < paramArr.length; index++) {
                const item = paramArr[index];
                msgDom +='<p style="line-height:18px;"><span class="tooltip-round" style="vertical-align: middle;border-radius:50%;display:inline-block;width:12px;height:12px;margin-right:8px;background-color:'+item.color+'"></span>'+(item.value!=undefined && item.value != null && item.value>=0 && item.value!=='' ? (item.seriesName != 'bps' ? item.value.toFixed(2) + ' ' +item.seriesName : this.getUnit(item.value)) : '--' )+'</p>'
              }
              return msgDom
            }
          }
        },
        grid: {
          top: "15",
          left: "10",
          right: "25",
          bottom: "5",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine:{
              show:isbigscreen ? false : false,
              lineStyle:{
                color:isbigscreen ? '#5CA0D5':this.isdarkSkin == 1 ? '#5CA0D5':'#0e2a5f',
                width:1
              }
            },
            axisLabel:
            // isbigscreen ? {
            //   fontSize:12,
            //   color:'#8fd4ff',
            //   formatter: function (value) {
            //     var valueArr = value ? value.split(/\s+/):[];
            //     var ret = "";//拼接加\n返回的类目项
            //     if (valueArr.length>1) {
            //       for (var i = 0; i < valueArr.length; i++) {
            //           var temp = "";//每次截取的字符串
            //           temp = valueArr[i] + "\n";
            //           ret += temp; //拼接最终的字符串
            //       }
            //       return ret;
            //     }else{
            //       return value
            //     }
            //   }
            // } :
            {
              color:isbigscreen ? "#5CA0D5":this.isdarkSkin == 1 ? '#5CA0D5':'',
              formatter: function (value) {
                var valueArr = value ? value.split(/\s+/):[];
                var ret = "";//拼接加\n返回的类目项
                if (valueArr.length>1) {
                  for (var i = 0; i < valueArr.length; i++) {
                      var temp = "";//每次截取的字符串
                      temp = valueArr[i] + "\n";
                      ret += temp; //拼接最终的字符串
                  }
                  return ret;
                }else{
                  return value
                }
              }
            },
            data: that.time
          }
        ],
        yAxis: [
          {
            position:this.delayYAxis,
            type: "value",
            scale: true,
            min: 0,
            axisLine:{show:isbigscreen ? false : false},
            axisTick:{show:false},
            splitLine: {
              show: true,
              lineStyle: 
              {type: "solid",width: 0.5,color:isbigscreen? "#5CA0D5":this.isdarkSkin == 1 ? '#5CA0D5':'#C0C4CC'}
            },
            axisLabel:
            {
                show:true,
                color:isbigscreen? '#5CA0D5':this.isdarkSkin == 1 ? '#5CA0D5':'',
                formatter: value => {
                  let text = value + (this.delayflag&&!this.flowflag ? 'ms' :''),resualt = '';
                  if (text.length>8){
                    resualt = text.substring(0,7)+'..'
                  }else{
                    resualt = text
                  }
                  return resualt;
                }
              },
            max:this.delayflag&&!this.flowflag ? this.maxDelay * 2 : null,
          },
          {
            position:this.useYAxis,
            type: "value",
            scale: true,
            axisLine:{show:isbigscreen ? false : false},
            axisTick:{show:false},
            splitLine: {
              show: true,
              lineStyle: 
              // isbigscreen ? {
              //   type: "solid",
              //   width: 0.5,
              //   color:'#0e2a5f',
              // } : 
              {type: "solid",width: 0.5,color:isbigscreen ?'#5CA0D5': this.isdarkSkin == 1 ? '#5CA0D5':'#C0C4CC'}
            },
            axisLabel:
            // isbigscreen ? {
            //   fontSize:12,
            //   color:'#8fd4ff',
            //   show:this.useflag ? true :false
            // } :
            {show:this.useflag ? true :false,color:isbigscreen ?'#5CA0D5': this.isdarkSkin == 1 ? '#5CA0D5':''},
            // min: 0,
            // max: 100,
          },
          {
            // name: "单位：个",
            // show:false,
            nameLocation:'end',
            nameTextStyle:{
              color:isbigscreen ? '#5CA0D5' : this.isdarkSkin == 1 ? '#5CA0D5':'#0e2a5f'
            },
            type: "value",
            scale: true,
            min: 0,
            position:this.flowYAxis,
            offset:1,
            axisLine:{show:isbigscreen ? false : false},
            axisTick:{show:false},
            splitLine: {
              show: true,
              lineStyle: 
              // isbigscreen ? {
              //   type: "solid",
              //   width: 0.5,
              //   color:'#0e2a5f',
              // } : 
              // {type: "solid",width: 0.5}
              {type: "solid",width: 0.5,color:isbigscreen ?'#5CA0D5': this.isdarkSkin == 1 ? '#5CA0D5':'#C0C4CC'}

            },
            axisLabel:
            // isbigscreen ? {
            //   fontSize:12,
            //   color:'#8fd4ff',
            //   show:this.flowYshow,
            //   formatter: value => {
            //     let num = value / this.getflownum.toFixed(2);
            //     let unit = !this.delayshow&&this.flowflag ? this.getflowUnit :'';
            //     // return (num+unit);
            //     let resualt = '';
            //     if ((num+unit).length>8){
            //       resualt = (num+unit).substring(0,7)+'..';
            //     }else{
            //       resualt = (num+unit);
            //     }
            //     return resualt;
            //   }
            // } :
            {
                show:this.flowYshow,
                color:isbigscreen?'#5CA0D5':this.isdarkSkin == 1 ? '#5CA0D5':'',
                formatter: value => {
                  let num = value / this.getflownum.toFixed(2);
                  let unit = !this.delayshow&&this.flowflag ? this.getflowUnit :'';
                  // return (!this.delayshow&&this.flowflag?num+this.getflowUnit : num);
                  let resualt = '';
                  if ((num+unit).length>8){
                    resualt = (num+unit).substring(0,7)+'..';
                  }else{
                    resualt = (num+unit);
                  }
                  return resualt;
                }
              },
            // splitLine: {
            //   show: false,
            //   lineStyle: isbigscreen ? {
            //     type: "solid",
            //     width: 0.5,
            //     color:'#0e2a5f',
            //   } : {type: "solid",width: 0.5}
            // },
            // axisLabel:isbigscreen ? {
            //   fontSize:12,
            //   color:'#8fd4ff'
            // } :{},
            // min: 'dataMin'
          }
        ],
        series: series

        // [
        //   {
        //     // name: this.$t('common_degradation'),
        //     type: "line",
        //     yAxisIndex:0,
        //     symbol: "emptyCircle",
        //     symbolSize:.1,
        //     smooth: true,
        //     z: 1,
        //     itemStyle: {
        //       normal: {
        //         lineStyle: {
        //           width: 1,
        //           corlor:"red"
        //         }
        //       }
        //     },
        //     data: that.series1(),
        //     areaStyle: {
        //       color: {
        //         type: "linear",
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: "rgba(78,123,255, 0.9)"
        //           },
        //           {
        //             offset: 0.5,
        //             color: "rgba(78,123,255, 0.4)"
        //           },
        //           {
        //             offset: 1,
        //             color: "rgba(78,123,255, 0.1)"
        //           }
        //         ]
        //       }
        //     }
        //   },
        //   {
        //     // name: this.$t('common_loss_degradation'),
        //     type: "line",
        //     yAxisIndex:2,
        //     z: 2,
        //     smooth: true,
        //     symbol: "emptyCircle",
        //     symbolSize:.1,
        //     data: that.series2(),
        //     itemStyle: {
        //       normal: {
        //         lineStyle: {
        //           width: 1
        //         }
        //       }
        //     },
        //     areaStyle: {
        //       color: {
        //         type: "linear",
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: "rgba(255,162,18, 0.95)"
        //           },
        //           {
        //             offset: 0.3,
        //             color: "rgba(255,162,18, 0.3)"
        //           },
        //           {
        //             offset: 0.9,
        //             color: "rgba(255,162,18, 0.1)"
        //           }
        //         ]
        //       }
        //     }
        //   },
        //   {
        //     name: this.$t('dash_interrupt'),
        //     type: "line",
        //     yAxisIndex:2,
        //     z: 3,
        //     smooth: true,
        //     symbol: "emptyCircle",
        //     symbolSize:.1,
        //     data: that.series3(),
        //     itemStyle: {
        //       normal: {
        //         lineStyle: {
        //           width: 1
        //         }
        //       }
        //     },
        //     areaStyle: {
        //       color: {
        //         type: "linear",
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: "rgba(255,32,77, .95)"
        //           },
        //           {
        //             offset: 0.5,
        //             color: "rgba(255,32,77, 0.3)"
        //           },
        //           {
        //             offset: .9,
        //             color: "rgba(255,32,77, 0.1)"
        //           }
        //         ]
        //       }
        //     }
        //   },

        // ]
      });
      // window.addEventListener("resize", () => {
      //   if (that.myChart1 != null) {
      //     that.myChart1.resize();
      //   }
      // });
    },
    xAxis() {
      let time = [];
      this.lineData.data.forEach(e => {
        time.push(e.date);
      });
      return time;
    },
    // 时延劣化
    series1() {
      let arry = [];
      this.lineData.data.forEach(e => {
        arry.push(Number(e.degrCount));
      });
      return arry;
    },
    // 丢包劣化
    series2() {
      let arry = [];
      this.lineData.data.forEach(e => {
        arry.push(Number(e.lossDegrCount));
      });
      return arry;
    },
    // 中断
    series3() {
      let arry = [];
      this.lineData.data.forEach(e => {
        arry.push(Number(e.interruptCount));
      });
      return arry;
    }
  },
  beforeDestroy() {
    this.myChart1.clear();
    echarts.dispose(this.myChart1);
    console.log('beforeDestroy');
  }
};
</script>

<style scoped>
.brokenLine {
  height: 100%;
  /*min-height: 160px;*/
}
.specTbaleBox {
  width: 84%;
  margin: 0 auto;
}
.biscreen.specTbaleBox {
  color: #1a80c0;
}
</style>
