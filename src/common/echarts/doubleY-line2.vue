<template>
  <section style="width: 100%;height: 100%">
    <div :id="node" style="width: 100%; height:100%"></div>
  </section>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
  require('echarts/lib/component/dataZoom')
import eConfig from "@/config/echart.config.js";
export default {
  name: "doubleY-line",
  props: {
    node: {
      type: String,
      default: "line2"
    },
    lineData: {
      type: Object,
      default: function() {
        return {
          title: "",
          legend: [],
          data: []
        };
      }
    },
    showLegend: {
      type: Boolean
    }
  },
  data() {
    return {
      color_obj: {
        时延: ["#4e7bff", "78,123,255"],
        丢包率: ["#ffa212", "255,162,18"]
      },
      name_obj: {
        时延: "ms",
        丢包率: "%"
      },
      color: [],
      colorRgb: [],
      line_data: {}
    };
  },
  watch: {
    lineData: {
      handler(val) {
        if (!this.myChart) {
          return;
        }
        this.myChart.dispose();
        this.myChart = null;
        this.line_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    showLegend: {
      handler(val) {
        this.showLegend = val;
      },
      deep: true
    },
    node: {
      handler(val) {
        this.node = val;
      },
      deep: true
    }
  },
  methods: {
    init() {
      let _self = this;
      _self.myChart = echarts.init(document.getElementById(_self.node));
      let option = Object.assign(
        {},
        {
          color: _self.setColor(),
          title: _self.setTitle(),
          tooltip: _self.setTooltip(),
          legend: _self.showLegend ? [] : _self.legend(),
          grid: _self.grid(),
          xAxis: _self.xAxis(),
          yAxis: _self.yAxis(),
          dataZoom: _self.setDataZoom(),
          series: _self.series()
        }
      );
      if (option && typeof option === "object") {
        _self.myChart.setOption(option, true);
        window.onresize = function() {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        };
      }
    },
    setColor() {
      let _self = this,
        legend = _self.line_data.legend;
      for (let i = 0, len = legend.length; i < len; i++) {
        _self.color.push(_self.color_obj[legend[i]][0]);
        _self.colorRgb.push(_self.color_obj[legend[i]][1]);
      }
      return _self.color;
    },
    setTitle() {
      return {
        text: this.line_data.title,
        left: "center",
        align: "right",
        textStyle: {
          fontSize: this.showLegend ? "13px" : ""
        }
      };
    },
    setTooltip() {
      let _self = this;
      return eConfig.tip("axis", function(param) {
        let src = "";
        src += param[0].data[0] + "<br />";
        for (let i = 0, len = param.length; i < len; i++) {
          src +=
            '<span class="tooltip-round" style="background-color:' +
            param[i].color +
            '"></span>';
          src +=
            param[i].seriesName +
            "：" +
            (param[i].value[1] === undefined ||
            param[i].value[1] === null ||
            param[i].value[1] === ""
              ? "--"
              : param[i].value[1]) +
            _self.name_obj[param[i].seriesName] +
            "<br />";
        }
        return src;
      });
    },
    legend() {
      let obj = {};
      obj.right = "4%";
      obj.icon = "circle";
      obj.data = this.line_data.legend;
      obj.textStyle = {
        color: "#484b56",
        fontSize: 14,
        fontFamily: "MicrosoftYaHei-Bold"
      };
      return obj;
    },
    grid() {
      return {
        top: "15%",
        left: "3%",
        right: "3%",
        bottom: "10%",
        containLabel: true
      };
    },
    xAxis() {
      return {
        type: "time",
        boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: "#676f82", //左边线的颜色
            width: "1" //坐标线的宽度
          }
        },
        nameTextStyle: {
          color: "#676f82",
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#e3e7f2"
          }
        },
        axisLabel: {
          show: true,
          margin: 20,
          textStyle: {
            color: "#676f82",
            fontSize: 12
          }
        }
      };
    },
    yAxis() {
      let _self = this,
        legend = this.line_data.legend,
        array = [];
      for (let i = 0, len = legend.length; i < len; i++) {
        let obj = {
          nameTextStyle: {
            color: "#676f82",
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: "#676f82",
              width: "1" //坐标线的宽度
            }
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#e3e7f2"
            }
          },
          axisLabel: {
            show: true,
            margin: 20,
            textStyle: {
              color: "#676f82",
              fontSize: 12
            }
          },
          axisTick: {
            show: false
          }
        };
        obj.name = legend[i] + _self.name_obj[legend[i]];
        obj.type = "value";
        array.push(obj);
      }
      return array;
    },
    setDataZoom() {
      return [
        {
          show: true,
          realtime: true
        },
        {
          type: "inside",
          realtime: true
        }
      ];
    },
    series() {
      let array = [],
        legend = this.line_data.legend,
        data = this.line_data.data;
      for (let i = 0, len = legend.length; i < len; i++) {
        let obj = {
          name: legend[i],
          type: "line",
          lineStyle: {
            width: 1
          },
          // connectNulls: true,
          data: data[i],
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(" + this.colorRgb[i] + ", 0.25)"
                },
                {
                  offset: 0.9,
                  color: "rgba(" + this.colorRgb[i] + ", 0)"
                }
              ]
            }
          }
        };
        if (i > 0) {
          obj.yAxisIndex = i;
        }
        array.push(obj);
      }
      return array;
    }
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.dispose();
    // this.myChart.clear();
    this.myChart = null;
  },
  mounted() {
    this.line_data = Object.assign({}, this.lineData);
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
