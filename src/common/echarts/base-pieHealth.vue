<!--圆饼图组件
*@props        @node        {String}          节点
               @pieData     {Object}          圆饼数据对象
-->

<template>
  <section style="width: 100%; height: 100%" ref="pie">
    <div :id="node" style="width: 100%; height: 100%"></div>
  </section>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/pie')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
import eConfig from "@/config/echart.config.js";
export default {
  name: "base-pie",
  props: {
    node: {
      type: String,
      default: "pie"
    },
    pieData: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    },
    isdarkSkin:{
      type: [Number, String],
      default: 0
    },
  },
  data() {
    return {
      pie_data: {},
      sizeWH: 0
    };
  },
  watch: {
    pieData: {
      handler(val) {
        this.pie_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    isdarkSkin:{
      handler(val) {console.log(val)
        this.init();
      },
      deep: true
    }
  },
  methods: {
    init() {
      let _self = this;
      _self.myChart = echarts.init(document.getElementById(_self.node));
      this.sizeWH = (document.getElementById(_self.node).offsetHeight - 28) / 2;
      let option = Object.assign(
        {},
        {
          color: _self.setColor(),
          title: _self.title(),
          series: _self.series()
        }
      );
      console.log(option);
      if (option && typeof option === "object") {
        _self.myChart.clear();
        _self.myChart.setOption(option, true);
        window.addEventListener("resize", () => {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        });
      }
    },
    setColor() {
      let newisdarkSkin = this.isdarkSkin;
      let newnode = this.node;
      // debugger
      return this.node == "nodePairAnalysisLinkPie"
        ? ["#4e7bff", "#ffa212", "#FED92E", "#fb204d"] :
        this.isdarkSkin == 1 ? ["#07c5a3", "#feae3c", "#ff4d4f"]: ["#04B999", "#FEA31B", "#FE5C5C"];
    },
    title() {
      let _self = this;
      return {
        text: _self.pie_data.totalValue,
        subtext: _self.pie_data.title,
        x: "center",
        y: "40%",
        textStyle: {
          // color: this.isdarkSkin == 1 ? "#d1e4ff":"#0290FD",
          color: this.isdarkSkin == 1 ? "#ffffff":"#17233D",
          fontSize: 24,
          fontFamily: "MicrosoftYaHei",
          fontWeight: "bold"
        },
        subtextStyle: {
          color: this.isdarkSkin == 1 ? "#465b7a":"#515A6E",
          fontSize: 12,
          fontFamily: "MicrosoftYaHei",
        }
      };
    },
    series() {
      let _self = this,
        data = _self.pie_data.data,
        array = [];
      for (let i = 0, len = data.length; i < len; i++) {
        array.push({
          type: "pie",
          silent: true,
          radius: [this.sizeWH - i * 15, this.sizeWH + 5 - i * 15],
          label: {
            show: false
          },
          data: [
            {
              value: data[i].value,
              name: data[i].name
            },
            {
              value:
                parseFloat(_self.pie_data.totalValue) === 0
                  ? 10
                  : (parseFloat(_self.pie_data.totalValue) -parseFloat(data[i].value))>0?parseFloat(_self.pie_data.totalValue) -parseFloat(data[i].value):0,
              name: "",
              itemStyle: this.isdarkSkin == 1 ?{color:'#032A4D'}:eConfig.placeHolderStyle
            }
          ]
        });
      }
      return array;
    }
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.dispose();
    this.myChart = null;
    window.removeEventListener("resize");
  },
  mounted() {
    this.pie_data = Object.assign({}, this.pieData);
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
