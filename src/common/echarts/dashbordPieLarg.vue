<template>
  <div style="position: relative; height: 100%; overflow: hidden">
    <div class="cake" :id="node" v-if="echartShow"></div>
    
    <div
      v-if="echartShow"
      class="cake"
      :style="{ position: 'absolute', width: '100%', left: '0', top: '0' }"
    >
     
      <div
        class="legend"
        
       
      >
      
          <div
            class="zc"
            :style="
              'color:' + (isbigscreen ? '#fff' : isdarkSkin == 1 ? '#fff' : '#0e2a5f')
            "
            @click.stop="goPath(datas.data[1].link, 0)"
          >
            {{ $t("common_Normal") }}:{{ datas.data[0].value }}
            <span style="color: #5ca0d5; margin-left: 10px">
              {{ datas.data[0].percent + "%" }}</span
            >
          </div>
        <!-- </Tooltip>
        <Tooltip transfer :content="$t('probetask_deterioration_tip')" max-width="200" style="width: auto;white-space: normal;"> -->
          <div
            class="lh"
            :style="
              'color:' + (isbigscreen ? '#fff' : isdarkSkin == 1 ? '#fff' : '#1A80C0')
            "
            @click.stop="goPath(datas.data[1].link, 2)"
          >
            {{ $t("phytopo_deterioration") }}:{{ datas.data[1].value }}
            <span style="color: #5ca0d5; margin-left: 10px">{{
              datas.data[1].percent + "%"
            }}</span>
          </div>
        <!-- </Tooltip>
        <Tooltip transfer :content="$t('probetask_interrupt_tip')" max-width="200" style="width: auto;white-space: normal;"> -->
          <div
            class="zd"
            :style="
              'white-space: normal;color:' + (isbigscreen ? '#fff' : isdarkSkin == 1 ? '#fff' : '#1A80C0')
            "
            @click.stop="goPath(datas.data[2].link, 1)"
          >
            {{ $t("comm_interruption") }}:{{ datas.data[2].value }}
            <span style="color: #5ca0d5; margin-left: 10px">{{
              datas.data[2].percent + "%"
            }}</span>
          </div>
        <!-- </Tooltip>
        <Tooltip ip transfer :content="$t('probetask_suspend_tip')" max-width="200" style="width: auto;white-space: normal;"> -->
          <div
            class="zt"
            :style="
              'white-space: normal;color:' + (isbigscreen ? '#fff' : isdarkSkin == 1 ? '#fff' : '#1A80C0')
            "
            @click.stop="goPath(datas.data[3].link, 1)"
          >
            {{ $t("dash_pause") }}:{{ datas.data[3].value }}
            <span style="color: #5ca0d5; margin-left: 10px">{{
              datas.data[3].percent + "%"
            }}</span>
          </div>
        <!-- </Tooltip> -->
      </div>
    </div>
    <div v-if="!echartShow" style="width: 100%; height: 100%; display: table">
      <span style="display: table-cell; vertical-align: middle; width: 100%">{{
        $t("common_No_data")
      }}</span>
    </div>
  </div>
</template>

<script>
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/pie");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
//import "echarts-gl";

import eConfig from "@/config/echart.config.js";
export default {
  props: {
    node: {
      type: String,
      default: "pie",
    },
    isbigscreen: {
      type: Boolean,
      default: false,
    },
    isdarkSkin: {
      type: Number,
      default: 0,
    },
    height: {
      type: Number,
      default: 260,
    },
    styleProp: {
      type: Object,
      default: function () {
        return {
          data: [],
        };
      },
    },
    r: {
      type: Number,
      default: 75,
    },
    datas: {
      type: Object,
      default: function () {
        return {
          data: [],
        };
      },
    },
    time: {
      type: Object,
      default: function () {
        return {
          startTime: new Date().format2("yyyy-MM-dd 00:00:00"),
          endTime: new Date().format2("yyyy-MM-dd 23:59:59"),
        };
      },
    },
  },
  data() {
    return {
      minSize: 0,
      echartShow: true,
      
    };
  },
  watch: {
    height: {
      handler(val) {
        this.height = val;
      },
      deep: true,
    },
    styleProp: {
      handler(val) {
        // console.log(val)
      },
      deep: true,
    },
    isdarkSkin: {
      handler(val) {
        console.log(val);
        this.drawPie(this.minSize / 2);
      },
      deep: true,
    },
  },
  mounted() {
    this.minSize = Math.min(this.styleProp.width, this.styleProp.height);
    if (this.datas.data.length > 0) {
      this.echartShow = true;
    } else if (this.datas.data.length < 1) {
      this.echartShow = false;
    }
    if (this.echartShow === true) {
      // this.drawPie(this.minSize*0.97/3);
      this.drawPie(this.minSize / 2);
    }
  },
  methods: {
    // 绘制环形图
    drawPie(minSize) {
      let that = this;
      that.myChart = echarts.init(document.getElementById(that.node));
      if (that.myChart === undefined) {
        that.myChart = echarts.init(document.getElementById(that.node));
      }
      that.myChart.clear();
      var color = this.isbigscreen
        ? ["#00FFEE", "#feae3c", "#ff4d4f", "#4EAED2"]
        : this.isdarkSkin == 1
        ? ["#00FFEE", "#feae3c", "#ff4d4f", "#4EAED2"]
        : ["#33CC33", "#ffa212", "#fb204d", "#4EAED2"];
      that.myChart.setOption({
        color: color,
        tooltip: {
          trigger: "item",
          show: true,
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            right: "25%",
            avoidLabelOverlap: true,
             label: {
            normal: {
                show: true,
                position: 'center',
                color:'#4c4a4a',
                formatter: '{total|' + this.datas.totalValue +'}'+ '\n\r' + '{active|' + this.datas.title + '}',
                rich: {
                    total:{
                        fontSize: 30,
                        fontFamily : "微软雅黑",
                        color:'#fff'
                    },
                    active: {
                        fontFamily : "微软雅黑",
                        fontSize: 14,
                        color:'#5CA0D5',
                        lineHeight:20,
                    },
                }
            },
            emphasis: {//中间文字显示
                show: true,
            }
        },
        lableLine: {
            normal: {
                show: false
            },
            emphasis: {
                show: true
            },
            tooltip: {
                show: false
            }
        },
           
            // emphasis: {
            //   label: {
            //     show: true,
            //     fontSize: "40",
            //     fontWeight: "bold",
            //   },
            // },
            // label: {
            //   show: false,
            //   position: "center",
            // },
            // labelLine: {
            //   show: false,
            //   position: "center",
            // },
            data: this.seriesData(),
          },
        ],
      });
    },
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      const midRatio = (startRatio + endRatio) / 2;
      const startRadian = startRatio * Math.PI * 2;
      const endRadian = endRatio * Math.PI * 2;
      const midRadian = midRatio * Math.PI * 2;
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }
      k = typeof k !== "undefined" ? k : 1 / 3;
      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      // 鼠标滑过时外环放大大小
      const hoverRate = isHovered ? 1.05 : 1;
      // 返回曲面参数方程
      return {
        u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },
        v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },

        x(u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        y(u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },

        z(u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1;
          }
          // 当前图形的高度是Z根据h（每个value的值决定的）
          let hight =Math.sin(v) > 0 ? 1 * h * 0.1 : -1
           return  hight;
        },
      };
    },
    getPie3D(pieData, internalDiameterRatio) {
      const series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      const legendData = [];
      const k =
        typeof internalDiameterRatio !== "undefined"
          ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
          : 1 / 3;
      for (let i = 0; i < pieData.length; i += 1) {
        sumValue += pieData[i].value;
        const seriesItem = {
          name: typeof pieData[i].name === "undefined" ? `series${i}` : pieData[i].name,
          type: "surface",
          parametric: true,
          wireframe: { show: false },
          pieData: pieData[i],
          pieStatus: { selected: false, hovered: false, k },
        };
        if (typeof pieData[i].itemStyle !== "undefined") {
          const { itemStyle } = pieData[i];
          // eslint-disable-next-line no-unused-expressions
          typeof pieData[i].itemStyle.color !== "undefined"
            ? (itemStyle.color = pieData[i].itemStyle.color)
            : null;
          // eslint-disable-next-line no-unused-expressions
          typeof pieData[i].itemStyle.opacity !== "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;
          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }
      // 计算每个区域的高度
      for (let i = 0; i < series.length; i += 1) {
        endValue = startValue + series[i].pieData.value;
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          true,
          k,
          (20/sumValue)*series[i].pieData.value   // 在此处传入饼图初始高度
        );
        startValue = endValue;
        legendData.push(series[i].name);
      }
      // 准备待返回的配置项，把准备好的series 传入。
      var color = this.isbigscreen
        ? ["#00FFEE", "#feae3c", "#ff4d4f", "#4EAED2"]
        : this.isdarkSkin == 1
        ? ["#00FFEE", "#feae3c", "#ff4d4f", "#4EAED2"]
        : ["#33CC33", "#ffa212", "#fb204d", "#4EAED2"];
      const option = {
        title: {
          top: "1%",
          textAlign: "left",
          left: "1%",
          textStyle: {
            color: "#38adb9",
            fontSize: 32,
            fontWeight: "600",
          },
        },
        legend: {
          show: false,
          type: "scroll",
          right: 10,
          top: 10,
          orient: "vertical", // 纵向
          icon: "circle", // icon 类型
          itemHeight: 12, // icon高度
          itemWidth: 12, // icon 宽度
          itemGap: 5, // 图例间隔
          textStyle: {
            color: "#709DD9",
            fontSize: 12,
            fontWeight: "400",
          },
          formatter: (name) => {
            if (pieData.length) {
              const item = pieData.filter((item) => item.name === name)[0];
              return `  ${name}：${item.value}`;
            }
          },
        },
        color: color,
        tooltip: {
          formatter: (params) => {
            if (params.seriesName !== "mouseoutSeries") {
              return `${params.marker}${params.seriesName}：${
                pieData[params.seriesIndex].value
              }`;
            }
            return "";
          },
        },
        xAxis3D: {  },
        yAxis3D: {  },
        zAxis3D: {  },
        grid3D: {
          show: false,
          boxHeight: 10, // 修改立体饼图的高度
          // top: "-5%",
          // left: "-13%",
          viewControl: {
            projection:'orthographic',
            // 3d效果可以放大、旋转等，
            alpha: 42, // 饼图翻转的程度
            beta: 30,
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            panSensitivity: 0,
            autoRotate: false, // 是否自动旋转
            distance: 190, // 距离越小看到的饼图越大
            boxWidth: 130,
            boxDepth: 130,
          },
          light:{
            main: {
              shadow:true
            }
          }
        },
        series,
      };
      return option;
    },
    drawPie1() {
      let that = this;
      var chartDom = document.getElementById(that.node);
      var myChart = echarts.init(chartDom);

      myChart.dispatchAction({
        type: "setCamera",
        position: [0, 0, 100], // 将相机移动到适当的距离，这里的数值可以根据实际大小调整
        up: [0, 1, 0], // 相机向上方向为Y轴正方向
        center: [0, 0, 0], // 相机看向场景中心
        orthographic: true,
      });
      myChart.setOption(this.getPie3D(this.seriesData(), 0.7));
    },
    seriesData() {
      let array = [];
      this.datas.data.forEach((item) => {
        array.push({ value: item.value, name: item.name });
      });
      return array;
    },
    //跳转
    goPath(link, state) {
      // debugger
      // console.log(link);
      // 正常跳转
      //  if(state === 0) {
      //    top.document.getElementById('sub-content-page').src = (window.location.hostname==='localhost' ? '/anpm-plug-'+link+'-ui.html': '/'+link);
      //    return

      //  }
      const localstorageMenus = JSON.parse(sessionStorage.getItem("accessToken")).funcs;
      if (localstorageMenus && !this.isbigscreen) {
        let toPathMenu = localstorageMenus.filter(
          (item) => item.routeUrl == "/" + link
        )[0];
        // console.log(toPathMenu);
        let activeParam = {
          parentactiveId: toPathMenu.parentFnId,
          activeId: toPathMenu.fnId,
          parentFunctionName:
            link === "pathquality"
              ? "拨测分析"
              : link === "specquality"
              ? "专线管理"
              : link === "rpquality"
              ? "中继分析"
              : "",
          navName: toPathMenu.fnName,
          isdarkSkin: top.window.isdarkSkin,
        };
        top.window.vm.$data.preMenu = activeParam.parentFunctionName;
        top.window.vm.$data.activeName = activeParam.navName;
        top.window.vm.$data.parentactiveId = activeParam.parentactiveId;
        top.window.vm.$data.activeId = activeParam.activeId;
        window.parent.postMessage(activeParam, window.location.origin); //window.postMessage() 方法可以安全地实现跨源通信
        let menu = {
          activeId: activeParam.activeId,
          functionUrl: "/" + link,
          navName: activeParam.navName,
          node1: "",
          parentFunctionName: activeParam.parentFunctionName,
          parentactiveId: activeParam.parentactiveId,
          subMenuName: "",
        };
        sessionStorage.setItem("menu", JSON.stringify(menu));

        top.document.getElementById("sub-content-page").src =
          window.location.hostname === "localhost"
            ? "/anpm-plug-" + link + "-ui.html"
            : "/" + link;
        //  top.document.getElementById('sub-content-page').src = (window.location.hostname==='localhost' ? '/anpm-plug-'+link+'-ui.html?start='+this.time.startTime+'&end='+this.time.endTime+'&type=2&screening='+state : '/'+link+'?start='+this.time.startTime+'&end='+this.time.endTime+'&type=2&screening='+state);
      }
    },
  },
  beforeDestroy() {
    this.myChart.clear();
    echarts.dispose(this.myChart);
    this.myChart = void 0;
  },
};
</script>

<style scoped>
.cake,
.brokenLine {
  /*min-height: 160px;*/
  height: 100%;
}
.cake .piePCont {
  position: absolute;
  line-height: 24px;
  width: 120px;
  height: 24px;
}
.cake .piePCont.normal {
  color: #00ffee;
  top: -75px;
  left: 115px;
}
.cake .piePCont.normal:before {
  content: "";
  width: 85px;
  height: 0;
  display: block;
  position: absolute;
  left: -75px;
  border-top: 1px solid #00ffee;
  transform: translate(0px, 10px) rotate(0deg);
}
.cake .piePCont.degradation {
  color: #feae3c;
  top: 75px;
  left: 115px;
}
.cake .piePCont.degradation:before {
  content: "";
  width: 140px;
  height: 0;
  display: block;
  position: absolute;
  left: -85px;
  border-top: 1px solid #feae3c;
  -webkit-transform: translate(15px, 10px) rotate(0deg);
  transform: translate(0px, -55px) rotate(50deg);
}
.cake .piePCont.break {
  color: #ff4d4f;
  top: -75px;
  right: 115px;
  text-align: right;
}
.cake .piePCont.break:before {
  content: "";
  width: 110px;
  height: 0;
  display: block;
  position: absolute;
  right: -120px;
  border-top: 1px solid #ff4d4f;
  -webkit-transform: translate(15px, 10px) rotate(0deg);
  transform: translate(0px, 20px) rotate(10deg);
}
/* .legend>div{line-height: 30px;text-align: left;cursor: default;} */
/* .zc,.zd,.zt,.lh{
    line-height: 30px;
    text-align: left;

  } */
.legend {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
  position: absolute;
  top: 400px;
  left: 60%;
}
/* .legend>div:before{
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  vertical-align: middle;
  margin-right: 6px;
  margin-top: -2px;
  cursor: pointer;
} */
.legend div.zc:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  vertical-align: middle;
  margin-right: 6px;
  margin-top: -2px;
  cursor: pointer;
  background: #00ffee;
  border: 1px solid #00ffee;
}
.legend div.lh:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  vertical-align: middle;
  margin-right: 6px;
  margin-top: -2px;
  cursor: pointer;
  background: #feae3c;
  border: 1px solid #feae3c;
}
.legend div.zd:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  vertical-align: middle;
  margin-right: 6px;
  margin-top: -2px;
  cursor: pointer;
  background: #ff4d4f;
  border: 1px solid #ff4d4f;
}
.legend div.zt:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  vertical-align: middle;
  margin-right: 6px;
  margin-top: -2px;
  cursor: pointer;
  background: #4eaed2;
  border: 1px solid #4eaed2;
}
.legend div.zc {
  color: #617ca5;
  height: 30px;
  line-height: 30px;
}
.legend div.lh,
.legend div.zd {
  cursor: pointer;
  color: #d1e4ff;
  height: 30px;
  line-height: 30px;
}
.legend div.zc {
  cursor: pointer;
  height: 30px;
  line-height: 30px;
}
.legend div.zt {
  cursor: pointer;
}

.legend.bigscreen > div:before {
  content: "";
  display: inline-block;
  width: 26px;
  height: 8px;
  border-radius: 4px;
  vertical-align: middle;
  margin-right: 8px;
  margin-top: -2px;
}
.legend.bigscreen > div.zc:before {
  background: #1252c8;
  border: 1px solid #1252c8;
}
.legend.bigscreen > div.lh:before {
  background: #ed921c;
  border: 1px solid #ed921c;
}
.legend.bigscreen > div.zd:before {
  background: #f01c1c;
  border: 1px solid #f01c1c;
}
.legend.bigscreen {
  color: #1a80c0;
}
.legend.bigscreen > div.zc,
.legend > div.zt {
  cursor: pointer;
  /* color: #57a3f3; */
}
.legend.bigscreen > div.lh,
.legend > div.zd {
  cursor: pointer;
  /* color: #57a3f3; */
}
.flexBox {
  display: flex;
  flex-direction: column;
  align-content: center;
}
.flexBox p {
  text-align: center;
  -webkit-flex: 1;
  flex: 1;
}
 /deep/.ivu-tooltip-inner {
           max-width: unset !important;

           
        }
    
</style>
