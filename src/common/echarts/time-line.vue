<template>
  <section style="width: 100%;height: 100%" ref="timeEcharts">
    <div :id="node" style="width: 100%; height:100%"></div>
  </section>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
  require('echarts/lib/component/dataZoom')
import eConfig from "@/config/echart.config.js";
export default {
  name: "time-line",
  props: {
    node: {
      type: String,
      default: "line"
    },
    buttonType: {
      type: [String, Number],
      default: 1
    },
    defaultTime: {
      type: String,
      default: ""
    },
    zoomType: {
      type: Boolean,
      default: true
    },
    lineData: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    }
  },
  data() {
    return {
      color_obj: {
        1: ["#4e7bff", "78,123,255"],
        2: ["#ffa212", "255,162,18"]
      },
      name_obj: {
        1: ["延迟", "ms"],
        2: ["丢包率", "%"]
      },
      color: [],
      colorRgb: [],
      line_data: {},
      imgIcon: "iconTriangle",
      imgUrl: require("../../assets/wisdom/triangle.png")
    };
  },
  watch: {
    lineData: {
      handler(val) {
        if (!this.myChart) {
          return;
        }
        this.myChart.dispose();
        this.myChart = null;
        this.line_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    },
    buttonType: {
      handler() {
        if (!this.myChart) {
          return;
        }
        this.myChart.dispose();
        this.myChart = null;
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    }
  },
  methods: {
    init() {
      let _self = this;
      _self.myChart = echarts.init(document.getElementById(_self.node));
      let option = Object.assign(
        {},
        {
          color: _self.setColor(),
          tooltip: _self.setTooltip(),
          grid: _self.grid(),
          xAxis: _self.xAxis(),
          yAxis: _self.yAxis(),
          dataZoom: _self.setDataZoom(),
          series: _self.series()
        }
      );
      if (option && typeof option === "object") {
        _self.myChart.clear();
        _self.myChart.setOption(option, true);
        window.onresize = function() {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        };
      }
    },
    setColor() {
      let _self = this;
      _self.color = _self.color_obj[_self.buttonType][0];
      _self.colorRgb = _self.color_obj[_self.buttonType][1];
      return _self.color;
    },
    setTooltip() {
      let _self = this;
      return eConfig.tip("axis", function(param) {
        let src = "";
        src +=
          '<span class="tooltip-round" style="background-color:' +
          param[0].color +
          '"></span>';
        src += param[0].data[0] + "<br />";
        src +=
          _self.name_obj[_self.buttonType][0] +
          "：" +
          (param[0].data[1] === undefined ||
          param[0].data[1] === null ||
          param[0].data[1] === ""
            ? "--"
            : param[0].data[1]) +
          _self.name_obj[_self.buttonType][1];
        return src;
      });
    },
    grid() {
      if (this.zoomType) {
        return {
          top: "10%",
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true
        };
      } else {
        return {
          top: "10%",
          left: "1%",
          right: "1.5%",
          bottom: "3%",
          containLabel: true
        };
      }
    },
    xAxis() {
      let _self = this,
        obj = eConfig.axis(12, true, false, "#676f82", "#e3e7f2");
      if (_self.defaultTime != "") {
        obj.type = "time";
        // obj.type='category';
        obj.axisLabel.formatter = function(param) {
          let time = new Date(param).format("yyyy-MM-dd HH:mm:ss"),
            text = "";
          if (_self.defaultTime === time) {
            text = `{${_self.imgIcon}|}`;
          } else {
            text =
              new Date(param).format("HH:mm") +
              "\n" +
              new Date(param).format("MM-dd");
          }
          return text;
        };
        obj.axisLabel.rich = {
          iconTriangle: {
            height: 14,
            align: "center",
            backgroundColor: {
              image: _self.imgUrl
            }
          }
        };
      } else {
        obj.type = "time";
      }
      return obj;
    },
    yAxis() {
      let _self = this,
        obj = eConfig.axis(12, false, true, "#676f82", "#e3e7f2");
      obj.type = "value";
      obj.name =
        "平均" +
        _self.name_obj[_self.buttonType][0] +
        "（" +
        _self.name_obj[_self.buttonType][1] +
        "）";
      return obj;
    },
    setDataZoom() {
      if (this.zoomType) {
        return {
          show: true,
          start: 0,
          end: 30
        };
      }
    },
    series() {
      let array = [],
        data = this.line_data.data;
      let obj = {
        type: "line"
      };
      obj.data = data;
      obj.smooth = true;
      obj.areaStyle = {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(" + this.colorRgb + ", 0.25)"
            },
            {
              offset: 0.9,
              color: "rgba(" + this.colorRgb + ", 0)"
            }
          ]
        }
      };
      array.push(obj);
      return array;
    }
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.dispose();
    this.myChart = null;
  },
  mounted() {
    this.line_data = Object.assign({}, this.lineData);
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>
