<template>
  <div>
    <div class="brokenLine" :id="node" :style="{ height: height + 'px' }"></div>
  </div>
</template>

<script>
  let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
import eConfig from "@/config/echart.config.js";
export default {
  props: {
    node: {
      type: String,
      default: "line"
    },
    height: {
      type: Number,
      default: 260
    },
    lineData: {
      type: Object,
      default: function() {
        return {
          data: []
        };
      }
    }
  },
  data() {
    return {};
  },
  watch: {
    height: {
      handler(val) {
        this.height = val;
      },
      deep: true
    }
  },
  mounted() {
    this.ceshi();
  },
  methods: {
    ceshi() {
      let that = this;
      that.myChart1 = echarts.init(document.getElementById(that.node));
      that.myChart1.setOption({
        color: ["#fb204d", "#ffa212", "#4e7bff"],
        title: {
          text: "最近30天" + that.lineData.title + "告警走势",
          top: "3%"
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
            snap: true,
            shadowStyle: {
              color: "rgba(238,240,246,0)",
              width: 30
            }
          },
          textStyle: {
            align: "left"
          }
          // formatter:'{b}:{c}\n\n'
        },
        legend: {
          top: "10%",
          right: "7%",
          icon: "circle",
          textStyle: {
            color: "#484b56",
            fontSize: 14,
            fontFamily: "MicrosoftYaHei-Bold"
          },
          data: [this.$t('common_degradation'), this.$t('common_loss_degradation'), this.$t('dash_interrupt')]
        },
        grid: {
          top: "25%",
          left: "5%",
          right: "7%",
          bottom: "12%",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: that.xAxis()
          }
        ],
        yAxis: [
          {
            name: this.$t('comm_unit_one'),
            type: "value",
            scale: true,
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                width: 0.5
              }
            }
            // min: 'dataMin'
          }
        ],
        series: [
          {
            name: this.$t('dash_interrupt'),
            type: "line",
            stack: this.$t('comm_gross_amount'),
            z: 1,
            smooth: true,
            symbol: "circle",
            data: that.series3(),
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 0.5
                }
              }
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255,32,77, 0.8)"
                  },
                  {
                    offset: 0.8,
                    color: "rgba(255,32,77, 0.1)"
                  }
                ]
              }
            }
          },
          {
            name: this.$t('common_loss_degradation'),
            type: "line",
            stack: "总量",
            z: 2,
            smooth: true,
            symbol: "circle",
            data: that.series2(),
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 0.5
                }
              }
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255,162,18, 0.8)"
                  },
                  {
                    offset: 0.8,
                    color: "rgba(255,162,18, 0.1)"
                  }
                ]
              }
            }
          },
          {
            name: this.$t('common_degradation'),
            type: "line",
            stack: this.$t('comm_gross_amount'),
            symbol: "circle",
            smooth: true,
            z: 3,
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 0.5
                }
              }
            },
            data: that.series1(),
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(78,123,255, 0.8)"
                  },
                  {
                    offset: 0.8,
                    color: "rgba(78,123,255, 0.1)"
                  }
                ]
              }
            }
          }
        ]
      });
      window.addEventListener("resize", () => {
        if (that.myChart1 != null) {
          that.myChart1.resize();
        }
      });
    },
    xAxis() {
      let time = [];
      this.lineData.data.forEach(e => {
        time.push(e.date);
      });
      return time;
    },
    // 时延劣化
    series1() {
      let arry = [];
      this.lineData.data.forEach(e => {
        arry.push(Number(e.degrCount));
      });
      return arry;
    },
    // 丢包劣化
    series2() {
      let arry = [];
      this.lineData.data.forEach(e => {
        arry.push(Number(e.lossDegrCount));
      });
      return arry;
    },
    // 中断
    series3() {
      let arry = [];
      this.lineData.data.forEach(e => {
        arry.push(Number(e.interruptCount));
      });
      return arry;
    }
  },
  beforeDestory() {
    window.removeEventListener("resize");

  }
  
};
</script>

<style scoped>
.brokenLine {
  height: 260px;
  border-bottom: 1px solid #e7e7e7;
}
</style>
