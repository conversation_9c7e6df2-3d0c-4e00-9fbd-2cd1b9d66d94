<template>
  <section style="width: 100%; height: 100%" ref="line">
    <div
      :id="node"
      style="height: 100%"
      :style="{ width: line_width + 'px' }"
    ></div>
  </section>
</template>

<script>
import echarts from "echarts";
export default {
  name: "operatorRanking-ecahrt",
  props: {
    node: {
      type: String,
      default: "line"
    },
    lineData: {
      type: Object,
      default: function() {}
    }
  },
  data() {
    return {
      line_width: ""
    };
  },
  methods: {
    init() {
      let _self = this;
      if (_self.myChart) {
        _self.myChart.clear();
      }
      _self.myChart = echarts.init(document.getElementById(_self.node));
      let option = Object.assign(
        {},
        {
          grid: _self.grid(),
          xAxis: _self.xAxis(),
          yAxis: _self.yAxis(),
          series: _self.series()
        }
      );
      if (option && typeof option === "object") {
        _self.myChart.clear();
        _self.myChart.setOption(option, true);
        window.addEventListener("resize", () => {
          if (_self.myChart != null) {
            _self.myChart.resize();
          }
        });
      }
    },
    grid() {
      return {
        left: "5%",
        right: "5%",
        bottom: "3%",
        top: "10",
        containLabel: true
      };
    },
    xAxis() {
      let obj = {};
      obj.type = "value";
      obj.position = "top";
      obj.splitLine = {
        show: false
      };
      obj.max = 104;
      obj.axisLine = {
        show: true,
        symbol: ["none", "arrow"],
        symbolSize: [6, 10],
        color: "#ddd"
      };
      obj.axisLabel = {
        show: false
      };
      obj.axisTick = {
        show: false
      };
      obj.barCategoryGap = "10";
      obj.boundaryGap = false;
      // obj.data=this.line_data.xAxis;
      return obj;
    },
    yAxis() {
      let obj = {};
      obj.type = "category";
      obj.splitLine = {
        show: false
      };
      obj.axisTick = {
        show: false,
        interval: 0,
        alignWithLabel: true
      };
      obj.axisLabel = {
        fontSize: 14
      };
      obj.boundaryGap = true;
      obj.inverse = true;
      // obj.interval = 5;
      obj.max = value => {
        // return value.max - (5-this.line_data.yAxis.length)
        return value.max;
      };
      obj.data = this.line_data.yAxis;
      return obj;
    },
    series() {
      let obj = {
        name: "占比",
        type: "bar",
        barWidth: 30,
        // animation:true,
        // animationThreshold:100,
        label: {
          show: true,
          position: "insideRight",
          color: "#fff",
          distance: -2,
          formatter: function(param) {
            if (Number(param.data) > 10) {
              return "{percentIn|" + param.data + "%}{percentOut|}";
            } else {
              return "{percentIn|}{percentOut|" + param.data + "%}";
            }
          },
          // "{percentIn|{@[1]}>20?{@[1]}%:''}{percentOut|{c}>20?'':{c}%}",
          rich: {
            percentIn: {
              color: "#fff"
            },
            percentOut: {
              width: 2,
              color: "#000"
            }
          }
        }
      };
      (obj.itemStyle = {
        normal: {
          // 定制显示（按顺序）
          color: params => {
            var colorList = [
              "#0C5ABD",
              "#32B16C",
              "#00A0E9",
              "#7EBEF4",
              "#8DCB77"
            ];
            // var colorList = ['#F9E79F','#FFCC00','#CC66FF','#3399FF','#00CC33'];
            return colorList[params.dataIndex];
          }
        }
      }),
        // obj.name=legend[i];
        (obj.data = this.line_data.xAxis);
      return obj;
    },
    getWidth() {
      let node = this.$refs["line"];
      if (node != undefined) {
        let offsetWidth = node.offsetWidth;
        this.line_width = offsetWidth;
        if (this.myChart != undefined) {
          this.myChart.clear();
          this.myChart.dispose();
          this.myChart = null;
        }
        this.$nextTick(() => {
          this.init();
        });
      }
    },
    resize() {
      let _self = this;
      window.addEventListener("resize", () => {
        _self.getWidth();
      });
    }
  },
  watch: {
    lineData: {
      handler(val) {
        this.line_data = Object.assign({}, val);
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.line_data = Object.assign({}, this.lineData);
    this.getWidth();
    // this.resize();
  },
  beforeDestroy() {
    if (!this.myChart) {
      return;
    }
    this.myChart.clear();
    this.myChart.dispose();
    this.myChart = null;
    window.removeEventListener("resize");
  }
};
</script>

<style scoped></style>
