<!--  -->
<template>
<div class='select-container'>
    <!-- 输入框 -->
    
        <div 
         @mouseenter="handleMouseEnter"
         @mouseleave="handleMouseLeave"
        style="width: 100%;"
         >
         <div @click="inputFocus">
             <Input 
                    v-model="str" 
                    ref="input"
                    :icon="inputIcon"
                    :placeholder="$t('comm_select')" 
                    style="width: 100%"
                    @on-click.stop="iconClick"     
                    @on-change="inputChange"
                    @on-blur="close"
                  
                     />

         </div>
           

        </div>
        <!-- <div class="del-icon" v-if="isDelIcon">
            <Icon type="ios-close-circle" color="#05EEFF" size="14" />
        </div> -->
        <!-- 删除图标 -->
         <!-- v-if="isDropdownShow" -->
        <div v-if="isDropdownShow" >
               <div v-if="dropdownList.length !== 0 " class="dropdown-container">
            <div 
            :class="['dropdown-item', active === index ? 'active' : '']" 
            v-for="(item,index) in dropdownList" 
            @mousedown="itemClick(item,index)"
            :key="index">{{item[label]}}</div>
        </div>
        

        <div  v-else  class="no-data" >{{$t('no_matching_data')}}</div>

        </div>
        
     
        
  
</div>
</template>

<script>

export default {
name: '',
components: {},
props:{
    dropdownData:{
        type:Array
    },
    inputVal: {
        required:true
    },
    label: {
        required:true
    }, 
    value:{
        required:true
    }
},
data() {
return {
    // hostList:[]
    str:'',
    isDropdownShow:false,
    // 显示删除图标
    isDelIcon:false,
    // 筛选出来的数据
    searchList:[],
    // 点击item的index
    active:null,
    // 处理后的下拉框数据
    // dropdownList:[]
    searchOK:true

}
},
computed: {
    // 处理图标
    inputIcon() {
        if(this.isDropdownShow && !this.isDelIcon) {
            return 'ios-arrow-up'
        }else if(this.isDelIcon) {
            return "ios-close-circle"
        }
        else {
            return "ios-arrow-down"
        }
    },
     // 处理下拉框的数据渲染
    dropdownList:{
        get() {
            
        if(this.searchList.length === 0 && !this.searchOK) {
            console.log('没有搜索到')
            return []
        }else if(this.searchList.length === 0 && this.searchList){
            return this.dropdownData

        }
        else {
            return this.searchList
        }

        },
        set() {


        }
       
    }
},
watch: {
    str() {
          if(this.searchList.length === 0 && !this.searchOK) {
            console.log('没有搜索到')
            return []
        }else if(this.searchList.length === 0 && this.searchList){
            return this.dropdownData

        }
        else {
            return this.searchList
        }

       
    }
    
},
methods: {
   
    // 输入框获取焦点
    inputFocus() {

  
        // this.isDropdownShow = this.isDropdownShow ? false : true
        if(this.isDropdownShow === true) {
            console.log(this.isDropdownShow)
            this.isDropdownShow = false
        }else {
            this.isDropdownShow = true
        }
        this.searchList = []


    },
    // 节点点击事件
    itemClick(obj,index) {
        console.log(index,'index')
        console.log('点击了')
        this.active = index
        this.str = obj.labelName
        this.isDropdownShow = false
        this.$emit('update:inputVal',obj[this.value])
        this.$refs.input.focus({
            cursor:"end"

        })
        

    },
    handleMouseEnter() {
        // 处理鼠标移入输入框
        console.log('鼠标移入')
        if(this.str.length !== 0) {
            console.log('显示')
            this.isDelIcon = true

        }
    },
    handleMouseLeave() {
        // 处理鼠标移出
        this.isDelIcon = false
    },
    // 点击图标删除
    iconClick() {
     if(this.inputIcon === "ios-close-circle")
            // 删除
                this.str = ''
                this.isDropdownShow = false
                this.$emit('update:inputVal',null)
                this.isDelIcon = false
                this.active = null
                this.searchOK = true
                this.searchList = []

        
       
    },
    // 输入搜索
    inputChange() {
        // console.log(this.str)
       this.searchOK = true
        this.searchList = []
        
        
        this.dropdownData.forEach(item => {
            // console.log(item[this.label])
         

            if(item[this.label].includes(this.str)) {
                console.log('3333333333333')

               this.searchList.push(item)
                console.log(111)
                console.log(this.searchList,"this.searchList")


            }
            
           if(this.searchList.length === 0) {
            console.log(3333333333)
            this.searchOK = false

           }
        })
        
    },
    // 关闭下拉
    close() {
        this.isDropdownShow = false
    }


},
created() {
   

},
mounted() {
    
   
    

},
}
</script>
<style scoped lang='less'>
.select-container {
    position: relative;
    cursor: pointer;
    width: 100%;
}
.no-data {
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #c5c8ce;
         border: 1px solid #04478e;
          background-color: #061824;
        //  margin-top: 5px;
         border-radius: 4px;
         position: absolute;
          top: 35px;
          width: 100%;
          z-index: 999;


    }
.dropdown-container {
    max-height: 200px;
    width: 100%;
    overflow-y: auto;
    position: absolute;
    top: 35px;
    z-index: 999;
    background-color: #061824;
    border: 1px solid #04478e;
     border-radius: 4px;
    
   
    .dropdown-item {
        padding: 10px;
        color: #fff;
        white-space: nowrap;

    }
    .dropdown-item:hover {
        background-color: #082C43;
        color: #1affec;
        cursor: pointer;
        
       

    }
    .dropdown-active {
        color: #1d83e9;
    }
}
/deep/.ivu-input-icon {
    // color: #05EEFF;
    color: var(--ivu_select_arrow , #05EEFF);
    font-size: 14px;

}
.del-icon {
    position: absolute;
    top: 6px;
    right: 10px;
   


}
.active {
    color: #2d8cf0 !important;
}
</style>