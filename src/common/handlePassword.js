const sm2 = require('sm-crypto').sm2;
const sm3 = require('sm-crypto').sm3;
// 密码解密
function decrypt(val){
    if (val){
        // 解密秘钥
        const privateKey ="7e667c234cd50ea243989090d15d8ac4c4fa312d8087a3efb0e9157096feb18b";
        let decryptPassw = sm2.doDecrypt(val,privateKey,1); // 解密结果
        return decryptPassw;
    }else{
        return null
    }

}
// 密码解密
function decryptByJSON(val){
    if (val){
        // 解密秘钥
        const privateKey ="069eaca522d39e7b5f4f6bef7164a241e3e624288d69efec0a486fb44bcf274b";
        let decryptPassw = sm2.doDecrypt(val,privateKey,1); // 解密结果
        let value =JSON.parse(decryptPassw).value
        return value;
    }else{
        return null
    }

}
// 密码加密
function encryption(val){
    if (!val){
        return ''
    }
    let sm3Pass=sm3(val);
    //加密秘钥
    const publicKey= "047fc238aaaf1ef1e0e334ca4244e3d9fc41f40dcd515aa1dd23f2365999d835caa810481aba795b99462c630a3ed8567d960a4fb735041b2bda1ff3c361273519"
    let  value={
        value:val,
        deValue:sm3Pass
    }
    
    let encryptionPassw = sm2.doEncrypt(JSON.stringify(value), publicKey, 1); // 加密结果 
    console.log(encryptionPassw);
    return encryptionPassw;
}

export default {
    decrypt,
    encryption,
    decryptByJSON
}