
        // this.$Message.info(config)
        // this.$Message.success(config)
        // this.$Message.warning(config)
        // this.$Message.error(config)
        // this.$Message.loading(config)
var warning = function (msg){
    // console.log(msg,"--------msg监听--------");
    var _msg = msg;
    if(isJson(msg)){
        _msg = msg.content || "";
    }
    window.parent.postMessage({type:'msg',msg:_msg,proType:'warning'}, '*');
}
var success = function (msg){
    // console.log(msg,"--------msg监听--------");
    var _msg = msg;
    if(isJson(msg)){
        _msg = msg.content || "";
    }
    window.parent.postMessage({type:'msg',msg:_msg,proType:'success'}, '*');
}
var info = function (msg){
    var _msg = msg;
    if(isJson(msg)){
        _msg = msg.content || "";
    }
    window.parent.postMessage({type:'msg',msg:_msg,proType:'info'}, '*');
}
var error = function (msg){
    var _msg = msg;
    if(isJson(msg)){
        _msg = msg.content || "";
    }
    window.parent.postMessage({type:'msg',msg:_msg,proType:'error'}, '*');
}
var loading = function (msg){
    var _msg = msg;
    if(isJson(msg)){
        _msg = msg.content || "";
    }
    window.parent.postMessage({type:'msg',msg:_msg,proType:'loading'}, '*');
}

function isJson(obj){
    return typeof obj === 'object' && Object.prototype.toString.call(obj) === '[object Object]'
}

export default {
    warning,
    success,
    info,
    error,
    loading
}