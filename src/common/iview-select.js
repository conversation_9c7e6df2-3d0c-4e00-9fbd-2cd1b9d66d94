import ViewUI from 'view-design';
import 'view-design/dist/styles/iview.css';

ViewUI.Select.props.onlyFilterWithText = {
    type: Boolean,
    default: false
  };
  ViewUI.Select.methods.originValidateOption = ViewUI.Select.methods.validateOption;
  ViewUI.Select.methods.validateOption = function ({children, elm, propsData}) {
      if (this.onlyFilterWithText) {
        const textContent = (elm && elm.textContent) || (children || []).reduce((str, node) => {
            const nodeText = node.elm ? node.elm.textContent : node.text;
            return `${str} ${nodeText}`;
        }, '') || '';
        const query = this.query.toLowerCase().trim();
        return textContent.toLowerCase().includes(query);
      } else {
        return this.originValidateOption({children, elm, propsData});
      }
  };
  //因为iview内部的validateOption函数不仅会匹配conName，还会匹配conCode。只要其中有一项匹配，那么这条数据就会被选中。我们现在重写iview的validateOption函数，让它只匹配conName
  