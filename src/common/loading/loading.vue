<template>
  <Spin fix v-show="loading" class="loading-wrapper">
    <!--<Icon type="ios-loading" :size="24" class="circular" />-->
    <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
    <div>{{$t('common_loading')}}</div>
  </Spin>
</template>

<script>
export default {
  name: "Loading",
  props: {
    loading: Boolean
  }
};
</script>
<style scoped>
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
