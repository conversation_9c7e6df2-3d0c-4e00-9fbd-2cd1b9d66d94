@font-face {
  font-family: "iconfont"; /* Project id 4038417 */
  src: url('iconfont.woff2?t=1684981004435') format('woff2'),
       url('iconfont.woff?t=1684981004435') format('woff'),
       url('iconfont.ttf?t=1684981004435') format('truetype'),
       url('iconfont.svg?t=1684981004435#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-img-print:before {
  content: "\e692";
}

.icon-icon-return1:before {
  content: "\e687";
}

.icon-icon-sjtb:before {
  content: "\e68e";
}

.icon-btn-sp:before {
  content: "\e686";
}

.icon-btn-password:before {
  content: "\e684";
}

.icon-btn-dormant:before {
  content: "\e685";
}

.icon-btn-activate:before {
  content: "\e682";
}

.icon-btn-cancel:before {
  content: "\e683";
}

.icon-btn-freeze:before {
  content: "\e67f";
}

.icon-btn-manage02:before {
  content: "\e680";
}

.icon-btn-manage01:before {
  content: "\e681";
}

.icon-btn-del:before {
  content: "\e67b";
}

.icon-btn-handle:before {
  content: "\e67c";
}

.icon-btn-edit:before {
  content: "\e67d";
}

.icon-btn-look:before {
  content: "\e67e";
}

.icon-icon-execute:before {
  content: "\e67a";
}

.icon-icon-screen:before {
  content: "\e679";
}

.icon-icon-query:before {
  content: "\e674";
}

.icon-icon-add:before {
  content: "\e675";
}

.icon-icon-del:before {
  content: "\e676";
}

.icon-icon-renovate:before {
  content: "\e677";
}

.icon-icon-derive:before {
  content: "\e678";
}

