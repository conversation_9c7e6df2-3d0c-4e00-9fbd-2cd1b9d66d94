<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="620px" height="301px" viewBox="0 0 620 301" preserveAspectRatio="none">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#060d15"/>
      <stop offset="0.418" stop-color="#060d15" stop-opacity="0"/>
      <stop offset="0.674" stop-color="#060d15" stop-opacity="0"/>
      <stop offset="1" stop-color="#060d15"/>
    </linearGradient>
    <filter id="路径_210">
      <feOffset dy="4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-color="#05d4ff" flood-opacity="0.302" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00a3af"/>
      <stop offset="1" stop-color="#00585e"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x2="0.968" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0"/>
      <stop offset="0.477" stop-color="#05d4ff"/>
      <stop offset="1" stop-color="#05d4ff" stop-opacity="0"/>
    </linearGradient>
    <filter id="矩形_766" x="221" y="9" width="379" height="31" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-2"/>
      <feFlood flood-color="#0fe"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-4" x1="0.433" y1="0.057" x2="0.437" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#05eeff" stop-opacity="0.502"/>
      <stop offset="0.22" stop-color="#31f0fe" stop-opacity="0"/>
      <stop offset="0.815" stop-color="#31f0fe" stop-opacity="0"/>
      <stop offset="1" stop-color="#05eeff" stop-opacity="0.4"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x2="0.968" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0"/>
      <stop offset="0.477" stop-color="#0fe"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <filter id="路径_288" x="10" y="0" width="204" height="19" filterUnits="userSpaceOnUse">
      <feOffset dy="6" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-color="#0fe"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="img-frame" transform="translate(-30 -150)">
    <g data-type="innerShadowGroup">
      <path id="路径_210-2" data-name="路径 210" d="M0,16,15.86,0,216.815.017l16.322,18.078L620,17.844V281.938L603.727,298H16.071L0,282Z" transform="translate(30 153)" fill="url(#linear-gradient)"/>
      <g transform="matrix(1, 0, 0, 1, 30, 150)" filter="url(#路径_210)">
        <path id="路径_210-3" data-name="路径 210" d="M0,16,15.86,0,216.815.017l16.322,18.078L620,17.844V281.938L603.727,298H16.071L0,282Z" transform="translate(0 3)" fill="#fff"/>
      </g>
    </g>
    <path id="联合_22" data-name="联合 22" d="M-3657.006-3745.314l-5.666-5.3h4.714l5.666,5.3Zm-10.146,0-6.521-6.529h5.812l6.519,6.529Zm-12,0-7.525-7.979h7.1l7.525,7.979Zm-14.147,0L-3702-3755h8.622l8.707,9.687Z" transform="translate(3958 3911)" opacity="0.5" fill="url(#linear-gradient-2)"/>
    <g transform="matrix(1, 0, 0, 1, 30, 150)" filter="url(#矩形_766)">
      <rect id="矩形_766-2" data-name="矩形 766" width="349" height="1" transform="translate(236 21)" fill="url(#linear-gradient-3)"/>
    </g>
    <path id="路径_288_-_轮廓" data-name="路径 288 - 轮廓" d="M16.277,1,1,16.412V281.585L16.484,297H603.318L619,281.585l.012-262.751-386.343.244L216.37,1.017,16.277,1M15.86,0,216.815.017l16.295,18.062L620,17.833V282l-16.273,16H16.071L0,282V16Z" transform="translate(30 153)" fill="url(#linear-gradient-4)"/>
    <g transform="matrix(1, 0, 0, 1, 30, 150)" filter="url(#路径_288)">
      <path id="路径_288-2" data-name="路径 288" d="M0,0H186V1H0Z" transform="translate(19 3)" fill="url(#linear-gradient-5)"/>
    </g>
  </g>
  
</svg>
