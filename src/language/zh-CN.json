{"login_browser": "建议使用360、chrome浏览器进行访问", "test_enum": "枚举类测试", "test_string": "字符串测试", "change": "切换", "改变": "改变", "login_user": "请输入账号", "login_pwd": "请输入密码", "login_code": "请输入验证码", "login_captcha_pictures": "验证码图片", "login_in": "登录中", "login_ok": "登录", "login_browser_1": "建议使用360、chrome浏览器访问", "login_browser_2": "建议分辨率:1920 * 1080", "login_remember": "记住密码", "login_pwd_icon": "密码图标", "login_user_icon": "帐户图标", "login_captcha_icon": "验证码图标", "login_msg_user_limit": "帐户名限制2到30个字符", "login_msg_pwd_length": "密码长度不小于8位", "login_msg_pwd_limit": "密码仅限:字母、数字、符号任意两种以上组合且不小于8位", "login_msg_vcode_len": "验证码长度限制4到6位", "login_msg_vcode_limit": "验证码必须为数字/字母", "os_copyright": "", "login_msg_auth": "未配置权限,请联系管理员配置!", "login_success": "登录成功", "login_failed": "登录失败", "login_try_again": "请稍后再试…", "login_error": "登录错误", "login_network_error": "请检查网络连接", "login_chang_layout": "调整布局", "login_code_error": "获取验证码错误", "login_code_fail": "验证码获取失败", "login_edit_status_error": "禁止编辑状态", "login_first_update_password_tip": "首次登录需要修改初始密码，请修改您的初始密码！", "login_update_password_tip": "友情提示：您的密码已到期，请重置密码！", "login_password_confirmation_tip": "密码确认", "login_password_confirmation_tip2": "请确认密码", "login_password_verify_tip": "密码为大写字母、小写字母、数字、符号任意三种以上组合，不少于8个字符", "login_update_idcard": "绑定身份证", "login_update_idcard_tip": "根据国家信息安全部要求，需要绑定身份证号才可使用系统，", "login_update_idcard_tip2": "请绑定您的身份证号！", "login_update_idcard_error": "身份证不能为空", "login_update_idcard_format_error": "身份证格式不正确", "server_pop_up": "弹出提示", "server_sound": "声音", "server_auto_shutdown": "自动关闭", "server_pro_hold": "保持", "server_inter_alarm": "中断", "server_deter_alarm": "劣化", "server_prom_alarm": "通知", "server_want_delete": "<p>要删除吗?</p>", "but_search": "查询", "but_add": "新建", "but_edit": "编辑", "but_copy": "复制", "but_set": "配置", "but_update": "更新", "but_upload": "上传", "but_delete": "删除", "but_reset": "重置", "but_export": "导出", "but_clear": "清除", "but_choose": "选择", "but_choose1": "选择", "but_on": "开启", "but_off": "关闭", "but_confirm": "确认", "but_cancel": "取消", "but_query": "查询", "but_activate": "激活", "but_freeze": "冻结", "but_batch_import": "批量导入", "but_data_export": "数据导出", "but_export_check": "导出检查", "but_export_all": "导出全部", "but_job_adjustment": "任务调整", "but_alarm_setting": "告警设置", "but_pause": "暂停", "but_enable": "启用", "but_import_updates": "导入更新", "but_import": "导入", "but_filter": "掩码", "but_unfilter": "取消掩码", "but_filter_list": "掩码列表", "but_remove": "删除", "but_recovery": "恢复", "but_add_new_cond": "添加新条件", "but_add_widget": "添加构件", "but_edit_widget": "编辑构件", "but_next_step": "下一步", "but_prev_step": "上一步", "but_full_screen": "全屏", "but_zoom_out": "缩小", "but_zoom_in": "放大", "but_export_png": "导出为PNG", "but_tree": "树形图", "but_star": "星形图", "but_center": "居中", "but_frame": "框架", "but_mouse_zoom": "鼠标缩放", "but_return": "返回", "but_print": "打印", "but_data_sysn": "数据同步", "but_hide": "屏蔽", "but_hide_list": "屏蔽列表", "comm_add_tasks": "创建任务", "comm_warn_tip_title": "警告", "comm_success": "成功", "comm_verify_success": "验证通过", "comm_failure": "失败", "comm_push": "推送", "comm_pushing": "正在推送", "comm_pushing1": "推送中", "comm_success1": "成功", "comm_failure1": "失败", "but_export_checked": "导出勾选", "but_operation": "操作", "comm_create_new": "新", "discover_date_rang": "请配置时间段", "comm_device_manage_menu": "设备管理", "comm_device_find_menu": "设备发现", "comm_language_title": "切换语言", "comm_language_zh": "简体中文", "comm_language_en": "English", "comm_go_to": "去处理", "comm_view_success": "查看成功", "comm_upload_file": "请上传文件", "comm_upload_file2": "请选择探针、请选择类型、请输入端口、请输入目标IP等", "comm_enter_probe": "请输入探测端口", "comm_maximum": "最多可添加五个端口", "comm_probe_range": "探测端口输入错误，探测范围（20-60000）", "comm_ip": "IP段仅支持一个'-'连接", "comm_import_succeeded": "导入成功", "comm_import_failed": "导入失败", "comm_colon": "：", "comm_semicolon": "；", "comm_exigency": "紧急", "comm_seriousness": "严重", "comm_records": "条记录", "comm_excellent": "优秀", "comm_good": "良好", "comm_poor": "差", "comm_times": "次", "comm_strong": "强", "comm_medium": "中", "comm_weak": "弱", "comm_year1": "年", "comm_format_year": "yyyy年MM月", "comm_format_year2": "yyyy年", "comm_week_tip": "请选择周数", "comm_month": "月", "comm_day": "日", "comm_day1": "天", "comm_week": "周", "comm_minutes": "分钟", "comm_second": "秒", "comm_view_more": "查看更多", "comm_discovery_state": "发现状态", "comm_already_delete": "数据已被删除", "comm_object_filter": "对象筛选", "comm_select_date": "选择时间", "comm_select_date2": "选择日期", "comm_serial_number": "序号", "comm_time": "时间", "comm_hour": "小时", "comm_select_time": "请选择日期", "comm_select_month": "请选择月份", "comm_remove": "移除", "comm_data_source": "数据源", "comm_keywords": "关键字", "comm_org": "机构", "comm_org_export_excel_file_name": "机构列表数据.xlsx", "comm_org_export_all_excel_file_name": "全部机构列表数据.xlsx", "comm_org_import_template_file_name": "机构导入模板.xlsx", "comm_org_export_error_file_name": "机构错误数据列表.xlsx", "comm_org_alias": "机构别名", "comm_group": "分组", "comm_select_group": "请选择分组", "comm_all": "全部", "comm_part": "部分", "comm_num": "总数量", "comm_check_num": "勾选数量", "comm_isp": "运营商", "comm_minute": "分钟", "comm_select_org": "选择机构", "comm_half_hour": "半小时", "comm_effective": "有效", "comm_invalid": "无效", "comm_disable": "暂停", "comm_startup": "启动", "comm_select_device_type": "选择设备类型", "comm_enable": "启用", "comm_all_state": "所有状态", "comm_type1": "可以查询名称、账户、电话", "comm_started": "已开始", "comm_paused": "已暂停", "comm_prev_text": "上一页", "comm_next_text": "下一页", "comm_prev_step": "上一步", "comm_next_step": "下一步", "comm_next": "下一步", "comm_today": "今天", "comm_yesterday": "昨天", "comm_last_7": "最近7天", "comm_last_30": "最近30天", "comm_curr_month": "当月", "comm_preced_month": "上月", "comm_not_recovered": "未恢复", "comm_has_recovered": "已恢复", "comm_last_day_1": "最近1天", "comm_last_day_15": "最近15天", "comm_last_hour_12": "最近12小时", "comm_save": "保存", "comm_export_check": "导出选中", "comm_export_all": "导出全部", "comm_high_frequency_monitoring": "高频监控", "comm_start_time": "起止日期", "comm_end_time": "结束日期", "comm_create_successfully": "创建成功", "comm_modified_successfully": "编辑成功", "comm_delete_successfully": "删除成功", "comm_delete_failed": "删除失败", "comm_operation_successfully": "操作成功", "comm_operation_create_successfully": "创建成功", "comm_operation_failed": "操作失败", "comm_move_success": "移动成功", "comm_move_out": "移出", "comm_organization_delete": "选择要删除的机构", "comm_task_type": "选择任务类型", "comm_deadline": "请选择截止日期", "comm_start_date": "请选择开始日期", "comm_select_data": "请选择数据", "comm_report_name": "报告名称", "comm_report_name2": "报告名称", "comm_enter_value": "请输入值", "comm_segmentation": "多模式，数字分段", "comm_report_type": "报告类型", "comm_quality": "专线质差分析", "comm_wifi": "wifi分析", "comm_report_type_1": "日检测报告", "comm_report_type_2": "周检测报告", "comm_report_type_3": "月检测报告", "comm_report_type_4": "自定义报告", "comm_report_generation_time": "报告生成时间", "comm_report_time_range": "统计时间范围", "comm_report_comp_score": "综合得分", "comm_report_user_expe": "用户体验", "comm_report_score": "综合评分", "comm_report_provider": "报告提供方", "comm_statistical_angle": "统计视角", "comm_angle_failure": "故障", "comm_topo_style": "拓扑样式", "comm_pattern": "样式", "comm_angle_event": "事件", "comm_event_query": "事件", "comm_fault_query": "故障", "comm_repeat": "重复", "comm_server_name": "名称/IP", "comm_enter": "请输入", "comm_sender": "发件人姓名", "comm_email": "发送邮件", "comm_port1": "端口", "comm_mailbox": "邮箱用户", "comm_email_password": "电子邮件密码", "comm_email_test": "测试接收邮件", "comm_segment": "目标地址段", "comm_segment_mistake": "目标地址段不正确", "comm_generation": "劣化生成阈值", "comm_template": "请选择模板类型", "comm_message_type": "请选择消息类型", "comm_fuzzy_query": "支持按模板消息名称、备注进行模糊查询", "comm_template_name": "模板名称：", "comm_fill_message": "请输入模板名称", "comm_remarks": "备注", "comm_task": "任务", "comm_unit_one": "单位：个", "comm_gross_amount": "总量", "comm_interruption": "中断", "comm_delay": "时延劣化", "comm_loss_package": "丢包劣化", "comm_loss_package_simple": "丢包劣化", "comm_rout_changes": "路由波动", "licence_end": "许可证到期，请上传超出记录附件，将自动截断，不再提醒！", "comm_equal": "等于", "comm_not_equal_to": "不等于", "comm_contain": "包含", "comm_not_contain": "不包含", "comm_Device_name": "设备名称", "comm_deal_with": "处理方式", "comm_enter_name": "请输入设备名称", "comm_enter_type": "请输入设备类型", "comm_enter_show_code": "请输入类型编码", "comm_local_address": "本地地址", "comm_no": "否", "comm_yes": "是", "comm_show": "显示", "comm_no_show": "不显示", "comm_enter_device_name": "输入设备名称", "comm_enter_device_ip": "输入设备IP地址", "comm_port_name": "，端口号：", "comm_normal": "正常", "comm_normal_line": "正常线路", "comm_deteriorate": "劣化", "comm_interrupted": "中断", "comm_deterioration": "劣化", "comm_unknown": "未知", "comm_not_collected": "未采集", "comm_Blackout": "停电", "comm_failure_up": "故障升级", "comm_failure_dwon": "故障降级", "comm_path_switching": "路径切换", "comm_failure_re": "故障恢复", "comm_failure_link_recovery": "故障链路恢复", "comm_cut": "割接", "comm_congestion": "拥塞", "comm_probe": "探针", "comm_mid_node": "中间节点", "comm_unknown_node": "未知节点", "comm_two_node": "二层节点", "comm_target_node": "目标节点", "comm_urgent_notice": "紧急通知详情", "comm_symptom": "故障详情", "comm_portStatus": "端口状态监控详情", "comm_path": "初始路径", "comm_untreated": "未处理", "comm_process": "处理中", "comm_closed_loop": "已闭环", "comm_enterprise_network": "企业网", "comm_special_line_network": "专线", "comm_other": "其他", "comm_pl_enter_ip": "请输入IP地址", "comm_source": "数据来源", "comm_name": "名称", "comm_type": "类型", "comm_describe": "描述", "comm_note": "备注", "comm_status": "状态", "comm_operate": "操作", "comm_a_ip": "A-IP", "comm_z_ip": "B-IP", "comm_interrup_times": "中断累计次数", "comm_interrup_total": "中断累计时长", "comm_deterioration_times": "劣化累计次数", "comm_deterioration_total": "劣化累计时长", "comm_packetLossDegradation_times": "丢包劣化累计次数", "comm_packetLossDegradation_avg": "丢包劣化平均时长", "comm_packetLossDegradation_duration": "丢包劣化累计时长", "comm_delayDegradation_times": "时延劣化累计次数", "comm_delayDegradation_avg": "时延劣化平均时长", "comm_delayDegradation_duration": "时延劣化累计时长", "comm_usable_rate": "可用率", "comm_good_rate": "良好率", "comm_data": "数据不匹配", "comm_add": "新建", "comm_": "链路", "comm_addresses": "支持通过故障疑似IP地址模糊搜索", "comm_faulty": "疑似故障链路IP/拨测任务编号/中继编号/故障现象", "comm_faulty2": "疑似故障链路的IP地址/拨测任务号/故障现象", "comm_IP_collector": "IP、采集器", "comm_work": "工单", "comm_difference": "链路质差分析", "comm_private": "专线拥堵详情", "comm_classification": "功能分类", "comm_filtering": "对象筛选", "comm_topo": "拓扑图", "comm_topo_view": "进入物理拓扑时，默认为：", "comm_add_topo": "添加至拓扑视图", "comm_remove_topo": "移除拓扑", "comm_test_phone": "测试电话", "comm_enter_test_phone": "请输入测试电话号码", "comm_content": "测试内容", "comm_Initial_TTL": "起始TTL", "comm_task_name": "任务名称", "comm_task_code": "任务代码", "comm_source_ip": "源IP", "comm_target_ip": "目标IP", "comm_target_name": "目标名称", "comm_enter_target_name": "请输入目标名称", "comm_target_number": "影响目标数", "comm_target": "影响目标", "comm_relevant_fault": "关联故障数", "comm_relevant_event": "关联事件数", "comm_interruption_sup_time": "中断叠加时长", "comm_sup_time2": "叠加时长", "comm_interruption_top5": "中断TOP5", "comm_degradation_top5": "劣化TOP5", "comm_link_index": "链路统计指标", "comm_relay_indicators": "中继统计指标", "comm_relay_details": "中继详情", "comm_link": "链路", "comm_link_interrup_times": "中断总数", "comm_link_de_times": "劣化总数", "comm_link_de_long_time": "劣化总时长", "comm_break_avg_long_time": "中断平均时长", "comm_avg_long_time": "平均时长", "comm_probe_ip_address": "请选择探针IP", "comm_probe_destination_ip_address": "请输入目标IP", "comm_dial_type": "请选择拨测类型", "comm_integers": "只能输入大于0的正整数", "comm_starting": "不能大于限制跳数", "comm_greater30": "值不能大于30", "comm_restricted": "不能小于起始值", "comm_select_org2": "请选择组织机构", "comm_TTL": "TTL受限跳数：", "comm_rule_set": "规则参数设置：", "comm_length128": "长度不能超过128", "comm_length30": "长度不能超过30", "comm_length50": "长度不能超过50", "comm_upload": "请上传文件", "comm_enter_destination_port": "请输入目标端口", "comm_branch": "请选择分支", "comm_mail": "邮件", "comm_fill_verification": "请填写验证码", "comm_secret_key": "请填写密钥", "comm_secret_key1": "密钥", "comm_fuzzy": "通过拨测任务编号、探测器IP/端口和目标名称进行模糊搜索是支持的", "comm_sure_information": "是否确实要启用选定的接口信息？", "comm_branch_deleted": "分支已成功删除", "comm_enter_user": "请输入用户名", "comm_server": "请输入服务器IP地址", "comm_serve_port": "请输入服务器端口", "comm_device_model": "输入设备型号", "comm_message": "消息", "comm_nail": "钉钉", "comm_voice": "语音", "comm_enterprise_wechat": "企业微信", "comm_mail_service": "邮件服务", "comm_sms_service": "短信服务", "comm_wechat_service": "微信服务", "comm_dingtalk_service": "钉钉服务", "comm_enterprise_wechat_service": "企业微信服务", "comm_message_service": "消息服务", "comm_choose_time": "请选择时间", "comm_download_tpl": "下载模板", "comm_download_tpl1": "请下载模板", "comm_use_download_tpl": "使用模板进行数据编辑、导入!", "comm_import_right": "（如有误，仅导入正确项，或调整excel后重新上传）", "comm_export_error": "导出错误", "comm_probe_port": "探针端口", "comm_probe_ip": "探针IP", "comm_please_enter": "请输入", "comm_please_enter_name": "请输入名称", "comm_service": "服务", "comm_service_name": "服务名称", "comm_service_path": "部署路径", "comm_monday": "周一", "comm_tuesday": "周二", "comm_wednesday": "周三", "comm_thursday": "周四", "comm_friday": "周五", "comm_saturday": "周六", "comm_sunday": "周日", "comm_no_data": "暂无数据", "comm_conditions": "条件", "common_select_group": "请选择一个组", "common_group": "组", "common_get_data_error": "获取数据失败", "common_enable_prompt": "启用提示", "common_disable_prompt": "禁用提示", "common_forbidden_prompt": "禁用提示", "common_delete_prompt": "删除提示", "comm_ip_correct": "请填写正确的ip", "comm_dial_protocol": "拨测协议", "comm_dial_protocol1": "拨号测试协议1", "comm_port": "端口", "comm_time_range": "时间范围", "comm_repeat_cycle": "重复周期", "comm_operation_scheduling": "作业计划", "comm_add_to_group": "添加到组", "comm_deleted": "所选数据包含正在删除的数据，请重新选择", "comm_unmask": "您确定要取消掩码此数据吗？", "comm_deleteing": "所选数据包含正在删除的数据，请勿重复执行此操作", "comm_nailing_service": "钉住服务", "comm_example": "示例：（5或5-7）", "comm_responsible": "支持负责人员、负责人员电话以进行模糊匹配搜索", "comm_search_ip_name": "支持按设备IP、设备名称进行模糊匹配搜索", "comm_search_ip_device_task": "支持按设备IP、设备名称、任务编号进行模糊匹配搜索", "comm_search_code_device_ip_group": "支持按设备编码，设备名称，设备IP，分组模糊查询", "comm_variable": "变量", "comm_start": "选择开始时间", "comm_year": "请选择年份", "comm_matching": "探针IP、目标IP/目标名称、任务ID、路径ID支持模糊匹配", "comm_matching2": "通过应用程序名称和AgentId支持模糊匹配", "comm_enabled": "已启用", "comm_uncorrelated": "未关联", "comm_unlinked": "未关联", "comm_deleting": "正在删除", "comm_line_deleted": "专线删除成功", "comm_deleting2": "数据删除中,请勿进行其他操作", "comm_time_window": "时间窗口", "comm_last_1hour": "最近1小时", "comm_last_3hour": "最近3小时", "comm_last_4hour": "最近4小时", "comm_last_6hour": "最近6小时", "comm_last_8hour": "最近8小时", "comm_last_24hour": "最近24小时", "comm_alarms_number": "告警数量", "comm_select_manufacturer": "请选择商家", "comm_relay_index": "统计指标", "comm_select_start_time": "请选择开始日期", "comm_select_end_time": "请选择结束日期", "comm_select_cycle": "请选择周期", "comm_please_select": "请选择", "comm_from": "从", "comm_new": "新建", "comm_automatic_dial": "自动拨测", "comm_deal": "处理", "comm_add_on": "追加", "comm_changed_permission": "权限修改成功", "comm_changed_successful": "修改成功", "comm_add_successful": "新建成功", "comm_function_list": "功能列表", "comm_to": "至", "comm_no_boot": "未选择要启动的设备！", "comm_select_time1": "请选择时间", "comm_add10": "最多添加10条测速时间", "comm_physical": "物理拓扑链路异常项", "comm_data_import": "没有数据可以导入！", "comm_data_deletion": "未选择要删除的数据！", "comm_indicator": "至少保留一个指标", "comm_permission": "未配置权限，请联系管理员进行配置！", "comm_fill_assignment": "请填写分配类型", "comm_pause2": "未选择要暂停的任务！", "comm_range": "选择查询的时间范围", "comm_indicators": "已达到最大指标数", "comm_time_period": "时间段", "comm_select_time_tip": "请选择查询的时间段", "comm_unlikelihood": "不可能性", "comm_topo_mgt": "拓扑管理", "comm_jump_prompt": "跳转提示", "comm_configuration": "如果未配置视图，将在3秒内显示配置视图页面", "comm_topo_select": "拓扑选择", "comm_topo_set": "信息展示设置", "comm_topo_legend": "图例", "comm_topo_alarm": "告警", "comm_alarm_triggering": "告警触发", "comm_alarm_recovery": "告警恢复", "comm_topo_device_name": "设备名称", "comm_topo_device_id": "设备ID", "comm_topo_device_ip": "设备IP", "comm_topo_device_flow": "线路流量", "comm_topo_device_delay": "线路时延", "comm_topo_device_loss": "线路丢包率", "comm_select_delete_data": "请先勾选要删除的数据", "comm_msg_del_record": "<p>您确定要删除这些记录吗？</p>", "comm_msg_activate_record": "<p>您确定要激活这些记录吗？</p>", "comm_msg_freeze_record": "<p>您确定要冻结这些记录吗？</p>", "comm_tip": "提示", "comm_tip2": "勾选重复项时，至少选择一个重复周期项", "comm_tip3": "多个时间段存在交叉，请重新选择", "comm_large_value": "填写值过大", "comm_must_less": "该值必须不小于", "comm_must_greater": "该值必须不大于", "comm_must_less_attribute": "该值必须小于关联属性", "comm_must_greater_attribute": "该值必须大于关联属性", "comm_value": "值", "comm_correct_value": "请填写正确的值", "comm_item_filled": "项目未填写", "comm_item_filled_round": "请填写整数", "comm_ordinary_dial": "普通拨测", "comm_snmp_monitoring": "SNMP监控", "comm_high_freq_monitoring": "高频监控", "comm_high_freq": "高频", "comm_relay_task": "中继任务", "comm_line_task": "专线任务", "comm_grouping": "分组", "comm_end_to_end": "端到端", "comm_IP(MAC)": "目标IP(MAC)", "comm_om_level": "运维等级", "comm_delay(ms)": "时延（ms）", "comm_loss_rate": "丢包率（%）", "comm_unit": "单位", "comm_select": "请选择", "comm_select_type": "请选择目标类型", "comm_target_type": "目标类型", "comm_note_content": "备注内容", "comm_delay_packet": "时延+丢包率", "comm_device": "设备信息", "comm_failure_recovery": "故障恢复时间", "comm_failure_start": "故障开始时间", "comm_event_start": "事件开始时间", "comm_event_recovery": "事件恢复时间", "comm_event_type_all": "全部事件类型", "comm_failure_type_all": "全部故障类型", "comm_duration_event": "事件历时", "comm_duration_failure": "故障历时", "comm_Index_name": "指标名称", "comm_cycle": "采集周期", "comm_job_type": "作业类型", "comm_select_topology_type": "请选择拓扑类型", "comm_opology_type": "拓扑类型", "comm_multiple": "可输入多个手机号，用逗号分隔", "comm_port_number": "端口号", "comm_enter_content": "请输入内容", "comm_content1": "内容", "comm_supported": "支持按内容进行模糊查询", "comm_disclaimer": "免责", "comm_ranking_loading": "排名加载中", "comm_select_job_type": "选择任务类型", "comm_select_file": "选择文件", "comm_no_select_file": "未选择文件", "comm_number": "中断线路编号", "comm_line_number": "降级线路编号", "comm_edit": "修改", "comm_objects_add": "添加对象", "comm_not": "暂无", "comm_objects_removed": "移除对象", "comm_line_management": "线路管理", "comm_event": "事件类型", "comm_prompt": "启用提示", "comm_prompt1": "提示", "comm_pause_prompt": "暂停提示", "comm_Job_adjustment": "任务调整成功", "comm_Job_adjustment_fa": "任务调整失败", "comm_adjust": "请选择要调整的任务", "comm_up10t": "最多添加10个时间段", "comm_ip_segment": "使用相同的IP段，用“-”连接", "comm_ip_segment1": "请使用相同ip段", "comm_ip_segment2": "请按从小到大的顺序填写ip段", "comm_select_probe": "请选择探针", "comm_select_dial_type": "请选择拨测方式", "comm_sure": "确定清空验证数据吗？", "comm_imported": "导入模板可达性校验", "comm_remote": "远程备份中, 远程信息不完整!", "comm_backup": "备份成功", "comm_open": "开", "comm_close": "关", "comm_view_details": "查看详情", "comm_select_upload_file": "请选择要上传的文件", "comm_upload_file_msg_1": "正在上传，请勿重复操作", "comm_upload_file_msg_2": "正在读取数据，请稍后", "comm_file_incomplete": "上述信息输入不完整", "comm_import_confirmation": "导入确认", "comm_map_incomplete": "映射关系对应不完整", "comm_file_upload_again": "上传的文件有误，请重新上传", "comm_data_temp": "专线资料数据模板", "comm_error_list": "重复项或异常项列表", "comm_operators": "运营商", "comm_packet_loss": "丢包率", "comm_inflow": "入流速", "comm_outflow": "出流速", "comm_failed_menu": "获取菜单失败！", "comm_tip1": "任务编号、源/目标IP、目标名称", "comm_duration": "中断累计时长", "comm_duration1": "累计时长", "comm_alarm_sound_tip": "<p>因为浏览器相关安全限制，告警音效提示已关闭，若需要开启音效提示请需手动开启！</p>", "comm_alarm_sound_open_btn": "开启音效", "comm_alarm_sound_close_btn": "关闭音效", "comm_alarm_sound_open_success_tip": "提示音已开启", "comm_alarm_sound_close_success_tip": "提示音已关闭", "comm_alarm_table_alarm_type": "告警类型", "comm_alarm_table_popup_prompt": "弹窗提示", "comm_alarm_table_sound_effect_prompt": "音效提示", "comm_alarm_table_auto_close": "自动关闭", "comm_alarm_table_projection_screen": "投屏保持", "common_more": "更多", "common_disable": "禁用", "common_alarm_generation": "告警生成设置", "common_interrupt_threshold": "中断阈值", "common_degradation_threshold": "劣化阈值", "common_alarm_recovery": "告警恢复设置", "common_dial_analysis": "拨测分析", "common_max": "最大", "common_min": "最小", "common_latest_value": "最新", "common_avg": "平均", "common_length_60": "内容不能超过60个字符", "automatic_discovery_management": "自动发现管理", "topo_def_view_note": "进入路径拓扑图时，缺省打开的拓扑图", "topo_path_topology": "路径拓扑", "topo_data_deleted": "数据已经被删除", "topo_select_delete_first": "请先勾选删除数据", "topo_pl_graph_name": "请输入拓扑图名称", "topo_pl_graph_group": "请选择拓扑图分组", "topo_select_mode": "请选择管理方式", "topo_100_500": "请输入大于等于100，小于等于500的正整数", "topo_pl_line_name": "请输入线路名称", "topo_pl_dev_ip": "请输入设备IP", "topo_pl_dire": "请选择方向", "topo_pl_port_1": "请输入端口1", "topo_pl_port_2": "请输入端口2", "topo_pl_device_1": "请选择设备1", "topo_pl_device_2": "请选择设备2", "topo_pl_device": "请选择设备", "topo_line_name": "线路名称", "topo_name": "拓扑名称", "topo_group": "拓扑图分组", "topo_path": "路径拓扑图", "topo_50_dial": "路径拓扑图自动呈现分组内50个拨测任务的路径", "topo_down": "指拓扑图上最大显示节点数量，系统会智能收缩过多节点为一个可下钻查看子图的图标", "topo_l50": "请输入小于500的正整数", "topo_group_note": "路径拓扑图默认展示分组内50个拨测测试任务的路径", "topo_max_num": "最大呈现数", "topo_max_num_note": "指拓扑图上最多显示的节点数量，系统会智能将过多的节点缩小为可以下钻查看子图的图标", "topo_max_num_500": "请输入一个大于等于100且小于等于500的正整数", "topo_object_num": "管理对象数量", "topo_pl_conn_device1": "请选择连接设备1", "topo_pl_conn_device2": "请选择连接设备2", "topo_resource_selection": "资源选择", "topo_conn_device1": "连接设备1", "topo_conn_device2": "连接设备2", "topo_conn_device_ip1": "连接设备IP1", "topo_conn_device_ip2": "连接设备IP2", "topo_port_1": "端口1", "topo_port_2": "端口2", "topo_port_1_des": "端口1描述", "topo_port_2_des": "端口2描述", "topo_deport1_to_deport2": "设备1端口到设备2端口", "topo_deport2_to_deport1": "设备2端口到设备1端口", "topo_up_direction": "上行方向", "phys_list_keyword": "支持线路名称进行模糊匹配", "phys_add_line_note": "<p>此操作会更新地图中自动添加的电路，不涉及手动添加的电路，可能会有变动</p>", "shieldlist_alias": "别名：", "dash_panel": "仪表盘", "dash_panel_edit": "编辑视图", "dashboard_integer": "应为正整数", "dashboard_integer_tip": "应为正整数,例如:(5或者5-7)", "dashboard_integer_tip2": "不能超过当前视图配置的最大值 @max", "dashboard_integer_tip3": "不能小于 @min", "dashboard_range": "范围为", "dashboard_sort": "排序", "dashboard_normal_view": "普通视图", "dashboard_polymerization_view": "聚合图形", "dashboard_url_view": "URL链接", "dashboard_select_type": "请选择视图类型", "dash_edit": "编辑视图", "dash_minutes_60": "60分钟", "dash_customize": "自定义", "dash_view_type": "视图类型", "dash_create_view": "创建视图", "dash_delete_view": "删除视图", "dash_delete_view_succ": "删除视图成功", "dash_view_succ": "视图成功", "dash_up": "上移", "dash_down": "下移", "dash_top": "当前视图已在最顶部", "dash_end": "当前视图已在最底部", "dash_enter_name": "请输入视图名称", "dash_length": "长度不能超过", "dash_rows": "行数", "dash_columns": "列数", "dash_name": "视图名称", "dash_template_name": "模板", "dash_scale": "屏幕比例", "dash_scale_error": "请输入自定义屏幕比例", "dash_scale_number_error": "自定义屏幕比例只能输入正整数", "dash_other": "其它", "dash_description": "描述", "dash_delete": "确定要删除选中的视图", "dash_delete_failure": "删除失败", "dash_interface": "指定本地接口地址", "dash_IP": "输入本地设备的IP地址", "dash_trunkt": "请输入专线名称", "dash_average_delay": "平均时延", "dash_average_delay_ms": "平均时延(ms)", "dash_A": "A端互联IP", "dash_Z": "Z端互联IP", "dash_line_number": "专线编号", "dash_average_packet_loss": "平均丢包率", "dash_special_line": "专线运维摘要", "dash_configured": "此视图未配置构件！", "dash_interrupt_alarm": "中断告警", "dash_deterioration_alarm": "劣化告警", "dash_prompt_alarm": "提示告警", "dash_no_content": "暂无内容", "dash_quality_nodes": "上周质差节点数TOPN", "dash_Special_line": "专线", "dash_Special_line_name": "专线名称", "dash_order_generation_time": "工单生成时间", "dash_faulty_link": "疑似故障链路", "dash_fault_state": "故障状态", "dash_commissioning_task": "拨测任务", "dash_device_task": "设备", "dash_commissioning_task_num": "拨测任务编号", "dash_relay": "中继", "dash_nodename": "节点名称", "dash_normal": "正常", "dash_deterioration": "劣化", "dash_interrupt": "中断", "dash_interrupt1": "中断", "dash_pause": "暂停", "dash_enable": "启用", "dash_start_up": "开启", "dash_alarm_time": "告警时间", "dash_half_hour": "半小时", "dash_fault_type": "故障类型", "dash_delay_deterioration": "时延劣化", "dash_packet_loss": "丢包劣化", "dash_routing_fluctuation": "路由波动", "dash_urgent_notice": "紧急通知", "dash_line_congestion": "专线拥塞", "dash_port_congestion": "端口拥塞", "dash_link_faulty": "疑似故障链路", "dash_far": "到目前为止", "dash_view_list": "视图列表", "dash_related": "由于浏览器相关的安全限制，声音提示已被关闭。如果你想启用声音提示，请手动启用！", "dash_anomaly": "网络异常", "dash_clear": "确定要清除这些记录吗？", "dash_select_delete": "请先选择要删除的数据", "dash_clear_successfully": "清除成功", "dash_sure": "确定要删除此线路吗", "dash_masking": "屏蔽成功", "dash_unmasking": "取消屏蔽成功", "dash_moved": "当前登录用户无法移出", "dashboard_add_component": "添加构件", "dashboard_save_set": "保存设置", "dashboard_component_group_tip": "选择分组后，只统计分析分组内的数据，不选择分组则统计分析所有数据", "dashboard_no_configured": "未配置指标", "dashboard_not_greater_than_20": "该值应为正整数且不大于20", "dashboard_integer_not_than_20": "应为正整数且不大于20", "dashboard_enter_org_name": "请输入构件名称", "dashboard_not_allowed_special": "不允许使用特殊字符", "dashboard_len_not_exceed": "长度不能超过", "dashboard_position": "位置", "dashboard_support_sheet": "，支持表格", "dashboard_support_sheet2": "，支持单行多行；", "dashboard_more": "更多", "dashboard_example_57": "例如：（5或5-7）", "dashboard_type": "类型", "dashboard_name": "名称", "dashboard_no_show_name": "显示名称", "dashboard_no_show_border": "显示边框", "dashboard_refresh_interval": "刷新间隔", "dashboard_cross_bank": "跨行", "dashboard_cross_column": "跨列", "dashboard_alarms_displayed": "显示告警条数", "dashboard_selective_topology": "选择拓扑图", "dashboard_select_map": "选择地图", "dashboard_select_province": "省/直辖市", "dashboard_object_type": "对象类型", "dashboard_one_minute": "1分钟", "dashboard_five_minutes": "5分钟", "dashboard_ten_minutes": "10分钟", "dashboard_thirty_minutes": "30分钟", "dashboard_no_refresh": "不刷新", "alarm_failure_index": "故障指标", "dashboard_select_artifact_type": "选择工件类型", "dashboard_selec_refresh_interval": "请选择刷新间隔", "dashboard_line": "行", "dashboard_column": "列", "dashboard_region": "区域", "dashboard_color": "颜色", "dashboard_line_name": "线路名称", "dashboard_task_number": "任务数量", "dashboard_ob": "对象", "dashboard_port": "端口", "dashboard_index": "指标", "dashboard_url": "URL链接", "dashboard_alarm_statistics_type": "统计维度", "dashboard_alarm_statistics_select1": "每日新增告警数", "dashboard_alarm_statistics_select2": "每日发生告警对象数", "dashboard_select_ot": "请选择对象类型", "dashboard_select_ob": "请选择对象", "dashboard_select_indicators": "请选择指标", "dashboard_first_select_ot": "请先选择对象类型", "dashboard_added_monitoring": "选择需要实时监控的对象", "dashboard_maximum_selected_300": "最多可以选择300个对象", "dashboard_graphics_metrics": "聚合图形以添加指标", "dashboard_not_same_config": "不要在相同位置配置构件", "dashboard_edit_component": "编辑构件成功", "dashboard_no_configured_add_artifacts": "尚未配置工件，请添加工件", "dashboard_whether_del_after_saving": "是否在保存后删除此构件并生效", "dashboard_add_indicator_error_tip": "聚合图形指标已存在相同的指标", "dashboard_add_graphics_metrics_error_tip": "聚合图形需要添加指标", "dashboard_delay": "时延", "dashboard_lost_packet": "丢包", "dashboard_availability": "可用率", "dashboard_excellent_rate": "优良率", "dashboard_upstream": "上行流速", "dashboard_down": "下行流速", "dashboard_uplink": "上行带宽利用率", "dashboard_downstream": "下行带宽利用率", "dashboard_inlet_velocity": "入流速", "dashboard_outflow_velocity": "出流速", "dashboard_inflow_rate": "入利用率", "dashboard_outflow_rate": "出利用率", "default_manager_enter_password": "请输入密码", "default_manager_switchboard": "交换机", "default_manager_router": "路由器", "default_manager_Linux_server": "Linux服务器", "default_manager_camera": "摄像头", "default_manager_routing/switching": "路由/交换", "default_manager_printer": "打印机", "default_manager_PC": "PC终端", "report_select_type": "请选择报告类型", "report_statistics_time": "报告统计时间", "report_please_provider": "请输入提供方", "report_name_len_50": "报告提供方长度不能超过50", "report_des_leng_200": "描述长度不能超过200", "report_provider_len_50": "报告提供方长度不能超过50", "discover_msg_ip_format": "IP段格式不正确（前者IP至后者IP）", "discover_msg_delete": "<p>确定要删除这些任务吗？</p>", "discover_msg_enable": "<p>确定要启用这些任务吗？</p>", "discover_msg_disable": "<p>确定要禁用这些任务吗？</p>", "discover_msg_add_10": "最多添加10个时间段", "discover_rule_name": "规则名称", "discover_rule_1": "规则名称、IP范围、采集器", "discover_pd_rule_name": "请填写规则名称", "discover_pd_keyword": "规则名称，IP范围和采集器", "discover_pd_rule_keyword": "规则名称、开始IP或结束IP、采集器", "discover_has_monitor": "是否已监测", "discover_has_monitor1": "是否被监控", "discover_uplink_ip": "上行IP", "discover_gether_name": "采集器", "discover_ip_rang": "IP地址范围", "discover_date_lang": "请选择时间段", "discover_select_type": "选择类型", "discover_device": "发现设备", "discover_device_type": "设备类型", "discover_device_type_showCode": "类型编码", "discover_device_type_tips": "设备类型存在空格", "discover_device_factory_tips": "设备厂商存在空格", "discover_device_factory_showCode": "设备厂商编码", "discover_time": "最后扫描时间", "discover_time_note": "注释：时间段最多支持10个", "discover_select_device_type": "请选择设备类型", "discover_rule": "发现规则", "discover_last_time": "上次扫描时间", "discover_maintain_level": "运维等级", "discover_level": "等级", "discover_select_maintain_level": "请选择运维等级", "discover_select_inter_time": "请填写时间间隔", "discover_add_group": "添加到分组", "discover_msg_mutime": "多个时间段交叉，请重新选择", "discover_create_task": "创建任务", "discover_donot_create_task": "不创建任务", "discover_search_keyword": "IP、采集器", "discover_dis_inter": "发现间隔", "discover_discovery_rule": "发现规则", "discover_dial_protocol": "拨号测试协议", "discover_port": "端口", "discover_discovery_time": "发现时间", "discover_equipment_type": "设备类型", "discover_discovery_status": "发现状态", "discover_dial_probe": "映射探针：", "discover_upload_attachment": "上传附件", "discover_select_upload_file": "上传", "discover_duplicate_term": "重复项", "discover_cannot_be_null": "不能为空", "discover_character_length": "字符长度不能超过200个字符", "discover_save_failure": "保存失败", "discover_source_device": "源设备", "discover_connected_device": "从源到设备连接的设备", "analyse_spec_name": "专线名称", "analyse_spec_code": "特殊代码", "gether_name": "采集器名称", "gether_type": "采集器类型", "gether_authorized_org": "授权机构", "gether_select_type": "请选择采集器类型", "gether_select_collector": "请选择采集器", "gether_select_snmp": "请选择SNMP版本", "gether_fill_snmp_port": "请填写SNMP端口", "task_name": "任务名称", "task_status": "任务状态", "task_type": "运维等级", "task_normal_dial_test": "普通拨测", "task_pd_dial_type": "请选择拨测类型", "task_dial_type": "拨测类型", "licence_num_start": "提示：您当前还剩余许可证", "licence_num_end": "个，上传记录在附件中将自动截取，不再通知！", "wifi_search_dete_time": "检测时间", "wifi_search_keywrod": "特征/房间号/SSID", "wifi_report_time": "上报时间", "wifi_features": "特征（和房间号）", "wifi_band": "频段", "wifi_bandwidth": "频宽", "wifi_agreement": "协议", "wifi_signal_strength": "信号强度", "wifi_neg_rate": "协商连接速率", "wifi_down_rate": "下载速率", "wifi_up_rate": "上行速率", "wifi_internal_down_rate": "内网下载速率", "wifi_internal_up_rate": "内网上行速率", "wifi_internal_speed": "内网测速", "wifi_gateway_res": "网关响应", "wifi_problem_item": "问题项", "wifi_find_problem": "发现问题", "wifi_test": "Wifi测试", "wifi_grade_proportion": "每个位置经验级别的比例", "wifi_detail": "详情", "wifi_loading_failed": "图片加载失败", "wifi_access_wifi": "接入WIFI", "wifi_download": "下载", "wifi_highest": "最高", "wifi_average": "平均", "wifi_bad": "差", "wifi_bad_t": "差", "wifi_bad1": "差", "wifi_tiktok": "抖音", "wifi_video_conference": "视频会议", "wifi_high_video_conference": "高清视频会议", "wifi_do_office_work": "办公", "wifi_problem_introduction": "问题介绍", "wifi_solution_suggestions": "解决建议", "wifi_chat": "聊天", "wifi_monitor": "监控", "wifi_4K_monitor": "4K监控", "wifi_signal_interference": "信号干扰", "wifi_connection_rate": "连接速率", "wifi_internal_network_response": "内网响应", "wifi_encryption_protocol": "加密协议", "wifi_illustrate": "说明", "wifi_advise": "建议", "wifi_good": "优", "wifi_good1": "优", "wifi_gateway_delay": "网关延时（压力）", "wifi_channel_width": "信道带宽", "wifi_channel_frequency": "信道频率", "wifi_frequency_band": "频段", "wifi_channel": "信道", "wifi_devices": "网上设备数量", "wifi_fine": "优秀", "wifi_fine1": "良", "wifi_centre": "中等", "wifi_centre1": "中", "wifi_order": "次", "wifi_proportion_tip": "各点位体验等级占比", "spec_time_granularity": "时间粒度", "spec_line_code": "专线编号", "spec_line_relationship": "主备关系", "spec_acc_number": "关联专线编号", "spec_line_logotype": "标识", "spec_line_duplic": "是否有备用线路", "spec_no_duplicates": "暂无错误项", "spec_fail": "设备错误项", "spec_error_file_name": "专线错误数据", "spec_no_valid": "无有效数据，请重新编辑并导入", "spec_identification": "标识", "spec_line_uplink_broad": "上行带宽", "spec_line_down_broad": "下行带宽", "spec_a_end_device_ip": "A端设备IP", "spec_z_end_device_ip": "Z端设备IP", "spec_a_end_inter_ip": "A端互联IP", "spec_z_end_inter_ip": "Z端互联IP", "spec_indic": "指标", "spec_line": "专线", "spec_tip": "已选数据中包含正在删除的数据,请勿进行其他操作", "spec_tip_error": "数据正在删除中,请勿进行其他操作", "spec_select_line": "选择专线", "spec_indic_statis": "统计指标", "spec_date_type_1": "按月", "spec_date_type_2": "按周", "spec_date_type_3": "自定义", "spec_traffic_tip": "提示：若流量未匹配，请输入A端或Z端设备的IP地址", "spec_topo": "此操作将更新拓扑图中自动添加的线路，不涉及手动添加线路，修改后拓扑图可能会发生改变", "spec_pl_enter_line_name": "请输入专线名称", "spec_pl_enter_line_number": "请输入专线编号", "spec_search_keyword": "支持按专线编号、专线名称、Z端互联IP、Z端别名进行模糊匹配查找", "spec_export_file_name": "专线健康档案.xlsx", "report_generate_statistical": "生成统计报告", "path_routine": "常规图", "path_fiber": "纤维图", "path_qu_search_keyword": "支持源IP、目标IP、目标名称、任务名称的模糊匹配搜索", "node_qu_search_keyword": "支持源IP、链路的模糊匹配搜索", "node_ip": "源IP、链路", "msg_config_search_keyword": "支持任务编号、探针IP、目标IP等的模糊查询", "oid_search_keyword": "支持名称、OID的模糊查询", "path_hthy_search_keyword": "支持模糊匹配任务编号、IP、目标名称", "rpmger_search_keyword": "支持中继编号、中继名称、本端/对端设备IP、别名的模糊查询", "rpmger_search_keyword2": "支持中继编号、中继名称、本端/对端设备IP、本端/对端别名的模糊匹配搜索", "rphth_search_keyword": "支持中继名称、本地设备IP、本端/对端地址、对端别名的模糊匹配搜索", "rpath_search_keyword2": "模糊搜索名称、本地/对端地址、对端别名", "rpath_search_keyword3": "中继名称、本/对端地址、本/对端别名", "rpqu_search_keyword": "支持中继名称、本地设备IP、本地/对端地址、对端别名的模糊匹配搜索", "rpmanager_update": "更新", "rpmanager_basic_info": "基本信息", "rpmanager_trunk_name": "专线名称：", "rpmanager_org_name": "机构名称：", "rpmanager_org_name2": "机构名称", "rpmanager_local_addr": "本端地址：", "rpmanager_local_alias": "本端别名：", "rpmanager_local_IP": "本端设备IP：", "rpmanager_choose_peer_ip": "请选择设备IP", "rpmanager_peer_addr": "对端地址：", "rpmanager_peer_alias": "对端别名：", "rpmanager_local_interface": "本端接口地址：", "rpmanager_peer_interface": "对端接口地址：", "rpmanager_combination": "机构ID不正确，请输入12位字母组合", "rpmanager_local": "本地设备：", "rpmanager_peer": "对端设备：", "rpmanager_local_port": "本地端口号：", "rpmanager_peer_port": "对端端口号：", "rpmanager_peer_IP": "对端设备IP：", "rpmanager_peer_dev_name": "对端设备名称：", "rpmanager_peer_port_number": "对端端口号：", "rpmanager_monitoring_index": "监控指标", "rpmanager_alarm_para": "告警参数", "rpmanager_base_info_para": "基础信息", "rpmanager_frequency": "频次", "rpmanager_packet_loss": "丢包劣化生成阈值，单次命中：", "rpmanager_continuous": "连续：", "rpmanager_reach": "达到：", "rpmanager_reach1": "达到", "rpmanager_only_int": "只能输入正整数", "rpmanager_alarm_packet": "告警参数中丢包劣化阈值取值范围为1至100", "rpmanager_degree_not_0": "度数不能为0", "rpmanager_relay_coding": "编码", "rpmanager_local_dev_name": "本地设备名称", "rpmanager_local_num": "本地端口", "rpmanager_peer_IP_addr": "对端设备IP", "rpmanager_peer_IP_addr1": "对端IP", "rpmanager_peer_name": "对端设备名称", "rpmanager_peer_port_num": "对端端口", "rpmanager_started": "已启动", "rpmanager_suspended": "已暂停", "rpmanager_deleting": "删除中", "rpmanager_deleting_not_oper": "在数据删除过程中，请勿进行其他操作", "rpmanager_enter_local_addr": "请输入本端地址", "rpmanager_enter_org_name": "请输入机构名称", "rpmanager_enter_peer_addr": "请输入对端地址", "rpmanager_len_not_128": "长度不可超过128", "rpmanager_unmasking": "解除屏蔽", "rpmanager_unprompt": "取消提示", "rpmanager_sure_unmask_data": "确定要解除此数据的屏蔽吗？", "rpmanager_masking_success": "屏蔽成功", "rpmanager_success_unmasking": "解除屏蔽成功", "rpmanager_select_unmask": "请选择要解除屏蔽的数据", "rpmanager_selected_data_select_new": "所选数据中含有正在删除的数据，请重新选择", "rpmanager_select_data_set": "请选择要设置的数据", "rpmanager_successful_set": "设置成功", "rpmanager_select_mask": "请选择要屏蔽的数据", "rpmanager_mask_prompt": "屏蔽提示", "rpmanager_block_data": "确定要屏蔽这个数据吗？", "rpmanager_sure_del": "<p>请确保删除", "rpmanager_these": "这些", "rpmanager_article": "篇文章", "rpmanager_recorded": "是否已记录？</p>", "rpmanager_deleted_not_repeat": "所选数据中包含正在删除的数据，请勿重复此操作", "rpmanager_data_loading_failure": "数据加载故障", "rpmanager_alarm_integers": "告警参数只能为正整数", "rpmanager_consecutive_num_not_0": "连续次数不能为0", "rpmanager_alarm_loss_100": "告警参数的丢包劣化阈值范围为1至100", "rpmanager_addr_combination_exists": "对端设备IP地址组合已存在", "rpmanager_trunk_modified_success": "专线信息修改成功", "rpmanager_select_upload_attachment": "请选择要上传的附件", "rpmanager_trunk_xls": "专线信息.xls", "rpmanager_trunk_xlsx": "专线信息.xlsx", "rpmanager_download_xls": "中继信息.xls", "rpmanager_download_xlsx": "中继信息.xlsx", "rpmanager_health_download_xls": "中继健康档案信息.xls", "rpmanager_health_download_xlsx": "中继健康档案信息.xlsx", "rpmanager_update_verify_tip": "对端与本端地址组合已存在", "rpmanager_interrupt_deterioration_tip": "统计时间范围内，没有发生过中断、劣化的中继数", "rpmanager_deterioration_tip": "统计时间范围内，发生过劣化的中继数", "rpmanager_interrupt_tip": "统计时间范围内，发生过中断的中继数", "rpmanager_quality_download_xls": "中继质差报表.xls", "rpmanager_quality_download_xlsx": "中继质差报表.xlsx", "rpmanager_snmp_export_trend_name": "snmp检测-", "alarm_9999": "0-9999的整数", "alarm_offline": "采集器离线", "alarm_congestion": "流量拥塞详情", "alarm_start_time": "故障开始时间", "alarm_select_first": "请先选择恢复数据", "alarm_recover": "恢复", "alarm_generation_time": "故障开始时间", "alarm_all": "全部", "alarm_l60m": "≤60分钟", "alarm_g60m": ">60分钟", "alarm_custom": "自定义", "alarm_minute": "分钟", "alarm_symptom": "故障现象", "alarm_failure_elapsed": "影响路径", "alarm_failure_duration": "故障历时", "alarm_failure_status": "故障状态", "alarm_ticket_status": "工单状态", "alarm_symptom_placeholder": "支持按照故障现象模糊查询", "network_append": "追加", "alarm_susp_failed_link": "疑似故障链路", "gether_select_province": "请选择省份", "gether_select_city": "请选择城市", "gether_verify_name": "请选择采集器名称", "gether_verify_name_len": "长度不可超过64", "gether_code": "采集器编号", "gether_verify_type": "请选择采集器类型", "gether_ip": "采集器IP", "gether_verify_ip": "请填写采集器IP", "gether_last_time": "最后活跃时间", "gether_level": "运维等级", "gether_tasks": "任务数量（正常/总数）", "gether_search_keyword": "名称、IP", "gether_pl_type": "请选择采集器类型", "gether_pl_status": "请选择状态", "snmp_local_alias": "本地别名", "snmp_local_device": "本地设备名称", "snmp_self_port_number": "本端端口号", "snmp_peer_device_ip": "对端设备IP", "snmp_peer_device_name": "对端设备名称", "snmp_peer_port_number": "对端端口号", "snmp_no": "编号", "snmp_code": "中继编码", "snmp_local_alias2": "本端别名", "snmp_this_end_ip": "本端设备IP", "snmp_local_dev_name": "本端设备名", "snmp_local_dev_port": "本端端口号", "snmp_name": "中继名称", "snmp_name1": "名称", "snmp_this_ip": "本地IP", "snmp_this_end_addr": "本端地址", "snmp_paired_end": "对端地址", "snmp_paired_end_alia": "对端别名", "snmp_delay_max": "时延最大值", "snmp_delay_avg": "时延平均值", "snmp_loss_max": "丢包率最大值", "snmp_loss_avg": "丢包率平均值", "snmp_inflow_max": "入流速最大值", "snmp_inflow_avg": "入流速平均值", "snmp_outflow_max": "出流速最大值", "snmp_outflow_avg": "出流速平均值", "snmp_device_man": "设备商", "snmp_manufacturer": "厂商", "snmp_device_ip": "设备IP", "snmp_task_device_model": "设备型号", "snmp_task_op_level": "操作级别", "snmp_task_code": "任务编号", "snmp_task_device_name": "设备名称", "snmp_task_device_ip": "设备IP", "snmp_pl_man": "请选择", "snmp_pl_model": "选择型号", "group_name": "分组名称", "group_remove": "删除", "group_search_keyword": "支持按名称进行模糊查询", "group_address_IP": "目标IP地址：", "group_target_address": "目标地址：", "group_target_name": "，目标名称：", "group_ClassB": "B类遍历", "group_ClassA": "A类遍历", "group_matching": "按采集器、设备名称和设备IP地址进行模糊匹配", "group_matching2": "按任务编号、探针IP和目标IP进行模糊匹配查询", "group_matching3": "按任务编号、探针IP和目标IP进行模糊匹配查询", "group_removed": "删除成功", "group_confirm_add": "<p>确认是否添加？</p>", "group_check_data": "请核对数据", "group_want_remove": "<p>是否要进行删除？</p>", "group_description_content": "描述内容", "group_length_exceed": "长度不能超过", "group_add": "添加", "group_select_want_remove": "请选择要删除的数据", "group_add_successfully": "创建成功", "group_delete_successfully": "删除成功", "group_select_delete": "请选择要删除的数据", "snmp_relay_tasks": "中继任务数量", "snmp_probe_tasks": "探针任务数量", "interface_import_success": "导入成功", "interface_import_failure": "导入失败", "interface_operation_again": "已经上传过一次，请返回上一步重新操作", "interface_address": "设备IP地址，设备名称，接口名称，接口IP地址", "interface_name": "接口名称", "interface_status": "接口状态", "interface_dial_probe": "映射探针", "interface_ip": "接口IP", "interface_enter_ip": "请输入设备IP", "interface_enter_name": "请输入接口名称", "interface_enter_interface": "请输入接口的IP地址", "interface_no_event": "没有重复/异常", "interface_enter_address": "输入正确的IP地址", "interface_enter_bandwidth": "请输入正确的带宽", "interface_mapping_dial": "映射拨测探针", "interface_enter_device": "请输入设备名称", "interface_value_maximum": "该值最多包含50个字符", "interface_select_probe": "选择映射拨测探针", "interface_select_manipulate": "请选择要操作的数据", "interface_deactivated_success": "成功停用", "interface_select_export": "请选择需要导出的数据", "interface_information_management": "接口管理信息", "interface_all_information_management": "全部接口管理信息", "interface_update_success": "更新成功", "interface_select_attachment": "请选择要更新的附件", "interface_log_details": "日志详情", "interface_select_delete": "选择要删除的数据", "comm_delete": "删除", "comm_disabled": "停用", "comm_loop": "跳出循环", "comm_home": "主动式网络性能透视系统", "comm_deleted_success": "删除成功", "comm_deleted_failed": "删除失败", "comm_name_empty": "名称不能为空", "comm_name_exists": "名称已存在", "logback_enter_ip": "输入IP地址", "logback_fill_userName": "请填写用户名", "logback_fill_password": "请填写密码", "logback_enter_path": "输入备份路径", "message_delete": "确定要删除所选的消息记录吗？", "logback_select_content": "请选择内容", "logback_select": "选择", "logback_delete": "删除", "logback_selective_grouping": "选择性分组", "logback_supports_query": "支持按组名查询", "logback_select_dial": "选择拨测任务", "logback_select_relay": "选择中继任务", "logback_equal": "等于", "logback_no_equal": "不等于", "logback_first": "一级", "logback_second": "二级", "logback_tertiary": "三级", "logback_overwrite": "所选数据在条件中已存在，是否覆盖？", "alarm_fault_suspected": "疑似故障链路IP", "alarm_disclaimer": "可用率（免责）", "alarm_fault_list": "故障清单", "alarm_fault_type": "故障类型", "alarm_order_status": "工单状态", "alarm_failure_boundary": "故障定界", "alarm_network_fault": "网络故障", "alarm_urgent_notice": "紧急通知", "alarm_route_undulate": "路由波动", "alarm_system_alarm": "系统告警", "alarm_special_congestion": "流量拥塞", "alarm_fault_duration": "故障历时", "alarm_index": "指标", "alarm_resolved": "已解决", "alarm_content": "请输入内容", "alarm_index_trend": "指标趋势图", "alarm_historical": "历史处理记录", "alarm_settled": "已解决", "alarm_off_line": "离线", "alarm_fault_index": "故障指标", "alarm_load": "加载中", "alarm_dial_probe": "拨测探针", "alarm_SNMP_collector": "采集代理", "alarm_relay_probe": "中继探针", "alarm_High-frequency_probe": "高频探针", "alarm_topology": "路由拓扑", "alarm_addressIP": "本端设备IP", "alarm_address": "接口地址", "alarm_device_name": "设备名称", "alarm_port_num": "端口号", "alarm_alias": "别名", "alarm_no_data": "暂无数据", "view_component_type_1": "时延", "view_component_type_2": "时延/丢包率", "view_component_type_3": "可用率", "view_component_type_4": "优良率", "view_component_type_5": "入口速率", "view_component_type_6": "出口速率", "view_component_type_7": "入利用率", "view_component_type_8": "出利用率", "view_obj_num": "对象数量不能超过", "view_save_succ": "保存构件成功", "view_save_failed": "保存构件失败", "view_add_title": "新建构件", "view_Leased_type_1": "时延", "view_Leased_type_2": "丢包率", "view_Leased_type_3": "入流速", "view_Leased_type_4": "速率", "view_Leased_type_5": "出流速", "view_Leased_type_6": "入利用率", "view_Leased_type_7": "可用率", "view_Leased_type_8": "出利用率", "view_Leased_type_9": "可用率", "view_Leased_type_10": "优良率", "view_dial_type_1": "时延", "view_dial_type_2": "丢包率", "view_dial_type_3": "可用率", "view_dial_type_4": "优良率", "view_snmp_type_1": "时延", "view_snmp_type_2": "丢包率", "view_snmp_type_3": "可用率", "view_snmp_type_4": "优良率", "view_snmp_type_5": "入流速", "view_snmp_type_6": "出流速", "view_indi_10": "最多十个指标", "view_max_comp_30": "最多支持30个构件", "view_select_object": "选择对象", "singlesign_advised": "建议使用360、Chrom或Firefox", "singlesign_recommended": "推荐分辨率：1920*1080", "singlesign_login_error": "登录错误，请检查网络连接！", "singlesign_name_contains": "账号名称包含2到18个字符", "singlesign_name_word": "账号名称只能为字母、数字、下划线", "singlesign_empty": "账号名称不能为空", "singlesign_password": "密码长度至少为8个字符", "singlesign_two": "密码包含至少两种字母、数字或符号的组合且不少于八个字符", "singlesign_password_empty": "账号密码不能为空", "singlesign_verification": "验证码包含4到6个字符", "singlesign_verification_number": "验证码仅能为数字/字母", "singlesign_verification_empty": "验证码不能为空", "snmpoid_oid": "OID", "snmpoid_equipment": "厂商", "snmpoid_equipment_model": "设备型号", "snmpoid_model": "型号", "snmpoid_type": "类型", "snmpoid_select_type": "请选择类型", "snmpoid_all_OID": "全部OID", "snmpoid_common_OID": "通用OID", "snmpoid_cisco": "思科", "snmpoid_huawei": "华为", "snmpoid_huasan": "华三", "snmpoid_ruijie": "锐捷", "snmpoid_maipu": "迈普", "snmpoid_shenxinfu": "深信服", "snmpoid_f5": "F5", "snmpoid_ss": "山石", "snmpoid_trx": "天融信", "snmpoid_fill_name": "请填写名称", "snmpoid_fill_OID": "请填写OID", "snmpoid_select_manufacturer": "选择设备厂商", "snmpoid_device_model": "输入设备型号", "snmpoid_port": "请输入本地端口号", "snmpoid_Note": "备注最多包含64个字符", "snmpoid_error_details": "错误详情", "snmpoid_device_merchant": "设备厂商", "snmpoid_device_merchant_select": "请选择设备厂商", "snmpoid_equipment_model_select": "请选择设备型号", "snmpoid_search_device_merchant": "支持按名称、OID等进行模糊查询", "snmpoid_error_oid": "内置的OID不能编辑", "snmpoid_error_length_oid": "长度不能超过64", "snmpoid_error_remark": "备注长度不能超过64", "snmpoid_all_oid": "所有OID", "snmpoid_public_oid": "公共OID", "device_discovery": "设备发现管理", "device_discovery_collect_or_not": "是否采集", "device_discovery_probe_IP": "探测IP", "device_discovery_at_least_one_indicator": "请至少保留一条指标", "device_discovery_most_indicator": "已达到最多指标数", "device_discovery_system_not_support": "系统暂不支持监测该厂商的设备", "device_discovery_error_select": "请勾选要创建任务的数据", "device_discovery_warning_system_not_support": "勾选的数据中有系统暂不支持监测该厂商的设备", "device_discovery_warning_same_indicator": "存在相同指标", "device_discovery_system_support": "系统暂时仅支持为设备厂商（华为、迈普、思科、锐捷）创建采集任务，其它设备厂商暂不支持！", "device_discovery_timing_indicators": "(分：< 60，小时：< 24，天：< 7)", "device_discovery_gether_level_select": "请选择运维等级", "device_discovery_map_select": "选择映射到拨测探针", "device_discovery_same_IP": "选择的拨测探针探测到的节点与采集到的端口IP相同时，则认为业务路径经过该设备！", "device_discovery_dial_test_protocol": "拨测协议", "device_discovery_whether_monitored": "是否已监测", "device_discovery_monitored": "已监测", "device_discovery_not_monitored": "未监测", "device_discovery_enabled": "已启动", "device_discovery_trunk_information": "中继信息", "device_discovery_port_indicators": "端口指标", "device_discovery_link_information": "链路信息", "device_discovery_device_information": "设备信息", "device_discovery_ARP_information": "ARP信息", "device_discovery_MAC_information": "MAC信息", "device_discovery_port_information": "端口信息", "device_discovery_port_name": "端口名称", "device_discovery_port_ip": "端口IP", "device_discovery_rule_matching": "支持按规则名称、IP范围、采集器进行模糊匹配查询", "device_discovery_choose_credentials": "请选择凭证", "device_discovery_select_credential": "选择凭证", "device_discovery_action_conf": "动作配置", "device_discovery_discover_only": "仅发现数据", "device_discovery_select": "选择发现规则执行定义的操作所需满足的条件。", "device_discovery_select_standard": "请选择标准", "device_discovery_match_value": "请输入匹配值", "device_discovery_meet_any": "满足以下任意项（或）", "device_discovery_match_all": "匹配以下所有项（与）", "device_discovery_standard": "标准", "device_discovery_condition": "指定满足上述条件时要执行的操作", "device_discovery_select_action": "请选择动作", "device_discovery_select_level": "请选择等级", "device_discovery_error_ip": "IP段不符合规范(前IP需在末IP之前)", "device_discovery_ip_range": "IP范围", "device_discovery_last_scan_time": "最后扫描时间", "device_discovery_new_task": "创建采集任务", "device_discovery_only_add_device": "仅添加设备", "device_discovery_warning_condition_add": "条件至少添加一行", "device_discovery_warning_action_add": "动作至少添加一行", "device_discovery_warning_device": "设备信息和端口信息指标选项为必选", "device_discovery_warning_validate": "验证不通过", "device_discovery_select_properties": "请选择属性", "device_discovery_fill_value": "请填写值", "device_discovery_mac_conditions": "已达到最多条件数", "device_discovery_select_credentials": "支持按凭证名称进行模糊匹配查询", "device_discovery_credentials": "凭证名称", "device_discovery_credentials_type": "请选择凭证类型", "device_discovery_fill_credentials": "请填写凭证名称", "device_discovery_select_security": "请选择安全级别", "device_discovery_security1": "无验证、无加密", "device_discovery_security2": "验证、无加密", "device_discovery_security3": "验证、加密", "device_discovery_verify_password": "验证密码", "device_discovery_verify_protocol": "HASH算法", "device_discovery_verify_password2": "HASH密码", "device_discovery_select_protocol": "请选择HASH算法", "device_discovery_org_not_null": "组织不能为空", "device_discovery_snmp_not_null": "snmp端口不能为空", "device_discovery_create_success": "新建成功", "device_discovery_select_del": "请选择删除内容", "device_discovery_verify_pass": "验证密码不能为空", "device_discovery_ip_device": "设备IP、设备名称、接口IP", "device_discovery_ip": "接口IP", "device_discovery_correct_ip": "请输入正确IP", "device_discovery_map_probe": "映射拨测探针", "device_discovery_max_char": "最长支持50个字符", "device_discovery_fill_ip": "请输入接口IP", "device_discovery_select_probe": "请选择映射拨测探针", "device_discovery_enable_ip": "<p>您确认要启用选中的接口信息吗？</p>", "device_discovery_disable_ip": "<p>您确认要停用选中的接口信息吗？</p>", "device_discovery_del_device": "<p>您确定要删除这些设备吗？</p>", "device_discovery_del_device_factory": "<p>您确定要删除这些设备厂商吗？</p>", "device_discovery_del_device_type": "<p>您确定要删除这些设备类型吗？</p>", "device_discovery_del_device_model": "<p>您确定要删除这些设备型号吗？</p>", "device_discovery_enable": "启用成功", "device_discovery_disable": "停用成功", "device_discovery_associated_excel": "关联显示信息.xlsx", "device_discovery_all_associated_excel": "全部关联显示信息.xls", "device_discovery_all_associated_excel2": "全部关联显示信息.xlsx", "device_discovery_del_ip_info": "<p>您确认要删除选中的接口信息吗？</p>", "device_discovery_del_duplicates": "删除重复项", "device_discovery_template": "关联显示模板.xlsx", "device_discovery_duplicate_interface": "接口信息重复项.xlsx", "device_all_info": "全部设备信息.xlsx", "device_import_tmp": "设备导入模板.xlsx", "device_info": "设备信息.xlsx", "device_error": "设备错误项.xlsx", "device_discovery_no_repeat_exception": "暂无重复/异常项", "device_code": "设备编码", "device_name_alias": "设备名称", "device_code_fill": "请输入设备编码", "device_alias": "设备别名", "device_manage_ip": "管理IP", "device_number_of_ports": "端口数", "device_interface_info": "接口信息", "device_network_neighbor": "网络邻居", "device_core_switch": "核心交换机", "device_interface_index": "接口索引", "device_running_state": "运行状态", "device_manage_state": "管理状态", "device_interface_type": "接口类型", "device_interface_bandwidth": "接口带宽", "device_incoming_utilization": "入带宽利用率", "device_outgoing_utilization": "出带宽利用率", "device_receive_packet_loss": "接收丢包", "device_sending_packet_loss": "发送丢包", "device_receive_error": "接收错误", "device_sending_error": "发送错误", "device_local_port": "本端设备端口", "device_local_mac": "本端设备MAC", "device_local_ip": "本端设备IP", "device_peer_device_model": "对端设备型号", "device_peer_device": "对端设备名称", "device_peer_port": "对端设备端口", "device_peer_mac": "对端设备MAC", "device_peer_ip": "对端设备IP", "device_port_index": "端口索引", "device_current_value": "当前值", "device_packs_number": "包数", "device_select_device": "选择设备：", "device_select_device2": "选择设备", "device_task_code": "任务编码", "device_trunk_line": "中继线路数", "device_acquisition_task": "采集任务.xls", "device_acquisition_task2": "采集任务.xlsx", "device_all_acquisition_task": "全部采集任务.xls", "device_all_acquisition_task2": "全部采集任务.xlsx", "device_add_device_must": "请至少添加一个设备！", "device_startup_success": "启动成功！", "device_alarm_update": "告警参数批量修改成功", "device_alarm_update_fail": "告警参数批量修改失败", "device_portInfo_limit_time": "时间跨度不得大于90天", "device_monitor_state": "状态", "device_enter_ip": "请输入正确的IP", "device_enter_mac": "请输入正确的MAC", "device_model_fuzzy_search": "支持按设备型号,sysObjectID进行模糊匹配查询", "device_factory_fuzzy_search": "支持按设备厂商,OID进行模糊匹配查询", "device_type_fuzzy_search": "支持按设备类型进行模糊匹配查询", "remarks_length": "长度不能超过", "device_fill_model": "请填写设备型号", "device_fill_factory": "请填写设备厂商", "device_fill_type": "请填写设备类型", "device_fill_sysObjectID_tips": "请输入正确的sysObjectID", "device_fill_oid": "请输入正确的OID", "device_fill_type_code": "请填写设备类型编码", "device_run_record": "设备重启记录", "device_restart_time": "设备重启时间", "device_collection_time": "本次采集时间", "device_collection_value": "本次采集值", "device_prev_collection_time": "上次采集时间", "device_prev_collection_value": "上次采集值", "showCode_uppercase": "类型编码为大写字母", "model_fill": "请填写sysObjectID", "model_uppercase": "厂商编码为大写字母", "device_fill_sysObjectID": "请填写sysObjectID", "select_attachment": "选择附件", "data_validation": "数据校验", "export_error": "导出错误项", "org_num": "编号", "org_name": "机构名称", "org_alias": "机构别名", "org_type": "机构类型", "org_id": "ID", "org_parent": "上级机构", "org_province": "所属省份", "org_city": "所属城市", "org_county": "所属区县", "org_leading": "负责人", "org_leading_phone": "负责人电话", "org_cont_name": "联系人姓名", "org_cont_info": "联系人信息", "org_technical": "技术人员", "org_phone": "技术人员电话", "org_tech_a": "技术人员A", "org_tech_aphone": "技术人员电话A", "org_tech_z": "技术人员Z", "org_tech_zphone": "技术人员电话Z", "org_phone_format": "支持输入多个手机,英文逗号分隔", "org_name_placeholder": "请输入机构名称", "org_alias_placeholder": "请输入机构别名", "org_id_placeholder": "请输入机构ID", "org_parent_placeholder": "请选择上级机构", "org_user_name_placeholder": "请输入姓名", "org_select_org": "请选择机构", "org_st_key": "支持按名称、账户、电话查询", "user_full_name": "姓名", "but_exit_full_screen": "退出全屏", "user_sex": "性别", "user_sex_man": "男", "user_sex_woman": "女", "user_dingTalk": "电话（钉钉）", "user_cell_phone": "手机", "user_status": "状态", "user_number": "用户数", "user_account": "账号", "user_pwd_expri_date": "密码有效期", "user_pwd_expri_date_1": "1个月", "user_pwd_expri_date_3": "3个月", "user_pwd_expri_date_6": "6个月", "user_pwd_expri_date_12": "12个月", "user_v_pwd_long": "密码长度至少为8个字符", "user_v_old_pass": "新密码与旧密码相同", "user_v_limt_len": "超过限制长度", "user_v_pwd_two_no": "两次密码输入不一致", "user_v_pwd_format": "密码是任意两种或更多的字母、数字和符号的组合，长度不少于8位", "user_v_pwd_len_32": "长度不能超过32位", "user_v_pwd_enter_old": "请输入旧密码", "user_v_phone_format": "电话号码格式不正确", "user_v_mailbox_format": "邮箱格式不正确", "user_v_mailbox_empty": "邮箱格式不正确", "user_v_account_exists": "该账号已存在", "user_edit_title_top": "修改用户", "server_system": "NetDem系统", "server_shallow": "S", "server_deep": "D", "server_change_password": "修改密码", "server_exit": "退出", "server_current_position:": "当前位置：", "server_reminder": "温馨提示", "server_sure_exit": "确定退出当前账户？", "server_old_password": "旧密码：", "server_prompt_message": "提示信息", "server_node_type": "节点类型", "server_input": "输入占位符", "server_enter_password": "请输入企业登录密码", "server_different": "两次输入的密码不一致", "server_trunk_monitoring": "专线监测", "server_home_page": "首页", "server_home_last_page": "末页", "server_last_page": "上一页", "server_total": "共", "server_strip": "条", "server_page": "页", "server_faults": "故障列表", "server_browser": "由于浏览器相关安全限制，声音报警已被关闭。如果您想启用声音提醒，请手动开启！", "server_turn_on_sound": "开启声音", "server_turn_off_sound": "关闭声音", "server_cannot_played": "无法播放音频", "server_permission": "如果菜单权限未配置，请联系管理员进行权限配置", "server_changed_successfully": "密码修改成功", "server_least": "密码长度必须至少为八个字符！", "server_storage1": "存储", "server_view_details": "查看详情", "server_correct_ip": "请输入正确的ip段", "server_password_30": "密码长度不得大于30位", "server_password_8": "密码长度不得小于8位", "server_positive_integers": "端口只能为正整数", "server_enter_path": "请输入正确的路径", "server_view": "查看", "server_partition": "分区", "server_sure_delete": "确定要删除这些数据吗？", "server_trunk": "专线信息修改成功", "server_query_failure": "查询失败", "server_China_Mobile": "中国移动", "server_China_Unicom": "中国联通", "server_China_Telecom": "中国电信", "server_China_Broadcasting": "中国广电", "server_China_other": "其他", "server_China_other2": "其它", "server_affected": "影响链路数", "server_closed_loop": "已闭环", "server_process": "处理中", "server_unprocessed": "未处理", "server_fluctuation": "路由波动详情", "server_select_Restore": "请先选择还原数据", "server_recover": "确定要恢复这些记录吗？", "server_recovery_success": "恢复成功", "server_flow_rate": "流速", "specinfo_system": "使用系统模板", "specinfo_ctd": "确认删除", "specinfo_ctd_tip": "确定要删除这些数据吗?", "specinfo_these": "这些", "specinfo_those": "那些", "specinfo_isr": "是否已记录？", "specinfo_city_A": "A终端城市", "specinfo_County_A": "A终端县区", "specinfo_province_Z": "Z终端省份", "specinfo_city_Z": "Z终端城市", "specinfo_County_Z": "Z终端县区", "specinfo_carrier_mapping": "运营商对应关系", "specinfo_relationship": "主备关系对应关系", "specinfo_Z_ip": "Z端IP对应关系", "specinfo_A_ip": "A端IP对应关系", "specinfo_province_A": "A终端省份", "specinfo_enter_number": "请输入关联专线编号", "specinfo_identification_task": "标识任务", "specinfo_enter_identification_task": "请输入标识任务", "specinfo_identification_trunk": "标识中继", "specinfo_enter_identification_trunk": "请输入标识中继", "specinfo_select_info": "请选择设备信息", "specinfo_flow_value": "流量取值", "specinfo_select_interface": "请选择接口信息", "specinfo_flow_direction": "流量出方向", "specinfo_enter_correct_broadband": "请输入正确的宽带", "specinfo_line_exists": "该专线号已存在", "specinfo_format_combination": "字母和数字的组合", "specinfo_org_first": "请选择所属机构", "specinfo_enter_line_num": "请输入专线号码", "specinfo_select_org": "请先选择您的机构", "specinfo_primary_use": "主用", "specinfo_standby": "备用", "specinfo_load_balancing": "负载均衡", "specinfo_no": "无", "specinfo_optical_fiber": "光纤", "specinfo_frame_relay": "帧中继", "specinfo_enter_line_name": "请输入专线名称", "specinfo_line_number": "请输入关联专线号", "specinfo_select_branch_office": "请选择分公司", "specinfo_select_carrier": "请选择运营商", "specinfo_enter_Z_IP": "请输入Z端IP地址", "specinfo_select_active_standby": "选择主备关系", "specinfo_enter_associated_number": "请输入关联专线号码", "specinfo_enter_IP_A": "请输入A端IP地址", "specinfo_line_associated_others": "该专线编号已关联其他", "specinfo_relay": "（中继）", "specinfo_dial": "（拨测）", "export_quality_report": "链路质差分析", "export_template": "接口管理模板", "export_data_template": "机构数据模板", "export_line_quality_report": "专线质差报表", "export_line_data": "专线数据", "export_health": "中继健康记录", "export_duplicate": "任务的重复项已汇总", "export_organization": "机构数据的重复项", "export_trunk": "专线质量差异报告", "export_dial": "拨测质差分析", "export_routes": "物理拓扑上的路由列表", "export_verification": "可达性验证结果集", "export_task": "中继任务模板", "export_policy": "任务策略重复录入", "export_collection": "采集任务模板", "export_topology": "导入物理拓扑线路模板", "export_probe": "探针任务模板", "role_name": "角色名称", "role_type": "角色类型", "role_status": "角色状态", "role_list": "角色管理列表", "role_number_of_users": "用户数量", "role_peop_mgt": "SM", "role_perm_view": "权限查看", "role_fun_action_domain": "功能动作域", "role_fun_list": "功能列表", "role_permis_scope": "权限范围", "role_step_1": "步骤1：定义角色名称", "role_step_2": "步骤2：配置权限", "role_already": "该角色名称已被占用", "role_all_scopes": "所有范围（所属组织及下属组织的数据权限）", "role_org_scopes": "组织范围（所属组织的数据权限）", "role_pd_name": "请输入角色名称", "role_pd_type": "请选择", "role_pd_status": "请选择角色状态", "role_pd_perm_rang": "请选择权限范围", "role_type_1": "维护角色", "role_type_2": "管理员角色", "role_type_3": "普通角色", "role_len_rang": "长度不能超过", "role_auth_mgt": "RM", "role_activate": "激活中...", "role_freeze": "执行冻结中...", "role_reselect": "所选数据中包含已激活的数据，请重新选择", "role_frozen_res": "所选数据中包含已冻结的数据，请重新选择", "role_rdl": "删除的功能列表:", "role_afl": "新增的功能列表:", "role_se_ale": "所选用户已存在", "role_acplre": "账号，重新选择", "role_per_not": "人员未变化", "role_role": "角色", "role_pechfo": "，人员变更如下:", "role_re_per": "减少人员:", "role_ad_per": "新增人员:", "role_system_freeze_tip": "系统内置角色不能冻结", "role_system_activate_tip": "系统内置角色不能激活", "role_system_delete_tip": "系统内置角色不能删除", "message_tpl_name": "消息模板名称", "message_content": "消息内容", "message_type": "消息类型", "message_alerm_type": "告警类型", "message_alerm_select_type": "请选择告警类型", "message_tpl_type": "模板类型", "message_create_date": "创建日期", "message_push_to": "推送至", "message_push_way": "推送方式", "message_silence": "沉默周期", "message_silence_cycle": "分钟 在第一次触发告警通知后多久时间内,如果有相同类型的告警,则不会再推送告警通知", "message_mail_service_name": "邮件服务名", "message_server_and_ip": "服务器名/IP地址", "message_email": "邮箱地址", "message_email_user": "邮箱用户名", "message_email_pwd": "邮箱密码", "message_sender": "发件人", "message_server_port": "端口", "message_select_person": "请选择要推送人员", "message_select_push": "选择推送方式", "message_exceed_240": "内容不能超过240个字符", "message_or": "'或'只允许在计算式的最后位置，例如允许A和B和C或D，但不允许A和B或C和D", "message_phone_number": "电话号码", "message_reminder": "友情提醒", "message_whether_delete": "是否删除选中的信息", "message_network_exception": "网络异常", "message_pd_select_alarm": "请为对应任务选择告警内容", "message_pd_or_mode": "或模式只能在最后的位置", "message_pd_mul_ide": "存在多个相同的条件", "message_alarm_content": "为任务选择告警内容", "message_alarm_type": "为任务选择告警类型", "message_identical": "存在多个相同的条件", "message_mutually": "如果存在互斥条件，请重新配置", "message_configuration_10": "最多支持10个配置条件", "message_add_success": "创建成功", "message_network_failure": "网络故障", "message_overwritten": "所选人员已存在于列表中，是否覆盖？", "message_application_name": "应用名称", "message_test_phone_number": "测试手机号码", "message_enter_test_phone_number": "请输入测试手机号码", "message_testing": "测试中", "message_test": "测试", "message_format_incorrect": "手机号码格式不正确", "message_receiving_phone": "请输入测试接收手机号码", "message_select_data": "请先选择要删除的数据", "message_enter_correct": "请输入正确的服务器名称/IP", "message_mailbox_incorrect": "邮箱格式不正确", "message_enter_correct_email": "请输入正确的电子邮件地址", "message_enter_port": "请输入端口号", "message_email_password": "请输入电子邮件密码", "message_interface_type": "接口类型：", "message_SMS": "短信URL网关提供商。不包括URL参数。例如：http://www.smsserver.com/sendsms", "message_URL": "URL参数：", "message_server_ip": "服务器IP：", "message_server_port1": "服务器端口：", "message_user_name": "用户名：", "message_password": "密码：", "message_Intensity": "强度：", "message_push_mode": "推送方式：", "message_password_confirmation": "密码确认：", "message_password_again": "请再次输入密码：", "message_password_contains": "密码至少包含两种字母、数字和符号的组合，并且不少于八个字符", "message_password_consist": "密码为字母、数字、符号任意两种以上组合，不小于8位", "message_pd_mulexc_cond": "存在互斥条件，请重新配置", "message_pd_sup_10": "支持最多10个配置条件", "message_search_mail_keyword": "模糊匹配查询邮件、服务器、发件人姓名、电子邮件", "message_search_sms_keyword": "支持按短信服务名称进行模糊匹配查询", "message_sure": "确定要删除选中的消息模板吗？", "message_records": "请选择要删除的记录", "message_same_message": "当前机构中存在相同消息类型、告警类型和模板类型的记录", "message_inserted": "插入变量后，消息内容超过最大长度", "message_select_variables": "请选择消息变量", "message_title": "消息标题", "message_fill_title": "请填写消息标题", "message_fill_content": "请填写消息内容", "message_variable": "消息变量", "message_information": "中继信息更新模板", "message_again": "读取失败！请重新下载模板并上传", "message_empty": "您上传了一个空模板，请输入数据", "message_match": "如果模板不匹配，请选择提供的模板进行导入", "message_exception": "中继信息异常项", "message_no_abnormal": "没有异常项", "message_no_criteria": "没有符合条件的数据", "message_upload": "上传成功", "message_upload_failure": "上传失败", "message_invalid": "配置无效。起始值不能超过结束值", "message_cfg_cond_note": "默认推送所有告警信息，您可以进行高级配置告警推送条件", "message_cfg_switch_easy": "切换到简易设置", "message_cfg_switch_advanced": "切换到高级设置", "message_cfg_switch_note": "计算方式“或”只允许在计算方式的最后，例如：A和B和C或D，不允许A和B或C和D", "message_cfg_push_to": "推送给", "message_cfg_push_method": "推送方式", "message_cfg_push_person": "选择要推送的人员", "message_cfg_port": "端口", "server_name": "服务器名称", "server_ip": "服务器IP", "server_port": "服务器端口", "server_cpu": "CPU", "server_cpu_ratio": "CPU利用率", "server_cpu_ratio_mam": "CPU使用率%（最小/平均/最大）", "server_memory": "内存", "server_memory_usage": "内存使用情况", "server_memory_usage_mam": "内存使用率%（最小/平均/最大）", "server_last_upate_time": "最后更新时间", "server_access_status": "访问状态", "server_user_name": "用户名", "server_user_pwd": "用户密码", "server_storage_avg": "平均存储使用量最大分区", "server_storage_size": "存储大小", "server_storage": "存储使用情况", "server_storage_usage_mam": "存储使用率（最小/平均/最大）", "server_process_name": "进程名称", "server_process_id": "进程ID", "server_pe_user_name": "请输入用户名", "server_pe_user_pwd": "请输入用户密码", "server_pe_server_name": "请输入服务器名称", "server_pe_server_pwd": "请输入服务器密码", "server_pe_server_port": "请输入服务器端口", "server_pe_server_ip": "请输入服务器IP", "server_pe_server_describe": "请输入描述", "speed_start_test": "开始测速", "speed_stop_test": "取消测速", "speed_rest_test": "重新测试", "speed_timed_mea": "测速时长", "speed_list": "测速记录", "speed_server": "测速服务器", "speed_test_server": "测速服务器", "speed_jitter": "抖动", "speed_delay": "时延", "speed_confirm": "是否确认移出选中人员？", "speed_server_test": "请选择测速服务器和测试时长", "speed_time": "测速时间", "speed_time1": "测速时间", "speed_test_time": "速度测试时间", "speed_user": "用户", "speed_institution": "机构", "speed_method": "测速方式", "speed_local_ip": "本地IP", "speed_route_ine": "专线路径", "speed_internet_equivalent": "您的网速相当于", "speed_broadband": "宽带", "speed_reach": "达到", "speed_fall_reach": "未达到", "speed_cyberspace_standard": "网信办标准", "speed_no_matched": "未匹配到专线", "speed_search_user": "支持通过用户进行模糊查询", "speed_search_keyword": "支持PC端名称、PC端编号模糊匹配", "speed_note": "注意：定时测速需保持测速界面打开!", "speed_switch": "定时测速开关", "speed_recurrence": "重复周期", "speed_all": "全部", "speed_manual": "手动", "speed_timing": "定时", "speed_packet_loss": "丢包", "speed_download": "下载", "speed_upload": "上传", "speed_URL": "URL参数不能为空", "backup_time": "备份时间", "backup_file_size": "文件大小", "backup_path": "备份路径", "backup_path_local": "本地备份", "backup_path_remote": "远程备份", "backup_address": "备份地址", "backup_ing": "备份中", "backup_sucess": "备份成功", "backup_fail": "备份失败", "access_des_port": "目标端口", "access_dial_methond": "拨号方式", "access_path": "路径", "access_port": "目标端口", "access_mac": "目标MAC", "access_enter_mac": "请输入目标MAC", "access_response_iP": "响应IP", "access_loss": "丢包降级生成阈值，一旦达到：", "access_con_port": "已连接端口", "access_accessibility": "可达性", "access_exe_mode": "执行方式", "access_exe_mode_enter": "手动输入", "access_exe_mode_import": "批量导入", "access_note": "按顺序检查端口的可达性，如果可达，则标记为可用并停止检查其他端口。如果所有端口都不可达，则标记目的地不可达", "access_des_ip_rang": "目标IP/IP范围", "access_ip_rang": "IP范围", "access_upload": "上传附件", "access_separate": "请使用英文逗号分隔多个案例", "access_destination_ip": "目标IP", "access_destination_acc": "目标地址", "access_enter_destination_ip": "请输入目标IP，支持多个目标IP(使用逗号隔开)", "access_enter_destination_ip2": "请输入响应IP地址，支持多个响应IP地址（用逗号分隔）", "access_HIP": "H节点IP", "access_tip": "支持多个IP(使用+号隔开)", "access_tip2": "支持多个IP使用+号隔开,如:(********+********)", "access_tip3": "支持多个IP地址段，上限10个(使用+号隔开)", "access_tip4": "例如", "access_1": "任务编码、采集器、目标IP、目标名称", "access_2": "中继编码、采集器、对端地址、中继名称", "access_3": "专线编号、专线名称、A端互联IP、Z端互联IP", "access_4": "专线名称、A端互联IP、Z端互联IP", "access_5": "中继名称、本端地址、对端地址", "access_6": "任务编码、探针IP、目标IP、目标名称", "access_want_add": "请勾选要添加的数据", "lisence_org_name": "机构名称", "lisence_v_info": "版本信息", "lisence_serial_number": "序列号", "lisence_up_package": "升级包上传", "lisence_lice_info": "License信息", "lisence_cur_remainin": "当前剩余", "lisence_expir_date": "有效期", "lisence_up_lice": "更新License", "lisence_total": "总数", "lisence_purchase_date": "购买日期", "lisence_upgrade_note": "升级包上传，需要人工操作升级，暂不支持自动升级(文件格式示例: ANPM_2.3.9.20200215_RE.zip)。", "lisence_pro_info": "产品信息", "log_op_account": "操作账号", "log_op_ip": "操作IP", "log_op_object": "对象", "log_op_if": "接口地址", "log_op_result": "操作结果", "log_op_date": "日期", "log_op_time": "操作时间（ms）", "log_search_key": "支持按关键字对象、结果进行模糊查询", "snmptask_device_type": "选择设备类型", "snmptask_operation_level": "操作级别：", "snmptask_SNMP": "SNMP版本", "snmptask_SNMP_port": "SNMP端口", "snmptask_select_SNMP": "选择SNMP版本", "snmptask_select_SNMP_port": "输入SNMP端口号", "snmptask_fill_community": "请填写community", "snmptask_security_level": "安全级别", "snmptask_select_security_level": "选择安全级别", "snmptask_context_name": "上下文名称", "snmptask_fill_context_name": "请填写上下文名称", "snmptask_save_interface": "保存接口信息", "snmptask_select_map": "选择地图进行拨测", "snmptask_alarm_parameter": "告警参数设置", "snmptask_threshold": "中断生成阈值", "snmptask_time": "时间", "snmptask_alarm_setting": "告警设置", "snmptask_delay_deterioration": "时延劣化生成阈值", "snmptask_loss_deterioration": "丢包率劣化生成阈值", "snmptask_server": "服务器", "snmptask_firewall": "防火墙", "snmptask_authentication": "无认证，无加密", "snmptask_authentication2": "认证，无加密", "snmptask_authentication3": "认证，加密", "snmptask_minutes": "分钟", "snmptask_hour": "小时", "snmptask_day": "天", "snmptask_suspended": "已暂停", "snmptask_lines": "中继链路数", "snmptask_collection_task": "采集任务", "snmptask_repeat": "删除数据时不要重复此操作", "snmptask_perform_operations": "所选数据中包含您正在删除的数据。在删除数据时不要执行其他操作", "snmptask_select_task": "请选择要删除的任务", "snmptask_perform": "在删除数据时不要执行其他操作", "snmptask_same_index": "存在相同的指标", "snmptask_select_type": "请选择作业类型", "snmptask_community_empty": "community不能为空", "snmptask_security_empty": "安全级别不能为空", "snmptask_user_empty": "用户名不能为空", "snmptask_authentication_empty": "认证协议不能为空", "snmptask_password_empty": "认证密码不能为空", "snmptask_protocol_empty": "私有协议不能为空", "snmptask_encryption_empty": "加密密码不能为空", "snmptask_select_map_to_probe": "选择映射到探测的地图", "snmptask_task": "您所选的任务包含", "snmptask_task2": "个任务，请选择", "snmptask_not_started": "已暂停", "snmptask_unsuspended": "已启用", "snmptask_not_enabled": "未启用", "snmptask_not_paused": "未暂停", "snmptask_task1": "个任务", "snmptask_pause": "确定要暂停所选数据吗？", "snmptask_pause2": "<p>您确定要暂停选中的数据吗？</p>", "snmptask_activate": "确定要启用所选数据吗？", "snmptask_activate2": "<p>您确定要启用所选数据吗？</p>", "snmptask_select": "请选择要暂停/启用的数据！", "snmptask_batches": "批量修改告警参数", "snmptask_batches_failed": "批量修改告警参数失败", "snmptask_protocol": "认证协议：", "snmptask_select_protocol": "请选择认证协议", "snmptask_verification_password": "认证密码：", "snmptask_private_protocol": "加密算法：", "snmptask_select_private_protocol": "请选择私有协议", "snmptask_encryption_password": "加密密码：", "snmptask_fill_password": "请填写加密密码", "snmptask_loss": "丢包劣化生成阈值", "snmptask_once_achieved": "单次达到", "snmptask_deleting": "正在删除", "snmptask_pause_success": "暂停成功！", "snmptask_enter_device": "请填写设备名称", "specquality_time_range": "结束时间不能晚于开始时间", "specquality_quality": "专线质量报告", "specquality_select": "请选择数据", "specquality_no_data": "无数据", "specquality_incoming_velocity": "入流速", "specquality_Interruption_times": "中断次数", "specquality_exit_velocity": "出流速", "specquality_incoming_rate": "可用率(免责)", "specquality_output_rate": "优良率(免责)", "specquality_exemption": "（免责）", "specquality_velocity_flow": "流速", "specquality_utilization": "利用率（%）", "specquality_rate_exemption": "免责", "specquality_good_rate": "优良率", "specquality_availability": "可用率", "specquality_liability": "免责", "specquality_1": "源IP、目标IP、目标名称、任务名称", "specquality_availability_exemption": "可用率（免责）", "specquality_good_rate_exemption": "优良率（免责）", "specquality_average_delay": "平均时延（ms）", "specquality_maximum_delay": "最大时延（ms）", "specquality_average_loss": "平均丢包率", "specquality_incoming": "上行流速平均值", "specquality_maximum_flow": "上行流速最大值", "specquality_average_velocity": "下行流速平均值", "specquality_down": "下行", "specquality_up": "上行", "flow": "流量", "all_line_data": "全部专线数据", "specquality_maximum_flow_rate": "下行流速最大值", "specquality_time": "结束时间不得小于开始时间", "specquality_deterioration_number": "劣化次数", "specquality_deterioration_time": "劣化平均时长", "specquality_availability_rate": "可用率及良好率", "specquality_good_ratio": "良好率", "specquality_health_record": "专线健康档案", "specquality_connection_error": "网络连接错误", "specquality_no_exportable": "没有可导出的数据", "specquality_fuzzy": "支持按A端IP、Z端IP、专线名称等模糊查询", "specquality_msg1": "统计时间范围内，没有发生过中断、劣化的专线数", "specquality_msg2": "统计时间范围内，发生过劣化的专线数", "specquality_msg3": "统计时间范围内，发生过中断的专线数", "specquality_index": "统计指标", "specquality_basic_index": "基本指标", "specquality_operating_index": "运行指标", "specinfo_perspective": "统计角度", "specquality_topo_diagram": "拓扑图：", "specquality_start_time": "开始时间", "specquality_end_time": "结束时间", "specquality_recovery_time": "恢复时间", "specquality_duration": "历时", "specquality_cause": "产生原因", "specquality_recovery_cause": "恢复原因", "specquality_type": "类型", "specquality_current_state": "当前状态", "specquality_strat": "请先选择开始时间", "specquality_high_frequency": "高频次", "specquality_task_statistics": "任务统计指标", "specialmonitor_data": "无更多数据", "specialmonitor_select_line": "选择线路", "specialmonitor_matching": "支持按专线名称、A端互联IP、Z端互联IP进行模糊匹配查询", "specialmonitor_delay": "时延/丢包率", "specialmonitor_delay2": "上行流速/下行流速/上行带宽利用率/下行带宽利用率", "specialmonitor_delay3": "可用率/优良率/可用率(免责)/优良率(免责)", "specialmonitor_up_data": "最新", "specialmonitor_lost_bag": "丢包", "specialmonitor_packet_loss": "丢包率（%）", "specialmonitor_maximum": "最大", "specialmonitor_average": "平均", "specialmonitor_minimum": "最小", "specialmonitor_flow_rate": "流速", "specialmonitor_loading": "数据加载中", "specialmonitor_utilization_rate": "利用率", "user_activate": "（已激活）", "user_keyword": "支持名称、账号、手机号的模糊搜索", "user_mechanism": "机构：", "user_last": "历时", "user_name": "名称", "user_in_account": "请输入账号", "user_freeze": "（已冻结）", "user_super_admin": "超级管理员", "user_admin": "管理员", "user_ordinary": "普通用户", "user_pass_reset": "密码重置", "user_modified_successfully": "编辑成功", "user_same_in": "存在相同账号，请重新输入", "user_wait": "正在提交，请稍候", "user_pass_changed": "密码修改成功", "user_modify": "编辑用户", "user_add": "新建用户", "user_contact_admin": "无法重置密码，请联系管理员", "user_sure_freeze": "<p>确定要冻结此账号吗？</p>", "user_activate_account": "确定要激活账号吗？", "user_selected_exists": "选择的用户已存在", "user_activated_account": "激活账号", "user_reselect": "，请重新选择", "user_prompt": "激活提示", "user_sure_to": "<p>确定要", "user_sure_to1": "这些任务吗？", "user_sure_to2": "确定要启用这些任务吗？", "user_activate1": "激活", "user_dormancy": "休眠", "user_sign_out": "注销", "user_freeze1": "冻结", "user_selected": "已选中用户？</p>", "user_select_need": "选择需要", "user_user": "用户", "user_sure_delete": "<p>确定要删除选中的用户吗？</p>", "user_select_delete": "选择要删除的用户", "user_sure_delete_content": "<p>确定要删除此内容吗？</p>", "user_parent": "选择父机构", "user_organization_type": "请选择机构类型", "user_hide_password": "隐藏密码", "user_show_password": "显示密码", "user_update_password_tip": "两次输入密码不一致", "user_update_password_equal_tip": "新密码与旧密码一样", "user_update_password_validation_error_tip": "8-20位，大小写字母、数字、符号任意3种以上组合", "user_no_find_permissions": "没有配置菜单权限,请联系管理员配置权限", "user_dormancy_account": "确定休眠该账号吗?", "process_memory": "内存", "process_unit": "单位", "testspeed_select_indicators": "请选择指标", "testspeed_select_org": "请选择机构", "testspeed_select": "选择", "testspeed_sort_number": "排序号：", "testspeed_velocity_plan": "测速计划", "testspeed_return": "返回", "testspeed_PC_name": "PC端名称：", "testspeed_enter_PC_name": "请填写PC端名称", "testspeed_PC_number": "PC编号：", "testspeed_enter_PC_number": "输入PC编号", "testspeed_affiliated_org": "所属机构：", "testspeed_org": "机构", "testspeed_IP_address": "IP地址：", "testspeed_fill_remarks": "请填写备注", "testspeed_rule": "规则", "testspeed_select_server": "请选择测速服务器", "testspeed_select_time": "请选择起止日期", "testspeed_repeat_period": "请勾选至少一个重复周期", "testspeed_select_start_time": "选择开始时间", "testspeed_measurement_time": "请选择测速时间", "testspeed_period": "请选择周期", "testspeed_plan_content": "测速计划仅对在测速时间正常的PC端有效，离线的PC端不进行测速！", "testspeed_immediate": "立即测速", "testspeed_measuring_server": "测速服务器：", "testspeed_the_measurement_plan": "测速计划仅对正常测速时间的PC终端有效，离线的PC终端不会进行测速！", "testspeed_remark": "备注", "testspeed_state": "状态", "testspeed_off_line": "离线", "testspeed_selected_duplicate": "选中的时间重复，请删除重复的时间", "testspeed_selected_less_five_minutes": "选中的测速间隔不得少于五分钟", "testspeed_save_successfully": "保存成功", "testspeed_operation_succeeded_check_result": "操作成功,请稍后到测速记录中查看结果", "testspeed_operation_failure": "操作失败，", "testspeed_err_mess": "错误信息：", "testspeed_contact_admin": "请联系系统管理员", "testspeed_add_10_time": "最多添加10个测速时间", "testspeed_selected_measurement_no_less_5": "选中的测速间隔不得少于五分钟", "testspeed_selected_duplicate_time": "选中的时间重复，请删除重复的时间", "testspeed_whether_delete": "是否删除选中的信息", "testspeed_deleting": "删除中...", "testspeed_network_exception": "网络异常", "testspeed_select_want_task": "请选择需要配置的任务", "testspeed_select_task": "请选择任务", "testspeed_select_method": "请选择测速方式", "testspeed_timing": "定时", "testspeed_support": "支持按用户、本地ip、测速服务器、途经专线模糊查询", "testspeed_manual": "手动", "testspeed_bandwidth": "达到专线带宽", "testspeed_uplink_bandwidth": "上行带宽", "testspeed_dwnstream_bandwidth": "下行带宽", "testspeed_path_line": "纠正途径专线", "testspeed_line": "未匹配到专线", "testspeed_speed_measurement": "定时测速", "testspeed_select_speed": "请选择测速服务器", "testspeed_select_duration": "请选择测速时长", "testspeed_select_speed_time": "请选择测速时间", "testspeed_speed_measured": "正在测速中...", "testspeed_selected_five": "所选测速时间间隔不得低于五分钟", "testspeed_selected_time": "所选时间存在重复，请删除重复时间", "testspeed_contact_administrator": "网络出现故障，请选择其他测速服务器重试或联系管理员", "testspeed_dedicated": "专线：未匹配到专线", "testspeed_unactivated": "未激活", "testspeed_activated": "已激活", "testspeed_event": "事件开始时间", "testspeed_failure_start": "故障开始时间", "testspeed_failure_recovery": "故障恢复时间", "testspeed_event_recovery": "事件恢复时间", "testspeed_event_duration": "事件持续时间", "testspeed_failure_duration": "故障历时", "testspeed_matching": "支持角色名称的模糊匹配搜索", "sysgether_characters": "注意：最多包含64个字符", "warning_time_not_exceed_62": "时间跨度不能超过62天", "warning_latter_greater_previous": "自定义的后者值必须大于前者", "warning_select_clear_first": "请先选择清除数据", "warning_view_deleted": "此视图已被删除", "warning_path_topology_empty": "描述：由于数据为空，无法渲染路径拓扑", "warning_device_node_connected": "将设备或连接到设备的节点至少添加一个源", "warning_date_not_null": "日期不能为空", "warning_task_repeat": "有任务正在执行，请勿重复执行", "warning_URL_incorrect": "URL格式不正确", "warning_enter_test_email_addr": "请输入接收测试的电子邮件地址", "warning_enter_test_phone": "请输入接收测试的电话号码", "warning_enter_test_content": "请输入测试内容", "warning_download_failed": "下载空文件失败。过程", "warning_parameter_max_20": "参数最多包含20个参数", "warning_cannot_del_org": "不能删除本用户所属机构", "warning_select_want_export": "请选择要导出的数据", "warning_first_operation_data": "请先检查操作数据", "warning_repeating": "当选择了重复项时，请至少选择一个重复周期项目", "warning_window_not_none": "窗口大小不能为空", "warning_window_size": "窗口大小只允许输入1-60", "warning_packet_loss": "丢包告警生成阈值不能为空", "warning_packet_loss100": "丢包告警生成阈值只允许输入1-100", "warning_interrupt_empty": "中断生成阈值不能为空", "warning_interrupt_1000": "中断生成阈值只允许输入0-1000", "warning_degradation_empty": "时延劣化生成阈值不能为空", "warning_degradation_1000": "时延劣化生成阈值只允许输入0-1000", "warning_empty_once": "丢包劣化生成阈值，单次达到不能为空", "warning_loss_100": "丢包劣化生成阈值，单次达到只允许输入1-100", "warning_loss": "丢包劣化生成阈值，单次达到", "warning_packet_loss_1": "丢包告警生成阈值", "warning_loss_empty": "丢包劣化生成阈值，连续不能为空", "warning_loss_1000": "丢包劣化生成阈值，连续只允许输入0-1000", "warning_loss_continuously_empty": "丢包劣化生成阈值，连续达到不能为空", "warning_loss_continuously_100": "丢包劣化生成阈值，连续达到只允许输入1-100", "warning_IP_first": "请先输入探针IP", "warning_too_long": "目标地址段过长", "warning_target_segment": "请输入目标地址段", "warning_target_segment_1": "请输入目标地址段1", "warning_range_20_60000": "20到60000之间", "warning_range_20000_60000": "20000到60000之间", "warning_g0": "只能输入大于0的正整数", "warning_cannot_g": "起始值不能大于受限跳数", "warning_g30": "该值不得大于30", "warning_cannot_l": "受限跳数不能小于起始值", "warning_mac_ip": "目标IP和目标MAC只允许填写一项，系统纳管目标上联设备后会自动补全另一项", "comm_task_first": "请先选择任务类型", "nodequality_degradation_time": "劣化叠加时长", "nodequality_all_degradation_time": "劣化累计时长", "nodequality_quality_analysis": "链路质差分析", "nodequality_difference_cumulative_time": "质差累计时长", "nodequality_difference_stacking_time": "质差叠加时长", "probetask_alarm_setting": "告警设置", "probetask_indicator_details": "指标详情（任务/路径编号：", "probetask_alarm_threshold": "告警阈值", "probetask_single_reach": "单次达到", "probetask_or_continuous": "或 连续", "probetask_route_alarm": "路由波动告警", "probetask_path_num": "任务/路径编号", "probetask_num_no": "路径编号", "probetask_num_paths": "路径数", "probetask_ended": "已结束", "probetask_monitoring_state": "监测状态", "probetask_delete_associated": "<p>您确定要删除与关联链接的数据报告的删除任务吗？</p>", "probetask_task_deletion_succeeded": "任务删除成功", "probetask_sure_want": "<p>您确定要", "probetask_assignment": "是否分配？</p>", "probetask_dial_test": "这是一个拨测任务", "probetask_high_dial": "高频拨测", "probetask_task_sucess": "任务,告警参数批量修改成功", "probetask_task_fail": "描述：批量修改告警参数失败", "probetask_no": "否", "probetask_related_task": "相关任务", "probetask_current_status": "当前状态：", "probetask_trend_chart": "趋势图：", "probetask_date": "日期：", "probetask_path_analysis_tip": "路径分析", "probetask_path_topology_tip": "路径拓扑", "probetask_indicator_trends_tip": "指标趋势", "probetask_target_name": "目标名称", "probetask_betw": "在20000到60000之间", "probetask_routine": "例行", "probetask_SIP": "SIP协议", "probetask_betw20": "在20到60000之间", "probetask_pack_size_min": "最小字节", "probetask_interrupt_deterioration_tip": "统计时间范围内，没有发生过中断、劣化的任务数", "probetask_interrupt_deterioration_suspend_tip": "统计时间范围内，没有发生过中断、劣化、暂停的任务数", "probetask_deterioration_tip": "统计时间范围内，发生过劣化的任务数", "probetask_interrupt_tip": "统计时间范围内，发生过中断的任务数", "probetask_suspend_tip": "统计时间范围内，发生过暂停的任务数", "probetask_export_excel_file_name": "拨测任务列表.xlsx", "org_tel_format_incorrect": "电话号格式不正确", "org_the": "第", "org_branch_office": "分支机构", "org_dept": "部门", "processinfo_view_details": "查看详情（进程名：", "processinfo_min": "最小（%）", "processinfo_max": "最大（%）", "processinfo_aver": "平均（%）", "nodequality_topo": "拓扑：", "nodequality_occurrence_time": "发生时间", "nodequality_duration": "历时", "nodequality_type": "类型", "nodequality_target": "目标", "nodequality_cause": "原因", "nodequality_Link_details": "链路详情", "nodequality_link": "（链路：", "nodequality_interrupt_details": "中断详情", "nodequality_deteriorate_details": "劣化详情", "nodequality_line_details": "专线详情", "pathhealthy_availb": "可用率", "pathhealthy_good_ratio": "优良比例", "pathhealthy_health_configuration": "拨测健康档案-导出文件配置", "pathhealthy_path_health_file": "拨测健康档案", "pathhealthy_export_invalid": "导出文件名无效！", "pathhealthy_export_success": "文件-导出成功", "dial_test_target_name": "目标名称", "dial_test_initial_TTL": "起始TTL", "dial_test_TTL_limited_number": "TTL限制次数", "dial_test_target_device": "目标设备", "rpquality_event": "事件", "rpquality_failure": "故障", "rpquality_health_records": "拨测健康档案", "rpquality_all_event_types": "所有事件类型", "rpquality_all_fault_types": "所有故障类型", "rpquality_cause": "原因", "rpquality_duration": "持续时间", "rphealthy_min": "最小值", "rphealthy_max": "最大值", "rphealthy_aver": "平均值", "rphealthy_current_value": "当前值", "rphealthy_cumulative_number": "累计次数", "rphealthy_cumulative_duration": "累计持续时间", "rphealthy_average_duration": "平均持续时间", "rphealthy_deteriorate_num": "劣化总次数", "specinfo_associated_identity": "是否关联", "specinfo_spec_line": "专线", "specinfo_remarks": "备注", "pathtopo_new_filter": "新建过滤", "pathtopo_unfilter": "解除过滤", "pathtopo_save": "保存", "pathtopo_sure_filter": "<p>确定要新建过滤这些数据吗？</p>", "pathtopo_sure_unfilter": "<p>确定要解除过滤这些数据吗？</p>", "pathtopo_probe_port": "探针端口", "pathtopo_enter_probe_port": "请输入探针端口", "pathtopo_target_name": "目标名称", "pathtopo_target_port": "目标端口", "pathtopo_task_type": "任务类型", "phystopo_firewall": "防火墙", "phystopo_server": "服务器", "phystopo_ul_ip": "<div>上行IP/下行IP：", "phystopo_dp_loss": "时延/丢包：", "phystopo_ifr_flow": "入流/出流：", "phystopo_sure_del": "您确定要删除这些记录吗？", "phystopo_startTime_error_tip": "请选择开始时间", "phystopo_endTime_error_tip": "请选择结束时间", "qualityreport_menu_first": "1、整体质量评估", "qualityreport_menu_second": "2、术语解释和测试方法", "qualityreport_menu_interpretation": "(一)术语解释", "qualityreport_methods": "(二)检测方法", "qualityreport_optimization": "3. 优化建议", "qualityreport_net_report": "网络质量检测报告", "qualityreport_evaluation": "数字评估模型", "qualityreport_detection_cycle": "检测周期", "qualityreport_report_provider": "报告提供方", "qualityreport_netDem_line": "NetDem采用先进的网络全覆盖主动检测技术，实现WIFI+拨测+专线", "qualityreport_relay": "中继", "qualityreport_360_degree": "360度无死角检测，利用领先的AI建模和机器学习算法，将不可见的网络质量转化为直观的数字评分，并且提供针对性的整改建议，帮助用户提升数字化体验。", "qualityreport_inner_detection": "拨测检测", "qualityreport_WIFI_detection": "WIFI检测", "qualityreport_special_detection": "专线检测", "qualityreport_relay_detection": "中继检测", "qualityreport_score": "分", "qualityreport_intranet_optimization": "拨测优化建议", "qualityreport_target_refers": "质差目标是指在统计分析时间范围内，检测到可用率小于99%或者优良率小于95%的目标，本次检测共发现", "qualityreport_Top10": "个质差目标，其中Top10如下：", "qualityreport_WiFi_dedicated": "通过APP、采集器对企业的WiFi、拨测、专线、中继进行全面监测", "qualityreport_comprehensive": "搜集分析采集数据，智能定位质差点，并且提供针对性的整改建议。", "qualityreport_whose_target": "质差目标是指在统计分析时间范围内，检测到可用率小于99%或者优良率小于95%的目标，本次检测共发现", "qualityreport_dif_10": "质差目标Top10", "qualityreport_good_95": "（优良率小于95%或可用率小于99%）", "qualityreport_more_details": "查询更多详情", "qualityreport_poor_link": "质差链路是指发生丢包劣化/时延劣化/中断并持续时间长或者影响范围广的链路，大面积的的网络质差可能是某个质差链路引起的，NetDem采用智能定位技术，定位到", "qualityreport_quality_impact": "个质差链路，对整个网络质量影响较大，其中Top10如下：", "qualityreport_Top10_links": "质差链路Top10", "qualityreport_quality_30": "(平均一天质差叠加时长大于30分钟)", "qualityreport_problem_first": "质差链路对整个网络质量影响较大，请优先处理", "qualityreport_test_problem": "本次检测未发现问题，网络质量非常好，请继续保持", "qualityreport_WiFi_optim": "WiFi优化建议", "qualityreport_within_total": "在统计分析时间范围内，共进行了", "qualityreport_handicap_10": "质差点位Top10", "qualityreport_check_found": "次打点检测，发现", "qualityreport_detection_point": "个可优化的检测点位，请根据建议优化。其中Top10如下：", "qualityreport_optimizable_items": "(存在可优化项)", "qualityreport_download_rate": "下载速率：", "qualityreport_question": "问题：", "qualityreport_suggestions": "建议:", "qualityreport_ascending_speed": "升速:", "qualityreport_signal_strength": "信号强度:", "qualityreport_signal_interference": "信号干扰:", "qualityreport_connection_rate": "连接速率:", "qualityreport_intranet_response": "内网响应:", "qualityreport_encryption_protocol": "加密协议:", "qualityreport_special_optim": "专线优化建议", "qualityreport_relay_optim": "中继优化建议", "qualityreport_availability": " 质差中继是指在统计分析时间范围内，检测到可用率小于99%或者优良率小于95%的目标，本次检测共发现", "qualityreport_targets_10": "条质差目标，其中Top10如下：", "qualityreport_trunk_10": "质差中继Top10", "qualityreport_XX": "成都XX公司网络质量检测报告", "qualityreport_overall": "一、总体质量评估", "qualityreport_terminology": "术语", "qualityreport_explain": "说明", "qualityreport_comprehensive_score": "综合评分", "qualityreport_level": "体验等级", "qualityreport_network_quality": "将所有监测目标的网络质量转化成百分制评分，评分越高质量越好", "qualityreport_user_experience": "用户体验", "qualityreport_describe_experience": "分四个等级（很好、较好、一般、很差）描述用户体验", "qualityreport_ranking": "综合排位", "qualityreport_network_assess": "用来评估在所有企业中网络质量的水平，排位越靠前代表质量越好", "qualityreport_dif_target": "质差目标", "qualityreport_target_95": "可用率小于99%或者优良率小于95%的网络拨测的目标", "qualityreport_dif_link": "质差链路", "qualityreport_a_link": "一条链路由两端的网络节点及节点间的通信线路组成，当链路出现中断或劣化影响到目标的叠加时长大于30分钟/天时定义为质差链路", "qualityreport_ql": "质差专线", "qualityreport_rate_95": "可用率小于99%或者优良率小于95%的专线", "qualityreport_qdr": "质差中继", "qualityreport_relay_95": "可用率小于99%或者优良率小于95%的中继", "qualityreport_wifi_channel": "Wi<PERSON>i信道", "qualityreport_transmission": "以无线信号作为传输载体的数据信号传送通道，同一信道上的设备越多，WiFi信号的强度越弱", "qualityreport_wifi_frequency": "WiFi同频/邻频干扰", "qualityreport_two_WiFi": "两个WiFi处于在相同的频段上WiFi信号强度相差小于10dbm，信号之间存在相关干扰，影响数据传输", "qualityreport_task_number": "任务编号", "qualityreport_source_IP": "源IP", "qualityreport_target_name": "目标名称", "qualityreport_availaby": "可用率", "qualityreport_cu_duration": "劣化累计时长", "qualityreport_good_ratio": "优良率", "qualityreport_influence_num": "影响目标数", "qualityreport_total_dif": "质差累计次数", "qualityreport_total_dif_time": "质差累计时长", "qualityreport_stack_dif_time": "质差叠加时长", "qualityreport_dot": "点缩略图", "qualityreport_point_label": "点标签", "qualityreport_access_WiFi": "接入WiFi", "qualityreport_download_Mbps": "下载Mbps", "qualityreport_inr_resp": "内网响应ms", "qualityreport_ips": "指标&问题&建议", "qualityreport_operator": "运营商", "qualityreport_dcn": "劣化累计次数", "qualityreport_trunk_number": "专线编号", "qualityreport_trunk_name": "专线名称", "qualityreport_local_addr": "本地地址", "qualityreport_peer_addr": "远程地址", "qualityreport_duration": "专线时长", "qualityreport_weight": "权重", "qualityreport_very_poor": "很差", "qualityreport_quality_poor": "网络质量很差，有很大提升空间", "qualityreport_general": "一般", "qualityreport_quality_averg": "网络质量一般，有很大提升空间", "qualityreport_better": "较好", "qualityreport_quality_good": "网络质量较好，有一定提升空间", "qualityreport_excellent": "很好", "qualityreport_please_keep": "网络质量很好，请继续保持", "qualityreport_intranet": "拨测", "qualityreport_sl": "专线", "qualityreport_refers_line": "质差专线是指在统计分析时间范围内，检测到可用率小于99%或者优良率小于95%的专线，本次检测共发现", "qualityreport_quality_10": "条质差专线，其中Top10如下：", "qualityreport_line_Top10": "质差专线Top10", "qualityreport_ar99": "(可用率低于99%或优良率低于95%)", "qualityreport_strategy": "报告生成策略", "alarmlist_iln": "影响路径编号", "alarmlist_st": "告警开始时间", "alarmlist_rt": "告警恢复时间", "alarmlist_st1": "告警开始时间", "alarmlist_rt2": "告警恢复时间", "alarmlist_operator": "操作人", "alarmlist_operate_time": "操作时间", "alarmlist_operate_process": "处理过程", "alarmlist_operate_result": "处理结果", "alarmlist_fault_type": "故障类型：", "alarmlist_sfl": "，疑似故障链路：", "alarmlist_cof": "，故障原因：", "alarmlist_fi": "故障指标（", "alarmlist_r_topology": "路由拓扑：", "alarmlist_source": "源：", "alarmlist_initial_path": "初始路径", "shieldlist_fgt": "生成时间", "shieldlist_as_far": "（尽量）", "menu_DT_Poor": "DT差质量分析", "menu_dial_test": "拨号测试", "menu_dlm": "专线管理", "menu_relay_line": "中继专线", "menu_NetDem": "网络专报", "menu_RL_qa": "RL差质量分析", "msgservemail_mes_test": "邮件消息测试头", "msgservemail_se_mes_sus": "发送邮件消息测试成功！", "pathrun_dimensions": "维度：", "pathrun_monitor": "监测", "pathrun_overview": "概览", "pathrun_path": "路径", "pathrun_total": "总数：", "pathrun_mom": "环比", "pathrun_last_month": "上个月", "pathrun_last_week": "上周", "pathrun_pbqt": "查询时间之前的时段", "pathrun_keep": "保持", "pathrun_reduce": "减少", "pathrun_increase": "增加", "pathrun_avd": "平均时延：", "pathrun_eagr": "优良率：", "pathrun_grr": "好/优良率=∑每条路径（运行时长-总故障时长-总劣化时长+总故障和劣化重叠时长）/∑每条路径运行时长100%", "pathrun_gr_running": "好/优良率=（运行时长-总故障时长-总劣化时长）/运行时长100%", "pathrun_Ava_rate": "可用率=∑每条路径（运行时长-总故障时长）/∑每条路径运行时长100%", "pathrun_Ava_run": "可用率=运行时长-总故障时长）/运行时长100%", "pathrun_average": "平均", "pathrun_duration": "持续时间：", "pathrun_insi": "中断情况 [前5名：可用性低于99%]", "pathrun_de5": "劣化情况 [前5名：优良率低于95%]", "pathrun_topo": "拓扑图：", "pathrun_event_time": "事件时间", "pathrun_as_far_po": "（尽量）", "pathrun_cause": "原因", "pathrun_event": "事件", "pathrun_w_n": "全网", "pathrun_operation_report": "运行报告", "pathrun_np": "正常路径", "pathrun_dp": "劣化路径", "pathrun_ipa": "中断路径", "pathrun_plr": "时延、丢包率 - 日图表", "pathrun_ava_good": "可用性、好和优良率 - 天气图表", "pathrun_plr24": "时延、丢包率 - 24小时图表", "pathrun_ava_good24": "可用性、好和优良率 - 24小时图表", "pathrun_total_num": "监测任务总数", "pathrun_iter_task": "中断任务数量", "pathrun_der_tn": "劣化任务数量", "pathrun_cuindu": "累积中断时长", "pathrun_mptn": "监测路径总数", "pathrun_ipn": "中断路径数量", "pathrun_dpn": "劣化路径数量", "pathrun_mltn": "监测线路总数", "pathrun_normal_task": "正常任务", "pathrun_de_task": "劣化任务", "pathrun_inte_task": "中断任务", "pathrun_branch_office": "分支机构", "pathrun_moor": "月度运行报告", "pathrun_task_details": "任务详情", "pathrun_source_IP": "（源IP地址：", "pathrun_objectives": "，目标：", "pathrun_failure": "故障", "pathrun_normal_line": "正常线路", "pathrun_degraded_line": "劣化线路", "pathrun_interrupt_line": "中断线路", "pathrun_pnum": "路径数量", "pathrun_Z_alias": "Z端别名", "pathrun_cum_det": "累积劣化时长", "dashboardPie_no_sa": "无统计数据", "dashboard_psad": "路径统计分析图", "msgservenote_SDK_file": "SDK文件：", "msgservenote_enter_content": "请输入测试内容", "msgservenote_incor_2060": "端口输入不正确（20-60000）", "msgservenote_SMS": "短信网关", "msgservenote_SDK_SMS": "SDK短信接口", "spechealthy_leaderboard": "排行榜：", "spechealthy_ie": "输入效率", "spechealthy_oe": "输出效率", "group_fill_name": "请填写组名", "messageconfig_select_mode": "选择推送模式", "messageconfig_not240": "内容不能超过240个字符", "qualityrs_aw": "接入WiFi", "qualityrs_highest": "最高", "qualityrs_average": "平均", "qualityrs_poor": "差", "qualityrs_work": "工作", "qualityrs_vc": "视频会议", "qualityrs_hvc": "高清视频会议", "qualityrs_inp": "问题介绍", "qualityrs_sosu": "解决方案建议", "qualityrs_sis": "信号强度", "qualityrs_sii": "信号干扰", "qualityrs_cr": "连接速率", "qualityrs_inre": "内网响应", "qualityrs_nodn": "网络设备数量", "qualityrs_chat": "聊天", "qualityrs_trill": "抖音", "qualityrs_sco": "监控", "qualityrs_4ksco": "4K监控", "pathquality_t_de": "任务详情", "pathquality_si": "（源IP：", "pathquality_obs": "，目标：", "pathquality_inde": "中断详情", "pathquality_dede": "劣化详情", "flowChart_sou": "源", "flowChart_aim": "目标", "flowChart_srl": "专线", "flowChart_n1": "节点1_IP", "flowChart_n2": "节点2_IP", "flowChart_atd": "平均时延", "flowChart_sou1": "（源）", "flowChart_aim1": "（目标）", "flowChart_aim3": "（目的）", "flowChart_sou2": "源", "flowChart_aim2": "目标", "flowChart_ld": "线路/目的地", "flowChart_un": "不可信任节点", "rppath_loading_data": "正在加载数据", "message_retransmission": "重发", "message_wechat": "微信", "common_data_time": "最近1小时", "common_data_time2": "最近3小时", "common_data_time3": "最近6小时", "common_data_time4": "最近12小时", "common_loading": "加载中...", "common_no_data": "没有更多数据...", "common_unrecovered": "未恢复", "common_recovered": "已恢复", "common_previous": "上一页", "common_next_page": "下一页", "common_query": "查询", "common_new": "新建", "common_add": "添加", "common_add_success": "添加成功", "common_update_success": "修改成功", "common_update": "修改", "common_delete": "删除", "common_back": "返回", "common_No": "序号", "common_cancel": "取消", "common_index_selection": "指标选择", "common_all": "全选", "common_verify": "确认", "common_test": "验证", "common_controls": "操作", "common_action": "动作", "common_action_attribute": "动作属性", "common_controls_succ": "操作成功", "common_controls_fial": "操作失败", "common_syn": "自动同步", "common_opa": "手动同步", "common_data_verification_tip": "数据校验", "common_data_verification_total_tip": "共", "common_data_verification_data_tip": "条数据", "common_data_verification_where_tip": "其中", "common_data_verification_error_tip": "错误", "common_data_verification_upload_again_tip": "如有误，仅导入正确项或调整Excel后重新上传", "common_file_match": "模板不匹配，请选择提供的模板进行导入", "phytopo_sycn": "配置为“自动同步”时，若系统检测到连接关系发生变化，会自动更新网络拓扑连接关系（不包括手动创建线路），是否确认更改为“自动同步”？", "phytopo_ops": "配置为“手动同步”时，若系统检测到连接关系发生变化，需手动同步才会更新网络拓扑连接关系，是否确认更改为“手动同步”？", "phytopo_line_name": "线路名称：", "phytopo_enter_line_name": "请输入线路名称", "phytopo_resource_selection": "资源选择", "phytopo_connect_device_1": "连接设备1：", "phytopo_connect_device_2": "连接设备2：", "phytopo_device_ip": "设备IP", "phytopo_device_ip1": "设备IP1", "phytopo_device_ip2": "设备IP2", "phytopo_enter_ip": "请输入设备IP", "phytopo_port_1": "端口1", "phytopo_port_2": "端口2", "phytopo_ohter": "其他", "phytopo_upward_direction": "上行方向：", "phytopo_port_1_to_port_2": "设备1端口到设备2端口", "phytopo_port_2_to_port_1": "设备2端口到设备1端口", "phytopo_remark": "备注", "phytopo_enter_remarks": "请输入备注", "phytopo_select_data": "请选择数据", "phytopo_sure_delete": "确定要删除这些记录吗？", "common_delete_success": "删除成功", "common_delete_failed": "删除失败", "phytopo_select_device_1": "请选择设备1", "phytopo_select_device_2": "请选择设备2", "phytopo_select_direction": "请选择方向", "phytopo_select_port_1": "请选择端口1", "phytopo_select_port_2": "请选择端口2", "phytopo_key_words": "关键字", "phytopo_fuzzy": "支持线路名称进行模糊匹配", "phytopo_synchronization": "数据同步", "phytopo_line": "线路名称", "phytopo_connected_device_name_1": "连接设备名称1", "phytopo_connected_device_name_2": "连接设备名称2", "phytopo_connect_device_IP1": "连接设备IP1", "phytopo_connect_device_IP2": "连接设备IP2", "phytopo_port_1_description": "端口1描述", "phytopo_port_2_description": "端口2描述", "phytopo_topology_name": "拓扑图名称", "phytopo_device_Name": "设备名称", "phytopo_device_Name1": "设备名称1", "phytopo_device_Name2": "设备名称2", "phytopo_enter_device_name": "请输入设备名称", "phytopo_enter_device_alias": "请输入设备别名", "phytopo_device_IP": "设备IP", "phytopo_enter_device_IP": "请输入设备IP", "phytopo_template_download": "模板下载", "phytopo_select_attachment": "选择附件", "phytopo_use_templates": "使用模板进行数据编辑、导入！", "phytopo_download_template": "下载模板：", "phytopo_upload_attachment": "上传附件", "phytopo_export_error_item": "导出错误项", "phytopo_row_num": "行数", "phytopo_error_details": "错误详情", "phytopo_topology_template": "物理拓扑线路导入模板", "phytopo_read_failed": "读取失败！请重新下载模板再次上传", "phytopo_empty_template": "您上传的是一个空模板,请输入数据", "phytopo_failed_import": "导入失败,请检查文件是否符合规范", "phytopo_abnormal": "物理拓扑线路异常项", "common_No_abnormal": "暂无异常项", "common_No_data_exprot": "暂无数据导入", "common_No_match": "没有符合条件的数据", "phytopo_upload_successfully": "上传成功", "phytopo_upload_failed": "上传失败", "phytopo_try_again": "已经上传过一次,请返回上一步重新操作", "phytopo_select_province": "请选择省", "phytopo_select_city": "请选择地市", "phytopo_information": "信息展示设置", "phytopo_legend": "图例", "phytopo_No_data": "拓扑图暂无数据", "common_Normal": "正常", "common_line": "正常专线", "phytopo_deterioration": "劣化", "common_degradation": "时延劣化", "common_loss_degradation": "丢包劣化", "common_line_congestion": "专线拥塞详情", "common_port_congestion": "端口拥塞详情", "common_collector_offline": "采集器离线", "phytopo_interrupt": "中断", "phytopo_enter": "请输入IP/名称查询", "phytopo_management": "拓扑图管理", "phytopo_line_management": "线路管理", "common_No_data": "暂无数据", "phytopo_selection": "拓扑图选择", "phytopo_alarm": "告警", "phytopo_alarm_show": "告警显示配置", "phytopo_line2": "线路", "phytopo_enter_name": "请输入名称", "phytopo_enter_description": "请输入描述", "phytopo_enter_number": "请输入呈现数量", "common_detele_sure": "确定要删除这些记录吗？", "phytopo_export_data": "请选择需要导出的数据", "phytopo_topology_list": "物理拓扑图线路列表", "phytopo_network_error": "网络连接错误", "phytopo_add_time": "最少添加一个时间段", "phytopo_10_time": "最多添加10个时间段", "phytopo_displayed": "进入物理拓扑图时,缺省打开的拓扑图：", "phytopo_subgraphs": "管理子图数量", "phytopo_managed_objects": "管理对象数量", "poto_maximum": "最大呈现数量", "poto_select_one": "至少选择一个对象", "poto_management_mode": "管理方式", "poto_object_filtering": "对象过滤", "phytopo_add": "增加", "phytopo_remvoe": "移除", "phytopo_obj": "对象", "phytopo_add_to": "增加到拓扑图", "phytopo_remove_to": "从拓扑图移除", "phytopo_subgraph": "子图", "phytopo_topo": "拓扑图", "phytopo_desc": "描述", "phytopo_enter_topo_name": "请输入拓扑图名称！", "phytopo_same_name": "已存在相同的名称", "phytopo_add_obj": "增加对象", "phytopo_remove_obj": "移出对象", "phytopo_add_sub": "添加子图", "phytopo_remove_sub": "移出子图", "phytopo_blow_up": "放大", "phytopo_minification": "缩小", "phytopo_page_restore": "页面还原", "phytopo_exporting_PNG": "导出PNG", "phytopo_fault_path": "故障路径", "common_email": "邮箱格式不正确", "system_configuration": "系统配置", "system_logo": "系统LOGO", "system_name": "系统名称", "system_copyright_information": "版权信息", "system_pixels_than_png_image": "64x64像素,小于1MB的PNG格式图片", "character": "字符", "system_network_name": "", "system_server_ip": "服务器IP", "system_process_id": "进程ID", "system_process_name": "进程名", "system_server_memory": "内存", "message_prompt": "提示", "message_1": "若存在相同专线时，请填写A端或者Z端设备IP。", "message_2": "未指定流量取值则自动匹配。流量取值请先选择设备信息，再选择接口信息。", "system_prompt_select_file": "请选择需要更新的附件", "system_prompt_input_system_name": "请填写系统名称", "system_prompt_input_copyright": "请填写版权信息", "system_prompt_uploaded_file_error": "上传的文件有误，仅允许png,PNG格式", "reachability_probe_online": "在线", "reachability_probe_offline": "离线", "reachability_probe_normal": "在线", "reachability_dial_type": "拨测方式", "reachability_probe_port": "探测端口", "reachability_prompt_port": "多个请用英文逗号分割", "reachability_port_verified_connected": "逐个验证端口连通性，有端口连通时则判定可达且不再验证其它端口；全部端口不通则判定为不可达！", "reachability_execution_mode": "执行方式", "reachability_execution_manual_input": "手动输入", "reachability_execution_batch_import": "批量导入", "reachability_dest_ip": "目标IP/IP段", "reachability_prompt_use_same_ip": "请使用相同ip段，并用‘-’连接", "reachability_please": "请", "reachability_user_template_edit": "，使用模板进行数据编辑、导入", "reachability_carry_out": "执行", "reachability_clear": "清空", "reachability_import_error_message": "导入错误信息", "reachability_path": "路径", "reachability_connected_port": "连通端口", "reachability_accessibility": "可达性", "reachability_verification_result_set": "可达性验证结果集", "reachability_maximum_ports": "端口最多添加5个", "reachability_verify_probe_port": "探测端口输入有误，探测范围", "reachability_only_one_hyphen": "ip段仅支持使用一个‘-’连接", "reachability_port": "请输入探测端口", "reachability_non_execution": "未执行： ", "reachability_accessibility_verification": "可达性验证", "reachability_clear_accessibility_verification": "清空可达性验证", "reachability_clear_prompt": "清空提示", "reachability_verification_import_template": "可达性验证导入模板", "test_report_file_format": "文件格式", "test_report_select_file_format": "请选择文件格式", "test_report_disposition": "配置", "test_report_build_failure": "生成失败", "test_report_noStart": "未开始", "test_report_generating": "生成中", "test_report_generated": "已生成", "test_report_label_name": "报告名称：", "test_report_label_statistical_time": "统计时间：", "test_report_label_group": "分组：", "test_report_label_file_format": "文件格式：", "test_report_generate_report": "生成报告", "test_report_update_disposition": "更新配置", "test_report_provider": "报告提供方", "test_report_describe": "描述", "test_report_limit_time": "时间跨度不得大于92天", "test_report_limit31_time": "时间跨度不得大于31天", "test_report_prompt_delete": "确定要删除这条数据吗？", "test_report_prompt_select_data": "请选择需要下载的数据", "test_report_prompt_no_down_file": "暂无下载文件", "test_report_pptx_template": "PPTX模板", "test_report_current_template": "当前模板", "test_report_no_template": "暂无模板", "test_report_update_template": "更新模板", "test_report_docx_template": "DOCX模板", "test_report_prompt_pptx_size": "请上传文件小于50M，格式为pptx的模板文件！", "test_report_prompt_docx_size": "请上传文件小于50M，格式为docx的模板文件！", "backup_incorrect_ip_format": "IP格式有误", "backup_user_name": "用户名", "backup_user_name1": "用户名", "backup_password": "密码", "backup_prompt_fill_password": "请填写密码", "backup_prompt_fill_path": "请填写备份路径", "backup_schedule": "进度", "backup_data_backup": "数据备份", "backup_down_data_backup": "下载数据备份(名称：", "backup_prompt_delete": "确认是否删除", "backup_prompt_remote_no_information": "远程备份时,远程信息填写不全!", "testspeed_local_to_server": "本机到服务器", "testspeed_pc_terminal_number": "PC端编号", "testspeed_ip_address": "IP地址", "testspeed_please_enter_number": "请填写PC端编号", "testspeed_please_enter_ip_address": "请填写IP地址", "comm_friendly_reminder": "友情提示", "comm_time_delay": "时延", "comm_quiver": "抖动", "comm_loss": "丢包", "comm_the_same_month": "当月", "comm_last_month": "上月", "comm_prompt_confirm_delete": "确认是否删除？", "comm_prompt_deleting": "删除中...", "testspeed_delete_selected_information": "是否要删除选择的信息", "peertopeer_monitoring": "监测状态", "peertopeer_tip": "路径名称、起始IP、起始名称、目标IP、目标名称", "peertopeer_path_name": "路径名称", "peertopeer_path_name2": "路径名称:", "peertopeer_start_IP": "起始IP", "peertopeer_start_name": "起始名称", "peertopeer_probe": "探针:", "peertopeer_initial_node": "起始节点:", "peertopeer_target_node": "目标节点：", "peertopeer_select_initial": "请选择起始节点", "peertopeer_select_target": "请选择目标节点", "peertopeer_select_name": "请填写路径名称", "peertopeer_select_probe": "请选择探针", "peertopeer_path_information": "路径信息", "probe_frequency": "拨测频率", "probe_dynamic": "动态IP", "probe_net_state": "跨DNAT监测", "probe_1": "探针IP、目标IP/MAC/目标名称、任务编号、路径编号", "probe_2": "当监测目标为动态IP时，可勾选该项；若临时监测动态IP的目标，且短时间内IP不变时，可不勾选该项", "probe_net_state_tip": "若您想监测跨DNAT网络，请勾选该项", "probe_adjustment": "作业调整", "probe_derived": "导出节点", "probe_unmatch": "未匹配", "probe_byte": "最小字节", "probe_ip_mac": "目标IP和目标MAC至少填写一条", "probe_mac_ip": "若填写了MAC,需要勾选动态IP", "probe_delay_sensitivity": "时延劣化灵敏度不能为空", "probe_delay_sensitivity_1": "时延劣化灵敏度,只允许输入1-10", "probe_delay_experience": "时延劣化经验值不能为空", "probe_delay_experience_3000": "时延劣化经验值,只允许输入1-3000", "probe_device_type_40": "目标类型最长仅允许40个字符", "probe_group_name_20": "分组名称最长仅允许20个字符", "probe_mac_48": "MAC最长仅允许48个字符", "probe_enter_mac": "请输入正确的MAC地址", "probe_enter_positive": "端口仅允许输入正整数", "probe_port_60000": "探针端口20到60000之间", "probe_target_port_60000": "目标端口20到60000之间", "probe_port_10": "目标端口只允许输入1-10个", "probe_param_setting": "拨测参数设置", "probe_event_setting": "事件参数设置", "probe_operation_plan_setting": "作业周期设置", "probe_packets_no": "发包个数", "probe_packet_size": "包大小", "probe_tos_value": "TOS值(IPv4)", "probe_delay_sensitivity_2": "时延劣化灵敏度", "probe_delay_sensitivity_3": "数值越小，系统感知时延劣化越灵敏，1-10个等级", "probe_delay_sensitivity_4": "无正常历史数据可学习时，使用该值判断是否劣化，允许输入1-3000", "probe_experience_value": "时延劣化经验值", "probe_route_alarm": "路由波动告警", "probe_no_occurs": "定位到目标中断时不产生中断事件", "probe_window_size": "窗口大小", "probe_frequency_positioning": "定位次数", "probe_Initial_TTL": "起始TTL:", "probe_TTL": "TTL受限跳数:", "processinfo_cpu_usage": "CPU使用率", "processinfo_memory_usage": "内存使用率", "comm_min": "最小（%）", "comm_max": "最大（%）", "comm_aver": "平均（%）", "comm_cpu_usage": "CPU使用率", "comm_memory_usage": "内存使用率", "comm_please_enter_correct_ip": "请输入正确的IP", "comm_please_enter_correct": "请输入正确的", "comm_duplicate": "存在重复ip", "comm_cpu_core": "CPU（核）", "serversmonito_average_usage_partition": "平均存储使用率最大分区", "sub_server_path_duplicate": "服务路径重复", "comm_prompt_select_delete_data": "请选择需要删除的数据", "systemlog_log_source": "日志来源", "systemlog_log_type": "日志类型", "systemlog_prompt_keywords": "支持按关键字日志内容、方法名进行模糊查询", "comm_derive": "导出", "comm_class_name": "类名", "comm_method_name": "方法名", "systemlog_content": "日志内容", "systemlog_report": "系统日志报表", "comm_prompt_confirm_delete_data": "确定要删除这些记录吗？", "comm_province": "省份", "comm_city": "地市", "comm_area": "区县", "org_head_phone": "负责人、负责人电话", "comm_contact_information": "联系方式", "comm_org_name": "机构名称", "comm_org_id": "机构ID", "comm_org_name_person_charge": "负责人姓名", "comm_org_maximum_phone": "手机号最多只允许输入10个", "comm_org_number": "机构编号", "comm_text_essage": "短信", "comm_send": "发送", "comm_no_send": "不发送", "comm_use_templates_edit_import": "使用模板进行数据编辑、导入！", "comm_list": "列表", "comm_sequence_number": "排序号", "comm_organization_data_template": "机构资料模板", "comm_prompt_file_uploaded_incorrect": "上传的文件有误，请重新上传", "comm_organization_data_duplicates": "机构资料重复项", "comm_use_status": "使用状态", "comm_please_select_account_status": "请选择账号状态", "comm_online_state": "在线状态", "comm_please_select_online_status": "请选择在线状态", "comm_approval_status": "审批状态", "comm_please_select_approval_status": "请选择审批状态", "comm_user_keyword": "支持按关键字姓名进行模糊查找", "but_dormancy": "休眠", "but_sign_out": "注销", "comm_account_types": "账号类型", "comm_select_account_types": "选择账号类型", "comm_please_enter_login_password": "请输入登录密码", "comm_intensity": "强度", "comm_pwd_expri_date_select": "选择密码有效期", "user_pwd_expri_date_2": "2个月", "comm_role_authority": "角色权限", "comm_please_select_role": "请选择角色", "comm_mobile_phone_number": "手机号码", "comm_please_enter_mobile_number": "请输入手机号码", "comm_please_enter_email_number": "请输入邮箱号", "comm_identification_card": "身份证", "comm_please_enter_id_card": "请输入身份证", "comm_client_binding": "客户端绑定", "comm_client_ip": "客户端IP", "comm_supported_ip_separated": "支持多个IP,中间以英文逗号隔开", "comm_password_no_contain_account": "密码不能含有账号字符", "comm_ip_maximum": "ip数量最多10个", "comm_please_enter_correct_ip_address": "请输入正确的IP地址", "comm_permanent_account": "长期账号", "comm_temporary_account": "临时账号", "comm_pending_approval": "待审批", "comm_pass": "通过", "comm_no_pass": "未通过", "comm_examine_and_approve": "审批", "comm_please_enter_password_again": "请再次输入密码", "comm_please_enter_password_validity_period": "请选择密码有效期", "comm_select_least_one_repeat_period": "至少选择一个重复周期", "comm_start_than_end_time": "开始时间不能大于结束时间", "comm_please_wait_while_submitting": "正在提交请稍等", "user_select_approved_select_other": "所选用户中存在待审批账号，请重新选择", "user_select_deregistered_select_other": "所选用户中存在注销账号，请重新选择", "user_logout_prompt": "注销提示", "comm_module": "模块", "comm_operation_type": "操作类型", "operation_log_keywords": "支持按事件内容模糊查询", "comm_consecutive_login_failure": "连续登录失败", "comm_ip_change": "IP地址变动过大", "comm_unauthorized_access": "越权访问", "comm_login_failure": "登录失败", "comm_login_successful": "登录成功", "comm_quit": "退出", "comm_routine_operation": "常规操作", "comm_access_ip": "访问IP", "operation_log_operation_log": "操作日志", "comm_log_event_content": "事件内容", "comm_system_log": "系统日志", "comm_check_all": "勾选全部", "comm_service_level_audit": "业务级审计", "comm_alarm_level": "告警等级", "comm_importance": "重要", "operation_event_keywords": "支持按告警内容进行模糊查找", "comm_alarm_content": "告警内容", "comm_event_alarm": "事件告警", "operation_visits_top10": "访问次数TOP10", "operation_indicates_event": "事件类型占比", "operation_allow_out_login": "允许时间外登录", "comm_point": "点", "comm_quantity": "数量", "comm_proportion": "占比", "comm_log_statistics": "日志统计", "comm_audit_strategy": "审计策略", "comm_sure_select_log_out": "确定注销该账号吗？", "comm_logout_successful": "注销成功", "comm_select_logged_out_user": "请选择需注销的用户", "user_approval_details": "审批详情", "comm_submission_time": "提交时间", "comm_submitter": "提交人", "comm_submit_content": "提交内容", "comm_create_account": "新建账号", "comm_change_authority": "权限变更", "comm_cancel_account": "注销账号", "comm_dormant_account": "休眠账号", "comm_account_expiration_activation": "账号到期激活", "comm_no_pass_fill_remark": "若不通过，请填写备注", "comm_approval_list": "审批列表", "comm_account_activation": "账号激活", "comm_approval_time": "审批时间", "user_approval_remark": "审批备注", "user_approver": "审批人", "user_approval_result": "审批结果", "user_identification": "身份确认", "user_please_enter_password": "请输入您的密码，确认是您本人操作", "user_login_password": "登录密码", "user_please_enter_login_password": "请输入登陆密码", "comm_start_stop_date": "起止日期", "comm_maximum_10_time_segments": "注释：时间段最多支持10个,且不能交叉", "comm_please_select_start_end_date": "请选择起止日期", "comm_descriptive_information": "描述信息", "comm_unselected_user": "未选用户", "comm_selected_user": "已选用户", "role_keyword_tips": "支持姓名、账号、手机号查询", "comm_length_exceed": "长度不得超过", "comm_customer_list": "客户列表", "comm_personnel_management": "人员管理", "role_creating_role": "新建角色", "role_authority_management": "权限管理", "comm_system_administrator": "系统管理员", "comm_audit_administrator": "审计管理员", "comm_service_configurator": "业务配置员", "comm_service_operator": "业务操作员", "comm_audit_manager": "审核管理员", "comm_please_select_customer_name": "请选择客户名称", "comm_remove_function_list": "去除功能列表", "comm_add_function_list": "增加功能列表", "role_sure_freeze": "确定冻结该角色吗？", "role_sure_activate": "确定激活该角色吗？", "role_selected_user_already_exists": "所选用户中已存在已", "role_account_please_select": "账号，请重新选择", "role_selected_role": "已选中的角色吗？", "role_please_select_need": "请选择需要", "role_add_personnel": "添加人员", "role_personnel_have_not_changed": "人员并未发生改动", "role_personnel_changes_follows": "人员变更如下", "role_contains_user_no_deleted": "该角色包含用户，不可删除！", "role_cannot_be_moved": "不允许移出当前登录用户", "role_select_user": "选择的用户", "role_only_one_role_not_move": "仅关联了一个角色，不允许移出", "role_please_select_person_move": "请选择移出的人员", "role_please_check_person_add": "请勾选添加的人员", "role_confirm_move_select_person": "是否确认移出选中的人员", "comm_please_fill_in": "请选择", "comm_please_select_delete": "请选择需要删除的记录", "message_temp_sure_delete": "您确认要删除选中的消息模板吗 ？", "message_current_org_exist_records": "当前机构下已存在消息类型、告警类型、模板类型相同的记录", "message_variable_length": "插入变量后消息内容超出了最大长度", "comm_error_message_details": "错误信息详情", "comm_creation_time": "创建时间", "comm_select_manipulate_data": "请选择需要操作的数据", "comm_fuzzy_query_user_supported": "支持按用户模糊查询", "message_config_push_detail": "推送条件详情", "message_config_perform_advanced_configuration": "默认推送所有告警信息，可进行高级配置对告警推送条件进行配置", "message_config_select_push_person": "请选择需要推送的人员", "message_config_repeat_reminder": "重复提醒", "message_config_single_reminder": "单次提醒", "message_config_alarm_reminder": "告警提醒", "message_config_after_minutes_push": "分钟后未恢复，再次推送消息通知用户", "message_config_please_enter_interval": "请输入间隔时间", "message_config_alarm_interval": "告警提醒后，每间隔", "message_config_push_every_minute": "分钟推送一次消息通知用户", "message_config_description_200": "描述内容只允许输入0-200个字符", "comm_advanced": "切换为高级", "comm_numbers": "个", "comm_simple": "切换为简易", "comm_settings": "设置", "comm_advanced_task": "切换为高级设置", "comm_simple_task": "切换为简易设置", "message_config_or_and": "计算方式\"or\"只允许出现在计算方式结尾，例：允许A and B and C or D,不允许A and B or C and D", "message_config_default_push_config": "默认推送所有告警信息，可进行高级配置对告警推送条件进行配置", "comm_selector": "选择人员", "message_config_keyword": "支持按姓名、账号、手机号等查询", "message_config_value_positive_1000": "值为1-1000内的正整数", "comm_delete_data": "确定删除这条数据吗?", "comm_being_activated": "正在激活", "comm_selected_data_activated": "所选数据包含已激活数据,请重新选择", "comm_freeze_effect": "正在执行冻结", "comm_selected_data_freeze": "所选数据包含已冻结数据,请重新选择", "comm_selected_alarm_task_content": "请选择相应任务的告警内容", "comm_selected_alarm_task_type": "请选择相应任务的告警类型", "message_config_or_last_position": "计算方式中or方式只能处于最后位置", "message_config_exist_same_conditions": "存在多个相同条件", "message_config_exist_mutually_conditions": "存在互斥的条件，请重新配置", "message_config_maximum_10_conditions": "最多支持10条配置条件", "message_config_select_reminder": "请选择重复提醒方式", "message_config_all_select_exist": "所选人员已存在列表中，是否覆盖", "message_config_gather_keywords": "名称、ip,编码", "message_config_add_conditions": "增加新的条件", "message_config_selective_collector": "选择采集器", "message_config_selective_grouping": "选择分组", "message_config_grouping_keywords": "支持按分组名称查询", "message_config_task_keywords": "支持按任务编号、探针IP、目标IP等查询", "message_config_relaying_keywords": "支持按设备名称、设备IP查询", "comm_group_name": "分组名称", "group_name2": "分组名称", "comm_traffic_congestion": "流量拥塞", "message_config_gather_maximum_100": "采集器最多选择100个", "message_config_selected_data_exist": "所选择的数据已经存在于条件中，是否覆盖", "message_mail_keywords": "支持按邮件服务名称、服务器名、发件人名、邮箱地址进行模糊匹配查询", "comm_sender_name": "发件人名称", "comm_sending_email_address": "发送邮箱地址", "comm_test_receiving_mailbox": "测试接收邮箱", "comm_testing_wait": "测试中，请稍后", "message_mail_server_ip_name": "请输入正确服务器名/IP地址", "message_enter_correct_mail": "请输入正确邮箱", "comm_enter_port": "请输入端口", "message_enter_email_password": "请输入email密码", "message_email_server_name": "邮件服务名称", "message_test_email_title": "邮件消息测试标题", "message_note_keywords": "支持按短信服务名称进行模糊匹配查询", "message_note_provider_url": "短信网关提供商的URL。不包括URL参数。例如:http://www.smsserver.com/sendsms", "message_note_Url_parameter": "URL参数", "comm_skd_file": "SDK文件", "comm_Script": "脚本", "message_note_result": "返回结果使用参数“result”表示，例：", "message_note_test_phone": "测试手机号", "message_note_format_phone": "手机号格式不正确", "message_note_agentId_empty": "请输入agentId", "message_note_format_agentId": "AgentId 格式不正确", "message_note_phone_empty_tip": "手机号不能为空", "message_note_incorrect_port": "端口输入有误", "message_note_service_name": "短信服务名称", "message_note_file_failed_download": "文件为空下载失败", "message_note_parameter_20": "参数最多不超过20个", "message_dingding_keywords": "支持按应用名称，AgentId进行模糊匹配查询", "message_dingding_test_phone": "请输入测试接收手机号", "verify_configuration_incorrect": "验证配置有误", "comm_item_fill_in": "该项未填写", "comm_file_value_large": "填入值过大", "comm_value_must_less_than": "该项值不得小于", "comm_value_must_greater_than": "该项值不得大于", "comm_associated_attribute": "关联属性", "comm_number_relay_tasks": "中继任务数", "comm_number_probe_missions": "探测任务数", "comm_number_spec_missions": "专线任务数", "group_gather_keywords": "按采集器、设备名称、设备ip进行模糊匹配查询", "group_task_keywords": "按任务编码、探针IP、目标IP、目标名称进行模糊匹配查询", "group_sure_create": "确认是否添加？", "comm_please_check_data": "请勾选数据", "group_sure_delete": "确定是否移除？", "comm_removed_successfully": "移除成功", "comm_select_removed_data": "请选择需要移除的数据", "gather_task_normal_all": "任务数(正常/全部)", "comm_remark_length_maximum": "备注长度不能超过", "specinfo_operator": "运营商", "please_enter_correct_idcard": "请输入正确的身份证号码", "idcard_cannot_contain_special_characters": "身份证不可含有特殊字符", "idcard_allows_20_characters": "只允许输入0-20个字符", "fibre": "纤维", "alarm_details": "告警详情", "go_and_check": "去查看", "number_of_alarms": "告警数量", "comm_optimal": "优", "comm_good_two": "良", "comm_line_flow": "线路流量", "comm_line_delay": "线路时延", "comm_line_packet_loss_rate": "线路丢包率", "comm_termination_request": "终止请求", "comm_routine": "常规", "comm_operation_plan": "作业计划", "comm_collected_or_not": "是否已监测", "discoverrule_no_select_device": "请至少添加一个源到设备或者源到设备上联节点", "nodequality_link_number_tips": "统计时间范围内，没有发生过中断、劣化的链路数", "nodequality_deterioration_tips": "统计时间范围内，发生过劣化的链路数", "nodequality_interrupt_tips": "统计时间范围内，发生过中断的链路数", "comm_mechanism_selection": "机构选择", "comm_source_to_device": "源到设备", "comm_node_connected_to_source_device": "源到设备上联节点", "comm_ip_range": "IP地址范围", "pathtopo_objfilter_keywords": "支持按拨测任务编号、探针IP、探针端口、目标名称等进行模糊查找", "comm_suspected_faulty_node": "疑似故障节点", "pathtopo_path_not_faulty": "该路径无故障", "comm_port_utilization": "端口利用率", "comm_packet_loss_degradation_threshold": "丢包劣化生成阈值", "comm_delay_degradation_threshold": "时延劣化生成阈值", "comm_interrupt_threshold": "中断生成阈值", "comm_no_data_import": "无可导入数据", "pathtopo_object_keywords": "任务编码、探针IP、目标Ip,目标名称", "discover_data_uplink_ip": "IP(上联IP)", "discover_data_discovery_time": "发现时间", "discover_ip_tips": "示例：***********-200或2001:db8::1a2f:0001-1a2b，若IP段过大，发现耗时会过长！", "auditlog_general": "一般", "auditlog_important": "重要", "auditlog_badly": "严重", "probetask_two_details": "指标详情（路径编号：", "pathtopo_filter_object_no_data": "请选择数据", "task_interrupt_generation_threshold": "中断生成阈值", "task_delay_degradation_generation_threshold": "时延劣化生成阈值", "please_select_suspended_data": "请选择暂停的数据", "please_select_disabled_data": "请选择已禁用的数据", "please_select_startup_data": "请选择已启用的数据", "task_your_current_remaining": "温馨提示：您当前剩余licence", "task_exceeded_records_upload_automatically": "个,上传附件中超出的记录将自动进行截断，不再进行通知! !", "report_comm_disable": "暂停", "report_comm_enable": "启用", "task_add_edit_tips_ordinary_dial": "普通拨测", "task_add_edit_tips_automatic_dial": "自动拨测", "task_add_edit_tips_but_add": "新建", "task_add_edit_tips_but_edit": "编辑", "task_pause_tips_sure_to": "<p>确定要", "task_pause_tips_sure_to1": "这些任务吗？</p>", "task_testspeed_server_tips_sure_to1": "这些测速服务器吗？</p>", "task_pause_tips_sure_to2": "这些数据吗？</p>", "task_import_list_abnormal_items": "异常项列表", "task_export_all_excel_file_name": "全部拨测任务列表.xlsx", "task_list_number": "任务/路径编号", "task_number": "任务编号", "task_list_probe_ip": "探针IP", "report_interruption_duration": "中断累计时长", "report_degradation_duration": "劣化累计时长", "report_map_score": "分数", "task_analyze_degradation_accumulated_duration": "劣化累计时长", "qualityreport_relay_number": "中继编号", "qualityreport_relay_name": "中继名称", "qualityreport_relay_local_ip": "本端地址", "qualityreport_relay_peer_ip": "对端地址", "qualityreport_relay_interruption_duration": "中断累计时长", "probe_tasks_add_target_ip_tips": "目标", "probe_tasks_add_target_name_tips": "目标名称", "msg_eg": "返回结果使用参数“result”表示，例: \nif [ $result -gt 0 ] \n then \n echo '1' \n else \n echo ${ result } \n fi", "probe_tasks_download": "拨测任务模板", "log_strategy": "系统级审计", "qualityreport_dialog_pt_analysis": "拨测质差分析", "qualityreport_dialog_link_analysis": "链路质差分析", "qualityreport_dialog_rl_analysis": "中继质差分析", "dashboard_alarm_num_tips1": "应为正整数,且不大于20", "dashboard_alarm_num_tips2": "为不大于20的正整数", "wifi_user_level_excellent": "优", "wifi_user_level_good": "良", "wifi_user_level_medium": "中", "wifi_user_level_poor": "差", "login_lang": "语言", "color_default": "默认颜色", "color_motif": "主题颜色", "color_more": "更多颜色", "color_norm": "标准颜色", "message_select_province": "最多可选10个", "virt_host_data": "采集数据", "virt_host_name": "宿主机", "virt_host_ip": "管理IP", "virt_host_config": "宿主机管理", "virt_host_column_name": "宿主机名称", "virt_host_column_ip": "IP", "virt_host_column_ip2": "设备IP", "virt_host_column_type": "虚拟方式", "virt_host_column_TLLS": "启用TLLS", "virt_host_column_TLLS2": "Socket", "virt_host_column_vmNumber": "虚拟机数量", "virt_host_column_lastTime": "最后采集时间", "virt_host_column_cycle": "采集间隔", "virt_host_column_post": "端口", "virt_host_column_username": "用户名", "virt_host_column_password": "密码", "virt_host_column_ip_error_tip": "请选择IP", "virt_host_column_post_error_tip": "请输入正确的端口", "virt_host_column_post_error_tip2": "只允许输入0-65535的正整数", "virt_host_column_username_error_tip": "请输入登录用户名", "virt_host_column_username_error_tip_verify": "登录用户名", "virt_host_column_password_error_tip": "请输入登录密码", "virt_host_column_host_error_tip": "宿主机名称不能包含空格", "virt_host_password_chinese_error_tip": "密码不允许输入中文", "virt_host_password_blank_error_tip": "密码不允许能输入空格", "virt_host_column_type_error_tip": "请选择虚拟方式", "virt_host_column_getherInterval_error_tip": "请输入正确的采集间隔", "virt_host_column_probeId_tip": "选择的拨测探针探测的目标节点与采集到的虚机IP相同时，将在补全路径拓扑上呈现虚拟交换机等相关信息！", "virt_host_config_keyWords_tip": "宿主机名称、IP、采集器", "virt_host_export_file_name": "宿主机.xlsx", "virt_host_export_all_file_name": "全部宿主机.xlsx", "virt_host_import_template_file_name": "宿主机数据导入模板.xlsx", "virt_host_export_error_file_name": "宿主机错误数据列表.xlsx", "virt_host_network_card": "网卡", "virt_host_virtual_switch": "虚拟交换机", "virt_host_switch_name": "虚拟交换机名称", "virt_host_switch_mac": "交换机MAC", "virt_host_switch_ip": "交换机IP", "virt_host_virtual_machine": "虚拟机", "virt_host_virtual_machine_ip": "虚拟机IP", "virt_host_network_card_ip": "网卡IP", "virt_host_connection_failure": "主机连接失败", "virt_host_collection_failure": "主机采集失败", "virt_host_collector_offline": "采集器离线", "system_upgrade": "系统升级", "virt_hostd_keyword_matching": "支持按网卡、虚拟交换机、交换机MAC、虚拟机、虚拟机IP模糊匹配查询", "firm_forbid_edit": "内置设备厂商不支持修改", "model_forbid_edit": "内置设备型号不支持修改", "type_forbid_edit": "内置设备类型不支持修改", "virtHostConfig_name_limit": "宿主机名称不超过30个字符", "validate_no_chinese": "不能包含中文", "virtHostConfig_server_key": "服务器公钥", "virtHostConfig_Client_key": "客户端公钥", "virtHostConfig_private_key": "客户端私钥", "upload_server": "请上传服务器公钥", "upload_Client": "请上传客户端公钥", "upload_private": "请上传客户端私钥", "factory_code_info": "请填写设备厂商编码", "virthost_upload_file_tips": "文件大小不超过32k", "virthost_name_verify_tips": "宿主机名称存在空格", "factoryCode_uppercase": "厂商编码为大写字母", "virt_host_username_exist_space_tip": "用户名不允许存在空格", "virt_host_username_exist_chinese_tip": "用户名不允许输入中文", "dashboard_select_province_cannot_exceed_10": "选择省/直辖市,不能超过10个", "deviceModel_oid_verify_limit_char_tip": "sysObjectID只允许输入1-30个字符", "no_matching_data": "无匹配数据", "nav_all_btn": "跳过向导", "nav_current_btn": "我知道了", "nav_step1_tip": "鼠标悬浮在【拨测分析】上方，唤起子菜单!", "nav_step2_tip": "点击【拨测任务管理】，进入拨测任务管理界面新增拨测任务!", "nav_step3_tip": "点击“新增”，创建新的拨测任务！", "nav_step4_tip": "填写带“*”的必填项，其它采用默认值即可！", "nav_step5_tip": "可以创建10秒周期的拨测任务，方便快速验证；平时监测频率无需高频率，以免影响服务器性能！", "nav_step6_tip": "点击“确认”，即可完成任务创建。", "nav_step7_tip": "等2分钟，可以查看学习到的路径及原始数据趋势。", "nav_step8_tip": "您可以断掉安装“NetDem”的电脑的网络，人为制造故障，稍等一会（10秒间隔的任务等30秒，1分钟间隔的任务等3分钟），可以在故障清单中查看故障和端到端定位。", "nav_step9_tip": "您可以在【路径拓扑】里管理、查看全局网络路径拓扑。", "nav_step10_tip": "【拨测质差分析】站在任务的角度分析哪些任务质差时间长，质差频次高。", "nav_step11_tip": "【链路质差分析】站在链路的角度，分析网络中哪些链路质差时间长，质差频次高。即分析网络的薄弱环节。", "nav_step12_tip": "【拨测健康档案】为每个拨测任务建案立档，用于统计分析拨测目标周期内的健康状况。", "nav_step13_tip": "可在“帮助”下重新唤起指引！", "nav_step14_tip": "可以切换到高级设置，设置拨测参数、事件参数、告警参数等！", "help_manual": "帮助手册", "help_nav": "界面引导", "legalResPool_device_name": "设备名称", "legalResPool_ip": "IP", "legalResPool_mac": "MAC", "legalResPool_remarks": "描述", "legalResPool_legitimacy": "合法性", "legalResPool_legal": "合法", "legalResPool_illegal": "非法", "legalResPool_keywords_placeholder": "设备名称，IP，MAC，描述", "legalResPool_sure_button": "确定", "legalResPool_update_resource_pool": "更新资源池", "legalResPool_export_name": "合法资源池", "legalResPool_import": "导入", "legalResPool_import_template_name": "合法资源池导入模板", "legalResPool_import_message_exception": "合法资源池信息异常项", "legalResPool_switch_illegal_access_alarm": "非法接入告警", "legalResPool_not_verified": "未核实", "legalResPool_verified": "已核实", "legalResPool_verify_status": "状态", "legalResPool_alarm_handle_time": "处理时间", "legalResPool_alarm_into_legal_resource_pool": "纳入合法资源池", "legalResPool_alarm_please_enter_procedure": "请输入处理过程", "legalResPool_illegal_access_details": "非法接入详情", "legalResPool_illegal_access": "非法接入告警", "legalResPool_illegal_device_access": "非法接入告警", "legalResPool_select_legitimacy_verification": "请选择合法性", "legalResPool_update_resource_pool_prompt": "更新资源池后，系统会自动的将当前采集的LLDP、MAC表信息、ARP表信息、端口信息等出现过的MAC汇总到合法资源池！", "legalResPool_des_leng_400": "描述长度不能超过400", "legalResPool_download_template": "下载模板：", "legalResPool_upload_attachment": "上传附件：", "legalResPool_data_verification_tip": "数据校验：", "legalResPool_data_verification_upload_again_tip": "如有误，仅导入正确项或调整Excel后重新上传", "dashboard_component_real_alarm_group_limit_tip": "最多选择10个分组", "pathtopo_box_select": "框选", "alarmlist_port_aram_title": "端口监控", "login_drag_code_text": "拖动滑块验证", "login_drag_code_success": "验证通过", "login_drag_code_tip": "请拖动滑块验证", "port_status_change_alarm": "端口监控", "prob_direction": "方向", "prob_direction_Forward": "目标-->探针", "prob_direction_Backward": "探针-->目标", "alarmlist_port_aram_file_name": "故障清单-端口状态监控", "icon_title_pathtopo": "路径拓扑图标", "icon_title_physicaltopo": "物理拓扑图标", "icon_title_path": "路径图标", "icon_title_background": "背景图片管理", "icon_add_success": "新增成功", "icon_edit_success": "编辑成功", "icon_copy_success": "复制成功", "icon_copy_text": "副本", "icon_upload_name": "1-50个字符", "icon_upload_text": "小于50MB的png、jpeg、jpg、bmp、gif格式图片", "icon_upload_text_icon": "64X64像素，小于1MB的png格式图片", "delete_icon_title": "友情提示：", "delete_icon_content": "您确认要删除选中的数据吗？", "icon_forbid_text": "内置图标禁止修改", "icon_pathtopo_title": "进入路径拓扑图时，使用的图标", "icon_phystopo_title": "进入物理拓扑图时，使用的图标", "icon_path_title": "进入路径时，使用的图标", "icon_bg_label": "背景图", "icon_bg_label_select": "选择背景图", "icon_zoom_ratio": "图标缩放比例", "icon_bg_placeholder": "请选择背景图", "icon_bg_forbit_text": "内置背景图不可编辑", "icon_img_size_forbid": "请上传小于50MB的png、jpeg、jpg、bmp、gif格式图片", "icon_img_size_forbid_format_icon": "请上传png格式的图片", "icon_img_size_forbid_format": "请上传.jpeg,.jpg,.png,.bmp,.gif格式的图片", "icon_img_size_forbid_size_64": "请上传64*64像素的图片", "icon_img_size_forbid_icon": "请上传小于1MB的图片", "validate_50_text": "长度不能超过50个字符", "validate_200_text": "长度不能超过200个字符", "icon_form_label": "图标名称", "validate_same_name_text": "已存在相同名称", "icon_link_title": "线路颜色：", "icon_size_validate": "请上传64x64像素图片", "default_background_image": "默认背景图", "task_not_support": "收起的任务不支持查看详情", "testspeedserver_keywords": "测速服务器名称，IP", "testspeedserver_name": "测速服务器名称", "testspeedserver_code": "编码", "testspeedserver_ip": "测速服务器IP", "testspeedserver_orgName": "机构", "testspeedserver_enable": "启用", "testspeedserver_disabled": "禁用", "testspeedserver_set_prompt": "可用时间设置", "testspeedserver_set_prompt2": "避免影响正常业务，仅允许在可用时间段内进行测速", "testspeedserver": "测速服务器", "testspeedserver_disabled_prompt": "您确认要启用选中的数据吗 ？", "testspeedserver_spareTime_prompt": "为了不影响业务，请在网络空闲时进行测速！", "testspeedserver_spareTime_prompt2": "本次测试结果为本地电脑到水务总部测速服务器的网速！", "please_inter_testspeedserver_ip": "请输入测速服务器IP", "please_inter_testspeedserver_period": "请选择重复周期", "please_inter_testspeedserver_timeList": "请设置可用时间段", "please_select_forbidden_data": "请选择已禁用的数据", "please_select_repeat_cycle": "请选择重复周期", "but_add_en_contains_spaces": "新建", "switch_advanced_setup": "切换为高级设置", "switch_primary_setup": "切换为初级设置", "performance_index": "性能指标", "quality_info": "质差信息", "path_info": "路径信息", "custom_list_items": "自定义列表", "btn_search": "搜索", "topo_data_abbreviation": "数据缩略", "flow_congestion_alarm": "流量拥塞告警", "drag_left_title": "自探针向目标显示点数", "drag_right_title": "自目标向探针显示点数", "dashboard_probetask_deterioration_tip": "当前发生劣化告警的拨测任务数/占比", "dashboard_probetask_interrupt_tip": "当前发生中断告警的拨测任务数/占比", "dashboard_probetask_normal_tip": "当前正常的拨测任务数/占比", "dashboard_probetask_suspend_tip": "当前暂停的拨测任务数/占比", "device_type_needs_create_task": "需要创建任务的设备类型", "discovery_Progress": "发现进度", "net_data_discover_change": "变化", "net_data_discover_change_record_details": "变化详情", "net_data_discover_change_time": "发现时间", "net_data_discover_change_operation": "操作", "net_data_discover_whether_monitor": "是否监测", "net_data_discover_whether_identify": "是否识别", "net_data_discover_whether_change": "是否变化", "net_data_discover_identify_no": "未识别", "net_data_discover_identify_yes": "已识别", "net_data_discover_change_no": "未变化", "net_data_discover_change_yes": "已变化", "net_data_discover_select_data": "请勾选没有生成任务的发现数据", "net_data_discover_export_data": "发现数据", "net_data_discover_discovery_in_progress": "发现中", "net_data_discover_identify_in_progress": "识别中", "net_data_discover_overtime": "超时", "net_rule_discover_scantime": "最近扫描开始时间", "net_rule_discover_completiontime": "最近扫描完成时间", "discover_data_ip": "IP", "dashboard_repeat_deterioration_tip": "当前发生劣化告警的中继数/占比", "dashboard_repeat_interrupt_tip": "当前发生中断告警的中继数/占比", "dashboard_repeat_normal_tip": "当前正常的中继数/占比", "dashboard_repeat_suspend_tip": "当前暂停的中继数/占比", "dashboard_special_deterioration_tip": "当前发生劣化告警的专线数/占比", "dashboard_special_interrupt_tip": "当前发生中断告警的专线数/占比", "dashboard_special_normal_tip": "当前正常的专线数/占比", "dashboard_special_suspend_tip": "当前暂停的专线数/占比", "topo_modal_title": "收缩详情", "display_objectives": "选择展示的目标", "display_objectives_required": "请选择展示的目标", "dashboard_select_icon_scale": "请输入图标缩放比例", "task_location": "所在位置", "please_input_dest_task_location": "请填写目标所在位置", "dashboard_obj_required": "请选择展示的目标", "dashboard_obj_max": "最多只能选择2000个目标", "flow_tabs_label_application": "应用", "flow_tabs_label_conversation": "会话", "flow_query_direction": "流量方向", "flow_query_infow": "流入", "flow_query_outflow": "流出", "rphealthy_total": "总流量", "flow_table_title_application_name": "应用名", "flow_table_title_traffic_percentage": "流量百分比", "flow_btn_tooltip_map": "映射", "flow_map_keywords_placeholder": "应用名称，端口号，IP", "punctuation_comma": "，", "punctuation_bracket_left": "（", "punctuation_bracket_right": "）", "exclamation_point": "！", "maping_data": "映射数据", "map_import_template": "映射导入模板", "map_import_error": "映射错误数据", "map_application_post_error_tip": "应用端口格式不正确，只能输入数字", "map_application_post_error_tip2": "应用端口只能输入1到65535之间", "device_discovery_del_flow_model_tips": "<p>您确定要删除这些数据吗？</p>", "flow_place_holder": "源、目标、应用", "flow_maping_ip_placeholder": "支持单个IP或IP段,多个用英文逗号“,”分隔,示例： ********,***********00-200", "application_flow_top10": "应用流量Top10", "conversation_flow_top10": "会话流量Top10", "source_flow_top10": "源流量Top10", "target_flow_top10": "目标流量Top10", "flow_number": "flow数", "device_systemUpTime": "运行时长", "duplex_status_lable": "工作模式", "pathtopo_tip_alarm_info": "告警信息", "routine-horizontal": "常规图—横向", "routine-vertical": "常规图—竖向", "routine-repulsion": "常规图—斥力", "fiber-vertical": "纤维图—竖向", "fiber-horizontal": "纤维图—横向", "edit_pop_component": "编辑弹窗构件", "impact_path_number": "影响路径数", "alarm_Alertable": "告警状态", "impact_path_number_text": "大于等于", "comm_please_enter_valid_number_1_9999": "请输入1-9999之间的整数", "comm_please_select_alarmStatus": "请选择告警状态", "comm_please_select_alarmStatus_max": "最多只能选择10个分组", "dashboard_max_5_items": "最多只能选择5个拓扑图", "line_monitoring_report": "线路巡检报告", "line_alarm_list": "线路告警清单", "line_assessment_checklist": "线路考核清单", "add_snmp_alarm": "新增SNMP告警", "statistical_scope": "统计范围", "customer_name": "客户名称", "please_input_customer_name": "请输入客户名称", "generate_immediately": "立即生成", "generate_immediately_short": "立即生成", "special_line_report": "专线线路报表", "flow_over_limit_list": "流量超限清单", "statistical_dimension_zero": "98th流量超过80%的线路", "statistical_dimension_one": "峰值流量低于20%的线路", "statistical_dimension_two": "连续三个月流量超过80%电路清单", "statistical_dimension_three": "连续三个月流量低于20%电路清单", "statistical_dimension": "统计维度", "please_select_statistical_dimension": "请选择统计维度", "inspection_scope": "巡检范围"}