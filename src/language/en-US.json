{"test_enum": "test enum", "test_string": "test string", "change": "change", "login_user": "Please enter the account", "login_pwd": "Please enter the password", "login_code": "Please enter verification code", "login_captcha_pictures": "Captcha pictures", "login_in": "Login in...", "login_ok": "<PERSON><PERSON>", "login_browser_1": "The 360, chrome browser is recommended for access", "login_browser_2": "Recommended resolution: 1920 * 1080", "login_remember": "Remember the password", "login_pwd_icon": "Password Icon", "login_user_icon": "Account I<PERSON>", "login_captcha_icon": "Captcha icon", "login_msg_user_limit": "Account name limit 2 to 30 characters", "login_msg_pwd_length": "password length should not be less than 8 bits", "login_msg_pwd_limit": "password only: letters, numbers, symbol and not less than 8", "login_msg_vcode_len": "verification code length limit 4 to 6 bits", "login_msg_vcode_limit": "verification code must be a number / letter", "os_copyright": "", "login_msg_auth": "No permissions have been configured, please contact the administrator to configure!", "login_success": "Login success", "login_failed": "<PERSON><PERSON> failed", "login_try_again": "Please try again later...", "login_error": "Login error.", "login_network_error": "Please check the network connection.", "login_chang_layout": "Adjust the layout.", "login_code_error": "Verification code acquisition fault.", "login_edit_status_error": "Prohibit editing status.", "login_first_update_password_tip": "The initial password needs to be modified for the first login. Please modify your initial password!", "login_update_password_tip": "Friendly reminder: Your password has expired, please reset it!", "login_password_confirmation_tip": "Confirm", "login_password_confirmation_tip2": "Please confirm the password", "login_password_verify_tip": "Password contains at least 8 characters", "login_update_idcard": "Bind ID Card", "login_update_idcard_tip": "According to the requirements of the Ministry of Information Security of the People's Republic of China, the system can be used only when the ID number is bound,", "login_update_idcard_tip2": "Please bind your ID number number!", "login_update_idcard_error": "ID card cannot be empty", "login_update_idcard_format_error": "Incorrect format of ID card", "server_pop_up": "Pop-up prompt", "server_sound": "Sound", "server_auto_shutdown": "Auto close", "server_pro_hold": "Keep", "server_inter_alarm": "Interrupt", "server_deter_alarm": "Deterioration", "server_prom_alarm": "Notice", "server_want_delete": "<p>Do you want to delete it?</p>", "but_search": "Search", "but_add": "Create", "but_edit": "Edit", "but_copy": "Copy", "but_set": "Allocation", "but_update": "Update", "but_upload": "Upload", "but_delete": "Delete", "but_reset": "Reset", "but_export": "Export", "but_clear": "Clear", "but_choose": "<PERSON><PERSON>", "but_choose1": "choose", "but_on": "ON", "but_off": "Off", "but_confirm": "Confirm", "but_cancel": "Cancel", "but_query": "Query", "but_activate": "Activate", "but_freeze": "Freeze", "but_batch_import": "Batch Import", "but_data_export": "Export", "but_export_check": "Export Check", "but_export_all": "Export All", "but_job_adjustment": "Job Adjustment", "but_alarm_setting": "Alarm Setting", "but_pause": "Pause", "but_enable": "Enable", "but_import_updates": "Import Update", "but_import": "Import", "but_filter": "Mask", "but_unfilter": "Unmask", "but_filter_list": "Mask List", "but_remove": "Delete", "but_recovery": "Recovery", "but_add_new_cond": "Add new", "but_add_widget": "Add Component", "but_edit_widget": "Edit Component", "but_next_step": "Next step", "but_prev_step": "Pre step", "but_full_screen": "Full Screen", "but_exit_full_screen": "Exit full Screen", "but_zoom_out": "Zoom Out", "but_zoom_in": "Zoom In", "but_export_png": "Export Png", "but_tree": "Tree", "but_star": "Star", "but_center": "Center", "but_frame": "<PERSON>ame", "but_mouse_zoom": "Mouse Zoom", "but_return": "Back", "but_print": "Print", "but_data_sysn": "Data synchronization", "but_hide": "Shield", "comm_add_tasks": "Add tasks", "but_hide_list": "Shield List", "comm_numbers": "", "comm_success": "success", "comm_verify_success": "Verification pass", "comm_failure": "Fail", "comm_push": "push", "comm_pushing": "pushing", "comm_pushing1": "Pushing", "comm_success1": "Success", "comm_failure1": "Fail", "but_export_checked": "Export check", "but_operation": "operations", "comm_create_new": "New", "comm_device_manage_menu": "<PERSON><PERSON>", "comm_device_find_menu": "<PERSON><PERSON>", "comm_language_title": "Language", "comm_language_zh": "简体中文", "comm_language_en": "English", "comm_go_to": "Go to", "comm_view_success": "view success", "comm_upload_file": "Please upload file", "comm_enter_probe": "Please enter the probe port", "comm_maximum": "A maximum of five ports can be added", "comm_probe_range": "Probe port input error, probe range (20-60000)", "comm_ip": "ip segment supports only one '-' connection", "comm_import_succeeded": "Import Success", "comm_import_failed": "Import Failed", "comm_colon": "：", "comm_semicolon": ";", "comm_exigency": "Exigency", "comm_seriousness": "Seriousness", "comm_records": "records", "comm_excellent": "excellent", "comm_good": "good", "comm_poor": "poor", "comm_strong": "S", "comm_medium": "M", "comm_weak": "W", "comm_year1": "-", "comm_format_year": "yyyy-MM", "comm_format_year2": "yyyy", "comm_week_tip": "Please select the number of weeks", "comm_to": "to", "comm_month": "month", "comm_day": "day", "comm_day1": "d", "comm_week": "week", "comm_minutes": "m", "comm_second": "s", "comm_view_more": "View more", "comm_discovery_state": "Discovery status", "comm_already_delete": "The data has already been removed", "comm_object_filter": "Object filter", "comm_select_date": "Select the date", "comm_serial_number": "NO.", "comm_time": "Time", "comm_hour": "h", "comm_select_time": "Please select the date", "comm_select_month": "Please select the month", "comm_remove": "Remove", "comm_data_source": "Data Source", "comm_keywords": "Keywords", "comm_org": "Org", "comm_org_export_excel_file_name": "Organization list data.xlsx", "comm_org_export_all_excel_file_name": "Organization all list data.xlsx", "comm_org_import_template_file_name": "Organization import template.xlsx", "comm_org_export_error_file_name": "Organization error data list.xlsx", "comm_org_alias": "<PERSON><PERSON>", "comm_group": "Group", "comm_all": "All", "comm_part": "Part", "comm_num": "all number", "comm_check_num": "check number", "comm_isp": "ISP", "comm_minute": "Minute", "comm_select_org": "Select Organization", "comm_select_org2": "Please select an organization", "access_HIP": "H node IP", "access_ip_rang": "IP range", "comm_half_hour": "Half hour", "comm_effective": "Effective", "comm_invalid": "Invalid", "comm_disable": "Pause", "comm_startup": "Enabled", "comm_select_device_type": "Select the device type", "comm_enable": "Enable", "comm_all_state": "All Status", "comm_type1": "You can query name, account, tel", "comm_started": "enabled", "comm_paused": "paused", "comm_prev_text": "Pre page", "comm_next_text": "Next page", "comm_prev_step": "Pre step", "comm_next_step": "Next step", "comm_next": "Next", "comm_today": "Today", "comm_yesterday": "Yesterday", "comm_last_7": "Last 7d", "comm_last_30": "Last 30d", "comm_curr_month": "This Mon", "comm_preced_month": "Pre Mon", "comm_not_recovered": "Unrecovered", "comm_has_recovered": "Recovered", "comm_last_day_1": "Last 1d", "comm_last_day_15": "Last 15d", "comm_last_hour_12": "The Last 12 hours", "comm_save": "Save", "comm_export_check": "Export check", "comm_export_all": "Export all", "comm_high_frequency_monitoring": "High-frequency monitor", "comm_start_time": "Start Time", "comm_end_time": "End Time", "discover_date_rang": "Please configure the time period", "comm_create_successfully": "Create Success", "comm_modified_successfully": "Edit Success", "comm_delete_successfully": "Delete Success", "comm_delete_failed": "Delete Failed", "comm_operation_successfully": "Operation Success", "comm_operation_create_successfully": "Create success", "comm_operation_failed": "Operation Failed", "comm_move_success": "Move out success", "comm_move_out": "Move out", "comm_organization_delete": "Select the organization you want to delete", "comm_task_type": "Select a task type", "comm_deadline": "Please select a deadline", "comm_start_date": "Please select a start date", "comm_select_data": "Please select the data", "comm_report_name": "Report Name", "comm_report_name2": "report name", "comm_enter_value": "Please enter a value", "comm_segmentation": "Multi-model, number segmentation", "comm_report_type": "Report Type", "comm_report_type_1": "Daily test report", "comm_report_type_2": "Weekly test report", "comm_report_type_3": "Monthly test report", "comm_report_type_4": "Custom report", "comm_report_generation_time": "Report Create Time", "comm_report_time_range": "Statistical Time Range", "comm_report_comp_score": "Overall Score", "comm_report_user_expe": "Experience", "comm_report_score": "Score", "comm_report_provider": "Provider", "comm_statistical_angle": "Stats View", "comm_angle_failure": "<PERSON><PERSON> ", "comm_angle_event": "Event ", "comm_event_query": "Event", "comm_fault_query": "<PERSON><PERSON>", "comm_repeat": "Repeat", "comm_server_name": "Name/IP", "comm_enter": "Please enter ", "comm_sender": "Sender Name", "comm_email": "Send email", "comm_port1": "Port", "comm_mailbox": "Mailbox user", "comm_email_password": "Email password", "comm_email_test": "Test receive email", "comm_segment": "Target address segment", "comm_generation": "Deterioration generation threshold", "comm_template": "Select template type", "comm_message_type": "Select message type", "comm_fuzzy_query": "Support fuzzy query name and content", "comm_template_name": "Name：", "comm_fill_message": "Please enter template name", "comm_remarks": "Remarks", "comm_task": "Task", "comm_unit_one": "Unit", "comm_gross_amount": "Gross amount", "comm_interruption": "Interrupt", "comm_delay": "Delay deterioration", "comm_loss_package": "Packet loss deterioration", "comm_loss_package_simple": "Packet loss", "comm_rout_changes": "Route Instability", "licence_end": "license end, upload attachments in excess of the record will be automatically truncated, no longer notice!!", "comm_equal": "Equal", "comm_not_equal_to": "Not equal to", "comm_contain": "Contain", "comm_not_contain": "Exclude", "comm_Device_name": "Device Name", "comm_deal_with": "To deal with", "comm_enter_name": "Please enter the device name", "comm_enter_type": "Please enter the device type", "comm_enter_show_code": "Please enter the type code", "comm_local_address": "Local address", "comm_enter_device_name": "Enter the device name", "comm_enter_device_ip": "Enter the device IP address", "comm_port_name": ", port number：", "comm_normal": "Normal", "common_degradation_threshold": "deterioration threshold", "common_alarm_recovery": "Alarm recovery Settings", "common_syn": "Auto syn", "common_opa": "Manual syn", "comm_normal_line": "normal line", "pathtopo_enter_probe_port": "Please enter the probe port", "comm_deteriorate": "Deteriorate", "comm_interrupted": "Interrupted", "comm_deterioration": "Deterioration", "comm_unknown": "Unknown", "comm_not_collected": "NotCollected", "comm_Blackout": "Blackout", "comm_failure_up": "Fault Escalation", "comm_failure_dwon": "Fault De-escalation", "comm_path_switching": "path switching", "comm_failure_re": "failure recovery", "comm_failure_link_recovery": "Failed link recovery", "comm_cut": "cut", "comm_congestion": "congestion", "comm_probe": "Probe", "comm_times": "times", "comm_mid_node": "Middle  node", "comm_unknown_node": "Unknown node", "comm_two_node": "Layer two node", "comm_target_node": "Target node", "comm_urgent_notice": "Details of urgent notice", "comm_symptom": "Fault Symptom Details", "comm_portStatus": "Port Status Details", "comm_path": "Initial path", "comm_untreated": "Unprocessed", "comm_process": "Processing", "comm_closed_loop": "Closed loop", "comm_enterprise_network": "Enterprise Network", "comm_special_line_network": "Leased Line", "comm_other": "Other", "comm_pl_enter_ip": "Please enter the IP address", "comm_source": "Data source", "comm_name": "Name", "comm_type": "Type", "comm_describe": "Description", "comm_note": "Remark", "comm_status": "Status", "comm_operate": "Operations", "comm_a_ip": "A-Device IP", "comm_z_ip": "B-Device IP", "comm_interrup_times": "Interrupt Total Count", "comm_interrup_total": "Interrupt Total Duration", "comm_deterioration_times": "Deterioration Total Count", "comm_deterioration_total": "Deterioration Duration", "comm_packetLossDegradation_times": "Packet Loss Deterioration Total Count", "comm_packetLossDegradation_avg": "Packet Loss Deterioration Avg Duration ", "comm_packetLossDegradation_duration": "Packet Loss Deterioration Duration", "comm_delayDegradation_times": "Delay Deterioration Total Count", "comm_delayDegradation_avg": "Delay Deterioration Avg Duration", "comm_delayDegradation_duration": "Delay Deterioration Duration", "comm_usable_rate": "Availability", "comm_good_rate": "Excellent", "comm_data": "Unmatched data", "comm_add": "Create ", "comm_addresses": "Fuzzy search by IP addresses of suspected fault links is supported", "comm_faulty": "Suspected Fault Link IP/dial task code/relay code/Fault Phenomenon", "comm_faulty2": "Suspected fault link IP address/Dial task number/Fault Symptom", "comm_IP_collector": "IP address or collector", "comm_work": "Work order", "comm_difference": "Link quality difference analysis", "comm_private": "Details about private line congestion", "comm_classification": "Function class", "comm_filtering": "Object filtering", "comm_topo": "Topological diagram", "comm_topo_view": "When entering the physical topology, the default is：", "comm_add_topo": "Add to the topology view", "comm_remove_topo": "Remove the topology", "comm_test_phone": "Test tel", "comm_enter_test_phone": "Please enter the test phone number", "comm_content": "Test content", "comm_Initial_TTL": "Initial TTL", "comm_task_name": "Task Name", "comm_task_code": "Task Code", "comm_source_ip": "Source IP", "comm_target_ip": "Target IP", "comm_target_name": "Target Name", "comm_target_number": "Impact Targets", "comm_target": "Impact Targets", "comm_relevant_fault": "Associated Faults", "comm_relevant_event": "Associated Events", "comm_interruption_sup_time": "Interrupt Stack Duration", "comm_sup_time2": "Stack Duration", "comm_interruption_top5": "TOP5 of Interrupt", "comm_degradation_top5": "TOP5 of Deterioration", "comm_link_index": "Statistical Index", "comm_relay_indicators": "statistical Index", "comm_relay_details": "Relay details", "comm_link": "Link", "comm_link_interrup_times": "Interrupt Total Count", "comm_link_de_times": "Deterioration Total Count", "comm_link_de_long_time": "Deterioration Duration", "comm_break_avg_long_time": "Interrupt Avg Duration", "comm_avg_long_time": "Avg Duration", "comm_probe_ip_address": "Enter the probe IP address", "comm_probe_destination_ip_address": "Please enter target IP", "comm_dial_type": "Please select a Dial Test type", "comm_integers": "Only positive integers greater than 0 can be entered", "comm_starting": "The start TTL cannot be greater than the TTL limit hops", "comm_greater30": "The TTL limit hops cannot be greater than 30", "comm_restricted": "Cannot be less than the starting value", "comm_TTL": "TTL limited hops：", "comm_rule_set": "Rule parameter setting:", "comm_segment_mistake": "The destination address segment is incorrect", "comm_length128": "The length cannot exceed 128", "comm_length30": "The length cannot exceed 30", "comm_length50": "The length cannot exceed 50", "comm_upload": "Please upload the file", "comm_enter_destination_port": "Please enter target port", "comm_branch": "Please select a branch", "comm_mail": "Mail", "comm_fill_verification": "Please fill in the verification password", "comm_secret_key": "Please fill in the secret key", "comm_secret_key1": "Secret key", "comm_fuzzy": "Fuzzy search by dial task number, probe IP/port, and target name is supported", "comm_sure_information": "Are you sure want to enable the selected interface information?", "comm_branch_deleted": "The branch is deleted successfully", "comm_enter_user": "Please enter the user name", "comm_server": "Please enter the server IP address", "comm_serve_port": "Please enter the server port", "comm_device_model": "Enter the device model", "comm_message": "Message", "comm_nail": "DingTalk", "comm_voice": "Voice", "comm_enterprise_wechat": "Enterprise wechat", "comm_mail_service": "Mail Service", "comm_sms_service": "SMS Service", "comm_wechat_service": "WeChat Service", "rpmanager_org_name2": "orgName", "comm_dingtalk_service": "DingTalk Service", "comm_enterprise_wechat_service": "Enterprise wechat Service", "comm_message_service": "Message Service", "comm_choose_time": "Please Choose The Time", "comm_download_tpl": "Download template", "comm_download_tpl1": "Please download template", "comm_use_download_tpl": "Use templates for data editing, import!", "comm_import_right": "(If there is an error, only import the correct item, or adjust excel and upload again)", "comm_export_error": "Export error", "comm_probe_port": "Probe Port", "comm_probe_ip": "Probe IP", "comm_please_enter": "Please enter ", "comm_please_enter_name": "Please enter a name ", "comm_service": "Service", "comm_service_name": "Service name", "comm_service_path": "Deployment path", "comm_monday": "Mon", "comm_tuesday": "<PERSON><PERSON>", "comm_wednesday": "Wed", "comm_thursday": "<PERSON>hu", "comm_friday": "<PERSON><PERSON>", "comm_saturday": "Sat", "comm_sunday": "Sun", "comm_no_data": "No data", "comm_conditions": "Conditions", "common_select_group": "Please select group", "common_group": "Group", "common_get_data_error": "Failed to get the data", "common_enable_prompt": "Enable prompt", "common_disable_prompt": "Pause prompt", "common_forbidden_prompt": "Pause prompt", "common_delete_prompt": "Delete  prompt", "comm_ip_correct": "Please complete the correct ip", "comm_dial_protocol": "Dial type", "comm_dial_protocol1": "Dial Test Protocol", "comm_port": "Port", "comm_time_range": "Time Period", "comm_repeat_cycle": "Repeat Cycle", "comm_operation_scheduling": "Operation scheduling", "comm_add_to_group": "Add to Group", "comm_deleted": "The selected data contains the data that is being deleted. Please select a new one", "comm_unmask": "Are you sure want to unmask this data?", "comm_deleteing": "The selected data contains the data that is being deleted. Do not repeat this operation", "comm_nailing_service": "Nailing service", "comm_example": "Example :(5 or 5-7)", "comm_responsible": "Support resp person, resp person tel for fuzzy matching search", "comm_search_ip_name": "Support device IP and device name for fuzzy matching search", "comm_search_ip_device_task": "Support device IP、device name、task code for fuzzy matching search", "comm_search_code_device_ip_group": "Support device code、device name、device IP、grouping for fuzzy matching search", "comm_variable": "variable", "comm_start": "Select start time", "comm_year": "Please select the year", "comm_matching": "Fuzzy matching is supported for probe IP, target IP/target names, task ids, path ids", "comm_matching2": "Fuzzy matching by application name and AgentId is supported", "comm_enabled": "Enabled", "comm_uncorrelated": "Unrelated", "comm_unlinked": "Unlinked", "comm_deleting": "deleting", "comm_deleting2": "Do not perform other operations during data deletion", "comm_time_window": "Time", "comm_last_1hour": "Last 1 hours", "comm_last_3hour": "The last 3 hours", "comm_last_4hour": "The last 4 hours", "comm_last_6hour": "The last 6 hours", "comm_last_8hour": "The last 8 hours", "comm_last_24hour": "The last 24 hours", "comm_alarms_number": "Number of alarms", "comm_select_manufacturer": "Please select merchant", "comm_relay_index": "Statistical Index", "comm_select_start_time": "Please select the start date", "comm_select_end_time": "Please select the end date", "comm_select_cycle": "Please select a cycle", "comm_please_select": "Please select ", "comm_from": "From", "comm_new": "Create", "comm_automatic_dial": "automatic dial", "comm_deal": "<PERSON><PERSON> ", "comm_add_on": "Add on ", "comm_changed_permission": "Permission changed successfully", "comm_changed_successful": "Edit success", "comm_add_successful": "Add success", "comm_function_list": "Function list", "comm_no_boot": "No boot to boot is selected!", "comm_select_time1": "Please select the time", "comm_add10": "Add a maximum of 10 speed measuring times", "comm_physical": "Abnormal items of the physical topology line", "comm_data_import": "No data to import!", "comm_data_deletion": "No data is selected for deletion!", "comm_indicator": "Reserve at least one indicator", "comm_permission": "No permission has been configured, please contact the administrator to configure!", "comm_fill_assignment": "Please fill in the assignment type", "comm_pause2": "No task is selected to pause!", "comm_range": "Select a time range for query", "comm_indicators": "The maximum number of indicators has been reached", "comm_time_period": "Time Period", "comm_select_time_tip": "Please select the time period for the query", "comm_unlikelihood": "unlikelihood", "comm_topo_mgt": "Topo management", "comm_jump_prompt": "Jump prompt", "comm_configuration": "If no view is configured, the Configuration view page will be displayed in 3 seconds", "comm_topo_select": "Topology selection", "comm_topo_set": "Info display set", "comm_topo_legend": "Legend", "comm_topo_alarm": "Alarm", "comm_alarm_triggering": "Alarm triggering", "comm_alarm_recovery": "Alarm recovery", "comm_topo_device_name": "Device Name", "comm_topo_device_id": "Device ID", "comm_topo_device_ip": "Device IP", "comm_topo_device_flow": "Line Flow", "comm_topo_device_delay": "Line Delay", "comm_topo_device_loss": "Line Loss", "comm_select_delete_data": "Please check Delete data first", "comm_msg_del_record": "<p> Are you sure want to delete these records?</p>", "comm_msg_activate_record": "<p> Are you sure want to activate these records?</p>", "comm_msg_freeze_record": "<p> Are you sure want to freeze these records?</p>", "comm_tip": "Tip", "comm_large_value": "The fill value is too large", "comm_must_less": "This value must greater than ", "comm_must_greater": "This value must less than ", "comm_must_less_attribute": "This value must less than the associated attribute", "comm_must_greater_attribute": "This value must greater than the associated attribute", "comm_value": "value", "comm_correct_value": "Please fill in the correct value", "comm_item_filled": "The item is not filled in", "comm_item_filled_round": "Please fill in round numbers", "comm_ordinary_dial": "Ordinary dial", "comm_line_task": "Private line task", "comm_snmp_monitoring": "snmp monitoring", "comm_high_freq_monitoring": "High-frequency monitor", "comm_high_freq": "High-frequency", "comm_relay_task": "Relay task", "comm_grouping": "Group", "comm_end_to_end": "End to End", "comm_om_level": "Level", "comm_delay(ms)": "Delay (ms)", "comm_loss_rate": "Packet loss rate (%)", "comm_unit": "Unit", "comm_tip2": "When repeating items are selected, select at least one repeat period item", "comm_tip3": "Multiple time ranges overlap. Please select another time range", "comm_select": "Please select ", "comm_select_type": "Please select the target type ", "comm_target_type": "Target type ", "comm_note_content": "Remark content", "comm_delay_packet": "delay + packet loss rate", "comm_device": "device Information", "comm_failure_recovery": "Fault recovery time", "comm_failure_start": "Fault start time", "comm_event_start": "Event start time", "comm_event_recovery": "Event recovery time", "comm_event_type_all": "All event type", "comm_failure_type_all": "All fault type", "comm_duration_event": "Event duration", "comm_duration_failure": "Fault duration", "comm_Index_name": "Index name", "comm_cycle": "Acquisition cycle", "comm_job_type": "Job type", "comm_select_topology_type": "Please select a topology type", "comm_opology_type": "topology type", "comm_multiple": "Multiple mobile phones can be entered, separated by commas", "comm_port_number": "Port", "comm_enter_content": "Please enter the content", "comm_content1": "Content", "comm_supported": "Fuzzy query by content is supported", "comm_disclaimer": "Disclaimer", "comm_ranking_loading": "Ranking loading", "comm_select_job_type": "Select job type", "comm_select_file": "Select file", "comm_no_select_file": "No file selected", "comm_number": "Interrupt line number", "comm_line_number": "Degraded line number", "comm_edit": "Edit", "comm_objects_add": "Add objects", "comm_objects_removed": "Remove object", "comm_line_management": "Line management", "comm_event": "Event type", "comm_prompt": "Startup prompt", "comm_prompt1": "Prompt", "comm_pause_prompt": "Pause prompt", "comm_Job_adjustment": "Job adjustment success", "comm_Job_adjustment_fa": "Job adjustment fault", "comm_adjust": "Select the task you want to adjust", "comm_up10t": "Add up to 10 time periods", "comm_ip_segment": "Use the same ip segment and connect with '-'", "comm_ip_segment1": "Use the same ip address segment", "comm_ip_segment2": "Enter ip address segments in ascending order", "comm_select_probe": "Please select the probe", "comm_select_dial_type": "Please select the dial type", "comm_sure": "Are you sure want to empty the validation data?", "comm_imported": "Verify the accessibility of the imported template", "comm_remote": "During the remote backup, the remote information is not complete!", "comm_backup": "Backup succeeded", "comm_open": "on", "comm_close": "Off", "comm_view_details": "View Details", "comm_select_upload_file": "Please select a file to upload", "comm_upload_file_msg_1": "Uploading, please do not repeat", "comm_upload_file_msg_2": "Reading data, please later", "comm_file_incomplete": "The above information input is incomplete", "comm_import_confirmation": "Import confirmation", "comm_map_incomplete": "Incomplete mapping correspondence", "comm_file_upload_again": "The uploaded file is incorrect, please upload it again", "comm_data_temp": "Leased line data template", "comm_error_list": "List of duplicates or anomalies", "comm_operators": "ISP", "comm_packet_loss": "Packet loss rate", "comm_inflow": "InflowVelocity", "comm_outflow": "OutflowVelocity", "comm_failed_menu": "Failed to get menu!", "comm_duration": "Interrupt Duration", "comm_duration1": "Duration", "comm_alarm_sound_tip": "<p>Due to browser related security restrictions, the alarm sound prompt has been turned off. If you need to turn on the sound prompt, please manually turn it on!</p>", "comm_alarm_sound_open_btn": "On", "comm_alarm_sound_close_btn": "off", "comm_alarm_sound_open_success_tip": "Prompt tone has been turned on", "comm_alarm_sound_close_success_tip": "Reminder tone turned off", "comm_alarm_table_alarm_type": "Alarm Type", "comm_alarm_table_popup_prompt": "Popup prompt", "comm_alarm_table_sound_effect_prompt": "Sound prompt", "comm_alarm_table_auto_close": "Close", "comm_alarm_table_projection_screen": "Projection screen", "common_more": "More", "common_disable": "Pause", "comm_tip1": "task code, source/target IP or target name", "automatic_discovery_management": "Discovery", "topo_def_view_note": "When entering the path topology map, the default open topology map", "topo_pl_graph_name": "Please enter a topology graph name", "topo_pl_graph_group": "Please select the topology graph group", "topo_pl_line_name": "Please enter the line name", "topo_pl_dev_ip": "Please enter the device IP", "topo_pl_dire": "Please select a direction", "topo_pl_port_1": "Please enter port 1", "topo_pl_port_2": "Please enter port 2", "topo_pl_device_1": "Please select device 1", "topo_pl_device_2": "Please select device 2", "topo_pl_device": "Please select device", "topo_line_name": "Line name", "topo_name": "Topo name", "topo_group": "Topo group", "topo_group_note": "The path topology map automatically displays the paths of 50 Dial Test tasks within the group", "topo_max_num": "<PERSON> present", "topo_max_num_note": "Refers to the maximum number of displayed nodes on the topology map, and the system will intelligently shrink too many nodes into an icon that can drill down to view the subgraph", "topo_max_num_500": "Please enter a positive integer greater than or equal to 100 and less than or equal to 500", "topo_object_num": "Manage Objects Ncomm_operation_successfullymber", "topo_pl_conn_device1": "Please select Connect Device1", "topo_pl_conn_device2": "Please select Connect Device2", "topo_resource_selection": "Resource selection", "topo_conn_device1": "Connect Device 1", "topo_conn_device2": "Connect Device 2", "topo_conn_device_ip1": "Connect the device IP1", "topo_conn_device_ip2": "Connect the device IP2", "topo_port_1": "Port 1", "topo_port_2": "Port 2", "topo_port_1_des": "Port 1 describes", "topo_port_2_des": "Port 2 describes", "topo_deport1_to_deport2": "Device 1 port to Device 2 port", "topo_deport2_to_deport1": "Device 2 port to Device 1 port", "topo_up_direction": "Up direction", "phys_list_keyword": "Support fuzzy matching of line names", "phys_add_line_note": "<p> This action updates the automatically added circuits in the map, and does not involve manually adding circuits, which may change </p>", "shieldlist_alias": "Alias：", "dash_panel": "Instrument panel", "dash_panel_edit": "Edit view", "dash_edit": "Edit view", "dash_minutes_60": "60minutes", "dash_customize": "Customize", "dash_view_type": "View Type", "dash_create_view": "Create view", "dash_up": "Move up", "dash_down": "Move down", "dash_top": "The current view is already at the top", "dash_end": "The current view is at the end", "dash_enter_name": "Please enter a view name", "dash_length": "The length shall not exceed ", "dash_rows": "Line Number", "dash_columns": "Column Number", "dash_name": "View name", "dash_template_name": "Template", "dash_other": "Other", "dash_scale": "Screen Scale", "dash_scale_error": "Please enter a custom screen scale", "dash_scale_number_error": "Custom screen proportions can only be entered as positive integers", "dash_description": "Description", "dash_delete": "Make sure to delete the selected view", "dash_delete_failure": "Delete fault", "dash_interface": "Specify the address of the local interface", "dash_IP": "Enter the IP address of the local device", "dash_trunkt": "Please enter the trunk name", "dash_special_line": "Summary of Leased Line operation", "dash_configured": "This view has not configured components!", "dash_interrupt_alarm": "Interrupt Alarm", "dash_deterioration_alarm": "Deterioration Alarm", "dash_prompt_alarm": "Prompt Alarm", "dash_no_content": "No content", "dash_quality_nodes": "The number of quality difference nodes in the last week is TOPN", "dash_Special_line": "Leased Line", "dash_order_generation_time": "Work order create time", "dash_faulty_link": "Suspected <PERSON>", "dash_fault_state": "Fault Status", "dash_commissioning_task": "Probe Task", "dash_device_task": "Device Task", "dash_commissioning_task_num": "Task code", "dash_relay": "<PERSON><PERSON>", "dash_node_ip": "Node IP", "dash_node_name": "Node Name", "dash_normal": "Normal", "dash_deterioration": "Deterioration", "dash_interrupt": "Interrupt", "dash_interrupt1": "Interrupt", "dash_pause": "Pause", "dash_enable": "Enable", "dash_start_up": "Enabled", "dash_alarm_time": "Alarm Time", "dash_half_hour": "Half an hour", "dash_fault_type": "Fault type", "dash_delay_deterioration": "Delay deterioration", "dash_packet_loss": "Packet loss deterioration", "dash_routing_fluctuation": "Route Instability", "dash_urgent_notice": "Urgent notice", "dash_line_congestion": "Leased Line Congestion", "dash_port_congestion": "Port Congestion", "dash_link_faulty": "Suspected <PERSON>", "dash_far": "As far as", "dash_view_list": "View list", "dash_related": "Due to browser related security restrictions, the alarm sound prompt has been turned off. If you want to enable the sound alert, please manually enable it!", "dash_anomaly": "Network anomaly", "dash_clear": "Are you sure want to clear these records?", "dash_select_delete": "Please select Delete data first", "dash_clear_successfully": "Clear successfully", "dash_sure": "Are you sure want to delete this line", "dash_masking": "Masking success", "dash_unmasking": "Unmask success", "dash_moved": "The current logged-in user cannot be moved out", "dashboard_add_component": "Add component", "dashboard_save_set": "Save Settings", "dashboard_component_group_tip": "After a group is selected, only the data in the group is analyzed. If no group is selected, all data is analyzed", "dashboard_no_configured": "No indicator is configured", "dashboard_not_greater_than_20": "The value should be a positive integer and not greater than 20", "dashboard_integer_not_than_20": "Is a positive integer not greater than 20", "dashboard_enter_org_name": "Please enter a component name", "dashboard_not_allowed_special": "Special characters are not allowed", "dashboard_len_not_exceed": "The length shall not exceed", "dashboard_position": "Location", "dashboard_support_sheet": ", Support sheet", "dashboard_support_sheet2": ", supports single lines and multiple lines；", "dashboard_more": "More", "dashboard_example_57": " For example :(5 or 5-7)", "dashboard_type": "Type", "dashboard_name": "Name", "dashboard_no_show_name": "Show name", "dashboard_no_show_border": "Show border", "dashboard_refresh_interval": "Refresh interval", "dashboard_cross_bank": "Cross row", "dashboard_cross_column": "Cross column", "dashboard_alarms_displayed": "Alarm number", "dashboard_selective_topology": "Select a topo", "dashboard_select_map": "Select map", "dashboard_select_province": "Province/municipality", "dashboard_object_type": "Object type", "dashboard_one_minute": "1 minutes", "dashboard_five_minutes": "5 minutes", "dashboard_ten_minutes": "10 minutes", "dashboard_thirty_minutes": "30 minutes", "dashboard_no_refresh": "No refresh", "dashboard_select_artifact_type": "Select the artifact type", "dashboard_selec_refresh_interval": "Please select the refresh interval", "dashboard_line": "Line", "dashboard_column": "Column", "dashboard_region": "Region", "dashboard_color": "Color", "dashboard_line_name": "Line name", "dashboard_task_number": "Task code", "dashboard_ob": "Object", "dashboard_port": "Port", "dashboard_index": "Index", "dashboard_url": "URL Link", "dashboard_alarm_statistics_type": "Statistical dimension", "dashboard_alarm_statistics_select1": "Number of new alarms added each day", "dashboard_alarm_statistics_select2": "Number of alarm objects generated each day", "dashboard_select_ot": "Please select an object type", "dashboard_select_ob": "Please select an object", "dashboard_select_indicators": "Please select indicators", "dashboard_first_select_ot": "Select the object type first", "dashboard_added_monitoring": "Select objects need to be added for real-time monitoring", "dashboard_maximum_selected_300": "A maximum of 300 objects can be selected", "dashboard_graphics_metrics": "Aggregate graphics to add metrics", "dashboard_not_same_config": "Do not configure components in the same location", "dashboard_edit_component": "Edit component successful", "dashboard_no_configured_add_artifacts": "No artifacts have been configured, please add artifacts", "dashboard_whether_del_after_saving": "Whether to delete this component and take effect after saving", "dashboard_add_indicator_error_tip": "The aggregation graph indicator already has the same indicator", "dashboard_add_graphics_metrics_error_tip": "Aggregated graphics require add metrics", "dashboard_delay": "Delay ", "dashboard_lost_packet": "Lost packet ", "dashboard_availability": "Availability", "dashboard_excellent_rate": "Excellent ", "dashboard_upstream": "Up Flow Rate ", "dashboard_down": "Down Flow Rate ", "dashboard_uplink": "Up Bandwidth Rate ", "dashboard_downstream": "Down Bandwidth Rate", "dashboard_inlet_velocity": "Input rate", "dashboard_normal_view": "Normal view", "dashboard_select_type": "Please select a view type", "dash_delete_view_succ": "Delete success", "dashboard_polymerization_view": "Polymerization view", "dashboard_url_view": "URL Link", "dash_delete_view": "Delete view", "dashboard_sort": "Sort", "dashboard_integer": "be a positive integer ", "dashboard_integer_tip": "Should be a positive integer, for example :(5 or 5-7)", "dashboard_integer_tip2": "The value cannot exceed the maximum value of @max configured for the current view ", "dashboard_integer_tip3": "Cannot be less than @min", "dashboard_range": "the range is ", "dashboard_outflow_velocity": "Output rate", "dashboard_inflow_rate": "IN Utilization", "dashboard_outflow_rate": "OUT Utilization", "default_manager_enter_password": "Please enter password", "default_manager_switchboard": "Switch", "default_manager_router": "Router", "default_manager_Linux_server": "Linux server", "default_manager_camera": "Camera", "default_manager_routing/switching": "Routing/switching", "default_manager_printer": "Printer", "default_manager_PC": "PC terminal", "report_select_type": "Please select report type", "report_statistics_time": "Report stat time", "report_please_provider": "Please enter the provider", "report_name_len_50": "Name length cannot exceed 50", "report_des_leng_200": "Description length cannot exceed 200", "report_provider_len_50": "Report provider length cannot exceed 50", "discover_msg_ip_format": "IP segment not specification (former IP before last IP)", "discover_msg_delete": "<p> Are you sure want to delete these data?</p>", "discover_msg_enable": "<p> Are you sure want to enable these data?</p>", "discover_msg_disable": "<p> Are you sure want to disable these data?</p>", "discover_msg_add_10": "Add Up to 10 time periods", "discover_rule_name": "Rule Name", "discover_rule_1": "Rule name, IP address range, and collector", "discover_add_group": "Add to group", "discover_pd_rule_name": "Please fill in the rule name", "discover_pd_keyword": "Rule name, IP range, and collector", "discover_pd_rule_keyword": "Rule name, Start IP, or End IP, Collector", "discover_has_monitor": "Whether monitored", "discover_has_monitor1": "Whether Monitored", "discover_uplink_ip": "IP(Uplink IP)", "discover_gether_name": "Collector", "discover_ip_rang": "IP address range", "comm_not": "not yet", "common_degradation": "Delay deterioration", "discover_date_lang": "Please select a time period", "discover_select_type": "Select type", "discover_device": "Discovery Device", "discover_device_type": "Node type", "discover_device_type_showCode": "Type code", "discover_device_type_tips": "There are spaces in the device type", "discover_device_factory_tips": "There are spaces in the device factory", "discover_device_factory_showCode": "Merchant code", "discover_time": "Discovery time", "discover_time_note": "Note: Up to 10 time periods are supported", "discover_select_device_type": "Please select device type", "discover_rule": "Discovery Rule", "discover_last_time": "Last scan time", "discover_maintain_level": "Level", "discover_level": "Level", "discover_select_maintain_level": "Please select level", "discover_select_inter_time": "Please fill in the time interval", "discover_msg_mutime": "Multiple time periods are crossed, please reselect", "discover_create_task": "Create Task", "discover_donot_create_task": "Do not create tasks", "discover_search_keyword": "IP、Collector", "discover_dis_inter": "Find interval", "discover_discovery_rule": "Discovery rule", "discover_dial_protocol": "Dial Test protocol", "discover_port": "port", "discover_discovery_time": "Discovery time", "discover_equipment_type": "Device type", "discover_discovery_status": "Discovery status", "discover_dial_probe": "Mapping probe：", "discover_upload_attachment": "Upload attachment", "discover_select_upload_file": "upload", "discover_duplicate_term": "Duplicate term", "discover_cannot_be_null": "Cannot be empty", "discover_character_length": "The character length cannot exceed 200 characters", "discover_save_failure": "Save fault", "discover_source_device": "source-to-device", "discover_connected_device": "Connected from source to device", "analyse_spec_name": "LL Name", "analyse_spec_code": "Special Code", "gether_name": "Name", "gether_type": "Type", "gether_authorized_org": "Authorized org", "gether_select_type": "Please select the collector type", "gether_select_collector": "Please select the collector", "gether_select_snmp": "Please select the SNMP version", "gether_fill_snmp_port": "Please fill in the SNMP port", "task_name": "Task Name", "task_status": "Task Status", "task_type": "Level", "task_normal_dial_test": "Normal Dial Test", "task_pd_dial_type": "Please select the dial type", "task_dial_type": "Dial type", "licence_num_start": "Tip: You currently have remaining license", "licence_num_end": "1, upload the record in the attachment will be automatically truncated, no longer notified!", "wifi_search_dete_time": "Detect  time", "wifi_search_keywrod": "Features and room no or SSID", "wifi_report_time": "Report time", "wifi_features": "Features and room no", "wifi_band": "Channel", "wifi_bandwidth": "Channel bandwidth", "wifi_agreement": "Protocol", "wifi_signal_strength": "Signal", "wifi_neg_rate": "Negotiated rate", "wifi_down_rate": "Download rate", "wifi_up_rate": "Upload rate", "wifi_internal_down_rate": "Internal download rate", "wifi_internal_up_rate": "Internal upload rate", "wifi_internal_speed": "Internal network speed", "wifi_gateway_res": "Gateway response", "wifi_problem_item": "Issue item", "wifi_find_problem": "Find the problem", "wifi_test": "Wifi Test", "wifi_grade_proportion": "Proportion of experience levels at each location", "wifi_detail": "Detail", "wifi_loading_failed": "Image loading failed", "wifi_access_wifi": "Access WIFI", "wifi_download": "Download", "wifi_highest": "<PERSON><PERSON><PERSON>", "wifi_average": "Avg", "wifi_bad": "Poor", "wifi_bad_t": "Bad", "wifi_bad1": "P", "wifi_tiktok": "Tiktok", "wifi_video_conference": "Video Conference", "wifi_high_video_conference": "HD video", "wifi_do_office_work": "Office", "wifi_problem_introduction": "Problem Introduction", "wifi_solution_suggestions": "Solution suggestions", "wifi_chat": "Cha<PERSON>", "wifi_monitor": "Monitor", "wifi_4K_monitor": "4K Monitor", "wifi_signal_interference": "Signal interference", "wifi_connection_rate": "Connection Rate", "wifi_internal_network_response": "Intranet response", "wifi_encryption_protocol": "Encryption protocol", "wifi_illustrate": "Illustrate", "wifi_advise": "Advise", "wifi_good": "Excellent", "wifi_good1": "E", "wifi_gateway_delay": "Gateway delay (pressure)", "wifi_channel_width": "Channel bandwidth", "wifi_channel_frequency": "Channel frequency", "wifi_frequency_band": "Frequency band", "wifi_channel": "Channel", "wifi_devices": "Number of online devices", "wifi_fine": "Good", "wifi_fine1": "G", "wifi_centre": "Medium", "wifi_centre1": "M", "wifi_order": "count", "wifi_proportion_tip": "Proportion of experience levels at each location", "spec_time_granularity": "Time", "spec_line_code": "LL NO", "spec_line_relationship": "Master-slave", "spec_acc_number": "Assoc LL NO", "spec_line_logotype": "logotype", "spec_line_duplic": "Leased Line duplicates", "spec_no_duplicates": "No error item", "comm_line_deleted": "Line deleted successfully", "spec_no_valid": "No valid data, please re-edit and import", "spec_identification": "ID", "spec_line_uplink_broad": "UpBandwidth", "spec_line_down_broad": "DownBandwidth", "spec_a_end_device_ip": "A-Device IP", "spec_z_end_device_ip": "Z-Device IP", "spec_a_end_inter_ip": "A-Internet-IP", "spec_z_end_inter_ip": "Z-Internet-IP", "spec_indic": "Metric", "spec_line": "Leased Line", "spec_select_line": "Select Leased Lines", "spec_indic_statis": "Leased line statistical indicators", "spec_date_type_1": "Month", "spec_date_type_2": "Week", "spec_date_type_3": "Custom", "spec_traffic_tip": "Tip: If the traffic cannot be matched, enter the IP address of the device on the A or Z end", "spec_pl_enter_line_name": "Please enter a LL name", "spec_pl_enter_line_number": "Please enter the LL NO", "spec_search_keyword": "Supports fuzzy matching LL NO、LL name, A-Internet-IP, Z-Internet-IP, Z-end alias", "spec_export_file_name": "Leased Lines health records.xlsx", "report_generate_statistical": "Generate statistical reports", "path_routine": "Normal", "topo_path_topology": "path topology", "topo_data_deleted": "The data has been deleted", "topo_select_delete_first": "Please select Delete data first", "topo_100_500": "Please enter a positive integer greater than or equal to 100 and less than or equal to 500", "poto_maximum": "<PERSON>", "poto_management_mode": "Method", "poto_select_one": "Select at least one object", "topo_50_dial": "The path topology automatically displays the paths of 50 dial and test tasks in a group", "topo_down": "Indicates the maximum number of nodes displayed on the topology. The system automatically shrinks too many nodes to an icon that can be drilled down to view the subgraph", "topo_select_mode": "Please select a management mode", "topo_l50": "Please enter a positive integer less than 500", "poto_object_filtering": "Filter objects", "path_fiber": "Fiber", "path_qu_search_keyword": "Support for source IP, target IP, target name, and task name for fuzzy matching search", "node_qu_search_keyword": "Support the source IP, link for fuzzy matching search", "msg_config_search_keyword": "Support queries by task number, probe IP, target IP, etc", "oid_search_keyword": "Support for fuzzy query by name, OID", "path_hthy_search_keyword": "Support fuzzy match task number, IP, target name", "rpmger_search_keyword": "Supports fuzzy search by relay number, relay name, local terminal / paired end device IP、alias", "rpmger_search_keyword2": "Supports fuzzy matching search by relay code, relay name, local / remote device IP, local / remote alias", "rphth_search_keyword": "Supports fuzzy matching search by relay name, local device IP, local / remote address and remote alias", "rpath_search_keyword2": "Fuzzy search device name, local/remote address, local/remote alias", "rpath_search_keyword3": "Fuzzy search device name, local/remote address, local/remote alias", "rpqu_search_keyword": "Support relay name, local device IP, local/terminal address, terminal alias fuzzy search", "rpmanager_update": "Update", "rpmanager_basic_info": "Basic information", "rpmanager_trunk_name": "Trunk name：", "rpmanager_org_name": "org name", "rpmanager_local_addr": "Local Address：", "rpmanager_local_alias": "Local Alias：", "rpmanager_local_IP": "Local Device IP：", "rpmanager_choose_peer_ip": "Please select a device IP", "rpmanager_peer_addr": "Remote Address：", "rpmanager_peer_alias": "Remote Alias：", "rpmanager_local_interface": "Address of the local interface：", "rpmanager_peer_interface": "Address of the peer interface：", "rpmanager_combination": "The organization ID is incorrect. Please enter a 12-digit letter combination", "rpmanager_local": "Local Device：", "rpmanager_peer": "Remote Device：", "rpmanager_local_port": "Local port number：", "rpmanager_peer_port": "Remote port：", "rpmanager_peer_IP": "Remote Device IP：", "rpmanager_peer_dev_name": "Remote Device Name：", "rpmanager_peer_port_number": "Remote port number：", "rpmanager_monitoring_index": "Monitoring index", "rpmanager_alarm_para": "Alarm parameter", "rpmanager_base_info_para": "Basic information parameter", "rpmanager_frequency": "frequency", "rpmanager_packet_loss": "Packet loss deterioration generation threshold, single hit：", "rpmanager_continuous": "Continuous：", "rpmanager_reach": "Reach：", "server_home_last_page": "Last page", "server_strip": "strip", "rpmanager_reach1": "Reach", "rpmanager_only_int": "Only positive integers can be entered", "rpmanager_alarm_packet": "Alarm parameter the packet loss deterioration threshold ranges from 1 to 100", "rpmanager_degree_not_0": "The degree cannot be 0", "rpmanager_relay_coding": "Code", "rpmanager_local_dev_name": "Local Device Name", "rpmanager_local_num": "Local Port", "rpmanager_peer_IP_addr": "Remote Device IP", "rpmanager_peer_IP_addr1": "Remote IP", "rpmanager_peer_name": "Remote Device Name", "rpmanager_peer_port_num": "Remote Port", "rpmanager_started": "Enabled", "rpmanager_suspended": "Paused", "rpmanager_deleting": "Deleting", "rpmanager_deleting_not_oper": "Do not perform other operations during data deletion", "rpmanager_enter_local_addr": "Please enter the local address", "rpmanager_enter_org_name": "Please enter the organization name", "rpmanager_enter_peer_addr": "Please enter the peer address", "rpmanager_len_not_128": "The length cannot exceed 128", "rpquality_health_records": "Test health records", "rpmanager_unmasking": "Unshield", "rpmanager_unprompt": "unprompt", "rpmanager_sure_unmask_data": "Are you sure you want to unshield this data?", "rpmanager_masking_success": "Shield success", "rpmanager_success_unmasking": "Unshield success", "rpmanager_select_unmask": "Select the data that you want to unmask", "rpmanager_selected_data_select_new": "The selected data contains the data that is being deleted. Please select a new one", "rpmanager_select_data_set": "Select the data you want to set", "rpmanager_successful_set": "Successful setting", "rpmanager_select_mask": "Select the data you want to mask", "rpmanager_mask_prompt": "Shield prompt", "rpmanager_block_data": "Are you sure you want to shield this data?", "rpmanager_sure_del": "<p>Make sure to delete", "rpmanager_these": "these", "rpmanager_article": "This article", "rpmanager_recorded": "Is it recorded?</p>", "rpmanager_deleted_not_repeat": "The selected data contains the data that is being deleted. Do not repeat this operation", "rpmanager_data_loading_failure": "Data loading fault", "rpmanager_alarm_integers": "The alarm parameters can only be positive integers", "rpmanager_consecutive_num_not_0": "The number of consecutive times cannot be 0", "rpmanager_alarm_loss_100": "Alarm parameter The packet loss deterioration threshold ranges from 1 to 100", "rpmanager_addr_combination_exists": "The IP address combination of the peer and local ends already exists", "rpmanager_trunk_modified_success": "The relay information is modified successfully", "rpmanager_select_upload_attachment": "Please select the attachment to upload", "rpmanager_trunk_xls": "Relay information.xls", "rpmanager_trunk_xlsx": "Relay information.xlsx", "rpmanager_download_xls": "Relay information.xls", "rpmanager_download_xlsx": "Relay information.xlsx", "rpmanager_health_download_xls": "Relay health information.xls", "rpmanager_health_download_xlsx": "Relay health information.xlsx", "rpmanager_update_verify_tip": "The combination of peer and local addresses already exists", "rpmanager_interrupt_deterioration_tip": "Number of relays that have not experienced interruption or deterioration within the statistical time range", "rpmanager_deterioration_tip": "Number of relays that have experienced deterioration within the statistical time range", "rpmanager_interrupt_tip": "Number of relays that have experienced interruption within the statistical time range", "rpmanager_quality_download_xls": "Relay quality difference report.xls", "rpmanager_quality_download_xlsx": "Relay quality difference report.xlsx", "rpmanager_snmp_export_trend_name": "snmp monitor-", "alarm_9999": "0 to 9999", "alarm_offline": "Collector offline", "alarm_congestion": "Traffic congestion details", "alarm_start_time": "Fault Start Time", "alarm_select_first": "Please select Recover data first", "alarm_recover": "Recover", "alarm_generation_time": "Fault Start Time", "alarm_all": "All", "alarm_l60m": "≤60 Minutes", "alarm_g60m": ">60 Minutes", "alarm_custom": "Custom", "alarm_minute": "Minutes", "alarm_symptom": "<PERSON><PERSON>", "alarm_symptom_placeholder": "Fuzzy query based on fault symptoms is supported", "alarm_failure_elapsed": "Affected path", "alarm_failure_duration": "Fault Duration", "alarm_failure_status": "Fault Status", "alarm_ticket_status": "Status", "network_append": "Append", "alarm_susp_failed_link": "Suspected <PERSON>", "alarm_failure_index": "Fault index", "gether_select_province": "Please select province", "gether_select_city": "Please select the city", "gether_verify_name": "Please select the collector name", "gether_verify_name_len": "The length cannot exceed 64", "gether_code": "Collector number", "gether_verify_type": "Please select the collector type", "gether_ip": "IP", "gether_verify_ip": "Please fill in the collector IP", "gether_last_time": "Last active time", "gether_level": "Level", "gether_tasks": "Number of tasks (normal/all)", "gether_search_keyword": "Name、IP", "gether_pl_type": "Please select the collector type", "gether_pl_status": "Please select a status", "snmp_local_alias": "Local alias", "snmp_local_device": "The name of the local device", "snmp_self_port_number": "Self-port number", "snmp_peer_device_ip": "Remote device IP", "snmp_peer_device_name": "Remote device name", "snmp_peer_port_number": "Remote port", "snmp_no": "NO.", "snmp_name": "Relay Name", "snmp_code": "Relay Code", "snmp_local_alias2": "Local Alias", "snmp_this_end_ip": "Local Device IP", "snmp_local_dev_name": "Local Device Name", "snmp_local_dev_port": "Local Port", "snmp_name1": "Name", "snmp_this_ip": "Local IP", "snmp_this_end_addr": "Local Address", "snmp_paired_end": "Remote Address", "snmp_paired_end_alia": "<PERSON><PERSON>", "snmp_delay_max": "Max delay(ms)", "snmp_delay_avg": "Avg delay(ms)", "snmp_loss_max": "Max loss rate", "snmp_loss_avg": "Avg loss rate", "snmp_inflow_max": "Max Inflow", "snmp_inflow_avg": "Avg Inflow", "snmp_outflow_max": "Max Outflow", "snmp_outflow_avg": "Avg Outflow", "snmp_device_man": "<PERSON><PERSON>", "snmp_manufacturer": "Merchant", "snmp_device_ip": "Device IP", "snmp_task_device_model": "Device Model", "snmp_task_op_level": "Level", "snmp_task_code": "Task code", "snmp_task_device_name": "Device Name", "snmp_task_device_ip": "Device IP", "snmp_pl_man": "Please select", "snmp_pl_model": "Select model", "group_name": "Group Name", "group_name2": "Name", "group_remove": "Remove", "group_search_keyword": "Support for fuzzy query by group name", "group_address_IP": "Target IP address：", "group_target_address": "Target address：", "group_target_name": ", target name：", "group_ClassB": "Class B traversal", "group_ClassA": "Class A traversal", "group_matching": "Fuzzy matching is performed by collector, device name, and device ip address", "group_matching2": "Fuzzy matching query is performed by task code, probe IP and target IP", "group_matching3": "Fuzzy matching query by task code, probe IP, target IP", "group_removed": "Removed success", "group_confirm_add": "<p>Confirm whether to add？</p>", "group_check_data": "Please check the data", "group_want_remove": "<p>Do you want to remove it?</p>", "group_description_content": "Description content", "group_length_exceed": "The length shall not exceed", "group_add": "Add", "group_select_want_remove": "Select the data you want to remove", "group_add_successfully": "Create success", "group_delete_successfully": "Delete success", "group_select_delete": "Select the data you want to delete", "snmp_relay_tasks": "Relay task count", "snmp_probe_tasks": "Probe task count", "interface_import_success": "Import success", "interface_import_failure": "Import fault", "interface_operation_again": "It has been uploaded once. Please go back to the previous step and perform the operation again", "interface_address": "Device IP, device name, interface name, interface IP", "interface_name": "Interface Name", "interface_status": "Interface status", "interface_dial_probe": "Mapping probe", "interface_ip": "Interface IP", "interface_enter_ip": "Please enter device IP", "interface_enter_name": "Please enter interface name", "interface_enter_interface": "Please enter the IP address of the interface", "interface_no_event": "There are no duplicates/exceptions", "interface_enter_address": "Enter the correct IP address", "interface_enter_bandwidth": "Please enter the correct bandwidth", "interface_mapping_dial": "Mapping dial probe", "interface_enter_device": "Please enter device name", "interface_value_maximum": "The value contains a maximum of 50 characters", "interface_select_probe": "Select Mapping Probe", "interface_select_manipulate": "Please select the data you want to manipulate", "interface_deactivated_success": "Successfully deactivated", "interface_select_export": "Select the data you want to export", "interface_information_management": "Interface Manage information", "interface_all_information_management": "All interface Manage information", "interface_update_success": "Update successfully", "interface_select_attachment": "Please select the attachment you want to update", "interface_log_details": "Log details", "interface_select_delete": "Select the data you want to delete", "comm_delete": "Delete", "comm_disabled": "Pause", "comm_loop": "Out of the loop", "comm_deleted_success": "Successfully deleted", "comm_deleted_failed": "Deletion failed", "comm_name_empty": "The name cannot be empty", "comm_name_exists": "Name already exists", "logback_enter_ip": "Enter the IP address", "logback_fill_userName": "Please fill in the user name", "logback_fill_password": "Please fill in the password", "logback_enter_path": "Enter the backup path", "message_delete": "Are you sure want to delete the selected message record?", "logback_select_content": "Please select content", "logback_select": "Select", "logback_delete": "Delete", "logback_selective_grouping": "Selective group", "logback_supports_query": "Supports query by group name", "logback_select_dial": "Select a Dial Test task", "logback_select_relay": "Select relay task", "logback_equal": "Equal to", "logback_no_equal": "No equal to", "logback_first": "First level", "logback_second": "Second level", "logback_tertiary": "Third level", "logback_overwrite": "The selected data already exists in the condition. Overwrite it?", "alarm_fault_type": "Type", "alarm_order_status": "Work Order Status", "alarm_failure_boundary": "Boundary", "alarm_network_fault": "Network Fault", "alarm_urgent_notice": "Urgent Notice", "alarm_route_undulate": "Route Instability", "alarm_system_alarm": "System Alarm", "alarm_special_congestion": "Traffic Congestion", "alarm_fault_duration": "Fault Duration", "alarm_index": "index", "alarm_index_trend": "Indicator trend chart", "alarm_historical": "Historical processing record", "alarm_settled": "settled", "alarm_off_line": "Off-line", "alarm_fault_index": "Fault index", "alarm_load": "Loading...", "alarm_resolved": "resolved", "alarm_content": "Please enter content", "alarm_fault_suspected": "IP address of the suspected fault link", "alarm_disclaimer": "Availability (disclaimer)", "alarm_fault_list": "<PERSON><PERSON><PERSON>", "alarm_dial_probe": "Dial probe", "alarm_SNMP_collector": "Collection agent", "alarm_relay_probe": "Relay probe", "alarm_High-frequency_probe": "High-frequency probe", "alarm_topology": "Route topology", "alarm_addressIP": "IP address of the local device", "specquality_1": "Source IP address, target IP address, target name, and task name", "alarm_address": "Interface address", "alarm_device_name": "Device name", "alarm_port_num": "Port number", "alarm_alias": "alias", "alarm_no_data": "No data", "nodequality_quality_analysis": "Link quality difference analysis", "view_component_type_1": "Delay", "view_component_type_2": "Delay / Packet Loss Rate", "view_component_type_3": "Availability", "view_component_type_4": "Excellent", "view_component_type_5": "Inlet Rate", "view_component_type_6": "OutflowVelocity", "view_component_type_7": "Ingress Rate", "view_component_type_8": "Outbound Rate", "view_obj_num": "The number of objects cannot exceed", "view_save_succ": "Component saved successfully", "view_save_failed": "Component saved Fail", "view_add_title": "New Component", "view_Leased_type_1": "Delay", "view_Leased_type_2": "LossRate", "view_Leased_type_3": "inflow", "view_Leased_type_4": "velocity", "view_Leased_type_5": "outflow", "view_Leased_type_6": "IN Utilization", "view_Leased_type_7": "Utilization Rate", "view_Leased_type_8": "OUT Utilization", "view_Leased_type_9": "Availability", "view_Leased_type_10": "Excellent", "view_dial_type_1": "Latency", "view_dial_type_2": "Packet Loss", "view_dial_type_3": "Availability", "view_dial_type_4": "Excellent", "view_snmp_type_1": "Latency", "view_snmp_type_2": "Packet Loss", "view_snmp_type_3": "Availability", "view_snmp_type_4": "Excellent", "view_snmp_type_5": "InflowVelocity", "view_snmp_type_6": "OutflowVelocity", "view_indi_10": "Up to ten indicator", "view_max_comp_30": "The maximum number of components is 30", "view_select_object": "Select obj", "singlesign_advised": "You are advised to use 360, Chrom, or Firefox", "singlesign_recommended": "Recommended resolution: 1920*1080", "singlesign_login_error": "Login error, please check the network connection!", "singlesign_name_contains": "The account name contains 2 to 18 characters", "singlesign_name_word": "Account names are word characters only", "singlesign_empty": "Account name cannot be empty", "singlesign_password": "Password must be at least 8 characters long", "singlesign_two": "The password contains at least two combinations of letters, digits, and symbols and is not less than eight characters", "singlesign_password_empty": "The account password cannot be empty", "singlesign_verification": "The verification code contains 4 to 6 characters", "singlesign_verification_number": "The verification code must be a number/letter", "singlesign_verification_empty": "The verification code cannot be empty", "snmpoid_oid": "OID", "snmpoid_equipment": "Merchant", "snmpoid_equipment_model": "Device model", "snmpoid_model": "Model", "snmpoid_type": "Type", "snmpoid_select_type": "Please select type", "snmpoid_all_OID": "All OID", "snmpoid_common_OID": "Common OID", "snmpoid_cisco": "CS", "snmpoid_huawei": "HW", "snmpoid_huasan": "H3C", "snmpoid_ruijie": "RG", "snmpoid_maipu": "MP", "snmpoid_shenxinfu": "SF", "snmpoid_f5": "F5", "snmpoid_ss": "Hillstone", "snmpoid_trx": "Topsec", "snmpoid_fill_name": "Please fill in the name", "snmpoid_fill_OID": "Please fill in OID", "snmpoid_select_manufacturer": "Select the device merchant", "snmpoid_device_model": "Enter the device model", "snmpoid_port": "Please enter the local port number", "snmpoid_Note": "Note Contains a maximum of 64 characters", "snmpoid_error_details": "<PERSON><PERSON><PERSON>", "snmpoid_device_merchant": "Device merchant", "snmpoid_device_merchant_select": "Please select the device merchant", "snmpoid_equipment_model_select": "Please select the device model", "snmpoid_search_device_merchant": "Supported by name, such as the OID fuzzy query", "snmpoid_error_oid": "The built-in OID cannot be edited", "snmpoid_error_length_oid": "The length cannot exceed 64", "snmpoid_error_remark": "The length of the note cannot exceed 64", "snmpoid_all_oid": "All OIDs", "snmpoid_public_oid": "Public OIDs", "device_discovery": "Device discovery", "device_discovery_collect_or_not": "Whether monitored", "device_discovery_probe_IP": "Detection IP", "device_discovery_at_least_one_indicator": "Please keep at least one indicator", "device_discovery_most_indicator": "The maximum number of indicators has been reached", "device_discovery_system_not_support": "The system does not support monitoring the equipment of this manufacturer", "device_discovery_error_select": "Check the data to create the task", "device_discovery_warning_system_not_support": "Some of the checked data do not support monitoring the equipment of this manufacturer", "device_discovery_warning_same_indicator": "The same indicators exist", "device_discovery_system_support": "The system temporarily only supports the creation of acquisition tasks for equipment manufacturers (HW、H3C、CS、RG), other equipment manufacturers do not support!", "device_discovery_timing_indicators": "(Minutes: <60, hours: <24, days: <7)", "device_discovery_gether_level_select": "Please select the operation and Level", "device_discovery_map_select": "Select Mapping Probe", "device_discovery_same_IP": "If the node detected by the selected dial probe is the same as the collected port IP, the traffic path is considered to pass through the device!", "device_discovery_dial_test_protocol": "Dial test protocol", "device_discovery_whether_monitored": "Whether monitored", "device_discovery_monitored": "Monitored", "device_discovery_not_monitored": "Unmonitored", "device_discovery_enabled": "Enabled", "device_discovery_trunk_information": "Relay information", "device_discovery_port_indicators": "Port indicators", "device_discovery_link_information": "Link information", "device_discovery_device_information": "Device information", "device_discovery_ARP_information": "ARP information", "device_discovery_MAC_information": "MAC information", "device_discovery_port_information": "Port information", "device_discovery_port_name": "Port name", "device_discovery_port_ip": "Port IP", "device_discovery_rule_matching": "Support according to the rule name, IP range, collector fuzzy matching the query", "device_discovery_choose_credentials": "Please select credentials", "device_discovery_select_credential": "Select credentials", "device_discovery_action_conf": "Action", "device_discovery_discover_only": "Discovery-only data", "device_discovery_select": "Select the conditions that a discovery rule needs to satisfy to perform the defined actions.", "device_discovery_select_standard": "Please select a standard", "device_discovery_match_value": "Please enter a matching value", "device_discovery_meet_any": "Meet any of the following (or)", "device_discovery_match_all": "Matches all of the following (and)", "device_discovery_standard": "Standard", "device_discovery_condition": "Specifies the action to be performed if the above conditions are met", "device_discovery_select_action": "Please select action", "device_discovery_select_level": "Please select Level", "device_discovery_error_ip": "The IP segment does not conform to the specification (the previous IP must precede the last IP).", "device_discovery_ip_range": "IP range", "device_discovery_last_scan_time": "Last scan time", "device_discovery_new_task": "Create SNMP task", "device_discovery_only_add_device": "Add device only", "device_discovery_warning_condition_add": "Conditions add at least one line", "device_discovery_warning_action_add": "Actions add at least one line", "device_discovery_warning_device": "The device information and port information metrics options are required", "device_discovery_warning_validate": "Failed validation", "device_discovery_select_properties": "Please select properties", "device_discovery_fill_value": "Please fill in the value", "device_discovery_mac_conditions": "The maximum number of conditions has been reached", "device_discovery_select_credentials": "Support according to the certificate name fuzzy matching the query", "device_discovery_credentials": "Credentials", "device_discovery_credentials_type": "Please select the credentials type", "device_discovery_fill_credentials": "Please fill in the credentials name", "device_discovery_select_security": "Please select the security level", "device_discovery_security1": "No authentication, no encryption", "device_discovery_security2": "Authentication, no encryption", "device_discovery_security3": "Authentication, encryption", "device_discovery_verify_password": "Verify password", "device_discovery_verify_protocol": "HASH algorithm", "device_discovery_verify_password2": "HASH password", "device_discovery_select_protocol": "Please select HASH algorithm", "device_discovery_org_not_null": "The organization cannot be empty", "device_discovery_snmp_not_null": "The SNMP port cannot be empty", "device_discovery_create_success": "Create successful", "device_discovery_select_del": "Please select delete content", "device_discovery_verify_pass": "The verification password cannot be empty", "device_discovery_ip_device": "Device IP, device name, interface IP", "device_discovery_ip": "Interface IP address", "device_discovery_correct_ip": "Please input the correct IP", "device_discovery_map_probe": "Map the dial probe", "device_discovery_max_char": "The value contains a maximum of 50 characters", "device_discovery_fill_ip": "Please enter interface IP", "device_discovery_fill_device_ip": "Please enter device IP", "device_discovery_select_probe": "Please select the mapping dial probe", "device_discovery_enable_ip": "<p>Are you sure want to enable the selected interface information?</p>", "device_discovery_disable_ip": "<p>Are you sure want to disable the selected interface information?</p>", "device_discovery_del_device": "<p>Are you sure want to delete these devices?</p>", "device_discovery_del_device_factory": "<p>Are you sure want to delete these devices factory?</p>", "device_discovery_del_device_type": "<p>Are you sure want to delete these devices type?</p>", "device_discovery_del_device_model": "<p>Are you sure want to delete these devices model?</p>", "device_discovery_enable": "Enabled successfully", "device_discovery_disable": "Disabled successfully", "device_discovery_associated_excel": "Interface information.xlsx", "device_discovery_all_associated_excel": "All interface information. xls", "device_discovery_all_associated_excel2": "All interface information.xlsx", "device_discovery_del_ip_info": "<p>Are you sure want to delete the selected interface information?</p>", "device_discovery_del_duplicates": "Delete duplicates", "device_discovery_template": "Associative display template.xlsx", "device_discovery_duplicate_interface": "Duplicate interface information.xlsx", "device_all_info": "All device information.xlsx", "device_import_tmp": "Device import template.xlsx", "device_info": "Device information.xlsx", "device_error": "Device error item.xlsx", "device_discovery_no_repeat_exception": "No repeat/exception", "device_code": "Device code", "device_name_alias": "<PERSON><PERSON> name (alias)", "device_code_fill": "Please enter device code", "device_alias": "<PERSON><PERSON> alias", "device_manage_ip": "Managing IP", "device_number_of_ports": "Number of ports", "device_interface_info": "Interface information", "device_network_neighbor": "Network neighbor", "device_core_switch": "Core switch", "device_interface_index": "Interface index", "device_running_state": "Running status", "device_manage_state": "Managing status", "device_interface_type": "Type", "device_interface_bandwidth": "Interface bandwidth", "device_incoming_utilization": "IN Utilization", "device_outgoing_utilization": "OUT Utilization", "device_receive_packet_loss": "IN discards", "device_sending_packet_loss": "OUT discards", "device_receive_error": "Receive error", "device_sending_error": "Sending error", "device_local_port": "Local device port", "device_local_mac": "Local device MAC", "device_local_ip": "Local device IP", "device_peer_device_model": "Peer device model", "device_peer_device": "Peer device name", "device_peer_port": "Peer device port", "device_peer_mac": "Peer device MAC", "device_peer_ip": "Peer device IP", "device_port_index": "Port index", "device_current_value": "Current", "device_packs_number": "Number of packs", "device_select_device": "Select device:", "device_select_device2": "Select device", "device_task_code": "Task code", "device_trunk_line": "<PERSON><PERSON>", "device_acquisition_task": "Acquisition task.xls", "device_acquisition_task2": "Acquisition task.xlsx", "device_all_acquisition_task": "All acquisition task.xls", "device_all_acquisition_task2": "All acquisition task.xlsx", "device_add_device_must": "Please add at least one device!", "device_startup_success": "Startup success!", "device_alarm_update": "The alarm parameters are successfully modified in batches", "device_alarm_update_fail": "Failed to batch modify alarm parameters", "device_portInfo_limit_time": "The Time Span Should Not Exceed 90 Days", "device_enter_device_alias": "Please enter device alias", "device_monitor_state": "Status", "device_enter_ip": "Please enter the correct ip", "device_enter_mac": "Please enter the correct MAC", "device_model_fuzzy_search": "Support according to the device model,sysObjectID fuzzy matching the query", "device_factory_fuzzy_search": "Support according to the device factory,OID fuzzy matching the query", "device_type_fuzzy_search": "Support according to the device type fuzzy matching the query", "remarks_length": "The length shall not exceed", "device_fill_model": "Please fill in the device model", "device_fill_factory": "Please fill in the device merchant", "device_fill_type": "Please fill in the device type", "device_fill_sysObjectID_tips": "Please enter the correct sysObjectID", "device_fill_oid": "Please enter the correct OID", "device_fill_type_code": "Please fill in the device type code", "device_run_record": "Device restart record", "device_restart_time": "Time", "device_collection_time": "Collection time", "device_collection_value": "Collected value ", "device_prev_collection_time": "Last time collection time", "device_prev_collection_value": "Last time collection value", "showCode_uppercase": "The type code is uppercase", "model_fill": "Please fill in the sysObjectID", "factoryCode_uppercase": "Manufacturer code in uppercase letters", "device_fill_sysObjectID": "Please fill in the sysObjectID", "select_attachment": "Select attachment", "data_validation": "Data validation", "export_error": "Export error item", "org_num": "NO.", "org_name": "Name", "org_alias": "<PERSON><PERSON>", "org_type": "Type", "org_id": "ID", "org_parent": "Parent", "org_province": "Province", "org_city": "City", "org_county": "County", "org_leading": "Resp person", "org_leading_phone": "Telephone", "org_cont_name": "Contact name", "org_cont_info": "Contact information", "org_technical": "Technical personnel", "org_phone": "Technical staff phone", "org_tech_a": "Technician A", "org_tech_aphone": "Technician phone", "org_tech_z": "Technician <PERSON>", "org_tech_zphone": "Technician phone", "org_phone_format": "Support to input multiple mobile phones, English comma separation", "org_name_placeholder": "Please enter org name", "org_alias_placeholder": "Please enter org alias", "org_id_placeholder": "Please enter org ID", "org_parent_placeholder": "Please select the parent", "org_user_name_placeholder": "Please enter the name", "org_select_org": "Please select the organization", "org_st_key": "Support name, account, tel query", "user_full_name": "Full Name", "user_sex": "Gender", "user_sex_man": "Man", "user_sex_woman": "Woman", "user_dingTalk": "Tel (DingTalk)", "user_cell_phone": "tel", "user_status": "status", "user_number": "Number Of Users", "user_account": "Account", "user_pwd_expri_date": "Expire Date", "user_pwd_expri_date_1": "1 months", "user_pwd_expri_date_3": "3 months", "user_pwd_expri_date_6": "6 months", "user_pwd_expri_date_12": "12 months", "user_pwd_expri_date_select": "Please select expire date", "user_pwd": "Password", "user_dis_pwd": "Display the password", "user_hide_pwd": "Hide the password", "user_strength": "Strength", "user_strength_weak": "Weak", "user_strength_in": "Medium", "user_strength_strong": "Strong", "user_role_permis": "Role auth", "user_role_permis_select": "Please select role", "user_new_user": "Create users", "user_user_placeholder": "Please enter username", "user_pwd_placeholder": "Please enter password", "user_dingtalk_placeholder": "Please enter mobile phone", "user_email_placeholder": "Please enter embil number", "user_status_placeholder": "Select account status", "user_sex_placeholder": "Please select gender", "user_role_placeholder": "Please select role", "user_account_placeholder": "Please enter account number", "user_descri_placeholder": "Describe information", "user_user_type_placeholder": "Please select user type", "user_v_pwd_long": "The password must be at least eight digits long", "user_v_old_pass": "The new password is the same as the old one", "user_v_limt_len": "Over limit length", "user_v_pwd_two_no": "The two password passwords are inconsistent", "user_v_pwd_format": "The password is any two or more combinations of letters, numbers and symbols, no less than 8 digits", "user_v_pwd_len_32": "The length cannot exceed 32", "comm_upload_file2": "Please select probe, please select type, please enter port, please enter destination IP", "user_v_pwd_enter_old": "Please enter old password", "user_v_phone_format": "Phone number is in an incorrect format", "user_v_mailbox_format": "The mailbox is incorrectly formed", "user_v_mailbox_empty": "The mailbox cannot be empty", "user_v_account_exists": "The account already exists", "user_edit_title_top": "Update user", "server_system": "NetDem", "server_shallow": "S", "server_deep": "D", "server_change_password": "Edit password", "server_exit": "Exit", "server_current_position:": "Current Position：", "server_reminder": "Warm reminder", "server_sure_exit": "Are you sure to exit your current account?", "server_old_password": "Old password：", "server_prompt_message": "Prompt message", "server_input": "input placeholder", "server_enter_password": "Please enter the enterprise login password", "server_different": "Two different passwords entered", "server_trunk_monitoring": "Trunk line monitoring", "server_home_page": "First page", "server_last_page": "Last page", "server_total": "Total", "server_page": "page", "server_faults": "List of faults", "server_browser": "Due to browser related security restrictions, the alarm sound prompt has been turned off. If you want to enable the sound alert, please manually enable it!", "server_turn_on_sound": "Turn on sound", "server_turn_off_sound": "Turn off sound", "server_cannot_played": "Audio cannot be played", "server_permission": "If the menu permission is not configured, contact the administrator to configure the permission", "server_changed_successfully": "Password changed successfully", "server_least": "Password must be at least eight characters long!", "server_storage1": "Torage", "server_view_details": "View details", "server_correct_ip": "Please enter the correct IP address", "server_password_30": "The password contains a maximum of 30 characters", "server_password_8": "The password contains a maximum of 8 characters", "server_positive_integers": "Ports must be positive integers", "server_enter_path": "Please enter the correct path", "server_view": "View", "server_partition": "Partition", "server_sure_delete": "Are you sure want to delete this data?", "server_trunk": "The relay information is modified successfully", "server_query_failure": "Query fault", "server_China_Mobile": "Globe", "server_China_Unicom": "PLDT", "server_China_Telecom": "Converged", "server_China_Broadcasting": "China Broadcasting and Television", "server_China_other": "Other", "server_China_other2": "Other", "server_affected": "Links Number", "server_closed_loop": "Closed loop", "server_process": "Processing", "server_unprocessed": "Unprocessed", "server_fluctuation": "Route instability details", "server_select_Restore": "Please select Restore data first", "server_recover": "Are you sure want to recover these records?", "server_recovery_success": "Recovery success", "server_flow_rate": "Flow rate", "specinfo_system": "Using system templates", "specinfo_ctd": "Confirm to delete", "specinfo_ctd_tip": "Are you sure you want to delete this data?", "specinfo_these": "these", "specinfo_those": "those", "specinfo_isr": "is it recorded?", "specinfo_city_A": "A Terminal City", "specinfo_County_A": "A Terminal District County", "specinfo_province_Z": "Z Terminal Province", "specinfo_city_Z": "Z Terminal City", "specinfo_County_Z": "Z End District County", "specinfo_carrier_mapping": "Carrier mapping", "specinfo_relationship": "Active/standby relationship Mapping", "specinfo_Z_ip": "Mapping between Z IP addresses", "specinfo_A_ip": "Mapping between IP addresses of end A", "specinfo_province_A": "A Terminal Province", "specinfo_enter_number": "Please enter the Assoc LL NO", "specinfo_identification_task": "Identifier Task", "specinfo_enter_identification_task": "Please enter the identifier task", "specinfo_identification_trunk": "ID Relay Line", "specinfo_enter_identification_trunk": "Please enter the ID relay line", "specinfo_enter_correct_broadband": "Please enter the correct broadband", "specinfo_line_exists": "The private line number already exists", "specinfo_format_combination": "letters and numbers", "specinfo_enter_line_num": "Please enter the line number", "specinfo_select_org": "Select your organization first", "specinfo_primary_use": "Master", "specinfo_standby": "Slave", "specinfo_load_balancing": "<PERSON><PERSON>", "specinfo_no": "No", "specinfo_optical_fiber": "Optical fiber", "specinfo_frame_relay": "Frame relay", "specinfo_enter_line_name": "Please enter the line name", "specinfo_select_branch_office": "Select a branch office", "specinfo_select_carrier": "Please select ISP", "specinfo_enter_Z_IP": "Enter the Z-Internet-IP", "specinfo_select_active_standby": "Select the active/standby relationship", "specinfo_enter_associated_number": "Enter the A-Internet-IP", "specinfo_enter_IP_A": "Enter the A-Internet-IP", "specinfo_line_associated_others": "The line number has been associated with others", "specinfo_relay": "(Relay Line)", "specinfo_dial": "(Probe Task)", "export_quality_report": "Link quality difference report", "export_template": "Interface Manage template", "export_data_template": "Organization data template", "export_line_quality_report": "Leased Line quality report", "export_line_data": "Private line data", "export_health": "Relay health record", "export_duplicate": "The duplicate items of a task are collected", "export_organization": "Duplicates of organization data", "export_trunk": "Trunk quality difference report", "export_dial": "Dial the quality difference report", "export_routes": "List of routes on the physical topology", "export_verification": "Reachability verification result set", "export_task": "Relay task template", "export_policy": "Task policy duplicate entry", "export_collection": "Collection task template", "export_topology": "Import a physical topology line template", "export_probe": "Probe task template", "role_name": "Role Name", "role_type": "Role Type", "role_status": "Status", "role_list": "Role Manage List", "role_number_of_users": "Number of users", "role_peop_mgt": "SM", "role_perm_view": "Auth view", "role_fun_action_domain": "Functional action domain", "role_fun_list": "Functional List", "role_permis_scope": "Permission scope", "role_step_1": "Step 1: Define the role name", "role_step_2": "Step 2: Configure permissions", "role_already": "The role name is already occupied", "role_all_scopes": "All scopes (data permissions of the organization and subordinate organizations)", "role_org_scopes": "Organization scope (data permissions for the organization you belong to)", "role_pd_name": "Please enter role name", "role_pd_type": "Please select", "role_pd_status": "Please select role status", "role_pd_perm_rang": "Please select permission range", "role_type_1": "Maintain roles", "role_type_2": "Administrative roles", "role_type_3": "Normal characters", "role_len_rang": "The length cannot exceed", "role_auth_mgt": "RM", "role_activate": "Activate...", "role_freeze": "Executing a freeze...", "role_reselect": "Selected data contains activated data, reselect", "role_frozen_res": "The selected data contains the frozen data, please select again", "role_rdl": "Remove feature list:", "role_afl": "Add feature list:", "role_se_ale": "The selected user already exists", "role_acplre": "account number, please re-select", "role_per_not": "Personnel have not changed", "role_role": "role", "role_pechfo": ",personnel changes are as follows:", "role_re_per": "reduce personnel:", "role_ad_per": "Add personnel:", "role_system_freeze_tip": "The built-in role cannot be frozen", "role_system_activate_tip": "The built-in role cannot be activated", "role_system_delete_tip": "The built-in role cannot be delete", "message_tpl_name": "Name", "message_content": "Content", "message_type": "Msg Type", "message_alerm_type": "Alarm Type", "message_alerm_select_type": "Select alarm type", "message_tpl_type": "Tmp Type", "message_create_date": "Create Date", "message_push_to": "<PERSON>ush To", "message_push_way": "Push method", "message_silence": "Silence cycle", "message_silence_cycle": "minutes How long after an alarm notification is triggered for the first time, if an alarm of the same type is generated, the alarm notification is not pushed again", "message_mail_service_name": "Mail Service Name", "message_server_and_ip": "Server / IP", "message_email": "Send Email", "message_email_user": "Mailbox Username", "message_email_pwd": "Mailbox Password", "message_sender": "Sender", "message_server_port": "Port", "message_select_person": "Please select the person to push", "message_select_push": "Select the push mode", "message_exceed_240": "The content must not exceed 240", "message_or": "'or' is allowed only at the end of the calculation mode. For example, A and B and C or D are allowed, but A and B or C and D are not allowed", "message_phone_number": "Tel", "message_reminder": "Friendly reminder", "message_whether_delete": "Whether to delete the selected information", "message_network_exception": "Network exception", "message_pd_select_alarm": "Please select the alarm content for the corresponding task", "message_pd_or_mode": "The or mode can be only in the last position", "message_pd_mul_ide": "Multiple identical conditions exist", "message_alarm_content": "Select the alarm content for the task", "message_alarm_type": "Select an alarm type for the task", "message_identical": "Multiple identical conditions exist", "message_mutually": "If mutually exclusive conditions exist, reconfigure them", "message_configuration_10": "A maximum of 10 configuration conditions are supported", "message_add_success": "Create success", "message_network_failure": "Network fault", "message_overwritten": "The selected person already exists in the list. Is it overwritten?", "message_application_name": "AppName", "message_test_phone_number": "Test phone number", "message_enter_test_phone_number": "Please enter the test phone number", "message_testing": "Under test", "message_test": "Test", "message_format_incorrect": "phone format is incorrect", "message_receiving_phone": "Please enter the test receiving phone number", "message_select_data": "Please select Delete data first", "message_enter_correct": "Enter the correct server name/IP", "message_mailbox_incorrect": "The mailbox format is incorrect", "message_enter_correct_email": "Please enter the correct email address", "message_enter_port": "Please enter port", "message_email_password": "Please enter the email password", "message_interface_type": "Interface Type：", "message_SMS": "SMS URL gateway provider. URL parameters are not included. For example: http://www.smsserver.com/sendsms", "message_URL": "URL param：", "message_server_ip": "Server IP：", "message_server_port1": "Server port：", "message_user_name": "User：", "message_password": "password：", "message_Intensity": "Intensity：", "message_push_mode": "Push method：", "message_password_confirmation": "Confirm：", "message_password_again": "Please enter your password again：", "message_password_contains": "The password contains at least two combinations of letters, numbers, and symbols, and is not less than eight characters", "message_password_consist": "The password shall consist of at least two combinations of letters, numbers and symbols, not less than 8 characters", "message_pd_mulexc_cond": "Mutual-exclusive conditions exist, please reconfigure them", "message_pd_sup_10": "Up to 10 configuration conditions are supported", "message_search_mail_keyword": "Fuzzy matching query mail、server、sender name, email", "message_search_sms_keyword": "Fuzzy matching query by SMS service name is supported", "message_sure": "Are you sure want to delete the selected message template?", "message_records": "Select the records you want to delete", "message_same_message": "Records with the same message type, alarm type, and template type exist in the current organization", "message_inserted": "The message content exceeded the maximum length after the variable was inserted", "message_select_variables": "Please select message variables", "message_title": "Message title", "message_fill_title": "Please fill in the message title", "message_fill_content": "Please fill in the Message Content", "message_variable": "Insert variable", "message_information": "Relay information update template", "message_again": "Read failed! Please download the template and upload it again", "message_empty": "You uploaded an empty template, please enter the data", "message_match": "If the template does not match, select the provided template to import", "message_exception": "Relay information exception item", "message_no_abnormal": "There are no abnormal items", "message_no_criteria": "There is no data that fits the criteria", "message_upload": "Upload successfully", "message_upload_failure": "Upload fault", "message_invalid": "The configuration is invalid. The start cannot exceed the end", "message_cfg_cond_note": "Default all alarm info is pushed, you can advanced configuration alarm push conditions", "message_cfg_switch_easy": "Switch to easy settings", "message_cfg_switch_advanced": "Switch to advanced settings", "message_cfg_switch_note": "The calculation mode 'or' is only allowed at the end of the calculation mode, for example: A and B and C or D, not allowed A and B or C and D", "message_cfg_push_to": "Push to", "message_cfg_push_method": "Push method", "message_cfg_push_person": "Select the person you want to push", "message_cfg_port": "Port", "server_name": "Name", "server_ip": "IP", "server_port": "Port", "server_cpu": "CPU", "server_cpu_ratio": "CPU Unilization Ratio", "server_cpu_ratio_mam": "CPU Ratio(Min/Avg/Max)", "server_memory": "Memory", "server_memory_usage": "Memory Usage", "server_memory_usage_mam": "Memory Usage(Min/Avg/Max)", "server_last_upate_time": "Last Update Time", "server_access_status": "Access status", "server_user_name": "User", "server_user_pwd": "User Password", "server_storage_avg": "Avg Storage Usage Maximum Partition", "server_storage_size": "Storage Size", "server_storage": "Storage Usage", "server_storage_usage_mam": "Storage usage% (Min/Avg/Max)", "server_process_name": "Process Name", "server_process_id": "Process ID", "process_memory": "memory", "process_unit": "unit", "server_pe_user_name": "Please enter user name", "server_pe_user_pwd": "Please enter user password", "server_pe_server_name": "Please enter server name", "server_pe_server_pwd": "Please enter server password", "server_pe_server_port": "Please enter server port", "server_pe_server_ip": "Please enter server IP", "server_pe_server_describe": "Please enter describe", "speed_start_test": "Start The Speed Test", "speed_stop_test": "Cancel The Speed Test", "speed_rest_test": "Speed again", "speed_timed_mea": "Duration", "speed_list": "Speed Recording", "speed_server": "Test Server", "speed_test_server": "Test Server", "speed_jitter": "Jitter", "speed_delay": "Delay", "speed_confirm": "Whether to confirm to move out the selected person", "speed_server_test": "Please select the speed measurement server and test duration", "speed_time": "Speed Time", "speed_time1": "Speed Time", "speed_test_time": "Speed Test Time", "speed_user": "User", "speed_institution": "Org", "speed_method": "Type", "speed_local_ip": "Local IP", "speed_route_ine": "Route Line", "speed_internet_equivalent": "Your net speed is equivalent to", "speed_broadband": "broadband", "speed_reach": "reach", "speed_fall_reach": "fall short of", "speed_cyberspace_standard": "Cyberspace Administration standard", "speed_no_matched": "No private line was matched", "speed_search_user": "Support fuzzy query by user", "speed_search_keyword": "upport PC end name, PC end number fuzzy matching", "speed_note": "Note:Keep the speed measurement interface open!", "speed_switch": "Timing switch", "speed_recurrence": "RepeatCycle", "speed_all": "All", "speed_manual": "manual", "speed_timing": "timing", "speed_packet_loss": "Packet Loss", "speed_download": "Download", "speed_upload": "Upload", "speed_URL": "URL parameter cannot be empty", "backup_time": "Backup time", "backup_file_size": "File size", "backup_path": "Path", "backup_path_local": "Local Backup", "backup_path_remote": "Remote Backup", "backup_address": "Backup address", "backup_ing": "Backing up", "backup_sucess": "Backup succeeded", "backup_fail": "Backup failed", "access_tip": "Support multiple IP addresses (separated by + signs)", "access_tip2": "Multiple IP addresses can be separated by + signs, for example :(********+********)", "access_tip3": "Supports a maximum of 10 IP address segments (separated by + signs)", "access_tip4": "Such as", "access_des_port": "Target Port", "access_dial_methond": "Dial Type", "access_path": "Path", "access_port": "Target port", "access_response_iP": "Response IP", "access_loss": "Packet loss deterioration generation threshold, once reached：", "access_con_port": "Connected Port", "access_accessibility": "Accessibility", "access_exe_mode": "Mode", "access_exe_mode_enter": "Enter Manually", "access_exe_mode_import": "Import", "access_note": "Sequentially check ports for reachability, if it's reachable, mark it available and stop checking other ports. If all ports are unreachable, mark the destination as unreachable", "access_des_ip_rang": "Target IP/IP Range", "access_upload": "Upload attachment", "access_separate": "Please use English comma to separate multiple cases", "access_destination_ip": "Target IP", "access_enter_destination_ip": "Enter the target IP address. Multiple target IP addresses (separated by commas) are supported", "access_enter_destination_ip2": "Please enter the response IP address. Multiple response IP addresses (separated by commas) are supported", "lisence_org_name": "org name", "lisence_v_info": "Version Information", "lisence_serial_number": "NO.", "lisence_up_package": "Upgrade Package Upload", "lisence_lice_info": "License Information", "lisence_cur_remainin": "Current <PERSON><PERSON><PERSON><PERSON>", "lisence_expir_date": "Expiration date", "lisence_up_lice": "Update License", "lisence_total": "Total", "lisence_purchase_date": "Purchase Date", "lisence_upgrade_note": "Automatic upgrade is not supported (file format example: ANPM_2.3.9.20200215_RE.zip).", "lisence_pro_info": "Product Information", "log_op_account": "Operation account", "log_op_ip": "Operational IP", "log_op_object": "Object", "log_op_if": "Interface address", "log_op_result": "Operation result", "log_op_date": "Date", "log_op_time": "Operating time (ms)", "log_search_key": "Fuzzy queries by keywords, objects, and results are supported", "snmptask_device_type": "Select the device type", "snmptask_operation_level": "Operations Level:", "snmptask_SNMP": "SNMP version", "snmptask_SNMP_port": "SNMP port", "snmptask_select_SNMP": "Select an SNMP version", "snmptask_select_SNMP_port": "Enter the SNMP port number", "snmptask_fill_community": "Please fill in community", "snmptask_security_level": "Security level", "snmptask_select_security_level": "Select a security level", "snmptask_context_name": "Context name", "snmptask_fill_context_name": "Please fill in the context name", "snmptask_save_interface": "Save interface information", "snmptask_select_map": "Select map to dial probe", "snmptask_alarm_parameter": "Alarm parameter", "snmptask_threshold": "Interrupt", "snmptask_time": "time", "snmptask_alarm_setting": "Alarm setting", "snmptask_delay_deterioration": "Delay", "snmptask_loss_deterioration": "Packet loss deterioration generation threshold", "snmptask_server": "Server", "snmptask_firewall": "Firewall", "snmptask_authentication": "No authentication, no encryption", "snmptask_authentication2": "Authentication, no encryption", "snmptask_authentication3": "Authentication, encryption", "snmptask_minutes": "m", "snmptask_minutes2": "m", "snmptask_hour": "h", "snmptask_day": "day", "snmptask_suspended": "Paused", "snmptask_lines": "<PERSON><PERSON>", "snmptask_collection_task": "Collection task", "snmptask_repeat": "Do not repeat this operation when deleting data", "snmptask_perform_operations": "The selected data contains the data you are deleting. Do not perform other operations", "snmptask_select_task": "Select the task that you want to delete", "snmptask_perform": "Do not perform other operations during data deletion", "snmptask_same_index": "Same indicator exists", "snmptask_select_type": "Please select a job type", "snmptask_community_empty": "community cannot be empty", "snmptask_security_empty": "The security level cannot be empty", "snmptask_user_empty": "The user name cannot be empty", "snmptask_authentication_empty": "The authentication protocol cannot be empty", "snmptask_password_empty": "The authentication password cannot be empty", "snmptask_protocol_empty": "The private protocol cannot be empty", "snmptask_encryption_empty": "The encryption password cannot be empty", "snmptask_select_map_to_probe": "Select map to dial probe", "snmptask_task": "The task you selected contains", "snmptask_task2": "task, please select", "snmptask_not_started": "paused", "snmptask_unsuspended": "enabled", "snmptask_not_enabled": "Not enabled", "snmptask_not_paused": "Not paused", "snmptask_task1": "task", "snmptask_pause": "Are you sure want to pause the selected data?", "snmptask_pause2": "Are you sure want to pause the selected data?", "snmptask_activate": "Are you sure want to enabled the selected data?", "snmptask_activate2": "<p>Are you sure want to enabled the selected data?</p>", "snmptask_select": "Please select the data you want to pause/start!", "snmptask_batches": "The alarm parameters are edit in batches", "snmptask_batches_failed": "Description Failed to batch modify alarm parameters", "snmptask_protocol": "Verification protocol：", "snmptask_select_protocol": "Select an authentication protocol", "snmptask_verification_password": "Verification password：", "snmptask_private_protocol": "Private protocol：", "snmptask_select_private_protocol": "Please select a private protocol", "snmptask_encryption_password": "Encryption password：", "snmptask_fill_password": "Please fill in the encryption password", "snmptask_loss": "Loss", "snmptask_once_achieved": "Once achieved", "snmptask_deleting": "Deleting", "snmptask_pause_success": "Pause success!", "snmptask_enter_device": "Please enter device name", "specquality_time_range": "The end time must be no later than the start time", "specquality_quality": "Leased Line quality report", "specquality_select": "Please select data", "specquality_no_data": "No data", "user_last": "Duration", "specquality_incoming_velocity": "InflowVelocity", "specquality_exit_velocity": "OutflowVelocity", "specquality_incoming_rate": "Inport Utilization", "specquality_output_rate": "Outport Utilization", "specquality_Interruption_times": "Interruption Count", "specquality_exemption": "(Disclaimer)", "specquality_velocity_flow": "velocity of flow", "specquality_utilization": "Utilization rate (%)", "specquality_rate_exemption": "Excellent(disclaimer)", "specquality_good_rate": "Excellent", "specquality_availability": "Availability", "specquality_liability": "(disclaimer)", "specquality_availability_exemption": "Availability(disclaimer)", "specquality_good_rate_exemption": "Excellent(disclaimer)", "common_line": "Normal line", "specquality_average_delay": "Avg delay(ms)", "specquality_maximum_delay": "Max delay(ms)", "specquality_average_loss": "Avg loss rate", "specquality_incoming": "Avg up flow", "specquality_maximum_flow": "Max up flow", "specquality_average_velocity": "Avg down flow", "flow": "Flow", "all_line_data": "All private line data", "specquality_maximum_flow_rate": "Max down flow", "specquality_deterioration_number": "Deterioration Count", "specquality_deterioration_time": "Deterioration Avg Duration", "specquality_availability_rate": "Availability & good and excellent", "specquality_good_ratio": "Good and good ratio", "specquality_health_record": "Leased line health record", "specquality_connection_error": "Network connection error", "specquality_no_exportable": "There is no exportable data", "specquality_fuzzy": "Fuzzy query is supported by A-IP, Z-IP, LL name", "specquality_index": "Statistical Index", "specquality_basic_index": "Basic index", "specquality_operating_index": "Operating index", "specquality_topo_diagram": "Topology diagram:", "specquality_start_time": "Start time", "specquality_end_time": "End time", "specquality_recovery_time": "Recovery time", "specquality_duration": "Duration", "specquality_cause": "Cause", "specquality_recovery_cause": "Recovery Cause", "specquality_type": "Type", "specquality_current_state": "Status", "specquality_high_frequency": "High frequency", "specquality_task_statistics": "Statistical Index", "specialmonitor_data": "No more data", "specialmonitor_select_line": "Select line", "specialmonitor_matching": "Fuzzy matching query by name,A-Internet-IP,Z-Internet-IP is supported", "specialmonitor_delay": "Delay / Packet Loss Rate", "specialmonitor_delay2": "Up Flow Rate / Down Flow Rate / Up Bandwidth Rate / Down Bandwidth Rate", "specialmonitor_delay3": "Availability / Excellent/ Availability (disclaimer) / Excellent (disclaimer)", "specialmonitor_up_data": "latest", "specialmonitor_lost_bag": "Lost bag", "specialmonitor_packet_loss": "Packet loss (%)", "specialmonitor_maximum": "Max", "specialmonitor_average": "Avg", "specialmonitor_minimum": "Min", "specialmonitor_flow_rate": "Flow rate: (Kbps)", "specialmonitor_loading": "Data loading", "specialmonitor_utilization_rate": "Utilization rate", "user_activate": "(activate)", "user_keyword": "Support fuzzy search by name, account, mobile phone", "user_mechanism": "mechanism：", "user_in_account": "Please enter account number", "user_freeze": "(freeze)", "user_super_admin": "Super administrator", "user_admin": "administrator", "user_ordinary": "Ordinary user", "user_pass_reset": "Password reset", "user_modified_successfully": "Edit successfully", "user_same_in": "The same account exists. Please re-enter it", "user_wait": "Please wait for a moment while submitting", "user_pass_changed": "Password changed successfully", "user_modify": "Modify user", "user_add": "Create user", "user_contact_admin": "Cannot reset your password, please contact the administrator", "user_sure_freeze": "<p>Are you sure to freeze this account?</p>", "user_activate_account": "Are you sure to activate the account?", "user_selected_exists": "The selected user already exists", "user_activated_account": "activate account", "user_reselect": ", please reselect", "user_prompt": "Activation prompt", "user_sure_to": "<p>Be sure to ", "user_sure_to1": "these tasks?", "user_sure_to2": "Be sure to enable these tasks?", "user_activate1": "Activate", "user_dormancy": "<PERSON><PERSON><PERSON>", "user_sign_out": "Log out", "user_freeze1": "freeze", "user_selected": " a selected user?</p>", "user_select_need": "Choose users to", "user_user": "user", "user_sure_delete": "<p>Are you sure want to delete the selected user?</p>", "user_select_delete": "Select the user you want to delete", "user_sure_delete_content": "<p>Are you sure to delete this content?</p>", "user_parent": "Select a parent organization", "user_organization_type": "Please select the organization type", "user_hide_password": "Hide password", "user_show_password": "Show password", "user_update_password_tip": "The passwords entered twice are inconsistent", "user_update_password_equal_tip": "The new password is the same as the old password", "user_update_password_validation_error_tip": "8 to 20 characters", "user_no_find_permissions": "No permission to configure menu. Please contact the administrator to configure permissions", "testspeed_org": "Org", "specinfo_org_first": "Select your organization first", "specquality_down": "Down", "specinfo_line_number": "Please enter the associated line number", "specinfo_select_info": "Please select device information", "specinfo_flow_value": "Flow value", "specinfo_select_interface": "Please select interface information", "nodequality_line_details": "Private line details", "specinfo_flow_direction": "Flow direction", "specinfo_perspective": "Type", "specquality_msg1": "Number of leaseds that have not experienced interruption or deterioration within the statistical time range", "specquality_msg2": "Number of Leased that have experienced deterioration within the statistical time range", "specquality_msg3": "Number of Leased that have experienced interruption within the statistical time range", "message_1": "If the same dedicated line exists, enter the IP address of end A or end Z.", "message_2": "If the traffic value is not specified, it is automatically matched. Traffic value Select device information and then interface information.", "specquality_up": "Up", "testspeed_select_indicators": "Please select indicators", "testspeed_select_org": "Please select organization", "testspeed_select": "select", "testspeed_sort_number": "Sort number：", "testspeed_velocity_plan": "Velocity plan", "testspeed_return": "Back", "testspeed_PC_name": "PC name：", "testspeed_enter_PC_name": "Enter the PC name", "testspeed_PC_number": "PC terminal number：", "testspeed_enter_PC_number": "Enter the PC number", "testspeed_affiliated_org": "Affiliated Org：", "testspeed_IP_address": "IP address：", "testspeed_fill_remarks": "Please fill in the remarks", "testspeed_rule": "rule", "testspeed_select_server": "Please select a speed server", "testspeed_select_time": "Please select the start and end time", "testspeed_select_start_time": "Select start time", "testspeed_measurement_time": "Please select the speed measurement time", "testspeed_period": "Please select period", "testspeed_plan_content": "The speed measurement plan is only valid for the PC terminal with normal speed measurement time, and the offline PC terminal will not be measured!", "testspeed_immediate": "Immediate velocity measurement", "testspeed_measuring_server": "Speed measuring server：", "testspeed_the_measurement_plan": "The speed measurement plan is only valid for the PC terminal with normal speed measurement time, and the offline PC terminal will not be measured!", "testspeed_remark": "Remark", "testspeed_state": "Status", "testspeed_off_line": "Off-line", "testspeed_selected_duplicate": "The selected time is duplicate. Delete the duplicate time", "testspeed_selected_less_five_minutes": "The selected speed measurement interval shall not be less than five minutes", "testspeed_save_successfully": "Save successfully", "testspeed_operation_succeeded_check_result": "The operation succeeded. Please check the result in the speed record later", "testspeed_operation_failure": "Operation fault,", "testspeed_err_mess": "Error message：", "testspeed_contact_admin": "Contact your system administrator", "testspeed_add_10_time": "Add a maximum of 10 speed measuring times", "testspeed_selected_measurement_no_less_5": "The selected speed measurement interval shall not be less than five minutes", "testspeed_selected_duplicate_time": "The selected time is duplicate. Delete the duplicate time", "testspeed_whether_delete": "Whether to delete the selected information", "testspeed_deleting": "deleting...", "testspeed_network_exception": "Network exception", "testspeed_select_want_task": "Select the task you want to configure", "testspeed_select_task": "Please select task", "testspeed_select_method": "Please select the speed measurement method", "testspeed_timing": "timing", "testspeed_support": "Support by user, local ip, Test server, Route line fuzzy query", "testspeed_manual": "Manual", "testspeed_bandwidth": "Reach line bandwidth", "testspeed_uplink_bandwidth": "UpBandwidth", "testspeed_dwnstream_bandwidth": "DownBandwidth", "testspeed_path_line": "Corrective path line", "testspeed_line": "No private line was matched", "testspeed_speed_measurement": "Timing speed measurement", "testspeed_select_speed": "Please select a speed server", "testspeed_select_duration": "Please select speed test time", "testspeed_select_speed_time": "Please select the speed measurement time", "testspeed_speed_measured": "The speed is being measured...", "testspeed_selected_five": "The selected speed measurement interval shall not be less than five minutes", "testspeed_selected_time": "The selected time is duplicate. Delete the duplicate time", "testspeed_contact_administrator": "The network is fault. Select another speed measurement server and try again or contact the administrator", "testspeed_dedicated": "Dedicated line: The leased Line is not matched", "testspeed_unactivated": "unactivated", "testspeed_activated": "activated", "testspeed_event": "Event start time", "testspeed_failure_start": "Fault start time", "testspeed_failure_recovery": "Fault recovery time", "testspeed_event_recovery": "Event recovery time", "testspeed_event_duration": "The duration of the event", "testspeed_failure_duration": "Fault duration", "testspeed_matching": "Fuzzy matching search by role name is supported", "sysgether_characters": "Note Contains a maximum of 64 characters", "warning_time_not_exceed_62": "The time span must not exceed 62 days", "warning_latter_greater_previous": "The latter value of the custom must be greater than the previous", "warning_select_clear_first": "Please select Clear data first", "warning_view_deleted": "This view has been deleted", "warning_path_topology_empty": "Description Failed to render the path topology because data is empty", "warning_device_node_connected": "Add at least one source to the device or the node connected to the device", "warning_task_repeat": "A task is being performed, do not repeat the task", "warning_date_not_null": "The date cannot be empty", "warning_URL_incorrect": "The URL format is incorrect", "warning_enter_test_email_addr": "Please enter the email address to receive the test", "warning_enter_test_phone": "Please enter the test receiving phone number", "warning_enter_test_content": "Please enter the test content", "warning_download_failed": "Failed to download the empty file. Procedure", "warning_parameter_max_20": "The parameter contains a maximum of 20 parameters", "warning_cannot_del_org": "You cannot delete the organization to which the user belongs", "warning_select_want_export": "Select the data you want to export", "warning_first_operation_data": "Please check operation data first", "warning_repeating": "When repeating items are selected, select at least one repeating period item", "warning_window_not_none": "The window size cannot be empty", "warning_window_size": "The window size can only be 1-60", "warning_packet_loss": "The packet loss alarm generation threshold cannot be empty", "warning_packet_loss100": "Packet loss alarm generation threshold Only 1-100 can be entered", "warning_interrupt_empty": "The interrupt generation threshold cannot be empty", "warning_interrupt_1000": "The interrupt generation threshold ranges from 0 to 1000", "warning_degradation_empty": "The latency deterioration generation threshold cannot be empty", "warning_degradation_1000": "The latency deterioration generation threshold can only be 0 to 1000", "warning_empty_once": "Packet loss deterioration generation threshold, which cannot be empty once reached", "warning_loss_100": "Packet loss deterioration generation threshold. Only 1 to 100 can be entered once", "warning_loss_empty": "Packet loss deterioration generation threshold. The value cannot be empty consecutively", "warning_loss_1000": "Packet loss deterioration generation threshold. The value can be 0 to 1000 continuously", "warning_loss_continuously_empty": "Packet loss deterioration generation threshold, which cannot be empty continuously", "warning_loss_continuously_100": "Packet loss deterioration generation threshold. The value can be 1 to 100 continuously", "warning_IP_first": "Please enter the probe IP first", "warning_too_long": "The target address segment is too long", "warning_target_segment": "Please enter the target address segment", "warning_target_segment_1": "Please enter the target address segment 1", "warning_range_20_60000": "Between 20 and 60,000", "warning_range_20000_60000": "Between 20000 and 60,000", "warning_g0": "Enter only positive integers greater than 0", "warning_cannot_g": "The starting value cannot be greater than the limited hop count ", "warning_g30": "The value must not be greater than 30", "warning_cannot_l": "The number of restricted hops cannot be less than the starting value ", "warning_mac_ip": "Only one value of the target IP address and target MAC address can be entered. The other value is automatically completed after the device connected to the target is managed by the system", "testspeed_repeat_period": "Please select at least one repeat period", "comm_enter_target_name": "Please enter a target name", "common_dial_analysis": "Probe Task", "common_max": "Max", "common_min": "Min", "common_latest_value": "Latest", "common_avg": "Avg", "common_length_60": "The content must not exceed 60", "common_No_data_exprot": "No data has been imported", "common_alarm_generation": "Alarm generation setting", "nodequality_degradation_time": "Deterioration Stack Duration", "nodequality_all_degradation_time": "Deterioration Duration", "comm_task_first": "Select a dial type first", "common_interrupt_threshold": "interrupt threshold", "nodequality_difference_cumulative_time": "Poor-quality Total Duration", "nodequality_difference_stacking_time": "Poor-quality Stack Duration", "probetask_alarm_setting": "Alarm setting", "probetask_indicator_details": "Indicator details (Task/path number：", "probetask_alarm_threshold": "Alarm threshold", "probetask_single_reach": "Single reach", "probetask_or_continuous": "Or continuous", "probetask_route_alarm": "Route instability alarm", "probetask_path_num": "Task/path number", "probetask_num_no": "Path number", "probetask_num_paths": "Paths Number", "probetask_ended": "Have ended", "probetask_monitoring_state": "Monitor status", "probetask_delete_associated": "<p>Are you sure want to delete the deletion task that will delete the data report of the associated link?</p>", "probetask_task_deletion_succeeded": "Task deletion succeeded", "probetask_sure_want": "<p>Are you sure want to", "probetask_assignment": "Is this assignment? </p>", "probetask_dial_test": "This Dial Test task", "probetask_high_dial": "High frequency dial", "probetask_task_sucess": "Task, the alarm parameters are successfully modified in batches", "probetask_task_fail": "Description Failed to batch modify alarm parameters", "probetask_no": "No", "probetask_related_task": "Related task", "probetask_current_status": "Current status：", "probetask_trend_chart": "Trend chart：", "probetask_date": "Date：", "probetask_path_analysis_tip": "Path analysis", "probetask_path_topology_tip": "Path topology", "probetask_indicator_trends_tip": "Indicator trends", "probetask_target_name": "Target name", "probetask_betw": "Between 20,000 and 60,000", "probetask_routine": "routine", "probetask_SIP": "SIP protocol", "probetask_betw20": "Between 20 and 60,000", "probetask_pack_size_min": "<PERSON>", "probetask_interrupt_deterioration_tip": "Number of tasks that have not experienced interruption or deterioration within the statistical time range", "probetask_deterioration_tip": "Number of tasks that have experienced deterioration within the statistical time range", "probetask_interrupt_tip": "Number of tasks that have experienced interruption within the statistical time range", "probetask_export_excel_file_name": "Dial tasks list.xlsx", "org_tel_format_incorrect": "The format is incorrect", "org_the": "The", "org_branch_office": "Branch office", "org_dept": "Dept", "processinfo_view_details": "View details (Process name:", "processinfo_min": "Min (%)", "processinfo_max": "Max (%)", "processinfo_aver": "Avg (%)", "nodequality_topo": "Topology ：", "nodequality_occurrence_time": "Occurrence time", "nodequality_duration": "duration", "nodequality_type": "type", "nodequality_target": "Target", "nodequality_cause": "cause", "nodequality_Link_details": "Link details", "nodequality_link": "(Link:", "nodequality_interrupt_details": "Interrupt details", "nodequality_deteriorate_details": "Deteriorate details", "pathhealthy_availb": "Availability", "pathhealthy_good_ratio": "Excellent", "pathhealthy_health_configuration": "Path Health File - Export file configuration", "pathhealthy_path_health_file": "PT Records", "pathhealthy_export_invalid": "The export file name is invalid!", "pathhealthy_export_success": "File - Export successful", "dial_test_target_name": "Target name", "dial_test_initial_TTL": "Initial TTL", "dial_test_TTL_limited_number": "TTL limited number", "dial_test_target_device": "Target device", "rpquality_event": "event based", "rpquality_failure": "fault based", "rpquality_all_event_types": "All event types", "rpquality_all_fault_types": "All fault types", "rpquality_cause": "cause", "rpquality_duration": "duration", "rphealthy_min": "Min", "rphealthy_max": "Max", "rphealthy_aver": "Avg", "rphealthy_current_value": "Current", "rphealthy_cumulative_number": "Cumulative Count", "rphealthy_cumulative_duration": "Cumulative Duration", "rphealthy_average_duration": "Avg Duration", "rphealthy_deteriorate_num": "Deterioration Total Count", "specinfo_associated_identity": "Whether is relevant", "specinfo_spec_line": "Leased Line", "specinfo_remarks": "Remark", "pathtopo_new_filter": "Create filter", "pathtopo_unfilter": "Unfilter", "pathtopo_save": "Save", "pathtopo_sure_filter": "<p> Are you sure want to add filters to this data? </p>", "pathtopo_sure_unfilter": "<p> Are you sure want to unfilter this data? </p>", "pathtopo_probe_port": "Probe port", "pathtopo_target_name": "Target name", "pathtopo_target_port": "Target port", "pathtopo_task_type": "Task type", "phystopo_firewall": "Firewall", "phystopo_server": "Server", "phystopo_ul_ip": "<div> up-ip/down-ip:", "phystopo_dp_loss": "Delay / Loss:", "phystopo_ifr_flow": "InflowVelocity/OutflowVelocity:", "phystopo_sure_del": "Are you sure want to delete these records?", "phystopo_startTime_error_tip": "Please select a start time", "phystopo_endTime_error_tip": "Please select a end time", "qualityreport_menu_first": "1、Overall quality assessment", "qualityreport_menu_second": "2、Explanation of terms and testing methods", "qualityreport_menu_interpretation": "(I) Interpretation of terms", "qualityreport_methods": "(II) Detection methods", "qualityreport_optimization": "3、Suggestions for optimization", "qualityreport_net_report": "Network quality test report", "qualityreport_evaluation": "Digital evaluation model", "qualityreport_detection_cycle": "Detection cycle", "qualityreport_report_provider": "Provider", "qualityreport_netDem_line": "NetDem uses advanced network full coverage active detection technology to achieve WIFI+ Dial test + Leased line", "qualityreport_relay": "<PERSON><PERSON>", "qualityreport_360_degree": "360-degree no-dead Angle detection,\n\nUsing leading AI modeling and machine learning algorithms, it translates invisible network quality into visual digital ratings and provides targeted corrective suggestions to help users improve their digital experience.", "qualityreport_inner_detection": "Dial test detection", "qualityreport_WIFI_detection": "WIFI detection", "qualityreport_special_detection": "Leased Line detection", "qualityreport_relay_detection": "Relay detection", "qualityreport_score": "score", "qualityreport_intranet_optimization": "Dial test optimization suggestions", "qualityreport_target_refers": "Quality difference target refers to the target whose availability rate is less than 99% or excellent rate is less than 95% detected within the statistical analysis time range, which is found in this test", "qualityreport_Top10": "Among them, the Top10 are as follows:", "qualityreport_WiFi_dedicated": "WiFi, Dial test and leased Line of the enterprise through APP and collector", "qualityreport_comprehensive": "Conduct comprehensive monitoring,\n\nCollect and analyze data, intelligently locate quality problems, and provide targeted corrective suggestions.", "qualityreport_whose_target": "Quality difference target refers to the target whose availability rate is less than 99% or excellent rate is less than 95% detected within the statistical analysis time range, which is found in this test", "qualityreport_dif_10": "Quality difference target Top10", "qualityreport_good_95": "(Good/excellent less than 95% or availability less than 99%)", "qualityreport_more_details": "More details", "qualityreport_poor_link": "Quality poor link refers to the link with packet loss deterioration/delay deterioration/interrupt and long duration or wide range of influence. A large area of network quality poor may be caused by a quality poor link. NetDem adopts intelligent positioning technology.\n\nLocate to", "qualityreport_quality_impact": "Links with poor quality have a great impact on the quality of the entire network. The Top10 links are as follows:", "qualityreport_Top10_links": "Top10 links with quality difference", "qualityreport_quality_30": "(Avg quality difference stacking time is more than 30 minutes per day)", "qualityreport_problem_first": "Poor links have adverse impact on the overall network quality. Therefore, handle this problem first.", "qualityreport_test_problem": "No problem was found in this test, the network quality is excellent, please continue to maintain.", "qualityreport_WiFi_optim": "WiFi optimization suggestions", "qualityreport_within_total": "Within the time range of statistical analysis, a total of", "qualityreport_handicap_10": "Quality and handicap rank Top10", "qualityreport_check_found": "the second check, found", "qualityreport_detection_point": "is an optimizable detection point. Optimize according to suggestions. The Top10 are as follows:", "qualityreport_optimizable_items": "(There are optimizable items)", "qualityreport_download_rate": "Download rate：", "qualityreport_question": "Question:", "qualityreport_suggestions": "Suggestions:", "qualityreport_ascending_speed": "Ascending speed:", "qualityreport_signal_strength": "Signal strength:", "qualityreport_signal_interference": "Signal interference:", "qualityreport_connection_rate": "Connection rate:", "qualityreport_intranet_response": "Intranet response:", "qualityreport_encryption_protocol": "Encryption protocol:", "qualityreport_special_optim": "Leased Line optimization suggestion", "qualityreport_relay_optim": " Relay optimization suggestion", "qualityreport_availability": "Quality difference relay refers to the targets whose availability rate is less than 99% or superior rate is less than 95% detected within the statistical analysis time range", "qualityreport_targets_10": "the Top10 targets are as follows:", "qualityreport_trunk_10": "Quality difference trunk Top10", "qualityreport_XX": "Network quality test report of Chengdu XX Company", "qualityreport_overall": "1、Overall quality assessment", "qualityreport_terminology": "terminology", "qualityreport_explain": "explain", "qualityreport_comprehensive_score": "Comprehensive score", "qualityreport_network_quality": "The network quality of all monitoring targets is converted into a percentage score, the higher the score, the better the quality", "qualityreport_user_experience": "User experience", "qualityreport_describe_experience": "Describe the user experience on four scales (excellent, good, fair, and poor)", "qualityreport_ranking": "Comprehensive ranking", "qualityreport_network_assess": "Used to assess the level of network quality in all enterprises, the higher the ranking, the better the quality", "qualityreport_dif_target": "Quality difference target", "qualityreport_target_95": "The target of network commissioning is less than 99% availability or 95% excellent", "qualityreport_dif_link": "Quality difference link", "qualityreport_a_link": "A link consists of network nodes at both ends and communication lines between nodes. A link with poor quality is defined as a link that is interrupted or deteriorated and affects targets for more than 30 minutes per day", "qualityreport_ql": "Quality difference line", "qualityreport_rate_95": "The availability is less than 99% or the excellent is less than 95%", "qualityreport_qdr": "Quality difference relay", "qualityreport_relay_95": "The availability rate is less than 99% or the excellent is less than 95% of the relay", "qualityreport_wifi_channel": "WiFi channel", "qualityreport_transmission": "The data signal transmission channel using wireless signal as transmission carrier, the more devices on the same channel, the weaker the strength of WiFi signal", "qualityreport_wifi_frequency": "WiFi same frequency/adjacent frequency interference", "qualityreport_two_WiFi": "When two WiFi networks are in the same frequency band, the difference in the intensity of WiFi signals is less than 10dbm, and there is relevant interference between signals, affecting data transmission.", "qualityreport_task_number": "Task code", "qualityreport_source_IP": "Source IP", "qualityreport_target_name": "Target name", "qualityreport_availaby": "Availability", "qualityreport_cu_duration": "Deterioration Cumulative duration", "qualityreport_good_ratio": "Excellent", "qualityreport_influence_num": "Influence target number", "qualityreport_total_dif": "Quality differences total number", "qualityreport_total_dif_time": "Poor-quality Total Duration", "qualityreport_stack_dif_time": "Poor-quality Stack Duration", "qualityreport_dot": "Dot thumbnail", "qualityreport_point_label": "Point label", "qualityreport_access_WiFi": "Access WiFi", "qualityreport_download_Mbps": "Download Mbps", "qualityreport_inr_resp": "Intranet response ms", "qualityreport_ips": "Metric & Problems & Suggestions", "qualityreport_operator": "ISP", "qualityreport_dcn": "Deterioration Cumulative Count", "qualityreport_trunk_number": "Trunk number", "qualityreport_trunk_name": "Trunk name", "qualityreport_local_addr": "Local address", "qualityreport_peer_addr": "Remote address", "qualityreport_duration": "Trunk duration", "qualityreport_weight": "weight", "qualityreport_very_poor": "Poor", "qualityreport_quality_poor": "The network quality is poor and there is a lot of room for improvement", "qualityreport_general": "Avg", "qualityreport_quality_averg": "Network quality is average, there is a lot of room for improvement", "qualityreport_better": "Good", "qualityreport_quality_good": "The network quality is good and there is room for improvement", "qualityreport_excellent": "Excellent", "qualityreport_please_keep": "The network quality is good, please keep it up", "qualityreport_intranet": "Dial test", "qualityreport_sl": "Leased Line", "qualityreport_refers_line": "Quality difference line refers to the line whose availability is less than 99% or excellent is less than 95% within the statistical analysis time range, which is found in this test", "qualityreport_quality_10": "quality difference line, the Top10 are as follows:", "qualityreport_line_Top10": "Quality difference line Top10", "qualityreport_ar99": "(Availability less than 99% or excellent less than 95%)", "alarmlist_iln": "Influence Path Number", "alarmlist_st": "Alarm Start Time", "alarmlist_rt": "Alarm Recovery Time", "alarmlist_st1": "Alarm Start Time", "alarmlist_rt1": "Alarm Recovery Time", "alarmlist_operator": "Operator", "alarmlist_operate_time": "Operate Time", "alarmlist_operate_process": "Operate Process", "alarmlist_operate_result": "Operate Result", "alarmlist_fault_type": "Fault type:", "alarmlist_sfl": ", suspected fault link:", "alarmlist_cof": ", cause of fault:", "alarmlist_fi": "fault index (", "alarmlist_r_topology": "Route topology:", "alarmlist_source": "Source:", "alarmlist_initial_path": "Initial path", "shieldlist_fgt": "Time", "shieldlist_as_far": "(as far as possible)", "menu_DT_Poor": "DT Poor-quality Analysis", "menu_dial_test": "Dial Test", "menu_dlm": "Dedicated line management", "menu_relay_line": "Relay Line", "menu_NetDem": "NetDem", "menu_RL_qa": "RL Poor-quality Analysis", "msgservemail_mes_test": "Mail message test header", "msgservemail_se_mes_sus": "Send mail message test successful!", "pathrun_dimensions": "Dimensions:", "pathrun_monitor": "monitor", "pathrun_overview": "overview", "pathrun_path": "Path", "pathrun_total": "Total:", "pathrun_mom": "month-on-month", "pathrun_last_month": "last month", "pathrun_last_week": "last week", "pathrun_pbqt": "Period before the query time", "pathrun_keep": "keep", "pathrun_reduce": "reduce", "pathrun_increase": "increase", "pathrun_avd": "Avg delay:", "pathrun_eagr": "Excellent and excellent:", "pathrun_grr": "Good/good ratio =∑ per path (running duration - total interrupt duration - total deterioration duration + total interrupt and deterioration overlap duration)/∑ per path running duration *100%", "pathrun_gr_running": "Good/excellent =(running duration - total interrupt duration - total deterioration duration)/ running duration *100%", "pathrun_Ava_rate": "Availability =∑ per path (running duration - total interrupt duration)/∑ per path running duration *100%", "pathrun_Ava_run": "Availability =(running duration - total interrupt duration)/ running duration *100%", "pathrun_average": "avg", "pathrun_duration": "duration:", "pathrun_insi": "Interruption situation [TOP5: Availability less than 99%]", "pathrun_de5": "Deterioration [TOP5: excellent is less than 95%]", "pathrun_topo": "Topology diagram:", "pathrun_event_time": "Event time", "pathrun_as_far_po": "(as far as possible)", "pathrun_cause": "cause", "pathrun_event": "event", "pathrun_w_n": "whole network", "pathrun_operation_report": "operation report", "pathrun_np": "Normal path", "pathrun_dp": "Deterioration path", "pathrun_ipa": "Interrupt path", "pathrun_plr": "Delay, packet loss rate -- day chart", "pathrun_ava_good": "Availability, good and excellent -- weather chart", "pathrun_plr24": "Delay, packet loss rate -- 24 hour graph", "pathrun_ava_good24": "Availability, good and excellent -- 24 hours chart", "pathrun_total_num": "Total number of monitoring tasks", "pathrun_iter_task": "Number of interrupted tasks", "pathrun_der_tn": "Deterioration task number", "pathrun_cuindu": "Cumulative interrupt duration", "pathrun_mptn": "Monitored paths total number", "pathrun_ipn": "Interrupt paths number", "pathrun_dpn": "Deterioration paths Number", "pathrun_mltn": "Monitor lines total number", "pathrun_normal_task": "Normal task", "pathrun_de_task": "Deterioration task", "pathrun_inte_task": "Interrupt task", "pathrun_branch_office": "Branch office", "pathrun_moor": "Monthly operation report", "pathrun_task_details": "Task details", "pathrun_source_IP": "(Source IP address:", "pathrun_objectives": ", objectives:", "pathrun_failure": "fault", "pathrun_normal_line": "Normal line", "pathrun_degraded_line": "Degraded line", "pathrun_interrupt_line": "Interrupt line", "pathrun_pnum": "Path number", "pathrun_Z_alias": "Z-terminal alias", "pathrun_cum_det": "Cumulative deterioration count", "dashboardPie_no_sa": "No statistics are available", "dashboard_psad": "Path statistical analysis diagram", "msgservenote_SDK_file": "SDK file:", "msgservenote_enter_content": "Please enter the test content", "msgservenote_incor_2060": "Incorrect port input (20-60000)", "msgservenote_SMS": "SMS gateway", "msgservenote_SDK_SMS": "SDK SMS interface", "spechealthy_leaderboard": "Leaderboard:", "spechealthy_ie": "Input efficiency", "spechealthy_oe": "Output efficiency", "group_fill_name": "please fill in the group name", "messageconfig_select_mode": "Select the push mode", "messageconfig_not240": "The content must not exceed 240", "qualityrs_aw": "Access WIFI", "qualityrs_highest": "Highest", "qualityrs_average": "Avg", "qualityrs_poor": "Poor", "qualityrs_work": "work", "qualityrs_vc": "Video", "qualityrs_hvc": "Hd video conferencing", "qualityrs_inp": "Introduction to the problem", "qualityrs_sosu": "Solution suggestion", "qualityrs_sis": "Signal strength", "qualityrs_sii": "Signal interference", "qualityrs_cr": "Connection rate", "qualityrs_inre": "Intranet response", "qualityrs_nodn": "Number of devices on network", "qualityrs_chat": "chat", "qualityrs_trill": "trill", "qualityrs_sco": "monitoring", "qualityrs_4ksco": "4K monitoring", "pathquality_t_de": "Task details", "pathquality_si": "(Source IP:", "pathquality_obs": ", objectives:", "pathquality_inde": "Interrupt details", "pathquality_dede": "Deterioration details", "flowChart_sou": "Source", "flowChart_aim": "Target", "flowChart_srl": "Leased line", "flowChart_n1": "Node 1_IP", "flowChart_n2": "Node 2_IP", "flowChart_atd": "AvgDelay", "flowChart_sou1": "(Source)", "flowChart_aim1": "(Target)", "flowChart_aim3": "(Target)", "flowChart_sou2": "Source", "flowChart_aim2": "Target", "flowChart_ld": "line/destination", "flowChart_un": "Untrusted node", "rppath_loading_data": "Loading data", "message_retransmission": "Retransmission", "common_data_time": "Last 1 hours", "common_data_time2": "Last 3 hours", "common_data_time3": "Last 6 hours", "common_data_time4": "Last 12 hours", "message_wechat": "Wechat", "common_loading": "Loading...", "common_no_data": "No more data available...", "comm_warn_tip_title": "<PERSON><PERSON>", "comm_pattern": "pattern", "comm_topo_style": "topo style", "common_unrecovered": "Unrecovered", "common_recovered": "Recovered", "server_node_type": "node type", "common_index_selection": "Index selection", "common_all": "select all", "common_previous": "Previous page", "common_next_page": "Next page", "common_query": "Query", "common_new": "New ", "common_add": "Add ", "common_add_success": "Add success", "common_update_success": "Update success", "common_update": "Update ", "common_delete": "Delete", "common_remove": "Remove", "common_email": "mail format is incorrect", "comm_IP(MAC)": "Target IP(MAC)", "access_destination_acc": "Destination address", "access_1": "task code, collector, target IP address, and target name", "access_2": "trunk code, collector, peer address, and trunk name", "access_3": "line number, line name, terminal A interconnection IP, terminal Z interconnection IP", "access_4": "line name, interconnection IP address of end A, and interconnection IP address of end Z", "access_5": "trunk name, local address, and peer address", "access_6": "task code, probe IP, target IP, and target name", "access_want_add": "Please select the data you want to add", "topo_path": "Path topology", "dash_A": "A-Internet-IP", "dash_Z": "Z-Internet-IP", "dash_line_number": "LL NO", "dash_view_succ": "view success", "dash_Special_line_name": "LL name", "dash_average_delay": "Avg delay", "dash_average_delay_ms": "Avg delay(ms)", "dash_average_packet_loss": "Avg packet loss rate", "specquality_time": "The end time must not be later than the start time", "spec_fail": "Device error item", "spec_error_file_name": "Leased Lines error item file", "common_loss_degradation": "Packet loss deterioration", "common_line_congestion": "Private line congestion details", "common_port_congestion": "Details about port congestion", "common_collector_offline": "Collector Offline", "common_back": "Return", "common_No": "No.", "common_cancel": "Cancel", "common_verify": "Confirm", "common_test": "Test", "common_controls": "Operations", "common_action": "Action", "common_action_attribute": "Action attribute", "common_controls_succ": "Operation succeed", "common_controls_fial": "Operation failed", "common_data_verification_tip": "Data verification", "common_data_verification_total_tip": "total", "common_data_verification_data_tip": "data", "common_data_verification_where_tip": "where", "common_data_verification_error_tip": "error", "common_data_verification_upload_again_tip": "If there are errors, only import the correct items or adjust Excel before uploading again", "common_file_match": "The template does not match. Please select the provided template for import", "qualityreport_strategy": "Strategy", "user_name": "Name", "comm_select_group": "Please select a group", "qualityreport_level": "Experience level", "specquality_strat": "Please select a start time first", "phytopo_sycn": "If Automatic synchronization is configured, the system automatically updates the network topology connection relationship (excluding manually creating a line) when it detects that the connection relationship changes. Do you want to change it to Automatic synchronization?", "phytopo_ops": "If Manual synchronization is set to Manual synchronization, the system will update the network topology only when it detects that the connection relationship changes. Do you want to change it to Manual synchronization?", "phytopo_line_name": "Line name :", "phytopo_enter_line_name": "Please enter a line name", "phytopo_enter_description": "Please enter a description", "phytopo_resource_selection": "Resource selection", "phytopo_connect_device_1": "Connect Device 1:", "phytopo_connect_device_2": "Connect Device 2:", "phytopo_device_ip": "Device ip", "phytopo_device_ip1": "Device ip1", "phytopo_device_ip2": "Device ip2", "phytopo_enter_ip": "Please enter device ip", "phytopo_port_1": "Port 1", "phytopo_port_2": "Port 2", "phytopo_ohter": "Other", "phytopo_upward_direction": "Upstream direction :", "phytopo_port_1_to_port_2": "Device port 1 to device port 2", "phytopo_port_2_to_port_1": "From Device port 2 to device port 1", "phytopo_remark": "Remark", "phytopo_enter_remarks": "Please enter remarks", "phytopo_select_data": "Please select data", "phytopo_sure_delete": "Are you sure want to delete these records?", "common_delete_success": "Delete success", "common_delete_failed": "Delete failed", "phytopo_select_device_1": "Select device 1", "phytopo_select_device_2": "Select Device 2", "phytopo_select_direction": "Please select direction", "phytopo_select_port_1": "Select port 1", "phytopo_select_port_2": "Select port 2", "phytopo_key_words": "Keyword", "phytopo_fuzzy": "Supports fuzzy matching of line names", "phytopo_synchronization": "Data synchronization", "phytopo_line": "Line name", "phytopo_connected_device_name_1": "Connect device name 1", "phytopo_connected_device_name_2": "Connect device name 2", "phytopo_connect_device_IP1": "Connect device IP1", "phytopo_connect_device_IP2": "Connect device IP2", "phytopo_port_1_description": "Port 1 Description", "phytopo_port_2_description": "Port 2 Description", "phytopo_topology_name": "Topo Name", "phytopo_device_Name": "Device name", "phytopo_device_Name1": "Device name 1", "phytopo_device_Name2": "Device name 2", "phytopo_enter_device_name": "Please enter device name", "phytopo_device_IP": "Device IP", "phytopo_enter_device_IP": "Enter device IP", "phytopo_template_download": "Template download", "phytopo_select_attachment": "Select attachment", "phytopo_use_templates": "Use templates to edit and import data!", "phytopo_download_template": "Download template:", "phytopo_upload_attachment": "Upload attachment", "phytopo_export_error_item": "Export error item", "phytopo_row_num": "Row number", "phytopo_error_details": "Error details", "phytopo_topology_template": "Import physical topology line template", "phytopo_read_failed": "Read failed! Please re-download the template and upload again", "phytopo_empty_template": "You uploaded an empty template, please enter data", "phytopo_failed_import": "Import failed, please check whether the file conforms to specifications", "phytopo_abnormal": "Abnormal item of a physical topology line", "common_No_abnormal": "No abnormal item", "common_No_match": "No matching data", "phytopo_upload_successfully": "Upload successfully", "phytopo_upload_failed": "Upload failed", "phytopo_try_again": "It has been uploaded once, please go back to the previous step to try again", "phytopo_select_province": "Please select province", "phytopo_select_city": "Please select city", "phytopo_information": "Information Settings", "phytopo_legend": "Legend", "phytopo_No_data": "There is currently no data in the topology map", "common_Normal": "Normal", "phytopo_deterioration": "Deterioration", "phytopo_interrupt": "Interrupt", "phytopo_enter": "IP/ name query", "phytopo_management": "Topo manage", "phytopo_line_management": "Line manage", "spec_topo": "This operation updates automatically added lines on the topo. This operation does not involve manually adding lines, the topology may change", "common_No_data": "No data", "phytopo_selection": "Topology selection", "phytopo_alarm": "Alarm", "phytopo_alarm_show": "Alarm display configuration", "phytopo_line2": "Line", "phytopo_enter_name": "Please enter name", "phytopo_enter_number": "Please enter the number of renders", "common_detele_sure": "Are you sure to delete these records?", "phytopo_export_data": "Select the data you want to export.", "phytopo_topology_list": "Physical topology line list", "phytopo_network_error": "Network connection error", "phytopo_add_time": "Add at least one time range.", "phytopo_10_time": "Add up to 10 periods", "phytopo_displayed": "When entering the physical topology map, the default open topology map:", "phytopo_subgraphs": "Managed Subgraphs", "phytopo_managed_objects": "Managed Objects", "phytopo_add": "Add ", "phytopo_remvoe": "Remove ", "phytopo_obj": "Object", "phytopo_add_to": "Add object", "phytopo_remove_to": "Remove object", "phytopo_subgraph": "subgraph", "phytopo_topo": "topo view", "phytopo_desc": "Description", "phytopo_enter_topo_name": "Please enter a topology name!", "phytopo_same_name": "Same name already exists", "phytopo_add_obj": "Add object", "phytopo_remove_obj": "Remove object", "phytopo_add_sub": "Add subgraph", "phytopo_remove_sub": "Remove subgraph", "phytopo_blow_up": "Zoom in", "phytopo_minification": "Zoom out", "phytopo_page_restore": "page restore", "phytopo_exporting_PNG": "export PNG", "phytopo_fault_path": "Fault path", "comm_quality": "Quality difference analysis of Leased line", "comm_wifi": "wifi analysis", "spec_tip": "The selected data contains the data that is being deleted. Do not perform other operations", "spec_tip_error": "Data is being deleted. Do not perform other operations", "system_configuration": "System Configuration", "system_logo": "System LOGO", "system_name": "System Name", "system_copyright_information": "Copyright Info", "system_pixels_than_png_image": "64x64 pixels, less than 1MB PNG format image", "character": "char", "system_network_name": "", "system_server_ip": "Server IP", "system_process_id": "Process ID", "system_process_name": "Process Name", "system_server_memory": "Memory", "message_prompt": "Tips", "system_prompt_select_file": "Please Select The Attachment You Want To Update", "system_prompt_input_system_name": "Please Enter The System Name", "system_prompt_input_copyright": "Please Fill In The Copyright Information", "system_prompt_uploaded_file_error": "The uploaded file is incorrected. Only PNG format is allowed", "reachability_probe_online": "Online", "reachability_probe_offline": "Offline", "reachability_probe_normal": "Normal", "reachability_dial_type": "Dial Type", "reachability_probe_port": "Target Port", "reachability_prompt_port": "Please Use English Comma To Separate Multiple", "reachability_port_verified_connected": "Port connectivity is verified one by one. If a port is connected, the port is reachable and other ports are not verified. If all ports are not consistent, they are judged to be unreachable!", "reachability_execution_mode": "Mode", "reachability_execution_manual_input": "Manual Input", "reachability_execution_batch_import": "Batch Import", "reachability_dest_ip": "Target IP/IP Range", "reachability_prompt_use_same_ip": "Use the same ip segment and connect with hyphens (-)", "reachability_please": "Please", "reachability_user_template_edit": "，Use a template to edit and import data", "reachability_carry_out": "Carry Out", "reachability_clear": "Clear", "reachability_import_error_message": "Import Error Message", "reachability_path": "Path", "reachability_connected_port": "Connected Port", "reachability_accessibility": "Accessibility", "reachability_verification_result_set": "Reachability Verification Result Set", "reachability_maximum_ports": "A Maximum Of Five Ports Can Be Added", "reachability_verify_probe_port": "The Probe Port Input Is Incorrect. The Probe Range Is Incorrect", "reachability_only_one_hyphen": "Only one hyphen (-) can be used to connect an ip address segment", "reachability_port": "Please enter the target port", "reachability_non_execution": "non-execution： ", "reachability_accessibility_verification": "Accessibility Verification", "reachability_clear_accessibility_verification": "Clear Accessibility Verification", "reachability_clear_prompt": "Clear Prompt", "reachability_verification_import_template": "Reachability Verification Import Template", "test_report_file_format": "File Type", "test_report_select_file_format": "Please select a file type", "test_report_disposition": "Disposition", "test_report_noStart": "Not Started", "test_report_build_failure": "Build Failure", "test_report_generating": "Creating", "test_report_generated": "Created", "test_report_label_name": "Reporte name", "test_report_label_statistical_time": "Time：", "test_report_label_group": "Group：", "test_report_label_file_format": "File Format：", "test_report_generate_report": "Create", "test_report_update_disposition": "Update Disposition", "test_report_provider": "Provider", "test_report_describe": "Describe", "test_report_limit_time": "The Time Span Should Not Exceed 92 Days", "test_report_limit31_time": "The Time Span Should Not Exceed 31 Days", "test_report_prompt_delete": "Are you sure want to delete this data", "test_report_prompt_select_data": "Please Select The Data That Needs To Be Downloaded", "test_report_prompt_no_down_file": "There Are Currently No Downloaded Files Available", "test_report_pptx_template": "PPTX Template", "test_report_current_template": "Current Template", "test_report_no_template": "There Is Currently No Template Available", "test_report_update_template": "Update Template", "test_report_docx_template": "DOCX Template", "test_report_prompt_pptx_size": "Please upload a template file with a size of less than 50MB and a format of pptx!", "test_report_prompt_docx_size": "Please upload a template file with a size of less than 50MB and a format of docx!", "backup_incorrect_ip_format": "Incorrect IP format", "backup_user_name": "User", "backup_user_name1": "User", "backup_password": "Password", "backup_prompt_fill_password": "Please Fill In The Password", "backup_prompt_fill_path": "Please Fill In The Backup Path", "backup_schedule": "Schedule", "backup_data_backup": "Data Backup", "backup_down_data_backup": "Download Data Backup (Name:", "backup_prompt_delete": "Confirm Whether To Delete", "backup_prompt_remote_no_information": "Incomplete remote information filling during remote backup!", "testspeed_local_to_server": "Local To Server", "testspeed_pc_terminal_number": "PC Terminal Number", "testspeed_ip_address": "IP Address", "testspeed_please_enter_number": "Enter The Number Of The PC", "testspeed_please_enter_ip_address": "Please enter the IP address", "comm_selector": "Selector", "comm_friendly_reminder": "Friendly Reminder", "comm_time_delay": "Delay", "comm_quiver": "Jitter", "comm_loss": "Packet Loss", "comm_no": "No", "comm_yes": "Yes", "comm_show": "Show", "comm_no_show": "Do not show", "comm_the_same_month": "The Same Month", "comm_last_month": "Last Month", "comm_prompt_confirm_delete": "Are you sure Want To Delete It?", "comm_prompt_deleting": "Deleting", "testspeed_delete_selected_information": "Whether to delete the selected information", "peertopeer_monitoring": "Monitor status", "peertopeer_tip": "Path name, start IP, start name, target IP,target name", "peertopeer_path_name": "Path name", "peertopeer_path_name2": "Path name:", "peertopeer_start_IP": "Start IP", "peertopeer_start_name": "Start name", "peertopeer_probe": "Probe", "peertopeer_initial_node": "Start node:", "peertopeer_target_node": "Target node:", "peertopeer_select_initial": "Please select the start node", "peertopeer_select_target": "Please select the target node", "peertopeer_select_name": "Please enter the path name", "peertopeer_select_probe": "Please select a probe", "peertopeer_path_information": "Path information", "probe_frequency": "Dial frequency", "probe_dynamic": "Dynamic IP", "probe_net_state": "DNAT Monitoring", "probe_1": "Probe IP, target IP/MAC/target name, task code, and path number", "probe_2": "When the monitoring target is a dynamic IP, this option can be checked; If the target of dynamic IP is temporarily monitored and the IP remains unchanged for a short period of time, this option can be left unchecked", "probe_net_state_tip": "Check this box if you want to monitor cross-DNAT networks", "probe_adjustment": "Job adjustment", "probe_derived": "Export nodes", "probe_unmatch": "Unmatch", "probe_byte": "<PERSON>", "probe_ip_mac": "Enter at least one target IP address and one destination MAC address", "probe_mac_ip": "If MAC is specified, select Dynamic IP", "probe_delay_sensitivity": "Delay deterioration sensitivity should not be empty ", "probe_delay_sensitivity_1": "Delay deterioration sensitivity, only 1-10 input is allowed ", "probe_delay_experience": "Delay deterioration experience value cannot be empty ", "probe_delay_experience_3000": "Delay deterioration experience value, only 1-3000 can be entered ", "probe_device_type_40": "Target type only allow a maximum of 40 characters", "probe_group_name_20": "Group name only allow a maximum of 40 characters", "probe_mac_48": "MAC only allows a maximum of 48 characters ", "probe_enter_mac": "Please enter the correct MAC address ", "probe_enter_positive": "Only positive integers are allowed on the port", "probe_port_60000": "Probe port between 20 and 60000", "probe_target_port_60000": "Target port 20 to 60000", "probe_port_10": "Only 1-10 target ports are allowed ", "warning_loss": "Packet loss deterioration generation threshold reached once ", "warning_packet_loss_1": "Packet loss alarm generation threshold", "probe_param_setting": "Dial test parameter", "probe_event_setting": "Event parameter", "probe_operation_plan_setting": "Job cycle setting", "probe_packets_no": "send packets nums", "probe_packet_size": "package size", "probe_tos_value": "TOS(IPv4)", "probe_delay_sensitivity_2": "Delay sensitivity ", "probe_delay_sensitivity_3": "The smaller the value, the more sensitive the system perception delay deteriorates,1-10 levels ", "probe_delay_sensitivity_4": "When there is no normal historical data to learn, use this value to determine whether it is deteriorating, allowing input of 1-3000", "probe_experience_value": "Delay threshold", "probe_route_alarm": "Route fluctuation alarm", "probe_no_occurs": "No interrupt event occurs when the target interrupt is located ", "probe_window_size": "window size", "probe_frequency_positioning": "frequency of positioning", "probe_Initial_TTL": "start TTL:", "probe_TTL": "TTL limited hops:", "processinfo_cpu_usage": "CPU Usage", "processinfo_memory_usage": "Memory Usage", "comm_duplicate": "duplicate ip", "comm_min": "Min (%)", "comm_max": "Max (%)", "comm_aver": "Avg (%)", "node_ip": "Source IP address and link", "comm_cpu_usage": "CPU Usage", "comm_memory_usage": "Memory Usage", "comm_please_enter_correct_ip": "Please enter the correct IP address", "comm_please_enter_correct": "Please enter the correct", "comm_cpu_core": "CPU (Core)", "serversmonito_average_usage_partition": "Maximum Avg Storage Usage Partition", "sub_server_path_duplicate": "Service path duplication", "comm_prompt_select_delete_data": "Select The Data You Want To Delete", "systemlog_log_source": "Source", "systemlog_log_type": "Type", "systemlog_prompt_keywords": "Fuzzy Query By Keyword Log Content And Method Name Is Supported", "comm_derive": "Export", "comm_home": "Proactive Network Performance Perspective System", "comm_class_name": "Class Name", "comm_method_name": "Method Name", "systemlog_content": "Log Content", "systemlog_report": "System Log Report", "comm_prompt_confirm_delete_data": "Are you sure want to delete these records?", "comm_province": "Province", "comm_city": "City", "comm_area": "County", "org_head_phone": "Resp person，Telephone", "comm_contact_information": "contact information", "comm_org_name": "Name", "comm_org_id": "ID", "comm_org_name_person_charge": "Resp person", "comm_org_maximum_phone": "Mobile phone numbers can only be entered up to 10", "comm_org_number": "Number", "comm_text_essage": "SMS", "comm_send": "Send", "comm_no_send": "No Send", "comm_use_templates_edit_import": "Using templates for data editing and import", "comm_list": "list", "comm_sequence_number": "Sort Number", "comm_organization_data_template": "Organization Information Template", "comm_prompt_file_uploaded_incorrect": "The uploaded file is incorrect. Please upload it again", "comm_organization_data_duplicates": "Organization data duplicates", "comm_use_status": "Condition", "comm_please_select_account_status": "Please Select Account Status", "comm_online_state": "Status", "comm_please_select_online_status": "Please Select Online Status", "comm_approval_status": "Approval", "comm_please_select_approval_status": "Please Select Approval Status", "comm_user_keyword": "Fuzzy Search By Keyword Name Is Supported", "but_dormancy": "<PERSON><PERSON><PERSON>", "but_sign_out": "Log out", "comm_account_types": "Type", "comm_select_account_types": "Select Account Type", "comm_please_enter_login_password": "Please Enter Your Login Password", "comm_intensity": "Intensity", "comm_pwd_expri_date_select": "Select Password Validity Period", "user_pwd_expri_date_2": "2 months", "comm_role_authority": "Juris<PERSON>", "comm_please_select_role": "Please select a role", "comm_mobile_phone_number": "Mobile Phone", "comm_please_enter_mobile_number": "Please enter your mobile number", "comm_please_enter_email_number": "Please enter your email number", "comm_identification_card": "Id Card", "comm_please_enter_id_card": "Please enter your ID card", "comm_client_binding": "Client bind", "comm_client_ip": "Client IP", "comm_supported_ip_separated": "Multiple IP addresses are supported and separated by commas (,)", "comm_password_no_contain_account": "The password cannot contain the account character", "comm_ip_maximum": "The maximum number of ip addresses is 10", "comm_please_enter_correct_ip_address": "Please enter the correct IP address", "comm_permanent_account": "Permanent account", "comm_temporary_account": "Temporary account", "comm_pending_approval": "Wait approval", "comm_pass": "Pass", "comm_no_pass": "Unpass", "comm_examine_and_approve": "Examine and approve", "comm_please_enter_password_again": "Please enter your password again", "comm_please_enter_password_validity_period": "Please select the password validity period", "comm_select_least_one_repeat_period": "Select at least one repeat period", "comm_start_than_end_time": "The start time cannot be later than the end time", "comm_please_wait_while_submitting": "Please wait while submitting", "user_dormancy_account": "Are you sure want to dormancy your account?", "user_select_approved_select_other": "The selected user has an account to be approved. Please select another one", "user_select_deregistered_select_other": "The selected user has a deregistered account. Please select another one", "user_logout_prompt": "Logout prompt", "comm_sure_select_log_out": "Are you sure want to log out the selected user?", "comm_logout_successful": "Logout successful", "comm_select_logged_out_user": "Select the user to be logged out", "user_approval_details": "Approval details", "comm_submission_time": "Time", "comm_submitter": "Submitter", "comm_submit_content": "Content", "comm_create_account": "Create an account", "comm_change_authority": "Change of authority", "comm_cancel_account": "Cancel account", "comm_dormant_account": "Dormant account", "comm_account_expiration_activation": "Account expiration activation", "comm_account_activation": "Account activation", "comm_no_pass_fill_remark": "If no, please fill in the remarks", "comm_approval_list": "Approval list", "comm_approval_time": "Approval time", "comm_select_date2": "Select the date", "user_approval_remark": "Approval remarks", "user_approver": "Approver", "user_approval_result": "Approval result", "user_identification": "Identification", "user_please_enter_password": "Please enter your password to confirm that you are the operator", "user_login_password": "Password", "user_please_enter_login_password": "Please enter your login password", "comm_start_stop_date": "Start-End", "comm_maximum_10_time_segments": "Note: A maximum of 10 time segments are supported and cannot be crossed", "comm_please_select_start_end_date": "Please select a start and end date", "access_mac": "Target MAC", "access_enter_mac": "Please enter the target MAC", "comm_descriptive_information": "Descriptive information", "comm_unselected_user": "Unselected user", "comm_selected_user": "Selected user", "role_keyword_tips": "Support name, account, phone number query", "comm_length_exceed": "The length must not exceed ", "comm_customer_list": "Customer list", "comm_personnel_management": "Personnel management", "role_creating_role": "Creating a role", "role_authority_management": "Authority management", "comm_system_administrator": "System administrator", "comm_audit_administrator": "Audit administrator", "comm_service_configurator": "Service configurator", "comm_service_operator": "Service operator", "comm_audit_manager": "Audit manager", "comm_please_select_customer_name": "Please select a customer name", "comm_remove_function_list": "Remove function list", "comm_add_function_list": "Add function list", "role_sure_freeze": "Are you sure to freeze this role?", "role_sure_activate": "Are you sure to activate the role?", "role_selected_user_already_exists": "The selected user already exists", "role_account_please_select": "Account number, please re-select", "role_selected_role": " a selected role?", "role_please_select_need": "Please select need", "role_add_personnel": "Add personnel", "role_personnel_have_not_changed": "Personnel have not changed", "role_personnel_changes_follows": "Personnel changes are as follows ", "role_contains_user_no_deleted": "This role contains users and cannot be deleted.", "role_cannot_be_moved": "The current logged-in user cannot be moved out", "role_select_user": "Selected user", "role_only_one_role_not_move": "Only one role is associated and cannot be removed", "role_please_select_person_move": "Please select the person to move out", "role_please_check_person_add": "Please check the person to be added", "role_confirm_move_select_person": "Confirm whether to move out the selected personnel", "comm_please_fill_in": "Please fill in ", "comm_please_select_delete": "Please select the record you want to delete", "message_temp_sure_delete": "Are you sure want to delete the selected message template?", "message_current_org_exist_records": "The current organization has records of the same message type, alarm type, and template type?", "message_variable_length": "The message content exceeded the maximum length after the variable was inserted", "comm_error_message_details": "Error message details", "comm_creation_time": "Creation time", "comm_select_manipulate_data": "Select the data you want to manipulate", "comm_fuzzy_query_user_supported": "Fuzzy query by user is supported", "message_config_push_detail": "Push condition details", "message_config_perform_advanced_configuration": "By default, all alarms are pushed. You can perform advanced configuration to configure the alarm push conditions", "message_config_select_push_person": "Please select the person you want to push", "message_config_repeat_reminder": "Repeat remind", "message_config_single_reminder": "Single remind", "message_config_alarm_reminder": "Alarm reminder", "message_config_after_minutes_push": "After minutes, the user is notified again by pushing a message", "message_config_please_enter_interval": "Please enter the interval", "message_config_alarm_interval": "Alarms are alerted after every interval", "message_config_push_every_minute": "push a message to notify the user", "message_config_description_200": "The description contains 0 to 200 characters", "comm_advanced": "Advanced", "comm_simple": "Simple", "comm_settings": "Set", "comm_advanced_task": "Advanced Settings", "comm_simple_task": "Simple Settings", "message_config_or_and": "Calculation mode or can appear only at the end of the calculation mode. For example, A and B and C or D are allowed, but A and B or C and D are not allowed", "message_config_default_push_config": "By default, all alarms are pushed. You can perform advanced configuration to configure the alarm push conditions", "message_config_keyword": "Support by name, account, mobile phone number, etc", "message_config_value_positive_1000": "The value is a positive integer ranging from 1 to 1000", "comm_delete_data": "Are you sure want to delete this data?", "comm_being_activated": "Being activated", "comm_selected_data_activated": "The selected data contains activated data, please re-select", "comm_freeze_effect": "Freeze in effect", "comm_selected_data_freeze": "The selected data contains frozen data, please re-select", "comm_selected_alarm_task_content": "Select the alarm content for the task", "comm_selected_alarm_task_type": "Select an alarm type for the task", "message_config_or_last_position": "The or mode can only be in the last position", "message_config_exist_same_conditions": "Multiple identical conditions exist", "message_config_exist_mutually_conditions": "If mutually exclusive conditions exist, reconfigure them", "message_config_maximum_10_conditions": "A maximum of 10 configuration conditions are supported", "message_config_select_reminder": "Please select the repeat reminder mode", "message_config_all_select_exist": "The selected person already exists in the list. Whether to overwrite the selected person", "message_config_gather_keywords": "query by name and Collector number", "message_config_add_conditions": "Add new conditions", "message_config_selective_collector": "Selective collector", "message_config_selective_grouping": "Selective group", "message_config_grouping_keywords": "Supports query by group name", "message_config_task_keywords": "Query by task code, probe IP and target IP", "message_config_relaying_keywords": "Query by device name and device ip", "comm_group_name": "Group Name", "comm_traffic_congestion": "Traffic congestion", "message_config_gather_maximum_100": "A maximum of 100 collectors can be selected", "message_config_selected_data_exist": "The selected data already exists in the condition, overwrite or not", "message_mail_keywords": "Fuzzy matching query by mail service name, server/ip, sender name, and email address is supported", "comm_sender_name": "Sender Name", "comm_sending_email_address": "Send Email", "comm_test_receiving_mailbox": "Test Mailbox", "comm_testing_wait": "Testing, please wait", "message_mail_server_ip_name": "Please enter correct server / IP ", "message_enter_correct_mail": "Please enter the correct Email address", "comm_enter_port": "Please enter port", "message_enter_email_password": "Please enter your email password", "message_email_server_name": "Mail Service Name", "message_test_email_title": "Mail message test title", "message_note_keywords": "Fuzzy matching query by SMS service name is supported", "message_note_provider_url": "URL of the SMS gateway provider. URL parameters are not included. Such as:http://www.smsserver.com/sendsms", "message_note_Url_parameter": "Param", "comm_skd_file": "SKD Document", "comm_Script": "<PERSON><PERSON><PERSON>", "message_note_result": "The returned result is represented by the result parameter, for example:", "message_note_test_phone": "Test phone", "message_note_format_phone": "Phone format is incorrect", "message_note_phone_empty_tip": "Please enter the phone", "message_note_agentId_empty": "Please enter agentId", "message_note_format_agentId": "AgentId format is incorrect", "message_note_incorrect_port": "Incorrect Port Input", "message_note_service_name": "SMS Service Name", "message_note_file_failed_download": "Failed to download the empty file. Procedure", "message_note_parameter_20": "The parameter contains a maximum of 20 parameters", "message_dingding_keywords": "Supports fuzzy matching query by appName and AgentId", "message_dingding_test_phone": "Please enter the test receiving phone number", "comm_module": "<PERSON><PERSON><PERSON>", "comm_operation_type": "OPR Type", "operation_log_keywords": "Fuzzy query by operation log is supported", "comm_consecutive_login_failure": "Consecutive login failure", "comm_ip_change": "The IP address changes too much", "comm_unauthorized_access": "Unauthorized access", "comm_login_failure": "<PERSON><PERSON> failed", "comm_login_successful": "Login success", "comm_quit": "Exit", "comm_routine_operation": "Common operations", "comm_access_ip": "Access IP", "operation_log_operation_log": "Operation Log", "comm_log_event_content": "Event content", "comm_system_log": "System log", "comm_check_all": "Check all", "comm_service_level_audit": "Business level audit", "comm_alarm_level": "Level", "comm_importance": "Importance", "operation_event_keywords": "Fuzzy search by alarm content is supported", "comm_alarm_content": "Alarm Content", "comm_event_alarm": "Event alarm", "operation_visits_top10": "The number of visits TOP10", "operation_indicates_event": "Indicates the proportion of the event type", "operation_allow_out_login": "Allow out-of-hours login", "comm_point": "Point", "comm_quantity": "Quantity", "comm_proportion": "Take up a proportion of", "comm_log_statistics": "Log statistics", "comm_audit_strategy": "Audit strategy", "verify_configuration_incorrect": "Verify that the configuration is incorrect", "comm_item_fill_in": "This item is not filled in", "comm_file_value_large": "The fill value is too large", "comm_value_must_less_than": "This value must greater than ", "comm_value_must_greater_than": "This value must less than ", "comm_associated_attribute": "Associated attribute", "comm_number_relay_tasks": "Relay task count", "comm_number_probe_missions": "Probe task count", "comm_number_spec_missions": "Leased lines  task count", "group_gather_keywords": "Fuzzy match query by collector, device name, and device ip", "group_task_keywords": "Fuzzy matching query is performed by task code, probe IP and target IP", "group_sure_create": "Confirm whether to add?", "comm_please_check_data": "Please check data", "group_sure_delete": "Are you sure to remove it?", "comm_removed_successfully": "Removed successfully", "comm_select_removed_data": "Select the data you want to remove", "gather_task_normal_all": "Number of tasks(Normal/All)", "comm_remark_length_maximum": "Note Contains a maximum of 200 characters", "specinfo_operator": "ISP", "please_enter_correct_idcard": "Please enter the correct ID number", "idcard_cannot_contain_special_characters": "The ID card cannot contain special characters", "idcard_allows_20_characters": "Only allows 0-20 characters", "fibre": "Fibre", "alarm_details": "Alarm details", "go_and_check": "Go and check", "number_of_alarms": "Number of alarms", "comm_optimal": "optimal", "comm_good_two": "good", "comm_line_flow": "Line flow", "comm_line_delay": "Line delay", "comm_line_packet_loss_rate": "Line packet loss rate", "comm_termination_request": "Termination request", "comm_routine": "routine", "comm_operation_plan": "Operation plan", "comm_collected_or_not": "Whether monitored", "discoverrule_no_select_device": "Add at least one source to the device or source to the node connected to the device", "nodequality_link_number_tips": "Number of links that have not experienced interruption or deterioration within the statistical time range", "nodequality_deterioration_tips": "Number of links that have experienced deterioration within the statistical time range", "nodequality_interrupt_tips": "Number of links that have experienced interruption within the statistical time range", "comm_mechanism_selection": "Mechanism selection", "comm_source_to_device": "Source to device", "comm_node_connected_to_source_device": "Source to device uplink node", "comm_ip_range": "IP range", "pathtopo_objfilter_keywords": "Fuzzy search by task code, probe IP, probe port, and target name is supported", "comm_suspected_faulty_node": "Suspected fault node", "pathtopo_path_not_faulty": "The path is not fault", "comm_port_utilization": "Port rate", "comm_packet_loss_degradation_threshold": "Packet loss deterioration generation threshold", "comm_delay_degradation_threshold": "Delay deterioration threshold", "comm_interrupt_threshold": "Interrupt threshold", "comm_no_data_import": "No data to import", "pathtopo_object_keywords": "Task code, probe Ip, target Ip, target name", "discover_data_uplink_ip": "IP(Uplink IP)", "discover_data_discovery_time": "Discovery time", "discover_ip_tips": "Example: ***********-200 or 2001:db8::1a2f:0001-1a2b. If the IP address segment is too large, the discovery takes too long.", "auditlog_general": "General", "auditlog_important": "Important", "auditlog_badly": "Serious", "probetask_two_details": "Indicator details (Task code：", "pathtopo_filter_object_no_data": "Please select data, add filter objects, and unfilter", "task_interrupt_generation_threshold": "Interrupt threshold", "task_delay_degradation_generation_threshold": "Delay deterioration threshold", "please_select_suspended_data": "Please select suspended data", "please_select_disabled_data": "Please select disabled data", "please_select_startup_data": "Please select enable data", "task_your_current_remaining": "Your current remaining license", "task_exceeded_records_upload_automatically": ",The exceeded records in the upload attachment will be automatically truncated, no more notifications! !", "report_comm_disable": "Pause ", "report_comm_enable": "Enable ", "task_add_edit_tips_ordinary_dial": "Probe task", "task_add_edit_tips_automatic_dial": "automatic dial ", "task_add_edit_tips_but_add": "add ", "task_add_edit_tips_but_edit": "edit ", "task_pause_tips_sure_to": "<p>Are you sure want to ", "task_pause_tips_sure_to1": "  this data?", "task_testspeed_server_tips_sure_to1": "  this data?", "task_pause_tips_sure_to2": "  this data?", "task_import_list_abnormal_items": "List of abnormal items", "task_export_all_excel_file_name": "All dial test list.xlsx", "task_list_number": "Task code", "task_number": "Task code", "task_list_probe_ip": "Probe IP", "report_interruption_duration": "Interrupt Duration", "report_degradation_duration": "Deterioration Duration", "report_map_score": "Score", "task_analyze_degradation_accumulated_duration": "Deterioration Duration", "qualityreport_relay_number": "Relay Code", "qualityreport_relay_name": "Relay Name", "qualityreport_relay_local_ip": "Local Address", "qualityreport_relay_peer_ip": "Remote Address", "qualityreport_relay_interruption_duration": "Interrupt Duration", "probe_tasks_add_target_ip_tips": " Target ", "probe_tasks_add_target_name_tips": " Target Name ", "msg_eg": "The return result is represented by the result parameter, for example: \n      if [ $result -gt 0 ] \n      then \n            echo '1' \n      else \n            echo ${ result } \n      fi", "probe_tasks_download": "Probe task template", "log_strategy": "System level audit", "qualityreport_dialog_pt_analysis": "PT Analysis", "qualityreport_dialog_link_analysis": "Link Analysis", "qualityreport_dialog_rl_analysis": "RL Analysis", "dashboard_alarm_num_tips1": "The value should be a positive integer and not greater than 20", "dashboard_alarm_num_tips2": "The value is a positive integer not greater than 20", "wifi_user_level_excellent": "Excellent", "wifi_user_level_good": "Good", "wifi_user_level_medium": "Medium", "wifi_user_level_poor": "Poor", "login_lang": "Language", "color_default": "<PERSON><PERSON><PERSON>", "color_motif": "Theme", "color_more": "More", "color_norm": "Standard", "message_select_province": "Up to 10 provinces can be selected", "virt_host_name": "Host", "virt_host_data": "Collect Data", "virt_host_ip": "Manage IP", "virt_host_config": "Host Manage", "virt_host_column_name": "Host name", "virt_host_column_ip": "IP", "virt_host_column_ip2": "Device IP", "virt_host_column_type": "Virtual mode", "virt_host_column_TLLS": "Enable TLLS", "virt_host_column_TLLS2": "Socket", "virt_host_column_vmNumber": "Number of virtual machines", "virt_host_column_lastTime": "Last collection time", "virt_host_column_cycle": "Collection interval", "virt_host_column_post": "Port", "virt_host_column_username": "Username", "virt_host_column_password": "Password", "virt_host_column_ip_error_tip": "Please select IP", "virt_host_column_post_error_tip": "Please enter correct port", "virt_host_column_post_error_tip2": "Only positive integers ranging from 0 to 65535 are allowed", "virt_host_column_username_error_tip": "Please enter your login username", "virt_host_column_username_error_tip_verify": "your login username", "virt_host_column_password_error_tip": "Please enter your login password", "virt_host_password_chinese_error_tip": "Password cannot be in Chinese", "virt_host_password_blank_error_tip": "Password cannot be entered in Spaces", "virt_host_column_host_error_tip": "The host name cannot contain Spaces", "virt_host_column_type_error_tip": "Please select type", "virt_host_column_getherInterval_error_tip": "Please enter the correct collection interval", "virt_host_column_probeId_tip": "When the target node detected by the selected dial probe is the same as the virtual machine IP address collected, information about the virtual switch is displayed on the complete path topology.", "virt_host_config_keyWords_tip": "Host name、IP、Collect", "virt_host_export_file_name": "Host.xlsx", "virt_host_export_all_file_name": "Host all.xlsx", "virt_host_import_template_file_name": "Host data import template.xlsx", "virt_host_export_error_file_name": "Host error data list.xlsx", "virt_host_network_card": "Network card", "virt_host_virtual_switch": "Virtual switch", "virt_host_switch_name": "Virtual switch name", "virt_host_switch_mac": "Switch MAC", "virt_host_switch_ip": "Switch IP", "virt_host_virtual_machine": "Virtual machine", "virt_host_virtual_machine_ip": "Virtual machine IP", "virt_host_network_card_ip": "Network card IP", "virt_host_connection_failure": "Host connection failure", "virt_host_collection_failure": "Host collection failure", "virt_host_collector_offline": "Collector offline", "system_upgrade": "upgrade", "virt_hostd_keyword_matching": "Support according to the Network card、Virtual switch name, Switch MAC, Virtual machine,Virtual machine IP fuzzy matching the query", "firm_forbid_edit": "The built-in device manufacturer does not support modification", "model_forbid_edit": "The built-in device model does not support modification", "type_forbid_edit": "The built-in device type does not support modification", "virtHostConfig_name_limit": "Host name limit  30 characters", "validate_no_chinese": "cannot include Chinese", "virtHostConfig_server_key": "Server Public Key", "virtHostConfig_Client_key": "Client Public Key", "virtHostConfig_private_key": "Client Private Key", "upload_server": "Please upload the server public key", "upload_Client": "Please upload the client's public key", "upload_private": "Please upload the client private key", "factory_code_info": "Please fill in the device manufacturer code", "virthost_upload_file_tips": "File size does not exceed 32k", "virthost_name_verify_tips": "There are spaces in the host name", "virt_host_username_exist_space_tip": "The username does not allow spaces", "virt_host_username_exist_chinese_tip": "The username does not allow Chinese input", "dashboard_select_province_cannot_exceed_10": "Select province/municipality,Cannot exceed 10", "deviceModel_oid_verify_limit_char_tip": "sysObjectID only allows input of 1-30 characters", "no_matching_data": "No matching data", "nav_all_btn": "Skip Navigation", "nav_current_btn": "Got It", "nav_step1_tip": "Hover your mouse over [Probe Task] to evoke the submenu!", "nav_step2_tip": "Click [Probe Tasks] to enter the dial test task management interface to add a dial test task!", "nav_step3_tip": "Click 'Add' to create a new test call task!", "nav_step4_tip": "Fill in the required fields with '*', and use default values for others!", "nav_step5_tip": "Can create a 10 second cycle call test task for easy and fast verification; Regular monitoring frequency does not require high frequency to avoid affecting server performance!", "nav_step6_tip": "Click 'Confirm' to complete the task creation.", "nav_step7_tip": "Wait for 2 min to view the learned path and raw data trend.", "nav_step8_tip": "You can disconnect the network of the computer installed with 'NetDem', artificially create faults, and wait for a while (wait 30 seconds for tasks with 10-second intervals, wait 3 minutes for tasks with 1-minute intervals). You can check the faults and end-to-end positioning in the [Alerts].", "nav_step9_tip": "You can manage and view the global network path topology in [Topology].", "nav_step10_tip": "From the perspective of tasks, analyze which tasks have poor quality, long duration, and high frequency in [PT Analysis].", "nav_step11_tip": "[Link Analysis] Standing from the perspective of the link, analyze which links in the network have long poor quality time and high frequency of poor quality. Analyze the weak links in the network.", "nav_step12_tip": "[PT Records] Establish a file for each Probe Tasks to statistically analyze the health status of the Probe Task during the period.", "nav_step13_tip": "You can recall the guidance under 'Help'!", "nav_step14_tip": "You can switch to advanced Settings, set dial parameters, event parameters, alarm parameters, and so on!", "help_manual": "Help Manual", "help_nav": "Interface Guidance", "legalResPool_device_name": "Device Name", "legalResPool_ip": "IP", "legalResPool_mac": "MAC", "legalResPool_remarks": "Describe", "legalResPool_legitimacy": "Legitimacy", "legalResPool_legal": "Legal", "legalResPool_illegal": "Illegal", "legalResPool_keywords_placeholder": "Device name, IP, MAC, Describe", "legalResPool_sure_button": "Determine", "legalResPool_update_resource_pool": "Update resource", "legalResPool_export_name": "Legal resource pool", "legalResPool_import": "Import", "legalResPool_import_template_name": "Legal resource pool import template", "legalResPool_import_message_exception": "Legal resource pool information exception item", "legalResPool_switch_illegal_access_alarm": "Illegal access alarm", "legalResPool_not_verified": "Not verified", "legalResPool_verified": "Verified", "legalResPool_verify_status": "Status", "legalResPool_alarm_handle_time": "Processing time", "legalResPool_alarm_into_legal_resource_pool": "Incorporate into the legal resource pool", "legalResPool_alarm_please_enter_procedure": "Please enter the processing procedure", "legalResPool_illegal_access_details": "Illegal access details", "legalResPool_illegal_access": "Illegal access alarm", "legalResPool_illegal_device_access": "Illegal access alarm", "legalResPool_select_legitimacy_verification": "Please select legality", "legalResPool_update_resource_pool_prompt": "After updating the resource pool, the system will automatically summarize the collected LLDP, MAC table information, ARP table information, port information, and other MAC that have appeared in the legal resource pool!", "legalResPool_des_leng_400": "Description length cannot exceed 400", "legalResPool_download_template": "Download template:", "legalResPool_upload_attachment": "Upload attachment:", "legalResPool_data_verification_tip": "Data validation：", "legalResPool_data_verification_upload_again_tip": "If no, import the correct item or adjust Excel and upload it again", "dashboard_component_real_alarm_group_limit_tip": "Select up to 10 groups", "pathtopo_box_select": "Box select", "alarmlist_port_aram_title": "Port alarm", "login_drag_code_text": "Drag slider verification", "login_drag_code_success": "Verification passed", "login_drag_code_tip": "Please drag the slider to verify", "port_status_change_alarm": "Port monitoring", "prob_direction": "Direction", "prob_direction_Forward": "Target-->Probe", "prob_direction_Backward": "Probe-->Target", "alarmlist_port_aram_file_name": "Alerts-Port status alarm", "icon_title_pathtopo": "Path Topology", "icon_title_physicaltopo": "Physical Topology", "icon_title_path": "Path icon", "icon_title_background": "Background Image", "icon_add_success": "New successfully added", "icon_edit_success": "Edit successful", "icon_copy_success": "Copy successful", "icon_copy_text": "copy", "icon_upload_name": "1-50 characters", "icon_upload_text": "PNG, JPEG, JPG, BMP, GIF format images smaller than 50MB", "icon_upload_text_icon": "64x64 pixels, less than 1MB png format image", "delete_icon_title": "Friendly reminder:", "delete_icon_content": "Are you sure you want to delete the selected data?", "icon_forbid_text": "Built in icons prohibit modification", "icon_pathtopo_title": "The icon used when entering the path topology diagram", "icon_phystopo_title": "The icon used when entering the physical topology diagram", "icon_path_title": "The icon used when entering the path", "icon_bg_label": "Background image", "icon_zoom_ratio": "Icon zoom ratio", "icon_bg_placeholder": "Please select a background image", "icon_bg_forbit_text": "The built-in background image is not editable", "icon_img_size_forbid": "Please upload images smaller than 50MB in png, jpeg, jpg, bmp, gif format", "icon_form_label": "Icon Name", "validate_50_text": "The length cannot exceed 50 characters", "validate_200_text": "The length cannot exceed 200 characters", "icon_img_size_forbid_icon": "Please upload images smaller than 1MB", "icon_img_size_forbid_format_icon": "Please upload images in png format", "icon_img_size_forbid_format": "Please upload images in .jpeg,.jpg,.png,.bmp,.gif format", "icon_img_size_forbid_size_64": "Please upload a 64 x 64 pixel image", "icon_link_title": "Line color:", "default_background_image": "Default Background Image", "task_not_support": "The folded task does not support viewing details", "testspeedserver_keywords": "Test Speed Server name, IP", "testspeedserver_name": "Test Speed Server name", "testspeedserver_code": "Code", "testspeedserver_ip": "Test Speed Server IP", "testspeedserver_orgName": "Organize", "testspeedserver_enable": "Enable", "testspeedserver_disabled": "Pause", "testspeedserver_set_prompt": "Available time settings", "testspeedserver_set_prompt2": "To avoid affecting normal business, speed measurement is only allowed during available time periods", "testspeedserver": "Test Speed Server", "testspeedserver_disabled_prompt": "Are you sure you want to enable the selected data ？", "testspeedserver_spareTime_prompt": "To avoid affecting business, please perform speed measurement when the network is idle!", "testspeedserver_spareTime_prompt2": "The test result is the network speed of the local computer to the water headquarters speed measurement server!", "please_inter_testspeedserver_ip": "Please enter the IP address of the speed measurement server", "please_inter_testspeedserver_period": "Please select a recurrence period", "please_inter_testspeedserver_timeList": "Please set available time periods", "please_select_forbidden_data": "Please select puse data", "please_select_repeat_cycle": "Please select a recurrence period", "but_add_en_contains_spaces": "New ", "switch_advanced_setup": "Switch to advanced Settings", "switch_primary_setup": "Switch to primary Settings", "performance_index": "performance index", "quality_info": "quality info", "path_info": "path info", "custom_list_items": "Custom List", "btn_search": "Search", "topo_data_abbreviation": "Data abbreviation", "flow_congestion_alarm": "Traffic congestion alarm", "drag_left_title": "Probe displays the number of points to the target", "drag_right_title": "Display the number of points from the target to the probe", "dashboard_probetask_deterioration_tip": "Indicates the number and proportion of the commissioning tasks for which deterioration alarms are generated", "dashboard_probetask_interrupt_tip": "Indicates the number and proportion of the commissioning tasks for which the interruption alarm is generated", "dashboard_probetask_normal_tip": "Number/proportion of the current normal commissioning tasks", "dashboard_probetask_suspend_tip": "Number/proportion of suspended commissioning tasks", "dashboard_repeat_deterioration_tip": "Number/proportion of trunks for which deterioration alarms are generated", "dashboard_repeat_interrupt_tip": "Indicates the number and proportion of trunks for which the interruption alarm is generated", "dashboard_repeat_normal_tip": "Number and ratio of the current normal trunks", "dashboard_repeat_suspend_tip": "Number/proportion of currently suspended trunks", "dashboard_special_deterioration_tip": "Number/proportion of current private lines with degradation alarms", "dashboard_special_interrupt_tip": "Indicates the number/proportion of the current private lines for which interruption alarms are generated", "dashboard_special_normal_tip": "Current number/proportion of normal lines", "dashboard_special_suspend_tip": "Number/proportion of currently suspended lines", "topo_modal_title": "Shrink Details", "device_type_needs_create_task": "The device type that needs to create a task", "discovery_Progress": "Discovery progress", "net_data_discover_change": "Change", "net_data_discover_change_record_details": "Change Details", "net_data_discover_change_time": "Discover Time", "net_data_discover_change_operation": "Operation", "net_data_discover_whether_monitor": "Whether Monitor", "net_data_discover_whether_identify": "Whether Identify", "net_data_discover_whether_change": "Whether Change", "net_data_discover_identify_no": "Unrecognized", "net_data_discover_identify_yes": "Identified", "net_data_discover_change_no": "Unchanged", "net_data_discover_change_yes": "Changed", "net_data_discover_select_data": "Please check the discovery data that did not generate a task", "net_data_discover_export_data": "Discover data", "net_data_discover_discovery_in_progress": "find", "net_data_discover_identify_in_progress": "identify", "net_data_discover_overtime": "overtime", "net_rule_discover_scantime": "Recent scan start time", "net_rule_discover_completiontime": "Recent scan completion time", "discover_data_ip": "IP", "display_objectives": "Display objectives", "task_location": "Location", "please_input_dest_task_location": "Please fill in the location of the target", "icon_bg_label_select": "Select background", "dashboard_select_icon_scale": "Please enter the icon zoom ratio", "dashboard_obj_required": "Please select the target for display", "dashboard_obj_max": "Up to 2000 targets can be selected", "flow_tabs_label_application": "Application", "flow_tabs_label_conversation": "Conversation", "flow_query_direction": "Flow direction", "flow_query_infow": "Inflow", "flow_query_outflow": "Outflow", "rphealthy_total": "Total flow", "flow_table_title_application_name": "Application name", "flow_table_title_traffic_percentage": "Traffic percentage", "flow_btn_tooltip_map": "Mapping", "flow_map_keywords_placeholder": "Application name, port number, IP", "punctuation_comma": ",", "punctuation_bracket_left": "(", "punctuation_bracket_right": ")", "exclamation_point": "!", "maping_data": "Mapping Data", "map_import_template": "Mapping Import Template", "map_import_error": "Mapping error data", "map_application_post_error_tip": "The format of the application port is incorrect. Only numbers can be entered", "map_application_post_error_tip2": "The value of the application port ranges from 1 to 65535", "device_discovery_del_flow_model_tips": "<p>Are you sure you want to delete these data?</p>", "flow_place_holder": "Source, Target, Application", "flow_maping_ip_placeholder": "Support single IP or IP segment, multiple separated by English comma, example: ********,*************-200", "application_flow_top10": "Application Flow Top10", "conversation_flow_top10": "Conversation Flow Top10", "source_flow_top10": "Source Flow Top10", "target_flow_top10": "Target Flow Top10", "flow_number": "flow number", "device_systemUpTime": "Running time", "duplex_status_lable": "Working mode", "pathtopo_tip_alarm_info": "Alarm information", "routine-horizontal": "Routine - Horizontal", "routine-vertical": "Routine - Vertical", "routine-repulsion": "Routine - Repulsion", "fiber-vertical": "Fiber - Vertical", "fiber-horizontal": "Fiber - Horizontal", "edit_pop_component": "Edit Popup Component", "impact_path_number": "Affects the number of paths", "alarm_Alertable": "Alertable", "comm_please_select_alarmStatus_max": "Up to 10 groups can be selected", "dashboard_max_5_items": "Up to 5 topologies can be selected", "line_monitoring_report": "Line monitoring report", "line_alarm_list": "Line alarm list", "line_assessment_checklist": "Line assessment checklist", "add_snmp_alarm": "Add SNMP alarm", "statistical_scope": "Group", "customer_name": "Customer Name", "please_input_customer_name": "Please input customer name", "generate_immediately": "Generate immediately", "generate_immediately_short": "Generate", "special_line_report": "LL Report", "flow_over_limit_list": "Flow Over Limit List", "statistical_dimension_zero": "Lines with traffic exceeding 80% on the 98th", "statistical_dimension_one": "Lines with peak flow below 20%", "statistical_dimension_two": "Circuit list with traffic exceeding 80% for three consecutive months", "statistical_dimension_three": "Circuit list with traffic below 20% for three consecutive months", "statistical_dimension": "Dimension", "please_select_statistical_dimension": "Please select the dimension", "inspection_scope": "Inspection scope"}