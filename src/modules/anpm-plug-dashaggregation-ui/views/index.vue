<template>
  <div
    class="itemEcharts"
    :style="
      'position: relative; height: 100%;background:' +
      (isbigscreen ? '#060D15' : isdarkSkin == 1 ? '1a222e' : '#ffffff')
    "
  >
    <!-- <div style="height: 35px;" v-if='!isbigscreen'>
      <span style="display: inline-block;height: 0;top: 0;left: 0;right: 0;position: absolute;border-radius: 4px;border-style: solid;border-width: 1px;border-left: none;border-right: none;border-top: none" :style="'border-color:'+(borderColor)"></span>
    </div> -->

    <Loading :loading="loading"></Loading>
    <div
      class="lineBox"
      :style="'height:75%'"
      v-if="
        !loading &&
        haveData(chartQuery.data) == true &&
        chartQuery.spec.length == getApiCount
      "
    >
      <!-- <div class="lineBox" v-if="!loading && haveData(chartQuery.data)==true && chartQuery.spec.length == getApiCount"> -->
      <!--<Loading :loading="'true'"></Loading>-->

      <draw-line
        :node="'line' + random"
        :lineData="chartQuery.data"
        :isbigscreen="isbigscreen"
        :isdarkSkin="isdarkSkin"
        :key="'line' + random"
      ></draw-line>
    </div>
    <div
      v-else-if="!loading && haveData(chartQuery.data) == false"
      :class="{ fillempty: isdarkSkin == 1, fillempty2: isdarkSkin == 0 }"
      :style="
        'height:' +
        (Object.keys(chartQuery.data).length * 21 + 21 + 35 <=
        Math.floor((thisbody.height - 1) * 0.25)
          ? '75%'
          : 'calc(100% - 35px)')
      "
    >
      <p
        class="emptyText"
        :style="{ color: isbigscreen ? '#D1E4FF!important' : '#303748' }"
      >
        {{ $t("common_No_data") }}
      </p>
    </div>
    <div
      style="height: 25%; overflow: auto"
      class="specTbaleBox"
      :class="isbigscreen ? 'biscreen' : ''"
      v-if="haveData(chartQuery.data) == true"
    >
      <table class="specTbale" cellpadding="0" cellspacing="0">
        <thead>
          <tr>
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
            ></td>
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
              v-for="(itemHead, index) in indictorHead"
              :key="index"
            >
              {{ itemHead }}
            </td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(value, key, index) in chartQuery.data" :key="key + index">
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
              :title="value.name"
            >
              <span
                class="indictorBanner"
                :style="'background:' + value.color"
              ></span
              ><span
                :style="
                  'max-width:' +
                  (thisbody.width * 0.84 * 0.45 - 38 < 100 ? '100px' : '')
                "
                >{{value.name}}</span
              >
            </td>
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
              :title="
                value.target.val == 0
                  ? 0 + ' ' + value.unit
                  : value.target.val
                  ? value.unit == 'bps'
                    ? flowSize(
                        flowDataHandle(value.target.val, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.val, key) +
                      getUnit(value.target.val, value.unit)
                  : '--'
              "
            >
              {{
                value.target.val == 0
                  ? 0 + " " + value.unit
                  : value.target.val
                  ? value.unit == "bps"
                    ? flowSize(
                        flowDataHandle(value.target.val, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.val, key) +
                      getUnit(value.target.val, value.unit)
                  : "--"
              }}
            </td>
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
              :title="
                value.target.valMin == 0
                  ? 0 + ' ' + value.unit
                  : value.target.valMin
                  ? value.unit == 'bps'
                    ? flowSize(
                        flowDataHandle(value.target.valMin, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.valMin, key) +
                      getUnit(value.target.valMin, value.unit)
                  : '--'
              "
            >
              {{
                value.target.valMin == 0
                  ? 0 + " " + value.unit
                  : value.target.valMin
                  ? value.unit == "bps"
                    ? flowSize(
                        flowDataHandle(value.target.valMin, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.valMin, key) +
                      getUnit(value.target.valMin, value.unit)
                  : "--"
              }}
            </td>
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
              :title="
                value.target.valMax == 0
                  ? 0 + ' ' + value.unit
                  : value.target.valMax
                  ? value.unit == 'bps'
                    ? flowSize(
                        flowDataHandle(value.target.valMax, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.valMax, key) +
                      getUnit(value.target.valMax, value.unit)
                  : '--'
              "
            >
              {{
                value.target.valMax == 0
                  ? 0 + " " + value.unit
                  : value.target.valMax
                  ? value.unit == "bps"
                    ? flowSize(
                        flowDataHandle(value.target.valMax, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.valMax, key) +
                      getUnit(value.target.valMax, value.unit)
                  : "--"
              }}
            </td>
            <td
              :style="[
                { background: isbigscreen ? 'none!important' : '' },
                { color: isbigscreen ? '#D1E4FF!important' : '#303748' },
              ]"
              :title="
                value.target.valAvg == 0
                  ? 0 + ' ' + value.unit
                  : value.target.valAvg
                  ? value.unit == 'bps'
                    ? flowSize(
                        flowDataHandle(value.target.valAvg, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.valAvg, key) +
                      getUnit(value.target.valAvg, value.unit)
                  : '--'
              "
            >
              {{
                value.target.valAvg == 0
                  ? 0 + " " + value.unit
                  : value.target.valAvg
                  ? value.unit == "bps"
                    ? flowSize(
                        flowDataHandle(value.target.valAvg, key),
                        true,
                        true
                      )
                    : flowDataHandle(value.target.valAvg, key) +
                      getUnit(value.target.valAvg, value.unit)
                  : "--"
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
  import drawLine from "@/common/echarts/dashaggregationLine.vue";
  import '@/timechange';
  import echartFn from "@/common/mixins/echartFun";
  export default {
    name: "dashaggregation",
    mixins: [echartFn],
    components: {
      drawLine
    },
    data() {
      return {
        isdarkSkin: sessionStorage.getItem('dark') || 1,
        isShow:true,
        //当前body高度
        thisbody:{
          height:document.body.clientHeight,
          width:document.body.clientWidth,
        },
        // indictorHead:["最新","最小","最大","平均"],
           indictorHead:[this.$t("common_latest_value"),this.$t("common_min"),this.$t("common_max"),this.$t("common_avg")],
        //最高故障颜色
        borderColor:'transparent',
        //指标颜色
        faultColor:{
          '1':'#fd341f',
          '2':'#ffa212',
          '3':'#ffa212',
          '9':'#ED921C',
          '10':'transparent',
        },
        random:parseInt(Math.random())+'dash_'+new Date().valueOf(),
        //查询时间
        querytime:null,
        queryTimeType:null,
        //刷新时间间隔
        intervalTime:null,
        //刷新参数
        interrefresh:null,
        nowDate:new Date(),
        //时间参数
        startTime:null,
        endTime:null,
        pieLoading: false,
        height: 260,
        loading:true,
        param: {
          startTime:new Date().format2('yyyy-MM-dd'),
          endTime:new Date().format2('yyyy-MM-dd'),
        },
        line_List: [],
        typeCode:"aggre_gation", //此参数由调用的时候传进来，传进来的是构件的code例（route_alarm_trend，special_alarm_trend，snmp_alarm_trend,aggre_gation）
        isbigscreen:false,
        //图形类型（1.折线图 2.柱状图）
        graphicType:null,
        //图形数据
        graphicList:[],
        //匹配指标类型
    //         "dashboard_delay": "时延",
    // "dashboard_lost_packet": "丢包",
    // "dashboard_availability": "可用率",
    // "dashboard_excellent_rate": "优良率",
    // "dashboard_upstream": "上行流速",
    // "dashboard_down": "下行流速",
    // "dashboard_uplink": "上行带宽利用率",
    // "dashboard_downstream": "下行带宽利用率",
    // "dashboard_inlet_velocity": "入流速",
    // "dashboard_outflow_velocity": "出流速",
        objectType:{
          "1-1":["specdelay"," "+this.$t('dashboard_delay')+"：","ms"],
          "1-2":["specloss"," "+this.$t('dashboard_lost_packet')+"："," %"],
          "1-3":["specuseRate"," "+this.$t('dashboard_availability')+"："," %"],
          "1-4":["specgoodRate"," "+this.$t('dashboard_excellent_rate')+"："," %"],
          "1-5":["specentryflow"," "+this.$t('dashboard_upstream')+"：","bps"],
          "1-6":["specoutflow"," "+this.$t('dashboard_down')+"：","bps"],
          "1-7":["specentryRate"," "+this.$t('dashboard_uplink')+"："," %"],
          "1-8":["specoutRate"," "+this.$t('dashboard_downstream')+"："," %"],
          "2-1":["rpdelay"," "+this.$t('dashboard_delay')+"："," ms"],
          "2-2":["rploss"," "+this.$t('dashboard_lost_packet')+"："," %"],
          "2-3":["rpuseRate"," "+this.$t('dashboard_availability')+"："," %"],
          "2-4":["rpgoodRate"," "+this.$t('dashboard_excellent_rate')+"："," %"],
          "2-5":["rpentryflow"," "+this.$t('dashboard_inlet_velocity')+"：","bps"],
          "2-6":["rpoutflow"," "+this.$t('dashboard_outflow_velocity')+"：","bps"],
          "3-1":["pathdelay"," "+this.$t('dashboard_delay')+"："," ms"],
          "3-2":["pathloss"," "+this.$t('dashboard_lost_packet')+"："," %"],
          "3-3":["pathuseRate"," "+this.$t('dashboard_availability')+"："," %"],
          "3-4":["pathgoodRate"," "+this.$t('dashboard_excellent_rate')+"："," %"],
          "3-5":["pathentryflow"," "+this.$t('dashboard_inlet_velocity')+"：","bps"],
          "3-6":["pathoutflow"," "+this.$t('dashboard_outflow_velocity')+"：","bps"],
          "3-7":["specentryRate"," "+this.$t('dashboard_inflow_rate')+"："," %"],
          "3-8":["specoutRate"," "+this.$t('dashboard_outflow_rate')+"："," %"],
          "4-5":["specentryflow"," "+this.$t('dashboard_inlet_velocity')+"：","bps"],
          "4-6":["specoutflow"," "+this.$t('dashboard_outflow_velocity')+"：","bps"],
          "4-7":["specentryRate"," "+this.$t('dashboard_inflow_rate')+"："," %"],
          "4-8":["specoutRate"," "+this.$t('dashboard_outflow_rate')+"："," %"],
        },
        //趋势图参数
        chartQuery:{
          spec:[],
          objectType:[],
          color:[],
          data:{}
        },
        //获取当前已经请求完毕多少个接口数据
        getApiCount:0,
        //批量多天指标请求合并
        paramArr:[],
      };
    },
    created() {
      // this.isdarkSkin = top.window.isdarkSkin;
      const iframeDom = window.frameElement,params = iframeDom.name;
     
      const src = window.frames.frameElement.getAttribute('src'),name=window.frames.frameElement.getAttribute('name');
      if (parent.window.isEdit) {
        this.intervalTime = params.split('&&topoId=')[0].split('?intervalTime=')[1];
        this.graphicType = params.split('&&graphicType=')[1].split('&&graphicList=')[0];
        this.graphicList = JSON.parse(params.split('&&graphicList=')[1].split('&&')[0]);
      }else{
        this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
        this.graphicType = window.frames.name.split('&&graphicType=')[1].split('&&graphicList=')[0];
        this.graphicList = JSON.parse(window.frames.name.split('&&graphicList=')[1].split('&&')[0]);
        if (window.frames.frameElement) {
          window.frames.frameElement.contentWindow.close();
        }
      }
      let timeArr = JSON.parse(unescape(src.split('param=')[1])).queryTime;
      let queryTimeType = JSON.parse(unescape(src.split('param=')[1])).queryTimeType;
      this.querytime = timeArr;
      this.queryTimeType = queryTimeType;
      this.chartQuery.spec = this.graphicList.map(({objectType,objectId,indicatorType,portId,aip,zip,name})=>{
        return {objectType,objectId,indicatorType,portId,aip,zip,name}
      })
      this.graphicList.forEach(({objectType,indicatorType,color}) => {
        this.chartQuery.objectType.push(this.objectType[objectType+'-'+indicatorType]);
        this.chartQuery.color.push(color);
      });
      this.paramArr = [];
      for (let index = 0; index < this.chartQuery.spec.length; index++) {
        const elementParamObj = this.chartQuery.spec[index];
        this.paramArr.push(
          {
            name:elementParamObj.name,
            objectType:elementParamObj.objectType,objectId:elementParamObj.objectId,indicatorType:elementParamObj.indicatorType,portId:elementParamObj.portId,
            aip:elementParamObj.aip,zip:elementParamObj.zip,typeArr:this.chartQuery.objectType[index],color:this.chartQuery.color[index]
          }
        )
      }
      this.getLines(this.paramArr);
    },
    mounted() {
      // 监听 storage 事件
      window.addEventListener('storage', this.handleStorageChange);
      this.isbigscreen = window.isbigscreen;
      if (this.intervalTime && Number(this.intervalTime)) {
        this.interrefresh = setInterval(()=>{this.loading = true;this.timeQuery(this.queryTimeType||4,this.intervalTime);this.getLines(this.paramArr)},this.intervalTime * 1000);
      }
    },
    methods: {
          handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        getUnit(newVal, unit){
            return  newVal!=null && newVal!=undefined  && newVal>=0  ? unit : ''
        },
        //流量数据处理
        flowDataHandle (val,key) {
            let newVal = val;
            if(key == null || key == undefined){
              key = "";
            }
            if (key.search("specentryflow")!=-1 || key.search("specoutflow")!=-1 || key.search("rpentryflow")!=-1 || key.search("rpoutflow")!=-1 || key.search("pathentryflow")!=-1 || key.search("pathoutflow")!=-1) {
              newVal = val;
            }

            return  newVal!=null && newVal!=undefined  && newVal>=0  ? Number.parseFloat(newVal).toFixed(2) : '--'
            // return newVal>=0 ? Math.ceil(newVal*100)/100 : '--'
      },
      //获取查询时间
      timeQuery(timeType,interval){
        let time = [],nowTime = new Date().getTime();
        switch (Number(timeType)){
          case 0:
            this.querytime[1] = new Date(new Date(this.querytime[1]).getTime()+interval * 1000).format2('yyyy-MM-dd HH:mm:ss');
            break
          case 1 :case 4:case 8:case 12:case 24:
            this.querytime = [new Date(new Date(this.querytime[0]).getTime()+interval * 1000).format2('yyyy-MM-dd HH:mm:ss'),new Date(new Date(this.querytime[1]).getTime()+interval * 1000).format2('yyyy-MM-dd HH:mm:ss')];
            break
          default:
            this.querytime = [new Date(new Date(this.querytime[0]).getTime()+interval * 1000).format2('yyyy-MM-dd HH:mm:ss'),new Date(new Date(this.querytime[1]).getTime()+interval * 1000).format2('yyyy-MM-dd HH:mm:ss')];
        }
        return time
      },
      // 获取折线图数据(单指标请求方式)
      getLine(param,aip,zip,typeArr,color) {
        let _self = this;
        let params = {objectType:param.objectType,objectId:param.objectId,indicatorType:param.indicatorType};
        _self.$http.wisdomPost("/dashboard/target/getData", params).then(res => {
          let datas = res.data;
          
          if (res.code === 1) {
            let valarr = [],timearr = [],dataarr = datas.trend || [];
            for (let index = 0; index < dataarr.length; index++) {
              const element = dataarr[index];
              valarr.push(element.val);
              timearr.push(element.dateTime);
            }
            this.chartQuery.data[typeArr[0]] = {target:(datas.target || {val:null,valAvg:null,valMax:null,valMin:null}),dataArr:valarr,time:timearr || []};
          }else{
            this.chartQuery.data[typeArr[0]] = {target:{val:null,valAvg:null,valMax:null,valMin:null}}
          }
        }).catch((error)=>{
          this.loading = false;
          this.chartQuery.data[typeArr[0]] = {target:{val:null,valAvg:null,valMax:null,valMin:null}};
          if (parent.loading){
            parent.loading['dashtaskalarm'] = false;
          }
        }).finally(()=>{
          this.chartQuery.data[typeArr[0]].name = param.name +typeArr[1];
          this.chartQuery.data[typeArr[0]].aip = aip;
          this.chartQuery.data[typeArr[0]].zip = zip;
          this.chartQuery.data[typeArr[0]].color = color;
          this.loading = false;
          this.getApiCount = Object.keys(this.chartQuery.data).length;
          if (parent.loading){
            parent.loading['dashtaskalarm'] = false;
          }
        });
      },
      // 获取折线图数据(多指标请求方式)
      getLines(paramArr,aip,zip,typeArr,color) {
        console.log('paramArr===>',paramArr);
        const apiParamArr = paramArr.map(({objectType,objectId,indicatorType,portId})=>{
          return {objectType,objectId,indicatorType,portId,currentStartTime:this.querytime?this.querytime[0]:null,currentEndTime:this.querytime?this.querytime[1]:null}
        })
        let httpRequest = this.$http.wisdomPost("/dashboard/target/getDatas", {jsonStr:JSON.stringify(apiParamArr)})
        httpRequest.then(res => {
          let datas = res.data || [];
          
          if (res.code === 1) {
            let minFaultType = 10;
            //遍历返回的数据数组
            datas.forEach(({info,target,trend})=>{
              let color ="red",aip="",zip="",typeArr=[],valarr= [],timearr = [];
              if (info.indicatorType == 1||info.indicatorType == 5 ||info.indicatorType == 6 ||info.indicatorType == 7 ||info.indicatorType == 8){
                target.fontColor = '';

                if (info.faultType==2 || info.faultType==9){
                  target.fontColor = this.faultColor[info.faultType];
                  minFaultType = Math.min(minFaultType,info.faultType)
                }
              }else if (info.indicatorType == 2||info.indicatorType == 5 ||info.indicatorType == 6 ||info.indicatorType == 7 ||info.indicatorType == 8){
                target.fontColor = ''
                if (info.faultType==3  || info.faultType==9){
                  target.fontColor = this.faultColor[info.faultType];
                  minFaultType = Math.min(minFaultType,info.faultType)
                }else if (info.faultType==1  || info.faultType==9){
                  target.fontColor = this.faultColor[info.faultType];
                  minFaultType = Math.min(minFaultType,info.faultType)
                }
              }else{
                target.fontColor = ''
              }
              for (let index = 0; index < paramArr.length; index++) {
                const element = paramArr[index];
                if (info.objectId == element.objectId &&info.objectType == element.objectType &&info.indicatorType == element.indicatorType ) {
                  color = element.color;
                  aip = element.aip;
                  zip = element.zip;
                  typeArr = element.typeArr;
                  break
                }
              }
              for (let index = 0; index < trend.length; index++) {
                const element = trend[index];
                valarr.push(element.val);
                timearr.push(element.dateTime);
              }
              let objAttr = `${typeArr[0]}-${info.objectType}-${info.indicatorType}-${info.objectId}`;
              this.chartQuery.data[objAttr] = {target:(target || {val:null,valAvg:null,valMax:null,valMin:null,fontColor:'#2c3e50'}),dataArr:valarr,time:timearr || []};
            })
            this.borderColor = this.faultColor[minFaultType];
          }else{
            paramArr.forEach(({objectType,indicatorType,objectId,typeArr})=>{
              let objAttr = `${typeArr[0]}-${objectType}-${indicatorType}-${objectId}`;
              this.chartQuery.data[objAttr] = {target:{val:null,valAvg:null,valMax:null,valMin:null,fontColor:'#2c3e50'}}
            })
          }
        }).catch((error)=>{
          this.loading = false;
          paramArr.forEach(({objectType,indicatorType,objectId,typeArr})=>{
            let objAttr = `${typeArr[0]}-${objectType}-${indicatorType}-${objectId}`;
            this.chartQuery.data[objAttr] = {target:{val:null,valAvg:null,valMax:null,valMin:null,fontColor:'#2c3e50'}}
          })
          if (parent.loading){
            parent.loading['dashtaskalarm'] = false;
          }
        }).finally(()=>{
          paramArr.forEach(({objectType,indicatorType,objectId,typeArr,name,aip,zip,color})=>{
            let objAttr = `${typeArr[0]}-${objectType}-${indicatorType}-${objectId}`;
            this.chartQuery.data[objAttr].name = name +typeArr[1];
            this.chartQuery.data[objAttr].unit = typeArr[2];
            this.chartQuery.data[objAttr].aip = aip;
            this.chartQuery.data[objAttr].zip = zip;
            this.chartQuery.data[objAttr].color = color;
          })
          this.loading = false;
          this.getApiCount = Object.keys(this.chartQuery.data).length;
          if (parent.loading){
            parent.loading['dashtaskalarm'] = false;
          }
        });
         httpRequest = null
      },

      //判断是否有数据
      haveData(obj){
        let hasData = true,hasDataArr = [];
        let objDom = obj;
        let arr = [];
        let flag = false;

        if(JSON.stringify(objDom) == "{}"){
          flag = false;
        }
        Object.keys(objDom).forEach(itemObj=>{
          arr.push(objDom[itemObj].target.val,objDom[itemObj].target.valAvg,objDom[itemObj].target.valMax,objDom[itemObj].target.valMin);
          if (Object.keys(itemObj).length<6) {
            hasDataArr.push(false)
          }else{
            hasDataArr.push(true)
          }
        })
        arr.forEach(itme=>{
          if(itme != null && itme != 'undefined' && itme !== ''){
            flag = true;
          }
        })
        let falseNum = 0;
        for (let index = 0; index < hasDataArr.length; index++) {
          const element = hasDataArr[index];
          if (element==false) {
            falseNum+=1
          }
        }
        if (falseNum === hasDataArr.length || !flag) {
          hasData = false
        }
        return hasData
      },
    },
    beforeDestroy(){
      // 移除事件监听
      window.removeEventListener('storage', this.handleStorageChange);
      if (this.interrefresh) {
        clearInterval(this.interrefresh);
        this.interrefresh = null;
      }
    }
  };
</script>
<style scoped>
.biscreen.specTbaleBox {
  color: #1a80c0;
  background-color: #060d15;
}
.specTbaleBox {
  /* width:84%; */
  margin: 0 auto;
  align-items: center;
}
.biscreen.specTbaleBox {
  align-items: center;
  color: #1a80c0;
}
.specTbale {
  border: none;
  width: 100%;
}
.specTbale thead td {
  white-space: nowrap;
}
.specTbale td {
  border: none;
  height: 16px;
  line-height: 16px;
  font-size: 14px;
  text-align: center;
  /*padding: 0 16px;*/
  vertical-align: middle;
  width: 11.5%;
  display: inline-block;
  cursor: default;
}
.specTbale td .indictorBanner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 3px;
  vertical-align: middle;
  margin-right: 10px;
}
.specTbale td .indictorBanner + span {
  display: inline-block;
  width: calc(100% - 38px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 22px;
  vertical-align: middle;
}
.specTbale tr > td:first-child {
  text-align: left;
  width: 43%;
  /*overflow: hidden;*/
  /*text-overflow: ellipsis;*/
  /*white-space: nowrap;*/
}
.specTbale tbody > tr > td:not(:first-child) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pieSearch {
  width: 42%;
  text-align: center;
}
.pieSearch .pieTime {
  display: inline-block;
  width: 100%;
}
/deep/.pieSearch .pieTime .ivu-input:hover {
  border-color: #dddddd;
}
.pieSearch .pieTime > label {
  font-weight: bold;
}
.tableBox {
  margin: 20px 20px 40px 20px;
}

.dialTest-tab-title {
  padding-top: 20px;
  padding-bottom: 20px;
}

.homeBox {
  padding-top: 10px;
  height: 100%;
}

.itemEcharts {
  /* display: flex;
    align-items: center; */
}

.pieBox {
  /*flex: 1;*/
  width: 42%;
}

.cake,
.brokenLine {
  height: 280px;
}

.lineBox {
  width: 100%;
  /* height: calc(100% - 35px); */
  height: 100%;
}

html,
body {
  position: relative;
  height: 100%;
}

body {
  background: #eee;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #000;
  margin: 0;
  padding: 0;
}
.ivu-spin-fix {
  background-color: transparent;
}
</style>
<style scoped lang="less">
.compnentHead {
  width: 100%;
  vertical-align: middle;
  padding: 12px 20px;
  position: relative;
  height: 35px;
  border-bottom: 1px solid #dddddd;
  .title {
    color: black;
    text-align: left;
    line-height: 1;
  }
  .tools {
    position: absolute;
    right: 20px;
    top: 9px;
    .toolsEdit,
    .toolsDelete {
      margin-left: 10px;
      color: #2d8cf0;
      font-size: 20px;
      cursor: pointer;
      line-height: 1;
    }
  }
}
</style>
