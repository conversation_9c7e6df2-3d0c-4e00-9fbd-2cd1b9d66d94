const state = {
  devModelList: [],
  ClientList: [],
  delayLossHistory: {
    dayData: {
      delay: [],
      loss: []
    },
    HoursData: {
      delay: [],
      loss: []
    },
    minuteData: {
      delay: [],
      loss: []
    },
    sencondData: {
      delay: [],
      loss: []
    }
  },
  flowHistory: {
    dayData: {
      enter: [],
      issue: []
    },
    HoursData: {
      enter: [],
      issue: []
    },
    minuteData: {
      enter: [],
      issue: []
    },
    sencondData: {
      enter: [],
      issue: []
    }
  },
  goodRateHistory: {
    dayData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    },
    HoursData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    },
    minuteData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    },
    sencondData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    }
  },
  delayTlevel: "",
  flowTlevel: "",
  goodRateTlevel: "",
  flowUnit: {
    dayUnit: "",
    hoursUnit: "",
    minuteUnit: ""
  },
  snmpDetails: {
    day: {},
    hours: {},
    minute: {},
    sencond: {}
  }
};
export default state;
