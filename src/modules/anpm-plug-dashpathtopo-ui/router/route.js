const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/dashpathtopo',
  },
  {
    path: "/dashpathtopo",
    name: lan.getLabel("src.pathTopology"),
    meta: {
      authority: true
    },
    component: resolve =>
      // require(["@/modules/anpm-plug-dashpathtopo-ui/views/topolist.vue"], resolve)
      require(["@/modules/anpm-plug-dashpathtopo-ui/views/index.vue"], resolve)
  },
];
