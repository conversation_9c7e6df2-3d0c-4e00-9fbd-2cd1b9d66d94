<template>
  <!-- 一个拓扑图 -->
  <div class="path-topo-box">
    <Loading :loading="topoLoading"></Loading>

    <PathTopo
      ref="PathTopo"
      :bodyWidth="boxWidth"
      :backgroundImage="backgroundImage"
      :configData="configData"
      v-if="havedata && pathtopoShow"
      :pathTopoData="dataList"
      :showType="showType"
      :showWarning="true"
      :topoId="topoTitleId"
      :layoutModel="layoutModel"
      :tabsId="topoTitleId"
      @getLinkTable="getLinkTable"
      @setPreIp="setPreIp"
      :pathTopoCoorType="2"
      :showPeerToPeerLinkDetail="showPeerToPeerLinkDetail"
      @updateCompleteNodesShow="updateCompleteNodesShow"
      :containerWidth="boxWidth"
      :containerHeight="boxHeight"
      :forbidUpdate.sync="forbidUpdate"
      :isMouseOver="isMouseOver"
      :isEditTopo="isEditTopo"
    >
    </PathTopo>
    <div
      class="bg-box"
      v-else-if="havedata && !pathtopoShow"
      :style="{
        ' width': '100%',
        height: '100%',
        backgroundImage: `url(${echartsBg})`,
      }"
    >
      <!-- 纤维图使用jtopo组件 -->
      <FiberTopo
        ref="fiberTopo"
        :fiberData="nodeData"
        :fiberTopoHeight="boxHeight"
        :fiberTopoWidth="boxWidth"
        :fibreType="fibreType"
        :topoId="topoTitleId"
        :configData="configData"
        @nodeClick="nodesClick"
      ></FiberTopo>

      <!-- <div v-else-if="havedata && !pathtopoShow"  id="fiberChart" :showWarning='true' style="width:100%;height:100%"> -->
    </div>
    <div v-else class="fillempty-box" :style="{ height: boxHeight }">
      <div :class="[currentSkin == 0 ? 'fillempty-light' : 'fillempty']"></div>
      <span
        :class="[currentSkin == 0 ? 'fillemptyTitle-light' : 'fillemptyTitle']"
        >{{ $t("common_No_data") }}</span
      >
    </div>
  </div>
</template>

<script>
export default {
    props: {
        isEditTopo:{
        type:Boolean,
        default:function () {
            return false
        }
        },
         paramsData:{
        type:Object,
        default:function () {
            return {}
        }
      
        },
        boxWidth:{
        type:Number,
        default:function () {
            return 0
        }
        },
        boxHeight:{
        type:Number,
        default:function () {
            return 0
        }
        },
        isTopoTab:{
        type:Boolean,
        default:function () {
            return false
        }
        },
        isMouseOver:{
        type:Boolean,
        default:function () {
            return false
        }
        },
        isFirst:{
        type:Boolean,
        default:function () {
            return false
        }
        },
     
    },
    data() {
        return {
           currentSkin:sessionStorage.getItem('dark') || 1,
            topoId:null,
            
            layoutModel:0,
            topoTitleId:null,
            forbidUpdate:false,
            pathtopoType:null,
            intervalTime:null,
            timeId:null,
            alarmAutoTimerInterval:null,
            topologyMd5:"",
             havedata: true,
            pathtopoShow:false,
            topoLoading:false,
           
            configData:null,
            dataList:null,
            showType:null,
           
            backgroundImage:null,
            configShowFieldResult:"",
            msgModal: [
                this.$t('comm_topo_device_ip'),
                this.$t('comm_topo_device_name'),
                this.$t('comm_topo_device_flow'),
                this.$t('comm_topo_device_delay'),
                this.$t('comm_topo_device_loss')
                ],
             msgModalData: [
            {
                id: 1,
                name: this.$t('comm_topo_device_ip'),
                filedName: 'value',
                className:"width90",
                type: 0,
                util: '',
                isShow: true
            },
            {
                id: 2,
                name: this.$t('comm_topo_device_name'),
                filedName: 'aliases',
                className:"width110",
                type: 0,
                util: '',
                isShow: true
            },
            {
              // 流速
                id: 3,
                name: this.$t('comm_topo_device_flow'),
                filedName: 'flowIntoSpeed',
                className:"width90",
                type: 0,
                util: '',
                isShow: true
            },
              
            {
              // 时延
                id: 4,
                name: this.$t('comm_topo_device_delay'),
                filedName: 'delay',
                className:"width110",
                type: 0,
                util: '',
                isShow: true
            },
              {
              // 丢包
                id: 5,
                name: this.$t('comm_topo_device_loss'),
                filedName: 'lossRate',
                type: 0,
                util: '',
                isShow: true
            },
      ],
            }
    },
    components: {
        PathTopo: () => import('./PathTopo.vue'),
      FiberTopo:() => import('./FiberTopoG6.vue'),
    },
    watch: {
       paramsData:{
            handler(val){
              
                // Clear existing interval before setting up new one
                this.clearIntervalTimer();
             
                this.topoId = val.topoId
                this.pathtopoType = val.pathtopoType
                this.intervalTime = val.intervalTime
           
                this.getPathTopoPulldown();
                
                // Set up new interval if intervalTime exists
                if (this.intervalTime && this.intervalTime > 0 && !this.isTopoTab && !this.isEditTopo) {
                this.setupIntervalTimer();
            }
            },
            deep:true,
            immediate:true
        },
            // 监听数据
            configShowFieldResult:{
            handler(val) {
                if (!val) return;
        
                var showTypeId = "";
                if(val === 'all'){
                    showTypeId = "all";
                }else if(val === '0'){
                    showTypeId = '';
                }else{
                    var showTypeIdArray = val.split(",");
                    var showTypeNameArray = [];
                    this.msgModalData.forEach((item)=>{
                    if(showTypeIdArray.indexOf(item.id + "") > -1){
                        showTypeNameArray.push(item.name);
                    }
                    });
                    showTypeId = showTypeNameArray;
                }
                
                this.showType = showTypeId;
                // this.$refs.PathTopo.showType = this.showType;
            },
            deep: true
            }
  },
    activated() {
    // 组件被激活时调用（切换到该组件时）
    console.log('组件被激活***************************************8888888');
  },

  deactivated() {
    // 组件被停用时调用（切换离开该组件时）
    console.log('组件被停用**********8888888888888888888');
    this.clearData();
  },
  methods: {
     clearIntervalTimer() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }

            if (this.alarmAutoTimerInterval) {
                clearInterval(this.alarmAutoTimerInterval);
                this.alarmAutoTimerInterval = null;
            }

        },
        
        setupIntervalTimer() {
            this.clearIntervalTimer(); // Clear any existing timer first
        if (!this.intervalTime || this.intervalTime <= 0) return; // Don't set up timer if invalid interval
        
        this.timeId = setInterval(() => {
            // Only set up new interval if component is still mounted
            if (!this._isDestroyed) {
                this.getPathTopoPulldown();
            } else {
                this.clearIntervalTimer();
            }
        }, this.intervalTime * 1000);


        let that = this;
        // 10 秒定时查看任务告警是否恢复，恢复了就通知前端更新数据
        // 应急部专用代码。不能删除
        // this.alarmAutoTimerInterval = setInterval(() => {
             
        //       let obj = {
        //         md:this.topologyMd5,
        //         topologyId: this.topoId
        //       };
        //       this.$http
        //     .wisdomPost('/pathTopo/autoRefresh', obj)
        //     .then(res => {
        //           if(res.code === 2){
        //               that.topologyMd5 = res.data;
        //               console.log("需要前端自动加载数据", res);
        //                if (!that._isDestroyed) {
        //                     that.getPathTopoPulldown();
        //                 } else {
        //                     that.clearIntervalTimer();
        //                 }
        //           }else{
        //             console.log("不自动加载数据", res);
        //             that.topologyMd5 = res.data;
        //           }
        //     });
        // }, 1000 * 10);




        },
    updateCompleteNodesShow(data) {
        this.$emit('updateCompleteNodesShow',data)
        // this.completeNodesShow = true
        // this.completeNodeData = data


      },
     nodesClick(param){
      this.$emit('nodesClick',param)
     
     },
    getLinkTable(arr,obj){
        this.$emit('getLinkTable',arr,obj)
    },
    setPreIp(ip){
       
        this.$emit('setPreIp',ip)
    },
     async findInUseIconManageConfigureVoByType() {
      // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标
      try {
        this.configData = null
       const res = await this.$http.post('/iconmanage/findInUseIconManageConfigureVoByType',{type:0,currentSkin:this.currentSkin })
       if(res.code === 1) {
        this.configData = res.data

        // debugger





       }
      }catch(err) {}

      },

    getPathTopoPulldown() {
     
      
     
      let index = null
      let defId = this.topoId;
      

      
      // 如果类型是1 是拓扑图
      // console.log('partBeforeAmpersands=>',partBeforeAmpersands);
      if (this.pathtopoType == '2' || this.pathtopoType == '5') {
        // 如果是2  是纤维图
        this.getFiberChartlogyResult(defId);
        this.pathtopoShow = false;
      } else {
        if(!this.forbidUpdate) {
          this.getPathTopologyResult(defId);
        this.pathtopoShow = true;

        }

      }
    },
    clearData(){
      this.dataList = null;
      this.nodeData = null;
      // this.configData = null;
      this.backgroundImage = null;
      
      
    },
    
     /**
   * 获取纤维图
   * @param id 传入拓扑图id参数
   */
   async getFiberChartlogyResult(id) {
  try {
    this.topoLoading = true;
     if (this.pathtopoType == '2') {
        this.fibreType = 0; // 横向
      } else if (this.pathtopoType == '5') {
        this.fibreType = 1; // 竖向
      }
    
    // Run these requests in parallel since they're independent
    const [configResult, topoResult] = await Promise.all([
      this.findInUseIconManageConfigureVoByType(),
      this.$http.wisdomPost("/pathTopo/getPathFibreTopoResult", { topologyId: id,pathtopoType: this.paramsData.pathtopoType })
    ]);

    if (topoResult.code !== 1) {
      this.nodeData = {};
      return;
    }

    const { backgroundImage, children = [] } = topoResult.data;
    this.echartsBg = backgroundImage ? `data:image/png;base64,${backgroundImage}` : backgroundImage;
    
    if (children.length > 0) {
      this.nodeData = topoResult.data;
      this.havedata = true;
      // fibreType 0 横向   1竖向
      // pathtopoType 1：拓扑图-横向 2：纤维图-横向，3:拓扑图-竖向，4:拓扑图斥力，5:纤维图-竖向
 
     

      // this.fibreType = topoResult.data.directionVerticalTransverse;
    } else {
      this.havedata = false;
      this.nodeData = {};
    }

  } catch (error) {
    console.error('Failed to fetch fiber chart data:', error);
    this.nodeData = {};
  } finally {
    this.topoLoading = false;
    if (parent?.loading) {
      parent.loading['dashpathtopo'] = false;
    }
  }
},
    /**
     * 获取拓扑图数据
     * @param id 传入拓扑图id参数
     */
   getPathTopologyResult(id) {
  
    
  this.showPeerToPeerLinkDetail = false;
  if(!this.isFirst){
   this.topoLoading = true;
  }
  
  console.log(this.pathtopoType)
  // debugger
  if (this.pathtopoType == '1') {
          this.layoutModel = 0; // 横向
        } else if (this.pathtopoType == '3') {
          this.layoutModel = 1; // 纵向
        } else if (this.pathtopoType == '4') {
          this.layoutModel = 2; // 星形
        } 
  
  // Chain promises for better flow control
  return this.findInUseIconManageConfigureVoByType()
  
    .then(() => {
      return this.$http.wisdomPost("/pathTopo/getPathTopologyResult", {
        topologyId: id,
        width: this.boxWidth,
        height: this.boxHeight,
        pathTopoCoorType: 2,
        pathtopoType: this.pathtopoType
      });
    })
    .then(res => {
      if (res.code !== 1) {
        throw new Error(res.msg);
      }

      if (!res.data) {
        this.$Message.warning({ content: res.msg, background: true });
        return;
      }
      if(this.isFirst){
        this.$emit('changeIsFirst',false)
      }

      // Destructure for cleaner code
      const { 
        configShowField,
        backgroundImage,
        pathTopologyLocXLocYMinMax,
        pathTopologyCoordinate,
       
        nodeList = []
      } = res.data;


      this.configShowFieldResult = configShowField;
      this.dataList = res.data;
      this.topoTitleId = id;
      this.$emit('setTopoTitleId', id);
      this.havedata = nodeList && nodeList.length > 0;
      this.backgroundImage = backgroundImage ? `data:image/png;base64,${backgroundImage}` : backgroundImage;
     
      this.showPeerToPeerLinkDetail = !!(pathTopologyCoordinate?.peertopeerTopology);
       // layoutModel：布局类型 0 横向 1 纵向  2 星形
      // pathtopoType :1：拓扑图-横向 3:拓扑图-竖向，4:拓扑图斥力
      // debugger
      
      console.log(this.paramsData.pathtopoType,this.pathtopoType,'this.paramsData.pathtopoType')
     
     
    })
    .catch(error => {
      this.dataList = [];
      console.error('Failed to fetch topology data:', error);
      // this.$Message.error({ content: error.message, background: true });
    })
    .finally(() => {
      this.topoLoading = false;
      if (parent.loading) {
        parent.loading['dashpathtopo'] = false;
      }
    });
}

  },
 beforeDestroy() {
    // Clear interval
    this.clearIntervalTimer();
    
    // Clear refs
    this.$refs.PathTopo = null;
    this.$refs.fiberTopo = null;
    
    // Clear data
    this.dataList = null;
    this.nodeData = null;
    this.configData = null;
}

}
</script>

<style scoped lang="less">
.path-topo-box {
  position: relative;
  width: 100%;
  height: 100%;
}

.fillempty-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .fillempty {
    text-align: center;
    // line-height: 40px;
    background-image: url("../../../assets/dashboard/fileempty.png");
    // width: 290px;
    // height: 200px;

    background-repeat: no-repeat;
    // background-size: 290px 200px;
    background-position: center;
  }
  .fillemptyTitle {
    // color: #0e2a5f
    color: #5ca0d5;
  }
  .fillempty-light {
    width: 290px;
    height: 200px;

    background-repeat: no-repeat;
    // background-size: 290px 200px;
    background-position: center;
    background-image: url("../../../assets/dashboard/fileempty2.png");
  }
  .fillemptyTitle-light {
    color: #515a6e;
  }
}

.bigscreen {
  .fillemptyTitle {
    color: #8fd4ff;
  }
}
.bg-box {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}
.box {
  background-color: #060d15;
}
.light-box {
  background-color: #fff;
}
</style>