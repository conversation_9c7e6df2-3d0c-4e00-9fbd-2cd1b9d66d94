<template>
  <div class="tab-box">
    <div v-if="!isEditTopo && !isBigScreen" @click="goPage" class="path-more">
      {{ $t("comm_view_more") }}>
    </div>
    <Tabs
      :value="activeId"
      id="tabPathTopo"
      type="card"
      @on-click="onTabClick"
      :animated="false"
    >
      <TabPane
        v-for="tab in tabList"
        :key="tab.id"
        :label="tab.name"
        :name="String(tab.id)"
      >
        <div
          class="path-topo-item"
          :style="{ height: boxHeight - 35 - 20 + 'px' }"
          v-if="tab.id == activeId"
        >
          <keep-alive :max="2">
            <PathTopoItem
              :paramsData="tabParamsData"
              :boxWidth="boxWidth - 20"
              :boxHeight="boxHeight"
              @getLinkTable="getLinkTable"
              @setPreIp="setPreIp"
              @nodesClick="nodesClick"
              @updateCompleteNodesShow="updateCompleteNodesShow"
              @setTopoTitleId="setTopoTitleId"
              :isTopoTab="true"
              :isMouseOver="isMouseOver"
              :isEditTopo="isEditTopo"
              @hook:deactivated="handleDeactivated"
              @changeIsFirst="changeIsFirst"
              :isFirst="isFirst"
            ></PathTopoItem>
          </keep-alive>
        </div>
        <div class="box-foot"></div>
      </TabPane>
    </Tabs>
    <div class="btn-tab btn-left" @click="btnTabClick('left')">
      <img src="@/assets/dashboard/icon-page-left.png" alt="" />
    </div>
    <div class="btn-tab btn-right" @click="btnTabClick('right')">
      <img src="@/assets/dashboard/icon-page-right.png" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  name:'TabPathTopo',
  data() {
    return {
      tabList:[],
      activeId:null,
       autoSwitchTimer: null,
     
       timer: null,  // 添加定时器变量
       preloadedTabs:[],
       nextActiveId:null,
       tabParamsData:{},
       isBigScreen:false
    };
  },
  props: {
    isFirst: {
      type: Boolean,
      default: false,
    },
    isMouseOver: {
      type: Boolean,
      default: false,
    },
    paramsData: {
      type: Object,
      default: () => ({}),
    },
    boxWidth: {
      type: Number,
      default: 0,
    },
    boxHeight: {
      type: Number,
      default: 0,
    },
    isEditTopo: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
   isMouseOver: {
    handler(newVal) {
      if (newVal) {
        // debugger
        // 鼠标悬停，停止自动切换
        this.stopAutoSwitch();
      } else {
        // 鼠标离开，恢复自动切换
        this.startAutoSwitch();
      }
    },
    immediate: true
  },
  isEditTopo: {
    handler(newVal) {
      if(newVal){
        this.stopAutoSwitch()
      }
    },
    immediate: true
  }
  },
  components: {
    PathTopoItem: () => import('./PathTopoItem.vue'),
  },
  methods: {
    changeIsFirst(val){
      this.$emit('changeIsFirst',val)
    },
    // 查看更多
    goPage() {
      let toUrl = 'pathtopo'
      var iframSrc = window.location.hostname === 'localhost' ? '/anpm-plug-' + toUrl + '-ui.html?checkedTab=networkFault' : '/' + toUrl + '?checkedTab=networkFault';
      sessionStorage.setItem('jtopo', this.activeId);
      // type==2  是纤维图 其他情况是拓扑图
      // this.pathtopoType == '2' || this.pathtopoType == '5'
      let num = this.paramsData.pathtopoType == '2' || this.paramsData.pathtopoType == '5' ? 2 : 1
            sessionStorage.setItem('jtopoType', num);
            top.document.getElementById('sub-content-page').src = iframSrc;

      

    },
      handleDeactivated(id) {
    // 在组件被缓存时清理大型数据
    if (this.$refs[`pathTopo${id}`]) {
      this.$refs[`pathTopo${id}`].clearData();
    }
  },
    btnTabClick(type){
      console.log(type)
      if(type == 'left'){
        const currentIndex = this.tabList.findIndex(tab => String(tab.id) === this.activeId);
        const prevIndex = (currentIndex - 1 + this.tabList.length) % this.tabList.length;
        this.onTabClick(String(this.tabList[prevIndex].id));
      }else{
        const currentIndex = this.tabList.findIndex(tab => String(tab.id) === this.activeId);
        const nextIndex = (currentIndex + 1) % this.tabList.length;
        this.onTabClick(String(this.tabList[nextIndex].id));
      }
    },
    setTopoTitleId(id) {
      this.$emit('setTopoTitleId', id)
    },
    getTabData(tabId) {
      return {
        topoId: Number(tabId),
        pathtopoType: this.paramsData.pathtopoType,
        intervalTime: this.paramsData.intervalTime
      };
    },
     startAutoSwitch() {
      // 编辑拓扑图不自动切换
      if(this.isEditTopo){
        return
      }
        // 防止重复创建定时器
    if (this.timer) {
      return;
    }
    
      this.timer = setInterval(() => {
        const currentIndex = this.tabList.findIndex(tab => String(tab.id) === this.activeId);
        const nextIndex = (currentIndex + 1) % this.tabList.length;
        this.onTabClick(String(this.tabList[nextIndex].id));
        // 暂时改为30秒
      },60 * 1000); // 60秒
    },
     stopAutoSwitch() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    updateCompleteNodesShow(data) {
        this.$emit('updateCompleteNodesShow',data)
        // this.completeNodesShow = true
        // this.completeNodeData = data


      },
     nodesClick(param){
      this.$emit('nodesClick',param)
     
     },
    getLinkTable(arr,obj){
        this.$emit('getLinkTable',arr,obj)
    },
    setPreIp(ip){
       
        this.$emit('setPreIp',ip)
    },
    getTabList() {
    //   debugger
       this.tabList = this.paramsData.topoInfos
       this.activeId = String(this.tabList[0]?.id || '')
       this.nextActiveId = String(this.tabList[1]?.id || '')    
        this.tabParamsData = {
          topoId: Number(this.activeId),
          pathtopoType: this.paramsData.pathtopoType,
          intervalTime: this.paramsData.intervalTime
        }
  },
  onTabClick(tab){
    this.activeId = String(tab)
    this.tabParamsData = {
      topoId: Number(tab),
      pathtopoType: this.paramsData.pathtopoType,
      intervalTime: this.paramsData.intervalTime  
    }

    
    // 找到当前激活tab的索引
    const currentIndex = this.tabList.findIndex(tab => String(tab.id) === this.activeId);

    if(currentIndex == this.tabList.length - 1){
      this.nextActiveId =String(this.tabList[0]?.id || '')
    }else{
      this.nextActiveId = String(this.tabList[currentIndex + 1]?.id || '')
    }
    
    this.tabParamsData = {
          topoId: Number(this.activeId),
          pathtopoType: this.paramsData.pathtopoType,
          intervalTime: this.paramsData.intervalTime
        }
  },
  


  },
     
  created() {
    if(window.name.indexOf('bigscreen') > -1) {
    this.isBigScreen = true
  }
    this.getTabList()

    
      this.startAutoSwitch()
    
  },
   beforeDestroy() {
    this.stopAutoSwitch();  // 组件销毁前清除定时器
    // 清理所有缓存的组件数据
  this.tabList.forEach(tab => {
    if (this.$refs[`pathTopo${tab.id}`]) {
      this.$refs[`pathTopo${tab.id}`].clearData();
    }
  });
  }
}
</script>

<style lang="less" scoped>
.box-foot:before,
.box-foot:after {
  content: "";
  display: inline-block;
  position: absolute;
}
.box-foot {
  width: calc(100% - 40px);
  background: url(../../../assets/bc.png) repeat-x;
  height: 20px;
  margin-left: 20px;
  position: relative;
}

.box-foot:before {
  background: url(../../../assets/bl.png) no-repeat;
  width: 20px;
  height: 20px;
  left: -20px;
}

.box-foot:after {
  background: url(../../../assets/br.png) no-repeat;
  width: 20px;
  height: 20px;
  right: -18px;
}
.tab-box {
  position: relative;
  width: 100%;
}
.btn-tab {
  position: absolute;
  top: 50%;
  transform: translateY(-50%); /* 居中对齐 */
  cursor: pointer;
  z-index: 99999;
}
.btn-left {
  left: 10px;
}
.btn-right {
  right: 10px;
}
.path-more {
  position: absolute;
  right: 10px;
  top: 40px;
  cursor: pointer;
  color: #0290fd;
  z-index: 9999;
}
</style>