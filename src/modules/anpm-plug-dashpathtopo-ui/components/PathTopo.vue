<!-- G6拓扑图 -->
<template>
  <div>
    <div
      id="container"
      :style="{
        width: containerWidth,
        height: containerHeight,
        backgroundImage: `url(${backgroundImage})`,
      }"
      class="topo-contaienr"
    ></div>
    <div>
      <Modal
        sticky
        :value="dialogVisible"
        :width="modalWidth"
        class="index-modal probetask-modal"
        :styles="{ top: 100 + 'px' }"
        :title="$t('pathtopo_tip_alarm_info')"
        draggable
        :mask="true"
        @on-cancel="customModalCancel"
        :footer-hide="true"
      >
        <iframe
          :src="dialogContent"
          width="100%"
          :height="iframeHeight + 'px'"
          frameborder="0"
        ></iframe>
        <!-- <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{ $t("but_confirm") }}</Button>
        </div> -->
      </Modal>
    </div>
  </div>
</template>

<script>

import G6 from '@antv/g6'
import { Minimap } from '@antv/g6-plugin'
// import { FullScreen } from '@antv/g6-plugin'
import ipv6Format from "@/common/ipv6Format";



export default {
name: '',
components: {},
props:[
  "pathTopoCoorType",
  'pathTopoData',
  "topoTabList",
  
  'showWarning',
  "containerWidth",
  "containerHeight",
  'layoutModel',
  'backgroundImage',
  "showPeerToPeerLinkDetail",
  'tabsId',
  "forbidUpdate",
  "configData",
  "isMouseOver",
  "isEditTopo",
  "showType",
  ],
  //  pathTopoCoorType 0 路径拓扑  2仪表盘路径拓扑构件
 
// layoutModel：布局类型 0 横向 1 纵向  2 星形
data() {
return {
  modalWidth:0,
  iframeHeight: 0, // 用于存储动态计算的高度
  dialogVisible: false, // 控制对话框的显示状态
  dialogContent: '', // 用于存储要显示的 URL 信息

  // containerWidth:null,
  // containerHeight:null,
  currentSkin: sessionStorage.getItem('dark') || 1,
  nodeData: {
    nodes:[],
    edges:[],



  },
   isBigScreen:false,
  graph:null,
 
  viewCenter:{},
  

  successed:true,
  tooltip:null,
   fullScreenPlugin: null,

// 必须用上一个页面传过来的值
  //  showType: 'all',
  
     currentTranslateY: 0, // 当前画布的Y轴偏移量
    scrollTimer: null, // 滚动定时器
    scrollInterval: 10000, // 切换间隔，30秒
    totalHeight: 0, // 画布内容总高度
    isResetting: false, // 是否正在重置位置
    minimap: null,
    scaleRatio: 1,
    timeouts: new Set(), // 存储所有 timeout ID
    isAnimating: false,     // 动画状态标记
    isScroll: false, // 是否开启自动滚动  


}
},
computed: {},
watch: {
  pathTopoData: {
  handler() {
    // 更新数据前先清理资源
      this.cleanupGraph();
      // debugger
      console.log(this.layoutModel,'this.kdddddddddddddddddddddddddddd')
      this.currentTranslateY = 0;

    // 更新节点和连线数据
    if(this.layoutModel == 2) {
      this.handleStarNodes();
    } else {
      this.handleNodes();
    }
    debugger
    console.log(this.showType , "----------------")
    this.handleLink();

    // 使用nextTick确保DOM更新后再初始化G6
    this.$nextTick(() => {
      this.initG6();
    });
  },
  deep: true
},
isMouseOver: {
  handler(newVal) {
    // alert(newVal,'newVal')s
    if (newVal) {
      this.stopAutoScroll();
    } else {
      // 重置位置
    
   if(this.isScroll) {
    this.startAutoScroll()
    // this.refreshPosition()
   }
    // this.initHorizontal()
      // this.startAutoScroll();
    }
  },
}
   
},
methods: {
  // 清理动画方法
  clearAnimations() {
    if (this.timeouts.size > 0) {
      this.timeouts.forEach(id => {
        cancelAnimationFrame(id);
      });
      this.timeouts.clear();
    }
    this.isAnimating = false;
  },
  refreshPosition() {
  if (!this.graph) return;

  // 停止当前的自动滚动
  this.stopAutoScroll();
  
  this.$nextTick(() => {
    // 获取所有节点的边界框
    const nodes = this.graph.getNodes();
    let minX = Infinity;
    let maxX = -Infinity;
    let minY = Infinity;
    let maxY = -Infinity;

    nodes.forEach(node => {
      const bbox = node.getBBox();
      minX = Math.min(minX, bbox.minX);
      maxX = Math.max(maxX, bbox.maxX);
      minY = Math.min(minY, bbox.minY);
      maxY = Math.max(maxY, bbox.maxY);
    });

    // 计算内容宽度
    const contentWidth = maxX - minX;
    const containerWidth = this.graph.get('width');
    
    // 计算合适的缩放比例（基于宽度，保证X轴铺满）
    const horizontalPadding = 40; // 左右边距
    const topPadding = 20;  // 顶部边距
    const scaleRatio = (containerWidth - horizontalPadding) / contentWidth;

    // 重置变换
    this.graph.get('group').resetMatrix();

    // 应用新的缩放，从左上角开始布局
    this.graph.zoomTo(scaleRatio, {
      x: 0,
      y: 0
    });

    // 调整位置，确保从左上角开始显示
     this.graph.translate(
      -minX * scaleRatio + horizontalPadding/2, 
      -minY * scaleRatio + topPadding  // 添加顶部边距
    );

    // 更新总高度用于滚动
    this.totalHeight = (maxY - minY) * scaleRatio;
    this.currentTranslateY = 0;

    // 刷新视图
    this.graph.refresh();

    // 重新开始自动滚动
    // this.startAutoScroll();
  });
},
  cleanupGraph() {
    // debuggers
  if(this.graph) {
    try {
      // 清除所有事件监听器
      this.graph.off();
      
      // 清除所有节点和边
      this.graph.clear();
      
      // 销毁minimap插件
      if(this.minimap) {
        this.minimap.destroy();
        this.minimap = null;
      }
      
      // 停止自动滚动
      if(!this.isMouseOver) {
        this.stopAutoScroll();
      }
      
      // 销毁图实例
      this.graph.destroy();
      this.graph = null;
      
      // 清理数据引用
      this.nodeData = {
        nodes: [],
        edges: []
      };
      
      // 清理其他引用
      this.tooltip = null;
      this.viewCenter = {};
     
      
      // 强制垃圾回收
      if(window.gc) {
        window.gc();
      }
    } catch(e) {
      console.warn('Error during graph cleanup:', e);
    }
  }
},

// 计算画布内容总高度
  calculateTotalHeight(scaleRatio) {
    // 获取所有节点
    const nodes = this.graph.getNodes()
    let minY = Infinity
    let maxY = -Infinity
    
    // 计算节点在y轴方向的实际范围
    nodes.forEach(node => {
      const bbox = node.getBBox()
      minY = Math.min(minY, bbox.minY)
      maxY = Math.max(maxY, bbox.maxY)
    })
    
    // 计算实际内容高度（考虑缩放比例）
    this.totalHeight = (maxY - minY) * (scaleRatio || 1)
    
    // 如果内容高度超过容器高度，启动自动滚动
    if (this.totalHeight - 40 > this.containerHeight) {
      this.isScroll = true
      this.startAutoScroll()
    }else {
       // 居中显示逻辑
       this.isScroll = false
   this.graph.fitView(20);

    }
  },
  // 开始自动滚动
  // 开始自动滚动
  
// 开始自动滚动
 startAutoScroll() {
  // 先清理之前可能存在的动画
  this.refreshPosition()
  
    this.clearAnimations();
    
    // 如果已经在动画中，不启动新动画
    if (this.isAnimating || !this.graph) return;
    
    this.isAnimating = true;
  
     if (!this.graph || !this.graph.translate) {
    console.warn('Graph not initialized for auto-scroll');
    return;
  }
  //  debugger
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
      this.scrollTimer = null;
    }

    
    this.isResetting = false;

    // 设定重叠比例（20%重叠）
    const overlapRatio = 0.1;
    // 计算每次滚动的实际距离
    const scrollDistance = this.containerHeight * (1 - overlapRatio);

    this.scrollTimer = setInterval(() => {
      // 如果正在重置，跳过这次滚动
      if (this.isResetting) {
        return;
      }

      // 计算下一个位置
      let nextY = this.currentTranslateY + scrollDistance;
      
      // 如果下一个位置超出了内容高度，重新开始
      if (nextY >= this.totalHeight) {
        this.isResetting = true;
        
       this.graph.translate(0, this.currentTranslateY);
          this.currentTranslateY = 0;
          this.isResetting = false;
      
      } else {
       // 缓动函数
    const easeInOutQuad = (t) => {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    };

    const duration = 1500; // 动画持续时间（毫秒）
    const startTime = performance.now();
    const startY = this.currentTranslateY;

    const animate = (currentTime) => {
        if (!this.graph) return;

        // 计算动画进度
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 应用缓动函数
        const easeProgress = easeInOutQuad(progress);
        
        // 计算当前位置
        const currentY = startY + (scrollDistance * easeProgress);
        
        // 计算这一帧需要移动的距离
        const deltaY = currentY - this.currentTranslateY;
        
        // 执行移动
        this.graph.translate(0, -deltaY);
        this.currentTranslateY = currentY;

        // 如果动画未完成，继续下一帧
        if (progress < 1) {
            const rafId = requestAnimationFrame(animate);
            this.timeouts.add(rafId);
        } else {
            // 确保最终位置精确
            const finalDelta = nextY - this.currentTranslateY;
            if (Math.abs(finalDelta) > 0.1) {
                this.graph.translate(0, -finalDelta);
                this.currentTranslateY = nextY;
            }
        }
    };

    const rafId = requestAnimationFrame(animate);
    this.timeouts.add(rafId);
      }
    }, this.scrollInterval);
  },
    // 停止自动滚动
  stopAutoScroll() {
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
      this.scrollTimer = null;
    }
     this.timeouts.forEach(id => {
    clearTimeout(id);
    cancelAnimationFrame(id);
  });
  this.timeouts.clear();
    this.isResetting = false;
  },
  // 初始化G6（横向纵向）
  initG6() {
     this.$emit('update:forbidUpdate',true)
    //console.log(this.containerWidth,this.containerHeight,'宽高================')

    // 区分竖向横向边样式
    //console.log(this.backgroundImage)
    let linkType = 'cubic-horizontal'
    // debugger
    //console.log(this.layoutModel,'this.layout')
    if(this.layoutModel == 1) {
      // 竖向
      linkType = 'cubic-vertical'


    }
    // let minimapWidth =Math.max(88/1450 * this.containerWidth,88) 
    // let minimapHeight =Math.max(153/600 * this.containerHeight,153)
    let minimapWidth = 88/1450 * this.containerWidth
    let minimapHeight = 153/600 * this.containerHeight

    this.minimap = new Minimap({
      size: [minimapWidth, minimapHeight],
      className: 'g6-minimap',
      type: 'delegate',
      delegateStyle: {
        fill: '#fff',
        stroke: '#fff'
      }
    })
    let tooltip = this.createTooltip()

    let obj = {
      container: document.getElementById('container'),
      width:this.containerWidth,
      height:this.containerHeight,
      plugins: [tooltip,this.minimap],

      nodeStateStyles: {
      // highlight: {
      //   stroke: '#91d5ff',
      //   lineWidth: 3,
      // },
      selected:{
        stroke: '#91d5ff',
          lineWidth: 3,
      }
    },
    edgeStateStyles: {
      highlight: {

        lineWidth: 4,
      },
    },




      defaultNode: {
          type: 'domNode', // 默认节点类型
        },

      defaultEdge: {
        type:linkType,
        style: {
            lineWidth: 1, // 默认线宽
          },



      },

        modes: {
             default: [
              'drag-canvas',
              'zoom-canvas',
              'drag-node'
              ], // 同时开启画布拖拽、缩放和节点拖拽
            
                  },

    }
    if(this.layoutModel == 2) {
      obj.layout = {
        type:'force',
        linkDistance: 120,
        nodeStrength: -80,
        edgeStrength: 5,
        // animate: false
      }
      // obj.nodeStrength = -100;
      obj.defaultEdge = {
        type:'line'
      }
    }
     this.graph = new G6.Graph(obj)
    // 调用事件监听
    this.addEventListenerGraph()


         this.graph.data(this.nodeData) // 数据映射
          this.graph.on('afterrender', () => {
             this.$emit('update:forbidUpdate',false)
             if(this.layoutModel == 0) {
              // 横向
              this.initHorizontal()
             }
        
      
      
     
       


    })
  
        this.graph.render() // 渲染
    var width = this.graph.get('width');
    var height = this.graph.get('height');
    // 找到视口中心
    this.viewCenter = {
      x: width / 2,
      y: height / 2
    };
        // 初步渲染还原画布位置
        // this.graph.translate(-240,-140)
       


  },
  // 事件监听合集
  addEventListenerGraph() {
   
     this.graph.on('edge:click', (evt) => {

      if(this.isBigScreen) {
        return
      }

      this.handleLinkClick(evt)
      })
       this.graph.on('edge:mouseover', (evt) => {

      this.handleLinkOver(evt)
      })
       this.graph.on('edge:mouseleave', (evt) => {

      this.handleLinkLeave(evt)
      })

    this.graph.on('node:click', (evt) => {
      // console.log('节点的点击事件')
       if(this.isBigScreen) {
        return
      }
      const clickedNode = evt.item;
      this.handleNodeSelection(clickedNode);
      })
      // 节点悬浮高亮
      this.graph.on('node:mouseover',(evt) => {
        // console.log(evt,'mouseover')
        this.handleLightNode(evt)
      })
      // 节点离开取消高亮
      this.graph.on('node:mouseleave',(evt) => {
        // console.log(evt,'mouseleave')
        this.handleLightLeave(evt)
      })

      


  },
  initHorizontal() {
    // 横向
    const nodes = this.graph.getNodes()
      //console.log(nodes,'nodes')
      let minX = Infinity
      let maxX = -Infinity
      let minY = Infinity
      
      // 找出所有节点x轴方向的范围
      nodes.forEach(node => {
        const bbox = node.getBBox()
        minX = Math.min(minX, bbox.minX)
        maxX = Math.max(maxX, bbox.maxX)
        minY = Math.min(minY, bbox.minY)
      })
      
      // 计算内容在x轴方向的总宽度
      const contentWidth = maxX - minX
      //console.log(contentWidth,'contentWidth')

      
      // 计算需要的x轴缩放比例
      const horizontalPadding = 60  // 左右边距，可以调整
      this.scaleRatio = (this.containerWidth - horizontalPadding * 2) / contentWidth
     
      
      // 应用缩放，基于画布中心点
      this.graph.zoomTo(this.scaleRatio, {
        x: 0,
        y: 0
      })
      
       // 正确计算平移量
       this.$nextTick(() => {
        if(!this.isEditTopo) {
          this.calculateTotalHeight(this.scaleRatio)
        }
        
       })
  },
  createTooltip() {
      return new G6.Tooltip({
        offsetX: 10,
        offsetY: 10,
        // The container to render the tooltip
        container: document.getElementById('container'),
        // Only show tooltip for nodes
        itemTypes: ['node'],
        // Fix position relative to node
        fixToNode: [1, 0],
         trigger: 'click',
         // 添加触发条件判断
        shouldBegin: (e) => {
          if (!e.item) return false;
          const model = e.item.getModel();
          const data = model.data;
          // 只有当 faultLink > 0 时才显示 tooltip
          return data.faultLink > 0 && !this.isBigScreen;
        },
        // Customize tooltip content
        getContent: (e) => {
          if (!e.item) return '';
          const model = e.item.getModel();
          const data = model.data;
          //console.log(data,'data....')
          const outDiv = document.createElement('div');

         let infoArry = [];


        // 将数据序列化并转义引号
        const serializedData = JSON.stringify(data).replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
        let bgColor = this.currentSkin == 1 ? '#16436b' : '#fff'
          let fontColor = this.currentSkin == 1 ? '#fff' : '#515A6E'
          let boxShadow = this.currentSkin == 1 ? 'unset' : '0px 0px 8px 1px rgba(0,0,0,0.16)'
          let borderColor = this.currentSkin == 1 ? '#16436b' : '#e2e2e2'
          outDiv.style.cssText = `
            min-width: 80px;
            background-color: ${bgColor};
            border: 1px solid ${borderColor};
            border-radius: 4px;
            box-shadow: ${boxShadow};
            padding: 10px 8px;
          `
        let titleColor = this.currentSkin == 1 ? '#0fe' : '#0290FD'
        let fontColorMain = this.currentSkin == 1 ? '#fff' : '#515A6E'
        let borderLineColor = this.currentSkin == 1 ? '#045b8e' : '#E4E7ED'
      let html = `
        <div class="header" style="font-size: 14px;color: ${fontColorMain};text-align:left; height:24px;line-height:24px;">
          ${this.$t("alarm_details")}
          <span id='goDetail' style="color:${titleColor};cursor: pointer"
                onclick='window.pathTopoInstance.handleGoDetail(${serializedData})'>
            ${this.$t("go_and_check")} >
          </span>
        </div>
        <div style="font-size: 14px;color:${fontColorMain};text-align:left;height:24px;line-height:24px;margin-bottom:5px;">
          ${this.$t("number_of_alarms")}: ${this.$t(data.faultLink)}
        </div>
        <div class=content>`;
        let info = data.faultDesc
      if (info) {
        infoArry = info.split("#$");
      }
      infoArry.forEach((item) => {
        let content = item.split(",");

        html = html +`<hr style="border:1px solid ${borderLineColor}"></hr>`;
        for (let index = 0; index < content.length; index++) {
          let element = content[index];
          html = html +`<div style="font-size: 14px;color: ${fontColorMain};text-align:left;height:24px;line-height:24px;white-space:nowrap;">${element ?? ""}</div>`;
        }
      });

          outDiv.innerHTML = html + '</div>'
          return outDiv;
        },
      })
    },

handleGoDetail(data) {
    //console.log(data, 'data....');

    let ids = data.faultIds.join(",");
    let nodeIp = data.value;
    let url = window.location.hostname === "localhost"
      ? "/anpm-plug-alarmlist-ui.html?faultIds=" + ids + "&days=62&keyword=" + nodeIp
      : "/alarmlist?faultIds=" + ids + "&days=62&keyword=" + nodeIp;

    this.dialogContent = url; // 保存生成的 URL 信息
    this.dialogVisible = true; // 打开对话框

    // this.$emit('go-detail', data);
  },
  // customModalOk() {
  //   window.location.href = this.dialogContent; // 执行页面跳转
  // },
  customModalCancel() {
    this.dialogVisible = false; // 关闭对话框
  },



    handleNodeSelection(clickedNode) {
    const nodeModel = clickedNode.getModel();
    //console.log(nodeModel,'nodeModel')
    if(nodeModel.data.childList && nodeModel.data.childList.length > 0) {
      // 收缩节点，点击显示弹框
      let obj = {}
      obj.sourceIp = nodeModel.data.value
      obj.nodeList =[ nodeModel.data,...nodeModel.data.childList]
      obj.linkList = this.pathTopoData.nodeRouteList

      obj.configData = this.configData
      obj.showWarning = this.showWarning
      obj.showType = this.showType
      this.$emit('updateCompleteNodesShow',obj)

    }
  },


  // 处理离开高亮节点样式恢复
  handleLightLeave(evt) {
    let data = evt.item._cfg.model.data
    if(data.inconType === 6) {
      const graph = this.graph;

  // 清除节点的高亮效果
  graph.getNodes().forEach(node => {
    const nodeModel = node.getModel();
    const nodeSize = Array.isArray(nodeModel.size) ? nodeModel.size[0] : nodeModel.size;

      // 只有当节点大小为30时才改变尺寸
      if (nodeSize === 32) {
    graph.updateItem(node, {
        size: 24,
      });
    }

  });

  // 清除边的高亮效果
  graph.getEdges().forEach(edge => {
    const edgeModel = edge.getModel();
    const lineWidth = edgeModel.style?.lineWidth;

      // 只有当边宽为4时才改变尺寸
      if (lineWidth === 4) {
    graph.updateItem(edge, {
        style: {

          lineWidth: 1,
        },
      });
    }

  });


    }

  },

  // 处理节点悬浮高亮
  handleLightNode(evt) {
    let data = evt.item._cfg.model.data

    if(data.inconType === 6) {
      //console.log('目标节点高亮')
      let despLinkIds = data.despLinkIds
      const graph = this.graph;
      // 高亮匹配的节点
    graph.getNodes().forEach(node => {
      const nodeModel = node.getModel()
      if (nodeModel.data && nodeModel.data.linkIds) {
        const hasMatchingLink = nodeModel.data.linkIds.some(id => despLinkIds.includes(id))
        if (hasMatchingLink) {
          // 获取原始大小
        const originalSize = nodeModel.size || graph.getDefaultNodeSize();



        // 设置高亮状态和新的大小

        graph.updateItem(node, {
          size: 32,
        });
        }
      }
    })

    // 高亮匹配的边
    graph.getEdges().forEach(edge => {
      const edgeModel = edge.getModel()
      if (edgeModel.data && edgeModel.data.linkIds) {
        const hasMatchingLink = edgeModel.data.linkIds.some(id => despLinkIds.includes(id))
        if (hasMatchingLink) {
          graph.updateItem(edge, {
          style: {
            ...edgeModel.style,
            lineWidth: 4, // 宽度变为原来的 2 倍
          },
        });
        }
      }
    })

    }

  },
      



   
       // 处理节点
    handleNodes() {
      //console.log(this.layoutModel,'布局类型布局类型')
       this.nodeData.nodes = []
        let nodeList = this.pathTopoData?.nodeList || []; // 节点数据
        nodeList.forEach((item, index) => {
          // 区分横向布局竖向布局节点位置
          let newX = item.locX
          let newY = item.locY
          let postion = [ [0, 0.5], [1, 0.5]]
          if(this.layoutModel == 1) {
         
            postion = [ [0.5,1],[0.5,0],]

          }
            // 区分横向布局竖向布局节点位置/
            // 区分横向竖向节点与边练节点位置


            let idx = item.id.indexOf('_') + 1



// TODO 处理节点
            let offset = 0
           
            let text = ''
            let text2 = ''
            let value2 = JSON.parse(JSON.stringify(item.value))
            // 判断如果有aliases就两排显示label
            let value = ipv6Format.abbreviateIPv6(item.value);
            // debugger

            let existsDeviceName = this.existShowType(this.$t("snmp_task_device_name"));
            let existsDeviceIp = this.existShowType(this.$t("comm_topo_device_ip"));
            let existsAll = this.existShowType("all");

            // if (this.showType === "all") {
            if ((existsDeviceName && existsDeviceIp) || existsAll) {
              //console.log('all',this.showType)
           text = `${item.aliases || ""}\n${value}`;
         
          text2 = `${item.aliases || ""}\n${value2}`;
         
        // } else if (this.showType === this.$t("snmp_task_device_name")) {
        } else if (existsDeviceName) {
          // debugger
          text = `${item.aliases || ""}`;
          text2 = `${item.aliases || ""}`;
    
        // } else if (this.showType === this.$t("comm_topo_device_ip")) {
        } else if (existsDeviceIp) {
          text = `${value || ""}`;
          text2 = `${value2 || ""}`;
          
        }

       let isSingleLine = text2.startsWith('\n')|| !text2.includes('\n');
       text2 = text2.replace(/^\n+/, '');
       item.label = text2;
       
      
      
       

            
            let obj = {}
            // console.log(item,'item===============================>')
            if(item.childList && item.childList.length > 0) {
              // console.log(item.childList,'=================================子集')
            // this.handleDomeNode(item)
            postion = [ [0.2, 0.3], [0.4, 0.3]]
            if(this.layoutModel == 1) {
              postion = [ [0.28,0.2],[0.28,0.2],]
            }

               obj = {
                id:item.id,
                label:text,
                type:'domNode',

                 x:newX,
                y:newY,
                size:[120,40],
                data:item,
                anchorPoints:postion


            }


            }else {
                    // 匹配图片
            let imgUrl = this.handleImage(item)
              obj = {
                id:item.id,
                label:text,
                type:'image',
                img: imgUrl,
                 x:newX,
                y:newY,
                anchorPoints:postion,
                size:[24,24],
                data:item,
                labelCfg: {           // 标签配置属性
                position: 'bottom',// 标签的属性，标签在元素中的位置
                offset: 8,
                style: {            // 包裹标签样式属性的字段 style 与标签其他属性在数据结构上并行
                fontSize: 12,      // 标签的样式属性，文字字体大小
                fill: this.currentSkin == 1 ? '#5CA0D5' : '#515A6E',
                 textBaseline: isSingleLine ? 'middle' : 'top',
                  textAlign: 'center',
               
                }
                }

            }
            }

            // console.log(index)
            this.nodeData.nodes.push(obj)
        })
        // console.log(this.nodeData,'节点数据id')
       
    },
    // 星形节点
    handleStarNodes() {
      //console.log(this.layoutModel,'布局类型布局类型')
       this.nodeData.nodes = []
        let nodeList = this.pathTopoData?.nodeList || []; // 节点数据
        nodeList.forEach((item, index) => {

            let idx = item.id.indexOf('_') + 1
            let x = item.locX
            let y = item.locY



            let text = ''
            let text2 = ''
            let value2 = JSON.parse(JSON.stringify(item.value))
            // 判断如果有aliases就两排显示label
            let value = ipv6Format.abbreviateIPv6(item.value);
           

          let existsDeviceName = this.existShowType(this.$t("snmp_task_device_name"));
          let existsDeviceIp = this.existShowType(this.$t("comm_topo_device_ip"));
          let existsAll = this.existShowType("all");


        // if (this.showType === "all") {
        if ((existsDeviceName && existsDeviceIp) || existsAll) {
              //console.log('all',this.showType)
          text = `${item.aliases || ""}\n${value}`;
         
          text2 = `${item.aliases || ""}\n${value2}`;
        // } else if (this.showType === this.$t("snmp_task_device_name")) {
        } else if (existsDeviceName) {
          // debugger
          text = `${item.aliases || ""}`;
          text2 = `${item.aliases || ""}`;
          //console.log(this.$t("snmp_task_device_name"),this.showType)
        // } else if (this.showType === this.$t("comm_topo_device_ip")) {
        } else if (existsDeviceIp) {
          text = `${value || ""}`;
          text2 = `${value2 || ""}`;
          //console.log(this.$t("comm_topo_device_ip"),this.showType)
        }
         let isSingleLine = text2.startsWith('\n')|| !text2.includes('\n');
       text2 = text2.replace(/^\n+/, '');
        item.label = text2
            let obj = {}
            // console.log(item,'item===============================>')
            if(item.childList && item.childList.length > 0) {
              // console.log(item.childList,'=================================子集')
            // this.handleDomeNode(item)
          let  postion = [ [0, 0.3], [1, 0.3]]
               obj = {
                id:item.id,
                label:text,
                type:'domNode',
                // x:x,
                // y:y,

                size:[120,40],
                 anchorPoints:postion,
                data:item,



            }


            }else {
                    // 匹配图片
            let imgUrl = this.handleImage(item)
              obj = {
                id:item.id,
                label:text,
                type:'image',
                img: imgUrl,
                data:item,
                 anchorPoints:[[0.5, 0.5]],


                x:null ,
                y:null,
                size:[24,24],
                labelCfg: {           // 标签配置属性
                position: 'bottom',// 标签的属性，标签在元素中的位置
                offset: 8,

                style: {            // 包裹标签样式属性的字段 style 与标签其他属性在数据结构上并行
                fontSize: 12,      // 标签的样式属性，文字字体大小
                fill:this.currentSkin == 1 ? '#5CA0D5' : '#515A6E',
                textBaseline: isSingleLine ? 'middle' : 'top',
                textAlign: 'center',
                }
                }

            }
              if(index === 0){
                obj.x = this.viewCenter.x ;
                obj.y = this.viewCenter.y ;
              }

            }

            // console.log(index)
            this.nodeData.nodes.push(obj)
        })
        // console.log(this.nodeData,'节点数据id')


    },

     // 处理连线
    handleLink() {
        // 用来装连线数组
        this.nodeData.edges = []
        let arr = []
        let linkList = this.pathTopoData?.nodeRouteList || []
         const edgeCount = new Map()
       linkList.forEach((item,index) => {
            // console.log(item,index)
            let linkColor = this.handleLinkColor(item)
            let linkLabelColor = this.handleLinkLabelColor(item)
            let linkLabel = this.handleLinkLabel(item)
              // 创建双向的key来统计边数
              const nodeKey1 = `${item.source}-${item.target}`
              const nodeKey2 = `${item.target}-${item.source}`
              // 检查两个方向的边数
              const count = (edgeCount.get(nodeKey1) || 0) + (edgeCount.get(nodeKey2) || 0)
              // 更新边计数
              edgeCount.set(nodeKey1, (edgeCount.get(nodeKey1) || 0) + 1)

             let idx = item.source.indexOf('_') + 1
             let obj = {
                source: item.source,
                target:item.target,
                data:item,
                style: {
                  stroke: linkColor,
                  lineWidth: 1,


                },
                label:linkLabel,
                labelCfg: {
                  autoRotate:true,
                  style: {
                    fill:linkLabelColor,
                    fontSize:12,
                    // stroke:linkColor,


                  }
                }

            }
            if(item.right == 1) {
              obj.labelCfg.position = 'end'
              // obj.labelCfg.offset = -150; // 标签整体偏移10像素
              obj.labelCfg.refX = -20
              // obj.autoRotate = true
              //console.log('obj...............................................999')
            }
             // 如果是节点对之间的第二条边，使用cubic类型
            //  console.log(count,'count.................')
        if (count > 0) {
            obj.type = 'cubic'
            // 0 横向20， 1 竖向-20
            if(this.layoutModel == 1) {
              obj.curveOffset = -20
            }else {
              obj.curveOffset = 20
            }

        }
            // 虚线逻辑
            if(item.destType !=null &&  item.destType == 0) {
             obj.style.lineDash = [4, 4]

            }
            // 缩略虚线逻辑
            if(item.scalingSplicingPoint === 1) {
               obj.style.lineDash = [4,1, 4]

            }
            arr.push(obj)


        })
        // console.log(arr,this.nodeData)
        this.nodeData.edges = arr


},
    handleImage(data) {
        let {
        deviceManageInconDeviceType,
        twoDeviceType,
        inconType,
        inconDeviceType,
        locX,
        locY,
        width = 24,
        height = 24,
        aliases = "",
        value,
        type,
        state,
        devType,
        interruptShowGrayNode,
        id,
        faultLink,
        faultIds,
        faultDesc,
        virtualNode,
        linkIds,
        despLinkIds,
        childList = []
      } = data;
         const showState = this.showWarning ? state : null;
        // 节点状态字段对应
      let statusType = 0;
      if (showState == null || showState == "") {
        statusType = 0;
      } else if (showState == 1) {
        statusType = 1;
      } else if (showState == 2 || showState == 3) {
        statusType = 2;
      } else {
        statusType = 3;
      }
       let url = null
         if (inconDeviceType) {
        this.configData.deviceTypeIconManageConfigures.forEach((item) => {
          if (item.deviceTypeId == inconDeviceType) {
            if (interruptShowGrayNode && interruptShowGrayNode == 1 && this.showWarning) {
              //    debugger
              item.types.forEach((item3) => {
                if (item3.type === 3) {
                  url = item3.image;
                }
              });
            } else {
              item.types.forEach((item2) => {
                if (item2.type == statusType) {
                  url = item2.image;
                  // // console.log(url);
                }
              });
            }
          }
        });

        if (url === null) {
          // 先判断deviM。。。。 与设备匹配，没有，匹配上为空往下进行
          if (deviceManageInconDeviceType) {
            this.configData.deviceTypeIconManageConfigures.forEach((item) => {
              if (item.deviceTypeId == deviceManageInconDeviceType) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  this.showWarning
                ) {
                  //    debugger
                  item.types.forEach((item3) => {
                    if (item3.type === 3) {
                      url = item3.image;
                    }
                  });
                } else {
                  item.types.forEach((item2) => {
                    if (item2.type == statusType) {
                      url = item2.image;
                      // // console.log(url);
                    }
                  });
                }
              }
            });
            if (url === null) {
              if (twoDeviceType) {
                let findR = this.configData.inUseIconManageConfigures.find(
                  (item) => item.code === "SLN"
                );
                if (findR) {
                  if (
                    interruptShowGrayNode &&
                    interruptShowGrayNode == 1 &&
                    this.showWarning
                  ) {
                    let findR2 = findR.types.find((item) => item.type === 3);
                    url = findR2.image;
                  } else {
                    let findR3 = findR.types.find((item) => item.type === statusType);
                    url = findR3.image;
                  }
                }
              } else {
                this.configData.inUseIconManageConfigures.forEach((item) => {
                  if (item.deviceTypeId == inconType) {
                    if (
                      interruptShowGrayNode &&
                      interruptShowGrayNode == 1 &&
                      this.showWarning
                    ) {
                      item.types.forEach((item3) => {
                        if (item3.type === 3) {
                          url = item3.image;
                        }
                      });
                    } else {
                      item.types.forEach((item2) => {
                        if (item2.type == statusType) {
                          url = item2.image;
                        }
                      });
                    }
                  }
                });
              }
            }
          } else {
            if (twoDeviceType) {
              let findR = this.configData.inUseIconManageConfigures.find(
                (item) => item.code === "SLN"
              );
              if (findR) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  this.showWarning
                ) {
                  let findR2 = findR.types.find((item) => item.type === 3);
                  url = findR2.image;
                } else {
                  let findR3 = findR.types.find((item) => item.type === statusType);
                  url = findR3.image;
                }
              }
            } else {
              this.configData.inUseIconManageConfigures.forEach((item) => {
                if (item.deviceTypeId == inconType) {
                  if (
                    interruptShowGrayNode &&
                    interruptShowGrayNode == 1 &&
                    this.showWarning
                  ) {
                    item.types.forEach((item3) => {
                      if (item3.type === 3) {
                        url = item3.image;
                      }
                    });
                  } else {
                    item.types.forEach((item2) => {
                      if (item2.type == statusType) {
                        url = item2.image;
                      }
                    });
                  }
                }
              });
            }
          }

          // 如果没有  二层节点不为空 二层节点   为空下面的逻辑


        }
      } else {
        // debugger
        // 先判断deviM。。。。 与设备匹配，没有，匹配上为空往下进行
        if (deviceManageInconDeviceType) {
          this.configData.deviceTypeIconManageConfigures.forEach((item) => {
            if (item.deviceTypeId == deviceManageInconDeviceType) {
              if (
                interruptShowGrayNode &&
                interruptShowGrayNode == 1 &&
                this.showWarning
              ) {
                //    debugger
                item.types.forEach((item3) => {
                  if (item3.type === 3) {
                    url = item3.image;
                  }
                });
              } else {
                item.types.forEach((item2) => {
                  if (item2.type == statusType) {
                    url = item2.image;
                    // // console.log(url);
                  }
                });
              }
            }
          });

          if (url == null) {
            this.configData.inUseIconManageConfigures.forEach((item) => {
              if (item.deviceTypeId == inconType) {
                if (
                  interruptShowGrayNode &&
                  interruptShowGrayNode == 1 &&
                  this.showWarning
                ) {
                  item.types.forEach((item3) => {
                    if (item3.type === 3) {
                      url = item3.image;
                    }
                  });
                } else {
                  item.types.forEach((item2) => {
                    if (item2.type == statusType) {
                      url = item2.image;
                    }
                  });
                }
              }
            });
          }
        } else {
          this.configData.inUseIconManageConfigures.forEach((item) => {
            if (item.deviceTypeId == inconType) {
              if (
                interruptShowGrayNode &&
                interruptShowGrayNode == 1 &&
                this.showWarning
              ) {
                item.types.forEach((item3) => {
                  if (item3.type === 3) {
                    url = item3.image;
                  }
                });
              } else {
                item.types.forEach((item2) => {
                  if (item2.type == statusType) {
                    url = item2.image;
                  }
                });
              }
            }
          });
        }
      }
      return "data:image/png;base64," + url

    },
    handleLinkLabelColor(item) {
      let { state,interruptShowGrayNode } = item;
      // 中断颜色
      let breakLineColor = this.configData.breakLineColor;
      // 正常颜色
      let normalLineColor = '#fff';
      // 裂化颜色
      let degradationLineColor = this.configData.degradationLineColor;
      // 未知颜色
      let unknownLineColor = this.configData.unknownLineColor;
       let color = normalLineColor;
       if (this.showWarning) {
        if (interruptShowGrayNode == 1) {
          // 未知
          color = unknownLineColor;
        }else{
          if (state == 1) {
            // 中断
            color = breakLineColor;
          } else if (state == 2 || state == 3) {
            // 劣化
            color = degradationLineColor;
          } else if (interruptShowGrayNode == 1) {
            // 未知
            color = unknownLineColor;
          }
        }
      }

      return color

    },

    handleLinkColor(item) {
      let { state,interruptShowGrayNode } = item;
      // 中断颜色
      let breakLineColor = this.configData.breakLineColor;
      // 正常颜色
      let normalLineColor = this.configData.normalLineColor;
      // 裂化颜色
      let degradationLineColor = this.configData.degradationLineColor;
      // 未知颜色
      let unknownLineColor = this.configData.unknownLineColor;
       let color = normalLineColor;
       if (this.showWarning) {
        if (interruptShowGrayNode == 1) {
          // 未知
          color = unknownLineColor;
        }else{
          if (state == 1) {
            // 中断
            color = breakLineColor;
          } else if (state == 2 || state == 3) {
            // 劣化
            color = degradationLineColor;
          } else if (interruptShowGrayNode == 1) {
            // 未知
            color = unknownLineColor;
          }
        }
      }

      return color

    },
    // 判断是否存在
    existShowType(type){
      if(type == "" || type == null){
          return false;
      }
      // 是否是数组
      if(Array.isArray(this.showType)){
          return this.showType.indexOf(type) > -1;
      }else if(/.*string/ig.test(Object.prototype.toString.call(this.showType).toString())){
          if(/all/ig.test(this.showType)){
              return true;
          }else if(this.showType == ''){
              return false;
          }
          // 是否是字符串类型
          return this.showType == type;
      }
      return false;
    },
    // 处理边文字
    // 处理边文字
    handleLinkLabel(data) {
        let {
        state,
        destType,
        delay,
        lossRate,
        flowIntoSpeed,
        flowOutSpeed,
        faultDesc,
        faultLink,
        faultIds,
        interruptShowGrayNode,
        specialLine,
      } = data;
      let text = "";
      let textOffsetY =  0


      // TODO 处理显示指标的问题。
      // 线路延时
      let existsDelay = this.existShowType(this.$t("comm_topo_device_delay"));
      // 线路丢包率
      let existsLoss = this.existShowType(this.$t("comm_topo_device_loss"));
      // 线路流量 
      let existsflow = this.existShowType(this.$t("comm_topo_device_flow"));
      let existsAll = this.existShowType("all");

      // TODO 处理节点显示问题,如果不显示就设置成一个无效的值，方便判断。
      if ((existsDelay && existsLoss && existsflow) || existsAll) {
          // 
      } else {
          if (!existsDelay) {
            // 时延
            delay = "";
          }
          if (!existsLoss) {
            // 丢包
            lossRate = "";
          }
          if (!existsflow) {
              // 入流速
            flowIntoSpeed = "";
              // 出流速
            flowOutSpeed = "";
          }
      }
      if (delay) {
        // 提取数字部分并转换为浮点数
        const delayNum = parseFloat(delay.replace('ms', ''));
        if (delayNum < 1) {
          delay = '<1ms';
        }
      }
      if (!existsDelay && !existsLoss && !existsflow) {
        text = "";

      } else{ 
        
        if (!existsflow) {
          // text = "\n\n";
          if(existsDelay && existsLoss){
            if(delay || lossRate){
              text = `${delay || "--"}/${lossRate || "--"}`;
            }
          }else if(existsDelay){
               text = `${delay || ""}`;
          }else if(existsLoss){
               text = `${lossRate || ""}`;
          }
            text += "\n";
           if(existsflow){
              if(specialLine == 1) {
                text +=  "[" + this.$t("dash_Special_line") + "]";
              }

           }
          textOffsetY = 0
        
        } else if (!existsDelay && !existsLoss) {
          text += "\n";
          if(existsflow){
          
            if(specialLine == 1) {
                text +=  "[" + this.$t("dash_Special_line") + "]";
            }

            if(flowIntoSpeed || flowOutSpeed){
               text += `<${flowIntoSpeed || "--"}/${flowOutSpeed || "--"}>`;
            }

          }

          textOffsetY = 0
        } else if (existsDelay && existsLoss && existsflow) {

           if(existsDelay  && existsLoss){

             if(delay || lossRate){
                text = `${delay || "--"}/${lossRate || "--"}`;
             }
          }else if(existsDelay){
               text = `${delay || ""}`;
          }else if(existsLoss){
               text = `${lossRate || ""}`;
          }

          text += "\n";
          if(existsflow){
          
              if(specialLine == 1) {
                text += "[" + this.$t("dash_Special_line") + "]";
              }

            if(flowIntoSpeed || flowOutSpeed){
              text += `<${flowIntoSpeed || "--"}/${flowOutSpeed || "--"}>`;
            }
          }

            textOffsetY = 0
        } else {

          if(existsDelay  && existsLoss){
            if(delay || lossRate){
              text = `${delay || "--"}/${lossRate || "--"}`;
            }
          }else if(existsDelay){
               text = `${delay || ""}`;
          }else if(existsLoss){
               text = `${lossRate || ""}`;
          }

          text += "\n";
          if(existsflow){
             
              if(specialLine == 1) {
                text +=  "[" + this.$t("dash_Special_line") + "]";
              }

              if(flowIntoSpeed || flowOutSpeed){
                  text += `<${flowIntoSpeed || "--"}/${flowOutSpeed || "--"}>`;
              }
          }
          textOffsetY = 0
        }


      }
      if(data.scalingSplicingPoint === 1) {
        text = ''
      }
      return text

    },
    // 自定义缩略节点
    handleDomeNode() {
      //console.log('自定义缩略节点')
      // 中间圆点

       G6.registerNode('domNode', {
        // 添加状态处理
        setState(name, value, item) {
          const group = item.getContainer();
          const shape = group.get('children')[0]; // 获取圆形背景

          if (name === 'hover') {
            if (value) {
              shape.attr('fillOpacity', 0.8);
            } else {
              shape.attr('fillOpacity', 1);
            }
          }
        },

        draw:(cfg, group) => {
          let data = cfg.data
          let color = this.handleLinkColor(data)
          let container = group.addGroup()

          container.addShape('circle', {
            attrs: {
              x: 0,
              y: 0,
              r: 13,
              fill: '#5b8ff9',
              stroke: color,
              lineWidth: 1,
            },
          draggable: true, // 启用拖拽
         name: 'circle-shape' // 添加名称以便识别
          });
          // 中间文字
          container.addShape('text', {
            attrs: {
              x: 0,
              y: 0,
              text: data.allChildCount,
              textAlign: 'center',
              textBaseline: 'middle',
              fontWeight:'bold',
              fill: '#060D15',
              fontSize: 12
            },
            draggable: true, // 启用拖拽
            name: 'text-shape' // 添加名称以便识别
          });
          // 底部label
           container.addShape('text', {
            attrs: {
              x: 0,
              y: 26,
              text: cfg.label,
              textAlign: 'center',
              textBaseline: 'middle',
              fill: this.currentSkin == 1 ? '#5CA0D5' : '#515A6E',
              fontSize: 10
            },
          });
          // 右侧文字
            // 顺序：中断/劣化/正常/未知


        let formatCount = [
   {id:1,value:data.breakChildCount,color:this.configData.breakLineColor},
                            {id:2,value:data.lossChildCount,color:this.configData.degradationLineColor},
                            {id:0,value:data.normalChildCount,color:this.configData.normalLineColor},
                             {id:3,value:data.unknownchildcount,color:this.configData.unknownLineColor},
].filter(item => item.value > 0);
//console.log(formatCount,'formatCount....................')
// 如果只有正常则不显示右侧文字
if(formatCount.length === 1 && formatCount[0].id === 0) {
  return container
}

   let locX = 25

        formatCount.forEach((item,index) => {


             container.addShape('text', {
              attrs:{
                x:locX,
                y:0,
                text:item.value,
                textAlign:'center',
                textBaseline:'middle',
                fill:item.color,
                fontSize:10
              }

          })
         locX  = locX +  String(item.value).length * 6
          if(index !== formatCount.length - 1) {
            container.addShape('text', {
              attrs:{
                x:locX,
                y:0,
                text:'/',
                textAlign:'center',
                textBaseline:'middle',
                fill:'#02B8FD',
                fontSize:16
              }



          })
            locX  = locX + 8


          }



        })



          return container;
        },
      });
//
    },
      // 竖向坐标处理
  adjustCoordinate(width, height, x,y) {
    let height2 = height * 0.9;
    const normalizedX = x / width;
    const normalizedY = y / height2;
    const newX = normalizedY * width * 0.5;
    const newY = normalizedX * height2 * 2.5;

    return { x: newX, y: newY };
},
handleLinkClick(evt) {
  // console.log(evt.item._cfg.model.data,'边的点击事件')
   this.getTaskList(this.tabsId, evt);

},
// 边线鼠标移入事件
handleLinkOver(evt) {
  // console.log(evt,'鼠标移入的事件')
  this.graph.updateItem(evt.item, {
    style: {
      lineWidth:4
    }
  });
},
// 边线鼠标移除事件
handleLinkLeave(evt) {
  this.graph.updateItem(evt.item, {
    style: {
      lineWidth:1
    }
  });

},
    // 请求获链路详情数据
    async getTaskList(pathId, evt) {

    // 这里要单独处理不可用节点的显示方式

    let data = evt.item._cfg.model.data;

    let suspectNodeData =
    this.handleNotAvailableNodeToPreNodeIp(data , evt.currentTarget.cfg.data.nodes);

      // debugger
      //console.log(pathId, data,'pathId, data');

      const arr = data.target.split("_");
      let obj = {
        pageNo: 1,
        pageSize: 10,
        sourceIp: arr[0],
        topoId: pathId,
        targetIp: data.targetIp,
        preIp: data.sourceIp,
        linkIds:data.linkIds,
      };
      let url = '/pathTopo/getTaskList';
      if(this.showPeerToPeerLinkDetail){
          obj.sourceIp= data.sourceIp;
          obj.targetIp= data.curIp;
          obj.preIp= data.preIp;
          url = '/pathTopo/getPeerToPeerTaskList';
      }
      try {
        const res = await this.$http.PostJson(url, obj);
        const linkTable = res.data.records;
        let usea = {
          // preNodeIp: data.preIp,
          // targetIp: data.targetIp,
          preNodeIp: suspectNodeData.preIp,
          targetIp: suspectNodeData.targetIp,
          // 趋势图专用参数
          nodeIpTrend: suspectNodeData.curIp,
          nodeIp: data.curIp,
          sourceIp: arr[0],
          topoId: pathId,
          total: res.data.total,
          linkIds:data.linkIds,
          linkNodePairVos:data.linkNodePairVos
        };
        this.$emit('setPreIp',data.sourceIp)
        this.$emit("getLinkTable", linkTable, usea);
      } catch (error) {
     ;
      }
    },
    // 处理不可用节点方法
    // 处理BUG: 22403 【V4.6.1】【路径拓扑】不可用节点与其后可用节点之间没有显示数据，且点击不可用节点与前后节点之间的线路时没有正确加载链路
    handleNotAvailableNodeToPreNodeIp(nodeData , nodes){

      var resultData = {
        targetIp: nodeData.targetIp,
        preIp: nodeData.sourceIp,
        preNodeIp:nodeData.preIp,
        curIp:nodeData.curIp
      };
      // 判断是否有不可用节点
      var notAvailableArrays =  nodes.filter((item)=>{
         return item.data.suspect == true;
      });

      if(notAvailableArrays == null && notAvailableArrays.length == 0){
          return resultData;
      }
      var size = nodes.length;
      // 不可用节点的数据
      if(nodeData.suspect){
        var targetIp = nodeData.preIp;
        // 找到当前节点之前的是不可用节点
          var prevNodeIndex = -1;
           for(var index = size-1 ; index >=0 ; index--){
              if(nodes[index].data.nodeId == targetIp){
                prevNodeIndex = index;
                break;
              }
          }
          var prevNodeData = null;
          if(prevNodeIndex > 0 && prevNodeIndex <= size){
              prevNodeData = nodes[prevNodeIndex];
          }

        // 前面第一个节点是否是不可用节点
        if(prevNodeData  && prevNodeData.data.suspect){
          // 找到不可用节点后面的不是不可用节点的第一个节点数据
          for(var index = prevNodeIndex-1 ; index > 0 ; index -- ){
            if(nodes[index].data.suspect == false){
                prevNodeIndex = index;
                break;
            }
          }
          prevNodeData = (prevNodeIndex > 0 && prevNodeIndex <= size)? nodes[prevNodeIndex] : null;
          if(prevNodeData){
            resultData.preNodeIp = prevNodeData.data.value;
            resultData.preIp = prevNodeData.data.value;
          }

        }
        return resultData;

      }

      resultData.preIp = nodeData.preIp;
      resultData.preNodeIp = nodeData.preIp;
      // 当前节点的下一个节点是否是不可用节点
       var nextNodeIndex = -1;
       var targetIp = nodeData.targetIp;
      for(var index = size-1 ; index >=0 ; index--){
          if(nodes[index].data.nodeId == targetIp){
            nextNodeIndex = index;
            break;
          }
      }
      var nextNodeData = null;
      if(nextNodeIndex > 0 && nextNodeIndex <= size){
          nextNodeData = nodes[nextNodeIndex];
      }

      // 后面的第一个节点是否是不可用节点
      if(nextNodeData && nextNodeData.data.suspect){

        // 找到不可用节点后面的不是不可用节点的第一个节点数据
        for(var index = nextNodeIndex+1 ; index < size ; index ++ ){
          if(nodes[index].data.suspect == false){
              nextNodeIndex = index;
              break;
          }
        }
        nextNodeData = (nextNodeIndex > 0 && nextNodeIndex <= size)? nodes[nextNodeIndex] : null;
        if(nextNodeData){
          resultData.targetIp = nextNodeData.data.value;
          resultData.curIp = nextNodeData.data.value;
        }
      }
      return resultData;
    },
   
},
created() {
  //console.log(window.name,'123')
  if(window.name.indexOf('bigscreen') > -1) {
    this.isBigScreen = true
  }

    // this.containerWidth = document.documentElement.clientWidth - 60 //实时屏幕宽度
    // this.containerHeight = document.documentElement.clientHeight //实时屏幕高度


},
mounted() {
  const screenWidth = window.screen.width * 0.98
  



  this.modalWidth = screenWidth > localStorage.getItem('modalWidth') ? screenWidth * 0.98 : localStorage.getItem('modalWidth') * 0.98

  this.iframeHeight = window.innerHeight * 1; // 根据需要调整比例
 
   
  window.pathTopoInstance = this;
  //  console.log(top.document.getElementById('container'),document.getElementById('container'),123)
  if(this.layoutModel == 2) {
    this.handleStarNodes()


  }else {
     this.handleNodes()

  }

debugger
  console.log(this.showType , "---------------show")

  this.handleLink()
  // 自定义节点
  this. handleDomeNode()

  this.initG6()

},
beforeDestroy() {
  // Clean up global reference when component is destroyed
  delete window.pathTopoInstance;
  // 清理图实例和相关资源
    this.cleanupGraph();
    this.clearAnimations();

  
}
}
</script>
<style scoped lang="less">
.topo-contaienr {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.mask-topo {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(55, 55, 55, 0.6);
  z-index: 1000;
}
.container {
  position: relative;

  .ellipsis-modal {
    position: absolute;
    top: 40px;
    left: 5%;
    width: 90%;
    height: 90%;
    padding: 42px 16px 0 16px;
    z-index: 1001;
    background-color: #08101a;
    .detail-logo-box {
      width: 150px;
      height: 28px;
      background-color: #08101a;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 9999;
    }
    .jtopo-box {
      width: 100%;
      height: 100%;
    }
    .modal-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 42px;
      line-height: 42px;
      text-align: left;
      display: flex;
      padding: 0 12px;
      justify-content: space-between;

      .title-text {
        font-size: 18px;
        color: #00ffee;
        font-weight: 700;
        margin-right: 20px;
      }
    }
  }
}
.logo-box {
  width: 160px;
  height: 28px;
  // background-color: #061824;
  background-color: var(--pathtopo_logo_box_bg_color, #061824);
  // border: 1px solid pink;
  position: fixed;
  left: 60px;
  bottom: 0;
  z-index: 666;
  display: flex;
  align-items: center;
  justify-content: center;
  .text {
    width: 90%;
    height: 28px;
    line-height: 28px;
    // color: #5ca0d5;
    color: var(--pathtopo_logo_box_font_color, #5ca0d5);
    font-weight: 700;
    overflow: hidden; /* 确保超出容器的文本被裁剪 */
    white-space: nowrap; /* 防止文本换行 */
    text-overflow: ellipsis; /* 在文本末端显示省略号 */
  }
}
/deep/.g6-minimap {
  position: absolute;
  bottom: 10px;
  right: 0;
  // border: 1px solid #e2e2e2;
  background: #060d15;
  border: 1px solid #06324d;
  // opacity: 0.8;
  .g6-minimap-viewport {
    border: unset;
    background-color: #5ca0d5;
    opacity: 0.3;
  }
}
</style>