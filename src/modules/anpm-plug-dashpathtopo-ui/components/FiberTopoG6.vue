<!-- 纤维图组件 -->
<template>
  <div class="fiber-topo" id="fiberTopo"></div>
</template>

<script>
import G6 from '@antv/g6';

 //   fibreType 0 横向   1竖向

export default {
name: '',
components: {},
props:['fiberData','fiberTopoHeight','fiberTopoWidth','configData','fibreType'],
data() {
return {
  currentSkin:sessionStorage.getItem('dark') || 1,
    fiberStage:null,
    fiberLayer:null,
    fiberTotoData:{
        nodeList:[],
        linkList:[]
    },
    handlerNodeData:{},
    allNode:[],
    graph:null,
      labelTooltip:null,
      isBigScreen:false,

}
},
computed: {},
watch: {
    fiberData: {
       immediate: true,
        handler() {
           if (!this.fiberData) return; 
            // console.log(this.fiberData,'fiberDatafiberDatafiberData2')
            if(this.graph) {
              this.graph.destroy()

            }
            if(this.fibreType == 0) {
              this.initTreeGraph()

            }else {
              this.initDendrogram()

            }


        },
        deep:true,


    }

},
methods: {
  // 动态计算节点间的距离
  handleNodeSep(width) {
    let length = ''
     let childCount = 0
         this.fiberData.children ? this.fiberData.children.forEach((item,i) => {
            if(item.children && item.children.length > 0) {
                childCount += item.children.length
            }else {
                childCount += 1

            }


        }) : childCount = 0
        length = width/(childCount-1)
        if(length > 300) {
          length = 300
        }else if(length < 30) {
          length = 30
        }


    return length

  },
  // 计算数据层数
  calculateTreeDepth(treeData) {
  function getMaxDepth(node, depth = 1) {
    let maxDepth = depth;
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        const childDepth = getMaxDepth(child, depth + 1);
        maxDepth = Math.max(maxDepth, childDepth);
      }
    }
    return maxDepth;
  }

  if (!treeData || !Array.isArray(treeData) || treeData.length === 0) {
    return 0;
  }

  let maxDepth = 0;
  for (const node of treeData) {
    const depth = getMaxDepth(node);
    maxDepth = Math.max(maxDepth, depth);
  }

  return maxDepth;
},

  // 计算连线距离
  handleRankSep(fiberTopoWidth) {
    let length = 0
  let count =  this.calculateTreeDepth([this.fiberData])
  // 两层，距离为屏幕宽度减去两边预留的距离
  if(count === 2) {
    length =fiberTopoWidth - fiberTopoWidth*0.4

  }else if(count === 3) {
    length = (fiberTopoWidth - fiberTopoWidth*0.4)/2
  }
  return length

  },
  // 计算连线高度距离

    // 处理颜色
    handleColor(state) {
      // console.log(state,'状态')
        let color = ''
        if(state === 1000) {
            color = this.configData.normalLineColor
        }else if(state == 1) {
            color = this.configData.breakLineColor
        }else if (state == undefined) {
          color = this.currentSkin == 1 ? '#ccc': '#808695' ; 
          // color = '#ccc';

        }else {
            color = this.configData.degradationLineColor
        }

        return color
    },
    // handelName(data) {
    //   // console.log(data,'data')
    //   let name = data.name
    //   if(data.name.length > 20) {
    //     name = data.name.substring(0, 15) + '...'
    //   }

    //   return name

    // },
    // tooltip提示方法
    initTooltip() {
      this.labelTooltip = new G6.Tooltip({
        // offsetX and offsetY include the border width of the graph
        offsetX: 10,
        offsetY: 10,
        // the types of items that allow the tooltip show up
        itemTypes: ['node'],
        // custom the tooltip's content
        getContent: (e) => {

          let data = e.item.getModel()
          let bgColor = this.currentSkin == 1 ? '#16436b' : '#fff'
          let fontColor = this.currentSkin == 1 ? '#fff' : '#515A6E'
          let boxShadow = this.currentSkin == 1 ? 'unset' : '0px 0px 8px 1px rgba(0,0,0,0.16)'
          let borderColor = this.currentSkin == 1 ? '#16436b' : '#e2e2e2'


          const tooltipDiv = document.createElement('div')
          tooltipDiv.style.cssText = `
            min-width: 80px;
            background-color: ${bgColor};
            border: 1px solid ${borderColor};
            border-radius: 4px;
            box-shadow: ${boxShadow};
            padding: 10px 8px;
            margin:0px;
          `

          tooltipDiv.style.minWidth = '80px'
            // tooltipDiv.style.background = 'pink'
          // tooltipDiv.style.height = '20px'
          // tooltipDiv.style.padding = '4px';
          tooltipDiv.innerHTML = `
            <div style="color:${fontColor};">${data.name}</div>
          `

          return tooltipDiv

        }
      })
  },

    initTreeGraph() {
        console.log(this.fiberData,'fiberDatafiberDatafiberData')
        if (!this.fiberData) return; // Exit if no data


        let rankSep = this.handleRankSep(this.fiberTopoWidth)
      let nodeSep =  this.handleNodeSep(this.fiberTopoHeight)


        this.graph = new G6.TreeGraph({
            container:'fiberTopo',
            width:this.fiberTopoWidth,
            height:this.fiberTopoHeight,
            plugins:[this.labelTooltip],
                animate: false,  // 关闭动画
                minZoom: 0.2,   // 最小缩放比例
                maxZoom: 5,     // 最大缩放比例
            modes: {
              default: [
                {
                  type: 'collapse-expand',
                  onChange: function onChange(item, collapsed) {
                    const data = item.getModel();
                    data.collapsed = collapsed;
                    return true;
                  },
                },
                'drag-canvas',
                 {
                    type: 'zoom-canvas',
                    sensitivity: 1.5,
                    enableOptimize: false,
                    throttle: 16,  // 添加节流，16ms 约等于 60fps
                    minZoom: 0.2,
                    maxZoom: 5,
                    optimizeZoom: 0.1,
                  },
                'zoom-canvas',
                'drag-node'
              ],
            },
            defaultNode: {
              size: 10,
              anchorPoints: [
                [0, 0.5],
                [1, 0.5],
              ],
            },
            defaultEdge: {
              type: 'cubic-horizontal',
            },
            layout: {
              type: 'dendrogram',
              direction: 'LR', // H / V / LR / RL / TB / BT
              nodeSep,
              rankSep,
            },
        })
          this.graph.node((node) => {

           let color = this.handleColor(node.state)
          //  let labelName = this.handelName(node)


          //  console.log(color)
           return {
              label: node.shortName || node.name,
              data:node,
              style: {
                fill:color,
                stroke:color,
                // lineWidth:0,
              },
              labelCfg: {
                offset: 10,
                style: {
                    fill:color,
                },
                position: node.children && node.children.length > 0 ? 'left' : 'right',
              },
            };
          });

          this.addEventListenerGraph()

         this.graph.data(this.fiberData);

            this.graph.render();
            this.graph.fitView();
            this.handleLink()

    },
    // 竖向布局
    initDendrogram() {
      if (!this.fiberData) return; // Exit if no data

      let rankSep = this.handleRankSep(this.fiberTopoHeight)
      let nodeSep = this.handleNodeSep(this.fiberTopoWidth)
       this.graph = new G6.TreeGraph({
      container: 'fiberTopo',
      width:this.fiberTopoWidth,
      height:this.fiberTopoHeight,
      plugins:[this.labelTooltip],
      linkCenter: true,

      modes: {
        default: [
          {
            type: 'collapse-expand',
            onChange: function onChange(item, collapsed) {
              const data = item.get('model');
              data.collapsed = collapsed;
              return true;
            },
          },
          'drag-canvas',
          'zoom-canvas',
          'drag-node'
        ],
      },
      defaultNode: {
        size: 10,
        anchorPoints: [
          [0, 0.5],
          [1, 0.5],
        ],
      },
      defaultEdge: {
        type: 'cubic-vertical',
      },
      layout: {
        type: 'dendrogram',
        direction: 'TB', // H / V / LR / RL / TB / BT
        nodeSep,
        rankSep
      },
    });

    this.graph.node((node) => {
      let position = 'right';
      let rotate = 0;
      if (!node.children) {
        position = 'bottom';
        rotate = Math.PI / 2;
      }
      let color = this.handleColor(node.state)
      // let labelName = this.handelName(node)


          //  console.log(color)
           return {
              label: node.shortName || node.name,
              data:node,
              style: {
                fill:color,
                stroke:color,
                // lineWidth:0,
              },
              labelCfg: {
                offset: 5,
                style: {
                    fill:color,
                    rotate,
                    textAlign:'start',
                    fontSize:12
                },
                position,
              },
            };
      // return {
      //   label: node.id,
      //   labelCfg: {
      //     position,
      //     offset: 5,
      //     style: {
      //       rotate,
      //       textAlign: 'start',
      //     },
      //   },
      // };
    });
   this.addEventListenerGraph()

    this.graph.data(this.fiberData);
    this.graph.render();
    this.graph.fitView();
    this.handleLink()

    },

    // 处理边颜色的方法
    handleLink() {
      // debugger
      const allEdges = this.graph.getEdges();

      allEdges.forEach((edge) => {
        console.log(edge,'bian颜色处理')
        const sourceNode = edge.getSource();
        const targetNode = edge.getTarget();
        const color = targetNode.getModel().style.fill
        this.graph.updateItem(edge, {
          // 边颜色
          style: {
            stroke: color
          },
        });

      });

    } ,

    // 所有事件监听的方法
    addEventListenerGraph() {
      this.graph.on('node:click', (e) => {
        console.log(e.item.getModel())
        let node = e.item.getModel().data
        if(!node.children) {
            node.name = node.destIp
           let param = {
                    data:node
                }
          this.$emit('nodeClick',param)
        }
      })

}

},
created() {
    console.log('纤维图组件渲染了')


},
mounted() {
    // 处理label度tooltip提示

        this.initTooltip()
  if(this.fibreType == 0) {
    this.initTreeGraph()

  }else {
    this.initDendrogram()

  }



},
}
</script>
<style scoped lang='less'>
.fiber-topo {
  width: 100%;
  height: 100%;
}
</style>
