/**
 * jtopo工具栏
 */
import $ from "../../../common/jtopo/jquery.min.js";
import "../../../common/jtopo/jtopo-s.js";
import ipv6Format from "@/common/ipv6Format";

import noImg from "../../../common/loading/noData.png";
// import jiedianImgG from "../../../common/jtopo/img/jiedian_green.png";
// import jiedianImgG from "../../../common/jtopo/img/icon-d2.png";
// import jiedianImgG from "../../../common/jtopo/img/icon-d3.png";
// import tanzhenImg from "../../../common/jtopo/img/tanzhen.png";
// import tanzhenImg from "../../../common/jtopo/img/icon-d.png";
// import jiedianImgH from "../../../common/jtopo/img/jiedian_grey.png";
// import jiedianImgH from "../../../common/jtopo/img/icon-d3.png";
// import weizhi from "../../../common/jtopo/img/icon-d6.png";
// import jiedianImgH from "../../../common/jtopo/img/icon-d2.png";
// import jiedianImgH2 from "../../../common/jtopo/img/icon-d22.png";
// import jiedianImgR from "../../../common/jtopo/img/jiedian_red.png";
// import jiedianImgR from "../../../common/jtopo/img/icon-d5.png";
// import jiedianImgR2 from "../../../common/jtopo/img/icon-d55.png";
// import jiedianImgR3 from "../../../common/jtopo/img/icon-d555.png";
// import jiedianImgY from "../../../common/jtopo/img/jiedian_yell.png";
// import jiedianImgY from "../../../common/jtopo/img/icon-d4.png";
// import jiedianImgY2 from "../../../common/jtopo/img/icon-d44.png";
// import jiedianImgY3 from "../../../common/jtopo/img/icon-d444.png";
// import mubiaoImg from "../../../common/jtopo/img/icon-d1.png";
// import serverInvalidImg  from "../../../common/jtopo/img/suolvtu.png";

// 探针
import tanzhenImg from "../../../common/jtopo/img/icon-d.png";
import serverInvalidImg from "../../../common/jtopo/img/suolvtu.png";

// 未知*
import weizhi from "../../../common/jtopo/img/icon-d6.png";
// 实心圈*
import jiedianImgH from "../../../common/jtopo/img/icon-d2.png";
import jiedianImgG from "../../../common/jtopo/img/icon-d3.png";
import jiedianImgR from "../../../common/jtopo/img/icon-d5.png";
import jiedianImgY from "../../../common/jtopo/img/icon-d4.png";
// 空心圈
import jiedianImgY3 from "../../../common/jtopo/img/icon-d444.png";
import jiedianImgR3 from "../../../common/jtopo/img/icon-d555.png";
// 菱形*
import mubiaoImg from "../../../common/jtopo/img/icon-d1.png";
import jiedianImgH2 from "../../../common/jtopo/img/icon-d22.png";
import jiedianImgY2 from "../../../common/jtopo/img/icon-d44.png";
import jiedianImgR2 from "../../../common/jtopo/img/icon-d55.png";
//方块
import jiedian1 from "../../../common/jtopo/img/icon-jhj-01.png";
import jiedian2 from "../../../common/jtopo/img/icon-jhj-02.png";
import jiedian3 from "../../../common/jtopo/img/icon-jhj-04.png";
import jiedian4 from "../../../common/jtopo/img/icon-jhj-03.png";
//物理图标
import fanghuoqiangG from "../../../common/jtopo/img/icon_fanghuoqiang_g.png";
import fanghuoqiangR from "../../../common/jtopo/img/icon_fanghuoqiang_r.png";
import fanghuoqiangY from "../../../common/jtopo/img/icon_fanghuoqiang_y.png";
import fuwuqiG from "../../../common/jtopo/img/icon_fuwuqi_g.png";
import fuwuqiR from "../../../common/jtopo/img/icon_fuwuqi_r.png";
import fuwuqiY from "../../../common/jtopo/img/icon_fuwuqi_y.png";
import jiaohuanjiG from "../../../common/jtopo/img/icon_jiaohuanji_g.png";
import jiaohuanjiR from "../../../common/jtopo/img/icon_jiaohuanji_r.png";
import jiaohuanjiY from "../../../common/jtopo/img/icon_jiaohuanji_y.png";
import luyouqiG from "../../../common/jtopo/img/icon_luyouqi_g.png";
import luyouqiR from "../../../common/jtopo/img/icon_luyouqi_r.png";
import luyouqiY from "../../../common/jtopo/img/icon_luyouqi_y.png";

import pathChakanW from "../../../common/jtopo/img/path_chakan_w.png";
import pathChakanY from "../../../common/jtopo/img/path_chakan_y.png";
import pathXXW from "../../../common/jtopo/img/path_xx_w.png";
import pathXXY from "../../../common/jtopo/img/path_xx_y.png";
import pathSXW from "../../../common/jtopo/img/path_sx_w.png";
import pathSXY from "../../../common/jtopo/img/path_sx_y.png";
import pathDaochuW from "../../../common/jtopo/img/path_daochu_w.png";
import pathDaochuY from "../../../common/jtopo/img/path_daochu_y.png";
import pathFangdaW from "../../../common/jtopo/img/path_fangda_w.png";
import pathFangdaY from "../../../common/jtopo/img/path_fangda_y.png";
import pathHuanyuanW from "../../../common/jtopo/img/path_huanyuan_w.png";
import pathHuanyuanY from "../../../common/jtopo/img/path_huanyuan_y.png";
import pathSuoxiaoW from "../../../common/jtopo/img/path_suoxiao_w.png";
import pathSuoxiaoY from "../../../common/jtopo/img/path_suoxiao_y.png";
import pathTuliW from "../../../common/jtopo/img/path_tuli_w.png";
import pathTuliY from "../../../common/jtopo/img/path_tuli_y.png";
import pathXxszW from "../../../common/jtopo/img/path_xxsz_w.png";
import pathXxszY from "../../../common/jtopo/img/path_xxsz_y.png";

var foldOpenStatus = {};
var maxy = 100;
var yx = 150;
var istop = true;

/**
 * 画布渲染主方法
 * @param dataList 渲染数据
 * @param type xx星形结构  sx树形结构
 * @param divId 创建画布的容器id
 * @param msgModal 需要展示的数据字段s
 * @returns {c}
 */
var keepIsdarkSkin;
export function newJtopo(datas, isdarkSkin, isbigscreen) {
    // console.log(dataList,"---dataList-");
    // var dataList = data;
    // console.log(isdarkSkin,'xxxxx')
    maxy = 100;
    yx = 150;
    keepIsdarkSkin = isdarkSkin;
    var dataList = datas.dataList;
    var type = datas.topoType;
    var contId = datas.contId;
    var msgModalData = datas.msgModal;
    var isDashBoard = datas.isDashBoard;
    var showWarning = datas.showWarning;
    var ispath = datas.ispath;
    var thistopo = datas.thistopo;
    // var permissionObj = datas.permissionObj;
    //点位
    var nodelist = setDataList(dataList.nodeList);
    //线路
    let ipCurIp = [];
    dataList.nodeRouteList.map(x => {
        let param = {
            state: '',
            curIp: ''
        }
        if (x.state == 1) {
            param.state = x.state;
            param.curIp = x.curIp;
        } else if (x.state == 2 || x.state == 3) {
            param.state = x.state;
            param.curIp = x.curIp;
        }
        ipCurIp.push(param)
    })
    let newLinkList = dataList.nodeRouteList.filter(item => {
        ipCurIp.filter(e => {
            if (item.curIp == e.curIp) {
                if (e.state == 1) {
                    if (item.targetIp == '*') {
                        item.linkState = 1;
                        item.state = e.state;
                        item.faultLink = '';
                    }
                } else if (e.state == 2 || e.state == 3) {
                    if (item.targetIp == '*') {
                        item.linkState = 2;
                        item.state = e.state;
                        item.faultLink = '';
                    }
                }
                console.log(item)
            }
        })
        return item;
    })
    console.log(JSON.stringify(newLinkList), 222222)
    var linklist = dataList.nodeRouteList;
    let d1 = new Date().getTime()
    //初始化容器
    //创建画布对象
    const canvasW = document.body.clientWidth;
    const canvasH = document.body.clientHeight;


    if (!isDashBoard) {
        $("#" + contId).html(`<div  style="width:100%;height:100%" >
<div name="topo_tips" class="panel-default" style="color: white;background: rgba(51, 122, 183, 0.5);position: absolute;" id="topoTips">
<div class="panel-body" style=""></div></div><div id="canvasdiv">
<canvas width= `+ (canvasW - 20) + ` height=` + (canvasH - 60) + ` id="canvas" style="border-top: 1px solid #d8dbe6;"></canvas></div></div>`);
    } else {
        $("#" + contId).html(`<div  style="width:100%;height:100%" ><canvas width= ` + (canvasW - 20) + ` height=` + (canvasH - 60) + ` id="canvas" ></canvas></div>`);
    }
    $("#clickProp").html("").hide();
    var canvas = document.getElementById('canvas');
    var stage = new JTopo.Stage(canvas);
    stage.wheelZoom = 1.2; // 设置鼠标缩放比例
    if (!isDashBoard) {
        // stage.eagleEye.visible = true;//鹰眼
        showJTopoToobar(stage, datas, isdarkSkin);
    }
    var scene = new JTopo.Scene(stage);
    scene.alpha = 1;//透明度
    scene.backgroundColor = isbigscreen ? '26,34,46' : isdarkSkin == 1 ? '26,34,46' : "255,255,255";
    if (nodelist && nodelist.length == 0) {
        let imgdata = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAkQAAAGQCAYAAABPp3/+AAAgAElEQVR4Xu3df6ynWV0f8PN8Z7l3hmwKYmFtLRFSGpP+ASikP7Qy34WoWUVLWyHamBZTs/YX1RWZO9Oa7OwfZPYOCKi0kW0bMI2RlBogbaUx1fkO2mqTEn60Gikmktiiy2qztMjMvbv3Ps2dXegO3Lvzfb5zzvOcz3Ne8w9/7POc8zmv99nZN/dnl/whQGCQwHJ3/+Vd339bv+i+NfX9c1Pqn5dSd/S/D/epe7jr06dS133o1Kn+13/5x7Z/a9DiHiZAgACBSQS6SXa1KYGAAq+4tP8Ni0V/LqX0vQPGf+/hYXf5wxe2PjrgHY8SIECAwMgCCtHI4LaLKbC8dO1it1i8qU/pmUNP0KX0hf7w8C2rC2cuDn3X8wQIECAwjoBCNI6zXQILnN29/q4udffe9hG6brU6t3X3ba9jAQIECBDILqAQZSe14JwElrt7fe7zrHa2/XuXG9V6BAgQuE0BfzHfJqDX5yuwvLx/JfX9MvcJ+9Q/dHXn9A/lXtd6BAgQILC5gEK0uZ03Zyxw9DVDabG4v9gRDw8f8DVFxXQtTIAAgcECCtFgMi/MXeDou8lOLfpf2+QLqNe16VL6zONbWy//1fu631/3Hc8RIECAQDkBhaicrZWDCix3935+4LfWb3ZSHyXazM1bBAgQKCCgEBVAtWRcgVe9de/PHxyk3xzpBJ9e7Wy/cKS9bEOAAAECTyOgELkeBJ4isNzd/zsp9f9iLJS+S6+7em77fWPtZx8CBAgQOF5AIXIzCDy1ED24929Sl/7GaCh9et/q/PbrRtvPRgQIECBwrIBC5GIQeIrA2d29j3cpvXgslD6lT1zd2X7JWPvZhwABAgR8hMgdIHBLgeXu9T9Iqbvrlg9me6B/eLVz+muyLWchAgQIENhIwEeINmLz0lwFSvxk6ltZ+cnVtxLyzwkQIFBeQCEqb2yHQAI+QhQoLKMSIEAgo4BClBHTUvEFfA1R/AydgAABApsIKESbqHlntgJL32U222wdjAABAk8noBC5HwSeIuDnELkOBAgQaFNAIWozd6c+QcBPqnY1CBAg0KaAQtRm7k79NAJ+l5nrQYAAgfYEFKL2MnfiWwj4bfeuCAECBNoTUIjay9yJ1xBYXrp2MS0W96/x6GaP+E33m7l5iwABAoUEFKJCsJaNL7C8vH8l9f0y90n61D90def0D+Ve13oECBAgsLmAQrS5nTcbECjxk6v9ZOoGLo4jEiAQTkAhCheZgccWOLt7/V1d6u697X27brU6t3X3ba9jAQIECBDILqAQZSe14BwFjr6mqFss3tSn9Myh5+tS+kJ/ePiW1YUzF4e+63kCBAgQGEdAIRrH2S4zEDj67rPFoj+XUvreAcd57+Fhd/nDF7Y+OuAdjxIgQIDAyAIK0cjgtosvsNzdf3nX99/WL7pvTX3/3JT656XUHf3vw33qHu769KnUdR86dar/9V/+se3fin9iJyBAgMD8BRSi+WfshAQIECBAgMAtBBQiV4QAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************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'
        $("#canvasdiv").html(`<div style="position: absolute;text-align: center;line-height: 40px;width: 100%;margin-top:100px;">
         <img src="`+ imgdata + `" 
         style=" left:40%;
             width: 290px;
             height: 200px;" />
             <div >暂无数据，请配置拓扑图</div>
      </div>`);
        return null;
    }
    // if(nodelist&&nodelist.length==0){
    //     var node = new JTopo.Node("暂无数据，请配置拓扑图！");
    //     node.setImage(noImg, true);
    //     node.setSize(580, 400);
    //     node.fontSize = 30
    //     node.showSelected = false;
    //     node.dragable = false;
    //     scene.add(node);
    //     stage.centerAndZoom();
    //     return null;
    // }
    let maxNodeNum = 11;
    let nodearr = [];
    var defLoc = 0;
    var tdata0 = {};
    tdata0.isDashBoard = isDashBoard;
    tdata0.unfoldFlag = "0";
    let screenArr = [];
    // return scene;

    if (nodelist.length > 0) {
        //创建节点
        screenArr = []
        for (var i = 0; i < nodelist.length; i++) {
            var thisData = nodelist[i];
            defLoc = nodelist[i].defLoc;
            let arr = [];
            //控制告警节点是否显示
            //节点图片
            // let nodeimg = null;
            // let nodeimg = getJtopoNodeImgByValue(thisData.type,ispath,null);
            let nodeimg = getJtopoNodeImgByValue(thisData.type, ispath, null, null, null, thisData.interruptShowGrayNode);
            var text = '';
            var id = thisData.id;
            var tdata = {};
            tdata.isDashBoard = isDashBoard;
            //判断节点字段的展示
            for (let msg in msgModalData) {
                if (msgModalData[msg].type == 0 && msgModalData[msg].isShow) {
                    if (thisData[msgModalData[msg].filedName]) {
                        if (text) {
                            text += ("\n" + thisData[msgModalData[msg].filedName])
                        } else {
                            text = thisData[msgModalData[msg].filedName];
                        }
                    }
                }
            }
            screenArr.push(thisData.locY)
            tdata.unfoldFlag = thisData.unfoldFlag;
            tdata.tztype = thisData.type;
            tdata.defLoc = thisData.defLoc;
            tdata.locX = thisData.locX;
            tdata.locY = thisData.locY;
            if (thisData.suspect) {
                text = text + "(不可信)";
            }
            let node = addNode(scene, text, nodeimg, id, tdata, ispath, isdarkSkin, isbigscreen);
            arr.push(node);
            nodearr[i] = arr;
        }
        sessionStorage.setItem('maxNum', Math.max(...screenArr) + 100)
        //节点连线
        for (var i = 0; i < linklist.length; i++) {
            var thisNode = null;
            var lastNode = null;
            for (var j = 0; j < nodearr.length; j++) {
                if (linklist[i].source == nodearr[j][0].nodeId) {
                    thisNode = nodearr[j][0];
                }
                if (linklist[i].target == nodearr[j][0].nodeId) {
                    lastNode = nodearr[j][0];
                }
            }
            // var iscont = false;
            // for(var j=0; j<linklist.length; j++){
            //     if(linklist[i].source == linklist[j].target&&linklist[j].source == linklist[i].target){
            //         iscont= true;
            //         break;
            //     }
            // }
            // if(iscont){
            //     continue;
            // }
            if (thisNode != null && lastNode != null) {
                var tdata = {};
                if (linklist[i].state == 1 || linklist[i].state == 2 || linklist[i].state == 3) {
                    if (lastNode.text == '*') {
                        lastNode.tztype = 2
                    }
                }
                var state = showWarning ? linklist[i].state : null;
                tdata.gaojing = showWarning ? linklist[i].faultLink : null;
                tdata.linkIds = linklist[i].linkIds;
                tdata.faultIds = linklist[i].faultIds;
                tdata.state = showWarning ? linklist[i].state : null;
                tdata.preIp = linklist[i].preIp;
                tdata.curIp = linklist[i].curIp;
                tdata.text = linklist[i].id;
                tdata.faultNum = linklist[i].faultLink;
                tdata.destType = linklist[i].destType == 0 ? 0 : 1;
                tdata.linkState = linklist[i].linkState;
                tdata.interruptShowGrayNode = linklist[i].interruptShowGrayNode;
                var faultDesc = linklist[i].faultDesc;
                // var  faultCont = "**********到*********中断；疑似故障节点:**************，影响1条路径，其目标节点分别为：*********(华为loopback)#$**********到*********中断；疑似故障节点:**************，影响1条路径，其目标节点分别为：*********";
                var faultarr = faultDesc && faultDesc.split("#$");
                tdata.faultCont = faultarr;
                // let nodeimg = getJtopoNodeImgByValue(lastNode.tztype,ispath,state,tdata.destType,tdata.linkState);
                let nodeimg = getJtopoNodeImgByValue(lastNode.tztype, ispath, state, tdata.destType, tdata.linkState, tdata.interruptShowGrayNode);
                lastNode.setImage(nodeimg, true);
                lastNode.imgSrcS = nodeimg;    // 在创建节点的时候添加自定义属性
                lastNode.serializedProperties.push("imgSrcS"); // 把自定义属性加入进节点的属性组
                addLink(scene, thisNode, lastNode, type, tdata);
            }
            // scene.doLayout(JTopo.layout.TreeLayout('right', 80, 200));

        }

    }
    // 第一层以当前节点名称为 key 区分折叠状态
    var indexnode = [];
    var nodearrs = [];
    var childs = scene.childs;
    for (var j = 0; j < childs.length; j++) {
        if (childs[j].elementType == 'node' && childs[j].unfoldFlag == 1) {
            childs[j].setImage(serverInvalidImg, true);
            childs[j].isshow = false;
            var thisNode = childs[j].nodeId;
            var status = [];
            var tarlink = childs[j].outLinks;
            for (var i = 0; i < tarlink.length; i++) {
                status[i] = { node: childs[j].visible, link: tarlink[i].visible };
                foldOpenStatus[thisNode] = status;
                // 收缩下一层级节点
                if (childs[j].outLinks.length != 0) {
                    dbfold(childs[j].outLinks, foldOpenStatus[thisNode][i]);
                }
            }
        }
        if (childs && childs[j] && childs[j].inLinks && childs[j].elementType == 'node' && childs[j].inLinks.length == 0) {
            indexnode.push(childs[j])
        }
    }
    var xx = 100;
    var yy = 100;
    // try {
    //     if(indexnode.length>1){
    //         throw "开始节点大于一个，不符合规定型号"
    //     }
    //     throw "自定义布局";
    //     // 树形布局
    //     if(type=='sx'){
    //         scene.doLayout(JTopo.layout.TreeLayout('right', 150, 120));
    //     }else if(type=='xx') {
    //         scene.doLayout(JTopo.layout.CircleLayout(500));
    //     }
    // }catch(err){
    //     console.log("throw:",err);
    //     // for (var i = 0; i < indexnode.length; i++) {
    //     //     yy=maxy+yx
    //     //     var arrs = [];
    //     //     indexnode[i].setLocation(xx,yy);
    //     //     istop = true;
    //     //     layoutCircle(indexnode[i].outLinks,arrs,xx,yy);
    //     // }
    // }
    let d2 = new Date().getTime()
    console.log(d2 - d1, "----------耗时ms-----");
    // scene.doLayout(JTopo.layout.TreeLayout('right', 100, 200));

    stage.centerAndZoom();
    //getPathTopoData(scene);
    return scene;
}

var iii = 0;
/**
 * 布局
 * @param {ht.Node} root - 根节点
 */
function layoutCircle(outLinks, arrs, x, y) {
    if (!outLinks) {
        if (istop) {
            maxy = y;
        } else {
            maxy = y + yx;
        }
        return;
    }
    istop = true;
    let xx = x + yx;
    for (var i = 0; i < outLinks.length; i++) {
        // 获取到所有的孩子节点对象数组
        var children = outLinks[i].nodeZ;
        // console.log(children.nodeId,iii++);
        if (children.nodeId == '167772308_20_8') {
            ;
        }
        let yy = y + yx * i;
        for (var j = 0; j < arrs.length; j++) {
            if (arrs[j].nodeId == children.nodeId) {
                return;
            }
            if (arrs[j].x == xx && arrs[j].y == yy) {
                yy = yy + yx;
            }
        }
        // if(children.nodeId == '167772308_10_7'){
        // }
        // console.log(iii++,"-------");
        children.setLocation(xx, yy);
        arrs.push(children)
        if (maxy < yy) maxy = yy;
        // if(children.outLinks.length==0)return;
        layoutCircle(children.outLinks, arrs, xx, yy);
    }
}


/**
 *创建node节点
 */
function addNode(scene, text, img, id, tdata, ispath, isdarkSkin, isbigscreen) {
    let text2 = ipv6Format.abbreviateIPv6(text)
    var node = new JTopo.Node(text2);
    var node = new JTopo.Node(text);
    node.setSize(30, 30);
    node.isshow = true;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("isshow"); // 把自定义属性加入进节点的属性组
    //点击收缩功能
    if (tdata && !tdata.isDashBoard && ispath) {
        node.addEventListener("dbclick", function (event) {
            if (node.isshow) {
                node.setImage(serverInvalidImg, true);
                node.isshow = false;
            } else {
                node.setImage(node.imgSrcS, true);
                node.isshow = true;
            }
            foldOpen(event, node);
            // 此事件不可删除
        });
    }
    let fontColor = isbigscreen ? '#D1E4FF' : isdarkSkin == 1 ? '#D1E4FF' : "#333"
    node.paintText = function (a) {
        a.beginPath(),
            a.font = this.font,
            a.wrapText(this.text || '', this.height / 2, this.height, fontColor);
        a.closePath()
    };
    //创建图片类型
    node.imgSrcS = img;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("imgSrcS"); // 把自定义属性加入进节点的属性组
    node.tztype = tdata.tztype;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("tztype"); // 把自定义属性加入进节点的属性组
    node.nodeId = id;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("nodeId"); // 把自定义属性加入进节点的属性组
    node.unfoldFlag = tdata.unfoldFlag;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("unfoldFlag"); // 把自定义属性加入进节点的属性组
    node.setImage(img, true);
    node.prop = { "name": text, "nodeId": id, "locX": tdata.locX, "locY": tdata.locY, "defLoc": tdata.defLoc };
    node.serializedProperties.push("prop"); // 把自定义属性加入进节点的属性组
    node.setSize(30, 30);
    node.setLocation(tdata.locX + tdata.locX / 2, tdata.locY + tdata.locY / 2);
    // 不可信节点
    // node.alarm = tdata.alarm;
    scene.add(node);
    return node;
}
/**
 * CurveLink 曲线
 * FlexionalLink 二次折线
 * FoldLink 折线
 * Link 简单连线
 *
 * 参数：对象，第一个node对象，第二个node对象，虚实线，颜色，线名称或数据
 */
function addLink(scene, nodeA, nodeZ, type, tdata) {
    // let texts = text+"ms "+diubaol+"%";
    // console.log(texts)
    let color = "171, 177, 194";
    var gjmsg = '';
    if (tdata.state == 1) {
        color = '240,28,28'
        gjmsg = tdata.gaojing + '';
    } else if (tdata.state == 2 || tdata.state == 3) {
        color = '237,146,28'
        gjmsg = tdata.gaojing + '';
    }
    var link = new JTopo.CurveLink(nodeA, nodeZ, gjmsg);
    link.fontColor = color;
    link.font = '15px Consolas';
    link.textOffsetY = 12;
    // link.textOffsetX = -10;
    if (tdata.destType != 1) {
        // color = '226,220,224'
        // color = '240,28,28'
    }
    link.strokeColor = color;
    link.lineWidth = tdata.destType == 1 ? 1 : 3;// 线宽
    link.dashedPattern = tdata.destType == 1 ? null : 6; // 虚线
    link.prop = { "name": tdata.text, "告警": gjmsg, "时延": "--", "linkIds": tdata.linkIds, "preIp": tdata.preIp, "丢包": "--", "curIp": tdata.curIp };
    link.serializedProperties.push("prop"); // 把自定义属性加入进节点的属性组

    link.addEventListener('click', function (event) {
        // if(event.button == 0){
        // event.scene.stage.mouseDown =false;//取消按下效果
        var info = {
            "告警数量": tdata.faultNum || '0',
        };
        var widths = "200px";
        var width = 0;
        if (tdata && tdata.faultCont) {
            for (var i = 0; i < tdata.faultCont.length; i++) {
                info['详情' + (i + 1)] = tdata.faultCont[i];
            }
            if (tdata.faultCont.length > 0) {
                widths = "400px";
                width = 415;
            }
        }
        var content = '';
        var len;
        if (tdata.faultNum != null && tdata.faultNum != '') {
            content += '<div class="topoTipsTitle">告警详情<a class="topoSeeLink">去查看 ></span></a><span class="topoTipsClose">x</span></div>'
        }
        $.each(info, function (k, v) {
            content += '<div class="topoTipsContent">' + k + ' : ' + v + '</div>';
            len = 300;
        });
        $('.panel-default').hide();
        $("#clickProp").html("")
        var html = '<div class="topoTipsBox">' + content + '</div>'
        $("#clickProp").html(html).show();
        $(".topoTipsBox").css({
            top: event.pageY - 50,
            left: event.pageX
        }).show();
        $(".topoSeeLink").on("click", function () {
            $(".loading-wrapper").show();
            let ids = tdata.faultIds.join(",");
            top.document.getElementById('sub-content-page').src = (window.location.hostname === 'localhost' ? '/anpm-plug-alarmlist-ui.html?faultIds=' + ids + "&days=90" : '/alarmlist?faultIds=' + ids + "&days=90");
        })
        $('.topoTipsClose').on('click', function () {
            $("#clickProp").html("").hide();
        })
        // showToolip({
        //     x: event.scene.stage.pageX ,
        //     y: event.scene.stage.pageY ,
        //     width: width,
        //     content: contents
        // });
        // }
    });
    $('#clickProp').on('click', function (e) {
        e.stopPropagation()
        if ($(e.target).hasClass("clickProp")) {
            $('#clickProp').html("").hide() //关闭弹出层
        }
    });
    scene.add(link);
    return link;
}



function showToolip(options) {
    var temp = $(document);
    var tipsWin = temp.find('div[name=topo_tips]').hide();
    //var tipsWin = $("#topoTips")
    var tips_body = tipsWin.find('.panel-body');
    var op = options || {};
    var x = op.x;
    var y = op.y;
    var width = op.width || 200;
    var content = op.content || '';
    if (content) {
        tips_body.show();
        tips_body.html(content);
        tipsWin.css({
            "left": x,
            "top": y,
            "width": width
        }).show();
    } else {
        tips_body.hide();
        tipsWin.hide();
    }
}

// /**
//  * 节点显示图片
//  * value：区分节点类型
//  * ispath:是否是路径tuopo
//  * gaojing告警：null正常；1中断；2时延劣化；3丢包劣化；其他；
//  */
// export function getJtopoNodeImgByValue(value,ispath,gaojing,destType,linkState){
//     let imgtype = '';

//     //路径拓扑图标
//     if(ispath){
//         //区分图标类型节点，探针
//         if(value&&value == 1){

//             imgtype = tanzhenImg;
//         }else if(value&&value == 2){
//             //区分图标颜色
//             if(gaojing == null||gaojing == ""){
//                 imgtype =jiedianImgG
//             }else if(gaojing == 1){
//                 if(destType == 1){
//                     if(linkState == 1){
//                         imgtype =jiedianImgR3
//                     }else{
//                         imgtype =jiedianImgR
//                     }
//                 }else{
//                     // imgtype =jiedianImgH
//                     imgtype =jiedianImgR
//                 }
//             }else if(gaojing == 2 || gaojing == 3){
//                 if(linkState == 2){
//                     imgtype =jiedianImgY3
//                 }else{
//                     imgtype =jiedianImgY
//                 }
//                 // imgtype =jiedianImgY
//             }else{
//                 // imgtype = jiedianImgH;
//                 imgtype = weizhi;
//             }
//         }else if(value&&value == 4){
//             if(gaojing == null||gaojing == ""){
//                 imgtype =mubiaoImg
//             }else if(gaojing == 1){
//                 // if(destType == 1){
//                 imgtype =jiedianImgR2
//                 // }else{
//                 //     imgtype =jiedianImgH2
//                 // }
//             }else if(gaojing == 2 || gaojing == 3){
//                 imgtype =jiedianImgY2
//             }else{
//                 imgtype = mubiaoImg
//                 // if(destType == 1){
//                 //     imgtype = mubiaoImg
//                 // }else{
//                 //     imgtype =jiedianImgR2
//                 // }
//             }
//         }else {
//             imgtype = weizhi;
//         }
//         //物理拓扑图标
//     }else{
//         // if(value&&value == 1){
//         //     //区分图标颜色
//         //     if(gaojing == null||gaojing == ""){
//         //         imgtype =fuwuqiG
//         //     }else if(gaojing == 1){
//         //         imgtype =fuwuqiR
//         //     }else if(gaojing == 2 || gaojing == 3){
//         //         imgtype =fuwuqiY
//         //     }else{
//         //         imgtype = fuwuqiG;
//         //     }
//         // }else if(value&&value == 2){
//         //     //区分图标颜色
//         //     if(gaojing == null||gaojing == ""){
//         //         imgtype =jiaohuanjiG
//         //     }else if(gaojing == 1){
//         //         imgtype =jiaohuanjiR
//         //     }else if(gaojing == 2 || gaojing == 3){
//         //         imgtype =jiaohuanjiY
//         //     }else{
//         //         imgtype = jiaohuanjiG;
//         //     }
//         // }else if(value&&value == 3){
//         //     //区分图标颜色
//         //     if(gaojing == null||gaojing == ""){
//         //         imgtype =luyouqiG
//         //     }else if(gaojing == 1){
//         //         imgtype =luyouqiR
//         //     }else if(gaojing == 2 || gaojing == 3){
//         //         imgtype =luyouqiY
//         //     }else{
//         //         imgtype = luyouqiG;
//         //     }
//         // }else {
//         //     //区分图标颜色
//         //     if(gaojing == null||gaojing == ""){
//         //         imgtype =fanghuoqiangG
//         //     }else if(gaojing == 1){
//         //         imgtype =fanghuoqiangR
//         //     }else if(gaojing == 2 || gaojing == 3){
//         //         imgtype =fanghuoqiangY
//         //     }else{
//         //         imgtype = fanghuoqiangG;
//         //     }
//         // }
//     }
//     return imgtype;
// };


/**
 * 节点显示图片
 * value：区分节点类型
 * ispath:是否是路径tuopo
 * gaojing告警：null正常；1中断；2时延劣化；3丢包劣化；其他；
 */
export function getJtopoNodeImgByValue(value, ispath, gaojing, destType, linkState, interruptShowGrayNode) {
    let imgtype = '';
    //路径拓扑图标
    if (ispath) {
        //区分图标类型节点，探针
        if (value && value == 1) {
            imgtype = tanzhenImg;
        } else if (value && value == 2) {
            //区分图标颜色
            if (gaojing == null || gaojing == "") {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedianImgH

                } else {
                    imgtype = jiedianImgG
                }
                //imgtype =jiedianImgG
            } else if (gaojing == 1) {
                if (destType == 1) {
                    if (linkState == 1) {
                        if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                            console.log();
                            imgtype = jiedianImgH
                        } else {
                            imgtype = jiedianImgR3
                        }
                        // imgtype =jiedianImgR3
                    } else {
                        if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                            imgtype = jiedianImgH
                        } else {
                            imgtype = jiedianImgR
                        }
                        // imgtype =jiedianImgR
                    }
                } else {
                    if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                        imgtype = jiedianImgH
                    } else {
                        imgtype = jiedianImgR
                    }
                    // imgtype =jiedianImgR
                }
            } else if (gaojing == 2 || gaojing == 3) {
                if (linkState == 2) {
                    if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                        imgtype = jiedianImgH
                    } else {
                        imgtype = jiedianImgY3
                    }
                    // imgtype =jiedianImgY3
                } else {
                    if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                        imgtype = jiedianImgH
                    } else {
                        imgtype = jiedianImgY
                    }
                    // imgtype =jiedianImgY
                }
            } else {
                imgtype = weizhi;
            }
        } else if (value && value == 4) {
            if (gaojing == null || gaojing == "") {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedianImgH2
                } else {
                    imgtype = mubiaoImg
                }
                // imgtype =mubiaoImg
            } else if (gaojing == 1) {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedianImgH2
                } else {
                    imgtype = jiedianImgR2
                }
                // imgtype =jiedianImgR2
            } else if (gaojing == 2 || gaojing == 3) {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedianImgH2
                } else {
                    imgtype = jiedianImgY2
                }
                // imgtype =jiedianImgY2
            } else {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedianImgH2
                } else {
                    imgtype = mubiaoImg
                }
                // imgtype = mubiaoImg

            }
        } else if (value && value == 6) {
            if (gaojing == null || gaojing == "") {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedian2
                } else {
                    imgtype = jiedian1
                }
            } else if (gaojing == 1) {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedian2
                } else {
                    imgtype = jiedian4
                }
            } else if (gaojing == 2 || gaojing == 3) {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedian2
                } else {
                    imgtype = jiedian3
                }
            } else {
                if (interruptShowGrayNode && interruptShowGrayNode == 1) {
                    imgtype = jiedian2
                } else {
                    imgtype = jiedian1
                }
            }
        } else {
            imgtype = weizhi;
        }
        //物理拓扑图标
    } else {
        // if(value&&value == 1){
        //     //区分图标颜色
        //     if(gaojing == null||gaojing == ""){
        //         imgtype =fuwuqiG
        //     }else if(gaojing == 1){
        //         imgtype =fuwuqiR
        //     }else if(gaojing == 2 || gaojing == 3){
        //         imgtype =fuwuqiY
        //     }else{
        //         imgtype = fuwuqiG;
        //     }
        // }else if(value&&value == 2){
        //     //区分图标颜色
        //     if(gaojing == null||gaojing == ""){
        //         imgtype =jiaohuanjiG
        //     }else if(gaojing == 1){
        //         imgtype =jiaohuanjiR
        //     }else if(gaojing == 2 || gaojing == 3){
        //         imgtype =jiaohuanjiY
        //     }else{
        //         imgtype = jiaohuanjiG;
        //     }
        // }else if(value&&value == 3){
        //     //区分图标颜色
        //     if(gaojing == null||gaojing == ""){
        //         imgtype =luyouqiG
        //     }else if(gaojing == 1){
        //         imgtype =luyouqiR
        //     }else if(gaojing == 2 || gaojing == 3){
        //         imgtype =luyouqiY
        //     }else{
        //         imgtype = luyouqiG;
        //     }
        // }else {
        //     //区分图标颜色
        //     if(gaojing == null||gaojing == ""){
        //         imgtype =fanghuoqiangG
        //     }else if(gaojing == 1){
        //         imgtype =fanghuoqiangR
        //     }else if(gaojing == 2 || gaojing == 3){
        //         imgtype =fanghuoqiangY
        //     }else{
        //         imgtype = fanghuoqiangG;
        //     }
        // }
    }
    return imgtype;
};





/**
 * 2.1拓扑图数据排序
 *
 */
export function setDataList(dataList) {
    let tanz = [];
    if (dataList) {
        for (let i = 0; i < dataList.length; i++) {
            if (dataList[i].type == 1) {
                tanz.push(dataList[i]);
            }
        }
        for (let i = 0; i < dataList.length; i++) {
            if (dataList[i].type != 1) {
                tanz.push(dataList[i]);
            }
        }
    }
    // console.log(tanz,"---tanz-");
    return tanz;
}

//1.循环层级，找到节点最多的层级下标maxIndex
function getMaxIndex(dataList) {
    let maxIndex = 0;
    for (let i = 1; i < dataList.length; i++) {
        if (dataList[i - 1].length < dataList[i].length) {
            maxIndex = i;
        }
    }
    return maxIndex;
}

//计算最外层最大节点个数
function getMaxLeafCnt(data) {
    var maxcnt = 0;//本身
    var children = data.children == undefined ? new Array() : data.children;
    if (children.length > 0) {
        for (var i = 0; i < children.length; i++) {
            var f = children[i];
            if (f.children == undefined) {
                maxcnt++;
            } else {
                maxcnt += getMaxLeafCnt(f);
            }
        }
    }
    return maxcnt;

}


/**
 * 页面工具栏
 * 必须调取
 * 参数：stage对象，容器id，是否显示工具栏
 */
export function showJTopoToobar(stage, datas, isdarkSkin) {
    var permissionObj = datas.permissionObj.exppng;
    var toobarDiv = '';

    toobarDiv += '<img  style="cursor:pointer;" id="zoomOutButton" title="放大" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkEwOUVGOTZBRDcxQzExRUNCQzgyQTlBQzdGODZEQjY1IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkEwOUVGOTZCRDcxQzExRUNCQzgyQTlBQzdGODZEQjY1Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTA5RUY5NjhENzFDMTFFQ0JDODJBOUFDN0Y4NkRCNjUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTA5RUY5NjlENzFDMTFFQ0JDODJBOUFDN0Y4NkRCNjUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5zJDgjAAACYElEQVR42uyXS0hUURjH76j4iB5aIOIDQ9y0yh5YLlIwiqg2RbkLohcUtCsMKgILijZuIgxC21igraw2abkxCrJSXAhSlDqIaDmWBKZk/Q78g+Fyz3TvHRtbeODHmbnnm/N993yvM5HLreP5juNshGwntWMW+jOk/A1MpdiAtbAlTW+eauWOdGanOUs8lg3ICPGbVbAHaqEIVsM0vIUO6PtXJ7ACLsIYtMEBWAPzUAqX4B28hJrFNqBcqXoVHsFWKJCiXbAJ1sFxKIRuuOlnfz8uKNKG5gR2Q5dFbgaa4T7chvOwEs4kY4BZb5ffd8CAzwp3TPNp6JVhoVxwEqrglEV5JpRYfntWyq8rUAMbEIFz8EpB5zVuwUco9lj7CfWQr5MIbEAFlMHdBDIm4NIhz7L+HIbhUBgDtml+kWSt6dTLZAU1oFDzqOv5QzWSKWWFGT36bmpEtUt+WMFcEDQLIprnXc8XPGS/wZxkf7nWciz7/NWAMc3rYSjueV3c58ewD/YmSNFSBeTnoC54rbk2yWa3U+k4F9QAU3o/qRZELDIj2jhmWd8v33eEyQLjy2uwGY4kKDamBkQ91kzU34CvcCdsJbwnV5iCU2kpNpOWfZtgg3rCl7AGGAUH9RbP4LAPv+eqch7VKcaSbcdRBVJUG3eqBWd6dM0L8EFG9yp2HoSthPFjSHeAK7AdnupUBqQoKkzjeS+DjVyLUr01TCFyj+/QAI1xV7Ji9XyTLYPwRM3rzzgBE6olscW6E87ojtDuQ3ZBblm+lv/fBszqj2Kqh9H5wwRhv1Isawn+nvf9FmAAffuDlFh9i5YAAAAASUVORK5CYII="/>'
    toobarDiv += '<img  style="cursor:pointer;" id="zoomInButton" title="缩小" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkEyNTMxQTNDRDcxQzExRUM4QzM0QTFCNjFDNjZFODBCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkEyNTMxQTNERDcxQzExRUM4QzM0QTFCNjFDNjZFODBCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTI1MzFBM0FENzFDMTFFQzhDMzRBMUI2MUM2NkU4MEIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTI1MzFBM0JENzFDMTFFQzhDMzRBMUI2MUM2NkU4MEIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6McIx0AAACOklEQVR42uzXT0gUYRjH8VlRW0XSEjpEiHrpFEKFeKqIFCP1phB0MSkSRKHQixZBCaYepE6iVoJ6EfwDYpFQpJcKpRQ8BB6kKIVSEYxs+2PfF37BMmztzsw2enDgw7Cvw7zP+2ee9zFwvX/5gGVZeQha/l6bmE1U5zNY9TmA/TiWoJH73bmlPoMJ1jZfuwEkOnw+GSdRjBxtpC+Yxzim8PN/zIB57iIW8ARVyMZ3ZKIaz/AW5xGIZwB7NboeLKJInR5FIQqwD6VYwwCGkRaPAFI14tO4ghOYiDDNPzCGfDSgBKNaMk8B3NNLK9AZw4C20IZLCrrVSwBmaitxByMON+sDLVkNjrgN4BpW0OzyC2tCCPVuAjDp+RwGseEygGU8Rtm/9sLfAjiMFEx6zDNmw6brwHMUwEHdP9rab2hZViP4hMu259/pfshpJtyje8jW/sv2ez2sbSvC3//UGAGnASzpbtLti7D22xLrlaX7B6dLMKeK5ZTHPVCks+K10wC+4pESUIbLznOVjMYiLGVMeaBZO/imywBakIR2t4nI1Im9qMUFh53XoRx3Me3lLDCp9BUeKjNGez5ZI+7Q76hJLNoLzQY6i+d68ZzOe/tRm6EaYV6BvtRn2YhbXuuBNZ37VepoQG3vNb2mSPmMbr2vUgfZVQVhzoQzXksyk2Duoy+sJMtVQOYbH8JTW63QoRksU6UUl5owpE4mYny+S3bL8p0dwKbqe78v0+c3swlncTzsCPbz3/M3vwUYAOvNfNDNGwKPAAAAAElFTkSuQmCC"/>'
    toobarDiv += '<img  style="cursor:pointer;" id="yemianhuanyuan" title="页面还原" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB8AAAAgCAYAAADqgqNBAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkEzRTlGN0Q3RDcxQzExRUNCNDQyOUU4RkY1RTNDNUYwIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkEzRTlGN0Q4RDcxQzExRUNCNDQyOUU4RkY1RTNDNUYwIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTNFOUY3RDVENzFDMTFFQ0I0NDI5RThGRjVFM0M1RjAiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTNFOUY3RDZENzFDMTFFQ0I0NDI5RThGRjVFM0M1RjAiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5OdNhsAAABuklEQVR42uyXzytEURTHn/HKJIQ0YmFhxUISKz+iSDJlqfzaW0jshtj4B5QfNQvbiaUFU34mSVkok8JGpGQiv5IakfG99Z163WbmzXszLot36lPz7rv3fM89c+7pvqzpQNijaVotcGvqLAJCOoWPwZNC8WJQ7+KOVQpr1HO7tD80R/xfik+B1hT8+MBlAnyJFulJHBaBUTABvGAP5IMgaJHm7oPCOD4GQI0d8WfQDnbAOgMoB82gAtwY5h4S2ZrSSfspA3hnALEUelUVnAigF+QYUtitQrwObJNsw3gbg/lV8SuwAlbBq2E8L8VTcA3u7RScsBewRMTcRtBFxO9Nk/VDyV7qFrL0xSMlmHTaK8/+OZjnKcjNhHgVeAMeEx93oACMsBc8gg0wDirtipewqktNxKNsuTETl5NO1sWgiv88KD0HQBmYUSG+Cz6ZhTPQD4bNFukZEhcN6IBdr4cdcY7vFtIRHwMP0liYzr8NY2vcvSi4DrDFOeJ52ar4LbhgH5dNOPTz/h2zWem9CGARHNnZuejr1RZSH41zPe5zLpCOuCwe4YebShN6H6LaQ6AhE9cii5/IJz8CDACSVFtpVcLdNQAAAABJRU5ErkJggg=="/>'
    // if(datas.topoType == 'sx'){
    //     //树形选中，星形白色
    //     toobarDiv +='<img  style="cursor:pointer;" id="shuxingButton" title="树形" src="data:image/png;base64,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"/>'
    //     toobarDiv +='<img  style="cursor:pointer;" id="xingxingButton" title="星型" src="data:image/png;base64,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"/>'
    // }else{
    //     toobarDiv +='<img  style="cursor:pointer;" id="shuxingButton" title="树形" src="data:image/png;base64,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"/>'
    //     toobarDiv +='<img  style="cursor:pointer;" id="xingxingButton" title="星型" src="data:image/png;base64,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"/>'
    // }
    if (permissionObj) {
        toobarDiv += '<img  style="cursor:pointer;" id="exportButton" title="导出PNG" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkE1NDFCODI1RDcxQzExRUNCNEJGRDczMDE1QkZDNTU3IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkE1NDFCODI2RDcxQzExRUNCNEJGRDczMDE1QkZDNTU3Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTU0MUI4MjNENzFDMTFFQ0I0QkZENzMwMTVCRkM1NTciIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTU0MUI4MjRENzFDMTFFQ0I0QkZENzMwMTVCRkM1NTciLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Mzo/RAAABtUlEQVR42uzXzSsEcRjA8dltd22KjeJCObiRCFGiEDdcJEkkucltD1K4+gdsUk5iD05S8nZwQCjapbwkBymEHCjteonv1HOYtl2zO7s7ov3Vp1/z0jzP/F5nLKNzd7mKopTCqZhbAvDbJPgBnkxOIBsVVnlzs4MrEtNpVX65pBL4Ewl04xCXEXhC7rdjF9XRJGDTuV6FWexjI8I92yHH6RJ8FXU4jicB9QEWtOIhxtY9wzLqpaUMdYFd6rcoWrIf67iQc4PwYQU5yRyE+djDuDR7L5pxjk7cS/dlGOkCveKSh19JU7+Euccj40hd8rcSncCYdFM7itGGQmlZNwowjZFwwePtgjQMYBJe7KBBxksHarGEKUwkYwyUI1PeTh1kJajBkFyfkQTcyVoJXVKfohEncvwl9Rr6kIUjlCU6gVt8yPR71Zx/ltWzC5/Ik9YpSvQsUN94QTPvtWXejM3oXa9/zdgNb/79dhyQ2hFHDIemy2IehJsykhf1ttUfiro2BOUbIeYE1N2sB8NoMpjAI1pwbXQaekXqozSVQNISCMiPotlFjRlUZ4EflfKBYfbvue9bgAEAXuhZGS7s7YYAAAAASUVORK5CYII="/>'
    }
    var toobarDiv2 = ''
    toobarDiv2 += '<input type="checkbox" name="modeRadio" value="select" id="r2"/><label for="r2"> 框选</label>'
    toobarDiv2 += '<input type="checkbox" id="zoomCheckbox" checked="checked"/><label for="zoomCheckbox"> 鼠标缩放</label>'
    toobarDiv2 += '<input type="text" id="findText" style="width: 200px;border:1px solid #eaebf0;padding:6px;border-radius:4px;outline:#00FF00" placeholder="请输入IP/名称查询" value="" onkeydown="enterPressHandler(event)">'
    toobarDiv2 += '<input type="button" id="findButton" style="padding:0 20px;color:#fff;background: #2d8cf0;border: none;border-radius:3px;cursor:pointer;height: 32px;" value="查询">'
    toobarDiv2 += '<input type="button" id="fullScreenButton" style="padding:0 20px;color:#fff;background: #2d8cf0;border: none;border-radius:3px;cursor:pointer;height: 32px;" value="全屏">'
    $('#butLeft').html(toobarDiv);
    $('#butRight').html(toobarDiv2);
    // 工具栏按钮处理

    $("input[name='modeRadio']").click(function () {
        // console.log(stage.mode,"----- stage.mode-------");
        if ($(`input[name='modeRadio']`).is(':checked')) {
            console.log(JSON.stringify(stage.mode), "----- stage.mode-------");
            stage.mode = $("input[name='modeRadio']:checked").val();
        } else {
            stage.mode = "normal";

        }
    });

    $('#shuxingButton').click(function () {
        if (datas.topoType != 'sx') {
            datas.topoType = "sx"
        }
        newJtopo(datas);
    });

    $('#xingxingButton').click(function () {
        if (datas.topoType != 'xx') {
            datas.topoType = "xx"
        }
        newJtopo(datas);
    });
    $('#yemianhuanyuan').click(function () {
        newJtopo(datas, keepIsdarkSkin);
    });
    $('#centerButton').click(function () {
        stage.centerAndZoom(); //缩放并居中显示
    });
    $('#zoomOutButton').click(function () {
        stage.zoomOut();
    });
    $('#zoomInButton').click(function () {
        stage.zoomIn();
    });
    $('#cloneButton').click(function () {
        stage.saveImageInfo();
    });
    $('#exportButton').click(function () {
        // var canvasData = $('#canvasdiv').children('canvas');
        // var a = document.createElement('a');
        // a.href = canvasData[0].toDataURL();
        // a.download = '路径拓扑图.png';
        // a.click();a.remove();

        stage.saveImageInfo();
    });
    $('#printButton').click(function () {
        stage.saveImageInfo();
    });
    $('#topoList').click(function () {
        //直接跳转
        this.$router.push({ path: "/topolist" })
        // window.parent.postMessage({type:'ifrUrl',msg:"msg"}, '*');
    });
    $('#zoomCheckbox').click(function () {
        if ($('#zoomCheckbox').is(':checked')) {
            stage.wheelZoom = 1.2; // 设置鼠标缩放比例
        } else {
            stage.wheelZoom = null; // 取消鼠标缩放比例
        }
    });
    $('#fullScreenButton').click(function () {
        if (datas.dataList.nodeList == null || datas.dataList.nodeList.length == 0) {
            $("#" + datas.contId).html("");
            setTimeout(function () { newJtopo(datas); }, 5);
            return;
        }


        runPrefixMethod(stage.canvas, "RequestFullScreen")
    });

    window.enterPressHandler = function (event) {
        if (event.keyCode == 13 || event.which == 13) {
            $('#findButton').click();
        }
    };

    // 查询
    $('#findButton').click(function () {
        console.log(datas.dataList)
        if (datas.dataList) {
            if (datas.dataList.nodeList == null || datas.dataList.nodeList.length == 0) {
                $("#" + datas.contId).html("");
                setTimeout(function () { newJtopo(datas); }, 5);
                return;
            }
        }
        var text = $('#findText').val().trim();
        //var nodes = stage.find('node[text="'+text+'"]');
        var scene = stage.childs[0];
        var nodes = scene.childs.filter(function (e) {
            return e instanceof JTopo.Node;
        });
        nodes = nodes.filter(function (e) {
            if (e.text == null) return false;
            return e.text.indexOf(text) != -1;
        });

        if (nodes.length > 0) {
            var node = nodes[0];
            node.selected = true;
            var location = node.getCenterLocation();
            // 查询到的节点居中显示
            stage.setCenter(location.x, location.y);

            function nodeFlash(node, n) {
                if (n == 0) {
                    node.selected = false;
                    return;
                };
                node.selected = !node.selected;
                setTimeout(function () {
                    nodeFlash(node, n - 1);
                }, 300);
            }
            // 闪烁几下
            nodeFlash(node, 6);
        }
    });
    return stage;
}

var runPrefixMethod = function (element, method) {
    var usablePrefixMethod;
    ["webkit", "moz", "ms", "o", ""].forEach(function (prefix) {
        if (usablePrefixMethod) return;
        if (prefix === "") {
            // 无前缀，方法首字母小写
            method = method.slice(0, 1).toLowerCase() + method.slice(1);
        }
        var typePrefixMethod = typeof element[prefix + method];
        if (typePrefixMethod + "" !== "undefined") {
            if (typePrefixMethod === "function") {
                usablePrefixMethod = element[prefix + method]();
            } else {
                usablePrefixMethod = element[prefix + method];
            }
        }
    }
    );

    return usablePrefixMethod;
};


function foldOpen(e, node) { 	 			// 折叠展开
    var thisNode = e.target.nodeId;  	// 第一层以当前节点名称为 key 区分折叠状态
    var tarlink = e.target.outLinks;
    if (tarlink == undefined) {
        return
    }
    if (tarlink.length != 0 && tarlink[0].visible === true) {
        var status = [];
        for (var i = 0; i < tarlink.length; i++) {
            status[i] = { node: tarlink[i].nodeZ.visible, link: tarlink[i].visible };
            foldOpenStatus[thisNode] = status;
            tarlink[i].nodeZ.visible = false;
            tarlink[i].visible = false;
            // 下一层还有节点
            if (tarlink[i].nodeZ.outLinks.length != 0) {
                dbfold(tarlink[i].nodeZ.outLinks, foldOpenStatus[thisNode][i]);
            }
        }
    } else if (tarlink.length != 0 && tarlink[0].visible === false) {
        for (var k = 0; k < tarlink.length; k++) {
            tarlink[k].nodeZ.visible = true;
            tarlink[k].visible = true;
            tarlink[k].nodeZ.setImage(tarlink[k].nodeZ.imgSrcS, true);
            // 下一层还有节点
            if (tarlink[k].nodeZ.outLinks.length != 0) {
                dbOpen(tarlink[k].nodeZ.outLinks, foldOpenStatus[thisNode][k]);
            }
        }
        //维护foldOpenStatus对象中的数据
        for (var ent in foldOpenStatus) {
            if (ent == thisNode) {
                delete foldOpenStatus[thisNode];
            }
        }
    }
}
//收缩
function dbfold(dblink, foldStatus) {
    var status = [];  // 下层以 status 为 key 记录
    for (var j = 0; j < dblink.length; j++) {
        status[j] = { node: dblink[j].nodeZ.visible, link: dblink[j].visible };
        foldStatus.status = status;
        dblink[j].nodeZ.visible = false;
        dblink[j].visible = false;
        if (dblink[j].nodeZ.outLinks.length != 0) {
            dbfold(dblink[j].nodeZ.outLinks, foldStatus.status[j]);
        }
    }
}

//打开
function dbOpen(dblink, openStatus) {
    for (var j = 0; j < dblink.length; j++) {
        dblink[j].nodeZ.visible = true;
        dblink[j].visible = true;
        dblink[j].nodeZ.setImage(dblink[j].nodeZ.imgSrcS, true);
        if (dblink[j].nodeZ.outLinks.length != 0) {
            dbOpen(dblink[j].nodeZ.outLinks, openStatus.status[j]);
        }
    }
}