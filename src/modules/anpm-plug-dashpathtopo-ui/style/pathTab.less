
#tabPathTopo.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab{
    margin-right: 0px;
    background: transparent;
    border: 1px solid #06324D;
    color: #5CA0D5;
    border-radius: 0px;
}
#tabPathTopo.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active{
    position: relative;
    background: transparent;
    margin-right: 0px;
    border: 1px solid #046875 !important;
    border-bottom: 1px solid #060D15 !important;
    color: #00FFEE !important;
    font-weight: bold !important;
    &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 60%;
        height: 1px;
        background: linear-gradient(136deg, rgba(255,255,255,0) 0%, #00FFEE 48%, rgba(255,255,255,0) 100%);
        
    }

}
#tabPathTopo {
    .ivu-tabs-bar {
        margin-bottom: unset;
        border-bottom: 1px solid #046875;
        
    }
    .path-topo-item {
        // height: calc(100% - 40px);
        background-image: url('../../../assets/ml.png'), url('../../../assets/mr.png');
        background-position: left center, right center;
        background-repeat: no-repeat; /* 改为不重复 */
        background-size: 10px 100%, 10px 100%; /* 设置左右两侧图片的宽度和高度 */
        padding: 0 10px;
    }
    
}
