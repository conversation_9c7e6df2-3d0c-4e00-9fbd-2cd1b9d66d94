<template>
  <section
    :class="currentSkin == 1 ? 'box' : 'light-box'"
    style="position: relative; height: 100%; width: 100%"
  >
    <div style="height: 100%; width: 100%">
      <div
        v-if="topoInfos.length < 2"
        class="topologys"
        style="overflow: hidden"
      >
        <PathTopoItem
          :paramsData="paramsData"
          :boxWidth="bodyWidth"
          :boxHeight="echartsHeight"
          @getLinkTable="getLinkTable"
          @setPreIp="setPreIp"
          @nodesClick="nodesClick"
          @updateCompleteNodesShow="updateCompleteNodesShow"
          @setTopoTitleId="setTopoTitleId"
          :isMouseOver="isMouseOver"
          :isEditTopo="isEditTopo"
          :isFirst="isFirst"
          @changeIsFirst="changeIsFirst"
        ></PathTopoItem>
      </div>

      <!-- 多路径拓扑 -->
      <div style="height: 100%; width: 100%" v-else>
        <TabPathTopo
          :paramsData="paramsData"
          :boxWidth="bodyWidth"
          :boxHeight="echartsHeight"
          @getLinkTable="getLinkTable"
          @setPreIp="setPreIp"
          @nodesClick="nodesClick"
          @updateCompleteNodesShow="updateCompleteNodesShow"
          @setTopoTitleId="setTopoTitleId"
          :isEditTopo="isEditTopo"
          :isMouseOver="isMouseOver"
          :isFirst="isFirst"
          @changeIsFirst="changeIsFirst"
        ></TabPathTopo>
      </div>
    </div>
    <!-- /多路径拓扑 -->
    <!-- 链路趋势详情弹窗 -->
    <LinkDetail
      ref="linkDetail"
      :linkDetailShow="linkDetailShow"
      :tabListModal="tabListModal"
      :indexData="tabListModal[0]"
      :tabsId="topoTitleId"
      :preIp="preIp"
      @closeLinkModal="closeLinkModal"
    ></LinkDetail>
    <!-- 端对端趋势详情弹窗 -->
    <PeerToPeerLinkDetail
      ref="peerToPeerLinkDetail"
      :peerToPeerLinkDetailShow="peerToPeerLinkDetailShow"
      :tabListModal="tabListModal"
      :indexData="tabListModal[0]"
      :tabsId="topoTitleId"
      @closePeerToPeerLinkModal="peerToPeerLinkDetailShow = false"
    ></PeerToPeerLinkDetail>

    <!-- 换种方式写，看看会不会出现这个问题  -->
    <ModalContent
      ref="modalContent"
      @setTitle="setTitle"
      :rowData="contentData"
      :modalTitle="modalTitle"
      :modalOmitTitle="modalOmitTitle"
      :fibreShow="fibreShow"
      :modalWidth="modalWidth"
      @onCancel="onCancel"
    ></ModalContent>

    <!--详情-->
    <DetailModal
      ref="detailModal"
      :detailShow="detailShow"
      :rowData="rowData"
      :indexData="indexModal.data"
      :isSpecial="echartIsSpecial"
      @closeModal="detailShow = false"
    >
    </DetailModal>
  </section>
</template>
<script>
// import "@/config/page.js"
// import '@/timechange'

export default {
  name: "index",
 components: {
    
    LinkDetail: () => import ('../components/LinkDetail.vue'),
     PeerToPeerLinkDetail: () => import ('@/common/topoG6/pathtopo/PeerToPeerLinkDetail.vue'),
     ModalContent: () => import('../components/ModalContent.vue'),
      DetailModal:()=>import('@/common/topoG6/pathtopo/DetailModal.vue'),
      TabPathTopo:()=>import('../components/TabPathTopo.vue'),
      PathTopoItem:()=>import('../components/PathTopoItem.vue'),

  },
  data() {
    return {
      isOnePathTopo:false,
      paramsData:{},
      isEditTopo:false,
      currentSkin:sessionStorage.getItem('dark') || 1,
      forbidUpdate:false,
      modalWidth: 0,
        showPeerToPeerLinkDetail:false,
        modalTitle: '',
       detailShow:false,
       echartIsSpecial: false,
      fibreShow: false,
       contentData: {
                taskId: '',
                reportIds: []
            },
       indexModal: {
              data: {},
            },
      preIp: '',
      peerToPeerLinkDetailShow:false,
      linkDetailShow:false,
      topoTitle:'',
      bodyWidth:0,
       pathTopologyLocXLocYMinMax:null,
       fibreType:0,
         layoutModel: 0,
       echartsHeight:0,
       backgroundImage:null,
        echartsBg:null,
          configData: {},
      rowData: {},
      //刷新时间间隔
      intervalTime: null,
      modalOmitTitle:'',
      //刷新参数
      interrefresh: null,
      tabListModal:[],
      loading: false,
      topoId: 0,
      pathtopoType: 0,
      dataList: {},
      nodeData: {},
      havedata: true,
      pathtopoShow: true,
      showWarning: true,
      showShuxing: 'sx',
      msgButtonModal: false,
      configShowFieldResult:"",
      isMouseOver: false,
      isFirst:false,
      msgModal: [
        this.$t('comm_topo_device_ip'),
        this.$t('comm_topo_device_name'),
        this.$t('comm_topo_device_flow'),
        this.$t('comm_topo_device_delay'),
        this.$t('comm_topo_device_loss')
        ],
      pathTopoData: {},
      //type:0节点字段，1线路字段
      showType: 'all',
      msgModalData: [
            {
                id: 1,
                name: this.$t('comm_topo_device_ip'),
                filedName: 'value',
                className:"width90",
                type: 0,
                util: '',
                isShow: true
            },
            {
                id: 2,
                name: this.$t('comm_topo_device_name'),
                filedName: 'aliases',
                className:"width110",
                type: 0,
                util: '',
                isShow: true
            },
            {
              // 流速
                id: 3,
                name: this.$t('comm_topo_device_flow'),
                filedName: 'flowIntoSpeed',
                className:"width90",
                type: 0,
                util: '',
                isShow: true
            },
              
            {
              // 时延
                id: 4,
                name: this.$t('comm_topo_device_delay'),
                filedName: 'delay',
                className:"width110",
                type: 0,
                util: '',
                isShow: true
            },
              {
              // 丢包
                id: 5,
                name: this.$t('comm_topo_device_loss'),
                filedName: 'lossRate',
                type: 0,
                util: '',
                isShow: true
            },
      ],
      legendButtonModal: false,
      selectTopoButtonModal: false,
      selectModal: "",
      selectModalData: [],
      // isbigscreen: false,
     
      topoTitleId:null,
      topoInfos:[],
       debounceTimer: null,
     
    }
  },
  watch: {
   
    // 监听数据
    configShowFieldResult:{
      handler(val) {
        // debugger
          var showTypeId = "";
          if(val === 'all'){
            showTypeId = "all";
          }else if(val === '0'){
            showTypeId = '';
          }else{
            var showTypeIdArray = val.split(",");
            var showTypeNameArray = [];
            this.msgModalData.forEach((item)=>{
              if(showTypeIdArray.indexOf(item.id + "") > -1){
                showTypeNameArray.push(item.name);
              }
            });
            showTypeId = showTypeNameArray;
          }
         
          this.showType = showTypeId;
          this.$refs.PathTopo.showType = this.showType;
      },
      deep: true
    }
  },
  created() {
    // debugger
    this.isFirst = true
    this.modalWidth = localStorage.getItem('modalWidth') || 0
     this.bodyWidth = document.body.clientWidth;
     this.echartsHeight = document.body.clientHeight;
   
    const src = window.frames.frameElement.getAttribute('src'), name = window.frames.frameElement.getAttribute('name');
    // console.log('src=>', src);
    // console.log(' window.frames=>', window.frames);
    if (parent.window.isEdit) {
      this.isEditTopo = true

      let editName = window.frames.frameElement.getAttribute('editName')
      // if (parent.window.dashpathtopo && parent.window.dashpathtopo.indexOf('&&topoId=')>-1) {
      //   this.topoId = parent.window.dashpathtopo.split('&&topoId=')[1].split('&&limitNum=')[0];
      // }
      if (unescape(src).split('=')[1]) {
         this.pathtopoType = JSON.parse(unescape(src).split('=')[1]).pathtopoType
        console.log(this.pathtopoType,'this.pathtopoType')
        console.log(window.frames.name,'window.frames.name')
        
        this.topoInfos = JSON.parse(editName.split('&&topoInfos=')[1].split('&&taskPresentationType=')[0])
        this.topoId = this.topoInfos[0].id
        // this.topoId = JSON.parse(unescape(src).split('=')[1]).topoId
        console.log(editName,'editName')
       
      }
      this.intervalTime = parent.window.dashpathtopo.split('&&topoId=')[0].split('?intervalTime=')[1];
    } else {
     this.isEditTopo = false
      if (window.frames.name && window.frames.name.indexOf('&&topoInfos=') > -1) {
        this.topoInfos = JSON.parse(window.frames.name.split('&&topoInfos=')[1].split('&&taskPresentationType=')[0])
        console.log(this.topoInfos,'this.topoInfos')
    
        this.topoId = this.topoInfos[0].id
       
        this.pathtopoType = window.frames.name.split('&&pathtopoType=')[1].split('&&groupIds=')[0];
     
        console.log(this.pathtopoType)
      }
      this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
      if (window.frames.frameElement) {
        // window.frames.frameElement.setAttribute('src','about:blank')
        // window.frames.frameElement.contentWindow.document.write("");
        window.frames.frameElement.contentWindow.close();
        // window.frames.frameElement.remove()

        // window.frames.frameElement.setAttribute('src',src)
      }
    }

   
     this.paramsData = {
      topoId:this.topoId,
      pathtopoType:this.pathtopoType,
      intervalTime:this.intervalTime,
      topoInfos:this.topoInfos
     }
  },
  methods: {
    changeIsFirst(val){
      this.isFirst = val
    },
     setTopoTitleId(id){
      this.topoTitleId = id
     },
    
    
      setTitle(val) {
            if (val && val.length > 50) {
                this.modalTitle = val;
                this.modalOmitTitle = val.substring(0, 50) + '...';
            } else {
                this.modalOmitTitle = val;
                this.modalTitle = val;
            }
        },
     nodesClick(param) {
          

                this.contentData.name = param.data.name;
                this.contentData.taskId = param.data.taskId;
                this.contentData.reportIds = param.data.reportIds;
                if (param.data.state == 1 || param.data.state == 2 || param.data.state == 3) {
                    this.fibreShow = true;
                }else{
                    if(param.data.state){
                        // this.$Messages.warning(this.$t('pathtopo_path_not_faulty'))
                        let _self = this;
                        // 查看详情
                        if (sessionStorage.getItem("delayPs")) {
                          sessionStorage.removeItem("delayPs");
                        }
                        this.$store.commit("setdelayLTlevel", "");
                        this.$store.commit("updateDelayLossHistory", -1);
                        this.$store.commit("setflowTlevel", "");
                        this.$store.commit("updateFlowHistory", -1);
                        let destIp = param.data.name;
                        _self.indexModal.data = [];
                        // 暂时屏蔽
                        if(param.data.taskId && param.data.taskId != '') {

                              _self.detailShow = true;

                        }else {
                          // 收起的禁止点击
                           this.$Messages.warning(this.$t('task_not_support'))

                        }

                        // _self.taskNum = param.data.taskNum;
                        _self.rowData = {"taskId":param.data.taskId}
                      // console.log("indexData destIp",destIp)
                        _self.indexModal.data = {
                          flowChartType: 1,
                          // respondIp: param.data.respondIp,
                          sourceIp: param.data.sourceIp,
                          // destip通过空格分割并且取最后一位
                          destIp: destIp.split(" ")[destIp.split(" ").length - 1],
                          respondIp: '',
                          startTime: "",
                          endTime: "",
                          taskType:param.data.configType,
                          // eventStart: param.data.eventStart || "",
                          // eventEnd: param.data.eventEnd || "",
                          eventStart: "",
                          eventEnd: "",
                          id:param.data.taskId,
                        };
                        // this.echartIsSpecial = param.data.isSpecial == 1 ? true : false;
                        this.echartIsSpecial = false;
                    }
                }

        },
     onCancel() {
          this.fibreShow = false;
          this.contentData = {};
          this.$refs.modalContent.resetRegion();
    },
     updateCompleteNodesShow(data) {
        console.log(data,'data')
        data.completeNodesShow = true
         window.parent.postMessage({type:'completeNodesShow',data:data});
        // this.completeNodesShow = true
        // this.completeNodeData = data


      },
    closeLinkModal() {
        this.linkDetailShow = false
        this.$refs.PathTopo.update()
      },
    setPreIp(ip) {
        this.preIp = ip

      },
    getLinkTable(arr,obj) {
      // debugger
        if(this.showPeerToPeerLinkDetail){
          this.peerToPeerLinkDetailShow = true
          // debugger
          console.log(arr)
          this.tabListModal = arr
          setTimeout(() => {
            this.$refs.peerToPeerLinkDetail.getPathChartData(1,obj)
          }, 100)
        }else{
        this.linkDetailShow = true
        // debugger
        // console.log(arr)
        this.tabListModal = arr
        setTimeout(() => {
          this.$refs.linkDetail.getPathChartData(1,obj)
        }, 100)
        }

      },
     async findInUseIconManageConfigureVoByType() {
      // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标
      try {
       const res = await this.$http.post('/iconmanage/findInUseIconManageConfigureVoByType',{type:0,currentSkin:this.currentSkin })
       if(res.code === 1) {
        this.configData = res.data
       
        // debugger





       }
      }catch(err) {}

      },
      async  getTopoTitle() {
       const res = await this.$http.wisdomPost('/pathTopo/getPathTopoPulldown')
       if(res && res.code === 1) {
        res.data.list.forEach(item => {
          if(item.id == this.topoId) {
            this.topoTitle = item.topologyName
          }
        })
       }


      },
       // 添加节流函数
 // 改为防抖函数
  debounceMouseState(newState) {
    console.log('收到状态更新请求:', newState);
    
    // 清除之前的定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      console.log('清除之前的定时器，重新计时');
    }
    
    // 设置新的定时器
    this.debounceTimer = setTimeout(() => {
      console.log('防抖时间结束，更新鼠标状态为:', newState);
      this.isMouseOver = newState;
      this.debounceTimer = null;
    }, 1000); // 500毫秒防抖s
  },
    


   
  },
  mounted() {
    window.addEventListener('message', e => {
      if (e.data && e.data.type === 'mouseEnterState') {
        console.log(e.data.data,'e.data.data')
        // 使用防抖控制状态更新
          this.debounceMouseState(e.data.data);

      }
    });

   
   
    // window.thisVm = this;
    // this.isbigscreen = window.isbigscreen;
   
  },
  beforeDestroy() {
     // 清理定时器
  if (this.debounceTimer) {
    clearTimeout(this.debounceTimer);
    this.debounceTimer = null;
  }
       // 移除事件监听
   
    if (this.interrefresh) {
      clearInterval(this.interrefresh);
      this.interrefresh = null;
    }
      if (this.$refs.PathTopo) {
      this.$refs.PathTopo.destroy && this.$refs.PathTopo.destroy();
    }
    
    if (this.$refs.linkDetail) {
      this.$refs.linkDetail.destroy && this.$refs.linkDetail.destroy();
    }
    
    if (this.$refs.peerToPeerLinkDetail) {
      this.$refs.peerToPeerLinkDetail.destroy && this.$refs.peerToPeerLinkDetail.destroy();
    }
 
    // Clean up window frame references
    if (window.frames.frameElement) {
      window.frames.frameElement = null;
    }
   
  }
}
</script>
<style>
body.bigscreen .topologys {
  background: var(--body_conent_b_color, #060d15);
}

.topologys {
  position: relative;
  width: 100%;
  height: 100%;
  /* min-height: 420px; */
  display: block;
  overflow: auto;
  color: black;
  box-sizing: border-box;
  /* background: var(--body_b_color, #060d15); */
}

.topologys button {
  margin: 10px;
}
</style>
<style scoped lang="less">
.fillempty-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .fillempty {
    text-align: center;
    // line-height: 40px;
    background-image: url("../../../assets/dashboard/fileempty.png");
    // width: 290px;
    // height: 200px;

    background-repeat: no-repeat;
    // background-size: 290px 200px;
    background-position: center;
  }
  .fillemptyTitle {
    // color: #0e2a5f
    color: #5ca0d5;
  }
  .fillempty-light {
    width: 290px;
    height: 200px;

    background-repeat: no-repeat;
    // background-size: 290px 200px;
    background-position: center;
    background-image: url("../../../assets/dashboard/fileempty2.png");
  }
  .fillemptyTitle-light {
    color: #515a6e;
  }
}

.bigscreen {
  .fillemptyTitle {
    color: #8fd4ff;
  }
}
.bg-box {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}
.box {
  background-color: #060d15;
}
.light-box {
  background-color: #fff;
}
</style>