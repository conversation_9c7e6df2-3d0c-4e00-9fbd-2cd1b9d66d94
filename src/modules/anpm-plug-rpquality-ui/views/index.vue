<template>
  <!-- 中继质差分析 -->
  <div
    :class="{ 'light-no-tabs': currentSkin == 0, padding0: reportState == 1 }"
  >
    <section class="sectionBox">
      <div class="section-top">
        <Row class="fn_box">
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadData"
                  @onSelectChange="setOrg"
                  @onClear="onClear"
                  @onFocus="focusFn"
                ></TreeSelect>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.groupId"
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('snmp_pl_man')"
                  clearable
                  style="width: 100%"
                >
                  <Option
                    style="width: 100%"
                    v-for="item in groupingList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("specinfo_perspective") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select v-model="query.type" :placeholder="$t('snmp_pl_man')">
                  <Option :value="1">{{ $t("comm_event_query") }} </Option>
                  <Option :value="2">{{ $t("comm_fault_query") }} </Option>
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <DatePicker
                  format="yyyy-MM-dd"
                  type="daterange"
                  :options="timeOptions"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="true"
                  style="width: 100%"
                  :confirm="false"
                >
                </DatePicker>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("alarm_fault_type") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.faultType"
                  filterable
                  clearable
                  :only-filter-with-text="true"
                >
                  <Option
                    v-for="item in faultTypeList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
              <div class="fn_item_box">
                <Input
                  v-model="query.keyword"
                  :title="$t('rphth_search_keyword')"
                  :placeholder="$t('rphth_search_keyword')"
                />
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <Button
              class="query-btn"
              type="primary"
              icon="ios-search"
              @click="queryClick"
              :title="$t('common_query')"
            ></Button>
          </div>
        </div>
      </div>

      <div class="section-body">
        <div class="section-body-content">
          <div>
            <Row class="pie-div">
              <i-col span="7" style="padding: 0 5px 0 0">
                <div>
                  <div class="pie-box">
                    <div class="pie-content">
                      <div class="echarts-pie">
                        <echarts-pie
                          :node="pie_List.node"
                          :pieData="pieData"
                          :isdarkSkin="isdarkSkin"
                        ></echarts-pie>
                      </div>
                      <div class="pie-example">
                        <Tooltip
                          :content="items.content"
                          v-for="(items, indexes) in pie_List.data"
                          :key="indexes"
                          max-width="200"
                        >
                          <div
                            class="pie-example-box"
                            :class="[
                              items.name === $t('common_Normal') ||
                              items.name === $t('common_line')
                                ? ''
                                : 'pie-example-hover',
                            ]"
                          >
                            <i
                              class="icon-box"
                              :class="[
                                indexes === 0
                                  ? currentSkin == 1
                                    ? 'icon-normal'
                                    : 'light-icon-normal'
                                  : indexes === 1
                                  ? 'icon-degradation'
                                  : 'icon-end',
                              ]"
                            ></i>
                            <div class="example-right">
                              <div class="example-header">
                                <div class="example-title">
                                  {{ items.name }}
                                </div>
                                <span class="example-value">{{
                                  items.value
                                }}</span>
                              </div>
                              <div class="example-beliel">
                                {{ items.Percent }}%
                              </div>
                            </div>
                          </div>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </i-col>
              <i-col span="17" style="padding: 0 0 0 0px">
                <div>
                  <Row style="margin-top: 10px" :gutter="5">
                    <i-col span="12" style="posization: reactive">
                      <Loading :loading="loading2"></Loading>
                      <p style="text-align: left; line-height: 32px">
                        <b>{{ $t("comm_interruption_top5") }}:</b>
                      </p>
                      <Table
                        ref="tableList"
                        stripe
                        class="topTable"
                        :columns="columns2"
                        :data="tableData2"
                        height="290"
                        :no-data-text="
                          loading2
                            ? ''
                            : tabList2.length > 0
                            ? ''
                            : currentSkin == 1
                            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                        "
                        size="small"
                      ></Table>
                    </i-col>
                    <i-col span="12" style="posization: reactive">
                      <Loading :loading="loading3"></Loading>
                      <p style="text-align: left; line-height: 32px">
                        <b>{{ $t("comm_degradation_top5") }}:</b>
                      </p>
                      <Table
                        ref="tableList"
                        stripe
                        class="topTable"
                        :columns="columns3"
                        :data="tableData3"
                        height="290"
                        :no-data-text="
                          loading3
                            ? ''
                            : tabList3.length > 0
                            ? ''
                            : currentSkin == 1
                            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                        "
                        size="small"
                      ></Table>
                    </i-col>
                  </Row>
                </div>
              </i-col>
            </Row>
            <Row style="margin-top: 10px">
              <i-col
                span="24"
                class="tableBorder contentBox_bg"
                style="padding: 20px; posization: reactive"
              >
                <Loading :loading="loading1"></Loading>
                <div
                  style="height: 38px; width: 100%; margin: 10px 0"
                  class="table-item"
                >
                  <b class="fleft" style="line-height: 38px">{{
                    $t("comm_relay_indicators")
                  }}</b>
                  <Button
                    class="daoChu-btn"
                    v-if="permissionObj.export"
                    type="primary"
                    id="exportData"
                    @click="exportClick"
                    :title="this.$t('but_export')"
                  >
                    <i class="iconfont icon-icon-derive" />
                  </Button>
                </div>
                <!-- 底部表格 -->
                <Table
                  v-if="tableShow"
                  ref="tableList"
                  class="my-table-fixed fixed-right"
                  stripe
                  :columns="columns"
                  :data="tableData1"
                  @on-sort-change="tableSortChange"
                  :no-data-text="
                    loading1
                      ? ''
                      : tabList1.length > 0
                      ? ''
                      : currentSkin == 1
                      ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                        $t('common_No_data') +
                        '</p></div>'
                      : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                        $t('common_No_data') +
                        '</p></div>'
                  "
                  size="small"
                ></Table>
                <div class="tab-page">
                  <Page
                    v-page
                    v-if="tabList1.length > 0"
                    :current.sync="pageNo"
                    :page-size="pageSize"
                    :total="totalCount"
                    :page-size-opts="pageSizeOpts"
                    :prev-text="$t('common_previous')"
                    :next-text="$t('common_next_page')"
                    @on-change="pageChange"
                    @on-page-size-change="pageSizeChange"
                    show-elevator
                    show-sizer
                  >
                  </Page>
                </div>
              </i-col>
            </Row>
          </div>
        </div>
      </div>

      <!--查看-->

      <Modal
        sticky
        v-model="taskModal.show"
        :styles="{ top: '100px' }"
        :width="modalWidth"
        class="task-modal"
        draggable
        :mask="true"
        footer-hide
        @on-visible-change="resetData"
      >
        <div slot="header">
          <span class="title-name">{{ $t("comm_relay_details") }}</span>
        </div>
        <section class="taskStrategy-modal">
          <Row type="flex" justify="end" class="btn-row-bg">
            <Button
              class="daoChu-btn"
              id="exportData"
              type="primary"
              @click="exportViewClick()"
              :title="this.$t('but_export')"
            >
              <i class="iconfont icon-icon-derive" />
            </Button>
          </Row>
          <Table
            ref="tableListModal"
            :height="computedHeight()"
            :columns="columnsModal"
            :data="tabListModal"
            :loading="loadingModal"
            @on-sort-change="sortChange"
            class="modalTableover"
            :no-data-text="
              loadingModal
                ? ''
                : tabListModal.length > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
          >
          </Table>
          <div class="tab-page">
            <Page
              v-page
              :current.sync="modalPage_no"
              :page-size="modalPageSize"
              :total="totalCountModal"
              show-sizer
              :page-size-opts="[3, 10, 50, 100, 200, 500, 1000]"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-page-size-change="tabParamPageSizeChange"
              @on-change="pageChangeModal"
              show-total
              style="padding-top: 20px; text-align: right"
            >
            </Page>
          </div>
          <div v-if="tabListModal.length > 0">
            <div class="wisdom-snmp-content" style="position: relative">
              <div class="btnChange">
                <a
                  href="javascript:void(0);"
                  :class="[btnValue == 1 ? 'active' : '']"
                  @click="btnClick(1)"
                  >{{ $t("specquality_basic_index") }}</a
                >
                <a
                  href="javascript:void(0);"
                  :class="[btnValue == 2 ? 'active' : '']"
                  @click="btnClick(2)"
                  >{{ $t("specquality_operating_index") }}</a
                >
              </div>
              <!--趋势图-->
              <div class="lookBox" style="position: relative">
                <Spin fix v-show="spinShow" style="width: 100%; height: 100%"
                  >{{ $t("alarm_load")
                  }}<Icon
                    type="ios-loading"
                    size="18"
                    class="demo-spin-icon-load"
                  ></Icon>
                </Spin>
                <div class="title" style="margin-left: 0px">
                  {{ echartLookParama2.preNodeIp }}
                  <span class="alias" v-if="preAlias.name"
                    >({{
                      preAlias.port
                        ? $t("comm_Device_name") +
                          ":" +
                          preAlias.name +
                          $t("comm_port_number") +
                          ":" +
                          preAlias.port
                        : $t("comm_Device_name") + ":" + preAlias.name
                    }})</span
                  >——>
                  {{ echartLookParama2.nodeIp }}
                  <span class="alias" v-if="zAlias.name"
                    >({{
                      zAlias.port
                        ? $t("comm_Device_name") +
                          ":" +
                          zAlias.name +
                          $t("comm_port_number") +
                          ":" +
                          zAlias.port
                        : $t("comm_Device_name") + ":" + zAlias.name
                    }})</span
                  >
                </div>
                <div class="contain">
                  <div
                    ref="rpquality-delayLoss"
                    v-show="btnValue == 1"
                    id="rpquality-delayLoss"
                    class="echartStyle"
                    :style="'height:' + height + 'px;width:' + echartsWidth"
                  ></div>
                  <div
                    ref="rpquality-delayLoss2"
                    v-show="btnValue == 2"
                    id="rpquality-delayLoss2"
                    class="echartStyle"
                    :style="'height:' + height2 + 'px;width:' + echartsWidth"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Modal>
      <!--自定义列表项 修改之后-->
      <Modal
        sticky
        v-model="customModalShow"
        width="540"
        height="720"
        class="threshold-modal modal-selected"
        :title="$t('custom_list_items')"
        draggable
        :mask="true"
        :loading="customModalShowLoading"
      >
        <Row class="modal-selected-row">
          <Col>
            <Table
              ref="fieldsModalTableData"
              height="550"
              width="400"
              :show-header="false"
              :columns="fieldsColumns"
              :data="allocationListFields"
              @on-row-click="getFieldsColumnsIndex"
              :row-class-name="rowClassName"
            >
            </Table>
          </Col>
          <Col>
            <div class="btn-box">
              <Tooltip :content="$t('dash_up')" placement="right">
                <div class="sort-btn" @click="moveUp">
                  <Icon
                    type="md-arrow-up"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
              <Tooltip :content="$t('dash_down')" placement="right">
                <div class="sort-btn" @click="moveDown">
                  <Icon
                    type="md-arrow-down"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
            </div>
          </Col>
        </Row>
        <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
import locationreload from "@/common/locationReload";

const pointGreen = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC';
const pointRed = 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC';
import echartsPie from "@/common/echarts/base-pieHealth.vue";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import echartFn from "@/common/mixins/echartFun";
import global from "@/common/global.js";
import "../../../timechange"
import axios from "axios";
import "@/config/page.js";
let echarts = require('echarts/lib/echarts');
require('echarts/lib/chart/line');
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
require('echarts/lib/component/dataZoom');
require('echarts/lib/component/markLine');
import eConfig from "@/config/echart.config.js";
import { addDraggable } from "@/common/drag.js";
import moment from 'moment';
import {tableEditBtn} from '@/assets/base64Img/img';
import {tableEditLightBtn} from '@/assets/base64Img/img';
import ipv6Format from "@/common/ipv6Format";
  import langFn  from '@/common/mixins/langFn';


function getQueryVariable(variable) {
  var url = top.document.getElementById('sub-content-page').src;
  var query = url.indexOf('?') > 0 ? url.split('?')[1] : null;
  if (query) {
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == variable) {
        return pair[1].replace("%20", " ");
      }
    }
  } else {
    // 处理质量报告页面的参数
    var element = top.document.getElementById("rpquality");
    if (element) {
      url = element.src;
      query = url.indexOf("?") > 0 ? url.split("?")[1] : null;
      if (query) {
        var params = query.split("param=");
        if (params && params.length > 0) {
          params = params[1];
        }
        if (params) {
          query = unescape(params);
          query = JSON.parse(query);
          if (query) {
            return query[variable];
          }
        }
      }
    }

  }
  return false;
}
export default {
  name: "healthManagement",
  mixins: [echartFn,langFn],
  components: {
    echartsPie,
    TreeSelect
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
            // 是否 报表跳转过来的页面
      reportState:0,
      modalWidth:0,
      echartsWidth:null,
      tableShow:true,
      modelShow:false,
      treeValue: '',
      isdarkSkin: sessionStorage.getItem('dark') || 1,
      //权限对象
      permissionObj: {},
      //机构参数
      treeData: [],
      spinShow: false,
      orgTree: false,
      orgLists: [],
      readonly: true,
      pie_List: {
        node: "linkPie",
        title: this.$t('dash_relay'),
        totalValue: 0,
        data: [
          { value: 0, name: this.$t('dash_normal'), Percent: 0, content: this.$t('rpmanager_interrupt_deterioration_tip') },
          { value: 0, name: this.$t('dash_deterioration'), Percent: 0, content: this.$t('rpmanager_deterioration_tip') },
          { value: 0, name: this.$t('dash_interrupt'), Percent: 0, content: this.$t('rpmanager_interrupt_tip') },
        ]
      },
      pieData: {},
      chartProperty: {
        color: '#303748',
        bgColor: '#ffffff',
      },
      query: {
        keyword: "",
        orgId: "",
        groupId: "",
        type: 2,
        operator: "",
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: new Date(new Date().getTime() + 86400000).format("yyyy-MM-dd 00:00:00"),
        pageNo: 1,
        pageSize: 10,
        faultType:"",
      },
      order: {
        status: null,
        usableRateOrder: 'desc',
        goodRateOrder: '',
        latestTimeOrder: '',
        brokenDurationOrder: '',
        degradationDurationOrder: '',
      },
      client_list: [],
      groupingList: [],
      // 1：中断 2：时延劣化 3：丢包劣化
      faultTypeList:[{
           value: 1,
          label: this.$t('comm_interruption')
      },{
           value: 2,
          label: this.$t('common_degradation'),

      },{
          value: 3,
          label: this.$t('comm_loss_package'),

      }],
      operatorList: [
        {
          value: 0,
          label: this.$t('comm_all')
        },
        {
          value: 1,
          label: "中国移动"
        },
        {
          value: 2,
          label: "中国联通"
        },
        {
          value: 3,
          label: "中国电信"
        },
        {
          value: 4,
          label: "中国广电"
        },
        {
          value: 6,
          label: "其他"
        },
        {
          value: 7,
          label: "--"
        }
      ],
      currentTime: Date.now(),
      startTime: new Date().format("yyyy-MM-dd 00:00:00"),
      endTime: new Date().format("yyyy-MM-dd 00:00:00"),
      interValCurTime: null,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },

      timeRange: [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")],
      timeOptions: {
        shortcuts: [
          // {
          //   text: this.$t('comm_half_hour'),
          //   value() {
          //     const start = new Date();
          //     const end = new Date();
          //     start.setTime(start.getTime() - 30 * 60 * 1000);
          //     return [start.format("yyyy-MM-dd HH:mm:ss"), end.format("yyyy-MM-dd HH:mm:ss")];
          //   }
          // },
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
              let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
              let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          }
        ]
      },
      queryModal: {
        orderBy: null,
        fieldName: null,
        keyword: "",
        startTime: "",
        endTime: "",
        linkId: "",
        pageNo: 1,
        pageSize: 1000000
      },
      edit: {
        dev_ip: ""
      },
      states: [{ value: 0, label: this.$t('testspeed_unactivated') }, { value: 1, label: this.$t('testspeed_activated') }],
      dateValue: [
        new Date().format("yyyy-MM-dd"),
        new Date().format("yyyy-MM-dd")
      ],
      dateValueCopy: [],
      pageNo: 1,
      pageSize: 10,
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      modalPage_no: 1,
      modalPageSize: 3,
      totalCount: 0,
      totalCountModal: 0,
      noDataLoading: false,
      loading: false,
      loading1: false,
      loading2: false,
      loading3: false,
      loadingModal: false,
      currentid: "",
      tabList: [],
      tabList1: [],
      originalList: [], //保存原始列表数据
      orderTabList: [],
      originalModalList: [], //保存原始列表数据
      orderTabModalList: [],
      tabList2: [],
      tabList3: [],
      tabListModal: [],
      columnsModal: [
        {
          title: this.$t('but_choose'),
          align: "left",
          width: 80,
          className: "bgColor",
          render: (h, params) => {
            let id = params.row.id;
            let flag = false;
            if (this.currentid === id) {
              flag = true;
            } else {
              flag = false;
            }
            let self = this;
            return h("div", [
              h("Radio", {
                style: {
                  marginRight: 0
                },
                props: {
                  value: flag
                },
                on: {
                  "on-change": () => {
                    self.currentid = id;
                    this.id = params.row.id;
                    this.echartLookParama2.startTime = moment(params.row.startEventTime).subtract(3, "hours").format("YYYY-MM-DD HH:mm:ss");
                    if (params.row.state === this.$t('common_unrecovered')) {
                      this.markPointGreen = '';
                      this.echartLookParama2.endTime = new Date().format('yyyy-MM-dd HH:mm:ss');
                    } else {
                      this.markPointGreen = params.row.endEventTime;
                      this.echartLookParama2.endTime = moment(params.row.endEventTime, "YYYY-MM-DD HH:mm:ss").add(3, "hours").format("YYYY-MM-DD HH:mm:ss");
                    }
                    this.markPoint = params.row.startEventTime;
                    this.getSnmp(this.echartLookParama2);
                  }
                }
              })
            ]);
          }
        },
        {
          title: this.$t('comm_event_start'),
          key: "startEventTime",
          align: "left",
          sortable: "custom",
          className: "bgColor"
        },
        {
          title: this.$t('testspeed_event_recovery'),
          key: "endEventTime",
          align: "left",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.endEventTime
            return h('span', str === '' || str == null || params.row.state == this.$t('common_unrecovered') ? '--' : str)
          },
        },
        {
          title: this.$t('comm_event'),
          key: "type",
          align: "left",
          className: "bgColor",
          // width: "200px",
          renderHeader: (createElement, { col }) => {
            return createElement(
              "Select",
              {
                props: {
                  value: this.ModalcheckAll,
                  // transfer: true
                },
                on: {
                  "on-change": value => {
                    this.ModalcheckAll = value;
                    if (value == 3) {
                      this.orderTabModalList = JSON.parse(
                        JSON.stringify(this.originalModalList)
                      ).filter(item => {
                        return item.type == this.$t('common_degradation');
                      });
                    } else if (value == 2) {
                      this.orderTabModalList = JSON.parse(
                        JSON.stringify(this.originalModalList)
                      ).filter(item => {
                        return item.type == this.$t('common_loss_degradation');
                      });
                    } else if (value == 1) {
                      this.orderTabModalList = JSON.parse(
                        JSON.stringify(this.originalModalList)
                      ).filter(item => {
                        return item.type == this.$t('dash_interrupt');
                      });
                    } else if (value == 0) {
                      this.orderTabModalList = JSON.parse(
                        JSON.stringify(this.originalModalList)
                      );
                    }

                    this.modalPage_no = 1;
                    this.totalCountModal = this.orderTabModalList.length;
                    this.tabListModal = this.orderTabModalList.slice(
                      0,
                      this.modalPageSize
                    );
                    if (this.tabListModal.length > 0) {
                      this.currentid = this.tabListModal[0].id;
                      this.markPoint = this.tabListModal[0].startEventTime;
                      this.echartLookParama2.startTime = moment(this.tabListModal[0].startEventTime).subtract(3, "hours").format("YYYY-MM-DD HH:mm:ss");
                      if (this.tabListModal[0].state === this.$t('common_unrecovered')) {
                        this.echartLookParama2.endTime = new Date().format('yyyy-MM-dd HH:mm:ss');
                      } else {
                        this.markPointGreen = this.tabListModal[0].endEventTime;
                        this.echartLookParama2.endTime = moment(this.tabListModal[0].endEventTime, "YYYY-MM-DD HH:mm:ss").add(3, "hours").format("YYYY-MM-DD HH:mm:ss");
                      }
                      this.getSnmp(this.echartLookParama2);
                    }
                  }
                },
                style: {
                  width: "100%",
                  // "min-width":"200px"
                },
                class: "headerSelectArea"
              },
              [
                createElement(
                  "Option",
                  {
                    props: {
                      value: 0
                    },
                    class: "modalSelect",
                  },
                  this.falutType == 1 ? this.$t('comm_event_type_all') : this.$t('comm_failure_type_all')
                ),
                createElement(
                  "Option",
                  {
                    props: {
                      value: 3
                    }
                  },
                  this.$t('common_degradation')
                ),
                createElement(
                  "Option",
                  {
                    props: {
                      value: 2
                    }
                  },
                  this.$t('common_loss_degradation')
                ),
                createElement(
                  "Option",
                  {
                    props: {
                      value: 1
                    }
                  },
                  this.$t('dash_interrupt')
                )
              ]
            );
          }
        },
        {
          title: this.$t('user_last'),
          key: "takeTimeSecond",
          align: "left",
          sortable: true,
          className: "bgColor",
          render: (h, param) => {
            return h('span', param.row.takeTime)
          }
        },
        {
          title: this.$t('specquality_recovery_cause'),
          key: "recoveryType",
          // width: 120,
          align: "left",
          render: (h, params) => {
             // 0故障升级、1故障降级、2路径切换、3故障恢复、4其他,5故障链路恢复
            let str = params.row.recoveryType,
              text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (str == 0) {
              text = this.$t('comm_failure_up');
            } else if (str == 1) {
              text = this.$t('comm_failure_dwon');
            } else if (str == 2) {
              text = this.$t('comm_path_switching');
            } else if (str == 3) {
              text = this.$t('comm_failure_re');
            }else if (str == 5) {
              text = this.$t('comm_failure_link_recovery');
            } else {
              text = this.$t('comm_other');
            }
            return h("span", text);
          },
        },
        {
          title: this.$t('specquality_current_state'),
          key: "state",
          align: "left",
          className: "bgColor",
          render: (h, params) => {
            if (params.row.state == this.$t('common_unrecovered')) {
              return h("span", { style: { color: "#fb204d" } }, this.$t('common_unrecovered'));
            } else if (params.row.state == this.$t('common_recovered')) {
              return h("span", { style: { color: "#23D692" } }, this.$t('common_recovered'));
            }
          },
        }
      ],
      indeterminate: true,
      checkAll: -1,
      ModalcheckAll: 0,
      falutType: 2,
      customKey:"api:rpquality/selectLinkCountIndexData+sys_func_id:19",
      customMoveIndex:0,
      customModalShow:false,
      customModalShowLoading:false,
      columns:[],
      fieldsJsonObjArr:[],
      allocationListFields:[],
      customFieldsColumnsKeyWidth:[],
      screenWidth:0,
       fieldsColumns: [
       {
        key: "showField",
        width: 35,
        render: (h, params) => {
          let row = params.row;
          return h("Checkbox", {
            props: {
              value: row.showField,
              disabled: row.fixedField // 当fixedField为true时禁用Checkbox
            },
            on: {
              input: (value) => {
                if (!row.fixedField) {
                  // 更新 showField 的值
                  this.allocationListFields[params.index].showField = value;
                }
              }
            }
          });
        }
      },
        {
          key: "parameterTitle",
          align: "left",
          // width: 160,
          render:(h,params) => {
                       let fontColor = 'var(--field_font_color ,#fff)'
            if(params.row.fixedField) {
              fontColor = '#5CA0D5'
            }
            return h('div',{
              style: {
                color:fontColor,
                fontWeight:400
              }
            },params.row.parameterTitle)
          }
        }
      ],
      fixedColumns: [

        {
          title: this.$t('snmp_name'),
          key: "sourceName",
          width: 160,
          align: "left",
          className: "bgColor",
          tooltip: true
        },
        // 机构
        {
          title: this.$t('comm_org'),
          key: "orgName",
          width: 100,
          align: "left",
          className: "bgColor",
          tooltip: true
        },
        // 本端ip
        {
          title: this.$t('snmp_this_end_ip'),
          key: "devIp",
          align: "left",
          minWidth: 200,
          className: "bgColor",
          tooltip: true,
          render: (h, params) => {
              let str = params.row.devIp;
              let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        // 本端地址
        {
          title: this.$t('snmp_this_end_addr'),
          key: "sourceIp",
          minWidth: 200,
          align: "left",
          className: "bgColor",
          tooltip: true,
          render: (h, params) => {
              let str = params.row.sourceIp;
              let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        // 对端地址
        {
          title: this.$t('snmp_paired_end'),
          key: "destIp",
          minWidth: 200,
          align: "left",
          className: "bgColor",
          tooltip: true,
          render: (h, params) => {
              let str = params.row.destIp;
              let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        // 对端别名
        {
          title: this.$t('snmp_paired_end_alia'),
          key: "destName",
          width: 180,
          align: "left",
          className: "bgColor",
          tooltip: true
        },
        // 可用率
        {
          title: this.$t('dashboard_availability'),
          key: "usableRate",
          width: this.getColumnWidth(90,120),
          align: "left",
          sortable: true,
          className: "bgColor",
          render: (h, params) => {
            // debugger
            let str = params.row.usableRate;
            return h(
              "span",
              str === undefined || str === null || str === "" || str === "--" ? "--" : Number(str) + "%"
            );
          }
        },
        // 优良率
        {
          title: this.$t('dashboard_excellent_rate'),
          key: "goodRate",
          width: this.getColumnWidth(90,105),
          align: "left",
          sortable: true,
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.goodRate;
            return h(
              "span",
              str === undefined || str === null || str === "" || str === "--" ? "--" : Number(str) + "%"
            );
          }
        },
        // 中断累计次数
        {
          title: this.$t('comm_interrup_times'),
          key: "brokenCount",
          width: this.getColumnWidth(130,180),
          align: "left",
          sortable: true,
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.brokenCount;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? 0
                : str
            );
          }
        },
        // 中断累计时长
        {
          title: this.$t('comm_duration'),
          key: "interruptTimeCountNum",
          width: this.getColumnWidth(130,160),
          align: "left",
          sortable: true,
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.interruptTimeCountNum;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : this.handleDuration(str)
            );
          }
        },
        // 中断平均时长
        {
          title: this.$t('comm_break_avg_long_time'),
          key: "avgBrokenLongTime",
          width:this.getColumnWidth(130,190),
          align: "left",
          sortable: true,
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.avgBrokenLongTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? 0
                : this.handleDuration(str)
            );
          }
        },
        // 时延劣化累计次数
        {
          title: this.$t('comm_delayDegradation_times'),
          key: "totalDegrCount",
          width:this.getColumnWidth(150,245),
          align: "left",
          sortable: true,
          render: (h, params) => {
            let str = params.row.totalDegrCount;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? 0
                : str
            );
          },
          className: "bgColor"
        },
        // 时延劣化累计时长
        {
          title: this.$t('comm_delayDegradation_duration'),
          key: "inferTimeCountNum",
          width:this.getColumnWidth(150,230),
          align: "left",
          sortable: true,
          render: (h, params) => {
            let str = params.row.inferTimeCountNum;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : this.handleDuration(str)
            );
          },
          className: "bgColor"
        },
        // 时延劣化平均时长
        {
          title: this.$t('comm_delayDegradation_avg'),
          key: "avgDegrLongTime",
          width: this.getColumnWidth(150,270),
          align: "left",
          sortable: true,
          render: (h, params) => {
            let str = params.row.avgDegrLongTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? 0
                : this.handleDuration(str)
            );
          },
          className: "bgColor"
        },
        //  丢包劣化累计次数
        {
          title: this.$t('comm_packetLossDegradation_times'),
          key: "packetLossDegradationCount",
          width:this.getColumnWidth(150,290),
          align: "left",
          sortable: true,
          render: (h, params) => {
            let str = params.row.packetLossDegradationCount;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? 0
                : str
            );
          },
          className: "bgColor"
        },
        // 丢包劣化累计时长
        {
          title: this.$t('comm_packetLossDegradation_duration'),
          key: "packetLossDegradationDuration",
          width:  this.getColumnWidth(150,270),
          align: "left",
          sortable: true,
          render: (h, params) => {
            let str = params.row.packetLossDegradationDuration;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : this.handleDuration(str)
            );
          },
          className: "bgColor"
        },
        // 丢包劣化平均时长
        {
          title: this.$t('comm_packetLossDegradation_avg'),
          key: "avgPacketLossDegradationTime",
          width: this.getColumnWidth(150,310),
          align: "left",
          sortable: true,
          render: (h, params) => {
            let str = params.row.avgPacketLossDegradationTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? 0
                : this.handleDuration(str)
            );
          },
          className: "bgColor"
        },
          {
          title: this.$t('comm_operate'),
          key: "action",
          align: "center",
          width: 120,
          className: "bgColor",
          // fixed:'right',
          renderHeader: (h) => {
            const handleClick = () => {
              this.customModalShow = true;
              this.customModalShowLoading = true;
            }
            return h('div',[
              h('span',this.$t('comm_operate')),
              h('img', {
                attrs: {
                  src:this.currentSkin == 1 ? tableEditBtn:tableEditLightBtn
                },
                style: {
                  width: '15px', // 调整图片大小
                  height: '15px', // 考虑保持宽高比
                  marginLeft: '10px',
                  verticalAlign: 'middle',// 使图片居中
                  cursor: 'pointer'
                },
                on: {
                click: handleClick,

              },
              })
            ])
          },
          render: (h,{ row }) => {
          let modify = h(
            'Tooltip',
            {
              props:{
                placement:'left-end',
                transfer: true
              }
            },
            [
              h('span',{
                // class:'look-icon',
                class: this.currentSkin == 1 ? 'look-icon':'light-look-icon',
                 on:{
                 click: () => {
                    this.$http.wisdomPost("/audit/detail", { tab: this.$t('qualityreport_dialog_rl_analysis')}).then(res => { });
                    this.actionClick(row, "look");
                  },

              }
              }),
               h('span', { slot: 'content' }, this.$t('comm_view_details'))
            ]
          )
          let array = [];
           array.push(modify);
           return h('div', array);
         }
        },
      ],
      columns2: [
        {
          title: this.$t('snmp_name'),
          key: "sourceName",
          align: "left",
          className: "bgColor",
          minWidth: 160,
          render: (h, param) => {
            let str = param.row.sourceName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            let str1 = str;
            if (str.length > 10) {
              str1 = str.substring(0, 10) + '...';
            }
            // let str1 = str;
            // if (str.length > 5) {
            //   str1 = str.substring(0, 5) + '...';
            // }
            // return h(
            //   'Tooltip',
            //   {
            //     props: {
            //       placement: 'top-end',
            //       transfer: true
            //     },
            //   },
            //   [
            //     h('span', {
            //       class: "action-btn",
            //        style: {
            //         cursor: 'pointer', color: this.permissionObj.look ? '#57a3f3':"#ffffff",
            //               display: this.permissionObj.look
            //                 ? "inline-block"
            //                 : "none",
            //         },
            //       on: {
            //         click: () => {
            //           if(this.permissionObj.look){
            //           this.$http.wisdomPost("/audit/detail", { tab: "中继质差分析" }).then(res => { });
            //           this.actionClick(param.row, "look");
            //           }else{
            //               console.log("无权限")
            //           }
            //         }
            //       }
            //     }, str1),
            //     h(
            //       'span',
            //       {
            //         slot: 'content', //slot属性
            //         style: {
            //           whiteSpace: 'normal',
            //           wordBreak: 'break-all'
            //         }
            //       },
            //       str
            //     ),
            //   ]
            // );
             if(str !== "--") {
              return h('div',
              {class:'table-ellipsis' ,
              style: {
                cursor: "pointer", 
                // color: "#05EEFF"
                color: this.currentSkin == 1?'#05EEFF':'#0290FD'
              },
              
                on: {
                      click: () => {
                       if(this.permissionObj.look){
                      this.$http.wisdomPost("/audit/detail", { tab: "中继质差分析" }).then(res => { });
                      this.actionClick(param.row, "look");
                      }else{
                          console.log("无权限")
                      }
                      },
                    },
              },[
              h('Tooltip',{
                props: {
                  placement:'top-start',
                  content: str,
                }

              },str1)

            ])
            }else {
              return h('div',{
                style: {
                cursor: "pointer", 
                // color: "#05EEFF"
                color: this.currentSkin == 1?'#05EEFF':'#0290FD'
              },
                on: {
                      click: () => {
                       if(this.permissionObj.look){
                      this.$http.wisdomPost("/audit/detail", { tab: "中继质差分析" }).then(res => { });
                      this.actionClick(param.row, "look");
                      }else{
                          console.log("无权限")
                      }
                      },
                    },
              },str)
            }
          }
        },
        {
          title: this.$t('snmp_this_end_addr'),
          key: "sourceIp",
          align: "left",
          minWidth: 145,
          className: "bgColor",
          tooltip: true
        },
        {
          title: this.$t('snmp_paired_end'),
          key: "destIp",
          align: "left",
          minWidth: 160,
          className: "bgColor",
          tooltip: true
        },
        {
          title: this.$t('rphealthy_cumulative_number'),
          key: "interruptCount",
          align: "left",
          minWidth: 200,
          className: "bgColor",
          tooltip: true
        },
        {
          title: this.$t('comm_duration1'),
          key: "interruptTimeCount",
          align: "left",
          minWidth: 200,
          className: "bgColor",
          tooltip: true
        }
      ],
      columns3: [
        {
          title: this.$t('snmp_name'),
          key: "sourceName",
          align: "left",
          className: "bgColor",
          minWidth: 160,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.sourceName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            let str1 = str;
            if (str.length > 10) {
              str1 = str.substring(0, 10) + '...';
            }
               if(str !== "--") {
              return h('div',
              {class:'table-ellipsis' ,
              style: {
                cursor: "pointer", 
                // color: "#05EEFF"
                color: this.currentSkin == 1?'#05EEFF':'#0290FD',
              },
              
                on: {
                      click: () => {
                       if(this.permissionObj.look){
                      this.$http.wisdomPost("/audit/detail", { tab: "中继质差分析" }).then(res => { });
                      this.actionClick(param.row, "look");
                      }else{
                          console.log("无权限")
                      }
                      },
                    },
              },[
              h('Tooltip',{
                props: {
                  placement:'top-start',
                  content: str,
                }

              },str1)

            ])
            }else {
              return h('div',{
                style: {
                cursor: "pointer", 
                // color: "#05EEFF"
                color: this.currentSkin == 1?'#05EEFF':'#0290FD',
              },
                on: {
                      click: () => {
                       if(this.permissionObj.look){
                      this.$http.wisdomPost("/audit/detail", { tab: "中继质差分析" }).then(res => { });
                      this.actionClick(param.row, "look");
                      }else{
                          console.log("无权限")
                      }
                      },
                    },
              },str)
            }
            
          }
        },
        {
          title: this.$t('snmp_this_end_addr'),
          key: "sourceIp",
          align: "left",
          minWidth: 145,
          className: "bgColor",
          tooltip: true
        },
        {
          title: this.$t('snmp_paired_end'),
          key: "destIp",
          align: "left",
          minWidth: 160,
          className: "bgColor",
          tooltip: true
        },
        {
          title: this.$t('rphealthy_cumulative_number'),
          key: "inferCount",
          align: "left",
          minWidth: 200,
          className: "bgColor",
          tooltip: true
        },
        {
          title: this.$t('comm_duration1'),
          key: "inferTimeCount",
          align: "left",
          minWidth: 200,
          className: "bgColor",
          tooltip: true
        }
      ],
      title: {
        deviceIp: "",
        sourceName: "",
        sourceIp: "",
        port: ""
      },
      editType: 0,
      healthLook: {},
      taskModal: {
        show: false
      },

      /*趋势图参数设置*/
      /*趋势图按钮*/
      btnValue: 1,
      echartLookParama2: {
        level: 2,
        taskFlag: 2,
        devIp: "",
        preNodeIp: "",
        nodeIp: "",
        orgId: "",
        linkId: "",
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: new Date(new Date().setTime(new Date().getTime())).format(
          "yyyy-MM-dd 23:59:59"
        ),
        queryType: 2,
        High: false,
        special: false
      },
      markPoint: "",
      markPointGreen: "",
      height: 500,
      height2: 260,
      echart1: {
        show: true
      },
      echart2: {
        show: true
      },
      preAlias: {
        name: "",
        port: ""
      },
      zAlias: {
        name: "",
        port: ""
      },
      delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "bps",
        出流速: "bps",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%"
      },
      flowColor: ["#478EE9", "#F19149"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: []
      },
      flowData: {
        enter: [],
        issue: []
      },
      goodRateData: {
        nrUseRateList: [],
        nrGoodRateList: [],
        useRateList: [],
        goodRateList: []
      },
      gooduseLength: 0,
      delayLossScale: true,
      startScale: false,
      startScale2: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayStart2: 0,
      delayEnd: 100,
      delayEnd2: 100,
      scale: "",
      startValue: "",
      startValue2: "",
      endValue: "",
      endValue2: "",
      scale2: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {}
      },
      delayPs2: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {}
      }
      //以上为趋势图有关参数
    };
  },
  created() {

    let delayLossUnitTemp = {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] = "ms";
    // 丢包率
    delayLossUnitTemp[this.$t('comm_packet_loss')] = "%";
    // 入流速
    delayLossUnitTemp[this.$t("dashboard_upstream")] = "bps";
    // 出流速
    delayLossUnitTemp[this.$t("dashboard_down")] = "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_uplink")] = "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_downstream")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t('specquality_availability_exemption')] = "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t('specquality_good_rate_exemption')] = "%";
    // 可用率
    delayLossUnitTemp[this.$t('specquality_availability')] = "%";
    // 优良率
    delayLossUnitTemp[this.$t('specquality_good_rate')] = "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit, delayLossUnitTemp);

    const src = window.frames.frameElement.getAttribute('src')
    let frameDataStr = unescape(src).split('=')[1] ?? ''
    if (frameDataStr && JSON.parse(frameDataStr).from == 'report') {
      this.orgLists = []
      // frameDataStr有值且from ==report 表明是从质量报告加载的iframe
      let frameData = JSON.parse(frameDataStr)
      this.orgLists.push({
        id: frameData.orgId,
        name: frameData.orgName
      })
      this.startTime = frameData.startTime
      this.endTime = frameData.showEndTime
      this.query.startTime = frameData.startTime
      this.query.endTime = frameData.endTime
      this.query.orgId = frameData.orgId
      this.query.type = frameData.type
      this.reportState = 1
    } else {
      this.reportState = 0
      // 不是从质量报告加载iframe时，才重定向
      this.$nextTick(() => {
         locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());

      })
     
    }
    moment.locale('zh-cn');
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.getTreeOrg();
    this.$nextTick(() => {
      this.dateValueCopy = [];
      // this.dateValueCopy.push(
      //   new Date().format("yyyy-MM-dd 00:00:00"),
      //   new Date().format("yyyy-MM-dd 23:59:59")
      // );
      this.dateValueCopy.push(
        this.query.startTime,
        this.query.endTime
      );
      if (getQueryVariable("start")) {
        this.startTime = this.query.startTime = getQueryVariable('start');
        this.endTime = this.query.endTime = getQueryVariable('end');
        this.dateValueCopy = [this.query.startTime, this.query.endTime]
      }
      if (getQueryVariable("startTime")) {
        this.startTime = this.query.startTime = getQueryVariable("startTime");
        this.endTime = this.query.endTime = getQueryVariable("endTime");
        this.dateValueCopy = [this.query.startTime, this.query.endTime];


        this.timeRange = [new Date(this.query.startTime), new Date(this.query.endTime).getTime() - 86400000];
      }
      if (getQueryVariable("type")) {
        this.query.type = Number(getQueryVariable("type"))
      }
      if (getQueryVariable("screening")) {
        this.query.screening = getQueryVariable("screening")
      }

      // this.getList1(this.query);
      this.getList2(this.query);
    });
    document.addEventListener("click", e => {
      var box = document.getElementById('selectBox');
      if (box && box.contains(e.target)) {

      } else {
        this.orgTree = false;
      }
    });
    top.document.addEventListener("click", e => {
      var box = document.getElementById('selectBox');
      if (box && box.contains(e.target)) {

      } else {
        this.orgTree = false;
      }
    });
    //获取屏幕总长度
    this.screenWidth = window.innerWidth-45;
    //保存原始的字段项展示长度
    this.setCustomFieldsColumnsKeyWidth();
  },
  watch: {
    chartProperty: {
      handler(val) {
        this.pieData = Object.assign(this.pie_List, val);
        this.$set(this.pieData, 'rand', new Date().getTime());
        this.$set(this.pieData, 'color', val.color);
        this.$set(this.pieData, 'bgColor', val.bgColor);
      },
      deep: true,
      // immediate:true
    }
  },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
        // 设置颜色
    this.delayLossColor = [eConfig.legend.delayColor[this.currentSkin] ,eConfig.legend.lossColor[this.currentSkin] ];
    this.flowColor = [eConfig.legend.flowInColor[this.currentSkin] ,eConfig.legend.flowOutColor[this.currentSkin] ];
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
     this.modalWidth = document.body.clientWidth * 0.98
    window.thisVm = this;
    // window.parent.addEventListener("message", (e) => {
    //   if (e) {
    //     if (e.data.type == 'msg') {
    //       return;
    //     } else if (typeof e.data == 'object') {
    //       this.isdarkSkin = e.data.isdarkSkin;
    //     } else if (typeof e.data == 'number') {
    //       this.isdarkSkin = e.data;
    //     }
    //   }
    // });
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    addDraggable();
    // 获取分组下拉
    this.getGroupingSelect()
  },
  computed: {
    tableData1: {
      get: function () {
        let arr = this.tabList1.map(item => {
          for (const key in item) {
            let str = item[key] === undefined || item[key] === null || item[key] === '' || item[key] === 'null' ? '--' : item[key]
            item[key] = str
          }
          return item
        })
        return arr
      },
    },
    tableData2: {
      get: function () {
        let arr = this.tabList2.map(item => {
          for (const key in item) {
            let str = item[key] === undefined || item[key] === null || item[key] === '' || item[key] === 'null' ? '--' : item[key]
            item[key] = str
          }
          return item
        })
        return arr
      },
    },
    tableData3: {
      get: function () {
        let arr = this.tabList3.map(item => {
          for (const key in item) {
            let str = item[key] === undefined || item[key] === null || item[key] === '' || item[key] === 'null' ? '--' : item[key]
            item[key] = str
          }
          return item
        })
        return arr
      },
    },
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    //保存原始的字段项展示长度
    setCustomFieldsColumnsKeyWidth(){
      if(this.customFieldsColumnsKeyWidth==0){
        this.fixedColumns.forEach(item=>{
          let customFieldsColumnsKeyWidthObj = {"key":item.key,"width":item.width};
          this.customFieldsColumnsKeyWidth.push(customFieldsColumnsKeyWidthObj);
        });
      }
    },
    resetData(value) {
      if(value) {
        this.modelShow = true

      }else {
        this.modelShow = false
        this.$nextTick(() => {
          const modalBody = this.$refs.lookModal.$el.querySelector('.ivu-modal-body')
          console.log(modalBody)
          const modalWidth = modalBody.offsetWidth; // 或 modalBody.clientWidth 或 modalBody.getBoundingClientRect().width
          console.log(modalWidth)
          this.echartsWidth = modalWidth
          // debugger

        })
      }
    },

    // 获取分组下拉
    getGroupingSelect() {
      this.$http.post('/group/list', { pageNo: 1, pageSize: 10000 }).then(res => {
        this.groupingList = res.data.records
      })
    },

    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item
          });
          _self.treeData = treeNodeList;
        }
      })
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item
          });
          callback(childrenOrgList);
        }
      })
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    focusFn() {
      this.getTreeOrg()
    },
    onClear() {
      this.query.orgId = ''
      this.treeValue = ""
    },
    choicesOrg() {
      this.orgTree = true;
    },
    setCurrentTime() {
      this.currentTime = Date.now();
    },
    // 排序设置
    tableSortChange({ column, key, order }) {
      if (order === 'normal') {
        this.query.fieldName = 'goodRate';
        this.query.sort = 'desc';
      } else {
        this.query.fieldName = key;
        this.query.sort = order;
      }
      this.getList2(this.query);
    },

    // 表格数据排序
    sortChange(column) {
      this.queryModal.fieldName = column.key;
      this.queryModal.orderBy = column.order;
      this.queryModal.pageNo = 1;
      if (column.order == "normal") {
        this.queryModal.orderBy = "desc";
      }
      this.getModalList(this.queryModal);
    },
    // 重置滚动条
    resetScroll() {
        this.$nextTick(() => {
     const tableHeader = this.$refs.tableList.$el.querySelector('.ivu-table-header');
     tableHeader.scrollLeft = 1; // 触发微小的滚动
     tableHeader.scrollLeft = 0; // 然后复位
      const tableBody = this.$refs.tableList.$el.querySelector('.ivu-table-body');

      if (tableBody) {
        // debugger

          //  alert(tableBody.scrollLeft)

          tableBody.scrollLeft = 1;
          tableBody.scrollLeft = 0;



      }
      // ivu-table-overflowX
      const tableWrap = this.$refs.tableList.$el.querySelector('.ivu-table-tip');

      if (tableWrap) {
        // alert(tableWrap.scrollLeft)


          tableWrap.scrollLeft = 1;
          tableWrap.scrollLeft = 0;



      }

   });


    },

    //查询
    queryClick() {

      // this.tableShow = false
      // this.tableShow = true

      this.query.keyword = this.query.keyword.trim();
      this.query.pageNo = 1;
      this.dateValueCopy = [];
      this.checkAll = -1;
      this.pageNo = 1;
      if (this.query.type == 1) {
        this.columnsModal[1].title = this.$t('comm_event_start');
        this.columnsModal[2].title = this.$t('comm_event_recovery');
        this.falutType = 1;
      } else if (this.query.type == 2) {
        this.columnsModal[1].title = this.$t('comm_failure_start');
        this.columnsModal[2].title = this.$t('comm_failure_recovery');
        this.falutType = 2;
      }
      if (this.query.orgId == 0 || this.query.orgId == undefined) {
        this.query.orgId = "";
      }
      if (this.query.operator == 0 || this.query.operator == undefined) {
        this.query.operator = "";
      }
      if (
        this.timeRange[0] == "" ||
        this.timeRange[0] == undefined ||
        this.timeRange[1] == "" ||
        this.timeRange[1] == undefined
      ) {
        this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
        this.query.endTime = new Date(new Date().getTime() + 86400000).format("yyyy-MM-dd 00:00:00");
      }
      if (
        this.timeRange[0] &&
        this.timeRange[0] != "" &&
        this.timeRange[1] &&
        this.timeRange[1] != ""
      ) {
        this.query.startTime = new Date(this.timeRange[0]).format(
          "yyyy-MM-dd HH:mm:ss"
        );
        this.query.endTime = new Date(new Date(this.timeRange[1]).getTime() + 86400000).format("yyyy-MM-dd 00:00:00");
      }
      if (new Date(this.query.startTime).valueOf() > new Date(this.query.endTime).valueOf()) {
        this.$Message.warning(this.$t('specquality_time'))
        return
      }
      if (moment(this.query.endTime).diff(moment(this.query.startTime), 'days') >= 62) {
        this.$Message.warning(this.$t('warning_time_not_exceed_62'))
        return
      }

      this.dateValueCopy.push(this.query.startTime, this.query.endTime);
      // this.getList1(this.query);
      this.getList2(this.query);
      this.resetScroll()
    },

    //获取列表数据
    getSnmpGraph(res) {
      let _self = this;
      this.loading2 = true;
      this.loading3 = true;
      if (res.data) {
        //统计图
        let snmpGraph = res.data.snmpGraph;
        //{{$t('comm_interruption_top5')}}
        let snmpBrokenTOPN = res.data.snmpBrokenTOPN;
        //{{$t('comm_degradation_top5')}}
        let snmpDegradationTOPN = res.data.snmpDegradationTOPN;
        _self.pie_List.totalValue = snmpGraph.total;
        _self.pie_List.data[0].value = snmpGraph.normalCount;
        _self.pie_List.data[0].Percent = snmpGraph.normalPercent;
        _self.pie_List.data[1].value = snmpGraph.inferCount;
        _self.pie_List.data[1].Percent = snmpGraph.inferPercent;
        _self.pie_List.data[2].value = snmpGraph.interruptCount;
        _self.pie_List.data[2].Percent =
          snmpGraph.interruptPercent;
        _self.tabList2 = snmpBrokenTOPN;
        _self.tabList3 = snmpDegradationTOPN;
      } else {
        _self.pie_List.totalValue = 0;
        _self.pie_List.data[0].value = 0;
        _self.pie_List.data[0].Percent = 0;
        _self.pie_List.data[1].value = 0;
        _self.pie_List.data[1].Percent = 0;
        _self.pie_List.data[2].value = 0;
        _self.pie_List.data[2].Percent = 0;
        _self.tabList2 = [];
        _self.tabList3 = [];
      }

      this.pieData = Object.assign(this.pie_List, this.chartProperty)
      this.loading2 = false;
      this.loading3 = false;
    },

    //获取列表数据
    // 暂时不使用该方法
    getList1(param) {
      let _self = this;
      //1.链路信息列表
      let params = {
        orgId: param.orgId,
        groupId: param.groupId,
        type: param.type,
        keyword: param.keyword ? param.keyword : "",
        startTime: param.startTime,
        endTime: param.endTime,
      };
      this.loading2 = true;
      this.loading3 = true;
      _self.$http.wisdomPost("/rpquality/selectData", params).then(res => {
        if (res.code === 1) {
          if (res.data) {
            //统计图
            let snmpGraph = res.data.snmpGraph;
            //{{$t('comm_interruption_top5')}}
            let snmpBrokenTOPN = res.data.snmpBrokenTOPN;
            //{{$t('comm_degradation_top5')}}
            let snmpDegradationTOPN = res.data.snmpDegradationTOPN;
            _self.pie_List.totalValue = snmpGraph.total;
            _self.pie_List.data[0].value = snmpGraph.normalCount;
            _self.pie_List.data[0].Percent = snmpGraph.normalPercent;
            _self.pie_List.data[1].value = snmpGraph.inferCount;
            _self.pie_List.data[1].Percent = snmpGraph.inferPercent;
            _self.pie_List.data[2].value = snmpGraph.interruptCount;
            _self.pie_List.data[2].Percent =
              snmpGraph.interruptPercent;
            _self.tabList2 = snmpBrokenTOPN;
            _self.tabList3 = snmpDegradationTOPN;
          } else {
            _self.pie_List.totalValue = 0;
            _self.pie_List.data[0].value = 0;
            _self.pie_List.data[0].Percent = 0;
            _self.pie_List.data[1].value = 0;
            _self.pie_List.data[1].Percent = 0;
            _self.pie_List.data[2].value = 0;
            _self.pie_List.data[2].Percent = 0;
            _self.tabList2 = [];
            _self.tabList3 = [];
          }
          this.loading2 = false;
          this.loading3 = false;
        } else {
          this.$Message.error(res.msg)
        }
      }).catch((err) => {
        console.log(err)
        this.loading2 = false;
        this.loading3 = false;
      }).finally(() => {
        this.pieData = Object.assign(this.pie_List, this.chartProperty)
        this.loading2 = false;
        this.loading3 = false;
      });
    },
    //修改自定列表项
    customModalOk() {
        this.$http.PostJson("/allocationlistfields/update", {allocationListFields:this.allocationListFields, key:this.customKey}).then((res) => {
        if (res.code === 1) {
          this.$Message.success({ content: this.$t('comm_success'), background: true });
          this.customModalShow= false;
          this.customModalShowLoading= false;
          this.getList2(this.query);
        }
      })
    },
    //取消修改
    customModalCancel() {
      this.customModalShow= false;
      this.customModalShowLoading= false;
    },
    //获取列表项下标并改变状态
    getFieldsColumnsIndex(row, index){
      this.customMoveIndex = index;
      // if(row.fixedField == false){
      //   if(row.showField == true){
      //   this.allocationListFields[index].showField =false;
      //   }else{
      //     this.allocationListFields[index].showField =true;
      //   }
      // }
    },
    rowClassName(row, index) {
      if (index === this.customMoveIndex) {
        return "selected-row";
      }
      return "";
    },

    //上移
    moveUp() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex > 0) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex - 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex - 1;
      }
    },
    //下移
    moveDown() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex < this.allocationListFields.length - 1) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex + 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex + 1;
      }

    },
    getAllocationListFieldsByKey(){
    //key值
      this.$http.PostJson("/allocationlistfields/getAllocationListFieldsByKey", { key: this.customKey }).then((res) => {
        if (res.code === 1) {
          if(res.data.allocationListFields){
            let screenWidthTemp = this.screenWidth;
            this.allocationListFields = res.data.allocationListFields;
            this.fieldsJsonObjArr = [];
            // 获取显示表头
           
            res.data.allocationListFields.forEach(item=>{
                if(item.showField === true){
                  this.fieldsJsonObjArr.push(item.parameterName);
                }
            });
             this.fieldsJsonObjArr.push("action");//第一列选择列

            this.columns = [];
            let customColumnsWidth = 0;//回显自定义字段总宽度
            let customColumnsAvgWidth = 0;//回显自定义字段平均宽度
            //将显示的表头拿到函数放到columns中
            if(this.fieldsJsonObjArr.length>0){
                this.fieldsJsonObjArr.forEach(item=>{
                    this.fixedColumns.forEach(item2=>{
                        if(item === item2.key){
                        //计算需要展示自定义字段项总长度
                        let customFieldsColumnsKeyWidthTemp = this.customFieldsColumnsKeyWidth.find(item3 => item3.key === item2.key);
                        if(customFieldsColumnsKeyWidthTemp){
                          customColumnsWidth = customColumnsWidth + customFieldsColumnsKeyWidthTemp.width;
                          item2.width = customFieldsColumnsKeyWidthTemp.width;
                        }
                        this.columns.push(item2);
                        return;
                        }
                    });
                });
             //赋值标头名称
             this.allocationListFields.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item.parameterName === item2.key){
                    item.parameterTitle = item2.title;
                    return;
                  }
                });
              });
              //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
              if(screenWidthTemp>customColumnsWidth ){
                if(this.columns.length>1){
                  let columnsLength = this.columns.length-1;
                  customColumnsAvgWidth = Math.floor((screenWidthTemp-customColumnsWidth)/columnsLength);
                }
              }
              this.columns.forEach(item=>{
                if(item.key != "action"){
                  item.width = item.width+customColumnsAvgWidth;
                }
              });
            }else{
              this.columns = this.fixedColumns;
            }
          }
        }else{
             this.columns = this.fixedColumns;
        }
      })
    },
    getList2(param) {
      this.getAllocationListFieldsByKey();
      this.loading1 = true;
      this.loading2 = true;
      this.loading3 = true;
      param.keyword = param.keyword ? param.keyword : ""; //搜索去空格
      let _self = this;
      //查询城域网链路统计指标信息列表
      _self.$http
        .wisdomPost("/rpquality/selectLinkCountIndexData", Object.assign(param, this.order))
        .then(res => {
          if (res.code === 1) {
            var resData = res.data;
            this.getSnmpGraph({
              data: {
                snmpGraph: resData.snmpGraph,
                snmpBrokenTOPN: resData.snmpBrokenTOPN,
                snmpDegradationTOPN: resData.snmpDegradationTOPN
              }
            });
            if (resData.pages && resData.pages.records) {
              _self.tabList1 = resData.pages.records;
              _self.totalCount = resData.pages.total;
            } else {
              _self.tabList1 = [];
              _self.totalCount = 0;
            }
            this.loading1 = false;
          } else {
            this.$Message.error(res.msg)
          }
        }).catch((err) => {
          console.log(err)
          this.loading1 = false;
          this.loading2 = false;
          this.loading3 = false;
        }).finally(() => {
          this.loading1 = false;
          this.loading2 = false;
          this.loading3 = false;
        });
    },
    topSortChange({ order, key }) {
      //排序
      if (key === 'usableRate') {
        this.order.usableRateOrder = order;
      } else if (key === 'goodRate') {
        this.order.goodRateOrder = order;
      } else if (key === 'recentTime') {
        this.order.latestTimeOrder = order;
      } else if (key === 'interruptTimeCountNum') {
        this.order.brokenDurationOrder = order;
      } else if (key === 'inferTimeCountNum') {
        this.order.degradationDurationOrder = order;
      }
      this.getList2(this.query)
    },
    compare(orderBy, prop) {
      if (orderBy === "desc") {
        return function (pre, next) {
          let val1 = pre[prop];
          let val2 = next[prop];
          if (prop === "recentTime") {
            val1 = new Date(val1).getTime();
            val2 = new Date(val2).getTime();
          }
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1);
            val2 = Number(val2);
          }
          if (val1 < val2) {
            return 1;
          } else if (val1 > val2) {
            return -1;
          } else {
            return 0;
          }
        };
      } else if (orderBy === "asc") {
        return function (pre, next) {
          let val1 = pre[prop];
          let val2 = next[prop];
          if (prop === "recentTime") {
            val1 = new Date(val1).getTime();
            val2 = new Date(val2).getTime();
          }
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1);
            val2 = Number(val2);
          }
          if (val1 < val2) {
            return -1;
          } else if (val1 > val2) {
            return 1;
          } else {
            return 0;
          }
        };
      }
    },

    //切换页码
    pageChange(page) {
      this.pageNo = this.query.pageNo = page;
      this.getList2(this.query);
    },
    //切换每页条数
    tabParamPageSizeChange(e) {
      this.modalPage_no = this.queryModal.pageNo = 1;
      this.modalPageSize = this.queryModal.pageSize = e;
      this.getModalList(this.queryModal);
    },
    pageChangeModal(page) {
      this.modalPage_no = page;
      this.tabListModal = this.orderTabModalList.slice(
        (page - 1) * this.modalPageSize,
        this.modalPageSize * page
      );
      this.currentid = this.tabListModal[0].id;
      this.markPoint = this.tabListModal[0].startEventTime;
      this.echartLookParama2.startTime = moment(this.tabListModal[0].startEventTime).subtract(3, "hours").format("YYYY-MM-DD HH:mm:ss");
      if (this.tabListModal[0].state === this.$t('common_unrecovered')) {
        this.echartLookParama2.endTime = new Date().format('yyyy-MM-dd HH:mm:ss');
      } else {
        this.markPointGreen = this.tabListModal[0].endEventTime;
        this.echartLookParama2.endTime = moment(this.tabListModal[0].endEventTime, "YYYY-MM-DD HH:mm:ss").add(3, "hours").format("YYYY-MM-DD HH:mm:ss");
      }
      this.getSnmp(this.echartLookParama2);
    },
    //切换每页条数
    pageSizeChange(e) {
      this.pageNo = this.query.pageNo = 1;
      this.pageSize = this.query.pageSize = e;
      this.getList2(this.query)
    },
    //导出
    exportClick() {
      if (this.tabList1.length > 0) {
        let param = {
          keyword: this.query.keyword,
          startTime: this.query.startTime,
          endTime: this.query.endTime,
          type: this.query.type,
          faultType: this.query.faultType,
          usableRateOrder: "desc",
          pageNo: 1,
          pageSize: this.totalCount,
          groupId: this.query.groupId
        };
        //blob下载
        this.$Loading.start();
        axios({
          url: '/rpquality/exportRelayExcel',
          method: 'get',
          params: param,
          responseType: 'blob', // 服务器返回的数据类型
        }).then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.$t('rpmanager_quality_download_xls'));
          } else {
            var fileName = this.$t('rpmanager_quality_download_xlsx');

            const elink = document.createElement('a');//创建一个元素
            elink.download = fileName;//设置文件下载名
            elink.style.display = 'none';//隐藏元素
            elink.href = URL.createObjectURL(blob);//元素添加href
            document.body.appendChild(elink);//元素放入body中
            elink.click();//元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        }).catch(error => {
          console.log(error);
          this.$Loading.finish();
        }).finally(() => {
          this.$Loading.finish();
        });
      } else {
        this.$Message.warning(this.$t('specquality_no_data'));
      }
    },
    handleDuration(data) {
      //时间处理函数
      var theTime = parseInt(data); // 需要转换的时间秒
      var theTime1 = 0; // 分
      var theTime2 = 0; // 小时
      var theTime3 = 0; // 天
      if (theTime > 60) {
        theTime1 = parseInt(theTime / 60);
        theTime = parseInt(theTime % 60);
        if (theTime1 > 60) {
          theTime2 = parseInt(theTime1 / 60);
          theTime1 = parseInt(theTime1 % 60);
          if (theTime2 > 24) {
            //大于24小时
            theTime3 = parseInt(theTime2 / 24);
            theTime2 = parseInt(theTime2 % 24);
          }
        }
      }
      var result = "";
      if (theTime == 0) {
        result = "" + parseInt(theTime);
      }
      if (theTime == 0 && (theTime1 > 0 || theTime2 > 0 || theTime3 > 0)) {
        result = "" + parseInt(theTime) + this.$t('comm_second');
      }
      if (theTime > 0) {
        result = "" + parseInt(theTime) + this.$t('comm_second');
      }
      if (theTime1 > 0) {
        result = "" + parseInt(theTime1) + this.$t('comm_minutes') + result;
      }
      if (theTime2 > 0) {
        result = "" + parseInt(theTime2) + this.$t('comm_hour') + result;
      }
      if (theTime3 > 0) {
        result = "" + parseInt(theTime3) + this.$t('comm_day1') + result;
      }
      return result;
    },
    //查看详情
    actionClick(rowDate, type) {
      let _self = this;
      _self.healthLook = rowDate;
      this.ModalcheckAll = 0;
      if (type === "look") {
        this.queryModal = {
          keyword: "",
          startTime: this.dateValueCopy[0],
          endTime: this.dateValueCopy[1],
          taskId: rowDate.id,
          pageNo: this.queryModal.pageNo,
          pageSize: this.queryModal.pageSize
        };
      }
      _self.echartLookParama2.linkId = rowDate.linkId;
      _self.echartLookParama2.devIp = rowDate.devIp;
      _self.echartLookParama2.preNodeIp = rowDate.sourceIp;
      _self.echartLookParama2.nodeIp = rowDate.destIp;
      _self.echartLookParama2.orgId = rowDate.orgId;
      this.originalModalList = [];
      this.orderTabModalList = [];
      this.tabListModal = [];
      this.btnValue = 1;
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.goodRateData.useRateList = [];
      this.goodRateData.goodRateList = [];
      this.markPoint = "";
      this.markPointGreen = "";
      if (this.delayLossChart1) {
        this.delayLossChart1.clear();
      }
      if (this.delayLossChart2) {
        this.delayLossChart2.clear();
      }
      _self.getModalList(this.queryModal);
      _self.taskModal.show = true;
    },
    //查看导出
    exportViewClick() {
      // this.saveLog();
      let fileName = this.$t('qualityreport_dialog_rl_analysis') + ".xlsx";
      // this.$Loading.start();
      this.$axios({
        url: "/rpquality/exportLinkEvent",
        method: "post",
        data: this.queryModal,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // var fileName = "";
            // fileName = "故障清单.xlsx";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => {
          this.selectedIds = new Set();
          this.query.ids = null;
          this.queryClick();
          this.$Loading.finish();
        });
    },
    computedHeight() {
      // 单行，两行，三行以上高度
      if (this.tabListModal.length == 1) {
        return 135;
      } else if (this.tabListModal.length == 2) {
        return 199;
      } else if (this.tabListModal.length == 3) {
        return 200;
      }else {
        return 250;
      }
      // // 行数乘以单行高度
      // let rowsHeight = this.tabListModalData.length * rowHeight;
      // // 返回计算后的高度，不超过最大高度
      // console.log('=====.>',Math.min(rowsHeight, this.maxHeight));
      // return Math.min(rowsHeight, this.maxHeight);
    },
    getModalList(param) {


      if (this.query.type == 1) {
        this.columnsModal[1].title = this.$t('comm_event_start') //'事件开始时间';
        this.columnsModal[2].title = this.$t('comm_event_recovery') //'事件恢复时间';
        this.falutType = 1;
      } else if (this.query.type == 2) {
        this.columnsModal[1].title = this.$t('comm_failure_start') //'故障开始时间';
        this.columnsModal[2].title = this.$t('comm_failure_recovery') //'故障恢复时间'
        this.falutType = 2;
      }
      this.loadingModal = true;
      this.modalPage_no = 1
      this.$http.PostJson("/rpquality/selectLinkEvent", Object.assign(param, { type: this.query.type })).then(res => {
        if (res.code === 1) {
          if (res.data.records.length > 0) {
            this.originalModalList = res.data.records;
            this.orderTabModalList = JSON.parse(
              JSON.stringify(this.originalModalList)
            );
            this.tabListModal = this.orderTabModalList.slice(
              0,
              this.modalPageSize
            );
            this.totalCountModal = res.data.total;
            this.currentid = this.tabListModal[0].id;
            this.markPoint = this.tabListModal[0].startEventTime;
            this.echartLookParama2.startTime = moment(this.tabListModal[0].startEventTime).subtract(3, "hours").format("YYYY-MM-DD HH:mm:ss");
            if (this.tabListModal[0].state === this.$t('common_unrecovered')) {
              this.echartLookParama2.endTime = new Date().format('yyyy-MM-dd HH:mm:ss');
            } else {
              this.markPointGreen = this.tabListModal[0].endEventTime;
              this.echartLookParama2.endTime = moment(this.tabListModal[0].endEventTime, "YYYY-MM-DD HH:mm:ss").add(3, "hours").format("YYYY-MM-DD HH:mm:ss");
            }
            this.getSnmp(this.echartLookParama2);
          } else {
            this.initEchart();
            this.initEchart2();
          }
          this.loadingModal = false;
        } else {
          this.$Message.error(res.msg)
        }
      }).catch((err) => {
        console.log(err)
        this.loadingModal = false;
      }).finally(() => {
        this.loadingModal = false;
      });
    },
    async getSnmp(params) {
      this.loading = true;
      this.spinShow = true;
      this.noDataLoading = false;
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.goodRateData.useRateList = [];
      this.goodRateData.goodRateList = [];
      this.gooduseLength = 0;
      params.level = '';
      await this.$http
        .PostJson("/trend/getDelayAndLostTrend", params)
        .then(res => {
          //时延丢包趋势数据
          if (res.code === 1 && res.data) {
            this.preAlias.name = res.data ? res.data.preName : '';
            this.preAlias.port = res.data ? res.data.prePort : '';
            this.zAlias.name = res.data ? res.data.name : '';
            this.zAlias.port = res.data ? res.data.port : "";
            this.delayLossLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
            }
            let lineData = [];
            if (res.data.lineOne) {
              lineData = res.data.lineOne
            } else if (res.data.lineTwo) {
              lineData = res.data.lineTwo
            }
            let index = this.closest(lineData, this.markPoint);
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.startScale = false;
              this.delayLossScale = true;
              this.delayLossData.delay = res.data.lineOne;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.delayLossData.loss = res.data.lineTwo;
            }
            this.delayStart = 0;
            // if (res.data.lineOnetotal != 0 && res.data.lineOnetotal <= 300) {
            this.delayEnd = 100;
            this.startValue = res.data.lineOne[0][0];
            this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
            // } else if (
            //   res.data.lineOnetotal != 0 &&
            //   res.data.lineOnetotal > 300
            // ) {
            //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
            //   if (index !== 'noPoint') {
            //     if (index <= 150) {
            //       this.startValue = res.data.lineOne[0][0];
            //       if (index + 150 < res.data.lineOnetotal - 1) {
            //         this.endValue = res.data.lineOne[index + 150][0];
            //       } else {
            //         this.endValue =
            //           res.data.lineOne[res.data.lineOne.length - 1][0];
            //       }
            //     } else {
            //       this.startValue = res.data.lineOne[index - 150][0];
            //       if (index + 150 < res.data.lineOnetotal) {
            //         this.endValue = res.data.lineOne[index + 150][0];
            //       } else {
            //         this.endValue =
            //           res.data.lineOne[res.data.lineOne.length - 1][0];
            //       }
            //     }
            //   }
            // } else {
            //   this.delayEnd = 100;
            // }
            this.$store.commit("updateDelayLossHistory", {
              level: res.data.level,
              datas: [this.delayLossData.delay, this.delayLossData.loss],
            }); //保存数据
            this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
          }
        });

      let trendParam = JSON.parse(JSON.stringify(params));
      trendParam = Object.assign(trendParam, { snmp: true, info: "中继质差分析" });
      trendParam.level = '';
      await this.$http
        .PostJson("/trend/getDataTrend", trendParam)
        .then(res => {
          //流速趋势数据
          if (res.code === 1) {
            let flowUnit = 1;
            this.flowLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              this.echart2.show = false;
              this.flowLevel = 99;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.echart2.show = true;
              let unitChange = this.getFlowUnit(res.data.lineTwo);
              this.flowUnit = unitChange[0];
              this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
              this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
              flowUnit = unitChange[1];
              this.flowData.enter = res.data.lineTwo;
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.flowData.issue = res.data.lineOne;
            }
            this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.$store.commit("updateFlowHistory", {
              level: res.data.level,
              datas: [this.flowData.issue, this.flowData.enter],
            });
            this.$store.commit("setflowUnit", {
              level: res.data.level,
              unit: this.flowUnit
            }); //保存单位
          }
        });
      this.startScale = false;
      params.level = 1;
      await this.$http
        .PostJson("/trend/getGoodAndUsableRateTrend", params)
        .then(res => {
          //优良率趋势数据
          if (res.code === 1) {
            this.startScale2 = false;
            this.goodRateLevel = res.data.level;
            if (!res.data.lineOne && !res.data.lineTwo) {
              this.goodRateLevel = 99;
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.goodRateData.useRateList = res.data.lineOne;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.goodRateData.goodRateList = res.data.lineTwo;
            }
            this.delayStart2 = 0;
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.delayEnd2 = 100;
              this.startValue2 = res.data.lineOne[0][0];
              this.endValue2 = res.data.lineOne[res.data.lineOne.length - 1][0];
            }
          }
        });
      this.spinShow = false;
      if (this.echart2.show) {
        this.height = 500;
      } else if (!this.echart2.show) {
        this.height = 260;
      }
      if (this.btnValue == 1) {
        this.initEchart();
      } else {
        this.initEchart2();
      }
    },
    setDelayLossTooltip() {
      let _self = this;
      return eConfig.tip("axis", function (param) {
        if (_self.btnValue == 1) {
          _self.scale = param[0].data[0];
        } else if (_self.btnValue == 2) {
          _self.scale2 = param[0].data[0];
        }
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex]
            ? ""
            : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let src = "",
          delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "",
          rateTime = "",
          rateTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                ? "--"
                :
                _self.flowSize(param[i].value[1], true, true)
              ) +
              "<br />";
          }
          if (
            param[i].seriesIndex === 4 ||
            param[i].seriesIndex === 5 ||
            param[i].seriesIndex === 6 ||
            param[i].seriesIndex === 7
          ) {
            rateTime = param[i].data[0] + "<br />";
            rateTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            rateTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === ""
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip + rateTime + rateTip;
      });
    },
    Option() {
      let optionArr = [
        {
          title: [
            {
              show: (this.delayLossData.delay.length < 1 && this.delayLossData.loss.length < 1) ? true : false,
              text: this.$t('common_No_data'),
              left: 'center',
              top: '80',
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : '#847f7f'
              }
            },
            {
              show: (this.echart2.show && this.flowData.enter.length < 1 && this.flowData.issue.length < 1) ? true : false,
              text: this.$t('common_No_data'),
              left: 'center',
              bottom: '160',
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : '#847f7f'
              }
            },
          ],
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all"
            }
          },
          grid: [
            {
              left: "4%",
              top: "40px",
              width: "92%",
              height: this.echart2.show ? "140px" : "140px"
            },
            {
              left: "6%",
              top: "270px",
              width: "90%",
              height: this.echart2.show ? "140px" : "0px"
            }
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold"
              },
              color: this.delayLossColor,
              data: [this.$t('speed_delay'), this.$t('comm_packet_loss')]
            },
            {
              show: this.echart2.show,
              top: "220px",
              right: "43%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold"
              },
               color: this.flowColor,
              data: [this.$t('specquality_incoming_velocity'), this.$t('specquality_exit_velocity')]
            }
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2"
                }
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                }
              },
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                }
              }
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2"
                }
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                }
              },
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                }
              }
            }
          ],
          yAxis: [
            {
              name: this.$t('comm_delay(ms)'),
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2"
                }
              }
            },
            {
              name: this.$t('comm_loss_rate'),
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "right",
              max: 100,
              min: 0,
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2"
                }
              }
            },
            {
              show: this.echart2.show,
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2"
                }
              },
              axisLabel: {
                show: true,
                formatter: value => {
                  let resualt = this.getUnit(value, true, true);
                  // if (text.length > 6) {
                  //   resualt = text.substring(0, 6) + '..';
                  // } else {
                  //   resualt = text;
                  // }
                  return resualt;
                }
              }
            }
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue,
              endValue: this.endValue
            },
            {
              type: "slider",
              top: this.echart2.show ? "460px" : "230px",
              height: 20,
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , // "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , //  top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            }
          ],
          series: [
            {
              type: "line",
              name: this.$t('speed_delay'),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbol:
                this.delayLossData.delay.length > 1 ? "none" : "none",
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.delayColor_0[this.currentSkin] , // "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.delayColor_8[this.currentSkin] , // "rgba(0, 255, 238, 0.2)",
                    },
                  ]
                }
              },
              data: this.delayLossData.delay,
              markLine: {
                symbol: ["pin", "none"],
                symbolSize: 15,
                data: [
                  {
                    symbol: 'image://' + pointRed,
                    symbolSize: 10,
                    xAxis: this.markPoint,
                    symbolRotate: '0',
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint
                      },
                      // backgroundColor: top.window.isdarkSkin == 1 ?"#5CA0D5" :"#fff",
                    }
                  },
                  {
                    symbol: 'image://' + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen,
                    symbolRotate: '0',
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: top.window.isdarkSkin == 1 ?"#5CA0D5" :"#fff",
                      formatter: () => {
                        return this.markPointGreen
                      },
                    }
                  }
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1
                  }
                }
              }
            },
            {
              type: "line",
              name: this.$t('comm_packet_loss'),
              xAxisIndex: 0,
              yAxisIndex: 1,
              smooth: true,
              symbol:
                this.delayLossData.loss.length > 1 ? "none" : "none",
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.lossColor_0[this.currentSkin] , //  "rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color:  eConfig.areaStyle.lossColor_8[this.currentSkin] , // "rgba(2, 144, 253, 0.2)",
                    },
                  ]
                }
              },
              data: this.delayLossData.loss
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_incoming_velocity'),
              type: "line",
              smooth: true,
              symbol: this.flowData.enter.length > 1 ? "none" : "none",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowInColor_0[this.currentSkin] , //  "rgba(71,142,233, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowInColor_8[this.currentSkin] , //  "rgba(71,142,233, 0.2)"
                    }
                  ]
                }
              }
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_exit_velocity'),
              type: "line",
              smooth: true,
              symbol: this.flowData.issue.length > 1 ? "none" : "none",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] , //"rgba(241,145,73, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowOutColor_8[this.currentSkin] , //"rgba(241,145,73, 0.2)"
                    }
                  ]
                }
              }
            }
          ]
        }
      ];
      return optionArr[0];
    },
    ip2int(ip_str) {
      const type = Object.prototype.toString.call(ip_str)
      if (type !== '[object Array]') {
        ip_str = ip_str.split('.')
      }
      if (ip_str.length !== 4) return 0
      ip_str = ip_str.map(i => Number(i))
      if (ip_str.find(i => i < 0 || i > 255)) return 0
      const t = (prev, cur) => prev * Number(1 << 8) + cur
      return ip_str.reduce(t, Number(0))
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart1) {
        that.delayLossChart1.clear();
      }
      top.document.getElementById("rpquality-delayLoss").style.height = this.height + "px";
      that.delayLossChart1 = echarts.init(this.$refs["rpquality-delayLoss"]);
      that.delayLossChart1.resize();
      /*设置时延丢包率echart图*/
      that.delayLossChart1.setOption(this.Option());
      that.delayLossChart1.getZr().on("mousewheel", (params) => {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据
        var pointInPixel = [params.offsetX, params.offsetY];
        if (
          that.delayLossChart1.containPixel(
            { gridIndex: [0, 1, 2] },
            pointInPixel
          )
        ) {
          let startValue =
            that.delayLossChart1.getModel().option.dataZoom[0].startValue;
          let endValue =
            that.delayLossChart1.getModel().option.dataZoom[0].endValue;
          this.echartLookParama2.startTime = new Date(startValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          this.echartLookParama2.endTime = new Date(endValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);
          let differ = endValue - startValue;
          let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
          let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
          let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
          var levelNum = "";
          let start = that.delayLossChart1.getModel().option.dataZoom[0].start;
          let end = that.delayLossChart1.getModel().option.dataZoom[0].end;

          if (params.wheelDelta >= 0 && that.startScale == false) {
            if (this.delayLossLevel == 1) {
              // 小时粒度
              if (differ < minute) {
                if (!that.startScale) {
                  that.startScale = true;
                  levelNum = 2;
                  let delayParam = Object.assign(this.echartLookParama2, {
                    level: levelNum,
                  });
                  let flowParam = Object.assign(delayParam, {
                    snmp: this.dataSource == 1 ? true : false,
                  });
                  this.getDelayLoss(delayParam, flowParam);
                }
              }
            }
          } else {
            if (start == 0 && end == 100) {
              if (!that.startScale) {
                //是否处在缩放过程中
                if (that.delayLossLevel == getTlevel) {
                  that.setPs(that.delayLossLevel, [
                    that.timeChange(startValue),
                    that.timeChange(endValue),
                  ]);
                  that.startScale = false;
                } else {
                  let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                  if (that.flowLevel == getflowTlevel) {
                    that.startScale = false;
                  } else {
                    if (that.flowLevel == 2) {
                      that.flowLevel = 1;
                      that.startScale = true;
                      that.flowUnit = getflowSaveUnit.HoursUnit;
                      that.flowData.enter = getflowSaveData.HoursData.enter;
                      that.flowData.issue = getflowSaveData.HoursData.issue;
                    } else if (that.flowLevel == 3 || that.flowLevel == 4) {
                      that.flowLevel = 2;
                      that.startScale = true;
                      that.flowUnit = getflowSaveUnit.minuteUnit;
                      that.flowData.enter = getflowSaveData.minuteData.enter;
                      that.flowData.issue = getflowSaveData.minuteData.issue;
                    }
                  }

                  if (that.delayLossLevel == getTlevel) {
                    that.startScale = false;
                  } else if (that.delayLossLevel == 2) {
                    that.delayLossLevel = 1;
                    that.startScale = true;
                    that.delayLossData.delay = getSaveData.HoursData.delay;
                    that.delayLossData.loss = getSaveData.HoursData.loss;
                    that.delayStart = getSite.psH.start;
                    that.startValue = getSite.psH.start;
                    that.delayEnd = getSite.psH.end;
                    that.endValue = getSite.psH.end;
                  } else if (
                    that.delayLossLevel == 3 ||
                    that.delayLossLevel == 4
                  ) {
                    that.delayLossLevel = 2;
                    that.startScale = true;
                    that.delayLossData.delay = getSaveData.minuteData.delay;
                    that.delayLossData.loss = getSaveData.minuteData.loss;
                    that.delayStart = getSite.psM.start;
                    that.startValue = getSite.psM.start;
                    that.delayEnd = getSite.psM.end;
                    that.endValue = getSite.psM.end;
                  }

                  setTimeout(() => {
                    that.startScale = false;
                    that.initEchart();
                  }, 300);
                }
              }
            }
          }
        }
      });
    },
    async getDelayLoss(
      delayParam,
      flowParam,
      cachF,
      hoverDelayTime,
    ) {
      let isUpdate = false;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        this.spinShow = true;
        await this.$http
          .PostJson("/trend/getDelayAndLostTrend", delayParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.delayLossLevel = res.data.level;
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              let index = this.closest(lineData, this.markPoint);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.delayStart = 0;
                // if (res.data.lineOnetotal <= 300) {
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue =
                  res.data.lineOne[res.data.lineOne.length - 1][0];
                // } else {
                //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
                //   if (index <= 150) {
                //     this.startValue = res.data.lineOne[0][0];
                //     if (index + 150 < res.data.lineOnetotal - 1) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //         res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   } else {
                //     this.startValue = res.data.lineOne[index - 150][0];
                //     if (index + 150 < res.data.lineOnetotal) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //         res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   }
                // }
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            }
          });
      }

      if (flowParam.level != 99) {

        flowParam.interfaceIp = this.ip2int(flowParam.devIp);
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, { snmp: true, info: "中继质差分析" });
        await this.$http
          .PostJson("/trend/getDataTrend", trendParam)
          .then((res) => {
            //流速趋势数据
            if (res.code === 1) {
              let flowUnit = 1;
              this.flowLevel = res.data.level;
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                let unitChange = this.getFlowUnit(res.data.lineOne);
                this.flowUnit = unitChange[0];
                this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
                this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
                flowUnit = unitChange[1];
                this.flowData.enter = res.data.lineTwo.map((item) => {
                  return [item[0], item[1]];
                });
              }
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.flowData.issue = res.data.lineOne.map((item) => {
                  return [item[0], item[1]];
                });
              }

              this.$store.commit("updateFlowHistory", {
                level: res.data.level,
                datas: [this.flowData.enter, this.flowData.issue],
              }); //保存数据
              this.$store.commit("setflowUnit", {
                level: res.data.level,
                unit: this.flowUnit,
              }); //保存单位
            }
          });
      }

      this.delayLoading = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      this.spinShow = false;
      if (isUpdate) {
        this.initEchart();
      }
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },
    Option2() {
      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          title: {
            show: (this.goodRateData.useRateList.length < 1 && this.goodRateData.goodRateList.length < 1) ? true : false,
            text: this.$t('common_No_data'),
            left: 'center',
            top: '80',
            textStyle: {
              color: '#847f7f'
            }
          },
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all"
            }
          },
          grid: [
            {
              left: "4%",
              top: "40px",
              width: "92%",
              height: "140px"
            }
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold"
              },
              color: this.delayLossColor,
              // 可用率
              // 优良率
              data: [this.$t('specquality_availability'), this.$t('specquality_good_rate')]
            }
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            }
          ],
          yAxis: [
            {
              name: this.$t('comm_unit') + "(" + this.goodRateUnit + ")",
              type: "value",
              scale: true,
              gridIndex: 0,
              max: 100,
              position: "left",
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2"
                }
              }
            }
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0],
              startValue: this.startValue2,
              endValue: this.endValue2
            },
            {
              type: "slider",
              top: "230px",
              height: 20,
              xAxisIndex: [0],
              realtime: true,
              startValue: this.startValue2,
              endValue: this.endValue2,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , // "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color:eConfig.dataZoom.textStyle.color[this.currentSkin] , //  top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            }
          ],
          series: [
            {
              name: this.$t('specquality_availability'),
              type: "line",
              z: 2,
              smooth: true,
              legendHoverLink: false,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol: this.goodRateData.useRateList.length > 1 ? "none" : "none",
              symbolSize: 4,
              emphasis: { scale: false },
              color: this.goodRateColor[2],
              data: this.goodRateData.useRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)"
                    }
                  ]
                }
              }
            },
            {
              name: this.$t('specquality_good_rate'),
              type: "line",
              z: 2,
              smooth: true,
              legendHoverLink: false,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol: this.goodRateData.goodRateList.length > 1 ? "none" : "none",
              symbolSize: 4,
              emphasis: { scale: false },
              color: this.goodRateColor[3],
              data: this.goodRateData.goodRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(36,195,197, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: "rgba(36,195,197, 0.2)"
                    }
                  ]
                }
              }
            }
          ]
        }
      ];
      return optionArr[0];
    },
    initEchart2() {
      let that = this;
      if (that.delayLossChart2) {
        that.delayLossChart2.clear();
      }
      that.delayLossChart2 = echarts.init(this.$refs["rpquality-delayLoss2"]);

      /*设置时延丢包率echart图*/
      that.delayLossChart2.setOption(this.Option2());
    },
    //时间推算
    setTime(level, time) {
      let Interval = 0,
        start = new Date(this.echartLookParama2.startTime).getTime(),
        end = new Date(this.echartLookParama2.endTime).getTime(),
        newStart = 0,
        newEnd = 0;
      if (level == 0) {
      } else if (level == 1) {
        Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 2) {
        Interval = 12 * 60 * 60 * 1000;
      }
      newStart = time - Interval;
      newEnd = time + Interval;

      if (newStart < start) {
        newStart = start;
      }
      if (newEnd > end) {
        newEnd = end;
      }
      newStart = this.timeChange(newStart);
      newEnd = this.timeChange(newEnd);
      return [newStart, newEnd];
    },
    editCancel() { },
    /*趋势图指标切换*/
    btnClick(num) {

      this.btnValue = num;
      // this.initEchart2()
      if (this.btnValue == 1) {
        setTimeout(() => {
          this.initEchart();
        }, 500);
        // this.initEchart();
      } else {
        setTimeout(() => {
          this.initEchart2();
        }, 500);
        // this.initEchart2();
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.interValCurTime);
    if (sessionStorage.getItem("delayPs")) {
      sessionStorage.removeItem("delayPs");
    }
    if (sessionStorage.getItem("delayPs2")) {
      sessionStorage.removeItem("delayPs2");
    }
    this.$store.commit("setdelayLTlevel", "");
    this.$store.commit("updateDelayLossHistory", -1);
    this.$store.commit("setflowTlevel", "");
    this.$store.commit("updateFlowHistory", -1);
    this.$store.commit("setgoodRateTlevel", "");
    this.$store.commit("updategoodRateHistory", -1);
  }
};
</script>

<style>
.table-item {
  display: flex;
  justify-content: space-between;
}

.action-btn {
  color: #05eeff;
  cursor: pointer;
}

.topTable td {
  background-color: var(--table_td_bg_color, #061824) !important;
  height: 45px !important;
}

.taskStrategy-modal .ivu-table-header th {
  background: #f1f6fe !important;
}

.fault-tab .ivu-table th.bgColor {
  background: #f1f6fe !important;
}

.fault-tab b {
  font-weight: bold !important;
}

.fault-tab .ivu-table-wrapper-with-border {
  border: 1px solid #dcdee2;
  border-bottom: 0;
  border-right: 0;
}

.tableBorder .ivu-table:after {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background-color: #dcdee2;
  z-index: 3;
}

.taskStrategy-modal .ivu-table-wrapper-with-border {
  border: 1px solid #dcdee2;
  border-right: 0;
}

.healthManage p {
  text-align: left !important;
  margin-bottom: 5px !important;
}
.echarts-pie {
  width: 208px;
  height: 208px;
  margin: 50px auto 34px;
}

.pie-div {
  background-color: var(--body_conent_b_color, #061824);
  padding: 15px;
}

.echarts-pie {
  width: 208px;
  height: 208px;
  margin: 0 auto;
}

.pie-example {
  display: flex;
  justify-content: space-around;
}

.pie-example-box {
  display: flex;
}

.pie-example-box .icon-box {
  display: block;
  width: 36px;
  height: 36px;
  margin: 0 auto 13px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 36px;
}

.icon-normal {
  background-image: url("../../../assets/wisdom/icon-chart1.png");
}

.light-icon-normal {
  background-image: url("../../../assets/wisdom/light-icon-chart1.png");
}
.icon-degradation {
  background-image: url("../../../assets/wisdom/icon-chart2.png");
}

.icon-end {
  background-image: url("../../../assets/wisdom/icon-chart3.png");
}

.example-right {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 5px;
}

.example-header {
  display: flex;
  align-items: center;
}

.example-title {
  font-size: 14px;
  color: var(--th_font_color, #303748) !important;
  text-align: center;
  height: 14px;
  line-height: 14px;
}

.example-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--th_font_color, #303748) !important;
  height: 24px;
  line-height: 24px;
  margin-left: 5px;
}

.example-beliel {
  text-align: center;
  font-size: 16px;
  color: #5ca0d5;
  height: 18px;
  line-height: 18px;
}

.pie-example-hover {
  cursor: pointer;
}

.task-modal .ivu-modal-body {
  font-size: 14px !important;
}

.task-modal .ivu-modal-body .taskStrategy-modal a,
.task-modal .ivu-modal-body .taskStrategy-modal .ivu-page-total {
  font-size: 14px !important;
  vertical-align: middle;
}

.task-modal .ivu-modal-body .taskStrategy-modal .snmp-content-right {
  font-size: 14px;
}

.wisdom-fault .wisdom-fault-top .fault-top-box .ivu-picker-confirm button {
  width: auto !important;
  height: auto !important;
}

.btnChange > a {
  display: inline-block;
  vertical-align: top;
  width: 120px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  /* color: var(--font_color, #303748); */
  font-weight: bold;
  /* border: solid 1px #e6e8ee; */

  color: var(--btnChange_color, #5ca0d5);
  border: solid 1px var(--btnChange_border_color, #02b8fd);
}

.btnChange > a.active {
  /* color: #fff; */
  /* background-color: #4e7bff; */

  color: var(--btnChange_active_color, #060d15);
  background-color: var(--btnChange_active_bg_color, #4e7bff);
  border: 0px;
}

.my-table-fixed .ivu-table-fixed {
}

.headerSelectArea .ivu-select-selection {
  border: none;
  background-color: #f1f6fe;
}

.headerSelectArea.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
</style>
<style scoped>
.headerSelectArea.ivu-select-single
  .ivu-select-selection
  .ivu-select-placeholder,
.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  font-size: 12px !important;
}
</style>

