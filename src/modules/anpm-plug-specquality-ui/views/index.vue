<template>
  <div
    :class="{ 'light-no-tabs': currentSkin == 0, padding0: reportState == 1 }"
  >
    <section class="sectionBox special">
      <div class="section-top">
        <Row class="fn_box">
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadData"
                  @onSelectChange="setOrg"
                  @onFocus="focusFn"
                  @onClear="onClear"
                ></TreeSelect>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_isp") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  v-model="query.operator"
                  :placeholder="$t('specinfo_select_carrier')"
                >
                  <Option
                    v-for="item in operatorList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("specinfo_perspective") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  clearable
                  v-model="query.statisticalAngle"
                  :placeholder="$t('snmp_pl_man')"
                >
                  <Option
                    v-for="item in statisticalAngleArrayList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <DatePicker
                  format="yyyy-MM-dd"
                  type="daterange"
                  :options="timeOptions"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="true"
                  style="width: 100%"
                  :confirm="false"
                >
                </DatePicker>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label">{{ $t("comm_group") }}：</label>
              <div class="fn_item_box">
                <Select
                  v-model="query.groupIds"
                  multiple
                  :max-tag-count="1"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                >
                  <Option
                    v-for="item in groupList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("alarm_fault_type") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.faultType"
                  filterable
                  clearable
                  :only-filter-with-text="true"
                >
                  <Option
                    v-for="item in faultTypeList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
              <div class="fn_item_box">
                <Input
                  v-model="query.keyWord"
                  clearable
                  :title="$t('specquality_fuzzy')"
                  :placeholder="$t('specquality_fuzzy')"
                />
              </div>
            </div>
          </Col>
        </Row>

        <div class="tool-btn">
          <div>
            <Button
              v-if="permissionObj.list"
              :loading="queryLoading"
              class="query-btn"
              icon="ios-search"
              type="primary"
              @click="queryClick"
              :title="$t('common_query')"
            ></Button>
          </div>
        </div>
      </div>

      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <div>
            <Row class="pie-div">
              <i-col span="7">
                <div
                  class="pie-box"
                  v-for="(item, index) in pie_List"
                  :key="index"
                >
                  <div class="pie-content">
                    <div class="echarts-pie">
                      <echarts-pie
                        :node="item.node"
                        :pieData="item"
                        :isdarkSkin="currentSkin"
                      ></echarts-pie>
                    </div>
                    <div class="pie-example">
                      <Tooltip
                        :content="items.content"
                        v-for="(items, indexes) in item.data"
                        :key="indexes"
                        max-width="200"
                      >
                        <div
                          class="pie-example-box"
                          :class="[
                            items.name === $t('common_Normal') ||
                            items.name === $t('common_line')
                              ? ''
                              : 'pie-example-hover',
                          ]"
                        >
                          <i
                            class="icon-box"
                            :class="[
                              indexes === 0
                                ? currentSkin == 1
                                  ? 'icon-normal'
                                  : 'light-icon-normal'
                                : indexes === 1
                                ? 'icon-degradation'
                                : 'icon-end',
                            ]"
                          ></i>
                          <div class="example-right">
                            <div class="example-header">
                              <div class="example-title">{{ items.name }}</div>
                              <span class="example-value">{{
                                items.value
                              }}</span>
                            </div>
                            <div class="example-beliel">
                              {{ items.Percent }}%
                            </div>
                          </div>
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </i-col>
              <i-col span="17">
                <Row style="margin-top: 10px" :gutter="5">
                  <i-col span="12" style="position: relative">
                    <Loading :loading="loading2"></Loading>
                    <p style="text-align: left; line-height: 32px">
                      <b>{{ $t("comm_interruption_top5") }}:</b>
                    </p>
                    <Table
                      class="topTable"
                      ref="tableList"
                      stripe
                      :columns="columns2"
                      :data="tableData2"
                      height="290"
                      :no-data-text="
                        loading2
                          ? ''
                          : tabList2.length > 0
                          ? ''
                          : currentSkin == 1
                          ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                            $t('common_No_data') +
                            '</p></div>'
                          : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                            $t('common_No_data') +
                            '</p></div>'
                      "
                      size="small"
                    ></Table>
                  </i-col>

                  <i-col span="12" style="position: relative">
                    <Loading :loading="loading3"></Loading>
                    <p style="text-align: left; line-height: 32px">
                      <b>{{ $t("comm_degradation_top5") }}:</b>
                    </p>
                    <Table
                      class="topTable"
                      ref="tableList"
                      stripe
                      :columns="columns3"
                      :data="tableData3"
                      height="290"
                      :no-data-text="
                        loading3
                          ? ''
                          : tabList3.length > 0
                          ? ''
                          : currentSkin == 1
                          ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                            $t('common_No_data') +
                            '</p></div>'
                          : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                            $t('common_No_data') +
                            '</p></div>'
                      "
                      size="small"
                    ></Table>
                  </i-col>
                </Row>
                <Row style="margin-top: 10px"> </Row>
              </i-col>
            </Row>
            <Row style="margin-top: 10px">
              <i-col span="24" class="tableBorder" style="position: relative">
                <Loading :loading="loading1"></Loading>
                <div style="height: 38px; width: 100%; margin-bottom: 8px">
                  <b class="fleft" style="">{{ $t("specquality_index") }}</b>
                  <a
                    v-if="permissionObj.export"
                    id="exportAll"
                    target="_blank"
                    class="fright"
                  >
                    <!-- 导出 -->
                    <template>
                      <Dropdown placement="bottom-start">
                        <Button
                          class="daoChu-btn"
                          type="primary"
                          id="exportData"
                          :title="this.$t('but_export')"
                        >
                          <i class="iconfont icon-icon-derive" />
                        </Button>
                        <Dropdown-menu slot="list">
                          <Dropdown-item
                            ><a
                              href="javascript:void(0)"
                              @click="exportsingleClick"
                              >{{ $t("but_export_checked") }}</a
                            ></Dropdown-item
                          >
                          <Dropdown-item
                            ><a
                              href="javascript:void(0)"
                              @click="exportClick"
                              >{{ $t("but_export_all") }}</a
                            ></Dropdown-item
                          >
                        </Dropdown-menu>
                      </Dropdown>
                    </template>
                  </a>
                </div>
                <Table
                  ref="tableListspecial"
                  class="my-table-fixed fixed-left-right"
                  stripe
                  :columns="columns1"
                  :data="tableData1"
                  :no-data-text="
                    loading1
                      ? ''
                      : tabList1.length > 0
                      ? ''
                      : currentSkin == 1
                      ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                        $t('common_No_data') +
                        '</p></div>'
                      : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                        $t('common_No_data') +
                        '</p></div>'
                  "
                  size="small"
                  @on-select="selectsingle"
                  @on-select-cancel="cancelsingle"
                  @on-select-all="selectPage"
                  @on-select-all-cancel="selectPage"
                  @on-sort-change="sortChange"
                ></Table>
                <div class="tab-page">
                  <Page
                    v-page
                    v-if="tabList1.length > 0"
                    :current.sync="pageNo"
                    :page-size="pageSize"
                    :total="totalCount"
                    :page-size-opts="pageSizeOpts"
                    :prev-text="$t('common_previous')"
                    :next-text="$t('common_next_page')"
                    @on-change="pageChange"
                    @on-page-size-change="pageSizeChange"
                    show-elevator
                    show-sizer
                  >
                  </Page>
                </div>
              </i-col>
            </Row>
          </div>
        </div>
      </div>

      <!--查看-->

      <Modal
        sticky
        v-model="look.show"
        :width="modalWidth"
        :styles="{ top: '100px' }"
        class="task-modal no-padding-modal"
        draggable
        footer-hide
        @on-visible-change="resetData"
      >
        <div slot="header">
          <span class="title-name">{{ this.look.title }}</span>
          <span class="title-content">
            {{ this.look.titleDetails }}
          </span>
        </div>
        <section class="taskStrategy-modal">
          <Row type="flex" justify="end" class="btn-row-bg">
            <Button
              class="daoChu-btn"
              id="exportData"
              type="primary"
              @click="exportViewClick()"
              :title="this.$t('but_export')"
            >
              <i class="iconfont icon-icon-derive" />
            </Button>
          </Row>
          <Table
            ref="tableListModal"
            stripe
            :height="computedHeight()"
            style="width: 100%"
            :columns="columnsModal"
            :data="tabListModalData"
            :loading="ModalTableLoading"
            @on-sort-change="sortModal"
            class="modalTable modalTableover"
            :no-data-text="
              ModalTableLoading
                ? ''
                : tabListModal.length > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
          >
          </Table>
          <div class="tab-page">
            <Page
              v-page
              class="infoListPage"
              :current.sync="modalPage_no"
              :page-size="modalPageSize"
              :page-size-opts="[3, 10, 50, 100, 200, 500, 1000]"
              :total="totalCountModal"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChangeModal"
              @on-page-size-change="tabParamPageSizeChange"
              show-total
              show-sizer
            >
            </Page>
          </div>
          <div v-if="tabListModal.length > 0">
            <div class="wisdom-snmp-content" style="position: relative">
              <div class="btnChange">
                <a
                  href="javascript:void(0);"
                  :class="[btnValue == 1 ? 'active' : '']"
                  @click="btnClick(1)"
                  >{{ $t("specquality_basic_index") }}</a
                >
                <a
                  href="javascript:void(0);"
                  :class="[btnValue == 2 ? 'active' : '']"
                  @click="btnClick(2)"
                  >{{ $t("specquality_operating_index") }}</a
                >
              </div>
              <!--趋势图-->
              <div class="lookBox">
                <Spin
                  fix
                  v-show="chartLoading"
                  style="width: 100%; height: 100%"
                  >{{ $t("alarm_load") }}
                  <Icon
                    type="ios-loading"
                    size="18"
                    class="demo-spin-icon-load"
                  ></Icon>
                </Spin>
                <div class="title" style="margin-left: 0px">
                  {{ healthLook.specialName }}({{ healthLook.specialAIp }}——>{{
                    healthLook.specialZIp
                  }})
                </div>
                <div class="contain">
                  <div
                    ref="specquality-delayLoss"
                    v-show="btnValue == 1"
                    id="specquality-delayLoss"
                    class="echartStyle"
                    :style="'height:' + height + 'px;width:' + echartsWidth"
                  ></div>
                  <div
                    ref="specquality-delayLoss2"
                    v-show="btnValue == 2"
                    id="specquality-delayLoss2"
                    class="echartStyle"
                    :style="'height:' + height2 + 'px;width:' + echartsWidth"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Modal>

      <!--自定义列表项 修改之后-->
      <Modal
        sticky
        v-model="customModalShow"
        width="540"
        height="720"
        class="threshold-modal modal-selected"
        :title="$t('custom_list_items')"
        draggable
        :mask="true"
        :loading="customModalShowLoading"
      >
        <Row class="modal-selected-row">
          <Col>
            <Table
              ref="fieldsModalTableData"
              height="550"
              width="400"
              :show-header="false"
              :columns="fieldsColumns"
              :data="allocationListFields"
              @on-row-click="getFieldsColumnsIndex"
              :row-class-name="rowClassName"
            >
            </Table>
          </Col>
          <Col>
            <div class="btn-box">
              <Tooltip :content="$t('dash_up')" placement="right">
                <div class="sort-btn" @click="moveUp">
                  <Icon
                    type="md-arrow-up"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
              <Tooltip :content="$t('dash_down')" placement="right">
                <div class="sort-btn" @click="moveDown">
                  <Icon
                    type="md-arrow-down"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
            </div>
          </Col>
        </Row>
        <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
import locationreload from "@/common/locationReload";

const pointGreen = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC';
const pointRed = 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC';
import echartsPie from "@/common/echarts/base-pieHealth.vue";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import echartFn from "@/common/mixins/echartFun";
import global from "@/common/global.js";
import "../../../timechange";
import axios from "axios";
import "@/config/page.js";
import langFn  from '@/common/mixins/langFn'

let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/dataZoom");
require("echarts/lib/component/markLine");
import eConfig from "@/config/echart.config.js";
import {addDraggable} from "@/common/drag.js";
import moment from "moment";
import {tableEditBtn} from '@/assets/base64Img/img'
import ipv6Format from "@/common/ipv6Format";
import {tableEditLightBtn} from '@/assets/base64Img/img'



function timeToStringMethod(times, _this) {
  const regPos = /^[0-9]+.?[0-9]*/; //判断是否是数字。
  if (regPos.test(times)) {
    var time = times,
        timeStr = "";
    var day = parseInt(time / 60 / 60 / 24);
    var hours = parseInt((time / 60 / 60) % 24);
    var minuts = parseInt((time / 60) % 60);
    var second = parseInt(time % 60);
    if (day > 0) {
      timeStr = day + _this.$t('comm_day1');
    }
    if (hours > 0) {
      timeStr += hours + _this.$t('comm_hour');
    }
    if (minuts > 0) {
      timeStr += minuts + _this.$t('comm_minutes');
    }
    if (second >= 0) {
      timeStr += second + _this.$t('comm_second');
    }
    return timeStr;
  } else {
    return 0 + _this.$t('comm_second');
  }
}

function getQueryVariable(variable) {
  var url = top.document.getElementById('sub-content-page').src;
  var query = url.indexOf('?') > 0 ? url.split('?')[1] : null;
  if (query) {
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == variable) {
        return pair[1].replace("%20", " ");
      }
    }
  } else {
    // 处理质量报告页面的参数
    var element = top.document.getElementById("specquality");
    if (element) {
      url = element.src;
      query = url.indexOf("?") > 0 ? url.split("?")[1] : null;
      if (query) {
        var params = query.split("param=");
        if (params && params.length > 0) {
          params = params[1];
        }
        if (params) {
          query = unescape(params);
          query = JSON.parse(query);
          if (query) {
            return query[variable];
          }
        }
      }

    }

  }
  return false;
}

export default {
  name: "healthManagement",
  mixins: [echartFn,langFn],
  components: {
    echartsPie,
    TreeSelect
  },
  data() {
    return {
      //以上为趋势图有关参数
      isdarkSkin: sessionStorage.getItem('dark') || 0,
      currentSkin: sessionStorage.getItem('dark') || 1,
      // 是否 报表跳转过来的页面
      reportState:0,
      modalWidth:0,
            groupList: [],
      echartsWidth:null,
      lossMax:'',
      lossMin:'',
      useMax:'',
      useMin:'',  
      modelShow: false,
      internationOperatorListType: '0',
      treeValue: "",
      chartLoading: true,
      //权限对象
      permissionObj: {},
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      maxHeight: 255, // 最大高度
      pie_List: [
        {
          node: "linkPie",
          title: this.$t('spec_line'),
          totalValue: 0,
          data: [
            {value: 0, name: this.$t('dash_normal'), Percent: 0, content: this.$t('specquality_msg1')},
            {value: 0, name: this.$t('dash_deterioration'), Percent: 0, content: this.$t('specquality_msg2')},
            {value: 0, name: this.$t('dash_interrupt'), Percent: 0, content: this.$t('specquality_msg3')},
          ],
        },
      ],
      query: {
        orgId: "",
        operator: "",
        statisticalAngle: 2,
        screening: '',
        keyWord: "",
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: new Date(new Date().setTime(new Date().getTime())).format("yyyy-MM-dd 23:59:59"),
        pageNo: 1,
        groupIds:[],
        pageSize: 10,
        faultType: "",
      },
      clickQuery: {
        orgId: "",
        operator: "",
        statisticalAngle: 1,
        screening: '',
        keyWord: "",
      },
      //排序参数
      useableRateOrder: null,
      nrUseRateOrder: null,
      goodRateOrder: null,
      nrGoodRateOrder: null,
      latestTimeOrder: null,
      brokenCountOrder: null,
      brokenAvgDurationOrder: null,
      brokenDurationOrder: null,
      degradationCountOrder: null,
      degradationAvgDurationOrder: null,
      degradationDurationOrder: null,
      packetLossDegradationCountOrder: null,
      avgPacketLossDegradationTimeOrder: null,
      packetLossDegradationDurationOrder: null,
      status: null,
 // 1：中断 2：时延劣化 3：丢包劣化
      faultTypeList:[{
           value: 1,
          label: this.$t('comm_interruption')
      },{
           value: 2,
          label: this.$t('common_degradation'),

      },{
          value: 3,
          label: this.$t('comm_loss_package'),

      }],
      //排序界面参数
      //排序字段
      orderKey: [
        "useableRate",
        "goodRate",
        "latestEventTime",
        "brokenDuration",
        "degradationDuration",
      ],
      // 排序类别（asc，desc）
      orderType: ["desc", "desc", "desc", "desc", "desc"],
      //排序的字段顺序（从0开始）（设置样式时使用）
      sortIndex: [0, 1, 2, 3, 4],
      // 排序类别（asc，desc，normal）（设置样式时使用）
      sortType: ["normal", "normal", "normal", "normal", "normal"],

      client_list: [],
      operatorList: global.operatorList,
      statisticalAngleArrayList: global.statisticalAngleList,
      currentTime: Date.now(),
      startTime: new Date().format2('yyyy-MM-dd 00:00:00'),
      endTime: new Date().format2('yyyy-MM-dd 23:59:59'),
      interValCurTime: null,
      timePickerOptions: {steps: [1, 1, 1]},
      savetimePickerOptions: {steps: [1, 1, 1]},
      timeRange: [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")],
      timeOptions: {
        shortcuts: [
          // {
          //   text: this.$t('comm_half_hour'),
          //   value() {
          //     const start = new Date();
          //     const end = new Date();
          //     start.setTime(start.getTime() - 30 * 60 * 1000);
          //     return [start.format("yyyy-MM-dd HH:mm:ss"), end.format("yyyy-MM-dd HH:mm:ss")];
          //   }
          // },
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
              let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
              let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          }
        ]
      },
      computedHeight() {
        // 如果数据为空或加载中，则使用最大高度
        if (!this.tabListModalData || this.ModalTableLoading) {
          return 0;
        }
        // 单行，两行，三行以上高度
        if (this.tabListModalData.length == 1) {
          return 100;
        } else if (this.tabListModalData.length == 2) {
          return 150;
        } else if (this.tabListModalData.length == 3) {
          return 200;
        } else {
          return this.maxHeight;
        }
        // // 行数乘以单行高度
        // let rowsHeight = this.tabListModalData.length * rowHeight;
        // // 返回计算后的高度，不超过最大高度
        // console.log('=====.>',Math.min(rowsHeight, this.maxHeight));
        // return Math.min(rowsHeight, this.maxHeight);
      },
      startOptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const start = new Date();
              return start;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let start = new Date(date.year, date.month, 1);
              return start;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();

              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              return start;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
        ],
        disabledDate: (date) => {
          let endTime = this.endTime == '' ? Date.now() : this.endTime
          return date > Date.now() || date > new Date(endTime).valueOf()
        },
      },
      endOptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              return end;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              return end;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
        ],
        disabledDate: (date) => {
          let startTime = this.startTime == '' ? '' : this.startTime
          return date > Date.now() || date < new Date(startTime).valueOf() - 8 * 3600 * 1000
        },
      },
      queryModal: {
        keyWord: "",
        startTime: "",
        endTime: "",
        linkId: "",
        pageNo: 1,
        pageSize: 1000000,
      },
      edit: {
        dev_ip: "",
      },
      states: [
        {value: 0, label: this.$t('testspeed_unactivated')},
        {value: 1, label: this.$t('testspeed_activated')},
      ],
      dateValue: [
        new Date().format("yyyy-MM-dd"),
        new Date().format("yyyy-MM-dd"),
      ],
      dateValueCopy: [],
      pageNo: 1,
      pageSize: 10,
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      modalPage_no: 1,
      modalPageSize: 3,
      totalCount: 0,
      totalCountModal: 0,
      noDataLoading: false,
      loading: false,
      loading1: false,
      loading2: false,
      loading3: false,
      queryLoading: false,
      ModalTableLoading: false,
      ModalTopoLoading: false,
      ModalTrendLoading: false,
      currentid: "",
      tabList: [],
      tabList1: [],
      originalList: [], //保存原始列表数据
      orderTabList: [],
      originalModalList: [], //保存原始列表数据
      orderTabModalList: [],
      tabList2: [],
      tabList3: [],
      tabListModal: [],
      eventTypes: 0,
      columnsModal: [
        {
          title: this.$t('but_choose'),
          align: "center",
          width: this.getColumnWidth(50,70),
          render: (h, params) => {
            let id = params.row.rowId;
            let flag = false;
            if (this.currentid === id) {
              flag = true;
            } else {
              flag = false;
            }
            let self = this;
            return h("div", [
              h("Radio", {
                style: {
                  marginRight: 0,
                },
                props: {
                  value: flag,
                },
                on: {
                  "on-change": () => {
                    //判断是否是故障段
                    this.isIpduan = params.row.preFurthestIp ? true : false;
                    //设置选中
                    self.currentid = id;
                    this.id = params.row.rowId;

                    //topo图参数设置，默认第一条
                    this.topologyParam = {
                      sourceIp: params.row.sourceIp,
                      destIp: params.row.destIp,
                      linkId: params.row.linkId,
                      logTracertId: params.row.logTracertId,
                      taskType: params.row.taskType,
                      startTime: params.row.startTs,
                      createTs: params.row.createTs,
                    };
                    if (Number(params.row.status) === 1) {
                      this.topologyParam.endTime = new Date().format2(
                          "yyyy-MM-dd hh:mm:ss"
                      );
                      this.echartLookParama2.endTime = new Date().format2(
                          "yyyy-MM-dd hh:mm:ss"
                      );
                      this.tabClickObj.endTime = new Date().format2(
                          "yyyy-MM-dd hh:mm:ss"
                      );
                    } else {
                      this.topologyParam.endTime = new Date(
                          Number(params.row.lastActiveTs) * 1000
                      ).format2("yyyy-MM-dd hh:mm:ss");
                      this.echartLookParama2.endTime = new Date(
                          Number(params.row.lastActiveTs) * 1000 + 3 * 3600 * 1000
                      ).format2("yyyy-MM-dd hh:mm:ss");
                      this.tabClickObj.endTime = new Date(
                          Number(params.row.lastActiveTs) * 1000 + 3 * 3600 * 1000
                      ).format2("yyyy-MM-dd hh:mm:ss");
                    }
                    //趋势图参数设置
                    this.echartLookParama2.startTime = moment(
                        params.row.startTs
                    )
                        .subtract(3, "hours")
                        .format("YYYY-MM-DD HH:mm:ss");
                    this.tabClickObj.startTime = moment(
                        params.row.startTs
                    )
                        .subtract(3, "hours")
                        .format("YYYY-MM-DD HH:mm:ss");

                    this.echartLookParama2.special = true;
                    this.echartLookParama2.queryType = 2; //1是链路，2是节点
                    this.echartLookParama2.snmp = true

                    this.echartLookParama2.devIp = this.healthLook.devIp;
                    this.echartLookParama2.interfactIp = this.healthLook.interfactIp;
                    this.echartLookParama2.preNodeIp = params.row.sourceIp;
                    this.echartLookParama2.nodeIp = params.row.destIp;
                    this.echartLookParama2.linkId = params.row.linkId;
                    this.echartLookParama2.linkIds = params.row.linkId;
                    this.echartLookParama2.isSpecial =
                        params.row.isSpecial || false;
                    this.echartLookParama2.queryType = params.row.taskType == 3 ? 1 : 2; //1是链路，2是节点
                    this.echartLookParama2.High =
                        params.row.taskType == 3 ? true : false;

                    //趋势图故障点定位
                    this.markPoint = params.row.startTs;
                    this.markPointGreen = params.row.status == 1 ? '' : new Date(Number(params.row.lastActiveTs) * 1000).format2('yyyy-MM-dd HH:mm:ss');
                    //获取topo图数据
                    this.getSnmp(this.echartLookParama2);
                  },
                },
              }),
            ]);
          },
        },
        {
          title: this.$t('comm_event_start'),
          key: "startTs",
          align: "left",
          sortable: "custom",
          width: 160,
          
          renderHeader: (createElement, {column}) => {
            return [
              createElement(
                  "span",
                  {class: "ivu-table-cell-sort"},
                  this.query.statisticalAngle === 2 ? this.$t('testspeed_failure_start') : column.title
              )
            ]
          }
        },
        {
          title: this.$t('testspeed_event_recovery'),
          key: "lastActiveTs",
          align: "left",
          width: 160,
          renderHeader: (createElement, {column}) => {
            return [
              createElement(
                  "span",
                  {class: "ivu-table-cell-sort"},
                  this.query.statisticalAngle === 2 ? this.$t('testspeed_failure_recovery') : column.title
              )
            ]
          },
          render: (h, param) => {
            let str = param.row.lastActiveTs;
            let text = '';
            if (param.row.status == 1) {
              text = '--'
            } else {
              text = new Date(Number(str) * 1000).format2("yyyy-MM-dd hh:mm:ss");
            }
            return h("span", str === undefined || str === null || str === "" ? '--' : text);
          }
        },
        {
          title: this.$t('comm_duration_event'),
          key: "durationOfTime",
          align: "left",
          sortable: "custom",
          width: 140,
          renderHeader: (createElement, {column}) => {
            return [
              createElement(
                  "span",
                  {class: "ivu-table-cell-sort"},
                  this.query.statisticalAngle === 2 ? this.$t('comm_duration_failure') : column.title
              )
            ]
          },
          render: (h, param) => {
            var str = this.timeToString(param.row.durationOfTime);

            var Arr1 = h(
                "span",
                str
            );

            return h(
                "div",
                {
                  class: {
                    "text-ellipsis": true,
                  },
                  style: {
                    "word-break": "keep-all",
                    "white-space": "nowrap",
                    "overflow": "hidden",
                    "text-overflow": "ellipsis",
                  },
                  domProps: {
                    title: str,
                  },
                },
                [Arr1]
            );
          }
        },
        {
          title: this.$t('comm_status'),
          align: "left",
          width:this.getColumnWidth(100,120),

          renderHeader: (createElement, {col}) => {
            return createElement(
                "Select",
                {
                  props: {
                    value: this.eventTypes,
                    // transfer: true,
                  },
                  on: {
                    "on-change": (value) => {
                      if (value == 0) {
                        this.look.listParam.eventType = ""
                      } else {
                        this.look.listParam.eventType = value
                      }
                      this.look.listParam.pageNo = 1;
                      this.eventTypes = value;
                      this.getModalList(this.look.listParam);
                    },
                  },
                  style: {
                    width: "100%",
                  },
                  class: "headerSelectArea",
                },
                [
                  createElement(
                      "Option",
                      {
                        props: {
                          value: 0,
                        },
                        class: "modalSelect",
                      },
                      this.query.statisticalAngle === 2 ? this.$t('dash_fault_type') : this.$t('comm_event')
                  ),
                  createElement(
                      "Option",
                      {
                        props: {
                          value: 1,
                        },
                      },
                      this.$t('dash_interrupt')
                  ),
                  createElement(
                      "Option",
                      {
                        props: {
                          value: 2,
                        },
                      },
                      this.$t('common_degradation')
                  ),
                  createElement(
                      "Option",
                      {
                        props: {
                          value: 3,
                        },
                      },
                      this.$t('common_loss_degradation')
                  ),
                ]
            );
          },
          key: "status",
          // width: 200,
          align: "left",
          render: (h, params) => {

            var str = params.row.eventType == 1
                ? this.$t('dash_interrupt')
                : params.row.eventType == 2
                    ? this.$t('common_degradation')
                    : params.row.eventType == 3
                        ? this.$t('common_loss_degradation')
                        : "--";
            var Arr1 = h(
                "span",
                str
            );

            return h(
                "div",
                {
                  class: {
                    "text-ellipsis": true,
                  },
                  style: {
                    "word-break": "keep-all",
                    "white-space": "nowrap",
                    "overflow": "hidden",
                    "text-overflow": "ellipsis",
                  },
                  domProps: {
                    title: str,
                  },
                },
                [Arr1]
            );
          },
        },
        {
          title: this.$t('alarm_susp_failed_link'),
          key: "errorIp",
          align: "left",
          // width: 170,
          minWidth:160,
          render: (h, param) => {
            let str = param.row.errorIp, 
            preFurthestIp = param.row.preFurthestIp;

            let text = "";

            if (str === undefined || str === null || str === "") {
              text = "--";
            } else {
              text = preFurthestIp ? `${preFurthestIp}-${str}` : `${str}`;
            }

             let maxWidth = param.column.width; // 获取动态传递的宽度
              text = ipv6Format.formatIPv6Address(text,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, text);

            // let Arr1 = h("span", text);

            // return h(
            //     "div",
            //     {
            //       class: {
            //         "text-ellipsis": true,
            //       },
            //       style: {
            //         "word-break": "keep-all",
            //         "white-space": "nowrap",
            //         "overflow": "hidden",
            //         "text-overflow": "ellipsis",
            //       },
            //       domProps: {
            //         title: text,
            //       },
            //     },
            //     [Arr1]
            // );
            //123
          },
        },
        {
          title: this.$t('specquality_cause'),
          key: "eventReason",
          // width: 120,
          width:this.getColumnWidth(80,100),
          align: "left",
          render: (h, params) => {


            // 0：未知 1：停电 2：割接  4：拥塞
            let str = params.row.eventCauseType,
                text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (str == 0) {
              text = this.$t('comm_unknown');
            } else if (str == 1) {
              text = this.$t('comm_Blackout');
            } else if (str == 2) {
              text = this.$t('comm_cut');
            } else if (str == 4) {
              text = this.$t('comm_congestion');
            } else {
              text = "--";
            }
            // text = this.$t('comm_congestion');
            return h("span", text);
          },
        },
        {
          title: this.$t('specquality_recovery_cause'),
          key: "recoveryType",
          width:130,
          width:160,
          align: "left",
          render: (h, params) => {
            // 0故障升级、1故障降级、2路径切换、3故障恢复、4其他,5故障链路恢复
            let str = params.row.recoveryType,
              text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (str == 0) {
              text = this.$t('comm_failure_up');
            } else if (str == 1) {
              text = this.$t('comm_failure_dwon');
            } else if (str == 2) {
              text = this.$t('comm_path_switching');
            } else if (str == 3) {
              text = this.$t('comm_failure_re');
            }else if (str == 5) {
              text = this.$t('comm_failure_link_recovery');
            } else {
              text = this.$t('comm_other');
            }
            // text = this.$t('comm_failure_link_recovery');
            return h("span", text);
          },
        },
        {
          title: this.$t('specquality_current_state'),
          key: "status",
          // width: 120,
          width:this.getColumnWidth(80,120),
          align: "left",
          render: (h, params) => {
            if (params.row.status == 1) {
              return h("span", {style: {color: "#fb204d"}}, this.$t('common_unrecovered'));
            } else if (params.row.status == 0) {
              return h("span", {style: {color: "#23D692"}}, this.$t('common_recovered'));
            }
          },
        },
      ],
      //故障段
      isIpduan: false,
      indeterminate: true,
      checkAll: -1,
      ModalcheckAll: 0,
      selectDta: new Set(),
      customKey:"api:specquality/getSpecAnalysisList+sys_func_id:353",
      customMoveIndex:0,
      customModalShow:false,
      customModalShowLoading:false,
      columns1:[],
      fieldsJsonObjArr:[],
      allocationListFields:[],
      customFieldsColumnsKeyWidth:[],
      screenWidth:0,
       fieldsColumns: [
       {
        key: "showField",
        width: 35,
        render: (h, params) => {
          let row = params.row;
          return h("Checkbox", {
            props: {
              value: row.showField,
              disabled: row.fixedField // 当fixedField为true时禁用Checkbox
            },
            on: {
              input: (value) => {
                if (!row.fixedField) {
                  // 更新 showField 的值
                  this.allocationListFields[params.index].showField = value;
                }
              }
            }
          });
        }
      },
        {
          key: "parameterTitle",
          align: "left",
          // width: 160,
          render:(h,params) => {
                        let fontColor = 'var(--field_font_color ,#fff)'
            if(params.row.fixedField) {
              fontColor = '#5CA0D5'
            }
            return h('div',{
              style: {
                color:fontColor,
                fontWeight:400
              }
            },params.row.parameterTitle)
          }
        }
      ],
      fixedColumns: [
        {
          //多选
          key: "firstColumnSelect",
          type: "selection",
          width: 30,
          align: "center",
          // fixed: 'left',
        },

        {
          title: this.$t('analyse_spec_name'),
          key: "specialName",
          width: 150,
          align: "left",
          tooltip: true,
          render: (h, param) => {
            let str = param.row.specialName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;

             return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'top',
                  content: str,
                }

              },str)

            ])
          },
        },
        {
          title: this.$t('spec_a_end_inter_ip'),
          key: "specialAIp",
         width: 200,
          align: "left",
          tooltip: true,
          render: (h, param) => {
            let str = param.row.specialAIp;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            // return h("span", str);
             let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },
        },
        {
          title: this.$t('spec_z_end_inter_ip'),
          key: "specialZIp",
           width: 200,
          align: "left",
          tooltip: true,
          render: (h, param) => {
            let str = param.row.specialZIp;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            // return h("span", str);
              let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },
        },
        //分组
        {
          title: this.$t('comm_grouping'),
          key: "groupNames",
          align: "left",
          width: 120,
          render: (h, params) => {
            let str = params.row.groupNames;
            if (str === undefined || str === null) {
              str = "--";
            }
            // let v = "";
            // if (str.length > 11) {
            //   v = str.substring(0, 9) + "...";
            //    return h('Tooltip',{
            //           props: {
            //             placement:'bottom-start'
            //           }
            //          },
            //          [v,h(
            //           'span',{
            //             slot:'content',
                       
                        
            //           },str
            //          )]);
            // } else {
            //   v = str;
            //    return h('span',v)
            // }
             if(str !== "--") {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'top',
                  content: str,
                }

              },str)

            ])
            }else {
              return h('div',str)
            }
          },
        },
        {
          title: this.$t('comm_operators'),
          key: "operatorName",
          width: 160,
          align: "left",
          tooltip: true,
          render: (h, param) => {
            let str = param.row.operatorName;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);

            let items = this.operatorList.filter(function (item) {
              return item.value == str;
            });

            if (items.length > 0) {
              str = items[0].label;
            }

            return h("span", str);
          },
        },
        {
          title: this.$t('comm_org'),
          key: "orgName",
          width: 180,
          align: "left",
          tooltip: true,
          render: (h, param) => {
            let str = param.row.orgName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            let Arr1 = h("span", str);
            let str2 = ''
            if (str.length > 16) {
              str2 = str.slice(0, 14) + '...'
              return h('Tooltip', {
                    props: {
                      placement: 'left-end'
                    }
                  },
                  [str2, h(
                      'span', {
                        slot: 'content',


                      }, str
                  )]);
            } else {
              str2 = str
              return h('span', str2)
            }
          },
        },
        // 可用率
        {
          title: this.$t('specquality_availability'), //可用率
          key: "useableRate",
          width: this.getColumnWidth(90,120),
          align: "left",
          sortNumber: 0,
          sortable: "custom",
          renderHeader: (h, {column}) => {

            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  debugger
                  this.useableRateOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.useableRateOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.useableRateOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);
          },
          render: (h, params) => {
            let str = params.row.useableRate;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : str + "%"
            );
          },
        },
        {
          title: this.$t('specquality_good_rate'), // 优良率
          key: "goodRate",
          width: this.getColumnWidth(90,105),
          align: "left",
          sortNumber: 1,
          sortable: "custom",
          renderHeader: (h, {column}) => {


            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.goodRateOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.goodRateOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.goodRateOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);


          },
          render: (h, params) => {
            let str = params.row.goodRate;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : str + "%"
            );
          },
        },
        {
          title: this.$t('specquality_incoming_rate'), // 可用率(免责)
          key: "nrUseRate",
          width: this.getColumnWidth(140,190),
          align: "left",
          sortNumber: 2,
          sortable: "custom",
          renderHeader: (h, {column}) => {
            return h("span", {
                  style: {
                    "cursor": "pointer"
                  },
                  on: {
                    click: () => {
                      debugger
                      this.nrUseRateOrder = null;
                      if (column._sortType == "normal") {
                        column._sortType = "asc"
                        this.nrUseRateOrder = "asc";
                      } else if (column._sortType == "asc") {
                        column._sortType = "desc"
                        this.nrUseRateOrder = "desc";
                      } else if (column._sortType == "desc") {
                        column._sortType = "normal"
                      }
                      console.log(column, "-----------------------")
                      this.topSortChange({
                        column,
                        key: column.key,
                        order: column._sortType,
                      });
                    }
                  }
                },
                [
                  h("span", this.$t('specquality_availability'),),
                  h(
                      "span",
                      {
                        style: {
                          color: "red!important",
                        },
                      },
                      this.$t('specquality_exemption'),
                  ),
                ]);


          },
          render: (h, params) => {
            let str = params.row.nrUseRate;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : str + "%"
            );
          },
        },
        {
          title: this.$t('specquality_good_rate_exemption'), // 优良率(免责)
          key: "nrGoodRate",
          width: this.getColumnWidth(140,190),
          align: "left",
          sortNumber: 3,
          sortable: "custom",
          renderHeader: (h, {column}) => {


            return h("span", {
                  style: {
                    "cursor": "pointer"
                  },
                  on: {
                    click: () => {
                      debugger
                      this.nrUseRateOrder = null;
                      if (column._sortType == "normal") {
                        column._sortType = "asc"
                        this.nrUseRateOrder = "asc";
                      } else if (column._sortType == "asc") {
                        column._sortType = "desc"
                        this.nrUseRateOrder = "desc";
                      } else if (column._sortType == "desc") {
                        column._sortType = "normal"
                      }
                      console.log(column, "-----------------------")
                      this.topSortChange({
                        column,
                        key: column.key,
                        order: column._sortType,
                      });
                    }
                  }
                },
                [
                  h("span", this.$t('specquality_good_rate'),),
                  h(
                      "span",
                      {
                        style: {
                          color: "red!important",
                        },
                      },
                      this.$t('specquality_exemption'),
                  ),
                ]);

          },
          render: (h, params) => {
            let str = params.row.nrGoodRate;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : str + "%"
            );
          },
        },
        // 中断次数
        {
          title: this.$t('specquality_Interruption_times'),
          key: "brokenCount",
          width: this.getColumnWidth(100,175),
          align: "left",
          sortNumber: 4,
          sortable: "custom",
          renderHeader: (h, {column}) => {

            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.brokenCountOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.brokenCountOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.brokenCountOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);

          },
          render: (h, params) => {
            return h("span", params.row.brokenCount ?? '--');
          },
        },
        // 中断平均时长
        {
          title: this.$t('comm_break_avg_long_time'),
          key: "brokenAvgDuration",
          width: this.getColumnWidth(130,190),
          align: "left",
          sortNumber: 5,
          sortable: "custom",
          renderHeader: (h, {column}) => {


            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.brokenAvgDurationOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.brokenAvgDurationOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.brokenAvgDurationOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);

          },
          render: (h, params) => {
            return h("span", this.timeToString(params.row.brokenAvgDuration));
          },
        },
        // 中断累计时长
        {
          title: this.$t('comm_duration'),
          key: "brokenDuration",
          width: this.getColumnWidth(130,160),
          align: "left",
          sortNumber: 6,
          sortable: "custom",
          renderHeader: (h, {column}) => {


            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.brokenDurationOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.brokenDurationOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.brokenDurationOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);

          },
          render: (h, params) => {
            return h("span", this.timeToString(params.row.brokenDuration));
          },
        },
        // 时延劣化
        {
          title: this.$t('comm_delayDegradation_times'),
          key: "degradationCount",
          width: this.getColumnWidth(150,245),
          align: "left",
          sortNumber: 7,
          sortable: "custom",
          renderHeader: (h, {column}) => {

            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.degradationCountOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.degradationCountOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.degradationCountOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);


          },
          render: (h, params) => {
            return h("span", params.row.degradationCount ?? '--');
          },
        },
        {
          title: this.$t('comm_delayDegradation_avg'),
          key: "degradationAvgDuration",
          width: this.getColumnWidth(150,270),
          align: "left",
          sortNumber: 8,
          sortable: "custom",
          renderHeader: (h, {column}) => {

            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.degradationAvgDurationOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.degradationAvgDurationOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.degradationAvgDurationOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);


          },
          render: (h, params) => {
            return h("span", this.timeToString(params.row.degradationAvgDuration));
          },
        },
        {
          title: this.$t('comm_delayDegradation_duration'),
          key: "degradationDuration",
          width: this.getColumnWidth(150,230),
          align: "left",
          sortNumber: 9,
          sortable: "custom",
          renderHeader: (h, {column}) => {


            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.degradationDurationOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.degradationDurationOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.degradationDurationOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);

          },
          render: (h, params) => {
            let str = params.row.degradationDuration;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : this.timeToString(str)
            );
          },
        },

        // 丢包劣化
        {
          title: this.$t('comm_packetLossDegradation_times'),
          key: "packetLossDegradationCount",
          width: this.getColumnWidth(150,290),
          align: "left",
          sortNumber: 7,
          sortable: "custom",
          renderHeader: (h, {column}) => {

            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.packetLossDegradationCountOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.packetLossDegradationCountOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.packetLossDegradationCountOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);


          },
          render: (h, params) => {
            return h("span", params.row.packetLossDegradationCount ?? '--');
          },
        },
        {
          title: this.$t('comm_packetLossDegradation_avg'),
          key: "avgPacketLossDegradationTime",
          width: this.getColumnWidth(150,300),
          align: "left",
          sortNumber: 8,
          sortable: "custom",
          renderHeader: (h, {column}) => {

            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.avgPacketLossDegradationTimeOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.avgPacketLossDegradationTimeOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.avgPacketLossDegradationTimeOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);


          },
          render: (h, params) => {
            return h("span", this.timeToString(params.row.avgPacketLossDegradationTime));
          },
        },
        {
          title: this.$t('comm_packetLossDegradation_duration'),
          key: "packetLossDegradationDuration",
          width: this.getColumnWidth(150,270),
          align: "left",
          sortNumber: 9,
          sortable: "custom",
          renderHeader: (h, {column}) => {


            return h("span", {
              style: {
                "cursor": "pointer"
              },
              on: {
                click: () => {
                  this.packetLossDegradationDurationOrder = null;
                  if (column._sortType == "normal") {
                    column._sortType = "asc"
                    this.packetLossDegradationDurationOrder = "asc";
                  } else if (column._sortType == "asc") {
                    column._sortType = "desc"
                    this.packetLossDegradationDurationOrder = "desc";
                  } else if (column._sortType == "desc") {
                    column._sortType = "normal"
                  }
                  console.log(column, "-----------------------")
                  this.topSortChange({
                    column,
                    key: column.key,
                    order: column._sortType,
                  });
                }
              }
            }, column.title);

          },
          render: (h, params) => {
            let str = params.row.packetLossDegradationDuration;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : this.timeToString(str)
            );
          },
        },
         {
          title: this.$t('comm_operate'),
          key: "action",
          align: "center",
          width: 120,
          // fixed:'right',
          renderHeader: (h) => {
            const handleClick = () => {
              this.customModalShow = true;
              this.customModalShowLoading = true;
            }
            return h('div',[
              h('span',this.$t('comm_operate')),
              h('img', {
                attrs: {
                  src:this.currentSkin == 1 ? tableEditBtn:tableEditLightBtn
                },
                style: {
                  width: '15px', // 调整图片大小
                  height: '15px', // 考虑保持宽高比
                  marginLeft: '10px',
                  verticalAlign: 'middle',// 使图片居中
                  cursor: 'pointer'

                },
                on: {
                click: handleClick,
              },
              })
            ])
          },
          render: (h, {row}) => {
            let modify = h(
                'Tooltip',
                {
                  props: {
                    placement: 'left-end',
                    transfer: true

                  }
                },
                [
                  h('span', {
                    class: this.currentSkin == 1 ? 'look-icon':'light-look-icon',
                    on: {
                      click: () => {
                        this.actionClick(row, "specialLine");
                      },

                    }
                  }),
                  h('span', {slot: 'content'}, this.$t('comm_view_details'))
                ]
            )
            let array = [];
            array.push(modify);
            return h('div', array);
          }
          // render: (h, params) => {
          //   return h(
          //     "a",
          //     {
          //       class: "action-btn",
          //       on: {
          //         click: () => {
          //           this.actionClick(params.row, "specialLine");
          //         },
          //       },
          //     },
          //     this.$t('comm_view_details')
          //   );
          // },
        },
      ],
      columns2: [
        {
          title: this.$t('analyse_spec_name'),
          key: "specialName",
          minWidth: 160,
          align: "left",
          render: (h, param) => {
            let str = param.row.specialName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            let str1 = str;
            if (str.length > 10) {
              str1 = str.substring(0, 10) + '...';
            }
            return h(
                'Tooltip',
                {
                  props: {
                    placement: 'top-end',
                    transfer: true
                  },
                },
                [
                  h('span', {
                    class: "action-btn",
                    style: {cursor: 'pointer', color: this.permissionObj.look ? this.currentSkin == 1?'#05EEFF':'#0290FD':"#ffffff"},
                    on: {
                      click: () => {
                        if( this.permissionObj.look){
                          this.actionClick(param.row, "broken");
                        }else{
                          console.log("无权限")
                        }
                      }
                    }
                  }, str1),
                  h(
                      'span',
                      {
                        slot: 'content', //slot属性
                        style: {
                          whiteSpace: 'normal',
                          wordBreak: 'break-all'
                        }
                      },
                      str
                  ),
                ]
            );
          }
        },
        {
          title: this.$t('spec_a_end_inter_ip'),
          key: "specialAIp",
          align: "left",
          width: 200,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.specialAIp;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;

             let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
            // return h("span", str);
          },
        },
        {
          title: this.$t('spec_z_end_inter_ip'),
          key: "specialZIp",
          align: "left",
          width: 200,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.specialZIp;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;

             let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
            // return h("span", str);
          },
        },
        {
          title: this.$t('rphealthy_cumulative_number'),
          key: "times",
          align: "left",
          minWidth: 170,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.times;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            return h("span", str);
          },
        },
        {
          title: this.$t('comm_duration1'),
          key: "duration",
          align: "left",
          minWidth: 160,
          render: (h, param) => {
            let str = param.row.duration;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            return h("span", this.timeToString(str));
          },
        },
      ],
      columns3: [
        {
          title: this.$t('analyse_spec_name'),
          key: "specialName",
          align: "left",
          minWidth: 160,
          render: (h, param) => {
            let str = param.row.specialName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            let str1 = str;
            if (str.length > 10) {
              str1 = str.substring(0, 10) + '...';
            }
            return h(
                'Tooltip',
                {
                  props: {
                    placement: 'top-end',
                    transfer: true
                  },
                },
                [
                  h('span', {
                    class: "action-btn",
                    style: {cursor: 'pointer', color: this.permissionObj.look ? this.currentSkin == 1?'#05EEFF':'#0290FD':"#ffffff"},
                    on: {
                      click: () => {
                        if( this.permissionObj.look){
                             this.actionClick(param.row, "degradation");
                        }else{
                            console.log("无权限")
                        }

                      }
                    }
                  }, str1),
                  h(
                      'span',
                      {
                        slot: 'content', //slot属性
                        style: {
                          whiteSpace: 'normal',
                          wordBreak: 'break-all'
                        }
                      },
                      str
                  ),
                ]
            );
          }
        },
        {
          title: this.$t('spec_a_end_inter_ip'),
          key: "specialAIp",
          align: "left",
          width: 200,
          render: (h, param) => {
            let str = param.row.specialAIp;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);
            // return h("span", str);
                         let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('spec_z_end_inter_ip'),
          key: "specialZIp",
          align: "left",
          width: 200,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.specialZIp;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);
            // return h("span", str);
            let maxWidth = param.column.width; // 获取动态传递的宽度
            str = ipv6Format.formatIPv6Address(str,maxWidth);
            return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('rphealthy_cumulative_number'),
          key: "times",
          align: "left",
          width: 170,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.times;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            return h("span", str);
          }
        },
        {
          title: this.$t('comm_duration1'),
          key: "duration",
          align: "left",
          minWidth: 160,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.duration;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            return h("span", this.timeToString(str));
          },
        },
      ],
      title: {
        deviceIp: "",
        sourceName: "",
        sourceIp: "",
        port: "",
      },
      editType: 0,
      healthLook: {},
      look: {
        show: false,
        title: "",
        titleDetails: "",
        listParam: {
          linkIds: null,
          startTime: "",
          endTime: "",
          lastEventTimeOrder: "desc",
          eventDurationOrder: null,
          eventType: 0,
          statisticalAngle: 1,
          operator: '',
          pageNo: 1,
          pageSize: 10,
        },
      },
      //topo图参数
      topologyParam: {
        linkId: "",
        startTime: "",
        endTime: "",
        logTracertId: "",
        sourceIp: "",
        destIp: "",
        taskType: null,
        createTs: "",
      },
      //topo数据
      topologyList: [],
      ipTitleParam: {
        sourceIp: "",
        destIp: "",
        name: "",
        degradationType: "",
        data_source: "",
      },
      /*趋势图参数设置*/
      /*趋势图按钮*/
      btnValue: 1,
      tabClickObj: {
        startTime: '',
        endTime: ''
      },
      echartLookParama2: {
        specialAIp: "",
        specialZIp: "",
        specialId: "",
        linkIds: "",
        orgId: "",
        dataSource: "",
        level: null,
        interfaceIp: "",
        devIp: "",
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: new Date(new Date().setTime(new Date().getTime())).format(
            "yyyy-MM-dd 23:59:59"
        )
      },
      markPoint: "",
      markPointGreen: "",
      height: 500,
      height2: 260,
      echart1: {
        show: true,
      },
      echart2: {
        show: true,
      },
      preAlias: {
        name: "",
        port: "",
      },
      zAlias: {
        name: "",
        port: "",
      },
      // 0：Z端匹配，1：A端匹配
      matchingType: "",
      delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "bps",
        出流速: "bps",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%",
        入利用率: "%",
        出利用率: "%"
      },
      flowColor: ["#478EE9", "#F19149", "#F19149", "#24C3C5"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: [],
      },
      flowData: {
        enter: [],
        issue: [],
        inUseRateList: [],
        outUseRateList: []
      },
      goodRateData: {
        nrUseRateList: [],
        nrGoodRateList: [],
        useRateList: [],
        goodRateList: [],
      },
      delayLossScale: true,
      startScale: false,
      startScale2: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayStart2: 0,
      delayEnd: 100,
      delayEnd2: 100,
      scale: "",
      startValue: "",
      startValue2: "",
      endValue: "",
      endValue2: "",
      scale2: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {},
      },
      delayPs2: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
      },
      //以上为趋势图有关参数
    };
  },
  created() {

    this.getListInternationaControlOperator();
    this.getGroupList();
    this.getAllocationListFieldsByKey();

    let delayLossUnitTemp = {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] = "ms";
    // 丢包率
    delayLossUnitTemp[this.$t('comm_packet_loss')] = "%";
    // 入流速
    delayLossUnitTemp[this.$t("dashboard_upstream")] = "bps";
    // 出流速
    delayLossUnitTemp[this.$t("dashboard_down")] = "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_uplink")] = "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_downstream")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t('specquality_availability_exemption')] = "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t('specquality_good_rate_exemption')] = "%";
    // 可用率
    delayLossUnitTemp[this.$t('specquality_availability')] = "%";
    // 优良率
    delayLossUnitTemp[this.$t('specquality_good_rate')] = "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit, delayLossUnitTemp);


    const src = window.frames.frameElement.getAttribute('src')
    let frameDataStr = unescape(src).split('=')[1] ?? ''
    if (frameDataStr && JSON.parse(frameDataStr).from == 'report') {
      this.orgLists = []
      // frameDataStr有值且from ==report 表明是从质量报告加载的iframe
      let frameData = JSON.parse(frameDataStr)
      this.orgLists.push({
        id: frameData.orgId,
        name: frameData.orgName
      })
      this.startTime = frameData.startTime
      this.endTime = frameData.showEndTime
      this.query.startTime = frameData.startTime
      this.query.endTime = frameData.endTime
      this.query.orgId = frameData.orgId
      this.query.statisticalAngle = frameData.type
      this.reportState = 1;
    } else {
      this.reportState = 0;
      // 不是从质量报告加载iframe时，才重定向
      this.$nextTick(() => {
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
      })
    }
    moment.locale("zh-cn");
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.getTreeOrg();

    this.$nextTick(() => {
      this.dateValueCopy = [];
      this.dateValueCopy.push(
          new Date().format("yyyy-MM-dd 00:00:00"),
          new Date().format("yyyy-MM-dd 23:59:59")
      );
      if (getQueryVariable("start")) {
        this.startTime = this.query.startTime = getQueryVariable("start");
        this.endTime = this.query.endTime = getQueryVariable("end");
        this.dateValueCopy = [this.query.startTime, this.query.endTime];
      }
      if (getQueryVariable("startTime")) {
        this.startTime = this.query.startTime = getQueryVariable("startTime");
        this.endTime = this.query.endTime = getQueryVariable("endTime");
        this.dateValueCopy = [this.query.startTime, this.query.endTime];


        this.timeRange = [new Date(this.query.startTime), new Date(this.query.endTime).getTime() - 86400000];
      }
      if (getQueryVariable("type")) {
        this.query.statisticalAngle = Number(getQueryVariable("type"))
      }
      if (getQueryVariable("linkNum")) {
        this.query.keyWord = getQueryVariable("linkNum")
      }
      if (getQueryVariable("screening")) {
        this.query.screening = getQueryVariable("screening")
      }
      if (permission.list) {
        this.queryClick();
      }
      document.addEventListener("click", e => {
        var box = document.getElementById('selectBox');
        if (box && box.contains(e.target)) {
        } else {
          this.orgTree = false;
        }
      });
      // top.document.addEventListener("click", e => {
      //   var box = document.getElementById('selectBox');
      //   if (box && box.contains(e.target)) {

      //   }else{
      //     this.orgTree = false;
      //   }
      // });
    });
    //获取屏幕总长度
    this.screenWidth = window.innerWidth-45;
    //保存原始的字段项展示长度
    this.setCustomFieldsColumnsKeyWidth();

  },
  watch: {},
  mounted() {
      this.modalWidth = document.body.clientWidth * 0.98
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    addDraggable();
    window.parent.addEventListener("message", (e) => {
      if (e) {
        if (e.data.type == 'msg') {
          return;
        } else if (typeof e.data == 'object') {
          this.isdarkSkin = e.data.isdarkSkin;
        } else if (typeof e.data == 'number') {
          this.isdarkSkin = e.data;
        }
      }
    });
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
          // 设置颜色
    this.delayLossColor = [eConfig.legend.delayColor[this.currentSkin] ,eConfig.legend.lossColor[this.currentSkin] ];
    this.flowColor = [eConfig.legend.flowInColor[this.currentSkin] ,eConfig.legend.flowOutColor[this.currentSkin],
    eConfig.legend.flowInRateColor[this.currentSkin] , eConfig.legend.flowOutRateColor[this.currentSkin] ];
  },
  computed: {
    tableData1: {
      get: function () {
        return this.tabList1;
      },
    },
    tableData2: {
      get: function () {
        return this.tabList2;
      },
    },
    tableData3: {
      get: function () {
        return this.tabList3;
      },
    },
    tabListModalData: {
      get: function () {

        return this.tabListModal;
      },
    },
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        getGroupList() {
      const param = {
        pageNo: 1,
        pageSize: 100000
      };
      this.$http.wisdomPost("/group/list", param).then(res => {
        if (res.code === 1) {
          if (res.data) {
            this.groupList = res.data.records;
          }
        }
      });
    },
    //保存原始的字段项展示长度
    setCustomFieldsColumnsKeyWidth(){
      if(this.customFieldsColumnsKeyWidth==0){
        this.fixedColumns.forEach(item=>{
          let customFieldsColumnsKeyWidthObj = {"key":item.key,"width":item.width};
          this.customFieldsColumnsKeyWidth.push(customFieldsColumnsKeyWidthObj);
        });
      }
    },
    //修改自定列表项
    customModalOk() {
        let param = {
          allocationListFields:this.allocationListFields,
          key:this.customKey
        }
        this.$http.PostJson("/allocationlistfields/update", param).then((res) => {
        if (res.code === 1) {
          this.$Message.success({ content: this.$t('comm_success'), background: true });
          this.customModalShow= false;
          this.customModalShowLoading= false;
          this.getAllocationListFieldsByKey();
          this.getTask(this.query);
        }
      })
    },
    //取消修改
    customModalCancel() {
      this.customModalShow= false;
      this.customModalShowLoading= false;
    },
    //获取列表项下标并改变状态
    getFieldsColumnsIndex(row, index){
      this.customMoveIndex = index;
      // if(row.fixedField == false){
      //   if(row.showField == true){
      //   this.allocationListFields[index].showField =false;
      //   }else{
      //     this.allocationListFields[index].showField =true;
      //   }
      // }
    },
    rowClassName(row, index) {
      if (index === this.customMoveIndex) {
        return "selected-row";
      }
      return "";
    },

    //上移
    moveUp() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex > 0) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex - 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex - 1;
      }
    },
    //下移
    moveDown() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex < this.allocationListFields.length - 1) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex + 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex + 1;
      }
    },
    resetData(value) {
      if (!value) {

        this.modelShow = false
      } else {
        this.modelShow = true
        this.$nextTick(() => {
          if (this.$refs.lookModal) {
        const modalBody = this.$refs.lookModal.$el.querySelector('.ivu-modal-body')
        if (modalBody) {
          const modalWidth = modalBody.offsetWidth
          this.echartsWidth = modalWidth
        }
      }
        })
      }
    },
    timeToString(times) {
      return timeToStringMethod(times, this);
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", {orgId: param}).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item
          });
          _self.treeData = treeNodeList;
        }
      })
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", {orgId: item.id}).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item
          });
          callback(childrenOrgList);
        }
      })
    },
    focusFn() {
      this.getTreeOrg()
    },
    setOrg(item) {
      this.treeValue = item[0].name;
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    onClear() {
      this.treeValue = "";
      this.query.orgId = null;
    },
    choicesOrg() {
      this.orgTree = true;
    },
    //时间事件
    startTimeChange(val) {
      if (val == "") {
        this.endTime = "";
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
        this.endOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        var now = new Date(val),
            y = now.getFullYear(),
            m = now.getMonth() + 1,
            h = now.getHours(),
            min = now.getMinutes(),
            s = now.getSeconds(),
            d = now.getDate();
        let ss =
            y +
            "-" +
            (m < 10 ? "0" + m : m) +
            "-" +
            (d < 10 ? "0" + d : d) +
            " " +
            now.toTimeString().substr(0, 8);
        this.endOptions.disabledDate = (date) => {
          let checkedDay = new Date(
              y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let checkedTime = new Date(this.startTime).valueOf();
          let interTime = checkedTime - checkedDay;
          let startTime = this.startTime
              ? new Date(this.startTime).valueOf()
              : "";
          let endTime = val
              ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000
              : "";
          return (
              (date && date.valueOf() < startTime - interTime) ||
              (date &&
                  date.valueOf() >
                  (endTime > this.currentTime ? this.currentTime : endTime))
          );
        };
        let hourArr = [];
        let hourMax = 0;
        for (let hour = 0; hour < h; hour++) {
          hourArr.push(hour);
          hourMax = hour;
        }
        this.savetimePickerOptions.disabledHours = this.timePickerOptions.disabledHours = hourArr;
        this.handleSendTimeMin(val, hourMax);
      }
    },
    handleSendTimeMin(val, hourMax) {
      let checkHour = new Date(val).getHours();
      let checkMinute = new Date(val).getMinutes();
      let checkSeconds = new Date(val).getSeconds();
      if (checkHour <= hourMax + 1) {
        let minArr = [];
        for (let min = 0; min < checkMinute; min++) {
          minArr.push(min);
        }
        this.savetimePickerOptions.disabledMinutes = this.timePickerOptions.disabledMinutes = minArr;
      } else {
        this.savetimePickerOptions.disabledMinutes = this.timePickerOptions.disabledMinutes = [];
      }
      this.handleSecondTimeMin(val, checkMinute);
    },
    handleSecondTimeMin(val, MinuteMax) {
      let checkMinute = new Date(val).getMinutes();
      let checkSeconds = new Date(val).getSeconds();
      if (checkMinute <= MinuteMax + 1) {
        let minArr = [];
        for (let min = 0; min < checkSeconds; min++) {
          minArr.push(min);
        }
        this.savetimePickerOptions.disabledSeconds = this.timePickerOptions.disabledSeconds = minArr;
        this.timePickerOptions = JSON.parse(
            JSON.stringify(this.timePickerOptions)
        );
        this.savetimePickerOptions = JSON.parse(
            JSON.stringify(this.timePickerOptions)
        );
      } else {
        this.savetimePickerOptions.disabledSeconds = this.timePickerOptions.disabledSeconds = [];
      }
    },
    endTimeChange(val) {
      let timePickerOptions = JSON.parse(
          JSON.stringify(this.savetimePickerOptions)
      );
      if (val == "") {
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        if (this.startTime == "" || this.startTime == undefined) {
          this.endTime = "";
          this.$Message.warning(this.$t('specquality_strat'));
        } else {
          var now = new Date(val),
              y = now.getFullYear(),
              m = now.getMonth() + 1,
              h = now.getHours(),
              min = now.getMinutes(),
              s = now.getSeconds(),
              d = now.getDate();
          let ss =
              y +
              "-" +
              (m < 10 ? "0" + m : m) +
              "-" +
              (d < 10 ? "0" + d : d) +
              " " +
              now.toTimeString().substr(0, 8);
          let checkedDayTimes = new Date(
              y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let nowDayTimes = moment().startOf("day").valueOf();
          if (ss.slice(-8) == "00:00:00") {
            if (checkedDayTimes == nowDayTimes) {
              this.endTime = new Date(this.currentTime).format(
                  "yyyy-MM-dd HH:mm:ss"
              );
            } else {
              this.endTime = new Date(val).format("yyyy-MM-dd 23:59:59");
            }
          }
          this.startOptions.disabledDate = (date) => {
            let checkedDay = new Date(
                y + "-" + m + "-" + d + " 00:00:00"
            ).valueOf();
            let checkedTime = new Date(this.endTime).valueOf();
            let interTime = checkedTime - checkedDay;
            let endTime = this.endTime ? new Date(this.endTime).valueOf() : "";
            let startTime = val
                ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000
                : "";
            return (
                (date && date.valueOf() < startTime) ||
                (date &&
                    date.valueOf() >
                    (endTime > this.currentTime ? this.currentTime : endTime))
            );
          };
          let checkedStart = this.startTime.format("yyyy-MM-dd HH:mm:ss"),
              replaceStart = checkedStart.split(" ")[0] + " 00:00:00";
          let checkedStartTime = new Date(replaceStart).valueOf();
          if (checkedDayTimes > checkedStartTime) {
            this.timePickerOptions.disabledHours = [];
            this.timePickerOptions.disabledMinutes = [];
            this.timePickerOptions.disabledSeconds = [];
          } else {
            this.timePickerOptions.disabledHours =
                timePickerOptions.disabledHours;
            if (
                h >
                timePickerOptions.disabledHours[
                timePickerOptions.disabledHours.length - 1
                    ] +
                1
            ) {
              this.timePickerOptions.disabledMinutes = [];
              this.timePickerOptions.disabledSeconds = [];
            }
            if (
                h ==
                timePickerOptions.disabledHours[
                timePickerOptions.disabledHours.length - 1
                    ] +
                1
            ) {
              this.timePickerOptions.disabledMinutes =
                  timePickerOptions.disabledMinutes;
              this.timePickerOptions.disabledSeconds =
                  timePickerOptions.disabledSeconds;
              if (
                  min >
                  timePickerOptions.disabledMinutes[
                  timePickerOptions.disabledMinutes.length - 1
                      ] +
                  1
              ) {
                this.timePickerOptions.disabledSeconds = [];
              }
              if (
                  min ==
                  timePickerOptions.disabledMinutes[
                  timePickerOptions.disabledMinutes.length - 1
                      ] +
                  1
              ) {
                this.timePickerOptions.disabledSeconds =
                    timePickerOptions.disabledSeconds;
              }
            }
          }
        }
      }
    },
    setCurrentTime() {
      this.currentTime = Date.now();
    },
    // 重置滚动条
    resetScroll() {
      this.$nextTick(() => {
        const tableHeader = this.$refs.tableListspecial.$el.querySelector('.ivu-table-header');
        tableHeader.scrollLeft = 1; // 触发微小的滚动
        tableHeader.scrollLeft = 0; // 然后复位
        const tableBody = this.$refs.tableListspecial.$el.querySelector('.ivu-table-body');

        if (tableBody) {
          // debugger

          //  alert(tableBody.scrollLeft)

          tableBody.scrollLeft = 1;
          tableBody.scrollLeft = 0;


        }
        // ivu-table-overflowX
        const tableWrap = this.$refs.tableListspecial.$el.querySelector('.ivu-table-tip');

        if (tableWrap) {
          // alert(tableWrap.scrollLeft)


          tableWrap.scrollLeft = 1;
          tableWrap.scrollLeft = 0;

        }

      });


    },
    //查询
    queryClick() {
      this.query.keyWord = this.query.keyWord.trim();
      this.query.pageNo = 1;
      this.dateValueCopy = [];
      this.checkAll = -1;
      this.pageNo = 1;

      if (this.query.orgId === 0 || this.query.orgId === undefined) {
        this.query.orgId = "";
      }
      if (this.query.operator === 0 || this.query.operator === "" || this.query.operator === undefined) {
        this.query.operator = "";
      }
      // 重新赋值时间
      this.getTimes();
      if (this.query.startTime && this.query.endTime && new Date(this.query.startTime).valueOf() > new Date(this.query.endTime).valueOf()) {
        this.$Message.warning(this.$t('specquality_time'))
        return
      }
      let startVal = moment(this.query.startTime, "YYYY-MM-DD hh:mm:ss").valueOf();
      let endVal = moment(this.query.endTime, "YYYY-MM-DD hh:mm:ss").valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t('warning_time_not_exceed_62'));
        return;
      }
      this.clickQuery = {
        orgId: this.query.orgId,
        operator: this.query.operator,
        statisticalAngle: this.query.statisticalAngle || "",
        screening: this.query.screening,
        keyWord: this.query.keyWord,
      };
      this.dateValueCopy.push(this.query.startTime, this.query.endTime);
      // this.getStatistics(this.query);
      // this.getInterruptTop(this.query);
      // this.getdegradationTop(this.query);
      this.getTask(this.query);
      this.resetScroll()
    },

    getStatisticsGraph(res) {
      let _self = this;
      if (res.data) {
        //统计图
        let taskGraph = res.data;
        _self.pie_List[0].totalValue = taskGraph.totalSpecNum || 0;
        _self.pie_List[0].data[0].value = taskGraph.normalSpecNum || 0;
        _self.pie_List[0].data[0].Percent = taskGraph.normalPercent || 0;
        _self.pie_List[0].data[1].value = taskGraph.degradationSpecNum || 0;
        _self.pie_List[0].data[1].Percent = taskGraph.degradationPercent || 0;
        _self.pie_List[0].data[2].value = taskGraph.brokenSpecNum || 0;
        _self.pie_List[0].data[2].Percent = taskGraph.brokenPercent || 0;
      } else {
        _self.pie_List[0].totalValue = 0;
        _self.pie_List[0].data[0].value = 0;
        _self.pie_List[0].data[0].Percent = 0;
        _self.pie_List[0].data[1].value = 0;
        _self.pie_List[0].data[1].Percent = 0;
        _self.pie_List[0].data[2].value = 0;
        _self.pie_List[0].data[2].Percent = 0;
      }
    },
    //获取任务统计图表信息
    // 方法已废弃
    getStatistics(param) {
      let _self = this;
      //1.链路信息列表
      let params = {
        orgId: param.orgId,
        operator: param.operator,
        statisticalAngle: param.statisticalAngle,
        keyWord: param.keyWord ? param.keyWord : "",
        startTime: param.startTime,
        endTime: param.endTime,
      };
      _self.$http
          .PostJson("/specquality/getSpecNumStatistic", params)
          .then((res) => {
            if (res.code === 1) {
              if (res.data) {
                //统计图
                let taskGraph = res.data;
                _self.pie_List[0].totalValue = taskGraph.totalSpecNum || 0;
                _self.pie_List[0].data[0].value = taskGraph.normalSpecNum || 0;
                _self.pie_List[0].data[0].Percent = taskGraph.normalPercent || 0;
                _self.pie_List[0].data[1].value = taskGraph.degradationSpecNum || 0;
                _self.pie_List[0].data[1].Percent = taskGraph.degradationPercent || 0;
                _self.pie_List[0].data[2].value = taskGraph.brokenSpecNum || 0;
                _self.pie_List[0].data[2].Percent = taskGraph.brokenPercent || 0;
              } else {
                _self.pie_List[0].totalValue = 0;
                _self.pie_List[0].data[0].value = 0;
                _self.pie_List[0].data[0].Percent = 0;
                _self.pie_List[0].data[1].value = 0;
                _self.pie_List[0].data[1].Percent = 0;
                _self.pie_List[0].data[2].value = 0;
                _self.pie_List[0].data[2].Percent = 0;
              }
            }
          });
    },
    //获取{{$t('comm_interruption_top5')}}
    // 方法已废弃
    getInterruptTop(param) {
      this.loading2 = true;
      this.$http
          .PostJson("/specquality/getBrokenSpecTop", {
            startTime: param.startTime,
            endTime: param.endTime,
            orgId: param.orgId,
            operator: param.operator,
            statisticalAngle: param.statisticalAngle,
            keyWord: param.keyWord ? param.keyWord : "",
            timesOrder: null,
            durationOrder: null,
          })
          .then(({code, data, msg}) => {
            if (code === 1) {
              if (data && Array.isArray(data)) {
                this.tabList2 = data;
              } else {
                this.tabList2 = [];
              }
              this.loading2 = false;
            } else {
              this.$Message.error(res.msg)
            }
          }).catch((msg) => {
        this.loading2 = false;
      }).finally(() => {
        this.loading2 = false;
      });
    },
    //获取劣化TOP
    // 方法已废弃
    getdegradationTop(param) {
      this.loading3 = true;
      this.$http
          .PostJson("/specquality/getDegradationSpecTop", {
            startTime: param.startTime,
            endTime: param.endTime,
            orgId: param.orgId,
            operator: param.operator,
            statisticalAngle: param.statisticalAngle,
            keyWord: param.keyWord ? param.keyWord : "",
            timesOrder: null,
            durationOrder: null,
          })
          .then(({code, data, msg}) => {
            if (code === 1) {
              if (data && Array.isArray(data)) {
                this.tabList3 = data;
              } else {
                this.tabList3 = [];
              }
              this.loading3 = false;
            } else {
              this.$Message.error(res.msg)
            }
          }).catch((msg) => {
        this.loading3 = false;
      }).finally(() => {
        this.loading3 = false;
      });
    },
    getAllocationListFieldsByKey(){
      //key值
      let _self = this;
      _self.$http.PostJson("/allocationlistfields/getAllocationListFieldsByKey", { key: this.customKey }).then((res) => {
        if (res.code === 1) {
          if(res.data.allocationListFields){
            let screenWidthTemp = this.screenWidth;
            this.allocationListFields = res.data.allocationListFields;
            this.fieldsJsonObjArr = [];
            this.fieldsJsonObjArr.push("firstColumnSelect");//第一列选择列
            //中间自定义列
            res.data.allocationListFields.forEach(item=>{
                if(item.showField === true){
                  this.fieldsJsonObjArr.push(item.parameterName);
                }
            });
            this.fieldsJsonObjArr.push("action");//第一列选择列
            this.columns1 = [];
            let customColumnsWidth = 0;//回显自定义字段总宽度
            let customColumnsAvgWidth = 0;//回显自定义字段平均宽度
            //从固定列中动态回显自定义列标头并且按照顺序
            if(this.fieldsJsonObjArr.length>0){
              this.fieldsJsonObjArr.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item === item2.key){
                    //计算需要展示自定义字段项总长度
                    let customFieldsColumnsKeyWidthTemp = this.customFieldsColumnsKeyWidth.find(item3 => item3.key === item2.key);
                    if(customFieldsColumnsKeyWidthTemp){
                      customColumnsWidth = customColumnsWidth + customFieldsColumnsKeyWidthTemp.width;
                      item2.width = customFieldsColumnsKeyWidthTemp.width;
                    }
                    this.columns1.push(item2);
                    return;
                  }
                });
              });
              //赋值标头名称
              this.allocationListFields.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item.parameterName === item2.key){
                    item.parameterTitle = item2.title;
                    return;
                  }
                });
              });
              //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
              if(screenWidthTemp>customColumnsWidth ){
                if(this.columns1.length>2){
                  let columnsLength = this.columns1.length-2;
                  customColumnsAvgWidth = Math.floor((screenWidthTemp-customColumnsWidth)/columnsLength);
                }
              }
              this.columns1.forEach(item=>{
                if(item.key != "action" && item.key != "firstColumnSelect"){
                  item.width = item.width+customColumnsAvgWidth;
                }
              });

            }else{
              this.columns1 = this.fixedColumns;
            }
          }
        }else{
          this.columns1 = this.fixedColumns;
        }
      })
    },
    //获取任务列表
    getTask(param) {
      this.loading1 = true;
      this.loading2 = true;
      this.loading3 = true;
      this.queryLoading = true;
      var data = {
        startTime: param.startTime,
        endTime: param.endTime,
        orgId: param.orgId,
        operator: param.operator,
        statisticalAngle: param.statisticalAngle,
        keyWord: param.keyWord ? param.keyWord : "",
        pageNo: param.pageNo,
        pageSize: param.pageSize,
        screening: param.screening,
        faultType: param.faultType,
        groupIds: param.groupIds ? param.groupIds.join(",") : "",
        // 排序参数
        useableRateOrder: this.useableRateOrder,
        nrUseRateOrder: this.nrUseRateOrder,
        goodRateOrder: this.goodRateOrder,
        nrGoodRateOrder: this.nrGoodRateOrder,
        latestTimeOrder: this.latestTimeOrder,
        brokenCountOrder: this.brokenCountOrder,
        brokenAvgDurationOrder: this.brokenAvgDurationOrder,
        brokenDurationOrder: this.brokenDurationOrder,
        degradationCountOrder: this.degradationCountOrder,
        degradationAvgDurationOrder: this.degradationAvgDurationOrder,
        degradationDurationOrder: this.degradationDurationOrder,
         packetLossDegradationCountOrder: this.packetLossDegradationCountOrder,
          avgPacketLossDegradationTimeOrder: this.avgPacketLossDegradationTimeOrder,
          packetLossDegradationDurationOrder: this.packetLossDegradationDurationOrder,
        status: this.status,
      }
      this.$http.PostJson("/specquality/getSpecAnalysisList", data).then(({code, data, msg}) => {

        this.loading1 = false;
        this.loading2 = false;
        this.loading3 = false;
        this.queryLoading = false;

        if (code === 1) {
          // 统计图
          this.getStatisticsGraph({
            data: data.graph,
          });

          // 中断
          this.tabList2 = data.brokenTOPN;
          // 劣化
          this.tabList3 = data.degradationTOPN;
          // 下面的表格
          if (data.pages && data.pages.records) {
            this.tabList = data.pages.records;
            this.tabList1 = data.pages.records;
            this.totalCount = data.pages.total;
          } else {
            this.tabList = [];
            this.tabList1 = [];
            this.totalCount = 0
          }

        } else {
          this.tabList = [];
          this.tabList1 = [];
          this.tabList3 = [];
          this.totalCount = 0;
          this.$Message.error(res.msg)
        }
      }).catch((msg) => {
        this.tabList = [];
        this.tabList1 = [];
        this.tabList2 = [];
        this.tabList3 = [];
        this.totalCount = 0;
        this.loading1 = false;
        this.queryLoading = false;
      }).finally(() => {
        this.loading1 = false;
        this.loading2 = false;
        this.loading3 = false;
        this.queryLoading = false;
      });
    },
    topSortChange({column, key, order}) {
      //order值可能为asc，desc，normal（不排序）
      debugger
      let k = [];
      let t = [];
      k = this.orderKey;
      t = this.orderType;
      if (k.includes(key)) {
        let i = this.printArray(k, key);
        if (order === "normal") {
          k.splice(i, 1);
          t.splice(i, 1);
        } else {
          t.splice(i, 1, order);
        }
      } else {
        k.push(key);
        t.push(order);
      }
      this.orderKey = k;
      this.orderType = t;
      this.setsortType(key, order);
      // this.handleTheadAddClass(column, order);
      this.getTask(this.query);
    },
      sortChange(column) {
        debugger
        var order = "";
        if (column.order == "normal") {
          order = "asc"
        } else if (column.order == "asc") {
          order = "asc"
        } else if (column.order == "desc") {
          order= "desc"
        }
      let key = column.key;
      this.setsortType(key, order);
      this.getTask(this.query);
    },
    //设置单排序
    setsortType(current, type) {
      this.useableRateOrder = null;
      this.nrUseRateOrder = null;
      this.goodRateOrder = null;
      this.nrGoodRateOrder = null;
      this.latestTimeOrder = null;
      this.brokenCountOrder = null;
      this.brokenAvgDurationOrder = null;
      this.brokenDurationOrder = null;
      this.degradationCountOrder = null;
      this.degradationAvgDurationOrder = null;
      this.degradationDurationOrder = null;
       this.packetLossDegradationCountOrder= null;
       this.avgPacketLossDegradationTimeOrder= null;
       this.packetLossDegradationDurationOrder= null;
      this.$data[current + 'Order'] = type == 'normal' ? null : type;
    },
    //返回某元素在数组中下标
    printArray(k, key) {
      for (let i = 0; i < k.length; i++) {
        if (k[i] == key) {
          return i;
        }
      }
    },
    // 标题行样式改变事件
    handleTheadAddClass(column, order) {
      let i = [];
      let s = [];
      i = this.sortIndex;
      s = this.sortType;
      let key = column.sortNumber;
      var t = 0;
      if (i.includes(key)) {
        t = this.printArray(i, key);
        s.splice(t, 1, order);
      } else {
        i.push(key);
        s.push(order);
      }
      this.sortIndex = i;
      this.sortType = s;
      var currentIndex = this.printArray(i, key);
      for (let a = 0; a < this.sortIndex.length; a++) {
        let udom = document.getElementsByClassName("ivu-icon-md-arrow-dropup")[
            this.sortIndex[a]
            ];
        let ddom = document.getElementsByClassName(
            "ivu-icon-md-arrow-dropdown"
        )[this.sortIndex[a]];
        udom.removeAttribute("id");
        udom.setAttribute("class", "ivu-icon ivu-icon-md-arrow-dropup");
        ddom.removeAttribute("id");
        ddom.setAttribute("class", "ivu-icon ivu-icon-md-arrow-dropdown");
        this.$nextTick(() => {
        });
      }
      var udom = document.getElementsByClassName("ivu-icon-md-arrow-dropup")[
          key
          ];
      var ddom = document.getElementsByClassName(
          "ivu-icon-md-arrow-dropdown"
      )[key];
      if (order === "asc") {
        udom.id = "select-sort" + key;
        udom.setAttribute("class", "ivu-icon ivu-icon-md-arrow-dropup on");
      }
      if (order === "desc") {
        ddom.id = "select-sort" + key;
        ddom.setAttribute(
            "class",
            "ivu-icon ivu-icon-md-arrow-dropdown on"
        );
      }
    },
    //切换页码
    pageChange(index) {
      this.pageNo = this.query.pageNo = index;
      // let _start = (index - 1) * this.pageSize;
      // let _end = index * this.pageSize;
      // this.tabList1 = this.tabList.slice(_start, _end);
      this.getTask(this.query);
    },
    pageChangeModal(page) {
      this.look.listParam.pageNo = this.modalPage_no = page;
      this.getModalList(this.look.listParam);
    },
    //切换每页条数
    tabParamPageSizeChange(e) {
      this.modalPage_no = this.look.listParam.pageNo = 1;
      this.modalPageSize = this.look.listParam.pageSize = e;
      this.getModalList(this.look.listParam);
    },
    //切换每页条数
    pageSizeChange(pageSize) {
      this.pageNo = this.query.pageNo = 1;
      this.pageSize = this.query.pageSize = pageSize;
      // let _start = (this.pageNo - 1) * pageSize;
      // let _end = this.pageNo * pageSize;
      // this.tabList1 = this.tabList.slice(_start, _end);
      this.getTask(this.query);

    },
    //查看导出
    exportViewClick() {
      // this.saveLog();
      let fileName = this.$t('export_line_quality_report') + ".xlsx";
      // this.$Loading.start();
      this.$axios({
        url: "/specquality/exportSpecAnalysisList",
        method: "post",
        data: this.look.listParam,
        responseType: "blob", // 服务器返回的数据类型
      })
          .then((res) => {
            let data = res.data;
            const blob = new Blob([data], {type: "application/vnd.ms-excel"});
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, fileName);
            } else {
              // var fileName = "";
              // fileName = "故障清单.xlsx";
              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.$Loading.finish();
          })
          .catch((error) => {
            console.log(error);
          }).finally(() => {
        this.selectedIds = new Set();
        this.query.ids = null;
        this.queryClick();
        this.$Loading.finish();
      });
    },
    getTimes() {
      var queryTime = 0;
      if (this.timeRange.length == 0 || this.timeRange[0] == '') {
        queryTime = 1;
      }

      if (queryTime == 1) {
        // getTask
        // (62 * 24 * 3600 * 1000)
        this.query.startTime = new Date(new Date().getTime() - (61 * 24 * 3600 * 1000)).format("yyyy-MM-dd 00:00:00");
        this.query.endTime = new Date(new Date().getTime() + 86400000).format("yyyy-MM-dd 00:00:00");
      } else {
        if (
            this.startTime &&
            this.startTime != "" &&
            this.endTime &&
            this.endTime != ""
        ) {
          this.query.startTime = this.timeRange[0].format("yyyy-MM-dd HH:mm:ss");
          this.query.endTime = new Date(new Date(this.timeRange[1]).getTime() + 86400000).format('yyyy-MM-dd 00:00:00');
        }
      }
    },
    //导出全部
    exportClick() {
      if (this.tabList1.length > 0) {
        this.getTimes();
        let tokenId = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
        let a = document.getElementById("exportAll");
        let param = {
          orgId: this.query.orgId,
          statisticalAngle: this.query.statisticalAngle,
          keyWord: this.query.keyWord,
          startTime: this.query.startTime,
          endTime: this.query.endTime,
          operator: this.query.operator,
          faultType: this.query.faultType,
          groupIds: this.query.groupIds ? this.query.groupIds.join(",") : "",
          pageNo: 1,
          pageSize: this.totalCount,
          useableRateOrder: this.useableRateOrder,
          nrUseRateOrder: this.nrUseRateOrder,
          goodRateOrder: this.goodRateOrder,
          nrGoodRateOrder: this.nrGoodRateOrder,
          latestTimeOrder: this.latestTimeOrder,
          brokenCountOrder: this.brokenCountOrder,
          brokenAvgDurationOrder: this.brokenAvgDurationOrder,
          brokenDurationOrder: this.brokenDurationOrder,
          degradationCountOrder: this.degradationCountOrder,
          degradationAvgDurationOrder: this.degradationAvgDurationOrder,
          degradationDurationOrder: this.degradationDurationOrder,
          packetLossDegradationCountOrder: this.packetLossDegradationCountOrder,
          avgPacketLossDegradationTimeOrder: this.avgPacketLossDegradationTimeOrder,
          packetLossDegradationDurationOrder: this.packetLossDegradationDurationOrder,
        };
        //blob下载
        this.$Loading.start();
        axios({
          url: "/specquality/exportSpecAnalysis",
          method: "get",
          params: param,
          responseType: "blob", // 服务器返回的数据类型
        }).then((res) => {
          let data = res.data;
          const blob = new Blob([data], {type: "application/vnd.ms-excel"});
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.$t('export_line_quality_report') + ".xls");
          } else {
            let fileName = this.$t('export_line_quality_report') + ".xlsx";

            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        }).catch(error => {
          console.log(error);
          this.$Loading.finish();
        }).finally(() => {
          this.$Loading.finish();
        });
      } else {
        this.$Message.warning(this.$t('specquality_no_data'));
      }
    },
    //导出部分
    exportsingleClick() {
      let selectData = Array.from(this.selectDta);
      if (this.tabList1.length > 0) {
        this.getTimes();
        if (selectData.length > 0) {
          let param = {
            orgId: this.query.orgId,
            idList: selectData.join(','),
            statisticalAngle: this.query.statisticalAngle,
            keyWord: this.query.keyWord,
            startTime: this.query.startTime,
            endTime: this.query.endTime,
            faultType: this.query.faultType,
            groupIds: this.query.groupIds ? this.query.groupIds.join(",") : "",
            pageNo: 1,
            pageSize: this.totalCount,
            useableRateOrder: this.useableRateOrder,
            nrUseRateOrder: this.nrUseRateOrder,
            goodRateOrder: this.goodRateOrder,
            nrGoodRateOrder: this.nrGoodRateOrder,
            latestTimeOrder: this.latestTimeOrder,
            brokenCountOrder: this.brokenCountOrder,
            brokenAvgDurationOrder: this.brokenAvgDurationOrder,
            brokenDurationOrder: this.brokenDurationOrder,
            degradationCountOrder: this.degradationCountOrder,
            degradationAvgDurationOrder: this.degradationAvgDurationOrder,
            degradationDurationOrder: this.degradationDurationOrder,
             packetLossDegradationCountOrder: this.packetLossDegradationCountOrder,
          avgPacketLossDegradationTimeOrder: this.avgPacketLossDegradationTimeOrder,
          packetLossDegradationDurationOrder: this.packetLossDegradationDurationOrder,
          };
          //blob下载
          this.$Loading.start();
          axios({
            url: "/specquality/exportSpecAnalysis",
            method: "get",
            params: param,
            responseType: "blob", // 服务器返回的数据类型
          }).then((res) => {
            let data = res.data;
            const blob = new Blob([data], {type: "application/vnd.ms-excel"});
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, this.$t('export_line_quality_report') + ".xls");
            } else {
              let fileName = this.$t('export_line_quality_report') + ".xlsx";

              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.selectDta = new Set();
            let _that = this;
            setTimeout(function () {
              let objData = _that.$refs.tableListspecial.$refs.tbody.objData;
              for (let key in objData) {
                if (_that.selectDta.has(objData[key].id)) {
                  objData[key]._isChecked = true;
                } else {
                  objData[key]._isChecked = false;
                }
              }
            }, 0);
            this.$Loading.finish();
          }).catch(error => {
            console.log(error);
            this.$Loading.finish();
          }).finally(() => {
            this.$Loading.finish();
          });
        } else {
          this.$Message.warning(this.$t('specquality_select'));
        }
      } else {
        this.$Message.warning(this.$t('specquality_no_data'));
      }
    },
    //查看详情
    actionClick(rowData, type) {
      this.echartLookParama2.linkIds = null;
      this.echartLookParama2.linkId = null;
      this.eventTypes = 0;
      if (!rowData.specialId) {
        this.look.show = false;
        this.$Message.warning(this.$t('alarm_no_data'));
        return false;
      }
      let _self = this;
      this.healthLook = rowData;
      this.queryModal = {
        keyWord: "",
        startTime: this.dateValueCopy[0],
        endTime: this.dateValueCopy[1],
        linkId: rowData.linkIds,
        specialId: rowData.specialId,
        pageNo: this.queryModal.pageNo,
        pageSize: this.queryModal.pageSize,
      };
      _self.echartLookParama2.orgId = rowData.orgId;
      _self.echartLookParama2.specialAIp = rowData.specialAIp;
      _self.echartLookParama2.specialZIp = rowData.specialZIp;
      _self.echartLookParama2.specialId = rowData.specialId;
      _self.echartLookParama2.interfaceIp = rowData.interfaceIp;
      _self.echartLookParama2.devIp = rowData.devIp;
      _self.echartLookParama2.dataSource = rowData.dataSource;
      _self.echartLookParama2.High = false;
      _self.echartLookParama2.upBandwidth = rowData.upstreamBandwidth;
      _self.echartLookParama2.downBandwidth = rowData.downstreamBandwidth;

      _self.look.show = true;
      let titleDetails = "(" + (rowData.specialAIp ? this.$t('spec_a_end_inter_ip') + rowData.specialAIp + '，' : '') + this.$t('spec_z_end_inter_ip') + rowData.specialZIp + ")";
      var eventTypes = [];
      if (type === "broken") {
        _self.look.title = this.$t('nodequality_interrupt_details')
        eventTypes.push(1);
      }
      if (type === "degradation") {
        _self.look.title = this.$t('nodequality_deteriorate_details')
        eventTypes.push(2);
        eventTypes.push(3);
      }
      if (type === "specialLine") {
        _self.look.title = this.$t('nodequality_line_details')

      }
      _self.look.titleDetails = titleDetails
      _self.look.listParam = {
        linkIds: rowData.linkIds,
        specialId: rowData.specialId,
        startTime: this.dateValueCopy[0],
        endTime: this.dateValueCopy[1],
        eventType: "",
        eventTypes: eventTypes.join(","),
        statisticalAngle: this.clickQuery.statisticalAngle,
        operator: this.clickQuery.operator,
        lastEventTimeOrder: _self.look.listParam.lastEventTimeOrder,
        eventDurationOrder: _self.look.listParam.eventDurationOrder,
        pageNo: 1,
        pageSize: 3,
      };
      _self.modalPage_no = 1;
      _self.getModalList(_self.look.listParam);
    },
    getModalList(param) {
      this.tabListModal = [];
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.flowData.inUseRateList = [];
      this.flowData.outUseRateList = [];
      this.goodRateData.useRateList = [];
      this.goodRateData.goodRateList = [];
      this.goodRateData.nrUseRateList = [];
      this.goodRateData.nrGoodRateList = [];
      // this.btnClick(1);
      this.btnValue = 1;
      this.ModalTableLoading = true;
      this.ModalTopoLoading = true;
      this.ModalTrendLoading = true;
      param.pageSize = this.modalPageSize;
      this.$http
          .PostJson("/specquality/getSpecEventList", param)
          .then(({code, data, msg}) => {
            if (code === 1) {
              const {total, records} = data;
              this.ModalTableLoading = false;
              this.totalCountModal = total;
              if (records && Array.isArray(records)) {
                records.map((item, index) => {
                  item.rowId = index + '' + Math.random() * 100;
                  return item
                });
                this.tabListModal = records;
                if (this.tabListModal.length === 0) {
                  this.delayLossData.delay = [];
                  this.delayLossData.loss = [];
                  this.flowData.enter = [];
                  this.flowData.issue = [];
                  this.flowData.inUseRateList = [];
                  this.flowData.outUseRateList = [];
                  this.goodRateData.useRateList = [];
                  this.goodRateData.goodRateList = [];
                  this.goodRateData.nrUseRateList = [];
                  this.goodRateData.nrGoodRateList = [];
                  // this.btnClick(1);
                  this.btnValue = 1;
                } else {

                  //默认第一个选中
                  this.currentid = this.tabListModal[0].rowId;
                  //topo图参数设置，默认第一条
                  this.topologyParam = {
                    sourceIp: this.tabListModal[0].sourceIp,
                    destIp: this.tabListModal[0].destIp,
                    linkId: this.tabListModal[0].linkId,
                    logTracertId: this.tabListModal[0].logTracertId,
                    taskType: this.tabListModal[0].taskType,
                    startTime: this.tabListModal[0].startTs,
                    createTs: this.tabListModal[0].createTs,
                  };
                  if (Number(this.tabListModal[0].status) === 1) {
                    this.topologyParam.endTime = new Date().format2(
                        "yyyy-MM-dd hh:mm:ss"
                    );
                    this.echartLookParama2.endTime = new Date().format2(
                        "yyyy-MM-dd hh:mm:ss"
                    );
                  } else {
                    this.topologyParam.endTime = new Date(
                        Number(this.tabListModal[0].lastActiveTs) * 1000
                    ).format2("yyyy-MM-dd hh:mm:ss");
                    this.echartLookParama2.endTime = new Date(
                        Number(this.tabListModal[0].lastActiveTs) * 1000 +
                        3 * 3600 * 1000
                    ).format2("yyyy-MM-dd hh:mm:ss");
                  }
                  //趋势图故障点定位
                  this.markPoint = this.tabListModal[0].startTs;
                  this.markPointGreen = this.tabListModal[0].status == 1 ? '' : new Date(Number(this.tabListModal[0].lastActiveTs) * 1000).format2('yyyy-MM-dd HH:mm:ss');
                  //趋势图参数
                  this.echartLookParama2.startTime = moment(
                      this.tabListModal[0].startTs
                  )
                      .subtract(3, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  //判断是否是故障段
                  this.isIpduan = this.tabListModal[0].preFurthestIp ? true : false;
                  this.echartLookParama2.devIp = this.healthLook.devIp;
                  this.echartLookParama2.interfactIp = this.healthLook.interfactIp;
                  this.echartLookParama2.preNodeIp = this.tabListModal[0].sourceIp;
                  this.echartLookParama2.nodeIp = this.tabListModal[0].destIp;
                  this.echartLookParama2.linkIds = this.tabListModal[0].linkId;
                  this.echartLookParama2.linkId = this.tabListModal[0].linkId;
                  this.echartLookParama2.level = null;
                  this.echartLookParama2.special = true;
                  this.echartLookParama2.queryType = 2; //1是链路，2是节点
                  this.echartLookParama2.snmp = true
                  //获取拓扑图数据
                  this.getSnmp(this.echartLookParama2);
                }
              }
              this.ModalTableLoading = false;
            }
          })
          .catch((msg) => {
            this.ModalTableLoading = false;
            this.ModalTopoLoading = false;
            this.ModalTrendLoading = false;
            console.log(msg);
          }).finally(() => {
        this.ModalTableLoading = false;
        this.ModalTopoLoading = false;
        this.ModalTrendLoading = false;
      });
    },
    //详情排序
    sortModal(data) {
      this.look.listParam.lastEventTimeOrder = "";
      this.look.listParam.eventDurationOrder = "";
      if (data.key === "lastEventTime" || data.key === "startTs") {
        this.look.listParam.lastEventTimeOrder = data.order;
      } else if (data.key === "eventDuration" || data.key === "durationOfTime") {
        this.look.listParam.eventDurationOrder = data.order;
      }
      this.look.listParam.pageNo = 1;
      this.getModalList(this.look.listParam);
    },

    //处理拓扑图数据
    topologyHandle(ip, data, type, extraFla) {
      let ipArray = ip.split(",")[0],
          list = data;
      let number = 0, brokenNum = 0;
      if (list.length > 0) {
        let extraFlag = extraFla;
        let isBrokenF = false, isDelay = false, borkenIndex = 0, delayIndex = 0;
        list.forEach((item, index) => {
          if (item.isBroken === 1) {
            isBrokenF = true;
            borkenIndex = index;
          }
          if (item.isBroken === 2 || item.isBroken === 3) {
            isDelay = true;
            delayIndex = index;
          }
        });
        for (let i = 0, len = list.length; i < len; i++) {
          /*重新组装数据start*/
          if (list[i].isBroken != 0) {
            brokenNum = list[i].isBroken;
          }
          list[i].nodeColor = list[i].isBroken === 0 ? 'b' : (list[i].isBroken === 2 ? 'y' : 'r');
          list[i].lineColor = list[i].isBroken === 0 ? 'b' : (list[i].isBroken === 2 ? 'y' : 'r');
          list[i].linkPoint = list[i].isBroken == 1 ? true : false;
        }
        if (isBrokenF) {//中断
          if (extraFlag == 1) {//最远可达
            if (borkenIndex + 1 < list.length) {
              list[borkenIndex + 1].lineColor = 'r';
              list[borkenIndex + 1].linkPoint = true;
            }
          }
        }
        if (isDelay) {//劣化
          if (extraFlag == 1) {//最远可达
          }
        }
        let indexBroken = list.map(item => item.isBroken != 0).indexOf(true), indexX = 0;
        if (extraFlag == 1) {
          for (let i = indexBroken + 1; i < list.length; i++) {
            if (i === indexBroken + 1 && list[i].ip1 === '*' && i + 1 < list.length) {
              indexX = i + 1;
              list[i].lineColor = (brokenNum === 2) ? 'y' : 'r';
              list[i + 1].lineColor = (brokenNum === 2) ? 'y' : 'r';

            } else if (indexX === i && list[i].ip1 === '*' && i + 1 < list.length) {
              indexX = i + 1;
              list[i].lineColor = (brokenNum === 2) ? 'y' : 'r';
              list[i + 1].lineColor = (brokenNum === 2) ? 'y' : 'r';
            }
          }
        } else {
          for (let i = indexBroken - 1; i >= 0; i--) {
            if (i === indexBroken - 1 && list[i].ip1 === '*' && i - 1 >= 0) {
              indexX = i - 1;
              list[i].lineColor = (brokenNum === 2) ? 'y' : 'r';
            } else if (indexX === i && list[i].ip1 === '*' && i - 1 >= 0) {
              indexX = i - 1;
              list[i].lineColor = (brokenNum === 2) ? 'y' : 'r';
            }
          }
        }
        if (list.length === 2 && (list[0].isBroken === 1 || list[1].isBroken === 1)) {
          list[1].lineColor = 'r';
          list[1].nodeColor = 'r';
          list[1].linkPoint = true;
        }
        this.topologyList = list;
      } else {
        this.topologyList = [];
      }
    },
    //拓扑图点击事件
    topologyClick(param) {
      let _self = this;
      if (_self.ipTitleParam.data_source != this.$t('comm_high_freq')) {
        this.delayStart = 0;
        this.delayEnd = 100;
        if (param.index === _self.topologyList.length - 1 && param.type === 1) {
          _self.buttonVal = 1;
          _self.echartLookParama2.preNodeIp = param.data[0].ip1;
          _self.echartLookParama2.nodeIp = param.data[1].ip1;
          _self.echartLookParama2.queryType = 1;
          _self.getSnmp(_self.echartLookParama2);
        } else if (param.type === 2) {
          _self.buttonVal = 1;
          _self.echartLookParama2.preNodeIp = param.data[0].ip1;
          _self.echartLookParama2.nodeIp = param.data[1].ip1;
          _self.echartLookParama2.queryType = 2;
          _self.getSnmp(_self.echartLookParama2);
        }
      }
    },

    async getSnmp(params) {
      this.chartLoading = true;
      this.loading = true;
      this.noDataLoading = false;
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.flowData.inUseRateList = [];
      this.flowData.outUseRateList = [];
      this.goodRateData.useRateList = [];
      this.goodRateData.goodRateList = [];
      this.goodRateData.nrUseRateList = [];
      this.goodRateData.nrGoodRateList = [];
      params.level = '';
      await this.$http
          .PostJson("/trendData/getDelayAndLostTrend", params)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.preAlias.name = res.data.preName;
              this.preAlias.port = res.data.prePort;
              this.zAlias.name = res.data.name;
              this.zAlias.port = res.data.port;
              this.delayLossLevel = res.data.level;
              if (!res.data.lineOne || res.data.lineOne.length < 1) {
              }
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              let index = this.closest(lineData, this.markPoint);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.delayStart = 0;
              // if (res.data.lineOne && res.data.lineOne.length <= 300) {
              this.delayEnd = 100;
              this.startValue = res.data.lineOne[0][0];
              this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
              // } else if (
              //     res.data.lineOne &&
              //     res.data.lineOne.length > 300
              // ) {
              //   this.delayEnd = (300 * 100) / res.data.lineOne.length;
              //   if (index !== "noPoint") {
              //     if (index <= 150) {
              //       this.startValue = res.data.lineOne[0][0];
              //       if (index + 150 < res.data.lineOne.length - 1) {
              //         this.endValue = res.data.lineOne[index + 150][0];
              //       } else {
              //         this.endValue =
              //             res.data.lineOne[res.data.lineOne.length - 1][0];
              //       }
              //     } else {
              //       this.startValue = res.data.lineOne[index - 150][0];
              //       if (index + 150 < res.data.lineOne.length) {
              //         this.endValue = res.data.lineOne[index + 150][0];
              //       } else {
              //         this.endValue =
              //             res.data.lineOne[res.data.lineOne.length - 1][0];
              //       }
              //     }
              //   }
              // } else {
              //   this.delayEnd = 100;
              // }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            } else {
              this.delayLossData.delay = [];
              this.delayLossData.loss = [];
            }
          })
          .catch((msg) => {
            console.log(msg);
          });

      let trendParam = JSON.parse(JSON.stringify(params));
      trendParam.snmp = false;
      await this.$http
          .PostJson("/trendData/getSpecialFlowTrend", trendParam)
          .then((res) => {
            //流速趋势数据
            if (res.code === 1) {
              this.matchingType = res.data.matchingType;
              let flowUnit = 1;
              this.flowLevel = res.data.level;
              if (!res.data.lineOne || res.data.lineOne.length < 1) {
                this.echart2.show = false;
                this.flowLevel = 99;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.echart2.show = true;
                let unitChange = this.getFlowUnit(res.data.lineTwo);
                this.flowUnit = unitChange[0];
                this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
                this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
                flowUnit = unitChange[1];
                this.flowData.enter = res.data.lineTwo.map((item) => {
                  return [item[0], item[1]];
                });
              }
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.flowData.issue = res.data.lineOne.map((item) => {
                  return [item[0], item[1]];
                });
              }
              if (res.data.inUseRateList) {
                this.flowData.inUseRateList = res.data.inUseRateList.map(item => {
                  return [item[0], item[1]];
                });
              }
              if (res.data.outUseRateList) {
                this.flowData.outUseRateList = res.data.outUseRateList.map(item => {
                  return [item[0], item[1]];
                })
              }
              this.$store.commit("updateFlowHistory", {
                level: res.data.level,
                datas: [this.flowData.enter, this.flowData.issue]
              }); //保存数据
              this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
              this.$store.commit("setflowUnit", {
                level: res.data.level,
                unit: this.flowUnit
              }); //保存单位
            } else {
              this.flowData.enter = [];
              this.flowData.issue = [];
              this.flowData.inUseRateList = [];
              this.flowData.outUseRateList = [];
            }
          }).catch(() => {
            this.flowData.enter = [];
            this.flowData.issue = [];
            this.flowData.inUseRateList = [];
            this.flowData.outUseRateList = [];
          });
      this.startScale = false;
      await this.$http
          .PostJson("/specqualityTrendData/getGoodRateTrend", params)
          .then((res) => {
            //优良率趋势数据
            if (res.code === 1) {
              this.startScale2 = false;
              this.goodRateLevel = res.data.level;
              if (!res.data.useRateList && !res.data.goodRateList) {
                this.goodRateLevel = 99;
              }
              if (res.data.useRateList && res.data.useRateList.length > 0) {
                this.goodRateData.useRateList = res.data.useRateList;
              }
              if (res.data.goodRateList && res.data.goodRateList.length > 0) {
                this.goodRateData.goodRateList = res.data.goodRateList;
              }
              if (res.data.nrUseRateList) {
                this.goodRateData.nrUseRateList = res.data.nrUseRateList;
              }
              if (res.data.nrGoodRateList) {
                this.goodRateData.nrGoodRateList = res.data.nrGoodRateList;
              }
              this.delayStart2 = 0;
              // if (res.data.useRateList && res.data.useRateList.length <= 300) {
              this.delayEnd2 = 100;
              this.startValue2 = res.data.useRateList[0][0];
              this.endValue2 = res.data.useRateList[res.data.useRateList.length - 1][0];
              // } else if (res.data.useRateList && res.data.useRateList.length > 300) {
              //   this.delayEnd2 = (300 * 100) / res.data.useRateList.length;
              //   this.startValue2 = res.data.useRateList[0][0];
              //   this.endValue2 = res.data.useRateList[299][0];
              // } else {
              //   this.delayEnd2 = 100;
              // }
              this.$store.commit("setgoodRateTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            }
          });
      this.chartLoading = false;
      if (this.echart2.show) {
        this.height = 500;
      } else if (!this.echart2.show) {
        this.height = 260;
      }
      if (this.btnValue == 1) {
        this.initEchart();
      } else {
        this.initEchart2();
      }
      this.ModalTrendLoading = false;
    },
    setDelayLossTooltip() {
      let _self = this;
      return eConfig.tip("axis", function (param) {
        if (_self.btnValue == 1) {
          _self.scale = param[0].data[0];
        } else if (_self.btnValue == 2) {
          _self.scale2 = param[0].data[0];
        }
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex]
              ? ""
              : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let src = "",
            delayTime = "",
            delayTip = "",
            flowTime = "",
            flowTip = "",
            rateTime = "",
            rateTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
            delayTip +=
                param[i].seriesName +
                "：" +
                (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                    ? "--"
                    : param[i].value[1]) +
                _self.getFlowLegendUnit(param[i].seriesName) +
                "<br />";
          }
          if ((param[i].seriesIndex === 2 || param[i].seriesIndex === 3) && _self.btnValue == 1) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
            flowTip +=
                param[i].seriesName +
                "：" +
                (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                    ? "--"
                    : _self.flowSize(param[i].value[1], true, true)) +
                "<br />";
          }
          if ((param[i].seriesIndex === 2 || param[i].seriesIndex === 3) && _self.btnValue == 2) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
            flowTip +=
                param[i].seriesName +
                "：" +
                (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === ""
                    ? "--"
                    : (param[i].value[1] + '%')) +
                "<br />";
          }
          if (
              param[i].seriesIndex === 4 ||
              param[i].seriesIndex === 5 ||
              param[i].seriesIndex === 6 ||
              param[i].seriesIndex === 7
          ) {
            rateTime = param[i].data[0] + "<br />";
            rateTip +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
            rateTip +=
                param[i].seriesName +
                "：" +
                (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                    ? "--"
                    : param[i].value[1]) +
                _self.getFlowLegendUnit(param[i].seriesName) +
                "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip + rateTime + rateTip;
      });
    },
    // 获取流速的单位
    getFlowLegendUnit(seriesName) {
      // 这里做个 取值的转换操作
      let seriesNameStr = seriesName;
      if (seriesName === this.$t('dashboard_upstream') || seriesName == this.$t('dashboard_upstream')) {
        seriesNameStr = "入流速";
      } else if (
          seriesName === this.$t('dashboard_downstream') ||
          seriesName == this.$t('dashboard_uplink')
      ) {
        seriesNameStr = "入利用率";
      }
      return this.delayLossUnit[seriesNameStr];
    },
    // 获取流速信息
    getFlowLegendOption() {
      let flowLegendArray = [this.$t('dashboard_upstream'), this.$t('dashboard_down'), this.$t('dashboard_uplink'), this.$t('dashboard_downstream')];
      // matchingType => 0：Z端匹配，1：A端匹配
      // if (this.matchingType == 0) {
      //   // 出流速=>上行流速 , 入流速 => 下行流速
      //   flowLegendArray = [
      //     this.$t('dashboard_down'),
      //     this.$t('dashboard_upstream'),
      //     this.$t('dashboard_downstream'),
      //     this.$t('dashboard_uplink'),
      //   ];
      // } else if (this.matchingType == 1) {
      //   // 出流速=>下行流速 , 入流速 => 上行流速
      //   flowLegendArray = [
      //     this.$t('dashboard_upstream'),
      //     this.$t('dashboard_down'),
      //     this.$t('dashboard_uplink'),
      //     this.$t('dashboard_downstream'),
      //   ];
      // }
      return flowLegendArray;
    },
    Option() {
      if (this.flowData.enter.length < 1 && this.flowData.issue.length < 1 && this.flowData.inUseRateList.length < 1 && this.flowData.outUseRateList.length < 1) {
        this.echart2.show = false;
      }

      let flowLegendOption = this.getFlowLegendOption();

      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "6%",
              top: "40px",
              width: "90%",
              height: this.echart2.show ? "140px" : "140px",
            },
            {
              left: "6%",
              top: "270px",
              width: "90%",
              height: this.echart2.show ? "140px" : "0px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#fff" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t('speed_delay'), this.$t('comm_packet_loss')]
            },
            {
              show: this.echart2.show,
              top: "220px",
              right: "35%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#fff" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.flowColor,
              data: flowLegendOption,
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              splitLine: {
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2"
                },
              },
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                }
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              splitLine: {
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2"
                },
              },
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                }
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: this.$t('comm_delay(ms)'),
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
            },
            {
              name: this.$t('comm_loss_rate'),
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "right",
              max:this.lossMax,
              min:this.lossMin,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_velocity_flow') + "(" + this.flowUnit + ")",
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
              axisLabel: {
                show: true,
                formatter: (value) => {
                  let resualt = this.getUnit(value, true, true);
                  return resualt;
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_utilization'),
              type: "value",
              scale: true,
              gridIndex: 1,
              max: this.useMax,
              min: this.useMin,
              position: "right",
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                }
              }
            }
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] ,// "rgba(2, 29, 54, 1)",
              borderColor:  eConfig.dataZoom.borderColor[this.currentSkin] ,// "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] ,//  "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] ,//  top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            },
            {
              type: "slider",
              top: this.echart2.show ? "460px" : "230px",
              height: 20,
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] ,//"rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] ,//"rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] ,//"rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] ,// top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            },
          ],
          series: [
            {
              type: "line",
              name: this.$t('speed_delay'),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbol: this.delayLossData.delay.length > 1 ? "none" : "circle",
              color: this.delayLossColor[0],
               connectNulls: false,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.delayColor_0[this.currentSkin] ,//"rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.delayColor_8[this.currentSkin] ,// "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.delay.map(item => {
                    return [item[0], item[1] === 0 ? null : item[1]];
                  }),
              markLine: {
                symbol: ["pin", "none"],
                symbolSize: 15,
                data: [
                  {
                    symbol: 'image://' + pointRed,
                    symbolSize: 10,
                    xAxis: this.markPoint,
                    symbolRotate: '0',
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint
                      },
                      // backgroundColor: "#fff",
                    }
                  },
                  {
                    symbol: 'image://' + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen,
                    symbolRotate: '0',
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: "#fff",
                      formatter: () => {
                        return this.markPointGreen
                      },
                    }
                  }
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1,
                  },
                },
              },
            },
            {
              type: "line",
              name: this.$t('comm_packet_loss'),
              xAxisIndex: 0,
              yAxisIndex: 1,
              smooth: true,
              symbol: this.delayLossData.loss.length > 1 ? "none" : "circle",
              color: this.delayLossColor[1],
               connectNulls: false,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.lossColor_0[this.currentSkin] ,//"rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.lossColor_8[this.currentSkin] ,//"rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
               data: this.delayLossData.loss.map(item => {
                return [item[0], item[1] === 0 ? null : item[1]];
              }),
            },
            {
              show: this.echart2.show,
              // name: this.$t('specquality_incoming_velocity'),
              name: flowLegendOption[0],
              type: "line",
              smooth: true,
              symbol: this.flowData.enter.length > 1 ? "none" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color:  eConfig.areaStyle.flowInColor_0[this.currentSkin] ,//"rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color:  eConfig.areaStyle.flowInColor_8[this.currentSkin] ,//"rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              // name: this.$t('specquality_exit_velocity'),
              name: flowLegendOption[1],
              type: "line",
              smooth: true,
              symbol: this.flowData.issue.length > 1 ? "none" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowOutColor_8[this.currentSkin] ,//"rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              // name: "入利用率",
              name: flowLegendOption[2],
              type: "line",
              smooth: true,
              symbol: this.flowData.inUseRateList.length > 1 ? "none" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 3,
              color: this.flowColor[2],
              data: this.flowData.inUseRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowInRateColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowInRateColor_8[this.currentSkin] ,// "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              // name: "出利用率",
              name: flowLegendOption[3],
              type: "line",
              smooth: true,
              symbol: this.flowData.outUseRateList.length > 1 ? "none" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 3,
              color: this.flowColor[3],
              data: this.flowData.outUseRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color:  eConfig.areaStyle.flowOutRateColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color:  eConfig.areaStyle.flowOutRateColor_8[this.currentSkin] ,//"rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            }
          ],
        },
      ];
      return optionArr[0];
    },
    // 找出最大最小值
    findMaxAndMinValue(nestedArray) {
      let maxValue = -Infinity;
      let minValue = Infinity;
      nestedArray.forEach((subArray) => {
        if (subArray.length >= 2 && subArray[1] !== "") {
          const value = parseFloat(subArray[1]);
          if (!isNaN(value)) {
            maxValue = Math.max(maxValue, value);
            minValue = Math.min(minValue, value);
          }
        }
      });

      // 如果没有找到有效值，返回null
      return {
        max: isNaN(maxValue) ? null : maxValue,
        min: isNaN(minValue) ? null : minValue,
      };
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart1) {
        that.delayLossChart1.clear();
      }
      top.document.getElementById("specquality-delayLoss").style.height = this.height + "px";
      that.delayLossChart1 = echarts.init(this.$refs["specquality-delayLoss"]);
      that.delayLossChart1.resize();
      /*设置时延丢包率echart图*/
      // 获取Y轴最大最小值
      let lossMaxMin=this.findMaxAndMinValue(this.delayLossData.loss);
      this.lossMax=lossMaxMin.max;
      this.lossMin=lossMaxMin.min;
      let inUseMaxMin=this.findMaxAndMinValue(this.flowData.inUseRateList);
      let outUseMaxMin=this.findMaxAndMinValue(this.flowData.outUseRateList);
      this.useMax=inUseMaxMin.max>outUseMaxMin.max?inUseMaxMin.max:outUseMaxMin.max;
      this.useMin=inUseMaxMin.min<outUseMaxMin.min?inUseMaxMin.min:outUseMaxMin.min;
      that.delayLossChart1.setOption(this.Option());
      that.delayLossChart1.getZr().on("mousewheel", (params) => {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据

        var pointInPixel = [params.offsetX, params.offsetY];
        if (
            that.delayLossChart1.containPixel(
                {gridIndex: [0, 1, 2]},
                pointInPixel
            )
        ) {
          let startValue =
              that.delayLossChart1.getModel().option.dataZoom[0].startValue;
          let endValue =
              that.delayLossChart1.getModel().option.dataZoom[0].endValue;
          this.echartLookParama2.startTime = new Date(startValue).format2(
              "yyyy-MM-dd HH:mm:ss"
          );
          this.echartLookParama2.endTime = new Date(endValue).format2(
              "yyyy-MM-dd HH:mm:ss"
          );
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);
          let differ = endValue - startValue;
          let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
          let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
          let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
          var levelNum = "";
          let start = that.delayLossChart1.getModel().option.dataZoom[0].start;
          let end = that.delayLossChart1.getModel().option.dataZoom[0].end;

          if (params.wheelDelta >= 0 && that.startScale == false) {
            if (this.delayLossLevel == 1) {
              console.log('小时粒度');
              // 小时粒度
              if (differ < minute) {
                if (!that.startScale) {
                  that.startScale = true;
                  levelNum = 2;
                  let delayParam = Object.assign(this.echartLookParama2, {
                    level: levelNum,
                  });
                  let flowParam = Object.assign(delayParam, {
                    snmp: this.dataSource == 1 ? true : false,
                  });
                  this.getDelayLoss(delayParam, flowParam);
                }
              }
            } else if (this.delayLossLevel == 2) {
              // 分钟粒度
              console.log('分钟粒度');
              if (differ < original) {
                if (!that.startScale) {
                  that.startScale = true;
                  if (this.High) {
                    levelNum = 4;
                    let delayParam = Object.assign(this.echartLookParama2, {
                      level: levelNum,
                    });
                    let flowParam = Object.assign(delayParam, {
                      snmp: this.dataSource == 1 ? true : false,
                    });
                    this.getDelayLoss(delayParam, flowParam);
                  }
                }
              }
            }
          } else {
            if (start == 0 && end == 100) {
              if (!that.startScale) {
                //是否处在缩放过程中
                if (that.delayLossLevel == getTlevel) {
                  that.setPs(that.delayLossLevel, [
                    that.timeChange(startValue),
                    that.timeChange(endValue),
                  ]);
                  that.startScale = false;
                } else {
                  let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                  if (that.flowLevel == getflowTlevel) {
                    that.startScale = false;
                  } else {
                    if (that.flowLevel == 2) {
                      that.flowLevel = 1;
                      that.startScale = true;
                      that.flowUnit = getflowSaveUnit.HoursUnit;
                      that.flowData.enter = getflowSaveData.HoursData.enter;
                      that.flowData.issue = getflowSaveData.HoursData.issue;
                    } else if (that.flowLevel == 3 || that.flowLevel == 4) {
                      that.flowLevel = 2;
                      that.startScale = true;
                      that.flowUnit = getflowSaveUnit.minuteUnit;
                      that.flowData.enter = getflowSaveData.minuteData.enter;
                      that.flowData.issue = getflowSaveData.minuteData.issue;
                    }
                  }

                  if (that.delayLossLevel == getTlevel) {
                    that.startScale = false;
                  } else if (that.delayLossLevel == 2) {
                    that.delayLossLevel = 1;
                    that.startScale = true;
                    that.delayLossData.delay = getSaveData.HoursData.delay;
                    that.delayLossData.loss = getSaveData.HoursData.loss;
                    that.delayStart = getSite.psH.start;
                    that.startValue = getSite.psH.start;
                    that.delayEnd = getSite.psH.end;
                    that.endValue = getSite.psH.end;
                  } else if (
                      that.delayLossLevel == 3 ||
                      that.delayLossLevel == 4
                  ) {
                    that.delayLossLevel = 2;
                    that.startScale = true;
                    that.delayLossData.delay = getSaveData.minuteData.delay;
                    that.delayLossData.loss = getSaveData.minuteData.loss;
                    that.delayStart = getSite.psM.start;
                    that.startValue = getSite.psM.start;
                    that.delayEnd = getSite.psM.end;
                    that.endValue = getSite.psM.end;
                  }

                  setTimeout(() => {
                    that.startScale = false;
                    that.initEchart();
                  }, 300);
                }
              }
            }
          }
        }
      });
    },
    Option2() {
      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "6%",
              top: "40px",
              width: "90%",
              height: "140px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "35%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#fff" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.goodRateColor,
              data: [this.$t('specquality_availability'), this.$t('specquality_good_rate'), this.$t('specquality_availability_exemption'), this.$t('specquality_good_rate_exemption')],
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2"
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: this.$t('comm_unit') + "(" + this.goodRateUnit + ")",
              type: "value",
              scale: true,
              gridIndex: 0,
              max: 100,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0],
              startValue: this.startValue2,
              endValue: this.endValue2,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // "rgba(2, 29, 54, 1)",
              borderColor:eConfig.dataZoom.borderColor[this.currentSkin] , //  "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , // "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , // top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              }
            },
            {
              type: "slider",
              left: "5%",
              right: "5%",
              top: "230px",
              height: 20,
              xAxisIndex: [0],
              realtime: true,
              startValue: this.startValue2,
              endValue: this.endValue2,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , //"rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , //"rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , //"rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , //top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              }
            },
          ],
          series: [
            {
              name: this.$t('specquality_availability'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol:
                  this.goodRateData.useRateList.length > 1 ? "none" : "circle",
              color: this.goodRateColor[0],
              data: this.goodRateData.useRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(71,142,233, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(71,142,233, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              name: this.$t('specquality_good_rate'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol:
                  this.goodRateData.goodRateList.length > 1 ? "none" : "circle",
              color: this.goodRateColor[1],
              data: this.goodRateData.goodRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(70,183,89, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(70,183,89, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              name: this.$t('specquality_availability_exemption'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol:
                  this.goodRateData.nrUseRateList.length > 1 ? "none" : "circle",
              color: this.goodRateColor[2],
              data: this.goodRateData.nrUseRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              name: this.$t('specquality_good_rate_exemption'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol:
                  this.goodRateData.nrGoodRateList.length > 1 ? "none" : "circle",
              color: this.goodRateColor[3],
              data: this.goodRateData.nrGoodRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(36,195,197, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(36,195,197, 0.2)",
                    },
                  ],
                },
              },
            }
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart2() {
      let that = this;
      if (that.delayLossChart2) {
        that.delayLossChart2.clear();
      }
      top.document.getElementById("specquality-delayLoss2").style.height = this.height + "px";
      that.delayLossChart2 = echarts.init(this.$refs["specquality-delayLoss2"]);
      that.delayLossChart2.resize();
      /*设置时延丢包率echart图*/
      that.delayLossChart2.setOption(this.Option2());
    },

    async getDelayLoss(
        delayParam,
        flowParam,
        cachF,
        hoverDelayTime,
    ) {
      console.log('getDelayLoss');
      let isUpdate = false;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        this.chartLoading = true;
        await this.$http
            .PostJson("/trendData/getDelayAndLostTrend", delayParam)
            .then((res) => {
              //时延丢包趋势数据
              if (res.code === 1) {
                this.delayLossLevel = res.data.level;
                let lineData = [];
                if (res.data.lineOne) {
                  lineData = res.data.lineOne;
                } else if (res.data.lineTwo) {
                  lineData = res.data.lineTwo;
                }
                let index = this.closest(lineData, hoverDelayTime);
                if (res.data.lineOne && res.data.lineOne.length > 0) {
                  this.delayStart = 0;
                  // if (res.data.lineOnetotal <= 300) {
                  this.delayEnd = 100;
                  this.startValue = res.data.lineOne[0][0];
                  this.endValue =
                      res.data.lineOne[res.data.lineOne.length - 1][0];
                  // } else {
                  //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
                  //   if (index <= 150) {
                  //     this.startValue = res.data.lineOne[0][0];
                  //     if (index + 150 < res.data.lineOnetotal - 1) {
                  //       this.endValue = res.data.lineOne[index + 150][0];
                  //     } else {
                  //       this.endValue =
                  //         res.data.lineOne[res.data.lineOne.length - 1][0];
                  //     }
                  //   } else {
                  //     this.startValue = res.data.lineOne[index - 150][0];
                  //     if (index + 150 < res.data.lineOnetotal) {
                  //       this.endValue = res.data.lineOne[index + 150][0];
                  //     } else {
                  //       this.endValue =
                  //         res.data.lineOne[res.data.lineOne.length - 1][0];
                  //     }
                  //   }
                  // }
                  this.startScale = false;
                  this.delayLossScale = true;
                  this.delayLossData.delay = res.data.lineOne;
                }
                if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                  this.delayLossData.loss = res.data.lineTwo;
                }
                this.$store.commit("updateDelayLossHistory", {
                  level: res.data.level,
                  datas: [this.delayLossData.delay, this.delayLossData.loss],
                }); //保存数据
                this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
              }
            });
      }

      if (flowParam.level != 99) {
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, {snmp: true});
        await this.$http
            .PostJson("/trendData/getSpecialFlowTrend", trendParam)
            .then((res) => {
              //流速趋势数据
              if (res.code === 1) {
                this.matchingType = res.data.matchingType;
                let flowUnit = 1;
                this.flowLevel = res.data.level;
                if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                  let unitChange = this.getFlowUnit(res.data.lineOne);
                  this.flowUnit = unitChange[0];
                  this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
                  this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
                  flowUnit = unitChange[1];
                  this.flowData.enter = res.data.lineTwo.map((item) => {
                    return [item[0], item[1]];
                  });
                }
                if (res.data.lineOne && res.data.lineOne.length > 0) {
                  this.flowData.issue = res.data.lineOne.map((item) => {
                    return [item[0], item[1]];
                  });
                }

                this.$store.commit("updateFlowHistory", {
                  level: res.data.level,
                  datas: [this.flowData.enter, this.flowData.issue],
                }); //保存数据
                this.$store.commit("setflowUnit", {
                  level: res.data.level,
                  unit: this.flowUnit,
                }); //保存单位
              }
            });
      }

      this.delayLoading = false;
      this.chartLoading = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      if (isUpdate) {
        this.initEchart();
      }
    },

    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },

    // 获取运营商类型国际化信息
    getListInternationaControlOperator() {
      this.$http.wisdomPost('/internationaControl/queryCode', {key: "internation_operator_type"}).then(res => {
        if (res.code == 1) {
          if (res.data) {
            this.internationOperatorListType = res.data.value;
          }
        }
        this.getListOperator();
      });
    },
    //获取运营商
    getListOperator() {
      this.operatorList = [];
      var internationOperatorTypeTemp = 'chinaOperator';
      if (this.internationOperatorListType == '1') {
        internationOperatorTypeTemp = 'philippinesOperator';
        this.operatorList.push({value: 0, label: 'All'});
      } else {
        this.operatorList.push({value: 0, label: this.$t('comm_all')});
      }
      this.$http
          .wisdomPost("/dataTable/queryCode", {key: internationOperatorTypeTemp})
          .then(({code, data, msg}) => {
            if (code === 1) {
              if (data) {
                data.map(item => {
                  this.operatorList.push({value: parseInt(item.value), label: item.lable});
                });
              }
            } else {
              this.operatorList = [];
              // this.$Message.warning({content:msg,background:true});
            }
          })
          .catch((err) => {
            this.operatorList = [];
            throw new Error(err);
          });
    },
    //时间推算
    setTime(level, time) {
      let Interval = 0,
          start = new Date(this.echartLookParama2.startTime).getTime(),
          end = new Date(this.echartLookParama2.endTime).getTime(),
          newStart = 0,
          newEnd = 0;
      if (level == 0) {
      } else if (level == 1) {
        Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 2) {
        Interval = 12 * 60 * 60 * 1000;
      }
      newStart = time - Interval;
      newEnd = time + Interval;

      if (newStart < start) {
        newStart = start;
      }
      if (newEnd > end) {
        newEnd = end;
      }
      newStart = this.timeChange(newStart);
      newEnd = this.timeChange(newEnd);
      return [newStart, newEnd];
    },
    editCancel() {
    },
    /*趋势图指标切换*/
    btnClick(num) {
      this.btnValue = num;
      if (this.btnValue == 1) {
        setTimeout(() => {
          this.echartLookParama2.level = ''
          this.getDelayLoss(this.echartLookParama2, this.echartLookParama2)
        }, 500)
      } else {
        setTimeout(() => {
          this.initEchart2()
        }, 500)
      }
    },
    //多选
    //添加人员表格数据选择
    selectsingle(slection, row) {
      this.selectDta.add(row.specialId);
    },
    cancelsingle(slection, row) {
      this.selectDta.delete(row.specialId);
    },
    selectPage(slection) {
      if (slection.length === 0) {
        let data = this.$refs.tableListspecial.data;
        data.forEach((item) => {
          if (this.selectDta.has(item.specialId)) {
            this.selectDta.delete(item.specialId);
          }
        });
      } else {
        slection.forEach((item) => {
          this.selectDta.add(item.specialId);
        });
      }
    },
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
};
</script>

<style>
.ivu-select-multiple .ivu-tag {
  max-width: 120px;
}
.action-btn {
  color: #05eeff;
  cursor: pointer;
}

.taskStrategy-modal .ivu-table-header th {
  background: #f1f6fe !important;
}

.fault-tab b {
  font-weight: bold !important;
}

.fault-tab .ivu-table-wrapper-with-border {
  border: 1px solid #dcdee2;
  border-bottom: 0;
  border-right: 0;
}

.tableBorder .ivu-table:after {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background-color: #dcdee2;
  z-index: 3;
}

.taskStrategy-modal .ivu-table-wrapper-with-border {
  border: 1px solid #dcdee2;
  border-right: 0;
}

.healthManage p {
  text-align: left !important;
  margin-bottom: 5px !important;
}

.pie-div {
  background-color: var(--body_conent_b_color, #061824);
  padding: 15px;
}

.echarts-pie {
  width: 208px;
  height: 208px;
  margin: 0 auto;
}

.pie-example {
  display: flex;
  justify-content: space-around;
}

.pie-example-box {
  display: flex;
}

.pie-example-box .icon-box {
  display: block;
  width: 36px;
  height: 36px;
  margin: 0 auto 13px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 36px;
}

.icon-normal {
  background-image: url("../../../assets/wisdom/icon-chart1.png");
}
.light-icon-normal {
  background-image: url("../../../assets/wisdom/light-icon-chart1.png");
}

.icon-degradation {
  background-image: url("../../../assets/wisdom/icon-chart2.png");
}

.icon-end {
  background-image: url("../../../assets/wisdom/icon-chart3.png");
}

.example-right {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 5px;
}

.example-header {
  display: flex;
  align-items: center;
}

.example-title {
  font-size: 14px;
  color: var(--th_font_color, #303748) !important;
  text-align: center;
  height: 14px;
  line-height: 14px;
}

.example-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--th_font_color, #303748) !important;
  height: 24px;
  line-height: 24px;
  margin-left: 5px;
}

.example-beliel {
  text-align: center;
  font-size: 16px;
  color: #5ca0d5;
  height: 18px;
  line-height: 18px;
}

.pie-example-hover {
  cursor: pointer;
}

.task-modal .ivu-modal-body {
  font-size: 14px !important;
}

.task-modal .ivu-modal-body .taskStrategy-modal a,
.task-modal .ivu-modal-body .taskStrategy-modal .ivu-page-total {
  font-size: 14px !important;
  vertical-align: middle;
}

.task-modal .ivu-modal-body .taskStrategy-modal .snmp-content-right {
  font-size: 14px;
}

.wisdom-fault .wisdom-fault-top .fault-top-box .ivu-picker-confirm button {
  width: auto !important;
  height: auto !important;
}

.btnChange > a {
  display: inline-block;
  vertical-align: top;
  width: 120px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  /* color: #484b56; */
  font-weight: bold;
  /* border: solid 1px #e6e8ee; */

  color: var(--btnChange_color, #5ca0d5);
  border: solid 1px var(--btnChange_border_color, #02b8fd);
}

.btnChange > a.active {
  /* color: #fff; */
  /* background-color: #4e7bff; */

  color: var(--btnChange_active_color, #060d15);
  background-color: var(--btnChange_active_bg_color, #4e7bff);
  border: 0px;
}

.my-table-fixed .ivu-table-fixed {
}

.headerSelectArea .ivu-select-selection {
  border: none;
  background-color: #f1f6fe;
}

.headerSelectArea.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
</style>
<style lang='less'>
.topTable td {
  background-color: var(--table_td_bg_color, #061824) !important;
  height: 45px !important;
}
</style>
