<template>
  <Modal
    sticky
    :width="modalWidth"
    v-model="modalShow"
    :title="modalTitle"
    :class="
      isbigscreen
        ? 'probetask-modal map-detail-model-bigscreen'
        : 'index-modal probetask-modal map-detail-model'
    "
    @on-visible-change="onVisibleChange"
    draggable
    :mask="true"
    :footer-hide="true"
  >
    <!-- 表格 -->
    <div style="position: relative">
      <loading :loading="ModalTableLoading" />
      <Table
        ref="tableListModal"
        stripe
        max-height="190"
        :columns="columnsModal"
        :data="tableList"
        class="modalTable modalTableover"
        :no-data-text="
          ModalTableLoading
            ? ''
            : tableList.length > 0
            ? ''
            : currentSkin == 1
            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
              $t('common_No_data') +
              '</p></div>'
            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
              $t('common_No_data') +
              '</p></div>'
        "
        size="small"
      >
      </Table>
    </div>
    <!-- /表格 -->
    <!-- 分页 -->
    <div class="tab-page">
      <Page
        v-if="tableList.length > 0"
        v-page
        class="infoListPage"
        :current="tableParams.pageNo"
        :page-size="tableParams.pageSize"
        :total="total"
        @on-page-size-change="tabParamPageSizeChange"
        :page-size-opts="[10, 20, 30, 40, 50]"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChangeModal"
        show-total
        show-sizer
      >
      </Page>
    </div>
    <!-- /分页 -->
    <section ref="indexEvent">
      <!-- 路径tuopu -->

      <div class="index-title">
        {{ $t("probetask_path_topology_tip") }}（{{
          this.routeCode ? this.routeCode : "--"
        }})
      </div>
      <div style="position: relative">
        <loading :loading="uTopoLoading" />
        <topology-item
          :data="topologyList"
          :linkData="linkData"
          ref="topologyChart"
          @on-click="topologyClick"
        ></topology-item>
      </div>
      <!-- 时间选择器 -->
      <!-- v-show="pathShow" -->
      <div class="index-query-r">
        <label>{{ $t("probetask_date") }}</label>

        <DatePicker
          format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          :options="timeOptions"
          v-model="timeRange"
          :editable="false"
          :clearable="false"
          style="width: 330px"
          :confirm="false"
          @on-change="dateChange"
        >
        </DatePicker>

        <Button
          class="query-btn"
          icon="ios-search"
          type="primary"
          @click="indexQueryClick"
        ></Button>
      </div>

      <!-- /时间选择器 -->
      <!-- 趋势图 -->
      <div class="index-title">
        {{ $t("probetask_indicator_trends_tip") }}
      </div>
      <div class="index-line-box" style="position: relative">
        <loading :loading="delayLossLoading" />
        <!--趋势图-->
        <div class="lookBox" style="position: relative; margin-top: 10px">
          <div
            class="title"
            style="position: absolute; left: 0; right: 0; top: -10px"
          >
            {{
              this.echartLookParama2.preNodeIp.includes("-9999")
                ? this.echartLookParama2.preNodeIp.split("_")[1]
                : this.echartLookParama2.preNodeIp
            }}————>{{
              this.echartLookParama2.nodeIp.includes("-9999")
                ? this.echartLookParama2.nodeIp.split("_")[2]
                : this.echartLookParama2.nodeIp
            }}
          </div>
          <div
            class="title"
            v-if="echart2.show"
            style="position: absolute; left: 0; right: 0; top: 45%"
          >
            {{ flowData.devName
            }}{{ flowData.interfaceName ? "—" + flowData.interfaceName : ""
            }}{{ flowData.interfaceIp ? "—" + flowData.interfaceIp : "" }}
          </div>
          <div class="contain">
            <div
              ref="probetask-delayLoss"
              id="probetask-delayLoss"
              class="echartStyle"
              :style="'height:' + height + 'px'"
            ></div>
          </div>
          <!-- <div
              :class="{
                table_empty: currentSkin == 1,
                table2_empty: currentSkin == 0,
              }"
              v-else
            >
              <p class="emptyText">{{ $t("common_No_data") }}</p>
            </div> -->
        </div>
      </div>
      <!-- /趋势图 -->
    </section>
  </Modal>
</template>
  
  <script>
  import ipv6Format from "@/common/ipv6Format";
  import echartFn from "@/common/mixins/echartFun";
  let echarts = require("echarts/lib/echarts");
  require("echarts/lib/chart/line");
  require("echarts/lib/component/tooltip");
  require("echarts/lib/component/title");
  require("echarts/lib/component/legend");
  require("echarts/lib/component/dataZoom");
  require("echarts/lib/component/markLine");
  import moment from "moment";
  import eConfig from "@/config/echart.config.js";
  
  import "@/config/page.js";
  export default {
        mixins: [echartFn],
      props:{
          // 弹框标题
          modalTitle:{
              type:String,
              default:''
          },
          queryData:{
              type:Object,
              default:()=>{}
          },
          // 控制弹框显示
          modalShow:{
              type:Boolean,
              default:false
          }
      },
      components:{
          topologyItem:()=>import('@/common/flowChart/topologyX.vue')
      },
      computed: {
          linkData() {
        let obj = {
          linkId: this.tabsId,
        };
        console.log("thisLInkData:::::" + this.tabsId)
        return obj;
      },
      },
      data(){
          return {
            echart1: {
                show: true,
            },
            isbigscreen: false,
              currentSkin:sessionStorage.getItem('dark') || 1,
              ModalTableLoading:false,
              delayLossLoading:false,
              uTopoLoading:false,
              delayLossColor: ["#00FFEE", "#0290FD"],
              flowColor: ["#478EE9", "#F19149"],
              modal1:false,
              modalWidth:'',
              taskId:'',
              topoShow: false,
              tableList:[],
              total:0,
              delayLossChart1:null,
              event_number: 0,
                delay_line: [], //时延走势数据
                  loss_line: [], //丢包走势数据
                  timeLine: {
                      data: [],
                  },
              tableParams:{
                  pageNo:1,
                  pageSize:10,
                  groupId:'',
                  alarmType:''
              },
                usea: {
                  preNodeIp: "",
                  nodeIp: "",
                  // 趋势图专用参数
                  nodeIpTrend: "",
                  sourceIp: "",
                  topoId: "",
                  targetIp: "",
              },
              routeCode: "",
                 delayLossData: {
                      delay: [],
                      loss: [],
                  },
                // 表格数据
        columnsModal: [
          // 选择
          {
            title: this.$t("but_choose"),
            width: 70,
            className: "bgColor",
            // slot: 'action'
            render: (h, params) => {
              let id = params.row.taskId;
              let flag = false;
              if (this.taskId === id) {
                flag = true;
              } else {
                flag = false;
              }
              let self = this;
  
              return h("div", [
                h("Radio", {
                  style: {
                    marginRight: 0,
                  },
                  props: {
                    value: flag,
                  },
                  on: {
                    "on-change": () => {
                       this.taskId = params.row.taskId;
                       this.routeParams = params.row
                     
                      
                      // this.routeCode = this.routeCode;
                       this.routeParams.sysLinkId = this.routeParams.routeId
                    
                       this.getPathTopo()
                      this.echartLookParama2.linkId = this.routeParams.sysLinkId
                      this.echartLookParama2.nodeIp = this.routeParams.showDestIp
                      this.echartLookParama2.preNodeIp = this.routeParams.showSourceIp
                      this.routeCode = this.routeParams.taskNum
                      this.echartLookParama2.startTime = this.startDay;
                      this.echartLookParama2.endTime = this.endDay;
                      this.getTrend()
                      // this.getPathChartData(2);
                      
                    },
                  },
                }),
              ]);
            },
          },
          // 影响路径编号
          {
            title: this.$t("task_number"),
            key: "displayId",
          },
          // 故障类型
          {
            title: this.$t("alarm_fault_type"),
            key: "alarmType",
            render: (h, params) => {
              let type = params.row.alarmType;
              let text = "--";
              let color = "#FF0000";
              
              switch (type) {
                case 0:
                  text = this.$t("comm_normal");
                   color = "#FFFFFF";
                  break;
                case 1:
                  text = this.$t("server_inter_alarm");
                  break;
                case 2:
                  text = this.$t("comm_delay");
                  break;
                case 3:
                  text = this.$t("comm_loss_package");
                  break;
                case 4:
                  text = this.$t("but_pause");
                  break;
                case 5:
                text = this.$t("comm_delay")+","+this.$t("comm_loss_package");
                break;
                default:
                  text = "--";
                  break;
              }
              return h("div", {
                style: {
                  color: color
                }
              }, text);
              
              return h("div", text);
            },
          },
          // 源ip
          {
            title: this.$t("comm_source_ip"),
            key: "showSourceIp",
            width:250,
            render: (h, params) => {
              let str = params.row.showSourceIp;
               str =
                str === undefined || str === null || str === "" || str === "null"
                  ? "--"
                  : str;
  
                   let maxWidth = params.column.width; // 获取动态传递的宽度
                str = ipv6Format.formatIPv6Address(str,maxWidth);
                return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
            },
  
          },
           // 目标IP
          {
            title: this.$t("comm_target_ip"),
            key: "showDestIp",
            width:250,
            render: (h, params) => {
              let str = params.row.showDestIp;
               str =
                str === undefined || str === null || str === "" || str === "null"
                  ? "--"
                  : str;
  
                   let maxWidth = params.column.width; // 获取动态传递的宽度
                str = ipv6Format.formatIPv6Address(str,maxWidth);
                return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
            },
          },
          // 目标名称
          {
            title: this.$t("comm_target_name"),
            key: "destIpName",
            render: (h, params) => {
              let str = params.row.destIpName;
              str =
                str === undefined || str === null || str === "" || str === "null"
                  ? "--"
                  : str;
              return h(
                "div",
                {
                  style: {
                    textAlign: "left",
                    width: "100%",
                    textIndent: "0px",
                    overflow: "hidden", //超出的文本隐藏
                    textOverflow: "ellipsis", //溢出用省略号显示
                    whiteSpace: "nowrap", //溢出不换行
                  },
                  attrs: {
                    title: str,
                  },
                },
                str
              );
            },
          },
          // 拨测类型
          {
            title: this.$t("task_dial_type"),
            key: "taskType",
            render: (h, params) => {
              let str = params.row.configType,
                text = "--";
              let routePath = params.row.routePath || "";
              switch (str * 1) {
                // case 0:
                //     text=' 集团遍历';
                //     break;
                case 1:
                  text = " UDP Trace";
                  break;
                case 2:
                  text = "ICMP Ping";
                  break;
                case 3:
                  text =
                    routePath.indexOf("-") >= 0
                      ? routePath.split("-")[1] == 1
                        ? "UDP Ping"
                        : routePath.split("-")[1] == 2
                        ? "ICMP Ping"
                        : routePath.split("-")[1] == 3
                        ? "TCP Ping"
                        : "--"
                      : "--";
                  break;
                case 4:
                  text = "UDP Trace";
                  break;
                case 5:
                  text = "ICMP Trace";
                  break;
                case 6:
                  text = "TCP Trace";
                  break;
                case 7:
                  text = "TCP Trace";
                  break;
                case 8:
                  text =
                    routePath.indexOf("-") >= 0
                      ? routePath.split("-")[1] == 1
                        ? "UDP Trace"
                        : routePath.split("-")[1] == 2
                        ? "ICMP Trace"
                        : routePath.split("-")[1] == 3
                        ? "TCP Trace"
                        : "--"
                      : "--";
                  break;
                default:
                  text = "--";
                  break;
              }
              return h("div", text);
            },
          },
         
         
        ],
        routeParams: {},
        topologyList:[],
    
          //以下为趋势图有关参数
        echartLookParama2: {
          preNodeIp: "",
          nodeIp: "",
          linkId: "",
          startTime: "",
          endTime: "",
          queryType: 1,
          special: false,
          High: false,
          level: "",
          taskFlag: 1,
        },
         timeOptions: {
          shortcuts: [
            {
              text: this.$t("comm_half_hour"),
              value() {
                const start = new Date();
                const end = new Date();
                start.setTime(start.getTime() - 30 * 60 * 1000);
                return [
                  start.format("yyyy-MM-dd HH:mm:ss"),
                  end.format("yyyy-MM-dd HH:mm:ss"),
                ];
              },
              onClick: () => {},
            },
            {
              text: this.$t("comm_today"),
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime());
                return [
                  new Date().format("yyyy-MM-dd 00:00:00"),
                  new Date().format("yyyy-MM-dd 23:59:59"),
                ];
              },
            },
            {
              text: this.$t("comm_yesterday"),
              value() {
                const start = new Date();
                const end = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24);
                end.setTime(end.getTime() - 3600 * 1000 * 24);
                return [
                  start.format("yyyy-MM-dd 00:00:00"),
                  end.format("yyyy-MM-dd 23:59:59"),
                ];
              },
            },
            {
              text: this.$t("comm_last_7"),
              value() {
                const start = new Date();
                const end = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                return [
                  start.format("yyyy-MM-dd 00:00:00"),
                  end.format("yyyy-MM-dd 23:59:59"),
                ];
              },
            },
            {
              text: this.$t("comm_last_30"),
              value() {
                const start = new Date();
                const end = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                return [
                  start.format("yyyy-MM-dd 00:00:00"),
                  end.format("yyyy-MM-dd 23:59:59"),
                ];
              },
            },
            {
              text: this.$t("comm_curr_month"),
              value() {
                let nowDate = new Date();
                let date = {
                  year: nowDate.getFullYear(),
                  month: nowDate.getMonth(),
                  date: nowDate.getDate(),
                };
                let end = new Date(date.year, date.month + 1, 0);
                let start = new Date(date.year, date.month, 1);
                return [
                  start.format("yyyy-MM-dd 00:00:00"),
                  end.format("yyyy-MM-dd 23:59:59"),
                ];
              },
            },
            {
              text: this.$t("comm_preced_month"),
              value() {
                let date = new Date();
                let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
                );
                let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
                );
                return [
                  start.format("yyyy-MM-dd 00:00:00"),
                  end.format("yyyy-MM-dd 23:59:59"),
                ];
              },
            },
          ],
        },
            timeRange: [],
             flowData: {
                  devName: "",
                  interfaceIp: "",
                  interfaceName: "",
                  enter: [],
                  issue: [],
        },
           flowLevel: 0,
             echart2: {
                  show: false,
              },
           flowUnit: "B",
              delayLossUnit: {
                  时延: "ms",
                  丢包率: "%",
                  入流速: "B",
                  出流速: "B",
                  "可用率（免责）": "%",
                  "优良率（免责）": "%",
                  可用率: "%",
                  优良率: "%",
              },
              startScale: false,
              height:300,
              startValue: "",
              endValue: "",
              delayStart:0,
              delayEnd:100,
              delayLossScale:false,
          }
      },
      methods:{
          initEchart() {
  
       
              if (this.delayLossChart1) {
                 
                  this.delayLossChart1.dispose();
                  this.delayLossChart1 = null
              }
  
              
              this.delayLossChart1 = echarts.init(this.$refs["probetask-delayLoss"]);
  
              /*设置时延丢包率echart图*/
  
              const option = this.Option();
              setTimeout(() => {
                  this.delayLossChart1.setOption(option);
              }, 500);
  
          },
           Option() {
        console.log(this.delayLossData,this.echart2,'delayLossData............')
  
        let optionArr = [
          {
            title: {
              show:
                this.delayLossData.delay.length === 0 &&
                this.delayLossData.loss.length === 0,
              text: this.$t("common_No_data"),
              left: "center",
              top: "90px",
              textStyle: {
                color: "#465b7a",
                fontFamily: "serif",
                fontWeigth: "400",
                fontSize: 18,
              },
            },
            tooltip: this.setDelayLossTooltip(),
            axisPointer: {
              type: "shadow",
              link: {
                xAxisIndex: "all",
              },
            },
            grid: [
              {
                left: "5%",
                top: "40px",
                width: "90%",
                height: "140px",
              },
              {
                // show:this.echart2.show,
                left: "9%",
                top: "300px",
                width: "90%",
                height: this.echart2.show ? "140px" : "0px",
              },
            ],
            legend: [
              {
                show: true,
                top: "0%",
                right: "40%",
                gridIndex: 0,
                icon: "roundRect",
                itemWidth: 16,
                itemHeight: 12,
                textStyle: {
                  color: this.currentSkin == 1 ? "#FFFFFF" : "#515A6E",
                  fontSize: 12,
                  fontFamily: "MicrosoftYaHei-Bold",
                },
                color: this.delayLossColor,
                data: [this.$t("speed_delay"), this.$t("comm_packet_loss")],
              },
              {
                show: this.echart2.show,
                top: "250px",
                right: "40%",
                icon: "roundRect",
                gridIndex: 1,
                itemWidth: 16,
                itemHeight: 12,
                textStyle: {
                  color:  "#5CA0D5", //"#FFFFFF",
                  fontSize: 12,
                  fontFamily: "MicrosoftYaHei-Bold",
                },
                color: this.flowColor,
                data: [
                  this.$t("specquality_incoming_velocity"),
                  this.$t("specquality_exit_velocity"),
                ],
              },
            ],
            xAxis: [
              {
                type: "time",
                gridIndex: 0,
                axisLabel: {
                  textStyle: {
                    color:  "#5CA0D5", //"#FFFFFF",
                  },
                },
                splitLine: {
                  lineStyle: {
                    type: "dotted",
                    color:  "#5CA0D5", //"rgba(42, 56, 64, 1)",
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56" , // "#5CA0D5",
                    width: 1,
                  },
                },
              },
              {
                show: this.echart2.show,
                type: "time",
                gridIndex: 1,
                splitLine: {
                  lineStyle: {
                    type: "dotted",
                    color:  "#5CA0D5", //"rgba(42, 56, 64, 1)",
                  },
                },
                axisLabel: {
                  textStyle: {
                    color:"rgba(42, 56, 64, 1)", // "#5CA0D5",
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#5CA0D5", // "#5CA0D5",
                    width: 1,
                  },
                },
              },
            ],
            yAxis: [
              {
                name: this.$t("comm_delay(ms)"),
                type: "value",
                scale: true,
                gridIndex: 0,
                position: "left",
                min:this.handleYcoordinate(this.delayLossData.delay).minNum,
                max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
                axisTick: {
                  show: false,
                },
                axisLine: {
                  symbol: ["none", "arrow"],
                  symbolSize: [6, 10],
                  lineStyle: {
                    color:  "#5CA0D5", // "#5CA0D5",
                    width: "1", //坐标线的宽度
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    type: "dashed",
                    color:  "#5CA0D5", // "#2A3840",
                  },
                },
              },
              {
                name: this.$t("comm_loss_rate"),
                type: "value",
                scale: true,
                gridIndex: 0,
                min: 0,
                max: 100,
                position: "right",
                axisTick: {
                  show: false,
                },
                axisLine: {
                  symbol: ["none", "arrow"],
                  symbolSize: [6, 10],
                  lineStyle: {
                    color:  "#5CA0D5", // "#5CA0D5",
                    width: "1", //坐标线的宽度
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    type: "dashed",
                    color:  "#5CA0D5", // "#2A3840",
                  },
                },
              },
              {
                show: this.echart2.show,
                name: this.$t("server_flow_rate"),
                type: "value",
                scale: true,
                position: "left",
                gridIndex: 1,
                axisTick: {
                  show: false,
                },
                axisLine: {
                  symbol: ["none", "arrow"],
                  symbolSize: [6, 10],
                  lineStyle: {
                    color:  "#5CA0D5", // "#5CA0D5",
                    width: "1", //坐标线的宽度
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    type: "dashed",
                    color:  "#5CA0D5", // "#2A3840",
                  },
                },
                axisLabel: {
                  show: true,
                  formatter: (value) => {
                    return this.flowSize(value, true, true);
                  },
                },
              },
            ],
            dataZoom: [
              {
                type: "inside",
                xAxisIndex: [0, 1],
                startValue: this.startValue == this.endValue ? "" : this.startValue,
                endValue: this.startValue == this.endValue ? "" : this.endValue,
                // start:this.delayStart,
                // end:this.delayEnd
              },
              {
                type: "slider",
                left: "5%",
                right: "5%",
                height: 20,
                top: this.echart2.show ? "480px" : "250px",
                xAxisIndex: [0, 1],
                realtime: true,
                startValue: this.startValue == this.endValue ? "" : this.startValue,
                endValue: this.startValue == this.endValue ? "" : this.endValue,
                fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin]  ,//"rgba(2, 29, 54, 1)",
                borderColor: eConfig.dataZoom.borderColor[this.currentSkin]  ,//"rgba(22, 67, 107, 1)",
                handleStyle: {
                  color: eConfig.dataZoom.handleStyle.color[this.currentSkin]  ,//"rgba(2, 67, 107, 1)",
                },
                textStyle: {
                  color: eConfig.dataZoom.textStyle.color[this.currentSkin]  ,// top.window.isdarkSkin == 1 ? "#617ca5" : "#e3e7f2",
                },
              },
            ],
            series: [
              {
                type: "line",
                name: this.$t("speed_delay"),
                xAxisIndex: 0,
                yAxisIndex: 0,
                smooth: true,
                symbol: "circle",
                symbolSize: 1,
                // symbol: this.delayLossData.delay.length>1?"none":"none",
                // seriesLayoutBy:'row',
                color: this.delayLossColor[0],
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 1,
                    },
                  },
                },
                areaStyle: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: eConfig.areaStyle.delayColor_0[this.currentSkin] ,//"rgba(0, 255, 238, 0.60)",
                      },
                      {
                        offset: 0.8,
                        color: eConfig.areaStyle.delayColor_8[this.currentSkin] ,//"rgba(0, 255, 238, 0.2)",
                      },
                    ],
                  },
                },
                data: this.delayLossData.delay,
                
              },
              {
                type: "line",
                name: this.$t("comm_packet_loss"),
                xAxisIndex: 0,
                yAxisIndex: 1,
                // seriesLayoutBy:'row',
                smooth: true,
                symbol: "circle",
                symbolSize: 1,
                // symbol: this.delayLossData.loss.length>1?"none":"none",
                color: this.delayLossColor[1],
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 1,
                    },
                  },
                },
                areaStyle: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: eConfig.areaStyle.lossColor_0[this.currentSkin] ,//"rgba(2, 144, 253, 0.60)",
                      },
                      {
                        offset: 0.8,
                        color: eConfig.areaStyle.lossColor_8[this.currentSkin] ,//"rgba(2, 144, 253, 0.2)",
                      },
                    ],
                  },
                },
                data: this.delayLossData.loss,
              },
              {
                show: this.echart2.show,
                name: this.$t("specquality_incoming_velocity"),
                type: "line",
                smooth: true,
                symbol: "circle",
                symbolSize: 1,
                // symbol: this.flowData.enter.length>1?"none":"none",
                xAxisIndex: 1,
                yAxisIndex: 2, // 指定y轴
                color: this.flowColor[0],
                data: this.flowData.enter,
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 1,
                    },
                  },
                },
                areaStyle: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: eConfig.areaStyle.flowInColor_0[this.currentSkin] ,//"rgba(71,142,233, 0.8)",
                      },
                      {
                        offset: 0.8,
                        color: eConfig.areaStyle.flowInColor_8[this.currentSkin] ,//"rgba(71,142,233, 0.2)",
                      },
                    ],
                  },
                },
              },
              {
                show: this.echart2.show,
                name: this.$t("specquality_exit_velocity"),
                type: "line",
                smooth: true,
                symbol: "circle",
                symbolSize: 1,
                //symbol: this.flowData.issue.length>1?"none":"none",
                xAxisIndex: 1,
                yAxisIndex: 2,
                color: this.flowColor[1],
                data: this.flowData.issue,
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 1,
                    },
                  },
                },
                areaStyle: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.8)",
                      },
                      {
                        offset: 0.8,
                        color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] ,//"rgba(241,145,73, 0.2)",
                      },
                    ],
                  },
                },
              },
            ],
          },
        ];
        return optionArr[0];
      },
       setDelayLossTooltip() {
        let _self = this;
        return eConfig.tip("axis", function (param) {
          _self.scale = param[0].data[0];
          var obj = {};
          param = param.reduce(function (item, next) {
            obj[next.seriesIndex] ? "" : (obj[next.seriesIndex] = true && item.push(next));
            return item;
          }, []);
          let delayTime = "",
            delayTip = "",
            flowTime = "",
            flowTip = "";
          for (let i = 0, len = param.length; i < len; i++) {
            if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
              delayTime = param[i].data[0] + "<br />";
              delayTip +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
              delayTip +=
                param[i].seriesName +
                "：" +
                (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                  ? "--"
                  : param[i].value[1]) +
                _self.delayLossUnit[param[i].seriesName] +
                "<br />";
            }
            if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
              flowTime = param[i].data[0] + "<br />";
              flowTip +=
                '<span class="tooltip-round" style="background-color:' +
                param[i].color +
                '"></span>';
              flowTip +=
                param[i].seriesName +
                "：" +
                (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                  ? "--"
                  : _self.flowSize(param[i].value[1], true, true)) +
                // param[i].value[1]
                // _self.delayLossUnit[param[i].seriesName] +
                "<br />";
            }
          }
          return delayTime + delayTip + flowTime + flowTip;
        });
      },  indexQueryClick() {
        this.startDay = new Date(this.timeRange[0]).format("yyyy-MM-dd HH:mm:ss");
        this.endDay = new Date(this.timeRange[1]).format("yyyy-MM-dd HH:mm:ss");
        let startVal = moment(this.startDay, "YYYY-MM-DD hh:mm:ss").valueOf();
        let endVal = moment(this.endDay, "YYYY-MM-DD hh:mm:ss").valueOf();
        if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
          this.$Message.warning({
            content: this.$t("warning_time_not_exceed_62"),
            background: true,
          });
          return;
        }
  
        
        if (!this.startDay || !this.endDay) {
          this.$Message.warning({ content: "请选择起始日期", background: true });
          return;
        } else if (this.startDay > this.endDay) {
          this.$Message.warning({ content: "开始日期不能大于结束日期", background: true });
          return;
        }
        this.echartLookParama2.startTime = this.startDay
        this.echartLookParama2.endTime = this.endDay
        this.getTrend()
       
      },
           dateChange(val, type) {
        if (type == "date") {
          this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")];
        }
      },
           // 获取最近1小时时间
          getLastOneHour() {
              const now = new Date();
              const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
              
              const formatDate = (date) => {
                  const year = date.getFullYear();
                  const month = String(date.getMonth() + 1).padStart(2, '0');
                  const day = String(date.getDate()).padStart(2, '0');
                  const hours = String(date.getHours()).padStart(2, '0');
                  const minutes = String(date.getMinutes()).padStart(2, '0');
                  const seconds = String(date.getSeconds()).padStart(2, '0');
                  
                  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
              };
              this.timeRange = [formatDate(oneHourAgo),formatDate(now)]
            
              this.startDay = this.timeRange[0]
              this.endDay =  this.timeRange[1]
              
              return {
                  endTime: formatDate(now),
                  startTime: formatDate(oneHourAgo)
              };
          },
       
              /*趋势图有关*/
      async getTrend() {
       
        this.delayLossLoading = true
        try{
            const res = await this.$http.PostJson("/trend/getDelayAndLostTrend", this.echartLookParama2);
  
        //时延丢包趋势数据
        if (res.code === 1) {
          if (res.data) {
           
           
            this.delayLossLevel = res.data.level;
            const delayData = res.data.lineOne,
              lossData = res.data.lineTwo;
            let hasData = true;
            if (!delayData && !lossData) {
              hasData = false;
              this.echart1.show = false;
            }
            if (hasData) {
              this.echart1.show = true;
              this.delayStart = 0;
              if (delayData) {
                // if (delayData.length <= 300) {
                this.delayEnd = 100;
                this.startValue = delayData[0][0];
                this.endValue = delayData[delayData.length - 1][0];
              
                this.delayLossScale = true;
                this.delayLossData.delay = delayData;
              } else if (lossData) {
                // if (lossData.length <= 300) {
                this.delayEnd = 100;
                this.startValue = lossData[0][0];
                this.endValue = lossData[lossData.length - 1][0];
               
                this.delayLossScale = true;
                this.delayLossData.loss = lossData;
              }
            }
  
            if (lossData && lossData.length > 0) {
              this.delayLossData.loss = lossData;
            }
           
          }
            }
          
          
        }catch(error){
          console.log(error)
         
        
      
        }finally{
           this.getDataTrend();
         this.delayLossLoading = false
        
        }
        
      },
          topologyClick(row) {
        // debugger
       
       
         
          if (row.type == 1 && row.index != 0) {
            this.echartLookParama2.queryType = 1;
            this.echartLookParama2.preNodeIp = row.data[0].ip1;
            this.echartLookParama2.nodeIp = row.data[1].ip1;
            if (this.echartLookParama2.nodeIp != "*") {
              this.topoShow = false;
              this.getTrend();
            } else {
              this.topoShow = true;
            }
          } else if (row.type == 2) {
            this.topoShow = false;
            this.echartLookParama2.queryType = 2;
            this.echartLookParama2.preNodeIp = row.data[0].ip1;
            this.echartLookParama2.nodeIp = row.data[1].ip1;
  
            if (row.suspect) {
              this.suspect = true;
              // return
            } else {
              this.suspect = false;
            }
            this.getTrend();
          
        }
      },
          onVisibleChange(visible){
            console.log(visible);
              if(visible){
              //    this.getLastOneHour()
                 
                  this.tableParams.groupId = this.queryData.groupId
                  this.tableParams.alarmType = this.queryData.alarmType
                  this.getLastOneHour()
                  this.getTableList()
          }else {
            this.$emit('send-message', '1');
            this.modalShow = false
            //   this.$emit('update:modalShow',false)
              // 清空数据
              this.delayLossData.delay = []
              this.delayLossData.loss = []
             this.topologyList = []
             this.tableList = []
          //    销毁echarts
          this.delayLossChart1.dispose()
          this.delayLossChart1.null
          }
         
      },
      //u型路径拓扑接口
    async getPathTopo(){
      this.uTopoLoading = true
      try {
            const res =  await this.$http.wisdomPost('/trendData/selectGongHangIndexPing',this.routeParams)
       let data = res.data || {},
              list = data.listl || [];
            if (list.length > 0) {
              list.unshift({
                ip1: data.sourceIp,
                isBuquan: 0,
                isBroken: 0,
              });
            }
            /*2021年4月25为改中断和任务策略任务显示一致，但数据不一致的bug而增加的代码*/
            let brokenNum = 0;
            if (list.length > 0) {
              let isBrokenF = false,
                isIpduan = false,
                isDelay = false,
                borkenIndex = 0,
                delayIndex = 0;
              list.forEach((item, index) => {
                if (item.isBroken === 1 || item.isBroken === 3 || item.isBroken === 7) {
                  isBrokenF = true;
                  borkenIndex = index;
                }
                if (item.isBroken === 4) {
                  isIpduan = true;
                }
                if (item.isBroken === 2) {
                  isDelay = true;
                  delayIndex = index;
                }
              });
              for (let i = 0, len = list.length; i < len; i++) {
                /*重新组装数据start*/
                /*nodeColor:r,y,b;lineColor:r,y,b,linkPoint:true*/
                if (list[i].isBroken != 0) {
                  brokenNum = list[i].isBroken;
                }
                list[i].nodeColor =
                  list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
                list[i].lineColor =
                  list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
                list[i].linkPoint = list[i].isBroken == 1 ? true : false;
                list[i].deviceTypeCss = "";
                /*重新组装数据end*/
              }
  
              //处理中断ip段 前的ip为*的处理
              let thisIndex = true;
              if (isBrokenF) {
                for (let index = borkenIndex; index > 0; index--) {
                  if (thisIndex && list[index - 1].ip1 == "*") {
                    list[index - 1].nodeColor = "r";
                    list[index - 1].lineColor = "r";
                    thisIndex = true;
                  } else if (thisIndex && list[index - 1].suspect === true) {
                    list[index - 1].nodeColor = "r";
                    list[index - 1].lineColor = "r";
                    thisIndex = true;
                  } else {
                    thisIndex = false;
                  }
                }
              }
              //处理时延ip段 前的ip为*的处理
              let delayFlag = true;
              if (isDelay) {
                for (let index = delayIndex; index > 0; index--) {
                  if (delayFlag && list[index - 1].ip1 == "*") {
                    list[index - 1].nodeColor = "y";
                    list[index - 1].lineColor = "y";
                    delayFlag = true;
                  } else if (delayFlag && list[index - 1].suspect === true) {
                    list[index - 1].nodeColor = "y";
                    list[index - 1].lineColor = "y";
                    delayFlag = true;
                  } else {
                    delayFlag = false;
                  }
                }
              }
              let indexBroken = list.map((item) => item.isBroken != 0).indexOf(true),
                indexX = 0;
              for (let i = indexBroken - 1; i >= 0; i--) {
                if (i === indexBroken - 1 && list[i].ip1 === "*" && i - 1 >= 0) {
                  indexX = i - 1;
                  list[i].lineColor = brokenNum === 2 ? "y" : "r";
                } else if (indexX === i && list[i].ip1 === "*" && i - 1 >= 0) {
                  indexX = i - 1;
                  list[i].lineColor = brokenNum === 2 ? "y" : "r";
                }
              }
  
              if (list.length === 2 && (list[0].isBroken === 1 || list[1].isBroken === 1)) {
                list[1].lineColor = "r";
                list[1].nodeColor = "r";
                list[1].linkPoint = true;
              }
              // for (let i = borkenIndex, len = list.length; i < len; i++) {
              //   if (isBrokenF && borkenIndex<i) {
              //     list[i].nodeColor = 'g';
              //     list[i].lineColor = 'g';
              //   }
              // }
              list.forEach((item, index) => {
                if (index > borkenIndex && borkenIndex != 0) {
                  item.nodeColor = "g";
                  item.lineColor = "g";
                }
              });
              this.topologyList = list;
              // getLineClickIndex
              // 解决链路默认高亮代码
              // 拓扑图线条变粗的需求
              // console.log('list===>',list);
              list.forEach((itemA, index) => {
                // console.log("itemA.ip1",itemA.ip1);
                let isReturn = false;
                // 虚拟节点需要拿到拼接的前节点
                if (this.usea.preNodeIp.includes('-9999')) {
                  this.usea.preNodeIp = this.usea.preNodeIp.split("_")[1]
                }
                console.log( this.$refs.topologyChart,'自组建封装方法')
                // debugger
                console.log(this.usea , "----------------------");
                if (itemA.ip1 == this.usea.preNodeIp) {
                  this.$refs.topologyChart.getLineClickIndex(index);
                  for(var i=++index;i< list.length ; i++){
                    console.log("itemB.ip1===>",i+'-'+list[i]);
                    var nodeTemp = this.usea.nodeIp;
                    if(this.usea.nodeIpTrend){
                        nodeTemp = this.usea.nodeIpTrend;
                    }
                    if (list[i].ip1 == nodeTemp) {
                         isReturn = true;
                        break;
                    }
                    console.log("高亮===>",i);
                    this.$refs.topologyChart.getLineClickIndex(i);
                  }
  
                }
                if (isReturn) {
                  return;
                }
              });
              // debugger
            } else {
              this.topologyList = [];
            }
            if (data.broken_num == null) {
              this.event_number = 0;
            } else {
              this.event_number = data.broken_num;
            }
            this.delay_line = data.delay || [];
            this.timeLine.data = data.delay || [];
            this.loss_line = data.las_vlue || [];
          
      }catch(error){
          console.log(error)
      }finally{
          this.uTopoLoading = false
      }
     
      },
      tabParamPageSizeChange(size){
          this.tableParams.pageNo = 1 
          this.tableParams.pageSize = size
  
          this.getTableList()
      },
      pageChangeModal(page){
          this.tableParams.pageNo = page
          this.getTableList()
      },
       async getDataTrend() {
          this.delayLossLoading = true
          let params = JSON.parse(JSON.stringify(this.echartLookParama2))
          params.snmp = false
        const res = await this.$http.PostJson("/trend/getDataTrend", params);
  
        //流速趋势数据
        if (res.code === 1) {
  
          this.flowData.devName = res.data.devName ?? "";
          this.flowData.interfaceIp = res.data.interfaceIp ?? "";
          this.flowData.interfaceName = res.data.interfaceName ?? "";
          let flowUnit = 1;
          this.flowLevel = res.data.level ?? "";
          if (!res.data.lineOne || res.data.lineOne.length < 1) {
            this.echart2.show = false;
            this.flowLevel = 99;
          }
          if (res.data.lineTwo && res.data.lineTwo.length > 0) {
            this.echart2.show = true;
            let unitChange = this.getFlowUnit(res.data.lineTwo);
            this.flowUnit = unitChange[0];
            this.delayLossUnit[this.$t("specquality_incoming_velocity")] = unitChange[0];
            this.delayLossUnit[this.$t("specquality_exit_velocity")] = unitChange[0];
            flowUnit = unitChange[1];
            this.flowData.enter = res.data.lineTwo.map((item) => {
              return [item[0], item[1]];
            });
          }
          if (res.data.lineOne && res.data.lineOne.length > 0) {
            this.flowData.issue = res.data.lineOne.map((item) => {
              return [item[0], item[1]];
            });
          }
  
          // this.$store.commit("updateFlowHistory", {
          //   level: res.data.level,
          //   datas: [this.flowData.enter, this.flowData.issue],
          // }); //保存数据
          // this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
          // this.$store.commit("setflowUnit", {
          //   level: res.data.level,
          //   unit: this.flowUnit,
          // }); //保存单位
        }
        this.delayLossLoading = false
  
        this.startScale = false;
        this.loading1 = false;
        if (this.echart2.show) {
          this.height = 500;
        } else if (!this.echart2.show) {
          this.height = 300;
        }
         top.document.getElementById("probetask-delayLoss").style.height =
          this.height + "px";
          this.$nextTick(() => {
            this.initEchart();
            
          })
  
        
      },
     async getTableList(){
      try {
          this.ModalTableLoading = true
           let res = await this.$http.PostJson('/home/<USER>',this.tableParams)
      if(res.code == 1){
          this.tableList = res.data.records
          this.taskId = res.data.records[0].taskId
          this.total = res.data.total
          this.routeParams = res.data.records[0]
          this.routeParams.sysLinkId = this.routeParams.routeId
          this.getPathTopo()
          this.echartLookParama2.linkId =  this.routeParams.sysLinkId 
          this.echartLookParama2.nodeIp = this.routeParams.showDestIp
          this.echartLookParama2.preNodeIp = this.routeParams.showSourceIp
          this.routeCode = this.routeParams.taskNum
          this.echartLookParama2.startTime = this.startDay;
          this.echartLookParama2.endTime = this.endDay;
          this.getTrend()
          
      }
      } catch (error) {
         console.log(error)
      } finally {
          this.ModalTableLoading = false
      }
     
     }
     },
      mounted() {
         this.isbigscreen = window.name.indexOf('bigscreen') > -1
          this.modalWidth = localStorage.getItem('modalWidth') * 0.98
           this.delayLossColor = [eConfig.legend.delayColor[this.currentSkin] ,eConfig.legend.lossColor[this.currentSkin] ];
      this.flowColor = [eConfig.legend.flowInColor[this.currentSkin] ,eConfig.legend.flowOutColor[this.currentSkin] ];
      },
      beforeDestroy(){
          if(this.delayLossChart1){
          this.delayLossChart1.dispose()
          this.delayLossChart1.null
          }
      }
  
  }
  </script>
  
  <style>
</style>