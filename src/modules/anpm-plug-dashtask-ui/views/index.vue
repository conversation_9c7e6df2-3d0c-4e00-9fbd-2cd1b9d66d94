<template>
    <div class="itemEcharts" ref="itemEcharts" :style="'display: flex;justify-content: center;align-items: center;position: relative;height: 100%;background:' +
        (isbigscreen ? '#060D15' : isdarkSkin == 1 ? 'transparent' : '#ffffff')
        ">
        <Loading :loading="loading"></Loading>
        <!-- 饼状图 -->
        <div ref="pie" :class="['pieBox', isOnePie ? 'onePieBox' : '']"
            v-if="pie_List.length > 0 && param.taskType == 0" :style="pieBoxStyle" style="position: relative"
            @click="clickup($event)">
            <div v-if="isOnePie" class="onePie" style="width: 100%">
                <div v-for="(item, index) in pie_List" :key="index" :style="pieStyle" :class="randomN">
                    <div class="monitor pieDivStyle" v-show="pie_List.length > 1" :data-link="item.detailLink"
                        style="z-index: 9999; position: relative" ref="radarRefs6"
                        @click="handleDetailClick(item.detailLink)">
                        <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                            <span class="pie-name">{{ item.name }}</span>
                            <span class="pie-link">{{ item.detailLink }}</span>
                        </div>
                        <img v-show="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png" alt=""
                            class="xqimg" />
                    </div>
                    <draw-pie :ref="pieListRef + index" :node="pieListRef + index" :datas="item"
                        :key="pieListRef + index" :isbigscreen="isbigscreen" :isdarkSkin="isdarkSkin"
                        :dashboardPie="dashboardPie" :typeCode="typeCode" :time="{
                            startTime: param.startTime + ' 00:00:00',
                            endTime: param.endTime + ' 23:59:59',
                        }" :styleProp="styleProp">
                    </draw-pie>
                </div>
            </div>
            <div v-else>
                <vue-seamless-scroll ref="scrollRefs1" :data="pie_List" :copy="false" :class-option="pieOptions">
                    <div v-for="(item, index) in pie_List" :key="index" :style="pieStyle" :class="randomN"
                        class="onePie">
                        <div class="monitor pieDivStyle" v-show="pie_List.length > 1" :data-link="item.detailLink"
                            style="z-index: 9999; position: relative" ref="radarRefs6"
                            @click="handleDetailClick(item.detailLink)">
                            <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                                <span class="pie-name">{{ item.name }}</span>
                                <span class="pie-link">{{ item.detailLink }}</span>
                            </div>
                            <img v-show="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png"
                                alt="" class="xqimg" />
                        </div>
                        <draw-pie :ref="pieListRef + index" :node="pieListRef + index" :datas="item"
                            :key="pieListRef + index" :isbigscreen="isbigscreen" :isdarkSkin="isdarkSkin"
                            :dashboardPie="dashboardPie" :typeCode="typeCode" :time="{
                                startTime: param.startTime + ' 00:00:00',
                                endTime: param.endTime + ' 23:59:59',
                            }" :styleProp="styleProp">
                        </draw-pie>
                    </div>
                </vue-seamless-scroll>
            </div>
        </div>
        <!-- 监测概览 -->
        <div v-if="param.taskType == 1 && overviewShow == 0 && overviewData.length > 0" class="overview-container"
            :style="overviewContainerStyle" @click="clickup($event)">
            <vue-seamless-scroll :data="overviewData" :class-option="overviewOptions">
                <div class="overview deStyle scroll-div" ref="radarRefs0" v-for="(overviewItem, index) in overviewData"
                    :key="index">
                    <div class="monitor" :data-link="overviewItem.detailLink">
                        <div class="div1" :class="{ 'white-text': isbigscreen }">
                            <span>{{ overviewItem.name }}</span>
                            <span class="deSpan">{{ overviewItem.total }}</span>
                        </div>
                        <img v-if="overviewItem.detailLink" :data-link="overviewItem.detailLink"
                            src="@/assets/dashtask/xq.png" alt="" class="xqimg" />
                    </div>
                    <div class="data-container">
                        <div class="data-box">
                            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video1 mainl">
                                <source src="@/assets/dashtask/y1.mp4" type="video/mp4" />
                            </video>
                            <div class="data-text">
                                <p class="pStyle">{{ overviewItem.monitorDestNum }}</p>
                                <p>监测目标</p>
                            </div>
                        </div>
                        <div class="data-box">
                            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video1">
                                <source src="@/assets/dashtask/y2.mp4" type="video/mp4" />
                            </video>
                            <div class="data-text">
                                <p class="pStyle">{{ overviewItem.networkLinkNum }}</p>
                                <p>网络链路</p>
                            </div>
                        </div>
                    </div>
                    <div class="status-container deStyle">
                        <div class="status-box normal">
                            <p :data-id="overviewItem.groupId" :data-type="0" class="pStyle1">
                                {{ overviewItem.normal }}
                            </p>
                            <p>正常</p>
                        </div>
                        <div class="status-box terminal">
                            <p :data-id="overviewItem.groupId" :data-type="1" class="pStyle1">
                                {{ overviewItem.terminal }}
                            </p>
                            <p>中断</p>
                        </div>
                        <div class="status-box deteriorate">
                            <p :data-id="overviewItem.groupId" :data-type="2" class="pStyle1">
                                {{ overviewItem.deteriorate }}
                            </p>
                            <p>劣化</p>
                        </div>
                        <div class="status-box pause">
                            <p :data-id="overviewItem.groupId" :data-type="3" class="pStyle1">
                                {{ overviewItem.pause }}
                            </p>
                            <p>暂停</p>
                        </div>
                    </div>
                </div>
            </vue-seamless-scroll>
        </div>
        <div v-if="param.taskType == 1 && overviewShow == 1 && overviewData.length > 0" class="overview-container"
            :style="overviewContainerStyle" style="position: relative">
            <div style="
          position: absolute;
          top: 50%;
          transform: translateY(-55%);
          width: 100%;
          text-align: left;
        ">
                <div class="overview deStyle" ref="radarRefs1" v-for="(item, index) in overviewData" :key="index">
                    <div class="monitor" @click="handleDetailClick(item.detailLink)">
                        <div class="div1" :class="{ 'white-text': isbigscreen }">
                            <span>{{ item.name }}</span>
                            <span class="deSpan">{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" src="@/assets/dashtask/xq.png" alt="" class="xqimg"
                            @click="handleDetailClick(item.detailLink)" />
                    </div>
                    <div class="data-container">
                        <div class="data-box">
                            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video1">
                                <source src="@/assets/dashtask/y1.mp4" type="video/mp4" />
                            </video>
                            <div class="data-text">
                                <p class="pStyle">{{ item.monitorDestNum }}</p>
                                <p>监测目标</p>
                            </div>
                        </div>
                        <div class="data-box">
                            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video1">
                                <source src="@/assets/dashtask/y2.mp4" type="video/mp4" />
                            </video>
                            <div class="data-text">
                                <p class="pStyle">{{ item.networkLinkNum }}</p>
                                <p>网络链路</p>
                            </div>
                        </div>
                    </div>
                    <div class="status-container">
                        <div class="status-box normal">
                            <p @click="handleClick(item.groupId, 0)" class="pStyle1">
                                {{ item.normal }}
                            </p>
                            <p>正常</p>
                        </div>
                        <div class="status-box terminal">
                            <p @click="handleClick(item.groupId, 1)" class="pStyle1">
                                {{ item.terminal }}
                            </p>
                            <p>中断</p>
                        </div>
                        <div class="status-box deteriorate">
                            <p @click="handleClick(item.groupId, 2)" class="pStyle1">
                                {{ item.deteriorate }}
                            </p>
                            <p>劣化</p>
                        </div>
                        <div class="status-box pause">
                            <p @click="handleClick(item.groupId, 3)" class="pStyle1">
                                {{ item.pause }}
                            </p>
                            <p>暂停</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 雷达-->
        <div class="target" v-if="param.taskType == 2 && scrollShow == 0 && scrollData.length > 0"
            :style="scanVideoStyle">
            <video ref="scanVideo2" :autoplay="!isPaused" loop muted class="scan-video">
                <source src="@/assets/dashtask/img-scan2.mp4" type="video/mp4" />
            </video>
            <div class="target-div targetStyle" @click="clickup($event)">
                <vue-seamless-scroll :data="scrollData" :class-option="scrollOptions">
                    <div v-for="(item, index) in scrollData" :key="index" ref="radarRefs2" class="scroll-div">
                        <div class="monitor" :data-link="item.detailLink">
                            <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                                <span class="divBgSpan1">{{ item.name }}</span>
                                <span class="divBgSpan2 divBgSpan2Y">{{ item.total }}</span>
                            </div>
                            <img v-if="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png"
                                alt="" class="xqimg" />
                            <!-- @click="handleDetailClick(item.detailLink)" -->
                        </div>
                        <div class="target-div1">
                            <div class="div-text">中断/劣化/正常/暂停</div>
                            <div class="div-color cursorP white-text">
                                <span :data-id="item.groupId" :data-type="1">{{
                                    item.terminal
                                    }}</span>
                                /
                                <span :data-id="item.groupId" :data-type="2">{{
                                    item.deteriorate
                                    }}</span>
                                /
                                <span :data-id="item.groupId" :data-type="0">{{
                                    item.normal
                                    }}</span>
                                /
                                <span :data-id="item.groupId" :data-type="3">{{
                                    item.pause
                                    }}</span>
                            </div>
                        </div>
                    </div>
                </vue-seamless-scroll>
            </div>
        </div>
        <div class="target" v-if="param.taskType == 2 && scrollShow == 1 && scrollData.length > 0"
            :style="scanVideoStyle">
            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video1">
                <source src="@/assets/dashtask/img-scan2.mp4" type="video/mp4" />
            </video>
            <div class="target-div targetStyle scroll1" style="margin: auto">
                <div v-for="(item, index) in scrollData" :key="index" ref="radarRefs3">
                    <div class="monitor" @click="handleDetailClick(item.detailLink)">
                        <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                            <span class="divBgSpan1">{{ item.name }}</span>
                            <span class="divBgSpan2 divBgSpan2Y">{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" src="@/assets/dashtask/xq.png" alt="" class="xqimg"
                            @click="handleDetailClick(item.detailLink)" />
                    </div>
                    <div class="target-div1">
                        <div class="div-text">中断/劣化/正常/暂停</div>
                        <div class="div-color cursorP white-text">
                            <span @click="handleClick(item.groupId, 1)">{{
                                item.terminal
                                }}</span>
                            /<span @click="handleClick(item.groupId, 2)">{{
                                item.deteriorate
                                }}</span>
                            /<span @click="handleClick(item.groupId, 0)">{{
                                item.normal
                                }}</span>
                            /<span @click="handleClick(item.groupId, 3)">{{
                                item.pause
                                }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 心跳 -->
        <div v-if="param.taskType == 3 && ensureShhow == 0 && ensureData.length > 0" :style="ensureStyle"
            @click="clickup($event)">
            <vue-seamless-scroll ref="scrollRefs4" :data="ensureData" :class-option="ensureOptions">
                <div v-for="(item, index) in ensureData" :key="index" class="ensure targetStyle scroll-div"
                    ref="heartRefs4">
                    <div class="monitor" :data-link="item.detailLink">
                        <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                            <span class="divBgSpan1">{{ item.name }}</span>
                            <span class="divBgSpan2 divBgSpan2Y">{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png" alt=""
                            class="xqimg" />
                    </div>
                    <div class="ensure-div">
                        <div style="width: 50%">
                            <div class="ensure-txt">中断/劣化/正常/暂停</div>
                            <div class="div-color cursorP white-text" style="margin-left: 12px">
                                <span :data-id="item.groupId" :data-type="1">{{
                                    item.terminal
                                    }}</span>/<span :data-id="item.groupId" :data-type="2">{{
                                        item.deteriorate
                                    }}</span>/<span :data-id="item.groupId" :data-type="0">{{
                                        item.normal
                                    }}</span>/<span :data-id="item.groupId" :data-type="3">{{
                                        item.pause
                                    }}</span>
                            </div>
                        </div>
                        <div style="width: 50%">
                            <img v-if="item.type == '0'" class="ensure-img" src="@/assets/dashtask/lineH.gif"
                                alt="line" />
                            <img v-if="item.type == '1'" class="ensure-img" src="@/assets/dashtask/lineR.gif"
                                alt="line" />
                            <img v-if="item.type == '2'" class="ensure-img" src="@/assets/dashtask/lineY.gif"
                                alt="line" />
                            <img v-if="item.type == '3'" class="ensure-img" src="@/assets/dashtask/lineB.gif"
                                alt="line" />
                        </div>
                    </div>
                </div>
            </vue-seamless-scroll>
        </div>
        <div v-if="param.taskType == 3 && ensureShhow == 1 && ensureData.length > 0" :style="ensureStyle"
            style="position: relative">
            <div style="
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 100%;
          text-align: left;
        ">
                <div v-for="(item, index) in ensureData" :key="index" class="ensure targetStyle" ref="heartRefs5">
                    <div class="monitor" @click="handleDetailClick(item.detailLink)">
                        <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                            <span class="divBgSpan1">{{ item.name }}</span>
                            <span class="divBgSpan2 divBgSpan2Y">{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" src="@/assets/dashtask/xq.png" alt="" class="xqimg"
                            @click="handleDetailClick(item.detailLink)" />
                    </div>
                    <div class="ensure-div">
                        <div style="width: 50%">
                            <div class="ensure-txt">中断/劣化/正常/暂停</div>
                            <div class="div-color cursorP white-text" style="margin-left: 12px; cursor: pointer">
                                <span @click="handleClick(item.groupId, 1)">{{
                                    item.terminal
                                    }}</span>/<span @click="handleClick(item.groupId, 2)">{{
                                        item.deteriorate
                                    }}</span>/<span @click="handleClick(item.groupId, 0)">{{
                                        item.normal
                                    }}</span>/<span @click="handleClick(item.groupId, 3)">{{
                                        item.pause
                                    }}</span>
                            </div>
                        </div>
                        <div style="width: 50%">
                            <img v-if="item.type == '0'" class="ensure-img" src="@/assets/dashtask/lineH.gif"
                                alt="line" />
                            <img v-if="item.type == '1'" class="ensure-img" src="@/assets/dashtask/lineR.gif"
                                alt="line" />
                            <img v-if="item.type == '2'" class="ensure-img" src="@/assets/dashtask/lineY.gif"
                                alt="line" />
                            <img v-if="item.type == '3'" class="ensure-img" src="@/assets/dashtask/lineB.gif"
                                alt="line" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 暂无数据 -->
        <div v-if="bgLoading" :class="{ fillempty: isdarkSkin == 1, fillempty2: isdarkSkin == 0 }">
            <p class="emptyText">{{ $t("common_No_data") }}</p>
        </div>
        <detailModal :modalTitle="modalTitle" :queryData="queryData" :modalShow="modalShow"
            @send-message="handleMessage"></detailModal>
    </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import drawPie from "@/common/echarts/dashboardPie22.vue";
import detailModal from "../components/DetailModal.vue";
import '@/timechange';

function clientProperty() {
    if (document.body.offsetHeight != 0 && document.body.offsetWidth != 0) {
        return { width: document.body.offsetWidth, height: document.body.offsetHeight }
    }
}
export default {
    name: "dashTask",
    components: {
        drawPie, vueSeamlessScroll, detailModal
    },
    data() {
        return {
            pieBoxStyle: {
                width: '100%',
                height: '100%'
            },
            pieStyle: {
                height: '300px'
            },
            overviewContainerStyle: {
                width: '100%',
                height: '100%'
            },
            scanVideoStyle: {
                width: '100%',
                height: '100%'
            },
            ensureStyle: {
                width: '100%',
                height: '100%'
            },
            preTaskData: null,
            // 饼状图
            //刷新时间间隔
            intervalTime: null,
            //刷新参数
            interrefresh: null,
            nowDate: new Date(),
            //时间参数
            startTime: null,
            endTime: null,
            loading: false,
            bgLoading: false,
            pieLoading: false,
            height: 260,
            param: {
                // 0默认环状图，1概览图，2雷达图，3心跳图
                taskPresentationType: null,
                taskType: null,
                componentId: "",
                startTime: new Date().format2('yyyy-MM-dd'),
                endTime: new Date().format2('yyyy-MM-dd'),
            },
            pie_List: [],
            pieOptions: {
                step: 1,
                limitMoveNum: 0,
                hoverStop: true,
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                openWatch: true,
                singleHeight: 0,
                waitTime: 5000,
            },
            styleProp: clientProperty(),
            typeCode: "route_statistic", //此参数由调用的时候传进来，传进来的是构件的code例（route_statistic，special_statistic，snmp_statistic）
            isbigscreen: false,
            isdarkSkin: sessionStorage.getItem('dark') || 1,
            // 概况
            isPaused: false,  // 添加视频暂停状态控制
            overviewShow: 0,
            overviewData: [],
            isOnePie: false,
            overviewOptions: {
                step: 1,
                limitMoveNum: 2,
                hoverStop: true,
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                // singleHeight: 184,
                singleHeight: 0,
                waitTime: 5000,
            },
            //雷达
            seamless: true,
            scrollShow: 0,
            scrollData: [],
            scrollOptions: {
                step: 1, // 数值越大速度滚动越快
                limitMoveNum: 5, // 开始无缝滚动的数据量
                hoverStop: true, // 是否开启鼠标悬停stop
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                // singleHeight: 95, // 单步运动停止的高度(默认值0是无缝不停止的滚动)
                singleHeight: 0,
                waitTime: 5000, // 单步运动停止的时间(默认值1000ms)
            },
            targetDivStyle: {},
            divBgSpan2Style: {},
            pStyle: {},
            pStyle1: {},
            ensureShhow: 0,
            ensureData: [
                // {
                //     name: '网络保障1',
                //     total: 15,
                //     terminal: '1025',
                //     deteriorate: '1080',
                //     normal: '3506',
                //     pause: '1150',
                //     type: '1' //表示任务显示的图片颜色 1红色 2黄色 3蓝色
                // },
                // {
                //     name: '网络保障2',
                //     total: 16,
                //     terminal: '1025',
                //     deteriorate: '1080',
                //     normal: '3506',
                //     pause: '1150',
                //     type: '2'
                // },
                // {
                //     name: '网络保障3',
                //     total: 18,
                //     terminal: '1025',
                //     deteriorate: '1080',
                //     normal: '3506',
                //     pause: '1150',
                //     type: '3'
                // }
            ],
            // 心跳
            ensureOptions: {
                step: 1, // 数值越大速度滚动越快
                limitMoveNum: 3, // 开始无缝滚动的数据量
                hoverStop: true, // 是否开启鼠标悬停stop
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                // singleHeight: 101, // 单步运动停止的高度(默认值0是无缝不停止的滚动)
                singleHeight: 0,
                waitTime: 5000, // 单步运动停止的时间(默认值1000ms)
            },
            modalTitle: '链路详情',
            queryData: {
                alarmType: '',
                groupId: ''
            },
            modalShow: false,
            debounceTimer: null,
            itemEchartsHeight: 0,
            itemEchartsWidth: 0,
            timer: null,
            scrollTimer: null,
            isUserInteraction: false,
        };
    },
    watch: {
        pie_List: "watchDataChange",
        overviewData: "watchDataChange",
        scrollData: "watchDataChange",
        ensureData: "watchDataChange",
    },
    computed: {
        randomN() {
            return String('randomN' + parseInt(Math.random() * 10000));
        },
        dashboardPie() {
            return String('dashboardPie' + parseInt(Math.random() * 10000));
        },
        pieListRef() {
            return String('pieListRef' + parseInt(Math.random() * 10000));
        }
    },
    created() {
        // 编辑
        if (parent.window.isEdit) {
            let editName = window.frames.frameElement.getAttribute('editName')
            let taskType = editName.split('&&taskPresentationType=')[1].split('&&componentId=')[0];
            let dashboardComponentTaskGroups = editName.split('&&dashboardComponentTaskGroups=')[1].split('&&')[0];
            // console.log("taskType--------", taskType);
            // console.log('dashboardComponentTaskGroups------', dashboardComponentTaskGroups);
            // debugger
            // 编辑走这里
            this.param.taskType = taskType;
            if (dashboardComponentTaskGroups && dashboardComponentTaskGroups.length > 0) {
                this.param.dashboardComponentTaskGroups = JSON.parse(dashboardComponentTaskGroups);
                this.param.taskPresentationType = taskType;
            }
        } else {
            this.param.taskType = window.frames.name.split('&&taskPresentationType=')[1].split('&&componentId=')[0];
            this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
            this.param.componentId = window.frames.name.split('&&componentId=')[1].split('&&')[0];
            if (window.frames.frameElement) {
                window.frames.frameElement.contentWindow.close();
            }
        }
        // 0默认环状图，1概览图，2雷达图，3心跳图
        if (this.param.taskType == 0) {
            this.getPie(this.param);
        } else {
            this.getData(this.param);
        }
    },
    mounted() {
        window.addEventListener('message', e => {
            if (e.data && e.data.type === 'mouseEnterState') {
                // 使用防抖控制状态更新
                this.debounceMouseState(e.data.data);
            }
        });
        // 获取 itemEcharts 元素的高宽
        const itemEcharts = this.$refs.itemEcharts;
        const itemEchartsWidth = itemEcharts.offsetWidth;
        this.itemEchartsWidth = itemEchartsWidth
        const itemEchartsHeight = itemEcharts.offsetHeight;
        if (this.param.taskType == 3) {
            this.itemEchartsHeight = itemEchartsHeight + 25;
        } else {
            this.itemEchartsHeight = itemEchartsHeight + 50;
        }

        // this.pieOptions.singleHeight = itemEcharts.offsetHeight;
        // 设置饼状体轮播问题
        // this.pieStyle.height = itemEchartsHeight + 'px';
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        this.isbigscreen = window.isbigscreen;
        // debugger
        //console.log('this.intervalTime', this.intervalTime);
        if (this.intervalTime && Number(this.intervalTime)) {
            this.interrefresh = setInterval(() => {
                this.loading = true;
                if (this.param.taskType == 0) {
                    this.getPie(this.param)
                } else {
                    this.getData(this.param)
                }
                // 
            }, this.intervalTime * 1000);
        }
        // 添加窗口大小变化监听
        window.addEventListener('resize', this.handleResize)
    },
    methods: {
        wh(val, showFlag) {
            const itemEcharts = this.$refs.itemEcharts;
            //console.log("内容数量", val);
            // 原始内容的宽高
            let originalWidth = 350, originalHeight = null;
            if (this.param.taskType == 1) {
                originalHeight = 190 * val;
            } else if (this.param.taskType == 2) {
                originalHeight = 95 * val;
            } else if (this.param.taskType == 3) {
                originalHeight = 88 * val;
            } else if (this.param.taskType == 0) {
                originalHeight = 150 * val;
            }
            //console.log("originalHeight::::" + originalHeight + ",itemEchartsHeight:" + this.itemEchartsHeight)
            // 计算宽高比
            let widthRatio = this.itemEchartsWidth / originalWidth;
            let heightRatio = this.itemEchartsHeight / originalHeight;
            let ratio = widthRatio > heightRatio ? heightRatio : widthRatio;
            // let ratio = 1
            // 选择较小的比例进行等比缩放
            if (ratio > 2.5) {
                ratio = 2.5;
            } else if (ratio < 0.75) {
                ratio = 0.75;
            }
            // 计算缩放后的宽高
            let scaledWidth = originalWidth * ratio;
            let scaledHeight = originalHeight * ratio;
            console.log('scaledHeight', scaledHeight, scaledWidth)

            if (this.param.taskType == 0) {
                this.$nextTick(() => {
                    if (scaledHeight <= scaledWidth * 0.65) {
                        this.pieStyle.height = scaledHeight + 'px';
                        this.pieOptions.singleHeight = scaledHeight;

                    } else {
                        this.pieStyle.height = scaledWidth * 0.65 + 'px';
                        this.pieOptions.singleHeight = scaledWidth * 0.65;
                    }
                    document.querySelectorAll('.onePie').forEach(element => {
                        element.style.height = this.pieStyle.height;
                    })
                });
            }


            // if (this.param.taskType == 0) {
            //     let el = document.getElementsByClassName('onePie')[0]
            //     let hh = el.clientHeight;
            //     this.$nextTick(() => {
            //         this.pieOptions.singleHeight = hh;
            //     });
            // }
            if (showFlag != 'pieShow') {
                this[showFlag] = scaledHeight >= (this.itemEchartsHeight) ? 0 : 1;
                if (this[showFlag] == 0) {
                    this.$nextTick(() => {
                        itemEcharts.style.overflow = 'hidden';
                    });
                }
                console.log("this.showFlag", scaledHeight / val)
            }
            console.log('this[showFlag]', this[showFlag]);
            // debugger
            console.log(this.scrollOptions)
            //console.log("当前宽度：" + scaledWidth + ",当前高度:" + scaledHeight + ",当前缩放比例：" + ratio)
            // debugger
            // 设置样式
            if (this.itemEchartsWidth > 345) {
                document.querySelectorAll('.scroll-div').forEach(element => {
                    element.style.height = `${scaledHeight / val}px`;
                });
                if (this.param.taskType == 1) {
                    this.overviewContainerStyle = {
                        width: `${scaledWidth}px`,
                        height: `${scaledHeight}px`,
                        margin: 'auto'  // 居中显示
                    };
                    //console.log(scaledHeight / val);
                    this.overviewOptions.singleHeight = scaledHeight / val;
                    this.$nextTick(() => {
                        document.querySelectorAll('.deStyle').forEach(element => {
                            element.style.fontSize = `${14 * ratio}px`;
                        });
                        document.querySelectorAll('.deSpan').forEach(element => {
                            element.style.fontSize = `${16 * ratio}px`;
                        });
                        document.querySelectorAll('.pStyle').forEach(element => {
                            element.style.fontSize = `${18 * ratio}px`;
                        });
                        document.querySelectorAll('.pStyle1').forEach(element => {
                            element.style.fontSize = `${20 * ratio}px`;
                        });
                    });
                } else if (this.param.taskType == 2) {
                    // 特殊处理居中
                    if (val == 1) {
                        scaledHeight = scaledHeight * 2;
                    }
                    this.scanVideoStyle = {
                        width: `${scaledWidth}px`,
                        height: `${scaledHeight}px`,
                        margin: 'auto'  // 居中显示
                    };

                    // console.log(scaledHeight / val);
                    this.scrollOptions.singleHeight = scaledHeight / val;
                    this.$nextTick(() => {
                        document.querySelectorAll('.targetStyle').forEach(element => {
                            element.style.fontSize = `${12.5 * ratio}px`;
                        });
                        document.querySelectorAll('.divBgSpan2Y').forEach(element => {
                            element.style.fontSize = `${16 * ratio}px`;
                        });
                        this.$refs.scanVideo2.style.marginTop = (itemEcharts.offsetHeight - (this.$refs.scanVideo2.offsetHeight * 2) - 20) / 2 + 'px';
                        console.log(itemEcharts.offsetHeight);
                        console.log(this.$refs.scanVideo2.offsetHeight);
                        // debugger
                    });
                } else if (this.param.taskType == 3) {
                    this.ensureStyle = {
                        width: `${scaledWidth}px`,
                        height: `${scaledHeight}px`,
                        margin: 'auto'  // 居中显示
                    };
                    //console.log(scaledHeight / val);
                    this.ensureOptions.singleHeight = scaledHeight / val;
                    this.$nextTick(() => {
                        document.querySelectorAll('.targetStyle').forEach(element => {
                            element.style.fontSize = `${14 * ratio}px`;
                        });
                        document.querySelectorAll('.divBgSpan2Y').forEach(element => {
                            element.style.fontSize = `${16 * ratio}px`;
                        });
                    });
                }
                // debugger
                if (this.param.taskType == 0) {
                    let ratio1 = widthRatio;
                    if (ratio1 < 1.5) {
                        ratio1 = 1;
                    } else if (ratio1 > 2 && ratio1 <= 2.5) {
                        ratio1 = 1.4;
                    } else if (ratio1 >= 3) {
                        ratio1 = 1.6;
                    }
                    this.$nextTick(() => {
                        document.querySelectorAll('.pieDivStyle').forEach(element => {
                            element.style.fontSize = `${14 * ratio1}px`;
                            // this.pieOptions.singleHeight = itemEcharts.offsetHeight +(14 * ratio1 +10);
                        });
                    });
                }
                //高度不能显示的
                if (this.itemEchartsHeight <= 217 && this.param.taskType == 1) {
                    itemEcharts.style.overflow = 'auto';
                }
                if (this.itemEchartsHeight <= 217 && this.param.taskType == 2) {
                    itemEcharts.style.overflow = 'auto';
                }
            }
            // console.log(this.itemEchartsWidth);
            // console.log('this.itemEchartsWidth----------------------------------------------------------------------');
            if (this.itemEchartsWidth <= 345) {
                // debugger
                if (this.param.taskType == 0) {
                    this.pieBoxStyle = {
                        width: '350px',
                        margin: '0 auto',  // 居中显示
                    };
                } else if (this.param.taskType == 1) {
                    this.overviewContainerStyle = {
                        width: '350px',
                        margin: '0 auto',  // 居中显示
                    };
                    this.overviewOptions.singleHeight = 172;
                } else if (this.param.taskType == 2) {
                    this.scanVideoStyle = {
                        width: '350px',
                        margin: '0 auto',  // 居中显示
                    };
                    this.scrollOptions.singleHeight = 95;
                } else if (this.param.taskType == 3) {
                    this.ensureStyle = {
                        width: '350px',
                        margin: '0 auto',  // 居中显示
                    };
                    this.ensureOptions.singleHeight = 88;
                }
                this.$nextTick(() => {
                    itemEcharts.style.display = 'block'; /* 取消 flex 布局 */
                    itemEcharts.style.justifyContent = 'unset'; /* 取消水平居中 */
                    itemEcharts.style.alignItems = 'unset'; /* 取消垂直居中 */
                    itemEcharts.style.position = 'static'; /* 取消 relative 定位 */
                    itemEcharts.style.overflowX = 'auto';
                    if (this.param.taskType == 0 && val == 1) {
                        itemEcharts.style.overflowY = 'hidden';
                    } else {
                        itemEcharts.style.overflowY = 'auto';
                    }
                })
            }
            // if (this.itemEchartsWidth < 346) {
            //     // debugger
            //     if (this.param.taskType == 0) {
            //         this.pieBoxStyle = {
            //             width: '350px',
            //             margin: '0 auto',  // 居中显示
            //         };
            //     }
            //     itemEcharts.style.display = 'block'; /* 取消 flex 布局 */
            //     itemEcharts.style.justifyContent = 'unset'; /* 取消水平居中 */
            //     itemEcharts.style.alignItems = 'unset'; /* 取消垂直居中 */
            //     itemEcharts.style.position = 'static'; /* 取消 relative 定位 */
            //     itemEcharts.style.overflowX = 'auto';
            //     if (this.param.taskType == 0 && val == 1) {
            //         itemEcharts.style.overflowY = 'hidden';
            //     } else {
            //         itemEcharts.style.overflowY = 'auto';
            //     }
            // }
        },
        watchDataChange(newVal) {
            if (newVal.length > 0) {
                this.$nextTick(() => {
                    setTimeout(this.handleTaskType, 0);
                });
            }
        },
        handleTaskType() {
            if (this.param.taskType == 0) {
                this.wh(this.pie_List.length, "pieShow");
            } else if (this.param.taskType == 1) {
                this.wh(this.overviewData.length, "overviewShow");
            } else if (this.param.taskType == 2) {
                this.wh(this.scrollData.length, "scrollShow");
            } else if (this.param.taskType == 3) {
                this.wh(this.ensureData.length, "ensureShhow");
            }
        },
        handleMessage(msg) {
            //console.log('msg', msg);
            if (msg) {
                this.modalShow = false;
            }
        },
        handleClick(groupId, type) {
            if (type == 0 || type == 3) {
                this.modalTitle = '链路详情';
            } else {
                this.modalTitle = '故障指标';
            }
            this.queryData = {
                alarmType: type,
                groupId: groupId
            }
            this.modalShow = true;
        },
        clickup(e) {
            console.log('e------------', e);
            let target = e.target;
            if (this.param.taskType == 1) {
                if (target.tagName == "P" || target.tagName == "SPAN") {
                    let type = target.dataset.type;
                    let groupId = target.dataset.id;
                    //console.log(target.dataset.type);
                    //console.log(target.dataset.id);
                    if (type == 0 || type == 3) {
                        this.modalTitle = '链路详情';
                    } else {
                        this.modalTitle = '故障指标';
                    }
                    this.queryData = {
                        alarmType: type,
                        groupId: groupId
                    }
                    if (type) {
                        this.modalShow = true;
                    }
                }
            }
            if (target.tagName == 'IMG' || target.tagName == 'DIV') {
                let link = target.dataset.link;
                this.handleDetailClick(link)
            }
        },
        handleVideoCanPlay(event) {
            event.target.style.visibility = 'visible';
        },
        //进行页面跳转
        handleDetailClick(link) {
            //console.log(link);
            //console.log(111);
            if (link) {
                window.parent.postMessage({ type: 'linkType', id: link }, '*')
            }
        },
        handleStorageChange(event) {
            if (event.key === 'dark') {
                this.currentSkin = event.newValue; // 更新肤色
            }
        },
        getPie(param) {

            this.loading = true;
            this.$http.PostJson("/home/<USER>", param)
                .then(res => {
                    //console.log('res----', res);
                    let currentTaskData = JSON.stringify(JSON.parse(JSON.stringify(res.data)));
                    if (currentTaskData != this.preTaskData) {
                        this.preTaskData = currentTaskData
                        this.pie_List = [];
                        console.log('数据不同。。。。。。。。')
                    } else {
                        console.log('数据相同。。。。。。。。')
                        return
                    }
                    if (res.code === 1 && res.data.length > 0 && this.typeCode === 'route_statistic') {
                        this.bgLoading = false;
                        this.pieOptions.limitMoveNum = res.data.length;
                        if (res.data.length == 1) {
                            this.pieOptions.direction = -1;
                            this.isOnePie = true;
                        } else {
                            this.isOnePie = false;
                        }
                        const datas = res.data;

                        const calculatePercent = (num, total) => (num === 0 ? '0.00' : ((num / total) * 100).toFixed(2));
                        this.pie_List = datas.map((r, index) => {
                            const totalValue = r.total || 0;
                            return {
                                name: r.name,
                                // node: `pie-${index}`,
                                title: this.$t('comm_task'),
                                totalValue,
                                detailLink: r.detailLink,
                                data: [
                                    {
                                        value: r.normal,
                                        name: this.$t('dash_normal'),
                                        percent: calculatePercent(r.normal, totalValue)
                                    },
                                    {
                                        value: r.deteriorate,
                                        name: this.$t('dash_deterioration'),
                                        link: 'pathquality',
                                        percent: r.deteriorateRate
                                    },
                                    {
                                        value: r.terminal,
                                        name: this.$t('dash_interrupt'),
                                        link: 'pathquality',
                                        percent: r.terminalRate
                                    },
                                    {
                                        value: r.pause,
                                        name: this.$t('but_pause'),
                                        link: 'pathquality',
                                        percent: r.pauseRate
                                    }
                                ]
                            };
                        });
                        console.log('this.pie_List', this.pie_List)
                        // debugger
                    } else {
                        this.bgLoading = true;
                        this.pie_List = [];
                    }
                    this.$nextTick(() => {
                        //console.log('666666')
                        setTimeout(() => {
                            let ind = 0
                            document.querySelectorAll('.' + this.randomN).forEach((dom, index) => {
                                if (index > this.pie_List.length - 1) {
                                    ind = index - this.pie_List.length
                                } else {
                                    ind = index
                                }
                                this.$refs[this.pieListRef + ind][0].drawPie1(index, this.pie_List[ind])
                                // this.drawChart(dom);
                            });
                        }, 100);
                    })
                })
                .catch(() => {
                    this.bgLoading = true;
                    this.pie_List = [];
                })
                .finally(() => {
                    this.loading = false;
                    this.pieLoading = false;
                    if (parent.loading) {
                        parent.loading['dashtask'] = false;
                    }
                });
        },
        // 1概览图，2雷达图，3心跳图
        getData(param) {
            this.overviewData = [];
            this.scrollData = [];
            this.ensureData = [];
            if (param.componentId == 'undefined') {
                delete param.componentId;
            }
            this.loading = true;
            this.$http.PostJson("/home/<USER>", param)
                .then(({ code, data }) => {
                    if (code === 1 && data.length > 0) {
                        this.bgLoading = false;
                        // 概况
                        if (param.taskType == 1) {
                            this.overviewData = data;
                            this.overviewOptions.limitMoveNum = data.length;
                            //雷达
                        } else if (param.taskType == 2) {
                            this.scrollData = data;
                            this.scrollOptions.limitMoveNum = data.length;
                            // 心跳
                        } else if (param.taskType == 3) {
                            this.ensureData = data;
                            this.ensureOptions.limitMoveNum = data.length;
                        }
                        // this.startCustomScrolling();
                    } else {
                        this.bgLoading = true;
                    }
                }).finally(() => {
                    this.loading = false;
                    this.pieLoading = false;
                    if (parent.loading) {
                        parent.loading['dashtask'] = false;
                    }
                });
        },
        // 改为防抖函数
        debounceMouseState(newState) {
            // console.log('收到状态更新请求:', newState);
            // 清除之前的定时器
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
                //console.log('清除之前的定时器，重新计时');
            }
            // 设置新的定时器
            this.debounceTimer = setTimeout(() => {
                //console.log('防抖时间结束，更新鼠标状态为:', newState);
                let newValues = newState == true ? -1 : 1;
                if (this.pie_List.length > 1) {
                    this.pieOptions.direction = newValues;
                } else if (this.pie_List.length == 1) {
                    this.pieOptions.direction = -1;
                }
                if (this.overviewData.length > 1) {
                    this.overviewOptions.direction = newValues;
                } else if (this.overviewShow == 1) {
                    this.overviewOptions.direction = -1;
                }
                if (this.scrollData.length > 1) {
                    this.scrollOptions.direction = newValues;
                } else if (this.scrollShow == 1) {
                    this.scrollOptions.direction = -1;
                }
                if (this.ensureData.length > 1) {
                    this.ensureOptions.direction = newValues;
                } else if (this.ensureShhow == 1) {
                    this.ensureOptions.direction = -1;
                }
                // this.pieOptions.direction = newValues;
                // this.scrollOptions.direction = newValues;
                // this.ensureOptions.direction = newValues;
                // this.overviewOptions.direction = newValues;
                this.debounceTimer = null;
            }, 1000);
        },
    },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
        if (this.interrefresh) {
            clearInterval(this.interrefresh);
            this.interrefresh = null;
        }
        // 清理定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
        if (this.scrollTimer) {
            clearInterval(this.scrollTimer);
            this.scrollTimer = null;
        }
    }
};
</script>
<style lang='less'>
/deep/ .itemEcharts ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    box-shadow: inset 0 0 5px var(--scrollbar_track_shadow_color, rgba(0, 0, 0, 0.2));
    border-radius: 10px;
    background: #0e1012;
    background: var(--scrollbar_track_bg_color, #06324d);
}

/deep/ .itemEcharts ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(6, 50, 77, 1);
    box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color, rgba(6, 50, 77, 1));
    background: #015197;
    background: var(--scrollbar_thumb_bg_color, #015197);
}
</style>
<style scoped>
.pieBox {
    height: 100%;
}

.overview-container {
    overflow: hidden;
    position: relative;
}

.overview {
    padding: 12px 12px 0 12px;
}

.monitor {
    display: flex;
    background-image: url("../../../assets/dashtask/tbg.png");
    background-size: cover;
    justify-content: space-between;
    align-items: center;
}

.div1 {
    font-weight: bold;
    width: 90%;
    display: flex;
    align-items: center;
}

.div1 span:first-child {
    margin: 0px 12px;
    display: inline-block;
    max-width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.div1 span:last-child {
    font-size: 16px;
    display: inline-block;
}

.xqimg {
    margin-right: 12px;
    width: 10px;
    height: 9px;
    cursor: pointer !important;
}

/* 图片旋转 */
.data-container {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.data-box {
    display: flex;
    align-items: center;
    width: 48%;
}

.scan-video1 {
    width: 40%;
    flex-shrink: 0;
}

/* .img-container { */
/* position: relative;
    height: 5rem;
    width: 5rem;
    margin-left: 0; */
/* } */

/* .img-container img {
    position: absolute;
    left: 0;
} */

.rotate {
    animation: rotation 4s infinite linear;
    width: 5rem;
}

.rotate1 {
    width: 4rem;
    margin: 0.5rem;
}

.data-text {
    text-align: left;
    margin-left: 5%;
}

.data-text p:first-of-type {
    /* font-size: 18px; */
    color: #02b8fd;
    font-weight: bold;
}

.data-text p:not(:first-of-type) {
    color: #fff;
    font-weight: normal;
}

@keyframes rotation {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.status-container {
    margin: 6px 0 8px 0;
    display: flex;
    justify-content: space-between;
}

.status-box {
    cursor: pointer;
    text-align: center;
    margin: 0 3%;
}

.status-box p:first-of-type {
    font-weight: bold;
    font-size: 20px;
}

.status-box p:not(:first-of-type) {
    font-weight: 400;
    /* font-size: 14px; */
    color: #d3e4ff;
}

.status-box.normal p:first-of-type {
    color: #00ffee;
}

.status-box.terminal p:first-of-type {
    color: #fe5c5c;
}

.status-box.deteriorate p:first-of-type {
    color: #fea31b;
}

.status-box.pause p:first-of-type {
    color: #5ca0d5;
}

/* 指标 */
.target {
    width: 100%;
    padding: 20px 24px;
    display: flex;
    align-items: flex-start;
}

.scan-video {
    width: 44%;
    flex-shrink: 0;
    /* margin: auto; */
    margin-top: 35%;
}

.scan-video1 {
    width: 44%;
    flex-shrink: 0;
    margin: auto;
}

.target-div {
    width: 100%;
    margin-left: 5%;
    /* height: 400px; */
    overflow: hidden;
}

.div-bg {
    text-align: left;
    font-weight: bold;
    /* background-image: url("../../../assets/dashtask/tbg.png"); */
    background-size: cover;
    width: 90%;
    display: flex;
    align-items: center;
}

.div-bg span {
    display: inline-block;
}

.pie-name {
    max-width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* .div-bg span:first-child {
    margin: 0px 12px;
} */
.divBgSpan1 {
    margin: 0px 12px;
    display: inline-block;
    max-width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.divBgSpan2 {
    color: #02b8fd;
    display: inline-block;
}

/* .div-bg span:last-child {
    color: #02b8fd;
} */

.target-div1 {
    text-align: left;
    padding: 12px 0 12px 12px;
}

.white-text {
    color: white;
}

.div-text {
    font-weight: 400;
    color: #d3e4ff;
    margin-bottom: 5px;
}

.cursorP {
    cursor: pointer !important;
}

.div-color {
    font-weight: bold;
}

.div-color span:nth-child(1) {
    color: #fe5c5c;
}

.div-color span:nth-child(2) {
    color: #fea31b;
}

.div-color span:nth-child(3) {
    color: #00ffee;
}

.div-color span:nth-child(4) {
    color: #5ca0d5;
}

.ensure {
    padding: 5px 12px 0 12px;
}

.ensure-div {
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: left;
    width: 100%;
    margin: 8px 0 14px;
}

.ensure-img {
    width: 100%;
    /* height: 50px; */
}

.ensure-txt {
    margin-left: 12px;
    font-weight: 400;
    color: #d3e4ff;
    margin-bottom: 5px;
}

.onePieBox {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
