<template>
    <div class="itemEcharts" ref="itemEcharts" :style="'position: relative;height: 100%;background:' +
        (isbigscreen ? '#060D15' : isdarkSkin == 1 ? 'transparent' : '#ffffff')">
        <Loading :loading="loading"></Loading>
        <!-- 饼状图 -->

        <div ref="pie" class="pieBox" v-if="pie_List.length > 0 && param.taskType == 0" :style="pieBoxStyle"
            style="position: relative">
            <vue-seamless-scroll ref="scrollRefs1" :data="pie_List" :copy="false" :class-option="pieOptions">
                <div v-for="(item, index) in pie_List" :key="index" :style="pieStyle" class="li-item">
                    <div class="monitor" v-show="pie_List.length > 1">
                        <div class="div1">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                    <draw-pie :ref="'pieListRef' + index" :node="'pieListRef' + index" :datas="item"
                        :key="'pieListRef' + index" :isbigscreen="isbigscreen" :isdarkSkin="isdarkSkin"
                        :typeCode="typeCode" :time="{
                            startTime: param.startTime + ' 00:00:00',
                            endTime: param.endTime + ' 23:59:59',
                        }" :styleProp="styleProp">
                    </draw-pie>
                </div>
            </vue-seamless-scroll>
        </div>
        <!-- <div ref="pie" class="pieBox" v-if="pie_List.length == 1 && param.taskType == 0" :style="pieBoxStyle"
            style="position: relative">
            <draw-pie :node="item.node" :datas="item" :isbigscreen="isbigscreen" :isdarkSkin="isdarkSkin"
                :typeCode="typeCode"
                :time="{ startTime: param.startTime + ' 00:00:00', endTime: param.endTime + ' 23:59:59' }"
                :styleProp="styleProp" v-for="(item, index) in pie_List" :key="index">
            </draw-pie>
        </div> -->
        <!-- 监测概览 -->
        <div v-if="param.taskType == 1 && overviewShow == 0" class="overview-container" :style="overviewContainerStyle"
            @click="clickup($event)">
            <vue-seamless-scroll ref="scrollRefs2" :data="overviewData" :class-option="overviewOptions"
                @mouseover="overviewSeamless = false" @mouseout="overviewSeamless = true">
                <div class="overview" ref="overview" v-for="(item, index) in overviewData" :key="index">
                    <div class="monitor">
                        <div class="div1" :class="{ 'white-text': isbigscreen }">
                            <span>{{ item.name }}</span>
                            <span>{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png" alt=""
                            class="xqimg" />
                        <!-- @click="handleDetailClick(item.detailLink)" -->
                    </div>
                    <div class="data-container">
                        <div class="data-box">
                            <div class="img-container">
                                <img src="@/assets/dashtask/img-jc02.png" alt="" class="rotate" />
                                <img src="@/assets/dashtask/img-jc01.png" alt="" class="rotate1" />
                            </div>
                            <div class="data-text">
                                <p>{{ item.monitorDestNum }}</p>
                                <p>监测目标</p>
                            </div>
                        </div>
                        <div class="data-box">
                            <div class="img-container">
                                <img src="@/assets/dashtask/img-jd02.png" alt="" class="rotate" />
                                <img src="@/assets/dashtask/img-jd01.png" alt="" class="rotate1" />
                            </div>
                            <div class="data-text">
                                <p>{{ item.networkLinkNum }}</p>
                                <p>网络链路</p>
                            </div>
                        </div>
                    </div>
                    <div class="status-container">
                        <div class="status-box normal">
                            <p :data-id="item.groupId" :data-type="0">{{ item.normal }}</p>
                            <p>正常</p>
                        </div>
                        <div class="status-box terminal">
                            <p :data-id="item.groupId" :data-type="1">{{ item.terminal }}</p>
                            <p>中断</p>
                        </div>
                        <div class="status-box deteriorate">
                            <p :data-id="item.groupId" :data-type="2">
                                {{ item.deteriorate }}
                            </p>
                            <p>劣化</p>
                        </div>
                        <div class="status-box pause">
                            <p :data-id="item.groupId" :data-type="3">{{ item.pause }}</p>
                            <p>暂停</p>
                        </div>
                    </div>
                </div>
            </vue-seamless-scroll>
        </div>
        <div v-if="param.taskType == 1 && overviewShow == 1" class="overview-container" :style="overviewContainerStyle">
            <div class="overview" ref="overview" v-for="(item, index) in overviewData" :key="index">
                <div class="monitor">
                    <div class="div1" :class="{ 'white-text': isbigscreen }">
                        <span>{{ item.name }}</span>
                        <span>{{ item.total }}</span>
                    </div>
                    <img v-if="item.detailLink" src="@/assets/dashtask/xq.png" alt="" class="xqimg"
                        @click="handleDetailClick(item.detailLink)" />
                </div>
                <div class="data-container">
                    <div class="data-box">
                        <div class="img-container">
                            <img src="@/assets/dashtask/img-jc02.png" alt="" class="rotate" />
                            <img src="@/assets/dashtask/img-jc01.png" alt="" class="rotate1" />
                        </div>
                        <div class="data-text">
                            <p>{{ item.monitorDestNum }}</p>
                            <p>监测目标</p>
                        </div>
                    </div>
                    <div class="data-box">
                        <div class="img-container">
                            <img src="@/assets/dashtask/img-jd02.png" alt="" class="rotate" />
                            <img src="@/assets/dashtask/img-jd01.png" alt="" class="rotate1" />
                        </div>
                        <div class="data-text">
                            <p>{{ item.networkLinkNum }}</p>
                            <p>网络链路</p>
                        </div>
                    </div>
                </div>
                <div class="status-container">
                    <div class="status-box normal">
                        <p @click="handleClick(item.groupId, 0)">{{ item.normal }}</p>
                        <p>正常</p>
                    </div>
                    <div class="status-box terminal">
                        <p @click="handleClick(item.groupId, 1)">{{ item.terminal }}</p>
                        <p>中断</p>
                    </div>
                    <div class="status-box deteriorate">
                        <p @click="handleClick(item.groupId, 2)">
                            {{ item.deteriorate }}
                        </p>
                        <p>劣化</p>
                    </div>
                    <div class="status-box pause">
                        <p @click="handleClick(item.groupId, 3)">{{ item.pause }}</p>
                        <p>暂停</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 雷达-->
        <div class="target" v-if="param.taskType == 2 && scrollShow == 0" :style="scanVideoStyle">
            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video">
                <source src="@/assets/dashtask/img-scan2.mp4" type="video/mp4" />
            </video>
            <div class="target-div" @click="clickup($event)">
                <vue-seamless-scroll ref="scrollRefs3" :data="scrollData" :class-option="scrollOptions">
                    <div v-for="(item, index) in scrollData" :key="index">
                        <div class="monitor">
                            <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                                <span>{{ item.name }}</span>
                                <span>{{ item.total }}</span>
                            </div>
                            <img v-if="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png"
                                alt="" class="xqimg" />
                            <!-- @click="handleDetailClick(item.detailLink)" -->
                        </div>
                        <div class="target-div1">
                            <div class="div-text">中断/劣化/正常/暂停</div>
                            <div class="div-color cursorP">
                                <span :data-id="item.groupId" :data-type="1">{{ item.terminal }}</span>/<span
                                    :data-id="item.groupId" :data-type="2">{{ item.deteriorate }}</span>/<span
                                    :data-id="item.groupId" :data-type="0">{{
                                        item.normal }}</span>/<span :data-id="item.groupId" :data-type="3">{{ item.pause
                                    }}</span>
                            </div>
                        </div>
                    </div>
                </vue-seamless-scroll>
            </div>
        </div>
        <div class="target" v-if="param.taskType == 2 && scrollShow == 1" :style="scanVideoStyle">
            <video ref="scanVideo" :autoplay="!isPaused" loop muted class="scan-video">
                <source src="@/assets/dashtask/img-scan2.mp4" type="video/mp4" />
            </video>
            <div class="target-div">
                <div v-for="(item, index) in scrollData" :key="index">
                    <div class="monitor">
                        <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                            <span>{{ item.name }}</span>
                            <span>{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" src="@/assets/dashtask/xq.png" alt="" class="xqimg"
                            @click="handleDetailClick(item.detailLink)" />
                    </div>
                    <div class="target-div1">
                        <div class="div-text">中断/劣化/正常/暂停</div>
                        <div class="div-color cursorP">
                            <span @click="handleClick(item.groupId, 1)">{{ item.terminal }}</span>/<span
                                @click="handleClick(item.groupId, 2)">{{ item.deteriorate }}</span>/<span
                                @click="handleClick(item.groupId, 0)">{{
                                    item.normal }}</span>/<span @click="handleClick(item.groupId, 3)">{{ item.pause
                                }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--  <img src="@/assets/dashtask/line.gif" alt="line"> -->
        <!-- <video :autoplay="!isPaused" loop muted class="scan-video" @canplay="handleVideoCanPlay">
            <source src="@/assets/dashtask/line.mp4" type="video/mp4">
        </video> -->
        <!-- 心跳 -->
        <div v-if="param.taskType == 3 && ensureShhow == 0" :style="ensureStyle" @click="clickup($event)">
            <vue-seamless-scroll ref="scrollRefs4" :data="ensureData" :class-option="ensureOptions">
                <div v-for="(item, index) in ensureData" :key="index" class="ensure">
                    <div class="monitor">
                        <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                            <span>{{ item.name }}</span>
                            <span>{{ item.total }}</span>
                        </div>
                        <img v-if="item.detailLink" :data-link="item.detailLink" src="@/assets/dashtask/xq.png" alt=""
                            class="xqimg" />
                        <!-- @click="handleDetailClick(item.detailLink)" -->
                    </div>
                    <div class="ensure-div">
                        <div style="width: 50%;">
                            <div class="ensure-txt">
                                中断/劣化/正常/暂停
                            </div>
                            <div class="div-color cursorP" style="margin-left: 12px;">
                                <span :data-id="item.groupId" :data-type="1">{{ item.terminal }}</span>/<span
                                    :data-id="item.groupId" :data-type="2">{{ item.deteriorate }}</span>/<span
                                    :data-id="item.groupId" :data-type="0">{{
                                        item.normal }}</span>/<span :data-id="item.groupId" :data-type="3">{{ item.pause
                                    }}</span>
                            </div>
                        </div>
                        <div style="width: 50%;">
                            <img v-if="item.type == '0'" class="ensure-img" src="@/assets/dashtask/lineH.gif"
                                alt="line">
                            <img v-if="item.type == '1'" class="ensure-img" src="@/assets/dashtask/lineR.gif"
                                alt="line">
                            <img v-if="item.type == '2'" class="ensure-img" src="@/assets/dashtask/lineY.gif"
                                alt="line">
                            <img v-if="item.type == '3'" class="ensure-img" src="@/assets/dashtask/lineB.gif"
                                alt="line">
                        </div>
                    </div>
                </div>
            </vue-seamless-scroll>
        </div>
        <div v-if="param.taskType == 3 && ensureShhow == 1" :style="ensureStyle">
            <div v-for="(item, index) in ensureData" :key="index" class="ensure">
                <div class="monitor">
                    <div class="div-bg" :class="{ 'white-text': isbigscreen }">
                        <span>{{ item.name }}</span>
                        <span>{{ item.total }}</span>
                    </div>
                    <img v-if="item.detailLink" src="@/assets/dashtask/xq.png" alt="" class="xqimg"
                        @click="handleDetailClick(item.detailLink)" />
                </div>
                <div class="ensure-div">
                    <div style="width: 50%;">
                        <div class="ensure-txt">
                            中断/劣化/正常/暂停
                        </div>
                        <div class="div-color cursorP" style="margin-left: 12px;cursor: pointer;">
                            <span @click="handleClick(item.groupId, 1)">{{ item.terminal }}</span>/<span
                                @click="handleClick(item.groupId, 2)">{{ item.deteriorate }}</span>/<span
                                @click="handleClick(item.groupId, 0)">{{
                                    item.normal }}</span>/<span @click="handleClick(item.groupId, 3)">{{ item.pause
                                }}</span>
                        </div>
                    </div>
                    <div style="width: 50%;">
                        <img v-if="item.type == '0'" class="ensure-img" src="@/assets/dashtask/lineH.gif" alt="line">
                        <img v-if="item.type == '1'" class="ensure-img" src="@/assets/dashtask/lineR.gif" alt="line">
                        <img v-if="item.type == '2'" class="ensure-img" src="@/assets/dashtask/lineY.gif" alt="line">
                        <img v-if="item.type == '3'" class="ensure-img" src="@/assets/dashtask/lineB.gif" alt="line">
                    </div>
                </div>
            </div>
        </div>
        <!-- 暂无数据 -->
        <div v-if="bgLoading" :class="{ fillempty: isdarkSkin == 1, fillempty2: isdarkSkin == 0 }">
            <p class="emptyText">{{ $t("common_No_data") }}</p>
        </div>
        <detailModal :modalTitle="modalTitle" :queryData="queryData" :modalShow="modalShow"></detailModal>
    </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import drawPie from "@/common/echarts/dashboardPie22.vue";
import detailModal from "../components/DetailModal.vue";
import '@/timechange';

function clientProperty() {
    if (document.body.offsetHeight != 0 && document.body.offsetWidth != 0) {
        return { width: document.body.offsetWidth, height: document.body.offsetHeight }
    }
}
export default {
    name: "dashTask",
    components: {
        drawPie, vueSeamlessScroll, detailModal
    },
    data() {
        return {
            pieBoxStyle: {
                width: '100%',
                height: '100%'
            },
            pieStyle: {
                height: '300px'
            },
            overviewContainerStyle: {
                width: '100%',
                height: '100%'
            },
            scanVideoStyle: {
                width: '100%',
                height: '100%'
            },
            ensureStyle: {
                width: '100%',
                height: '100%'
            },
            // 饼状图
            //刷新时间间隔
            intervalTime: null,
            //刷新参数
            interrefresh: null,
            nowDate: new Date(),
            //时间参数
            startTime: null,
            endTime: null,
            loading: false,
            bgLoading: false,
            pieLoading: false,
            height: 260,
            param: {
                // 0默认环状图，1概览图，2雷达图，3心跳图 
                taskPresentationType: null,
                taskType: null,
                componentId: "",
                startTime: new Date().format2('yyyy-MM-dd'),
                endTime: new Date().format2('yyyy-MM-dd'),
            },
            pie_List: [],
            pieOptions: {
                step: 1,
                limitMoveNum: 0,
                hoverStop: true,
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                openWatch: true,
                singleHeight: 0,
                waitTime: 5000,
            },
            styleProp: clientProperty(),
            typeCode: "route_statistic", //此参数由调用的时候传进来，传进来的是构件的code例（route_statistic，special_statistic，snmp_statistic）
            isbigscreen: false,
            isdarkSkin: sessionStorage.getItem('dark') || 1,
            // 概况
            isPaused: false,  // 添加视频暂停状态控制
            overviewSeamless: true,
            overviewShow: 0,
            overviewData: [],
            overviewOptions: {
                step: 1,
                limitMoveNum: 2,
                hoverStop: true,
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                singleHeight: 184,
                waitTime: 5000,
            },
            //雷达
            seamless: true,
            scrollShow: 0,
            scrollData: [],
            scrollOptions: {
                step: 1, // 数值越大速度滚动越快
                limitMoveNum: 5, // 开始无缝滚动的数据量
                hoverStop: true, // 是否开启鼠标悬停stop
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                singleHeight: 95, // 单步运动停止的高度(默认值0是无缝不停止的滚动) 
                waitTime: 5000, // 单步运动停止的时间(默认值1000ms)
            },
            ensureShhow: 0,
            ensureData: [
                {
                    name: '网络保障1',
                    total: 15,
                    terminal: '1025',
                    deteriorate: '1080',
                    normal: '3506',
                    pause: '1150',
                    type: '1' //表示任务显示的图片颜色 1红色 2黄色 3蓝色
                },
                {
                    name: '网络保障2',
                    total: 16,
                    terminal: '1025',
                    deteriorate: '1080',
                    normal: '3506',
                    pause: '1150',
                    type: '2'
                },
                {
                    name: '网络保障3',
                    total: 18,
                    terminal: '1025',
                    deteriorate: '1080',
                    normal: '3506',
                    pause: '1150',
                    type: '3'
                }
            ],
            // 心跳
            ensureOptions: {
                step: 1, // 数值越大速度滚动越快
                limitMoveNum: 3, // 开始无缝滚动的数据量
                hoverStop: true, // 是否开启鼠标悬停stop
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                singleHeight: 101, // 单步运动停止的高度(默认值0是无缝不停止的滚动) 
                waitTime: 5000, // 单步运动停止的时间(默认值1000ms)
            },
            modalTitle: '链路详情',
            queryData: {
                alarmType: '',
                groupId: ''
            },
            modalShow: false,
            debounceTimer: null,
            itemEchartsHeight: 0,
            timer: null,
            scrollTimer: null
        };
    },
    watch: {},
    created() {
        // 编辑
        if (parent.window.isEdit) {
            let editName = window.frames.frameElement.getAttribute('editName')
            let taskType = editName.split('&&taskPresentationType=')[1].split('&&componentId=')[0];
            let dashboardComponentTaskGroups = editName.split('&&dashboardComponentTaskGroups=')[1].split('&&')[0];
            // console.log("taskType--------", taskType);
            // console.log('dashboardComponentTaskGroups------', dashboardComponentTaskGroups);
            // debugger
            // 编辑走这里
            this.param.taskType = taskType;
            if (dashboardComponentTaskGroups && dashboardComponentTaskGroups.length > 0) {
                this.param.dashboardComponentTaskGroups = JSON.parse(dashboardComponentTaskGroups);
                this.param.taskPresentationType = taskType;
            }
        } else {
            this.param.taskType = window.frames.name.split('&&taskPresentationType=')[1].split('&&componentId=')[0];
            this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
            this.param.componentId = window.frames.name.split('&&componentId=')[1].split('&&')[0];
            if (window.frames.frameElement) {
                window.frames.frameElement.contentWindow.close();
            }
        }
        // 0默认环状图，1概览图，2雷达图，3心跳图 
        if (this.param.taskType == 0) {
            this.getPie(this.param);
        } else {
            this.getData(this.param);
        }
    },
    mounted() {
        window.addEventListener('message', e => {
            if (e.data && e.data.type === 'mouseEnterState') {
                // 使用防抖控制状态更新
                this.debounceMouseState(e.data.data);
            }
        });
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        this.isbigscreen = window.isbigscreen;
        debugger
        console.log('this.intervalTime', this.intervalTime);
        if (this.intervalTime && Number(this.intervalTime)) {
            this.interrefresh = setInterval(() => {
                this.loading = true;
                if (this.param.taskType == 0) {
                    this.getPie(this.param)
                } else {
                    this.getData(this.param)
                }
            }, this.intervalTime * 1000);
        }

        // 获取 itemEcharts 元素的高宽
        const itemEcharts = this.$refs.itemEcharts;
        const itemEchartsWidth = itemEcharts.offsetWidth;
        const itemEchartsHeight = itemEcharts.offsetHeight;
        this.itemEchartsHeight = itemEcharts.offsetHeight
        this.pieOptions.singleHeight = itemEcharts.offsetHeight;
        console.log(456456);
        console.log(itemEcharts.offsetHeight);
        // 设置饼状体轮播问题
        this.pieStyle.height = itemEchartsHeight + 'px';
        // 原始内容的宽高
        let originalWidth, originalHeight;
        if (this.param.taskType == 1 || this.param.taskType == 3) {
            originalWidth = 350;
            originalHeight = 400;
        } else if (this.param.taskType == 2) {
            originalWidth = 350;
        }
        // 2宽160 3款338

        // 计算宽高比
        const widthRatio = itemEchartsWidth / originalWidth;
        const heightRatio = itemEchartsHeight / originalHeight;

        // 选择较小的比例进行等比缩放
        const scaleRatio = Math.min(widthRatio, heightRatio);

        // 计算缩放后的宽高
        const scaledWidth = originalWidth * scaleRatio;
        const scaledHeight = originalHeight * scaleRatio;

        if (itemEchartsWidth > 580) {
            // 设置样式
            if (this.param.taskType == 1) {
                this.overviewContainerStyle = {
                    width: `${scaledWidth + 57}px`,
                    height: `${scaledHeight}px`,
                    margin: '0 auto'  // 居中显示
                };
            } else if (this.param.taskType == 2) {
                this.scanVideoStyle = {
                    width: `${scaledWidth}px`,
                    height: `${scaledHeight}px`,
                    margin: '0 auto'  // 居中显示
                };
            } else if (this.param.taskType == 3) {
                this.ensureStyle = {
                    width: `${scaledWidth}px`,
                    height: `${scaledHeight}px`,
                    margin: '0 auto'  // 居中显示
                };
            }
        }
        console.log(itemEchartsWidth);
        console.log('itemEchartsWidth----------------------------------------------------------------------');
        if (itemEchartsWidth <= 345) {
            if (this.param.taskType == 0) {
                this.pieBoxStyle = {
                    width: '350px',
                    margin: '0 auto',  // 居中显示
                };
            } else if (this.param.taskType == 1) {
                this.overviewContainerStyle = {
                    width: '350px',
                    margin: '0 auto',  // 居中显示
                };
            } else if (this.param.taskType == 2) {
                this.scanVideoStyle = {
                    width: '350px',
                    margin: '0 auto',  // 居中显示
                };
            } else if (this.param.taskType == 3) {
                this.ensureStyle = {
                    width: '350px',
                    margin: '0 auto',  // 居中显示
                };
            }
            itemEcharts.style.overflowX = 'auto';
            itemEcharts.style.overflowY = 'hidden';
        }
        // 添加窗口大小变化监听
        window.addEventListener('resize', this.handleResize)
    },
    methods: {
        handleClick(groupId, type) {
            if (type == 0 || type == 3) {
                this.modalTitle = '链路详情';
            } else {
                this.modalTitle = '故障指标';
            }
            this.queryData = {
                alarmType: type,
                groupId: groupId
            }
            this.modalShow = true;
        },
        clickup(e) {
            console.log(e);
            let target = e.target;
            if (target.tagName == "P" || target.tagName == "SPAN") {
                let type = target.dataset.type;
                let groupId = target.dataset.id;
                console.log(target.dataset.type);
                console.log(target.dataset.id);
                if (type == 0 || type == 3) {
                    this.modalTitle = '链路详情';
                } else {
                    this.modalTitle = '故障指标';
                }
                this.queryData = {
                    alarmType: type,
                    groupId: groupId
                }
                this.modalShow = true;
            }
            if (target.tagName == 'IMG') {
                let link = target.dataset.link;
                this.handleDetailClick(link)
            }
        },
        handleVideoCanPlay(event) {
            event.target.style.visibility = 'visible';
        },
        //进行页面跳转
        handleDetailClick(link) {
            console.log(link);
            console.log(111);
            if (link) {
                window.parent.postMessage({ type: 'linkType', id: link }, '*')
            }
        },
        handleStorageChange(event) {
            if (event.key === 'dark') {
                this.currentSkin = event.newValue; // 更新肤色
            }
        },
        getPie(param) {
            this.pie_List = [];
            this.loading = true;
            this.$http.PostJson("/home/<USER>", param)
                .then(res => {
                    console.log('res----', res);
                    if (res.code === 1 && res.data.length > 0 && this.typeCode === 'route_statistic') {
                        this.bgLoading = false;
                        this.pieOptions.limitMoveNum = res.data.length;
                        if (res.data.length == 1) {
                            this.pieOptions.direction = -1;
                        }
                        const datas = res.data;

                        const calculatePercent = (num, total) => (num === 0 ? '0.00' : ((num / total) * 100).toFixed(2));
                        this.pie_List = datas.map((r, index) => {
                            const totalValue = r.total || 0;
                            return {
                                name: r.name,
                                // node: `pie-${index}`,
                                title: this.$t('comm_task'),
                                totalValue,
                                data: [
                                    {
                                        value: r.normal,
                                        name: this.$t('dash_normal'),
                                        percent: calculatePercent(r.normal, totalValue)
                                    },
                                    {
                                        value: r.deteriorate,
                                        name: this.$t('dash_deterioration'),
                                        link: 'pathquality',
                                        percent: r.deteriorateRate
                                    },
                                    {
                                        value: r.terminal,
                                        name: this.$t('dash_interrupt'),
                                        link: 'pathquality',
                                        percent: r.terminalRate
                                    },
                                    {
                                        value: r.pause,
                                        name: this.$t('but_pause'),
                                        link: 'pathquality',
                                        percent: r.pauseRate
                                    }
                                ]
                            };
                        });
                    } else {
                        this.bgLoading = true;
                        this.pie_List = [];
                    }
                    setTimeout(() => {
                        let ind = 0
                        document.querySelectorAll('.li-item').forEach((dom, index) => {
                            if (index > this.pie_List.length - 1) {
                                ind = index - this.pie_List.length
                            } else {
                                ind = index
                            }
                            this.$refs['pieListRef' + ind][0].drawPie1(index, this.pie_List[ind])
                            // this.drawChart(dom);
                        });
                    }, 0);
                })
                .catch(() => {
                    this.bgLoading = true;
                    this.pie_List = [];
                })
                .finally(() => {
                    this.loading = false;
                    this.pieLoading = false;
                    if (parent.loading) {
                        parent.loading['dashtask'] = false;
                    }
                });
        },
        // 1概览图，2雷达图，3心跳图 
        getData(param) {
            this.overviewData = [];
            this.scrollData = [];
            this.ensureData = [];
            if (param.componentId == 'undefined') {
                delete param.componentId;
            }
            this.loading = true;
            this.$http.PostJson("/home/<USER>", param)
                .then(({ code, data }) => {
                    if (code === 1 && data.length > 0) {
                        this.bgLoading = false;
                        let H = this.itemEchartsHeight;
                        // 概况
                        if (param.taskType == 1) {
                            this.overviewShow = (data.length) * 184 > H ? 0 : 1;
                            this.overviewData = data;
                            this.overviewOptions.limitMoveNum = data.length;
                            //雷达
                        } else if (param.taskType == 2) {
                            this.scrollShow = (data.length) * 95 > H ? 0 : 1;
                            this.scrollData = data;
                            this.scrollOptions.limitMoveNum = data.length;
                            // 心跳
                        } else if (param.taskType == 3) {
                            this.ensureShhow = (data.length) * 106 > H ? 0 : 1;
                            this.ensureData = data;
                            this.ensureOptions.limitMoveNum = data.length;
                        }
                        // this.startCustomScrolling();
                    } else {
                        this.bgLoading = true;
                    }
                }).finally(() => {
                    this.loading = false;
                    this.pieLoading = false;
                    if (parent.loading) {
                        parent.loading['dashtask'] = false;
                    }
                });
        },
        // 改为防抖函数
        debounceMouseState(newState) {
            // console.log('收到状态更新请求:', newState);
            // 清除之前的定时器
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
                console.log('清除之前的定时器，重新计时');
            }

            // 设置新的定时器
            this.debounceTimer = setTimeout(() => {
                console.log('防抖时间结束，更新鼠标状态为:', newState);
                let newValues = newState == true ? -1 : 1;
                this.scrollOptions.direction = newValues;
                this.ensureOptions.direction = newValues;
                this.overviewOptions.direction = newValues;
                this.debounceTimer = null;
            }, 1000);
        },
    },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
        if (this.interrefresh) {
            clearInterval(this.interrefresh);
            this.interrefresh = null;
        }
        // 清理定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
        if (this.scrollTimer) {
            clearInterval(this.scrollTimer);
            this.scrollTimer = null;
        }
    }
};
</script>
<style lang='less'>
/deep/ .itemEcharts ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    box-shadow: inset 0 0 5px var(--scrollbar_track_shadow_color, rgba(0, 0, 0, 0.2));
    border-radius: 10px;
    background: #0e1012;
    background: var(--scrollbar_track_bg_color, #06324D);
}

/deep/ .itemEcharts ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(6, 50, 77, 1);
    box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color, rgba(6, 50, 77, 1));
    background: #015197;
    background: var(--scrollbar_thumb_bg_color, #015197);
}
</style>
<style scoped>
.pieBox {
    height: 100%;
}

.overview-container {
    overflow: hidden;
    position: relative
}

.overview {
    padding: 12px 12px 0 12px;
}

.monitor {
    display: flex;
    background-image: url("../../../assets/dashtask/tbg.png");
    background-size: cover;
    justify-content: space-between;
    align-items: center;
}

.div1 {
    font-weight: bold;
}

.div1 span:first-child {
    margin: 0px 12px;
}

.div1 span:last-child {
    font-size: 16px;
}

.xqimg {
    margin-right: 12px;
    width: 10px;
    height: 9px;
    cursor: pointer !important;
}

/* 图片旋转 */
.data-container {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
}

.data-box {
    display: flex;
    align-items: center;
    width: 48%;
}

.img-container {
    position: relative;
    height: 5rem;
    width: 5rem;
    margin-left: 0;
}

.img-container img {
    position: absolute;
    left: 0;
}

.rotate {
    animation: rotation 4s infinite linear;
    width: 5rem;
}

.rotate1 {
    width: 4rem;
    margin: 0.5rem;
}

.data-text {
    text-align: left;
    margin-left: 5%;
}

.data-text p:first-of-type {
    font-size: 18px;
    color: #02b8fd;
    font-weight: bold;
}

.data-text p:not(:first-of-type) {
    font-size: 14px;
    color: #fff;
    font-weight: normal;
}

@keyframes rotation {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.status-container {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}

.status-box {
    cursor: pointer;
    text-align: center;
    margin: 0 3%;
}

.status-box p:first-of-type {
    font-weight: bold;
    font-size: 20px;
}

.status-box p:not(:first-of-type) {
    font-weight: 400;
    font-size: 14px;
    color: #d3e4ff;
}

.status-box.normal p:first-of-type {
    color: #00ffee;
}

.status-box.terminal p:first-of-type {
    color: #fe5c5c;
}

.status-box.deteriorate p:first-of-type {
    color: #fea31b;
}

.status-box.pause p:first-of-type {
    color: #5ca0d5;
}

/* 指标 */
.target {
    width: 100%;
    padding: 20px 24px;
    display: flex;
    align-items: flex-start;
}

.scan-video {
    width: 44%;
    flex-shrink: 0;
    margin: auto;
}

.target-div {
    width: 100%;
    margin-left: 5%;
    /* height: 400px; */
    overflow: hidden;
}

.div-bg {
    text-align: left;
    font-weight: bold;
    /* background-image: url("../../../assets/dashtask/tbg.png"); */
    background-size: cover;
}

.div-bg span:first-child {
    font-size: 14px;
    margin: 0px 12px;
}

.div-bg span:last-child {
    font-size: 16px;
    color: #02b8fd;
}

.target-div1 {
    text-align: left;
    padding: 12px;
}

.white-text {
    color: white;
}

.div-text {
    font-size: 14px;
    font-weight: 400;
    color: #d3e4ff;
    margin-bottom: 5px;
}

.cursorP {
    cursor: pointer !important;
}

.div-color {
    font-weight: bold;
}

.div-color span:nth-child(1) {
    color: #fe5c5c;
}

.div-color span:nth-child(2) {
    color: #fea31b;
}

.div-color span:nth-child(3) {
    color: #00ffee;
}

.div-color span:nth-child(4) {
    color: #5ca0d5;
}

.ensure {
    padding: 5px 12px 0 12px;
}

.ensure-div {
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: left;
    width: 100%;
    margin: 12px 0 10px;
    height: 50px;
}

.ensure-img {
    width: 100%;
    height: 50px;
}

.ensure-txt {
    margin-left: 12px;
    font-weight: 400;
    color: #D3E4FF;
    margin-bottom: 5px;
}
</style>
