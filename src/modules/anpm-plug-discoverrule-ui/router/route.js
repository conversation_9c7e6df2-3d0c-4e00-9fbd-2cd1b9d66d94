const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/discoverrule',
  },
  {
    path: "/discoverrule",
    name: lan.getLabel("src.autodiscover"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-discoverrule-ui/views/index.vue"], resolve)
  },
];
