<template>
  <section class="sectionBox">
    <!--  查询面板  -->
    <div class="section-top">
      <!--  查询条件    -->
      <Row class="fn_box">
        <!--企业组织-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label">{{ $t("comm_org") }}：</label>
            <div class="fn_item_box">
              <TreeSelect
                v-model="treeValueQuery"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadData"
                @onSelectChange="setOrgQuery"
                @onClear="onClearQuery"
                @onFocus="focusFnQuery"
              ></TreeSelect>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("discover_discovery_status") }}：</label
            >
            <div class="fn_item_box">
              <Select
                v-model="status"
                :placeholder="$t('comm_please_select')"
                filterable
                :only-filter-with-text="true"
                clearable
              >
                <Option
                  v-for="(item, index) in statusList"
                  :value="item.value"
                  :key="index"
                  >{{ item.label }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
            <div class="fn_item_box">
              <Input
                v-model="keyWord"
                :title="$t('discover_pd_rule_keyword')"
                :placeholder="$t('discover_pd_rule_keyword')"
                style="width: 300px"
              />
            </div>
          </div>
        </Col>
      </Row>
      <!--面板功能按键区域-->
      <div class="tool-btn">
        <div>
          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.list"
            icon="ios-search"
            @click="queryClick(1)"
            :title="$t('common_query')"
          ></Button>
          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.add"
            icon="md-add"
            @click="openAddForm"
            :title="this.$t('common_new')"
          ></Button>
        </div>
        <Dropdown @on-click="moreBtnClick">
          <Button class="more-btn">
            {{ $t("common_more") }}
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem
              name='rowRemove("plural")'
              v-if="permissionObj.delete"
              >{{ $t("but_remove") }}</DropdownItem
            >
            <DropdownItem name="start()" v-if="permissionObj.status">{{
              $t("but_enable")
            }}</DropdownItem>
            <DropdownItem name="stop()" v-if="permissionObj.status">{{
              $t("common_disable")
            }}</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body" style="padding: 0">
      <Loading :loading="pageLoading"></Loading>
      <div class="section-body-content">
        <div>
          <!--    列表      -->
          <Table
            ref="tablelist"
            class="fixed-left-right"
            stripe
            :columns="tableColumn"
            :data="pageData.list"
            :no-data-text="
              pageData.total > 0
                ? ''
                : currentSkin == 1
                ? '<div class=&quot;table_empty&quot;><p class=&quot;emptyText&quot; > ' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=&quot;table2_empty&quot;><p class=&quot;emptyText&quot; > ' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
            <!--    列表操作列功能        -->
            <template slot-scope="{ row }" slot="action" class="tableTools">
              <Tooltip
                :content="$t('common_update')"
                placement="left"
                :transfer="true"
              >
                <span
                  :class="currentSkin == 1 ? 'edit1-btn' : 'light-edit1-btn'"
                  v-if="permissionObj.update"
                  @click="openEditForm(row)"
                ></span>
              </Tooltip>
              <Tooltip
                :content="$t('common_delete')"
                placement="left"
                :transfer="true"
              >
                <span
                  class="del1-btn"
                  v-if="permissionObj.delete"
                  @click="rowRemove('singular', row)"
                ></span>
              </Tooltip>
            </template>
            <!-- 进度条列 -->
            <template slot-scope="{ row }" slot="discoveryProgress">
              <div
                :class="[
                  'progress-box',
                  currentSkin == 0 ? 'progress-box-border' : '',
                ]"
              >
                <div
                  :class="[
                    'progress',
                    getActive(row) ? 'progress-animation' : '',
                  ]"
                  :style="getStyle(row)"
                ></div>
                <div class="progress-text">{{ formateText(row) }}</div>
              </div>
            </template>
            <!-- /进度条列 -->
          </Table>
          <!--   分页      -->
          <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
            <Page
              v-page
              :current.sync="page.pageNo"
              :page-size="page.pageSize"
              :total="pageData.total"
              :page-size-opts="page.pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建规则弹框  -->
    <Modal
      :title="$t('common_new')"
      sticky
      v-model="addOpen"
      width="1000"
      :styles="{ top: '120px' }"
      class="operation-modal"
      draggable
      :mask="true"
      :mask-closable="false"
      @on-ok="addSubmit"
      @on-cancel="cancleForm('addRuleForm')"
    >
      <Form
        ref="addRuleForm"
        v-if="addOpen"
        :model="addRuleForm"
        :rules="addRuleFormRule"
        class="width_50_Form"
        @submit.native.prevent
        :label-width="130"
      >
        <!-- 机构 -->
        <FormItem :label="$t('comm_org') + $t('comm_colon')" prop="orgId">
          <div class="fn_item_box">
            <TreeSelect
              v-model="treeValue"
              ref="TreeSelect"
              :data="treeData"
              :placeholder="$t('snmp_pl_man')"
              :loadData="loadData"
              @onSelectChange="setOrg"
              @onClear="onClear"
              @onFocus="focusFn"
            ></TreeSelect>
          </div>
        </FormItem>
        <FormItem
          :label="$t('discover_rule_name') + $t('comm_colon')"
          prop="name"
        >
          <Input v-model="addRuleForm.name" maxlength="50"></Input>
        </FormItem>
        <!-- 采集器 -->
        <FormItem
          :label="$t('discover_gether_name') + $t('comm_colon')"
          prop="getherCode"
        >
          <Select
            ref="getherClear"
            v-model="addRuleForm.getherCode"
            filterable
            :only-filter-with-text="true"
            clearable
            @on-open-change="getGether"
            :placeholder="$t('gether_select_collector')"
          >
            <Option
              v-for="item in gatherList"
              :value="item.code"
              :key="item.code"
              >{{ item.labelName }}
            </Option>
          </Select>
        </FormItem>
        <FormItem
          :label="$t('discover_dis_inter') + $t('comm_colon')"
          prop="intervalTime"
        >
          <div class="flex flex-between" style="float: left">
            <Input
              v-model.number="addRuleForm.intervalTime"
              style="width: 280px; margin-right: 5px"
            ></Input>
          </div>
          <span>{{ $t("snmptask_hour") }}</span>
        </FormItem>
        <Row>
          <Col span="19">
            <FormItem
              :label="$t('comm_ip_range') + $t('comm_colon')"
              class="width_100_item ipBox"
              prop="ipStr"
            >
              <Input
                v-model="addRuleForm.ipStr"
                :placeholder="$t('comm_ip_range')"
                maxlength="128"
              ></Input>
              <span>{{ $t("discover_ip_tips") }}</span>
              <!-- <div class="ipitemBox startIp">
              <label style="padding: 0 10px 0 0">{{ $t('comm_from') }}</label>
              <Input class="ipItem" v-model="addRuleForm.ipOne" @input="pushIp(0, $event, 0)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="addRuleForm.ipTwo" @input="pushIp(1, $event, 0)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="addRuleForm.ipThree" @input="pushIp(2, $event, 0)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="addRuleForm.startIpFour" @input="pushIp(3, $event, 0)"></Input>
            </div>
            <div class="ipitemBox endIp">
              <label style="padding: 0 10px 0 20px">{{ $t('comm_to') }}</label>
              <Input class="ipItem" v-model="addRuleForm.ipOne" @input="pushIp(0, $event, 0)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="addRuleForm.ipTwo" @input="pushIp(1, $event, 0)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="addRuleForm.ipThree" @input="pushIp(2, $event, 0)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="addRuleForm.endIpFour" @input="pushIp(4, $event, 0)"></Input>
            </div> -->
            </FormItem>
          </Col>
          <Col span="5">
            <FormItem
              :label="$t('probe_dynamic')"
              prop="dynamicIp"
              :label-width="100"
            >
              <Checkbox
                style="margin-top: 8px"
                v-model="addRuleForm.dynamicIp"
                :true-value="1"
                :false-value="0"
              ></Checkbox>
            </FormItem>
          </Col>
        </Row>
        <FormItem
          :label="$t('phytopo_desc') + $t('comm_colon')"
          class="width_100_item"
          style="margin-top: 10px"
        >
          <Input
            type="textarea"
            maxlength="200"
            v-model="addRuleForm.describe"
            :autosize="{ minRows: 5, maxRows: 5 }"
          ></Input>
        </FormItem>
        <Divider style="margin: 10px 0 30px 0" />
        <div>
          <div style="display: inline-block; width: 120px; float: left">
            <p
              style="
                text-align: right;
                line-height: 1;
                padding: 10px 12px 10px 0;
              "
            >
              {{ $t("discover_create_task") }}:
            </p>
          </div>
          <div style="margin-left: 120px">
            <FormItem label="" class="width_100_item">
              <RadioGroup v-model="addRuleForm.createTaskFlag">
                <Radio label="1">
                  <span>{{ $t("discover_create_task") }}</span>
                </Radio>
                <Radio label="0">
                  <span>{{ $t("discover_donot_create_task") }}</span>
                </Radio>
              </RadioGroup>
            </FormItem>
            <div v-if="addRuleForm.createTaskFlag != 0">
              <!-- <FormItem :label="$t('discover_select_type')" class="width_100_item">
                    <CheckboxGroup v-model="addRuleForm.deviceType">
                      <Checkbox v-for="item in deviceList" :label="item.value" :key="item.value">{{ item.lable }}</Checkbox>
                    </CheckboxGroup>

                  </FormItem> -->
              <typeComponent
                type="add"
                :data="deviceList"
                @sourceList="getSourceList"
                @nodeList="getNodeList"
              >
              </typeComponent>

              <FormItem :label="$t('discover_add_group')">
                <Select
                  v-model="addRuleForm.groupId"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('comm_select_group')"
                  style="width: 180px"
                >
                  <Option
                    v-for="item in groupList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem
                :label="$t('discover_maintain_level')"
                prop="maintainLevel"
                style="margin-left: -90px"
              >
                <Select
                  v-model="addRuleForm.maintainLevel"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('discover_select_maintain_level')"
                  style="width: 180px"
                >
                  <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.lable }}
                  </Option>
                </Select>
              </FormItem>
              <div class="divider">
                <div class="divider-title" style="widht: 90px">
                  {{ $t("comm_operation_scheduling") }}：
                </div>
                <div class="line"></div>
              </div>
              <div class="flx-com">
                <FormItem
                  :label="$t('comm_start_time')"
                  prop="startDate"
                  class="childContent width-auto"
                >
                  <DatePicker
                    type="date"
                    v-model="addRuleForm.startDate"
                    :placeholder="$t('comm_select_date2')"
                    class="myDateClass"
                    format="yyyy-MM-dd"
                    :options="addMolStartOpt"
                  ></DatePicker>
                </FormItem>
                <FormItem
                  label="—"
                  prop="endDate"
                  class="childContent width-auto notRequired"
                  :label-width="34"
                >
                  <DatePicker
                    type="date"
                    v-model="addRuleForm.endDate"
                    :placeholder="$t('comm_select_date2')"
                    class="myDateClass"
                    format="yyyy-MM-dd"
                    :options="addMolEndOpt"
                  ></DatePicker>
                </FormItem>
              </div>

              <FormItem
                :label="$t('comm_time_period')"
                class="childContent width_100_item"
                prop="timeList"
              >
                <div v-for="(item, index) in addRuleForm.timeList" :key="index">
                  <TimePicker
                    type="time"
                    v-model="item.start"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playStartTimeChange($event, index)"
                  ></TimePicker>
                  <span style="padding: 0 10px">—</span>
                  <TimePicker
                    type="time"
                    v-model="item.end"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playEndTimeChange($event, index)"
                  ></TimePicker>

                  <Icon
                    :type="
                      addRuleForm.timeList.length > 1
                        ? 'ios-remove-circle-outline'
                        : ''
                    "
                    class="iconItem"
                    :class="addRuleForm.timeList.length > 1 ? 'removeItem' : ''"
                    @click="speedTimeChangeRemove('addRuleForm', index)"
                  />
                  <Icon
                    :type="
                      index === addRuleForm.timeList.length - 1
                        ? 'ios-add-circle-outline'
                        : ''
                    "
                    class="iconItem"
                    :class="
                      index === addRuleForm.timeList.length - 1 ? 'addItem' : ''
                    "
                    @click="speedTimeChangeAdd('addRuleForm', index)"
                  />

                  <!-- <Icon
                    :type="addRuleForm.timeList.length > 1 && index === addRuleForm.timeList.length - 1 || addRuleForm.timeList.length === 1 ? 'ios-add-circle-outline' : 'ios-remove-circle-outline'"
                    class="iconItem ruleIcon"
                    :class="addRuleForm.timeList.length > 1 && index === addRuleForm.timeList.length - 1 || addRuleForm.timeList.length === 1 ? 'addItem' : 'removeItem'"
                    @click="taskTimeChange(index)" /> -->
                </div>
              </FormItem>
              <FormItem
                :label="$t('speed_recurrence')"
                class="childContent width_100_item"
                prop="repeatWeek"
              >
                <CheckboxGroup v-model="addRuleForm.repeatWeek">
                  <Checkbox :label="1">{{ $t("comm_monday") }}</Checkbox>
                  <Checkbox :label="2">{{ $t("comm_tuesday") }}</Checkbox>
                  <Checkbox :label="3">{{ $t("comm_wednesday") }}</Checkbox>
                  <Checkbox :label="4">{{ $t("comm_thursday") }}</Checkbox>
                  <Checkbox :label="5">{{ $t("comm_friday") }}</Checkbox>
                  <Checkbox :label="6">{{ $t("comm_saturday") }}</Checkbox>
                  <Checkbox :label="7">{{ $t("comm_sunday") }}</Checkbox>
                </CheckboxGroup>
              </FormItem>
              <p class="note">{{ $t("discover_time_note") }}</p>
            </div>
          </div>
        </div>
      </Form>

      <div slot="footer">
        <Button
          type="error"
          size="large"
          long
          @click="cancleForm('addRuleForm')"
          >{{ $t("common_cancel") }}</Button
        >
        <Button type="primary" size="large" long @click="addSubmit">{{
          $t("but_confirm")
        }}</Button>
      </div>
    </Modal>
    <!-- 修改弹框  -->
    <Modal
      :title="$t('common_update')"
      sticky
      v-model="editOpen"
      width="1000"
      :styles="{ top: '120px' }"
      draggable
      :mask="true"
      :mask-closable="false"
      @on-visible-change="editModalChange"
      class="operation-modal"
    >
      <Form
        ref="editForm"
        :model="editForm"
        :rules="addRuleFormRule"
        class="width_50_Form"
        @submit.native.prevent
        :label-width="130"
      >
        <FormItem :label="$t('comm_org') + $t('comm_colon')" prop="orgId">
          <div class="fn_item_box">
            <Select
              class="noIcon"
              v-model="editForm.orgId"
              filterable
              :only-filter-with-text="true"
              :disabled="true"
              :placeholder="$t('org_select_org')"
            >
              <Option
                v-for="item in orgLists"
                :value="item.id"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
            <div
              class="orgDiv"
              id="selectBox"
              v-show="orgTree"
              style="right: 0"
            >
              <div class="title" style="line-height: 32px">
                {{ $t("comm_mechanism_selection") }}
              </div>
              <div class="orgTreeScroll">
                <Tree
                  :data="treeData"
                  :load-data="loadData"
                  @on-select-change="setEditOrg"
                ></Tree>
              </div>
            </div>
          </div>
        </FormItem>

        <FormItem
          :label="$t('discover_rule_name') + $t('comm_colon')"
          prop="name"
        >
          <Input v-model="editForm.name" maxlength="50"></Input>
        </FormItem>
        <FormItem
          :label="$t('discover_gether_name') + $t('comm_colon')"
          prop="getherCode"
        >
          <Select
            v-model="editForm.getherCode"
            filterable
            :only-filter-with-text="true"
            clearable
            :disabled="true"
            :placeholder="$t('gether_select_collector')"
          >
            <Option
              v-for="item in gatherList"
              :value="item.code"
              :key="item.code"
              >{{ item.labelName }}
            </Option>
          </Select>
        </FormItem>
        <FormItem
          :label="$t('discover_dis_inter') + $t('comm_colon')"
          prop="intervalTime"
        >
          <div class="flex flex-between" style="float: left">
            <Input
              v-model.number="editForm.intervalTime"
              style="width: 280px; margin-right: 5px"
            ></Input>
          </div>
          <span>{{ $t("snmptask_hour") }}</span>
        </FormItem>
        <Row>
          <Col span="19">
            <FormItem
              :label="$t('comm_ip_range') + $t('comm_colon')"
              class="width_100_item ipBox"
              prop="ipStr"
            >
              <Input
                v-model="editForm.ipStr"
                :placeholder="$t('comm_ip_range')"
                maxlength="128"
              ></Input>
              <span>{{ $t("discover_ip_tips") }}</span>
              <!-- <div class="ipitemBox startIp">
              <label style="padding: 0 10px 0 0">{{ $t('comm_from') }}</label>
              <Input class="ipItem" v-model="editForm.ipOne" @input="pushIp(0, $event, 1)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="editForm.ipTwo" @input="pushIp(1, $event, 1)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="editForm.ipThree" @input="pushIp(2, $event, 1)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="editForm.startIpFour" @input="pushIp(3, $event, 1)"></Input>
            </div>
            <div class="ipitemBox endIp">
              <label style="padding: 0 10px 0 20px">{{ $t('comm_to') }}</label>
              <Input class="ipItem" v-model="editForm.ipOne" @input="pushIp(0, $event, 1)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="editForm.ipTwo" @input="pushIp(1, $event, 1)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="editForm.ipThree" @input="pushIp(2, $event, 1)"></Input><span
                class="ipitemspan">·</span>
              <Input class="ipItem" v-model="editForm.endIpFour" @input="pushIp(4, $event, 1)"></Input>
            </div> -->
            </FormItem>
          </Col>
          <Col span="2">
            <FormItem
              :label="$t('probe_dynamic')"
              prop="dynamicIp"
              :label-width="100"
            >
              <Checkbox
                style="margin-top: 8px"
                v-model="editForm.dynamicIp"
                :true-value="1"
                :false-value="0"
                :disabled="true"
              ></Checkbox>
            </FormItem>
          </Col>
        </Row>
        <FormItem
          :label="$t('phytopo_desc') + $t('comm_colon')"
          class="width_100_item"
          style="margin-top: 10px"
        >
          <Input
            type="textarea"
            v-model="editForm.describe"
            maxlength="200"
            :autosize="{ minRows: 5, maxRows: 5 }"
          ></Input>
        </FormItem>
        <Divider style="margin: 10px 0 30px 0" />
        <div>
          <div style="display: inline-block; width: 120px; float: left">
            <p
              style="
                text-align: right;
                line-height: 1;
                padding: 10px 12px 10px 0;
              "
            >
              {{ $t("discover_create_task") }}:
            </p>
          </div>
          <div style="margin-left: 120px">
            <FormItem label="" class="width_100_item">
              <RadioGroup
                v-model="editForm.createTaskFlag"
                @change="changeRuleAction()"
              >
                <Radio label="1">
                  <span>{{ $t("discover_create_task") }}</span>
                </Radio>
                <Radio label="0">
                  <span>{{ $t("discover_donot_create_task") }}</span>
                </Radio>
              </RadioGroup>
            </FormItem>
            <div v-if="editForm.createTaskFlag != 0">
              <!-- <FormItem :label="$t('discover_select_type')" class="width_100_item">
                  <CheckboxGroup v-model="editForm.deviceType">
                    <Checkbox v-for="item in deviceList" :label="item.value" :key="item.value">{{ item.lable }}</Checkbox>
                  </CheckboxGroup>
                </FormItem> -->
              <typeComponent
                type="edit"
                :data="deviceList"
                :sourceData="sourceData"
                :nodeData="nodeData"
                @sourceList="getSourceList"
                @nodeList="getNodeList"
              ></typeComponent>

              <FormItem :label="$t('discover_add_group')">
                <Select
                  v-model="editForm.groupId"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('comm_select_group')"
                  style="width: 180px"
                >
                  <Option
                    v-for="item in groupList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem
                :label="$t('discover_maintain_level')"
                prop="maintainLevel"
                style="margin-left: -90px"
              >
                <Select
                  v-model="editForm.maintainLevel"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('discover_select_maintain_level')"
                  style="width: 180px"
                >
                  <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.lable }}
                  </Option>
                </Select>
              </FormItem>
              <div class="divider">
                <div class="divider-title" style="width: 90px">
                  {{ $t("comm_operation_scheduling") }}：
                </div>
                <div class="line"></div>
              </div>
              <div class="flx-com">
                <FormItem
                  :label="$t('comm_start_time')"
                  prop="startDate"
                  class="childContent width-auto"
                >
                  <DatePicker
                    type="date"
                    v-model="editForm.startDate"
                    :placeholder="$t('comm_select_date2')"
                    class="myDateClass"
                    format="yyyy-MM-dd"
                    :options="editMolStartOpt"
                  ></DatePicker>
                </FormItem>
                <FormItem
                  label="—"
                  prop="endDate"
                  class="childContent width-auto notRequired"
                  :label-width="34"
                >
                  <DatePicker
                    type="date"
                    v-model="editForm.endDate"
                    :placeholder="$t('comm_select_date2')"
                    class="myDateClass"
                    format="yyyy-MM-dd"
                    :options="editMolEndOpt"
                  ></DatePicker>
                </FormItem>
              </div>

              <FormItem
                :label="$t('comm_time_period')"
                class="childContent width_100_item"
                prop="timeList"
              >
                <div v-for="(item, index) in editForm.timeList" :key="index">
                  <TimePicker
                    type="time"
                    v-model="item.start"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playStartTimeChange($event, index)"
                  ></TimePicker>
                  <span style="padding: 0 10px">—</span>
                  <TimePicker
                    type="time"
                    v-model="item.end"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playEndTimeChange($event, index)"
                  ></TimePicker>

                  <Icon
                    :type="
                      editForm.timeList.length > 1
                        ? 'ios-remove-circle-outline'
                        : ''
                    "
                    class="iconItem"
                    :class="editForm.timeList.length > 1 ? 'removeItem' : ''"
                    @click="speedTimeChangeRemove('editForm', index)"
                  />
                  <Icon
                    :type="
                      index === editForm.timeList.length - 1
                        ? 'ios-add-circle-outline'
                        : ''
                    "
                    class="iconItem"
                    :class="
                      index === editForm.timeList.length - 1 ? 'addItem' : ''
                    "
                    @click="speedTimeChangeAdd('editForm', index)"
                  />

                  <!-- <Icon
                    :type="editForm.timeList.length > 1 && index === editForm.timeList.length - 1 || editForm.timeList.length === 1 ? 'ios-add-circle-outline' : 'ios-remove-circle-outline'"
                    class="iconItem ruleIcon"
                    :class="editForm.timeList.length > 1 && index === editForm.timeList.length - 1 || editForm.timeList.length === 1 ? 'addItem' : 'removeItem'"
                    @click="taskTimeChange2(index)" /> -->
                </div>
              </FormItem>
              <FormItem
                :label="$t('speed_recurrence')"
                class="childContent width_100_item"
                prop="repeatWeek"
              >
                <CheckboxGroup v-model="editForm.repeatWeek">
                  <Checkbox :label="1">{{ $t("comm_monday") }}</Checkbox>
                  <Checkbox :label="2">{{ $t("comm_tuesday") }}</Checkbox>
                  <Checkbox :label="3">{{ $t("comm_wednesday") }}</Checkbox>
                  <Checkbox :label="4">{{ $t("comm_thursday") }}</Checkbox>
                  <Checkbox :label="5">{{ $t("comm_friday") }}</Checkbox>
                  <Checkbox :label="6">{{ $t("comm_saturday") }}</Checkbox>
                  <Checkbox :label="7">{{ $t("comm_sunday") }}</Checkbox>
                </CheckboxGroup>
              </FormItem>
              <p class="note">{{ $t("discover_time_note") }}</p>
            </div>
          </div>
        </div>
      </Form>

      <div slot="footer">
        <Button
          type="error"
          size="large"
          long
          @click="cancleForm('editForm')"
          >{{ $t("common_cancel") }}</Button
        >
        <Button type="primary" size="large" long @click="editSubmit">{{
          $t("but_confirm")
        }}</Button>
      </div>
    </Modal>
  </section>
</template>
<script>
//  机构网络监测 / 自动发现 / 发现规则
import validate from "@/common/validate";

/**
 * 时间转为秒
 * @param time 时间(00:00:00)
 * @returns {string} 时间戳（单位：秒）
 */
var time_to_sec = function (time) {
  var s = "";

  var hour = time.split(":")[0];
  var min = time.split(":")[1];
  var sec = time.split(":")[2] || 0;

  s = Number(hour * 3600) + Number(min * 60) + Number(sec);

  return s;
};
/**
 * 时间秒数格式化
 * @param s 时间戳（单位：秒）
 * @returns {*} 格式化后的时分秒
 */
var sec_to_time = function (s) {
  var t;
  if (s > -1) {
    var hour = Math.floor(s / 3600);
    var min = Math.floor(s / 60) % 60;
    // var sec = s % 60;
    if (hour < 10) {
      t = "0" + hour + ":";
    } else {
      t = hour + ":";
    }

    if (min < 10) {
      t += "0";
    }
    t += min;
    // if(sec < 10){t += "0";}
    // t += sec.toFixed(2);
  }
  return t;
};
import moment from "moment";
import "@/config/page.js";
import global from "../../../common/global.js";
import { mapGetters, mapActions, mapState } from "vuex";
import { addDraggable } from "@/common/drag.js";
import locationreload from "@/common/locationReload";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import langFn  from '@/common/mixins/langFn'
export default {
  name: "discoverRule",
    mixins: [langFn],
  components: {
    typeComponent: () => import('./typeComponent'),
    TreeSelect
  },
  props: {
    tabData: {
      type: String,
      default: "",
    },
  },
  watch: {
  },

  data() {
    // 验证之前
    const valiIp = (rule, value, callback) => {
      let notEmpty = true;
      for (let i = 0; i < 5; i++) {
        const item = value[i];
        if (item == undefined || item === '' || item == null) {
          notEmpty = false;
          break
        }
      }
      let startIp = '', endIp = '';
      if (notEmpty === false) {
        callback(new Error(this.$t('comm_ip_correct')))
      } else {
        const ipStartWith3 = value[0] + '.' + value[1] + '.' + value[2];
        startIp = ipStartWith3 + '.' + value[3];
        endIp = ipStartWith3 + '.' + value[4];
        const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if ((!reg.test(startIp)) || !reg.test(endIp)) {
          callback(new Error(this.$t('comm_ip_correct')));
        } else {
          if (value[4] - value[3] <= 0) {
            callback(new Error(this.$t('device_discovery_error_ip')));
          }
          callback();
        }
      }
    };
    const valTime = (rule, value, callback) => {
      console.log(rule)
      if (!value) {
        callback(rule.message);
      }
      callback();
    };


    // 验证 **********-200或2001:db8::1a2f:0001-1a2b 这种形式的IP
    const verifyIp = (rule, value, callback) => {

      debugger

      // 为空的情况
      if(value == null || value == '' || value == undefined){
           callback(new Error(this.$t('comm_ip_correct')))
           return
      }
      var indexOf = value.indexOf("-");
      if(indexOf <= 0){
          callback(new Error(this.$t('comm_ip_correct')))
          return
      }

      // 分隔判断
      var ipSegment = value.split("-");
       if(ipSegment.length != 2){
          callback(new Error(this.$t('comm_ip_correct')))
          return
      }

      // 先取出开头IP
      var startIp = ipSegment[0];
      if(startIp.indexOf(":") >=0){
        // ipV6的格式
        verifyIpV6(startIp,ipSegment[1],callback);
      }else{
        // ipv4的格式
        verifyIpV4(startIp,ipSegment[1],callback);
      }
    };


    // 验证 2001:db8::1a2f:0001-1a2b
    const verifyIpV4 = (startIp,endSegment,callback) => {

      // Ip地址验证
      const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
      if (!reg.test(startIp)) {
         callback(new Error(this.$t('comm_ip_correct')));
         return;
      }
      // 必须为数字格式 并且小于 255 大于等于 0 
      var result = Number(endSegment);
      if (isNaN(result) || result <= 0 || result > 255) {
         callback(new Error(this.$t('comm_ip_correct')));
         return;
      }
      // 不足四位进行补位
      var startArrays = startIp.split(".");
      var endIpSegment = [];;
      for(var i = 0 ; i < 3 ; i++){
          endIpSegment.push(startArrays[i]);
      }
      var endIpStr = endIpSegment.join(".")+"."+endSegment;
      console.log("结束Ip为：", endIpStr );
      if (!reg.test(endIpStr)) {
        callback(new Error(this.$t('comm_ip_correct')));
        return;
      }
       var startEndSegment = parseInt(startArrays[startArrays.length-1]);
      // 比较最后一位的大小
      if (result - startEndSegment <= 0) {
        callback(new Error(this.$t('device_discovery_error_ip')));
        return
      }
      callback();
    };

    // 验证 **********-200
    const verifyIpV6 = (startIp,endSegment,callback) => {

      // Ip地址验证
      const ipv6Regex = validate.getIpv6Regex();
      // /\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;
      if (!ipv6Regex.test(startIp)) {
         callback(new Error(this.$t('comm_ip_correct')));
         return;
      }
      var endSegmentRegex = /^[0-9a-fA-F]{1,4}$/;
      if (!endSegmentRegex.test(endSegment)) {
        callback(new Error(this.$t('comm_ip_correct')));
        return;
      }
      // 转换成16进制数字格式 并且小于 65535（2^{16} - 1） 大于等于 0 
      var result = parseInt(endSegment , 16);
      if (isNaN(result) || result <= 0 || result > 65535) {
         callback(new Error(this.$t('comm_ip_correct')));
         return;
      }
      // 先补全地址
      startIp = validate.expandIpv6(startIp);
      // 不足进行补位
      var startArrays = startIp.split(":");
      var endIpSegment = [];;
      for(var i = 0 ; i < 7 ; i++){
          endIpSegment.push(startArrays[i]);
      }
      var endIpStr = endIpSegment.join(":")+":"+endSegment;
      console.log("结束Ip为：", endIpStr );
      if (!ipv6Regex.test(endIpStr)) {
        callback(new Error(this.$t('comm_ip_correct')));
        return;
      }
      // 比较最后一位的大小
      var startEndSegment = parseInt(startArrays[startArrays.length-1] ,16);
      if (result - startEndSegment <= 0) {
        callback(new Error(this.$t('device_discovery_error_ip')));
        return
      }
      callback();
    };



    let _this = this;
    return {
     
      currentSkin: sessionStorage.getItem('dark') || 1,
      treeValueQuery:'',
      treeValue: '',
      pageLoading: true,
      treeData: [],
      orgTree: false,
      orgLists: [],
      sourceList: [],
      nodeList: [],
      nodeData: [],
      sourceData: [],
      orgIds: '',
      isdarkSkin: top.window.isdarkSkin,
      /*權限*/
      permissionObj: {},
      /* 参数列表 */
      //规则
      status: null,
      //规则列表
      statusList: [
        { value: 0, label: this.$t('comm_enable') },
        { value: 1, label: this.$t('common_disable') },
      ],
      //关键字
      keyWord: "",
      //分页参数
      page: {
        pageNo: 1,
        pageSize: 10,
        pageSizeOpts: [10, 50, 100, 200, 500, 1000],
        pageLoading: false,
      },
      /** 列表数据 */
      pageData: {
        total: 0,
        list: [],
      },
      /** 列表选中数据 */
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      selectedDatas: [],
      /** 新建表单 */
      addRuleForm: {
        orgId: '',
        name: '',
        getherCode: null,
        ipArr: [null, null, null, null, null],
        ipOne: '',
        ipTwo: '',
        ipThree: '',
        startIpFour: '',
        endIpFour: '',
        intervalTime: 24,
        describe: '',
        deviceType: [],
        groupId: null,
        dynamicIp: 0,
        ipStr:"",
        maintainLevel: '1',
        startDate: null,
        endDate: null,
        timeList: [{ start: "0000", end: "2359" }],
        repeatWeek: [1, 2, 3, 4, 5, 6, 7],
        createTaskFlag: '0',
      },
      //新建加载动画
      addLoading: true,
      //分组数据列表
      groupList: [],
      //运维等级数据列表
      maintainList: [],
      //采集器数据列表
      gatherList: [],
      //设备列表
      deviceList: [],
      //新建弹框
      addOpen: false,
      //新建表单验证规则
      addRuleFormRule: {
        orgId: [
          { required: true, type: "number", message: this.$t('org_select_org'), trigger: "change", },
        ],
        name: [
          { required: true, type: "string", message: this.$t('discover_pd_rule_name'), trigger: "blur", },
        ],
        getherCode: [
          { required: true, type: "string", message: this.$t('gether_select_collector'), trigger: "change", },
        ],
        // ipArr: [
        //   // {required: true, type: "array", message: "请填写IP段", trigger: "blur",},
        //   // { trigger: 'blur', length: 32, validator: valiIp }
        //   { trigger: 'blur', length: 128, validator: verifyIp }
        // ],
        ipStr: [
          { trigger: 'blur', length: 128, validator: verifyIp }
        ],
        intervalTime: [
          { required: true, type: "number", message: this.$t('discover_select_inter_time'), trigger: "blur", },
          { validator: validate.isInteger, min: 1, max: 100, trigger: "blur", isInteger: true, },
          { validator: validate.checkMax, min: 1, max: 100, trigger: "blur", isInteger: true, }
        ],
        deviceType: [{ required: true, type: "array", message: this.$t('snmpoid_select_type'), trigger: "change", },],
        groupId: [
          { required: true, type: "number", message: this.$t('comm_select_group'), trigger: "change", },
        ],
        maintainLevel: [
          { required: true, type: "string", message: this.$t('discover_select_maintain_level'), trigger: "change", },
        ],
        startDate: [
          { required: true, type: "date", validator: valTime, message: this.$t('comm_start_time'), trigger: "change", pattern: /.+/ },
        ],
        endDate: [
          { required: true, type: "date", validator: valTime, message: this.$t('comm_end_time'), trigger: "change", pattern: /.+/ },
        ],
        timeList: [{ required: true, type: "array", message: this.$t('discover_date_rang'), trigger: "change", },],
        repeatWeek: [
          { required: true, type: "array", message: this.$t('comm_select_cycle'), trigger: "change", },
        ],
      },
      /** 修改表单 */
      editForm: {
        id: null,
        orgId: '',
        name: '',
        getherCode: null,
        ipArr: [null, null, null, null, null],
        ipOne: '',
        ipTwo: '',
        ipThree: '',
        startIpFour: '',
        dynamicIp: 0,
        endIpFour: '',
        intervalTime: '',
        describe: '',
        deviceType: [],
        groupId: null,
        maintainLevel: null,
        startDate: null,
        endDate: null,
        ipStr:"",
        timeList: [{ start: "0000", end: "2359" }],
        repeatWeek: [1, 2, 3, 4, 5, 6, 7],
        createTaskFlag: '0'
      },
      //修改加载动画
      editLoading: true,

      //修改弹框
      editOpen: false,
      /** 表格标题列 */
      tableColumn: [
        { type: "selection", width: 30, className: "bgColor", align: "center" },
        { title: this.$t('discover_rule_name'), align: "left", minWidth: 200, key: "name", tooltip: true },
        {
          title: this.$t('access_ip_rang'), align: "left", minWidth: 200, key: "startIp",
          render: (h, param) => {
            return h('span', param.row.showStartIp + ' - ' + param.row.showEndIp)
          }
        },
        { title: this.$t('testspeed_org'), align: "left", width: 110, key: "orgName", tooltip: true },
        { title: this.$t('discover_gether_name'), align: "left",width:110, key: "getherName", tooltip: true },
        { 
          title: this.$t('discovery_Progress'), 
          align: "left", 
          minWidth: 120, 
          key: "discoveryProgress",
          //  render: this.renderDiscoveryProgress
          slot: "discoveryProgress",
        },
        {
          title: this.$t('comm_status'), align: "left", width:  this.getColumnWidth(90,100), key: "status",
          render: (h, param) => {
            return h('span', { style: { color: param.row.status == 1 ? '#C0C4CC' : '#19be6b', }, }, param.row.status == 1 ? this.$t('common_disable') : this.$t('comm_enable'))
          }
        },
        { title: this.$t('net_rule_discover_scantime'), align: "left", width: 180, key: "lastUpdateTime" },
        { title: this.$t('net_rule_discover_completiontime'), align: "left", width: 210, key: "lastCompletionTime" },
        { title: this.$t('comm_operate'),align: "center", width: 120, slot: "action"},
      ],
      addMolStartOpt: {
        disabledDate: date => {
          if (_this.addRuleForm.endDate) {
            let end = new Date(_this.addRuleForm.endDate);
            return date > end;
          } else {
            return date > Date.now();
          }
        }
      },
      addMolEndOpt: {
        disabledDate(date) {
          if (_this.addRuleForm.startDate) {
            let end = new Date(_this.addRuleForm.startDate);
            return date < end;
          } else {
            return date > Date.now();
          }
        }
      },
      editMolStartOpt: {
        disabledDate: date => {
          if (_this.editForm.endDate) {
            let end = new Date(_this.editForm.endDate);
            return date > end;
          } else {
            return date > Date.now();
          }
        }
      },
      editMolEndOpt: {
        disabledDate(date) {
          if (_this.editForm.startDate) {
            let end = new Date(_this.editForm.startDate);
            return date < end;
          } else {
            return date > Date.now();
          }
        }
      }
    };
  },
  mounted() {
    //  this.$Skin.skinChange(top.window.skin)
    top.window.addEventListener("message", (e) => {
      if (e) {
        if (e.data.type == 'msg') {
          return;
        } else if (typeof e.data == 'object') {
          this.isdarkSkin = e.data.isdarkSkin;
          this.$Skin.skinChange(top.window.skin)
        } else if (typeof e.data == 'number') {
          this.isdarkSkin = e.data;
          this.$Skin.skinChange(top.window.skin)
        }

      }
    });
    //机构弹框  点击外部 关闭
    // top.document.addEventListener("click", e => {
    //   var box = top.document.getElementById('selectBox');
    //   console.log(box);
    //   if (box && box.contains(e.target)) {
    //   } else {
    //     this.orgTree = false;
    //   }
    // })
    // 30秒定时更新数据
    this.timerInterval = setInterval(() => {
        let pageNo = this.page.pageNo|| 1;
        this.queryClick(pageNo);
        }, 1000 * 30);
     // 监听 storage 事件
     window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      
        top.window.removeEventListener("message", () => {})
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
  },
  created() {
    // console.log(11111111111111111111111)
    this.$nextTick(() => {
      locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    })
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    const orgId = JSON.parse(sessionStorage.getItem('accessToken')).user.orgId
    moment.locale("zh-cn");
    // this.getGroupList();
    this.getDeviceList();
    this.getmaintainLevelList();
    this.queryClick(1);
    this.groupGroupList();
    this.getTreeOrg()
    // 新增 机构 默认选中
                let token = JSON.parse(sessionStorage.getItem("accessToken"));
                if (token && token.user) {
                this.treeValue = token.user.orgName;
                console.log(this.treeValue,'this.treeValue')
                this.addRuleForm.orgId = token.user.orgId
                this.getGetherList(token.user.orgId,1)
                this.addRuleForm.getherCode =  this.getherCode
                // console.log(this.addRuleForm)
               
                
                
                }
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    getActive(row) {
      let discoveryProgress = row.discoveryProgress || 0; // 默认值为0
      let identifyProgress = row.identifyProgress || 0; // 默认值为0
      let status = row.status; // 发现状态0：启用 1：禁用
      let isActive = false
     
      
      let issuanceStatus = row.issuanceStatus||0;
      let progress = 0;
      if(1 == issuanceStatus){
       
        progress = discoveryProgress;
      }else if(2 == issuanceStatus){
       
        progress = 100;
        if(1 == status){
          let beforeIssuanceStatus = row.beforeIssuanceStatus ||0;//禁止之前的下发状态
          if(1 == beforeIssuanceStatus){
            progress = discoveryProgress;          
          }else if(4 == beforeIssuanceStatus){
            progress = identifyProgress;
          }else{
            if(100 != discoveryProgress){
            progress = discoveryProgress;
            }
            if(100 != identifyProgress && 0 != identifyProgress){
              progress = identifyProgress;
            }
          }
        }else{
          if('120' == discoveryProgress || '120' == identifyProgress){
             progress = 120;
          }
        }
      }else if(3 == issuanceStatus){
        progress = discoveryProgress;
      }else if(4 == issuanceStatus){
      
        progress = identifyProgress;
      }
       
        if(progress<100){
          // param.row.status 1 禁用 没有动画效果
         
        
        if(status == 1) {

        }else {
          isActive = true;
        }


         
        }
        return isActive

      

    },
    formateText(row) {
       let discoveryProgress = row.discoveryProgress || 0; // 默认值为0
      let identifyProgress = row.identifyProgress || 0; // 默认值为0
      let status = row.status; // 发现状态0：启用 1：禁用
      let showDisableProgress = row.showDisableProgress; // 是否显示禁用进度条，0否，1是
      let text = ''
      // if(status==1){
      //   discoveryProgress = 0;
      //   identifyProgress = 0;
      // }
      
      let issuanceStatus = row.issuanceStatus||0;
      let beforeIssuanceStatus = row.beforeIssuanceStatus ||0;
      let progress = 0;
      let issuanceStatusName = "";
      if(1 == issuanceStatus){
        issuanceStatusName=this.$t('net_data_discover_discovery_in_progress');
        progress = discoveryProgress;
      }else if(2 == issuanceStatus){
        issuanceStatusName = "";
        progress = 100;
        if(1 == status){
          if(1 == beforeIssuanceStatus){
            progress = discoveryProgress;          
          }else if(4 == beforeIssuanceStatus){
            progress = identifyProgress;
          }else{
            if(100 != discoveryProgress){
            progress = discoveryProgress;
            }
            if(100 != identifyProgress && 0 != identifyProgress){
              progress = identifyProgress;
            }
          }
          if(1 == beforeIssuanceStatus){
            issuanceStatusName=this.$t('net_data_discover_discovery_in_progress');
          }else if(4 == beforeIssuanceStatus){
            issuanceStatusName=this.$t('net_data_discover_identify_in_progress');
          }
        }else{
          if(1 == showDisableProgress){
            if(1 == beforeIssuanceStatus){
              progress = discoveryProgress;
              issuanceStatusName=this.$t('net_data_discover_discovery_in_progress');
            }else if(4 == beforeIssuanceStatus){
              progress = identifyProgress;
              issuanceStatusName=this.$t('net_data_discover_identify_in_progress');
            }else{
              if(100 != discoveryProgress){
              progress = discoveryProgress;
              }
              if(100 != identifyProgress && 0 != identifyProgress){
                progress = identifyProgress;
              }
            }
          }else{
            if('120' == discoveryProgress || '120' == identifyProgress){
             progress = 120;
             }
          }
        }
      }else if(3 == issuanceStatus){
        progress = discoveryProgress;
      }else if(4 == issuanceStatus){
        issuanceStatusName=this.$t('net_data_discover_identify_in_progress');
        progress = identifyProgress;
      }
        let overTime = this.$t('net_data_discover_overtime');
        // debugger
        if(progress<100){
          // param.row.status 1 禁用 没有动画效果
          // issuanceStatusName+
        
        text = issuanceStatusName + progress + '%';


         
        }else if(progress==100){
          console.log('进度条100')
          text = progress + '%';
        }else{
          text = overTime;
           
        }
        return text

    },
    getStyle(row) {
      console.log(row,row)
      let obj = {
        backgroundColor: 'red',
        width:'50%',

      }
        let discoveryProgress = row.discoveryProgress || 0; // 默认值为0
      let identifyProgress = row.identifyProgress || 0; // 默认值为0
      let status = row.status; // 发现状态0：启用 1：禁用
      let showDisableProgress = row.showDisableProgress; // 是否显示禁用进度条，0否，1是
      // if(status==1){
      //   discoveryProgress = 0;
      //   identifyProgress = 0;
      // }
      let beforeIssuanceStatus = row.beforeIssuanceStatus ||0;//禁止之前的下发状态
      let issuanceStatus = row.issuanceStatus||0;
      let progress = 0;
      let issuanceStatusName = "";
      if(1 == issuanceStatus){
        issuanceStatusName=this.$t('net_data_discover_discovery_in_progress');
        progress = discoveryProgress;
      }else if(2 == issuanceStatus){
        issuanceStatusName = "";
        progress = 100;
        if(1 == status){
          
          if(1 == beforeIssuanceStatus){
            progress = discoveryProgress;          
          }else if(4 == beforeIssuanceStatus){
            progress = identifyProgress;
          }else{
            if(100 != discoveryProgress){
            progress = discoveryProgress;
            }
            if(100 != identifyProgress && 0 != identifyProgress){
              progress = identifyProgress;
            }
          }
        }else{
          if(1 == showDisableProgress){
            if(1 == beforeIssuanceStatus){
              progress = discoveryProgress;
            }else if(4 == beforeIssuanceStatus){
              progress = identifyProgress;
            }else{
              if(100 != discoveryProgress){
              progress = discoveryProgress;
              }
              if(100 != identifyProgress && 0 != identifyProgress){
                progress = identifyProgress;
              }
            }
          }else{
            if('120' == discoveryProgress || '120' == identifyProgress){
             progress = 120;
             }
          }
        }
      }else if(3 == issuanceStatus){
        progress = discoveryProgress;
      }else if(4 == issuanceStatus){
        issuanceStatusName=this.$t('net_data_discover_identify_in_progress');
        progress = identifyProgress;
      }
        let overTime = this.$t('net_data_discover_overtime');
        // debugger
        if(progress<100){
          // param.row.status 1 禁用 没有动画效果
          // issuanceStatusName+
        
            if(row.status === 1) {
              //  return (<Progress percent={progress} stroke-width={16}   text-inside />);
              obj.backgroundColor = '#2d8cf0'
              obj.width = progress+'%'

            }else {
              if(1 == showDisableProgress){
               obj.backgroundColor = '#2d8cf0'
               obj.width = progress+'%'
              }else{
                //  return (<Progress percent={progress} stroke-width={16} status="active"  text-inside />);
                obj.backgroundColor = '#2d8cf0'
                obj.width = progress+'%'
              }

            }

         
        }else if(progress==100){
          console.log('进度条100')
          // return (<Progress percent={100} stroke-width={16} text-inside />);
             obj.backgroundColor = '#19be6b'
              obj.width = '100%'
        }else{
          // return (<Progress percent={overTime}  stroke-width={16} status="wrong" text-inside />);
//           return h("div",{style:{
//             width:"100%",
//             height:"16px",
//             borderRadius:'8px',
//             background:'red',
//             fontSize:'12px',
//             textAlign:'center',
//             lineHeight:'16px'
// }
//           },overTime)
            obj.backgroundColor = '#ed4014'
             obj.width = '100%'
        }
         return obj
      
      // return '20%'
    },
    renderDiscoveryProgress(h, { row }) {
      let discoveryProgress = row.discoveryProgress || 0; // 默认值为0
      let identifyProgress = row.identifyProgress || 0; // 默认值为0
      let status = row.status; // 发现状态0：启用 1：禁用
      // if(status==1){
      //   discoveryProgress = 0;
      //   identifyProgress = 0;
      // }
      
      let issuanceStatus = row.issuanceStatus||0;
      let progress = 0;
      let issuanceStatusName = "";
      if(1 == issuanceStatus){
        issuanceStatusName=this.$t('net_data_discover_discovery_in_progress');
        progress = discoveryProgress;
      }else if(2 == issuanceStatus){
        issuanceStatusName = "";
        progress = 100;

        if('120' == discoveryProgress || '120' == identifyProgress){
          progress = 120;
        }
      }else if(3 == issuanceStatus){
        progress = discoveryProgress;
      }else if(4 == issuanceStatus){
        issuanceStatusName=this.$t('net_data_discover_identify_in_progress');
        progress = identifyProgress;
      }
        let overTime = this.$t('net_data_discover_overtime');
        // debugger
        if(progress<100){
          // param.row.status 1 禁用 没有动画效果
          // issuanceStatusName+
        
            if(row.status === 1) {
               return (<Progress percent={progress} stroke-width={16}   text-inside />);

            }else {
               return (<Progress percent={progress} stroke-width={16} status="active"  text-inside />);

            }

         
        }else if(progress==100){
          console.log('进度条100')
          // return (<Progress percent={100} stroke-width={16} text-inside />);
        }else{
          return (<Progress percent={overTime}  stroke-width={16} status="wrong" text-inside />);
          return h("div",{style:{
            width:"100%",
            height:"16px",
            borderRadius:'8px',
            background:'red',
            fontSize:'12px',
            textAlign:'center',
            lineHeight:'16px'
            }
          },overTime)
        }
    },
    setOrgQuery(item) {
      console.log(item);
      this.treeValueQuery = item[0].name
      this.orgIdQuery = item[0] ? item[0].id : null;
    },
    focusFnQuery() {
      this.getTreeOrg()
    }, 
    onClearQuery() {
      this.orgIdQuery = ''
      this.treeValueQuery = ''
    },
    moreBtnClick(val) {
      eval(`this.${val}`)
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    getGether(e) {
      if(e) {
         this.getGetherList(this.addRuleForm.orgId)
          // this.addRuleForm.getherCode =  this.getherCode


      }
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.addRuleForm.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
      // 选择机构后，动态更新采集器数据
      // this.$refs.getherClear.clearSingleSelect()
      this.getGetherList(this.addRuleForm.orgId, item[0])
       this.addRuleForm.getherCode =  this.getherCode
      this.groupGroupList(this.addRuleForm.orgId)
    },
      focusFn() {
      this.getTreeOrg()
    }, 
    onClear() {
      this.addRuleForm.orgId = ''
      this.treeValue = ''
    },
    setEditOrg(item) {
      this.editForm.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
      // 选择机构后，动态更新采集器数据
      this.getGetherList(this.editForm.orgId, item[0])
    },
    choicesOrg() {
      this.orgTree = true;
    },

    //查询事件
    queryClick(pageNo) {
      this.keyWord = this.keyWord.trim();
      //初始化页码
      this.page.pageNo = pageNo;
      //设置查询参数
      const queryParam = {
        status: this.status,
        keyword: this.keyWord,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        orgId:this.orgIdQuery
      };
      //打开加载动画
      this.pageLoading = true;

      //请求数据
      this.getTableList(queryParam);
    },
    //获取列表数据
    getTableList(queryParam) {
      this.$http
        .wisdomPost("/netDiscoverRule/list", queryParam)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if (data) {
              this.pageData.list = data.records;
              this.pageData.total = data.total || 0;
            } else {
              this.setTableListEmpty();
            }
          } else {
            this.setTableListEmpty();
            this.$Message.warning({ content: msg, background: true });
          }
        })
        .catch((error) => {
          this.setTableListEmpty();
        })
        .finally(() => {
          //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
          let _that = this;
          setTimeout(() => {
            let objData = this.$refs.tablelist.$refs.tbody.objData;
            for (let key in objData) {
              if (this.selectedIds.has(objData[key].id)) {
                objData[key]._isChecked = true;
              }
            }
          }, 0);
          this.pageLoading = false;
        });
    },
    //设置列表数据为空列表
    setTableListEmpty() {
      this.pageData.list = [];
      this.pageData.total = 0;
    },
    // 获取源到设备数据
    getSourceList(val) {
      this.sourceList = []
      val.forEach(item => {
        this.sourceList.push(item.value)
      })
    },
    // 获取源到设备上联节点数据
    getNodeList(val) {
      this.nodeList = []
      val.forEach(item => {
        this.nodeList.push(item.value)
      })
    },
    groupGroupList(orgId) {
      this.$http
        .wisdomPost("/group/groupList", { orgId: orgId })
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.groupList = data;
          }
        })
        .catch((err) => {
          throw new Error(err);
        })
    },
    /** 新建 */
    //打开新建表单
    async openAddForm() {
       
      this.addOpen = true
      this.getDeviceList();
      this.groupGroupList();
      // this.getGetherList();// 获取采集器数据
      this.$nextTick(async () => {
        // this.$refs["addRuleForm"].resetFields();
        this.orgLists = this.treeData
        // this.treeValue = ''
        this.addRuleForm = {
          name: '',
          getherCode: this.getherCode,
          ipArr: [null, null, null, null, null],
          ipOne: '',
          ipTwo: '',
          ipThree: '',
          orgId:this.addRuleForm.orgId,
          startIpFour: '',
          endIpFour: '',
          dynamicIp: 0,
          intervalTime: 24,
          describe: '',
          deviceType: [],
          maintainLevel: '1',
          groupId: null,
          ipStr:"",
          startDate: new Date().format('yyyyMMdd'),
          endDate: new Date('2099-01-01').format('yyyyMMdd'),
          timeList: [{ start: "0000", end: "2359" }],
          repeatWeek: [1, 2, 3, 4, 5, 6, 7],
          createTaskFlag: '0',
        }
        
      })



    },

    getIps(valeIp){
      var startIp = "";
      var endIp = "";
       // 分隔判断
      var ipSegment = valeIp.split("-");
      if(ipSegment.length != 2){
         return [startIp,endIp];
      }
       // 先取出开头IP
      var startIp = ipSegment[0];
      var separator = ".";
      if(startIp.indexOf(":") >=0){
        // ipV6的格式
        separator = ":";
      }else{
        separator = ".";
        // ipv4的格式
      }
      // 得到最后的一个 .或者: 的位置
      var ipPrevIndex =  startIp.lastIndexOf(separator);
      endIp = startIp.substring(0,ipPrevIndex) + separator + ipSegment[1];

      console.log("原始IP = " , valeIp);
      console.log("startIp = " , startIp);
      console.log("endIp = " , endIp );

      return [startIp,endIp];
    },
    //新建请求接口
    addSubmit() {
      console.log(this.addRuleForm,'addRuleForm')
      // console.log(this.$Message)
      // this.$Message.success('成功')
      // debugger 
      this.$refs["addRuleForm"].validate((validate) => {
        if (validate) {
          //时间段验证
          const ruleTimeFlag = this.ruleTimes(this.addRuleForm.timeList);
          if (ruleTimeFlag === false) {
            this.$Message.warning({ content: this.$t('comm_tip3'), background: true });
            //取消按钮加载效果
            this.addLoading = false;
            this.$nextTick(() => {
              this.addLoading = true;
            });
            return
          } else if (ruleTimeFlag) {
            const ipArr = this.addRuleForm.ipArr;
            var startDate = '';
            var endDate = '';
            var deviceTypeParam = '';
            var lastDeviceTypeParam = ''
            var groupIdParam = '';
            var maintainLevelParam = '';
            var repeatWeekParam = '';
            var timeSlotParam = '';
            var orgId = '';
            var createTaskFlagTemp = this.addRuleForm.createTaskFlag;
            if (1 == createTaskFlagTemp) {//创建任务
              if (this.nodeList.length == 0 && this.sourceList.length == 0) {
                this.addLoading = false;
                this.$Message.warning({ content: this.$t('discoverrule_no_select_device'), background: true });
                return
              }
              startDate = new Date(this.addRuleForm.startDate).format('yyyyMMdd');
              endDate = new Date(this.addRuleForm.endDate).format('yyyyMMdd');
              deviceTypeParam = this.sourceList.join(',');
              lastDeviceTypeParam = this.nodeList.join(',');
              groupIdParam = this.addRuleForm.groupId;
              maintainLevelParam = this.addRuleForm.maintainLevel;
              repeatWeekParam = this.addRuleForm.repeatWeek.join(",");
              timeSlotParam = this.addRuleForm.timeList
                .map((item) => {
                  return item.start.replace(':', '') + "-" + item.end.replace(':', '');
                })
                .join(",");
            }
            // 拼接ip
            var ips = this.getIps(this.addRuleForm.ipStr);
            const addParam = {
              name: this.addRuleForm.name,
              getherCode: this.addRuleForm.getherCode,
              // startIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[3],
              // endIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[4],
              startIp: ips[0],
              endIp: ips[1],
              intervalTime: this.addRuleForm.intervalTime,
              describe: this.addRuleForm.describe,
              deviceType: deviceTypeParam,
              lastDeviceType: lastDeviceTypeParam,
              groupId: groupIdParam,
              maintainLevel: maintainLevelParam,
              dynamicIp: this.addRuleForm.dynamicIp,
              startDate: startDate,
              endDate: endDate,
              repeatWeek: repeatWeekParam,
              createTaskFlag: this.addRuleForm.createTaskFlag,
              timeSlot: timeSlotParam,
              orgId: this.addRuleForm.orgId,
            };
            this.$http
              .wisdomPost("/netDiscoverRule/add", addParam)
              .then(({ code, data, msg }) => {
                if (code === 1) {
                  this.$Message.success({ content: this.$t('device_discovery_create_success'), background: true });
                  this.addOpen = false;
                  this.$refs["addRuleForm"].resetFields();
                } else {
                  console.log(msg);
                  this.$Message.warning({ content: msg, background: true });
                }
              })
              .catch((err) => {
                throw new Error(err);
              })
              .finally(() => {
                //取消按钮加载效果
                this.addLoading = false;
                this.$nextTick(() => {
                  this.addLoading = true;
                });
                this.queryClick(1);
              });
          }
        } else {
          //取消按钮加载效果
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
        }
      })
    },
    editModalChange(val) {
      if (!val) {
        this.sourceData = []
        this.nodeData = []
        //  this.deviceList= []
        this.editForm.createTaskFlag = 0
      }
    },
    // 转换IPs
    convertIps(startIp,endIp){
      var startIpStr= "";
      var endIpStr= "";
      // 先取出开头IP
      var separator = ".";
      // 判断
       if(startIp.indexOf(":") >=0){
        // ipV6的格式
        separator = ":";
      }else{
        separator = ".";
        // ipv4的格式
      }
      // 得到最后的一个 .或者: 的位置
      var ipPrevIndex =  startIp.lastIndexOf(separator);
      startIpStr = startIp.substring(0,ipPrevIndex);

      // 得到结束Ip的最后一段
      ipPrevIndex =  endIp.lastIndexOf(separator);
      endIpStr = endIp.substring(ipPrevIndex+1);

      return startIp +"-"+endIpStr;
    },
    /** 编辑 */
    //打开编辑表单
    async openEditForm(param) {
      this.orgLists = []
      this.gatherList = []
      this.sourceData = []
      this.nodeData = []
      this.$refs["editForm"].resetFields();
      this.$http
        .wisdomPost("/netDiscoverRule/getDetail", { id: param.id }).then(({ code, data, msg }) => {
          if (code == 1) {
            this.orgLists.push({
              id: data.orgId,
              name: param.orgName,
            })
            this.gatherList.push({
              code: data.getherCode,
              labelName: param.getherName,
            })

            // const endIpLast = data.endIp ? (data.endIp.split('.')[3] >= 0 ? data.endIp.split('.')[3] : null) : null;
            // const ipArr = data.startIp ? data.startIp.split('.').concat(endIpLast) : [null, null, null, null, endIpLast];

            const timeList = data.timeSlot ? (data.timeSlot.split(',').map(item => {
            const itemArr = item.split('-');
              return { start: itemArr[0], end: itemArr[1] }
            })) : [{ start: '0000', end: '2359' }];
            this.editForm.orgId = data.orgId;
            this.editForm.id = data.id;
            this.editForm.name = data.name;
            this.editForm.dynamicIp = data.dynamicIp;
            this.editForm.getherCode = data.getherCode;
            this.editForm.ipStr = this.convertIps(data.startIp,data.endIp);
            // this.editForm.ipArr = ipArr;
            // this.editForm.ipOne = ipArr[0];
            // this.editForm.ipTwo = ipArr[1];
            // this.editForm.ipThree = ipArr[2];
            // this.editForm.startIpFour = ipArr[3];
            // this.editForm.endIpFour = ipArr[4];
            this.editForm.intervalTime = data.intervalTime || '';
            this.editForm.describe = data.describe;
            //this.editForm.deviceType = data.deviceType ? data.deviceType.split(',').map(Number) : [];
            this.sourceData = data.deviceType ? data.deviceType.split(',') : [];
            this.nodeData = data.lastDeviceType ? data.lastDeviceType.split(',') : [];
            // this.editForm.deviceType = data.deviceType ? data.deviceType.split(',') : [];
            this.editForm.groupId = parseInt(data.groupId);
            this.editForm.maintainLevel = data.maintainLevel ? (data.maintainLevel + '') : '1';
            this.editForm.startDate = data.startDate ? String(data.startDate).replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3") : new Date().format('yyyyMMdd');
            this.editForm.endDate = data.endDate ? String(data.endDate).replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3") : new Date('2099-01-01').format('yyyyMMdd');
            this.editForm.timeList = timeList ? timeList : [{ start: "0000", end: "2359" }];
            this.editForm.createTaskFlag = data.createTaskFlag ? String(data.createTaskFlag) : '0';
            this.editForm.repeatWeek = data.repeatWeek ? data.repeatWeek.split(',').map(Number) : [1, 2, 3, 4, 5, 6, 7];
            this.getGetherList(data.orgId);// 获取当前组织下的采集器数据
            this.groupGroupList(data.orgId);// 获取当前组织下的分组信息
            this.editOpen = true;

            this.$nextTick(() => {
              this.editLoading = true;
            });
          } else {
            this.$Message.warning({ content: msg, background: true });
          }
        }).catch((error) => {
          throw new Error(error)
        })
    },
    //修改请求接口
    editSubmit() {
      this.$refs["editForm"].validate(validate => {
        if (validate) {
          //时间段验证
          const ruleTimeFlag = this.ruleTimes(this.editForm.timeList);
          console.log('ruleTimeFlag', ruleTimeFlag);
          if (ruleTimeFlag === false) {
            this.$Message.warning({ content: this.$t('comm_tip3'), background: true });
            //取消按钮加载效果
            this.editLoading = false;
            this.$nextTick(() => {
              this.editLoading = true;
            });
            return
          } else if (ruleTimeFlag) {
            const ipArr = this.editForm.ipArr;
            console.log(this.editForm.endDate)
            var startDate = '';
            var endDate = '';
            var deviceTypeParam = '';
            var lastDeviceTypeParam = ''
            var groupIdParam = '';
            var maintainLevelParam = '';
            var repeatWeekParam = '';
            var timeSlotParam = '';
            var createTaskFlagTemp = this.editForm.createTaskFlag;
            console.log('createTaskFlagTemp', createTaskFlagTemp);
            if (1 == createTaskFlagTemp) {//创建任务
              if (this.nodeList.length == 0 && this.sourceList.length == 0) {
                this.editLoading = false;
                this.$Message.warning({ content: this.$t('discoverrule_no_select_device'), background: true });
                return
              }
              startDate = new Date(this.editForm.startDate).format('yyyyMMdd');
              endDate = new Date(this.editForm.endDate).format('yyyyMMdd');
              deviceTypeParam = this.sourceList.join(',');
              lastDeviceTypeParam = this.nodeList.join(',')
              if (this.editForm.groupId) {
                groupIdParam = this.editForm.groupId;
              }
              maintainLevelParam = this.editForm.maintainLevel;
              repeatWeekParam = this.editForm.repeatWeek.join(",");
              timeSlotParam = this.editForm.timeList
                .map((item) => {
                  return item.start.replace(':', '') + "-" + item.end.replace(':', '');
                })
                .join(",");
            }
            var ips = this.getIps(this.editForm.ipStr);
            const editParam = {
              id: this.editForm.id,
              name: this.editForm.name,
              getherCode: this.editForm.getherCode,
              // startIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[3],
              // endIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[4],

              startIp: ips[0],
              endIp:ips[1],
              intervalTime: this.editForm.intervalTime || 1,
              describe: this.editForm.describe,
              deviceType: deviceTypeParam,
              lastDeviceType: lastDeviceTypeParam,
              groupId: groupIdParam,
              maintainLevel: maintainLevelParam,
              startDate: startDate,
              endDate: endDate,
              repeatWeek: repeatWeekParam,
              createTaskFlag: this.editForm.createTaskFlag,
              timeSlot: timeSlotParam,
              orgId: this.editForm.orgId,
            };
            this.$http
              .wisdomPost("/netDiscoverRule/update", editParam)
              .then(({ code, data, msg }) => {
                if (code === 1) {
                  this.$Message.success({ content: this.$t('comm_changed_successful'), background: true });
                  this.editOpen = false;
                } else {
                  this.$Message.warning({ content: msg, background: true });
                }
              })
              .catch((err) => {
                throw new Error(err);
              })
              .finally(() => {
                //取消按钮加载效果
                this.editLoading = false;
                this.$nextTick(() => {
                  this.editLoading = true;
                });
                this.queryClick(this.page.pageNo);
              });
          }
        } else {
          //取消按钮加载效果
          this.editLoading = false;
          this.$nextTick(() => {
            this.editLoading = true;
          });
        }
      })
    },
    /** 删除 */
    rowRemove(type, data) {
      console.log('---------');
      let checkedIds = '';
      if (type === "plural") {
        //多选
        const idArr = Array.from(this.selectedIds);
        if (idArr.length < 1) {
          this.$Message.warning({ content: this.$t('specquality_select'), background: true });
          return;
        } else {
          checkedIds = idArr.join(",");
        }
      } else if (type === "singular") {
        //单选
        checkedIds = data.id;
      }
      top.window.$iviewModal.confirm({
        title: this.$t('common_delete_prompt'),
        content: this.$t('discover_msg_delete'),
        onOk: () => {
          this.$http
            .wisdomPost("/netDiscoverRule/delete", { ids: checkedIds })
            .then(({ code, data, msg }) => {
              if (code === 1) {
                this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                //清空选择的数据
                this.selectedIds = new Set();
                this.selectedDatas = [];
                this.queryClick(1);
              } else {
                this.$Message.warning({ content: msg, background: true });
              }
            })
            .catch((err) => {
              throw new Error(err);
            });
        },
      });
    },
    /** 启用*/
    start() {
      const idArr = Array.from(this.selectedIds);
      if (idArr.length < 1) {
        this.$Message.warning({ content: this.$t('specquality_select'), background: true });
        return;
      } else {
        let ids = idArr.join(",");
        top.window.$iviewModal.confirm({
          title: this.$t('common_enable_prompt'),
          content: this.$t('discover_msg_enable'),
          onOk: () => {
            this.$http.wisdomPost("/netDiscoverRule/enable", { ids: ids, status: 0 }).then(({ code, data, msg }) => {
              if (code === 1) {
                this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                //清空选择的数据
                this.selectedIds = new Set();
                this.selectedDatas = [];
                this.queryClick(1);
              } else {
                this.$Message.warning({ content: msg, background: true });
              }
            }).catch(error => {
              this.$Message.warning({ content: this.$t('common_controls_fial'), background: true });
              throw new Error(error)
            })
          },
        });
      }
    },
    /** 禁用*/
    stop() {
      const idArr = Array.from(this.selectedIds);
      if (idArr.length < 1) {
        this.$Message.warning({ content: this.$t('specquality_select'), background: true });
        return;
      } else {
        let ids = idArr.join(",");
        top.window.$iviewModal.confirm({
          title: this.$t('common_disable_prompt'),
          content: this.$t('discover_msg_disable'),
          onOk: () => {
            this.$http.wisdomPost("/netDiscoverRule/disable", { ids: ids, status: 1 }).then(({ code, data, msg }) => {
              if (code === 1) {
                this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                this.queryClick(1);
              } else {
                this.$Message.warning({ content: msg, background: true });
              }
            }).catch(error => {
              this.$Message.warning({ content: this.$t('common_controls_fial'), background: true });
              throw new Error(error)
            })
          },
        });
      }
    },
    /** 取消事件 */
    cancleForm(formObj) {
      if (formObj == 'editForm') {
        this.editOpen = false
      } else {
        this.addOpen = false
      }

      this.$refs[formObj].resetFields();
       this.$refs["addRuleForm"].resetFields();
    },

    /** 获取基础数据 */
    //获取分组数据
    // getGroupList() {
    //   this.groupList = [];
    //   this.$http
    //       .wisdomPost("/group/list", {pageNo: 1, pageSize: 10000})
    //       .then(({code, data, msg}) => {
    //         if (code === 1) {
    //           this.groupList = data ? data.records : [];
    //         } else {
    //           this.groupList = [];
    //           // this.$Message.warning({content:msg);
    //         }
    //       })
    //       .catch((err) => {
    //         this.groupList = [];
    //         throw new Error(err);
    //       });
    // },
    //获取采集器数据
    getGetherList(orgId, orgItem) {
      console.log(11111)
      this.gatherList = []
      this.getherCode = ''
      this.$http.wisdomPost("/sys/gether/discoverList", {
        orgId: orgId,
        type: 1,
        pageNo: 1,
        pageSize: 10000
      }).then(({ code, data, msg }) => {
        if (code === 1 && data.records.length > 0) {
          this.gatherList = data.records;
          this.getherCode = this.gatherList[0].code;
          console.log(this.getherCode,this.gatherList,'getherCode')
        } else {
          this.gatherList = [];
        }
      }).catch((error) => {
        this.gatherList = [];
        throw new Error(error)
      })
    },
    //获取设备数据
    getDeviceList() {
      // this.deviceList = [];
      var netDeviceTypeKey = 'netDeviceType';
      // "/dataTable/queryCode"
      //  querySystem 是否只查询系统的 设备类型
      this.$http
        .wisdomPost("/dataTable/queryDeviceType", { key: netDeviceTypeKey,querySystem:1 })
        .then(({ code, data, msg }) => {
          if (code === 1) {
            const deviceData = data ?? [];
            this.deviceList = [...[{ lable: this.$t('comm_all'), value: 'ALL' }], ...deviceData]
          } else {
            this.deviceList = [];
            //this.$Message.warning({content:msg);
          }
        })
        .catch((err) => {
          this.deviceList = [];
          throw new Error(err);
        });
    },
    //获取运维等级数据
    getmaintainLevelList() {
      this.maintainList = [];
      var maintainLevelKey = 'maintainLevel';
      this.$http
        .wisdomPost("/dataTable/queryCode", { key: maintainLevelKey })
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.maintainList = data ?? [];
          } else {
            this.maintainList = [];
            //this.$Message.warning({content:msg);
          }
        })
        .catch((err) => {
          this.maintainList = [];
          throw new Error(err);
        });
    },
    //数据列表页码切换
    pageChange(pageNo) {
      this.queryClick(pageNo);
    },
    //数据列表页码大小改变
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.queryClick(this.page.pageNo);
    },
    //选中table的项目
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.tableList.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
            this.selectedDatas.splice(
              this.selectedDatas.findIndex((current) => item.id === current.id),
              1
            );
            // this.selectedDatas.remove(JSON.stringify(item));
          }
        });
        console.log(this.selectedDatas);
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
          this.selectedDatas.push(item);
        });
      }
    },
    handleSelect(slection, row) {
      console.log(row.id);
      this.selectedIds.add(row.id);
      this.selectedDatas.push(row);
      console.log(this.selectedIds);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
      this.selectedDatas.splice(
        this.selectedDatas.findIndex((item) => item.id === row.id),
        1
      );
    },

    /** 新建时时间选择控制 */
    playStartTimeChange(time, index) {
      const endTime = this.addRuleForm.timeList[index].end;
      if (time != undefined && time != null && time != "") {
        const endTimes = time_to_sec(endTime);
        const startTimes = time_to_sec(time);
        if (endTime == undefined || endTimes < startTimes) {
          this.addRuleForm.timeList[index].end = sec_to_time(startTimes);
        }
      }
    },
    playEndTimeChange(time, index) {
      const startTime = this.addRuleForm.timeList[index].start;
      if (time != undefined && time != null && time != "") {
        const endTimes = time_to_sec(time);
        const startTimes = time_to_sec(startTime);
        if (startTime == undefined || endTimes < startTimes) {
          this.addRuleForm.timeList[index].start = sec_to_time(endTimes);
        }
      }
    },
    //时间段改变事件
    //时间段操作（增加，删除）

    speedTimeChangeRemove(formType, index) {
      const length = this[formType].timeList.length;
      if (length === 1) {
        this.$Message.warning({ content: this.$t('phytopo_add_time'), background: true });
        return
      }
      this[formType].timeList.splice(index, 1)
    },
    speedTimeChangeAdd(formType, index) {
      const length = this[formType].timeList.length;
      if (index === length - 1) {//增加
        if (length >= 10) {
          this.$Message.warning({ content: this.$t('phytopo_10_time'), background: true });
          return
        } else {
          this[formType].timeList.push({ start: "0000", end: "2359" })
        }
      }
    },
    //时间段校验是否存在交叉
    ruleTimes(times) {
      const timeArray = times;
      //结果返回值
      let timesFlag = true;
      //将每一项换算成分钟数
      let timeMinArray = timeArray.map(item => {
        let start = 0, end = 0;
        start = Number(item.start.split(':')[0]) * 60 + Number(item.start.split(':')[1]);
        end = Number(item.end.split(':')[0]) * 60 + Number(item.end.split(':')[1]);
        return [start, end]
      })
      for (let i = 0; i < timeMinArray.length - 1; i++) {
        for (let j = i + 1; j < timeMinArray.length; j++) {
          if ((timeMinArray[j][1] <= timeMinArray[i][0] || timeMinArray[j][0] >= timeMinArray[i][1])) {
            timesFlag = true;
          } else {
            timesFlag = false;
            break;
          }
        }
        if (timesFlag === false) {
          break;
        }
      }
      return timesFlag
    },
    //IP操作
    pushIp(index, value, type) {
      if (type === 0) {
        this.addRuleForm.ipArr[index] = value;
      } else if (type === 1) {
        this.editForm.ipArr[index] = value;
      }
    },
  },
  destroyed() {
  },
};
</script>

<style lang='less'>
.sectionBox .section-top .fn_item .fn_item_label {
  min-width: 140px;
}
.progress-animation:before {
  content: "";
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 8px;
  animation: progress-active 2s ease-in-out infinite;
  box-sizing: border-box;
}
@keyframes progress-active {
  0% {
    opacity: 0.5;
    width: 0;
  }
  100% {
    opacity: 0;
    width: 100%;
  }
}
</style>
<style scoped lang="less">
.sectionBox .section-top .fn_item .fn_item_label {
  min-width: 140px;
}
.progress-box-border {
  border: 1px solid #c0c4cc;
}
.progress-box {
  width: 100%;
  height: 16px;
  background-color: #fff;
  border-radius: 8px;
  position: relative;
  .progress-text {
    position: absolute;
    top: 0;
    right: 3px;
    color: #060d15;
    height: 16px;
    border-radius: 8px;
    // color: #fff;
    text-align: center;
    line-height: 16px;
    font-size: 10px;
  }
  .progress {
    height: 16px;
    border-radius: 8px;
    // color: #fff;
    text-align: center;
    line-height: 16px;

    // background: pink;
  }
}
</style>


