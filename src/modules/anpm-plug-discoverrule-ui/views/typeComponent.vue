<template>
  <Row class="deviceType">
    <Col span="8">
      <p>{{ $t("discover_device_type") }}</p>
      <div class="redefine diff deviceH" style="height: 200px">
        <CheckboxGroup v-model="typeValue" @on-change="changeType">
          <Checkbox
            v-for="item in deviceList"
            :label="item.value"
            :key="item.value"
            >{{ item.lable }}</Checkbox
          >
        </CheckboxGroup>
      </div>
    </Col>
    <Col span="6" class="btn-div">
      <div class="widBtn flex-column" style="height: 250px !important">
        <Button @click="addSource">&#62;&#62;</Button>
        <Button @click="reduceSource">&#60;&#60;</Button>
      </div>
      <!-- <div class="widBtn flex-column">
        <Button @click="addNode">&#62;&#62;</Button>
        <Button @click="reduceNode">&#60;&#60;</Button>
      </div> -->
    </Col>
    <Col span="8" class="elseDiV">
      <!-- <div class="sourceDiv"> -->
        <!-- <p>{{ $t("comm_source_to_device") }}</p> -->
        <p>{{ $t("device_type_needs_create_task") }}</p>
        <div class="redefine diff eleH" style="height: 200px">
          <CheckboxGroup v-model="sourceValue" @on-change="changeSource">
            <Checkbox
              v-for="item in sourceList"
              :label="item.value"
              :key="item.value"
              >{{ item.lable }}</Checkbox
            >
          </CheckboxGroup>
        </div>
      <!-- </div>
      <div class="nodeDiv">
        <p>{{ $t("comm_node_connected_to_source_device") }}</p>
        <div class="redefine diff eleH">
          <CheckboxGroup v-model="nodeValue" @on-change="changeNode">
            <Checkbox
              v-for="item in nodeList"
              :label="item.value"
              :key="item.value"
              >{{ item.lable }}</Checkbox
            >
          </CheckboxGroup>
        </div>
      </div> -->
    </Col>
  </Row>
</template>

<script>
export default {
    props:{
        data:{
            type:Array,
            default: function () {
                return []
            }
        },
        type:{
            type:String,
            default: function () {
                return 'add'
            }
        },
        // 源到设备默认值
        sourceData:{
            type:Array,
            default: function () {
                return []
            }
        },
        // 源到设备上联节点默认值
        nodeData:{
            type:Array,
            default: function () {
                return []
            }
        }
    },
    data(){
        return {
            deviceList: [],
            typeValue: [],
            sourceValue:[],
            nodeValue:[],
            sourceList:[],
            nodeList:[],
            sourceTempData:[],
            nodeTempData:[],
            // 其他 属性值
            ifClickAll: false,
        }
    },
    watch:{
        data: {
            handler(newValue,oldValue) {
                this.deviceList= [...newValue]
                if (this.type == 'add') {
                    this.setDefaultSource()
                    // this.setDefaultNode()
                } 
            },
            deep: true,
            immediate:true
        },
        deviceList: {
            handler(val) {
                if (val?.length ==1) {
                    this.deviceList.splice(0,1)
                }
            },
            deep: true,
           // immediate:true
        },
        sourceData:{
            handler(newValue,oldValue) {
                if (this.type == 'edit'  && newValue) {
                    this.sourceTempData = [...newValue]
                    if(this.sourceTempData.length >0){
                        if (this.data.length !=0) {
                         this.setEditSource()
                        }
                        if(newValue.length==0){
                        this.setDefaultSource()
                        }
                    }
                }
            },
            deep: true,
            immediate:true
        },
        nodeData:{
            handler(newValue,oldValue) {
                if (this.type == 'edit' && newValue) {
                    this.nodeTempData = [...newValue]
                    if(this.nodeTempData.length >0){
                        if (this.data.length !=0) {
                        this.setEditNode()
                        }
                        if(newValue.length==0){
                            this.setDefaultNode()
                        }
                    }
                }
            },
            deep: true,
            immediate:true
        }
    },
    created(){
    },
    methods:{
        changeType(val){
             let ifAll = val.includes('ALL');
            // 以下跟【全部】挂钩 特殊处理
            if (ifAll && !this.ifClickAll && val.length != this.deviceList.length) {
                // 点击全部
                this.typeValue = this.deviceList.map(item => {
                    return item.value;
                });
                this.ifClickAll = true;
            } else if (ifAll && this.ifClickAll && ifAll && val.length != this.deviceList.length) {
                // 点击其他数据 剔除全部
                this.typeValue.shift();
                this.ifClickAll = false;
            } else if (!ifAll && !this.ifClickAll && val.length == this.deviceList.length - 1) {
                // 其他数据选择完 添加全部
                this.typeValue.unshift('ALL');
                this.ifClickAll = true;
            } else if (!ifAll && this.ifClickAll) {
                // 取消全部选择
                this.typeValue = [];
                this.ifClickAll = false;
            }
            console.log(this.typeValue)

        },
        addSource(){
            if (this.typeValue.length == 0) {
                this.$Message.warning(this.$t('discover_select_device_type'));
                return;
            }
            // 将选择的设备类型添加到 源到设备
            for (let item of this.typeValue) {
                if (item == 'ALL') {
                    continue;
                }
                let data = this.deviceList.find(chid => {
                    return chid.value == item;
                });
                this.sourceList.push(data)
            }

            // 清除设备类型中 已经被添加到源到设备的数据
            this.typeValue.forEach(item=>{
                let index = this.deviceList.findIndex(val=>{
                  return  val.value == item
                })
                this.deviceList.splice(index,1)
            })
            this.$emit('sourceList',this.sourceList)
           // 清空已选的值
            this.typeValue = []
        },
        reduceSource(){
            if (this.sourceValue.length == 0) {
                this.$Message.warning(this.$t('discover_select_device_type'));
                return;
            }
            // 将源到设备的值 添加到设备类型
            if ( this.deviceList.length == 0) {
                this.deviceList.push({
                    lable: this.$t('comm_all'), 
                    value: 'ALL'
                })
            }
            this.sourceValue.forEach(item=>{
                let data =  this.sourceList.find(chid => {
                    return chid.value == item;
                });
                this.deviceList.push(data)
            })
            // 删除源到设备中勾选的值
            this.sourceValue.forEach(item=>{
                let index = this.sourceList.findIndex(val=>{
                  return  val.value == item
                })
                this.sourceList.splice(index,1)
            })   
             this.$emit('sourceList',this.sourceList)
            // 清空已选的值         
            this.sourceValue = []
        },
        // 设置 源到设备默认值为。Linux服务，摄像头
//         1	服务器
// 2	交换机
// 3	路由器
// 4	防火墙
// 5	未知
// 6	linux服务器
// 7	摄像头
// 8	打印机
// 9	路由/交换
// 10	PC终端
        setDefaultSource(){
            console.log("33data",this.data)
            let source =  this.data.filter(item=>{
                return item.value == '1' || item.value == '6' || item.value == '7' || item.value == '10' || item.value == '5'
            })
            this.sourceList = [...source]
            this.sourceList.forEach(item=>{
                let index = this.deviceList.findIndex(val=>{
                  return  val.value == item.value
                })
                this.deviceList.splice(index,1)
            })
            console.log("444sourceList",this.sourceList)
            this.$emit('sourceList',this.sourceList)
        },
        // 设置 源到设备上联接节点默认值为。pc终端，未知，打印机
        setDefaultNode(){
            let node =  this.data.filter(item=>{
                return item.value == '10' || item.value == '5' || item.value == '8'
            })
            this.nodeList = [...node]
            this.nodeList.forEach(item=>{
                let index = this.deviceList.findIndex(val=>{
                  return  val.value == item.value
                })
                this.deviceList.splice(index,1)
            })
            this.$emit('nodeList',this.nodeList)
        },
        // 修改 设置源到设备默认值
        setEditSource(){
            console.log('deviceList',this.deviceList);
            console.log('sourceTempData',this.sourceTempData);
            this.sourceList= []
            for (let item of this.sourceTempData) {
                let data = this.deviceList.find(chid => {
                    return chid.value == item;
                });
                this.sourceList.push(data)
            }  
            this.sourceTempData.forEach(item=>{
                let index = this.deviceList.findIndex(val=>{
                  return  val.value == item
                })
                this.deviceList.splice(index,1)
            })
            this.$emit('sourceList',this.sourceList)
        },
        // 修改 设置源到设备上联接节点默认值
        setEditNode(){
            this.nodeList= []
            let arryTemp = [...this.deviceList]
             console.log('deviceList',arryTemp);
              console.log('nodeTempData',this.nodeTempData);
            for (let item of this.nodeTempData) {
                let data = arryTemp.find(chid => {
                    return chid.value == item;
                });
                this.nodeList.push(data)
            }  
            this.nodeTempData.forEach(item=>{
                let index = this.deviceList.findIndex(val=>{
                  return  val.value == item
                })
                this.deviceList.splice(index,1)
            })
             this.$emit('nodeList',this.nodeList)
        },
        changeSource(val){

        },
        addNode(){
            if (this.typeValue.length == 0) {
                this.$Message.warning(this.$t('discover_select_device_type'));
                return;
            }
 
            // 将选择的设备类型添加到 源到设备上联节点
            for (let item of this.typeValue) {
                if (item == 'ALL') {
                    continue;
                }
                let data = this.deviceList.find(chid => {
                    return chid.value == item;
                });
                this.nodeList.push(data)
            }

            // 清除设备类型中 已经被添加到源到设备上联节点中的数据
            this.typeValue.forEach(item=>{
                let index = this.deviceList.findIndex(val=>{
                  return  val.value == item
                })
                this.deviceList.splice(index,1)
            })
             this.$emit('nodeList',this.nodeList)
           // 清空已选的值
            this.typeValue = []
        },
        reduceNode(){
            if (this.nodeValue.length == 0) {
                this.$Message.warning(this.$t('discover_select_device_type'));
                return;
            }
           if ( this.deviceList.length == 0) {
                this.deviceList.push({
                    lable: this.$t('comm_all'), 
                    value: 'ALL'
                })
            }
            // 将源到设备的值 添加到设备类型
            this.nodeValue.forEach(item=>{
                let data =  this.nodeList.find(chid => {
                    return chid.value == item;
                });
                this.deviceList.push(data)
            })
            // 删除源到设备中勾选的值
            this.nodeValue.forEach(item=>{
                let index = this.nodeList.findIndex(val=>{
                  return  val.value == item
                })
                this.nodeList.splice(index,1)
            })   
             this.$emit('nodeList',this.nodeList)
            // 清空已选的值         
            this.nodeValue = [] 
        },
        changeNode(){

        },
        tips(){

        }
    }
}
</script>

<style>
</style>