<template>
    <div>
        <div  id="fiberChart" :style="{' width':'100%','height':canvasH+'px'}"></div>
    </div>
</template>

<script>
import { Stage, Layer, Node, TextNode, Link,CurveLink,BezierLink ,PopupMenu,Tooltip} from '../../../common/jtopo/jtopo1.4/jtopo-1.4.0_trial-esm-min.js';
let stage = null;
let layer = null;
let popupMenu = null
import tanzhenImg from "../../../common/jtopo/img/icon-tz-01.png";
import jiedianImgH from "../../../common/jtopo/img/icon-d2.png";
import jiedianImgG from "../../../common/jtopo/img/icon-d3.png";
import jiedianImgR from "../../../common/jtopo/img/icon-d5.png";
import jiedianImgY from "../../../common/jtopo/img/icon-d4.png";
// 空心圈
import jiedianImgY3 from "../../../common/jtopo/img/icon-d444.png";
import jiedianImgR3 from "../../../common/jtopo/img/icon-d555.png";
// 菱形*
import mubiaoImg from "../../../common/jtopo/img/icon-d1.png";
import jiedianImgH2 from "../../../common/jtopo/img/icon-d22.png";
import jiedianImgY2 from "../../../common/jtopo/img/icon-d44.png";
import jiedianImgR2 from "../../../common/jtopo/img/icon-d55.png";
//方块
import jiedian1 from "../../../common/jtopo/img/icon-jhj-01.png";
import jiedian2 from "../../../common/jtopo/img/icon-jhj-02.png";
import jiedian3 from "../../../common/jtopo/img/icon-jhj-04.png";
import jiedian4 from "../../../common/jtopo/img/icon-jhj-03.png";
// 未知*
import weizhi from "../../../common/jtopo/img/icon-d6.png";
export default {
    name:'FiberChart',
    props:['pathTopoData','showType','showWarning'],
    data(){
        return{
            nodeData:{},
            canvasH:'500',
            // pathTopoData:[]
        }
    },
    watch:{
        pathTopoData:{
            handler(val) {
                if (!layer) {
                    console.log('watch-init');
                    this.init()
                    this.handlerData()
                    this.initTopo();
                    popupMenu.hide()
                }else{
                    console.log('watch-update');
                    stage.removeChild(layer);
                    popupMenu.hide()
                    this.updateTopo()
                }
            },
            deep: true ,
            // immediate:true
        },
        showType:{
            handler(val) {
                 this.showTypeChange();
            },
            deep: true 
        },
        // 是否展示告警信息
        showWarning:{
            handler(val) {
                this.showTypeChange()
            },
            deep: true 
        }
    },
    methods:{
        // 纤维图
        init() {
            let data = {};
            if (this.nodeData && this.nodeData.children.length > 0) {
                this.nodeData.label = {
                    color: '#fff'
                };
                data = this.nodeData.children.map(r => {
                    r.label = {
                        color: '#00FFEE'
                    };
                    if (r.children) {
                        r.children.map(e => {
                            if (e.state == 1000) {
                                e.lineStyle = {
                                    color: '#00FFEE'
                                };
                                // 节点字体颜色
                                e.label = {
                                    color: '#00FFEE'
                                };
                                // 圆圈颜色
                                e.itemStyle = {
                                    color: '#00FFEE',
                                    borderColor: '#00FFEE'
                                };
                            } else if (e.state == 1) {
                                e.lineStyle = {
                                    color: '#FE5C5C'
                                };
                                // 节点字体颜色
                                e.label = {
                                    color: '#FE5C5C'
                                };
                                // 圆圈颜色
                                e.itemStyle = {
                                    color: '#FE5C5C',
                                    borderColor: '#FE5C5C'
                                };
                            } else {
                                e.lineStyle = {
                                    color: '#FEA31B'
                                };
                                // 节点字体颜色
                                e.label = {
                                    color: '#FEA31B'
                                };
                                // 圆圈颜色
                                e.itemStyle = {
                                    color: '#FEA31B',
                                    borderColor: '#FEA31B'
                                };
                            }
                            return e;
                        });
                        return r;
                    } else {
                        if (r.state == 1000) {
                            r.lineStyle = {
                                color: '#00FFEE'
                            };
                            // 节点字体颜色
                            r.label = {
                                color: '#00FFEE'
                            };
                            // 圆圈颜色
                            r.itemStyle = {
                                color: '#00FFEE',
                                borderColor: '#00FFEE'
                            };
                        } else if (r.state == 1) {
                            r.lineStyle = {
                                color: '#FE5C5C'
                            };
                            // 节点字体颜色
                            r.label = {
                                color: '#FE5C5C'
                            };
                            // 圆圈颜色
                            r.itemStyle = {
                                color: '#FE5C5C',
                                borderColor: '#FE5C5C'
                            };
                        } else {
                            r.lineStyle = {
                                color: '#FEA31B'
                            };
                            // 节点字体颜色
                            r.label = {
                                color: '#FEA31B'
                            };
                            // 圆圈颜色
                            r.itemStyle = {
                                color: '#FEA31B',
                                borderColor: '#FEA31B'
                            };
                        }
                        return r;
                    }
                });
            }
            this.myChart = echarts5.init(document.getElementById('fiberChart'));
            this.myChart.clear();
            let option = {
                tooltip: {
                    trigger: 'item',
                    borderWidth: '0',
                    backgroundColor: '#252729',
                    triggerOn: 'mousemove',
                    formatter: '{b}',
                    textStyle: {
                        color: '#fff'
                    }
                },
                series: [
                    {
                        type: 'tree',
                        data: [this.nodeData],
                        top: '5%',
                        // this.leftTrue == true ? '7%' : '15%',
                        left: '10%',
                        bottom: '2%',
                        right: '20%',
                        symbolSize: 7,
                        //控制父节点
                        label: {
                            position: 'left',
                            verticalAlign: 'middle',
                            align: 'right',
                            // color: '#00FFEE',
                        },
                        // 子节点
                        leaves: {
                            label: {
                                position: 'right',
                                verticalAlign: 'middle',
                                align: 'left',
                                formatter: function (value, index) {
                                    return value.data.name.length > 30 ? value.data.name.substring(0, 30) + '...' : value.data.name;
                                }
                            }
                        },
                        //实心圈
                        symbol: 'circle',
                        // itemStyle: {
                        //     borderColor: '#ccc'
                        // },
                        expandAndCollapse: true,
                        animationDuration: 550,
                        animationDurationUpdate: 750
                    }
                ]
            };
            this.myChart.setOption(option, true, true);
            this.myChart.on('click', param => {
                this.contentData.name = param.data.name;
                this.contentData.taskId = param.data.taskId;
                this.contentData.reportId = param.data.reportId;
                if (param.data.state == 1 || param.data.state == 2 || param.data.state == 3) {
                    this.fibreShow = true;
                }
            });
        },
    },
    mounted(){
        //  this.pathTopoData = demoData
        // stage = new Stage('fiberChart');
        // popupMenu = new PopupMenu(stage);
        this.init()
    },
    updated(){
        console.log('update');
    },
}
</script>

<style lang='less' scoped>
    #topoChart{
        width: 100%;
        height: 500px;
    }
    /deep/
    .jtopo_popoupmenu{
        text-align: left;
        background-color: #16436b ;   
        border: 1px solid #16436b ;
        .header{
            span{
                cursor: pointer;
                color: #00FFEE;
            };
        }
        .content{
            max-height: 150px;
            overflow-y: auto;
        }        
        hr{
             border: 1px solid #045B8E;
        }
        a{
            color: #fff;
            padding-left: 0px;
        }
        a:hover {
            color: #fff;
        }
    }
</style>