<template>
  <section
    class=""
    :style="
      'position: relative; height: 100%;width:100% ;background:' +
      (isbigscreen ? '#1a222e' : isdarkSkin == 1 ? '#1a222e' : '#ffffff')
    "
  >
    <Loading :loading="loading"></Loading>
    <div class="topologys">
      <div
        v-if="havedata"
        id="fiberChart"
        :showWarning="true"
        style="width: 100%; height: 100%"
      ></div>
      <div v-else :class="{'fillempty':currentSkin==1 , 'fillempty2':currentSkin==0}" style="height: 100%">
        <span class="fillemptyTitle">{{ $t("common_No_data") }}</span>
      </div>
    </div>
  </section>
</template>
<script>
import "@/config/page.js"
import $ from "../../../common/jtopo/jquery.min.js";
import "../../../common/jtopo/jtopo-s.js";
import { newJtopo } from "../style/jtopo-editor.js";
import * as echarts5 from 'echarts5';
import '@/timechange'
export default {
  name: "index",
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      //刷新时间间隔
      intervalTime: null,
      //刷新参数
      interrefresh: null,
      loading: false,
      topoId: 0,
      canvasH:'200',
      nodeData: {},
      havedata: true,
      showWarning: true,
      showShuxing: 'sx',
      msgButtonModal: false,
      msgModal: ["设备ip", "设备名称"],
      fiberChartData: {},
      //type:0节点字段，1线路字段
      msgModalData: [
        {
          id: 1,
          name: this.$t('comm_topo_device_ip'),
          filedName: 'value',
          type: 0,
          util: "",
          isShow: true,
        }, {
          id: 2,
          name: this.$t('comm_topo_device_name'),
          filedName: 'aliases',
          type: 0,
          util: "",
          isShow: true,
        },
      ],
      legendButtonModal: false,
      selectTopoButtonModal: false,
      selectModal: "",
      selectModalData: [],
      isbigscreen: false,
      isdarkSkin: 0
    }
  },
  watch: {
    isdarkSkin: {
      handler(val) {
        setTimeout(() => {
        }, 1000)
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    console.log(parent.window.isEdit)
    this.isdarkSkin = top.window.isdarkSkin;
    const src = window.frames.frameElement.getAttribute('src'), name = window.frames.frameElement.getAttribute('name');
    if (parent.window.isEdit) {
      // if (parent.window.dashfiberchart && parent.window.dashfiberchart.indexOf('&&topoId=')>-1) {
      //   this.topoId = parent.window.dashfiberchart.split('&&topoId=')[1].split('&&limitNum=')[0];
      // }
      if (unescape(src).split('=')[1]) {
        this.topoId = JSON.parse(unescape(src).split('=')[1]).topoId
      }
      this.intervalTime = parent.window.dashfiberchart.split('&&topoId=')[0].split('?intervalTime=')[1];
    } else {
      if (window.frames.name && window.frames.name.indexOf('&&topoId=') > -1) {
        this.topoId = window.frames.name.split('&&topoId=')[1].split('&&limitNum=')[0];
      }
      this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
      if (window.frames.frameElement) {
        // window.frames.frameElement.setAttribute('src','about:blank')
        // window.frames.frameElement.contentWindow.document.write("");
        window.frames.frameElement.contentWindow.close();
        // window.frames.frameElement.remove()

        // window.frames.frameElement.setAttribute('src',src)
      }
    }

    this.getFiberChartPulldown();
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 纤维图
    init() {
      this.canvasH = document.body.clientHeight/2;
      let data = {};
      if (this.nodeData && this.nodeData.children.length > 0) {
        this.nodeData.label = {
          color: '#fff'
        };
        data = this.nodeData.children.map(r => {
          r.label = {
            color: '#00FFEE'
          };
          if (r.children) {
            r.children.map(e => {
              if (e.state == 1000) {
                e.lineStyle = {
                  color: '#00FFEE'
                };
                // 节点字体颜色
                e.label = {
                  color: '#00FFEE'
                };
                // 圆圈颜色
                e.itemStyle = {
                  color: '#00FFEE',
                  borderColor: '#00FFEE'
                };
              } else if (e.state == 1) {
                e.lineStyle = {
                  color: '#FE5C5C'
                };
                // 节点字体颜色
                e.label = {
                  color: '#FE5C5C'
                };
                // 圆圈颜色
                e.itemStyle = {
                  color: '#FE5C5C',
                  borderColor: '#FE5C5C'
                };
              } else {
                e.lineStyle = {
                  color: '#FEA31B'
                };
                // 节点字体颜色
                e.label = {
                  color: '#FEA31B'
                };
                // 圆圈颜色
                e.itemStyle = {
                  color: '#FEA31B',
                  borderColor: '#FEA31B'
                };
              }
              return e;
            });
            return r;
          } else {
            if (r.state == 1000) {
              r.lineStyle = {
                color: '#00FFEE'
              };
              // 节点字体颜色
              r.label = {
                color: '#00FFEE'
              };
              // 圆圈颜色
              r.itemStyle = {
                color: '#00FFEE',
                borderColor: '#00FFEE'
              };
            } else if (r.state == 1) {
              r.lineStyle = {
                color: '#FE5C5C'
              };
              // 节点字体颜色
              r.label = {
                color: '#FE5C5C'
              };
              // 圆圈颜色
              r.itemStyle = {
                color: '#FE5C5C',
                borderColor: '#FE5C5C'
              };
            } else {
              r.lineStyle = {
                color: '#FEA31B'
              };
              // 节点字体颜色
              r.label = {
                color: '#FEA31B'
              };
              // 圆圈颜色
              r.itemStyle = {
                color: '#FEA31B',
                borderColor: '#FEA31B'
              };
            }
            return r;
          }
        });
      }
      this.myChart = echarts5.init(document.getElementById('fiberChart'));
      this.myChart.clear();
      let option = {
        tooltip: {
          trigger: 'item',
          borderWidth: '0',
          backgroundColor: '#252729',
          triggerOn: 'mousemove',
          formatter: '{b}',
          textStyle: {
            color: '#fff'
          }
        },
        series: [
          {
            type: 'tree',
            data: [this.nodeData],
            top: '5%',
            // this.leftTrue == true ? '7%' : '15%',
            left: '30%',
            bottom: '2%',
            right: '30%',
            symbolSize: 7,
            //控制父节点
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              // color: '#00FFEE',
            },
            // 子节点
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left',
                formatter: function (value, index) {
                  return value.data.name.length > 30 ? value.data.name.substring(0, 30) + '...' : value.data.name;
                }
              }
            },
            //实心圈
            symbol: 'circle',
            // itemStyle: {
            //     borderColor: '#ccc'
            // },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      };
      this.myChart.setOption(option, true, true);
      this.myChart.on('click', param => {
        this.contentData.name = param.data.name;
        this.contentData.taskId = param.data.taskId;
        this.contentData.reportId = param.data.reportId;
        if (param.data.state == 1 || param.data.state == 2 || param.data.state == 3) {
          this.fibreShow = true;
        }
      });
    },
    getFiberChartPulldown() {
      //默认id
      // 
      let defId = this.topoId;
      this.getFiberChartlogyResult(defId);
    },
    /**
     * 获取拓扑图数据
     * @param id 传入拓扑图id参数
     */
    getFiberChartlogyResult(id) {
      let _self = this;
      let httpRequest = this.$http.wisdomPost("/pathTopo/getPathFibreTopoResult", { topologyId: id })
      httpRequest.then((res) => {
        _self.loading = true;
        if (res.code === 1) {
          if (res.data) {
            _self.nodeData = res.data;
            _self.$nextTick(() => {
              this.init();
            });   
          }else {
              this.$Message.warning({content:res.msg,background:true});
              _self.dataList = {};
            }
        } else {
          _self.nodeData = {};
          _self.loading = false;
        }
      }).catch((error) => {
        _self.loading = false;
        _self.dataList = {};
        if (parent.loading) {
          parent.loading['dashfiberchart'] = false;
        }
      }).finally(() => {
        _self.loading = false;
        if (parent.loading) {
          parent.loading['dashfiberchart'] = false;
        }
      })
      httpRequest = null
    },
  },
  mounted() {
        // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    window.parent.addEventListener("message", (e) => {
      if (e) {
        if (e.data.type == 'msg') {
          return;
        } else if (typeof e.data == 'object') {
          this.isdarkSkin = e.data.isdarkSkin;
        } else if (typeof e.data == 'number') {
          this.isdarkSkin = e.data;
        }
      }
    });
    window.thisVm = this;
    this.isbigscreen = window.isbigscreen;
    if (this.intervalTime && Number(this.intervalTime)) {
      this.interrefresh = setInterval(() => { this.loading = true; this.getFiberChartPulldown() }, this.intervalTime * 1000);
    }
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
    if (this.interrefresh) {
      clearInterval(this.interrefresh);
      this.interrefresh = null;
    }
    window.parent.removeEventListener("message", () => {})
  }
}
</script>
<style>
body.bigscreen .topologys {
  background: var(--body_conent_b_color , #060d15);
}

.topologys {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
  overflow: auto;
  color: black;
  box-sizing: border-box;
  background: var(--body_conent_b_color , #060d15);
}

.topologys button {
  margin: 10px;
}
</style>
<style scoped lang="less">
.fillempty {
  text-align: center;
  line-height: 40px;
  background-image: url("../../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  // background-size: 290px 200px;
  background-position: center;

  .fillemptyTitle {
    position: absolute;
    display: block;
    width: 100%;
    // top: calc(50% + 110px);
    color:  #0e2a5f;
    
  }
}

.bigscreen {
  .fillemptyTitle {
    color: #8fd4ff;
  }
}
</style>