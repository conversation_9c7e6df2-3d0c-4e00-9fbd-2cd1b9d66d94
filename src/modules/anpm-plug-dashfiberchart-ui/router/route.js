const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/dashfiberchart',
  },
  {
    path: "/dashfiberchart",
    name: lan.getLabel("src.fiberDiagram"),
    meta: {
      authority: true
    },
    component: resolve =>
      // require(["@/modules/anpm-plug-dashfiberchart-ui/views/topolist.vue"], resolve)
      require(["@/modules/anpm-plug-dashfiberchart-ui/views/index.vue"], resolve)
  },
];
