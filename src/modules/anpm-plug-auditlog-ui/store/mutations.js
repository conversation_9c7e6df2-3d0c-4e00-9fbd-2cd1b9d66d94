"use strict";

// 获取人员管理
export const setPerson = (state, data) => {
  state.Person = data;
};

// 获取客户列表
export const setClientList = (state, data) => {
  state.ClientList = data;
};
// 获取客户列表（用于故障管理）
export const setClientList1 = (state, data) => {
  state.ClientList1 = data;
};
//策略配置中修改选择目的ip
export const setListProbeTargetName = (state, data) => {
  //  let newsdata=data.records;
  //
  //   let datas=[];
  //   if(newsdata.length>0){
  //       for(let i in newsdata) {
  //           let item = {};
  //           item = newsdata[i];
  //           item.reanonly = true;
  //           datas.push(item)
  //       }
  //   }
  //   data.records=datas;
  state.ProbeTargetName = data;
};
//策略配置中修改根据选择目的ip选择名称
export const setListProbeTargetNameByIP = (state, data) => {
  state.ProbeTargetNameByIP = data;
};
//链路类型配置列表
export const setLinkTypeList = (state, data) => {
  state.LinkTypeList = data;
};

//获取链路类型列表
export const setLinkTypeListss = (state, data) => {
  state.LinkTypeListss = data;
};
//获取链路配置列表
export const setListLinks = (state, data) => {
  state.ListLinks = data;
};
//获取告警等级
export const setAramLIst = (state, data) => {
  state.AramLIst = data;
};
//获取告警规则列表
export const setAramRoleList = (state, data) => {
  state.AramRoleList = data;
};
//获取公共节点对比分析
export const setPublicNode = (state, data) => {
  state.PublicNodeList = data;
};

//获取公共节点对比分析承载链路总数
export const setChengzai = (state, data) => {
  state.ChengzaiList = data;
};
//// 获取公共节点对比分析中断链路总数
export const setZhongduan = (state, data) => {
  state.ZhongduanList = data;
};
//获取公共节点对比分析劣化链路总数
export const setLiehua = (state, data) => {
  state.LiehuaList = data;
};

//获取公共节点对比分析链路树图
export const setlinkMore = (state, data) => {
  state.linkMoreList = data;
};
//获取公共节点对比分析 单链路图
export const setLinks = (state, data) => {
  state.LinksList = data;
};
//获取实时告警
export const setRealTimeAlarm = (state, data) => {
  state.RealTimeAlarmList = data;
};
export const setAlarmDetail = (state, data) => {
  state.AlarmDetailList = data;
};
export const sethistoryAlarm = (state, data) => {
  state.historyAlarmList = data;
};
// 获取链路查询列表
export const setLinkSearch = (state, data) => {
  state.LinkSearchList = data;
};
// 获取链路详情
export const setLinkSearchDetail = (state, data) => {
  state.LinkSearchDetailList = data;
};

// 获取链路统计
export const setLinkNumber = (state, data) => {
  state.LinkNumberList = data;
};
// 获取中断链路信息
export const setListBrokenEvent = (state, data) => {
  state.ListBrokenEvent = data;
};
export const setBrokenTime = (state, data) => {
  state.BrokenTimeList = data;
};
export const setBrokenCount = (state, data) => {
  state.BrokenCountList = data;
};
export const setBrokenAll = (state, data) => {
  state.BrokenAllList = data;
};
export const setBrokenEvent = (state, data) => {
  state.BrokenEventList = data;
};
export const setBrokenEventB = (state, data) => {
  state.BrokenEventBList = data;
};
// 劣化链路分析
export const setDeglist = (state, data) => {
  state.Deglist = data;
};
export const setdegDetails = (state, data) => {
  state.degDetailsList = data;
};
//默认值管理列表数据
export const setDefaultConfigList = (state, data) => {
  state.defaultConfigList = data;
};
//获取字典oid型号
export const setDevModel = (state, data) => {
  state.devModelList = data;
};
//浏览是否允许回退
// export const updateAppSetting2=(state,data)=>{
//     console.log(data)
//     state.allowBack=data;
// };
export const updateDelayLossHistory = (state, data) => {
  if (data.level == 0) {
    state.delayLossHistory.dayData.delay = data.datas[0];
    state.delayLossHistory.dayData.loss = data.datas[1];
  }
  if (data.level == 1) {
    state.delayLossHistory.HoursData.delay = data.datas[0];
    state.delayLossHistory.HoursData.loss = data.datas[1];
  }
  if (data.level == 2) {
    state.delayLossHistory.minuteData.delay = data.datas[0];
    state.delayLossHistory.minuteData.loss = data.datas[1];
  }
  if (data.level == 3 || data.level == 4) {
    state.delayLossHistory.sencondData.delay = data.datas[0];
    state.delayLossHistory.sencondData.loss = data.datas[1];
  }
  if (data === -1) {
    //清空所有数据
    state.delayLossHistory.dayData.delay = [];
    state.delayLossHistory.dayData.loss = [];
    state.delayLossHistory.HoursData.delay = [];
    state.delayLossHistory.HoursData.loss = [];
    state.delayLossHistory.minuteData.delay = [];
    state.delayLossHistory.minuteData.loss = [];
    state.delayLossHistory.sencondData.delay = [];
    state.delayLossHistory.sencondData.loss = [];
  }
};
export const updateFlowHistory = (state, data) => {
  if (data.level == 0) {
    state.flowHistory.dayData.enter = data.datas[0];
    state.flowHistory.dayData.issue = data.datas[1];
  }
  if (data.level == 1) {
    state.flowHistory.HoursData.enter = data.datas[0];
    state.flowHistory.HoursData.issue = data.datas[1];
  }
  if (data.level == 2) {
    state.flowHistory.minuteData.enter = data.datas[0];
    state.flowHistory.minuteData.issue = data.datas[1];
  }
  if (data.level == 3 || data.level == 4) {
    state.flowHistory.sencondData.enter = data.datas[0];
    state.flowHistory.sencondData.issue = data.datas[1];
  }
  if (data === -1) {
    //清空所有数据
    state.flowHistory.dayData.enter = [];
    state.flowHistory.dayData.issue = [];
    state.flowHistory.HoursData.enter = [];
    state.flowHistory.HoursData.issue = [];
    state.flowHistory.minuteData.enter = [];
    state.flowHistory.minuteData.issue = [];
    state.flowHistory.sencondData.enter = [];
    state.flowHistory.sencondData.issue = [];
  }
};
export const updategoodRateHistory = (state, data) => {
  if (data.level == 0) {
    state.goodRateHistory.dayData.nrUseRateList = data.datas[0];
    state.goodRateHistory.dayData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.dayData.useRateList = data.datas[2];
    state.goodRateHistory.dayData.goodRateList = data.datas[3];
  }
  if (data.level == 1) {
    state.goodRateHistory.HoursData.nrUseRateList = data.datas[0];
    state.goodRateHistory.HoursData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.HoursData.useRateList = data.datas[2];
    state.goodRateHistory.HoursData.goodRateList = data.datas[3];
  }
  if (data.level == 2) {
    state.goodRateHistory.minuteData.nrUseRateList = data.datas[0];
    state.goodRateHistory.minuteData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.minuteData.useRateList = data.datas[2];
    state.goodRateHistory.minuteData.goodRateList = data.datas[3];
  }
  if (data.level == 3 || data.level == 4) {
    state.goodRateHistory.sencondData.nrUseRateList = data.datas[0];
    state.goodRateHistory.sencondData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.sencondData.useRateList = data.datas[2];
    state.goodRateHistory.sencondData.goodRateList = data.datas[3];
  }
  if (data === -1) {
    //清空所有数据
    state.goodRateHistory.dayData.nrUseRateList = [];
    state.goodRateHistory.dayData.nrGoodRateList = [];
    state.goodRateHistory.dayData.useRateList = [];
    state.goodRateHistory.dayData.goodRateList = [];
    state.goodRateHistory.HoursData.nrUseRateList = [];
    state.goodRateHistory.HoursData.nrGoodRateList = [];
    state.goodRateHistory.HoursData.useRateList = [];
    state.goodRateHistory.HoursData.goodRateList = [];
    state.goodRateHistory.minuteData.nrUseRateList = [];
    state.goodRateHistory.minuteData.nrGoodRateList = [];
    state.goodRateHistory.minuteData.useRateList = [];
    state.goodRateHistory.minuteData.goodRateList = [];
    state.goodRateHistory.sencondData.nrUseRateList = [];
    state.goodRateHistory.sencondData.nrGoodRateList = [];
    state.goodRateHistory.sencondData.useRateList = [];
    state.goodRateHistory.sencondData.goodRateList = [];
  }
};
export const setdelayLTlevel = (state, data) => {
  state.delayTlevel = data;
};
export const setflowTlevel = (state, data) => {
  state.flowTlevel = data;
};
export const setflowUnit = (state, data) => {
  if (data.level == -1) {
    state.flowUnit.dayUnit = "";
    state.flowUnit.hoursUnit = "";
    state.flowUnit.minuteUnit = "";
  }
  if (data.level == 0) {
    state.flowUnit.dayUnit = data.unit;
  }
  if (data.level == 1) {
    state.flowUnit.hoursUnit = data.unit;
  }
  if (data.level == 2) {
    state.flowUnit.minuteUnit = data.unit;
  }
};
export const setgoodRateTlevel = (state, data) => {
  state.goodRateTlevel = data;
};
export const setsnmpDetails = (state, data) => {
  if (data == -1) {
    state.snmpDetails.day = {};
    state.snmpDetails.hours = {};
    state.snmpDetails.minute = {};
    state.snmpDetails.sencond = {};
  }
  if (data.level == 0) {
    state.snmpDetails.day.valueParam = data.valueParam;
    state.snmpDetails.day.flowParam = data.flowParam;
  }
  if (data.level == 1) {
    state.snmpDetails.hours.valueParam = data.valueParam;
    state.snmpDetails.hours.flowParam = data.flowParam;
  }
  if (data.level == 2) {
    state.snmpDetails.minute.valueParam = data.valueParam;
    state.snmpDetails.minute.flowParam = data.flowParam;
  }
  if (data.level == 3) {
    state.snmpDetails.sencond.valueParam = data.valueParam;
    state.snmpDetails.sencond.flowParam = data.flowParam;
  }
};
