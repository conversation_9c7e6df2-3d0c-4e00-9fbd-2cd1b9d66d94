const lan = require('../../../common/language')
const state = {
  Person: [], // 人员管理
  ClientList: [],
  LinkTypeList: [],
  LinkTypeListss: [],
  ListLinks: [],
  AramLIst: [],
  AramRoleList: [],
  PublicNodeList: [],
  ChengzaiList: [],
  ZhongduanList: [],
  LiehuaList: [],
  linkMoreList: [],
  LinksList: [],
  RealTimeAlarmList: [],
  AlarmDetailList: [],
  historyAlarmList: [],
  LinkSearchList: [],
  LinkSearchDetailList: [],
  LinkNumberList: [],
  ListBrokenEvent: [],
  BrokenTimeList: [],
  BrokenCountList: [],
  BrokenAllList: [],
  BrokenEventList: [],
  BrokenEventBList: [],
  Deglist: [],
  degDetailsList: [],
  defaultConfigList: {},
  devModelList: [],
  // allowBack:true,
  operators: [
    // {
    //     value: 0,
    //     label: '全部'
    // },
    {
      value: 1,
      label: lan.getLabel("src.ChinaMobile")
    },
    {
      value: 2,
      label: lan.getLabel("src.ChinaUnicom")
    },
    {
      value: 3,
      label: lan.getLabel("src.ChinaTelecom")
    },
    {
      value: 4,
      label: lan.getLabel("src.CRAT")
    },
    {
      value: 6,
      label: lan.getLabel("src.other")
    },
    {
      value: 7,
      label: "--"
    }
  ],
  delayLossHistory: {
    dayData: {
      delay: [],
      loss: []
    },
    HoursData: {
      delay: [],
      loss: []
    },
    minuteData: {
      delay: [],
      loss: []
    },
    sencondData: {
      delay: [],
      loss: []
    }
  },
  flowHistory: {
    dayData: {
      enter: [],
      issue: []
    },
    HoursData: {
      enter: [],
      issue: []
    },
    minuteData: {
      enter: [],
      issue: []
    },
    sencondData: {
      enter: [],
      issue: []
    }
  },
  goodRateHistory: {
    dayData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    },
    HoursData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    },
    minuteData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    },
    sencondData: {
      nrUseRateList: [],
      nrGoodRateList: [],
      useRateList: [],
      goodRateList: []
    }
  },
  delayTlevel: "",
  flowTlevel: "",
  goodRateTlevel: "",
  flowUnit: {
    dayUnit: "",
    hoursUnit: "",
    minuteUnit: ""
  },
  snmpDetails: {
    day: {},
    hours: {},
    minute: {},
    sencond: {}
  }
};
export default state;
