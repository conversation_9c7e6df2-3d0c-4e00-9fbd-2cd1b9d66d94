<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>icon_logo.ico">
  <title></title>
</head>
<body>

<div id="app"></div>
<!-- built files will be auto injected -->
<script>

</script>
<style scoped>
  #app {
    overflow-x: hidden;
    
  }

  ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;
  }

  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    /* box-shadow: inset 0 0 5px rgba(6, 50, 77, 1); */
    box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color,rgba(6, 50, 77, 1));
    /* background: #015197; */
    background: var(--scrollbar_thumb_bg_color,#015197);
  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
    box-shadow: inset 0 0 5px var(--scrollbar_track_shadow_color,rgba(0, 0, 0, 0.2));
    border-radius: 10px;
    /* background: #06324D; */
    background: var(--scrollbar_track_bg_color,#06324D);

  }
</style>
</body>
</html>
