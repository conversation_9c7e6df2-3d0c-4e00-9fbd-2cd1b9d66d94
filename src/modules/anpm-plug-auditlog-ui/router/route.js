const lan = require('../../../common/language')
export default [
  // {
  //   path: "/",
  //   meta: {
  //     authority: true
  //   },
  //   redirect:'/log',
  // },
  // {
  //   path: "/log",
  //   name: "日志查询",
  //   meta: {
  //     authority: true
  //   },
  //   component: resolve =>
  //     require(["@/modules/anpm-plug-log-ui/views/index.vue"], resolve)
  // },
  {
    path: "/",
    meta: {
      authority: true
    },
    redirect:'/auditlog',
  },
  {
    path: "/auditlog",
    name: lan.getLabel("src.securityAudit"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-auditlog-ui/views/auditLog.vue"], resolve)
  },
];
