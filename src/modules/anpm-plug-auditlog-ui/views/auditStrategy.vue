<template>
<div class="auditStrategy">
  <!-- <Row>
    <Col span="12">
      <div class="card_box">
        <div class="card_title"><b>系统级审计</b></div>
        <div class="card_content">
          <CheckboxGroup v-model="systemCheck">
            <Checkbox v-for="(item) in currentData['1']" :disabled="item.isDefault==1" :label="item.id" :key="item.id">
              <span>{{item.name}}</span>
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </Col>
    <Col span="12">
      <div class="card_box">
        <div class="card_title"><b>业务级审计</b></div>
        <div class="card_content">
          <CheckboxGroup v-model="operationCheck">
            <Checkbox v-for="(item) in currentData['2']" :disabled="item.isDefault==1" :label="item.id" :key="item.id">
              <span>{{item.name}}</span>
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </Col>
  </Row> -->
  <Row>
    <Col span="24">
      <div class="card_box">
        <div class="card_title">
          <b>{{ $t('log_strategy') }}</b>
          <span style="margin-left:20px;"><Checkbox v-model="sysCheckAll" @on-change="setSysCheckAll">{{$t('comm_check_all')}}</Checkbox></span>
        </div>
        <div class="card_content">
          <CheckboxGroup v-model="systemCheck">
            <Checkbox v-for="(item) in currentData['1']" :disabled="item.isDefault==1" :label="item.id" :key="item.id">
              <span>{{item.name}}</span>
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </Col>
  </Row>
  <Row style="margin-top:20px;">
    <Col span="24">
      <div class="card_box">
        <div class="card_title">
          <b>{{$t('comm_service_level_audit')}}</b>
          <span style="margin-left:20px;"><Checkbox v-model="busCheckAll" @on-change="setBusCheckAll">{{$t('comm_check_all')}}</Checkbox></span>
        </div>
        <div class="card_content">
          <CheckboxGroup v-model="operationCheck">
            <Checkbox v-for="(item) in currentData['2']" :disabled="item.isDefault==1" :label="item.id" :key="item.id">
              <span>{{item.name}}</span>
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </Col>
  </Row>
  <div class="card_btn">
    <!-- <Button type="primary" icon="md-document" @click="save" >保存</Button > -->
      <Button type="info" class="jiaHao-btn ivu-btn ivu-btn-default" style="background-color:#096dd9;border: none;" @click="save">{{$t('comm_save')}}</Button>
<!--    <Button-->
<!--        type="primary"-->
<!--        ghost-->
<!--        @click="returnSave"-->
<!--    >恢复默认</Button-->
<!--    >-->
  </div>
</div>
</template>

<script>
export default {
  name: "auditStrategy",
  props:{
    tabData:{
      type: String,
      default:''
    }
  },
  data(){
    return {
      oldData:{
        "1":[],
        "2":[],
      },
      currentData:{
        "1":[],
        "2":[],
      },
      //业务审计选中的数据
      operationCheck:[],
      //系统审计选中的数据
      systemCheck:[],
      sysCheckAll:false,
      busCheckAll:false
    }
  },
  watch:{
    tabData:{
      handler(value) {
        if (value === 'auditStrategy') {
          this.getPolicyList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods:{
    getPolicyList(){
      this.$http.wisdomPost("/auditConfig/queryList")
      .then(({code,data,msg})=>{
        
        if (code===1){
          this.currentData["1"]=data.filter(item=>item.type==1);
          this.currentData["2"]=data.filter(item=>item.type==2);
        }else{
          this.currentData["1"]=[];
          this.currentData["2"]=[];
        }
      }).catch((error)=>{
        this.currentData["1"]=[];
        this.currentData["2"]=[];
      }).finally(()=>{
        this.systemCheck = this.currentData["1"].filter(item=>item.isCheck!=0 && item.type==1).map(item=>{return item.id});
        this.operationCheck = this.currentData["2"].filter(item=>item.isCheck!=0 && item.type==2).map(item=>{return item.id});
        this.oldData["1"] = this.currentData["1"];
        this.oldData["2"] = this.currentData["2"];
        if(this.systemCheck.length === this.currentData["1"].length){
          this.sysCheckAll = true;
        }
        if(this.operationCheck.length === this.currentData["2"].length){
          this.busCheckAll = true;
        }
      })
      
    },
    save(){
      const allIds = this.oldData['1'].concat(this.oldData['2']).map(item=>{return item.id});
      const checkedData = this.systemCheck.concat(this.operationCheck);
      const handleArray = (arr1,arr2)=>{
        var temp = []; //临时数组1
        var temparray = [];//临时数组2
        for (var i = 0; i < arr2.length; i++) {
          temp[arr2[i]] = true;//巧妙地方：把数组B的值当成临时数组1的键并赋值为真
        }
        for (var i = 0; i < arr1.length; i++) {
          if (!temp[arr1[i]]) {
            temparray.push(arr1[i]);//巧妙地方：同时把数组A的值当成临时数组1的键并判断是否为真，如果不为真说明没重复，就合并到一个新数组里，这样就可以得到一个全新并无重复的数组
          }
        }
        return temparray
       }
      const noCheckdData = handleArray(allIds,checkedData);
      this.$http.wisdomPost("/auditConfig/add",{enableStatusIds:checkedData.toString(),stopStatusIds:noCheckdData.toString()})
      .then(({code,data,msg})=>{
        if (code===1){
          this.$Message.success(this.$t('testspeed_save_successfully'))
        }else{
          this.$Message.warning(msg)
        }
      }).catch((error)=>{
        throw new Error(error)
      })
    },
    returnSave(){},
    //系统日志勾选全部
    setSysCheckAll(){
      if(this.sysCheckAll){
        this.systemCheck = this.currentData["1"].map(item=>{return item.id});
      }else{
        this.systemCheck = this.currentData["1"].filter(item=>item.isDefault == 1).map(item=>{return item.id});
      }
    },
    //业务日志勾选全部
    setBusCheckAll(){
      if(this.busCheckAll){
        this.operationCheck = this.currentData["2"].map(item=>{return item.id});
      }else{
        this.operationCheck = this.currentData["2"].filter(item=>item.isDefault == 1).map(item=>{return item.id});
      }
    },
  }
}
</script>

<style scoped>
.card_box{
  margin: 0 20px;
  padding: 5px 12px;
  box-shadow: 0 0 6px #dcdee2;
  border-radius: 4px;
  min-height: 300px;
  text-align: left;
  background: var(--contentBox_bgcolor, #f4f6f9);
}
.card_title{
padding: 6px 0;
  text-align: left;
}
.card_btn{
  padding: 20px;
  text-align: left;
}
.card_content .ivu-checkbox-wrapper{
  height: 40px;
  line-height: 40px;
  min-width: 200px;
  margin-right: 20px;
}

</style>