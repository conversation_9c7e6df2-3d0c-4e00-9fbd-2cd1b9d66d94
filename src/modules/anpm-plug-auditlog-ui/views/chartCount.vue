<template>
  <div class="chart" ref="chartCount"></div>
</template>

<script>
let echarts = require('echarts/lib/echarts');
require('echarts/lib/chart/line');
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
require('echarts/lib/component/markLine');
export default {
  name: "chartCount",
  props: {
    chartData: {
      type: Object,
      default: function() {
        return {};
      }
    },
    isChange: {
      type: Boolean,
      default: false
    },
    
  },
  data(){
    return {
      isdarkSkin: sessionStorage.getItem('dark') || 1,
    }
  },
  watch: {
    isChange(val) {
      this.drawChart()
    },
    isdarkSkin: {
      handler(val) {
        this.drawChart()
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.drawChart()
    top.window.addEventListener("message", (e) => {
      if (e) {
        if (e.data.type == "msg") {
          return;
        } else if (typeof e.data == "object") {
          this.isdarkSkin = e.data.isdarkSkin;
        } else if (typeof e.data == "number") {
          this.isdarkSkin = e.data;
        }
      }
    });
  },
  methods:{
    setColor(item){
      let name = item[0];
        if(name === this.$t('comm_consecutive_login_failure')){
            return '#5470c6';
        }else if(name === this.$t('comm_ip_change')){
            return '#91cc75';
        }if(name === this.$t('comm_unauthorized_access')){
            return '#fac858';
        }if(name === this.$t('login_failed')){
            return '#ee6666';
        }if(name === this.$t('login_success')){
            return '#73c0de';
        }if(name === this.$t('server_exit')){
            return '#3ba272';
        }if(name === this.$t('comm_routine_operation')){
            return '#fc8452';
        }if(name === this.$t('comm_other')){
            return '#c23531';
        }
        return '#ea7ccc';
    },
    drawChart(){
      let data = this.chartData;
      let chartDom = this.$refs['chartCount'];
      if (this.myChart) {
        this.myChart.clear();
      }
      
      this.myChart = echarts.init(chartDom);
      let sourceData = []
      for(let key in data){
        let str = "";
        if(key == 1){
          str = this.$t('comm_consecutive_login_failure');
        }else if(key == 2){
          str = this.$t('comm_ip_change');
        }else if(key == 3){
          str = this.$t('comm_unauthorized_access');
        }else if(key == 4){
          str = this.$t('comm_login_failure');
        }else if(key == 5){
          str = this.$t('comm_login_successful');
        }else if(key == 6){
          str = this.$t('comm_quit');
        }else if(key == 7){
          str = this.$t('comm_routine_operation');
        }else if(key == 8){
          str = this.$t('comm_other');
        }else if(key == 9){
          str = this.$t('operation_allow_out_login');
        }
        sourceData.push([str].concat(data[key]))
      }
      
      
      let series = [];
      for (let i =0;i<sourceData.length;i++){
        let item = {type: 'line', smooth: true, seriesLayoutBy: 'row',color:this.setColor(sourceData[i])};
        series.push(item)
      }
      var option = {
        // color:['#5087EC','#68BBC4','#58A55C','#fff','#000'],
        tooltip: {
          trigger: 'axis',
          shadowBlur: 10,
          // backgroundColor: 'rgba(20,20,20,.6)',
          backgroundColor:this.isdarkSkin ==1 ? 'rgba(18,55,127,.8)':"#FFFFFF",
          // formatter:'{b}<br/>数量: {c} <br/> 占比: {d}%'
          textStyle: {
            color: this.isdarkSkin == 1 ? '#ffffff' : '#515A6E'
          },
          extraCssText: 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16)',
          formatter:itemData=>{
            let div = '<div style="text-align: left;}">' ;
            itemData.forEach((item,index)=>{
                if (index===0){
                    div+= item.name + this.$t('comm_point') + '<br/>'
                }
                const seriesNameIndex = item.dimensionNames.findIndex(seriesItme => {return seriesItme == item.seriesName});
                div+= item.seriesName + '：' + '<span style="display: inline-block;width:70px;}">'+(item.value[seriesNameIndex]??'--')+'</span>'+'<br/>';
                if(index%2!=0){
                    div+= ''
                }
            })
            div += '</div>';
            return div
          }
        },
        legend: {
          left: 'center',
          itemGap:10,
          icon: "circle",
          top:20,
          textStyle: {
            color: top.window.isdarkSkin == 1 ? '#617ca5' : '#484b56',
            fontSize: 12,
            fontFamily: 'MicrosoftYaHei-Bold'
          }
        },
        xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
                textStyle: {
                    color: top.window.isdarkSkin == 1 ? '#617ca5' : '#0e2a5f'
                }
            },
        },
        yAxis: {
          name: this.$t('comm_unit_one'),
          nameLocation:'end',
          nameTextStyle:{
            color:'#0e2a5f'
          },
          type: "value",
          scale: true,
          axisLine:{show:true},
          splitLine: {
            show: true,
            lineStyle:{type: "dashed",width: 0.5}
          },
          axisLabel: {
                textStyle: {
                    color: top.window.isdarkSkin == 1 ? '#617ca5' : '#0e2a5f'
                }
            },
        },
        grid: {top: '60',bottom:60,left: '100',right:'100'},
        dataset: {
          source: [
            ['time', '0', '1', '2', '3', '4', '5','6', '7', '8', '9', '10', '11', '12','13', '14', '15', '16', '17', '18', '19','20', '21', '22', '23'],

          ].concat(sourceData)
        },
        series: series
      };
      this.myChart.setOption(option);
      
    },
  },
}
</script>

<style scoped>
.chart {
  height: 100%;
}
</style>