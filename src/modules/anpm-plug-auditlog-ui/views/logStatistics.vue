<template>
    <section class="sectionBox">
        <!-- 日志统计 页面 -->
        <div class="section-top">

            <Row class="fn_box">
                <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{$t('comm_time')}}{{$t('comm_colon')}}</label>
                    <div class="fn_item_box">
                        <DatePicker format="yyyy-MM-dd HH:mm:ss" type="daterange" :options="timeOptions" v-model="timeRange" :editable="false"
                                    :clearable="false" style="width:100%" :confirm='false'>
                        </DatePicker>
                    </div>
                </div>
                </Col>
                <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{$t('comm_event')}}{{$t('comm_colon')}}</label>
                    <div class="fn_item_box">
                        <Select v-model="query.alarmType" clearable filterable :only-filter-with-text="true" :placeholder="$t('comm_please_select')">
                            <Option v-for="(item,index) in eventTypeList" :value="item.id" :key="index">{{item.name}}</Option>
                        </Select>
                    </div>
                </div>
                </Col>
                <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{$t('user_account')}}{{$t('comm_colon')}}</label>
                    <div class="fn_item_box">
                        <Select v-model="query.userName" clearable filterable :placeholder="$t('comm_please_select')">
                            <Option v-for="(item,index) in userList" :value="item.userName" :key="index">{{item.userName}}
                            </Option>
                        </Select>
                    </div>
                </div>
                </Col>
            </Row>
            <!-- <div class="fn_box">
                <div class="fn_item">
                    <label class="fn_item_label">事件类型：</label>
                    <div class="fn_item_box">
                        <Select v-model="query.alarmType" style="width:160px" clearable filterable :placeholder="$t('comm_please_select')">
                            <Option v-for="(item,index) in eventTypeList" :value="item.id" :key="index">{{item.name}}</Option>
                        </Select>
                    </div>
                </div>
                <div class="fn_item">
                    <label class="fn_item_label">账号：</label>
                    <div class="fn_item_box">
                        <Select v-model="query.userName" style="width:160px" clearable filterable :placeholder="$t('comm_please_select')">
                            <Option v-for="(item,index) in userList" style="width:160px" :value="item.userName" :key="index">{{item.userName}}
                            </Option>
                        </Select>
                    </div>
                </div>
                <div class="fn_item">
                    <label class="fn_item_label">{{$t('comm_time')}}{{$t('comm_colon')}}</label>
                    <div class="fn_item_box">
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss" v-model="startTime" placement="bottom-end" :placeholder="$t('comm_please_select')"
                                    style="width: 220px" @on-change="startTimeChange" :options="startOptions" confirm></DatePicker>
                        <span style="padding: 0 10px">{{$t('comm_to')}}</span>
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss" v-model="endTime" placement="bottom-end" :placeholder="$t('comm_please_select')"
                                    style="width: 220px" :options="endOptions" confirm @on-change="endTimeChange"></DatePicker>
                    </div>
                </div>
            </div> -->
            <div class="tool-btn">
                <div>
                    <Button class="jiaHao-btn" type="primary" v-if="permissionObj.list" @click="queryClick" :title="$t('common_query')">
                        <i class="iconfont icon-icon-query" />
                    </Button>
                    <!-- <Button class="query-btn" type="primary" v-if="permissionObj.list" icon="ios-search" @click="queryClick" :title="$t('common_query')"></Button> -->
                </div>
            </div>
        </div>

        <div class="section-body">
            <div class="section-body-content">
                <div>
                    <Loading :loading="loading"></Loading>
                    <div class="chartArea">
                        <div style="height: 100%;background: var(--body_conent_b_color , #061824);">
                            <chart-count v-show="!lineData" :isChange="isChange" :chartData="chartCountData"></chart-count>
                            <div v-show="lineData" :class="{'table_empty':currentSkin==1, 'table2_empty':currentSkin == 0}">
                                <p class='emptyText'>{{$t('common_No_data')}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="chartArea">
                        <div class="chartNumArea">
                            <div class="chartTitle" style="text-indent: 20px">
                                <p>
                                    <b>{{$t('operation_visits_top10')}}</b>
                                </p>
                            </div>
                            <div class="chartBox" style="background:  var(--body_conent_b_color , #061824);margin-top: 20px;" >
                                <chart-Num v-show="!topData" :chartData="chartNumData"></chart-Num>
                                <div v-show="topData" :class="{'table_empty':currentSkin==1, 'table2_empty':currentSkin == 0}">
                                    <p class='emptyText'>{{$t('common_No_data')}}</p>
                                </div>
                            </div>
                        </div>
                        <div class="chartTypeArea">
                            <div class="chartTitle">
                                <p><b>{{$t('operation_indicates_event')}}</b></p>
                                <!-- <p style="font-size: 12px">单位（%）</p> -->
                            </div>
                            <div class="chartBox" style="background: var(--body_conent_b_color , #061824);margin-left: 10px;margin-top: 20px;" >
                                <chart-type v-show="!pieData" :chartData="chartTypeData"></chart-type>
                                <div v-show="pieData" :class="{'table_empty':currentSkin==1, 'table2_empty':currentSkin == 0}">
                                    <p class='emptyText'>{{$t('common_No_data')}}</p>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import '@/config/page.js';
import global from '@/common/global.js';
import chartCount from './chartCount.vue';
import chartNum from './chartNum.vue';
import chartType from './chartType.vue';
import moment from 'moment';

export default {
    name: 'logStatistics',
    components: {
        chartCount,
        chartNum,
        chartType
    },
    props: {
        tabData: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            //权限对象
            permissionObj: {},
            timeRange: [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')],
            // startTime: new Date().format('yyyy-MM-dd 00:00:00'),
            // endTime: new Date().format('yyyy-MM-dd 23:59:59'),
            currentTime: Date.now(),
            timeOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return [start.format('yyyy-MM-dd HH:mm:ss'), end.format('yyyy-MM-dd HH:mm:ss')];
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate()
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    }
                ]
            },
            alarmTypeList: {
                //操作类型下拉
                1: this.$t('comm_unauthorized_access'),
                2: this.$t('comm_ip_change'),
                3: this.$t('comm_consecutive_login_failure')
            },
            //搜索字段
            query: {
                alarmType: '', //操作类型
                startTime: new Date().format('yyyy-MM-dd 00:00:00'), //开始时间
                endTime: new Date().format('yyyy-MM-dd 23:59:59'), //结束时间
                userName: ''
            },
            //表格参数设置
            //loading状态
            loading: false,
            chartCountData: {},
            chartNumData: {
                alarmType: '',
                data: []
            },
            chartTypeData: {},
            lineData: false,
            pieData: false,
            topData: false,
            //top10类型切换
            numType: true,
            eventTypeList: [
                { id: 1, name: this.$t('comm_consecutive_login_failure') },
                { id: 2, name: this.$t('comm_ip_change') },
                { id: 3, name: this.$t('comm_unauthorized_access') },
                { id: 4, name: this.$t('login_failed') },
                { id: 5, name: this.$t('login_success') },
                { id: 6, name: this.$t('server_exit') },
                { id: 7, name: this.$t('comm_routine_operation') },
                { id: 8, name: this.$t('comm_other') }
                // {id:9,name:"允许时间外登录"},
            ],
            isChange: false,
            //用户列表，账号查询条件使用
            userList: []
        };
    },
    watch: {
        tabData: {
            handler(value) {
                if (value === 'logStatistics') {
                    this.numType = true;
                    this.queryClick();
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        this.getDecryptUserList();
    },
    computed: {},
    mounted() {
        this.query.pageNo = this.currentNum;
           // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
     beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
    },
    methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        //获取所有用户信息
        getDecryptUserList() {
            this.$http.wisdomPost('/user/getDecryptUserList').then(res => {
                this.userList = res.data;
            });
        },
        //重置
        restClick() {
            (this.timeRange[0] = new Date().format('yyyy-MM-dd 00:00:00')), //开始时间
                (this.timeRange[1] = new Date().format('yyyy-MM-dd 23:59:59')), //结束时间
                (this.query.alarmType = '');
            this.query.userName = '';
            this.queryClick();
        },
        //时间事件
        startTimeChange(val) {
            // this.timeRange[1] = '';
            if (val == '') {
                this.timeRange[1] = '';
                this.startOptions.disabledDate = date => {
                    return date && date.valueOf() > this.currentTime;
                };
                this.endOptions.disabledDate = date => {
                    return date && date.valueOf() > this.currentTime;
                };
            } else {
                var now = new Date(val),
                    y = now.getFullYear(),
                    m = now.getMonth() + 1,
                    h = now.getHours(),
                    min = now.getMinutes(),
                    s = now.getSeconds(),
                    d = now.getDate();
                let ss = y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d) + ' ' + now.toTimeString().substr(0, 8);
                this.endOptions.disabledDate = date => {
                    let checkedDay = new Date(y + '-' + m + '-' + d + ' 00:00:00').valueOf();
                    let checkedTime = new Date(this.timeRange[0]).valueOf();
                    let interTime = checkedTime - checkedDay;
                    let startTime = this.timeRange[0] ? new Date(this.timeRange[0]).valueOf() : '';
                    let endTime = val ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000 : '';
                    return (date && date.valueOf() < startTime - interTime) || (date && date.valueOf() > (endTime > this.currentTime ? this.currentTime : endTime));
                };
            }
        },
        endTimeChange(data, type) {
            if (type == 'date') {
                this.timeRange[1] = new Date(data).format('yyyy-MM-dd 23:59:59');
            }
        },

        queryClick() {
            let startVal = moment(this.timeRange[0] === '' || this.timeRange[0] == undefined ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0)) : this.timeRange[0], 'YYYY-MM-DD hh:mm:ss').valueOf();
            let endVal = moment(this.timeRange[1] === '' || this.timeRange[1] == undefined ? new Date() : this.timeRange[1], 'YYYY-MM-DD hh:mm:ss').valueOf();
            console.log('startVal', startVal / 1000 / 3600 / 24);
            console.log('endVal', endVal / 1000 / 3600 / 24);
            console.log('时间跨度1：', endVal / 1000 / 3600 / 24 - startVal / 1000 / 3600 / 24);
            console.log('时间跨度2：', (endVal - startVal) / 1000 / 3600 / 24);
            if ((endVal - startVal) / 1000 / 3600 / 24 > 31) {
                this.$Message.warning('时间跨度不得大于30天');
                return;
            }
            //点击搜索
            this.query.startTime = new Date(this.timeRange[0]).format('yyyy-MM-dd HH:mm:ss');
            this.query.endTime = new Date(this.timeRange[1]).format('yyyy-MM-dd HH:mm:ss');
            this.getbrokenLineData(this.query);
            this.getPieData(this.query);
            this.getvisitUserTopData(this.query);
        },
        sortData(data) {
            data.sort((a, b) => {
                return a.total - b.total > 0 ? 1 : -1;
            });
            return data;
        },
        //TOP10类型切换事件
        numChange() {
            this.chartNumData.data = [];
            if (!this.numType) {
                this.getvisitUserTopData(this.query);
            } else {
                this.getvisitIpTopData(this.query);
            }
            this.numType = !this.numType;
        },
        //获取权限告警折线图数据
        getbrokenLineData(param) {
            console.log('param', param);
            this.$http
                .PostJson('/audit/brokenLine', param)
                .then(res => {
                    if (res.code === 1) {
                        this.loading = false;
                        if (res.data && JSON.stringify(res.data) !== '{}') {
                            this.isChange = !this.isChange;
                            this.lineData = false;
                            this.chartCountData = JSON.parse(JSON.stringify(res.data));
                        } else {
                            this.chartCountData = {};
                            this.lineData = true;
                        }
                        this.loading = false;
                    } else {
                        this.chartCountData = {};
                        this.lineData = true;
                        this.$Message.error(res.msg);
                    }
                })
                .catch(err => {
                    this.chartCountData = {};
                    this.lineData = true;
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        //获取饼图占比数据
        getPieData(param) {
            this.$http
                .PostJson('/audit/pieData ', param)
                .then(res => {
                    if (res.code === 1) {
                        this.loading = false;
                        if (res.data && JSON.stringify(res.data) !== '{}') {
                            // if (param.alarmType=='' || param.alarmType==undefined){
                            //   this.chartTypeData=[
                            //     { value: res.data['1'], name: this.$t('comm_unauthorized_access'),color:'#5087EC' },
                            //     { value: res.data['2'], name: this.$t('comm_ip_change'),color:'#68BBC4' },
                            //     { value: res.data['3'], name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                            //   ];
                            // }else if (param.alarmType==1){
                            //   this.chartTypeData=[
                            //     { value: res.data['1'], name: this.$t('comm_unauthorized_access'),color:'#5087EC' }
                            //   ];
                            // }else if (param.alarmType==2){
                            //   this.chartTypeData=[
                            //     { value: res.data['2'], name: this.$t('comm_ip_change'),color:'#68BBC4' }
                            //   ];
                            // }else if (param.alarmType==3){
                            //   this.chartTypeData=[
                            //     { value: res.data['3'], name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                            //   ];
                            // }
                            this.pieData = false;
                            this.chartTypeData = res.data;
                        } else {
                            this.chartTypeData = {};
                            this.pieData = true;
                            // if (param.alarmType==''){
                            //   this.chartTypeData=[
                            //     { value: 0, name: this.$t('comm_unauthorized_access'),color:'#5087EC' },
                            //     { value: 0, name: this.$t('comm_ip_change'),color:'#68BBC4' },
                            //     { value: 0, name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                            //   ];
                            // }else if (param.alarmType==1){
                            //   this.chartTypeData=[
                            //     { value: 0, name: this.$t('comm_unauthorized_access'),color:'#5087EC' }
                            //   ];
                            // }else if (param.alarmType==2){
                            //   this.chartTypeData=[
                            //     { value: 0, name: this.$t('comm_ip_change'),color:'#68BBC4' }
                            //   ];
                            // }else if (param.alarmType==3){
                            //   this.chartTypeData=[
                            //     { value: 0, name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                            //   ];
                            // }
                        }
                        this.loading = false;
                    } else {
                        this.chartTypeData = {};
                        this.pieData = true;
                        // if (param.alarmType==''){
                        //   this.chartTypeData=[
                        //     { value: 0, name: this.$t('comm_unauthorized_access'),color:'#5087EC' },
                        //     { value: 0, name: this.$t('comm_ip_change'),color:'#68BBC4' },
                        //     { value: 0, name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                        //   ];
                        // }else if (param.alarmType==1){
                        //   this.chartTypeData=[
                        //     { value: 0, name: this.$t('comm_unauthorized_access'),color:'#5087EC' }
                        //   ];
                        // }else if (param.alarmType==2){
                        //   this.chartTypeData=[
                        //     { value: 0, name: this.$t('comm_ip_change'),color:'#68BBC4' }
                        //   ];
                        // }else if (param.alarmType==3){
                        //   this.chartTypeData=[
                        //     { value: 0, name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                        //   ];
                        // }
                        this.$Message.error(res.msg);
                    }
                })
                .catch(err => {
                    this.chartTypeData = {};
                    this.pieData = true;
                    // if (param.alarmType==''){
                    //   this.chartTypeData=[
                    //     { value: 0, name: this.$t('comm_unauthorized_access'),color:'#5087EC' },
                    //     { value: 0, name: this.$t('comm_ip_change'),color:'#68BBC4' },
                    //     { value: 0, name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                    //   ];
                    // }else if (param.alarmType==1){
                    //   this.chartTypeData=[
                    //     { value: 0, name: this.$t('comm_unauthorized_access'),color:'#5087EC' }
                    //   ];
                    // }else if (param.alarmType==2){
                    //   this.chartTypeData=[
                    //     { value: 0, name: this.$t('comm_ip_change'),color:'#68BBC4' }
                    //   ];
                    // }else if (param.alarmType==3){
                    //   this.chartTypeData=[
                    //     { value: 0, name: this.$t('comm_consecutive_login_failure'),color:'#58A55C' }
                    //   ];
                    // }
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        //账户访问次数TOP10
        getvisitUserTopData(param) {
            this.chartNumData.alarmType = param.alarmType || '';
            this.$http
                .PostJson('/audit/visitUserTop', param)
                .then(res => {
                    if (res.code === 1) {
                        this.loading = false;
                        if (res.data && res.data.length > 0) {
                            this.topData = false;
                            this.chartNumData.data = res.data;

                            // for (let key in data){
                            //   let item = data[key];
                            //   arrayData.push({
                            //     name:key,
                            //     a1:item['1']||0,
                            //     a2:item['2']||0,
                            //     a3:item['3']||0,
                            //     total:eval(Object.values(item).join("+")),
                            //   })
                            // }

                            // const datas = this.sortData(Object.assign(arrayData,[]));
                            // this.chartNumData.data = datas;
                        } else {
                            this.chartNumData.data = [];
                            this.topData = true;
                        }
                        this.loading = false;
                    } else {
                        this.chartNumData.data = [];
                        this.topData = true;
                        this.$Message.error(res.msg);
                    }
                })
                .catch(err => {
                    this.chartNumData.data = [];
                    this.topData = true;
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        //获取IP访问次数TOP10
        getvisitIpTopData(param) {
            this.chartNumData.alarmType = param.alarmType || '';
            this.$http
                .wisdomPost('/log/visitIpTop ', param)
                .then(res => {
                    if (res.code === 1) {
                        this.loading = false;
                        if (res.data && res.data.length > 0) {
                            this.topData = false;
                            this.chartNumData.data = res.data;
                            // let data = res.data;
                            // let arrayData = [];
                            // for (let key in data){
                            //   let item = data[key];
                            //   arrayData.push({
                            //     name:key,
                            //     a1:item['1']||0,
                            //     a2:item['2']||0,
                            //     a3:item['3']||0,
                            //     total:eval(Object.values(item).join("+")),
                            //   })
                            // }
                            // const datas = this.sortData(Object.assign(arrayData,[]));
                            // this.chartNumData.data = datas;
                            // console.log(this.chartNumData)
                        } else {
                            this.chartNumData.data = [];
                            this.topData = true;
                        }
                        this.loading = false;
                    } else {
                        this.chartNumData.data = [];
                        this.topData = true;
                        this.$Message.error(res.msg);
                    }
                })
                .catch(err => {
                    this.chartNumData.data = [];
                    this.topData = true;
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    }
};
</script>

<style scoped lang='less'>
.section-body {
    background: var(--contentBox_bgcolor, #f4f6f9);
}
.searchTop .ivu-select-input {
    height: 32px;
    line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
    background: #f1f6fe !important;
}
.chartArea {
    height: 400px;
    margin-bottom: 20px;
    text-align: center;
}
.chartArea .chartNumArea,
.chartArea .chartTypeArea {
    width: 50%;
    display: inline-block;
    height: 100%;
    float: left;
}
.chartTitle {
    text-align: left;
    line-height: 21px;
    text-indent: 120px;
}
.chartBox {
    height: calc(100% - 21px);
}
.table_empty {
    height: 100% !important;
    background-position: center 110px !important;
    .emptyText {
        top: calc(45% + 50px) !important;
    }
}
</style>