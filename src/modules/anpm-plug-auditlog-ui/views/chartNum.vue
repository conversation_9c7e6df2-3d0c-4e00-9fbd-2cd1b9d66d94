<template>
  <div class="chart" ref="chartNum"></div>
</template>

<script>
let echarts = require('echarts/lib/echarts');
require('echarts/lib/chart/bar');
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
require('echarts/lib/component/markLine');
export default {
    name: 'chartNum',
    props: {
        chartData: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    data() {
        return {
            datas: [],
           isdarkSkin: sessionStorage.getItem('dark') || 1,
        };
    },
    watch: {
        chartData: {
            handler(val) {
                //console.log(val.alarmType)
                if (JSON.stringify(val) !== '{}') {
                    this.drawChart(val.data, val.alarmType);
                }
            },
            deep: true
            // immediate: true
        },
        isdarkSkin: {
            handler(val) {
                this.drawChart(JSON.parse(JSON.stringify(this.chartData.data)), this.chartData.alarmType)
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {
        this.drawChart(JSON.parse(JSON.stringify(this.chartData.data)), this.chartData.alarmType);
        top.window.addEventListener("message", (e) => {
            if (e) {
                if (e.data.type == "msg") {
                return;
                } else if (typeof e.data == "object") {
                this.isdarkSkin = e.data.isdarkSkin;
                } else if (typeof e.data == "number") {
                this.isdarkSkin = e.data;
                }
            }
        });
    },
    beforeDestroy() {
        top.wondow.removeEventListener("message", (e) => {
            // console.log(e)
        });

    },
    methods: {
        sortData(data) {
            data.sort((a, b) => {
                return a.total - b.total > 0 ? 1 : -1;
            });
            return data;
        },
        setColor(item){
            if(item.name === this.$t('comm_consecutive_login_failure')){
                return '#5470c6';
            }else if(item.name === this.$t('comm_ip_change')){
                return '#91cc75';
            }if(item.name === this.$t('comm_unauthorized_access')){
                return '#fac858';
            }if(item.name === this.$t('comm_login_failure')){
                return '#ee6666';
            }if(item.name === this.$t('comm_login_successful')){
                return '#73c0de';
            }if(item.name === this.$t('comm_quit')){
                return '#3ba272';
            }if(item.name === this.$t('comm_routine_operation')){
                return '#fc8452';
            }if(item.name === this.$t('comm_other')){
                return 'red';
            }
            // return '#ea7ccc';
        },
        drawChart(data, type) {
            let newData = [];
            let arrayData = [];
            let userName = [];
            let resultArry = []
            // 原生数据排序
            data.forEach(item=>{
                let val = 0
                item.list.forEach(childItem=>{
                    val = val+childItem.value
                })
                resultArry.push({ val, item})
            })
            resultArry.sort((a,b)=>{ return a.val-b.val})
            newData =  resultArry.map(item=>{return item['item']})
            // 组装echart需要的数据
            newData.forEach(item => {
                userName.push(item.userName);
                arrayData.push(...item.list);
            });
            let newList = [];
            arrayData.forEach(item => {
                let newItem = newList.find(i => i.name == item.name);
                if (!newItem) {
                    newList.push({ name: item.name, value: [item.value],val:[] });
                } else {
                    newItem.value.push(item.value);
                }
            });
             newList.forEach(item=>{
                 let arry = []
                 newData.forEach(subItem=>{
                     let value = ''
                    subItem.list.forEach(i=>{
                         if (item.name === i.name) {
                             value = i.value
                         }
                    })
                    arry.push(value)
                 })
                 item.val.push(...arry)
             })
            const datas = newData;
            let chartDom = this.$refs['chartNum'];
            if (this.myChart) {
                this.myChart.clear();
            }
            this.myChart = echarts.init(chartDom);
            const seriesFn = (type1, data) => {
                let series = [];
                newList.map(item => {
                    let dataArr = {
                        color:this.setColor(item),
                        name: item.name,
                        type: 'bar',
                        stack: 'total',
                        barMaxWidth: 25,
                        label: {
                            show: true,
                            position: 'inside',
                            fontSize: item.valueitem > 0 ? 12 : 0
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: item.val
                    };
                    series.push(dataArr);
                     
                });
                return series;
            };
            var option;
            option = {
                // color:type == '' ? ['#5087EC','#68BBC4','#58A55C'] : type == 1 ? ['#5087EC'] : type == 2 ? ['#68BBC4'] : ['#58A55C'] ,
                // color:['#5087EC','#68BBC4','#58A55C'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none' // 'shadow' as default; can also be 'line' or 'shadow'
                    },
                    backgroundColor:this.isdarkSkin ==1 ? 'rgba(18,55,127,.8)':"#FFFFFF",
                      textStyle: {
            color: this.isdarkSkin == 1 ? '#ffffff' : '#515A6E'
          },
          extraCssText: 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16)',
                    formatter: itemData => {
                        let div = '<div style="text-align: left;}">';
                        itemData.forEach((item, index) => {
                            if (index === 0) {
                                div += item.name + '<br/>';
                            }
                            div += item.seriesName + '：' + `${item.value?item.value:'--'}` + '<br/>';
                        });
                        div += '</div>';
                        return div;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    top: 10,
                    bottom: '40',
                    containLabel: true
                },
                xAxis: {
                    show: false,
                    type: 'value',

                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'category',
                    // splitNumber:10,
                    axisLabel: {
                        textStyle: {
                            color: top.window.isdarkSkin == 1 ? '#617ca5' : '#0e2a5f'
                        }
                    },

                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        // show:false
                    },
                    data: userName
                },
                legend: {
                    left: 'center',
                    itemGap: 10,
                    itemWidth: 18,
                    itemHeight: 12,
                    bottom: 10,
                    left: 50,
                    right: 10,
                    textStyle: {
                        color: top.window.isdarkSkin == 1 ? '#617ca5' : '#484b56',
                        fontSize: 12,
                        fontFamily: 'MicrosoftYaHei-Bold'
                    }
                    // data:type == '' ? [this.$t('comm_unauthorized_access'),this.$t('comm_ip_change'),this.$t('comm_consecutive_login_failure')] : type == 1 ? [this.$t('comm_unauthorized_access')] : type == 2 ? [this.$t('comm_ip_change')] : [this.$t('comm_consecutive_login_failure')]
                    // selectedMode:false
                }
            };

            option.series = seriesFn(type, datas);
            option && this.myChart.setOption(option);
            // this.myChart.off('legendselectchanged');
            // this.myChart.on("legendselectchanged", (params)=> {console.log(params)
            //   let chartData = JSON.parse(JSON.stringify(this.chartData.data));
            //   if (params.selected[this.$t('comm_unauthorized_access')] && params.selected[this.$t('comm_ip_change')] && params.selected[this.$t('comm_consecutive_login_failure')]){
            //     chartData.sort((a,b)=>{
            //       return a.a1+a.a2+a.a3 - b.a1-b.a2-b.a3;
            //     })
            //   }else if (params.selected[this.$t('comm_unauthorized_access')] && params.selected[this.$t('comm_ip_change')] && !params.selected[this.$t('comm_consecutive_login_failure')]){
            //     chartData.sort((a,b)=>{
            //       return a.a1+a.a2 - b.a1-b.a2 ;
            //     })
            //   }else if (params.selected[this.$t('comm_unauthorized_access')] && params.selected[this.$t('comm_consecutive_login_failure')] && !params.selected[this.$t('comm_ip_change')]){
            //     chartData.sort((a,b)=>{
            //       return a.a1+a.a3 - b.a1-b.a3 ;
            //     })
            //   }else if (params.selected[this.$t('comm_ip_change')] && params.selected[this.$t('comm_consecutive_login_failure')] && !params.selected[this.$t('comm_unauthorized_access')]){
            //     chartData.sort((a,b)=>{
            //       return a.a2+a.a3 - b.a2-b.a3;
            //     })
            //   }else if (params.selected[this.$t('comm_ip_change')] && !params.selected[this.$t('comm_consecutive_login_failure')] && !params.selected[this.$t('comm_unauthorized_access')]){
            //     chartData.sort((a,b)=>{
            //       return a.a2 - b.a2;
            //     })
            //   }else if (!params.selected[this.$t('comm_ip_change')] && params.selected[this.$t('comm_consecutive_login_failure')] && !params.selected[this.$t('comm_unauthorized_access')]){
            //     chartData.sort((a,b)=>{
            //       return a.a3 - b.a3;
            //     })
            //   }else if (!params.selected[this.$t('comm_ip_change')] && !params.selected[this.$t('comm_consecutive_login_failure')] && params.selected[this.$t('comm_unauthorized_access')]){
            //     chartData.sort((a,b)=>{
            //       return a.a1 - b.a1 ;
            //     })
            //   }
            //   option.yAxis.data = chartData.map(item=>{return item.name});
            //   option.series = seriesFn(type,chartData);
            //   option && this.myChart.setOption(option);
            // })
        }
    }
};
</script>

<style scoped>
.chart {
  height: 100%;
}
</style>