<template>
  <div class="chart" ref="chartType"></div>
</template>

<script>
let echarts = require('echarts/lib/echarts');
require('echarts/lib/chart/pie');
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
require('echarts/lib/component/markLine');
export default {
  name: "chartType",
  props: {
    chartData: {
      type: Object,
      default: function() {
        return {};
      }
    },
  },
  data() {
      return {
          datas: [],
             isdarkSkin: sessionStorage.getItem('dark') || 1,
      };
  },
  watch: {
    chartData: {
      handler(val) {
        if (JSON.stringify(val) !== "[]") {
          this.drawChart(val)
        }
      },
      deep: true,
      // immediate: true
    },
    isdarkSkin: {
        handler(val) {
            this.drawChart(this.chartData)
        },
        deep: true,
        immediate: true,
    },
  },
  mounted() {
    this.drawChart(this.chartData)
    top.window.addEventListener("message", (e) => {
        if (e) {
            if (e.data.type == "msg") {
            return;
            } else if (typeof e.data == "object") {
            this.isdarkSkin = e.data.isdarkSkin;
            } else if (typeof e.data == "number") {
            this.isdarkSkin = e.data;
            }
        }
    });
  },
  beforeDestroy() {
        top.wondow.removeEventListener("message", (e) => {
            // console.log(e)
        });

    },
  methods:{
    drawChart(data){
      let chartDom = this.$refs['chartType'];
      const myChart = echarts.init(chartDom);
      var option;
      let total = 0;
      let newArr = []
      let colorArr = []
      for(let key in data){
        if(key != 'total'){
          let str = "";
          let color = '#ea7ccc';
          if(key == 1){
            str = this.$t('comm_consecutive_login_failure');
            color = '#5470c6';
          }else if(key == 2){
            str = this.$t('comm_ip_change');
            color = '#91cc75';
          }else if(key == 3){
            str = this.$t('comm_unauthorized_access');
            color = '#fac858';
          }else if(key == 4){
            str = this.$t('comm_login_failure');
            color = '#ee6666';
          }else if(key == 5){
            str = this.$t('comm_login_successful');
            color = '#73c0de';
          }else if(key == 6){
            str = this.$t('comm_quit');
            color = '#3ba272';
          }else if(key == 7){
            str = this.$t('comm_routine_operation');
            color = '#fc8452';
          }else if(key == 8){
            str = this.$t('comm_other');
            color = '#c23531';
          }
          colorArr.push(color);
          newArr.push({value:data[key],name:str})
        }
      }
      newArr.forEach(item=>{
        total += (item.value?item.value:0);
      })
      option = {
        title: {
          text: this.$t('lisence_total'),
          textStyle:{
            color: top.window.isdarkSkin == 1 ? '#617ca5' : '#484b56',
            fontWeight:'normal',
            fontSize:16
          },
          subtextStyle:{
            fontWeight:'normal',
            fontSize:14,
             color: top.window.isdarkSkin == 1 ? '#617ca5' : '#484b56',
          },
          subtext: Math.ceil(total)+'',
          x: 'center',
          y: '128'
        },
        // color:data.map(item=>{return item.color}),
        tooltip: {
          trigger: 'item',
          backgroundColor:this.isdarkSkin ==1 ? 'rgba(18,55,127,.8)':"#FFFFFF",
           textStyle: {
            color: this.isdarkSkin == 1 ? '#ffffff' : '#515A6E'
          },
          extraCssText: 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16)',
          // formatter:'{b}<br/>数量: {c} <br/> 占比: {d}%'
          formatter:itemData=>{
            const data = itemData.data;
            return '<div style="text-align: left;}">' +
                itemData.name+'<br/>'+
                this.$t('comm_quantity')+this.$t('comm_colon')+Math.ceil(itemData.value)+'<br/>'+
                this.$t('comm_proportion')+this.$t('comm_colon')+itemData.percent+'%'+'<br/>'+
                '</div>'

          }
        },
        legend: {
          left: 'center',
          itemGap:10,
          itemWidth:18,
          itemHeight:12,
          bottom:10,
          left:50,
          right:10,
          textStyle: {
            color: top.window.isdarkSkin == 1 ? '#617ca5' : '#484b56',
            fontSize: 12,
            fontFamily: 'MicrosoftYaHei-Bold'
          }
        },
        emphasis: {
          disabled:true,
          scale:false,
          scaleSize:1,
          label: {
            show: true,
          }
        },
        series: [
          {
            color:colorArr,
            name: this.$t('operation_indicates_event'),
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: true,
            stillShowZeroSum: true,
            top:10,
            bottom:40,
            startAngle:90,
            hoverAnimation:false,
            label: {
              show: true,
              // position: 'inner',
              formatter:'{d}'+'%'
            },

            // labelLine: {
            //   show: false
            // },
            data: newArr
          },
        ]
      };

      option && myChart.setOption(option);
    },
  },
}
</script>

<style scoped>
.chart {
  height: 100%;
}
</style>