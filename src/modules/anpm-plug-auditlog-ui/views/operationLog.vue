<template>
  <section class="sectionBox">
    <!--  操作日志页面-->
    <div class="section-top">
      <Row class="fn_box">
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
              >
              </DatePicker>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("user_account") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.userName"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('comm_please_select')"
              >
                <Option
                  v-for="(item, index) in userList"
                  :value="item.userName"
                  :key="index"
                  >{{ item.userName }}
                </Option>
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_account_types") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.userType"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('comm_please_select')"
              >
                <Option value="1">{{ $t("comm_permanent_account") }}</Option>
                <Option value="2">{{ $t("comm_temporary_account") }}</Option>
              </Select>
            </div>
          </div>
        </Col>
        <!--                <Col span="6">-->
        <!--                <div class="fn_item">-->
        <!--                    <label class="fn_item_label">{{$t('comm_module')}}{{$t('comm_colon')}}</label>-->
        <!--                    <div class="fn_item_box">-->
        <!--                        <Input v-model.trim="query.funName" :placeholder="$t('comm_enter')+$t('comm_module')" />-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--                </Col>-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_operation_type") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.operationType"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('snmp_pl_man')"
              >
                <Option
                  v-for="(item, index) in operateTypeList"
                  :value="item.value"
                  :key="index"
                  >{{ item.lable }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <!-- <Col span="6">
                <div class="fn_item">
                    <label class="fn_item_label">{{$t('comm_keywords')}}：</label>
                    <div class="fn_item_box">
                        <Input v-model.trim="query.keyword" :placeholder="$t('operation_log_keywords')" />
                    </div>
                </div>
                </Col> -->
      </Row>

      <!-- <div class="fn_box">
                <div class="fn_item">
                    <label class="fn_item_label">{{$t('comm_time')}}{{$t('comm_colon')}}</label>
                    <div class="fn_item_box">
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss" v-model="startTime" placement="bottom-end" :placeholder="$t('comm_please_select')"
                                    style="width: 220px" :options="startOptions" confirm></DatePicker>
                        <span style="padding: 0 10px">{{$t('comm_to')}}</span>
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss" v-model="endTime" placement="bottom-end" :placeholder="$t('comm_please_select')"
                                    style="width: 220px" :options="endOptions" confirm @on-change="endTimeChange"></DatePicker>
                    </div>
                </div>
                <div class="fn_item">
                    <label class="fn_item_label">账号：</label>
                    <div class="fn_item_box">
                        <Select v-model="query.userName" style="width:160px" clearable filterable :placeholder="$t('comm_please_select')">
                            <Option v-for="(item,index) in userList" style="width:160px" :value="item.userName" :key="index">{{item.userName}}
                            </Option>
                        </Select>
                    </div>
                </div>
                <div class="fn_item">
                    <label class="fn_item_label">账号类型：</label>
                    <div class="fn_item_box">
                        <Select v-model="query.userType" style="width:160px" clearable filterable :placeholder="$t('comm_please_select')">
                            <Option value="1">长期账号</Option>
                            <Option value="2">临时账号</Option>
                        </Select>
                    </div>
                </div>
                <div class="fn_item">
                    <label class="fn_item_label">模块：</label>
                    <div class="fn_item_box">
                        <Input v-model.trim="query.funName" placeholder="输入模块名" style="width: 160px" />
                    </div>
                </div>
                <div class="fn_item">
                    <label class="fn_item_label">操作类型：</label>
                    <div class="fn_item_box">
                        <Select v-model="query.operationType" style="width:160px" clearable filterable :placeholder="$t('comm_please_select')">
                            <Option v-for="(item,index) in operateTypeList" :value="item.value" :key="index">{{item.lable}}</Option>
                        </Select>
                    </div>
                </div>

                <div class="fn_item">
                    <label class="fn_item_label">{{$t('comm_keywords')}}：</label>
                    <div class="fn_item_box">
                        <Input v-model.trim="query.keyword" placeholder="支持按操作日志进行模糊查找" style="width: 300px" />
                    </div>
                </div>
            </div> -->
      <div class="tool-btn">
        <div>
          <Button
            class="jiaHao-btn"
            type="primary"
            v-if="permissionObj.list"
            @click="queryClick"
            :title="$t('common_query')"
          >
            <i class="iconfont icon-icon-query" />
          </Button>
          <Button
            class="delete-btn"
            type="primary"
            v-if="permissionObj.delete"
            @click="deleteClick"
            :title="$t('common_delete')"
          >
            <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
          </Button>
          <!-- <Button class="query-btn" type="primary" v-if="permissionObj.list" icon="ios-search" @click="queryClick" :title="$t('common_query')"></Button>
                    <Button class="query-btn" type="primary" v-if="permissionObj.delete" icon="md-trash" @click="deleteClick" :title="$t('common_delete')"></Button> -->
        </div>
      </div>
    </div>

    <div class="section-body">
      <div class="section-body-content">
        <div>
          <Loading :loading="loading"></Loading>
          <Table
            ref="tableList"
            stripe
            :columns="columns"
            :data="tableList"
            @on-sort-change="sortQuery"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
            :no-data-text="
              loading
                ? ''
                : tableList.length > 0
                ? ''
                : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
          ></Table>
          <div
            class="tab-page"
            style="border-top: 0"
            v-if="tableList.length > 0"
          >
            <Page
              v-page
              :current.sync="currentNum"
              :page-size="query.pageSize"
              :total="totalCount"
              :page-size-opts="pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import '@/config/page.js';
import global from '@/common/global.js';
import moment from 'moment';
import ipv6Format from "@/common/ipv6Format";

export default {
    name: 'operationLog',
    props: {
        tabData: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
                  currentSkin: sessionStorage.getItem('dark') || 1,
            timeRange: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')],
            //权限对象
            permissionObj: {},
            currentTime: Date.now(),
            timeOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return [start.format('yyyy-MM-dd HH:mm:ss'), end.format('yyyy-MM-dd HH:mm:ss')];
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate()
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    }
                ]
            },
            //操作类型下拉
            operateTypeList: [],
            //事件类型下拉
            eventTypeList: [
                { id: 1, name: this.$t('comm_consecutive_login_failure')},
                { id: 2, name: this.$t('comm_ip_change') },
                { id: 3, name: this.$t('comm_unauthorized_access') },
                { id: 4, name: this.$t('comm_login_failure') },
                { id: 5, name: this.$t('comm_login_successful') },
                { id: 6, name: this.$t('comm_quit') },
                { id: 7, name: this.$t('comm_routine_operation') },
                { id: 8, name: this.$t('comm_other')}
                // {id:9,name:"允许时间外登录"},
            ],
            //搜索字段
            query: {
                logType: 2,
                userName: '', //账号
                userType: '', //账号类型
                funName: '', //模块名
                operationType: '', //操作类型
                startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format('yyyy-MM-dd 00:00:00'), //开始时间
                endTime: new Date().format('yyyy-MM-dd 23:59:59'), //结束时间
                pageNo: 1, //页数
                pageSize: 10, //每页展示多少数据
                keyword: '',
                fieldName: 'createDate',
                orderBy: 'desc',
                flag: 'business'
            },
            //表格参数设置
            //loading状态
            loading: false,
            //当前页数
            currentNum: 1,
            //总条数
            totalCount: 0,
            //表格每页多少条数据
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            //表格数据
            tableList: [],
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: new Set(),
            selectedDatas: new Set(),
            columns: [
                {
                    //多选
                    type: 'selection',
                    width: 30,
                    className: 'bgColor',
                    align: 'center',
                    fixed:'left'
                },
                {
                    title: this.$t('user_account'),
                    key: 'userName',
                    align: 'left',
                    className: 'bgColor',
                    width:120,
                    sortable: 'custom',
                    render: (h, params) => {
                        let str = params.row.userName;
                        str = str === undefined || str === null || str === '' ? '--' : str
                        // return h('span', str === undefined || str === null || str === '' ? '--' : str);
                                if(str !== "--") {
                    return h('div',{class:'table-ellipsis'},[
                    h('Tooltip',{
                        props: {
                        placement:'top-start',
                        content: str,
                        }

                    },str)

                    ])
                    }else {
                    return h('div',str)
                    }
                    }
                },
                {
                    title: this.$t('comm_account_types'),
                    key: 'userType',
                    align: 'left',
                    className: 'bgColor',
                    sortable: 'custom',
                    width: 160,
                    render: (h, params) => {
                        if (params.row.userType == 1) {
                            return h('span', this.$t('comm_permanent_account'));
                        } else if (params.row.userType == 2) {
                            return h('span', this.$t('comm_temporary_account'));
                        }
                    }
                },
                {
                    title: this.$t('comm_access_ip'),
                    key: 'loginIp',
                    align: 'left',
                    className: 'bgColor',
                    sortable: 'custom',
                    width:200,
                    render: (h, params) => {
                        let str = params.row.loginIp;
                        str = ( str === undefined || str === null || str === '' ? '--' : str);
                         let maxWidth = params.column.width || 200; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                    }
                },
                {
                    title: this.$t('comm_module'),
                    key: 'funName',
                    align: 'left',
                    className: 'bgColor',
                    sortable: 'custom',
                    minWidth: 150,
                    render: (h, params) => {
                        let str = params.row.funName;
                        // return h('span', str === undefined || str === null || str === '' ? '--' : str);
                        str = str === undefined || str === null || str === '' ? '--' : str
                            if(str !== "--") {
                    return h('div',{class:'table-ellipsis'},[
                    h('Tooltip',{
                        props: {
                        placement:'top-start',
                        content: str,
                        }

                    },str)

                    ])
                    }else {
                    return h('div',str)
                    }
                    }
                },
                {
                    title: this.$t('comm_operation_type'),
                    key: 'operationType',
                    align: 'left',
                    className: 'bgColor',
                    sortable: 'custom',
                    width:100,
                    render: (h, params) => {
                        const operationType = params.row.operationType;
                        let arr = this.operateTypeList.filter(t => t.value == params.row.operationType);
                        return h('span', arr && arr.length > 0 ? arr[0].lable : '');
                    }
                },
                {
                    title: this.$t('operation_log_operation_log'),
                    key: 'description',
                    align: 'left',
                    tooltip: true,
                    className: 'bgColor',
                    minWidth: 200
                },

                {
                    title: this.$t('comm_time'),
                    key: 'createDate',
                    align: 'left',
                    className: 'bgColor',
                    sortable: 'custom',
                    sortType: 'desc',
                    width:160,
                    render: (h, params) => {
                        let str = params.row.createDate;
                        return h('span', str === undefined || str === null || str === '' ? '--' : new Date(str).format('yyyy-MM-dd HH:mm:ss'));
                    }
                },
                {
                    title: this.$t('common_controls'),
                    width: '120',
                    align: 'center',
                    className: 'bgColor',
                    fixed:'right',
                    render: (h, params) => {
                        let auditLogdelete = h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'left-end',
                                    transfer: true
                                }
                            },
                            [
                                h('span', {
                                    class: 'del1-btn',
                                    style: {
                                        display: this.permissionObj.delete ? 'inline-block' : 'none',
                                        marginRight: '0px',

                                    },
                                    on: {
                                        click: () => {
                                            this.auditLogdelete(params.row);
                                        }
                                    }
                                }),
                                h('span', { slot: 'content' }, this.$t('common_delete'))
                            ]
                        );
                        // let auditLogdelete = h(
                        //     'a',
                        //     {
                        //         class: 'action-btn action-red',
                        //         style: {
                        //             background: 'none',
                        //             color: 'red', //: "#FB204D"
                        //             fontWeight: '600',
                        //             display: this.permissionObj.delete ? 'inline-block' : 'none'
                        //         },
                        //         on: {
                        //             click: () => {
                        //                 this.auditLogdelete(params.row);
                        //             }
                        //         }
                        //     },
                        //     this.$t('common_delete')
                        // );

                        let array = [];
                        array.push(auditLogdelete);
                        // return h('div', array);
                        return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                    }
                }
            ],
            //用户列表，账号查询条件使用
            userList: []
        };
    },
    watch: {
        tabData: {
            handler(value) {
                if (this.tableList.length === 0 && value === 'operationLog') {
                    this.getList(this.query);
                }
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        this.getBusinessOperationType();
        this.getDecryptUserList();
    },
    computed: {},
        beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
                 // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        this.query.pageNo = this.currentNum;
    },
    methods: {
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        //获取所有用户信息
        getDecryptUserList() {
            this.$http.wisdomPost('/user/getDecryptUserList').then(res => {
                this.userList = res.data;
            });
        },
        //排序查询
        sortQuery(column) {
            if (column.order == 'normal') {
                this.query.fieldName = 'createDate';
                this.query.orderBy = 'desc';
            } else {
                this.query.fieldName = column.key;
                this.query.orderBy = column.order;
            }
            this.getList(this.query);
        },
        //时间事件
        startTimeChange(val) {
            // this.endTime = '';
            if (val == '') {
                this.timeRange[1] = '';
                this.startOptions.disabledDate = date => {
                    return date && date.valueOf() > this.currentTime;
                };
                this.endOptions.disabledDate = date => {
                    return date && date.valueOf() > this.currentTime;
                };
            } else {
                var now = new Date(val),
                    y = now.getFullYear(),
                    m = now.getMonth() + 1,
                    h = now.getHours(),
                    min = now.getMinutes(),
                    s = now.getSeconds(),
                    d = now.getDate();
                let ss = y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d) + ' ' + now.toTimeString().substr(0, 8);
                this.endOptions.disabledDate = date => {
                    let checkedDay = new Date(y + '-' + m + '-' + d + ' 00:00:00').valueOf();
                    let checkedTime = new Date(this.timeRange[0]).valueOf();
                    let interTime = checkedTime - checkedDay;
                    let startTime = this.timeRange[0] ? new Date(this.timeRange[0]).valueOf() : '';
                    let endTime = val ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000 : '';
                    return (date && date.valueOf() < startTime - interTime) || (date && date.valueOf() > (endTime > this.currentTime ? this.currentTime : endTime));
                };
            }
        },
        endTimeChange(data, type) {
            if (type == 'date') {
                this.timeRange[1] = new Date(data).format('yyyy-MM-dd 23:59:59');
            }
        },
        back_endTimeChange(val) {
            if (val == '') {
                this.startOptions.disabledDate = date => {
                    return date && date.valueOf() > this.currentTime;
                };
            } else {
                if (this.timeRange[0] == '' || this.timeRange[0] == undefined) {
                    this.timeRange[1] = '';
                    this.$Message.warning('请先选择开始时间');
                } else {
                    var now = new Date(val),
                        y = now.getFullYear(),
                        m = now.getMonth() + 1,
                        h = now.getHours(),
                        min = now.getMinutes(),
                        s = now.getSeconds(),
                        d = now.getDate();
                    let ss = y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d) + ' ' + now.toTimeString().substr(0, 8);
                    let checkedDayTimes = new Date(y + '-' + m + '-' + d + ' 00:00:00').valueOf();
                    let nowDayTimes = moment().startOf('day').valueOf();
                    if (ss.slice(-8) == '00:00:00') {
                        if (checkedDayTimes == nowDayTimes) {
                            this.timeRange[1] = new Date(this.currentTime).format('yyyy-MM-dd HH:mm:ss');
                        } else {
                            this.timeRange[1] = new Date(val).format('yyyy-MM-dd 23:59:59');
                        }
                    }
                    this.startOptions.disabledDate = date => {
                        let endTime = this.timeRange[1] ? new Date(this.timeRange[1]).valueOf() : '';
                        let startTime = val ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000 : '';
                        return (date && date.valueOf() < startTime) || (date && date.valueOf() > (endTime > this.currentTime ? this.currentTime : endTime));
                    };
                }
            }
        },

        queryClick() {
            //点击搜索
            this.query.pageNo = this.currentNum = 1;
            this.query.startTime = new Date(this.timeRange[0]).format('yyyy-MM-dd HH:mm:ss');
            this.query.endTime = new Date(this.timeRange[1]).format('yyyy-MM-dd HH:mm:ss');
            this.getList(this.query);
        },
        getBusinessOperationType() {
            this.operateTypeList = [];
            this.$http
                .wisdomPost('/dataTable/queryCode', { key: 'businessOperationType' })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.operateTypeList = data;
                    } else {
                        this.operateTypeList = [];
                        this.$Message.warning(msg);
                    }
                })
                .catch(err => {
                    this.operateTypeList = [];
                    throw new Error(err);
                });
        },
        getList(param) {
            //OID列表查询请求
            let _self = this;
            _self.loading = true;
            _self.$http
                .wisdomPost('/audit/list', param)
                .then(res => {
                    if (res.code === 1) {
                        _self.loading = false;
                        if (res.data.records) {
                            _self.tableList = res.data.records;
                            _self.totalCount = res.data.total || 0;
                            //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                            let _that = this;
                            setTimeout(function () {
                                let objData = _that.$refs.tableList.$refs.tbody.objData;
                                for (let key in objData) {
                                    if (_that.selectedIds.has(objData[key].id)) {
                                        objData[key]._isChecked = true;
                                    }
                                }
                            }, 0);
                        } else {
                            _self.tableList = [];
                        }
                        _self.loading = false;
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .catch(err => {
                    _self.loading = false;
                })
                .finally(() => {
                    _self.loading = false;
                });
        },
        pageChange(val) {
            //表格页码切换
            this.currentNum = val;
            this.query.pageNo = this.currentNum;
            this.getList(this.query);
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList(this.query);
        },
        selectChange() {
            if (this.query.roleId === undefined) {
                this.query.roleId = '';
            }
            if (this.query.orgId === undefined) {
                this.query.orgId = '';
            }
        },
        deleteClick() {
            let selectedIdsArrary = Array.from(this.selectedIds);
            let ids = '';
            for (let i = 0; i < selectedIdsArrary.length; i++) {
                if (i === 0) {
                    ids += selectedIdsArrary[i];
                } else {
                    ids += ',' + selectedIdsArrary[i];
                }
            }
            let param = {
                ids: ids,
                tab: this.$t('operation_log_operation_log')
            };
            if (selectedIdsArrary.length > 0) {
                top.window.$iviewModal.confirm({
                    title: this.$t('common_delete_prompt'),
                    content: '<p>'+this.$t('server_sure_delete')+'</p>',
                    onOk: () => {
                        this.$http.wisdomPost('/audit/delete', param).then(res => {
                            if (res.code === 1) {
                                this.$Message.success(this.$t('common_delete_success'));
                            } else {
                                this.$Message.error(res.msg);
                            }
                            this.currentNum = this.query.pageNo = 1;
                            this.getList(this.query);
                            this.selectedIds = new Set();
                        });
                    }
                });
            } else {
                this.$Message.warning(this.$t('group_select_delete'));
            }
        },
        auditLogdelete(data) {
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: '<p>'+this.$t('server_sure_delete')+'</p>',
                onOk: () => {
                    this.$http.wisdomPost('/audit/delete', { ids: data.id, tab: this.$t('operation_log_operation_log') }).then(res => {
                        if (res.code === 1) {
                            this.$Message.success(this.$t('common_delete_success'));
                        } else {
                            this.$Message.error(res.msg);
                        }
                        this.currentNum = this.query.pageNo = 1;
                        this.getList(this.query);
                    });
                }
            });
        },
        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
            if (slection.length === 0) {
                let data = this.$refs.tableList.data;
                data.forEach(item => {
                    if (this.selectedIds.has(item.id)) {
                        this.selectedIds.delete(item.id);
                        this.selectedDatas.delete(JSON.stringify(item));
                    }
                });
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                    this.selectedDatas.add(JSON.stringify(item));
                });
            }
        },
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
            this.selectedDatas.add(JSON.stringify(row));
        },
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
            this.selectedDatas.delete(JSON.stringify(row));
        }
    }
};
</script>

<style>
.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  background: #f1f6fe !important;
}
.action-btn {
  font-size: 14px;
  font-weight: bold;
}
.action-red {
  color: red !important;
}
</style>
