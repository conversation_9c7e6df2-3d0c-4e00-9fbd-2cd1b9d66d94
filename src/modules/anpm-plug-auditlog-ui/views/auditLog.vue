<template>
  <!-- currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin' -->
  <div
    :class="[currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin']"
  >
    <Tabs
      :type="currentSkin == 1 ? 'card' : 'line'"
      id="aa"
      v-model="checkedTab"
      :class="{ 'tabs-card-content-black': currentSkin == 1 }"
      style="overflow: visible"
    >
      <TabPane :label="$t('comm_system_log')" name="systemLog"
        ><system-log
          v-if="checkedTab === 'systemLog'"
          :tabData="checkedTab"
        ></system-log
      ></TabPane>
      <TabPane :label="$t('operation_log_operation_log')" name="operationLog"
        ><operation-log
          v-if="checkedTab === 'operationLog'"
          :tabData="checkedTab"
        ></operation-log>
      </TabPane>
      <TabPane :label="$t('comm_event_alarm')" name="eventAlarm"
        ><event-alarm
          v-if="checkedTab === 'eventAlarm'"
          :tabData="checkedTab"
        ></event-alarm
      ></TabPane>
      <TabPane :label="$t('comm_log_statistics')" name="logStatistics"
        ><log-statistics
          v-if="checkedTab === 'logStatistics'"
          :tabData="checkedTab"
        ></log-statistics>
      </TabPane>
      <TabPane :label="$t('comm_audit_strategy')" name="auditStrategy"
        ><audit-strategy
          v-if="checkedTab === 'auditStrategy'"
          :tabData="checkedTab"
        ></audit-strategy>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import systemLog from './systemLog.vue';
import operationLog from './operationLog.vue';
import eventAlarm from './eventAlarm.vue';
import logStatistics from './logStatistics.vue';
import auditStrategy from './auditStrategy.vue';
import locationreload from '@/common/locationReload';
export default {
    name: 'index',
    components: {
        systemLog,
        operationLog,
        eventAlarm,
        logStatistics,
        auditStrategy
    },
    data() {
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            checkedTab: 'systemLog'
        };
    },
    created() {
      this.$nextTick(() => {
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
      })
        // const src = window.frames.frameElement.getAttribute('src');
        // if (src && src.indexOf('?checkedTab=')>-1){
        //   this.checkedTab = src.split('?checkedTab=')[1].replace(/^\s*|\s*$/g,"");
        // }
    },
    mounted() {
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
     },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
     },
    methods: {
         handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    }
};
</script>

<style scoped lang='less'>
/deep/ #aa .ivu-tabs-bar {
  border-bottom: 0px !important;
}
/deep/ #aa .ivu-tabs-bar {
  border-color: none;
}
.ivu-tabs {
  padding-top: 20px;
}
.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  margin-right: 8px;
}

/deep/ .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
  border-color: transparent !important;
}
/deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  border: 0;
}
</style>
<style>
.section-text .ivu-picker-confirm .ivu-btn {
  padding: 0px 12px !important;
}
.tabs-card .ivu-tabs-bar {
  margin-left: 20px;
  margin-right: 20px;
}
.tabs-card > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  /*border-radius: 0;*/
  background: #fff;
}
</style>