<template>
  <!--  :class="currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin'" -->

  <section
    :class="['sectionBox', currentSkin == 1 ? 'dark-skin' : 'light-skin']"
  >
    <div class="section-top">
      <div class="fn_box">
        <div class="fn_item">
          <label class="fn_item_label"
            >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
          >
          <div class="fn_item_box">
            <DatePicker
              type="date"
              format="yyyy-MM-dd 00:00:00"
              v-model="startTime"
              placement="bottom-end"
              :placeholder="$t('snmp_pl_man')"
              style="width: 175px"
              @on-change="startTimeChange"
              :options="startOptions"
              confirm
            ></DatePicker>
            <span
              class="fn_item_label"
              style="min-width: auto; padding-right: 12px; padding-left: 12px"
              >{{ $t("comm_to") }}</span
            >
            <DatePicker
              type="date"
              format="yyyy-MM-dd 23:59:59"
              v-model="endTime"
              placement="bottom-end"
              :placeholder="$t('snmp_pl_man')"
              style="width: 175px"
              :options="endOptions"
              confirm
              @on-change="endTimeChange"
            ></DatePicker>
          </div>
        </div>
        <div class="fn_item">
          <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
          <div class="fn_item_box">
            <Input
              v-model.trim="query.keyword"
              :placeholder="$t('log_search_key')"
              style="width: 280px"
            />
          </div>
        </div>
      </div>

      <div class="fn_tool">
        <Button
          type="primary"
          v-if="permissionObj.list"
          icon="ios-search"
          @click="queryClick"
          >查询
        </Button>
        <Button
          type="error"
          class="skinError"
          icon="ios-trash"
          v-if="permissionObj.list"
          @click="clearClick"
          >删除
        </Button>
      </div>
    </div>

    <div class="section-body contentBox_bg">
      <div class="section-body-content">
        <div>
          <Loading :loading="loading"></Loading>
          <Table
            ref="tableList"
            border
            :columns="columns"
            :data="tableList"
            :no-data-text="
              loading
                ? ''
                : tableList.length > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          ></Table>
          <div
            class="tab-page"
            style="border-top: 0"
            v-if="tableList.length > 0"
          >
            <Page
              v-page
              :current.sync="currentNum"
              :page-size="query.pageSize"
              :total="totalCount"
              :page-size-opts="pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import "@/config/page.js";
import global from "@/common/global.js";
import moment from 'moment';

export default {
  name: "userOperateLog",
  data() {
    return {
            currentSkin: sessionStorage.getItem('dark') || 1,
      //权限对象
      permissionObj: {},
      startTime: new Date().format("yyyy-MM-dd 00:00:00"),
      endTime: new Date().format("yyyy-MM-dd 23:59:59"),
      currentTime: Date.now(),
      startOptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const start = new Date();
              return start;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let start = new Date(date.year, date.month, 1);
              return start;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();

              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              return start;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
        ],
        disabledDate: (date) => {
          return date && date.valueOf() > Date.now();
        },
      },
      endOptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              return end;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              return end;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
        ],
        disabledDate: (date) => {
          let data = this.startTime == "" ? "" : this.startTime;
          return date < data || date.valueOf() > Date.now();
        },
      },
      descriptionList: [], //操作结果下拉列表
      //搜索字段

      query: {
        logType: "", //操作结果
        startTime: new Date().format("yyyy-MM-dd 00:00:00"), //开始时间
        endTime: new Date().format("yyyy-MM-dd 23:59:59"), //结束时间
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
        keyword: "",
      },
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      //表格数据
      tableList: [],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 60,
          className: "bgColor",
          align: "center",
          fixed:'left'
        },
        {
          //序号
          title: this.$t('common_No'),
          // type:'index',
          width: 60,
          className: "bgColor",
          align: "center",
          render: (h, params) => {
            return h(
                "span",
                params.index + 1 + (this.query.pageNo - 1) * this.query.pageSize
            );
          },
        },
        {
          title: "操作账号",
          key: "userName",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.userName;
            return h(
                "span",
                str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: "操作IP",
          key: "loginIp",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.loginIp;
            return h(
                "span",
                str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: "对象",
          key: "funName",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.funName;
            return h(
                "span",
                str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        //  {
        //    title: '操作类型',
        //    key: 'logType',
        //   align: 'center',
        //   className: 'bgColor',
        //   render: (h, params) => {
        //    let str = params.row.logType;
        //    return h('span', (str === undefined || str === null || str === '') ? '--' : str === 1 ? '操作日志' : str === 2 ? '登录成功' : str === 3 ? '登录失败' : '--')
        //   }
        //  },
        // {
        //     title:'操作概要',
        //     key:'roleName',
        //     align: 'center',
        //     className:'bgColor',
        //     render:(h,params)=>{
        //         let str=params.row.roleName;
        //         return h('span',(str===undefined || str===null || str==='')?'--':str)
        //     }
        // },
        {
          title: "接口地址",
          key: "url",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.url;
            return h(
                "span",
                str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: "操作结果",
          key: "description",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.description;
            return h(
                "span",
                str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: "日期",
          key: "createDate",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.createDate;
            return h(
                "span",
                str === undefined || str === null || str === ""
                    ? "--"
                    : new Date(str).format("yyyy-MM-dd HH:mm:ss")
            );
          },
        },
        {
          title: "操作用时(ms)",
          key: "timeSpent",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.timeSpent;
            return h(
                "span",
                str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
      ],
    };
  },
  created() {
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.$nextTick(() => {
      this.getList(this.query);
    });
  },
  computed: {},
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
             // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    this.query.pageNo = this.currentNum;
  },
  methods: {
    //时间事件
    startTimeChange(val) {
      if (val == "") {
        this.endTime = "";
        this.startTime= new Date().format("yyyy-MM-dd 00:00:00"),
        this.endTime=new Date().format("yyyy-MM-dd 23:59:59"),
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
        this.endOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        var now = new Date(val),
            y = now.getFullYear(),
            m = now.getMonth() + 1,
            h = now.getHours(),
            min = now.getMinutes(),
            s = now.getSeconds(),
            d = now.getDate();
        let ss =
            y +
            "-" +
            (m < 10 ? "0" + m : m) +
            "-" +
            (d < 10 ? "0" + d : d) +
            " " +
            now.toTimeString().substr(0, 8);
        this.endOptions.disabledDate = (date) => {
          let checkedDay = new Date(
              y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let checkedTime = new Date(this.startTime).valueOf();
          let interTime = checkedTime - checkedDay;
          let startTime = this.startTime
              ? new Date(this.startTime).valueOf()
              : "";
          let endTime = val
              ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000
              : "";
          return (
              (date && date.valueOf() < startTime - interTime) ||
              (date &&
                  date.valueOf() >
                  (endTime > this.currentTime ? this.currentTime : endTime))
          );
        };
      }
    },
    endTimeChange(val) {
      if (val == "") {
        this.endTime=new Date().format("yyyy-MM-dd 23:59:59"),
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        if (this.startTime == "" || this.startTime == undefined) {
          this.endTime = "";
          this.$Message.warning({content:this.$t('specquality_strat'),background:true});
        } else {
          var now = new Date(val),
              y = now.getFullYear(),
              m = now.getMonth() + 1,
              h = now.getHours(),
              min = now.getMinutes(),
              s = now.getSeconds(),
              d = now.getDate();
          let ss =
              y +
              "-" +
              (m < 10 ? "0" + m : m) +
              "-" +
              (d < 10 ? "0" + d : d) +
              " " +
              now.toTimeString().substr(0, 8);
          let checkedDayTimes = new Date(
              y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let nowDayTimes = moment().startOf("day").valueOf();
          if (ss.slice(-8) == "00:00:00") {
            if (checkedDayTimes == nowDayTimes) {
              this.endTime = new Date(this.currentTime).format(
                  "yyyy-MM-dd HH:mm:ss"
              );
            } else {
              this.endTime = new Date(val).format("yyyy-MM-dd 23:59:59");
            }
          }
          this.startOptions.disabledDate = (date) => {
            let endTime = this.endTime ? new Date(this.endTime).valueOf() : "";
            let startTime = val
                ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000
                : "";
            return (
                (date && date.valueOf() < startTime) ||
                (date &&
                    date.valueOf() >
                    (endTime > this.currentTime ? this.currentTime : endTime))
            );
          };
        }
      }
    },

    queryClick() {      //点击搜索
      this.query.pageNo = this.currentNum = 1;
      this.query.startTime = new Date(this.startTime).format(
          "yyyy-MM-dd 00:00:00"
      );
      this.query.endTime = new Date(this.endTime).format("yyyy-MM-dd 23:59:59");
      this.getList(this.query);
    },

    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.branchTable.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },

    clearClick() {
      let selectedIdsArrary = Array.from(this.selectedIds);
      if (selectedIdsArrary.length < 1) {
        this.$Message.warning({content:"请选择需要删除的数据",background:true});
        return;
      }
      let ids = "";
      for (let i = 0; i < selectedIdsArrary.length; i++) {
        if (i === 0) {
          ids += selectedIdsArrary[i];
        } else {
          ids += "," + selectedIdsArrary[i];
        }
      }
      // alert(ids)
      // let ids = '';
      // let objData = this.$refs.tableList.$refs.tbody.objData;
      // if (this.tableList.length > 0) {
      //   for (let i = 0; i < this.tableList.length; i++) {
      //     if (i === 0) {
      //       ids += this.tableList[i].id
      //     }
      //     else {
      //       ids += ',' + this.tableList[i].id
      //     }
      //   }
      let param = {
        ids: ids,
      };

      top.window.$iviewModal.confirm({
        title: this.$t('common_delete_prompt'),
        content: "<p>确定要删除这些记录吗？</p>",
        onOk: () => {
          this.$http.wisdomPost("/log/delete", param).then((res) => {
            if (res.code === 1) {
              this.$Message.success({content:"成功删除",background:true});
              this.selectedIds = new Set();
              this.query.pageNo = this.currentNum = 1;
              this.getList(this.query);
            } else {
              this.$Message.success({content:"删除失败！",background:true});
            }
          });
        },
      });

      // }
    },
    getList(param) {
      //OID列表查询请求
      let _self = this;
      _self.loading = true;
      _self.$http
          .wisdomPost("/log/queryLog", param)
          .then((res) => {
            if (res.code === 1) {
              _self.loading = false;
              if (res.data.records) {
                _self.tableList = res.data.records;
                _self.totalCount = res.data.total || 0;
                //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                let _that = this;
                setTimeout(function () {
                  let objData = _that.$refs.tableList.$refs.tbody.objData;
                  for (let key in objData) {
                    if (_that.selectedIds.has(objData[key].id)) {
                      objData[key]._isChecked = true;
                    }
                  }
                }, 0);
              } else {
                _self.tableList = [];
              }
              _self.loading = false;
            } else {
              this.$Message.error({content:res.msg,background:true});
            }
          })
          .catch((err) => {
            _self.loading = false;
          })
          .finally(() => {
            _self.loading = false;
          });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      this.getList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      this.getList(this.query);
    },
    selectChange() {
      if (this.query.roleId === undefined) {
        this.query.roleId = "";
      }
      if (this.query.orgId === undefined) {
        this.query.orgId = "";
      }
    },
    // startTimeChange: function (val) {
    //   if (val === "" || val === undefined || val === null) {
    //     this.query.startTime = "";
    //     this.query.endTime = "";
    //     this.endOptions.disabledDate = (date) => {
    //       return date && date.valueOf() > Date.now();
    //     };
    //   } else {
    //     this.endOptions.disabledDate = (date) => {
    //       return (
    //         (date &&
    //           date.valueOf() <
    //             new Date(new Date(val).getTime() - 24 * 3600 * 1000)) ||
    //         (date && date.valueOf() > Date.now())
    //       );
    //     };
    //     this.query.startTime = val + " 00:00:00";
    //   }
    // },
    // endTimeChange: function (val) {
    //   if (val === "" || val === undefined || val === null) {
    //     this.query.startTime = "";
    //     this.query.endTime = "";
    //     this.startOptions.disabledDate = (date) => {
    //       return date && date.valueOf() > Date.now();
    //     };
    //   } else {
    //     this.startOptions.disabledDate = (date) => {
    //       return date && date.valueOf() > new Date(val);
    //     };
    //     this.query.endTime = val + " 23:59:59";
    //   }
    // },
  },
};
</script>

<style>
.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  /*background: #f1f6fe !important;*/
}
</style>