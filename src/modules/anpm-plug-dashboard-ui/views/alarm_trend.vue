<template>
  <section class="homeBox" style="min-width:1200px">
    
    <div
      class="itemEcharts"
      :style="{ position: 'relative', minHeight: '400px' }"
    >

      <div class="lineBox">
        <draw-line
          :node="'line' + index"
          :lineData="item"
          :height="
            line_List.length === 3 ? 260 : line_List.length === 2 ? 330 : 400
          "
          v-for="(item, index) in line_List"
          :key="index"
        ></draw-line>
      </div>
    </div>
  </section>
</template>

<script>
import drawLine from "@/common/echarts/draw-line.vue";
import '@/timechange'
import global from "@/common/global.js";
export default {
  name: "wisdomIndex",
  components: {
    drawLine
  },
  data() {
    return {
      nowDate:new Date(),
      //时间参数
      startTime:null,
      endTime:null,
      pieLoading: false,
      height: 260,
      param: {
        startTime:new Date().format2('yyyy-MM-dd'),
        endTime:new Date().format2('yyyy-MM-dd'),
      },
      line_List: [],
      typeCode:"snmp_alarm_trend" //此参数由调用的时候传进来，传进来的是构件的code例（route_alarm_trend，special_alarm_trend，snmp_alarm_trend）
    };
  },
  created() {
    this.getLine();
  },
  mounted() {},
  methods: {
    // 获取折线图数据
    getLine(param) {
      let _self = this;
      _self.$http.wisdomPost("/home/<USER>", param).then(res => {
        let datas = res.data;
        if (res.code === 1) {
          
          if(this.typeCode=="route_alarm_trend")//最近30天路径告警走势
          {
              this.line_List.push(datas[0]);
          }else if(this.typeCode=="special_alarm_trend"){//最近30天专线告警走势
              this.line_List.push(datas[1]);
          }else if(this.typeCode=="snmp_alarm_trend"){//最近30天中继告警走势
              this.line_List.push(datas[2]);
          }       
          ;    
        }
      });
    },
  
  }
};
</script>
<style scoped>
  .pieSearch{
    width: 42%;
    text-align: center;
  }
  .pieSearch .pieTime{
    display: inline-block;
    width: 100%;
  }
  /deep/.pieSearch .pieTime .ivu-input:hover{
    border-color: #dddddd;
  }
  .pieSearch .pieTime >label{
    font-weight: bold;
  }
.tableBox {
  margin: 20px 20px 40px 20px;
}

.dialTest-tab-title {
  padding-top: 20px;
  padding-bottom: 20px;
}

.homeBox {
  padding-top: 10px;
  height: 100%;
}

.itemEcharts {
  display: flex;
  align-items: center;
}

.pieBox {
  /*flex: 1;*/
  width: 42%;
}

.cake,
.brokenLine {
  height: 280px;
  border-bottom: 1px solid #e7e7e7;
}

.lineBox {
  /*flex: 1.5;*/
  width: 58%;
}

html,
body {
  position: relative;
  height: 100%;
}

body {
  background: #eee;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #000;
  margin: 0;
  padding: 0;
}
</style>
