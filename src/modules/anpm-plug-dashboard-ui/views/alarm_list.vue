<template>
  <section class="sectionBox">
    <div class="section-body">
      <div class="section-body-content">
        <div>
          <Loading :loading="loading"></Loading>
          <Table
            ref="tableList"
            border
            :columns="columns"
            :data="tableList"
            :no-data-text="
              loading ? '' : tableList.length > 0 ? '' : $t('alarm_no_data')
            "
            size="small"
          ></Table>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import "@/config/page.js";
  import global from "@/common/global.js";
  import { mapGetters, mapActions, mapState } from "vuex";
  export default {
    name: "snmpOid",
    data() {
      return {
        //搜索字段
        query: {
          limitNum: 10, //状态
          pageNo: 1, //页数
          pageSize: 10 //每页展示多少数据
        },
        //loading状态
        loading: false,
        //表格数据
        tableList: [],
        columns: [
          {
            title: this.$t('dash_order_generation_time'),
            key: "faultStarttime",
            width: 210,
            align: "center",
            className: "bgColor",
            render: (h, params) => {
              let str = params.row.faultStarttime;
              return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                  ? "--"
                  : str
              );
            }
          },
          {
            title: this.$t('dash_faulty_link'),
            key: 'errorIps',
            width: 210,
            align: 'center',
            ellipsis: true,
            render: (h, params) => {
              // if (params.row.dealStatus == 7 || params.row.faultType == 7) {
              //   const getRecentlyEffective = (ip, index, list) => {
              //     if (ip.includes('*') && index > 0 && list.length > 0) {
              //       for (--index; index >= 0; index--) {
              //         if (! list[ index ].includes('*')) {
              //           return `${list[ index ]}(最远可达)`;
              //         }
              //       }
              //     }
              //     return ip;
              //   };
              //   const error_ips = String(params.row.errorIps).split(',').map(getRecentlyEffective);
              //   const error_ips_detail = String(params.row.errorIpsDetail).split(',').map(getRecentlyEffective);
              //   const minSize = Math.min(error_ips.length, error_ips_detail.length);
              //   let ip = null;
              //   const isIc = error_ips.length === minSize;
              //   if (minSize > 0 && (isIc ? error_ips : error_ips_detail).some((text, index) => {
              //     if (text !== (isIc ? error_ips_detail : error_ips)[ index ]) {
              //       ip = /*error_ips_detail[ index ] || */text;
              //       return true;
              //     }
              //     return false;
              //   })) {
              //     return h('div', [ ip ]);
              //   } else if (isIc
              //     ? error_ips_detail.join(',').startsWith(error_ips.join(','))
              //     : error_ips.join(',').startsWith(error_ips_detail.join(','))
              //   ) {
              //     return h('div', [ (isIc ? error_ips_detail : error_ips)[ minSize ] ]);
              //   }
              // }
              let str = params.row.errorIps;
              if (str.indexOf('(') >= 0) {
                str = str.slice(0, str.indexOf('('))
              }
              let name = params.row.errIpName;
              let extraFlag = params.row.extraFlag;
              let left = h('span', '(');
              let right = h('span', ')');
              let extraFlagS = h('span',
                {
                  style: {
                    color: 'red'
                  },
                }, this.$t('dash_far'));
              if (name && name != '') {
                name = ' 【' + name + '】'
              } else {
                name = '';
              }
              let Arr1 = h('span', (str === undefined || str === null || str === '') ? '--' : str + name);
              if (extraFlag == 1) {
                return h('div', {
                  class: {
                    'text-ellipsis': true
                  },
                  style: {
                    'word-break': 'keep-all',
                    'white-space': 'nowrap',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis'
                  },
                  domProps: {
                    title: str + name + ',' + this.$t('dash_far')
                  }
                }, [Arr1, left, extraFlagS, right])
              } else {
                return h('div', {
                  class: {
                    'text-ellipsis': true
                  },
                  style: {
                    'word-break': 'keep-all',
                    'white-space': 'nowrap',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis'
                  },
                  domProps: {
                    title: str + name
                  }
                }, [Arr1])
              }
            }
          },
          {
            title: this.$t('alarm_symptom'),
            key: "faultDesc",
            align: "center",
            className: "bgColor",
            render: (h, params) => {
              let str = params.row.faultDesc;
              return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                  ? "--"
                  : str
              );
            }
          },
          {
            title: this.$t('dash_fault_state'),
            key: 'reportState',
            align: 'center',
            width: 100,
            render: (h, params) => {
              if (params.row.dealStatus == 8 || params.row.faultType == 8) {
                return h('span', '--');
              }
              let str = params.row.reportState, text = '--';
              if (params.row.dealStatus != 7 && params.row.faultType != 7) {
                switch (str) {
                  case 1:
                    text = this.$t('common_unrecovered');
                    break;
                  case 0:
                    text = this.$t('common_recovered');
                    break;
                  default:
                    text = '--';
                    break;
                }
              }
              return h('span', {
                class: (str === 0 ? 'action-red' : (str === 1 ? 'action-green' : '')),
              }, text)
            }
          }
        ]
      };
    },
    created() {
        this.getList(this.query);
    },
    methods: {
      getList(param) {
        console.log("eeeeeeeeeeeee")
        //OID列表查询请求
        let _self = this;
        _self.$http.wisdomPost("/dashboard/getFaultList", param).then(res => {
          _self.loading = true;
          if (res.code === 1) {
              _self.tableList = res.data;
              _self.loading = false;
          }
        });
      },
    }
  };
</script>

<style>
  .searchTop .ivu-select-input {
    height: 32px;
    line-height: 32px !important;
  }
  .fault-tab .dialTest-tab-content .ivu-table th.bgColor {
    background: #f1f6fe !important;
  }
  .task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
    height: auto !important;
    min-height: 32px;
  }
  .multipleSelect .ivu-select-input {
    padding: 0 !important;
  }
</style>
