<template>
  <div class="content" id="cc">
    <div class="buttonBox">
      <Button
        class="userDefined add skinPrimary"
        icon="md-add"
        @click="addComponent"
        >{{ $t("dashboard_add_component") }}
      </Button>
      <Button
        class="userDefined add skinPrimary"
        icon="md-folder"
        @click="addComponentSave"
        >{{ $t("dashboard_save_set") }}
      </Button>
      <Button
        class="userDefined skinWarning"
        type="warning"
        icon="ios-undo"
        @click="goBack"
        >{{ $t("but_cancel") }}
      </Button>
    </div>
    <!--<router-view name="dashboardStatistic"></router-view>-->

    <div
      style="position: relative; width: 100%; margin-top: 20px"
      id="viewTableContent"
    >
      <table class="viewTable" border="0" :style="'width:' + tableW + 'px;'">
        <tr>
          <th
            v-for="rowItem in colArray"
            :key="rowItem.rowIndex"
            :style="'width:' + 100 / colArray.length + '%;'"
          ></th>
        </tr>
        <tr v-for="rowItem in rowArray" :key="rowItem.rowIndex">
          <td
            v-for="columnItem in rowItem.columns"
            :key="columnItem.columnKey"
            :colspan="columnItem.colspan"
            :rowspan="columnItem.rowspan"
            :style="
              'width:' +
              (tableW / column) * columnItem.colspan +
              'px;height:' +
              (tableH / row) * columnItem.rowspan +
              'px'
            "
          >
            <div
              v-for="iframeItem in viewList"
              :key="iframeItem.key"
              v-if="
                rowItem.rowIndex == iframeItem.startRow &&
                columnItem.columIndex == iframeItem.startCol
              "
              style="width: 100%; height: 100%; text-align: left"
            >
              <components-iframe
                v-if="
                  rowItem.rowIndex == iframeItem.startRow &&
                  columnItem.columIndex == iframeItem.startCol
                "
                :iframeData="
                  Object.assign(iframeItem, {
                    queryTime: [
                      new Date(new Date().getTime() - 4 * 3600 * 1000).format2(
                        'yyyy-MM-dd HH:mm:ss'
                      ),
                      new Date().format2('yyyy-MM-dd HH:mm:ss'),
                    ],
                    queryTimeType: 4,
                  })
                "
                :height="(tableH / row) * columnItem.rowspan - 50"
                @edit="editClick"
                @delete="deleteClick"
              ></components-iframe>
            </div>
          </td>
        </tr>
      </table>
    </div>

    <!--新建构件模态框-->
    <Modal
      v-model="modalShow"
      :title="modalTitle"
      :mask="true"
      sticky
      draggable
      width="900"
      class="addComponent-modal bg-modal"
      @on-visible-change="addVisibleChange"
    >
      <Form
        ref="addComponentForm"
        class="addComponentForm"
        v-if="modalShow"
        :model="addComponentForm"
        :rules="rulesValidate"
        :label-width="170"
        @submit.native.prevent
      >
        <!-- addFields过滤 下面内容 -->
        <FormItem
          v-for="item in addFields.filter((item) => {
            return (
              item.prop !== 'alarmListNum' &&
              item.prop !== 'alarmListNum1' &&
              item.prop !== 'topoId' &&
              item.prop !== 'graphicType' &&
              item.prop !== 'provinceCode' &&
              item.prop !== 'provinceMunicipalityCode' &&
              item.prop !== 'pathtopoType' &&
              item.prop !== 'objType' &&
              item.prop !== 'groupId' &&
              item.prop !== 'alarmStatisticsType' &&
              item.prop !== 'url' &&
              item.prop !== 'taskPresentationType'
            );
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <!--alarmListNum-->
          <Input
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'input'"
            :placeholder="
              $t('comm_please_enter') +
              item.name +
              (item.prop == 'name' ? '' : ',' + $t('comm_example'))
            "
            maxlength="20"
          />
          <!-- 选择类型下拉框 -->
          <Select
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'typeCode'"
            :placeholder="$t('comm_select') + item.name"
            @on-change="addtypeChange($event, 'add')"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="componentTypeItem in componentTypeAarray"
              :key="componentTypeItem.key"
              :value="
                componentTypeItem.typeCode +
                '?pageUrl=' +
                componentTypeItem.pageUrl
              "
            >
              {{ componentTypeItem.name }}
            </Option>
          </Select>

          <!-- noShowName , noShowFrame  -->
          <RadioGroup
            v-model="addComponentForm[item.prop]"
            v-if="
              item.type === 'checkbox' &&
              (item.prop === 'noShowName' || item.prop === 'noShowFrame')
            "
          >
            <Radio
              v-for="item in showBorderList"
              :key="item.value"
              :value="item.value"
              :label="item.value"
            >
              {{ item.name }}</Radio
            >
          </RadioGroup>

          <!--intervalTime-->
          <Select
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'intervalTime'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="componentRefreshItem in componentRefreshAarray"
              :key="componentRefreshItem.key"
              :value="componentRefreshItem.refreshTime"
              >{{ componentRefreshItem.intervalTime }}
            </Option>
          </Select>
          <textarea
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'textarea'"
            :placeholder="$t('comm_enter') + item.name"
          ></textarea>
        </FormItem>
        <!-- 实时监控目标分布特有 -->
        <div
          v-if="
            addComponentForm.typeCode ===
            'monitor_target_spread_map?pageUrl=monitorTargetSpreadMap'
          "
        >
          <!-- 背景图 -->
          <FormItem
            :label="$t('icon_bg_label_select') + '：'"
            prop="backgroundImageId"
          >
            <Select
              style="width: calc(100% - 20px)"
              v-model="addComponentForm.backgroundImageId"
            >
              <Option
                v-for="item in bgList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              >
                <div class="item">
                  <img
                    class="bg-img"
                    :src="'data:image/png;base64,' + item.image"
                    width="300"
                    alt=""
                  />
                  <span>{{ item.name }}</span>
                </div>
              </Option>
            </Select>
          </FormItem>
          <!-- 图标缩放比例 -->
          <FormItem
            :label="$t('icon_zoom_ratio') + '：'"
            prop="iconScalingRatio"
          >
            <Input
              style="width: 120px"
              v-model="addComponentForm.iconScalingRatio"
            ></Input>
            <span>%</span>
          </FormItem>
          <!-- 选择展示的目标 -->
          <div class="diaplay-objectives">
            <FormItem
              class="obj-required"
              :label="$t('display_objectives') + '：'"
              prop="taskIds"
            >
              <span v-if="objRequiredEdit" class="required-text">{{
                objRequiredText
              }}</span>
            </FormItem>
            <div class="btn-box">
              <Tooltip :content="$t('group_add')">
                <Button class="add" @click="addObj('btnAdd')">
                  <Icon type="md-add" />
                </Button>
              </Tooltip>
              <Tooltip :content="$t('comm_remove')">
                <Button class="del" @click="delObj('btnAdd')">
                  <Icon type="md-remove" />
                </Button>
              </Tooltip>
            </div>
          </div>
          <!-- 目标表格 -->
          <div class="table-box" v-if="objSelectedData.length > 0">
            <Table
              height="250"
              :columns="spreadMapcolumns"
              :data="objSelectedData"
              @on-selection-change="handleSelect"
            ></Table>
          </div>
        </div>


        <!-- 30天任务告警选择分组-->
        <div v-show="getShowGroupComponent(addComponentForm['typeCode'])">
                  <FormItem
                    v-for="item in addFields.filter((item) => {
                      return item.prop === 'groupId';
                    })"
                    :key="item.key"
                    :label="item.name === '' ? '' : item.name + '：'"
                    :prop="item.prop"
                  >
                    <Select
                      class="selectInput"
                      v-model="addComponentForm[item.prop]"
                      filterable
                      multiple
                      :max-tag-count="1"
                      v-if="item.type === 'select' && item.prop === 'groupId'"
                      :placeholder="$t('comm_select') + item.name"
                      style="width: calc(100% - 20px)"
                    >
                      <Option
                        v-for="topoItem in groupType"
                        :key="topoItem.groupId"
                        :value="topoItem.groupId"
                      >
                        {{ topoItem.name }}
                      </Option>
                    </Select>
                  </FormItem>
        </div>

        <!-- 实时告警选择分组 -->
        <div v-show="addComponentForm['typeCode'] == checkedType" >
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'groupId';
            })"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
            :prop="item.prop"
          >
            <Select
              class="selectInput"
              v-model="addComponentForm[item.prop]"
              filterable
              multiple
              :max-tag-count="1"
              v-if="item.type === 'select' && item.prop === 'groupId'"
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="topoItem in groupType"
                :key="topoItem.groupId"
                :value="topoItem.groupId"
              >
                {{ topoItem.name }}
              </Option>
            </Select>
          </FormItem>

          <!-- 横向滚动消息栏 -->
          <FormItem :label="'横向滚动消息栏：'">
            <RadioGroup
              v-model="addComponentForm.componentRealAlarmConfigs[0].showType"
            >
              <Radio
                v-for="item in showList"
                :key="item.value"
                :value="item.value"
                :label="item.value"
              >
                {{ item.name }}</Radio
              >
            </RadioGroup>
          </FormItem>
          <div
            v-show="
              addComponentForm.componentRealAlarmConfigs[0].showType == 0
                ? true
                : false
            "
            style="
              margin: 0px 0px 10px 169px;
              width: 590px;
              border: 1px dashed rgb(1, 81, 151);
            "
          >
            <FormItem :label="'运维等级：'">
              <CheckboxGroup v-model="maintainCheckboxValues">
                <Checkbox label="一级"></Checkbox>
                <Checkbox label="二级"></Checkbox>
                <Checkbox label="三级"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警类型：'">
              <CheckboxGroup v-model="alarmCheckboxValues">
                <Checkbox label="中断"></Checkbox>
                <Checkbox label="时延劣化"></Checkbox>
                <Checkbox label="丢包劣化"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警状态：'">
              <CheckboxGroup v-model="recoverCheckboxValues">
                <Checkbox label="未恢复"></Checkbox>
                <Checkbox label="已恢复"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <!-- 告警条数 -->
            <FormItem
              v-for="item in addFields.filter((item) => {
                return item.prop === 'alarmListNum';
              })"
              :key="item.key"
              :label="item.name === '' ? '' : item.name + '：'"
              :prop="item.prop"
            >
              <Input
                v-model="addComponentForm[item.prop]"
                v-if="item.type === 'input'"
                :placeholder="$t('comm_enter') + item.name"
              />
            </FormItem>
          </div>
          <FormItem :label="'告警列表：'">
            <RadioGroup
              v-model="addComponentForm.componentRealAlarmConfigs[1].showType"
            >
              <Radio
                v-for="item in showList"
                :key="item.value"
                :value="item.value"
                :label="item.value"
              >
                {{ item.name }}</Radio
              >
            </RadioGroup>
          </FormItem>
          <div
            v-show="
              addComponentForm.componentRealAlarmConfigs[1].showType == 0
                ? true
                : false
            "
            style="
              margin: 0px 0px 10px 169px;
              width: 590px;
              border: 1px dashed rgb(1, 81, 151);
            "
          >
            <FormItem :label="'运维等级：'">
              <CheckboxGroup v-model="maintainCheckboxValues1">
                <Checkbox label="一级"></Checkbox>
                <Checkbox label="二级"></Checkbox>
                <Checkbox label="三级"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警类型：'">
              <CheckboxGroup v-model="alarmCheckboxValues1">
                <Checkbox label="中断"></Checkbox>
                <Checkbox label="时延劣化"></Checkbox>
                <Checkbox label="丢包劣化"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警状态：'">
              <CheckboxGroup v-model="recoverCheckboxValues1">
                <Checkbox label="未恢复"></Checkbox>
                <Checkbox label="已恢复"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <!-- 告警条数 -->
            <FormItem
              v-for="item in addFields.filter((item) => {
                return item.prop === 'alarmListNum1';
              })"
              :key="item.key"
              :label="item.name === '' ? '' : item.name + '：'"
              :prop="item.prop"
            >
              <Input
                v-model="addComponentForm[item.prop]"
                v-if="item.type === 'input'"
                :placeholder="$t('comm_enter') + item.name"
              />
            </FormItem>
          </div>
          <!-- 显示告警条数 -->
          <!-- <FormItem  v-for="item in addFields.filter((item) => {
                        return item.prop === 'alarmListNum';
                    })" :key="item.key" :label="item.name === '' ? '' : item.name + '：'" :prop="item.prop">
                        <Input v-model="addComponentForm[item.prop]" v-if="item.type === 'input'"
                            :placeholder="$t('comm_enter') + item.name" />
                    </FormItem> -->
        </div>

        <!-- 30天告警统计维度 下拉 -->
        <FormItem
          v-show="
            getShowAlarmStatisticsTypeComponent(addComponentForm['typeCode'])
          "
          v-for="item in addFields.filter((item) => {
            return item.prop === 'alarmStatisticsType';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            class="selectInput"
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'alarmStatisticsType'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="topoItem in alarmStatisticsTypeList"
              :key="topoItem.value"
              :value="topoItem.value"
            >
              {{ topoItem.name }}
            </Option>
          </Select>
        </FormItem>

        <!-- URL 链接  -->
        <FormItem
          v-show="addComponentForm['typeCode'] == checkedUrlLink"
          v-for="item in addFields.filter((item) => {
            return item.prop === 'url';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Input
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'input'"
            maxlength="200"
            :placeholder="$t('comm_enter') + item.name"
          />
        </FormItem>

        <!-- 应急大屏 呈现类型  -->
        <div v-if="addComponentForm['typeCode'] == checkedStatistic">
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'taskPresentationType';
            })"
            :key="item.key"
            :label="item.name + '：'"
            :prop="item.prop"
          >
            <Select
              class="selectInput"
              value="1"
              v-model="addComponentForm[item.prop]"
              v-if="
                item.type === 'select' && item.prop === 'taskPresentationType'
              "
              placeholder="请选择呈现类型"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="(item, index) in diagramsAarray"
                :key="index"
                :value="item.value"
              >
                {{ item.lable }}
              </Option>
            </Select>
          </FormItem>

          <div
            v-for="(
              item, index
            ) in addComponentForm.dashboardComponentTaskGroups"
            :key="index"
          >
            <div style="display: flex">
              <div
                style="
                  margin: 0px 0px 10px 169px;
                  width: 590px;
                  border: 1px dashed rgb(1, 81, 151);
                "
              >
                <FormItem
                  :label="'选择分组：'"
                  style="margin: 24px 24px 24px -30px"
                >
                  <!-- :prop="`dashboardComponentTaskGroups.${index}.groupId`" :rules="{
                                        required: true,
                                        message: '请选择分组',
                                        trigger: 'change',
                                    }"> -->
                  <Select
                    v-model="item.groupId"
                    filterable
                    :placeholder="$t('comm_select') + '分组'"
                    @on-change="handleGroupChange"
                  >
                    <Option
                      v-for="group in groupDown"
                      :key="group.groupId"
                      :value="group.groupId"
                    >
                      {{ group.name }}
                    </Option>
                  </Select>
                </FormItem>

                <FormItem
                  :label="'跳转链接：'"
                  style="margin: 24px 24px 24px -30px"
                >
                  <Select
                    v-model="item.dashboardViewId"
                    :placeholder="$t('comm_select') + '链接'"
                  >
                    <Option
                      v-for="link in linkList"
                      :key="link.id"
                      :value="link.id"
                    >
                      {{ link.name }}
                    </Option>
                  </Select>
                </FormItem>
              </div>
              <Button
                type="error"
                style="margin-left: 20px"
                @click="removeItem(index)"
                v-if="addComponentForm.dashboardComponentTaskGroups.length > 1"
                >删除</Button
              >
            </div>
            <div style="margin-left: 169px">
              <Button
                type="primary"
                icon="md-add"
                @click="addItem"
                v-if="
                  index ===
                  addComponentForm.dashboardComponentTaskGroups.length - 1
                "
                >增加新的条件</Button
              >
            </div>
          </div>
        </div>

        <!-- 样式 -->
        <FormItem
          v-show="addComponentForm['typeCode'] == checkedrouteTopo"
          v-for="item in addFields.filter((item) => {
            return item.prop === 'pathtopoType';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <!-- @on-open-change="handleSelectOpenChange" -->
          <Select
            class="selectInput"
            value="1"
            v-model="addComponentForm[item.prop]"
            @on-change="pathTopoTypeSelectChange"
            v-if="item.type === 'select' && item.prop === 'pathtopoType'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="topoItem in pathTopoTypeList"
              :key="topoItem.code"
              :value="topoItem.code"
            >
              {{ topoItem.name }}
            </Option>
          </Select>
        </FormItem>

        <!-- 选择拓扑图 -->
        <FormItem
          v-show="
            addComponentForm['typeCode'] == checkedrouteTopo ||
            addComponentForm['typeCode'] == checkedphysTopo
          "
          v-for="item in addFields.filter((item) => {
            return item.prop === 'topoId';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            class="selectInput"
            filterable
            :multiple="addComponentForm['typeCode'] == checkedrouteTopo"
            max-tag-count="1"
            v-model="addComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'topoId'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
            @on-change="handleTopoChange"
          >
            <Option
              v-for="topoItem in topoType"
              :key="topoItem.topoId"
              :value="topoItem.topoId"
            >
              {{ topoItem.name }}
            </Option>
          </Select>
        </FormItem>

        <div
          v-if="addComponentForm['typeCode'] == 'timely_map?pageUrl=dashgis'"
        >
          <!-- 选择全国还是省。。。。 -->
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'provinceCode';
            })"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
          >
            <Select
              class="selectInput"
              v-model="addComponentForm[item.prop]"
              filterable
              v-show="item.type === 'select' && item.prop === 'provinceCode'"
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="gisItem in gisList"
                :key="gisItem.code"
                :value="gisItem.code"
                >{{ gisItem.name }}
              </Option>
            </Select>
          </FormItem>
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'provinceMunicipalityCode';
            })"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
            v-show="addComponentForm['provinceCode'] === 100001"
            :prop="item.prop"
          >
            <Select
              class="selectInput"
              v-model="addComponentForm[item.prop]"
              filterable
              v-show="
                item.type === 'select' &&
                item.prop === 'provinceMunicipalityCode'
              "
              :placeholder="$t('dashboard_select_province_cannot_exceed_10')"
              style="width: calc(100% - 20px)"
              multiple
              @on-change="slectChange"
            >
              <Option
                v-for="gisItem in gisListSub"
                :key="gisItem.code"
                :value="gisItem.code"
                :disabled="totalDisabled && gisItem.isDisabled"
                >{{ gisItem.name }}
              </Option>
            </Select>
          </FormItem>
        </div>
        <!-- 聚合图形批量 -->
        <!-- 对象类型 -->
        <FormItem
          :label="this.$t('dashboard_object_type') + this.$t('comm_colon')"
          prop="objectType"
          v-if="
            addComponentForm['typeCode'] ==
            'batch_aggre_gation?pageUrl=dashaggregation'
          "
        >
          <Select
            v-model="addComponentForm.objectType"
            style="width: calc(100% - 20px)"
            @on-select="chooseType"
          >
            <Option value="1">{{ $t("spec_line") }}</Option>
            <Option value="2">{{ $t("dash_relay") }}</Option>
            <Option value="3">{{ $t("dash_commissioning_task") }}</Option>
          </Select>
        </FormItem>
        <!-- 聚合图形批量 -->
        <!-- 选择对象 -->
        <div
          class="monitorBox"
          v-show="
            addComponentForm['typeCode'] ==
            'batch_aggre_gation?pageUrl=dashaggregation'
          "
        >
          <label class="monitorLabel monitorBatchAggreLabel"
            >{{ $t("view_select_object") }}{{ $t("comm_colon") }}</label
          >
          <div class="itemContent">
            <Tag
              size="medium"
              closable
              @on-close="delTag(index)"
              v-for="(item, index) in TagDatas"
              :key="item.objId"
              >{{ item.name }}
            </Tag>
          </div>
          <Button
            class="userDefined add skinPrimary"
            icon="md-add"
            @click="addObj('addComponentForm')"
          >
            {{ $t("but_choose") }}
          </Button>
        </div>
        <!-- 聚合图形批量 -->
        <div
          class="targetBox"
          v-show="
            addComponentForm['typeCode'] ==
            'batch_aggre_gation?pageUrl=dashaggregation'
          "
          style="margin-top: 5px"
        >
          <label class="targetLabel monitorBatchAggreLabel"
            >{{ $t("spec_indic") }}{{ $t("comm_colon") }}</label
          >
          <div class="targetContent">
            <div class="targetTablebox">
              <Table
                stripe
                :columns="targetsTableColumns"
                :data="targetsTableDatas"
                :no-data-text="$t('dashboard_no_configured')"
              >
                <!-- 要改的地方 -->
                <!-- <template slot-scope="{ row, index }" slot="colorAction">
                                    <colorPicker v-model="row.color" :width="40" :height="40" :defaultColor="defaultColor"
                                        @change="headleChangeColors($event, row, index)" />
                                </template> -->
                <template slot="colorAction" slot-scope="{ row, index }">
                  <ColorPickerVue
                    v-model="row.color"
                    :width="40"
                    :height="40"
                    :defaultColor="defaultColor"
                    @change="headleChangeColors($event, row, index)"
                  >
                  </ColorPickerVue>
                </template>
              </Table>
            </div>
            <p class="targetAddBox">
              <label class="targetAddBtn" @click="addtargets()">
                <Icon type="md-add" />
                <span class="addText">{{ $t("common_add") }}</span>
              </label>
            </p>
          </div>
        </div>

        <div
          class="targetBox"
          v-show="
            addComponentForm['typeCode'] ==
            'aggre_gation?pageUrl=dashaggregation'
          "
        >
          <label class="targetLabel">{{ $t("spec_indic") }}:</label>
          <div class="targetContent">
            <div class="targetTablebox">
              <Table
                stripe
                :columns="targetTableColumns"
                :data="targetTableDatas"
                :no-data-text="$t('dashboard_no_configured')"
              >
                <template slot-scope="{ row, index }" slot="colorAction">
                  <ColorPickerVue
                    v-model="row.color"
                    :width="40"
                    :height="40"
                    :defaultColor="defaultColor"
                    @change="headleChangeColor($event, row, index)"
                  />
                </template>
              </Table>
            </div>
            <p class="targetAddBox">
              <label
                class="targetAddBtn"
                @click="addtarget(addComponentForm['typeCode'])"
              >
                <Icon type="md-add" />
                <span class="addText">{{ $t("common_add") }}</span>
              </label>
            </p>
          </div>
        </div>

        <!-- 实时监控 选择对象 -->
        <FormItem
          v-show="
            addComponentForm['typeCode'] ==
            'real_time_monitor?pageUrl=realtimemonitor'
          "
          v-for="item in addFields.filter((item) => {
            return item.prop === 'objType';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            v-if="item.type === 'select' && item.prop === 'objType'"
            class="selectInput"
            v-model="addComponentForm[item.prop]"
            filterable
            :placeholder="$t('comm_select') + item.name"
            @on-select="chooseType"
            style="width: calc(100% - 20px)"
          >
            <Option value="1">{{ $t("spec_line") }}</Option>
            <Option value="2">{{ $t("dash_relay") }}</Option>
            <Option value="3">{{ $t("dash_commissioning_task") }}</Option>
          </Select>
        </FormItem>

        <div
          class="monitorBox"
          v-show="
            addComponentForm['typeCode'] ==
            'real_time_monitor?pageUrl=realtimemonitor'
          "
        >
          <label class="monitorLabel monitorBatchAggreLabel"
            >{{ $t("view_select_object") }}{{ $t("comm_colon") }}</label
          >
          <div class="itemContent">
            <Tag
              size="medium"
              closable
              @on-close="delTag(index)"
              v-for="(item, index) in TagDatas"
              :key="item.objId"
              >{{ item.name }}
            </Tag>
          </div>
          <Button
            class="userDefined add skinPrimary"
            icon="md-add"
            @click="addMonitorBoxObj('addComponentForm')"
            >{{ $t("but_choose") }}
          </Button>
        </div>
      </Form>
      <div slot="footer">
        <Button type="error" style="margin-right: 20px" @click="cancleClick"
          >{{ $t("common_cancel") }}
        </Button>
        <Button type="primary" @click="submitClick">{{
          $t("common_verify")
        }}</Button>
      </div>
    </Modal>
    <!--编辑构件模态框-->
    <Modal
      v-model="editShow"
      @on-visible-change="editModalClose"
      :title="editTitle"
      width="900"
      :mask="true"
      sticky
      draggable
      class="addComponent-modal bg-modal"
    >
      <Form
        ref="editComponentForm"
        class="editComponentForm"
        v-if="editShow"
        :model="editComponentForm"
        :rules="editRulesValidate"
        :label-width="170"
        @submit.native.prevent
      >
        <FormItem
          v-for="item in addFields.filter((item) => {
            return (
              item.prop !== 'alarmListNum' &&
              item.prop !== 'alarmListNum1' &&
              item.prop !== 'topoId' &&
              item.prop !== 'graphicType' &&
              item.prop !== 'provinceCode' &&
              item.prop !== 'provinceMunicipalityCode' &&
              item.prop !== 'pathtopoType' &&
              item.prop !== 'objType' &&
              item.prop !== 'groupId' &&
              item.prop !== 'alarmStatisticsType' &&
              item.prop !== 'url' &&
              item.prop !== 'taskPresentationType'
            );
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Input
            v-model="editComponentForm[item.prop]"
            v-if="item.type === 'input'"
            :placeholder="
              $t('comm_please_enter') +
              item.name +
              (item.prop == 'name' ? '' : ',' + $t('comm_example'))
            "
          />
          <Select
            v-model="editComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'typeCode'"
            :placeholder="$t('comm_select') + item.name"
            @on-change="edittypeChange($event, 'edit')"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="componentTypeItem in componentTypeAarray"
              :key="componentTypeItem.key"
              :disabled="componentTypeItem.typeCode === 'batch_aggre_gation'"
              :value="
                componentTypeItem.typeCode +
                '?pageUrl=' +
                componentTypeItem.pageUrl
              "
            >
              {{ componentTypeItem.name }}
            </Option>
          </Select>

          <!-- noShowName , noShowFrame  -->
          <RadioGroup
            v-model="editComponentForm[item.prop]"
            v-if="
              item.type === 'checkbox' &&
              (item.prop === 'noShowName' || item.prop === 'noShowFrame')
            "
          >
            <Radio
              v-for="item in showBorderList"
              :key="item.value"
              :value="item.value"
              :label="item.value"
            >
              {{ item.name }}</Radio
            >
          </RadioGroup>

          <!--intervalTime-->
          <Select
            v-model="editComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'intervalTime'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="componentRefreshItem in componentRefreshAarray"
              :key="componentRefreshItem.key"
              :value="componentRefreshItem.refreshTime"
              >{{ componentRefreshItem.intervalTime }}
            </Option>
          </Select>
          <textarea
            v-model="editComponentForm[item.prop]"
            v-if="item.type === 'textarea'"
            :placeholder="$t('comm_select') + item.name"
          ></textarea>
        </FormItem>
        <!-- 实时监控目标分布特有 -->
        <div
          v-if="
            editComponentForm.typeCode ===
            'monitor_target_spread_map?pageUrl=monitorTargetSpreadMap'
          "
        >
          <!-- 背景图 -->
          <FormItem
            :label="$t('icon_bg_label_select') + '：'"
            prop="backgroundImageId"
          >
            <Select
              style="width: calc(100% - 20px)"
              v-model="editComponentForm.backgroundImageId"
            >
              <Option
                v-for="item in bgList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              >
                <div class="item">
                  <img
                    class="bg-img"
                    :src="'data:image/png;base64,' + item.image"
                    width="300"
                    alt=""
                  />
                  <span>{{ item.name }}</span>
                </div>
              </Option>
            </Select>
          </FormItem>
          <!-- 图标缩放比例 -->
          <FormItem
            :label="$t('icon_zoom_ratio') + '：'"
            prop="iconScalingRatio"
          >
            <Input
              style="width: 120px"
              v-model="editComponentForm.iconScalingRatio"
            ></Input>
            <span>%</span>
          </FormItem>
          <!-- 选择展示的目标 -->
          <div class="diaplay-objectives">
            <FormItem
              class="obj-required"
              :label="$t('display_objectives') + '：'"
            >
              <span v-if="objRequiredEdit" class="required-text">{{
                objRequiredText
              }}</span>
            </FormItem>
            <div class="btn-box">
              <Tooltip :content="$t('group_add')">
                <Button class="add" @click="addObj('btnEdit')">
                  <Icon type="md-add" />
                </Button>
              </Tooltip>
              <Tooltip :content="$t('comm_remove')">
                <Button class="del" @click="delObj('btnEdit')">
                  <Icon type="md-remove" />
                </Button>
              </Tooltip>
            </div>
          </div>
          <!-- 目标表格 -->
          <div class="table-box" v-if="objSelectedData.length > 0">
            <Table
              height="250"
              :columns="spreadMapcolumns"
              :data="objSelectedData"
              @on-selection-change="handleSelect"
            ></Table>
          </div>
        </div>
        <!-- /实时监控目标分布特有 -->
        <!-- v-show="editComponentForm['typeCode'] == checkedType" -->


        <!--统计30天告警选择分组 -->
        <div v-show="getShowGroupComponent(editComponentForm['typeCode'])">
                  <FormItem
                    v-for="item in addFields.filter((item) => {
                      return item.prop === 'groupId';
                    })"
                    :key="item.key"
                    :label="item.name === '' ? '' : item.name + '：'"
                    :prop="item.prop"
                  >
                    <Select
                      class="selectInput"
                      v-model="editComponentForm[item.prop]"
                      filterable
                      multiple
                      :max-tag-count="1"
                      v-if="item.type === 'select' && item.prop === 'groupId'"
                      :placeholder="$t('comm_select') + item.name"
                      style="width: calc(100% - 20px)"
                    >
                      <Option
                        v-for="topoItem in groupType"
                        :key="topoItem.groupId"
                        :value="topoItem.groupId"
                      >
                        {{ topoItem.name }}
                      </Option>
                    </Select>
                  </FormItem>

        </div>


        <!-- 实时告警选择分组 -->
        <div  v-show="editComponentForm['typeCode'] == checkedType" >
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'groupId';
            })"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
            :prop="item.prop"
          >
            <Select
              class="selectInput"
              v-model="editComponentForm[item.prop]"
              filterable
              multiple
              :max-tag-count="1"
              v-if="item.type === 'select' && item.prop === 'groupId'"
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="topoItem in groupType"
                :key="topoItem.groupId"
                :value="topoItem.groupId"
              >
                {{ topoItem.name }}
              </Option>
            </Select>
          </FormItem>

          <!-- 横向滚动消息栏 -->
          <FormItem :label="'横向滚动消息栏：'">
            <RadioGroup
              v-model="editComponentForm.componentRealAlarmConfigs[0].showType"
            >
              <Radio
                v-for="item in showList"
                :key="item.value"
                :value="item.value"
                :label="item.value"
              >
                {{ item.name }}</Radio
              >
            </RadioGroup>
          </FormItem>
          <div
            v-show="
              editComponentForm.componentRealAlarmConfigs[0].showType == 0
                ? true
                : false
            "
            style="
              margin: 0px 0px 10px 169px;
              width: 590px;
              border: 1px dashed rgb(1, 81, 151);
            "
          >
            <FormItem :label="'运维等级：'">
              <CheckboxGroup v-model="maintainCheckboxValues">
                <Checkbox label="一级"></Checkbox>
                <Checkbox label="二级"></Checkbox>
                <Checkbox label="三级"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警类型：'">
              <CheckboxGroup v-model="alarmCheckboxValues">
                <Checkbox label="中断"></Checkbox>
                <Checkbox label="时延劣化"></Checkbox>
                <Checkbox label="丢包劣化"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警状态：'">
              <CheckboxGroup v-model="recoverCheckboxValues">
                <Checkbox label="未恢复"></Checkbox>
                <Checkbox label="已恢复"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <!-- 告警条数 -->
            <FormItem
              v-for="item in addFields.filter((item) => {
                return item.prop === 'alarmListNum';
              })"
              :key="item.key"
              :label="item.name === '' ? '' : item.name + '：'"
              :prop="item.prop"
            >
              <Input
                v-model="editComponentForm[item.prop]"
                v-if="item.type === 'input'"
                :placeholder="$t('comm_enter') + item.name"
              />
            </FormItem>
          </div>
          <FormItem :label="'告警列表：'">
            <RadioGroup
              v-model="editComponentForm.componentRealAlarmConfigs[1].showType"
            >
              <Radio
                v-for="item in showList"
                :key="item.value"
                :value="item.value"
                :label="item.value"
              >
                {{ item.name }}</Radio
              >
            </RadioGroup>
          </FormItem>
          <div
            v-show="
              editComponentForm.componentRealAlarmConfigs[1].showType == 0
                ? true
                : false
            "
            style="
              margin: 0px 0px 10px 169px;
              width: 590px;
              border: 1px dashed rgb(1, 81, 151);
            "
          >
            <FormItem :label="'运维等级：'">
              <CheckboxGroup v-model="maintainCheckboxValues1">
                <Checkbox label="一级"></Checkbox>
                <Checkbox label="二级"></Checkbox>
                <Checkbox label="三级"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警类型：'">
              <CheckboxGroup v-model="alarmCheckboxValues1">
                <Checkbox label="中断"></Checkbox>
                <Checkbox label="时延劣化"></Checkbox>
                <Checkbox label="丢包劣化"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="'告警状态：'">
              <CheckboxGroup v-model="recoverCheckboxValues1">
                <Checkbox label="未恢复"></Checkbox>
                <Checkbox label="已恢复"></Checkbox>
              </CheckboxGroup>
            </FormItem>
            <!-- 告警条数 -->
            <FormItem
              v-for="item in addFields.filter((item) => {
                return item.prop === 'alarmListNum1';
              })"
              :key="item.key"
              :label="item.name === '' ? '' : item.name + '：'"
              :prop="item.prop"
            >
              <Input
                v-model="editComponentForm[item.prop]"
                v-if="item.type === 'input'"
                :placeholder="$t('comm_enter') + item.name"
              />
            </FormItem>
          </div>
          <!-- 显示告警条数 -->
          <!-- <FormItem  v-for="item in addFields.filter((item) => {
                        return item.prop === 'alarmListNum';
                    })" :key="item.key" :label="item.name === '' ? '' : item.name + '：'" :prop="item.prop">
                        <Input v-model="addComponentForm[item.prop]" v-if="item.type === 'input'"
                            :placeholder="$t('comm_enter') + item.name" />
                    </FormItem> -->
        </div>

        <!-- 30天告警统计维度 下拉 -->
        <FormItem
          v-show="
            getShowAlarmStatisticsTypeComponent(editComponentForm['typeCode'])
          "
          v-for="item in addFields.filter((item) => {
            return item.prop === 'alarmStatisticsType';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            class="selectInput"
            v-model="editComponentForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'alarmStatisticsType'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="topoItem in alarmStatisticsTypeList"
              :key="topoItem.value"
              :value="topoItem.value"
            >
              {{ topoItem.name }}
            </Option>
          </Select>
        </FormItem>

        <!-- URL 链接  -->
        <FormItem
          v-show="editComponentForm['typeCode'] == checkedUrlLink"
          v-for="item in addFields.filter((item) => {
            return item.prop === 'url';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Input
            v-model="editComponentForm[item.prop]"
            v-if="item.type === 'input'"
            maxlength="200"
            :placeholder="$t('comm_enter') + item.name"
          />
        </FormItem>

        <!-- 应急大屏 呈现类型  -->
        <div v-if="editComponentForm['typeCode'] == checkedStatistic">
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'taskPresentationType';
            })"
            :key="item.key"
            :label="item.name + '：'"
            :prop="item.prop"
          >
            <Select
              class="selectInput"
              value="1"
              v-model="editComponentForm[item.prop]"
              v-if="
                item.type === 'select' && item.prop === 'taskPresentationType'
              "
              placeholder="请选择呈现类型"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="(item, index) in diagramsAarray"
                :key="index"
                :value="item.value"
              >
                {{ item.lable }}
              </Option>
            </Select>
          </FormItem>

          <div
            v-for="(
              item, index
            ) in editComponentForm.dashboardComponentTaskGroups"
            :key="index"
          >
            <div style="display: flex">
              <div
                style="
                  margin: 0px 0px 10px 169px;
                  width: 590px;
                  border: 1px dashed rgb(1, 81, 151);
                "
              >
                <FormItem
                  :label="'选择分组：'"
                  style="margin: 24px 24px 24px -30px"
                >
                  <!-- :prop="`dashboardComponentTaskGroups.${index}.groupId`" :rules="{
                                        required: true,
                                        message: '请选择分组',
                                        trigger: 'change',
                                    }"> -->
                  <Select
                    v-model="item.groupId"
                    filterable
                    :placeholder="$t('comm_select') + '分组'"
                    @on-change="editHandleGroupChange"
                  >
                    <Option
                      v-for="group in groupDown"
                      :key="group.groupId"
                      :value="group.groupId"
                    >
                      {{ group.name }}
                    </Option>
                  </Select>
                </FormItem>

                <FormItem
                  :label="'跳转链接：'"
                  style="margin: 24px 24px 24px -30px"
                >
                  <Select
                    v-model="item.dashboardViewId"
                    :placeholder="$t('comm_select') + '链接'"
                  >
                    <Option
                      v-for="link in linkList"
                      :key="link.id"
                      :value="link.id"
                    >
                      {{ link.name }}
                    </Option>
                  </Select>
                </FormItem>
              </div>
              <Button
                type="error"
                style="margin-left: 20px"
                @click="editremoveItem(index)"
                v-if="editComponentForm.dashboardComponentTaskGroups.length > 1"
                >删除</Button
              >
            </div>
            <div style="margin-left: 169px">
              <Button
                type="primary"
                icon="md-add"
                @click="editItem"
                v-if="
                  index ===
                  editComponentForm.dashboardComponentTaskGroups.length - 1
                "
                >增加新的条件</Button
              >
            </div>
          </div>
        </div>

        <!-- 样式 -->
        <FormItem
          v-show="editComponentForm['typeCode'] == checkedrouteTopo"
          v-for="item in addFields.filter((item) => {
            return item.prop === 'pathtopoType';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            class="selectInput"
            v-model="editComponentForm[item.prop]"
            @on-open-change="handleSelectOpenChange"
            @on-change="pathTopoTypeSelectChange"
            v-if="item.type === 'select' && item.prop === 'pathtopoType'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="topoItem in pathTopoTypeList"
              :key="topoItem.code"
              :value="topoItem.code"
            >
              {{ topoItem.name }}
            </Option>
          </Select>
        </FormItem>
        <!-- 选择拓扑图 -->
        <FormItem
          v-show="
            editComponentForm['typeCode'] == checkedrouteTopo ||
            editComponentForm['typeCode'] == checkedphysTopo
          "
          v-for="item in addFields.filter((item) => {
            return item.prop === 'topoId';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            filterable
            class="selectInput"
            v-model="editComponentForm[item.prop]"
            :multiple="editComponentForm['typeCode'] == checkedrouteTopo"
            max-tag-count="1"
            v-if="item.type === 'select' && item.prop === 'topoId'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
            @on-change="handleTopoEditChange"
          >
            <Option
              v-for="topoItem in topoType"
              :key="topoItem.topoId"
              :value="topoItem.topoId"
            >
              {{ topoItem.name }}
            </Option>
          </Select>
        </FormItem>

        <div
          v-show="editComponentForm['typeCode'] == 'timely_map?pageUrl=dashgis'"
        >
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'provinceCode';
            })"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
          >
            <Select
              class="selectInput"
              v-model="editComponentForm[item.prop]"
              filterable
              v-show="item.type === 'select' && item.prop === 'provinceCode'"
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="gisItem in gisList"
                :key="gisItem.code"
                :value="gisItem.code"
                >{{ gisItem.name }}
              </Option>
            </Select>
          </FormItem>
          <!-- 编辑选省份 -->
          <FormItem
            v-for="item in addFields.filter((item) => {
              return item.prop === 'provinceMunicipalityCode';
            })"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
            v-show="editComponentForm['provinceCode'] === 100001"
            :prop="item.prop"
          >
            <Select
              class="selectInput"
              v-model="editComponentForm.provinceMunicipalityCode"
              filterable
              v-show="
                item.type === 'select' &&
                item.prop === 'provinceMunicipalityCode'
              "
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
              multiple
              @on-change="slectChange"
            >
              <Option
                v-for="gisItem in gisListSub"
                :key="gisItem.code"
                :disabled="totalDisabled && gisItem.isDisabled"
                :value="gisItem.code"
              >
                {{ gisItem.name }}
              </Option>
            </Select>
          </FormItem>
        </div>
        <!-- 聚合图形新建代码 -->
        <!--        <FormItem v-show="editComponentForm['typeCode'] == 'aggre_gation?pageUrl=dashaggregation'"-->
        <!--                  v-for="item in (addFields.filter((item)=>{return item.prop ==='graphicType'}))" :key="item.key"-->
        <!--                  :label="item.name === '' ? '' : item.name+'：'" :prop="item.prop">-->
        <!--          <Select v-model="editComponentForm[item.prop]" filterable-->
        <!--                  v-if="item.type==='select' && item.prop==='graphicType'" :placeholder="$t('comm_select') + item.name"-->
        <!--                  style="width: calc(100% - 20px)">-->
        <!--            <Option :value="1">折线图</Option>-->
        <!--            <Option :value="2">柱状图</Option>-->
        <!--          </Select>-->
        <!--        </FormItem>-->

        <div
          class="targetBox"
          v-show="
            editComponentForm['typeCode'] ==
              'aggre_gation?pageUrl=dashaggregation' ||
            editComponentForm['typeCode'] ==
              'batch_aggre_gation?pageUrl=dashaggregation'
          "
        >
          <label class="targetLabel">{{ $t("spec_indic") }}:</label>
          <div class="targetContent">
            <div class="targetTablebox">
              <Table
                stripe
                :columns="targetTableColumns"
                :data="targetTableDatas"
                :no-data-text="$t('dashboard_no_configured')"
              >
                <template slot-scope="{ row, index }" slot="colorAction">
                  <!-- <colorPicker v-model="row.color" :width="40" :height="40" :defaultColor="defaultColor"
                                        @change="headleChangeColor($event, row, index)" /> -->
                  <!-- 颜色选择器修改 -->
                  <ColorPickerVue
                    v-model="row.color"
                    :width="40"
                    :height="40"
                    :defaultColor="defaultColor"
                    @change="headleChangeColor($event, row, index)"
                  >
                  </ColorPickerVue>
                </template>
              </Table>
            </div>
            <p class="targetAddBox">
              <label
                class="targetAddBtn"
                @click="addtarget(editComponentForm['typeCode'])"
              >
                <Icon type="md-add" />
                <span class="addText">{{ $t("common_add") }}</span>
              </label>
            </p>
          </div>
        </div>

        <!-- 实时监控 选择对象 -->
        <FormItem
          v-show="
            editComponentForm['typeCode'] ==
            'real_time_monitor?pageUrl=realtimemonitor'
          "
          v-for="item in addFields.filter((item) => {
            return item.prop === 'objType';
          })"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
        >
          <Select
            v-if="item.type === 'select' && item.prop === 'objType'"
            class="selectInput"
            v-model="editComponentForm[item.prop]"
            filterable
            :placeholder="$t('comm_select') + item.name"
            @on-select="chooseType"
            style="width: calc(100% - 20px)"
          >
            <Option value="1">{{ $t("spec_line") }}</Option>
            <Option value="2">{{ $t("dash_relay") }}</Option>
            <Option value="3">{{ $t("dash_commissioning_task") }}</Option>
          </Select>
        </FormItem>

        <div
          class="monitorBox"
          v-show="
            editComponentForm['typeCode'] ==
            'real_time_monitor?pageUrl=realtimemonitor'
          "
        >
          <label class="monitorLabel monitorBatchAggreLabel"
            >{{ $t("view_select_object") }}{{ $t("comm_colon") }}</label
          >
          <div class="itemContent">
            <Tag
              size="medium"
              closable
              @on-close="delTag(index)"
              v-for="(item, index) in TagDatas"
              :key="item.objId"
              >{{ item.name }}
            </Tag>
          </div>
          <Button
            class="userDefined add skinPrimary"
            icon="md-add"
            @click="addMonitorBoxObj('editComponentForm')"
            >{{ $t("but_choose") }}
          </Button>
        </div>
      </Form>
      <div slot="footer">
        <Button type="error" style="margin-right: 20px" @click="editCancleClick"
          >{{ $t("common_cancel") }}
        </Button>
        <Button type="primary" @click="editSubmitClick">{{
          $t("common_verify")
        }}</Button>
      </div>
    </Modal>
    <!-- 聚合图形批量选择对象弹窗 -->
    <ObjectModal
      v-if="objectModalShow"
      :objType="objectType"
      :objectModalShow="objectModalShow"
      :TagDatas="TagDatas"
      :isDashMap="isDashMap"
      @closeModal="objectModalShow = false"
      @getSelectData="getSelectData"
    >
    </ObjectModal>

    <!-- 聚合图形批量选择指标弹框 -->
    <Modal
      v-model="indicatorsModalShow"
      :title="$t('common_index_selection')"
      width="520"
      :mask="true"
      sticky
      draggable
    >
      <Form
        ref="indicatorsModalForm"
        class="indicatorsModalForm"
        :model="indicatorsModalForm"
        :rules="indicatorModalRule"
        :label-width="50"
        @submit.native.prevent
      >
        <FormItem :label="$t('spec_indic')">
          <Checkbox :value="checkAll" @click.prevent.native="handleCheckAll">{{
            $t("common_all")
          }}</Checkbox>
          <CheckboxGroup
            v-model="indicatorsModalForm.indicatorType"
            @on-change="checkAllGroupChange"
          >
            <Checkbox
              v-for="(value, key, index) in typesList"
              :key="index"
              :label="value"
            ></Checkbox>
          </CheckboxGroup>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button
          type="error"
          style="margin-right: 20px"
          @click="indicatorsModalShow = false"
          >{{ $t("common_cancel") }}
        </Button>
        <Button type="primary" @click="addIndicatots">{{
          $t("common_verify")
        }}</Button>
      </div>
    </Modal>

    <!-- 聚合图形指标选择弹框 -->
    <Modal
      v-model="indicatorModal.show"
      :title="$t('common_index_selection')"
      width="520"
      :mask="true"
      sticky
      draggable
    >
      <Form
        ref="indicatorModalForm"
        class="indicatorModalForm"
        :model="indicatorModalForm"
        :rules="indicatorModalRule"
        :label-width="130"
        @submit.native.prevent
      >
        <FormItem
          v-for="item in indicatorFields"
          :key="item.key"
          :label="item.name === '' ? '' : item.name + '：'"
          :prop="item.prop"
          v-show="item.show"
        >
          <Select
            v-model="indicatorModalForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'objectType'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
            @on-change="indicatorChange($event)"
          >
            <Option
              v-for="(value, key, index) in indicatorList"
              :key="index"
              :value="key"
              >{{ value }}
            </Option>
          </Select>
          <Select
            v-model="indicatorModalForm[item.prop]"
            filterable=""
            v-if="item.type === 'select' && item.prop === 'objectId'"
            :placeholder="$t('comm_select') + item.name"
            @on-change="indicatorObjectIdChange($event)"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="itemObj in indicatorObject"
              :key="itemObj.id"
              :value="JSON.stringify(itemObj)"
            >
              {{ itemObj.name }}
            </Option>
          </Select>
          <Select
            v-model="indicatorModalForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'indicatorType'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option
              v-for="(value, key, index) in typeList"
              :key="index"
              :value="key"
              >{{ value }}</Option
            >
          </Select>

          <!-- 端口信息 -->
          <Select
            v-model="indicatorModalForm[item.prop]"
            v-if="item.type === 'select' && item.prop === 'portId'"
            :placeholder="$t('comm_select') + item.name"
            style="width: calc(100% - 20px)"
          >
            <Option v-for="item in portList" :key="item.id" :value="item.id">{{
              item.name
            }}</Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button
          type="error"
          style="margin-right: 20px"
          @click="cancleIndicatotClick"
          >{{ $t("common_cancel") }}
        </Button>
        <Button type="primary" @click="addIndicatot">{{
          $t("common_verify")
        }}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { countCharactersWithChineseWeight } from "@/units/validate";

const validaalarmNum = (rule, value, callback) => {
    var dashboardAlarmNumTips1 = "应为正整数,且不大于20";
    var dashboardAlarmNumTips2 = "为不大于20的正整数";
    var commEnter = "请输入";
    if (localStorage.getItem("locale") === "en") {
        dashboardAlarmNumTips1 =
            " the value should be a positive integer and not greater than 20";
        dashboardAlarmNumTips2 =
            " the value is a positive integer not greater than 20";
        commEnter = "Please enter ";
    }
    const num = /^[1-9]\d*$/;
    if (!value || value === "") {
        callback(new Error(commEnter + rule.name));
    } else if (!num.test(Number(value))) {
        callback(new Error(rule.name + dashboardAlarmNumTips1));
    } else if (num.test(Number(value))) {
        if (Number(value) > 20) {
            callback(new Error(rule.name + dashboardAlarmNumTips2));
        } else {
            callback();
        }
    }
};

const validaAlarmGroup = (rule, value, callback) => {
    if (value && value.length) {
        if (value.length > rule.Length) {
            var groupLength = "最多只能选择";
            var groupLengthEnd = "项";
            if (localStorage.getItem("locale") === "en") {
                groupLength = "At best can only choose ";
                groupLengthEnd = " items";
            }
            callback(new Error(groupLength + rule.Length + groupLengthEnd));
        } else {
            callback();
        }
    } else {
        callback();
    }
};

// 验证 url  地址
const validaUrl = (rule, value, callback) => {
    var urlError = "URL格式不正确";
    var urlLength = "长度不能超过";
    var commEnter = "请输入";
    if (localStorage.getItem("locale") === "en") {
        urlError = "The URL format is incorrect";
        urlLength = "The length shall not exceed";
        commEnter = "Please enter ";
    }

    if (value == undefined || value === "") {
        callback(new Error(commEnter + rule.name));
    } else if (!validate.validateURL(value)) {
        callback(new Error(urlError));
    } else if (value.length > 0) {
        // 计算字符长度
        var size = countCharactersWithChineseWeight(value);
        if (size > rule.Length) {
            callback(new Error(urlLength + rule.Length));
        } else {
            callback();
        }
    } else {
        callback();
    }
};

// 验证 背景图片  地址
const validaBackgroundImageId = (rule, value, callback) => {
     // debugger;
    if (value == undefined || value === "") {
        callback(new Error(rule.message));
    } else {
        callback();
    }
};

import axios from "axios";
import qs, { parse } from "qs";
import componentsIframe from "./componentIframe";
import { addDraggable } from "@/common/drag.js";
import md5 from "js-md5";
import validate from "@/common/validate";
import ObjectModal from "../components/ObjectModal.vue";
import MonitorModal from "../components/MonitorModal.vue";
import { getRandomColor } from "@/common/util.js";
import ColorPickerVue from "../components/colorPicker/ColorPicker.vue";

export default {
    name: "viewEdit",
    components: {
        componentsIframe,
        ObjectModal,
        MonitorModal,
        ColorPickerVue,
    },
    data() {
        // 验证图标缩放比例
        const validaIconscalingRatio = (rule, value, callback) => {
            const regex = /^(?:[1-9]\d{0,2}|1000)$/;
            if (!value) {
                callback(new Error(this.$t("dashboard_select_icon_scale")));
            } else if (!regex.test(value)) {
                callback(new Error("请输入1-1000间的整数数字"));
            } else {
                callback();
            }
            console.log(value, "输入的内容");
        };

        //视图名称校验
        const validaName = (rule, value, callback) => {
            if (!value || value === "") {
                callback(new Error(this.$t("dashboard_enter_org_name")));
            } else if (value.length > rule.Length) {
                callback(
                    new Error(
                        this.$t("dashboard_len_not_exceed") +
                        rule.Length +
                        this.$t("dashboard_position")
                    )
                );
            } else {
                callback();
            }
        };
        const validaRowColumn = (rule, value, callback) => {
            const valueArr = value.split("-");
            const num = /^[1-9]\d*$/;
            if (!value || value === "") {
                callback(
                    new Error(
                        this.$t("comm_enter") +
                        rule.name +
                        this.$t("dashboard_support_sheet2") +
                        this.$t("dashboard_example_57")
                    )
                );
            } else if (valueArr.length > 2) {
                callback(new Error(this.$t("comm_example")));
            } else {
                let ispas = true;
                for (let i = 0; i < valueArr.length; i++) {
                    if (!num.test(valueArr[i])) {
                        ispas = false;
                    }
                }
                if (!ispas) {
                    callback(new Error(this.$t("dashboard_integer_tip")));
                } else if (ispas) {
                    if (Number(valueArr[valueArr.length - 1]) > rule.max) {
                        var _msg = this.$t("dashboard_integer_tip2");
                        _msg = _msg.replace("@max", rule.max);
                        callback(new Error(_msg));
                    } else if (Number(valueArr[0]) < rule.min) {
                        var _msg = this.$t("dashboard_integer_tip3");
                        _msg = _msg.replace("@min", rule.min).replace("@name", rule.name);
                        callback(new Error(_msg));
                    } else {
                        if (Number(valueArr[valueArr.length - 1]) < Number(valueArr[0])) {
                            callback(new Error(this.$t("message_invalid")));
                        } else {
                            callback();
                        }
                    }
                }
            }
        };
        const validaProvince = (rule, value, callback) => {
            if (!value || value === "") {
                callback(new this.$t("comm_enter") + rule.name);
            } else {
                callback();
            }
        };
        // 多选省份校验个数
        const provinceMunicipality = (rule, value, callback) => {
            if (value.length === 0) {
                callback(new Error(this.$t("comm_please_select")));
            } else {
                callback();
            }
        };

        // 验证分组
        const validatorGroup = (rule, value, callback) => {
            if (value == null || value === "" || value.length == 0) {
                callback();
            } else {
                if (value.length > 10) {
                    callback(
                        new Error(this.$t("dashboard_component_real_alarm_group_limit_tip"))
                    );
                } else {
                    callback();
                }
            }
        };

        // 验证 端口信息
        const validatorPortId = (rule, value, callback) => {
            // 只有 任务我类型为设置的时候
            if (this.indicatorModalForm.objectType == 4) {
                if (value == undefined || value === "") {
                    callback(new Error(this.$t("comm_please_select") + rule.name));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };

        return {
            objRequiredText: "",
            // 对象的提示
            objRequiredEdit: false,
            // 区分编辑新增构件
            isEditComponent: false,
            bgList: [],
            isDashMap: false,
            // 构建是否是编辑状态
            monitorEditState: false,
            //比例
            ratio: "16:9",
            //行
            row: 8,
            rowArray: [],
            colArray: [],
            rowArrayCopy: [],
            //列
            column: 12,
            columnArray: [],
            //body宽度
            //计算格子属性width,height
            //格子总宽度
            tableW: document.body.clientWidth - 41,
            //格子总高度
            tableH: 0,
            //每个格子width,height
            grid: {
                width: 0,
                height: 0,
            },
            //模态框状态(开启状态)
            modalShow: false,
            editShow: false,
            objectModalShow: false,
            //模态框标题
            modalTitle: this.$t("but_add_widget"),
            editTitle: this.$t("but_edit_widget"),
            TagDatas: [],
            addShowType1:true,
            addShowTyp2:true,
            editShowTyp3:true,
            editShowTyp4:true,
            //新建参数
            addComponentForm: {
                typeCode: "",
                name: "",
                intervalTime: null,
                crossRow: "",
                crossCol: "",
                pathtopoType: 1,
                alarmListNum: "3",
                alarmListNum1: "10",
                topoId: [],
                provinceCode: "",
                provinceMunicipalityCode: [],
                graphicType: 2,
                objType: "", // 对象类型
                objectType: "", // 对象类型
                graphicList: [],
                groupId: [],
                // URL连接地址
                url: "",
                // 呈现类型
                taskPresentationType: "0",
                dashboardComponentTaskGroups: [
                    {
                        groupId: "", // 分组
                        dashboardViewId: "", // 链接
                    },
                ],
                // 端口的ID
                portId: "",
                backgroundImageId: "",
                // 实时监控分布对象
                taskIds: [],
                taskIdsStr: "",
                // 30天告警统计维度：1-每日新增告警数、2-每日发生告警对象数
                alarmStatisticsType:1,
                iconScalingRatio: "100",

                // 显示名称(0:显示，1-不显示)
                noShowName: 0,
                // 显示边框(0:显示，1-不显示)
                noShowFrame: 0,
                taskIds: [],
                // 实时告警
                componentRealAlarmConfigs: [
                    {
                        alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                        alarmRecoverStatus: 1, //告警状态恢复 ,0选择，1不选择
                        alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                        alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                        alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                        maintainLevelOne: 0, //运维等级
                        maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                        maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                        showRecentlyAlarmNum: 0, //显示最新告警条数
                        showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                        type: 1 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                    },
                    {
                        alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                        alarmRecoverStatus: 1, //告警状态恢复 ,0选择，1不选择
                        alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                        alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                        alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                        maintainLevelOne: 0, //运维等级
                        maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                        maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                        showRecentlyAlarmNum: 0, //显示最新告警条数
                        showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                        type: 0 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                    }
                ]
            },
            objectType: "",
            checkAll: false,
            indeterminate: true,
            //编辑参数
            editComponentForm: {
                crossCol: null,
                name: "",
                code: "",
                intervalTime: "",
                crossRow: null,
                pathtopoType: 1,

                id: null,
                typeCode: "",
                viewId: "",
                createTime: "",
                alarmListNum: "",
                alarmListNum1: "",
                topoId: [],
                provinceCode: "",
                provinceMunicipalityCode: [],
                orgId: "",
                key: "",
                editType: "",
                pageUrl: "",
                objType: "", // 对象类型
                graphicType: null,
                graphicList: [],
                // 30天告警统计维度：1-每日新增告警数、2-每日发生告警对象数
                alarmStatisticsType:1,
                groupId: [],
                // URL连接地址
                url: "",
                // 呈现类型
                taskPresentationType: "",
                dashboardComponentTaskGroups: [
                    {
                        groupId: "", // 分组
                        dashboardViewId: "", // 链接
                    },
                ],
                // 端口的ID
                portId: "",
                backgroundImageId: "",
                taskId: [],
                taskIdsStr: "",
                iconScalingRatio: "100",

                // 显示名称(0:显示，1-不显示)
                noShowName: 0,
                // 显示边框(0:显示，1-不显示)
                noShowFrame: 0,
                taskIds: [],
                componentRealAlarmConfigs: [
                    {
                        alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                        alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                        alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                        alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                        alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                        maintainLevelOne: 0, //运维等级
                        maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                        maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                        showRecentlyAlarmNum: 0, //显示最新告警条数
                        showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                        type: 1 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                    },
                    {
                        alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                        alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                        alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                        alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                        alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                        maintainLevelOne: 0, //运维等级
                        maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                        maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                        showRecentlyAlarmNum: 0, //显示最新告警条数
                        showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                        type: 0 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                    }
                ]
            },
            // 显示边框
            showBorderList: [
                //0:显示，1：不显示
                { value: 1, name: this.$t("comm_no_show") },
                { value: 0, name: this.$t("comm_show") },
            ],
            // 显示内容
            showList: [
                //0:显示，1：不显示
                { value: 0, name: this.$t("comm_show") },
                { value: 1, name: this.$t("comm_no_show") },
            ],
            //保存编辑时topoid
            physicstopoid: null,
            routetopoid: null,
            //选择的类型
            checkedType: "",
            checkedrouteTopo: "",
            checkedphysTopo: "",
            checkedFiberChart: "",
            checkedUrlLink: "",
            checkedStatistic: "",
            // 最近30天拨测新增告警走势、最近30天专线新增告警走势、最近30天中继新增告警走势 类型
            checkedRouteAlarmTreadType:'',
            checkedSpecialAlarmTreadType:'',
            checkedSnmpAlarmTreadType:'',
            // 监测目标分布图对象选中数据
            objSelectedData: [],
            // 监测目标分布图对象要删除的数据
            objDelData: [],
            //新建字段
            addFields: [
                // 类型
                {
                    name: this.$t("dashboard_type"),
                    prop: "typeCode",
                    type: "select",
                    key: 0,
                },
                // 名称
                {
                    name: this.$t("dashboard_name"),
                    prop: "name",
                    type: "input",
                    key: 1,
                },
                // 显示名称
                {
                    name: this.$t("dashboard_no_show_name"),
                    prop: "noShowName",
                    type: "checkbox",
                    key: 16,
                },
                // 显示边框
                {
                    name: this.$t("dashboard_no_show_border"),
                    prop: "noShowFrame",
                    type: "checkbox",
                    key: 17,
                },
                // 刷新间隔
                {
                    name: this.$t("dashboard_refresh_interval"),
                    prop: "intervalTime",
                    type: "select",
                    key: 2,
                },
                // 跨行
                {
                    name: this.$t("dashboard_cross_bank"),
                    prop: "crossRow",
                    type: "input",
                    key: 3,
                },
                // 跨列
                {
                    name: this.$t("dashboard_cross_column"),
                    prop: "crossCol",
                    type: "input",
                    key: 4,
                },
                // 显示告警条数
                {
                    name: this.$t("dashboard_alarms_displayed"),
                    prop: "alarmListNum",
                    type: "input",
                    key: 5,
                },
                // 显示告警条数
                {
                    name: this.$t("dashboard_alarms_displayed"),
                    prop: "alarmListNum1",
                    type: "input",
                    key: 19,
                },
                // 选择拓扑图
                {
                    name: this.$t("dashboard_selective_topology"),
                    prop: "topoId",
                    type: "select",
                    key: 6,
                },
                // 选择地图
                {
                    name: this.$t("dashboard_select_map"),
                    prop: "provinceCode",
                    type: "select",
                    key: 7,
                },
                // 样式
                {
                    name: this.$t("comm_pattern"),
                    prop: "pathtopoType",
                    type: "select",
                    key: 8,
                },
                // 对象类型
                {
                    name: this.$t("dashboard_object_type"),
                    prop: "objType",
                    type: "select",
                    key: 9,
                },
                // 省/直辖市
                {
                    name: this.$t("dashboard_select_province"),
                    prop: "provinceMunicipalityCode",
                    type: "select",
                    key: 10,
                },
                // 分组
                {
                    name: this.$t("comm_group"),
                    prop: "groupId",
                    type: "select",
                    key: 11,
                },
                // 添加 URL 链接
                { name: this.$t("dashboard_url"), prop: "url", type: "input", key: 12 },
                  // 30天告警统计维度
                {name: this.$t('dashboard_alarm_statistics_type'), prop: 'alarmStatisticsType', type: 'select', key: 23},
                // 统计分析图
                { name: "选择呈现类型", prop: "taskPresentationType", type: "select", key: 18 },
                // 监测目标分布图特有
                // 背景图
                // {name:this.$t('icon_bg_label'),prop:'bgUrl',type:'select',key:13},
                // // 图标缩放比例
                // {name:this.$t('icon_zoom_ratio'),prop:'iconZoom',type:'input',key:14},
                // // 展示的目标
                // {name:this.$t('display_objectives'),prop:'targetId',type:'table',key:15}
            ],
            diagramsAarray: [],
            linkList: [],
            groupList: [],
            //构件类型
            componentTypeAarray: [],
            //topo图拓扑类型
            topoType: [],
            //分组类型
            groupType: [],
            // 路径拓扑图样式下拉
            pathTopoTypeList: [
                { name: this.$t("routine-horizontal"), code: 1 },
                { name: this.$t("routine-vertical"), code: 3 },
                { name: this.$t("routine-repulsion"), code: 4 },
                { name: this.$t("fiber-horizontal"), code: 2 },
                { name: this.$t("fiber-vertical"), code: 5 },

            ],
            // 30天告警统计维度 ,1-每日新增告警数、2-每日发生告警对象数
            alarmStatisticsTypeList:[{value:1,name:this.$t('dashboard_alarm_statistics_select1')},{value:2,name:this.$t('dashboard_alarm_statistics_select2')}],
            //地图选择下拉
            gisList: [
                { name: "全国", code: 100000 },
                { name: "省/直辖市", code: 100001 },
            ],
            //地图选择下拉(省/直辖市)
            gisListSub: [
                { name: "北京", code: 110000, isDisabled: false },
                { name: "天津", code: 120000, isDisabled: false },
                { name: "河北", code: 130000, isDisabled: false },
                { name: "山西", code: 140000, isDisabled: false },
                { name: "内蒙古", code: 150000, isDisabled: false },
                { name: "辽宁", code: 210000, isDisabled: false },
                { name: "吉林", code: 220000, isDisabled: false },
                { name: "黑龙江", code: 230000, isDisabled: false },
                { name: "上海", code: 310000, isDisabled: false },
                { name: "江苏", code: 320000, isDisabled: false },
                { name: "浙江", code: 330000, isDisabled: false },
                { name: "安徽", code: 340000, isDisabled: false },
                { name: "福建", code: 350000, isDisabled: false },
                { name: "江西", code: 360000, isDisabled: false },
                { name: "山东", code: 370000, isDisabled: false },
                { name: "河南", code: 410000, isDisabled: false },
                { name: "湖北", code: 420000, isDisabled: false },
                { name: "湖南", code: 430000, isDisabled: false },
                { name: "广东", code: 440000, isDisabled: false },
                { name: "广西", code: 450000, isDisabled: false },
                { name: "海南", code: 460000, isDisabled: false },
                { name: "重庆", code: 500000, isDisabled: false },
                { name: "四川", code: 510000, isDisabled: false },
                { name: "贵州", code: 520000, isDisabled: false },
                { name: "云南", code: 530000, isDisabled: false },
                { name: "西藏", code: 540000, isDisabled: false },
                { name: "陕西", code: 610000, isDisabled: false },
                { name: "甘肃", code: 620000, isDisabled: false },
                { name: "青海", code: 630000, isDisabled: false },
                { name: "宁夏", code: 640000, isDisabled: false },
                { name: "新疆", code: 650000, isDisabled: false },
                { name: "台湾", code: 710000, isDisabled: false },
                { name: "香港", code: 810000, isDisabled: false },
                { name: "澳门", code: 820000, isDisabled: false },
            ],
            //路径下拉
            pathDown: [],
            pathDownDataList: [],
            //物理下拉
            physDown: [],
            //分组下拉
            groupDown: [],
            //构件刷新时间间隔
            componentRefreshAarray: [
                {
                    intervalTime: this.$t("dashboard_one_minute"),
                    key: "intervalTime-2",
                    refreshTime: 60,
                },
                {
                    intervalTime: this.$t("dashboard_five_minutes"),
                    key: "intervalTime-3",
                    refreshTime: 5 * 60,
                },
                {
                    intervalTime: this.$t("dashboard_ten_minutes"),
                    key: "intervalTime-4",
                    refreshTime: 10 * 60,
                },
                {
                    intervalTime: this.$t("dashboard_no_refresh"),
                    key: "intervalTime-5",
                    refreshTime: 0,
                },
            ],
            //验证规则
            rulesValidate: {
                typeCode: [
                    {
                        required: true,
                        type: "string",
                        message: this.$t("comm_select") + this.$t("dashboard_type"),
                        trigger: "change",
                    },
                ],
                name: [
                    {
                        required: true,
                        type: "string",
                        trigger: "blur",
                        name: this.$t("dashboard_name"),
                        minLength: 1,
                        maxLength: 100,
                        validator: validate.validateStrContainsSpecialChars,
                    },
                ],
                intervalTime: [
                    {
                        required: true,
                        type: "number",
                        message: this.$t("dashboard_selec_refresh_interval"),
                        trigger: "change",
                    },
                ],
                crossRow: [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("dashboard_cross_bank"),
                        min: 1,
                        max: 0,
                        trigger: "blur",
                        validator: validaRowColumn,
                    },
                ],
                crossCol: [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("dashboard_cross_column"),
                        min: 1,
                        max: 0,
                        trigger: "blur",
                        validator: validaRowColumn,
                    },
                ],
                alarmListNum: [
                    {
                        required: true,
                        type: "string",
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ],
                alarmListNum1: [
                    {
                        required: true,
                        type: "string",
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ],
                topoId: [
                    {
                        required: true,
                        // type: "string",
                        message: this.$t("comm_select_topology_type"),
                        name: this.$t("comm_opology_type"),
                        trigger: "blur",
                    },
                ],

                objectType: [
                    {
                        required: true,
                        message: this.$t("dashboard_select_ot"),
                        name: this.$t("dashboard_object_type"),
                        trigger: "blur",
                    },
                ],
                provinceCode: [
                    {
                        required: true,
                        type: "number",
                        name: this.$t("dashboard_region"),
                        trigger: "change",
                        validator: validaProvince,
                    },
                ],
                provinceMunicipalityCode: [
                    {
                        trigger: "change",
                        validator: provinceMunicipality,
                    },
                ],
                pathtopoType: [
                    {
                        required: true,
                        type: "number",
                        message: this.$t("dashboard_select_artifact_type"),
                        name: this.$t("comm_topo_style"),
                        trigger: "blur",
                    },
                ],
                dashboardComponentTaskGroups: {
                    type: "array",
                    required: true,
                    trigger: "change",
                    fields: {
                        0: {
                            fields: {
                                groupId: {
                                    required: true,
                                    message: "请选择分组",
                                    trigger: "change",
                                },
                            },
                        },
                    },
                },
                taskPresentationType: [
                    {
                        required: true,
                        type: "string",
                        message: this.$t("comm_select") + "呈现类型",
                        trigger: "change",
                    },
                ],
                url: [
                    {
                        required: true,
                        name: this.$t("dashboard_url"),
                        type: "string",
                        Length: 200,
                        validator: validaUrl,
                        trigger: "change",
                    },
                ],
                backgroundImageId: [
                    {
                        required: true,
                        trigger: "change",
                        validator: validaBackgroundImageId,
                        message: this.$t("icon_bg_placeholder"),
                    },
                ],
                iconScalingRatio: [
                    {
                        required: true,
                        message: this.$t("dashboard_select_icon_scale"),
                        trigger: "change",
                    },
                    {
                        validator: validaIconscalingRatio,
                        trigger: "change",
                    },
                ],

                groupId: [
                    {
                        type: "number",
                        name: this.$t("comm_group"),
                        trigger: "change",
                        validator: validatorGroup,
                    },
                ],
            },
            editRulesValidate: {
                typeCode: [
                    {
                        required: true,
                        type: "string",
                        message: "请选择构件类型",
                        trigger: "change",
                    },
                ],
                name: [
                    {
                        required: true,
                        type: "string",
                        trigger: "blur",
                        name: this.$t("dashboard_name"),
                        minLength: 1,
                        maxLength: 100,
                        validator: validate.validateStrContainsSpecialChars,
                    },
                ],
                intervalTime: [
                    {
                        required: true,
                        type: "number",
                        message: this.$t("dashboard_selec_refresh_interval"),
                        trigger: "change",
                    },
                ],
                crossRow: [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("dashboard_line"),
                        min: 0,
                        max: 0,
                        trigger: "blur",
                        validator: validaRowColumn,
                    },
                ],
                crossCol: [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("dashboard_column"),
                        min: 0,
                        max: 0,
                        trigger: "blur",
                        validator: validaRowColumn,
                    },
                ],
                alarmListNum: [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("comm_alarms_number"),
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ],
                alarmListNum1: [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("comm_alarms_number"),
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ],
                objectType: [
                    {
                        required: true,
                        message: this.$t("dashboard_select_ot"),
                        name: this.$t("dashboard_object_type"),
                        trigger: "blur",
                    },
                ],
                provinceCode: [
                    {
                        required: true,
                        type: "number",
                        name: this.$t("dashboard_region"),
                        trigger: "change",
                        validator: validaProvince,
                    },
                ],
                provinceMunicipalityCode: [
                    {
                        validator: provinceMunicipality,
                    },
                ],
                pathtopoType: [
                    {
                        required: true,
                        type: "number",
                        message: this.$t("dashboard_select_artifact_type"),
                        name: this.$t("comm_topo_style"),
                        trigger: "blur",
                    },
                ],
                topoId: [
                    {
                        required: true,
                        // type: "string",
                        message: this.$t("comm_select_topology_type"),
                        name: this.$t("comm_opology_type"),
                        trigger: "blur",
                    },
                ],
                dashboardComponentTaskGroups: {
                    type: "array",
                    required: true,
                    trigger: "change",
                    fields: {
                        0: {
                            fields: {
                                groupId: {
                                    required: true,
                                    message: "请选择分组",
                                    trigger: "change",
                                },
                            },
                        },
                    },
                },
                taskPresentationType: [
                    {
                        required: true,
                        type: "string",
                        message: this.$t("comm_select") + "呈现类型",
                        trigger: "change",
                    },
                ],
                url: [
                    {
                        required: true,
                        name: this.$t("dashboard_url"),
                        type: "string",
                        Length: 200,
                        validator: validaUrl,
                        trigger: "blur",
                    },
                ],

                groupId: [
                    {
                        type: "number",
                        name: this.$t("comm_group"),
                        trigger: "change",
                        validator: validatorGroup,
                    },
                ],
                backgroundImageId: [
                    {
                        required: true,
                        trigger: "change",
                        validator: validaBackgroundImageId,
                        message: this.$t("icon_bg_placeholder"),
                    },
                ],
                iconScalingRatio: [
                    {
                        required: true,
                        message: this.$t("dashboard_select_icon_scale"),
                        trigger: "change",
                    },
                    {
                        validator: validaIconscalingRatio,
                        trigger: "change",
                    },
                ],
            },
            // 监测分布图选中对象表格
            spreadMapcolumns: [
                {
                    type: "selection",
                    width: 30,
                    align: "left",
                    render: (h, params) => { },
                },
                {
                    title: this.$t("comm_target_name"),
                    key: "destIpName",
                    render: (h, params) => {
                        let str = params.row.destIpName;
                        str =
                            str === undefined || str === null || str === "" || str === "null"
                                ? "--"
                                : str;
                        return h(
                            "div",
                            {
                                style: {
                                    textAlign: "left",
                                    width: "100%",
                                    textIndent: "0px",
                                    overflow: "hidden", //超出的文本隐藏
                                    textOverflow: "ellipsis", //溢出用省略号显示
                                    whiteSpace: "nowrap", //溢出不换行
                                },
                                attrs: {
                                    title: str,
                                },
                            },
                            str
                        );
                    },
                },
                {
                    title: this.$t("comm_IP(MAC)"),
                    key: "destIp",
                    ellipsis: true,
                    render: (h, params) => {
                        let config_type = params.row.configType;
                        let destMac = params.row.destMac ?? "--";
                        let text = "";
                        let showText = "";
                        let titleText = JSON.parse(JSON.stringify(params.row.destIp));
                        let str = params.row.destIp;
                        if (str == undefined || str == null || str == "") {
                            text = "--";
                            titleText = "--";
                        } else if (str.length > 16) {
                            text = str.substring(0, 16) + "...";
                        } else {
                            text = str;
                        }
                        showText = text + "(" + destMac + ")";
                        titleText = titleText + "(" + destMac + ")";
                        let dest_ip_name = params.row.destIpName;
                        let nameText = "";
                        if (
                            dest_ip_name == undefined ||
                            dest_ip_name == null ||
                            dest_ip_name == ""
                        ) {
                            nameText = "--";
                        } else {
                            nameText = dest_ip_name;
                        }
                        let title =
                            (showText != "--"
                                ? (config_type != 8
                                    ? this.$t("access_destination_ip") + "："
                                    : this.$t("access_destination_acc") + "：") + showText
                                : "") +
                            (nameText != "--"
                                ? "," + this.$t("probetask_target_name") + "：" + dest_ip_name
                                : "");
                        return h(
                            "span",
                            {
                                class: {
                                    "text-ellipsis": true,
                                },
                                domProps: {
                                    title: titleText,
                                },
                            },
                            showText
                        );
                    },
                },
            ],
            //已经存在的构件
            componentList: [],
            //新建构件列表数据
            addComponentList: [],
            //编辑构件列表数据
            editComponentList: [],
            //渲染的数据列表
            viewList: [],
            //删除的列表数据
            deleteComponentList: [],
            //当前最后一行位置
            currentRow: 0,
            //当前最后一列位置
            currentColumns: 0,
            //视图id
            viewId: null,
            // 控制选择10个省份之后不可点击
            totalDisabled: false,
            //聚合图形列表表头
            targetTableColumns: [
                {
                    title: this.$t("dashboard_name"),
                    align: "left",
                    render: (h, params) => {
                        // getTypes
                        let str =
                            params.row.name +
                            "：" +
                            this.getTypes(params.row.objectType)[params.row.indicatorType];
                        return h(
                            "span",
                            {
                                style: {
                                    cursor: "default",
                                    overflow: "hidden", //超出的文本隐藏
                                    textOverflow: "ellipsis", //溢出用省略号显示
                                    whiteSpace: "nowrap", //溢出不换行
                                },
                                attrs: {
                                    title: str,
                                },
                            },
                            str
                        );
                    },
                },
                {
                    title: this.$t("dashboard_color"),
                    width: 110,
                    align: "center",
                    className: "colorCell",
                    slot: "colorAction",
                },
                {
                    title: this.$t("common_controls"),
                    width: 120,
                    align: "center",
                    render: (h, params) => {
                        return h(
                            "span",
                            {
                                style: {
                                    color: "#2b85e4",
                                    cursor: "pointer",
                                },
                                on: {
                                    click: () => {
                                        this.targetTableDatas.splice(params.index, 1);
                                    },
                                },
                            },
                            this.$t("phytopo_remvoe")
                        );
                    },
                },
            ],
            targetsTableColumns: [
                {
                    title: this.$t("dashboard_name"),
                    align: "left",
                    key: "name",
                },
                {
                    title: this.$t("dashboard_color"),
                    width: 110,
                    align: "center",
                    className: "colorCell",
                    slot: "colorAction",
                },
                {
                    title: this.$t("common_controls"),
                    width: 120,
                    align: "center",
                    render: (h, params) => {
                        return h(
                            "span",
                            {
                                style: {
                                    color: "#2b85e4",
                                    cursor: "pointer",
                                },
                                on: {
                                    click: () => {
                                        this.targetsTableDatas.splice(params.index, 1);
                                    },
                                },
                            },
                            this.$t("phytopo_remvoe")
                        );
                    },
                },
            ],
            //聚合图形列表数据
            targetTableDatas: [],
            targetsTableDatas: [],
            //匹配聚合类型
            indicatorTypeList: {
                "1": this.$t("dashboard_line_name"),
                "2": this.$t("dash_relay"),
                "3": this.$t("dashboard_task_number"),
            },
            //聚合对象类型
            indicatorList: {
                "1": this.$t("dash_Special_line"),
                "2": this.$t("dash_relay"),
                "3": this.$t("dash_commissioning_task"),
                "4": this.$t("dash_device_task"),
            },
            typeList: {
                1: this.$t("view_Leased_type_1"),
                2: this.$t("view_Leased_type_2"),
                3: this.$t("view_Leased_type_7"),
                4: this.$t("view_Leased_type_10"),
                5: this.$t("view_Leased_type_3"),
                6: this.$t("view_Leased_type_5"),
                7: this.$t("view_Leased_type_6"),
                8: this.$t("view_Leased_type_8"),
            },
            // 端口信息集合
            portList: [],
            typesList: [],
            //聚合图形指标选择
            indicatorFields: [
                {
                    name: this.$t("dashboard_object_type"),
                    prop: "objectType",
                    type: "select",
                    key: 0,
                    show: true,
                },
                {
                    name: this.$t("dashboard_ob"),
                    prop: "objectId",
                    type: "select",
                    key: 1,
                    show: true,
                },
                {
                    name: this.$t("dashboard_port"),
                    prop: "portId",
                    type: "select",
                    key: 3,
                    show: false,
                },
                {
                    name: this.$t("dashboard_index"),
                    prop: "indicatorType",
                    type: "select",
                    key: 2,
                    show: true,
                },
            ],
            indicatorModal: {
                show: false,
            },
            indicatorsModalShow: false,
            indicatorObject: [],
            indicatorsModalForm: {
                indicatorType: [],
            },
            indicatorModalForm: {
                objectType: null,
                objectId: null,
                indicatorType: null,
                portId: null,
            },
            indicatorModalRule: {
                objectType: [
                    {
                        required: true,
                        type: "string",
                        message: this.$t("dashboard_select_ot"),
                        trigger: "change",
                    },
                ],
                objectId: [
                    {
                        required: true,
                        type: "string",
                        message: this.$t("dashboard_select_ob"),
                        trigger: "change",
                    },
                ],
                indicatorType: [
                    {
                        required: true,
                        type: "string",
                        message: this.$t("dashboard_select_indicators"),
                        trigger: "change",
                    },
                ],
                portId: [
                    {
                        required: true,
                        name: this.$t("dashboard_port"),
                        type: "string",
                        validator: validatorPortId,
                        trigger: "change",
                    },
                ],
            },
            //默认颜色
            defaultColor: "#398210",
            currentClientWidth: 0,
            topoInfos: []
        };
    },
    watch: {
        objSelectedData: {
            handler(newValue, oldValue) {
                if (this.objSelectedData.length > 2000) {
                    this.objRequiredEdit = true;
                    this.objRequiredText = this.$t("dashboard_obj_max");
                } else if (this.objSelectedData.length === 0) {
                    this.objRequiredEdit = true;
                    this.objRequiredText = this.$t("dashboard_obj_required");
                } else {
                    this.objRequiredEdit = false;
                }
            },
            deep: true,
        }
    },
    created() {
        //此逻辑是判断专业版和企业版的屏蔽中继信息
        const anpmVersion = JSON.parse(sessionStorage.getItem("accessToken"))
            .anpmVersion;
        if (anpmVersion != "1") {
            this.indicatorList = {
                "1": this.$t("dash_Special_line"),
                "3": this.$t("dash_commissioning_task"),
            };
        }
        const routerParams = this.$route.query;
        window.isEdit = true;
        this.viewId = routerParams.id;
        let windowResolution = routerParams.windowResolution;
        this.ratio = windowResolution;
        this.row = routerParams.rowNum;
        this.column = routerParams.colNum;
        this.rulesValidate.crossRow[0].max = this.row;
        this.rulesValidate.crossCol[0].max = this.column;
        this.editRulesValidate.crossRow[0].max = this.row;
        this.editRulesValidate.crossCol[0].max = this.column;
        //初始化表格范围
        this.setGridProperty(this.ratio, this.row, this.column);
        //初始化表格
        for (let i = 0; i < this.row; i++) {
            this.rowArray.push({ rowIndex: i + 1, columns: [] });
            for (let column = 0; column < this.column; column++) {
                this.rowArray[i].columns.push({
                    columnKey: this.rowArray[i].rowIndex + "-" + (column + 1),
                    columIndex: column + 1,
                    colspan: 1,
                    rowspan: 1,
                });
            }
        }
        for (let i = 0; i < this.column; i++) {
            this.colArray.push({ rowIndex: i + 1, columns: [] });
        }

        this.rowArrayCopy = JSON.parse(JSON.stringify(this.rowArray));

        this.getComponentList().then(({ code, data, msg }) => {
            if (code === 1) {
                data.map((item) => {
                    item.key = md5(
                        new Date().getTime() + "t" + Math.random() * 1024 + "r"
                    );
                    let crossRowArr = item.crossRow.split("-");
                    let crossColArr = item.crossCol.split("-");
                    item.startRow = crossRowArr[0];
                    item.endRow = crossRowArr[crossRowArr.length - 1];
                    item.startCol = crossColArr[0];
                    item.endCol = crossColArr[crossColArr.length - 1];
                    item.graphicType = 1;
                    item.graphicList = item.graphicList || [];
                    item.provinceCode = item.provinceCode
                        ? Number(item.provinceCode)
                        : 100000;
                    item.groupIds = item.groupId ? item.groupId : "";
                    item.alarmStatisticsType = item.alarmStatisticsType ? item.alarmStatisticsType : '';
                    item.url = item.url || "";
                    item.portId = item.portId || "";
                    item.typeCode = item.typeCode || "";
                    if (item.typeCode === "monitor_target_spread_map") {
                        item.monitorTargetSpreadTaskInfos = item.monitorTargetSpreadTaskInfos
                            ? item.monitorTargetSpreadTaskInfos
                            : [];
                        item.taskIds = item.monitorTargetSpreadTaskInfos.map(
                            (item) => item.id
                        );
                    }
                });
                this.componentList = JSON.parse(JSON.stringify(data));
                this.viewList = JSON.parse(JSON.stringify(data));
                this.handleTableProp();
            }
        });

        this.getComponentTypeAarray();
        this.getPathDown();
        this.getPhysDown();
        this.getGroupDown();
        // 加载背景图
        this.getBgList();
        // 选择呈现类型
        this.queryCode();
        // 视图列表
        this.viewArray();
    },
    mounted() {
        addDraggable();
        window.addEventListener("resize", this.changeWidth);
    },
    methods: {
        // 视图列表
        viewArray() {
            this.$http.post("/dashboard/view/list",{filterViewId:this.viewId}).then((res) => {
                console.log(res, "视图列表");
                console.log('465454564654564654564');
                if (res.code === 1) {
                    this.linkList = res.data;
                }
            });
        },
        //呈现类型
        queryCode() {
            this.$http.post("/dataTable/queryCode", { 'key': 'taskAnalysisPresentationType' }).then((res) => {
                if (res.code === 1) {
                    this.diagramsAarray = res.data;
                }
            });
        },
        handleTopoChange(selectedValues) {

            if (selectedValues && selectedValues.length > 5) {
                  this.$Message.warning(this.$t('dashboard_max_5_items'));
                this.addComponentForm.topoId = selectedValues.slice(0,5);
            }
        },
        // 编辑
        handleTopoEditChange(selectedValues) {

            if (selectedValues && selectedValues.length > 5) {
                  this.$Message.warning(this.$t('dashboard_max_5_items'));
                this.editComponentForm.topoId = selectedValues.slice(0,5);
            }
        },
        // 创建
        handleGroupChange(value, option) {

            console.log('分组变化前：', JSON.stringify(this.addComponentForm.dashboardComponentTaskGroups));
            this.addComponentForm.dashboardComponentTaskGroups.forEach((item, index) => {
                if (item.groupId === value) {
                    this.$set(this.addComponentForm.dashboardComponentTaskGroups[index], 'groupId', value);
                }
            });
            // 强制触发表单验证
            this.$nextTick(() => {
                this.$refs["addComponentForm"].validate();
            });
            console.log('分组变化后：', JSON.stringify(this.addComponentForm.dashboardComponentTaskGroups));
        },
        // 添加新项
        addItem() {
            if (this.addComponentForm.dashboardComponentTaskGroups.length < 10) {
                this.$set(
                    this.addComponentForm.dashboardComponentTaskGroups,
                    this.addComponentForm.dashboardComponentTaskGroups.length,
                    {
                        groupId: "",
                        dashboardViewId: "",
                    }
                );
            }else{
                this.$Message.warning("最多添加十个！");
            }
        },
        // 删除项
        removeItem(index) {
            this.addComponentForm.dashboardComponentTaskGroups.splice(index, 1);
        },

        // 编辑项
        editHandleGroupChange() {
            // 强制触发表单验证
            this.$nextTick(() => {
                this.$refs["editComponentForm"].validate();
            });
        },
        editItem() {
            if (this.editComponentForm.dashboardComponentTaskGroups.length < 10) {
                this.$set(
                    this.editComponentForm.dashboardComponentTaskGroups,
                    this.editComponentForm.dashboardComponentTaskGroups.length,
                    {
                        groupId: "",
                        dashboardViewId: "",
                    }
                );
            }else{
                this.$Message.warning("最多添加十个！");
            }
        },
        // 删除项
        editremoveItem(index) {
            this.editComponentForm.dashboardComponentTaskGroups.splice(index, 1);
        },
        handleSelectOpenChange(isOpen) {
            if (isOpen) {
                // 下拉框打开时强制刷新选项列表
                this.$nextTick(() => {
                    this.pathTopoTypeList = [...this.pathTopoTypeList];
                });
            }
        },

        // 背景图片列表
        async getBgList() {
            let page = {
                pageNo: 1,
                pageSize: 10000,
            };
            try {
                const res = await this.$http.PostJson("/backgroundimage/list", page);
                if (res.code === 1) {
                    this.bgList = res.data.records;
                } else {
                }
            } catch (err) { }
        },
        delObj(type) {
            console.log(this.objSelectedData, this.objDelData);
            console.log(this.objSelectedData.length, this.objDelData.length);
            if (this.objSelectedData.length === this.objDelData.length) {
                this.objSelectedData = [];
            } else {
                let arr = [];
                arr = this.objDelData.map((item2) => {
                    return item2.id;
                });
                this.objSelectedData = this.objSelectedData.filter((item) => {
                    if (arr.indexOf(item.id) == -1) {
                        return item;
                    }
                });
            }

            // this.objSelectedData = arr
            if (type === "btnAdd") {
                this.addComponentForm.taskIds = this.objSelectedData.map(
                    (item) => item.id
                );
                console.log(this.objSelectedData, this.addComponentForm.taskIds);
            } else {
                this.editComponentForm.taskIds = this.objSelectedData.map(
                    (item) => item.id
                );
            }
        },
        handleSelect(selection) {
            console.log(selection);
            this.objDelData = selection;
        },
        delTag(index) {
            this.TagDatas.splice(index, 1);
        },
        getSelectData(data) {
            // debugger
            console.log(this.isDashMap, "什么构件");
            if (this.isDashMap) {
                // 监控目标分布图
                // 区分编辑新增
                this.objSelectedData = data;
                if (this.isEditComponent) {
                    // 编辑
                    this.editComponentForm.taskIds = data.map((item) => item.id);
                    console.log(this.addComponentForm.taskIds, "对象");
                    this.editComponentForm.taskIdsStr = this.addComponentForm.taskIds.join(
                        ","
                    );
                } else {
                    this.addComponentForm.taskIds = data.map((item) => item.id);
                    console.log(this.addComponentForm.taskIds, "对象");
                    this.addComponentForm.taskIdsStr = this.addComponentForm.taskIds.join(
                        ","
                    );
                }
            } else {
                let selectData = [];
                selectData = [
                    ...new Set(data.map((item) => JSON.stringify(item))),
                ].map((i) => JSON.parse(i)); // 数组去重
                // 总数限制
                let total = 30,
                    _typeCode = "";
                if (this.monitorEditState) {
                    _typeCode = this.editComponentForm["typeCode"];
                } else {
                    _typeCode = this.addComponentForm["typeCode"];
                }
                if (_typeCode.search("real_time_monitor") > -1) {
                    // 实时监控的对象在 300 个任务之内
                    total = 300;
                } else {
                    total = 30;
                }
                if (selectData.length > total) {
                    this.$Message.warning(this.$t("view_obj_num") + total + "！");
                    return;
                }
                this.TagDatas = [];
                // 如果是拨测任务
                if (this.objectType == 3) {
                    selectData.forEach((item) => {
                        const name = item.destIpName
                            ? item.destIpName +
                            "(" +
                            (item.sourceIp || "--") +
                            "-" +
                            (item.destIp || "--") +
                            ")"
                            : "--" + (item.sourceIp || "--") + "-" + (item.destIp || "--");
                        this.TagDatas.push({
                            name: name,
                            id: item.id,
                            objectId: item.id,
                            aip: item.sourceIp,
                            zip: item.destIp,
                            objType: this.objectType,
                        });
                    });
                    return;
                }
                // 如果是中继
                if (this.objectType == 2) {
                    selectData.forEach((item) => {
                        const name = item.repeatName
                            ? item.repeatName + "(" + (item.destIp || "--") + ")"
                            : item.taskCode + "(" + (item.destIp || "--") + ")";
                        this.TagDatas.push({
                            name: name,
                            id: item.id,
                            objectId: item.id,
                            zip: item.destIp,
                            objType: this.objectType,
                        });
                    });
                    return;
                }
                // 如果是专线
                if (this.objectType == 1) {
                    selectData.forEach((item) => {
                        let name = item.name
                            ? item.name + "(" + (item.specialZIp || "--") + ")"
                            : item.specialNum + "(" + (item.specialZIp || "--") + ")";
                        this.TagDatas.push({
                            name: name,
                            id: item.id,
                            objectId: item.id,
                            zip: item.specialZIp,
                            objType: this.objectType,
                        });
                    });
                    return;
                }
            }
        },
        // 选择对象类型
        chooseType(data) {
            this.objectType = data.value;
            this.addComponentForm.objectType = this.objectType;
            this.editComponentForm.objectType = this.objectType;
            this.typesList = this.getTypes(data.value);
            this.TagDatas = [];
            this.targetsTableDatas = [];
        },
        // 选择对象
        addMonitorBoxObj(formName) {
            // debugger
            this.isDashMap = false;
            console.log(formName, "111111111111111111111111s11111111");
            if (!this[formName].objType) {
                this.$Message.warning(this.$t("dashboard_first_select_ot"));
                return;
            }
            this.objectType = this[formName].objType;
            this.objectModalShow = true;
        },

        // 选择对象
        addObj(formName) {
            // debugger;
            console.log(formName, "111111111111111111111111s11111111");
            if (formName === "btnAdd") {
                this.isDashMap = true;
                this.objectType = "3";
                this.TagDatas = [];
                this.objSelectedData.forEach((item) => {
                    this.TagDatas.push({
                        name: item.name,
                        id: item.id,
                        objectId: item.id,
                        aip: item.sourceIp,
                        zip: item.destIp,
                        objType: this.objectType,
                    });
                });
                this.objectModalShow = true;
                return;
            }
            if (formName === "btnEdit") {
                this.isDashMap = true;
                this.objectType = "3";
                this.TagDatas = [];
                console.log(this.objSelectedData);
                // debugger
                this.objSelectedData.forEach((item) => {
                    this.TagDatas.push({
                        name: item.name,
                        id: item.id,
                        objectId: item.id,
                        aip: item.sourceIp,
                        zip: item.destIp,
                        objType: this.objectType,
                    });
                });
                this.objectModalShow = true;
                return;
            }
            this.isDashMap = false;
            // console.log(isDashMap,'是否正确')
            if (!this.objectType) {
                this.$Message.warning(this.$t("dashboard_first_select_ot"));
                return;
            }
            this.objectModalShow = true;
        },
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;

            if (this.checkAll) {
                this.indicatorsModalForm.indicatorType = Object.values(this.typesList);
            } else {
                this.indicatorsModalForm.indicatorType = [];
            }
        },
        checkAllGroupChange(data) {
            if (data.length === Object.values(this.typesList).length) {
                this.indeterminate = false;
                this.checkAll = true;
            } else if (data.length > 0) {
                this.indeterminate = true;
                this.checkAll = false;
            } else {
                this.indeterminate = false;
                this.checkAll = false;
            }
        },
        //改变告警颜色框
        changeBorder() { },
        //改变宽度
        changeWidth() {
            this.tableW = document.body.clientWidth - 41;
            this.setGridProperty(this.ratio, this.row, this.column);
        },
        //类型选择
        addtypeChange(val, type) {
            console.log(val, type, "选中的东西");
            // debugger;

            this.targetTableDatas = []; // 切换类型时，清空聚合图形的指标数据
            this.TagDatas = [];
            if (val.search("route_statistic") !== -1) {
                // 当选择统计类型时，初始化 items
                this.addComponentForm.dashboardComponentTaskGroups = [
                    {
                        groupId: "",
                        dashboardViewId: "",
                    },
                ];
            }
            if (val.search("real_time_alarm") !== -1) {
                this.editRulesValidate.provinceCode = [];
                this.editRulesValidate.provinceMunicipalityCode = [];
                this.rulesValidate.alarmListNum = [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("comm_alarms_number"),
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ];
                this.rulesValidate.alarmListNum1 = [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("comm_alarms_number"),
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ];
                this.groupType = this.groupDown;
                // 重置选择拓扑下拉框
                this.$refs["addComponentForm"].fields.forEach(function (e) {
                    if (e.prop === "groupId") {
                        e.resetField();
                    }
                });
            } else if (val.search("timely_map") !== -1) {
                this.rulesValidate.alarmListNum = [];
                this.rulesValidate.provinceCode = [
                    {
                        required: true,
                        type: "number",
                        name: this.$t("dashboard_region"),
                        trigger: "change",
                        // validator: validaProvince
                    },
                ];
            } else if (val.search("monitor_target_spread_map") !== -1) {
                this.editRulesValidate.provinceCode = [];
                this.editRulesValidate.provinceMunicipalityCode = [];
                this.rulesValidate.alarmListNum = [];
                this.rulesValidate.url = [];
                this.objRequiredEdit = false;
            } else {
                this.editRulesValidate.provinceCode = [];
                this.editRulesValidate.provinceMunicipalityCode = [];
                this.rulesValidate.alarmListNum = [];
                this.rulesValidate.url = [];
                if (
                    val.search("route_topo") !== -1 ||
                    val.search("fiber_chart") !== -1
                ) {
                    this.rulesValidate.topoId = [
                        {
                            required: true,
                            // type: "string",
                            message: this.$t("comm_select_topology_type"),
                            name: this.$t("comm_opology_type"),
                            trigger: "blur",
                        },
                    ];
                    this.topoType = this.pathDown;
                    // 重置选择拓扑下拉框
                    this.$refs["addComponentForm"].fields.forEach(function (e) {
                        if (e.prop === "topoId") {
                            e.resetField();
                        }
                    });
                    // 重新渲染数据
                    this.pathTopoTypeSelectChange(this.addComponentForm.pathtopoType);
                } else if (val.search("physics_topo") !== -1) {
                    this.rulesValidate.topoId = [
                        {
                            required: true,
                            // type: "string",
                            message: this.$t("comm_select_topology_type"),
                            name: this.$t("comm_opology_type"),
                            trigger: "blur",
                        },
                    ];
                    this.topoType = this.physDown;
                    // 重置选择拓扑下拉框
                    this.$refs["addComponentForm"].fields.forEach(function (e) {
                        if (e.prop === "topoId") {
                            e.resetField();
                        }
                    });
                } else if (val.search("real_time_monitor") !== -1) {
                    this.TagDatas = [];
                    this.addComponentForm["objType"] = "";
                    // 重置对象类型下拉框
                    this.$refs["addComponentForm"].fields.forEach(function (e) {
                        if (e.prop === "objType") {
                            e.resetField();
                        }
                    });
                } else if (val.search("url_link") !== -1) {
                    // // URL 链接
                    //  this.rulesValidate.url = [
                    //   {
                    //     required: true,
                    //     name: this.$t("dashboard_url"),
                    //     type: "string",
                    //     Length: 200,
                    //     validator: validaUrl,
                    //     trigger: "blur",
                    //   }
                    // ];
                }else if(val.search('route_alarm_trend') !==-1 || val.search('special_alarm_trend') !==-1 || val.search('snmp_alarm_trend') !==-1){
                   this.groupType = this.groupDown;
                   //  // 默认为：每日新增告警数
                   // this.editComponentForm['alarmStatisticsType'] = 1
                    // 重置选择拓扑下拉框
                   this.$refs['addComponentForm'].fields.forEach(function (e) {
                     if (e.prop === 'groupId') {
                       e.resetField();
                     }
                   });

                } else {
                    this.rulesValidate.topoId = [];
                }
                // else if (val.search('route_statistic') !== -1) {
                //     console.log(123)
                // }
            }
        },
        edittypeChange(val, type) {
            // debugger;
            console.log(val, type);

            if (val.search("route_statistic") !== -1) {
                // 当选择统计类型时，初始化 items
                this.addComponentForm.dashboardComponentTaskGroups = [
                    {
                        groupId: "",
                        dashboardViewId: "",
                    },
                ];
            }
            this.editComponentForm.alarmListNum = "3";
            this.editComponentForm.alarmListNum1 = "10";
            if (this.editComponentForm["typeCode"].search("physics_topo") != -1) {
                this.editComponentForm["topoId"] = this.physicstopoid;
            }
            if (
                this.editComponentForm["typeCode"].search("route_topo") != -1 ||
                this.editComponentForm["typeCode"].search("fiber_chart")
            ) {
                this.editComponentForm["topoId"] = this.routetopoid;
            }
            if (val.search("real_time_alarm") !== -1) {
                this.editRulesValidate.provinceCode = [];
                this.editRulesValidate.provinceMunicipalityCode = [];
                this.$set(this.editRulesValidate["alarmListNum"], 0, {
                    required: true,
                    type: "string",
                    name: this.$t("comm_alarms_number"),
                    trigger: "blur",
                    validator: validaalarmNum,
                });
                this.editRulesValidate.alarmListNum = [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("comm_alarms_number"),
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ];
                this.$set(this.editRulesValidate["alarmListNum1"], 0, {
                    required: true,
                    type: "string",
                    name: this.$t("comm_alarms_number"),
                    trigger: "blur",
                    validator: validaalarmNum,
                });
                this.editRulesValidate.alarmListNum1 = [
                    {
                        required: true,
                        type: "string",
                        name: this.$t("comm_alarms_number"),
                        trigger: "blur",
                        validator: validaalarmNum,
                    },
                ];
                this.groupType = this.groupDown;
            } else if (val.search("timely_map") !== -1) {
                this.editRulesValidate.alarmListNum = [];
                this.editRulesValidate.alarmListNum1 = [];
                this.editRulesValidate.provinceCode = [
                    {
                        required: true,
                        type: "number",
                        name: this.$t("dashboard_region"),
                        trigger: "change",
                        validator: validaProvince,
                    },
                ];
                this.editRulesValidate.provinceMunicipalityCode = [
                    {
                        required: true,
                        type: "number",
                        name: this.$t("dashboard_region"),
                        trigger: "change",
                        validator: validaProvince,
                    },
                ];
            } else if(val.search('route_alarm_trend') !==-1 || val.search('special_alarm_trend') !==-1 || val.search('snmp_alarm_trend') !==-1){
                this.groupType = this.groupDown;
                // // 默认为：每日新增告警数
                // this.editComponentForm['alarmStatisticsType'] = 1
            } else {
                this.editRulesValidate.alarmListNum = [];
                this.editRulesValidate.alarmListNum1 = [];
                this.editRulesValidate.provinceCode = [];
                this.editRulesValidate.provinceMunicipalityCode = [];
                this.editRulesValidate.url = [];
                if (
                    val.search("route_topo") !== -1 ||
                    val.search("fiber_chart") !== -1
                ) {
                    this.editRulesValidate.topoId = [
                        {
                            required: true,
                            // type: "string",
                            message: this.$t("comm_select_topology_type"),
                            name: this.$t("comm_opology_type"),
                            trigger: "blur",
                        },
                    ];
                    this.topoType = this.pathDown;
                    // 重新渲染数据
                    this.pathTopoTypeSelectChange(this.editComponentForm.pathtopoType);
                } else if (val.search("url_link") !== -1) {
                    // URL 链接

                    this.editRulesValidate.url = [
                        {
                            required: true,
                            name: this.$t("dashboard_url"),
                            type: "string",
                            Length: 200,
                            validator: validaUrl,
                            trigger: "blur",
                        },
                    ];
                } else if (val.search("physics_topo") !== -1) {
                    this.editRulesValidate.topoId = [
                        {
                            required: true,
                            // type: "string",
                            message: this.$t("comm_select_topology_type"),
                            name: this.$t("comm_opology_type"),
                            trigger: "blur",
                        },
                    ];
                    this.topoType = this.physDown;
                } else {
                    this.editRulesValidate.topoId = [];
                }
            }
            if (
                this.editComponentForm["typeCode"].search(
                    "monitor_target_spread_map"
                ) != -1
            ) {
                this.editComponentForm.iconScalingRatio =
                    this.editComponentForm.iconScalingRatio == "" ||
                        this.editComponentForm.iconScalingRatio == null
                        ? "100"
                        : this.editComponentForm.iconScalingRatio;
                this.editComponentForm.backgroundImageId = "";
                this.objRequiredEdit = false;
            }
        },
        //获取当前视图的已配置构件列表
        getComponentList() {
            return new Promise((resolve, reject) => {
                this.$http
                    .post("/dashboard/component/list", { viewId: this.viewId })
                    .then((res) => {
                        if (res.code === 1) {
                            res.data.map((item) => {
                                item.nameAll = item.name;
                                item.name = item.shortName || item.name;
                            });
                        }
                        resolve(res);
                    })
                    .catch(reject);
            });
        },
        getAggregationList(item) {
            return new Promise((resolve, reject) => {
                this.$http
                    .post("/dashboard/aggregation/list", { componentId: item.id })
                    .then((data) => {
                        resolve(data);
                    })
                    .catch(reject);
            });
        },
        // 控制选10个后不可选
        slectChange(value) {
            this.gisListSub.forEach((obj) => {
                if (value.indexOf(obj.code) >= 0) {
                    obj.isDisabled = false;
                } else {
                    obj.isDisabled = true;
                }
            });

            if (value.length === 10) {
                this.totalDisabled = true;
            } else {
                this.totalDisabled = false;
            }
        },
        //返回主页面
        goBack() {
            this.$router.push({ path: "/dashboard" });
        },
        //设置格子属性width，height
        setGridProperty(ratio, row, column) {
            const ratioStr = ratio,
                ratioRow = ratioStr.split(":")[0],
                ratioColumn = ratioStr.split(":")[1];
            this.grid.width = this.tableW / column;
            this.tableH = (this.tableW * ratioColumn) / ratioRow;
            this.grid.height = this.tableH / row;
        },
        // 是否显示 30天告警统计维度 下拉
        getShowAlarmStatisticsTypeComponent(value){
          return value === this.checkedRouteAlarmTreadType ||  value === this.checkedSpecialAlarmTreadType ||  value === this.checkedSnmpAlarmTreadType;
        },
        // 是否显示 分组下拉选择控制
        getShowGroupComponent(value){
          return value === this.checkedRouteAlarmTreadType ||  value === this.checkedSpecialAlarmTreadType ||  value === this.checkedSnmpAlarmTreadType;
        },
        //获取构件类型
        getComponentTypeAarray() {
            this.$http
                .post("/dashboard/component/typeList")
                .then(({ code, data, msg }) => {
                    console.log("");
                    console.log(data);
                    if (code === 1) {
                        if (data && data instanceof Array) {
                            this.componentTypeAarray = data.map((item, index) => {
                                if (item.code === "real_time_alarm") {
                                    this.checkedType = item.code + "?pageUrl=" + item.url;
                                }
                                if (item.code === "route_topo") {
                                    this.checkedrouteTopo = item.code + "?pageUrl=" + item.url;
                                }
                                if (item.code === "fiber_chart") {
                                    this.checkedFiberChart = item.code + "?pageUrl=" + item.url;
                                }
                                if (item.code === "physics_topo") {
                                    this.checkedphysTopo = item.code + "?pageUrl=" + item.url;
                                }
                                if (item.code === "url_link") {
                                    this.checkedUrlLink = item.code + "?pageUrl=" + item.url;
                                }
                                if (item.code === "route_statistic") {
                                    this.checkedStatistic = item.code + "?pageUrl=" + item.url;
                                }

                                // 最近30天拨测新增告警走势
                                if (item.code === 'route_alarm_trend') {
                                this.checkedRouteAlarmTreadType = item.code + '?pageUrl=' + item.url;
                                }
                                // 最近30天专线新增告警走势
                                if (item.code === 'special_alarm_trend') {
                                this.checkedSpecialAlarmTreadType = item.code + '?pageUrl=' + item.url;
                                }
                                // 最近30天中继新增告警走势
                                if (item.code === 'snmp_alarm_trend') {
                                this.checkedSnmpAlarmTreadType = item.code + '?pageUrl=' + item.url;
                                }


                                return {
                                    typeCode: item.code,
                                    name: item.name,
                                    key: index,
                                    pageUrl: item.url,
                                };
                            });
                            console.log(this.componentTypeAarray);
                            if (this.$route.query.type == 1) {
                                this.componentTypeAarray = this.componentTypeAarray.filter(
                                    (item) =>
                                        item.typeCode == "aggre_gation" ||
                                        "batch_aggre_gation" == item.typeCode
                                );
                            }
                        }
                    }
                });
        },
        pathTopoTypeSelectChange(value) {
            console.log(value, "----------------------");
            var list = this.pathDownDataList;
            if (value == 2) {
                list = list.filter((item) => {
                    return item.managemode != 2;
                });
            }
            this.topoType = this.pathDown = list.map((item) => {
                return { topoId: String(item.id), name: item.topologyName };
            });
        },
        //获取路径下拉
        getPathDown() {
            this.$http
                .post("/pathTopo/getPathTopoPulldown")
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        if (data && data.list instanceof Array) {
                            this.pathDownDataList = data.list;
                            this.pathDown = data.list.map((item) => {
                                return { topoId: String(item.id), name: item.topologyName };
                            });
                        }
                    }
                });
        },
        //获取物理下拉
        getPhysDown() {
            this.$http.post("/physTopo/getTopoList").then(({ code, data, msg }) => {
                if (code === 1) {
                    if (data && data.list instanceof Array) {
                        this.physDown = data.list.map((item) => {
                            return { topoId: String(item.id), name: item.topologyName };
                        });
                    }
                }
            });
        },
        //获取分组下拉
        getGroupDown() {
            this.$http
                .post("/group/list", { pageNo: 1, pageSize: 10000 })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        if (data.records && data.records instanceof Array) {
                            this.groupDown = data.records.map((item) => {
                                return { groupId: String(item.id), name: item.name };
                            });
                            this.groupList = data.records;
                            console.log('8989898989898989898989898')
                            console.log(this.groupDown);
                        }
                    }
                });
        },
        //添加构件
        addComponent() {
            this.isEditComponent = false;
            this.objectType = "";
            this.addComponentForm.objectType = "";
            this.addComponentForm.typeCode = "";
            this.addComponentForm.name = "";
            this.addComponentForm.intervalTime = 60;
            this.addComponentForm.crossRow = "";
            this.addComponentForm.crossCol = "";
            this.addComponentForm.topoId = "";
            this.addComponentForm.groupId = "";
            // 默认为： 1-每日新增告警数
            this.addComponentForm.alarmStatisticsType = 1;
            this.addComponentForm.alarmListNum = "3";
            this.addComponentForm.alarmListNum1 = "10";
            this.addComponentForm.provinceCode = 100000;
            this.addComponentForm.provinceMunicipalityCode = [];
            this.addComponentForm.graphicList = [];
            this.addComponentForm.url = "";
            this.addComponentForm.portId = "";
            this.addComponentForm.noShowName = 0;
            this.addComponentForm.noShowFrame = 0;
            this.addComponent.componentRealAlarmConfigs= [
                {
                    alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                    alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                    alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                    alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                    alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                    maintainLevelOne: 0, //运维等级
                    maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                    maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                    showRecentlyAlarmNum: 0, //显示最新告警条数
                    showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                    type: 1 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                },
                {
                    alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                    alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                    alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                    alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                    alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                    maintainLevelOne: 0, //运维等级
                    maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                    maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                    showRecentlyAlarmNum: 0, //显示最新告警条数
                    showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                    type: 0 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                }
            ];
            this.TagDatas = [];
            this.targetTableDatas = [];
            this.targetsTableDatas = [];
            this.modalTitle = this.$t("view_add_title");
            this.modalShow = true;
            this.monitorEditState = false;
        },
        addVisibleChange(val) {

            if (!val) {
                this.cancleClick();
            } else {
                this.objSelectedData = [];
                this.objRequiredEdit = false;
            }
        },
        //新建取消
        cancleClick() {
            this.$refs["addComponentForm"].resetFields();
            this.modalShow = false;
            // this.addComponentForm.objectType = ''
            this.TagDatas = [];
            this.targetsTableDatas = [];
            this.objSelectedData = [];
            this.objRequiredEdit = false;
            this.topoInfos = [];

            this.addComponentForm = {
                typeCode: "",
                name: "",
                intervalTime: null,
                crossRow: "",
                crossCol: "",
                pathtopoType: 1,
                alarmListNum: "",
                alarmListNum1: "",
                topoId: "",
                provinceCode: "",
                provinceMunicipalityCode: [],
                graphicType: 2,
                objType: "", // 对象类型
                objectType: "", // 对象类型
                graphicList: [],
                // 30天告警统计维度
                alarmStatisticsType:1,
                groupId: [],
                // URL连接地址
                url: "",
                taskPresentationType: "",
                dashboardComponentTaskGroups: [
                    {
                        groupId: "", // 分组
                        dashboardViewId: "", // 链接
                    },
                ],
                componentRealAlarmConfigs: [
                    {
                        alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                        alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                        alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                        alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                        alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                        maintainLevelOne: 0, //运维等级
                        maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                        maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                        showRecentlyAlarmNum: 0, //显示最新告警条数
                        showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                        type: 1 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                    },
                    {
                        alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                        alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                        alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                        alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                        alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                        maintainLevelOne: 0, //运维等级
                        maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                        maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                        showRecentlyAlarmNum: 0, //显示最新告警条数
                        showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                        type: 0 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                    }
                ],
                backgroundImageId: "",
                // 实时监控分布对象
                taskIds: [],
                taskIdsStr: "",
                iconScalingRatio: "100",
            };
        },
        cancleIndicatotClick() {
            this.indicatorModal.show = false;
            this.indicatorModalForm["objectType"] = null;
            this.indicatorModalForm["objectId"] = null;
            this.indicatorModalForm["indicatorType"] = null;
            this.indicatorModalForm["portId"] = null;
        },
        //编辑取消
        editCancleClick() {
            this.$refs["editComponentForm"].resetFields();
            this.editShow = false;
            this.editComponentForm.objectType = "";
            this.TagDatas = [];
            this.targetsTableDatas = [];
            this.objSelectedData = [];
        },
        //新建确认
        submitClick() {
            // 特殊处理 dashboardComponentTaskGroups 中groupId 消失
            const formData = JSON.parse(JSON.stringify(this.addComponentForm));
            console.log(formData, "选中的构件0");
            // debugger
            console.log(this.addComponentForm, "选中的构件");
            if (
                this.addComponentForm["typeCode"].search("route_topo") != -1 ||
                this.addComponentForm["typeCode"].search("physics_topo") != -1
            ) {
                this.rulesValidate.topoId = [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value || (Array.isArray(value) && value.length === 0)) {
                                callback(new Error(this.$t("comm_select_topology_type")));
                            } else {
                                callback();
                            }
                        },
                        message: this.$t("comm_select_topology_type"),
                        trigger: "change"
                    }
                ];
            } else {
                this.rulesValidate.topoId = [];
            }

            if (this.addComponentForm["typeCode"].search("route_topo") != -1) {
                this.rulesValidate.pathtopoType = [
                    {
                        required: true,
                        type: "number",
                        message: this.$t("dashboard_select_artifact_type"),
                        name: this.$t("comm_topo_style"),
                        trigger: "blur",
                    },
                ];
            } else {
                this.rulesValidate.pathtopoType = [];
            }
            if (this.addComponentForm["typeCode"].search("real_time_alarm") !== -1) {
                if(this.addComponentForm.componentRealAlarmConfigs[0].showType==0){
                        this.rulesValidate.alarmListNum = [
                            {
                                required: true,
                                type: "string",
                                name: this.$t("comm_alarms_number"),
                                trigger: "blur",
                                validator: validaalarmNum,
                            },
                        ];
                    }else{
                        this.$delete(this.rulesValidate, 'alarmListNum');
                    }
                    if(this.addComponentForm.componentRealAlarmConfigs[1].showType==0){
                        this.rulesValidate.alarmListNum1 = [
                            {
                                required: true,
                                type: "string",
                                name: this.$t("comm_alarms_number"),
                                trigger: "blur",
                                validator: validaalarmNum,
                            },
                        ];
                    }else{
                        this.$delete(this.rulesValidate, 'alarmListNum1');
                    }
               // 判断是否存在 存在的话给告警条数赋值
               if (this.addComponentForm.componentRealAlarmConfigs && this.addComponentForm.componentRealAlarmConfigs.length > 0) {
                    this.addComponentForm.componentRealAlarmConfigs[0].showRecentlyAlarmNum = this.addComponentForm.alarmListNum;
                    this.addComponentForm.componentRealAlarmConfigs[1].showRecentlyAlarmNum = this.addComponentForm.alarmListNum1;
                }
            } else {
                this.rulesValidate.alarmListNum = [];
                this.rulesValidate.alarmListNum1 = [];
                this.rulesValidate.groupId = [];
                // 排除
                 if(
                  this.addComponentForm['typeCode'].search('route_alarm_trend') !== -1 ||
                  this.addComponentForm['typeCode'].search('special_alarm_trend') !== -1 ||
                  this.addComponentForm['typeCode'].search('snmp_alarm_trend') !== -1 ){

                   this.rulesValidate.groupId = this.editRulesValidate.groupId;
                  }else{
                    this.rulesValidate.groupId = [];
                 }
            }
            if (this.addComponentForm["typeCode"].search("aggre_gation") !== -1) {
                if (
                    this.targetTableDatas.length < 1 &&
                    this.targetsTableDatas.length < 1
                ) {
                    this.$Message.warning({
                        content: this.$t("dashboard_add_graphics_metrics_error_tip"),
                        background: true,
                    });
                    return;
                }
                var existsFlag = this.validationMetricsRepeat();
                if (!existsFlag) {
                    return;
                }
            }

            if (
                this.addComponentForm["typeCode"].search("real_time_monitor") !== -1 &&
                this.TagDatas.length < 1
            ) {
                this.$Message.warning({
                    content: this.$t("dashboard_added_monitoring"),
                    background: true,
                });
                return;
            }
            if (
                this.addComponentForm["typeCode"].search("real_time_monitor") !== -1 &&
                this.TagDatas.length > 300
            ) {
                this.$Message.warning({
                    content: this.$t("dashboard_maximum_selected_300"),
                    background: true,
                });
                return;
            }

            if (this.viewList.length > 30) {
                this.$Message.warning(this.$t("view_max_comp_30"));
                return;
            }

            if (
                this.addComponentForm["intervalTime"] == null ||
                this.addComponentForm["intervalTime"] == undefined
            ) {
                this.$Message.warning({
                    content: this.$t("dashboard_selec_refresh_interval"),
                    background: true,
                });
                return;
            } else {
                var intervalTime = this.addComponentForm["intervalTime"];
                var intervalTimeArrays = this.componentRefreshAarray.filter((item) => {
                    return item.refreshTime == intervalTime;
                });
                if (intervalTimeArrays.length <= 0) {
                    this.$Message.warning({
                        content: this.$t("dashboard_selec_refresh_interval"),
                        background: true,
                    });
                    return;
                }
            }
            // 校验多选省
            if (
                this.addComponentForm["typeCode"].search("timely_map") != -1 &&
                this.addComponentForm.provinceCode == 100001
            ) {
                this.$refs["addComponentForm"].validateField(
                    "provinceMunicipalityCode"
                );
            } else {
                this.rulesValidate.provinceMunicipalityCode = [];
            }

            // 检查 URL 地址输入是否正确
            if (this.addComponentForm["typeCode"].search("url_link") != -1) {
                this.rulesValidate.url = [
                    {
                        required: true,
                        name: this.$t("dashboard_url"),
                        type: "string",
                        Length: 200,
                        validator: validaUrl,
                        trigger: "blur",
                    },
                ];
            } else {
                this.rulesValidate.url = [];
            }
            console.log(this.rulesValidate, "this.rulesValidate");
            console.log(this.addComponentForm, "选中的构件1");

            this.$refs["addComponentForm"].validate((valid) => {
                //验证实时监控目标分布选择目标
                if (
                    this.addComponentForm.typeCode.search("monitor_target_spread_map") !=
                    -1
                ) {
                    if (
                        this.addComponentForm.taskIds.length === 0 ||
                        this.addComponentForm.taskIds.length > 2000
                    ) {
                        this.objRequiredEdit = true;
                        // this.objSelectedData = [];
                        return;
                    }
                }
                if(this.addComponentForm.componentRealAlarmConfigs[0].showType==1 && this.addComponentForm.componentRealAlarmConfigs[1].showType==1){
                    this.$Message.warning({
                        content:"横向滚动消息栏和告警列表至少显示一个!",
                        background: true,
                    });
                    return;
                }
                console.log(valid, "校验");
                if (valid) {
                    console.log("校验通过", this.addComponentForm.topoId, this.topoType);
                    if (this.addComponentForm["typeCode"].search("route_topo") != -1 && this.addComponentForm.topoId.length > 0) {
                        this.addComponentForm.topoId.forEach(item => {
                            this.topoType.forEach(item2 => {
                                if (item == item2.topoId) {
                                    this.topoInfos.push({
                                        id: item2.topoId,
                                        name: item2.name,
                                    })
                                }
                            })
                        })
                        console.log(this.topoInfos, "this.topoInfos");
                    }
                    let submitParam = Object.assign(this.addComponentForm, {});
                    console.log('submitParamsubmitParamsubmitParamsubmitParam');
                    console.log(submitParam);
                    if (Array.isArray(submitParam.topoId)) {
                        submitParam.topoId = submitParam.topoId.join(',');
                    }

                    let crossRowArr = submitParam.crossRow.split("-"),
                        crossColArr = submitParam.crossCol.split("-");
                    submitParam.startRow = Number(crossRowArr[0]);
                    submitParam.endRow = Number(crossRowArr[crossRowArr.length - 1]); // 每行的格子数
                    submitParam.startCol = Number(crossColArr[0]);
                    submitParam.endCol = Number(crossColArr[crossColArr.length - 1]); // 每列的格子数
                    let spaceXNum = submitParam.endCol - submitParam.startCol + 1; // 分配的横向格子数
                    let spaceYNum = submitParam.endRow - submitParam.startRow + 1; // 分配的竖向格子数
                    let totalNum = spaceYNum * spaceXNum; // 分配区域内的总格子数

                    // 批量集合图形要判断数据记录条数
                    if (submitParam.typeCode.split("?pageUrl=")[0] == "batch_aggre_gation") {
                        // 所选对象数量，超过分配的空格数，从尾部删除多余的对象
                        if (this.TagDatas.length > totalNum) {
                            this.TagDatas = this.TagDatas.slice(0, totalNum);
                        }
                    }

                    let totalObjNum = this.TagDatas.length; // 所选的对象数量
                    //判断是否可以在该位置配置构件
                    const isPas = this.isBeyondScope(
                        submitParam.startRow,
                        submitParam.endRow,
                        submitParam.startCol,
                        submitParam.endCol,
                        submitParam.key
                    );
                    if (isPas) {
                        if (submitParam.typeCode.split("?pageUrl=")[0] != "batch_aggre_gation") {
                            let addData = {
                                name: submitParam.name,
                                nameAll: submitParam.name,
                                typeCode: submitParam.typeCode.split("?pageUrl=")[0],
                                pageUrl: submitParam.typeCode.split("?pageUrl=")[1],
                                crossRow: submitParam.crossRow,
                                crossCol: submitParam.crossCol,
                                intervalTime: submitParam.intervalTime,
                                // alarmListNum:
                                //     submitParam.typeCode.split("?pageUrl=")[0] ==
                                //         "real_time_alarm"
                                //         ? submitParam.alarmListNum
                                //         : null,
                                topoId:
                                    submitParam.typeCode.split("?pageUrl=")[0] == "route_topo" ||
                                        submitParam.typeCode.split("?pageUrl=")[0] ==
                                        "physics_topo" ||
                                        submitParam.typeCode.split("?pageUrl=")[0] == "fiber_chart"
                                        ? submitParam.topoId
                                        : null,
                                topoInfos: this.topoInfos,
                                key: md5(
                                    new Date().getTime() + "t" + Math.random() * 1024 + "r"
                                ),
                                startRow: submitParam.startRow,
                                endRow: submitParam.endRow,
                                startCol: submitParam.startCol,
                                endCol: submitParam.endCol,
                                editType: "add",
                                graphicType:
                                    submitParam.typeCode.split("?pageUrl=")[0] ==
                                        "aggre_gation" ||
                                        submitParam.typeCode.split("?pageUrl=")[0] ==
                                        "batch_aggre_gation"
                                        ? submitParam.graphicType
                                        : null,
                                graphicList:
                                    submitParam.typeCode.split("?pageUrl=")[0] ==
                                        "aggre_gation" ||
                                        submitParam.typeCode.split("?pageUrl=")[0] ==
                                        "batch_aggre_gation"
                                        ? this.targetTableDatas
                                        : [],
                                // 实时监控对象
                                monitorObjList:
                                    submitParam.typeCode.split("?pageUrl=")[0] ==
                                        "real_time_monitor"
                                        ? this.TagDatas
                                        : [],
                                provinceCode: submitParam.provinceCode || 100000,
                                provinceMunicipalityCode: submitParam.provinceMunicipalityCode
                                    ? submitParam.provinceMunicipalityCode.join()
                                    : "",
                                pathtopoType: submitParam.pathtopoType,
                                objType: submitParam.objType,
                                groupIds: submitParam.groupId
                                    ? submitParam.groupId.join(",")
                                    : "",
                                alarmStatisticsType: submitParam.alarmStatisticsType ? submitParam.alarmStatisticsType : "",
                                url: submitParam.url || "",
                                portId: submitParam.portId || "",
                                noShowName: submitParam.noShowName || 0,
                                noShowFrame: submitParam.noShowFrame || 0,

                                backgroundImageId: submitParam.backgroundImageId,
                                iconScalingRatio: submitParam.iconScalingRatio,
                                taskIds: submitParam.taskIds,
                                monitorTargetSpreadTaskInfos: this.objSelectedData,
                                taskPresentationType: formData.taskPresentationType,
                                dashboardComponentTaskGroups: formData.dashboardComponentTaskGroups,
                                componentRealAlarmConfigs: submitParam.componentRealAlarmConfigs

                            };
                            console.log(addData, "addData");
                            this.addComponentList.push(JSON.parse(JSON.stringify(addData)));
                            this.viewList.push(JSON.parse(JSON.stringify(addData)));
                            this.handleTableProp();
                            this.modalShow = false;
                        } else {
                            // 批量新增聚合图形 逻辑代码
                            let cNum = 0; //勾选对象可以分配的列数
                            let startRow = submitParam.startRow - 1;

                            let rNum = Math.ceil(Number(totalObjNum / spaceXNum)); //勾选对象可以分配的行数
                            totalObjNum <= submitParam.endRow
                                ? (cNum = totalObjNum)
                                : (cNum = submitParam.endCol);
                            // 行循环
                            for (let r = 1; r <= Number(submitParam.endRow); r++) {
                                startRow++;
                                if (r == rNum) {
                                    if (totalObjNum <= totalNum) {
                                        cNum =
                                            totalObjNum % spaceXNum == 0
                                                ? spaceXNum
                                                : totalObjNum % spaceXNum; // 最后一行可以分配的列数
                                    } else {
                                        cNum = spaceXNum;
                                    }
                                }
                                if (r > rNum) {
                                    break;
                                }
                                let startCol = submitParam.startCol;
                                // 列循环
                                for (let c = 1; c <= Number(submitParam.endCol); c++) {
                                    if (c > cNum) {
                                        break;
                                    }

                                    if (startCol > Number(submitParam.endCol)) {
                                        break;
                                    }
                                    this.targetTableDatas = [];
                                    // 批量添加聚合图形数据
                                    this.targetsTableDatas.forEach((item) => {
                                        let indicatorType = "";
                                        let subData = null;
                                        for (const key in this.typesList) {
                                            if (this.typesList[key] == item.name) {
                                                indicatorType = key;
                                            }
                                        }
                                        subData = { ...this.TagDatas[(r - 1) * spaceXNum + c - 1] };
                                        this.targetTableDatas.push({
                                            id: subData.id,
                                            objectId: subData.id,
                                            name: subData.name,
                                            nameAll: subData.name,
                                            componentId: subData.componentId,
                                            objectType: this.objectType,
                                            aip: subData.aip,
                                            zip: subData.zip,
                                            indicatorType: indicatorType,
                                            color: item.color,
                                            portId: item.portId,
                                            noShowName: subData.noShowName || 0,
                                            noShowFrame: subData.noShowFrame || 0,
                                        });
                                    });

                                    let obj = {
                                        name: submitParam.name,
                                        nameAll: submitParam.name,
                                        typeCode: submitParam.typeCode.split("?pageUrl=")[0],
                                        pageUrl: submitParam.typeCode.split("?pageUrl=")[1],
                                        crossRow: `${startRow}-${startRow}`,
                                        crossCol: `${startCol}-${startCol}`,
                                        intervalTime: submitParam.intervalTime,
                                        // alarmListNum:
                                        //     submitParam.typeCode.split("?pageUrl=")[0] ==
                                        //         "real_time_alarm"
                                        //         ? submitParam.alarmListNum
                                        //         : null,
                                        topoId:
                                            submitParam.typeCode.split("?pageUrl=")[0] ==
                                                "route_topo" ||
                                                submitParam.typeCode.split("?pageUrl=")[0] ==
                                                "physics_topo" ||
                                                submitParam.typeCode.split("?pageUrl=")[0] ==
                                                "fiber_chart"
                                                ? submitParam.topoId
                                                : null,
                                        key: md5(
                                            new Date().getTime() + "t" + Math.random() * 1024 + "r"
                                        ),
                                        startRow: `${startRow}`,
                                        endRow: `${startRow}`,
                                        startCol: `${startCol}`,
                                        endCol: `${startCol}`,
                                        editType: "add",
                                        graphicType: submitParam.graphicType,
                                        graphicList: this.targetTableDatas,
                                        url: submitParam.url || "",
                                        portId: submitParam.portId || "",
                                        alarmStatisticsType: submitParam.alarmStatisticsType || 1,
                                        noShowName: submitParam.noShowName || 0,
                                        noShowFrame: submitParam.noShowFrame || 0,
                                        taskPresentationType: formData.taskPresentationType,
                                        dashboardComponentTaskGroups: formData.dashboardComponentTaskGroups,
                                        componentRealAlarmConfigs: submitParam.componentRealAlarmConfigs
                                    };
                                    startCol++;
                                    this.addComponentList.push(obj);
                                    this.viewList.push(obj);
                                    this.handleTableProp();
                                    this.modalShow = false;
                                }
                            }
                        }
                    } else {
                        this.$Message.warning({
                            content: this.$t("dashboard_not_same_config"),
                            background: true,
                        });
                        return;
                    }
                }
            });
        },
        // 验证指标重复
        validationMetricsRepeat() {
            var arrays = [],
                existsFlag = 0,
                obj = {},
                targetTableDatas = this.targetTableDatas;
            for (var i = 0; i < targetTableDatas.length; i++) {
                var key = [
                    targetTableDatas[i].objectType,
                    targetTableDatas[i].objectId,
                    targetTableDatas[i].indicatorType,
                ].join("_");
                if (arrays.indexOf(key) == -1) {
                    arrays.push(key);
                    continue;
                } else {
                    existsFlag = 1;
                    obj = targetTableDatas[i];
                    break;
                }
            }

            if (existsFlag == 1) {
                // objectType  对象类型(1.专线;2. 中继;3.拨测;4 设备端口信息)
                let typeJson = this.getTypes(obj.objectType);
                this.$Message.warning({
                    content:
                        this.$t("dashboard_add_indicator_error_tip") +
                        "【" +
                        typeJson[obj.indicatorType] +
                        "】",
                    background: true,
                });
                return false;
            }
            return true;
        },
        //编辑确认
        editSubmitClick() {
            // debugger;
            let _that = this;
            console.log(this.editComponentForm);
            console.log(this.objSelectedData, "表格数据");
            // debugger
            if (
                this.editComponentForm["typeCode"].search("route_topo") != -1 ||
                this.editComponentForm["typeCode"].search("physics_topo") != -1
            ) {
                // 拓扑类型
                this.editRulesValidate.topoId = [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value || (Array.isArray(value) && value.length === 0)) {
                                callback(new Error(this.$t("comm_select_topology_type")));
                            } else {
                                callback();
                            }
                        },
                        message: this.$t("comm_select_topology_type"),
                        trigger: "change"
                    }
                ];
                console.log("拓扑类型topoId");
            } else {
                this.editRulesValidate.topoId = [];
            }
            if (this.editComponentForm["typeCode"].search("real_time_alarm") !== -1) {
                // 显示告警条数
                console.log("显示告警条数 alarmListNum");
              debugger
             
                // 判断是否存在 存在的话给告警条数赋值
                if (this.editComponentForm.componentRealAlarmConfigs && this.editComponentForm.componentRealAlarmConfigs.length > 0) {
                    if(this.editComponentForm.componentRealAlarmConfigs[0].showType==0){
                        this.editRulesValidate.alarmListNum = [
                            {
                                required: true,
                                type: "string",
                                name: this.$t("comm_alarms_number"),
                                trigger: "blur",
                                validator: validaalarmNum,
                            },
                        ];
                    }else{
                        this.$delete(this.editRulesValidate, 'alarmListNum');
                    }
                    
                    if(this.editComponentForm.componentRealAlarmConfigs[1].showType==0){
                        this.editRulesValidate.alarmListNum1 = [
                            {
                                required: true,
                                type: "string",
                                name: this.$t("comm_alarms_number"),
                                trigger: "blur",
                                validator: validaalarmNum,
                            },
                        ];
                    }else{
                        this.$delete(this.editRulesValidate, 'alarmListNum1');
                    }
                    this.editComponentForm.componentRealAlarmConfigs[0].showRecentlyAlarmNum = this.editComponentForm.alarmListNum;
                    this.editComponentForm.componentRealAlarmConfigs[1].showRecentlyAlarmNum = this.editComponentForm.alarmListNum1;
                }
            } else {
                this.editRulesValidate.alarmListNum = [];
                this.editRulesValidate.alarmListNum1 = [];
                this.editRulesValidate.groupId = [];

                // 排除
                if(
                    this.editComponentForm['typeCode'].search('route_alarm_trend') !== -1 ||
                    this.editComponentForm['typeCode'].search('special_alarm_trend') !== -1 ||
                    this.editComponentForm['typeCode'].search('snmp_alarm_trend') !== -1 ){
                        this.editRulesValidate.groupId = this.rulesValidate.groupId;
                }else{
                    this.editRulesValidate.groupId = [];
                }

            }
            if (this.editComponentForm["typeCode"].search("route_topo") !== -1) {
                // 工件类型
                console.log("工件类型pathtopoType");
                this.editRulesValidate.pathtopoType = [
                    {
                        required: true,
                        type: "number",
                        message: this.$t("dashboard_select_artifact_type"),
                        name: this.$t("comm_topo_style"),
                        trigger: "blur",
                    },
                ];
            } else {
                this.editRulesValidate.pathtopoType = [];
            }
            if (this.editComponentForm["typeCode"].search("aggre_gation") !== -1) {
                if (
                    this.targetTableDatas.length < 1 &&
                    this.targetsTableDatas.length < 1
                ) {
                    this.$Message.warning({
                        content: this.$t("dashboard_add_graphics_metrics_error_tip"),
                        background: true,
                    });
                    return;
                } else {
                    var existsFlag = this.validationMetricsRepeat();
                    if (!existsFlag) {
                        return;
                    }
                }
            }

            if (
                this.editComponentForm["typeCode"].search("real_time_monitor") !== -1 &&
                this.TagDatas.length < 1
            ) {
                this.$Message.warning({
                    content: this.$t("dashboard_added_monitoring"),
                    background: true,
                });
                return;
            }
            if (
                this.editComponentForm["typeCode"].search("real_time_monitor") !== -1 &&
                this.TagDatas.length > 300
            ) {
                this.$Message.warning({
                    content: this.$t("dashboard_maximum_selected_300"),
                    background: true,
                });
                return;
            }
            if (
                this.addComponentForm["typeCode"].search("timely_map") != -1 &&
                this.addComponentForm.provinceCode == 100001
            ) {
                console.log(1111);
                this.$refs["editComponentForm"].validateField(
                    "provinceMunicipalityCode"
                );
            } else {
                this.editRulesValidate.provinceMunicipalityCode = [];
            }

            // 检查 URL 地址输入是否正确
            if (this.editComponentForm["typeCode"].search("url_link") != -1) {
                this.editRulesValidate.url = [
                    {
                        required: true,
                        name: this.$t("dashboard_url"),
                        type: "string",
                        Length: 200,
                        validator: validaUrl,
                        trigger: "blur",
                    },
                ];
            } else {
                this.editRulesValidate.url = [];
            }
            if(this.editComponentForm.componentRealAlarmConfigs[0].showType==1 && this.editComponentForm.componentRealAlarmConfigs[1].showType==1){
                this.$Message.warning({
                    content:"横向滚动消息栏和告警列表至少显示一个!",
                    background: true,
                });
                return;
            }
            this.$refs["editComponentForm"].validate((valid) => {
                //验证实时监控目标分布选择目标
                if (
                    this.editComponentForm.typeCode.search("monitor_target_spread_map") !=
                    -1
                ) {
                    console.log(
                        this.editComponentForm,
                        this.editComponentForm.taskIds,
                        "---------------------"
                    );
                    if (
                        !this.editComponentForm.taskIds ||
                        this.editComponentForm.taskIds.length === 0 ||
                        this.editComponentForm.taskIds.length > 2000
                    ) {
                        this.objRequiredEdit = true;
                        // this.objSelectedData = [];
                        return;
                    }
                }
                console.log(valid);
                debugger
                if (valid) {
                    if (this.editComponentForm["typeCode"].search("route_topo") != -1 && this.editComponentForm.topoId.length > 0) {
                        // 不知道什么原因变成了字符串，这里强行改为数组
                        // this.editComponentForm.topoId = "string" ? this.editComponentForm.topoId.split(',') : []
                        // debugger;
                        this.topoInfos = [];
                        console.log(this.editComponentForm.topoId);
                        
                        this.editComponentForm.topoId.forEach(item => {
                            this.topoType.forEach(item2 => {
                                if (item == item2.topoId) {
                                    this.topoInfos.push({
                                        id: item2.topoId,
                                        name: item2.name,
                                    })
                                }
                            })
                        })
                        console.log(this.topoInfos, "this.topoInfos");
                    }
                    // 批量添加聚合图形数据
                    this.targetsTableDatas.forEach((item) => {
                        let indicatorType = "";
                        for (const key in this.typesList) {
                            if (this.typesList[key] == item.name) {
                                indicatorType = key;
                            }
                        }
                        this.TagDatas.forEach((sub) => {
                            this.targetTableDatas.push({
                                id: sub.id,
                                objectId: sub.objectId,
                                name: sub.name,
                                componentId: sub.componentId,
                                objectType: this.objectType,
                                aip: sub.aip,
                                zip: sub.zip,
                                indicatorType: indicatorType,
                                color: item.color,
                                portId: item.portId,
                            });
                        });
                    });

                    //获取编辑的数据
                    let submitParam = JSON.parse(JSON.stringify(_that.editComponentForm));
                    console.log(submitParam, "submitParam+++++++++++++++");
                    let crossRowArr = submitParam.crossRow.split("-"),
                        crossColArr = submitParam.crossCol.split("-");
                    submitParam.startRow = crossRowArr[0];
                    submitParam.endRow = crossRowArr[crossRowArr.length - 1];
                    submitParam.startCol = crossColArr[0];
                    submitParam.endCol = crossColArr[crossColArr.length - 1];
                    submitParam.pageUrl = submitParam.typeCode.split("?pageUrl=")[1];
                    submitParam.typeCode = submitParam.typeCode.split("?pageUrl=")[0];
                    submitParam.graphicList =
                        submitParam.typeCode.split("?pageUrl=")[0] == "aggre_gation" ||
                            submitParam.typeCode.split("?pageUrl=")[0] == "batch_aggre_gation"
                            ? this.targetTableDatas
                            : [];
                    submitParam.monitorObjList =
                        submitParam.typeCode.split("?pageUrl=")[0] == "real_time_monitor"
                            ? this.TagDatas
                            : [];
                    submitParam.monitorTargetSpreadTaskInfos = JSON.parse(
                        JSON.stringify(this.objSelectedData)
                    );
                    submitParam.topoInfos = JSON.parse(JSON.stringify(this.topoInfos));

                    //判断是否可以在该位置配置构件
                    const isPas = this.isBeyondScope(
                        submitParam.startRow,
                        submitParam.endRow,
                        submitParam.startCol,
                        submitParam.endCol,
                        submitParam.key
                    );
                    if (isPas) {


                        // debugger
                        if (submitParam.editType && submitParam.editType === "add") {

                            console.log(_that.addComponentList, "_that.addComponentList");
                            //更新新建列表
                            _that.addComponentList.forEach((item) => {
                                if (item.key == submitParam.key) {
                                    item.name = submitParam.name;
                                    item.typeCode = submitParam.typeCode;
                                    item.pageUrl = submitParam.pageUrl;
                                    item.crossRow = submitParam.crossRow;
                                    item.crossCol = submitParam.crossCol;
                                    item.startRow = submitParam.startRow;
                                    item.endRow = submitParam.endRow;
                                    item.startCol = submitParam.startCol;
                                    item.endCol = submitParam.endCol;
                                    item.intervalTime = submitParam.intervalTime;
                                    item.pathtopoType = submitParam.pathtopoType;
                                    // item.alarmListNum =
                                    //     submitParam.typeCode == "real_time_alarm"
                                    //         ? submitParam.alarmListNum
                                    //         : null;
                                    item.topoId =
                                        submitParam.typeCode == "route_topo" ||
                                            submitParam.typeCode == "physics_topo" ||
                                            submitParam.typeCode == "fiber_chart"
                                            ? (Array.isArray(submitParam.topoId)
                                                ? submitParam.topoId.join(',')
                                                : String(submitParam.topoId))
                                            : null;
                                    item.topoInfos = JSON.parse(JSON.stringify(this.topoInfos));

                                    item.key = submitParam.key;
                                    item.editType = "add";
                                    item.graphicType =
                                        submitParam.typeCode.split("?pageUrl=")[0] ==
                                            "aggre_gation" ||
                                            submitParam.typeCode.split("?pageUrl=")[0] ==
                                            "batch_aggre_gation"
                                            ? submitParam.graphicType
                                            : null;
                                    //编辑的分组内容
                                    item.dashboardComponentTaskGroups = submitParam.dashboardComponentTaskGroups;
                                    // 编辑告警
                                    item.alarmListNum = submitParam.componentRealAlarmConfigs[0].showRecentlyAlarmNum;
                                    item.alarmListNum1 = submitParam.componentRealAlarmConfigs[1].showRecentlyAlarmNum;
                                    item.componentRealAlarmConfigs = submitParam.componentRealAlarmConfigs;
                                    item.taskPresentationType = submitParam.taskPresentationType;

                                    item.graphicList = submitParam.graphicList;
                                    item.monitorObjList = submitParam.monitorObjList;
                                    item.provinceCode = submitParam.provinceCode || 100000;
                                    item.provinceMunicipalityCode = submitParam.provinceMunicipalityCode
                                        ? submitParam.provinceMunicipalityCode.join()
                                        : "";
                                    (item.groupIds = submitParam.groupId? submitParam.groupId.join(","): ""),
                                     (item.alarmStatisticsType = submitParam.alarmStatisticsType || ""),
                                        (item.portId = submitParam.portId || ""),
                                        (item.url = submitParam.url || ""),
                                        (item.noShowName = submitParam.noShowName || 0);
                                    item.noShowFrame = submitParam.noShowFrame || 0;
                                    (item.taskIds = submitParam.taskIds),
                                        (item.iconScalingRatio = submitParam.iconScalingRatio),
                                        (item.backgroundImageId = submitParam.backgroundImageId),
                                        (item.monitorTargetSpreadTaskInfos = JSON.parse(JSON.stringify(this.objSelectedData)));
                                }
                            });
                            console.log(_that.addComponentList, "_that.addComponentList");
                            // debugger
                        } else {
                          
                            console.log(submitParam, "_that.addComponentList");
                            // debugger
                            // 更新编辑列表
                            // 添加之前处理数组参数
                            submitParam.provinceMunicipalityCode = submitParam.provinceMunicipalityCode
                                ? submitParam.provinceMunicipalityCode.join()
                                : "";

                            submitParam.groupIds = submitParam.groupId
                                ? submitParam.groupId.join(",")
                                : "";

                            submitParam.alarmStatisticsType = submitParam.alarmStatisticsType ? submitParam.alarmStatisticsType : "";

                            if (_that.editComponentList.length < 1) {
                                _that.editComponentList.push(submitParam);
                            } else {
                                let index = -1;
                                for (
                                    let i = 0, len = _that.editComponentList.length;
                                    i < len;
                                    i++
                                ) {
                                    if (_that.editComponentList[i].id === submitParam.id) {
                                        index = i;
                                        break;
                                    }
                                }
                                if (index !== -1) {
                                    _that.editComponentList[index] = submitParam;
                                } else {
                                    _that.editComponentList.push(submitParam);
                                }
                            }
                            let index2 = 0;
                            for (
                                let x = 0, len2 = _that.componentList.length;
                                x < len2;
                                x++
                            ) {
                                if (submitParam.id === _that.componentList[x].id) {
                                    index2 = x;
                                    break;
                                }
                            }
                            _that.componentList[index2] = submitParam;
                        }
                        let changeIndex = -1;
                        for (
                            let index = 0, length = _that.viewList.length;
                            index < length;
                            index++
                        ) {
                            if (submitParam.key === _that.viewList[index].key) {
                                changeIndex = index;
                            }
                        }
                        this.viewList.forEach((item, index) => {
                            if (index === changeIndex) {
                                item = submitParam;
                            }
                        });

                        _that.viewList = _that.componentList.concat(_that.addComponentList);
                        console.log(_that.viewList, "_that.viewList");
                        this.handleTableProp();
                        this.$Message.success({
                            content: this.$t("dashboard_edit_component"),
                            background: true,
                        });
                        _that.editShow = false;
                    } else {
                        this.$Message.warning({
                            content: this.$t("dashboard_not_same_config"),
                            background: true,
                        });
                        return;
                    }
                }
            });
        },
        // 编辑弹框关闭时的函数
        editModalClose(value) {
            // 关闭
            console.log(value);
            // debugger
            if (!value) {
                this.editComponentForm = {
                    crossCol: null,
                    name: "",
                    code: "",
                    intervalTime: "",
                    crossRow: null,
                    pathtopoType: 1,
                    id: null,
                    typeCode: "",
                    viewId: "",
                    createTime: "",
                    alarmListNum: "",
                    alarmListNum1: "",
                    topoId: "",
                    provinceCode: "",
                    provinceMunicipalityCode: [],
                    orgId: "",
                    key: "",
                    editType: "",
                    pageUrl: "",
                    objType: "", // 对象类型
                    graphicType: null,
                    graphicList: [],
                    alarmStatisticsType: 1,
                    groupId: [],
                    url: "",
                    taskPresentationType: "",
                    dashboardComponentTaskGroups: [
                        {
                            groupId: "", // 分组
                            dashboardViewId: "", // 链接
                        },
                    ],
                    componentRealAlarmConfigs:[
                        {
                            alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                            alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                            alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                            alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                            alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                            maintainLevelOne: 0, //运维等级
                            maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                            maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                            showRecentlyAlarmNum: 0, //显示最新告警条数
                            showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                            type: 1 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                        },
                        {
                            alarmNoRecoverStatus: 0, //告警状态未恢复 ,0选择，1不选择
                            alarmRecoverStatus: 0, //告警状态恢复 ,0选择，1不选择
                            alarmTypeBroken: 0, //告警类型中断 ,0选择，1不选择
                            alarmTypeDelay: 0, //告警类型时延劣化 ,0选择，1不选择
                            alarmTypePackLoss: 0, //告警类型丢包时延 ,0选择，1不选择
                            maintainLevelOne: 0, //运维等级
                            maintainLevelThree: 0, //运维等级三级,0选择，1不选择
                            maintainLevelTwo: 0, //运维等级二级,0选择，1不选择
                            showRecentlyAlarmNum: 0, //显示最新告警条数
                            showType: 0, //实时告警配置是否显示:默认0显示，1不显示
                            type: 0 //实时告警配置类型:默认0告警列表，1横向滚动消息栏
                        }
                    ],
                    topoInfos: [],
                    portId: "",
                    noShowName: 0,
                    noShowFrame: 0,
                    taskIds: [],
                };
                this.topoInfos = [];
            }
            this.objRequiredEdit = false;
        },
        //表格属性计算
        handleTableProp() {
            if (this.viewList.length < 0) {
                this.$Message.warning({
                    content: this.$t("dashboard_no_configured_add_artifacts"),
                    background: true,
                });
            } else {
                let componentList = this.viewList;
                let rowArray = JSON.parse(JSON.stringify(this.rowArrayCopy));
                let newArr = [];
                let startRow = 0,
                    startColumn = 0,
                    endRow = 0;
                componentList.forEach((item, index) => {
                    const itemClRowArr = item.crossRow.split("-"),
                        itemClColumnArr = item.crossCol.split("-");
                    //获取开始和结束 行和列
                    const itemStartR = parseInt(itemClRowArr[0]),
                        itemEndR = parseInt(itemClRowArr[itemClRowArr.length - 1]),
                        itemStartCol = parseInt(itemClColumnArr[0]),
                        itemEndCol = parseInt(itemClColumnArr[itemClColumnArr.length - 1]);
                    //获取最终跨多少行和列的数量
                    const itemRowspan = itemEndR - itemStartR + 1;
                    const itemCoslspan = itemEndCol - itemStartCol + 1;
                    for (let index = 0; index < rowArray.length; index++) {
                        if (rowArray[index].rowIndex == itemStartR) {
                            let columnArr = rowArray[index].columns;

                            for (let colIndex = 0; colIndex < columnArr.length; colIndex++) {
                                if (columnArr[colIndex].columIndex == itemStartCol) {
                                    columnArr[colIndex].rowspan = itemRowspan;
                                    columnArr[colIndex].colspan = itemCoslspan;
                                    columnArr.splice(colIndex + 1, itemEndCol - itemStartCol);
                                    break;
                                }
                            }
                            break;
                        }
                    }
                    let emptyCol = [];
                    for (let rowIndex = 0; rowIndex < rowArray.length; rowIndex++) {
                        if (
                            rowArray[rowIndex].rowIndex > itemStartR &&
                            rowArray[rowIndex].rowIndex <= itemEndR
                        ) {
                            for (
                                let colIndex = rowArray[rowIndex].columns.length - 1;
                                colIndex >= 0;
                                colIndex--
                            ) {
                                if (
                                    rowArray[rowIndex].columns[colIndex].columIndex >=
                                    itemStartCol &&
                                    rowArray[rowIndex].columns[colIndex].columIndex <= itemEndCol
                                ) {
                                    rowArray[rowIndex].columns.splice(colIndex, 1);
                                }
                            }
                            if (rowArray[rowIndex].columns.length < 1) {
                                emptyCol.push(rowIndex);
                            }
                        }
                    }
                    if (emptyCol.length > 0) {
                    }
                });
                this.rowArray = rowArray;
            }
        },

        //编辑构件
        editClick(e) {
            this.isEditComponent = true;
            console.log(e, "eeeee");
            // console.log(this.editComponentForm,'rrrrrrrrr')
            // debugger
            this.targetTableDatas = [];
            this.targetsTableDatas = [];
            this.TagDatas = [];
            const obj = JSON.parse(JSON.stringify(e));
            this.editComponentForm.componentRealAlarmConfigs[0].alarmRecoverStatus = 1;
            this.editComponentForm.componentRealAlarmConfigs[1].alarmRecoverStatus = 1;
            console.log(obj, "obj...........................");
            this.monitorEditState = true;
            if (obj.typeCode == "physics_topo") {
                this.physicstopoid = obj.topoId;
                this.topoType = this.physDown;
                  if (obj.topoId) {
                      if (Array.isArray(obj.topoId)) {
                        // 如果已经是数组，直接赋值
                        this.editComponentForm.topoId = obj.topoId;
                      } else {
                        // 如果是字符串，则进行分割
                        this.editComponentForm.topoId = obj.topoId.split(',');
                      }
                    } else {
                      // 如果为空，设置为空数组
                      this.editComponentForm.topoId = [];
                    }

                
            }
            if (obj.typeCode == "route_topo") {
                this.routetopoid = obj.topoId;
                this.topoType = this.pathDown;
                if (obj.topoId) {
                      if (Array.isArray(obj.topoId)) {
                        // 如果已经是数组，直接赋值
                        this.editComponentForm.topoId = obj.topoId;
                      } else {
                        // 如果是字符串，则进行分割
                        this.editComponentForm.topoId = obj.topoId.split(',');
                      }
                    } else {
                      // 如果为空，设置为空数组
                      this.editComponentForm.topoId = [];
                    }
                
                
                this.editComponentForm.pathtopoType = obj.pathtopoType;
                this.pathTopoTypeSelectChange(this.editComponentForm.pathtopoType);
            }
            if (obj.typeCode == "fiber_chart") {
                this.fiberchartid = obj.topoId;
                this.topoType = this.pathDown;
            }
            if (
                obj.typeCode == "aggre_gation" ||
                obj.typeCode == "batch_aggre_gation"
            ) {
                this.targetTableDatas = obj.graphicList || [];
            }
            if (obj.typeCode == "real_time_alarm") {
                this.groupType = this.groupDown;
            }

            else if(obj.typeCode == 'route_alarm_trend'|| obj.typeCode == 'special_alarm_trend'||obj.typeCode == 'snmp_alarm_trend'){
              this.groupType = this.groupDown;
            }

            if (obj.editType && obj.editType === "add") {
                console.log('编辑的是新建的构件');
                // debugger
                //编辑的是新建的构件
                this.editComponentForm.crossCol = obj.crossCol;
                this.editComponentForm.crossRow = obj.crossRow;
                this.editComponentForm.typeCode =
                    obj.typeCode + "?pageUrl=" + obj.pageUrl;
                this.editComponentForm.name = obj.name;
                this.editComponentForm.key = obj.key;
                this.editComponentForm.editType = obj.editType;
                this.editComponentForm.alarmStatisticsType = obj.alarmStatisticsType || 1;
                // this.editComponentForm.alarmListNum = obj.alarmListNum
                //     ? String(obj.alarmListNum)
                //     : "";
                if (obj.groupId) {
                    if (typeof obj.groupId === "string") {
                        this.editComponentForm.groupId = obj.groupId.split(",");
                    }
                }
                // 呈现
                if (obj.dashboardComponentTaskGroups && obj.dashboardComponentTaskGroups.length>0) {
                    this.editComponentForm.taskPresentationType = String(obj.taskPresentationType);
                    let dashboardComponentTaskGroups = (obj.dashboardComponentTaskGroups).map(item => ({
                        ...item,
                        groupId: String(item.groupId)
                    }))

                    this.editComponentForm.dashboardComponentTaskGroups = dashboardComponentTaskGroups
                    console.log(dashboardComponentTaskGroups);
                    console.log('新建的构件222');
                }

                 // 实时告警
                 if (obj.componentRealAlarmConfigs && obj.componentRealAlarmConfigs.length > 0) {
                    this.editComponentForm.componentRealAlarmConfigs =obj.componentRealAlarmConfigs;
                    debugger
                    this.editComponentForm.alarmListNum = obj.componentRealAlarmConfigs[0].showRecentlyAlarmNum;
                    this.editComponentForm.alarmListNum1 = obj.componentRealAlarmConfigs[1].showRecentlyAlarmNum;
                }

                this.editComponentForm.topoId = obj.topoId ? String(obj.topoId) : "";
                this.editComponentForm.pageUrl = obj.pageUrl;
                this.editComponentForm.intervalTime = Number(obj.intervalTime);
                this.editComponentForm.graphicType = obj.graphicType;
                this.editComponentForm.graphicList = obj.graphicList;
                this.editComponentForm.provinceCode = obj.provinceCode || 100000;
                this.editComponentForm.provinceMunicipalityCode = this.stringToArray(
                    obj.provinceMunicipalityCode
                );
                this.editComponentForm.objType = obj.objType;
                this.TagDatas = obj.monitorObjList;
                this.editComponentForm.url = obj.url || "";
                this.editComponentForm.portId = obj.portId || "";
                this.editComponentForm.noShowName = obj.noShowName || 0;
                this.editComponentForm.noShowFrame = obj.noShowFrame || 0;
                // 实时告警分组会显配置
                if (obj.groupIds) {
                    if (typeof obj.groupIds === "string") {
                        this.editComponentForm.groupId = obj.groupIds.split(",");
                    }
                }
                // if (obj.typeCode == "route_topo") {
                //     this.editComponentForm.topoId = 'string' ? obj.topoId.split(',') : obj.topoId
                //     console.log(this.editComponentForm, "编辑的资料资料");
                // }
                
                // 监控目标分布图编辑
                if (obj.typeCode == "monitor_target_spread_map") {
                    // this.getBgList()
                    this.editComponentForm.iconScalingRatio = obj.iconScalingRatio;
                    this.editComponentForm.backgroundImageId = obj.backgroundImageId;
                    this.objSelectedData = obj.monitorTargetSpreadTaskInfos
                        ? obj.monitorTargetSpreadTaskInfos
                        : [];
                    this.editComponentForm.taskIds = this.objSelectedData.map(
                        (item) => item.id
                    );
                    // console.log(this.objSelectedData,this.addComponentForm.taskIds)
                    // this.editComponentForm.taskIdsStr =this.editComponentForm.taskIds.join(',')
                    console.log(this.editComponentForm, "编辑的资料资料");
                }
            } else {
                console.log(obj);
                // // debugger
                console.log('编辑的旧数据');
                //编辑的旧数据
                this.editComponentForm.code = obj.code;
                // 这里写入全名称
                this.editComponentForm.name = obj.nameAll || obj.name;
                this.editComponentForm.intervalTime = Number(obj.intervalTime);
                this.editComponentForm.crossCol = obj.crossCol;
                this.editComponentForm.crossRow = obj.crossRow;
                this.editComponentForm.id = obj.id;
                this.editComponentForm.typeCode =
                    obj.typeCode + "?pageUrl=" + obj.pageUrl;
                this.editComponentForm.viewId = obj.viewId;
                this.editComponentForm.alarmStatisticsType = obj.alarmStatisticsType || 1;
                this.editComponentForm.createTime = obj.createTime;
                // this.editComponentForm.alarmListNum = obj.alarmListNum
                //     ? String(obj.alarmListNum)
                //     : "";
                if (obj.groupId) {
                    if (typeof obj.groupId === "string") {
                        this.editComponentForm.groupId = obj.groupId.split(",");
                    } else {
                        this.editComponentForm.groupId = obj.groupId;
                    }
                }
                console.log(obj);
                // debugger
                // 实时告警
                if (obj.componentRealAlarmConfigs && obj.componentRealAlarmConfigs.length > 0) {
                    this.editComponentForm.alarmListNum = obj.componentRealAlarmConfigs[0].showRecentlyAlarmNum;
                    this.editComponentForm.alarmListNum1 = obj.componentRealAlarmConfigs[1].showRecentlyAlarmNum;
                    console.log(obj.componentRealAlarmConfigs[0].showType);
                    console.log(obj.componentRealAlarmConfigs[1].showType);
                    this.editComponentForm.componentRealAlarmConfigs = obj.componentRealAlarmConfigs;
                    // debugger
                }
                // 呈现
                if (obj.dashboardComponentTaskGroups && obj.dashboardComponentTaskGroups.length>0) {
                    this.editComponentForm.taskPresentationType = String(obj.taskPresentationType);
                    let val =  (obj.dashboardComponentTaskGroups).map(({ id, dashboardComponentId, createTime, ...rest }) => rest);
                    let dashboardComponentTaskGroups = val.map(item => ({
                        ...item,
                        groupId: String(item.groupId)
                    }))
                    this.editComponentForm.dashboardComponentTaskGroups = dashboardComponentTaskGroups
                    console.log(dashboardComponentTaskGroups);
                    console.log('旧的的构件333');
                }
                // this.editComponentForm.topoId = obj.topoId ? obj.topoId.split(',') : []
                // this.editComponentForm.topoId = obj.topoId ? String(obj.topoId) : "";
                this.editComponentForm.orgId = obj.orgId;
                this.editComponentForm.key = obj.key;
                this.editComponentForm.editType = null;
                this.editComponentForm.pageUrl = obj.pageUrl;
                this.editComponentForm.graphicType = obj.graphicType;
                this.editComponentForm.graphicList = obj.graphicList;
                this.editComponentForm.provinceCode = obj.provinceCode || 100000;
                this.editComponentForm.provinceMunicipalityCode = this.stringToArray(
                    obj.provinceMunicipalityCode
                );
                this.editComponentForm.objType = String(obj.objType);
                this.TagDatas = obj.monitorObjList;

                this.editComponentForm.url = obj.url || "";
                this.editComponentForm.portId = obj.portId || "";

                this.editComponentForm.noShowName = obj.noShowName || 0;
                this.editComponentForm.noShowFrame = obj.noShowFrame || 0;
                // 监控目标分布图编辑
                if (obj.typeCode == "monitor_target_spread_map") {
                    // this.getBgList()
                    this.editComponentForm.iconScalingRatio = obj.iconScalingRatio;
                    this.editComponentForm.backgroundImageId = obj.backgroundImageId;
                    this.objSelectedData = obj.monitorTargetSpreadTaskInfos
                        ? obj.monitorTargetSpreadTaskInfos
                        : [];
                    this.editComponentForm.taskIds = this.objSelectedData.map(
                        (item) => item.id
                    );

                    // console.log(this.objSelectedData,this.addComponentForm.taskIds)
                    // this.editComponentForm.taskIdsStr =this.editComponentForm.taskIds.join(',')

                    console.log(this.objSelectedData, "表格数据");
                }
                // if (obj.typeCode == "route_topo") {

                //   if (obj.topoId) {
                //       if (Array.isArray(obj.topoId)) {
                //         // 如果已经是数组，直接赋值
                //         this.editComponentForm.topoId = obj.topoId;
                //       } else {
                //         // 如果是字符串，则进行分割
                //         this.editComponentForm.topoId = obj.topoId.split(',');
                //       }
                //     } else {
                //       // 如果为空，设置为空数组
                //       this.editComponentForm.topoId = [];
                //     }
                // }
                console.log( this.editComponentForm.topoId);
                // debugger
                // 实时告警分组会显配置
                if (obj.groupIds) {
                    if (typeof obj.groupId === "string") {
                        this.editComponentForm.groupId = obj.groupIds.split(",");
                    } else {
                        this.editComponentForm.groupId = obj.groupId;
                    }
                }
            }
            this.editShow = true;
        },
        // 逗号凭借字符串转array
        stringToArray(data) {
            if (data instanceof Array) {
                return data;
            } //true
            if (!data) {
                return [];
            }
            const array = data.split(",");
            return array.map((val) => {
                return Number(val);
            });
        },
        //删除构件
        deleteClick(e) {
          // debugger
            top.window.$iviewModal.confirm({
                title: this.$t("comm_tip"),
                content: this.$t("dashboard_whether_del_after_saving"),
                onOk: () => {
                    let index1 = -1,
                        index2 = -1,
                        index3 = -1,
                        index4 = -1;
                    for (let i = 0, len = this.componentList.length; i < len; i++) {
                        if (this.componentList[i].key === e.key) {
                            index1 = i;
                        }
                    }
                    for (
                        let i2 = 0, len2 = this.addComponentList.length;
                        i2 < len2;
                        i2++
                    ) {
                        if (this.addComponentList[i2].key === e.key) {
                            index2 = i2;
                        }
                    }
                    for (
                        let i3 = 0, len3 = this.editComponentList.length;
                        i3 < len3;
                        i3++
                    ) {
                        if (this.editComponentList[i3].key === e.key) {
                            index3 = i3;
                        }
                    }
                    for (let i4 = 0, len4 = this.viewList.length; i4 < len4; i4++) {
                        if (this.viewList[i4].key === e.key) {
                            index4 = i4;
                        }
                    }

                    this.componentList.splice(index1, index1 === -1 ? 0 : 1);
                    this.addComponentList.splice(index2, index2 === -1 ? 0 : 1);
                    this.editComponentList.splice(index3, index3 === -1 ? 0 : 1);
                    this.viewList.splice(index4, index4 === -1 ? 0 : 1);
                    if (e.editType && e.editType === "add") {
                    } else {
                        this.deleteComponentList.push(e);
                    }
                    this.handleTableProp();
                    sessionStorage.setItem(
                        "components",
                        JSON.stringify(this.componentList)
                    );
                },
            });
        },
        //保存设置
        addComponentSave() {
            let addParams = [];
            console.log(this.viewList, "所有的数据");
            if (this.viewList.length > 30) {
                this.$Message.warning(this.$t("view_max_comp_30"));
                return;
            }
            for (let i = 0; i < this.viewList.length; i++) {
                let dashboardComponentId = null;
                if (this.viewList[i].typeCode == "monitor_target_spread_map") {
                    dashboardComponentId = this.viewList[i].id || null;
                }
                let topoId = this.viewList[i].topoId;
                if (Array.isArray(topoId)) {
                    topoId = topoId.join(',');
                }
                addParams.push({
                    objType: this.viewList[i].objType,
                    name: this.viewList[i].name,
                    crossRow: this.viewList[i].crossRow,
                    crossCol: this.viewList[i].crossCol,
                    intervalTime: this.viewList[i].intervalTime,
                    typeCode: this.viewList[i].typeCode,
                    viewId: this.viewId,
                    // alarmListNum:
                    //     this.viewList[i].typeCode == "real_time_alarm"
                    //         ? this.viewList[i].alarmListNum
                    //         : null,
                    topoId:
                        this.viewList[i].typeCode == "route_topo" ||
                            this.viewList[i].typeCode == "physics_topo" ||
                            this.viewList[i].typeCode == "fiber_chart"
                            ? topoId
                            : null,
                    graphicType:
                        this.viewList[i].typeCode == "aggre_gation" ||
                            this.viewList[i].typeCode == "batch_aggre_gation"
                            ? this.viewList[i].graphicType
                            : null,
                    graphicList:
                        this.viewList[i].typeCode == "aggre_gation" ||
                            this.viewList[i].typeCode == "batch_aggre_gation"
                            ? this.viewList[i].graphicList
                            : null,
                    monitorObjList:
                        this.viewList[i].typeCode == "real_time_monitor"
                            ? this.viewList[i].monitorObjList
                            : null,
                    provinceCode: this.viewList[i].provinceCode || 100000,
                    provinceMunicipalityCode: this.viewList[i].provinceMunicipalityCode,
                    pathtopoType: this.viewList[i].pathtopoType,
                    groupIds: this.viewList[i].groupIds,
                    alarmStatisticsType: this.viewList[i].alarmStatisticsType || 1,
                    url: this.viewList[i].url || "",
                    portId: this.viewList[i].portId || "",
                    noShowName: this.viewList[i].noShowName || 0,
                    noShowFrame: this.viewList[i].noShowFrame || 0,
                    backgroundImageId: this.viewList[i].backgroundImageId || null,
                    iconScalingRatio: this.viewList[i].iconScalingRatio,
                    taskIds: this.viewList[i].taskIds || [],

                    dashboardComponentId: dashboardComponentId,
                    taskPresentationType: this.viewList[i].taskPresentationType,
                    dashboardComponentTaskGroups: this.viewList[i].dashboardComponentTaskGroups,
                    componentRealAlarmConfigs: this.viewList[i].componentRealAlarmConfigs

                });
            }
            console.log(addParams, "请求参数");
            this.$http
                .post("/dashboard/component/add", {
                    viewId: this.viewId,
                    componentArr: JSON.stringify(addParams),
                })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.$Message.success({
                            content: this.$t("view_save_succ"),
                            background: true,
                        });
                    } else {
                        let msgText = msg || this.$t("view_save_failed");
                        this.$Message.warning({
                            content: msgText,
                            background: true,
                        });
                    }
                })
                .catch((error) => console.log(error));
        },

        //判断是否超出可配置的范围
        isBeyondScope(startRow, endRow, startCol, endCol, key) {
            let flag = true;
            const list = this.viewList.filter((item) => {
                return item.key != key;
            }); //当前已配置数据
            const startrPoint = parseInt(startRow),
                endrPoint = parseInt(endRow);
            const startCPoint = parseInt(startCol),
                endCPoint = parseInt(endCol);
            //计算出配置的起始坐标数组
            for (let i = 0; i < list.length; i++) {
                let itemPointArr = [];
                let itemstartRow = parseInt(list[i].startRow),
                    itemendRow = parseInt(list[i].endRow),
                    itemstartCol = parseInt(list[i].startCol),
                    itemendCol = parseInt(list[i].endCol);
                if (
                    startrPoint > itemendRow ||
                    endrPoint < itemstartRow ||
                    (startrPoint >= itemstartRow &&
                        startrPoint <= itemendRow &&
                        (startCPoint > itemendCol || endCPoint < itemstartCol))
                ) {
                    flag = true;
                } else if (
                    itemstartRow >= startrPoint &&
                    itemstartRow <= endrPoint &&
                    (startCPoint > itemendCol || endCPoint < itemstartCol)
                ) {
                    flag = true;
                } else {
                    flag = false;
                    break;
                }
            }
            return flag;
        },
        //打开聚合图形指标选择弹框
        addtarget(typeCode) {
            if (this.targetTableDatas.length < 10) {
                this.indicatorModal.show = true;
                this.indicatorModalForm["objectType"] = null;
                this.indicatorModalForm["objectId"] = null;
                this.indicatorModalForm["indicatorType"] = null;
                this.indicatorModalForm["portId"] = null;
                this.indicatorObject = [];
                this.typeList = {};
                this.$refs["indicatorModalForm"].resetFields();
            } else {
                this.$Message.warning({
                    content: this.$t("view_indi_10"),
                    background: true,
                });
            }
        },
        // 聚合图形批量添加指标
        addtargets() {
            if (this.TagDatas.length == 0) {
                this.$Message.error(this.$t("dashboard_select_ob"));
                return;
            }
            this.indicatorsModalShow = true;
        },
        //指标选择任务类型切换
        indicatorChange(e) {
            this.convertTndicatorFields(e);
            this.getIndicatorMsg(e);
        },
        getTypes(e) {
            if (e === "1") {
                return {
                    1: this.$t("dashboard_delay"),
                    2: this.$t("dashboard_lost_packet"),
                    3: this.$t("dashboard_availability"),
                    4: this.$t("dashboard_excellent_rate"),
                    5: this.$t("dashboard_upstream"),
                    6: this.$t("dashboard_down"),
                    7: this.$t("dashboard_uplink"),
                    8: this.$t("dashboard_downstream"),
                };
            } else if (e === "2") {
                return {
                    1: this.$t("dashboard_delay"),
                    2: this.$t("dashboard_lost_packet"),
                    3: this.$t("dashboard_availability"),
                    4: this.$t("dashboard_excellent_rate"),
                    5: this.$t("dashboard_inlet_velocity"),
                    6: this.$t("dashboard_outflow_velocity"),
                };
            } else if (e === "3") {
                return {
                    1: this.$t("dashboard_delay"),
                    2: this.$t("dashboard_lost_packet"),
                    3: this.$t("dashboard_availability"),
                    4: this.$t("dashboard_excellent_rate"),
                    // 2024年7月4日16:18:35 添加内容
                    5: this.$t("dashboard_inlet_velocity"),
                    6: this.$t("dashboard_outflow_velocity"),
                    7: this.$t("dashboard_inflow_rate"),
                    8: this.$t("dashboard_outflow_rate"),
                };
            } else if (e === "4") {
                // 设备信息
                return {
                    // 2024年7月4日16:18:35 添加内容
                    5: this.$t("dashboard_inlet_velocity"),
                    6: this.$t("dashboard_outflow_velocity"),
                    7: this.$t("dashboard_inflow_rate"),
                    8: this.$t("dashboard_outflow_rate"),
                };
            }
        },
        convertTndicatorFields(e) {
            //显示端口信息数据
            this.indicatorFields.forEach((item) => {
                if (e == "4") {
                    item.show = true;
                } else {
                    if (item.prop == "portId") {
                        item.show = false;
                    } else {
                        item.show = true;
                    }
                }
            });
            console.log(this.indicatorFields, "-------------");
        },
        //获取对应类型的任务信息（对象）
        getIndicatorMsg(e) {
            //专线
            if (e === "1") {
                let aaa = [];
                this.indicatorObject = [];
                this.typeList = {
                    1: this.$t("dashboard_delay"),
                    2: this.$t("dashboard_lost_packet"),
                    3: this.$t("dashboard_availability"),
                    4: this.$t("dashboard_excellent_rate"),
                    5: this.$t("dashboard_upstream"),
                    6: this.$t("dashboard_down"),
                    7: this.$t("dashboard_uplink"),
                    8: this.$t("dashboard_downstream"),
                };
                this.$http
                    .post("/link/list", { pageNo: 1, pageSize: 10000000 })
                    .then(({ code, data, msg }) => {
                        if (code === 1) {
                            aaa = data.records;
                            this.indicatorObject = aaa.map((item) => {
                                item.id = item.id;
                                item.objectId = item.id;
                                item.name = item.name
                                    ? item.name +
                                    "(" +
                                    item.specialAIp +
                                    "-" +
                                    item.specialZIp +
                                    ")"
                                    : "--" + "(" + item.specialAIp + "-" + item.specialZIp + ")";
                                item.aip = item.specialAIp;
                                item.zip = item.specialZIp;
                                return item;
                            });
                        }
                    });
                //拨测
            } else if (e === "3") {
                let aaa = [];
                this.indicatorObject = [];
                this.typeList = {
                    1: this.$t("dashboard_delay"),
                    2: this.$t("dashboard_lost_packet"),
                    3: this.$t("dashboard_availability"),
                    4: this.$t("dashboard_excellent_rate"),
                    // 2024年7月4日16:18:35 添加内容
                    5: this.$t("dashboard_inlet_velocity"),
                    6: this.$t("dashboard_outflow_velocity"),
                    7: this.$t("dashboard_inflow_rate"),
                    8: this.$t("dashboard_outflow_rate"),
                };
                this.$http
                    .post("/probetask/dashboardTaskList", {
                        pageNo: 1,
                        pageSize: 10000000,
                    })
                    .then(({ code, data, msg }) => {
                        if (code === 1) {
                            aaa = data.records;
                            this.indicatorObject = aaa.map((item) => {
                                item.id = item.id;
                                item.objectId = item.id;
                                item.name = item.destIpName
                                    ? item.destIpName +
                                    "(" +
                                    (item.sourceIp || "-") +
                                    "-" +
                                    (item.destIp || "-") +
                                    ")"
                                    : "--" + (item.sourceIp || "-") + "-" + (item.destIp || "-");
                                item.aip = item.sourceIp;
                                item.zip = item.destIp;
                                return item;
                            });
                        }
                    });
            }
            //中继
            else if (e === "2") {
                let aaa = [];
                this.indicatorObject = [];
                this.typeList = {
                    1: this.$t("dashboard_delay"),
                    2: this.$t("dashboard_lost_packet"),
                    3: this.$t("dashboard_availability"),
                    4: this.$t("dashboard_excellent_rate"),
                    5: this.$t("dashboard_inlet_velocity"),
                    6: this.$t("dashboard_outflow_velocity"),
                };
                this.$http
                    .post("/rpmanger/list", { pageNo: 1, pageSize: 10000000 })
                    .then(({ code, data, msg }) => {
                        if (code === 1) {
                            aaa = data.records;

                            this.indicatorObject = aaa.map((item) => {
                                item.id = item.id;
                                item.objectId = item.id;
                                item.name = item.repeatName
                                    ? item.repeatName + "(" + item.srcIp + "-" + item.destIp + ")"
                                    : "--" + "(" + item.srcIp + "-" + item.destIp + ")";
                                item.aip = item.srcIp;
                                item.zip = item.destIp;
                                return item;
                            });
                        }
                    });
            } else if (e === "4") {
                // 设备信息
                let aaa = [];
                this.indicatorObject = [];
                this.typeList = {
                    // 2024年7月4日16:18:35 添加内容
                    5: this.$t("dashboard_inlet_velocity"),
                    6: this.$t("dashboard_outflow_velocity"),
                    7: this.$t("dashboard_inflow_rate"),
                    8: this.$t("dashboard_outflow_rate"),
                };
                this.$http.wisdomPost("/device/data").then(({ code, data, msg }) => {
                    if (code === 1) {
                        aaa = data;
                        this.indicatorObject = aaa.map((item) => {
                            item.id = item.id;
                            item.objectId = item.id;
                            item.name =
                                (item.deviceName || "--") + "(" + (item.deviceIp || "--") + ")";
                            item.aip = item.deviceIp;
                            item.zip = item.deviceIp;
                            return item;
                        });
                    }
                });
            }
        },
        // 设备加载端口信息列表
        indicatorObjectIdChange(e) {
            // 加载端口信息
            if (this.indicatorModalForm.objectType == 4) {
                var itemObj = JSON.parse(e);
                this.getDeviceInterfaceData(itemObj.id);
            }
        },
        // 加载端口信息
        getDeviceInterfaceData(deviceId) {
            let that = this;
            that.portList = [];
            that.$http
                .wisdomPost("/deviceDetail/queryInterfaceByDeviceId", {
                    deviceId: deviceId,
                    queryManualAddition: 1,
                })
                .then((res) => {
                    if (res.code === 1) {
                        let lists = res.data.map((item) => {
                            var name = item.ifName || "";
                            name += "(" + (item.ifIp || "--") + ")";
                            return { id: item.id, name: name };
                        });
                        that.portList = lists;
                    } else {
                        that.portList = [];
                    }
                });
        },
        //增加聚合指标
        addIndicatot() {
            this.$refs["indicatorModalForm"].validate((valid) => {
                if (valid) {
                    let {
                        objectType,
                        objectId,
                        indicatorType,
                        portId,
                    } = this.indicatorModalForm;

                    let obj = JSON.parse(objectId),
                        targetTableDatas = this.targetTableDatas;
                    let haveCommon = false;
                    for (let index = 0; index < targetTableDatas.length; index++) {
                        const element = targetTableDatas[index];
                        if (
                            element.aip == obj.aip &&
                            element.zip == obj.zip &&
                            element.objectType == objectType &&
                            element.indicatorType == indicatorType
                        ) {
                            haveCommon = true;
                        }
                        if (haveCommon) {
                            break;
                        }
                    }
                    this.targetTableDatas.push({
                        id: obj.id,
                        objectId: obj.objectId,
                        name: obj.name,
                        componentId: obj.componentId,
                        objectType: objectType,
                        aip: obj.aip,
                        zip: obj.zip,
                        indicatorType: indicatorType,
                        portId: portId,
                        color: getRandomColor(),
                    });
                    this.indicatorModal.show = false;
                    this.indicatorModalForm["objectType"] = null;
                    this.indicatorModalForm["objectId"] = null;
                    this.indicatorModalForm["indicatorType"] = null;
                    this.indicatorModalForm["portId"] = null;
                    this.$refs["indicatorModalForm"].resetFields();
                }
            });
        },
        addIndicatots() {
            this.indicatorsModalForm.indicatorType.forEach((item) => {
                let has = false;
                this.targetsTableDatas.forEach((sub) => {
                    if (sub.name == item) {
                        has = true;
                    }
                });
                if (!has) {
                    this.targetsTableDatas.push({
                        name: item,
                        color: getRandomColor(),
                    });
                }
            });
            this.indicatorsModalForm.indicatorType = [];
            this.checkAll = false;
            this.indicatorsModalShow = false;
        },
        //颜色改变
        headleChangeColor(color, row, index) {
            this.targetTableDatas[index].color = color;
        },
        headleChangeColors(color, row, index) {
            this.targetsTableDatas[index].color = color;
        },
    },
    computed: {
        currentForm() {
            return this.editShow ? this.editComponentForm : this.addComponentForm;
        },
        maintainCheckboxValues: {
            get() {
                // 检查 componentRealAlarmConfigs 是否存在并且是一个数组
                if (!this.currentForm.componentRealAlarmConfigs || !Array.isArray(this.currentForm.componentRealAlarmConfigs) || this.currentForm.componentRealAlarmConfigs.length === 0) {
                    return [];
                }
                // 将对象值转换为 ["一级", "二级", "三级"]
                let selected = [];
                if (this.currentForm.componentRealAlarmConfigs[0].maintainLevelOne === 0) selected.push("一级");
                if (this.currentForm.componentRealAlarmConfigs[0].maintainLevelTwo === 0) selected.push("二级");
                if (this.currentForm.componentRealAlarmConfigs[0].maintainLevelThree === 0) selected.push("三级");
                return selected;
            },
            set(newValues) {
                // 更新对象的选中状态
                this.currentForm.componentRealAlarmConfigs[0].maintainLevelOne = newValues.includes("一级") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[0].maintainLevelTwo = newValues.includes("二级") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[0].maintainLevelThree = newValues.includes("三级") ? 0 : 1;
            }
        },
        alarmCheckboxValues: {
            get() {
                if (!this.currentForm.componentRealAlarmConfigs || !Array.isArray(this.currentForm.componentRealAlarmConfigs) || this.currentForm.componentRealAlarmConfigs.length === 0) {
                    return [];
                }
                // 将对象值转换为 ["中断", "时延劣化", "丢包劣化"]
                let selected = [];
                if (this.currentForm.componentRealAlarmConfigs[0].alarmTypeBroken === 0) selected.push("中断");
                if (this.currentForm.componentRealAlarmConfigs[0].alarmTypeDelay === 0) selected.push("时延劣化");
                if (this.currentForm.componentRealAlarmConfigs[0].alarmTypePackLoss === 0) selected.push("丢包劣化");
                return selected;
            },
            set(newValues) {
                // 更新对象的选中状态
                this.currentForm.componentRealAlarmConfigs[0].alarmTypeBroken = newValues.includes("中断") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[0].alarmTypeDelay = newValues.includes("时延劣化") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[0].alarmTypePackLoss = newValues.includes("丢包劣化") ? 0 : 1;
            }
        },
        recoverCheckboxValues: {
            get() {
                if (!this.currentForm.componentRealAlarmConfigs || !Array.isArray(this.currentForm.componentRealAlarmConfigs) || this.currentForm.componentRealAlarmConfigs.length === 0) {
                    return [];
                }
                // 将对象值转换为 ["未恢复", "已恢复"]
                let selected = [];
                if (this.currentForm.componentRealAlarmConfigs[0].alarmNoRecoverStatus === 0) selected.push("未恢复");
                if (this.currentForm.componentRealAlarmConfigs[0].alarmRecoverStatus === 0) selected.push("已恢复");
                return selected;
            },
            set(newValues) {
                // 更新对象的选中状态
                this.currentForm.componentRealAlarmConfigs[0].alarmNoRecoverStatus = newValues.includes("未恢复") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[0].alarmRecoverStatus = newValues.includes("已恢复") ? 0 : 1;
            }
        },
        maintainCheckboxValues1: {
            get() {
                if (!this.currentForm.componentRealAlarmConfigs || !Array.isArray(this.currentForm.componentRealAlarmConfigs) || this.currentForm.componentRealAlarmConfigs.length === 0) {
                    return [];
                }
                // 将对象值转换为 ["一级", "二级", "三级"]
                let selected = [];
                if (this.currentForm.componentRealAlarmConfigs[1].maintainLevelOne === 0) selected.push("一级");
                if (this.currentForm.componentRealAlarmConfigs[1].maintainLevelTwo === 0) selected.push("二级");
                if (this.currentForm.componentRealAlarmConfigs[1].maintainLevelThree === 0) selected.push("三级");
                return selected;
            },
            set(newValues) {
                // 更新对象的选中状态
                this.currentForm.componentRealAlarmConfigs[1].maintainLevelOne = newValues.includes("一级") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[1].maintainLevelTwo = newValues.includes("二级") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[1].maintainLevelThree = newValues.includes("三级") ? 0 : 1;
            }
        },
        alarmCheckboxValues1: {
            get() {
                if (!this.currentForm.componentRealAlarmConfigs || !Array.isArray(this.currentForm.componentRealAlarmConfigs) || this.currentForm.componentRealAlarmConfigs.length === 0) {
                    return [];
                }
                // 将对象值转换为 ["中断", "时延劣化", "丢包劣化"]
                let selected = [];
                if (this.currentForm.componentRealAlarmConfigs[1].alarmTypeBroken === 0) selected.push("中断");
                if (this.currentForm.componentRealAlarmConfigs[1].alarmTypeDelay === 0) selected.push("时延劣化");
                if (this.currentForm.componentRealAlarmConfigs[1].alarmTypePackLoss === 0) selected.push("丢包劣化");
                return selected;
            },
            set(newValues) {
                // 更新对象的选中状态
                this.currentForm.componentRealAlarmConfigs[1].alarmTypeBroken = newValues.includes("中断") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[1].alarmTypeDelay = newValues.includes("时延劣化") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[1].alarmTypePackLoss = newValues.includes("丢包劣化") ? 0 : 1;
            }
        },
        recoverCheckboxValues1: {
            get() {
                if (!this.currentForm.componentRealAlarmConfigs || !Array.isArray(this.currentForm.componentRealAlarmConfigs) || this.currentForm.componentRealAlarmConfigs.length === 0) {
                    return [];
                }
                // 将对象值转换为 ["未恢复", "已恢复"]
                let selected = [];
                if (this.currentForm.componentRealAlarmConfigs[1].alarmNoRecoverStatus === 0) selected.push("未恢复");
                if (this.currentForm.componentRealAlarmConfigs[1].alarmRecoverStatus === 0) selected.push("已恢复");
                return selected;
            },
            set(newValues) {
                // 更新对象的选中状态
                this.currentForm.componentRealAlarmConfigs[1].alarmNoRecoverStatus = newValues.includes("未恢复") ? 0 : 1;
                this.currentForm.componentRealAlarmConfigs[1].alarmRecoverStatus = newValues.includes("已恢复") ? 0 : 1;
            }
        },
    },
    beforeDestroy() {
        sessionStorage.removeItem("components");
        window.removeEventListener("resize", this.changeWidth);
    },
};
</script>

<style scoped lang="less">
.content {
  padding: 10px 20px;
}

.buttonBox {
  text-align: right;

  .userDefined {
    height: 38px;
    margin-left: 5px;
    color: white;
  }

  .add {
    background: #2d8cf0;
  }
}

/deep/ .ivu-divider-horizontal {
  margin: 10px 0;
}

.btnSpan {
  color: #2d8cf0;
  cursor: pointer;
  margin-left: 8px;
}

.viewTable {
  width: 100%;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #dddddd;
      padding: 8px;
    }
  }
}

.addComponentForm,
.editComponentForm {
  input,
  select,
  textarea {
    width: calc(100% - 20px);
    border: 1px solid #dcdee2;
    padding-left: 8px;
    border-radius: 4px;
  }

  input:focus,
  textarea:focus {
    border-color: #57a3f3;
  }
}

/deep/ .ivu-modal-no-mask {
  pointer-events: auto;
  /**透设除弹框外其他区域不能点击 */
  background-color: rgba(55, 55, 55, 0.6);
  /**透明的遮罩效果 */
}

.targetBox {
  .targetLabel {
    display: inline-block;
    padding: 10px 12px 10px 0;
    box-sizing: border-box;
    width: 150px !important;
    text-align: right;
    vertical-align: top;
    float: left;
  }

  .targetLabel::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }

  .targetContent {
    display: block;
    margin-left: 170px !important;
    border: 1px solid #dcdee2;

    .targetTablebox {
      /deep/ .ivu-table td,
      /deep/ .ivu-table th {
        border-bottom: 0;
        background-color: transparent;
      }

      /deep/ .ivu-table:before {
        background-color: transparent;
      }

      /deep/ .colorCell .ivu-table-cell {
        overflow: visible;
      }
    }

    .targetAddBox {
      margin: 8px 10px;

      .targetAddBtn {
        padding: 4px 8px;
        cursor: pointer;
        color: #2b85e4;

        .addText {
          vertical-align: middle;
        }
      }
    }
  }
}

/deep/ .targetTablebox .ivu-table-wrapper {
  overflow: visible;
}

/deep/ .targetTablebox .ivu-table {
  overflow: visible;
}

/deep/ .m-colorPicker {
  border: 1px solid #d3d3d3;
}
</style>
