<template>
  <section class="homeBox" style="min-width:1200px">
    
    <div
      class="itemEcharts"
      :style="{ position: 'relative', minHeight: '400px' }"
    >
      <Loading :loading="loading1"></Loading>
      <div class="pieBox" style="position: relative">
        <Loading :loading="pieLoading"></Loading>
        <draw-pie
          :node="item.node"
          :datas="item"
          :time="{startTime:param.startTime+' 00:00:00',endTime:param.endTime+' 23:59:59'}"
          :height="
            pie_List.length === 3 ? 260 : pie_List.length === 2 ? 330 : 400
          "
          :r="pie_List.length === 3 ? 75 : pie_List.length === 2 ? 85 : 95"
          v-for="(item, index) in pie_List"
          :key="index"
        ></draw-pie>
      </div>
    </div>
  </section>
</template>

<script>
import drawPie from "@/common/echarts/draw-pie.vue";
import '@/timechange'
import global from "@/common/global.js";
export default {
  name: "wisdomIndex",
  components: {
    drawPie
  },
  data() {
    return {
      nowDate:new Date(),
      //时间参数
      startTime:null,
      endTime:null,
      loading: false,
      loading1: true,
      pieLoading: false,
      height: 260,
      param: {
        startTime:new Date().format2('yyyy-MM-dd'),
        endTime:new Date().format2('yyyy-MM-dd'),
      },
      pie_List: [],
      typeCode:"route_statistic" //此参数由调用的时候传进来，传进来的是构件的code例（route_statistic，special_statistic，snmp_statistic）
    };
  },
  created() {
    this.getPie(this.param);
  },
  mounted() {},
  methods: {
    // 获取环形图数据
    getPie(param) {
      this.pie_List = [];
      this.$http.wisdomPost("/home/<USER>", param).then(res => {
        let datas = res.data;
        if (res.code === 1) {
          //路径统计分析图
          if (datas.route && this.typeCode=='route_statistic') 
          {
            this.pie_List.push({
              node: "pie",
              title: this.$t('comm_task'),
              totalValue: datas.route.linkNum,
              data: [
                {
                  value: datas.route.normal,
                  name: this.$t('dash_normal'),
                  percent:
                    datas.route.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.route.normal) /
                            Number(datas.route.linkNum)) *
                          100
                        ).toFixed(2)
                },
                {
                  value: datas.route.degradation,
                  name: this.$t('dash_deterioration'),
                  link:'pathquality',
                  percent:
                    datas.route.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.route.degradation) /
                            Number(datas.route.linkNum)) *
                          100
                        ).toFixed(2)
                },
                {
                  value: datas.route.interrupt,
                  name: this.$t('dash_interrupt'),
                  link:'pathquality',
                  percent:
                    datas.route.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.route.interrupt) /
                            Number(datas.route.linkNum)) *
                          100
                        ).toFixed(2)
                }
              ]
            });
          }
          //专线统计分析图
          if (datas.special && this.typeCode=='special_statistic') {
            this.pie_List.push({
              node: "pie2",
              title: this.$t('spec_line'),
              totalValue: datas.special.linkNum,
              data: [
                {
                  value: datas.special.normal,
                  name: this.$t('dash_normal'),
                  percent:
                    datas.special.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.special.normal) /
                            Number(datas.special.linkNum)) *
                          100
                        ).toFixed(2)
                },
                {
                  value: datas.special.degradation,
                  name: this.$t('dash_deterioration'),
                  link:'pathquality',
                  percent:
                    datas.special.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.special.degradation) /
                            Number(datas.special.linkNum)) *
                          100
                        ).toFixed(2)
                },
                {
                  value: datas.special.interrupt,
                  name: this.$t('dash_interrupt'),
                  link:'pathquality',
                  percent:
                    datas.special.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.special.interrupt) /
                            Number(datas.special.linkNum)) *
                          100
                        ).toFixed(2)
                }
              ]
            });
          }
          //中继统计分析图
           if (datas.relay && this.typeCode=='snmp_statistic') {
            this.pie_List.push({
              node: "pie3",
              title: this.$t('dash_relay'),
              totalValue: datas.relay.linkNum,
              data: [
                {
                  value: datas.relay.normal,
                  name: this.$t('dash_normal'),
                  percent:
                    datas.relay.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.relay.normal) /
                            Number(datas.relay.linkNum)) *
                          100
                        ).toFixed(2)
                },
                {
                  value: datas.relay.degradation,
                  name: this.$t('dash_deterioration'),
                  link:'rpquality',
                  percent:
                    datas.relay.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.relay.degradation) /
                            Number(datas.relay.linkNum)) *
                          100
                        ).toFixed(2)
                },
                {
                  value: datas.relay.interrupt,
                  name: this.$t('dash_interrupt'),
                  link:'rpquality',
                  percent:
                    datas.relay.linkNum == 0
                      ? 0
                      : (
                          (Number(datas.relay.interrupt) /
                            Number(datas.relay.linkNum)) *
                          100
                        ).toFixed(2)
                }
              ]
            });
          }
          this.loading1 = false;
        }
      }).finally(()=>{
        this.pieLoading = false;
      });
    },
  }
};
</script>
<style scoped>
  .pieSearch{
    width: 80%;
    text-align: center;
  }
  .pieSearch .pieTime{
    display: inline-block;
    width: 100%;
  }
  /deep/.pieSearch .pieTime .ivu-input:hover{
    border-color: #dddddd;
  }
  .pieSearch .pieTime >label{
    font-weight: bold;
  }
.tableBox {
  margin: 20px 20px 40px 20px;
}

.dialTest-tab-title {
  padding-top: 20px;
  padding-bottom: 20px;
}

.homeBox {
  padding-top: 10px;
  height: 100%;
}

.itemEcharts {
  display: flex;
  align-items: center;
}

.pieBox {
  /*flex: 1;*/
  width: 90%;
}

html,
body {
  position: relative;
  height: 100%;
}

body {
  background: #eee;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #000;
  margin: 0;
  padding: 0;
}
</style>
