<template>
  <div :class="[currentSkin == 0 ? 'light-no-tabs' : '']">
    <div class="content">
      <div class="buttonBox">
        <Button
          v-if="permissionObj.add"
          class="jiaHao-btn btnSty"
          type="primary"
          @click="addTab"
          :title="$t('dash_create_view')"
          ><i class="iconfont icon-icon-add"
        /></Button>
        <Button
          class="delete-btn btnSty"
          type="primary"
          v-if="permissionObj.delete"
          @click="deleteTab('select')"
          :title="$t('dash_delete_view')"
        >
          <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
        </Button>
        <Button
          class="daoChu-btn btnSty"
          type="primary"
          @click="goBack"
          :title="$t('common_back')"
        >
          <i
            class="iconfont icon-icon-return1"
            :style="currentSkin == 1 ? 'color: #31f0fe' : 'color: #0290FD'"
          />
        </Button>
      </div>
      <Table
        ref="viewList"
        stripe
        :columns="tabsColumns"
        :data="tabsList"
        :loading="loading"
        :no-data-text="
          loading
            ? ''
            : tabsList.length > 0
            ? ''
            : currentSkin == 1
            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
              $t('common_No_data') +
              '</p></div>'
            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
              $t('common_No_data') +
              '</p></div>'
        "
        @on-select="handleSelect"
        @on-select-cancel="handleCancel"
        @on-select-all="handleSelectAll"
        @on-select-all-cancel="handleSelectAll"
        size="small"
      >
        <template
          slot-scope="{ row, index }"
          v-if="permissionObj.sort"
          slot="orderAction"
        >
          <span class="btnSpan" @click="upMove(row, index)">{{
            $t("dash_up")
          }}</span>
          <span class="btnSpan" @click="downMove(row, index)">{{
            $t("dash_down")
          }}</span>
        </template>
      </Table>

      <!--新建视图模态框-->
      <Modal
        v-model="modalShow"
        :title="
          modalTitle === 1 ? $t('dash_create_view') : $t('dash_panel_edit')
        "
        width="700"
        :mask="true"
        sticky
        draggable
      >
        <Form
          ref="addViewForm"
          class="addForm dashboard-view-form"
          v-if="modalShow"
          :model="addForm"
          :rules="rulesValidate"
          :label-width="lang == 'zh' ? 110 : 155"
          @submit.native.prevent
        >
          <FormItem
            v-for="item in fieldArray"
            :key="item.key"
            :label="item.name === '' ? '' : item.name + '：'"
            v-if="item.show"
            :prop="item.prop"
          >
            <!-- 模板文件,编辑不显示 -->
            <Select
              v-model="addForm[item.prop]"
              v-if="
                item.type === 'select' &&
                item.prop === 'templateId' &&
                modalTitle === 1
              "
              @on-change="templateChange"
              clearable
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
            >
              <Option
                v-for="templateItem in templateList"
                :key="templateItem.id"
                :value="templateItem.id"
                >{{ templateItem.name }}</Option
              >
            </Select>

            <input
              v-model.trim="addForm[item.prop]"
              :disabled="item.readonly"
              v-if="item.type === 'input'"
              :placeholder="$t('comm_enter') + item.name"
              :maxlength="
                item.prop == 'url' ? 200 : item.prop == 'name' ? 50 : 20
              "
            />

            <!-- 屏幕尺寸 -->
            <Select
              v-model="addForm[item.prop]"
              v-if="item.type === 'select' && item.prop === 'proportion'"
              clearable
              :disabled="item.readonly"
              :placeholder="$t('comm_select') + item.name"
              :class="proportionClass"
              @on-change="proportionChange"
              :style="proportionStyle"
            >
              <Option
                v-for="ratioItem in viewScreenRatioAarray"
                :key="ratioItem.key"
                :value="ratioItem.key"
                >{{ ratioItem.ratio }}</Option
              >
            </Select>

            <span
              class="proportion-mumber"
              v-if="item.prop === 'proportion' && proportionData.show"
              style="margin-left: 5px"
            >
              <InputNumber
                :max="99999"
                :min="1"
                :step="1"
                v-model="proportionData.x"
              ></InputNumber>
              <span class="proportion-fenge" style="padding: 0px 5px">:</span>
              <InputNumber
                :max="99999"
                :min="1"
                :step="1"
                v-model="proportionData.y"
              ></InputNumber>
            </span>

            <!-- 视图类型 -->
            <Select
              v-model="addForm[item.prop]"
              :disabled="item.readonly"
              v-if="item.type === 'select' && item.prop === 'type'"
              :placeholder="$t('comm_select') + item.name"
              style="width: calc(100% - 20px)"
              @on-change="viewTypeChange"
            >
              <Option
                v-for="viewItem in viewType"
                :key="viewItem.value"
                :value="viewItem.value"
                >{{ viewItem.name }}</Option
              >
            </Select>
            <textarea
              v-model.trim="addForm[item.prop]"
              v-if="item.type === 'textarea'"
              maxlength="200"
              :placeholder="$t('comm_enter') + item.name"
            ></textarea>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button
            type="error"
            style="margin-right: 20px"
            @click="cancleClick"
            >{{ $t("common_cancel") }}</Button
          >
          <Button
            type="primary"
            :loading="submitLoading"
            @click="submitClick"
            >{{ $t("common_verify") }}</Button
          >
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
import { addDraggable } from "@/common/drag.js";
import validate from "@/common/validate.js";
import { countCharactersWithChineseWeight } from "@/units/validate";
export default {
  name: "viewList",
  data() {
    //视图名称校验

    const validaremarks = (rule, value, callback) => {
      if (value && value.length > rule.Length) {
        callback(new Error(this.$t("dash_length") + rule.Length));
      } else {
        callback();
      }
    };
    const validaRowColumn = (rule, value, callback) => {
      const num = /^[1-9]\d*$/;
      if (!value || value === "") {
        callback(new Error(this.$t("comm_enter") + rule.name));
      } else if (!num.test(value)) {
        callback(new Error(rule.name + this.$t("dashboard_integer")));
      } else if (num.test(value)) {
        if (Number(value) < rule.min || Number(value) > rule.max) {
          callback(
            new Error(
              rule.name +
                this.$t("dashboard_range") +
                rule.min +
                "～" +
                rule.max
            )
          );
        } else {
          callback();
        }
      }
    };

    // 验证 url  地址
    const validaUrl = (rule, value, callback) => {
      // debugger
      if (value == undefined || value === "") {
        callback(new Error(this.$t("comm_enter") + rule.name));
      } else if (!validate.validateURL(value)) {
        callback(new Error(this.$t("warning_URL_incorrect")));
      }else if (value.length > 0) {
        // 计算字符长度
        var size = countCharactersWithChineseWeight(value);
        if(size > rule.Length){
          callback(new Error(this.$t("dash_length") + rule.Length));
        }else{
          callback();
        }
      } else {
        callback();
      }
    };

    // 验证 屏幕  地址
    const validaProportion = (rule, value, callback) => {
      if (value == undefined || value === "") {
        // callback(new Error(this.$t("comm_select") + rule.name));
        // 非必填，直接校验通过
          callback();
      } else if (value == 5) {
        // 自定义屏幕
        const x = this.proportionData.x;
        const y = this.proportionData.y;
        if (x == null || x == undefined) {
          callback(new Error(this.$t("dash_scale_error")));
          return;
        }else{
           if (!/^[0-9]\d*$/.test(x)) {
            callback(new Error(this.$t("dash_scale_number_error")));
            return
          }
        }
        if (y == null || y == undefined) {
          callback(new Error(this.$t("dash_scale_error")));
          return;
        }else{

          if (!/^[0-9]\d*$/.test(y)) {
            callback(new Error(this.$t("dash_scale_number_error")));
            return
          }
        }

        callback();
      } else {
        callback();
      }
    };

    return {
      lang:localStorage.getItem('locale') || 'zh',
            currentSkin: sessionStorage.getItem('dark') || 1,
      //权限对象
      permissionObj: {},
      //按钮加载状态
      submitLoading: false,
      //表格加载状态
      loading: true,
      //表格数据
      tabsList: [],
      //选择的数据
      selectedIds: new Set(),
      //表格columns列
      tabsColumns: [
        {
          //多选
          type: "selection",
          width: 60,
          className: "bgColor",
          align: "center",
        },
        {
          title: this.$t("comm_name"),
          key: "name",
          align: "center",
          className: "bgColor",
        },
        {
          title: this.$t("dash_view_type"),
          key: "type",
          align: "center",
          className: "bgColor",
          render: (h, param) => {
            var items = this.viewType.filter((item) => {
              return item.value == param.row.type;
            });
            var name = "--";
            if (items.length > 0) {
              name = items[0].name;
            }
            return h("span", name);
          },
        },
        {
          title: this.$t("dash_rows"),
          key: "rowNum",
          align: "center",
          className: "bgColor",
          render: (h, param) => {
            var type = param.row.type;
            var name = "--";
            if (type != 2) {
              name = param.row.rowNum;
            }
            return h("span", name);
          },
        },
        {
          title: this.$t("dash_columns"),
          key: "colNum",
          align: "center",
          className: "bgColor",
          render: (h, param) => {
            var type = param.row.type;
            var name = "--";
            if (type != 2) {
              name = param.row.colNum;
            }
            return h("span", name);
          },
        },
        {
          title: this.$t("dash_scale"),
          key: "proportion",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            var type = params.row.type;
            var name = "--";
            if (type != 2) {
              if (params.row.proportion == 5) {
                // 自定义屏幕
                name = params.row.proportionScreenScale;
              } else {
                // 选择的屏幕
                let rowObject = this.viewScreenRatioAarray.filter((item) => {
                  return item.key == params.row.proportion;
                });
                name = rowObject[0].ratio;
              }
            }
            return h("span", name);
          },
        },
        {
          title: this.$t("dashboard_sort"),
          align: "center",
          className: "bgColor",
          slot: "orderAction",
        },
        {
          title: this.$t("comm_operate"),
          align: "center",
          className: "bgColor",
          key: "handleAction",
          render: (h, params) => {
            let tmp = this;
            return h("div", [
              h(
                "Tooltip",
                {
                  props: {
                    placement: "left-end",
                    size: "20",
                  },
                },
                [
                  h("span", {
                    class: "gl1-btn",
                    style: {
                      display: this.permissionObj.guolv
                        ? "inline-block"
                        : "none",
                    },
                    on: {
                      click: () => {
                        let that = this;
                        this.getById(params.row.id, function (res) {
                          if (!res || res.data == 0) {
                            that.$Message.warning({
                              content: this.$t("topo_data_deleted"),
                              background: true,
                            });
                            that.getList(that.query);
                            return;
                          }
                          that.filters = params.row;
                          that.showImport = true;
                          that.$refs.objFilter.setFilterId(params.row.id);
                        });
                      },
                    },
                  }),
                  h(
                    "span",
                    { slot: "content" },
                    this.$t("poto_object_filtering")
                  ),
                ]
              ),
              h(
                "Tooltip",
                {
                  props: {
                    placement: "left-end",
                    size: "20",
                  },
                },
                [
                  h("span", {
                    class: this.currentSkin == 1 ? "edit1-btn" : "light-edit1-btn",
                    style: {
                      display: this.permissionObj.update
                        ? "inline-block"
                        : "none",
                    },
                    on: {
                      click: () => {
                        tmp.edit(params.row);
                      },
                    },
                  }),
                  h("span", { slot: "content" }, tmp.$t("common_update")),
                ]
              ),
              h(
                "Tooltip",
                {
                  props: {
                    placement: "left-end",
                    size: "20",
                  },
                },
                [
                  h("span", {
                    class: "del1-btn",
                    style: {
                      display: this.permissionObj.delete
                        ? "inline-block"
                        : "none",
                    },
                    on: {
                      click: () => {
                        this.deleteTab(params.row);
                      },
                    },
                  }),
                  h("span", { slot: "content" }, this.$t("common_delete")),
                ]
              ),
            ]);
          },
        },
      ],
      //模态框状态(开启状态)
      modalShow: false,
      //模态框标题
      modalTitle: 1,
      // 视图模板数据
      templateList: [],
      // 自定义屏幕比例classname
      proportionClass: "proportion-default",
      proportionStyle: "width: calc(100% - 20px)",
      // 自定义屏幕比例
      proportionData: {
        x: 1920,
        y: 1080,
        show: false,
      },
      //新建参数
      addForm: {
        id: null,
        name: "",
        rowNum: "",
        colNum: "",
        proportion: null,
        remarks: "",
        type: 0,
        templateId: null,
        url: "",
      },
      fieldArray: [],
      //新建字段 type,colNum,rowNum,proportion
      addFields: [
        {
          name: this.$t("dash_template_name"),
          prop: "templateId",
          type: "select",
          key: 6,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dash_name"),
          prop: "name",
          type: "input",
          key: 0,
          show: true,
        },
        {
          name: this.$t("dash_view_type"),
          prop: "type",
          type: "select",
          key: 5,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dash_rows"),
          prop: "rowNum",
          type: "input",
          key: 1,
          show: true,
        },
        {
          name: this.$t("dash_columns"),
          prop: "colNum",
          type: "input",
          key: 2,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dash_scale"),
          prop: "proportion",
          type: "select",
          key: 3,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dash_description"),
          prop: "remarks",
          type: "textarea",
          key: 4,
          readonly: false,
          show: true,
        },

        {
          name: this.$t("dashboard_url_view"),
          prop: "url",
          type: "input",
          key: 7,
          readonly: false,
          show: false,
        },
      ],

      editFields: [
        {
          name: this.$t("dash_name"),
          prop: "name",
          type: "input",
          key: 0,
          show: true,
          readonly: false,
        },
        {
          name: this.$t("dash_view_type"),
          prop: "type",
          type: "select",
          key: 5,
          readonly: true,
          show: true,
        },
        {
          name: this.$t("dash_rows"),
          prop: "rowNum",
          type: "input",
          key: 1,
          show: true,
          readonly: false,
        },
        {
          name: this.$t("dash_columns"),
          prop: "colNum",
          type: "input",
          key: 2,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dash_scale"),
          prop: "proportion",
          type: "select",
          key: 3,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dash_description"),
          prop: "remarks",
          type: "textarea",
          key: 4,
          readonly: false,
          show: true,
        },
        {
          name: this.$t("dashboard_url_view"),
          prop: "url",
          type: "input",
          key: 7,
          readonly: false,
          show: false,
        },
      ],
      viewType: [
        { name: this.$t("dashboard_normal_view"), value: 0 },
        { name: this.$t("dashboard_polymerization_view"), value: 1 },
        { name: this.$t("dashboard_url_view"), value: 2 },
      ],
      //屏幕比例下拉数据
      viewScreenRatioAarray: [
        { ratio: "16:9", key: 1 },
        { ratio: "16:10", key: 2 },
        { ratio: "4:3", key: 3 },
        { ratio: "5:4", key: 4 },
        { ratio: this.$t("dash_other"), key: 5 },
        {ratio:'--',key:null}
      ],
      //验证规则
      rulesValidate: {},
      // 验证模板
      rulesValidateTemplate: {
        name: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            maxLength: 50,
            minLength: 1,
            validator: validate.validateStrContainsSpecialChars,
            name: this.$t("dash_name"),
          },
        ],
        rowNum: [
          {
            required: true,
            type: "string",
            name: this.$t("dash_rows"),
            min: 1,
            max: 10,
            trigger: "blur",
            validator: validaRowColumn,
          },
        ],
        colNum: [
          {
            required: true,
            type: "string",
            name: this.$t("dash_columns"),
            min: 1,
            max: 10,
            trigger: "blur",
            validator: validaRowColumn,
          },
        ],
        proportion: [
          {
           
            name: this.$t("dash_scale"),
            // message: this.$t("dash_scale"),
            trigger: "change",
            validator: validaProportion,
          },
        ],
        remarks: [
          {
            type: "string",
            Length: 200,
            trigger: "blur",
            validator: validaremarks,
          },
        ],
        type: [
          {
            required: true,
            type: "number",
            message: this.$t("dashboard_select_type"),
            trigger: "change",
          },
        ],
         url: [
          {
            required: true,
            name: this.$t("dashboard_url_view"),
            type: "string",
            Length: 200,
            validator: validaUrl,
            trigger: "blur",
          },
        ],
      },
      // url 验证案列
      rulesValidateUrl: {
        name: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            maxLength: 20,
            minLength: 1,
            validator: validate.validateStrContainsSpecialChars,
            name: this.$t("dash_name"),
          },
        ],
        type: [
          {
            required: true,
            type: "number",
            message: this.$t("dashboard_select_type"),
            trigger: "blur",
          },
        ],
        url: [
          {
            required: true,
            name: this.$t("dashboard_url_view"),
            type: "string",
            Length: 200,
            validator: validaUrl,
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    let permission = this.$route.query;
    this.permissionObj = permission;
    if (this.permissionObj.sort === false) {
      this.tabsColumns.splice(5, 1);
    }
    this.fieldArray = this.addFields;
    this.rulesValidateTemplate.url = null
    this.rulesValidate = this.rulesValidateTemplate;
    this.getTabsList();
    this.getTemplateList();
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 屏幕比例下拉
    proportionChange(value) {
      // 自定义屏幕 默认的 class 名称
      if (value === 5) {
        // 只有新增的时候，才需要恢复到默认值
        if (this.modalTitle == 1 || (this.proportionData.x == 1 &&  this.proportionData.y == 1)) {
          this.proportionData.x = 1920;
          this.proportionData.y = 1080;
        }
        this.proportionData.show = true;
        this.proportionClass = "proportion-custom";
        this.proportionStyle = "width: calc(100% - 200px)";
      } else {
        this.proportionData.x = 1;
        this.proportionData.y = 1;
        this.proportionData.show = false;
        this.proportionClass = "proportion-default";
        this.proportionStyle = "width: calc(100% - 20px)";
      }
    },
    // 视图类型 修改
    viewTypeChange(value) {
      // debugger
      // url 视图
      if (value === 2) {
        this.rulesValidateTemplate.url = this.rulesValidateUrl.url
        // hide
        var fieldCols = [
          "templateId",
          "colNum",
          "rowNum",
          "proportion",
          "remarks",
        ];
        // 需要隐藏这个控件的显示
        this.fieldArray.forEach((item) => {
          if (fieldCols.indexOf(item.prop) >= 0) {
            item.show = false;
          } else {
            item.show = true;
          }
        });
        console.log(this.fieldArray,'fieldArray');
        // 不存在URl 就添加
        // this.rulesValidate = {}
       
        // this.rulesValidate.
       
        this.rulesValidate.remarks = null
        this.rulesValidate.proportion = null
        // this.rulesValidate.url = this.rulesValidateUrl.url
        console.log(this.rulesValidate,'rulesValidate');
        this.addForm.rowNum = "1";
        this.addForm.colNum = "1";
      }  else if (value === 1) {
        // 聚合图形，不显示模板属性
        // hide
        var fieldCols = [
          "templateId",
          "url"
        ];
        this.rulesValidateTemplate.url = null
        // 需要隐藏这个控件的显示
        this.fieldArray.forEach((item) => {
          if (fieldCols.indexOf(item.prop) >= 0) {
            item.show = false;
          } else {
            item.show = true;
          }
        });
        this.rulesValidate = this.rulesValidateTemplate;
        // 只有新增的时候，才需要清空这个数据
        if (this.modalTitle == 1) {
          this.addForm.rowNum = "";
          this.addForm.colNum = "";
          this.addForm.url = "";
        }
      } else {
        this.rulesValidateTemplate.url = null
        // 其他的视图全部显示
        this.fieldArray.forEach((item) => {
          if ("url" === item.prop) {
            item.show = false;
          } else {
            item.show = true;
          }
        });
        // 只有新增的时候，才需要清空这个数据
        if (this.modalTitle == 1) {
          this.addForm.rowNum = "";
          this.addForm.colNum = "";
          this.addForm.url = "";
        }

        this.rulesValidate = this.rulesValidateTemplate;
      }
    },
    // 模板选择
    templateChange(value) {
      console.log(value);
      // 选择的模板
      if (value) {
        var fieldCols = ["type", "colNum", "rowNum", "proportion"];
        const templateData = this.templateList.filter((item) => {
          return item.id === value;
        });
        if (templateData && templateData.length > 0) {
          if (this.modalTitle == 1) {
            // 新建
            // 设置控件是否可读
            this.fieldArray.forEach((item) => {
              if (fieldCols.indexOf(item.prop) >= 0) {
                item.readonly = true;
              } else {
                item.readonly = false;
              }
            });
          }

          var dataForm = templateData[0];
          this.addForm.name = dataForm.name;
          this.addForm.rowNum = dataForm.rowNum;
          this.addForm.colNum = dataForm.colNum;
          this.addForm.proportion = dataForm.proportion;
          this.addForm.remarks = dataForm.remarks;
          this.addForm.type = dataForm.type;
        }
      } else {
        // 全部可读写
        this.fieldArray.forEach((item) => {
          item.readonly = false;
        });
        this.addForm.name = "";
        this.addForm.rowNum = "";
        this.addForm.colNum = "";
        this.addForm.proportion = this.viewScreenRatioAarray[0].key;
        this.addForm.remarks = "";
        this.addForm.type = 0;
      }
    },
    //返回主页面
    goBack() {
      this.$router.go(-1);
    },
    //添加视图
    addTab() {
      // this.rulesValidate.rowNum[0].min = 0;
      // this.rulesValidate.colNum[0].min = 0;
      this.addForm.id = null;
      this.addForm.templateId = "";
      this.addForm.name = "";
      this.addForm.rowNum = "";
      this.addForm.colNum = "";
      this.addForm.proportion = this.viewScreenRatioAarray[0].key;
      this.addForm.remarks = "";
      this.addForm.url = "";
      this.addForm.type = 0;
      this.modalTitle = 1;
      this.modalShow = true;
      this.fieldArray = this.addFields;
      this.rulesValidate = this.rulesValidateTemplate;
      this.viewTypeChange(this.addForm.type);
      this.templateChange("");
      this.proportionChange(this.addForm.proportion);
    },
    //删除视图
    deleteTab(type) {
      let ids = "";
      if (type === "select") {
        //多选删除
        let selectDatas = Array.from(this.selectedIds);
        if (selectDatas.length < 1) {
          this.$Message.warning({
            content: this.$t("group_select_delete"),
            background: true,
          });
          return;
        } else {
          for (let i = 0; i < selectDatas.length; i++) {
            if (i === 0) {
              ids += selectDatas[i].id;
            } else {
              ids += "," + selectDatas[i].id;
            }
          }
        }
      } else {
        //行内删除
        ids = type.id;
      }
      top.window.$iviewModal.confirm({
        title: this.$t("message_reminder"),
        content: this.$t("dash_delete"),
        onOk: () => {
          this.$http
            .post("/dashboard/view/delete", { ids: ids })
            .then(({ code, data, msg }) => {
              if (code === 1) {
                this.$Message.success({
                  content: this.$t("dash_delete_view_succ"),
                  background: true,
                });
                this.selectedIds = new Set();
              } else {
                this.$Message.warning(msg);
              }
            })
            .finally(() => {
              this.loading = false;
              this.getTabsList();
            })
            .catch(() => {
              this.$Message.warning({
                content: this.$t("common_delete_failed"),
                background: true,
              });
              throw new Error();
            });
        },
        onCancel: () => {},
      });
    },
    //上移
    upMove(row, index) {
      if (index === 0) {
        this.$Messages.warning({
          content: this.$t("dash_top"),
          background: true,
        });
        return;
      }
      let param = { sortType: 1, id: row.id };
      this.sortFunc(param);
    },
    //下移
    downMove(row, index) {
      if (index === this.tabsList.length - 1) {
        this.$Messages.warning({
          content: this.$t("dash_end"),
          background: true,
        });
        return;
      }
      let param = { sortType: 2, id: row.id };
      this.sortFunc(param);
    },
    //排序接口
    sortFunc(param) {
      this.loading = true;
      this.$http
        .post("/dashboard/view/sort", param)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.getTabsList();
          } else {
            this.$Message.warning({ content: msg, background: true });
          }
        })
        .finally(() => {
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$Message.warning({
            content: this.$t("message_network_failure"),
            background: true,
          });
          throw new Error();
        });
    },
    //修改
    edit(row) {
      this.addForm.id = row.id;
      this.addForm.name = row.name;
      this.addForm.rowNum = row.rowNum;
      this.addForm.colNum = row.colNum;
      this.addForm.proportion = row.proportion;
      this.addForm.remarks = row.remarks;
      this.addForm.type = row.type;
      this.addForm.url = row.url;
      this.addForm.templateId = "";
      this.modalTitle = 2;
      this.modalShow = true;
      this.fieldArray = this.editFields;
      this.rulesValidate = this.rulesValidateTemplate;

      const scale = row.proportionScreenScale;
      if (scale) {
        var scaleStrs = scale.split(":");
        this.proportionData.x = Number(scaleStrs[0]);
        this.proportionData.y = Number(scaleStrs[1]);
      }
      this.viewTypeChange(this.addForm.type);
      this.proportionChange(this.addForm.proportion);
    },
    getTemplateList() {
      this.loading = true;
      this.$http
        .post("/dashboard/view/findTemplateList", {})
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.templateList = data;
          } else {
            this.templateList = [];
          }
        })
        .finally(() => {
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //获取tabs列表数据
    getTabsList() {
      let param = { type: 1 };
      this.loading = true;
      this.$http
        .post("/dashboard/view/list", param)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.tabsList = data.sort(this.compare("sortNum"));
            //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
            let _that = this;
            setTimeout(function () {
              let objData = _that.$refs.viewList.$refs.tbody.objData;
              for (let key in objData) {
                if (_that.selectedIds.has(objData[key].id)) {
                  objData[key]._isChecked = true;
                }
              }
            }, 0);
          } else {
            this.$Message.warning({ content: msg, background: true });
          }
        })
        .finally(() => {
          this.loading = false;
        })
        .catch(() => {
          this.$Messages.warning({
            content: this.$t("message_network_failure"),
            background: true,
          });
          throw new Error();
        });
    },
    //全选与全不选
    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.viewList.data;
        let selectDatas = Array.from(this.selectedIds);
        for (let i = 0; i < selectDatas.length; i++) {
          data.forEach((item) => {
            if (selectDatas[i].id === item.id) {
              selectDatas.splice(i, 1);
            }
          });
        }
        this.selectedIds = new Set(selectDatas);
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item);
        });
      }
    },
    //逐条单选
    handleSelect(slection, row) {
      this.selectedIds.add(row);
    },
    //逐条取消
    handleCancel(slection, row) {
      // this.selectedIds.delete(row);
      let selectDatas = Array.from(this.selectedIds);
      for (let i = 0; i < selectDatas.length; i++) {
        if (selectDatas[i].id === row.id) {
          selectDatas.splice(i, 1);
        }
      }
      this.selectedIds = new Set(selectDatas);
    },
    //新建取消
    cancleClick() {
      this.$refs["addViewForm"].resetFields();
      this.modalShow = false;
    },
    //新建,修改确认
    submitClick() {
      console.log(this.rulesValidate,'需要校验的内容')
      this.submitLoading = true;
      console.log(this.addForm,'addForm')
      debugger
      this.$refs["addViewForm"].validate((valid) => {
        if (valid) {
          const submitParam = Object.assign(this.addForm, {});
          let url = "/dashboard/view/add";
          if (this.modalTitle === 2) {
            url = "/dashboard/view/update";
          }

          // 自定义屏幕比例
          submitParam.proportionScreenScale = null;
          if (submitParam.proportion == 5) {
            submitParam.proportionScreenScale = [
              this.proportionData.x,
              this.proportionData.y,
            ].join(":");
          }

          this.$http
            .post(url, submitParam)
            .then(({ code, data, msg }) => {
              if (code === 1) {
                this.modalShow = false;
                this.$Message.success({
                  content:
                    this.modalTitle === 2
                      ? this.$t("comm_changed_successful")
                      : this.$t("comm_operation_create_successfully"),
                  background: true,
                });
                this.getTabsList();
              } else {
                this.$Message.warning({ content: msg, background: true });
              }
            })
            .finally(() => {
              this.submitLoading = false;
              this.loading = false;
            })
            .catch(() => {
              this.submitLoading = false;
              throw new Error();
            });
        } else {
          this.submitLoading = false;
        }
      });
    },
    //根据某个字段排序
    compare(prop) {
      return (obj1, obj2) => {
        let val1 = obj1[prop];
        let val2 = obj2[prop];
        if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
          val1 = Number(val1);
          val2 = Number(val2);
        }
        if (val1 < val2) {
          return -1;
        } else if (val1 > val2) {
          return 1;
        } else {
          return 0;
        }
      };
    },
  },
  mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    //模态框拖拽
    addDraggable();
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
};
</script>

<style scoped>
.proportion-fenge {
  padding: 0px 5px;
}

.proportion-default {
  width: calc(100% - 20px);
}

.proportion-custom {
  width: calc(100% - 190px);
}
</style>

<style scoped lang="less">
.content {
  padding: 20px;
  background: var(--contentBox_bgcolor, #fff);
}
.buttonBox {
  text-align: right;
  margin-bottom: 20px;
  .userDefined {
    height: 38px;
    margin-left: 5px;
    color: white;
  }
  .add {
    background: #2d8cf0;
  }
}

/deep/.ivu-divider-horizontal {
  margin: 10px 0;
}
.btnSpan {
  color: #2d8cf0;
  cursor: pointer;
  margin-left: 8px;
}
.addForm {
  input,
  select,
  textarea {
    width: calc(100% - 20px);
    border: 1px solid #dcdee2;
    padding-left: 8px;
    border-radius: 4px;
  }
  input:focus,
  textarea:focus {
    border-color: #57a3f3;
  }
}

// 设置仪表盘 屏幕比例样式
.dashboard-view-form {
  /deep/.proportion-fenge {
    padding: 0px 5px;
  }

  /deep/.proportion-default {
    width: calc(100% - 20px);
  }

  /deep/.proportion-custom {
    width: calc(100% - 190px);
  }
}

.btnSty {
  margin-left: 10px !important;
}
</style>