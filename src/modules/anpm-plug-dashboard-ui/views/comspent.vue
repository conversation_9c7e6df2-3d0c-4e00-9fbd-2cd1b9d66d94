<template>
  <div style="width: 100%; height: 100%">
    <div v-if="loading" class="loading-overlay"></div>
    <iframe
      ref="asas"
      class="iframeElement"
      :id="iframeId"
      :src="loadSrc"
      :name="
        paramsData.pageUrl +
        (isbigscreen ? 'bigscreen' : '') +
        ('?intervalTime=' +
          paramsData.intervalTime +
          '&&topoId=' +
          paramsData.topoId +
          '&&limitNum=' +
          paramsData.alarmListNum +
          '&&graphicType=' +
          paramsData.graphicType +
          '&&graphicList=' +
          JSON.stringify(paramsData.graphicList) +
          '&&monitorObjList=' +
          JSON.stringify(paramsData.monitorObjList) +
          '&&provinceCode=' +
          JSON.stringify(paramsData.provinceCode || 100000) +
          '&&pathtopoType=' +
          paramsData.pathtopoType) +
        '&&groupIds=' +
        paramsData.groupIds +
        '&&mapId=' +
        paramsData.id +
        '&&componentId=' +
        paramsData.id +
        '&&statisticsType=' +
        paramsData.alarmStatisticsType +
        '&&_t=111' +
        '&&topoInfos=' +
        JSON.stringify(paramsData.topoInfos) +
        '&&taskPresentationType=' +
        paramsData.taskPresentationType +
        '&&componentId=' +
        paramsData.id +
        '&&componentRealAlarmConfigs=' +
        JSON.stringify(paramsData.componentRealAlarmConfigs)
      "
      frameborder="0"
      style="width: 100%; overflow: auto"
      :style="'height:100%;'"
      scrolling="no"
      @load="onIframeLoad"
    >
    </iframe>
  </div>
</template>

<script>
export default {
  name: "coms",
  props:{
    paramsData:{
      type:Object,
      default:function () {
        return {}
      }
    },
    time:{
      type:[String,Number],
      default:function (){
        return 4
      }
    },
    height:{
      type:[String,Number],
      default:function (){
        return 140
      }
    }
  },
  data(){
    return {
      loading: true, // 添加 loading 状态
      iframeItem:{},
      //是否处于大屏界面
      isbigscreen:false,
      iframeId:null,
      loadSrc:null,
      iframeName:null,
    }
  },
  watch:{
    paramsData:{
      handler(val){
      
        this.iframeItem = Object.assign(val);
        this.iframeId = 'dash_'+val.viewId+'_'+val.id+'_'+val.code;
        this.reloadIframe();
      },
      deep:true,
      immediate:true,
    },
    time:{
      handler(val){
        this.reloadIframe();
      },
      deep:true,
      immediate:true,
    }
  },
  created(){
    if(window.name.indexOf('bigscreen')>-1){
      this.isbigscreen = true;
    }else{
      this.isbigscreen = false;
    }
  },
  methods:{
    onIframeLoad() {
      this.loading = false; // iframe 加载完成后隐藏 loading 状态
    },

    //获取是本地
    getNetWorkAddress(){
      return top.window.location.hostname
    },
    //中文转码
    escapeFun(data){
      return escape(data)
    },
    //重新加载iframe
    reloadIframe(){
        // 存入字符串
      console.log(this.paramsData,'this.paramsData----------------------------------------')
      if (this.paramsData.typeCode == 'real_time_monitor') {
        let idArr =[]
        if (this.paramsData.monitorObjList?.length>0) {
            this.paramsData.monitorObjList.forEach(item => {
              idArr.push(item.objId)
            });
        }
        this.loadSrc = this.getNetWorkAddress() ===`localhost` ? (`/anpm-plug-`+this.paramsData.pageUrl+`-ui.html?param=`+ this.escapeFun(JSON.stringify(idArr))):(`/`+this.paramsData.pageUrl+`?param=`+this.escapeFun(JSON.stringify(idArr)));
      }else if (this.paramsData.typeCode == 'url_link'){
        // 处理 url 连接视图
        this.loadSrc = this.paramsData.url;
      }else if (this.paramsData.typeCode == "monitor_target_spread_map"){
        let params = {
          id: this.paramsData.id,
          intervalTime: this.paramsData.intervalTime,
        }
        
        this.loadSrc = this.getNetWorkAddress() ===`localhost` ? (`/anpm-plug-`+this.paramsData.pageUrl+`-ui.html?param=`+ this.escapeFun(JSON.stringify(params))):(`/`+this.paramsData.pageUrl+`?param=`+this.escapeFun(JSON.stringify(params)));
      }else{
        this.loadSrc = this.getNetWorkAddress() ===`localhost` ? (`/anpm-plug-`+this.paramsData.pageUrl+`-ui.html?param=`+ this.escapeFun(JSON.stringify(this.paramsData))):(`/`+this.paramsData.pageUrl+`?param=`+this.escapeFun(JSON.stringify(this.paramsData)));
      }
      },
  },
  beforeDestroy(){
    console.log('beforeDestroy')
     if (this.$refs.asas) {
      this.$refs.asas.src = 'about:blank';
      this.$refs.asas = null;
    }
    
  },
}
</script>

<style scoped>
body {
  background-color: white !important; /* 或者您想要的任何颜色 */
}

.loading-overlay {
  position: absolute;
  top: 1;
  left: 1;
  width: 100%;
  height: 100%;
  background: var(
    --dashboard_component_loading_bg_color,
    #000000
  ); /* 半透明背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}
</style>