<template>
  <section class="sectionBox nodeTop">
    <div class="title">{{$t('dash_quality_nodes')}}</div>
    <div class="section-body">
      <div class="section-body-content">
        <div>
          <Loading :loading="loading"></Loading>
          <Table
            ref="tableList"
            border
            :columns="columns"
            :data="tableList"
            :no-data-text="
              loading ? '' : tableList.length > 0 ? '' : $t('alarm_no_data')
            "
            size="small"
          ></Table>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import "@/config/page.js";
  import global from "@/common/global.js";
  import { mapGetters, mapActions, mapState } from "vuex";
  export default {
    name: "snmpOid",
    data() {
      return {
        //搜索字段
        query: {
          limitNum: 10, //状态
          pageNo: 1, //页数
          pageSize: 10, //每页展示多少数据
          groupIds:''
        },
        //loading状态
        loading: false,
        //表格数据
        tableList: [],
        columns: [
        {
            title: this.$t('dash_node_ip'),
            key: "nodeIp",
            align: "center"
          },
          {
            title: this.$t('dash_node_name'),
            key: "nodeName",
            align: "center"
          },
          {
            title: this.$t('comm_org'),
            key: "clientName",
            align: "center"
          },
          {
            title: this.$t('server_node_type'),
            key: "nodeType",
            sortable: true,
            width: 150,
            align: "center"
          },
          {
            title: this.$t('comm_interrup_total'),
            key: "brokenDuration",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 180,
            align: "center",
            render: (h, params) => {
              let str = params.row.brokenDuration;
              return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                  ? "--"
                  : this.handleDuration(str)
              );
            }
          },
          {
            title: this.$t('comm_interrup_times'),
            key: "brokenTimes",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 160,
            align: "center"
          },
          {
            title: this.$t('comm_link_de_long_time'),
            key: "degradationDuration",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 180,
            align: "center",
            render: (h, params) => {
              let str = params.row.degradationDuration;
              return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                  ? "--"
                  : this.handleDuration(str)
              );
            }
          },
          {
            title: this.$t('comm_deterioration_times'),
            key: "degradationTimes",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 160,
            align: "center"
          }
        ]
      };
    },
    created() {
        this.getList(this.query);
    },
    methods: {     
      getList(param) {
        //OID列表查询请求
        let _self = this;
        _self.$http.wisdomPost("/home/<USER>", param).then(res => {
          _self.loading = true;
          if (res.code === 1) {
              _self.tableList = this.getTop(res.data);
          }
        }).catch((err)=>{
          console.log(err);
          _self.loading = false;
        }).finally(()=>{
          _self.loading = false;
        });
      },
      //四个类型取前五
      getTop(data) {
        let brokenTimesTop = [],
          brokenDurationTop = [],
          degradationDurationTop = [],
          degradationTimesTop = [],
          conect = [];
        brokenTimesTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("brokenTimes")
        ); //中断次数排序
        brokenDurationTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("brokenDuration")
        ); //中断时长排序
        degradationDurationTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("degradationDuration")
        ); //时延时长排序
        degradationTimesTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("degradationTimes")
        ); //时延次数排序
        //长度大于5，取前五条，否则取全部
        if (brokenTimesTop.length >= 5) {
          brokenTimesTop = brokenTimesTop.slice(0, 5);
        }
        if (brokenDurationTop.length >= 5) {
          brokenDurationTop = brokenDurationTop.slice(0, 5);
        }
        if (degradationDurationTop.length >= 5) {
          degradationDurationTop = degradationDurationTop.slice(0, 5);
        }
        if (degradationTimesTop.length >= 5) {
          degradationTimesTop = degradationTimesTop.slice(0, 5);
        }
        //合并数组（每个排序项top5数组）
        conect = [
          ...brokenTimesTop,
          ...brokenDurationTop,
          ...degradationDurationTop,
          ...degradationTimesTop
        ];
        //去除重复项
        let hash = {};
        const newListArr = conect.reduceRight((item, next) => {
          hash[next.nodeIp] ? "" : (hash[next.nodeIp] = true && item.push(next));
          return item;
        }, []);
        let newList = newListArr.sort(function(pre, next) {
          //默认以中断时长排序
          return -(pre.brokenDuration - next.brokenDuration);
        });
        //返回新的数组List
        return newList;
      },
      compare(prop) {
        return function(pre, next) {
          let val1 = pre[prop];
          let val2 = next[prop];
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1);
            val2 = Number(val2);
          }
          if (val1 < val2) {
            return 1;
          } else if (val1 > val2) {
            return -1;
          } else {
            return 0;
          }
        };
      },
      handleDuration(data) {
        //时间处理函数
        // let newData = data.map(item=>{
        var theTime = parseInt(data); // 需要转换的时间秒
        var theTime1 = 0; // 分
        var theTime2 = 0; // 小时
        var theTime3 = 0; // 天
        if (theTime > 60) {
          theTime1 = parseInt(theTime / 60);
          theTime = parseInt(theTime % 60);
          if (theTime1 > 60) {
            theTime2 = parseInt(theTime1 / 60);
            theTime1 = parseInt(theTime1 % 60);
            if (theTime2 > 24) {
              //大于24小时
              theTime3 = parseInt(theTime2 / 24);
              theTime2 = parseInt(theTime2 % 24);
            }
          }
        }
        var result = "";
        if (theTime == 0) {
          result = "" + parseInt(theTime);
        }
        if (theTime == 0 && (theTime1 > 0 || theTime2 > 0 || theTime3 > 0)) {
          result = "" + parseInt(theTime) + 's';
        }
        if (theTime > 0) {
          result = "" + parseInt(theTime) + 's';
        }
        if (theTime1 > 0) {
          result = "" + parseInt(theTime1) + "m" + result;
        }
        if (theTime2 > 0) {
          result = "" + parseInt(theTime2) + "h" + result;
        }
        if (theTime3 > 0) {
          result = "" + parseInt(theTime3) + "d" + result;
        }
        // item.durations=result;
        return result;
        // });
        // return newData
      }
    }
  };
</script>

<style>
  .searchTop .ivu-select-input {
    height: 32px;
    line-height: 32px !important;
  }
  .fault-tab .dialTest-tab-content .ivu-table th.bgColor {
    background: #f1f6fe !important;
  }
  .task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
    height: auto !important;
    min-height: 32px;
  }
  .multipleSelect .ivu-select-input {
    padding: 0 !important;
  }
</style>
<style scoped lang="less">
  .nodeTop{
    padding:0 20px;
    .title{
      font-size: 16px;
      text-align: left;
      font-weight: bold;
      line-height: 38px;
    }
  }
</style>
