<template>
  <section
    :class="[
      'dashboardSection',
      isbigscreen ? 'bigscreen' : '',
      currentSkin == 1 ? 'dark-skin' : 'light-skin',
    ]"
    :style="getSectionStyle()"
  >
    <Tabs
      v-if="!isbigscreen"
      :type="currentSkin == 1 ? 'card' : 'line'"
      id="aa"
      :value="valueName"
      :class="{
        'tabs-card-content-black': currentSkin == 1,
        'white-tabs-bar': currentSkin == 0,
      }"
      :animated="isUseAnimation"
      capture-focus
      @on-click="tabsClick"
    >
      <TabPane
        v-for="tab in tabs"
        :key="tab.key"
        :name="JSON.stringify(tab)"
        :label="tab.name"
      >
        <div
          :class="{ tabsBody: true, 'white-tabsBody': currentSkin == 0 }"
          v-if="currentViewComponent.length > 0 && tab.name === checkedTab"
        >
          <table
            class="viewTable"
            border="0"
            :style="
              'width:' +
              currentClientWidth +
              'px;height:' +
              currentClientHeight +
              'px'
            "
          >
            <tr>
              <th
                v-for="rowItem in colArray"
                :key="rowItem.rowIndex"
                :style="'width:' + currentClientWidth / colArray.length + 'px;'"
              ></th>
            </tr>
            <tr v-for="rowItem in rowArray" :key="rowItem.rowIndex">
              <td
                v-for="columnItem in rowItem.columns"
                :key="columnItem.columnKey"
                :colspan="columnItem.colspan"
                :rowspan="columnItem.rowspan"
                :style="
                  'width:' +
                  (currentClientWidth / tab.colNum) * columnItem.colspan +
                  'px;height:' +
                  (currentClientHeight / tab.rowNum) * columnItem.rowspan +
                  'px;background:transparent!important'
                "
              >
                <div
                  v-for="iframeItem in currentViewComponent"
                  :key="iframeItem.key"
                  v-if="
                    rowItem.rowIndex == iframeItem.startRow &&
                    columnItem.columIndex == iframeItem.startCol
                  "
                  style="width: 100%; height: 100%"
                >
                  <div
                    :class="{ box: true, 'white-box': currentSkin == 0 }"
                    style="width: 100%"
                    @mouseenter="handleMouseEnter"
                    @mouseleave="handleMouseLeave"
                  >
                    <!-- 头部 -->
                    <div
                      :class="['tabsBodyItem', 'dashitem']"
                      style="width: 100%"
                      v-if="
                        iframeItem.noShowFrame != 1 ||
                        iframeItem.noShowName != 1
                      "
                    >
                      <div
                        :class="[
                          'titleDiv',
                          iframeItem.noShowFrame == 1
                            ? 'box-title-no-show'
                            : '',
                        ]"
                      >
                        <div
                          :class="[
                            'itemHeader',
                            'dashheader',
                            iframeItem.noShowName == 0 ? 'show' : 'no-show',
                          ]"
                        >
                          <img
                            :src="currentSkin == 0 ? lightArrow : rightArrow"
                            alt=""
                          />

                          <p
                            style="height: 2px"
                            :style="
                              'display:' +
                              (iframeItem.typeCode == 'aggre_gation'
                                ? 'block'
                                : 'none')
                            "
                          ></p>
                          <p
                            class="title"
                            :style="
                              iframeItem.typeCode == 'aggre_gation'
                                ? 'line-height:32px;z-index:1000;position:relative;border-bottom:1px solid var(--dash_h_b_color,#f4f6f9)'
                                : ''
                            "
                          >
                            {{ iframeItem.name }}
                          </p>
                        </div>
                        <div class="title-right">
                          <img
                            :src="titleIcon"
                            :class="[
                              iframeItem.noShowName == 0 ? 'show' : 'no-show',
                            ]"
                            alt=""
                            style="width: 50px; height: 10px; margin-top: 3px"
                          />
                          <!-- <div class="line"></div> -->
                        </div>
                      </div>
                    </div>
                    <!-- /头部 -->
                    <!-- 内容 -->
                    <div
                      :class="[
                        'box-body',
                        iframeItem.noShowFrame == 1 ? 'box-body-no-show' : '',
                      ]"
                    >
                      <div
                        :class="[
                          'itemBox',
                          iframeItem.noShowFrame == 1 ? 'box-no-padding' : '',
                        ]"
                        :style="
                          iframeItem.pageUrl === 'dashtimelywarn'
                            ? 'height: calc(100% - 21px)'
                            : 'height:100%'
                        "
                      >
                        <span
                          v-show="
                            !isbigscreen &&
                            (iframeItem.pageUrl === 'dashtimelywarn' ||
                              iframeItem.pageUrl === 'dashphystopo' ||
                              (iframeItem.pageUrl === 'dashpathtopo' &&
                                iframeItem.topoInfos.length < 2))
                          "
                          :style="{
                            paddingRight: '8px',
                            float: 'right',
                            cursor: 'pointer',
                            color: '#0290fd',
                            marginTop: currentSkin == 1 ? 'auto' : '-20px',
                          }"
                          @click="goPage(iframeItem)"
                          >{{ $t("comm_view_more") }}>
                        </span>
                        <Loading
                          style="top: 35px"
                          :loading="loading[iframeItem.pageUrl]"
                        ></Loading>
                        <coms
                          :paramsData="
                            Object.assign(iframeItem, {
                              queryTime: timeQuery(dateValue),
                              queryTimeType: dateValue,
                            })
                          "
                          :time="dateValue"
                          :height="
                            (currentClientHeight / tab.rowNum) *
                              columnItem.rowspan -
                            30
                          "
                          :key="iframeItem.pageUrl + iframeItem.id + iframeTime"
                        ></coms>
                      </div>
                    </div>
                    <!-- /内容 -->
                    <!-- 底部 -->
                    <div
                      v-if="iframeItem.noShowFrame != 1"
                      :class="[
                        'box-foot',
                        iframeItem.noShowFrame == 1 ? 'box-foot-no-show' : '',
                      ]"
                    ></div>
                    <!-- /底部 -->
                  </div>
                </div>
              </td>
            </tr>
          </table>
        </div>
        <div v-else style="height: 400px; width: 100%">
          <div :class="currentSkin == 1 ? 'noComponent' : 'lightNoComponent'">
            <p>{{ $t("dash_configured") }}</p>
          </div>
        </div>
      </TabPane>
      <div
        class="section-top-text"
        slot="extra"
        style="display: inline-block; width: 180px; vertical-align: middle"
        v-if="
          permissionObj.edit &&
          editViewData.status != 1 &&
          editViewData.type == 1
        "
        v-show="tabs.length > 0 && !isbigscreen"
      >
        <div class="section-text" style="height: 32px">
          <select-item
            readonly
            v-model="dateValue"
            :list="date_list"
            @on-change="dateChange"
          ></select-item>
        </div>
      </div>
      <Button
        v-show="!isbigscreen"
        v-if="permissionObj.list"
        class="userDefined"
        @click="viewList"
        slot="extra"
      >
        {{ $t("dash_view_list") }}</Button
      >
      <Button
        class="userDefined"
        slot="extra"
        v-show="!isbigscreen"
        v-if="permissionObj.editPopComponent"
        @click="editPopComponent"
      >
        {{ $t("edit_pop_component") }}
      </Button>
      <Button
        class="userDefined"
        v-if="permissionObj.edit"
        v-show="tabs.length > 0 && !isbigscreen && showEditBtn"
        @click="viewHandle"
        slot="extra"
        >{{ $t("dash_edit") }}</Button
      >
      <!--v-if="permissionObj.edit && tab.status !=1"-->
      <span
        class="bigscale"
        v-if="!isbigscreen"
        :title="$t('but_full_screen')"
        @click="viewScale"
        slot="extra"
      >
        <img
          :src="
            currentSkin == 0
              ? require('../../../assets/dashboard/icon-dp.png')
              : require('../../../assets/dashboard/icon-screen01.png')
          "
          alt=""
        />
        <!-- <img src="../../../assets/dashboard/iframeIcon.png" alt=""> -->
      </span>
      <span
        class="userDefined bigscale"
        v-if="isbigscreen"
        :title="$t('but_exit_full_screen')"
        @click="viewScale"
        slot="extra"
        ><img src="../../../assets/dashboard/scalesmall.svg" alt=""
      /></span>
      <!--<div slot="extra" id="asdas" style="width: 60px;height:30px;background-color: yellow">放大</div>-->
    </Tabs>
    <div
      class="mask"
      v-if="$store.state.tipStatus == 'true' && currentSkin == 1"
    ></div>
    <!-- 大屏代码 -->
    <div v-if="isbigscreen" id="bb" style="width: 100%; height: 100%">
      <!-- 大屏头部 -->
      <div
        class="bigScreenTitle"
        :style="{
          height: calculateHeaderHeight + 'px',
          lineHeight: calculateHeaderHeight + 'px',
          fontSize: calculateHeaderFont + 'px',
          color: '#a2d2fe',
          position: 'relative',
          marginBottom: '20px',
        }"
      >
        <!-- <div class="bigScreenbg bigScreenLeft" :style="'width: calc(50% - '+bigScreenMiddleWidth/2+'px)'"></div>
        <div class="bigScreenbg bigScreenMiddle" :style="'width: '+bigScreenMiddleWidth+'px'"></div>
        <div class="bigScreenbg bigScreenRight" :style="'width: calc(50% - '+bigScreenMiddleWidth/2+'px)'"></div> -->
        <div class="biscreenScaleSelect" v-if="bigScreenView.type == 1">
          <select-item
            readonly
            v-model="bigdateValue"
            :isbigscreen="isbigscreen"
            :list="date_list"
            @on-change="bigdateChange"
          ></select-item>
        </div>
        <div
          :style="{
            width: '100%',
            lineHeight: calculateHeaderHeight + 'px',
            position: 'absolute',
            top: '0',
            color: '#fff',
            fontWeight: 'bold',
          }"
        >
          {{ bigScreenName }}
        </div>
        <!-- 大屏时间 大屏时间 -->
        <div
          class="big-time"
          :style="{
            height: (calculateHeaderFont * 14) / 26 + 'px',
            fontSize: (calculateHeaderFont * 14) / 26 + 'px',
          }"
        >
          <!-- 年月日 -->
          <span style="margin-right: 10px"
            >{{ currentTime.getFullYear() }}-{{
              formatNumber(currentTime.getMonth() + 1)
            }}-{{ formatNumber(currentTime.getDate()) }}</span
          >
          <!-- 星期 -->
          <span style="margin-right: 10px"
            >星期{{ weekDayMap[currentTime.getDay()] }}</span
          >
          <!-- 时分秒 -->
          <span style="color: #05eeff"
            >{{ formatNumber(currentTime.getHours()) }}:{{
              formatNumber(currentTime.getMinutes())
            }}:{{ formatNumber(currentTime.getSeconds()) }}</span
          >
        </div>
        <!-- 大屏时间结束 -->
        <div
          class="bigscale bigScreenExit"
          style="
            position: absolute;
            right: 1%;
            top: 10%;
            z-index: 999;
            background: transparent;
          "
          v-if="isbigscreen"
          :title="$t('but_exit_full_screen')"
          @click="viewScale"
          slot="extra"
        >
          <img
            src="../../../assets/dashboard/quit-screen.png"
            :alt="$t('but_exit_full_screen')"
          />
        </div>
      </div>

      <!-- /大屏头部结束 -->
      <div class="tabsBody" v-if="bigcurrentViewComponent.length > 0">
        <table
          class="viewTable"
          border="0"
          :style="'width:' + bigwidth + 'px;'"
        >
          <tr>
            <th
              v-for="rowItem in colArray"
              :key="rowItem.rowIndex"
              :style="'width:' + bigwidth / colArray.length + 'px;'"
            ></th>
          </tr>

          <tr v-for="rowItem in bigrowArray" :key="rowItem.rowIndex">
            <td
              v-for="columnItem in rowItem.columns"
              :key="columnItem.columnKey"
              :colspan="columnItem.colspan"
              :rowspan="columnItem.rowspan"
              :style="
                'width:' +
                (bigwidth / bigCol) * columnItem.colspan +
                'px;height:' +
                ((bigheight / bigRow) * columnItem.rowspan - 20) +
                'px;background:transparent!important'
              "
            >
              <div
                v-for="iframeItem in bigcurrentViewComponent"
                :key="iframeItem.key"
                v-if="
                  rowItem.rowIndex == iframeItem.startRow &&
                  columnItem.columIndex == iframeItem.startCol
                "
                style="width: 100%; height: 100%; text-align: left"
              >
                <div class="box" style="width: 100%">
                  <div
                    v-if="iframeItem.noShowFrame != 1"
                    :class="['tabsBodyItem', 'dashitem']"
                    style="width: 100%"
                  >
                    <div
                      :class="[
                        'titleDiv',
                        iframeItem.noShowFrame == 1 ? 'box-title-no-show' : '',
                      ]"
                    >
                      <div
                        :class="[
                          'itemHeader',
                          'dashheader',
                          iframeItem.noShowName == 0 ? 'show' : 'no-show',
                        ]"
                      >
                        <img
                          :src="currentSkin == 0 ? lightArrow : rightArrow"
                          alt=""
                        />
                        <p
                          class="title"
                          :class="
                            iframeItem.typeCode == 'aggre_gation'
                              ? 'bigTitle'
                              : ''
                          "
                        >
                          {{ iframeItem.name }}
                        </p>
                      </div>
                      <div
                        class="title-right"
                        v-if="iframeItem.noShowFrame != 1"
                      >
                        <img
                          :src="titleIcon"
                          :class="[
                            iframeItem.noShowName == 0 ? 'show' : 'no-show',
                          ]"
                          alt=""
                          style="width: 50px; height: 10px; margin-top: 3px"
                        />
                        <!-- <div class="line"></div> -->
                      </div>
                    </div>
                  </div>
                  <div
                    :class="[
                      'box-body',
                      iframeItem.noShowFrame == 1 ? 'box-body-no-show' : '',
                    ]"
                  >
                    <div
                      :class="[
                        'itemBox',
                        iframeItem.noShowFrame == 1 ? 'box-no-padding' : '',
                      ]"
                      @mouseenter="handleMouseEnter"
                      @mouseleave="handleMouseLeave"
                      :style="'height:100%'"
                    >
                      <Loading :loading="loading[iframeItem.pageUrl]"></Loading>
                      <coms
                        :paramsData="
                          Object.assign(iframeItem, {
                            queryTime: timeQuery(bigdateValue),
                            queryTimeType: bigdateValue,
                          })
                        "
                        :time="bigdateValue"
                        :height="(bigheight / bigRow) * columnItem.rowspan - 50"
                        :key="iframeItem.pageUrl + iframeItem.id"
                      ></coms>
                    </div>
                  </div>
                  <div
                    v-if="iframeItem.noShowFrame != 1"
                    :class="[
                      'box-foot',
                      iframeItem.noShowFrame == 1 ? 'box-foot-no-show' : '',
                    ]"
                  ></div>
                </div>
              </div>
            </td>
          </tr>
        </table>
      </div>
      <!-- /大屏内容结束 -->
      <div v-else style="height: 100%; width: 100%">
        <div
          :class="currentSkin == 1 ? 'noComponent' : 'lightNoComponent'"
        ></div>
        <p>{{ $t("dash_configured") }}</p>
      </div>

      <div class="tipsBox">
        <!-- 中断告警弹窗 -->
        <transition name="moveR">
          <div class="modal-backdrop tipsModalStyle1" v-show="tipsModal">
            <div class="modal-header">
              <h3>{{ $t("dash_interrupt_alarm") }}</h3>
              <Icon
                type="ios-close"
                size="31"
                color="#fff"
                @click="tipsModal = false"
              />
            </div>
            <div class="modal-body">
              <ul>
                <li
                  v-for="(item, index) in breakList"
                  :key="index"
                  class="alarmTypeColor1"
                >
                  <i class="alarmSign"></i>
                  <div class="tipsModalRight">
                    <div class="alarmTime">
                      <Icon type="md-time" size="20" />
                      <span>{{ item.faultStartTime || "--" }}</span>
                    </div>
                    <div class="alarmTipsTitle">
                      <div>
                        <label class="alarmType">{{ returnLabel(item) }}</label>
                        <span class="alarmIp"
                          >{{ item.errorIps | ipGet
                          }}{{
                            item.recoveryed == "0"
                              ? "（" + $t("common_recovered") + "）"
                              : "（" + $t("common_unrecovered") + "）"
                          }}</span
                        >
                      </div>
                    </div>
                    <div>
                      <p
                        v-if="item.faultDesc != null"
                        style="word-break: break-all"
                      >
                        {{ item.faultDesc }}
                      </p>
                      <p v-else style="text-align: center">
                        {{ $t("dash_no_content") }}
                      </p>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <!-- <div class="modal-footer">
              <Button class="btnConfirm" @click="handleFault">去处理</Button>
            </div> -->
          </div>
        </transition>

        <!-- 劣化告警弹窗 -->
        <transition name="moveR">
          <div class="modal-backdrop2 tipsModalStyle2" v-show="tipsModal2">
            <div class="modal-header">
              <h3>{{ $t("dash_deterioration_alarm") }}</h3>
              <Icon
                type="ios-close"
                size="31"
                color="#fff"
                @click="tipsModal2 = false"
              />
            </div>
            <div class="modal-body">
              <ul>
                <li
                  v-for="(item, index) in badList"
                  :key="index"
                  class="alarmTypeColor2"
                >
                  <i class="alarmSign"></i>
                  <div class="tipsModalRight">
                    <div class="alarmTime">
                      <Icon type="md-time" size="20" />
                      <span>{{ item.faultStartTime || "--" }}</span>
                    </div>
                    <div class="alarmTipsTitle">
                      <div>
                        <label class="alarmType">{{ returnLabel(item) }}</label>
                        <span class="alarmIp"
                          >{{ item.errorIps | ipGet
                          }}{{
                            item.recoveryed == "0"
                              ? "（" + $t("common_recovered") + "）"
                              : "（" + $t("common_unrecovered") + "）"
                          }}</span
                        >
                      </div>
                    </div>
                    <div>
                      <p
                        v-if="item.faultDesc != null"
                        style="word-break: break-all"
                      >
                        {{ item.faultDesc }}
                      </p>
                      <p v-else style="text-align: center">
                        {{ $t("dash_no_content") }}
                      </p>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <!-- <div class="modal-footer">
              <Button class="btnConfirm" @click="handleFault">去处理</Button>
            </div> -->
          </div>
        </transition>

        <!-- 提示信息弹窗 -->
        <transition name="moveR">
          <div class="modal-backdrop3 tipsModalStyle3" v-show="tipsModal3">
            <div class="modal-header">
              <h3>{{ $t("server_prompt_message") }}</h3>
              <Icon
                type="ios-close"
                size="31"
                color="#fff"
                @click="tipsModal3 = false"
              />
            </div>
            <div class="modal-body">
              <ul>
                <li
                  v-for="(item, index) in otherList"
                  :key="index"
                  class="alarmTypeColor3"
                >
                  <i class="alarmSign"></i>
                  <div class="tipsModalRight">
                    <div class="alarmTime">
                      <Icon type="md-time" size="20" />
                      <span>{{ item.faultStartTime || "--" }}</span>
                    </div>
                    <div class="alarmTipsTitle">
                      <div>
                        <label class="alarmType">{{ returnLabel(item) }}</label>
                        <!-- 专线告警、端口告警 才显示状态 -->
                        <span
                          v-if="
                            item.faultType == 9 ||
                            item.faultType == 10 ||
                            item.faultType == 11
                          "
                          class="alarmIp"
                        >
                          {{
                            item.recoveryed == 0
                              ? "（" + $t("common_recovered") + "）"
                              : "（" + $t("common_unrecovered") + "）"
                          }}</span
                        >
                      </div>
                    </div>
                    <div>
                      <p
                        v-if="item.faultDesc != null"
                        style="word-break: break-all"
                      >
                        {{ item.faultDesc }}
                      </p>
                      <p v-else style="text-align: center">
                        {{ $t("dash_no_content") }}
                      </p>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <!-- <div class="modal-footer">
              <Button class="btnConfirm" @click="handleFault">去处理</Button>
            </div> -->
          </div>
        </transition>
      </div>
      <audio
        src="../../../assets/audio/kaiguan.mp3"
        controls
        hidden
        muted
        ref="kaiguan"
      ></audio>
      <audio
        src="../../../assets/audio/zhongduan.mp3"
        controls
        hidden
        ref="audio"
      ></audio>
      <audio
        src="../../../assets/audio/liehua.mp3"
        controls
        hidden
        ref="audio2"
      ></audio>
      <audio
        src="../../../assets/audio/liehua.mp3"
        controls
        hidden
        ref="audio3"
      ></audio>
    </div>
    <!-- 大屏代码结束 -->
    <!-- 收缩节点弹框，路径拓扑在此显示 -->

    <CompleteNodes
      ref="completeNodes"
      v-if="completeNodesShow"
      :completeNodeData="completeNodeData"
      :containerWidth="containerHeight"
      :containerHeight="containerHeight"
      @closeNodeModal="completeNodesShow = false"
    ></CompleteNodes>
    <!-- 编辑弹窗构件 -->
    <PopComponentModal
      ref="PopComponentModal"
      :popComponentShow.sync="popComponentShow"
      :queryCodeList="queryCodeList"
      @editComponentSuccess="editComponentSuccess"
    ></PopComponentModal>

    <!-- /编辑弹窗构件 -->
    <!-- 告警弹窗 -->

    <div class="pop-box" v-if="popList.length > 0">
      <AlarmPop
        :popList="popList"
        :bodyWidth="bodyWidth"
        :popTitle="popTitle"
        @closePop="closePop"
      ></AlarmPop>
    </div>
    <!-- /告警弹窗 -->
  </section>
</template>

<script>
//二级iframe页面改变颜色函数
function skinChange(obj) {
    const property = Object.keys(obj);
    const color = Object.keys(obj).map(function (i) {
        return obj[i];
    });
    let root = document.documentElement;
    for (let i = 0; i < property.length; i++) {
        root.style.setProperty(property[i], color[i]);
    }
}
import selectItem from '@/common/select/index.vue';
import coms from './comspent';
import global from '@/common/global.js';
import locationreload from '@/common/locationReload';


export default {
    name: 'index',
    components: {
        coms,
        selectItem,
       CompleteNodes: () => import('@/common/topoG6/pathtopo/CompleteNodes.vue'),
       PopComponentModal: () => import('@/modules/anpm-plug-dashboard-ui/components/PopComponentModal.vue'),
       AlarmPop: () => import('@/modules/anpm-plug-dashboard-ui/components/AlarmPop.vue')
    },
    created() {
      this.currentSkin = Number(sessionStorage.getItem('dark'))
       this.containerHeight = window.innerWidth
       this.containerWidth = window.innerHeight
        // console.log(this.$store.state.tipStatus,'状态')
        // 如果在界面导航，页面静止点击
        // this.tipStatus = localStorage.getItem('tipStatus')
        // if(this.tipStatus == 'true') {
        //     console.log('禁止点击')
        // }
         window.addEventListener('message', e => {
        
           if(e.data.type == 'completeNodesShow'){
            this.updateCompleteNodesShow(e.data.data);
           }
        });
        window.loading = this.loading;
        window.isEdit = false;
        if (window.name.indexOf('bigscreen') > -1) {
          // 大屏暂时暗色
           sessionStorage.setItem('dark',1)
            this.isbigscreen = true;
            var name = window.name;
            var paramstr = name.split("bigscreen,")[1];
            var paramobj = JSON.parse(paramstr);
            console.log(paramobj,'paramobj')
            // debuggers
            this.bigRow = paramobj.rowNum;
            this.bigCol = paramobj.colNum;
            this.bigScreenView = paramobj;
            this.bigScreenName = paramobj.name;
            this.bigdateValue = paramobj.dateValue;
            const reg = /[^\x00-\xff]/g;
            let fontLength = this.bigScreenName.replace(reg, 'aa').length;
            this.bigScreenMiddleWidth = 13 * fontLength - 26 < 100 ? 100 : 13 * fontLength - 26;
        } else {
            this.isbigscreen = false;
           if (!this.isbigscreen && top.window.vm) {
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    }
        }
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        this.getTabs();
        this.getQueryCodeList(1)
    },
    watch: {
        window(value) { }
    },
    data() {
        return {
            valueName:'',
          popComponentShow: false,
          currentSkin: 1,
          iframeTime:0,
            // 控制是否在界面导航之中
            containerHeight:0,
            containerWidth:0,
           
            rightArrow: require('../../../assets/icon-zs.png'),
            lightArrow: require('../../../assets/light-skin-icon/img-b.png'),
            titleIcon: require('../../../assets/img-frame-icon.png'),
            tipsModal: false,
            tipsModal2: false,
            tipsModal3: false,
            // 中断告警
            statrList: {
                breakNews: false, //中断告警弹窗提示
                breakSound: false, //中断告警音效提示
                breakClose: false, //中断告警自动关闭
                breakKeep: false, //中断告警全屏保持
                // 劣化告警
                badNews: false, //劣化告警弹窗提示
                badSound: false, //劣化告警音效提示
                badClose: false, //劣化告警自动关闭
                badKeep: false, //劣化告警全屏保持
                // 提示告警
                otherNews: false, //提示告警弹窗提示
                otherSound: false, //提示告警音效提示
                otherClose: false, //提示告警自动关闭
                otherKeep: false //提示告警全屏保持
            },
            breakList: [],
            badList: [],
            otherList: [],
            maxId: '',
            brominId: '',
            degminId: '',

            //大屏标题宽度
            bigScreenMiddleWidth: 100,
            //当前选中的tab名称
            checkedTab: '',
            //权限对象
            permissionObj: {},
            tabname: {},
            bigScreenName: '',
            bigScreenView: {},
            rowArray: [],
            colArray: [],
            tableH: 0,
            //配置跳转参数
            pageList: {
                dashtimelywarn: {
                    level: 1,
                    url: 'alarmlist',
                    parentName: ''
                },
                dashpathtopo: {
                    level: 2,
                    url: 'pathtopo',
                    parentName: '拓扑管理'
                },
                dashfiberchart: {
                    level: 2,
                    url: 'pathtopo',
                    parentName: '拓扑管理'
                },
                dashphystopo: {
                    level: 2,
                    url: 'phystopo',
                    parentName: '拓扑管理'
                }
            },
            //加载
            loading: {
                dashtask: true,
                dashtaskalarm: true,
                dashrp: true,
                dashrpalarm: true,
                dashspec: true,
                dashspecalarm: true,
                dashgis: true,
                dashqualitytop: true,
                dashtimelywarn: true,
                dashpathtopo: true,
                dashfiberchart: true,
                dashphystopo: true,
                realtimemonitor: true
            },
            // 是否显示编辑视图的按钮
            showEditBtn:true,
            //高度
            height: 300,
            //是否处于大屏界面
            isbigscreen: false,
            windowWidth: 0,
            //视图标签tab
            tabs: [],
            //tabs是否使用动画
            isUseAnimation: false,
            //编辑视图参数
            editViewData: {},
            //当前视图列表构件列表
            currentViewComponent: [],
            //当前视图屏幕宽高比例
            currentClientWidth: 0,
            currentClientHeight: 0,
            //大屏参数
            bigwidth: 0,
            bigheight: 0,
            bigRow: 0,
            bigCol: 0,
            bigrowArray: [],
            bigcurrentViewComponent: [],
            bigRefreshTime: 5,
            bigSetInterval: null,
            //屏幕比例下拉数据
            viewScreenRatioAarray: [
                { width: 16, height: 9, key: 1 },
                { width: 16, height: 10, key: 2 },
                { width: 4, height: 3, key: 3 },
                { width: 5, height: 4, key: 4 }
            ],
            //路径列表
            typeList: [
                { code: 'route_topo', name: this.$t('topo_path_topology') },
                { code: 'physics_topo', name: '物理拓扑' },
                { code: 'timely_map', name: '及时地图' },
                { code: 'real_time_alarm', name: '实时告警' },
                { code: 'route_statistic', name: '路径统计分析图' },
                { code: 'route_alarm_trend', name: '最近30天路径告警走势' },
                { code: 'special_statistic', name: '专线统计分析图' },
                { code: 'special_alarm_trend', name: '最近30天专线告警走势' },
                { code: 'snmp_statistic', name: '中继统计分析图' },
                { code: 'snmp_alarm_trend', name: '最近30天中继告警走势' },
                { code: 'node_top_n', name: '最近一周质差节点TOPN' }
            ],
            dateValue: 4, //下拉时间选中
            bigdateValue: 4, //下拉时间选中
            date_list: [
                //时间窗口下拉数据
                {
                    value: 0,
                    label: this.$t('comm_today')
                },
                {
                    value: 1,
                    label: this.$t('comm_last_1hour')
                },
                {
                    value: 4,
                    label: this.$t('comm_last_4hour')
                },
                {
                    value: 8,
                    label: this.$t('comm_last_8hour')
                },
                {
                    value: 12,
                    label: this.$t('comm_last_hour_12')
                },
                {
                    value: 24,
                    label: this.$t('comm_last_24hour')
                }
            ],
            switchStateTime: null,
            logoImgUrl: '',
            systemName: '',
            imageBase64: '',
            completeNodeData:{},
            completeNodesShow:false,
              refreshTimer: null, // 添加刷新定时器
            queryCodeList:[],
            popTimer:null,
            popRefreshTime:null,
            popList:[],
            bodyWidth:document.documentElement.clientWidth,
            popTitle:'',
            isMouseEnter:false,
            viewData: {
              x:16,
              y:9
            },
             currentTime: new Date(),
            bigTimer: null,
              weekDayMap: ['日', '一', '二', '三', '四', '五', '六'],
               memoryTimer: null,
              // memoryThreshold: 4096, // 内存临界值(MB)
              memoryThreshold:1536,
              refimer:null,
        };
    },
    computed: {
       calculateHeaderHeight() {
    // 获取当前容器宽度（或使用window.innerWidth获取窗口宽度）
    const containerWidth = window.innerWidth;
    console.log(containerWidth,'containerWidth')
    // debugger
    
    // 根据原始比例1920:82计算高度
    const ratio = 82 / 1920; // 约0.0427
    const calculatedHeight = Math.round(containerWidth * ratio);
    console.log(calculatedHeight,'calculatedHeight')
    
    // 设置高度，最小不低于50px
    return Math.max(calculatedHeight, 50);
  },
  calculateHeaderFont() {
  // 获取当前容器或窗口宽度
  const currentWidth = document.getElementById('bb')?.clientWidth || window.innerWidth;
  
  // 计算与标准宽度(1920px)的比例
  const ratio = currentWidth / 1920;
  
  // 根据比例计算字体大小，基准是26px
  let fontSize = Math.round(26 * ratio);
  
  // 设置最小和最大字体大小限制，防止过小或过大
  fontSize = Math.max(fontSize, 16); // 最小不小于16px
  fontSize = Math.min(fontSize, 36); // 最大不大于36px
  
  // 返回计算出的字体大小
  return fontSize ;
}
},
    filters: {
       
        ipGet(val) {
            if (val != '' || val != null) {
                return val.split(',')[0];
            } else {
                return val;
            }
        }
    },
    methods: {
      
      // 检查内存使用情况
  checkMemoryUsage() {
    if (window.performance && window.performance.memory) {
      const usedMemory = window.performance.memory.usedJSHeapSize / (1024 * 1024); // 转换为MB
      console.log('当前内存使用:', usedMemory.toFixed(2) + 'MB');
      
      if (usedMemory > this.memoryThreshold) {
        console.log('内存占用超过阈值，准备刷新页面');
        // 刷新页面前保存必要的状态
       
        window.location.reload();
      }
    }
  },
  // 启动内存监控
  startMemoryMonitor() {
    this.memoryTimer = setInterval(() => {
      this.checkMemoryUsage();
    },  2 * 60 * 1000); // 每30分钟检查一次
  },
           // 添加设置刷新定时器的方法
  
      // / 更新时间方法
  updateTime() {
    // 在当前时间基础上加一秒
    this.currentTime = new Date(this.currentTime.getTime() + 1000);
    console.log(this.currentTime,'this.currentTime')
    console.log(new Date())
  },
  // 应急大屏产品经理需求更改显示时间为服务器时间
  async getCurrentTime() {
    try {
       const res = await this.$http.get('/home/<USER>')
       if(res.code == 1){
        let timestamp = res.data.timestamp
        // 确保时间戳是毫秒级的
        this.currentTime = new Date(Number(timestamp) * 1000)
       }else {
        this.currentTime = new Date();
       }
    }catch(error) {
      this.currentTime = new Date();
    }
  },
  
  // 格式化日期的辅助方法
  formatNumber(num) {
    return num < 10 ? '0' + num : num;
  },
      handleResize() {
    this.bigwidth = document.getElementById('bb').clientWidth;
    this.checkForScrollbar();
  },
  
  checkForScrollbar() {
    // 获取实际内容宽度
    const contentWidth = document.getElementById('bb').scrollWidth;
    const containerWidth = document.getElementById('bb').clientWidth;
    
    // 如果内容宽度大于容器宽度，调整样式
    if (contentWidth > containerWidth) {
      // 查找可能导致溢出的元素并调整
      const children = document.getElementById('bb').children;
      for (let i = 0; i < children.length; i++) {
        if (children[i].offsetWidth > containerWidth) {
          children[i].style.width = '100%';
          children[i].style.maxWidth = '100%';
        }
      }
    }
  },
    
      handleMouseEnter() {
       
       
        this.isMouseEnter = true;
         const iframes = document.querySelectorAll('iframe');
        iframes.forEach(iframe => {
          try {
            iframe.contentWindow.postMessage({ type: 'mouseEnterState', data: true }, '*');
          } catch (e) {
            console.log('Failed to send mouseEnter state to iframe', e);
          }
  });
      },
      handleMouseLeave() {
      
        this.isMouseEnter = false;
          const iframes = document.querySelectorAll('iframe');
         iframes.forEach(iframe => {
          try {
            iframe.contentWindow.postMessage({ type: 'mouseEnterState', data: false }, '*');
          } catch (e) {
            console.log('Failed to send mouseLeave state to iframe', e);
          }
  });
      },
      closePop() {
        this.popList = []
      },
      editComponentSuccess(params){
      
        console.log(params,'params')
        this.popTitle = params.name
        this.popRefreshTime = params.intervalTime
        if(!this.popTimer){
          this.startRefresh()
        }

      },
      startRefresh() {
        // 暂时
         this.getDashboardFrameComponentDetails()
      
        if(this.popTimer){
          clearInterval(this.popTimer)
            this.popTimer = null
          }
        // if(this.popRefreshTime == 0){
        //   // 不刷新，只请求一次
        //   this.getDashboardFrameComponentDetails()
        // }
        if(this.popRefreshTime && this.popRefreshTime > 0){
        this.popTimer = setInterval(() => {
          this.getDashboardFrameComponentDetails()
        }, this.popRefreshTime * 60 * 1000)
        }

      },
     async getDashboardFrameComponentDetails() {
     
      const res = await this.$http.post('/home/<USER>')

      if(res.code == 1){
        this.popList = res.data || []
       
        
       
      }


      },
       async getDashboardFrameComponentDetail(){
       
        const res = await this.$http.post('/home/<USER>',{type:this.queryCodeList[0].value})
        console.log(res,'res')
       
        if(res.code == 1){
          
          
           if(res.data && Object.keys(res.data).length > 0){
            this.popRefreshTime = res.data.intervalTime
            this.popTitle = res.data.name
            this.startRefresh()

           }
        }
       },
    async getQueryCodeList(type){
     
      const res = await this.$http.post('/dataTable/queryCode',{key:'frameComponentType'})
      //  debugger
      if(res.code == 1){
        this.queryCodeList = res.data
        if(type == 1){
          this.getDashboardFrameComponentDetail()
        }
      }


      },

      
      
     async editPopComponent(){
          this.getQueryCodeList(2)
        
        this.popComponentShow = true;
        // 获取弹框构件类型
    
       
       
       
      },
      getSectionStyle() {
        let style = {
          minHeight: !this.isbigscreen ? this.height + 'px' : '100%',
          padding: this.isbigscreen ? '0px' : this.currentSkin == 1 ? '20px' : '0px'
        }

    return style
      },
          handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
      updateCompleteNodesShow(data) {
        console.log(data,'data父组件的弹框')
        this.completeNodesShow = true
        this.completeNodeData = data
        
        
      },
         returnLabel(item) {
            // debugger
            // console.log(item , this , "------------------")
            let val = item.faultType;
            let faultList = global.faultList , _this = this;
            if (faultList.length > 0) {
                var lists = faultList.filter(item => item.value == 10);
                if (lists.length == 0) {
                    faultList.push({ value: 10, label: _this.$t('dash_port_congestion') });
                }
            }

            let nameObj = faultList.filter(item => item.value == val);
            if (nameObj.length != 0) {
                return nameObj[0].label;
            } else {
                return _this.$t('comm_other');
            }
        },
        getSystemInfo() {
            this.$http.post('/lisence/queryLogo').then(res => {
                if (res.code == 1) {
                    if (res.data) {
                        this.systemName = res.data.name;
                        this.logoImgUrl = res.data.path;
                        this.imageBase64 = res.data.image;
                    }

                    this.upateIcon();
                }
            });
        },
        // 修改浏览器导航图标
        upateIcon() {
            let link = document.querySelector('link[rel="icon"]');
            let imageBase64 = `data:image/png;base64,${this.imageBase64}`;
            // link.href = this.imageBase64 ? imageBase64 : require('@/assets/logo-title.png');
            link.href = this.imageBase64 ? imageBase64 : '';
        },
        //获取查询时间
        timeQuery(timeType) {
            let time = [],
                nowTime = new Date().getTime();
            switch (Number(timeType)) {
                case 0:
                    time = [new Date().format2('yyyy-MM-dd 00:00:00'), new Date().format2('yyyy-MM-dd HH:mm:ss')];
                    break;
                case 1:
                case 4:
                case 8:
                case 12:
                case 24:
                    time = [new Date(nowTime - timeType * 3600 * 1000).format2('yyyy-MM-dd HH:mm:ss'), new Date().format2('yyyy-MM-dd HH:mm:ss')];
                    break;
                default:
                    time = [new Date(nowTime - 4 * 3600 * 1000).format2('yyyy-MM-dd HH:mm:ss'), new Date().format2('yyyy-MM-dd HH:mm:ss')];
            }
            return time;
        },
        // 第一次进入页面获取提示开关状态
        getSwitchState() {
            this.$http.post('/fault/getPrompt').then(res => {
                if (res.code == 1) {
                    if (res.data != null) {
                        this.statrList.breakNews = res.data.pop_flag_break == 1 ? true : false;
                        this.statrList.breakSound = res.data.sound_flag_break == 1 ? true : false;
                        this.statrList.breakClose = res.data.self_closin_break == 1 ? true : false;
                        this.statrList.breakKeep = res.data.large_screen_hold_break == 1 ? true : false;

                        this.statrList.badNews = res.data.pop_flag_degradation == 1 ? true : false;
                        this.statrList.badSound = res.data.sound_flag_degradation == 1 ? true : false;
                        this.statrList.badClose = res.data.self_closin_degradation == 1 ? true : false;
                        this.statrList.badKeep = res.data.large_screen_hold_degradation == 1 ? true : false;

                        this.statrList.otherNews = res.data.pop_flag_else == 1 ? true : false;
                        this.statrList.otherSound = res.data.sound_flag_else == 1 ? true : false;
                        this.statrList.otherClose = res.data.self_closin_else == 1 ? true : false;
                        this.statrList.otherKeep = res.data.large_screen_hold_else == 1 ? true : false;

                        if (!this.statrList.breakKeep && !this.statrList.badKeep && !this.statrList.otherKeep) {
                            return;
                        } else {
                            if (this.statrList.breakSound || this.statrList.badSound || this.statrList.otherSound) {
                                // 浏览器相关安全限制，刷新页面判断是否开启关闭声音
                                this.$refs.kaiguan
                                    .play()
                                    .then(res => {
                                        this.$refs.kaiguan.muted = true;
                                        console.log('音频正常播放');
                                    })
                                    .catch(err => {
                                        this.$Modal.confirm({
                                            title: this.$t('comm_tip'),
                                            content:
                                                "<p>" + this.$t('dash_related') + "</p>",
                                            okText: this.$t('server_turn_on_sound'),
                                            cancelText: this.$t('server_turn_off_sound'),
                                            onOk: () => {
                                                this.soundSwitch(this.statrList.breakSound, 'break');
                                                this.soundSwitch(this.statrList.badSound, 'bad');
                                                this.soundSwitch(this.statrList.otherSound, 'other');
                                                this.$Message.success({ content: 'The prompt tone is on!', background: true });
                                            },
                                            onCancel: () => {
                                                this.soundSwitch(false, 'break');
                                                this.soundSwitch(false, 'bad');
                                                this.soundSwitch(false, 'other');
                                                this.$Message.warning({ content: 'The beep is off!', background: true });
                                            }
                                        });
                                        console.log('音频不能播放');
                                    });
                            }

                            if (this.statrList.breakSound || this.statrList.badSound || this.statrList.otherSound || this.statrList.breakNews || this.statrList.badNews || this.statrList.otherNews) {
                                this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
                            }
                        }
                    } else {
                        this.statrList.breakNews = true;
                        this.statrList.breakSound = true;
                        this.statrList.breakClose = false;
                        this.statrList.breakKeep = false;

                        this.statrList.badNews = true;
                        this.statrList.badSound = true;
                        this.statrList.badClose = true;
                        this.statrList.badKeep = false;

                        this.statrList.otherNews = true;
                        this.statrList.otherSound = true;
                        this.statrList.otherClose = true;
                        this.statrList.otherKeep = false;
                    }
                }
            });
        },
        // 后续每分钟获取提示开关状态
        getSwitchStates() {
            this.$http.post('/fault/getPrompt').then(res => {
                if (res.code == 1) {
                    if (res.data != null) {
                        this.statrList.breakNews = res.data.pop_flag_break == 1 ? true : false;
                        this.statrList.breakSound = res.data.sound_flag_break == 1 ? true : false;
                        this.statrList.breakClose = res.data.self_closin_break == 1 ? true : false;
                        this.statrList.breakKeep = res.data.large_screen_hold_break == 1 ? true : false;

                        this.statrList.badNews = res.data.pop_flag_degradation == 1 ? true : false;
                        this.statrList.badSound = res.data.sound_flag_degradation == 1 ? true : false;
                        this.statrList.badClose = res.data.self_closin_degradation == 1 ? true : false;
                        this.statrList.badKeep = res.data.large_screen_hold_degradation == 1 ? true : false;

                        this.statrList.otherNews = res.data.pop_flag_else == 1 ? true : false;
                        this.statrList.otherSound = res.data.sound_flag_else == 1 ? true : false;
                        this.statrList.otherClose = res.data.self_closin_else == 1 ? true : false;
                        this.statrList.otherKeep = res.data.large_screen_hold_else == 1 ? true : false;
                    } else {
                        this.statrList.breakNews = true;
                        this.statrList.breakSound = true;
                        this.statrList.breakClose = false;
                        this.statrList.breakKeep = false;

                        this.statrList.badNews = true;
                        this.statrList.badSound = true;
                        this.statrList.badClose = true;
                        this.statrList.badKeep = false;

                        this.statrList.otherNews = true;
                        this.statrList.otherSound = true;
                        this.statrList.otherClose = true;
                        this.statrList.otherKeep = false;
                    }
                }

                 // 判断是否弹窗提示
                if (!this.statrList.breakNews) {
                  setTimeout(() => {
                    this.tipsModal = false;
                  }, 100);
                }

                if (!this.statrList.badNews) {
                  setTimeout(() => {
                    this.tipsModal2 = false;
                  }, 100);
                }

                if (!this.statrList.otherNews) {
                  setTimeout(() => {
                    this.tipsModal3 = false;
                  }, 100);
                }
            });
        },
        // 1分钟获取一次告警数据
        getMinuteData() {
            if (this.statrList.breakNews || this.statrList.breakSound || this.statrList.badNews || this.statrList.badSound || this.statrList.otherNews || this.statrList.otherSound) {
                setTimeout(() => {
                    if (!this.tipsModal) {
                        this.brominId = '';
                        this.breakList = [];
                    }
                    if (!this.tipsModal2) {
                        this.degminId = '';
                        this.badList = [];
                    }
                    if (!this.tipsModal3) {
                        this.otherList = [];
                    }
                    let param = {
                        id: this.maxId,
                        brominId: this.brominId,
                        degminId: this.degminId
                    };
                    this.$http.wisdomPost('/fault/getFaultPrompt', param).then(res => {
                        if (res.code == 1) {
                            let resData = res.data;
                            if (resData.total >= 1) {
                                // 获取最大ID
                                // let totalArr = [...resData.broPro,...resData.degPro,...resData.hintPro]
                                // totalArr = totalArr.map(e=>e.id)
                                // let repeatId = Array.from(new Set(totalArr));
                                // this.maxId = Math.min(...repeatId);
                                // 中断数据处理
                                if (resData.broPro.length >= 1) {
                                    // let broProArr = []
                                    // 改变新返回数据id相同的恢复状态
                                    // resData.broPro.filter((item) =>{
                                    //   // broProArr.push(item.id);
                                    //   this.breakList.filter(e=>{
                                    //     if(item.id == e.id){
                                    //       if(item.recoveryed == '0'){
                                    //         e.recoveryed = '0';
                                    //       }else{
                                    //         e.recoveryed = '1';
                                    //       }
                                    //     }
                                    //   })
                                    // })
                                    // let idFrom = Array.from(new Set(broProArr))
                                    // this.brominId = Math.min(...idFrom);
                                    // 删除新返回的数据与弹窗数据相同id的数据重新赋值resData的数据
                                    // resData.broPro = resData.broPro.filter((item) =>{
                                    //   let id = this.breakList.map(v=>v.id)
                                    //   return !id.includes(item.id)
                                    // })
                                    // resData.broPro.forEach((item) =>{
                                    //   if(this.statrList.breakKeep){
                                    //     this.breakList.unshift(item)
                                    //   }
                                    // })
                                    // this.brominId = this.breakList[this.breakList.length - 1].id

                                    this.breakList = resData.broPro;
                                    // 直接取最小的值
                                    let broProAttr = resData.broPro.map(e => e.id);
                                    let broProId = Array.from(new Set(broProAttr));
                                    this.brominId = Math.min(...broProId);
                                }

                                // 劣化数据处理
                                if (resData.degPro.length >= 1) {
                                    // let degProArr = []
                                    // resData.degPro.filter((item) =>{
                                    //   // degProArr.push(item.id);
                                    //   this.badList.filter(e=>{
                                    //     if(item.id == e.id){
                                    //       if( item.recoveryed == '0'){
                                    //       e.recoveryed = '0';
                                    //       }else{
                                    //         e.recoveryed = '1';
                                    //       }
                                    //     }
                                    //   })
                                    // })
                                    // let idFrom = Array.from(new Set(degProArr))
                                    // this.degminId = Math.min(...idFrom);
                                    // resData.degPro = resData.degPro.filter((item) =>{
                                    //   let id = this.badList.map(v=>v.id)
                                    //   if( item.recoveryed == '0'){
                                    //       item.recoveryed = '0';
                                    //       }else{
                                    //         item.recoveryed = '1';
                                    //       }
                                    //   return !id.includes(item.id)
                                    // })
                                    // resData.degPro.forEach(item=>{
                                    //   if(this.statrList.badKeep){
                                    //     this.badList.unshift(item)
                                    //   }
                                    // })

                                    // this.degminId = this.badList[this.badList.length - 1].id

                                    this.badList = resData.degPro;

                                    // 直接取最小的值
                                    let degProAttr = resData.degPro.map(e => e.id);
                                    let degProId = Array.from(new Set(degProAttr));

                                    this.degminId = Math.min(...degProId);
                                }

                                if (resData.hintPro.length >= 1) {
                                    this.otherList = resData.hintPro;

                                    let hintProAttr = resData.hintPro.map(e => e.id);
                                    let hintProId = Array.from(new Set(hintProAttr));
                                    this.maxId = Math.min(...hintProId);

                                    // resData.hintPro.forEach(item=>{
                                    //   if(this.statrList.otherKeep){
                                    //     this.otherList.unshift(item)
                                    //   }
                                    // })
                                }

                                // 中断
                                if (this.breakList.length > 0 && this.statrList.breakNews) {
                                    let listLen = this.breakList.length;
                                    if (listLen > 100) {
                                        this.breakList.splice(0, 100);
                                    }
                                    this.tipsModal = true;
                                    if (this.statrList.breakClose) {
                                        setTimeout(() => {
                                            this.tipsModal = false;
                                        }, 30000);
                                    }
                                }
                                // 劣化
                                if (this.badList.length > 0 && this.statrList.badNews) {
                                    let listLen = this.badList.length;
                                    if (listLen > 100) {
                                        this.badList.splice(0, 100);
                                    }
                                    this.tipsModal2 = true;
                                    if (this.statrList.badClose) {
                                        setTimeout(() => {
                                            this.tipsModal2 = false;
                                        }, 30000);
                                    }
                                }
                                // 提示
                                if (this.otherList.length > 0 && this.statrList.otherNews) {
                                    let listLen = this.otherList.length;
                                    if (listLen > 100) {
                                        this.otherList.splice(0, 100);
                                    }
                                    this.tipsModal3 = true;
                                    if (this.statrList.otherClose) {
                                        setTimeout(() => {
                                            this.tipsModal3 = false;
                                        }, 30000);
                                    }
                                }

                                if (this.statrList.breakSound && resData.broPro.length > 0 && this.statrList.breakKeep) {
                                    this.$refs.audio.currentTime = 0;
                                    this.$refs.audio.muted = false;
                                    this.$refs.audio.play(); //播放
                                } else if (this.statrList.badSound && resData.degPro.length > 0 && this.statrList.badKeep) {
                                    this.$refs.audio2.currentTime = 0;
                                    this.$refs.audio2.muted = false;
                                    this.$refs.audio2.play(); //播放
                                } else if (this.statrList.otherSound && resData.hintPro.length > 0 && this.statrList.otherKeep) {
                                    this.$refs.audio3.currentTime = 0;
                                    this.$refs.audio3.muted = false;
                                    this.$refs.audio3.play(); //播放
                                }
                            }
                        }
                    });
                }, 5000);
            }
        },
        // 语言提醒开起关闭
        soundSwitch(val, type) {
            if (type == 'break') {
                this.statrList.breakSound = val;
                if (val) {
                    if (!this.statrList.badSound && !this.statrList.otherSound && !this.statrList.breakNews && !this.statrList.badNews && !this.statrList.otherNews) {
                        this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
                    }
                } else {
                    if (!this.statrList.badSound && !this.statrList.otherSound && !this.statrList.breakNews && !this.statrList.badNews && !this.statrList.otherNews) {
                        clearTimeout(this.getTimelyData);
                        this.getTimelyData = null;
                        this.maxId = '';
                        this.brominId = '';
                        this.degminId = '';
                        this.$refs.audio.pause(); //关闭
                    }
                }
            }
            if (type == 'bad') {
                this.statrList.badSound = val;
                if (val) {
                    if (!this.statrList.breakSound && !this.statrList.otherSound && !this.statrList.breakNews && !this.statrList.badNews && !this.statrList.otherNews) {
                        this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
                    }
                } else {
                    if (!this.statrList.breakSound && !this.statrList.otherSound && !this.statrList.breakNews && !this.statrList.badNews && !this.statrList.otherNews) {
                        clearTimeout(this.getTimelyData);
                        this.getTimelyData = null;
                        this.maxId = '';
                        this.brominId = '';
                        this.degminId = '';
                        this.$refs.audio.pause(); //关闭
                    }
                }
            }
            if (type == 'other') {
                this.statrList.otherSound = val;
                if (val) {
                    if (!this.statrList.breakSound && !this.statrList.badSound && !this.statrList.breakNews && !this.statrList.badNews && !this.statrList.otherNews) {
                        this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
                    }
                } else {
                    if (!this.statrList.breakSound && !this.statrList.badSound && !this.statrList.breakNews && !this.statrList.badNews && !this.statrList.otherNews) {
                        clearTimeout(this.getTimelyData);
                        this.getTimelyData = null;
                        this.maxId = '';
                        this.brominId = '';
                        this.degminId = '';
                        this.$refs.audio.pause(); //关闭
                    }
                }
            }
            this.$refs.audio.currentTime = 0; //从头开始播放提示音
            this.setSwitchState(this.statrList);
        },
        // 设置语音和弹窗开关状态
        setSwitchState(param) {
            let params = {
                popFlagBreak: param.breakNews ? 1 : 0,
                soundFlagBreak: param.breakSound ? 1 : 0,
                selfClosinBreak: param.breakClose ? 1 : 0,
                largeScreenHoldBreak: param.breakKeep ? 1 : 0,

                popFlagDegradation: param.badNews ? 1 : 0,
                soundFlagDegradation: param.badSound ? 1 : 0,
                selfClosinDegradation: param.badClose ? 1 : 0,
                largeScreenHoldDegradation: param.badKeep ? 1 : 0,

                popFlagElse: param.otherNews ? 1 : 0,
                soundFlagElse: param.otherSound ? 1 : 0,
                selfClosinElse: param.otherClose ? 1 : 0,
                largeScreenHoldElse: param.otherKeep ? 1 : 0
            };

            this.$http.post('/fault/setPrompt', params).then(res => {
                console.log(res, msg);
            });
        },

        // // 告警处理跳转
        // handleFault(){
        //   window.close()
        // },

        //改变宽度
        changeWidth() {
            console.log('改变宽度');
            if (this.isbigscreen) {
                this.bigwidth = document.getElementById('bb').clientWidth;
            } else {
                this.currentClientWidth = document.getElementById('aa').clientWidth - 7;
            }
            this.height = top.document.body.clientHeight - 100;
        },
        //获取tabs
        getTabs() {
            this.$http
                .post('/dashboard/view/list')
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        if (data && data.length > 0) {
                            data.map(item => {
                                switch (item.proportion) {
                                   case null:
                                    // 获取实际屏幕宽高比
                                    let screenWidth = window.screen.width;
                                    let screenHeight = window.screen.height;
                                   
                                    
                                    // 计算最大公约数以简化比例
                                    const gcd = (a, b) => {
                                        return b === 0 ? a : gcd(b, a % b);
                                    };
                                    let divisor = gcd(screenWidth, screenHeight);
                                    let x1 = screenWidth / divisor;
                                    let y1 = screenHeight / divisor;
                                    console.log(x1,y1,'x1,y1')
                                    
                                    item.windowResolution = x1 + ":" + y1;
                                    item.percentw = x1;
                                    item.percenth = y1;
                                    this.viewData.x = x1;
                                    this.viewData.y = y1;
                                   
                                    break;
                                    case 1:
                                        item.windowResolution = '16:9';
                                        item.percentw = 16;
                                        item.percenth = 9;
                                        this.viewData.x = 16;
                                        this.viewData.y = 9;
                                        break;
                                    case 2:
                                        item.windowResolution = '16:10';
                                        item.percentw = 16;
                                        item.percenth = 10;
                                        this.viewData.x = 16;
                                        this.viewData.y = 10;
                                        break;
                                    case 3:
                                        item.windowResolution = '4:3';
                                        item.percentw = 4;
                                        item.percenth = 3;
                                        this.viewData.x = 4;
                                        this.viewData.y = 3;
                                        break;
                                    case 4:
                                        item.windowResolution = '5:4';
                                        item.percentw = 5;
                                        item.percenth = 4;
                                        this.viewData.x = 5;
                                        this.viewData.y = 4;
                                        break;
                                    case 5:
                                    default:
                                        var x = 16 , y = 9;
                                        // 其他比例
                                        if(item.proportionScreenScale){
                                           let screenScales =  item.proportionScreenScale.split(":");
                                           if(screenScales.length >= 2){
                                                x = Number(screenScales[0]);
                                                y = Number(screenScales[1]);
                                           }
                                        }
                                        item.windowResolution = x+":"+y;
                                        item.percentw = x;
                                        item.percenth = y;
                                        this.viewData.x = x;
                                        this.viewData.y = y;
                                        break;
                                }
                            });
                            // debugger
                            // 如果第一个 视图是 URL 视图 就不显示 编辑视图按钮
                            const item = data[0];
                            this.showUrlViewEditBtn(item);
                            this.tabs = data;
                        } else {
                            this.tabs = [];
                            let time = 3;
                            this.$Message.loading({
                                title: this.$t('comm_jump_prompt'),
                                content: this.$t('comm_configuration'),
                                duration: time
                            });
                            setTimeout(() => {
                                this.$router.push({ path: '/dashboardList' });
                            }, 3000);
                        }
                    } else {
                        console.log(msg);
                    }
                })
                .catch(() => { })
                .finally(() => {
                    if (window.name.indexOf('bigscreen') > -1) {
                        var name = window.name;
                        var paramstr = name.split("bigscreen,")[1];
                        let id = JSON.parse(paramstr).id,
                            haveList = true,
                            param = JSON.parse(paramstr);
                        

                        try {
                            this.tabs.forEach(item => {
                                if (item.id == id) {
                                    haveList = true;
                                    param = item;
                                    throw new Error('跳出循环');
                                } else {
                                    haveList = false;
                                }
                            });
                        } catch (e) {
                            console.log('成功跳出来了');
                        }
                        if (haveList) {
                            this.tabsClick(param);
                        } else {
                            this.$Message.warning({ content: this.$t('warning_view_deleted'), background: true });
                        }
                    } else {
                      
                            this.tabsClick(this.tabs[0]);
                        
                    }
                });
        },
        //视图列表方法
        viewList() {

            this.$router.push({ path: '/dashboardList', query: this.permissionObj });
            top.window.vm.$data.preMenu = this.$t('dash_panel');
            top.window.vm.$data.activeName = this.$t('dash_view_list')
        },
        //编辑视图
        viewHandle() {
            // console.log(this.editViewData,'啥--------------------')
            this.$router.push({ path: '/dashboardEdit', query: this.editViewData });
            top.window.vm.$data.preMenu = this.$t('dash_panel');
            top.window.vm.$data.activeName = this.$t('dash_panel_edit') + this.checkedTab;
        },
        //大屏切换
        viewScale() {
            if (!this.isbigscreen) {
              // 大屏默认暗色 
              // sessionStorage.setItem('dark',1)
                let param = Object.assign({ dateValue: this.dateValue }, this.editViewData);
                var big = top.open(window.location.hostname === 'localhost' ? '/anpm-plug-dashboard-ui.html' : '/dashboard', 'bigscreen,' + JSON.stringify(param));
                 big.onload = function() {
                  big.sessionStorage.setItem('dark', '1');
        }
            } else {
                window.close();
            }
        },
        showUrlViewEditBtn(nameObj){
            // URL 视图不显示编辑视图按钮
            if(nameObj.type == 2){
                this.showEditBtn = false;
            }else{
                this.showEditBtn = true;
            }
        },
        //tabs点击切换事件
        tabsClick(name) {
           
            console.log('name', name);
            let name2 = '';
            typeof name === 'object' ? name2 = JSON.stringify(name)  : name2 = name;
          

            // debugger
            this.valueName =name2;
          this.iframeTime = new Date().getTime();
            this.colArray = []
            this.dateValue = 4;
            window['view'] = `view_${name.id}_${name.type}_${this.dateValue}`;
            if (this.isbigscreen) {
                this.bigrowArray = [];
                let nameObj = JSON.parse(JSON.stringify(name));
                if (typeof name === 'string') {
                    nameObj = JSON.parse(name);
                }
                let windowResolution = nameObj.windowResolution;
                let width = windowResolution.split(':')[0],
                    height = windowResolution.split(':')[1];
                this.bigwidth = document.getElementById('bb').clientWidth - 7;
                this.bigheight = (this.bigwidth / width) * height - this.calculateHeaderHeight - 20;
                // this.widthRatio =
                //初始化表格
                for (let i = 0; i < nameObj.rowNum; i++) {
                    this.bigrowArray.push({ rowIndex: i + 1, columns: [] });
                    for (let column = 0; column < nameObj.colNum; column++) {
                        this.bigrowArray[i].columns.push({ columnKey: this.bigrowArray[i].rowIndex + '-' + (column + 1), columIndex: column + 1, colspan: 1, rowspan: 1 });
                    }
                }
                for (let i = 0; i < nameObj.colNum; i++) {
                    this.colArray.push({ rowIndex: i + 1, columns: [] });
                }
                this.getCurrentViewList(nameObj.id);
            } else {
                this.tabname = name;
                if (name) {
                    this.rowArray = [];
                    this.currentViewComponent = [];
                    // 处理有tab的不显示边框
                   
                    let nameObj = JSON.parse(JSON.stringify(name));
                    if (typeof name === 'string') {
                        nameObj = JSON.parse(name);
                    }
                     // URL 视图不显示编辑视图按钮
                    this.showUrlViewEditBtn(nameObj);
                    this.checkedTab = nameObj.name;
                    this.editViewData = nameObj;
                    let windowResolution = this.editViewData.windowResolution;
                    let width = windowResolution.split(':')[0],
                        height = windowResolution.split(':')[1];
                    this.currentClientWidth = document.getElementById('aa').clientWidth - 7;
                    this.currentClientHeight = (this.currentClientWidth / width) * height;
                    //初始化表格
                    for (let i = 0; i < nameObj.rowNum; i++) {
                        this.rowArray.push({ rowIndex: i + 1, columns: [] });
                        for (let column = 0; column < nameObj.colNum; column++) {
                            this.rowArray[i].columns.push({ columnKey: this.rowArray[i].rowIndex + '-' + (column + 1), columIndex: column + 1, colspan: 1, rowspan: 1 });
                        }
                    }
                    for (let i = 0; i < nameObj.colNum; i++) {
                        this.colArray.push({ rowIndex: i + 1, columns: [] });
                    }
                    this.getCurrentViewList(this.editViewData.id);
                }
            }
        },
        //获取当前视图构件信息
        getCurrentViewList(id) {
            this.$http
                .post('/dashboard/component/list', { viewId: id })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        data.map(item => {
                            item.startRow = item.crossRow.split('-')[0];
                            item.startCol = item.crossCol.split('-')[0];
                            item.provinceCode = item.provinceCode ? Number(item.provinceCode) : 100000;
                            item.groupIds = item.groupId ? item.groupId : '';
                            if (this.editViewData.type == 1) {
                                console.log(this.dateValue);
                            }
                            item.nameAll = item.name;
                            item.name = item.shortName || item.name;
                            // 处理有tab的不显示边框
                            if (item.topoInfos && item.topoInfos.length > 1) {
                                item.noShowFrame = 1;
                                item.noShowName = 1;
                            }
                          
                        });
                        if (this.isbigscreen) {
                            this.bigcurrentViewComponent = data;
                        } else {
                            this.currentViewComponent = data;
                        }
                        this.handleTableProp();
                    } else {
                        console.log(msg);
                    }
                })
                .catch(() => { })
                .finally(() => { });
        },
        //表格属性计算
        handleTableProp() {
            if (this.isbigscreen) {
                if (this.bigcurrentViewComponent.length < 0) {
                } else {
                    let componentList = this.bigcurrentViewComponent;
                    let rowArray = JSON.parse(JSON.stringify(this.bigrowArray));
                    let newArr = [];
                    let startRow = 0,
                        startColumn = 0,
                        endRow = 0;
                    componentList.forEach((item, index) => {
                        const itemClRowArr = item.crossRow.split('-'),
                            itemClColumnArr = item.crossCol.split('-');
                        //获取开始和结束 行和列
                        const itemStartR = parseInt(itemClRowArr[0]),
                            itemEndR = parseInt(itemClRowArr[itemClRowArr.length - 1]),
                            itemStartCol = parseInt(itemClColumnArr[0]),
                            itemEndCol = parseInt(itemClColumnArr[itemClColumnArr.length - 1]);
                        //获取最终跨多少行和列的数量
                        const itemRowspan = itemEndR - itemStartR + 1;
                        const itemCoslspan = itemEndCol - itemStartCol + 1;
                        for (let index = 0; index < rowArray.length; index++) {
                            if (rowArray[index].rowIndex == itemStartR) {
                                let columnArr = rowArray[index].columns;

                                for (let colIndex = 0; colIndex < columnArr.length; colIndex++) {
                                    if (columnArr[colIndex].columIndex == itemStartCol) {
                                        columnArr[colIndex].rowspan = itemRowspan;
                                        columnArr[colIndex].colspan = itemCoslspan;
                                        columnArr.splice(colIndex + 1, itemEndCol - itemStartCol);
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                        let emptyCol = [];
                        for (let rowIndex = 0; rowIndex < rowArray.length; rowIndex++) {
                            if (rowArray[rowIndex].rowIndex > itemStartR && rowArray[rowIndex].rowIndex <= itemEndR) {
                                for (let colIndex = rowArray[rowIndex].columns.length - 1; colIndex >= 0; colIndex--) {
                                    if (rowArray[rowIndex].columns[colIndex].columIndex >= itemStartCol && rowArray[rowIndex].columns[colIndex].columIndex <= itemEndCol) {
                                        rowArray[rowIndex].columns.splice(colIndex, 1);
                                    }
                                }
                                if (rowArray[rowIndex].columns.length < 1) {
                                    emptyCol.push(rowIndex);
                                }
                            }
                        }
                        if (emptyCol.length > 0) {
                        }
                    });
                    this.bigrowArray = rowArray;
                }
            } else {
                if (this.currentViewComponent.length < 0) {
                } else {
                    let componentList = this.currentViewComponent;
                    let rowArray = JSON.parse(JSON.stringify(this.rowArray));
                    let newArr = [];
                    let startRow = 0,
                        startColumn = 0,
                        endRow = 0;
                    componentList.forEach((item, index) => {
                        const itemClRowArr = item.crossRow.split('-'),
                            itemClColumnArr = item.crossCol.split('-');
                        //获取开始和结束 行和列
                        const itemStartR = parseInt(itemClRowArr[0]),
                            itemEndR = parseInt(itemClRowArr[itemClRowArr.length - 1]),
                            itemStartCol = parseInt(itemClColumnArr[0]),
                            itemEndCol = parseInt(itemClColumnArr[itemClColumnArr.length - 1]);
                        //获取最终跨多少行和列的数量
                        const itemRowspan = itemEndR - itemStartR + 1;
                        const itemCoslspan = itemEndCol - itemStartCol + 1;
                        for (let index = 0; index < rowArray.length; index++) {
                            if (rowArray[index].rowIndex == itemStartR) {
                                let columnArr = rowArray[index].columns;

                                for (let colIndex = 0; colIndex < columnArr.length; colIndex++) {
                                    if (columnArr[colIndex].columIndex == itemStartCol) {
                                        columnArr[colIndex].rowspan = itemRowspan;
                                        columnArr[colIndex].colspan = itemCoslspan;
                                        columnArr.splice(colIndex + 1, itemEndCol - itemStartCol);
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                        let emptyCol = [];
                        for (let rowIndex = 0; rowIndex < rowArray.length; rowIndex++) {
                            if (rowArray[rowIndex].rowIndex > itemStartR && rowArray[rowIndex].rowIndex <= itemEndR) {
                                for (let colIndex = rowArray[rowIndex].columns.length - 1; colIndex >= 0; colIndex--) {
                                    if (rowArray[rowIndex].columns[colIndex].columIndex >= itemStartCol && rowArray[rowIndex].columns[colIndex].columIndex <= itemEndCol) {
                                        rowArray[rowIndex].columns.splice(colIndex, 1);
                                    }
                                }
                                if (rowArray[rowIndex].columns.length < 1) {
                                    emptyCol.push(rowIndex);
                                }
                            }
                        }
                        if (emptyCol.length > 0) {
                        }
                    });
                    this.rowArray = rowArray;
                }
            }
        },

        //根据某个字段排序
        compare(prop) {
            return (obj1, obj2) => {
                let val1 = obj1[prop];
                let val2 = obj2[prop];
                if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
                    val1 = Number(val1);
                    val2 = Number(val2);
                }
                if (val1 < val2) {
                    return -1;
                } else if (val1 > val2) {
                    return 1;
                } else {
                    return 0;
                }
            };
        },
        //中文转码
        escapeFun(data) {
            return escape(data);
        },
        //获取是本地
        getNetWorkAddress() {
            return top.window.location.hostname;
        },
        //页面跳转
        goPage(param) {
   
            const toUrlObj = this.pageList[param.pageUrl],
                toUrl = toUrlObj.url,
                level = toUrlObj.level;
            var iframSrc = window.location.hostname === 'localhost' ? '/anpm-plug-' + toUrl + '-ui.html?checkedTab=networkFault' : '/' + toUrl + '?checkedTab=networkFault';
            let funcs = JSON.parse(sessionStorage.getItem('accessToken')).funcs,
                parUrlObj = [];
            if (level === 2) {
                parUrlObj = funcs.filter(item => {
                    return item.fnName === toUrlObj.parentName;
                });
            }
            let UrlObj = funcs.filter(item => {
                return item.routeUrl === '/' + toUrl;
            });
            let navData = {
                navName: UrlObj[0].fnName,
                functionCode: null,
                subMenuName: null,
                functionUrl: UrlObj[0].routeUrl,
                node1: null,
                parentactiveId: level === 2 ? UrlObj[0].parentFnId : UrlObj[0].fnId,
                activeId: level === 2 ? UrlObj[0].fnId : null,
                parentFunctionName: level === 2 && parUrlObj.length>0? parUrlObj[0].fnName : null,
                topoId: param.topoId
            };
            if(this.currentSkin == 0) {
              
              // 浅色版本
               if(top.window.vm) {
              
                // 通知父级iframe更新主题为浅色
                // top.sessionStorage.setItem('dark', '0');
                top.window.vm.$emit('goPage', navData);
                 sessionStorage.setItem('menu', JSON.stringify(navData));
            // 拓扑图
                sessionStorage.setItem('jtopo', param.topoId);
                sessionStorage.setItem('jtopoType', param.pathtopoType);
              }

            }else {
               top.window.vm.parentactiveId = navData.parentactiveId;
            top.window.vm.activeId = navData.activeId;
            top.window.vm.preMenu = navData.parentFunctionName;
            top.window.vm.activeName = navData.navName;
            sessionStorage.setItem('menu', JSON.stringify(navData));
            // 拓扑图
            sessionStorage.setItem('jtopo', param.topoId);
            sessionStorage.setItem('jtopoType', param.pathtopoType);
            top.document.getElementById('sub-content-page').src = iframSrc;

            }
           
        },
        //下拉时间选择
        dateChange(val) {
            this.dateValue = val;
        },
        bigdateChange(val) {
            this.bigdateValue = val;
        }
    },
    mounted() {
     
      
        // this.setRefreshTimer()
           // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        if (this.$route.path === '/dashboard' && !this.isbigscreen && top.window.vm) {
            top.window.vm.$data.preMenu = null;
            top.window.vm.$data.activeName = this.$t('dash_panel');
        }
        window.addEventListener('message', e => {
            if (e.data && e.data.type == 'linkType' && e.data.id) {
                const res = this.tabs.filter(item => item.id == e.data.id);
              
                if(res && res.length>0){
                    this.tabsClick(JSON.stringify(res[0]))
                }
                console.log('message');
            }
        });
        if (this.isbigscreen) {
           this.refimer  = setTimeout(() => {
        this.startMemoryMonitor();
      }, 5 * 60 * 1000)
          this.getCurrentTime();
          this.bigTimer = setInterval(this.updateTime, 1000);
            this.bigwidth = document.getElementById('bb').clientWidth - 7;
            // this.bigSetInterval = setInterval(() => {
            //     this.getTabs();
            // }, this.bigRefreshTime * 60 * 1000);
            this.switchStateTime = setInterval(() => {
                this.getSwitchStates();
            }, 60 * 1000);
        } else {
            this.currentClientWidth = document.getElementById('aa').clientWidth - 7;
        }
              // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize);
        
        // 初始调用一次避免初始滚动条
        this.checkForScrollbar();
              this.getSwitchState();
              this.getSystemInfo();
              window.vm = this;
          },
    beforeDestroy() {
     
     
      
      if (this.refimer) {
        clearTimeout(this.refimer);
        this.refimer = null;
      }
      if (this.memoryTimer) {
    clearInterval(this.memoryTimer);
    this.memoryTimer = null;
  }
      // this.bigSetInterval
      if(this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
       // 移除事件监听
       if (this.bigTimer) {
    clearInterval(this.timer);
    this.bigTimer = null;
  }
   window.removeEventListener('resize', this.handleResize);
      // 移除事件监听
      window.removeEventListener('storage', this.handleStorageChange);
       window.removeEventListener('message', this.handleMessage);
        if (this.bigSetInterval) {
            clearInterval(this.bigSetInterval);
            this.bigSetInterval = null;
        }
        if (this.switchStateTime) {
            clearInterval(this.switchStateTime);
            this.switchStateTime = null;
        }
        
        if(this.getTimelyData) {
          clearInterval(this.getTimelyData)
          this.getTimelyData = null
        }
        if(this.popTimer){
          clearInterval(this.popTimer)
          this.popTimer = null
        }
         // 清理全局引用
  delete window.pathTopoInstance;
  delete window.vm;
  // 清理DOM引用
  const container = document.getElementById('container');
  if (container) {
    container.innerHTML = '';
  }
  
  // 清理iframe引用
  const iframes = document.querySelectorAll('iframe');
  iframes.forEach(iframe => {
    iframe.src = 'about:blank';
    iframe.remove();
  });
    }
};
</script>

<style scoped lang="less">
#bb {
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-right: 0;
  margin-right: 0;
}

/* 确保所有子元素不超出容器 */
#bb > * {
  max-width: 100%;
  box-sizing: border-box;
}
.pop-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 998;

  background: rgba(0, 0, 0, 1);
}
.pop-box-one {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.itemHeader {
  background-image: url("../../../assets/img-frame01.png");
  background-size: 101% 100%;
}

.title-right {
  position: relative;
  height: 25px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.line {
  height: 1px;
  width: 100%;
  background: url("../../../assets/tc.png") repeat-x;
}

.itemBox {
  // background-image: url('../../../assets/img-frame04.png');
  // background-size: 100% 100%;
  width: 100%;
  height: 100%;
}

.box-head:before,
.title-right:after,
.box-body:before,
.box-body:after,
.box-foot:before,
.box-foot:after {
  content: "";
  display: inline-block;
  position: absolute;
}

.box {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
}

.title-right:after {
  background: url("../../../assets/tc.png") repeat-x;
  height: 20px;
  width: 100%;
  top: 5px;
}

.box-body {
  width: 100%;
  height: 100%;
  position: relative;
}

.box-body:before {
  background: url(../../../assets/ml.png) repeat-y;
  width: 10px;
  left: 0px;
  top: 0;
  bottom: 0;
}

.box-body:after {
  background: url(../../../assets/mr.png) repeat-y;
  width: 10px;
  right: 0px;
  top: 0;
  bottom: 0;
}

.box-body-no-show:before,
.box-body-no-show:after,
.box-foot-no-show:before,
.box-foot-no-show:after,
.box-foot-no-show,
.box-body-no-show {
  background: none !important;
}

.box-title-no-show {
  .itemHeader,
  .title-right:after {
    background: none !important;
  }

  .title-right img {
    display: none !important;
  }
}

.box-foot {
  width: calc(100% - 40px);
  background: url(../../../assets/bc.png) repeat-x;
  height: 25px;
  position: relative;
}

.box-foot:before {
  background: url(../../../assets/bl.png) no-repeat;
  width: 20px;
  height: 20px;
  left: -20px;
}

.box-foot:after {
  background: url(../../../assets/br.png) no-repeat;
  width: 20px;
  height: 20px;
  right: -20px;
}

.itemBox {
  padding: 15px 2% 0 2%;
}
.box-no-padding {
  padding: 0;
}

.dashboardSection .tabsBody .tabsBodyItem {
  height: 29px;
}

.no-show {
  display: none !important;
}

.dashboardSection .tabsBody .tabsBodyItem .itemHeader p.title {
  line-height: 14px;
  // text-align: left;
  font-size: 14px;
  padding-left: 8px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  // color: #00ffee;
  color: var(--dashboard_component_title_font_color, #00ffee);
}

.titleDiv {
  display: flex;
  align-items: center;
  height: 25px;
}

.dashboardSection .tabsBody .tabsBodyItem .itemHeader {
  padding: 5px 35px 0px 23px;
  height: 25px;
  border-left: none;
  white-space: nowrap;
}

// /deep/ .ivu-tabs-tab:nth-child(2) {
//   background-image: url("../../../assets/dashboard/btn-tabs1-01.png") !important;
//   background-size: 100% 100% !important;
// }

// /deep/ .ivu-tabs-tab:not(.ivu-tabs-tab:nth-child(2)) {
//   background-image: url("../../../assets/dashboard/btn-tabs2-01.png") !important;
//   background-size: 100% 100% !important;
// }

// /deep/ .ivu-tabs-tab:nth-child(2).ivu-tabs-tab-active {
//   background-image: url("../../../assets/dashboard/btn-tabs1-02.png") !important;
//   background-size: 100% 100% !important;
// }

// /deep/ .ivu-tabs-tab:not(.ivu-tabs-tab:nth-child(2)).ivu-tabs-tab-active {
//   background-image: url("../../../assets/dashboard/btn-tabs2-02.png") !important;
//   background-size: 100% 100% !important;
// }

/deep/ .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
  border-color: transparent !important;
}

/deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  border: 0;
}

// /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
//   font-size: 13px;
//   font-weight: bolder;
//   color: #060d15;
//   font-family: Microsoft YaHei-Bold, Microsoft YaHei;
// }

/deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-nav-container,
/deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  height: 38px;
}

.bigscreen {
  /deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
    background: #0d151f;
    color: #2974fe;
    // border-bottom: 1px solid #1252c8     ;
  }

  /deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
    height: 38px;
    background: #0d151f;
    color: #8fd4ff;
    border-bottom: none;
  }

  /deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab:hover {
    color: #89cbf6;
  }

  /deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
    padding: 8px 16px;
    border-color: #1251c5;
  }

  /deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
    border-color: #1251c5;
  }
}

/deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
  height: 38px;
  border-bottom: none;
}

/deep/ .ivu-tabs-nav {
  display: flex;
}

/deep/.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 35px;
  font-size: 13px;
}

.bigscreen .ivu-spin-fix {
  background-color: transparent;
}

table tr td {
  padding-bottom: 10px;
  padding-left: 5px;
  padding-right: 5px;
}

table tr td:nth-child(1) {
  padding-left: 5px;
}

// table tr td:last-child{
//   padding-right: 0;
// }
.bigScreenTitle {
  background-image: url("../../../assets/dashboard/img-title.gif");
  background-size: 100% 100%;
  position: relative;
  .big-time {
    position: absolute;
    bottom: 2%;
    right: 2%;
    display: flex;
    align-items: center;
  }

  .bigScreenbg {
    height: 100%;
  }

  .bigScreenLeft {
    background: url("../../../assets/dashboard/bigscreenL.png") no-repeat;
    float: left;
    background-size: 100% 100%;
  }

  .bigScreenMiddle {
    background: url("../../../assets/dashboard/bigscreenM.png") repeat-x;
    float: left;
    //background-size: 100% 100%;
  }

  .bigScreenRight {
    background: url("../../../assets/dashboard/bigscreenR.png") no-repeat;
    float: right;
    background-size: 100% 100%;
  }
}

.moveR-enter-active,
.moveR-leave-active {
  transition: all 0.3s linear;
  transform: translateX(0);
}

.moveR-enter,
.moveR-leave {
  transform: translateX(100%);
}

.moveR-leave-to {
  transform: translateX(100%);
}

.tipsBox {
  position: fixed;
  z-index: 998;
  right: 10px;
  // top:45px;
  bottom: 5px;
}

@media screen and (max-width: 1600px) {
  .tipsBox {
    .modal-body {
      max-height: 160px;
    }
  }
}

@media screen and (max-width: 1440px) {
  .tipsBox {
    .modal-body {
      max-height: 140px;
    }
  }
}

.tipsModalStyle {
  margin-bottom: 5px;
  padding-bottom: 10px;
  width: 450px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: left;
  background-color: #253142;
  // background-color: #fff;
  color: #d1e4ff;
}

.tipsModalStyle1 {
  margin-top: 5px;
  width: 450px;
  box-shadow: inset 0px 4px 10px 1px rgba(254, 92, 94, 0.4);
  text-align: left;
  background-color: #08101a;
}

.tipsModalStyle2 {
  margin-top: 5px;
  width: 450px;
  box-shadow: inset 0px 4px 10px 1px rgba(254, 163, 27, 0.4);
  text-align: left;
  background-color: #08101a;
}

.tipsModalStyle3 {
  margin-top: 5px;
  width: 450px;
  box-shadow: inset 0px 4px 10px 1px rgba(5, 212, 255, 0.52);
  text-align: left;
  background-color: #08101a;
}

.modal-backdrop {
  .modal-header {
    background: linear-gradient(272deg, #ff7474 0%, #fe5c5e 100%);
  }
}

.modal-backdrop2 {
  .modal-header {
    background: linear-gradient(272deg, #ffbe5e 0%, #fea31b 100%);
  }
}

.modal-backdrop3 {
  .modal-header {
    background: linear-gradient(272deg, #05eeff 0%, #0290fd 100%);
  }
}

.modal-header {
  color: #fff;
  // border-bottom: 1px solid #e8eaec;
  padding: 8px 15px;
  position: relative;

  h3 {
    font-weight: 500;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
  }

  .ivu-icon {
    position: absolute;
    right: 8px;
    top: 4px;
    overflow: hidden;
    cursor: pointer;
    color: #fff !important;
  }
}

.modal-footer {
  border-top: 1px solid #eee;
  justify-content: flex-end;
  padding: 8px 15px;
  display: flex;
}

.modal-body {
  position: relative;
  max-height: 190px;
  overflow-y: auto;
  padding: 10px 10px 0 10px;

  ul {
    li {
      display: flex;
      align-items: flex-start;
      margin: 0 10px 0 10px;
      padding: 0 0 10px 10px;
      border-left: 3px solid #35455d;
      position: relative;
    }
  }

  .tipsModalRight {
    margin-left: 10px;
    // width: calc(100% - 30px);
    width: 100%;
  }

  .alarmSign {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    position: absolute;
    left: -12px;
  }

  .alarmType {
    padding: 0 10px;
    border-radius: 4px;
  }

  .alarmTypeColor1 {
    .alarmType {
      border: 1px solid #fe0c0c;
      color: #fe0c0c;
    }

    .alarmSign {
      background: #fe0c0c;
    }
  }

  .alarmTypeColor2 {
    .alarmType {
      border: 1px solid #ff9e3d;
      color: #ff9e3d;
    }

    .alarmSign {
      background: #ff9e3d;
    }
  }

  .alarmTypeColor3 {
    .alarmType {
      border: 1px solid #0d93ed;
      color: #0d93ed;
    }

    .alarmSign {
      background: #0d93ed;
    }
  }

  .alarmIp {
    font-weight: 600;
    margin-left: 10px;
  }

  .alarmTime {
    margin-bottom: 10px;

    .ivu-icon {
      margin-right: 10px;
    }
  }

  .alarmTipsTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }
}

.btnConfirm {
  padding: 0 20px;
  margin-left: 40px;
  height: 32px;
  border: none;
  cursor: pointer;
  color: #fff;
  background-color: #2d8cf0;
  border-radius: 0;
}
.mask {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
}

/deep/ .white-tabsBody {
  background-color: #f5f6fa !important;
}

/deep/ .white-tabs-bar {
  background-color: #f5f6fa !important;
  .ivu-tabs-bar {
    background-color: #fff !important;
    border-bottom: none !important;
    margin-bottom: 0px !important;

    .ivu-tabs-nav .ivu-tabs-tab {
      padding-top: 15px;
      padding-bottom: 15px;
    }

    .ivu-tabs-nav-right {
      margin-top: 10px;
    }
  }

  .ivu-tabs-content {
    margin-top: 20px !important;
  }
}

/deep/ .white-box {
  background-color: #fff !important;

  .tabsBodyItem {
    margin-top: 5px !important;
  }

  .itemHeader {
    background-image: none !important;
    width: 100%;
    .title {
      color: #17233d !important;
      font-weight: bold;
    }
  }

  .title-right {
    display: none !important;
    img {
      display: none !important;
    }
  }

  .title-right:after {
    background-image: none !important;
  }

  .box-body {
    .itemBox {
      padding-top: 0px !important;
      //  padding-left: 0px !important;
      //  padding-right: 0px !important;
    }
  }

  .box-body:before,
  .box-body:after,
  .box-foot,
  .box-foot:after,
  .box-foot:before {
    width: 0px !important;
    background-image: none !important;
  }
}
.bigScreenExit {
  height: 20%;
}
.bigScreenExit img {
  height: 100%;
  width: auto;
}
</style>