<template>
  <div class="compnentIframe">
    <div class="compnentHead">
      <p
        class="title"
        style="border-bottom-color: var(--dash_h_b_color, #f4f6f9) !important"
      >
        {{ thisComponentName }}
      </p>
      <div class="tools">
        <span class="toolsEdit">
          <Icon type="md-create" @click="editCompnent(iframeData)" />
        </span>
        <span class="toolsDelete">
          <Icon type="md-close" @click="deleteCompnent(iframeData)" />
        </span>
      </div>
    </div>
    <div class="compnentContent">
      <!--<router-view name="dashboardStatistic"></router-view>-->
      <!-- <iframe :src=url class="iframeElement" :id= "iframeData.pageUrl + iframeData.id" :name = "saveName==name ? saveName : name" frameborder="0" style="width: 100%;" :style="'height:'+height+'px'"> -->
      <iframe
        :src="url"
        class="iframeElement"
        :id="iframeData.pageUrl + iframeData.id"
        :name="saveName == name ? saveName : name"
        :params="name"
        :taskIds="taskIds"
        :isEdit="true"
        frameborder="0"
        :editName="name"
        style="width: 100%; height: 100%"
      >
      </iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: "compnentIframe",
  props: {
    iframeData:{
      type:Object,
      default:function () {
        return {}
      }
    },
    title: {
      type: String,
      default: function() {
        return this.$t('dashboard_psad');
      }
    },
    height:{
      type:[String,Number],
      default:function (){
        return 140
      }
    }
  },
  data(){
    return {
      taskIds:[],
      thisComponentName:'',
      thisComponentNameAll:'',
      url:'',
      name:'',
      saveName:'',
      typeList:[
        {"code":"route_topo","name":"路径拓扑"},
        {"code":"physics_topo","name":"物理拓扑"},
        {"code":"timely_map","name":"及时地图"},
        {"code":"real_time_alarm","name":"实时告警"},
        {"code":"route_statistic","name":"路径统计分析图"},
        {"code":"route_alarm_trend","name":"最近30天路径告警走势"},
        {"code":"special_statistic","name":"专线统计分析图"},
        {"code":"special_alarm_trend","name":"最近30天专线告警走势"},
        {"code":"snmp_statistic","name":"中继统计分析图"},
        {"code":"snmp_alarm_trend","name":"最近30天中继告警走势"},
        {"code":"node_top_n","name":"质差节点topN"}],
    }
  },
  watch:{
    'iframeData': {
      handler(newName, oldName) {
        // debugger
       
        this.taskIds = JSON.stringify(this.iframeData.taskIds); 
  


        console.log(newName,this.iframeData,'传的参数')
      
        this.getComponentName() ;
        let randomTime = new Date().getTime();
        this.name = this.iframeData.pageUrl+( '?intervalTime='+this.iframeData.intervalTime+'&&topoId='+this.iframeData.topoId+'&&limitNum='+this.iframeData.alarmListNum+'&&graphicType='+this.iframeData.graphicType+'&&graphicList='+JSON.stringify(this.iframeData.graphicList)+'&&monitorObjList='+JSON.stringify(this.iframeData.monitorObjList)+'&&provinceCode='+JSON.stringify(this.iframeData.provinceCode||100000)+'&&groupIds='+this.iframeData.groupIds +'&&mapId=' +this.iframeData.id + '&&statisticsType='+(this.iframeData.alarmStatisticsType || "")+ '&&componentId='+this.iframeData.id +"&&_t=111" +'&&topoInfos='+JSON.stringify(this.iframeData.topoInfos)+'&&taskPresentationType=' +
        this.iframeData.taskPresentationType +'&&componentId=' +this.iframeData.id + '&&dashboardComponentTaskGroups=' +JSON.stringify(this.iframeData.dashboardComponentTaskGroups) + '&&componentRealAlarmConfigs=' +JSON.stringify(this.iframeData.componentRealAlarmConfigs));
        this.saveName = this.iframeData.pageUrl+( '?intervalTime='+this.iframeData.intervalTime+'&&topoId='+this.iframeData.topoId+'&&limitNum='+this.iframeData.alarmListNum+'&&graphicType='+this.iframeData.graphicType+'&&graphicList='+JSON.stringify(this.iframeData.graphicList)+'&&monitorObjList='+JSON.stringify(this.iframeData.monitorObjList)+'&&provinceCode='+JSON.stringify(this.iframeData.provinceCode||100000)+'&&groupIds='+this.iframeData.groupIds+'&&mapId=' +this.iframeData.id + '&&statisticsType='+(this.iframeData.alarmStatisticsType || "")+ '&&componentId='+this.iframeData.id +"&&_t=111"+'&&topoInfos='+JSON.stringify(this.iframeData.topoInfos));
        console.log(this.saveName,this.name,'this.saveName')  
        window[this.iframeData.pageUrl] = this.name;
   
        
        this.reloadIframe();
      },
      deep: true,
      immediate: true
    },
  },
  mounted(){
 

  },
  methods:{
    editCompnent(data){
      // debugger
      this.$emit('edit',data)
    },
    deleteCompnent(data){
      this.$emit('delete',data)
    },
    getComponentName(){//获取当前构件标题
    // 名称
    // debugger
      this.thisComponentName =  this.iframeData.pageUrl === "dashgis" ? this.iframeData.name : this.iframeData.name;
      this.thisComponentNameAll =  this.iframeData.pageUrl === "dashgis" ? (this.iframeData.nameAll || this.iframeData.name ) :  (this.iframeData.nameAll || this.iframeData.name );
      console.log(this.thisComponentName,'this.thisComponentName')
    },
    //重新加载iframe
    reloadIframe(){
      // 重新复制一个对象
     
      
      console.log(this.iframeData,'iframeData')
      var dataJson= JSON.parse(JSON.stringify(this.iframeData));
      // console.log(dataJson,'传过去的参数。。。。')
      if(dataJson && dataJson.pageUrl == "realtimemonitor"){
          // 防止 实时监控的对象太大了，导致页面加载不出来。
          dataJson.monitorObjList = [];
      }

      // URL 连接单独处理
      if(dataJson.typeCode == "url_link"){
          this.url = this.iframeData.url;
      }else if (dataJson.typeCode == "monitor_target_spread_map"){
        let params = {
          id: dataJson.id,
          intervalTime: dataJson.intervalTime,
          t:Date.now(),
          iconScalingRatio:dataJson.iconScalingRatio,
          backgroundImageId:dataJson.backgroundImageId,
          

          
          
        }
        let params2 = JSON.parse(JSON.stringify(params));
        if (top.window.location.hostname === 'localhost') {
          this.url = '/anpm-plug-'+this.iframeData.pageUrl+'-ui.html?param='+escape(JSON.stringify(params2));
        }else{
          this.url = '/'+this.iframeData.pageUrl+'?param='+escape(JSON.stringify(params2));
        }
        
      }else{
        if (top.window.location.hostname === 'localhost') {
          this.url = '/anpm-plug-'+this.iframeData.pageUrl+'-ui.html?param='+escape(JSON.stringify(dataJson));
        }else{
          this.url = '/'+this.iframeData.pageUrl+'?param='+escape(JSON.stringify(dataJson));
        }
      }

      // this.url += "&_t="+Date.now();

      console.log(' debugger.................',this.url)
      
    },
  },
  beforeDestroy() {
    // Clear the iframe src
    const iframe = document.getElementById(this.iframeData.pageUrl + this.iframeData.id);
    if (iframe) {
      iframe.src = 'about:blank';
    }
    // Remove global reference
    delete window[this.iframeData.pageUrl];
  },
}
</script>

<style scoped lang="less">
.compnentIframe {
  width: 100%;
  height: 100%;
  border: 1px solid var(--border_color, #dddddd);
  //   border-radius: 4px;
  .compnentHead {
    width: 100%;
    vertical-align: middle;
    padding: 10px 20px;
    position: relative;
    height: 35px;
    border-bottom: 1px solid var(--border_color, #dddddd);
    background: var(--dash_h_b_color, #f4f6f9);
    .title {
      color: var(--dash_h_f_color, #303748);
      text-align: left;
      line-height: 1;
    }
    .tools {
      position: absolute;
      right: 20px;
      top: 9px;
      .toolsEdit,
      .toolsDelete {
        margin-left: 10px;
        color: #2d8cf0;
        font-size: 20px;
        cursor: pointer;
        line-height: 1;
      }
    }
  }
  .compnentContent {
    width: calc(100% - 2px);
    height: calc(100% - 35px);
    // height: 100%;
  }
}
</style>