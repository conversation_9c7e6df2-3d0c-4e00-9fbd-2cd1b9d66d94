<template>
  <div>
    <Modal
      :styles="{ top: '120px' }"
      :value="objectModalShow"
      :title="$t('view_select_object')"
      width="1200"
      :mask="true"
      sticky
      @on-visible-change="visibleChange"
      @on-cancel="onCancel"
      draggable
    >
      <div class="sectionBox">
        <div class="section-top" style="padding: 0 20px 0 20px">
          <div class="">
            <!-- <div class="fn_item">
                            <label class="fn_item_label">{{$t('comm_keywords')}}：</label>
                            <div class="fn_item_box">
                                <Input
                                    v-model.trim="querys.datas"
                                    :placeholder="searchKeysPlaceholder"
                                    maxlength="50"
                                    style="width: 320px"
                                />
                            </div>
                        </div>
                        <div class="tool-btn">
                            <Button class="query-btn" type="primary"  icon="ios-search" @click="queryBtn" :title="$t('common_query')"></Button >
                        </div> -->
            <Row class="fn_box">
              <Col span="6">
                <div class="fn_item">
                  <label class="fn_item_label"
                    >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
                  >
                  <div class="fn_item_box">
                    <TreeSelect
                      v-model="treeValue"
                      ref="TreeSelect"
                      :data="treeData"
                      :placeholder="$t('snmp_pl_man')"
                      :loadData="loadData"
                      @onSelectChange="setOrg"
                      @onClear="onClear"
                      @onFocus="focusFn"
                    ></TreeSelect>
                  </div>
                </div>
              </Col>
              <Col span="6" v-if="this.objType != 1">
                <div class="fn_item">
                  <label class="fn_item_label"
                    >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
                  >
                  <div class="fn_item_box">
                    <Select
                      v-model="querys.groupIdsList"
                      :placeholder="$t('comm_please_select')"
                      multiple
                      :max-tag-count="1"
                      clearable
                      filterable
                      style="width: 250px"
                    >
                      <Option
                        v-for="item in groupList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option
                      >
                    </Select>
                  </div>
                </div>
              </Col>
              <Col span="6" style="margin-left: 130px">
                <div class="fn_item">
                  <label class="fn_item_label"
                    >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
                  >
                  <div class="fn_item_box">
                    <Input
                      v-model.trim="querys.datas"
                      :placeholder="searchKeysPlaceholder"
                      :title="searchKeysPlaceholder"
                      maxlength="50"
                      style="width: 300px"
                    />
                  </div>
                  <div class="fn_item_box" style="margin-left: 100px">
                    <Button
                      class="query-btn"
                      type="primary"
                      icon="ios-search"
                      @click="queryBtn"
                      :title="$t('common_query')"
                    ></Button>
                  </div>
                </div>
              </Col>
              <!-- <Col span="6">
                         <div class="fn_item">
                            <label class="fn_item_label"></label>
                            <div class="fn_item_box">
                                <Button class="query-btn" type="primary"  icon="ios-search" @click="queryBtn" :title="$t('common_query')"></Button >
                            </div>
                         </div>
                        </Col> -->
            </Row>
          </div>
        </div>
        <div class="section-body contentBox_bg" style="padding: 0px">
          <!-- 专线列表 -->
          <specialList
            ref="specialList"
            v-if="objType == 1"
            @getSelectedDatas="getSelectedDatas"
            :TagDatas="TagDatas"
            :querys="querys"
          >
          </specialList>
          <!-- 中断列表 -->
          <relayList
            ref="relayList"
            v-if="objType == 2"
            @getSelectedDatas="getSelectedDatas"
            :TagDatas="TagDatas"
            :querys="querys"
          >
          </relayList>
          <!-- 拨测任务列表 -->
          <dialList
            ref="dialList"
            v-if="objType == 3"
            @getSelectedDatas="getSelectedDatas"
            :TagDatas="TagDatas"
            :querys="querys"
          >
          </dialList>
        </div>
      </div>
      <div slot="footer">
        <Button type="error" style="margin-right: 20px" @click="onCancel">{{
          $t("common_cancel")
        }}</Button>
        <Button type="primary" @click="addData">{{ $t("common_add") }}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import TreeSelect from "@/common/treeSelect/treeSelect.vue";

export default {
    name:'MonitorModal',
    props:['objType','objectModalShow','TagDatas','isDashMap'],
    components:{
        specialList:()=> import('./specialList.vue'),// 专线列表
        relayList:()=> import('./relayList.vue'),// 中断列表
        dialList:()=> import('./dialList.vue'),// 拨测任务列表
        TreeSelect ,
    },
    data(){
        return{
            searchKeysPlaceholder:'',
            querys:{
                datas:'',
                objType:this.objType,
                groupIdsList:[],
                groupIds:'',
                orgId:'',
                dashboardGroupIds:'',
            },
            selectData:[],
            //机构参数
            treeData: [],
            groupList: [],
            treeValue:'',
        }
    },
    watch:{
        objType:{
            handler(val){
                if (val == 1) {
                    this.searchKeysPlaceholder = this.$t('access_4')
                } else if (val == 2) {
                    this.searchKeysPlaceholder = this.$t('access_5')
                } else {
                    this.searchKeysPlaceholder = this.$t('access_6')
                }
            },
            immediate:true
        }
    },
    created(){
        this.getTreeOrg();
        this.getGroupList();
    },
    beforeDestroy(){
        this.selectData = [] // 清空勾选的数据
    },
    methods:{
        loadData(item, callback) {
        // this.$refs.TreeSelect.$refs.Vinput.focus({ preventScroll: true})
        this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
            if (res.code === 1) {
            let childrenOrgList = res.data.map((item, index) => {
                item.title = item.name;
                item.id = item.id;
                // item.expand=false;
                item.loading = false;
                item.children = [];
                if (index === 0) {
                // item.expand=true;
                }
                return item
            });
            callback(childrenOrgList);
            }
        })
        },
        getTreeOrg(param = null) {
        let _self = this;
        _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
            if (res.code === 1) {
            let treeNodeList = res.data.map((item, index) => {
                item.title = item.name;
                item.id = item.id;
                // item.expand=false;
                item.loading = false;
                item.children = [];
                if (index === 0) {
                // item.expand=true;
                }
                return item
            });
            _self.treeData = treeNodeList;
            }
        })
        },
        getGroupList() {
        const param = {
            pageNo: 1,
            pageSize: 100000
        };
        this.$http.wisdomPost("/group/list", param).then(res => {
            if (res.code === 1) {
            if (res.data) {
                this.groupList = res.data.records;
            }
            }
        });
        },
        setOrg(item) {
        console.log(item);
        this.treeValue = item[0].name
        this.querys.orgId = item[0] ? item[0].id : null;
        },
        focusFn() {
        this.getTreeOrg()
        }, 
        onClear(){
        this.querys.orgId = ''
        this.treeValue = ''
        },
       
        getSelectedDatas(data){
          debugger
          console.log( this.selectData , '---------------------')
            this.selectData = [...data]
        },
        queryBtn(){
            this.getList()
        },
        onCancel() {
            this.selectData = []
            this.$emit('closeModal')
        },
        visibleChange(val){
            if (val) {
                this.getList()
            }
        },
        getList(){
            switch (this.objType) {
                case '3':
                    this.querys.groupIds  = this.querys.groupIdsList;
                    this.$refs.dialList.getList()
                    break;
                case '2':
                    this.querys.dashboardGroupIds  = this.querys.groupIdsList;
                    this.$refs.relayList.getList()
                    break;
                case '1':
                    this.$refs.specialList.getList()
                    break;
                default:
                    break;
            }
        },
        addData(){
            if (this.selectData.length==0) {
                this.$Message.error(this.$t('access_want_add'))
                return
            }
          //  debugger
           // fixed 这里做一个去重的操作。（原因是：编辑构建的时候，在选择对象的时候，会显已经选中的数据的时候，selectData 变量会出现重复的数据，因此这里去个重）
           // 选择数据通过ID去重。
           var arrayUnId = [] , itemArray = []; 
           this.selectData.forEach((item)=>{
              if(arrayUnId.indexOf(item.id) == -1){
                  arrayUnId.push(item.id);
                  itemArray.push(item);
              }
           });
           this.selectData  = itemArray;
           this.$emit('getSelectData',this.selectData)
            
            this.$emit('closeModal')
        }
    },
  
}
</script>

<style>
</style>