<template>
  <div>
    <Table
      ref="tableList"
      stripe
      :columns="columns"
      :data="tabeList"
      :no-data-text="
        loading
          ? ''
          : tabeList.length > 0
          ? ''
          : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
      "
      size="small"
      max-height="600"
      @on-select="handleSelect"
      @on-select-cancel="handleCancel"
      @on-select-all="handleSelectAll"
      @on-select-all-cancel="handleSelectAll"
    ></Table>
    <Loading :loading="loading"></Loading>
    <!--   分页      -->
    <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
      <Page
        v-page
        :current.sync="page.pageNo"
        :page-size="page.pageSize"
        :total="pageData.total"
        :page-size-opts="page.pageSizeOpts"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-elevator
        show-sizer
      >
      </Page>
    </div>
  </div>
</template>

<script>
import "@/config/page.js";
export default {
    name:'specialList',
    props:['querys','TagDatas'],
    data(){
		return {
			      currentSkin: sessionStorage.getItem('dark') || 1,
			loading: false,
            tabeList: [],
 			selectedIds: [],
            selectedDatas: [],
 			//分页参数
			page: {
				pageNo: 1,
				pageSize: 10,
				pageSizeOpts: [10,50,100,200,300,500,1000],
			},
			pageData: {
				list: [],
				total: 0,
			},
			columns: [
				{
					type: "selection",
					width: 60,
					align: "left",
				},
				{
					title: this.$t('dash_Special_line_name'),
					key: "name",
					align: "left",
					render:(h,param)=>{
						let str = param.row.name;
						str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
						let str1 = str;
						if (str.length > 10) {
							str1 = str.substring(0, 10) + '...';
						}
						return h(
							'Tooltip',
							{
								props: {
									placement: 'top-end',
									transfer:true
								},
							},
							[
								str1,
								h(
									'span',
									{
										slot: 'content', //slot属性
										style: {
											whiteSpace: 'normal',
											wordBreak: 'break-all'
										}
									},
									str
								)
							]
						);
					}
				},
				{
					title: this.$t('dash_A'),
					key: "specialAIp",
					align: "left",
				},
				{
					title: this.$t('dash_Z'),
					key: "specialZIp",
					align: "left",
				},
				{
					title: this.$t('comm_org'),
					key: "orgName",
					align: "left",
				},
				{
					title: this.$t('dash_line_number'),
					key: "specialNum",
					align: "left",
				},
            ],
        }
    },
 	watch: {
        selectedDatas: {
            handler(val) {
				this.$emit("getSelectedDatas", val);
				this.selectedIds = []
				val.forEach(item=>{
					this.selectedIds.push(item.id)
				})
            },
            deep: true,
            immediate: true,
        },
	},
		 mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
   },
   beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    methods:{
		   handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
		async getList() {
			let param = { ...this.querys};
			param.pageNo = this.page.pageNo
			param.pageSize = this.page.pageSize
			param.groupIds = param.groupIdsList
		 	await this.$http.wisdomPost("/link/list", {...param}).then(({ code, data, msg }) => {
				if (code === 1) {
					if (data && Array.isArray(data.records)) {
						this.tabeList =[...data.records];
						this.pageData.total = data.total || 0;
					}
				} else {
					this.$Message.error({ content:msg, background: true });
				}
			})
			.catch((msg) => {
			})
			.finally(() => {
			
			});
		},
		// 获取所有数据，用于
		async getAllList(){
				let param = { ...this.querys};
				param.pageNo = this.page.pageNo
				param.pageSize = this.pageData.total
				await this.$http.wisdomPost("/link/list", {...param}).then(({ code, data, msg }) => {
					if (code === 1) {
						if (data && Array.isArray(data.records)) {
							this.allData = data.records
							this.setCheckStatus()
						}
						
					}
				}).finally(() => {
				this.loading = false;
			});
		},
		// 已选择的数据 默认选中
		setCheckStatus(){
			let records = [...this.tabeList]
			this.tabeList =	records.map(item=>{
				this.TagDatas.forEach(element=>{
					if (item.id == element.objectId) {
						item['_checked']=true
					}
				})
				return item
			})
			// 填充勾选数据
			this.TagDatas.forEach(item=>{
				let value = ''
				this.allData.forEach(element=>{
					if (item.objectId == element.id ) {
						value = element
					}
				})
				if (value) {
					this.selectedDatas.push(value)
				}
			})
			console.log('this.TagDatas',this.TagDatas);
			console.log('this.selectedDatas',this.selectedDatas);
		},
		handleSelect(slection, row) {
			if (!this.selectedIds.includes(row.id)) {
				this.selectedIds.push(row.id);
			}

			let arrays = this.selectedDatas.filter(current=>{
				return row.id === current.id
			});
			if(arrays.length == 0){
				this.selectedDatas.push(row);
			}

		},
		handleCancel(slection, row) {
			this.selectedDatas = [...new Set(this.selectedDatas.map(item=>JSON.stringify(item)))].map(i=>JSON.parse(i))// 数组去重
			this.selectedIds.splice(
				this.selectedIds.findIndex((id) => id === row.id),
				1
			);
			this.selectedDatas.splice(
				this.selectedDatas.findIndex((item) => item.id === row.id),
				1
			);
		},
		handleSelectAll(slection) {
			console.log(slection);
			this.selectedDatas = [...new Set(this.selectedDatas.map(item=>JSON.stringify(item)))].map(i=>JSON.parse(i))// 数组去重
			if (slection.length === 0) {
				console.log(this.$refs.tableList);
				let data = this.$refs.tableList.data;
				data.forEach((item) => {
					if (this.selectedIds.includes(item.id)) {

						this.selectedIds.splice(
							this.selectedIds.findIndex((id) => id === item.id),
							1
						);
						this.selectedDatas.splice(
							this.selectedDatas.findIndex((current) => item.id === current.id),
							1
						);
					}
				});
			} else {
				slection.forEach((item) => {
					if (!this.selectedIds.includes(item.id)) {
						this.selectedIds.push(item.id);
					}

					let arrays = this.selectedDatas.filter(current=>{
						return item.id === current.id
					});
					if(arrays.length == 0){
						this.selectedDatas.push(item);
					}
				});
			}
        },
		//数据列表页码切换
		async  pageChange(pageNo) {
			 this.page.pageNo = pageNo;
			 this.loading = true;
			 await this.getList();
			 this.setCheckStatus()
			 this.loading = false;
		},
		//数据列表页码大小改变
		async  pageSizeChange(pageSize) {
			this.page.pageSize = pageSize;
			this.loading = true;
			await this.getList();
			this.setCheckStatus()
			this.loading = false;
		},
    },
   async created(){
	    this.loading = true;
		await this.getList();
		await this.getAllList()
    }
}
</script>

<style>
</style>