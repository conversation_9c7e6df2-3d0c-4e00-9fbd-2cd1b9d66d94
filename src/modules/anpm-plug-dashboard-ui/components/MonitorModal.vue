<template>
    <div>
        <Modal 
            :styles="{top:'20px'}"
            :value="realMonitorShow"
            :title="$t('view_select_object')"
            width="900"
            :mask="true"
            sticky
            @on-visible-change='visibleChange'
            @on-cancel="onCancel"
            draggable>
            <div class="sectionBox">
                <div class="section-top" style="padding:0 20px 0 20px">
                    <div class="">
                        <div class="fn_item">
                            <label class="fn_item_label">{{$t('comm_keywords')}}：</label>
                            <div class="fn_item_box">
                                <Input
                                    v-model.trim="querys.filedName"
                                    :placeholder="searchKeysPlaceholder"
                                    maxlength="50"
                                    style="width: 320px"
                                />
                            </div>
                        </div>
                        <div class="fn_item">
                            <div class="fn_item_box">
                                <Button type="primary" @click="queryBtn">{{$t('common_query')}}</Button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section-body contentBox_bg" style="padding:0px">
                    <!-- 拨测任务列表 -->
                    <dialList  
                        ref="dialList" 
                        v-if="objType == 1" 
                        @getSelectedDatas='getSelectedDatas' 
                        :TagDatas='TagDatas' 
                        :querys='querys'>
                    </dialList>
                    <!-- 中断列表 -->
                    <relayList 
                        ref='relayList' 
                        v-if="objType == 2" 
                        @getSelectedDatas='getSelectedDatas' 
                        :TagDatas='TagDatas'
                        :querys='querys'>
                    </relayList>
                    <!-- 专线列表 -->
                    <specialList 
                        ref="specialList" 
                        v-if="objType == 3" 
                        @getSelectedDatas='getSelectedDatas'
                        :TagDatas='TagDatas' 
                        :querys='querys'>
                    </specialList>
                </div>
            </div>
            <div slot="footer">
                <Button type="error" style="margin-right:20px;" @click="onCancel">{{$t('common_cancel')}}</Button>
                <Button type="primary" @click="addData">{{$t('common_add')}}</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
export default {
    name:'MonitorModal',
    props:['objType','realMonitorShow','TagDatas'],
    components:{
        dialList:()=> import('./dialList.vue'),// 拨测任务列表
        relayList:()=> import('./relayList.vue'),// 中断列表
        specialList:()=> import('./specialList.vue')// 专线列表
    },
    data(){
        return{
            searchKeysPlaceholder:this.$t('access_1'),
            querys:{
                filedName:'',
                objType:this.objType
            },
            selectData:[]
        }
    },
    watch:{
        objType:{
            handler(val){
                if (val == 1) {
                    this.searchKeysPlaceholder = this.$t('access_1')
                } else if (val == 2) {
                    this.searchKeysPlaceholder = this.$t('access_2')
                } else {
                    this.searchKeysPlaceholder = this.$t('access_3')
                }
            },
            immediate:true
        }
    },
    created(){
    },
    beforeDestroy(){
        this.selectData = [] // 清空勾选的数据
    },
    methods:{
        getSelectedDatas(data){
            this.selectData = [...data]
        },
        queryBtn(){
            this.getList()
        },
        onCancel() {
            this.selectData = []
            this.$emit('closeModal')
        },
        visibleChange(val){
            if (val) {
                this.getList()
            }
        },
        getList(){
            switch (this.objType) {
                case '1':
                    this.$refs.dialList.getList()
                    break;
                case '2':
                    this.$refs.relayList.getList()
                    break;
                case '3':
                    this.$refs.specialList.getList()
                    break;
                default:
                    break;
            }
        },
        addData(){
            if (this.selectData.length==0) {
                this.$Message.error(this.$t('access_want_add'))
                return
            }
            this.$emit('getSelectData',this.selectData)
            this.$emit('closeModal')
        }
    },
  
}
</script>

<style>

</style>