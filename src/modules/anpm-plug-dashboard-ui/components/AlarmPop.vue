<template>
  <!-- 告警弹窗 -->
  <div class="pop-box" :style="{ width: bodyWidth * 0.34 + 'px' }">
    <div class="pop-title">
      <div
        class="pop-title-left"
        :style="{ fontSize: calculateFontSize(16) + 'px' }"
      >
        {{ popTitle }}
      </div>
      <div class="pop-title-right" @click="closePop">
        <Icon type="ios-close" color="#5CA0D5" :size="calculateFontSize(30)" />
      </div>
    </div>
    <div
      class="pop-content"
      :style="{
        fontSize: calculateFontSize(14) + 'px',
        maxHeight: calculateHeight() + 'px',
      }"
    >
      <div
        :class="{
          'pop-content-border':
            index !== popList.length - 1 && popList.length > 1,
        }"
        v-for="(item, index) in popList"
        :key="index"
      >
        <div class="pop-item">
          <div class="pop-item-left">
            {{ $t("comm_failure_start") + $t("comm_colon") }}
          </div>
          <div class="pop-item-right color-blue">{{ item.faultStartTime }}</div>
        </div>
        <div class="pop-item">
          <div class="pop-item-left">
            {{ $t("dash_fault_type") + $t("comm_colon") }}
          </div>
          <div class="pop-item-right color-red">
            {{ formateFaultType(item.faultType) }}
          </div>
        </div>
        <div class="pop-item">
          <div class="pop-item-left">
            {{ $t("impact_path_number") + $t("comm_colon") }}
          </div>
          <div class="pop-item-right color-red">{{ item.affectsRouteNum }}</div>
        </div>
        <div class="pop-item">
          <div class="pop-item-left">
            {{ $t("dash_faulty_link") + $t("comm_colon") }}
          </div>
          <div class="pop-item-right color-white">
            {{ item.suspectedFaultyLink }}
          </div>
        </div>
        <div class="pop-item">
          <div class="pop-item-left">
            {{ $t("alarm_symptom") + $t("comm_colon") }}
          </div>
          <div class="pop-item-right color-white">
            {{ item.faultPhenomenon }}
          </div>
        </div>
        <div
          class="pop-item-btn"
          :style="{ width: (calculateFontSize(14) * 74) / 14 + 'px' }"
          @click="showDetailModal(item)"
        >
          <span>查看</span>
        </div>
      </div>
    </div>
    <!-- 告警查看详情 -->
    <DetailModal
      :modalTitle="$t('alarm_failure_index')"
      :modalShow.sync="modalShow"
      :queryData="queryData"
    ></DetailModal>
  </div>
</template>

<script>
export default {
    props:['bodyWidth','popList','popTitle'],
    data() {
        return {
          modalShow:false,
         queryData:{}
          
          

        }
    },
    components:{
        DetailModal:()=>import('../components/DetailModal.vue')
    },
    methods: {
     async showDetailModal(item){
      this.queryData = {
        alarmType:item.faultType,
        taskId:item.taskId,
        alarmId:item.alarmId
      }
      this.modalShow = true
     
      
      },
      formateFaultType(type){
        // 1中断，2时延劣化，3丢包劣化
        switch (Number(type)) {
          case 1:
            return this.$t('server_inter_alarm')
          case 2:
            return this.$t('comm_delay') 
          case 3:
            return this.$t('comm_loss_package') ;
          case 4:
            return this.$t('comm_delay') +','+this.$t('comm_loss_package') ;
          default:
            return this.$t('comm_unknown');
        }
      },
      closePop(){
        this.$emit('closePop')
      },
            calculateFontSize(baseSize) {
                // 根据bodyWidth与标准宽度1920的比例计算字体大小
                const calculatedSize = (this.bodyWidth / 1920) * baseSize;
                // 如果算出来小于baseSize就取baseSize
                return Math.max(calculatedSize, baseSize);
            },
            calculateHeight() {
                // 选出1920与bodyWidth的最大值
                let max = Math.max(1920, this.bodyWidth);
               return max * 0.34 * 412 /660
            }

    }

}
</script>

<style scoped lang="less">
.pop-title-right {
  cursor: pointer;
}
.pop-box {
  min-width: 660px;
  border: 2px solid #04b8fc;
  background-color: #00273e;
  padding: 10px 10px 34px 20px;
  box-shadow: inset 0px 4px 10px 1px rgba(5, 212, 255, 0.3);

  .pop-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .pop-title-left {
      font-size: 16px;
      color: #00ffee;
      font-weight: 700;
    }
  }
  .pop-item-btn {
    margin: 0 auto;
    margin-top: 21px;
    margin-bottom: 21px;

    border: 1px solid #04eefe;
    color: #05eeff;
    border-radius: 4px;
    padding: 7px 0;

    cursor: pointer;
  }
  .pop-item {
    display: flex;
    margin-top: 21px;
    .pop-item-left {
      width: 23%;
      text-align: right;
      color: #fff;
    }
    .pop-item-right {
      flex: 1;
      text-align: left;
    }
    .color-white {
      color: #fff;
    }
    .color-blue {
      color: #02b8fd;
    }
    .color-red {
      color: #fe5c5c;
    }
  }
  .pop-content {
    overflow-y: auto;
  }
  .pop-content-border {
    border-bottom: 1px solid #04b8fc;
  }
}
</style>
