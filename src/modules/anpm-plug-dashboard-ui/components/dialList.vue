<template>
  <div>
    <Table
      ref="tableList"
      stripe
      :columns="columns"
      :data="tabeList"
      :no-data-text="
        loading
          ? ''
          : tabeList.length > 0
          ? ''
         : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
      "
      size="small"
      max-height="600"
      @on-select="handleSelect"
      @on-select-cancel="handleCancel"
      @on-select-all="handleSelectAll"
      @on-select-all-cancel="handleSelectAll"
    ></Table>
    <Loading :loading="loading"></Loading>
    <!--   分页      -->
    <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
      <Page
        v-page
        :current.sync="page.pageNo"
        :page-size="page.pageSize"
        :total="pageData.total"
        :page-size-opts="page.pageSizeOpts"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-elevator
        show-sizer
      >
      </Page>
    </div>
  </div>
</template>

<script>
import "@/config/page.js";
import ipv6Format from "@/common/ipv6Format";
export default {
    name:'dialList',
    props:['querys','TagDatas'],
    data(){
		return {
			      currentSkin: sessionStorage.getItem('dark') || 1,
			loading: false,
            tabeList: [],
 			selectedIds: [],
            selectedDatas: [],
 			//分页参数
			page: {
				pageNo: 1,
				pageSize: 10,
				pageSizeOpts: [10,50,100,200,300,500,1000],
			},
			pageData: {
				list: [],
				total: 0,
			},
			columns: [
				{
					type: "selection",
					width: 60,
					align: "center",
				},
				{
					title: this.$t('snmp_task_code'),
					key: "taskNum",
					align: "left",
					minWidth: 190,
					render: (h, params) => {
						let str = params.row.taskNum,
						span = h(
							"span",
							{
							class: "",
							},
							str === undefined || str === null || str === "" ? "--" : str
						),
						array = [span];
						return h("div", array);
				},
				},
				{




					title: this.$t('comm_probe_ip'),
					key: "sourceIp",
					align: "left",
					  width: 200,
					render: (h, params) => {
						let str = params.row.sourceIp;

						let maxWidth = params.column.width; // 获取动态传递的宽度

						str = ipv6Format.formatIPv6Address(str,maxWidth);
						return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
						// return h(
						// 	"div",
						// 	{
						// 	style: {
						// 		textAlign: "left",
						// 		width: "100%",
						// 		textIndent: "0px",
						// 		overflow: "hidden", //超出的文本隐藏
						// 		textOverflow: "ellipsis", //溢出用省略号显示
						// 		whiteSpace: "nowrap", //溢出不换行
						// 	},
						// 	attrs: {
						// 		title: str,
						// 	},
						// 	},
						// 	str
						// );
					},
				},
				{
					title: this.$t('comm_probe_port'),
					key: "sourcePort",
					align: "left",
					width: 100,
					render: (h, params) => {
						let str = params.row.sourcePort;
						let routePath = params.row.routePath || "";
						let routePathType =
						routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
						let routePathType2 =
						routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
						let text = "";
						if (str === undefined || str === null || str === "") {
						text = "--";
						} else if (
						params.row.configType == 1 ||
						params.row.configType == 2 ||
						params.row.configType == 5 ||
						params.row.configType == 6
						) {
						text = "--";
						} else if (
						(params.row.configType == 3 && routePathType == 2) ||
						(params.row.configType == 3 && routePathType == 1 && str < 20) ||
						(params.row.configType == 3 && routePathType == 3 && str < 20) ||
						(params.row.configType == 8 && routePathType2 == 2)
						) {
						text = "--";
						} else if (str < 20) {
						text = "--";
						} else {
						text = str;
						}
						return h("span", text);
					},
				},
				{
					title: this.$t('comm_grouping'),
					key: "groupNames",
					align: "left",
					width: 120,
					render: (h, params) => {
						let str = params.row.groupNames;
						if (str === undefined || str === null) {
						str = "--";
						}
						let v = "";
						if (str.length > 20) {
						v = str.substring(0, 20) + ".......";
						} else {
						v = str;
						}
						let Arr1 = h('span', v);
						return h('div', {
						class: {
							'text-ellipsis': true
						},
						style: {
							'word-break': 'keep-all',
							'white-space': 'nowrap',
							'overflow': 'hidden',
							'text-overflow': 'ellipsis'
						},
						domProps: {
							title: str
						}
						}, [Arr1])
					},
				},
				{
				title: this.$t('comm_IP(MAC)'),
				key: "destIp",
				align: "left",
				  width: 340,
				ellipsis: true,
				render: (h, params) => {
					 let maxWidth = params.column.width; // 获取动态传递的宽度
					let config_type = params.row.configType;
					let destMac = params.row.destMac??'--';
					let text = "";
					let showText = ''
					let str = params.row.destIp;
					if (str == undefined || str == null || str == "") {
					text = "--";
					} 
					// else if (str.length > 16) {
					// text = str.substring(0, 16) + "...";
					// }
					 else {
					text = str;
					}
					showText =  text+'('+destMac+')'
					let dest_ip_name = params.row.destIpName;
					let nameText = "";
					if (
					dest_ip_name == undefined ||
					dest_ip_name == null ||
					dest_ip_name == ""
					) {
					nameText = "--";
					} else {
					nameText = dest_ip_name;
					}
					let title =
					(showText != "--"
						? (config_type != 8 ? this.$t('access_destination_ip')+"：" : this.$t('access_destination_acc')+"：") + showText
						: "") + (nameText != "--" ? ","+this.$t('probetask_target_name')+"：" + dest_ip_name : "");
					
					    showText = ipv6Format.formatIPv6MAC(showText,maxWidth);
            return h('div', { style: { whiteSpace: 'pre-wrap' } }, showText);
					
					// return h(
					// "span",
					// {
					// 	class: {
					// 	"text-ellipsis": true,
					// 	},
					// 	domProps: {
					// 	title:  showText,
					// 	},
					// },
					// showText
					// );
				},
				},
				{
					title: this.$t('comm_target_name'),
					key: "destIpName",
					align: "left",
					width: 150,
					render: (h, params) => {
						let str = params.row.destIpName;
						str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
						return h(
							"div",
							{
							style: {
								textAlign: "left",
								width: "100%",
								textIndent: "0px",
								overflow: "hidden", //超出的文本隐藏
								textOverflow: "ellipsis", //溢出用省略号显示
								whiteSpace: "nowrap", //溢出不换行
							},
							attrs: {
								title: str,
							},
							},
							str
						);
					}
				},
				{
				title: this.$t('comm_org'),
				key: "orgName",
				align: "left",
				width: 120,
				render: (h, params) => {
					let str = params.row.orgName;
					return h(
						"div",
						{
						style: {
							textAlign: "left",
							width: "100%",
							textIndent: "0px",
							overflow: "hidden", //超出的文本隐藏
							textOverflow: "ellipsis", //溢出用省略号显示
							whiteSpace: "nowrap", //溢出不换行
						},
						attrs: {
							title: str,
						},
						},
						str
					);
				},
				},
				{
				title: this.$t('task_dial_type'),
				key: "configType",
				align: "left",
				width: 130,
				render: (h, params) => {
					let str = params.row.configType,
					text = "--";
					let routePath = params.row.routePath || "";
					switch (str) {
					// case 0:
					//     text=' 集团遍历';
					//     break;
					case 1:
						text = " UDP Trace";
						break;
					case 2:
						text = "ICMP Ping";
						break;
					case 3:
						text =
						routePath.indexOf("-") >= 0
							? routePath.split("-")[1] == 1
							? "UDP Ping"
							: routePath.split("-")[1] == 2
								? "ICMP Ping"
								: routePath.split("-")[1] == 3
								? "TCP Ping"
								: "--"
							: "--";
						break;
					case 4:
						text = "UDP Trace";
						break;
					case 5:
						text = "ICMP Trace";
						break;
					case 6:
						text = "TCP Trace";
						break;
					case 7:
						text = "TCP Trace";
						break;
					case 8:
						text =
						routePath.indexOf("-") >= 0
							? routePath.split("-")[1] == 1
							? "UDP Trace"
							: routePath.split("-")[1] == 2
								? "ICMP Trace"
								: routePath.split("-")[1] == 3
								? "TCP Trace"
								: "--"
							: "--";
						break;
					default:
						text = "--";
						break;
					}
					return h("div", text);
				},
				},

			],
			allData:[]
        }
    },
    watch: {
        selectedDatas: {
            handler(val) {
				// console.log(val,'val')
				this.$emit("getSelectedDatas", val);
				this.selectedIds = []
				val.forEach(item=>{
					this.selectedIds.push(item.id)
				})
            },
            deep: true,
            immediate: true,
        },
    },
	 mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
   },
   beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    methods:{
		   handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
		async getList() {
			let param = { ...this.querys};
			param.pageNo = this.page.pageNo
			param.pageSize = this.page.pageSize 
		 	await this.$http.wisdomPost("/probetask/dashboardTaskList", {...param}).then(({ code, data, msg }) => {
				if (code === 1) {
					if (data && Array.isArray(data.records)) {
						this.tabeList =[...data.records];
						this.pageData.total = data.total || 0;
					}
				} else {
					this.$Message.error({ content:msg, background: true });
				}
			})
			.catch((msg) => {
			})
			.finally(() => {
			
			});
		},
		async getAllList(){
				let param = { ...this.querys};
				param.pageNo = this.page.pageNo
				param.pageSize = this.pageData.total
				await this.$http.wisdomPost("/probetask/dashboardTaskList", {...param}).then(({ code, data, msg }) => {
					if (code === 1) {
						if (data && Array.isArray(data.records)) {
							this.allData = data.records
							this.setCheckStatus()
						}
						
					}
				}).finally(() => {
				this.loading = false;
			});
		},
		// 已选择的数据 默认选中
		setCheckStatus(){
			console.log(this.TagDatas,'选中的数据')
			// this.selectedDatas= []
			let records = [...this.tabeList]
			this.tabeList =	records.map(item=>{
				this.TagDatas.forEach(element=>{
					if (item.id == element.objectId) {
						item['_checked']=true
					}
				})
				return item
			})
			// 填充勾选数据
			this.TagDatas.forEach(item=>{
				let value = ''
				this.allData.forEach(element=>{
					if (item.objectId == element.id ) {
						value = element
					}
				})
				if (value) {
					this.selectedDatas.push(value)
				}
			})
		
			console.log('this.TagDatas',this.TagDatas);
			console.log('this.selectedDatas',this.selectedDatas);
		},
		handleSelect(slection, row) {
			if (!this.selectedIds.includes(row.id)) {
				this.selectedIds.push(row.id);
			}

			let arrays = this.selectedDatas.filter(current=>{
				return row.id === current.id
			});
			if(arrays.length == 0){
				this.selectedDatas.push(row);
			}

		},
		handleCancel(slection, row) {
			this.selectedDatas = [...new Set(this.selectedDatas.map(item=>JSON.stringify(item)))].map(i=>JSON.parse(i))// 数组去重
			this.selectedIds.splice(
				this.selectedIds.findIndex((id) => id === row.id),
				1
			);
			this.selectedDatas.splice(
				this.selectedDatas.findIndex((item) => item.id === row.id),
				1
			);
		},
		handleSelectAll(slection) {
			this.selectedDatas = [...new Set(this.selectedDatas.map(item=>JSON.stringify(item)))].map(i=>JSON.parse(i))// 数组去重
			console.log(slection);
			if (slection.length === 0) {
				let data = this.$refs.tableList.data;
				data.forEach((item) => {
					if (this.selectedIds.includes(item.id)) {

						this.selectedIds.splice(
							this.selectedIds.findIndex((id) => id === item.id),
							1
						);
						this.selectedDatas.splice(
							this.selectedDatas.findIndex((current) => item.id === current.id),
							1
						);
					}
				});
			} else {
				slection.forEach((item) => {
					if (!this.selectedIds.includes(item.id)) {
						this.selectedIds.push(item.id);
					}

					let arrays = this.selectedDatas.filter(current=>{
						return item.id === current.id
					});
					if(arrays.length == 0){
						this.selectedDatas.push(item);
					}
				});
			}
        },
		//数据列表页码切换
		async  pageChange(pageNo) {
			 this.page.pageNo = pageNo;
			 this.loading = true;
			 await this.getList();
			 this.setCheckStatus()
			 this.loading = false;
		},
		//数据列表页码大小改变
		async  pageSizeChange(pageSize) {
			this.page.pageSize = pageSize;
			this.loading = true;
			await this.getList();
			this.setCheckStatus()
			this.loading = false;
		},
    },
   async created(){
	    this.loading = true;
		await this.getList();
		await this.getAllList()
    }
}
</script>

<style>
</style>