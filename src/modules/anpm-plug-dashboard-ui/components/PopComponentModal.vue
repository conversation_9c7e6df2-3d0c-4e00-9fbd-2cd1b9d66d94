<template>
  <Modal
    v-model="popComponentShow"
    :title="$t('edit_pop_component')"
    class="pading-40-modal"
    width="900"
    :mask="true"
    sticky
    draggable
    @on-cancel="popComponentCancle"
    @on-visible-change="handleVisibleChange"
  >
    <Form
      :label-width="140"
      ref="formEditPop"
      :model="popFormData"
      :rules="popFormRule"
    >
      <!-- 类型 -->
      <FormItem :label="$t('comm_type') + $t('comm_colon')" prop="type">
        <Select
          v-model="popFormData.type"
          style="width: 100%"
          @on-change="handleTypeChange"
        >
          <Option
            v-for="item in typeList"
            :value="item.value"
            :key="item.value"
            >{{ item.lable }}</Option
          >
        </Select>
        <!-- <Input

          v-model="popFormData.typeCode"
          :placeholder="$t('snmpoid_select_type')"
        >
        </Input> -->
      </FormItem>
      <!-- 名称 -->
      <FormItem :label="$t('comm_name') + $t('comm_colon')" prop="name">
        <Input
          v-model="popFormData.name"
          :placeholder="$t('comm_please_enter_name')"
          :maxlength="20"
        >
        </Input>
      </FormItem>
      <!-- 刷新间隔时间 -->
      <FormItem
        :label="$t('dashboard_refresh_interval') + $t('comm_colon')"
        prop="intervalTime"
      >
        <Select v-model="popFormData.intervalTime" style="width: 100%">
          <Option
            v-for="item in componentRefreshAarray"
            :value="item.refreshTime"
            :key="item.key"
            >{{ item.intervalTime }}</Option
          >
        </Select>
      </FormItem>
      <!-- 分组 -->
      <FormItem :label="$t('comm_group') + $t('comm_colon')">
        <Select
          multiple
          v-model="popFormData.groupId"
          style="width: 100%"
          :max-tag-count="1"
          @on-change="handleGroupChange"
        >
          <Option
            v-for="item in groupSearchList"
            :value="item.id"
            :key="item.id"
            >{{ item.name }}</Option
          >
        </Select>
      </FormItem>

      <!-- /分组 -->
      <!-- 运维等级 -->
      <FormItem
        :label="$t('discover_maintain_level') + $t('comm_colon')"
        prop="maintainLevel"
      >
        <CheckboxGroup v-model="popFormData.maintainLevel">
          <Checkbox label="maintainLevelOne">{{
            $t("logback_first")
          }}</Checkbox>
          <Checkbox label="maintainLevelTwo">{{
            $t("logback_second")
          }}</Checkbox>
          <Checkbox label="maintainLevelThree">{{
            $t("logback_tertiary")
          }}</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <!-- 告警类型 -->
      <FormItem
        :label="$t('comm_alarm_table_alarm_type') + $t('comm_colon')"
        prop="alarmType"
      >
        <CheckboxGroup v-model="popFormData.alarmType">
          <Checkbox label="alarmTypeBroken">{{
            $t("server_inter_alarm")
          }}</Checkbox>
          <Checkbox label="alarmTypeDelay">{{ $t("comm_delay") }}</Checkbox>
          <Checkbox label="alarmTypePackLoss">{{
            $t("comm_loss_package")
          }}</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <!-- 告警状态 -->
      <FormItem
        :label="$t('alarm_Alertable') + $t('comm_colon')"
        prop="alarmStatus"
      >
        <CheckboxGroup v-model="popFormData.alarmStatus">
          <Checkbox label="alarmNoRecoverStatus">{{
            $t("comm_not_recovered")
          }}</Checkbox>
          <Checkbox label="alarmRecoverStatus">{{
            $t("comm_has_recovered")
          }}</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <!-- 影响路径数 -->
      <FormItem
        :label="$t('impact_path_number') + $t('comm_colon')"
        prop="affectsRouteNum"
      >
        <div>
          <span>{{ $t("impact_path_number_text") }}</span>
          <Input
            v-model.number.trim="popFormData.affectsRouteNum"
            style="width: 100px; margin: 0 10px"
          ></Input>

          <span>{{ $t("server_strip") }}</span>
        </div>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button
        type="error"
        style="margin-right: 20px"
        @click="popComponentCancle"
        >{{ $t("common_cancel") }}
      </Button>
      <Button type="primary" @click="popComponentSubmit">{{
        $t("common_verify")
      }}</Button>
    </div>
  </Modal>
</template>

<script>

export default {
    props:['popComponentShow','queryCodeList'],
    
    data(){
        // 校验影响链路数
const validateAffectsRouteNum = (rule, value, callback) => {
 
  const num = Number(value);
  console.log(num,'num')
  console.log(isNaN(num),'isNaN(num)')
  console.log(!Number.isInteger(num),'Number.isInteger(num)')
  console.log(num < 1,'num < 1')
  console.log(num > 9999,'num > 9999')
  if (isNaN(num) || !Number.isInteger(num) || num < 1 || num > 9999) {
     console.log(value,'value')
     callback(new Error(this.$t('comm_please_enter_valid_number_1_9999')))
   
   
    ;
  } else {
      callback();
   
   
  }
}
        return {
             popFormData: {
                type: "",
                name: "",
                intervalTime: 1,
                groupId:[],
                maintainLevel:['maintainLevelOne','maintainLevelTwo','maintainLevelThree'],
                // 告警类型 
                alarmType:['alarmTypeBroken','alarmTypeDelay','alarmTypePackLoss'],
                // 告警状态
                alarmStatus:['alarmNoRecoverStatus','alarmRecoverStatus'],
                // 影响路径数
                affectsRouteNum:5


               

          },
         typeList:[],
          popFormRule:{
            type:[{required:true,message:this.$t('comm_please_select_type'),trigger:'blur'}],
            name:[{required:true,message:this.$t('comm_please_enter_name'),trigger:'blur'}],
            intervalTime:[{required:true,type: 'number',message:this.$t('comm_please_select_interval_time'),trigger:'change'}],
            
           affectsRouteNum:[
             {required:true,type: 'number',message:this.$t('comm_please_enter_valid_number_1_9999'),trigger:'change,blur'},
             {
                validator: validateAffectsRouteNum,
                
                trigger: 'change'
              },
             
             
            ],
            maintainLevel:[{required:true,type: 'array',min: 1,message:this.$t('discover_select_maintain_level'),trigger:'change'}],
            alarmType:[{required:true,type: 'array',min: 1,message:this.$t('message_alerm_select_type'),trigger:'change'}],
            alarmStatus:[{required:true,type: 'array',min: 1,message:this.$t('comm_please_select_alarmStatus'),trigger:'change'}],
          },
            // 刷新间隔时间
            componentRefreshAarray: [
                {
                    intervalTime: this.$t("dashboard_one_minute"),
                    key: "intervalTime-2",
                    refreshTime: 1,
                },
                {
                    intervalTime: this.$t("dashboard_five_minutes"),
                    key: "intervalTime-3",
                    refreshTime: 5,
                },
                {
                    intervalTime: this.$t("dashboard_ten_minutes"),
                    key: "intervalTime-4",
                    refreshTime: 10,
                },
                {
                    intervalTime: this.$t("dashboard_thirty_minutes"),
                    key: "intervalTime-5",
                    refreshTime: 30,
                },
            ],
            // 分组
            groupSearchList:[],
             isEditComponent:false
           
             
          
            
        }
    },
    watch:{
        queryCodeList: {
            handler(newVal){
                console.log(newVal,'newVal')
                // debugger
             
                if(newVal.length > 0){
                    // 获取弹窗构件详情
                    // 获取分组列表
                    // debugger
                   
                    this.typeList = newVal
                     console.log(this.typeList,'newVal')
                    this.popFormData.type = newVal[0].value
                    this.getGroupSearchList()
                    this.getDashboardFrameComponentDetail()
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods:{
      handleVisibleChange(visible){
        if(visible){
             // 清空数据
            this.popFormData = {
                type: "",
                name: "",
                intervalTime: 1,
                groupId:[],
                maintainLevel:['maintainLevelOne','maintainLevelTwo','maintainLevelThree'],
                alarmType:['alarmTypeBroken','alarmTypeDelay','alarmTypePackLoss'],
                alarmStatus:['alarmNoRecoverStatus','alarmRecoverStatus'],
                affectsRouteNum:5
            }
            this.$refs.formEditPop.resetFields()
        }
      },
       handleGroupChange(value) {
       

      if (value.length > 10) {
        // this.$Message.warning(this.$t('最多只能选择10个分组'));
        // 只保留前10个选择
        this.popFormData.groupId = value.slice(0, 10);
        this.$Message.warning(this.$t('comm_please_select_alarmStatus_max')); 
      }
    },
        handleTypeChange(value) {
            this.popFormData = {
                type: value,
                name: '',
                intervalTime: 1,
                groupId:[],
                maintainLevel:['maintainLevelOne','maintainLevelTwo','maintainLevelThree'],
                alarmType:['alarmTypeBroken','alarmTypeDelay','alarmTypePackLoss'],
                alarmStatus:['alarmNoRecoverStatus','alarmRecoverStatus'],
                affectsRouteNum:5
            }
            this.popFormData.type = value
            this.getDashboardFrameComponentDetail()

        },
       
        popComponentSubmit() {
            console.log(this.popFormData)
            // debugger
        
            this.$refs.formEditPop.validate((valid) => {
                if (valid) {
                    // 转化参数
                    // 判断新增编辑
                    // debugger
                    let params = {
                affectsRouteNum:this.popFormData.affectsRouteNum,
                type:Number(this.popFormData.type),
                name:this.popFormData.name,
                intervalTime:this.popFormData.intervalTime,
                groupId: Array.isArray(this.popFormData.groupId) ? this.popFormData.groupId.join(',') : this.popFormData.groupId,

            }
        // 处理运维等级
        let maintainLevel = ['maintainLevelOne','maintainLevelTwo','maintainLevelThree']
        maintainLevel.forEach(item => {
            if(this.popFormData.maintainLevel.includes(item)){
                params[item] = 0
            }else {
                params[item] = 1
            }
        
        })
        // 处理告警类型
        let alarmType = ['alarmTypeBroken','alarmTypeDelay','alarmTypePackLoss']
        alarmType.forEach(item => {
            if(this.popFormData.alarmType.includes(item)){
                params[item] = 0
            }else {
                params[item] = 1
            }
        })
        // 处理告警状态
        let alarmStatus = ['alarmNoRecoverStatus','alarmRecoverStatus']
        alarmStatus.forEach(item => {
            if(this.popFormData.alarmStatus.includes(item)){
                params[item] = 0
            }else {
                params[item] = 1
            }
        })
        console.log(params,'params')
        if(this.isEditComponent){
            // 编辑
            params.id = this.popFormData.id
            this.editDashboardFrameComponent(params)

        }else {
            // 新增
            this.addDashboardFrameComponent(params)
        }
        }
            })

        },
        // 新增请求
        async addDashboardFrameComponent(params) {
            const res = await this.$http.PostJson('/home/<USER>',params)
            if(res.code == 1){
                if(res.code == 1){
               
                 this.$Message.success(this.$t('dashboard_edit_component'))
                  this.$emit('editComponentSuccess',params)
               
                this.popComponentCancle()
            }else {
                this.$Message.error(res.msg)
            }
               
            }
        },
        // 编辑请求
        async editDashboardFrameComponent(params) {
            const res = await this.$http.PostJson('/home/<USER>',params)
            if(res.code == 1){
           
                this.$Message.success(this.$t('dashboard_edit_component'))
                this.$emit('editComponentSuccess',params)
               
               
                this.popComponentCancle()

            }else {
                this.$Message.error(res.msg)
            }
        },
        popComponentCancle() {
           
             // 关闭弹窗
             this.$emit('update:popComponentShow',false)
          
           
        },
        // 获取弹框构件详情
       async getDashboardFrameComponentDetail(){
        if(this.popFormData.type == '' || this.popFormData.type == null){
          return
        }
     
        const res = await this.$http.post('/home/<USER>',{type:this.popFormData.type})
        console.log(res,'res')
        
        if(res.code == 1){
            if(res.data && Object.keys(res.data).length > 0){
                // 编辑
                // debugger
                this.isEditComponent = true
            let data = res.data
            this.popFormData.name = data.name
            this.popFormData.intervalTime = data.intervalTime
            this.popFormData.id = data.id
            this.popFormData.affectsRouteNum = data.affectsRouteNum
            if (typeof data.groupId === 'string' && data.groupId !== '') {
                this.popFormData.groupId = data.groupId.split(',').map(id => Number(id));
                } else if (Array.isArray(data.groupId)) {
                this.popFormData.groupId = data.groupId.map(id => Number(id));
                } else {
                this.popFormData.groupId = [];
                }
                console.log(this.popFormData.groupId,'this.popFormData.groupId')
            // 处理运维等级
            // 0 选中，1 未选中
            data.maintainLevelOne == 1 ?  this.popFormData.maintainLevel = this.popFormData.maintainLevel.filter(item => item !== 'maintainLevelOne') : ''
            data.maintainLevelTwo == 1 ? this.popFormData.maintainLevel = this.popFormData.maintainLevel.filter(item => item !== 'maintainLevelTwo') : ''
            data.maintainLevelThree == 1 ? this.popFormData.maintainLevel = this.popFormData.maintainLevel.filter(item => item !== 'maintainLevelThree') : ''
            // 处理告警类型
           
            data.alarmTypeBroken == 1 ? this.popFormData.alarmType = this.popFormData.alarmType.filter(item => item !== 'alarmTypeBroken') : ''
            data.alarmTypeDelay == 1 ? this.popFormData.alarmType = this.popFormData.alarmType.filter(item => item !== 'alarmTypeDelay') : ''
            data.alarmTypePackLoss == 1 ? this.popFormData.alarmType = this.popFormData.alarmType.filter(item => item !== 'alarmTypePackLoss') : ''
            // 处理告警状态
            // 0 未恢复 1 已恢复
            data.alarmRecoverStatus == 1 ? this.popFormData.alarmStatus = this.popFormData.alarmStatus.filter(item => item !== 'alarmRecoverStatus') : ''
            data.alarmNoRecoverStatus == 1 ? this.popFormData.alarmStatus = this.popFormData.alarmStatus.filter(item => item !== 'alarmNoRecoverStatus') : ''
           
            }else {
              // debugger
                // 新增
                this.isEditComponent = false
            }
        }
      


        },
       
        getGroupSearchList(){
      
       
            const param = {
                pageNo:1,
                pageSize:1000,
                
            };
            this.$http.wisdomPost("group/list", param).then((res) => {
        if (res.code == 1) {
          if (res.data) {
            this.groupSearchList = res.data.records;
          }
        }
      });
    }

}
}
</script>

<style scoped>
</style>
