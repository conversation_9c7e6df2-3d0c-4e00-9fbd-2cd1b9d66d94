const lan = require('../../common/language')
import Vue from 'vue'
import App from './App.vue'
import "./style/index.less";
import ViewUI from "view-design";
import "view-design/dist/styles/iview.css";
import axios from "axios";
import store from "./store/index";
import deplay from "../../server/deploy";
import base from "../../config/base.config.js";
import $http from "../../server/http";
import $message from "../../common/message.js";
import skin from "../../common/skinchange";
// 单独封装了国际化版本的颜色选择器，这个不用
// import vcolorpicker from 'vcolorpicker'
import i18n from '../../language';
import '@/timechange'
Vue.config.productionTip = false;

Vue.use(ViewUI);
Vue.use($http);
// Vue.use(vcolorpicker)
Vue.prototype.$axios = axios;
Vue.prototype.$baseUrl = deplay.baseUrl;
Vue.prototype.$base = base;
Vue.prototype.$Messages = $message
import router from "./router/index";
import "../../common";
router.beforeEach((to, from, next) => {
  ViewUI.LoadingBar.start();
  if (to.matched[0].meta.authority &&
    !sessionStorage.hasOwnProperty("accessToken")) {
    top.location.href = (window.location.hostname==='localhost' ? '/anpm-plug-login-ui.html': '/');
  }else{
    // console.log(window)
    if (window.name.search('bigscreen') > -1) {
      top.document.title = lan.getLabel("src.BigScreen");
    }else{
      top.document.title = to.name;
    }

    if (to.path === "/dashboard") {
      next()
    }else{
      next();
    }
  }
});
router.afterEach((to, from, next) => {
  ViewUI.LoadingBar.finish();
});
new Vue({
  i18n,
  router,
  store,
  render: h => h(App)
}).$mount('#app')
