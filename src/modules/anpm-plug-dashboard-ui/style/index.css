@import '../../assets/font_icon/iconfont.css';
.dashboardSection {
  /* padding: 20px; */
}
.dashboardSection .ivu-tabs-nav-right {
  display: flex;
}
.dashboardSection .userDefined {
  height: 32px;
  margin-left: 5px;
  opacity: 0.9;
  /* color: #02B8FD; */
  color:var(--more_btn_font_color,#02B8FD);
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  /* background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 37%, #31F0FE 46%, #015197 65%, #015197 100%) !important; */
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg,var(--more_btn_background_image_1_color,#015197) 37%, var(--more_btn_background_image_2_color,#31F0FE) 46%, var(--more_btn_background_image_1_color,#015197) 65%, var(--more_btn_background_image_1_color,#015197) 100%) !important;
}
.dashboardSection .bigscale {
  width: 50px;
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(357deg, #049DEC 0%, #05EBEB 100%);
  border-radius: 4px 4px 4px 4px;
  margin-left: 5px;
  cursor: pointer;
}
.dashboardSection .bigscale img {
  border: none;
  width: 18px;
  margin: 4px auto;
}
.dashboardSection .userDefined.bigscale:hover {
  background: var(--org_btn_bg_color, #2d8cf0);
}
.dashboardSection .userDefined:hover {
  background: var(--org_btn_bg_color, #2d8cf0);
  opacity: 1;
  color: white;
}
.dashboardSection .tabsBody {
  width: 100%;
  background: var(--body_b_color, #f4f6f9);
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  align-content: flex-start;
}
.dashboardSection .tabsBody .tabsBodyItem {
  box-sizing: border-box;
  overflow: hidden;
  height: 100%;
}
.dashboardSection .tabsBody .tabsBodyItem .itemHeader {
  padding: 10px 7px;
  height: 35px;
  border-left: none;
}
.dashboardSection .tabsBody .tabsBodyItem .itemHeader p.title {
  line-height: 1;
  text-align: left;
  font-size: 14px;
  padding-left: 8px;
}
.dashboardSection .tabsBody .tabsBodyItem .itemBox {
  box-sizing: border-box;
  height: 90%;
}
.dashboardSection .noComponent {
  position: relative;
  height: 130px;
  margin-top: 140px;
  background-image: url("../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  background-size: 120px 101px;
  background-position: center top;
}
.dashboardSection .noComponent p {
  display: block;
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 0;
}
.fillempty {
  width: 120px;
  height: 120px;
  background-position: 50% 45%;
  background-size: 120px 101px;
  background-image: url("../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  position: relative;
  margin: 0 auto;
}
.fillempty .emptyText {
  position: absolute;
  display: block;
  width: 100%;
  text-align: center;
  top: calc(45% + 80px);
}
/*设置空白表格*/
.table_empty {
  width: 100%;
  height: 170px;
  background-position: center 20px;
  background-size: 150px 103px;
  background-image: url("../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  position: relative;
}
.table_empty .emptyText {
  position: absolute;
  display: block;
  width: 100%;
  text-align: center;
  top: calc(45% + 60px);
}
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #abb1c2;
}
::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ededed;
}
.dashboardSection.bigscreen {
  background-color: #060D15 !important;
}
.dashboardSection.bigscreen .ivu-tabs-bar {
  border-bottom: 1px solid #1252c8;
}
.dashboardSection.bigscreen .ivu-tabs-nav-container {
  margin-bottom: -2px;
}
.dashboardSection.bigscreen .noComponent {
  color: #8fd4ff;
}
.dashboardSection.bigscreen .userDefined {
  background: #2d8cf0;
  border-radius: 3px;
  color: #8fd4ff;
  border-color: #8fd4ff;
}
.dashboardSection.bigscreen .tabsBody {
  background: #060D15 !important;
}
.dashboardSection.bigscreen .tabsBody .tabsBodyItem {
  border: none!important;
}
.dashboardSection.bigscreen .tabsBody .tabsBodyItem .itemHeader {
  color: #d1e4ff;
  padding: 10px 7px;
  height: 35px;
}
.dashboardSection.bigscreen .tabsBody .tabsBodyItem .itemBox {
  background: rgba(2, 35, 97, 0.15);
}
body.bigscreen {
  background: rgba(2, 35, 97, 0.15);
}
body.bigscreen .ivu-table-wrapper-with-border {
  border: none;
}
body.bigscreen .ivu-table table {
  width: 100%!important;
}
body.bigscreen .ivu-table .ivu-table-sort i {
  color: #d1e4ff !important;
}
body.bigscreen .ivu-table td,
body.bigscreen .ivu-table th {
  border: none;
  background-color: transparent;
}
body.bigscreen .ivu-table:before {
  background-color: transparent  !important;
}
body.bigscreen .ivu-table-border:after {
  background-color: transparent  !important;
}
body.bigscreen .ivu-table .ivu-table-header thead tr th {
  background: #061C2B !important;
  color: #fff !important;
  border: none;
}
body.bigscreen .ivu-table td {
  background: #060D15 !important;
  color: #fff !important;
}
body.bigscreen .ivu-table .ivu-table-body tr:nth-child(old) td {
  background: red;
}
body.bigscreen .topologys {
  background: rgba(2, 35, 97, 0.15);
  background: var(--body_conent_b_color ,rgba(2, 35, 97, 0.15));
}
.select-content {
  width: 100%;
  height: 100%;
  position: relative;
}
.select-content .select-input {
  position: relative;
  height: 100%;
}
.select-content .select-input input {
  display: block;
  width: 100%;
  height: 100%;
  line-height: 1.5;
  font-size: 14px;
  border: 1px solid #dcdee2;
  color: var(--font_color, #303748);
  background-color: #fff;
  background-image: none;
  position: relative;
  cursor: pointer;
  padding-left: 20px;
  padding-right: 30px;
}
.select-content .select-input input:hover {
  border-color: #57a3f3;
}
.select-content .select-input input:focus {
  border-color: #57a3f3;
  outline: 0;
  -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
.select-content .select-input input:focus ~ i {
  transform: translateY(-50%) rotate(180deg);
}
.select-content .select-input .focus {
  border-color: #57a3f3;
  outline: 0;
  -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
.select-content .select-input .focus ~ i {
  transform: translateY(-50%) rotate(180deg);
}
.select-content .select-dropdown {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 3;
  background: var(--selectdrop_b_color, #ffffff) !important;
  padding: 5px 0;
  margin: 5px 0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  max-height: 200px;
  border-radius: 4px;
  overflow: auto;
}
.select-content .select-dropdown p {
  width: 100%;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  cursor: pointer;
  padding: 0 16px;
}
.select-content .select-dropdown p:hover {
  background: #f3f3f3;
}
.select-content .select-dropdown .active {
  background-color: none !important;
  color: #2d8cf0 !important;
}
.select-content .select-dropdown .select-no-data {
  text-align: center;
  color: var(--font_color, #f4f6f9);
}
.biscreenScaleSelect {
  position: absolute;
  right: 70px;
  top: 16px;
  height: 20px;
  width: 180px;
  color: var(--font_color, #f4f6f9);
  z-index: 9;
}
.biscreenScaleSelect .select-content {
  width: 100%;
  height: 100%;
  position: relative;
}
.biscreenScaleSelect .select-content .select-input {
  position: relative;
  height: 100%;
}
.biscreenScaleSelect .select-content .select-input input {
  display: block;
  width: 100%;
  height: 100%;
  line-height: 1.5;
  font-size: 14px;
  border: 1px solid var(--border_color, #dddddd);
  color: var(--font_color, #f4f6f9);
  background-color: transparent;
  background-image: none;
  position: relative;
  cursor: pointer;
  padding-left: 20px;
  padding-right: 30px;
}
.biscreenScaleSelect .select-content .select-input input:hover {
  border-color: #a2d2fe;
}
.biscreenScaleSelect .select-content .select-input input:focus {
  border-color: #a2d2fe;
  outline: 0;
  -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
.biscreenScaleSelect .select-content .select-input input:focus ~ i {
  transform: translateY(-50%) rotate(180deg);
}
.biscreenScaleSelect .select-content .select-input .focus {
  border-color: #a2d2fe;
  outline: 0;
  -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
.biscreenScaleSelect .select-content .select-input .focus ~ i {
  transform: translateY(-50%) rotate(180deg);
}
.biscreenScaleSelect .select-content .select-dropdown {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 3;
  background-color: transparent;
  padding: 5px 0;
  margin: 0 0 5px 0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  max-height: 200px;
  border-radius: 4px;
  overflow: auto;
  border: 1px solid var(--border_color, #dddddd);
  border-top: 0;
  top: 20px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.biscreenScaleSelect .select-content .select-dropdown p {
  width: 100%;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  cursor: pointer;
  padding: 0 16px;
}
.biscreenScaleSelect .select-content .select-dropdown p:hover {
  background: url("../assets/dashboard/iframetitle1.png");
  background-repeat: repeat;
}
.biscreenScaleSelect .select-content .select-dropdown .active {
  background-color: transparent!important;
  background: url("../assets/dashboard/iframetitle1.png");
  background-repeat: repeat;
  color: #2d8cf0 !important;
}
.biscreenScaleSelect .select-content .select-dropdown .select-no-data {
  text-align: center;
  color: var(--body_b_color, #dddddd);
}
.biscreenScaleSelect .select-content .ivu-select-arrow {
  color: #a2d2fe;
}
html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
ul,
ol {
  list-style-type: none;
}
ul,
ol,
li,
p,
b {
  margin: 0;
  padding: 0;
}
body {
  background-color: var(--body_b_color, #f4f6f9);
  color: var(--font_color, #303748);
  text-align: center;
}
/*界面主体*/
.sectionBox {
  height: 100%;
  width: 100%;
  padding: 18px;

  background: var(--contentBox_bgcolor, #f4f6f9);
  /*搜索栏*/
  /*分割栏*/
  /*内容栏*/
  /*底部栏*/
}
.sectionBox .section-top {
  height: auto;
  margin: -18px -18px 0 -18px;
  padding: 18px;
  background: var(--contentBox_bgcolor, #f4f6f9);
  /*查询框区域*/
  /*按钮区域*/
}
.sectionBox .section-top .fn_box {
  margin-bottom: 10px;
  text-align: left;
  width: calc(100% - 235px);
  display: flex;
}
.sectionBox .section-top .fn_tool {
  text-align: right;
  margin-bottom: 12px;
}
.sectionBox .section-top .fn_tool button {
  margin-right: 8px;
}
.sectionBox .section-top .fn_tool button:last-child {
  margin-right: 0;
}
.sectionBox .section-top .fn_item {
  display: flex;
  width: 100%;
  height: 32px;
  margin-bottom: 20px;
  padding-right: 10px;
}
.sectionBox .section-top .fn_item > label {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
  padding: 9px 0;
  text-align: right;
  min-width: 75px;
}
.sectionBox .section-top .fn_item .fn_item_label {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
  padding: 9px 0;
  text-align: right;
  min-width: 85px;
  color: var(--search_lable_font_color, #fff);
}
.sectionBox .section-top .fn_item .fn_item_box {
  display: inline-block;
  height: inherit;
  width: 100%;
}
.sectionBox .section-body {
  position: relative;
}
/*故障清单恢复未恢复*/
.butBoxStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.butBoxStyle .fault-top-box {
  margin-bottom: 0 !important;
}
.butBoxStyle ul {
  display: flex;
  align-items: center;
  list-style: none;
}
.butBoxStyle li {
  padding: 0 15px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: var(--font_color, #303748);
  cursor: pointer;
  border: 1px solid var(--border_color, #dddddd);
  border-right: none;
}
.butBoxStyle li:last-child {
  border-right: 1px solid var(--border_color, #dddddd);
}
.butBoxStyle .tabStyle {
  background: #061824;
  color: #fff;
  border-color: #05eeff;
}
/*表格样式*/
.ivu-table th {
  background-color: var(--th_b_color, #f1f6fe) !important;
  border-color: var(--thd_border_color, #e8eaec) !important;
  font-weight: bold !important;
  font-family: Arial-Regular, Arial;
}
th {
  background-color: var(--th_b_color, #f1f6fe) !important;
  color: var(--th_font_color, #303748) !important;
  border-color: var(--thd_border_color, #e8eaec) !important;
}
td {
  color: var(--td_font_color, #303748) !important;
  background-color: var(--td_b_color, #ffffff) !important;
  border-color: var(--thd_border_color, #e8eaec) !important;
}
.ivu-table-stripe .ivu-table-body tr:nth-child(2n) td,
.ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
  background-color: var(--th_b_stripe_color, #f1f6fe) !important;
}
.ivu-table-wrapper-with-border {
  border-color: var(--thd_border_color, #e8eaec) !important;
}
.ivu-table-border:after,
.ivu-table-border:before,
.ivu-table:before {
  background-color: transparent !important;
}
.ivu-table-sort i {
  color: var(--table_sort_color, #c5c8ce) !important;
}
.ivu-table-sort i.on {
  color: var(--table_sorton_color, #2d8cf0) !important;
}
.ivu-table td,
.ivu-table th {
  border: none !important;
}
.ivu-table-small {
  font-size: 14px !important;
}
.ivu-table-header thead tr th {
  padding: 19px 0 !important;
}
.ivu-table td {
  height: 60px !important;
}
.tableBorder .ivu-table:after {
  background-color: transparent !important;
}
/*设置空白表格*/
.table_empty {
  width: 100%;
  height: 170px;
  background-position: center 20px;
  background-size: 130px 103px;
  background-image: url('../../assets/dashboard/fileempty.png');
  background-repeat: no-repeat;
  position: relative;
}
.table_empty .emptyText {
  position: absolute;
  display: block;
  width: 100%;
  text-align: center;
  top: calc(45% + 60px);
}
/*左右浮动*/
.fleft {
  float: left;
}
.fright {
  float: right;
}
/*边距*/
.mgT10 {
  margin-top: 10px;
}
.mgL10 {
  margin-left: 10px;
}
.ivu-tooltip-inner {
  background-color: #16436b !important;

}
/*下拉框*/
.ivu-select-visible {
  border-color: #05eeff !important;
}
.ivu-select {
  border: 1px solid transparent;
  border-radius: 3px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #060d15, #060d15), linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%);
}
.tool-btn .ivu-select-dropdown {
  width: 120px !important;
}
.ivu-select-dropdown {
  border: 1px solid #04478e;
  background-color: var(--input_b_color, #ffffff) !important;
}
.ivu-select-item,
.ivu-form .ivu-form-item-label {
  color: var(--input_font_color, #303748) !important;
}
.ivu-select-item:hover {
  background: var(--selectdrop_b_color, #ffffff) !important;
  color: var(--selectdrop_font_color, #303748) !important;
}
.select-dropdown p:hover {
  background: none !important;
}
.ivu-select-item.ivu-select-item-focus {
  background: none !important;
}
.ivu-select-item.ivu-select-item-selected {
  background: none !important;
  color: #2d8cf0 !important;
}
.ivu-select-input,
.ivu-select {
  color: var(--input_font_color, #303748) !important;
}
.ivu-select-selection,
.select-content .select-input input,
.ivu-radio-inner,
.ivu-select-selection,
.ivu-picker-panel-sidebar {
  background-color: var(--input_b_color, #ffffff) !important;
  border-color: transparent !important;
}
.ivu-picker-panel-sidebar {
  width: 102px !important;
}
.ivu-radio-inner:after {
  background-color: #05eeff !important;
}
.ivu-modal-content .ivu-radio-inner {
  background-color: transparent !important;
  border-color: #04478e !important;
}
/*下拉菜单*/
.ivu-dropdown-item:hover {
  background: #06324d !important;
  color: #00ffee !important;
}
.tool-btn .ivu-select-dropdown .ivu-dropdown-item {
  text-align: left !important;
  width: 77px !important;
  padding: 10px !important;
}
.ivu-dropdown-item {
  color: #dff1ff !important;
  font-size: 14px !important;
}
/*ivu-input*/
.ivu-input:focus {
  border-color: #05eeff !important;
}
.ivu-input {
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
.ivu-input,
.ivu-input-number-input,
.ivu-input-number,
.select-content .select-input input,
.ivu-select-selection {
  color: var(--input_font_color, #303748) !important;
  background-color: var(--input_b_color, #ffffff) !important;
}
.ivu-input-number {
  border-color: var(--border_color, #dddddd) !important;
}
.ivu-input-number .ivu-input-number-handler-wrap {
  background-color: var(--input_b_color, #ffffff) !important;
  opacity: 0.5;
}
.ivu-date-picker-cells-cell-range:before {
  background: #06324d !important;
}
.ivu-time-picker-cells-cell-selected,
.ivu-time-picker-cells-cell-selected:hover {
  color: #2d8cf0;
  background: #06324d !important;
}
.ivu-date-picker-cells-cell-selected em,
.ivu-date-picker-cells-cell-selected:hover em {
  border-radius: 16px !important;
}
.ivu-time-picker-cells-cell:hover {
  background: #06324d !important;
}
.ivu-picker-confirm .ivu-btn {
  color: #c5c8ce !important;
  background-color: #06324d !important;
  border-color: #06324d !important;
}
.ivu-picker-confirm .ivu-btn-text {
  color: #c5c8ce !important;
}
.ivu-picker-confirm .ivu-btn-text:hover,
.ivu-picker-confirm .ivu-btn-default:hover,
.ivu-picker-confirm .ivu-btn-primary:hover {
  color: #57a3f3 !important;
  background-color: #06324d !important;
}
.ivu-time-picker-with-range .ivu-picker-panel-content:after {
  content: '';
  /* background: #154b81 !important; */
  background:var(--picker_panel_content_after_color,#154b81) !important;

}
.ivu-time-picker-header {
  border-bottom: 1px solid #06324d !important;
}
.ivu-time-picker-cells-list {
  border-left: 1px solid #06324d !important;
}

.tabs-card > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before {
  background: transparent !important;
}
.ivu-tabs-tab:nth-child(2) {
  background-image: url('../../assets/dashboard/btn-tabs1-01.png') !important;
  background-size: 100% 100% !important;
}
.ivu-tabs-tab:not(.ivu-tabs-tab:nth-child(2)) {
  background-image: url('../../assets/dashboard/btn-tabs2-01.png') !important;
  background-size: 100% 100% !important;
}
.ivu-tabs-tab:nth-child(2).ivu-tabs-tab-active {
  background-image: url('../../assets/dashboard/btn-tabs1-02.png') !important;
  background-size: 100% 100% !important;
}
.ivu-tabs-tab:not(.ivu-tabs-tab:nth-child(2)).ivu-tabs-tab-active {
  background-image: url('../../assets/dashboard/btn-tabs2-02.png') !important;
  background-size: 100% 100% !important;
}
.ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
  border-color: transparent !important;
}
/*机构*/
/*任务列表*/
.icon-box {
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 22px 22px;
}
.icon-index {
  display: inline-block;
  vertical-align: top;
  background-image: url('../../assets/wisdom/zhibiao.png');
  margin-right: 10px;
}
.light-icon-index{
  display: inline-block;
  vertical-align: text-top;
  background-image: url('../../assets/wisdom/light-zhibiao.png');
  margin-right: 10px;
  border: 1px solid #C0C4CC;
}
.light-icon-index:hover{
  border-color: #0290FD;
}
.tableTitle {
  text-align: left;
  margin-bottom: 12px;
  font-size: 16px;
}
.tab-page {
  text-align: right;
  padding: 12px 0;
}
.ivu-page .page-btn {
  margin: 0 20px;
}
.ivu-page .ivu-page-item.ivu-page-item-active {
  border-color: #05eeff !important;
  color: #06324d !important;
  background-color: var(--page_b_color, #fff) !important;
}
.ivu-page .ivu-page-custom-text {
  border: none;
}
.tab-page .ivu-page-item,
.ivu-page-item-jump-next,
.ivu-page-item-jump-prev,
.ivu-page-next,
.ivu-page-prev {
  background-color: var(--input_b_color, #ffffff) !important;
  border-color: var(--border_color, #e8eaec) !important;
  color: var(--input_font_color, #303748) !important;
}
.ivu-page-next,
.ivu-page-prev,
.ivu-page-item-jump-next,
.ivu-page-item-jump-prev,
.ivu-page-next,
.ivu-page-prev,
.ivu-page-options-elevator input {
  background-color: var(--input_b_color, #ffffff) !important;
  color: var(--input_font_color, #303748) !important;
}
.ivu-page-prev,
.ivu-page-next {
  background-color: transparent !important;
}
.ivu-page-item a,
.ivu-page-next a,
.ivu-page-prev a {
  color: var(--input_font_color, #303748) !important;
}
.ivu-checkbox .ivu-checkbox-inner,
.ivu-checkbox-disabled .ivu-checkbox-inner {
  background-color: var(--input_b_color, #ffffff) !important;
  border-color: var(--border_color, #e8eaec) !important;
}
.ivu-checkbox-indeterminate .ivu-checkbox-inner {
  background-color: var(--input_checkbox_b_color, #2d8cf0) !important;
}
.ivu-checkbox-checked .ivu-checkbox-inner {
  background-color: var(--input_checkbox_b_color, #2d8cf0) !important;
}
.ivu-checkbox-wrapper .ivu-checkbox-checked .ivu-checkbox-inner:after {
  content: '';
  display: table;
  width: 4px;
  height: 8px;
  position: absolute;
  top: 2px;
  left: 5px;
  border: 2px solid #05eeff;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg) scale(1);
  transition: all 0.2s ease-in-out;
}
/*机构*/
.orgDiv {
  position: absolute;
  height: 260px;
  overflow: hidden;
  width: 100%;
  padding: 0 10px;
  background: var(--selectdrop_b_color, #ffffff);
  z-index: 9;
  border-width: 1px;
  border-style: solid;
  border-top: 0;
  border-color: var(--border_color, #ddd);
  box-shadow: 0 0 6px var(--border_color, #ddd);
}
.orgDiv > .title {
  background: var(--modal_header_b_color, radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9)));
  color: white;
  margin: 0 -10px;
  padding: 0 10px;
}
.orgTreeScroll {
  width: calc(100% + 20px);
  height: calc(100% - 25px);
  overflow-y: scroll;
}
.orgTreeScroll overscroll-behavior:none .ivu-tree-title {
  color: var(--input_font_color, #303748);
}
.ivu-tree-title {
  color: var(--input_font_color, #303748) !important;
}
 /deep/ .noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}
 /deep/ .noIcon .ivu-icon-ios-arrow-down:before {
  content: '';
}
.ivu-tag {
  background-color: var(--ivu_tag_border, #ffffff) !important;
}
.ivu-tag .ivu-tag-text {
  color: var(--input_font_color, #303748) !important;
}
span.ivu-date-picker-cells-cell-disabled {
  background-color: var(--time_disabled_b_color, #ffffff) !important;
  color: var(--input_font_color, #303748) !important;
}
.ivu-date-picker-cells-cell:hover em,
.ivu-picker-panel-shortcut:hover {
  background: var(--time_hover_b_color, #e1f0fe) !important;
}
span.ivu-date-picker-cells-cell-disabled:hover em {
  background: inherit !important;
}
.ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused,
.ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused:hover em,
.ivu-date-picker-cells-year .ivu-date-picker-cells-cell-focused:hover em {
  background-color: #2d8cf0 !important;
}
.ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused,
.ivu-date-picker-cells-year .ivu-date-picker-cells-cell-focused {
  background-color: transparent !important;
}
.look-icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-image: url('../../assets/wisdom/zhibiao.png');
  cursor: pointer;
}
.light-look-icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-image: url('../../assets/wisdom/light-zhibiao.png');
  cursor: pointer;
  border: 1px solid #C0C4CC;
}
/* 修改悬停时的边框颜色 */
.light-look-icon:hover {
  border-color: #0290FD; /* 悬停时的边框颜色 */
}
.ivu-table {
  background-color: transparent !important;
}
/*仪表盘*/
.dashboardSection .tabsBody .tabsBodyItem .itemHeader {
  padding: 0px 17px;
  height: 25px;
  border-left: none;
}
.dashheader {
  color: var(--dash_h_f_color, #303748);
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
}
#aa.ivu-tabs {
  color: var(--header_list_font_color, #303748);
}
#aa.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  border-color: transparent;
  background: transparent;
  min-width: 140px;
  margin-right: -20px;
}
#aa .ivu-tabs-bar {
  border-color: var(--border_color, #dddddd);
}
#aa.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
  color: #060d15;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  background: transparent;
  border-color: transparent;
  z-index: 2000;
}
.ivu-btn {
  border-width: 0;
}
.ivu-input-word-count {
  background-color: var(--modal_b_color, #ffffff) !important;
  color: var(--font_color, #303748) !important;
}
.ivu-date-picker-header {
  border-bottom: 1px solid transparent !important;
}
.ivu-picker-confirm {
  border-top: 1px solid transparent !important;
}
.divider {
  display: flex;
  width: 100%;
  align-items: center;
  margin-bottom: 10px;
}
.divider .divider-title {
  width: 110px;
  font-size: 14px;
  font-weight: bold;
  margin-left: 30px;
  color: var(--th_font_color, #303748) !important;
}
.divider .line {
  width: 100%;
  height: 1px;
  /* background-color: var(--border_color , #06324d); */
  border-top:1px solid var(--border_color , #06324d);
}
.ivu-divider {
  background: #06324d !important;
}
.phystopo > .ivu-divider {
  background: transparent !important;
}
.phystopo .ivu-divider-horizontal.ivu-divider-with-text-center:after,
.ivu-divider-horizontal.ivu-divider-with-text-center:before,
.ivu-divider-horizontal.ivu-divider-with-text-left:after,
.ivu-divider-horizontal.ivu-divider-with-text-left:before,
.ivu-divider-horizontal.ivu-divider-with-text-right:after,
.ivu-divider-horizontal.ivu-divider-with-text-right:before {
  content: '';
  display: table-cell;
  top: 50%;
  width: 50%;
  border-top: 1px solid var(--border_color , #06324d) !important;
  transform: translateY(50%);
}
.cancel-btn {
  height: 36px;
  background: #061824 !important;
  border-radius: 4px 4px 4px 4px !important;
  opacity: 0.74 !important;
  color: #5ca0d5 !important;
  border: 1px solid #04478e !important;
}
.primary-btn {
  height: 36px !important;
  width: 74px !important;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%) !important;
  border-radius: 4px 4px 4px 4px !important;
  opacity: 1 !important;
  color: #060d15 !important;
  font-weight: normal !important;
  border-color: #57a3f3 !important;
}
.fontSiz-20 {
  font-size: 20px !important;
}
.query-btn {
  width: 60px !important;
  height: 38px !important;
  line-height: 38px !important;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%) !important;
  border-radius: 4px !important;
  font-size: 22px !important;
  color: #060d15 !important;
  display: flex !important;
  justify-content: center !important;
}
.outLine-btn {
  width: 60px !important;
  height: 35px !important;
  border: 1px solid transparent !important;
  border-radius: 4px;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 0%, #31f0fe 51%, #015197 100%) !important;
  color: #05eeff !important;
  justify-content: center !important;
}
.tool-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: -60px;
}
.tool-btn div {
  display: flex;
}
.tool-btn button {
  margin-right: 8px;
}
.tool-btn .more-btn {
  width: 100% !important;
  height: 38px !important;
  color: #02b8fd;
  background-color: #061824;
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
.ivu-select-single .ivu-select-selection {
  width: 100%;
}
.jiaHao-btn {
  width: 60px !important;
  height: 35px !important;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%) !important;
  border-radius: 4px !important;
  color: #060d15 !important;
}
.chongZhi-btn {
  width: 60px !important;
  height: 35px !important;
  border: 1px solid transparent !important;
  border-radius: 4px;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 0%, #31f0fe 51%, #015197 100%) !important;
  color: #05eeff !important;
}
.daoChu-btn {
  width: 80px !important;
  height: 35px !important;
  border: 1px solid transparent !important;
  border-radius: 4px;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 0%, #31f0fe 51%, #015197 100%) !important;
  color: #05eeff !important;
  justify-content: center !important;
}
.delete-btn {
  width: 60px !important;
  height: 38px !important;
  border: 1px solid transparent !important;
  border-radius: 4px;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #570202 0%, #ff5153 50%, #970101 100%) !important;
}
.create1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-task.png');
  background-repeat: no-repeat;
}
.look1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-look.png');
  background-repeat: no-repeat;
}
.edit1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-edit.png');
  background-repeat: no-repeat;
}
.edit2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-edit02.png');
  background-repeat: no-repeat;
}
.del1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-del.png');
  background-repeat: no-repeat;
}
.del2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-del-02.png');
  background-repeat: no-repeat;
}
.yc1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-yc.png');
  background-repeat: no-repeat;
}
.yc2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-yc02.png');
  background-repeat: no-repeat;
}
.handle1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-handle.png');
  background-repeat: no-repeat;
}
.freeze1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-freeze.png');
  background-repeat: no-repeat;
}
.cancel1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-cancel.png');
  background-repeat: no-repeat;
}
.activate1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-activate.png');
  background-repeat: no-repeat;
}
.dormant1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-dormant.png');
  background-repeat: no-repeat;
}
.sp1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-sp.png');
  background-repeat: no-repeat;
}
.password1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-password-01.png');
  background-repeat: no-repeat;
}
.manage1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-manage01.png');
  background-repeat: no-repeat;
}
.manage2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-manage02.png');
  background-repeat: no-repeat;
}
.cf2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-cf.png');
  background-repeat: no-repeat;
}
.gl1-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-filter.png');
  background-repeat: no-repeat;
}
.zj-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-zj.png');
  background-repeat: no-repeat;
}
.zjzt-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-zjzt.png');
  background-repeat: no-repeat;
}
.yczt-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-yczt.png');
  background-repeat: no-repeat;
}
.sjtb-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/icon-sjtb.png');
  background-repeat: no-repeat;
}
.dow-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-download.png');
  background-repeat: no-repeat;
}
.dow-btn-dis {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url('../../assets/btn-img/btn-download-dis.png');
  background-repeat: no-repeat;
}
.ivu-form-item-error-tip {
  color: #fe5c5c !important;
}
.textClass {
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:root {
  /*--body_b_color: #060D15;*/
  --font_color: #303748;
  --border_color: #dddddd;
  --dash_b_color: rgba(1, 22, 56, 0.9);
  --th_b_color: #f1f6fe;
  --td_b_color: #ffffff;
  --th_font_color: #303748;
  --td_font_color: #303748;
  --thd_border_color: #e8eaec;
  --table_sort_color: #c5c8ce;
  --table_sorton_color: #2d8cf0;
  --input_font_color: #303748;
  --input_b_color: #ffffff;
  --selectdrop_font_color: #303748;
  --selectdrop_b_color: #ffffff;
  --confirmModal_font_color: #17233d;
  --input_placeholder_color: #c5c8ce;
  --org_btn_bg_color: #57c5f7;
  --table_checkbox_border_color: #e8eaec;
  --dash_h_b_color: #f4f6f9;
  --dash_h_f_color: #303748;
  --dash_b_b_color: #ffffff;
  --dash_border_color: #dddddd;
  /*按钮样式重新*/
  --primary_bcg: #2d8cf0;
  --primary_border: #2d8cf0;
  --primary_font: #fff;
  --warning_bcg: #ffad33;
  --warning_border: #ffad33;
  --warning_font: #fff;
  --success_bcg: #47cb89;
  --success_border: #47cb89;
  --success_font: #fff;
  --error_bcg: #f16643;
  --error_border: #f16643;
  --error_font: #fff;
  --ivu_tag_border: #e8eaec;
  --ivu-select-arrow: #808695;
  --modal_header_b_color: radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9));
  --modal_b_color: #ffffff;
  --modal_input_border_color: #dcdee2;
  --contentBox_bgcolor: 'none';
  --header_b_color: '#1252c8';
  --header_font_color: '#ffffff';
  --header_active_bg_color: 'rgba(125, 165, 232, 0.16)';
  --header_list_bg_color: '#ffffff';
  --header_list_font_color: '#303748';
  --header_list_activefont_color: '#ffffff';
  --header_list_activebg_color: '#7da5e8';
  --message_bg: 'rgba(255,255,255)';
  --chartBak: '#303748';
  --link_b_color: '#f4f6f9';
  --modal_footer_butok_b_color: '#2d8cf0';
  --confirmmodal_footer_cancel_b_color: 'transparent';
}
body {
  background-color: var(--body_b_color, #f4f6f9) !important;
  color: var(--font_color, #303748) !important;
}
.ivu-modal-content {
  background-color: var(--modal_b_color, #ffffff) !important;
  color: var(--font_color, #303748) !important;
}
.ivu-modal-content .select-content .select-input input,
.ivu-modal-content .ivu-radio-inner,
.ivu-modal-content .ivu-picker-panel-sidebar {
  background-color: transparent !important;
  border-color: var(--modal_input_border_color, #dcdee2) !important;
}
.ivu-modal-content .ivu-input,
.ivu-modal-content .ivu-input-number-input,
.ivu-modal-content .ivu-input-number,
.ivu-modal-content .select-content .select-input {
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
input,
select,
textarea,
table {
  border-color: var(--border_color, #dddddd) !important;
}
.ivu-modal-content {
  text-align: left;
}
.ivu-modal-confirm-head {
  display: flex !important;
}
.ivu-modal-confirm-head .ivu-modal-confirm-head-icon {
  font-size: 20px !important;
}
.ivu-modal-confirm-head-title {
  color:var(--modal_title_name_color,#00FFEE) !important;
  line-height: 16px;
}
.ivu-modal-confirm .ivu-modal-confirm-body {
  color:var(--modal_title_name_color_two , #fff) !important;
  font-size: 14px !important;
  margin-top: 20px !important;
}
.ivu-modal-confirm-footer .ivu-btn-text span {
  color: var(--confirmModal_font_color, #17233d);
}
.ivu-modal-confirm-footer .ivu-btn-text:hover {
  background-color: transparent;
}
.ivu-modal-confirm-footer .ivu-btn-primary {
  background: var(--modal_footer_but_ok_background_color) !important;
  border-radius: 4px !important;
  color: var(--modal_footer_butok_b_color , #060D15) !important;
}
.ivu-spin-fix {
  background-color: transparent !important;
}
.ivu-divider {
  color: var(--font_color, #303748) !important;
}
.ivu-select-input::-webkit-input-placeholder,
.ivu-select-multiple .ivu-select-selection .ivu-select-placeholder,
.ivu-input::-webkit-input-placeholder,
.ivu-select-single .ivu-select-selection .ivu-select-placeholder {
  color: var(--input_placeholder_color, #c5c8ce) !important;
}
::-webkit-input-placeholder,
.selectContent .content p.placeholder {
  color: var(--input_placeholder_color, #c5c8ce) !important;
}
.selectContent .content {
  background: transparent !important;
  border: 1px solid var(--modal_input_border_color, #dcdee2) !important;
}
.noIcon + .ivu-btn-info {
  background: var(--org_btn_bg_color, #57c5f7) !important;
  border: none;
}
.ivu-table .ivu-checkbox .ivu-checkbox-inner {
  border-color: var(--table_checkbox_border_color, #e8eaec);
}


/* 选中状态 */
.ivu-checkbox-checked .ivu-checkbox-inner {
    border-color: var(--from_checkbox_check_border_color, #2d8cf0) !important;
}

.skinPrimary {
  background-color: var(--primary_bcg, #2d8cf0) !important;
  border-color: var(--primary_border, #2d8cf0) !important;
  color: var(--primary_font, #fff) !important;
  border-style: solid;
  border-width: 1px !important;
}
.skinWarning {
  background-color: var(--warning_bcg, #ffad33) !important;
  border-color: var(--warning_border, #ffad33) !important;
  color: var(--warning_font, #fff) !important;
  border-style: solid;
  border-width: 1px !important;
}
.skinSuccess {
  background-color: var(--success_bcg, #47cb89) !important;
  border-color: var(--success_border, #47cb89) !important;
  color: var(--success_font, #fff) !important;
  border-style: solid;
  border-width: 1px !important;
}
.skinError {
  background-color: var(--error_bcg, #f16643) !important;
  border-color: var(--error_border, #f16643) !important;
  color: var(--error_font, #fff) !important;
  border-style: solid;
  border-width: 1px !important;
}
body .ivu-message-notice-with-background {
  background: transparent !important;
}
body .ivu-message-notice-content {
  background: var(--message_bg, rgba(255, 255, 255)) !important;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2) !important;
}
.ivu-message-notice-with-background .ivu-message-notice-content-warning {
  border: 1px solid var(--message_warning_color, #fff) !important;
  color: var(--message_font_warning, #515a6e) !important;
}
.ivu-message-notice-with-background .ivu-message-notice-content-error {
  border: 1px solid var(--message_err_color, #fff) !important;
  color: var(--message_font_err, #515a6e) !important;
}
.ivu-message-notice-with-background .ivu-message-notice-content-success {
  border: 1px solid var(--message_success_color, #fff) !important;
  color: var(--message_font_success, #515a6e) !important;
}
.ivu-tag {
  border-color: var(--ivu_tag_border, #e8eaec) !important;
}
.ivu-select-arrow,
.ivu-icon-ios-close {
  color: var(--ivu_select_arrow, #808695) !important;
}
.skinError .ivu-icon-ios-close {
  color: var(--error_font, #fff) !important;
}
.ivu-modal .ivu-icon-ios-close {
  color: #00ffee !important;
}
.ivu-select-multiple .ivu-select-item-selected {
  background-color: transparent !important;
  color: #2d8cf0 !important;
}
.ivu-select-item.ivu-select-item-focus {
  color: none !important;
}
.ivu-tree-title {
  background: none !important;
}
.orgDiv {
  position: absolute;
  height: 260px;
  overflow: hidden;
  width: 100%;
  padding: 0 10px;
  background: var(--selectdrop_b_color, #ffffff);
  z-index: 9;
  border-width: 1px;
  border-style: solid;
  border-top: 0;
  border-color: var(--border_color, #ddd);
  box-shadow: 0 0 6px var(--border_color, #ddd);
}
.orgDiv > .title {
  background: var(--modal_header_b_color, radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9)));
  color: white;
  margin: 0 -10px;
  padding: 0 10px;
}
 /deep/ .orgTreeScroll {
  width: calc(100% + 20px);
  height: calc(100% - 32px);
  overflow-y: scroll;
  overflow-x: hidden;
}
 /deep/ .orgTreeScroll .ivu-tree-title {
  color: var(--input_font_color, #303748);
}
.contentBox_bg {
  background: var(--contentBox_bgcolor, none);
  border-radius: 5px;
}
.rpapth_a_link {
  color: var(--input_font_color, #303748);
}
.ivu-modal-content .ivu-modal-footer button:last-child {
  width: 74px;
  height: 38px;
  color: #060d15;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #05ebeb;
}
.ivu-modal-confirm-footer .ivu-btn-text {
  background-color: var(--confirmmodal_footer_cancel_b_color, transparent);
  color: var(--modal_footer_but_b_color, #5CA0D5) !important;
  border: 1px var(--modal_footer_but_cancel_background_border_color,#015197) solid !important;
}
.ivu-modal-confirm-footer .ivu-btn-text:hover {
  background-color: var(--confirmmodal_footer_cancel_b_color, transparent) !important;
}
.ivu-table-fixed-body {
  background-color: var(--modal_b_color, #ffffff) !important;
}
.emptyText {
  color: var(--font_color, #303748) !important;
}
body.bigscreen {
  background: #1a222e !important;
}
.ivu-btn {
  padding: 0 13px!important;
}
.wisdom-fault-top {
  border-radius: 4px;
}
.wisdom-content {
  margin-top: 20px;
}
.ivu-checkbox-inner {
  border-radius: 4px!important;
}
.inlineBlock {
  display: inline-block;
}
.ivu-table-wrapper .ivu-table .ivu-table-header thead tr th {
  padding: 0;
  height: 50px;
}
.ivu-table-wrapper .ivu-table td {
  height: 50px;
}
.ivu-table .ivu-table-tip td {
  border-bottom: none;
}
/*设置空白表格*/
.table_empty {
  width: 100%;
  height: 170px;
  background-position: center 20px;
  background-size: 130px 103px;
  background-image: url("../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  position: relative;
}
.table_empty .emptyText {
  position: absolute;
  display: block;
  width: 100%;
  text-align: center;
  top: calc(45% + 60px);
}
/*公共search部分样式start*/
.sectionBox .section-top {
  border-radius: 4px;
  padding: 20px 20px 0 20px;
}
.sectionBox .section-top .section-top-content {
  text-align: left;
}
.sectionBox .section-top .section-top-content .section-top-text {
  display: inline-block;
  margin-bottom: 20px;
  margin-right: 24px;
}
.sectionBox .section-top .section-top-content .section-top-text label {
  float: left;
  text-align: right;
  line-height: 32px;
}
.sectionBox .section-top .section-top-content .section-top-text .section-text {
  min-width: 200px;
  display: inline-block;
  margin-left: 16px;
}
.sectionBox .section-top .section-top-content .section-top-text .section-text select,
.sectionBox .section-top .section-top-content .section-top-text .section-text input {
  width: 100%;
}
.sectionBox .section-top .section-top-content .section-top-text:first-child {
  margin-left: 0;
}
.sectionBox .section-top .section-top-btn {
  text-align: right;
}
.sectionBox .section-top .section-top-btn button {
  margin-left: 8px;
  margin-bottom: 20px;
}
.sectionBox .section-top .section-top-btn button:first-child {
  margin-left: 0;
}
.sectionBox .section-body {
  margin-top: 16px;
}
.sectionBox .section-body .section-body-left {
  border-radius: 4px;
  width: 350px;
  float: left;
}
.sectionBox .section-body .section-body-right {
  border-radius: 4px;
  padding: 20px;
  margin-left: 350px;
}
.sectionBox .section-body .section-body-content {
  padding: 20px;
}
.sectionBox .section-body .tableTitle {
  line-height: 32px;
  text-align: left;
}
.sectionBox .look-icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-image: url("../../assets/wisdom/zhibiao.png");
  margin-right: 10px;
}
.sectionBox .light-look-icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-image: url("../../assets/wisdom/light-zhibiao.png");
  margin-right: 10px;
  border: 1px solid #C0C4CC;
}
/* 修改悬停时的边框颜色 */
.sectionBox .light-look-icon:hover {
  border-color: #0290FD; /* 悬停时的边框颜色 */
}
.bigscreen .section-body-content {
  background: rgba(1, 22, 56, 0.9) !important;
}
/*公共search部分样式end*/
/**/
/*modal模态框表单样式*/
.ivu-form.overiteForm {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.ivu-form.overiteForm .ivu-form-item {
  width: 50%;
}
.ivu-form.overiteForm .ivu-form-item.width_100 {
  width: 100%;
}
.modal_search {
  margin-bottom: 12px;
}
.modal_search .condition_item {
  display: inline-block;
  margin-right: 20px;
}
.modal_search .condition_item .item_lable {
  float: left;
  line-height: 32px;
}
.modal_search .condition_item .item_input {
  display: inline-block;
}
.modal_search .condition_item:last-child {
  margin-right: 0;
}
.modalLook {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.modalLook .look_item {
  line-height: 16px;
  height: 32px;
  margin-top: 5px;
}
.modalLook .look_item label {
  width: 120px;
  text-align: right;
  float: left;
}
.modalLook .look_item p {
  margin-left: 120px;
}
.modalLook .width_50 {
  width: 50%;
}
.modalLook .width_100 {
  width: 100%;
}
/*表单width50%*/
.width_50_Form .ivu-form-item {
  display: inline-block;
  width: 50%;
}
.width_50_Form .ivu-form-item.width-auto {
  width: auto;
}
.width_50_Form .notRequired .ivu-form-item-label::before {
  display: none;
}
.width_50_Form .ivu-form-item.width_100_item {
  width: 100%;
}
.width_50_Form .formTime > label {
  width: 140px !important;
  margin-left: -22px !important;
}
.width_50_Form .formKey > label {
  width: 172px !important;
  margin-left: -52px !important;
}
.width_50_Form .childContent > div {
  margin-left: 120px;
}
.width_50_Form .childContent > div .myDateClass {
  width: 120px;
  margin-bottom: 6px;
}
.width_50_Form .ruleIcon {
  line-height: 32px;
  vertical-align: middle;
}
.width_50_Form .ipBox .ivu-form-item-label:before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 14px;
  color: #ed4014;
}
.width_50_Form .ipitemBox {
  display: inline-block;
}
.width_50_Form .ipItem {
  width: 50px!important;
}
.width_50_Form .ipitemspan {
  padding: 0 5px;
  font-weight: bold;
}
.fn_tool button {
  margin-right: 8px;
}
.fn_tool button:last-child {
  margin-right: 0;
}
/***flex布局兼容性问题解决*****/
.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
}
.flex .flex-item {
  -webkit-box-flex: 1;
  -moz-flex-grow: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
}
.flex .flex-item2 {
  flex-basis: 25%;
}
.flex .flex-item3 {
  flex-basis: 33.3%;
}
.flex .flex-item5 {
  flex-basis: 50%;
}
.flex .flex-item6 {
  flex-basis: 60%;
}
.flex .flex-item4 {
  flex-basis: 40%;
}
.flex-wrap {
  flex-direction: row;
  flex-wrap: wrap;
}
.flex-center {
  -webkit-box-pack: center;
  -moz-justify-content: center;
  -webkit-justify-content: center;
  justify-content: center;
}
.flex-between {
  -webkit-box-pack: space-between;
  -moz-justify-content: space-between;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
.flex-colume {
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
}
.homePage {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 20px;
}
.pageTitles {
  width: 100%;
  height: 32px;
  line-height: 32px;
  text-align: left;
  padding-left: 10px;
  color: #333;
  font-size: 1em;
  font-weight: 600;
}
.pageTitles span {
  display: inline-block;
  float: right;
}
.pageTitles span b {
  font-size: 20px;
}
.pdL10 {
  padding-left: 10px;
}
.pdL20 {
  padding-left: 20px;
}
.pdL30 {
  padding-left: 30px;
}
.pdR10 {
  padding-right: 10px;
}
.pdR20 {
  padding-right: 20px;
}
.pdR30 {
  padding-right: 30px;
}
.pdB10 {
  padding-bottom: 10px;
}
.pdB20 {
  padding-bottom: 20px;
}
.pdB30 {
  padding-bottom: 30px;
}
.pdT10 {
  padding-top: 10px;
}
.pdT20 {
  padding-top: 20px;
}
.pdT30 {
  padding-top: 30px;
}
.fleft {
  float: left;
}
.fright {
  float: right;
}
.mgL5 {
  margin-left: 5px;
}
.mgL10 {
  margin-left: 10px;
}
.mgL15 {
  margin-left: 15px;
}
.mgL20 {
  margin-left: 20px;
}
.mgL100 {
  margin-left: 100px;
}
.mgR5 {
  margin-right: 5px;
}
.mgR10 {
  margin-right: 10px;
}
.mgR15 {
  margin-right: 15px;
}
.mgR20 {
  margin-right: 20px;
}
.mgT5 {
  margin-top: 5px;
}
.mgT10 {
  margin-top: 10px;
}
.mgT15 {
  margin-top: 15px;
}
.mgT20 {
  margin-top: 20px;
}
.mgB5 {
  margin-bottom: 5px;
}
.mgB10 {
  margin-bottom: 10px;
}
.mgB15 {
  margin-bottom: 15px;
}
.mgB20 {
  margin-bottom: 20px;
}
.mgB40 {
  margin-bottom: 32px;
}
.subMenuBox {
  width: 100%;
  height: 32px;
  position: relative;
  margin-bottom: 20px;
}
.subMenuBox .btnSearchClass {
  background: #4E7BFF;
  color: #fff;
  border: 1px solid #4E7BFF;
  height: 100%;
  border-radius: 4px;
}
.subMenuBox .btnDeleteClass {
  background: #e13d13;
  color: #fff;
  border: 1px solid #e13d13;
  height: 100%;
  border-radius: 4px;
}
.subMenuBox .btnStopClass {
  background: #f29100;
  color: #fff;
  border: 1px solid #f29100;
  height: 100%;
  border-radius: 4px;
}
.subMenuBox .btnStartClass {
  background: #18b566;
  color: #fff;
  border: 1px solid #18b566;
  height: 100%;
  border-radius: 4px;
}
.subMenuBox .btnImportClass {
  background: #2EC7C9;
  color: #fff;
  border: 1px solid #2EC7C9;
  height: 100%;
  border-radius: 4px;
}
.subMenuBox .seachInput {
  height: 100%;
}
.subMenuBox .seachInput .ivu-input {
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
}
.subMenuBox .ivu-select-selection {
  border-radius: 4px;
  border: 1px solid #ECEDF1;
}
.subMenuBox .ivu-select-single .ivu-select-selection {
  height: 32px;
}
.subMenuBox .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.subMenuBox .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  height: 32px;
  line-height: 32px;
}
.publicTableBox {
  width: 100%;
  height: auto;
  position: relative;
  border: 1px solid #ECEDF1;
  background: #fff;
}
.defaultTableBox {
  width: 50%;
  height: auto;
  position: relative;
  border: 1px solid #FAFAFA;
  background: #FAFAFA;
}
.lookBox {
  margin-top: 30px;
}
.lookBox .title {
  font-size: 16px;
  color: var(modal_title_name_color , #fff);
  margin-left: 0px;
}
.lookBox .title span.alias {
  color: #ff0000;
}
.lookBox .contain .echartStyle {
  height: 240px;
  width: 100%;
}
.ivu-modal .ivu-modal-content {
  border: 1px solid transparent;
  border-radius: 3px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: var(--modal_background_color)
}
.ivu-modal .ivu-modal-content .ivu-table td,
.ivu-modal .ivu-modal-content .ivu-table th {
  min-width: 0;
  height: 48px;
  box-sizing: border-box;
  text-align: left;
  text-overflow: ellipsis;
  vertical-align: middle;
  border-bottom: 1px solid var(--table_td_th_border_color,#032A4D) !important;
  font-weight: 200 !important;
}
.ivu-modal .ivu-modal-content .title-name {
  color: var(--modal_title_name_color ,#00FFEE);
  font-size: 16px;
  font-weight: bolder;
}
.ivu-modal .ivu-modal-content .title-content {
  color: var(--modal_title_name_color_two ,#fff);
  font-size: 13px;
}
.ivu-modal .ivu-modal-content .modalTable {
  font-size: 14px;
  font-weight: 400 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
}
.ivu-modal .ivu-modal-content .eventTable .ivu-table td,
.ivu-modal .ivu-modal-content .eventTable .ivu-table th {
  border-bottom: 1px #06324D solid !important;
}
.ivu-modal .ivu-modal-content .eventTable .ivu-table-header thead tr th {
  padding: 15px 0 !important;
}
.ivu-modal .ivu-modal-content .eventTable .ivu-table td {
  height: 50px !important;
}
.ivu-modal .ivu-modal-content .Treeselect {
  position: relative;
  cursor: pointer;
}
.ivu-modal .ivu-modal-content .Treeselect .treeDiv {
  position: absolute;
  max-height: 150px;
  width: 100%;
  border: 1px solid var(--border_color , #04478e);
  border-radius: 5px;
  background-color: var(--input_b_color, #fff);
  z-index: 1000;
  padding: 1px 10px;
  top: 33px;
  overflow: auto;
  overscroll-behavior: none;
  z-index: 2000;
}
.ivu-modal .ivu-modal-content .ivu-input-icon {
  color: var(--ivu_select_arrow , #05EEFF);
}
.ivu-modal .ivu-modal-close .ivu-icon-ios-close {
  color: #fff;
}
.ivu-modal .ivu-modal-close .ivu-icon-ios-close:hover {
  color: #fff;
}
.ivu-modal .ivu-modal-header {
  border-bottom: 1px solid transparent;
  background-color: #08101A;
}
.ivu-modal .ivu-modal-header .ivu-modal-header-inner,
.ivu-modal .ivu-modal-header p {
  color: #31F0FE;
  font-weight: bold;
  font-size: 18px;
}
.ivu-modal .ivu-modal-content .ivu-modal-footer {
  text-align: center;
}
.ivu-modal .ivu-modal-content .ivu-modal-footer .btnConfirm {
  width: 74px;
  height: 32px;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  color: #042039;
  border: none;
  cursor: pointer;
}
.ivu-modal .ivu-modal-footer {
  border-top: 1px solid transparent;
}
.ivu-page-item-jump-next i,
.ivu-page-item-jump-prev i,
.ivu-page-next i,
.ivu-page-prev i {
  line-height: 2 !important;
}
.ivu-page-item {
  background-color: transparent !important;
}
.query-btn {
  width: 60px !important;
  height: 38px !important;
  background: linear-gradient(357deg, #049DEC 0%, #05EBEB 100%) !important;
  border-radius: 4px !important;
  font-size: 24px !important;
  color: #060D15 !important;
}
.ivu-modal-content .ivu-modal-footer button {
  width: 90px;
  color: #fff;
  border-radius: 0;
}
.ivu-modal-content .ivu-modal-footer button:first-child {
  width: 74px;
  height: 38px;
  color:var(--modal_footer_but_cancel_color,#5CA0D5);
  background: var(--modal_footer_but_cancel_background_color,#061824);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid var(--modal_footer_but_cancel_background_border_color,#015197);
}
.headerSelectArea .ivu-select-selection {
  border: none;
  background-color: #f1f6fe;
}
.headerSelectArea.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
.v-transfer-dom .ivu-icon-ios-help-circle:before {
  content: "\f107";
}
.topology {
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}
.topology .topology-noData {
  font-size: 14px;
  color: var(--font_color, #f4f6f9);
  text-align: center;
  height: 138px;
  line-height: 138px;
}
.topology .topology-content {
  display: flex;
  font-size: 0;
  margin: 0 0 5px;
}
.topology .topology-content .topology-box {
  display: flex;
  margin-left: -30px;
}
.topology .topology-content .topology-box .icon-text i {
  display: block;
  width: 52px;
  height: 52px;
  border-style: dashed;
  border-width: 1px;
  border-color: transparent;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 45px 45px;
  margin: 0 auto;
}
.topology .topology-content .topology-box .icon-text i.icon-click {
  border-color: #2d8cf0;
}
.topology .topology-content .topology-box .icon-text .two-icon-4 {
  background-image: url("../assets/png/40.png");
}
.topology .topology-content .topology-box .icon-text .two-icon-1 {
  background-image: url("../assets/png/10.png");
}
.topology .topology-content .topology-box .icon-text .two-icon-2 {
  background-image: url("../assets/png/20.png");
}
.topology .topology-content .topology-box .icon-text .two-icon-3 {
  background-image: url("../assets/png/30.png");
}
.topology .topology-content .topology-box .icon-text .two-icon-5 {
  background-image: url("../assets/png/50.png");
}
.topology .topology-content .topology-box .icon-text .icon-source {
  background-image: url("../assets/topology/icon-c-blue.png");
}
.topology .topology-content .topology-box .icon-text .icon-route {
  background-image: url("../assets/topology/icon-ip-bule.png");
}
.topology .topology-content .topology-box .icon-text .icon-server {
  background-image: url("../assets/topology/icon-target-blue.png");
}
.topology .topology-content .topology-box .icon-text .icon-cloud {
  background-image: url("../assets/topology/icon-yun-blue.png");
}
.topology .topology-content .topology-box .icon-text .two-icon {
  background-image: url("../assets/topology/icon-switch-blue.png");
}

.topology .topology-content .topology-box .icon-text .virtual-node-icon {
  background-image: url("../assets/topology/icon-virtual-blue.png");
}

.topology .topology-content .topology-box .icon-text .text {
  min-width: 110px;
  margin-top: 10px;
  word-wrap: break-word;
  line-height: 14px;
  font-size: 14px;
  color: var(--font_color, #f4f6f9);
  text-align: center;
}
.topology .topology-content .topology-box .icon-text .fontHidden {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 30px;
  position: absolute;
}
.topology .topology-content .topology-box .icon-text .icon-hover {
  cursor: pointer;
}
.topology .topology-content .topology-box .icon-text:last-child {
  margin-right: 0;
}
.topology .topology-content .topology-box .line-box {
  position: relative;
  z-index: 1;
  width: 142px;
  margin-left: -30px;
  cursor: pointer;
}
.topology .topology-content .topology-box .line-box .line {
  width: 100%;
  border: 1.5px solid;
  border-color: #00FFEE;
}
.topology .topology-content .topology-box .line-box .text {
  font-size: 14px;
  height: 14px;
  line-height: 14px;
  text-align: center;
  color: #00FFEE;
}
.topology .topology-content .topology-box .line-box .delay {
  margin-bottom: 9px;
}
.topology .topology-content .topology-box .line-box .loss {
  margin-top: 9px;
}
.topology .topology-content .topology-box .line-box .icon-error {
  position: absolute;
  top: 24px;
  left: 50%;
  width: 18px;
  height: 18px;
  background-image: url("../assets/topology/icon-error.png");
  background-position: center center;
  background-size: 18px 18px;
  background-repeat: no-repeat;
  margin-top: -9px;
  margin-left: -9px;
}
.topology .topology-content .topology-box .line-box:hover .line {
  border: 3px solid #00FFEE;
  margin-top: -3px;
}
.topology .topology-content .topology-box .ine-click .line {
  border: 3px solid #00FFEE;
  margin-top: -3px;
}
.topology .topology-content .topology-box .mask {
  position: absolute;
  top: 0;
  left: -20px;
  z-index: 3;
  padding: 10px 16px;
  background-color: var(--body_b_color, #f4f6f9);
  border: 1px solid var(--border_color, #f4f6f9);
  border-radius: 10px;
  width: 230px;
}
.topology .topology-content .topology-box .mask .mask-box {
  font-size: 14px;
  color: #3559be;
  height: 14px;
  line-height: 14px;
  text-align: left;
}
.topology .topology-content .topology-box .mask .mask-box label {
  float: left;
}
.topology .topology-content .topology-box .mask .mask-box .mask-text {
  margin-left: 70px;
}
.topology .topology-content .topology-box .mask .mask-box .loss-mask-test {
  margin-left: 84px;
}
.topology .topology-content .topology-box .mask .mask-box + .mask-box {
  margin-top: 10px;
}
.topology .topology-content .error-state .icon-source {
  background-image: url("../assets/topology/icon-c-red.png") !important;
}
.topology .topology-content .error-state .icon-route {
  background-image: url("../assets/topology/icon-ip-red.png") !important;
}
.topology .topology-content .error-state .icon-server {
  background-image: url("../assets/topology/icon-target-red.png") !important;
}
.topology .topology-content .error-state .icon-cloud {
  background-image: url("../assets/topology/icon-yun-red.png") !important;
}
.topology .topology-content .error-state .two-icon {
  background-image: url("../assets/topology/icon-switch-red.png") !important;
}

.topology .topology-content .error-state .virtual-node-icon {
  background-image: url("../assets/topology/icon-virtual-red.png") !important;
}


.topology .topology-content .error-line-state .text {
  color: #fd341f !important;
}
.topology .topology-content .error-line-state .line {
  border-color: #fd341f !important;
}
.topology .topology-content .inferior-state .icon-source {
  background-image: url("../assets/topology/icon-c-yellow.png") !important;
}
.topology .topology-content .inferior-state .icon-route {
  background-image: url("../assets/topology/icon-ip-yellow.png") !important;
}
.topology .topology-content .inferior-state .icon-server {
  background-image: url("../assets/topology/icon-target-yellow.png") !important;
}
.topology .topology-content .inferior-state .icon-cloud {
  background-image: url("../assets/topology/icon-yun-yellow.png") !important;
}
.topology .topology-content .inferior-state .two-icon {
  background-image: url("../assets/topology/icon-switch-yellow.png") !important;
}

.topology .topology-content .inferior-state .virtual-node-icon {
  background-image: url("../assets/topology/icon-virtual-yellow.png") !important;
}


.topology .topology-content .inferior-line-state .text {
  color: #ffa212 !important;
}
.topology .topology-content .inferior-line-state .line {
  border-color: #ffa212 !important;
}
.topology .topology-content .repair-state .icon-source {
  background-image: url("../assets/topology/icon-c-newGrey.png") !important;
}
.topology .topology-content .repair-state .icon-route {
  background-image: url("../assets/topology/icon-ip-newGrey.png") !important;
}
.topology .topology-content .repair-state .icon-server {
  background-image: url("../assets/topology/icon-target-newGrey.png") !important;
}
.topology .topology-content .repair-state .icon-cloud {
  background-image: url("../assets/topology/icon-yun-grey.png") !important;
}
.topology .topology-content .repair-state .two-icon {
  background-image: url("../assets/topology/icon-switch-grey.png") !important;
}

.topology .topology-content .repair-state .virtual-node-icon {
  background-image: url("../assets/topology/icon-virtual-grey.png") !important;
}


.topology .topology-content .repair-line-state .line {
  border-color: #5695C8 !important;
}
.topology .topology-content .repair-line-state .text {
  color: #5695C8 !important;
}
.topology .topology-content .repair-line-state .line {
  border-color: #5695C8 !important;
}
.topology .topology-content :first-child {
  margin-left: 0px;
}
:root {
  --font_color: #303748;
  --border_color: #dddddd;
  --dash_b_color: rgba(1, 22, 56, 0.9);
  --th_font_color: #ffffff;
  --td_font_color: #ffffff;
  --thd_border_color: #e8eaec;
  --table_sort_color: #c5c8ce;
  --table_sorton_color: #2d8cf0;
  /*--body_b_color: #060D15;*/
  --font_color: #fff;
  --border_color: #06324D;
  --icon_tree: #04478E;
  --th_b_color: #032A4D;
  --th_b_stripe_color: #011B2D;
  --td_b_color: #060D15;
  --th_font_color: #FFFFFF;
  --td_font_color: #FFFFFF;
  --thd_border_color: #2e3c51;
  --table_sort_color: #465b7a;
  --table_sorton_color: #8fd4ff;
  --input_font_color: #fff;
  --input_b_color: #061824;
  --input_checkbox_b_color: #1A222E;
  --selectdrop_b_color: #06324D;
  --selectdrop_font_color: #00FFEE;
  --confirmModal_font_color: #5CA0D5;
  --dash_h_b_color: #060D15;
  --dash_h_f_color: #00FFEE;
  --dash_b_b_color: #1a222e;
  --dash_border_color: #202A39;
  --input_placeholder_color: #5CA0D5;
  --org_btn_bg_color: #465b7a;
  --table_checkbox_border_color: #04478E;
  --primary_bcg: transparent;
  --primary_font: #2d8cf0;
  --warning_bcg: transparent;
  --warning_font: #ffad33;
  --success_bcg: transparent;
  --success_font: #47cb89;
  --error_bcg: transparent;
  --error_font: #f16643;
  --message_success_color: #19be6b;
  --message_err_color: #ed3f13;
  --message_warning_color: #f90;
  --message_font_success: #19be6b;
  --message_font_err: #ed3f13;
  --message_font_warning: #f90;
  --ivu_tag_border: #06324D;
  --ivu_select_arrow: #05EEFF;
  --modal_header_b_color: #465b7a;
  --modal_footer_but_b_color: #465b7a;
  --modal_footer_butok_b_color: #096dd9;
  --modal_b_color: #08101A;
  --modal_input_border_color: #35455d;
  --contentBox_bgcolor: #060D15;
  --header_b_color: #253142;
  --header_font_color: #ffffff;
  --header_active_bg_color: rgba(97, 124, 165, 0.16);
  --header_list_bg_color: #253142;
  --header_list_font_color: #5CA0D5;
  --header_list_activefont_color: #ffffff;
  --header_list_activebg_color: rgba(97, 124, 165, 0.16);
  --message_bg: rgba(44, 37, 48, 0.6);
  --chartBak: #617ca5;
  --link_b_color: #1a222e;
  --page_b_color: #06324D;
  --modal_header_color: var(--modal_header_color ,#D1E4FF);
  --alarm_modal_border_color: #35455d;
  --time_disabled_b_color: #06324D;
  --time_hover_b_color: #465b7a;
  --confirmmodal_footer_cancel_b_color: #061824;
  --reset_but_b_color: #465b7a;
  --topo_model_head_color: #465b7a;
  --topo_checkbox_b_color: #1A222E;
  --oid_l_m_b_color: #1F2A38;
  --menu_active_color: #253142;
  --left_menu_color: #1A222E;
  --report_bg_color: #0D151F;
  --wifi_boder_color: #465B7A;
  --wifi_tip_color: #808695;
  --wifi_device_color: #fff;
  --wifi_tip_content_color: #DFF1FF;
  --monitorItem_color: #013164;
  --monitorItem_bg_color: #1A222E;
  --input_font_color: #303748;
  --input_b_color: #ffffff;
  --selectdrop_font_color: #303748;
  --selectdrop_b_color: #ffffff;
  --confirmModal_font_color: #17233d;
  --input_placeholder_color: #c5c8ce;
  --org_btn_bg_color: #57c5f7;
  --table_checkbox_border_color: #e8eaec;
  --dash_h_b_color: #f4f6f9;
  --dash_h_f_color: #303748;
  --dash_b_b_color: #ffffff;
  --dash_border_color: #dddddd;
  /*按钮样式重新*/
  --primary_bcg: #2d8cf0;
  --primary_border: #2d8cf0;
  --primary_font: #fff;
  --warning_bcg: #ffad33;
  --warning_border: #ffad33;
  --warning_font: #fff;
  --success_bcg: #47cb89;
  --success_border: #47cb89;
  --success_font: #fff;
  --error_bcg: #f16643;
  --error_border: #f16643;
  --error_font: #fff;
  --ivu_tag_border: #e8eaec;
  --ivu-select-arrow: #808695;
  --modal_header_b_color: radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9));
  --modal_b_color: #ffffff;
  --modal_input_border_color: #dcdee2;
  --contentBox_bgcolor: 'none';
  --header_b_color: '#1252c8';
  --header_font_color: '#ffffff';
  --header_active_bg_color: 'rgba(125, 165, 232, 0.16)';
  --header_list_bg_color: '#ffffff';
  --header_list_font_color: '#303748';
  --header_list_activefont_color: '#ffffff';
  --header_list_activebg_color: '#7da5e8';
  --message_bg: 'rgba(255,255,255)';
  --chartBak: '#303748';
  --link_b_color: '#f4f6f9';
  --modal_footer_butok_b_color: '#2d8cf0';
  --confirmmodal_footer_cancel_b_color: 'transparent';
  --monitorItem_color2:var(--monitorItem_color2);
  --monitorItem_border_color:var(--monitorItem_border_color);
  --monitorItem_color: var(--monitorItem_color);
  --monitorItem_bg_color: var(--monitorItem_bg_color);
}
