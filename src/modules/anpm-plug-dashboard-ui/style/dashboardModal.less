.addComponent-modal {
    .table-box {
        width: 100%;
        // height: 200px;
        // background-color: pink;
        padding: 0 14px;
    }
    .diaplay-objectives {
        position: relative;
        .btn-box {
            position: absolute;
            top: 0;
            right: 14px;
            .ivu-btn {
                width: 52px ;
                height: 32px;
                background-color: var(--modal_b_color, #ffffff);
                border: 1px solid #00FFEE;
                font-size: 24px;
                margin-right: 10px;
               
            }
            .ivu-icon {
                color: #00FFEE;
            }
            .del {
              background-color: var(--modal_b_color, #ffffff);
                border-color: #FE5C5C;
                .ivu-icon {
                    color: #FE5C5C;
                }
            }
            
            
        }
    }
    .required-text {
        color: #fe5c5c;

    }
    .obj-required {
        .ivu-form-item-label:before {
            content: '*';
            display: inline-block;
            margin-right: 4px;
            line-height: 1;
            font-family: SimSun;
            font-size: 14px;
            color: #ed4014;
            
        }
    }
    
}


.map-detail-model {
    .index-title {
        color: var(--modal_title_name_color_two,#fff) !important;

    }
    .header-box {
        display: flex;
        align-items: center;
        .header-title {
            color:var(--more_btn_ivu_dropdown_item_hover_font_color,#00FFEE);
            font-size: 18px;
            font-weight: 600;
            
        }
        .header-address {
            margin-left: 10px;
            // font-size: 14px;
            color:var(--modal_title_name_color_two,#fff) ;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
  
    
}

.map-detail-model-bigscreen {
  .index-title {
      color: #fff !important;

  }
  .ivu-modal-body {
    background-color: #08101A !important;
    padding: 10px 20px 20px 20px !important;
  }
  .index-query-r {
    display: flex;
    height: 38px;
    margin-bottom: 16px;
    align-items: center;
    // margin-left: 10px;
    .query-btn {
        margin-left: 10px;
    }

}
.title-lookBox {
    .title-name {
        margin-right: 20px;
    }
}

  .header-box {
      display: flex;
      align-items: center;
      .header-title {
          color: #00FFEE;
          font-size: 18px;
          font-weight: 600;
          
      }
      .header-address {
          margin-left: 10px;
          // font-size: 14px;
          color:#fff ;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
      }
  }
  .query-btn {
    background: linear-gradient(357deg, #049DEC 0%, #05EBEB 100%) !important;;
  }
  .ivu-modal-content  {
    background-color: #08101A !important;
  }
  .ivu-modal .ivu-modal-header {
    background-color: #08101A !important;
  }
  .ivu-page-next a, .ivu-page-prev a {
      color: #fff !important;
      
  }
  .ivu-select-selection  {
      background-color:#061824 !important ;
  }
  .ivu-page-options-elevator input  {
      background-color: #061824 !important;
      color: #fff !important;
  }
  .ivu-select-selected-value {
      color: #fff !important;
  }
  .ivu-page .ivu-page-item.ivu-page-item-active a  {
      color: #fff !important;
  }
  .tab-page .ivu-page-item {
    
      background-color: #061824 !important;
  }
  .tab-page .ivu-page-item a {
      color: #fff !important;
  }
  .ivu-select-dropdown {
      background-color: #061824 !important;
  }
  .ivu-select-item {
      color: #fff !important;
  }
  .ivu-select-item:hover {
      background-color: #032A4D !important;
  }
  .ivu-select-item-selected {
      color: #2d8cf0 !important;
  }
  .ivu-input,
  .ivu-input-icon{
    height: 32px !important;
    line-height: 32px !important;
  }
  .ivu-input{
    //color:#303748 !important;
    border-radius: 4px !important;
    padding-left: 20px !important;
    background-color: #061824 !important;
    
    color: #fff !important;
    background-image: linear-gradient(to right, #061824, #061824), linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
  }
    .ivu-date-picker-cells-cell-range:before {
      background: #06324d !important;
    }
    .ivu-time-picker-cells-cell-selected,
    .ivu-time-picker-cells-cell-selected:hover {
      color: #2d8cf0;
      background: #06324d !important;
    }
    .ivu-date-picker-cells-cell-selected em,
    .ivu-date-picker-cells-cell-selected:hover em {
      border-radius: 16px !important;
    }
    .ivu-time-picker-cells-cell:hover {
      background: #06324d !important;
    }
    .ivu-picker-confirm .ivu-btn {
      color: #c5c8ce !important;
      background-color: #06324d !important;
      border-color: #06324d !important;
    }
    .ivu-picker-confirm .ivu-btn-text {
      color: #c5c8ce !important;
    }
    .ivu-picker-confirm .ivu-btn-text:hover,
    .ivu-picker-confirm .ivu-btn-default:hover,
    .ivu-picker-confirm .ivu-btn-primary:hover {
      color: #57a3f3 !important;
      background-color: #06324d !important;
    }
    .ivu-time-picker-with-range .ivu-picker-panel-content:after {
      content: '';
      // background: #154b81 !important;
      background:var(--picker_panel_content_after_color,#154b81) !important;

    }
    .ivu-time-picker-header {
      border-bottom: 1px solid #06324d !important;
    }
    .ivu-time-picker-cells-list {
      border-left: 1px solid #06324d !important;
    }
    .ivu-select-dropdown {
      border: 1px solid #04478e;
      background-color: #061824 !important;
    }
    .ivu-picker-panel-sidebar {
      background-color: transparent !important;
      border-color: #35455d !important;
  }
  .index-line-box{
      padding: 0 20px;
      .index-query{
        font-size: 0;
        height: 32px;
        margin-bottom: 18px;
        .index-query-box{
          display: inline-block;
          vertical-align: top;
          height: 32px;
          label{
            display: block;
            width: 44px;
            height: 32px;
            line-height: 32px;
            float: left;
            font-size: 14px;
            color: var(--font_color,#303748)
          }
          .time-content{
            margin-left: 54px;
            font-size: 14px;
            .ivu-input,
            .ivu-input-icon{
              height: 32px !important;
              line-height: 32px !important;
            }
            .ivu-input{
              //color:#303748 !important;
              border-radius: 4px !important;
              padding-left: 20px !important;
            }
          }
          .ivu-select-selection{
            height: 32px !important;
            border-radius: 4px !important;
          }
          .ivu-select-placeholder,
          .ivu-select-selected-value{
            text-align: left !important;
            height: 32px !important;
            line-height: 32px !important;
            padding-left: 20px !important;
            color: var(--font_color,#303748)!important;
            font-size: 14px !important;
          }
          button{
            //width: 100px !important;
            //height: 32px !important;
            // border-radius: 0 !important;
            i{
              font-size: 16px !important;
              font-weight: bold !important;
            }
            span{
              font-size: 14px !important;
            }
          }
         
          .export-button:hover{
            background-color: #4DED95;
          }
        }
        .index-query-box+.index-query-box{
          margin-left: 20px;
        }
        .btn-groups{
          float: right;
          .btn-icon{
            color: #05EBEB  !important;
            cursor: pointer;
          }
        }
      }
      .index-button{
        font-size: 0;
        margin-bottom: 20px;
        .button-box{
          display: inline-block;
          vertical-align: top;
          padding: 15px 26px;
          border: 1px solid #e6e8ee;
          background-color: #fff;
          font-size: 14px;
          font-weight: bold;
          color: #484b56;
          cursor: pointer;
        }
        .button-box+.button-box{
          margin-left: -1px;
        }
        .button-color{
          background-color: #4e7bff !important;
          color: #ffffff !important;
        }
      }
      .index-number{
        font-size: 14px;
        height: 14px;
        color: #fb204d;
        font-weight: bold;
        margin-bottom: 18px;
        label{
          float: left;
          display: block;
          width: 100px;
          height: 14px;
          line-height: 14px;
        }
        a{
          margin-left: 110px;
          display: block;
          height: 14px;
          line-height: 14px;
          color: #fb204d;
        }
      }
      .index-line{
        width: 100%;
        height: 304px;
        position: relative;
        .index-line-no-data{
          position: absolute;
          top: 50%;
          left: 50%;
          width: 164px;
          height: 156px;
          text-align: center;
          margin-top: -78px;
          margin-left: -82px;
        }
      }
    }
    .ivu-page-item-jump-next  {
      background-color:#061824 !important ;
      color: #fff;
    }
    .ivu-page-item-jump-prev {
      background-color:#061824 !important ;
      color: #fff;
      
    }
    .page-total {
      color: #fff !important;  
    }
    .ivu-radio-inner:after {
      background-color: #0290FD !important;
    }
    .ivu-modal-content .ivu-radio-inner {
      border-color: #04478e !important;
    }
  
  
}


