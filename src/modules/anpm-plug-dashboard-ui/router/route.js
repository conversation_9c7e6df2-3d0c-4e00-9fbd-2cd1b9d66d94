const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/dashboard',
    // component:{
    //   default:'/dashboard',
    //   dashboardStatistic:'/dashboardEdit'
    // }
  },
  {
    path: "/dashboard",
    name: lan.getLabel("src.dashBoard"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashboard-ui/views/index.vue"], resolve)
  },
  {
    path: "/dashboardList",
    name: lan.get<PERSON>abel("src.ViewList"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashboard-ui/views/viewList.vue"], resolve)
  },
  {
    path: "/dashboardEdit",
    name: lan.getLabel("src.EditView"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashboard-ui/views/viewEdit.vue"], resolve)
  },
  {
    path: "/dashboardStatistic/:url",
    name: lan.getLabel("src.summaryGraph"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashboard-ui/views/statistic_analysis.vue"], resolve)
  },
  {
    path: "/dashboardAlarmTrend/:url",
    name: lan.getLabel("src.Trend"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashboard-ui/views/alarm_trend.vue"], resolve)
  },
  {
    path: "/dashboardNodeTop",
    name: lan.getLabel("src.PoorQualityTop"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashboard-ui/views/node_topn.vue"], resolve)
  },
];
