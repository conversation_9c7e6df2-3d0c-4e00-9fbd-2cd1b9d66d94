<!DOCTYPE html>
<html lang="en" id="hthtml">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <!-- <link rel="icon" href="<%= BASE_URL %>icon_logo.ico"> -->
  <link rel="icon" type="image/x-icon" href="">
  <title></title>
</head>
<body>

<div id="app"></div>
<!-- built files will be auto injected -->
<script>
  //二级iframe页面改变颜色函数
    function skinChange(obj){
        const property = Object.keys(obj);
        const color = Object.keys(obj).map(function(i){return obj[i]});
        let root = document.documentElement;
        for (let i=0;i<property.length;i++){
            root.style.setProperty(property[i], color[i]);
        }
        const iframeElement = document.getElementsByClassName("iframeElement");
        for (let index = 0;index < iframeElement.length;index++){
            iframeElement[index].contentWindow.skinChange(obj);
        }
    }
  function fullScreen(el) {
    var rfs = el.requestFullScreen || el.webkitRequestFullScreen || el.mozRequestFullScreen || el.msRequestFullScreen,
      wscript;

    if(typeof rfs != "undefined" && rfs) {
      rfs.call(el);
      return;
    }

    if(typeof window.ActiveXObject != "undefined") {
      wscript = new ActiveXObject("WScript.Shell");
      if(wscript) {
        wscript.SendKeys("{F11}");
      }
    }
  }
  function exitFullScreen(el) {
    var el= document,
      cfs = el.cancelFullScreen || el.webkitCancelFullScreen || el.mozCancelFullScreen || el.exitFullScreen,
      wscript;

    if (typeof cfs != "undefined" && cfs) {
      cfs.call(el);
      return;
    }

    if (typeof window.ActiveXObject != "undefined") {
      wscript = new ActiveXObject("WScript.Shell");
      if (wscript != null) {
        wscript.SendKeys("{F11}");
      }
    }
  }
  window.onload= function () {
    // if (window.name.search('bigscreen')<0) {
    //   const windowHeight = top.document.body.clientHeight;
    //   document.getElementById('app').style.setProperty ('height', windowHeight - 150+'px');
    //   top.window.onresize = function () {
    //     const windowHeight = top.document.body.clientHeight;
    //     document.getElementById('app').style.setProperty ('height', windowHeight - 100+'px')
    //   }
    // }

    if (window.name.search('bigscreen')>-1 ) {
      document.getElementById('hthtml').style.height = '100%';
      document.body.style.height = '100%';
      document.getElementById('app').style.height = '100%';
      var btn = document.getElementById('asdas');
      var content = document.getElementById('app');
      // console.log(window)
      // btn.onclick = function(){
      //   fullScreen(content);
      // }
    }


  };
</script>
<style>
  #app {
    /* overflow-y: auto; */
  }

  ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;
  }
  /*滚动条里面小方块*/
  /* ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(6, 50, 77, 1);
    background: #015197;
  } */
  /*滚动条里面轨道*/
  /* ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: #06324D;
  } */
  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    /* box-shadow: inset 0 0 5px rgba(6, 50, 77, 1); */
    box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color,rgba(6, 50, 77, 1));
    /* background: #015197; */
    background: var(--scrollbar_thumb_bg_color,#015197);

  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
    box-shadow: inset 0 0 5px var(--scrollbar_track_shadow_color,rgba(0, 0, 0, 0.2));
    border-radius: 10px;
    /* background: #06324D; */
    background: var(--scrollbar_track_bg_color,#06324D);
  }
  /* 当同时有垂直滚动条和水平滚动条时交汇的部分 */
::-webkit-scrollbar-corner {
    /* background: #0e1012; */
    background: transparent;
}
</style>
</body>
</html>