<template>
  <section
      class="sectionBox"
  >
    <!--  查询面板  -->
    <div class="section-top">
      <!--  查询条件    -->
      <div class="fn_box">

        <div class="fn_item">
          <label class="fn_item_label">{{$t('discover_discovery_status')}}</label>
          <div class="fn_item_box">
            <Select
                v-model="status"
                :placeholder="$t('comm_please_select')"
                filterable
                clearable
            >
              <Option
                  v-for="(item, index) in statusList"
                  :value="item.value"
                  :key="index"
              >{{ item.label }}
              </Option
              >
            </Select>
          </div>
        </div>
        <div class="fn_item">
          <label class="fn_item_label">{{$t('comm_keywords')}}：</label>
          <div class="fn_item_box">
            <Input
                v-model.trim="keyWord"
                :placeholder="$t('discover_rule_1')"
                style="width: 240px"
            />
          </div>
        </div>
      </div>
      <!--面板功能按键区域-->
      <div class="fn_tool">
        <Button type="primary" icon="ios-search" v-if="permissionObj.list" @click="queryClick(1)"
        >查询
        </Button
        >
        <Button class="skinPrimary" v-if="permissionObj.add" type="info" icon="md-add" @click="openAddForm"
        >新建
        </Button
        >
        <Button class="skinError" v-if="permissionObj.delete" type="error" icon="ios-close" @click="rowRemove('plural',selectedIds)"
        >删除
        </Button
        >
        <Button class="skinSuccess" v-if="permissionObj.status" type="success" icon="ios-stats" @click="start"
        >启用
        </Button
        >
        <Button class="skinWarning" v-if="permissionObj.status" type="warning" icon="ios-play" @click="stop"
        >禁用
        </Button
        >
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body">
      <div class="section-body-content">
        <div>
          <!--    列表      -->
          <Table
              ref="tablelist"
              border
              :columns="tableColumn"
              :data="pageData.list"
              :loading="page.pageLoading"
              :no-data-text='
              page.pageLoading ? "" : pageData.total > 0 ? ""  : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                            
            '
              size="small"
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
          >
            <!--    列表操作列功能        -->
            <template
                slot-scope="{ row }"
                slot="action"
                class="tableTools"
            >
              <span
                  v-if="permissionObj.update"
                  style="margin-right: 6px; color: #57a3f3; cursor: pointer"
                  @click="openEditForm(row)"
              >{{$t('comm_edit')}}</span
              >

              <span
                  v-if="permissionObj.delete"
                  style="margin-right: 6px; color: #f16643; cursor: pointer"
                  @click="rowRemove('singular',row)"
              >{{$t('but_remove')}}</span
              >
            </template>
          </Table>
          <!--   分页      -->
          <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
            <Page
                v-page
                :current.sync="page.pageNo"
                :page-size="page.pageSize"
                :total="pageData.total"
                :page-size-opts="page.pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建规则弹框  -->
    <Modal :title="$t('common_new')" sticky
           v-model="addOpen"
           width="822"
           draggable
           :mask="true"
           :loading="addLoading"
           @on-ok="addSubmit"
           @on-cancel="cancleForm('addRuleForm')">
      <Form ref="addRuleForm"
            :model="addRuleForm"
            :rules="addRuleFormRule"
            class="width_50_Form"
            @submit.native.prevent
            :label-width="120">
        <FormItem :label="$t('discover_rule_name')" prop="name">
          <Input v-model="addRuleForm.name" maxlength="50"></Input>
        </FormItem>
        <FormItem :label="$t('discover_gether_name')" prop="getherCode">
          <Select
              v-model="addRuleForm.getherCode"
              filterable
              clearable
              :placeholder="$t('gether_select_collector')"
          >
            <Option
                v-for="item in gatherList"
                :value="item.code"
                :key="item.code"
            >{{ item.name }}
            </Option>
          </Select>
        </FormItem>
        <FormItem :label="$t('discover_ip_rang')" class="width_100_item ipBox" prop="ipArr">
          <div class="ipitemBox startIp">
            <label style="padding: 0 20px 0 0">{{$t('comm_from')}}</label>
            <Input class="ipItem" v-model="addRuleForm.ipOne" @input="pushIp(0,$event,0)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="addRuleForm.ipTwo" @input="pushIp(1,$event,0)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="addRuleForm.ipThree" @input="pushIp(2,$event,0)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="addRuleForm.startIpFour" @input="pushIp(3,$event,0)"></Input>
          </div>
          <div class="ipitemBox endIp">
            <label style="padding: 0 20px 0 20px">{{$t('comm_to')}}</label>
            <Input class="ipItem" v-model="addRuleForm.ipOne" @input="pushIp(0,$event,0)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="addRuleForm.ipTwo" @input="pushIp(1,$event,0)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="addRuleForm.ipThree" @input="pushIp(2,$event,0)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="addRuleForm.endIpFour" @input="pushIp(4,$event,0)"></Input>
          </div>
        </FormItem>
        <FormItem :label="$t('discover_dis_inter')" prop="intervalTime">
          <Input v-model="addRuleForm.intervalTime" style="width: 230px;margin-right: 5px"></Input>小时
        </FormItem>
        <FormItem :label="$t('phytopo_desc')+$t('comm_colon')" class="width_100_item">
          <Input type="textarea" maxlength="200" v-model="addRuleForm.describe"></Input>
        </FormItem>
        <Divider style="margin: 10px 0 30px 0;"/>
        <div>
          <div style="display: inline-block;width: 120px;float: left;"><p
              style="text-align: right;line-height: 1;padding: 10px 12px 10px 0;">{{$t('discover_create_task')}}:</p></div>
          <div style="margin-left: 120px">
            <FormItem :label="$t('discover_select_type')" class="width_100_item">
              <CheckboxGroup v-model="addRuleForm.deviceType">
                <Checkbox v-for="item in deviceList" :label="item.value" :key="item.value">{{item.lable}}</Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="$t('discover_add_group')">
              <Select
                  v-model="addRuleForm.groupId"
                  filterable
                  clearable
                  :placeholder="$t('comm_select_group')"
              >
                <Option
                    v-for="item in groupList"
                    :value="item.value"
                    :key="item.value"
                >{{ item.label }}
                </Option>
              </Select>
            </FormItem>
            <FormItem :label="$t('discover_maintain_level')" prop="maintainLevel">
              <Select
                  v-model="addRuleForm.maintainLevel"
                  filterable
                  clearable
                  :placeholder="$t('discover_select_maintain_level')"
              >
                <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                >{{ item.lable }}
                </Option>
              </Select>
            </FormItem>
            <p class="mgB10" style="width: 120px;text-align: right;line-height: 1;padding: 10px 12px 10px 0;">{{$t('comm_operation_scheduling')}}：</p>
            <FormItem
                :label="$t('comm_start_time')"
                prop="startDate"
                class="childContent width_100_item"
            >
              <DatePicker
                  type="date"
                  v-model="addRuleForm.startDate"
                  :placeholder="$t('comm_select_date2')" 
                  class="myDateClass"
                  format="yyyy-MM-dd"
                  @on-change="playStartDateChange"
              ></DatePicker>
              <span style="padding: 0 10px">—</span>
              <DatePicker
                  type="date"
                  v-model="addRuleForm.endDate"
                  :placeholder="$t('comm_select_date2')" 
                  class="myDateClass"
                  format="yyyy-MM-dd"
                  :options="playEndDate"
              ></DatePicker>
            </FormItem>
            <FormItem
                :label="$t('comm_time_period')"
                class="childContent width_100_item"
                prop="timeList"
            >
              <div v-for="(item,index) in addRuleForm.timeList" :key="index">
                <TimePicker
                    type="time"
                    v-model="item.start"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playStartTimeChange($event,index)"
                ></TimePicker>
                <span style="padding: 0 10px">—</span>
                <TimePicker
                    type="time"
                    v-model="item.end"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playEndTimeChange($event,index)"
                ></TimePicker>
                <Icon
                    :type="addRuleForm.timeList.length > 1 && index===addRuleForm.timeList.length-1 || addRuleForm.timeList.length===1 ? 'ios-add-circle-outline' : 'ios-remove-circle-outline'"
                    class="iconItem ruleIcon"
                    :class="addRuleForm.timeList.length > 1 && index===addRuleForm.timeList.length-1 || addRuleForm.timeList.length===1 ? 'addItem':'removeItem'"
                    @click="taskTimeChange(index)"/>

              </div>
            </FormItem>
            <FormItem
                :label="$t('speed_recurrence')"
                class="childContent width_100_item"
                prop="repeatWeek"
            >
              <CheckboxGroup v-model="addRuleForm.repeatWeek">
                <Checkbox :label="1">{{$t('comm_monday')}}</Checkbox>
                <Checkbox :label="2">{{$t('comm_tuesday')}}</Checkbox>
                <Checkbox :label="3">{{$t('comm_wednesday')}}</Checkbox>
                <Checkbox :label="4">{{$t('comm_thursday')}}</Checkbox>
                <Checkbox :label="5">{{$t('comm_friday')}}</Checkbox>
                <Checkbox :label="6">{{$t('comm_saturday')}}</Checkbox>
                <Checkbox :label="7">{{$t('comm_sunday')}}</Checkbox>
              </CheckboxGroup>
            </FormItem>
          </div>
        </div>
        <p class="note">{{$t('discover_time_note')}}</p>
      </Form>
    </Modal>
    <!-- 修改弹框  -->
    <Modal :title="$t('common_update')" sticky
           v-model="editOpen"
           width="822"
           draggable
           :mask="true"
           :loading="editLoading"
           @on-ok="editSubmit"
           @on-cancel="cancleForm('editForm')">
      <Form ref="editForm"
            :model="editForm"
            :rules="editFormRule"
            class="width_50_Form"
            @submit.native.prevent
            :label-width="120">
        <FormItem :label="$t('discover_rule_name')" prop="name">
          <Input v-model="editForm.name"></Input>
        </FormItem>
        <FormItem :label="$t('discover_gether_name')" prop="getherCode">
          <Select
              v-model="editForm.getherCode"
              filterable
              clearable
              :placeholder="$t('gether_select_collector')"
          >
            <Option
                v-for="item in gatherList"
                :value="item.code"
                :key="item.code"
            >{{ item.name }}
            </Option>
          </Select>
        </FormItem>
        <FormItem :label="$t('discover_ip_rang')" class="width_100_item ipBox" prop="ipArr">
          <div class="ipitemBox startIp">
            <label style="padding: 0 20px 0 0">{{$t('comm_from')}}</label>
            <Input class="ipItem" v-model="editForm.ipOne" @input="pushIp(0,$event,1)"></Input><span class="ipitemspan">·</span>
            <Input class="ipItem" v-model="editForm.ipTwo" @input="pushIp(1,$event,1)"></Input><span class="ipitemspan">·</span>
            <Input class="ipItem" v-model="editForm.ipThree" @input="pushIp(2,$event,1)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="editForm.startIpFour" @input="pushIp(3,$event,1)"></Input>
          </div>
          <div class="ipitemBox endIp">
            <label style="padding: 0 20px 0 20px">{{$t('comm_to')}}</label>
            <Input class="ipItem" v-model="editForm.ipOne" @input="pushIp(0,$event,1)"></Input><span class="ipitemspan">·</span>
            <Input class="ipItem" v-model="editForm.ipTwo" @input="pushIp(1,$event,1)"></Input><span class="ipitemspan">·</span>
            <Input class="ipItem" v-model="editForm.ipThree" @input="pushIp(2,$event,1)"></Input><span
              class="ipitemspan">·</span>
            <Input class="ipItem" v-model="editForm.endIpFour" @input="pushIp(4,$event,1)"></Input>
          </div>
        </FormItem>
<!--        <FormItem :label="$t('discover_dis_inter')" prop="intervalTime">-->
<!--          <Input v-model="editForm.intervalTime" style="width: 230px;margin-right: 5px"></Input>小时-->
<!--        </FormItem>-->
        <FormItem :label="$t('phytopo_desc')+$t('comm_colon')" class="width_100_item">
          <Input type="textarea" v-model="editForm.describe"></Input>
        </FormItem>
        <Divider style="margin: 10px 0 30px 0;"/>
        <div>
          <div style="display: inline-block;width: 120px;float: left;"><p
              style="text-align: right;line-height: 1;padding: 10px 12px 10px 0;">{{$t('discover_create_task')}}:</p></div>
          <div style="margin-left: 120px">
            <FormItem :label="$t('discover_select_type')" class="width_100_item">
              <CheckboxGroup v-model="editForm.deviceType">
                <Checkbox v-for="item in deviceList" :label="item.value" :key="item.value">{{item.lable}}</Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem :label="$t('discover_add_group')">
              <Select
                  v-model="editForm.groupId"
                  filterable
                  clearable
                  :placeholder="$t('comm_select_group')"
              >
                <Option
                    v-for="item in groupList"
                    :value="item.value"
                    :key="item.value"
                >{{ item.label }}
                </Option>
              </Select>
            </FormItem>
            <FormItem :label="$t('discover_maintain_level')" prop="maintainLevel">
              <Select
                  v-model="editForm.maintainLevel"
                  filterable
                  clearable
                  :placeholder="$t('discover_select_maintain_level')"
              >
                <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                >{{ item.lable }}
                </Option>
              </Select>
            </FormItem>
            <p class="mgB10" style="width: 120px;text-align: right;line-height: 1;padding: 10px 12px 10px 0;">{{$t('comm_operation_scheduling')}}：</p>
            <FormItem
                :label="$t('comm_start_time')"
                prop="startDate"
                class="childContent width_100_item"
            >
              <DatePicker
                  type="date"
                  v-model="editForm.startDate"
                  :placeholder="$t('comm_select_date2')" 
                  class="myDateClass"
                  format="yyyy-MM-dd"
                  @on-change="playStartDateChange"
              ></DatePicker>
              <span style="padding: 0 10px">—</span>
              <DatePicker
                  type="date"
                  v-model="editForm.endDate"
                  :placeholder="$t('comm_select_date2')" 
                  class="myDateClass"
                  format="yyyy-MM-dd"
                  :options="playEndDate"
              ></DatePicker>
            </FormItem>
            <FormItem
                :label="$t('comm_time_period')"
                class="childContent width_100_item"
                prop="timeList"
            >
              <div v-for="(item,index) in editForm.timeList" :key="index">
                <TimePicker
                    type="time"
                    v-model="item.start"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playStartTimeChange($event,index)"
                ></TimePicker>
                <span style="padding: 0 10px">—</span>
                <TimePicker
                    type="time"
                    v-model="item.end"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playEndTimeChange($event,index)"
                ></TimePicker>
                <Icon
                    :type="editForm.timeList.length > 1 && index===editForm.timeList.length-1 || editForm.timeList.length===1 ? 'ios-add-circle-outline' : 'ios-remove-circle-outline'"
                    class="iconItem ruleIcon"
                    :class="editForm.timeList.length > 1 && index===editForm.timeList.length-1 || editForm.timeList.length===1 ? 'addItem':'removeItem'"
                    @click="taskTimeChange2(index)"/>

              </div>
            </FormItem>
            <FormItem
                :label="$t('speed_recurrence')"
                class="childContent width_100_item"
                prop="repeatWeek"
            >
              <CheckboxGroup v-model="editForm.repeatWeek">
                <Checkbox :label="1">{{$t('comm_monday')}}</Checkbox>
                <Checkbox :label="2">{{$t('comm_tuesday')}}</Checkbox>
                <Checkbox :label="3">{{$t('comm_wednesday')}}</Checkbox>
                <Checkbox :label="4">{{$t('comm_thursday')}}</Checkbox>
                <Checkbox :label="5">{{$t('comm_friday')}}</Checkbox>
                <Checkbox :label="6">{{$t('comm_saturday')}}</Checkbox>
                <Checkbox :label="7">{{$t('comm_sunday')}}</Checkbox>
              </CheckboxGroup>
            </FormItem>
          </div>
        </div>
        <p class="note">{{$t('discover_time_note')}}</p>
      </Form>
    </Modal>
  </section>
</template>
<script>
import validate from "@/common/validate";

/**
 * 时间转为秒
 * @param time 时间(00:00:00)
 * @returns {string} 时间戳（单位：秒）
 */
var time_to_sec = function (time) {
  var s = "";

  var hour = time.split(":")[0];
  var min = time.split(":")[1];
  var sec = time.split(":")[2] || 0;

  s = Number(hour * 3600) + Number(min * 60) + Number(sec);

  return s;
};
/**
 * 时间秒数格式化
 * @param s 时间戳（单位：秒）
 * @returns {*} 格式化后的时分秒
 */
var sec_to_time = function (s) {
  var t;
  if (s > -1) {
    var hour = Math.floor(s / 3600);
    var min = Math.floor(s / 60) % 60;
    // var sec = s % 60;
    if (hour < 10) {
      t = "0" + hour + ":";
    } else {
      t = hour + ":";
    }

    if (min < 10) {
      t += "0";
    }
    t += min;
    // if(sec < 10){t += "0";}
    // t += sec.toFixed(2);
  }
  return t;
};
import moment from "moment";
import "@/config/page.js";
import global from "../../../common/global.js";
import {mapGetters, mapActions, mapState} from "vuex";
import {addDraggable} from "@/common/drag.js";
import locationreload from "@/common/locationReload";

export default {
  name: "discoverRule",
  components: {},
  props: {
    tabData: {
      type: String,
      default: "",
    },
  },
  watch: {
    tabData: {
      handler(value) {
        if (this.pageData.list.length === 0 && value === "discoverRule") {
          this.getGroupList();
          this.getDeviceList();
          this.getGetherList();
          this.getmaintainLevelList();
          this.queryClick(1);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.$nextTick(() => {
      locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    })
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    moment.locale("zh-cn");
  },
  data() {
    const valiIp = (rule, value, callback) => {
      let notEmpty = true;
      for (let i = 0; i < 5; i++) {
        const item = value[i];
        if (item == undefined || item === '' || item == null) {
          notEmpty = false;
          break
        }
      }
      let startIp = '', endIp = '';
      if (notEmpty === false) {
        callback(new Error(this.$t('comm_ip_correct')))
      } else {
        const ipStartWith3 = value[0] + '.' + value[1] + '.' + value[2];
        startIp = ipStartWith3 + '.' + value[3];
        endIp = ipStartWith3 + '.' + value[4];
        const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if ((!reg.test(startIp)) || !reg.test(endIp)) {
          callback(new Error(this.$t('comm_ip_correct')));
        } else {
          if (value[4] - value[3] <= 0) {
            callback(new Error(this.$t('device_discovery_error_ip')));
          }
          callback();
        }
      }
    };
    return {
      /*權限*/
      permissionObj:{},
      /* 参数列表 */
      //规则
      status: null,
      //规则列表
      statusList: [
        {value: 0, label: this.$t('comm_enable')},
        {value: 1, label: this.$t('comm_disable')},
      ],
      //关键字
      keyWord: "",
      //分页参数
      page: {
        pageNo: 1,
        pageSize: 10,
        pageSizeOpts: [10, 50, 100, 200, 500, 1000],
        pageLoading: false,
      },
      /** 列表数据 */
      pageData: {
        total: 0,
        list: [],
      },
      /** 列表选中数据 */
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      selectedDatas: [],
      /** 新建表单 */
      addRuleForm: {
        name: '',
        getherCode: null,
        ipArr: [null, null, null, null, null],
        ipOne: '',
        ipTwo: '',
        ipThree: '',
        startIpFour: '',
        endIpFour: '',
        intervalTime: '1',
        describe: '',
        deviceType: [],
        groupId: null,
        maintainLevel: null,
        startDate: null,
        endDate: null,
        timeList: [{start: "0000", end: "2359"}],
        repeatWeek: [1, 2, 3, 4, 5, 6, 7],
      },
      //新建加载动画
      addLoading:true,
      //分组数据列表
      groupList: [],
      //运维等级数据列表
      maintainList: [],
      //采集器数据列表
      gatherList: [],
      //设备列表
      deviceList:[],
      //新建弹框
      addOpen: false,
      //新建表单验证规则
      addRuleFormRule: {
        name: [
          {required: true, type: "string", message: this.$t('discover_pd_rule_name'), trigger: "blur",},
        ],
        getherCode: [
          {required: true, type: "string", message: this.$t('gether_select_collector'), trigger: "change",},
        ],
        ipArr: [
          // {required: true, type: "array", message: "请填写IP段", trigger: "blur",},
          {trigger: 'blur', length: 32, validator: valiIp}
        ],
        intervalTime: [
          {required: true, type: "string", message: this.$t('discover_select_inter_time'), trigger: "blur",},
          {validator: validate.checkMax, min: 1, max: 100, trigger: "blur", isInteger: true,}
        ],
        deviceType: [{required: true, type: "array", message: this.$t('comm_select_cycle'), trigger: "change",},],
        groupId: [
          {required: true, type: "number", message: this.$t('comm_select_group'), trigger: "change",},
        ],
        maintainLevel: [
          {required: true, type: "string", message: this.$t('discover_select_maintain_level'), trigger: "change",},
        ],
        startDate: [
          {required: true, type: "date", message: this.$t('comm_start_time'), trigger: "change",},
        ],
        endDate: [
          {required: true, type: "date", message: this.$t('comm_end_time'), trigger: "change",},
        ],
        timeList: [{required: true, type: "array", message: this.$t('discover_date_rang'), trigger: "change",},],
        repeatWeek: [
          {required: true, type: "array", message: this.$t('comm_select_cycle'), trigger: "change",},
        ],
      },
      /** 修改表单 */
      editForm: {
        id: null,
        name: '',
        getherCode: null,
        ipArr: [null, null, null, null, null],
        ipOne: '',
        ipTwo: '',
        ipThree: '',
        startIpFour: '',
        endIpFour: '',
        intervalTime: '',
        describe: '',
        deviceType: [],
        groupId: null,
        maintainLevel: null,
        startDate: null,
        endDate: null,
        timeList: [{start: "0000", end: "2359"}],
        repeatWeek: [1, 2, 3, 4, 5, 6, 7],
      },
      //修改加载动画
      editLoading:true,
      //修改验证规则
      editFormRule: {
        name: [
          {required: true, type: "string", message: this.$t('discover_pd_rule_name'), trigger: "blur",},
        ],
        getherCode: [
          {required: true, type: "string", message: this.$t('gether_select_collector'), trigger: "change",},
        ],
        ipArr: [
          // {required: true, type: "array", message: "请填写IP段", trigger: "blur",},
          {trigger: 'blur', length: 32, validator: valiIp}
        ],
        intervalTime: [
          {required: true, type: "string", message: this.$t('discover_select_inter_time'), trigger: "blur",},
          {validator: validate.checkMax, min: 1, max: 100, trigger: "blur", isInteger: true,}
        ],
        deviceType: [{required: true, type: "array", message: this.$t('comm_select_cycle'), trigger: "change",},],
        groupId: [
          {required: true, type: "number", message: this.$t('comm_select_group'), trigger: "change",},
        ],
        maintainLevel: [
          {required: true, type: "string", message: this.$t('discover_select_maintain_level'), trigger: "change",},
        ],
        startDate: [
          {required: true, type: "date", message: this.$t('comm_start_time'), trigger: "change",},
        ],
        endDate: [
          {required: true, type: "date", message: this.$t('comm_end_time'), trigger: "change",},
        ],
        timeList: [{required: true, type: "array", message: this.$t('discover_date_rang'), trigger: "change",},],
        repeatWeek: [
          {required: true, type: "array", message: this.$t('comm_select_cycle'), trigger: "change",},
        ],
      },
      //修改弹框
      editOpen: false,
      /** 表格标题列 */
      tableColumn: [
        {type: "selection", width: 60, className: "bgColor", align: "center"},
        {title: this.$t('discover_rule_name'), align: "center", minWidth: 110, key: "name"},
        {
          title: this.$t('access_ip_rang'), align: "center", minWidth: 110, key: "startIp",
          render: (h, param) => {
            return h('span', param.row.startIp + ' — ' + param.row.endIp)
          }
        },
        {title: "所属企业", align: "center", minWidth: 80, key: "orgName"},
        {title: this.$t('discover_gether_name'), align: "center", minWidth: 60, key: "getherName"},
        {
          title: this.$t('comm_status'), align: "center", width: 80, key: "status",
          render: (h, param) => {
            return h('span', param.row.status == 1 ? this.$t('comm_disable') : this.$t('comm_enable'))
          }
        },
        {title: this.$t('discover_time'), align: "center", minWidth: 120, key: "lastUpdateTime"},
        {title: this.$t('comm_operate'), align: "center", width: 140, slot: "action"},
      ],

      playEndDate: {
        disabledDate(date) {
          // return date && date.valueOf() < Date.now() - 86400000;
        },
      },
    };
  },
  mounted() {
  },
  methods: {
    //查询事件
    queryClick(pageNo) {
      //初始化页码
      this.page.pageNo = pageNo;
      //设置查询参数
      const queryParam = {
        status: this.status,
        keyword: this.keyWord,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
      };
      //打开加载动画
      this.pageLoading = true;
      //清空选择的数据
      this.selectedIds = new Set();
      this.selectedDatas = [];
      //请求数据
      this.getTableList(queryParam);
    },
    //获取列表数据
    getTableList(queryParam) {
      this.$http
          .wisdomPost("/netDiscoverRule/list", queryParam)
          .then(({code, data, msg}) => {
            if (code === 1) {
              if (data) {
                this.pageData.list = data.records;
                this.pageData.total = data.total || 0;
              } else {
                this.setTableListEmpty();
              }
            } else {
              this.setTableListEmpty();
              this.$Message.warning({content:msg,background:true});
            }
          })
          .catch((error) => {
            this.setTableListEmpty();
          })
          .finally(() => {
            //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
            let _that = this;
            setTimeout(() => {
              let objData = this.$refs.tablelist.$refs.tbody.objData;
              for (let key in objData) {
                if (this.selectedIds.has(objData[key].id)) {
                  objData[key]._isChecked = true;
                }
              }
            }, 0);
            this.pageLoading = false;
          });
    },
    //设置列表数据为空列表
    setTableListEmpty() {
      this.pageData.list = [];
      this.pageData.total = 0;
    },
    /** 新建 */
    //打开新建表单
    openAddForm() {
      this.$refs["addRuleForm"].resetFields();
      (this.addRuleForm = {
        name: '',
        getherCode: null,
        ipArr: [null, null, null, null, null],
        ipOne: '',
        ipTwo: '',
        ipThree: '',
        startIpFour: '',
        endIpFour: '',
        intervalTime: '',
        describe: '',
        deviceType: [],
        groupId: null,
        maintainLevel: null,
        startDate: null,
        endDate: null,
        timeList: [{start: "0000", end: "2359"}],
        repeatWeek: [1, 2, 3, 4, 5, 6, 7],
      }),
          (this.addOpen = true);
    },
    //新建请求接口
    addSubmit() {
      this.$refs["addRuleForm"].validate((validate) => {
        if (validate) {
          //时间段验证
          const ruleTimeFlag = this.ruleTimes(this.addRuleForm.timeList);
          if(ruleTimeFlag === false){
            this.$Message.warning({content:this.$t('comm_tip3'),background:true});
            //取消按钮加载效果
            this.addLoading = false;
            this.$nextTick(() => {
              this.addLoading = true;
            });
            return
          }else if (ruleTimeFlag){
            const ipArr = this.addRuleForm.ipArr;
            const addParam = {
              name: this.addRuleForm.name,
              getherCode: this.addRuleForm.getherCode,
              startIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[3],
              endIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[4],
              intervalTime: this.addRuleForm.intervalTime,
              describe: this.addRuleForm.describe,
              deviceType: this.addRuleForm.deviceType.join(','),
              groupId: this.addRuleForm.groupId,
              maintainLevel: this.addRuleForm.maintainLevel,
              startDate: new Date(this.addRuleForm.startDate).format('yyyyMMdd'),
              endDate: new Date(this.addRuleForm.endDate).format('yyyyMMdd'),
              repeatWeek: this.addRuleForm.repeatWeek.join(","),
              timeSlot: this.addRuleForm.timeList
                  .map((item) => {
                    return item.start.replace(':','') + "-" + item.end.replace(':','');
                  })
                  .join(","),
            };
            this.$http
                .wisdomPost("/netDiscoverRule/add", addParam)
                .then(({code, data, msg}) => {
                  if (code === 1) {
                    this.$Message.success({content:this.$t('device_discovery_create_success'),background:true});
                    this.addOpen = false;
                  } else {
                    this.$Message.warning({content:msg,background:true});
                  }
                })
                .catch((err) => {
                  throw new Error(err);
                })
                .finally(() => {
                  //取消按钮加载效果
                  this.addLoading = false;
                  this.$nextTick(() => {
                    this.addLoading = true;
                  });
                  this.queryClick(1);
                });
          }
        }else{
          //取消按钮加载效果
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
        }
      })
    },
    /** 编辑 */
    //打开编辑表单
    openEditForm(param) {
      this.$refs["editForm"].resetFields();

      this.$http
          .wisdomPost("/netDiscoverRule/getDetail", {id:param.id}).then(({code,data,msg})=>{
            if (code == 1){
              const endIpLast = data.endIp ? (data.endIp.split('.')[3]>=0?data.endIp.split('.')[3]:null): null;
              const ipArr = data.startIp ? data.startIp.split('.').concat(endIpLast) :[null,null,null,null,endIpLast];
              const timeList = data.timeSlot ? (data.timeSlot.split(',').map(item=>{const itemArr = item.split('-');return {start:itemArr[0],end:itemArr[1]}})):[{start:'0000',end:'2359'}];
              this.editForm.id = data.id;
              this.editForm.name = data.name;
              this.editForm.getherCode = data.getherCode;
              this.editForm.ipArr = ipArr;
              this.editForm.ipOne = ipArr[0];
              this.editForm.ipTwo = ipArr[1];
              this.editForm.ipThree = ipArr[2];
              this.editForm.startIpFour = ipArr[3];
              this.editForm.endIpFour = ipArr[4];
              this.editForm.intervalTime = data.intervalTime;
              this.editForm.describe = data.describe;
              this.editForm.deviceType = data.deviceType ? data.deviceType.split(',').map(Number) :[];
              this.editForm.groupId = data.groupId;
              this.editForm.maintainLevel = data.maintainLevel;
              this.editForm.startDate = data.startDate ? String(data.startDate).replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3") :null;
              this.editForm.endDate = data.endDate ? String(data.endDate).replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3") :null;
              this.editForm.timeList = timeList;
              this.editForm.repeatWeek = data.repeatWeek? data.repeatWeek.split(',').map(Number) :[];
              this.editOpen = true;
            }else{
              this.$Message.warning({content:this.$t('common_get_data_error'),background:true})
            }
      }).catch((error)=>{
        throw new Error(error)
      })
    },
    //修改请求接口
    editSubmit() {
      this.$refs["editForm"].validate(validate=>{
        if (validate){
          //时间段验证
          const ruleTimeFlag = this.ruleTimes(this.editForm.timeList);
          if(ruleTimeFlag === false){
            this.$Message.warning(this.$t('comm_tip3'));
            //取消按钮加载效果
            this.editLoading = false;
            this.$nextTick(() => {
              this.editLoading = true;
            });
            return
          }else if (ruleTimeFlag){
            const ipArr = this.editForm.ipArr;
            console.log(this.editForm.endDate)
            const editParam = {
              id: this.editForm.id,
              name: this.editForm.name,
              getherCode: this.editForm.getherCode,
              startIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[3],
              endIp: ipArr[0] + '.' + ipArr[1] + '.' + ipArr[2] + '.' + ipArr[4],
              intervalTime: this.editForm.intervalTime||'1',
              describe: this.editForm.describe,
              deviceType: this.editForm.deviceType.join(','),
              groupId: this.editForm.groupId,
              maintainLevel: this.editForm.maintainLevel,
              startDate: new Date(this.editForm.startDate).format('yyyyMMdd'),
              endDate: new Date(this.editForm.endDate).format('yyyyMMdd'),
              repeatWeek: this.editForm.repeatWeek.join(","),
              timeSlot: this.editForm.timeList
                  .map((item) => {
                    return item.start.replace(':','') + "-" + item.end.replace(':','');
                  })
                  .join(","),
            };
            this.$http
                .wisdomPost("/netDiscoverRule/update", editParam)
                .then(({code, data, msg}) => {
                  if (code === 1) {
                    this.$Message.success({content:this.$t('comm_changed_successful'),background:true});
                    this.editOpen = false;
                  } else {
                    this.$Message.warning({content:msg,background:true});
                  }
                })
                .catch((err) => {
                  throw new Error(err);
                })
                .finally(() => {
                  //取消按钮加载效果
                  this.editLoading = false;
                  this.$nextTick(() => {
                    this.editLoading = true;
                  });
                  this.queryClick(1);
                });
          }
        }else {
          //取消按钮加载效果
          this.editLoading = false;
          this.$nextTick(() => {
            this.editLoading = true;
          });
        }
      })
    },
    /** 删除 */
    rowRemove(type, data) {
      let checkedIds = '';
      if (type === "plural") {
        //多选
        const idArr = Array.from(data);
        if (idArr.length < 1) {
          this.$Message.warning({content:this.$t('warning_first_operation_data'),background:true});
          return;
        } else {
          checkedIds = idArr.join(",");
        }
      } else if (type === "singular") {
        //单选
        checkedIds = data.id;
      }
      top.window.$iviewModal.confirm({
        title: this.$t('common_delete_prompt'),
        content: this.$t('discover_msg_delete'),
        onOk: () => {
          this.$http
              .wisdomPost("/netDiscoverRule/delete", {ids: checkedIds})
              .then(({code, data, msg}) => {
                if (code === 1) {
                  this.$Message.success({content:this.$t('common_controls_succ'),background:true})
                  this.queryClick(1);
                } else {
                  this.$Message.warning({content:msg,background:true});
                }
              })
              .catch((err) => {
                throw new Error(err);
              });
        },
      });
    },
    /** 启用*/
    start() {
      const idArr = Array.from(this.selectedIds);
      if (idArr.length < 1) {
        this.$Message.warning(this.$t('specquality_select'));
        return;
      } else {
        let ids = idArr.join(",");
        top.window.$iviewModal.confirm({
          title: this.$t('common_enable_prompt'),
          content: this.$t('discover_msg_enable'),
          onOk: () => {
            this.$http.wisdomPost("/netDiscoverRule/enable", {ids: ids, status: 0}).then(({code, data, msg}) => {
              if (code === 1) {
                this.$Message.success({content:this.$t('common_controls_succ'),background:true});
                this.queryClick(1);
              } else {
                this.$Message.warning({content:this.$t('common_controls_fial'),background:true});
              }
            }).catch(error => {
              this.$Message.warning({content:this.$t('common_controls_fial'),background:true});
              throw new Error(error)
            })
          },
        });
      }
    },
    /** 禁用*/
    stop() {
      const idArr = Array.from(this.selectedIds);
      if (idArr.length < 1) {
        this.$Message.warning({content:this.$t('warning_first_operation_data'),background:true});
        return;
      } else {
        let ids = idArr.join(",");
        top.window.$iviewModal.confirm({
          title: this.$t('common_disable_prompt'),
          content: this.$t('discover_msg_disable'),
          onOk: () => {
            this.$http.wisdomPost("/netDiscoverRule/disable", {ids: ids, status: 1}).then(({code, data, msg}) => {
              if (code === 1) {
                this.$Message.success({content:this.$t('common_controls_succ'),background:true});
                this.queryClick(1);
              } else {
                this.$Message.warning({content:this.$t('common_controls_fial'),background:true});
              }
            }).catch(error => {
              this.$Message.warning({content:this.$t('common_controls_fial'),background:true});
              throw new Error(error)
            })
          },
        });
      }
    },
    /** 取消事件 */
    cancleForm(formObj) {
      this.$refs[formObj].resetFields();
    },

    /** 获取基础数据 */
    //获取分组数据
    getGroupList() {
      this.groupList = [];
      this.$http
          .wisdomPost("/group/list", {pageNo: 1, pageSize: 10000})
          .then(({code, data, msg}) => {
            if (code === 1) {
              this.groupList = data ? data.records : [];
            } else {
              this.groupList = [];
              // this.$Message.warning(msg);
            }
          })
          .catch((err) => {
            this.groupList = [];
            throw new Error(err);
          });
    },
    //获取采集器数据
    getGetherList() {
      this.$http.wisdomPost("/sys/gether/list", {org: null,type:1,pageNo:1,pageSize:10000}).then(({code, data, msg}) => {
        if (code === 1) {
          this.gatherList = data.records;
        } else {
          this.gatherList = [];
        }
      }).catch((error) => {
        this.gatherList = [];
        throw new Error(error)
      })
    },
    //获取设备数据
    getDeviceList() {
      this.deviceList = [];
      this.$http
          .wisdomPost("/dataTable/queryCode",{key:'netDeviceType'})
          .then(({ code, data, msg }) => {
            if (code === 1) {
              this.deviceList = data ?? [];
            } else {
              this.deviceList = [];
              this.$Message.warning({content:msg,background:true});
            }
          })
          .catch((err) => {
            this.deviceList = [];
            throw new Error(err);
          });
    },
    //获取运维等级数据
    getmaintainLevelList() {
      this.maintainList = [];
      this.$http
          .wisdomPost("/dataTable/queryCode",{key:'maintainLevel'})
          .then(({ code, data, msg }) => {
            if (code === 1) {
              this.maintainList = data ?? [];
            } else {
              this.maintainList = [];
              this.$Message.warning({content:msg,background:true});
            }
          })
          .catch((err) => {
            this.maintainList = [];
            throw new Error(err);
          });
    },
    //数据列表页码切换
    pageChange(pageNo) {
      this.queryClick(pageNo);
    },
    //数据列表页码大小改变
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.queryClick(this.page.pageNo);
    },
    //选中table的项目
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.tableList.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
            this.selectedDatas.splice(
                this.selectedDatas.findIndex((current) => item.id === current.id),
                1
            );
            // this.selectedDatas.remove(JSON.stringify(item));
          }
        });
        console.log(this.selectedDatas);
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
          this.selectedDatas.push(item);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
      this.selectedDatas.push(row);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
      this.selectedDatas.splice(
          this.selectedDatas.findIndex((item) => item.id === row.id),
          1
      );
    },

    /** 新建时时间选择控制 */
    playStartTimeChange(time, index) {
      const endTime = this.addRuleForm.timeList[index].end;
      if (time != undefined && time != null && time != "") {
        const endTimes = time_to_sec(endTime);
        const startTimes = time_to_sec(time);
        if (endTime == undefined || endTimes < startTimes) {
          this.addRuleForm.timeList[index].end = sec_to_time(startTimes);
        }
      }
    },
    playEndTimeChange(time, index) {
      const startTime = this.addRuleForm.timeList[index].start;
      if (time != undefined && time != null && time != "") {
        const endTimes = time_to_sec(time);
        const startTimes = time_to_sec(startTime);
        if (startTime == undefined || endTimes < startTimes) {
          this.addRuleForm.timeList[index].start = sec_to_time(endTimes);
        }
      }
    },
    playStartDateChange(val) {
      this.playEndDate.disabledDate = (date) => {
        // return date && date.valueOf() < new Date(val).valueOf() - 86400000;
      };
    },
    //时间段改变事件
    //时间段操作（增加，删除）
    taskTimeChange(index) {
      const length = this.addRuleForm.timeList.length;
      if (index === length - 1) {
        //增加
        if (length >= 10) {
          this.$Message.warning({content:this.$t('discover_msg_add_10'),background:true});
          return;
        } else {
          this.addRuleForm.timeList.push({start: "0000", end: "2359"});
        }
      } else {
        //删除
        this.addRuleForm.timeList.splice(index, 1);
      }
    },
    taskTimeChange2(index) {
      const length = this.editForm.timeList.length;
      if (index === length - 1) {
        //增加
        if (length >= 10) {
          this.$Message.warning({content:this.$t('discover_msg_add_10'),background:true});
          return;
        } else {
          this.editForm.timeList.push({start: "0000", end: "2359"});
        }
      } else {
        //删除
        this.editForm.timeList.splice(index, 1);
      }
    },
    //时间段校验是否存在交叉
    ruleTimes(times){
      const timeArray = times;
      //结果返回值
      let timesFlag = true;
      //将每一项换算成分钟数
      let timeMinArray = timeArray.map(item=>{
        let start =0,end =0;
        start = Number(item.start.split(':')[0])*60+Number(item.start.split(':')[1]);
        end = Number(item.end.split(':')[0])*60+Number(item.end.split(':')[1]);
        return [start,end]
      })
      for(let i = 0;i<timeMinArray.length-1;i++){
        for(let j = i+1;j<timeMinArray.length;j++){
          if((timeMinArray[j][1]<=timeMinArray[i][0] || timeMinArray[j][0]>=timeMinArray[i][1])){
            timesFlag = true ;
          }else{
            timesFlag = false ;
            break;
          }
        }
        if(timesFlag === false){
          break;
        }
      }
      return timesFlag
    },
    //IP操作
    pushIp(index, value, type) {
      if (type === 0) {
        this.addRuleForm.ipArr[index] = value;
      } else if (type === 1) {
        this.editForm.ipArr[index] = value;
      }
    },
  },
  destroyed() {
  },
};
</script>

<style lang='less'>
</style>
<style scoped lang="less">
</style>


