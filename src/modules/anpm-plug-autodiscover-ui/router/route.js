const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/autodiscover',
  },
  {
    path: "/autodiscover",
    name: lan.getLabel("src.autodiscover"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-autodiscover-ui/views/index.vue"], resolve)
  },
];
