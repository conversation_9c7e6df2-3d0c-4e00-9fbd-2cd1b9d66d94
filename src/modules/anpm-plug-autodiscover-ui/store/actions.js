const lan = require('../../../common/language')
"use strict";
import { $http } from "@/server/http";
export const getDevModel = ({ commit, state }, param) => {
  $http
    .post("snmpoid/getDevModel", param)
    .then(res => {
      let data = res.data;
      var resultArr = [];
      resultArr = data.filter(item => {
        return item !== "" && item != undefined;
      });
      commit("setDevModel", resultArr);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取客户列表
export const getClientList = ({ commit, state }, param) => {
  if (!param) {
    param = {
      pageNo:1,
      pageSize:10000,
      province:'',
      city:'',
      area:'',
      keyword:'',
    }
  }
  $http
    .post("org/list", param)
    .then(res => {
      let data = res.data;
      if (res.code === 1) {
        commit("setClientList", data.records);
      }else{
        throw new Error(lan.getLabel("src.EGE"))
      }
    })
    .catch(err => {
      //console.log(err)
    });
};
