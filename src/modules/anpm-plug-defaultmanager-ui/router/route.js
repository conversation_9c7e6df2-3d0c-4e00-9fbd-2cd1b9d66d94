const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: lan.getLabel("src.DVM"),
    meta: {
      authority: true
    },
    redirect:'/defaultmanager',
  },
  {
    path: "/defaultmanager",
    name: lan.getLabel("src.DVM"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-defaultmanager-ui/views/index.vue"], resolve)
  },
];
