const lan = require('../../../common/language')
const StringEMPTY = '';

/**
 * 正整数验证
 * @param {Object} rule 验证规则
 * @param {*} value 验证数据值
 * @param {Function(Error)} callback 验证执行回调
 * @return {*}
 */
export function positiveInteger(rule, value, callback) {
    if (value) {
        value = String(value || StringEMPTY);
        if (/^\d+$/.test(value)) {
            value = Number.parseInt(value, 10);
            if (value <= 0) {
                return callback(new Error(lan.getLabel("src.TIVMBGTOETAI(")));
            }
        } else {
            return callback(new Error(lan.getLabel("src.TIVMBAN")));
        }
    }
    return callback();
};

/**
 * 整数限制范围
 * Bing绑定（min ~ max）
 * @param {Object} rule 验证规则
 * @param {*} value 验证数据值
 * @param {Function(Error)} callback 验证执行回调
 * @return {*}
 */
export function integerLimitRange(rule, value, callback) {
    if (typeof this === 'object' && typeof this.min === 'number' && typeof this.max === 'number') {
        value = String(value || StringEMPTY);
        if (/^\d+$/.test(value)) {
            value = Number.parseInt(value, 10);
            const min = Math.min(this.min, this.max);
            const max = Math.max(this.min, this.max);
            if (value < min || value > max) {
                return callback(new Error(`输入值范围：${min} ~ ${max}.`));
            }
            return callback();
        } else {
            return callback(new Error(lan.getLabel("src.TIVMBAN")));
        }
    }
    return callback(new Error(lan.getLabel("src.TIVRPINC")));
};

/**
 * 数值限制范围
 * Bing绑定（min ~ max, unit）
 * @param {Object} rule 验证规则
 * @param {*} value 验证数据值
 * @param {Function(Error)} callback 验证执行回调
 * @return {*}
 */
export function bigdecimalLimitRange(rule, value, callback) {
    if (typeof this === 'object' && typeof this.min === 'number' && typeof this.max === 'number') {
        value = String(value || StringEMPTY);
        if (/^-?\d+(\.\d+)?$/.test(value)) {
            value = Number.parseFloat(value);
            const min = Math.min(this.min, this.max);
            const max = Math.max(this.min, this.max);
            if (value < min || value > max) {
                const unit = this.unit || StringEMPTY;
                return callback(new Error(`输入值范围：${min}${unit} ~ ${max}${unit}.`));
            }
            return callback();
        } else {
            return callback(new Error(lan.getLabel("src.TIVMBAN")));
        }
    }
    return callback(new Error(lan.getLabel("src.NVRPANC")));
};

/**
 * 时间周期
 * Bing绑定（unit: min ~ max）
 * @param {Object} rule 验证规则
 * @param {*} value 验证数据值
 * @param {Function(Error)} callback 验证执行回调
 * @param {String} unit 周期单位
 * @return {*}
 */
export function cycleTime(rule, value, callback, unit) {
    if (typeof this === 'object') {
        value = String(value || StringEMPTY);
        if (/^-?\d+$/.test(value)) {
            value = Number.parseInt(value, 10);
            if (value <= 0) {
                return callback(new Error(lan.getLabel("src.TIVMBGTAI(")));
            }
            if (Object.prototype.hasOwnProperty.call(this, unit)) {
                const limit = this[ unit ];
                if (typeof limit === 'object' && typeof limit.min === 'number' && typeof limit.max === 'number') {
                    const min = Math.min(limit.min, limit.max);
                    const max = Math.max(limit.min, limit.max);
                    if (value < min || value > max) {
                        return callback(new Error(`时间周期 ${unit} 限制：${min} ~ ${max}.`));
                    }
                    return callback();
                }
                return callback(new Error(`时间周期 ${unit} 配置无效.`));
            }
            return callback(new Error(`时间周期单位 ${unit} 未配置.`));
        } else {
            return callback(new Error(lan.getLabel("src.TIVMBAIN")));
        }
    }
    return callback(new Error(lan.getLabel("src.TTPVRPANC")));
};
