@charset "UTF-8";

.ivu-table th {
    background-color: var(--body_conent_b_color,#061824) !important;
    font-weight: bolder !important;
}
th {
    background-color: var(--body_conent_b_color,#061824) !important;
    color: var(--th_font_color, #303748) !important;
}
td {
    color: var(--td_font_color, #303748) !important;
    background-color: var(--body_conent_b_color,#061824) !important;
}
.chunk-row {
  .title {
    font-family: 'Arial Normal', 'Arial';
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    color: #333333;
    line-height: normal;
    text-align: right;
  }
  .content-special {
    text-align: left;
    // position: relative;
    margin-bottom: 80px;
    // .content-elem {
    //   display: flex;
    //   justify-content: center;
    //   justify-items: center;

    // }
    .ivu-form-item {
      margin-bottom:unset;
    }
    .special-des {
      color:var(--default_value_remarks_color , #5CA0D5);
      // position: absolute;
      // top: 30px;
      // left: 0;
    }

  }
  .content {
    text-align: center;
    .content-elem {
      position: relative;
      display: flex;
      justify-content: center;
      justify-items: center;
    }
    /deep/ .ivu-form-item {
      margin-bottom: 0;
    }
  }
  .remarks {
    font-family: 'Arial Normal', 'Arial';
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    color:var(--default_value_remarks_color , #5CA0D5);
    line-height: normal;
    text-align: left;
  }
}
.chunk {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-bottom: 64px;
  box-sizing: border-box;
  .chunk-header {
    position: relative;
    text-align: left;
    color: var(--font_color, #303748) !important;
    padding-bottom: 10px;
    box-sizing: border-box;
    border-style: solid;
    border-color: var(--modal_input_border_color, #dcdee2) !important;
    border-width: 0 0 1px;
    margin-left: 118px;
    margin-top: -20px;
  }
  .chunk-name {
    width: 116px;
    font-family: 'Arial Negreta', 'Arial';
    padding-left: 0;
    font-weight: 700;
    font-style: normal;
    font-size: 13px;
    text-align: center;
  }
  .chunk-body {
    position: relative;
    width: 100%;
    height: auto;
    display: block;
    padding: 24px 0 0;
    box-sizing: border-box;
    .chunk-form {
      // position: relative;
      // display: inline-block;
      // left: 50%;
      // transform: translateX(-50%);
    }
    .chunk-table {
      position: relative;
      width: 100%;
      height: auto;
      border-collapse: collapse;
      text-align: center;
      /*table-layout: fixed;*/
      td {
        padding: 0 0 28px;
        &.content {
        
          padding-right: 24px;
        }
      }
      tr:last-child td {
        padding-bottom: 0;
      }
    }
  }
  .chunk-foot {
    position: absolute;
    width: 100%;
    height: 60px;
    bottom: 0;
    display: flex;
    margin-left: 300px;
    align-items: center;
 
  
    .ivu-btn + .ivu-btn {
      margin-left: 40px;
      background-color: var(--modal_footer_butok_b_color,#2d8cf0);
      border: none;
    }
    /deep/ .ivu-btn.ivu-btn-reset {
      color: #fff;
      background-color: var(--reset_but_b_color,#465b7a);
      border: none;
    }
  }
}
