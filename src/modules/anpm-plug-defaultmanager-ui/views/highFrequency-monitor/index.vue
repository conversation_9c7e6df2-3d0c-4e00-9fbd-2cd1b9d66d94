<template>
    <!-- 默认值管理页面 -->
    <div class="chunk">

        <div v-for="(detailArr,groupName) in myChildData" :key="groupName">
            <div class="chunk-name">{{ groupName }}</div>
            <div class="chunk-header"></div>
            <div class="chunk-body">
                <Form :ref="groupName" :model="detailArr[0]" class="chunk-form" :rules="ruleCustom[groupName]">
                    <table cellspacing="0" cellpadding="0" class="chunk-table">
                        <tr class="chunk-row" v-for="(detailItem,i) in detailArr" :key="i" v-show="detailItem.conceal != 0">
                            <!--                                    {{detailItem}}-->
                            <td v-if="!detailItem.radioType" class="title" style="width:300px">{{detailItem.lable }}：</td>
                            <td v-if="detailItem.radioType" class="title">
                                <Checkbox v-model="detailItem.checkBoxValue">{{detailItem.lable }}：</Checkbox>
                            </td>
                           
                            <td v-if="detailItem.id != 105" class="content" style="width:260px">
                                <FormItem class="content-elem" :prop="groupName + '_' + i">
                                    <Input v-if="detailItem.valueType =='text'" v-model="detailItem.value" class="heartbeat" :min="0" size="small"
                                           :disabled="(detailItem.radioType && !detailItem.checkBoxValue) || detailItem.conceal == 2">
                                    <Select v-if="detailItem.unitType==3 " v-model="detailItem.unit" slot="append" class="heartbeat-unit"
                                            :transfer="true">
                                        <Option v-for="(defItem,index) in detailItem.unitDef" :key="index" :value="defItem.key">{{ defItem.label }}
                                        </Option>
                                    </Select>
                                    <span v-if="detailItem.unitType==2" slot="append" class="heartbeat-unit">{{detailItem.unitDef}}</span>
                                    </Input>
                                    <Input v-if="detailItem.valueType =='usi-switcherName'" v-model="detailItem.value" class="heartbeat" :min="0" size="small"
                                           :disabled="(detailItem.radioType && !detailItem.checkBoxValue) || detailItem.conceal == 2">
                                    <Select v-if="detailItem.unitType==3 " v-model="detailItem.unit" slot="append" class="heartbeat-unit"
                                            :transfer="true">
                                        <Option v-for="(defItem,index) in detailItem.unitDef" :key="index" :value="defItem.key">{{ defItem.label }}
                                        </Option>
                                    </Select>
                                    <span v-if="detailItem.unitType==2" slot="append" class="heartbeat-unit">{{detailItem.unitDef}}</span>
                                    </Input>
                                    <Select v-if="detailItem.valueType =='select'" @on-change="selectChange(detailItem)" v-model="detailItem.value"
                                            class="heartbeat">
                                        <Option v-for="(defItem,index) in detailItem.valueDef" :key="index" :value="defItem.key">{{ defItem.label }}
                                        </Option>
                                    </Select>
                                    <Input v-model="detailItem.value" v-if="detailItem.valueType =='userName'" 
                                            :placeholder='$t("server_pe_user_name")' class="heartbeat" />
                                    <Input v-model="detailItem.value" v-if="detailItem.valueType =='pwd'" autocomplete="new-password" type="password"
                                           password :placeholder='$t("server_pe_user_pwd")' class="heartbeat" />
                                    <i-switch v-if="detailItem.valueType =='switch'" v-model="detailItem.value" true-value="ENABLE"
                                              false-value="DISABLE">
                                        <span slot="open">{{$t("comm_open")}}</span>
                                        <span slot="close">{{$t("comm_close")}}</span>
                                    </i-switch>
                                    <CheckboxGroup v-model="detailItem.value" v-if="detailItem.valueType =='checkBox'">

                                            <Checkbox v-for="deviceTypeItem in deviceTypeList" :key="deviceTypeItem.value" 
                                             :label="deviceTypeItem.value" ref="checkboxGroup">
                                             
                                             <span>{{ deviceTypeItem.lable }}</span></Checkbox>
                                     
                                    </CheckboxGroup>
                                    <DatePicker v-if="detailItem.valueType =='date'" :options="optionsDate" @on-change='dateChange(detailItem.value)'
                                                v-model="detailItem.value" type="date" size="small" class="heartbeat"></DatePicker>
                                    <TimePicker v-if="detailItem.valueType =='time'" @on-change='timeChange(detailItem.value)'
                                                :disabled-hours="disabledH" :disabled-minutes="disabledM" format="HH:mm:00" v-model="detailItem.value"
                                                type="time" size="small" class="heartbeat"></TimePicker>
                                </FormItem>
                            </td>
                            <td v-else class="content-special" colspan="2" style="width:260px">
                                <FormItem class="content-elem" :prop="groupName + '_' + i">
                                    <Input v-if="detailItem.valueType =='text'" v-model="detailItem.value" class="heartbeat" :min="0" size="small"
                                           :disabled="(detailItem.radioType && !detailItem.checkBoxValue) || detailItem.conceal == 2">
                                    <Select v-if="detailItem.unitType==3 " v-model="detailItem.unit" slot="append" class="heartbeat-unit"
                                            :transfer="true">
                                        <Option v-for="(defItem,index) in detailItem.unitDef" :key="index" :value="defItem.key">{{ defItem.label }}
                                        </Option>
                                    </Select>
                                    <span v-if="detailItem.unitType==2" slot="append" class="heartbeat-unit">{{detailItem.unitDef}}</span>
                                    </Input>
                                    <Input v-if="detailItem.valueType =='usi-switcherName'" v-model="detailItem.value" class="heartbeat" :min="0" size="small"
                                           :disabled="(detailItem.radioType && !detailItem.checkBoxValue) || detailItem.conceal == 2">
                                    <Select v-if="detailItem.unitType==3 " v-model="detailItem.unit" slot="append" class="heartbeat-unit"
                                            :transfer="true">
                                        <Option v-for="(defItem,index) in detailItem.unitDef" :key="index" :value="defItem.key">{{ defItem.label }}
                                        </Option>
                                    </Select>
                                    <span v-if="detailItem.unitType==2" slot="append" class="heartbeat-unit">{{detailItem.unitDef}}</span>
                                    </Input>
                                    <Select v-if="detailItem.valueType =='select'" @on-change="selectChange(detailItem)" v-model="detailItem.value"
                                            class="heartbeat">
                                        <Option v-for="(defItem,index) in detailItem.valueDef" :key="index" :value="defItem.key">{{ defItem.label }}
                                        </Option>
                                    </Select>
                                    <Input v-model="detailItem.value" v-if="detailItem.valueType =='userName'" 
                                            :placeholder='$t("server_pe_user_name")' class="heartbeat" />
                                    <Input v-model="detailItem.value" v-if="detailItem.valueType =='pwd'" autocomplete="new-password" type="password"
                                           password :placeholder='$t("server_pe_user_pwd")' class="heartbeat" />
                                    <i-switch v-if="detailItem.valueType =='switch'" v-model="detailItem.value" true-value="ENABLE"
                                              false-value="DISABLE">
                                        <span slot="open">{{$t("comm_open")}}</span>
                                        <span slot="close">{{$t("comm_close")}}</span>
                                    </i-switch>
                                    <CheckboxGroup v-model="detailItem.value" v-if="detailItem.valueType =='checkBox'">

                                            <Checkbox v-for="deviceTypeItem in deviceTypeList" :key="deviceTypeItem.value" 
                                             :label="deviceTypeItem.value" ref="checkboxGroup">
                                             
                                             <span>{{ deviceTypeItem.lable }}</span></Checkbox>
                                        <!-- 
                                                    1	交换机
                                                    2	路由器
                                                    3	windows服务器
                                                    4	linux服务器
                                                    5	摄像头
                                                    6	打印机
                                                    7	未知
                                                    8	路由/交换
                                                    9   PC终端


                                                        "default_manager_switchboard": "交换机",
                                                        "default_manager_router": "路由器",
                                                        "default_manager_Linux_server": "Linux服务器",
                                                        "default_manager_camera": "摄像头",
                                                        "default_manager_routing/switching": "路由/交换",
                                                        "default_manager_printer": "打印机",
                                                        "default_manager_PC": "PC终端",
                                                 -->
                                        <!-- <div class="checkBox"> -->
                                            <!-- <Checkbox label="1">{{$t("default_manager_switchboard")}}</Checkbox>
                                            <Checkbox label="2">{{$t("default_manager_router")}}</Checkbox>
                                            <Checkbox label="4">{{$t("default_manager_Linux_server")}}</Checkbox>
                                            <Checkbox label="5">{{$t("default_manager_camera")}}</Checkbox> -->
                                        <!-- </div> -->
                                        <!-- <p></p> -->
                                        <!-- <div class="checkBox"> -->
                                            <!-- <Checkbox label="8">{{$t("default_manager_routing/switching")}}</Checkbox>
                                            <Checkbox label="6">{{$t("default_manager_printer")}}</Checkbox>
                                            <Checkbox label="9">{{$t("default_manager_PC")}}</Checkbox>
                                            <Checkbox label="7">{{$t('comm_unknown')}}</Checkbox> -->
                                        <!-- </div> -->
                                    </CheckboxGroup>
                                    <DatePicker v-if="detailItem.valueType =='date'" :options="optionsDate" @on-change='dateChange(detailItem.value)'
                                                v-model="detailItem.value" type="date" size="small" class="heartbeat"></DatePicker>
                                    <TimePicker v-if="detailItem.valueType =='time'" @on-change='timeChange(detailItem.value)'
                                                :disabled-hours="disabledH" :disabled-minutes="disabledM" format="HH:mm:00" v-model="detailItem.value"
                                                type="time" size="small" class="heartbeat"></TimePicker>
                                </FormItem>
                                <div class="special-des">{{detailItem.description}}</div>
                            </td>
                            <td >
                                <div v-if="detailItem.id != 105" class="remarks" :class="{ 'disabled': (detailItem.radioType && !detailItem.checkBoxValue)} ">
                                    {{detailItem.description}}</div>
                            </td>
                        </tr>
                    </table>
                </Form>
            </div>
        </div>
        <div class="chunk-foot">
            <Button class="cz" v-on:click="resetForm">{{$t("but_reset")}}</Button>
            <Button class="jiaHao-btn" :loading="saveLoading" @click="submitForm">{{$t("comm_save")}}</Button>
        </div>
    </div>
</template>

<script>
import cryptoAES from '@/common/crypto.js';
import handlePassword from '@/common/handlePassword';

export default {
    props: ['childData'],
    name: 'highFrequency-monitor',

    data() {
        return {
            optionsDate: {
                disabledDate(date) {
                    return date && date.valueOf() <= Date.now() - 86400000;
                }
            },
            disabledH: [],
            disabledM: [],
            saveDate: '',
            backupsDate: '',
            building: false,
            deviceTypeList:[],
            myChildData: this.childData,
            saveLoading: false,
            ruleCustom: {},
            alarm_param: this.$t("rpmanager_alarm_para")
        };
    },
    mounted() {
            // 加载设备类型
            this.getDeviceTypeList();
    },
    methods: {

        getDeviceTypeList(){
            // 之前的接口：/dataTable/queryCode
            // 现在的接口: /dataTable/queryDeviceType
            this.deviceTypeList = [];
            this.$http
                .wisdomPost("/dataTable/queryDeviceType",{key:'netDeviceType'})
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                    this.deviceTypeList = data ?? [];
                    } else {
                    this.deviceTypeList = [];
                    }
                })
                .catch((err) => {
                    this.deviceTypeList = [];
                    throw new Error(err);
                });
        },
        // 选择执行时间控制之前的时间是否可以选择
        timeChange(val) {
            let h = val.split(':');
            let myDate = new Date();
            this.saveDate = val;
            this.disabledM = [];
            let today = new Date(new Date().format('yyyy-MM-dd')).getTime() / 1000;
            if (this.backupsDate > today) {
                this.disabledM = [];
            } else if (this.backupsDate <= today) {
                if (Number(h[0]) <= myDate.getHours()) {
                    for (let i = 0; i < myDate.getMinutes(); i++) {
                        this.disabledM.push(i);
                    }
                } else {
                    this.disabledM = [];
                }
            }
        },
        // 选择备份日期控制之前的日期是否可以选择
        dateChange(val) {
            this.disabledH = [];
            this.disabledM = [];
            let today = new Date(new Date().format('yyyy-MM-dd')).getTime() / 1000;
            let other = val.getTime() / 1000;
            this.backupsDate = other;
            if (other > today) {
                this.disabledH = [];
                this.disabledM = [];
            } else if (other <= today) {
                let h = this.saveDate.split(':');
                let myDate = new Date();
                if (Number(h[0]) < myDate.getHours()) {
                    for (let i = 0; i < 60; i++) {
                        this.disabledM.push(i);
                    }
                } else {
                    this.disabledM = [];
                }
                for (let i = 0; i < myDate.getHours(); i++) {
                    this.disabledH.push(i);
                }
            }
        },
        selectChange(res) {
            console.log(res);
            //自动备份-备份路径-在线与远程备份切换，显示隐藏其他信息
            let data = this.myChildData;
            if ('BACK_PATH' == res.key) {
                for (var key in data) {
                    for (var d in data[key]) {
                        if (data[key][d].key == 'BACK_SERVER_USERNAME' || data[key][d].key == 'BACK_SERVER_IP' || data[key][d].key == 'BACK_SERVER_PASSWORD') {
                            //远程备份
                            if (res.value == 1) {
                                data[key][d].conceal = 1;
                            } else {
                                data[key][d].conceal = 0;
                                // 切换到本地备份时，需要清空远程备份的数据
                                data[key][d].value = '';
                            }
                        }
                    }
                }
            }
            // 拨测频率 和 发包大小 发包个数 关联   选择的时候 要根据频率重置大小和个数值
            if ('DIAL_FREQUENCY' == res.key) {
                let array = JSON.parse(res.remarks);
                let dialFrequencyValue = res.value;
                for (var key in data) {
                    for (var d in data[key]) {
                        // 根据频率确定选项
                        if (data[key][d].key == 'DIAL_SIZE') {
                            for (let index in array) {
                                if (array[index].value == dialFrequencyValue) {
                                    data[key][d].valueDef = array[index].size;
                                    data[key][d].value = '';
                                    break;
                                }
                            }
                            //将选项
                        }
                        if (data[key][d].key == 'DIAL_NUM') {
                            for (let index in array) {
                                if (array[index].value == dialFrequencyValue) {
                                    data[key][d].valueDef = array[index].num;
                                    data[key][d].value = '';
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        },
        formatDate(date, format) {
            if (!date) return;
            if (!format) format = 'yyyy-MM-dd';
            switch (typeof date) {
                case 'string':
                    date = new Date(date.replace(/-/, '/'));
                    break;
                case 'number':
                    date = new Date(date);
                    break;
            }
            if (!date instanceof Date) return;
            var dict = {
                yyyy: date.getFullYear(),
                M: date.getMonth() + 1,
                d: date.getDate(),
                H: date.getHours(),
                m: date.getMinutes(),
                s: date.getSeconds(),
                MM: ('' + (date.getMonth() + 101)).substr(1),
                dd: ('' + (date.getDate() + 100)).substr(1),
                HH: ('' + (date.getHours() + 100)).substr(1),
                mm: ('' + (date.getMinutes() + 100)).substr(1),
                ss: ('' + (date.getSeconds() + 100)).substr(1)
            };
            return format.replace(/(yyyy|MM?|dd?|HH?|ss?|mm?)/g, function () {
                return dict[arguments[0]];
            });
        },
        resetForm() {
            // 重置的时候给value 赋值默认值。
            for (let key in this.myChildData) {
                let detailArr = this.myChildData[key];
                detailArr.forEach(element => {
                    if (element.radioType) {
                        // 获取默认的checkbox key
                        let elements = element.elements;
                        elements.forEach(item => {
                            if (element.checkboxKey == item.key) {
                                element.checkBoxValue = item.defaultValue == 'ENABLE';
                            } else {
                                element.value = item.defaultValue;
                            }
                        });
                    } else if (element.key == 'INTERRUPT_DEVICE_TYPE' || element.key == 'HIGH_INTERRUPT_DEVICE_TYPE') {
                        element.unit = element.defaultUnit;
                        element.value =element.defaultValue? element.defaultValue.split(','):[];
                    } else {
                        element.value = element.defaultValue;
                        element.unit = element.defaultUnit;
                    }
                });
            }
        },

        submitForm() {
            let keys = [];
            // 这一部做数据校验
            for (let key in this.myChildData) {
                for (var d in this.myChildData[key]) {
                    if (this.myChildData[key][d].required == 1 && (this.myChildData[key][d].value == null || this.myChildData[key][d].value == '')) {
                        this.$Message.error(this.myChildData[key][d].lable + this.$t("discover_cannot_be_null") +'!');
                        return;
                    }
                    if (this.myChildData[key][d].value && this.myChildData[key][d].value.length > 200) {
                        this.$Message.error(this.myChildData[key][d].lable + this.$t("discover_character_length")+'!');
                        return;
                    }
                }
            }
            // 这一步做数据处理
            for (let key in this.myChildData) {
                keys.push({ over: false, result: false });
                for (var d in this.myChildData[key]) {
                    if (this.myChildData[key][d].valueType == 'date') {
                        let time = this.myChildData[key][d].value;
                        if (typeof time === 'string') {
                            time = new Date(time);
                        }
                        this.myChildData[key][d].value = this.formatDate(time.getTime());
                    }
                    if (this.myChildData[key][d].valueType == 'pwd' || this.myChildData[key][d].valueType == 'userName') {
                        // 如果为空的话  不加密
                        if (this.myChildData[key][d].value) {
                            this.myChildData[key][d].value = handlePassword.encryption(this.myChildData[key][d].value + '');
                        }
                    }
                }
            }
            let successCount = 0;
            for (let key in this.myChildData) {
                if (this.$refs[key] && this.$refs[key][0]) {
                    this.$refs[key][0].validate(valid => {
                        if (valid) {
                            successCount++;
                        }
                    });
                }
            }
            setTimeout(() => {
                console.log(this.myChildData);

                let that = this;
                if (successCount == keys.length) {
                    // 保存数据。
                    let params = [];
                    for (let key in this.myChildData) {
                        let detailArr = this.myChildData[key];
                        detailArr.forEach(element => {
                            // 针对radio 进行特殊处理。
                            if (element.radioType) {
                                let elements = element.elements;
                                elements.forEach(raItem => {
                                    if (element.checkboxKey == raItem.key) {
                                        raItem.value = element.checkBoxValue ? 'ENABLE' : 'DISABLE';
                                    } else {
                                        raItem.value = element.value;
                                    }
                                    params.push({ id: raItem.id, value: raItem.value, unit: raItem.unit, status: raItem.status, conceal: raItem.conceal, valueType: raItem.valueType });
                                });
                            } else if (element.key == 'INTERRUPT_DEVICE_TYPE' || element.key == 'HIGH_INTERRUPT_DEVICE_TYPE') {
                                params.push({ id: element.id, value: element.value?element.value.join(','):'', unit: element.unit, status: element.status, conceal: element.conceal, valueType: element.valueType });
                            } else {
                                params.push({ id: element.id, value: element.value, unit: element.unit, status: element.status, conceal: element.conceal, valueType: element.valueType, valueDef: JSON.stringify(element.valueDef) });
                            }
                        });
                    }
                     this.saveLoading = true;
                    this.$http
                        .post('/defvalue/updateCodeTables', { codes: JSON.stringify(params) })
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success(this.$t("comm_success"));
                                for (let key in this.myChildData) {
                                    for (var d in this.myChildData[key]) {
                                        if (this.myChildData[key][d].valueType == 'pwd' || this.myChildData[key][d].valueType == 'userName') {
                                            if (this.myChildData[key][d].value) {
                                                this.myChildData[key][d].value = handlePassword.decryptByJSON(this.myChildData[key][d].value + '');
                                            }
                                        }
                                    }
                                }
                            } else {
                                this.$Message.error(msg);
                            }
                            this.saveLoading = false;
                        })
                        .catch(err => {
                            this.$Message.error(code.msg);
                        });
                }
            }, 500);
        }
    },
    watch: {
        childData(newData) {
             
            if (newData.自动备份) {
                let myDate = new Date();
                let h = '';
                newData.自动备份.forEach(item => {
                    if (item.key == 'BACK_TIME') {
                        if(null == item.value){
                            item.value = new Date().format2("HH:mm:ss");
                        }
                        this.saveDate = item.value;
                        h = item.value.split(':');
                        if (Number(h[0]) < myDate.getHours()) {
                            for (let i = 0; i < 60; i++) {
                                this.disabledM.push(i);
                            }
                        }
                    }
                    if (item.key == 'BACK_FIRST_DATE') {
                        if(null == item.value){
                            item.value = new Date().format2("yyyy-MM-dd");
                        }
                        let today = new Date(new Date().format('yyyy-MM-dd')).getTime() / 1000;
                        let other = new Date(item.value).getTime() / 1000;
                        if (other < today) {
                            for (let i = 0; i < 24; i++) {
                                this.disabledH.push(i);
                            }
                            for (let i = 0; i < 60; i++) {
                                this.disabledM.push(i);
                            }
                        } else if (other == today) {
                            if (Number(h[0]) <= myDate.getHours()) {
                                for (let i = 0; i < myDate.getMinutes(); i++) {
                                    this.disabledM.push(i);
                                }
                            } else {
                                this.disabledM = [];
                            }
                            for (let i = 0; i < myDate.getHours(); i++) {
                                this.disabledH.push(i);
                            }
                        } else if (other > today) {
                            this.disabledH = [];
                            this.disabledM = [];
                        }
                    }
                });
            }

            // 还需要把验证信息也生成。
            const validatePass = (rule, value, callback) => {
                let groupIndexArr = rule.field.split('_');
                let groupName = groupIndexArr[0];
                let index = parseInt(groupIndexArr[1]);
                let element = this.myChildData[groupName][index];
                let verfi = element.verfi;
                console.log('verfi', verfi);
                let msg = '';
                if (verfi) {
                    let verfiObj = {};
                    try {
                        verfiObj = JSON.parse(verfi);
                    } catch (error) {
                        console.log(error);
                        callback(new Error('验证配置有误'));
                        return;
                    }

                    let singleVer = {};
                    if (verfiObj.isunit) {
                        singleVer = verfiObj[element.unit];
                    } else {
                        singleVer = verfiObj;
                    }
                    let writeValue = element.value;
                    if (!singleVer) {
                        callback(new Error(this.$t("comm_item_filled")));
                        return;
                    }
                    if (singleVer.type == 'number') {
                        // 是数字
                        if (!/^[0-9]\d*$/.test(writeValue)) {
                            msg = this.$t("comm_item_filled_round");
                            callback(new Error(msg));
                            return;
                        }
                        if (writeValue.length > 12) {
                            msg = this.$t("comm_large_value");
                            callback(new Error(msg));
                            return;
                        }


                        writeValue = parseInt(writeValue);
                        if (singleVer.min !== undefined && writeValue < singleVer.min) {
                             var _msg = this.$t("comm_must_less") + singleVer.min;
                            callback(new Error(_msg));
                            return;
                        }
                        if (singleVer.max !== undefined && writeValue > singleVer.max) {
                             var _msg = this.$t("comm_must_greater") + singleVer.max;
                            callback(new Error(_msg));
                            return;
                        }

                        // 判断是否有关联属性。
                        if (singleVer.rel) {
                            let relKey = singleVer.rel.key;
                            let type = singleVer.rel.type;
                            // 找到关联的key
                            let relElents = this.myChildData[groupName];
                            let relEle = undefined;
                            relElents.forEach(item => {
                                if (item.key == relKey) {
                                    relEle = item;
                                }
                            });
                            if (relEle) {
                                let relValue = relEle.value;
                                if (relValue && /\d+/.test(relValue)) {
                                    if ('<' == type && writeValue > parseInt(relValue)) {

                                        //  callback(new Error(`该项值不得大于关联属性（${relEle.lable}）的值${singleVer.relValue}`));
                                        var _msg = this.$t("comm_must_less_attribute") + " ( "+relEle.lable+" ) " + this.$t("comm_value");
                                        callback(new Error(_msg));

                                        return;
                                    }
                                    if ('>' == type && writeValue < parseInt(relValue)) {
                                        //  callback(new Error(`该项值不得小于关联属性（${relEle.lable}）的值${singleVer.relValue}`));
                                        var _msg = this.$t("comm_must_greater_attribute") + " ( "+ relEle.lable +" ) " + this.$t("comm_value");
                                        callback(new Error(_msg));

                                        return;
                                    }
                                }
                            }
                            callback();
                        }
                    } else if (singleVer.type == 'String') {
                        try {
                            let reg = new RegExp(singleVer.reg);
                            if (!reg.test(writeValue)) {
                                callback(new Error(this.$t("comm_correct_value")));
                                return;
                            }
                        } catch (error) {
                            callback();
                        }

                        callback();
                        return;
                    }
                }
                callback();
            };
            let arr = [{ validator: validatePass }];
            for (let groupName in newData) {
                let obj = {};
                for (let i = 0; i < newData[groupName].length; i++) {
                    obj[groupName + '_' + i] = arr;
                }
                this.ruleCustom[groupName] = obj;
            }
            this.myChildData = newData;
            // alarm parameter
            if (this.myChildData[this.alarm_param]) {
                this.myChildData[this.alarm_param].forEach(item => {
                    if (item.key == 'INTERRUPT_DEVICE_TYPE' || item.key == 'HIGH_INTERRUPT_DEVICE_TYPE') {
                        item.value = item.value?item.value.split(','):[];
                    }
                });
            }

        }
    }
};
</script>

<style type="text/css" lang="less" scoped>
@import '../../style/default-manager.less';
.heartbeat {
    width: 240px;
    .heartbeat-unit {
        width: 64px;
        border-left: 0px !important;
    }
}
.remarks {
    min-width: 390px;
    &.disabled {
        color: #bebebe;
    }
}
.checkBox {
    display: flex;
    display: -webkit-flex;
    justify-content: flex-start;
}
/deep/ .ivu-input-group-append .ivu-select-selection {
    margin: 0px !important;
    border: 0px !important;
}
/deep/ .ivu-select-single .ivu-select-selection {
    height: 30px !important;
}
/deep/ .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    height: 28px !important;
    line-height: 28px !important;
    float: left !important;
}
.cz {
    width: 70px;
    height: 35px;
    color: var(--default_value_result_btn_color,#5ca0d5);
    background: var(--default_value_result_btn_bg_color,#061824);
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid var(--default_value_result_btn_border_color,#015197);
}
/* .ivu-switch {
    background-color: #032a4d !important;
    border: 1px solid #015197 !important;
}

.ivu-switch:after {
    background-color: #015197 !important;
}
/deep/ .ivu-switch-inner {
    color: #5ca0d5 !important;
} */
.ivu-switch {
    background: var(--switch_close_color , #032a4d) !important;
    border: 1px solid var(--switch_close_border_color , #015197) !important;
    width: 68px !important;
    height: 30px !important;
    text-align: right;
    /deep/.ivu-switch-inner {
        color: var(--switch_close_font_color , #5ca0d5) !important;
        font-size: 16px !important;
        line-height: 28px !important;
        left:40px !important;
    }
}
.ivu-switch-checked {
    border-color: #0290FD !important;
    background-color: #0290FD !important;
    width: 68px !important;
    height: 30px !important;
    /deep/.ivu-switch-inner {
        color: #DFF1FF !important;
        font-size: 16px !important;
        line-height: 28px !important;
        left:10px !important;
    }
}
.ivu-switch:after{
    width: 29px !important;
    height: 26px !important;
    background-color: var(--switch_close_after_bg_color , #015197) !important;
}
.ivu-switch-checked:after{
    width: 29px !important;
    height: 26px !important;
    background-color: #DFF1FF !important;
    left:36.5px !important;
}
</style>
