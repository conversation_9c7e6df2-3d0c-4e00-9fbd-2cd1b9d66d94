<template>
  <div class="homePage">
    <Menu
      v-if="menu"
      :active-name="menu.active"
      width="140px"
      class="menu-tables contentBox_bg"
      @on-select="menuSelect"
    >
      <MenuGroup :title="$t('comm_classification')">
        <MenuItem v-for="item in menuArr" :key="item.id" :name="item.key">{{
          item.lable
        }}</MenuItem>
      </MenuGroup>
    </Menu>
    <div class="menu-content contentBox_bg">
      <high-frequency-monitor :childData="childData" />
    </div>
  </div>
</template>
<script>
import highFrequencyMonitor from './highFrequency-monitor';
import cryptoAES from '@/common/crypto.js';
import handlePassword from '@/common/handlePassword';
export default {
    name: 'defaultConfig',
    components: {
        highFrequencyMonitor
    },
    data() {
        return {
            currentSkin:sessionStorage.getItem('dark') || 1,
            menuArr: [],
            childData: [],
            deviceTypeList:[],
            menu: {
                active: '1'
            },
            model: {
                active: null
            }
        };
    },
    created() {
        this.fillHead();
    },
    methods: {
        // 菜单选择
        menuSelect(active) {
            this.fillChild(active);
        },

        fillHead() {
            this.getCodeTableChild().then(data => {
                this.menuArr = data;
                if (data && data.length > 0) {
                    // 获取二级节点数据
                    this.fillChild(data[0].key);
                }
            });
        },

        fillChild(parentType) {
            this.getCodeTableChild(parentType).then(data => {
                this.parseChildData(data);
                this.menu.active = parentType;
            });
        },

        parseChildData(data) {
            // 根据group name 生成数组。
            let formData = {};
            let prvElement = {};
            data.forEach(element => {
                let groupName = element['groupName'];
                let innerGroupRel = element['innerGroupRel'];
                if (innerGroupRel) {
                    // 还有两条合成一条的情况。raidoType 需要删除这两条，再和成一条。 需要合并出一个新的属性，checkboxValue , defaultChecboxValue
                    this.pushElementToObjArr(groupName, prvElement, element);
                } else {
                    this.setUnitDefAndTypeByDef(element['unitDef'], element);
                    this.pushElementToObjArr(groupName, formData, element);
                }
            });
            this.prvElementToFromData(prvElement, formData);
            this.childData = formData;
        
        },

        prvElementToFromData(prvElement, formData) {
            for (let groupName in prvElement) {
                this.pushElementToObjArr(groupName, formData, this.getSingleElement(prvElement[groupName]));
            }
        },

        getSingleElement(prvElement) {
            let obj = {};
            // 整合prv
            if (prvElement.length > 0) {
                obj.radioType = 1;
                let innerGroupRel = JSON.parse(prvElement[0].innerGroupRel);
                for (let key in innerGroupRel) {
                    obj[key] = innerGroupRel[key];
                }
                prvElement.forEach(item => {
                    if (item.key == innerGroupRel.checkboxKey) {
                        obj.defaultCheckValue = item.defaultValue;
                        obj.checkBoxValue = item.value == 'ENABLE';
                    } else {
                        obj.value = item.value;
                        obj.description = item.description;
                        obj.verfi = item.verfi;
                        obj.valueType = item.valueType;
                        this.setUnitDefAndTypeByDef(item.unitDef, obj);
                    }
                });
                obj.elements = prvElement;
            }
            return obj;
        },

        pushElementToObjArr(groupName, formData, element) {
            if (groupName) {
                if (formData[groupName]) {
                    formData[groupName].push(element);
                } else {
                    formData[groupName] = [element];
                }
            }
        },

        setUnitDefAndTypeByDef(unitDef, element) {
            // 里面分三种情况。 unitType ==1 没有单位，2 普通单位，3 下拉选单位，
            let unitType = 1;
            let newUnitDef = unitDef;
            if (unitDef) {
                let unitDefArray = [];
                try {
                    unitDefArray = JSON.parse(unitDef);
                    if (unitDefArray.length > 0) {
                        unitType = 3;
                    }
                    newUnitDef = this.getKeyLaelByArray(unitDefArray);
                } catch (error) {
                    unitType = 2;
                }
            }
            element.unitDef = newUnitDef;
            element.unitType = unitType;
            // 重新修改valueDef,
            let valueType = element.valueType;
            let valueDef = element.valueDef;
            if (!valueType) {
                valueType = 'text';
            }
            if (valueType && 'select,switch'.indexOf(valueType) != -1) {
                let array = [];
                try {
                    array = JSON.parse(valueDef);
                } catch (error) {}
                // valueDef = this.getKeyLaelByArray(array);
                element.valueDef = array;
                element.valueType = valueType;
                ;
            }
        },

        getKeyLaelByArray(unitDefArray) {
            let newUnitDef = [];
            newUnitDef = unitDefArray.map(val => {
                for (let key in val) {
                    return { key: key, label: val[key] };
                }
            });
            return newUnitDef;
        },

        getCodeTableChild(parentType) {
            if (!parentType) {
                parentType = 'DEFAULT_MANAGER';
            }
            let p = new Promise((resolve, reject) => {
                this.$http
                    .PostJson('/defvalue/getCodeTableChildInfos',{parentType:parentType})
                    .then(({ code, data, msg }) => {
                        if (code === 1) {
                            for (var d in data) {
                                if (data[d].valueType == 'pwd' || data[d].valueType == 'userName') {
                                    data[d].value = handlePassword.decrypt(data[d].value);
                                }
                            }
                            resolve(data);
                        } else {
                            this.$Message.error(code.msg);
                            reject([]);
                        }
                    })
                    .catch(err => {
                        this.$Message.error(code.msg);
                        reject([]);
                    });
            });
            return p;
        }
    }
};
</script>

<style type="text/css" lang="less" scoped>
.homePage {
  display: flex;
  justify-content: flex-start;
  height: initial;
  min-height: 100%;
  padding: 20px 20px 20px 0;
}
.homePage-light {
  padding-left: 20px;
}
.menu-tables {
  border-radius: 4px;
  overflow: auto;
  background: var(--body_conent_b_color, #061824) !important;

  color: var(--font_color, #303748) !important;
}
.menu-content {
  position: relative;
  min-height: 100%;
  width: calc(100%);
  // background-color: #fff;
  margin-left: 12px;
  padding: 20px 30px 0;
  box-sizing: border-box;
  border-radius: 6px;
  text-align: left;
  background: var(--body_conent_b_color, #061824) !important;
  color: var(--font_color, #303748);
}

/deep/ .ivu-input-small {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 16px !important;
}

/deep/ .ivu-input-group-small .ivu-input {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 16px !important;
}

/deep/ .ivu-date-picker-editor .ivu-input {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 16px !important;
}

.menu-content,
.menu-content * {
  user-select: none;
}
/deep/ .ivu-menu-vertical .ivu-menu-item-group-title {
  font-family: "Arial Negreta", "Arial";
  padding-left: 0;
  font-weight: 700;
  font-style: normal;
  font-size: 16px;
  text-align: center;
  color: var(--font_color, #303748) !important;
  // border-width: 0 0 2px;
  // border-style: solid;
  border-color: var(--border_color, #ddd);
}
.ivu-menu-item {
  // border-width: 0 0 1px;
  // border-style: solid;
  color: var(--input_font_color, #5ca0d5) !important;
  border-color: var(--modal_input_border_color, #dcdee2) !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  width: 0px !important;
  background: var(--modal_input_border_color, #dcdee2) !important;
}

.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  // background: var(--default_value_menu_select_bg_color , #16436b) !important;
  // color: var(--default_value_menu_select_font_color ,#05eeff) !important;
  background: var(--defaultmanager_left_menu_bg_color, #16436b) !important;
  color: var(--defaultmanager_left_menu_font_color, #05eeff) !important;
}

.ivu-menu-light.ivu-menu-vertical
  .ivu-menu-item-active:not(.ivu-menu-submenu):after {
  width: 0px;
}
/deep/.ivu-input-group-append {
  background: var(--default_value_group_append_bg_color, #061824) !important;
  color: var(--font_color, #303748) !important;
  border-color: var(
    --default_value_group_append_border_color,
    #015197
  ) !important;
}
</style>
