const lan = require('../../../common/language')
"use strict";
import { $http } from "@/server/http";

// 获取人员管理
export const getPerson = ({ commit, state }, param) => {
  $http
    .post("user/getUserList", param)
    .then(res => {
      let data = res.data;
      commit("setPerson", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取客户列表
export const getClientList = ({ commit, state }, param) => {
  if (!param) {
    param = {
      pageNo:1,
      pageSize:10000,
      province:'',
      city:'',
      area:'',
      keyword:'',
    }
  }
  $http
    .post("org/list", param)
    .then(res => {
      let data = res.data;
      if (res.code === 1) {
        commit("setClientList", data.records);
      }else{
        throw new Error(lan.getLabel("src.EGI"))
      }
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取客户列表（用于故障管理）
export const getClientList1 = ({ commit, state }, param) => {
  $http
    .post("client/getClientList1", param)
    .then(res => {
      let data = res.data;
      commit("setClientList1", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//策略配置中修改选择目的ip
export const listProbeTargetName = ({ commit, state }, param) => {
  $http
    .post("target/getAllProbeTargetName", param)
    .then(res => {
      let data = res.data;
      commit("setListProbeTargetName", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//策略配置中修改根据选择目的ip选择名称
export const getProbeTargetNameByIP = ({ commit, state }, param) => {
  $http
    .post("target/getProbeTargetNameByIP", param)
    .then(res => {
      let data = res.data;
      commit("setListProbeTargetNameByIP", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 链路类型配置列表
export const getLinkTypeList = ({ commit, state }, param) => {
  $http
    .post("config/listLinkForNameAndClientKey", param)
    .then(res => {
      let data = res.data;
      commit("setLinkTypeList", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路类型列表
export const getLinkTypeListss = ({ commit, state }, param) => {
  $http
    .post("linktype/getLinkTypeList", param)
    .then(res => {
      let data = res.data;
      commit("setLinkTypeListss", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路配置列表
export const getListLinks = ({ commit, state }, param) => {
  $http
    .post("link/listLink", param)
    .then(res => {
      let data = res.data;
      commit("setListLinks", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取告警等级
export const getAramLIst = ({ commit, state }, param) => {
  $http
    .post("alarm/levelList", param)
    .then(res => {
      let data = res.data;
      commit("setAramLIst", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取告警规则列表
export const getAramRoleList = ({ commit, state }, param) => {
  $http
    .post("alarm/list", param)
    .then(res => {
      let data = res.data;
      commit("setAramRoleList", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取公共节点对比分析
export const getPublicNode = ({ commit, state }, param) => {
  $http
    .post("node/nodesList", param)
    .then(res => {
      let data = res.data;
      commit("setPublicNode", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取公共节点对比分析承载链路总数
export const getChengzai = ({ commit, state }, param) => {
  $http
    .post("node/hostLinkList", param)
    .then(res => {
      let data = res.data;
      commit("setChengzai", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取公共节点对比分析中断链路总数
export const getZhongduan = ({ commit, state }, param) => {
  $http
    .post("node/brokenList", param)
    .then(res => {
      let data = res.data;
      commit("setZhongduan", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取公共节点对比分析劣化链路总数

// 获取公共节点对比分析链路树图
export const getlinkMore = ({ commit, state }, param) => {
  $http
    .post("node/nodeIpList", param)
    .then(res => {
      let data = res.data;
      commit("setlinkMore", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取公共节点对比分析 单链路图
export const getLinks = ({ commit, state }, param) => {
  $http
    .post("node/oneRouteList", param)
    .then(res => {
      let data = res.data;
      commit("setLinks", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取实时告警
export const getRealTimeAlarm = ({ commit, state }, param) => {
  $http
    .post("alarmlog/realTimeList", param)
    .then(res => {
      let data = res.data;
      commit("setRealTimeAlarm", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

export const getAlarmDetail = ({ commit, state }, param) => {
  $http
    .post("alarmlog/getRealTime", param)
    .then(res => {
      let data = res.data;
      commit("setAlarmDetail", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取历史告警
export const gethistoryAlarm = ({ commit, state }, param) => {
  $http
    .post("alarmlog/historyAlarmList", param)
    .then(res => {
      let data = res.data;
      console.log(res.data);
      commit("sethistoryAlarm", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路查询列表
export const getLinkSearch = ({ commit, state }, param) => {
  $http
    .post("statlink/listStatLinkDay", param)
    .then(res => {
      let data = res.data;
      commit("setLinkSearch", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//查看链路详情
export const getLinkSearchDetail = ({ commit, state }, param) => {
  $http
    .post("statlink/getDetailStatLinkDay", param)
    .then(res => {
      let data = res.data;
      commit("setLinkSearchDetail", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路数据统计
export const getLinkNumber = ({ commit, state }, param) => {
  $http
    .post("statlink/getLinkNumber", param)
    .then(res => {
      let data = res.data;
      commit("setLinkNumber", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取中断链路信息
export const getListBrokenEvent = ({ commit, state }, param) => {
  $http
    .post("broken/listBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setListBrokenEvent", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//中断时长分布
export const getBrokenTime = ({ commit, state }, param) => {
  $http
    .post("broken/listTimeForBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenTime", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//中断次数分布
export const getBrokenCount = ({ commit, state }, param) => {
  $http
    .post("broken/listCountForBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenCount", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//中断链路统计
export const getBrokenAll = ({ commit, state }, param) => {
  $http
    .post("broken/getBrokenEventNumber", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenAll", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取中断链路详细信息
export const getBrokenEvent = ({ commit, state }, param) => {
  $http
    .post("broken/getBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenEvent", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getBrokenEventB = ({ commit, state }, param) => {
  $http
    .post("broken/getBrokenEvent2", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenEventB", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 劣化链路分析
export const getDeglist = ({ commit, state }, param) => {
  $http
    .post("logdeg/deglist", param)
    .then(res => {
      let data = res.data;
      commit("setDeglist", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getdegDetails = ({ commit, state }, param) => {
  $http
    .post("logdeg/degDetails", param)
    .then(res => {
      let data = res.data;
      commit("setdegDetails", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getDevModel = ({ commit, state }, param) => {
  $http
    .post("snmpoid/getDevModel", param)
    .then(res => {
      let data = res.data;
      var resultArr = [];
      resultArr = data.filter(item => {
        return item !== "" && item != undefined;
      });
      commit("setDevModel", resultArr);
    })
    .catch(err => {
      //console.log(err)
    });
};
const getUnit = (value, type) => {
  let data = value;
  const transformSecond = [1, 60, 3600, 24 * 3600, 24 * 7 * 3600];
  switch (type) {
    case 1:
      if (data % transformSecond[4] == 0) {
        if (data / transformSecond[4] != 0) {
          return [data / transformSecond[4], 5];
        }
      } else if (data % transformSecond[3] == 0) {
        if (data / transformSecond[3] != 0) {
          return [data / transformSecond[3], 4];
        }
      } else if (data % transformSecond[2] == 0) {
        if (data / transformSecond[2] != 0) {
          return [data / transformSecond[2], 3];
        }
      } else if (data % transformSecond[1] == 0) {
        if (data / transformSecond[1] != 0) {
          return [data / transformSecond[1], 2];
        }
      } else {
        return [data / transformSecond[0], 1];
      }
      break;
    case 2:
      if (data % transformSecond[1] == 0) {
        if (data / transformSecond[1] != 0) {
          return [data / transformSecond[1], 2];
        }
      } else {
        return [data / transformSecond[0], 1];
      }
      break;
  }
};
export const getDefaultConfigList = ({ commit, state }, param) => {
  $http
    .post("defaultValue/getDefaultValue", param)
    .then(res => {
      let defaultValue = {};
      let data = res.data;
      for (let i = 0, len = res.data.length; i < len; i++) {
        if (data[i].type == 1 && data[i].key == lan.getLabel("src.PHT")) {
          let dataArr = getUnit(data[i].value, 2);
          defaultValue.sourceThreshold = dataArr[0];
          defaultValue.sourceThresholdUnit = dataArr[1];
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.probePort")) {
          defaultValue.dialTestSourcePort = data[i].value;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.UDPDestination")) {
          defaultValue.dialTestUdpPort = data[i].value || 53;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.TCPDestination")) {
          defaultValue.dialTestTcpPort = data[i].value || 80;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.DialTestFrequency")) {
          let dataArr = getUnit(data[i].value, 2);
          defaultValue.dialTestFrequency = dataArr[0];
          defaultValue.dialTestFrequencyUnit = dataArr[1];
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.StartingTTL")) {
          defaultValue.dialTestStratTTL = data[i].value;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.TraceLimited")) {
          defaultValue.dialTestTrace = data[i].value;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.AlarmPeriodT")) {
          defaultValue.dialTestAlarmCycle = data[i].value;
          defaultValue.dialTestAlarmCycleUnit = data[i].unit || "h";
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.NumberOfAlarms")) {
          defaultValue.dialTestAlarmNumber = data[i].value;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.IGT")) {
          defaultValue.dialTestBe_thr_value = data[i].value;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.DDGT")) {
          defaultValue.dialTestDe_thr_value = data[i].value;
        }
        if (data[i].type == 2 && data[i].key == lan.getLabel("src.PLDGT")) {
          defaultValue.packetLossDegradation = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.probePort")) {
          defaultValue.highSourcePort = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.UDPDestination")) {
          defaultValue.highUdpPort = data[i].value || 53;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.TCPDestination")) {
          defaultValue.highTcpPort = data[i].value || 80;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.StartingTTL")) {
          defaultValue.highStartTTL = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.TraceLimited")) {
          defaultValue.highRoutePath = data[i].value;
        }

        if (data[i].type == 3 && data[i].key == lan.getLabel("src.AlarmPeriodT")) {
          defaultValue.highAlarmCycle = data[i].value;
          defaultValue.highAlarmCycleUnit = data[i].unit || "h";
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.NumberOfAlarms")) {
          defaultValue.highAlarmNumber = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.WindowSizeW")) {
          defaultValue.highWindow = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.PositioningTimes")) {
          defaultValue.highSum = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.PLDGTES")) {
          defaultValue.highPlFlag = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.PLDGT")) {
          defaultValue.highPacketLossDegradation = data[i].value;
        }

        if (data[i].type == 4 && data[i].key == lan.getLabel("src.Delay/packet")) {
          let dataArr = getUnit(data[i].value, 1);
          defaultValue.relayDelayLoss = dataArr[0];
          defaultValue.relayDelayLossUnit = dataArr[1];
        }
        if (data[i].type == 4 && data[i].key == lan.getLabel("src.flow")) {
          let dataArr = getUnit(data[i].value, 1);
          defaultValue.relayFlow = dataArr[0];
          defaultValue.relayFlowUnit = dataArr[1];
        }
        if (data[i].type == 4 && data[i].key == lan.getLabel("src.DeviceInformation")) {
          let dataArr = getUnit(data[i].value, 1);
          defaultValue.relayEqInfor = dataArr[0];
          defaultValue.relayEqInforUnit = dataArr[1];
        }

        if (data[i].type == 4 && data[i].key == lan.getLabel("src.AlarmPeriodT")) {
          defaultValue.relayAlarmCycle = data[i].value;
          defaultValue.relayAlarmCycleUnit = data[i].unit || "h";
        }
        if (data[i].type == 4 && data[i].key == lan.getLabel("src.NumberOfAlarms")) {
          defaultValue.relayAlarmNumber = data[i].value;
        }

        if (data[i].type == 4 && data[i].key == lan.getLabel("src.IGT")) {
          defaultValue.relayBe_thr_value = data[i].value;
        }
        if (data[i].type == 4 && data[i].key == lan.getLabel("src.DDGT")) {
          defaultValue.relayDe_thr_value = data[i].value;
        }
        if (data[i].type == 4 && data[i].key == lan.getLabel("src.PLDGT")) {
          defaultValue.relayPacketLoss = data[i].value;
        }
        if (data[i].type == 4 && data[i].key == lan.getLabel("src.congestionThreshold")) {
          defaultValue.relayJam = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.IGT")) {
          defaultValue.highBe_thr_value = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.DGT")) {
          defaultValue.highDe_thr_value = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.IRT")) {
          defaultValue.highRe_be_thr_value = data[i].value;
        }
        if (data[i].type == 3 && data[i].key == lan.getLabel("src.DRT")) {
          defaultValue.highRe_de_thr_value = data[i].value;
        }

        if (data[i].type == 5 && data[i].key == lan.getLabel("src.AlarmPeriodT")) {
          defaultValue.AlarmCycle = data[i].value;
          defaultValue.AlarmCycleUnit = data[i].unit || "h";
        }
        if (data[i].type == 5 && data[i].key == lan.getLabel("src.NumberOfAlarms")) {
          defaultValue.AlarmNumber = data[i].value;
        }
      }
      commit("setDefaultConfigList", defaultValue);
    })
    .catch(err => {
      //console.log(err)
    });
};
