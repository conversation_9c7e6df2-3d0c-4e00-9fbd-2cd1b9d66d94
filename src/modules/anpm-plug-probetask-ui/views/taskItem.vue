<template>
  <section>
    <Form
      class="task-form"
      ref="taskForm"
      :model="formData"
      :rules="rulesValidate"
      :label-width="130"
      @submit.native.prevent
      inline
    >
      <Row>
        <Col span="12">
          <FormItem
            :label="$t('comm_org') + ': '"
            prop="orgId"
            style="width: 100%"
          >
            <TreeSelect
              v-model="treeValue"
              ref="TreeSelect"
              :data="treeData"
              :placeholder="$t('snmp_pl_man')"
              :loadData="loadOrgTreeData"
              :disabled="eventType == 2"
              @onSelectChange="setOrg"
              @onFocus="focusFn"
              @onClear="onClear"
            ></TreeSelect>
          </FormItem>
          <!-- 界面导航 -->
          <div
            class="nav-tip-left must-tip"
            v-if="tipStatus == 'true' && isMustTip"
          >
            <div class="tip-text">{{ $t("nav_step4_tip") }}</div>
            <div class="tip-btn">
              <Button class="current-btn" @click="closeCurrent(4)">{{
                $t("nav_current_btn")
              }}</Button>
            </div>
          </div>
        </Col>
        <Col span="12">
          <!-- 分组 -->
          <FormItem
            :label="$t('comm_group') + ': '"
            prop="groupIds"
            style="width: 100%"
          >
            <Select
              ref="serviceStatusClear"
              :filterable="groupCreate"
              :only-filter-with-text="true"
              :allow-create="groupCreate"
              v-model="groupIds"
              multiple
              clearable
              :placeholder="$t('comm_select_group')"
              @on-create="handleGroupCreate"
            >
              <Option
                v-for="item in groupList"
                :value="item.id"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
        </Col>
        <!-- 运维等级 -->
        <Col span="12">
          <FormItem
            :label="$t('comm_om_level') + ': '"
            prop="maintainLevel"
            style="width: 100%"
          >
            <Select
              v-model="formData.maintainLevel"
              clearable
              :placeholder="$t('discover_select_maintain_level')"
            >
              <Option
                v-for="item in maintainList"
                :value="item.value"
                :key="item.value"
                >{{ item.label }}</Option
              >
            </Select>
          </FormItem>
        </Col>
        <Col span="12">
          <!-- 目标类型 -->
          <FormItem
            :label="$t('comm_target_type') + ': '"
            prop="destType"
            style="width: 100%"
          >
            <Select
              v-model="formData.destType"
              :only-filter-with-text="true"
              filterable
              allow-create
              maxlength="40"
              :placeholder="$t('comm_select_type')"
              @on-select="selectType"
              @on-create="handleDeviceTypeCreate"
            >
              <Option
                v-for="item in deviceTypeList"
                :value="item.value"
                :key="item.value"
                >{{ item.lable }}</Option
              >
            </Select>
          </FormItem>
        </Col>
        <Col span="12">
          <!-- 拨测类型 -->
          <FormItem
            :label="$t('task_dial_type') + ': '"
            prop="configType"
            style="width: 100%"
          >
            <Select
              v-model="formData.configType"
              :placeholder="$t('snmp_pl_man')"
              @on-select="selectConfigType"
              filterable
              :only-filter-with-text="true"
              :disabled="eventType == 2"
              :style="sipProShow ? 'width:68%' : 'width:100%'"
            >
              <Option
                v-for="item in probetaskTypeList"
                :value="item.id"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
            <Select
              v-model="formData.sipPro"
              :placeholder="$t('snmp_pl_man')"
              v-show="sipProShow"
              filterable
              :only-filter-with-text="true"
              style="width: 100px"
              :disabled="eventType == 2"
            >
              <Option
                v-for="item in sipTypeList"
                :value="item.id"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <!-- 探针ip -->
        <Col span="12">
          <FormItem
            :label="$t('comm_probe_ip') + ': '"
            prop="sourceIp"
            style="width: 100%"
          >
            <Select
              v-model="formData.getherCode"
              :placeholder="$t('comm_probe_ip_address')"
              @on-select="selectSourceIp"
              filterable
              :only-filter-with-text="true"
              :disabled="eventType == 2"
            >
              <Option
                v-for="(item, index) in probeIpList"
                :value="item.label"
                :tag="item.value"
                :key="item.label + '-' + index"
                >{{ item.labelName }}</Option
              >
            </Select>
          </FormItem>
        </Col>
        <!-- 探针端口 -->
        <Col span="12" v-show="targetShow">
          <FormItem
            :label="$t('pathtopo_probe_port') + ': '"
            prop="sourcePort"
            style="width: 100%"
          >
            <Input
              v-model.trim="formData.sourcePort"
              :placeholder="$t('pathtopo_enter_probe_port')"
              :disabled="eventType == 2"
            />
          </FormItem>
        </Col>
      </Row>

      <Row>
        <!-- 目标ip -->
        <Col :span="targetShow ? '8' : '12'">
          <FormItem
            :label="$t('access_destination_ip')+'(IPv4/v6)' + ': '"
            :prop="getProp()"
            style="width: 100%"
          >
            <!-- 新建的显示 -->
            <Input
              :disabled="eventType == 2 || destIpDisabled"
              v-if="dynamicMacType == 0"
              v-model.trim="formData.destIp"
              :placeholder="$t('comm_probe_destination_ip_address')"
              :title="$t('access_enter_destination_ip')"
              @on-change="setSession('destIp', formData['destIp'])"
            />

            <!-- 编辑任务的显示  -->
            <Select
              v-model="formData.destIp"
              v-if="dynamicMacType == 1"
              clearable
              :placeholder="$t('comm_probe_destination_ip_address')"
              :title="$t('access_enter_destination_ip')"
              @on-select="destIpMacChange"
            >
              <Option
                v-for="item in destIpArry"
                :value="item.destIp"
                :key="item.destIp"
                :id="item.id"
                >{{ item.destIp }}
              </Option>
            </Select>
          </FormItem>
        </Col>
        <Col :span="targetShow ? '8' : '12'">
          <FormItem
            :label="$t('access_mac') + ': '"
            prop="destMac"
            style="width: 100%"
          >
            <Input
              v-model.trim="formData.destMac"
              :disabled="eventType == 2 || macDisabled"
              :placeholder="$t('access_enter_mac')"
            />
          </FormItem>
        </Col>
        <Col :span="targetShow ? '8' : '12'" v-show="targetShow">
          <FormItem
            :label="$t('pathtopo_target_port') + ': '"
            prop="destPort"
            style="width: 100%"
          >
            <Input
              v-model.trim="formData.destPort"
              :placeholder="$t('comm_enter_destination_port')"
              :disabled="eventType == 2"
            />
          </FormItem>
        </Col>
        <div class="tips">*{{ $t("warning_mac_ip") }}</div>
        <div class="dynamicIp">
          <Checkbox
            v-model="formData.dynamicIp"
            class="checxbox"
            :true-value="1"
            :false-value="0"
            :disabled="eventType == 2"
            >{{ $t("probe_dynamic") }}</Checkbox
          >
          <span>{{ $t("probe_2") }}</span>
        </div>

        <div class="dynamicIp" v-show="targetShow">
          <Checkbox
            v-model="formData.netState"
            class="checxbox"
            :true-value="1"
            :false-value="0"
            :disabled="eventType == 2"
            >{{ $t("probe_net_state") }}</Checkbox
          >
          <span>{{ $t("probe_net_state_tip") }}</span>
        </div>
      </Row>

      <Row>
        <!-- <Col span="12">
          <FormItem label="响应IP：" prop="respondIp" style="width: 100%">
            <Input
                v-model="formData.respondIp"
                @on-change="setSession('respondIp', formData['respondIp'])"
                placeholder="响应IP与目标IP不同时填写，相同时无需填写，支持“,”分隔填写多个IP"
            />
          </FormItem>
        </Col> -->
        <Col span="12">
          <FormItem
            :label="$t('comm_target_name') + ': '"
            style="width: 100%"
            prop="destIpName"
          >
            <Input
              v-model="formData.destIpName"
              @on-change="setSession('destIpName', formData['destIpName'])"
              :placeholder="$t('comm_enter_target_name')"
              maxlength="50"
            />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <FormItem
            :label="$t('task_location') + ': '"
            style="width: 150%"
            prop="location"
          >
            <Input
              v-model="formData.location"
              @on-change="setSession('location', formData['location'])"
              :placeholder="$t('please_input_dest_task_location')"
              maxlength="50"
            />
          </FormItem>
        </Col>
      </Row>

      <!-- 切换按钮 -->
      <Row class="configuration">
        <Col span="8" style="position: relative">
          <!-- 切换按钮  -->
          <div
            class="conditionBtn"
            @click="conditionClick"
            style="margin-left: 130px; margin-bottom: 20px"
          >
            {{ showParam ? $t("comm_simple_task") : $t("comm_advanced_task") }}
            <Icon
              class="patternIcon"
              :class="showParam ? '' : 'myrotate'"
              type="ios-arrow-up"
            />
          </div>

          <!-- 界面导航 -->
          <div
            class="nav-tip-left toggle-button-tip"
            v-if="tipStatus == 'true' && isToggleButtonTip"
          >
            <div class="tip-text">{{ $t("nav_step14_tip") }}</div>
            <div class="tip-btn">
              <Button class="current-btn" @click="closeCurrent(6)">{{
                $t("nav_current_btn")
              }}</Button>
            </div>
          </div>
        </Col>
      </Row>

      <div v-show="showParam">
        <div class="divider">
          <div class="divider-title" style="min-width: 100px">
            {{ $t("probe_param_setting") }}
          </div>
          <div class="line"></div>
        </div>
        <!-- 拨测参数 高级 设置 -->
        <Row>
          <!-- 拨测频率 -->
          <Col span="12">
            <FormItem
              :label="$t('probe_frequency') + ': '"
              prop="dialInterval"
              style="width: 100%"
            >
              <Select
                v-model="formData.dialInterval"
                clearable
                @on-select="dialChange"
                :disabled="eventType == 2"
              >
                <Option
                  v-for="item in dialFrequencyList"
                  :value="item.value"
                  :key="item.value"
                  :id="item.id"
                  >{{ item.label }}
                </Option>
              </Select>
            </FormItem>
            <!-- 界面导航 -->
            <div
              v-if="tipStatus == 'true' && isTimeTip"
              class="nav-tip-left frequency-tip"
            >
              <div class="tip-text">{{ $t("nav_step5_tip") }}</div>
              <div class="tip-btn">
                <Button class="current-btn" @click="closeCurrent(5)">{{
                  $t("nav_current_btn")
                }}</Button>
              </div>
            </div>
          </Col>
          <Col span="12">
            <FormItem
              :label="$t('probe_packets_no') + ': '"
              prop="packetNum"
              style="width: 100%"
              :label-width="150"
            >
              <Select
                v-model="formData.packetNum"
                clearable
                :disabled="eventType == 2"
                style="width: 80%"
              >
                <Option
                  v-for="item in packNumList"
                  :value="item.value"
                  :key="item.value"
                  :id="item.id"
                  >{{ item.label }}
                </Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem
              :label="$t('probe_packet_size') + ': '"
              prop="packetSize"
              style="width: 100%"
            >
              <div class="packetSize">
                <Select
                  v-model="formData.packetSize"
                  clearable
                  :disabled="eventType == 2"
                  style="width: 80%"
                >
                  <Option
                    v-for="item in packSizeList"
                    :value="item.value"
                    :key="item.value"
                    :id="item.id"
                    >{{ item.label }}
                  </Option>
                </Select>
                <span>Byte</span>
              </div>
            </FormItem>
          </Col>

          <!-- tos 值配置 -->
          <Col span="12">
            <FormItem
              :label="$t('probe_tos_value') + ': '"
              prop="tosValue"
               :label-width="150"
              style="width: 100%"
            >
                <Select
                  v-model="formData.tosValue"
                  clearable
                  :disabled="eventType == 2"
                  style="width: 80%"
                >
                  <Option
                    v-for="item in tosList"
                    :value="item.value"
                    :key="item.value"
                    :id="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
            </FormItem>
          </Col>
        </Row>
        <div class="divider">
          <div class="divider-title" style="min-width: 100px">
            {{ $t("probe_event_setting") }}
          </div>
          <div class="line"></div>
        </div>
        <Row>
          <Col span="24">
            <!--   :label-width="auto"  -->
            <FormItem
              :label="$t('probe_delay_sensitivity_2') + ': '"
              prop="sens"
            >
              <div class="packetSize">
                <Input
                  v-model="formData.sens"
                  style="width: 100px"
                  :placeholder="
                    $t('comm_enter') + $t('probe_delay_sensitivity_2')
                  "
                  maxlength="2"
                />
                <span
                  style="
                    margin-left: 10px;
                    display: block;
                    text-align: left;
                    width: auto;
                  "
                >
                  {{ $t("probe_delay_sensitivity_3") }}</span
                >
              </div>
            </FormItem>
          </Col>
          <!-- <Col span="12">
          <span style="margin-left:20px;line-height: 35px;">
            数值越小,系统感知时延劣化越灵敏度,1-10个等级</span>
        </Col> -->

          <Col span="24">
            <!-- :label-width="auto" -->
            <FormItem
              :label="$t('probe_experience_value') + ': '"
              prop="delayThreshold"
            >
              <div class="packetSize">
                <Input
                  v-model="formData.delayThreshold"
                  :placeholder="$t('comm_enter') + $t('probe_experience_value')"
                  maxlength="5"
                  style="width: 100px"
                />
                <span>ms</span>
                <span
                  style="
                    margin-left: 10px;
                    display: block;
                    text-align: left;
                    width: auto;
                  "
                >
                  {{ $t("probe_delay_sensitivity_4") }}</span
                >
              </div>
            </FormItem>
          </Col>
          <!-- <Col span="12">
          <span style="margin-left:20px;line-height: 35px;">
          无正常历史数据可学习时,使用该值判断是否劣化,允许输入1-3000</span>
        </Col> -->
        </Row>
        <div class="divider">
          <div class="divider-title" style="min-width: 100px">
            {{ $t("snmptask_alarm_parameter") }}
          </div>
          <div class="line"></div>
        </div>
        <div
          class="threshold-content"
          v-show="showSilde"
          style="font-size: 14px !important"
        >
          <div class="emergency">
            <div class="threshold-box">
              <div class="threshold-item" style="width: auto">
                <label style="width: auto"
                  >{{ $t("task_interrupt_generation_threshold") }}：</label
                >
                <div
                  class="threshold-item-content"
                  style="width: auto; float: left; margin-left: 0px"
                >
                  <div class="threshold-item-input" style="width: 50px">
                    <Input v-model="formData.beThrValue" style="width: 100%" />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <div class="threshold-item" style="width: auto">
                <label style="width: auto"
                  >{{
                    $t("task_delay_degradation_generation_threshold")
                  }}：</label
                >
                <div
                  class="threshold-item-content"
                  style="width: auto; float: left; margin-left: 0px"
                >
                  <div class="threshold-item-input" style="width: auto">
                    <Input v-model="formData.deThrValue" style="width: 50px" />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <br />
              <div class="threshold-item mgT10" style="margin-left: 0px">
                <label style="width: auto">{{ $t("warning_loss") }}：</label>
                <div
                  class="threshold-item-content"
                  style="margin-left: auto; float: left"
                >
                  <div class="threshold-item-input">
                    <Input v-model="formData.lrDeThrValue" style="width: 70%" />
                  </div>
                  <div class="threshold-item-unit">%</div>
                </div>
              </div>
              <div class="threshold-item mgT10" style="margin-left: 10px">
                <label style="width: auto"
                  >{{ $t("probetask_or_continuous") }}：</label
                >
                <div
                  class="threshold-item-content"
                  style="margin-left: auto; float: left"
                >
                  <div class="threshold-item-input">
                    <Input
                      v-model="formData.lrDeContinueNum"
                      style="width: 70%"
                    />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <div class="threshold-item mgT10" style="margin-left: 5px">
                <label style="width: auto"
                  >{{ $t("rpmanager_reach1") }}：</label
                >
                <div
                  class="threshold-item-content"
                  style="margin-left: auto; float: left"
                >
                  <div class="threshold-item-input">
                    <Input v-model="formData.lrDeReachPer" style="width: 70%" />
                  </div>
                  <div class="threshold-item-unit">%</div>
                </div>
              </div>
            </div>
            <div class="threshold-box">
              <div class="threshold-item">
                <label style="text-align: left">
                  <Checkbox
                    v-model="formData.surgeAlarm"
                    true-value="1"
                    false-value="0"
                    style="width: 350px"
                    >{{ $t("probe_route_alarm") }}
                  </Checkbox>
                </label>
              </div>
              <div class="threshold-item" style="margin-left: 140px">
                <label style="text-align: left">
                  <Checkbox
                    v-model.trim="formData.interruptState"
                    style="width: 450px"
                    true-value="0"
                    false-value="1"
                  >
                    {{ $t("probe_no_occurs") }}</Checkbox
                  >
                </label>
              </div>
            </div>
          </div>
          <div class="divider">
            <!-- <div class="divider-title">告警参数设置</div>
          <div class="line"></div> -->
          </div>
          <div class="emergency">
            <div class="threshold-box">
              <div class="threshold-item">
                <div
                  class="threshold-item-content"
                  style="width: auto; margin-left: 0px"
                >
                  <label style="width: auto"
                    >{{ $t("comm_Initial_TTL") }}：</label
                  >
                  <div class="threshold-item-input">
                    <Input
                      v-model="formData.startTTL"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                </div>
              </div>
              <div class="threshold-item" style="margin-left: 20px">
                <div
                  class="threshold-item-content"
                  style="width: auto; margin-left: 0px"
                >
                  <label style="width: auto">{{ $t("comm_TTL") }}</label>
                  <div class="threshold-item-input">
                    <Input
                      v-model="formData.limitHc"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Form>
  </section>
</template>

<script>
import { mapGetters, mapState, mapMutations } from "vuex";
import global from "@/common/global.js";
import validate from "@/common/validate";
export default {
  name: "taskItem",
  props: {
    modalStatus: Boolean,
    eventType: {
      type: [String, Number],
      default: 1, //1、新建，2、修改
    },
    taskData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    eventMethod:{
      type:Function,
      default: function () {
        console.log("--测试数据")
      },
    },
    defaultData: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  components: {
    TreeSelect: () => import("@/common/treeSelect/treeSelect.vue"),
  },
  data() {
    const validIp = (rule, value, callback) => {
      if (!value) {
        callback();
      }
      let ipv4Regex = validate.getIpv4Regex(),
      // /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
        ipv6Regex = validate.getIpv6Regex();
        // /\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;
      if (ipv4Regex.test(value) || ipv6Regex.test(value)) {
        if(ipv4Regex.test(value)){
            var  ips = value.split(".");
            if(ips[3] == '0' || ips[3] == '00' || ips[3] == '000'){
              callback(new Error(this.$t("comm_please_enter_correct") + rule.name + "IP"));
              return
            }
        }
        callback();
      } else {
        callback(new Error(this.$t("comm_please_enter_correct") + rule.name + "IP"));
      }
      // callback();
    };
    const validIps = (rule, data, callback) => {
      if (!data) {
        callback();
      } else {
        let values = data.split(",");
        var vArray = new Array(values.length);
        for (var i in values) {
          let value = values[i];
          if (vArray.indexOf(value) > -1) {
            callback(new Error(this.$t("comm_duplicate")));
          }
          vArray.push(value);
          let ipv4Regex = validate.getIpv4Regex(),
          // /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            ipv6Regex = validate.getIpv6Regex();
            // /\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;
          if (ipv4Regex.test(value) || ipv6Regex.test(value)) {
          } else {
            callback(new Error(this.$t("interface_enter_address")));
          }
        }
        callback();
      }
    };
    const validSource = (rule, value, callback) => {
      let _self = this,
        sourceIpString = this.formData.sourceIp,
        ipv4Regex = validate.getIpv4Regex();
        // /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      if (_self.eventType === 1) {
        if (ipv4Regex.test(sourceIpString)) {
          _self.$http
            .wisdomPost("/probetask/isHasProbe", { sourceIp: sourceIpString })
            .then((res) => {
              if (res.code === 1) {
                callback();
              } else {
                callback(new Error(res.msg));
              }
            });
        } else {
          callback(new Error(this.$t("comm_please_enter_correct_ip")));
        }
      } else {
        callback();
      }
    };

    return {
      // 是否开启目标IP的编辑操作
      dynamicMacType:0,
      destIpArry:[],
      showParam:false,
      // 界面导航控制带*必填提示
      isMustTip: true,
      isTimeTip: true,
      // 切换按钮提示
      isToggleButtonTip:true,
      groupCreate: false,
      groupIds: "",
      treeValue: "",
      typeArry: [],
      deviceTypeList: [],
      //探针code值（新建的时候用到，修改不用）
      getherCode: "",
      targetShow: true,
      sipProShow: false,
      maintainList: [
        { label: this.$t("logback_first"), value: 1 },
        { label: this.$t("logback_second"), value: 2 },
        { label: this.$t("logback_tertiary"), value: 3 },
      ],
      probetaskTypeList: [
        {
          id: 4,
          name: "UDP Trace",
        },
        {
          id: 5,
          name: "ICMP Trace",
        },
        {
          id: 2,
          name: "ICMP Ping",
        },
        {
          id: 7,
          name: "TCP Trace",
        },
      ],
      sipTypeList: [
        {
          id: 0,
          name: this.$t("comm_routine"),
        },
        {
          id: 1,
          name: this.$t("probetask_SIP"),
        },
      ],
      // 拨测频率
      // dialFrequencyList:[
      //   {label:'10s',value:'10',id:1},
      //   {label:'30s',value:'30',id:2},
      //   {label:'1min',value:'60',id:3},
      //   {label:'5min',value:'300',id:4},
      //   {label:'10min',value:'600',id:5},
      //   {label:'30min',value:'1800',id:6},
      //   {label:'1h',value:'3600',id:7},
      // ],
      // 发包个数
      packNumList: [],
      // 包大小
      packSizeList: [],
      // TOS list
      tosList: [{
        value:0,label:"0"},
        {value:1,label:"1"},
        {value:2,label:"2"},
        {value:3,label:"3"},
        {value:4,label:"4"},
       { value:5,label:"5"},
       { value:6,label:"6"},
       { value:7,label:"7",
      }],
      groupList: [],
      orgBtn: "",
      orgList: [],
      orgTree: false,
      treeData: [],
      readonly: true,
      showSilde: true,
      getSessionList: {
        sourceIp: "",
        destIp: "",
        destIpName: "",
        configType: "",
        orgId: "",
        isSpecial: 0,
      },
      taskSessionList: {
        sourceIp: "",
        destIp: "",
        destIpName: "",
        configType: "",
        orgId: "",
        isSpecial: 0,
      },
      isSpecial: false,
      formData: {
        destIp: "",
        sipPro: "",
        delayThreshold: "",
        sens: "",
        // 运维等级
        maintainLevel:"",
        // mac 修改 动态IP
        dynamicIpModification:"",
        // NET 检测
        netState:0,
        // TOS 值
        // tosValue:'',
      },
      defaultDatas: {},
      destPort: "",
      tcpPort: "",
      rulesValidate: {
        maintainLevel: [
          {
            required: true,
            type: "number",
            message: this.$t("discover_select_maintain_level"),
            trigger: "change",
          },
        ],
        configType: [
          {
            required: true,
            type: "number",
            message: this.$t("comm_dial_type"),
            trigger: "change",
          },
        ],
        sourceIp: [
          {
            required: true,
            message: this.$t("comm_probe_ip_address"),
            trigger: "change",
          },
        ],
        respondIp: [{ validator: validIps, trigger: "blur" }],
        orgId: [
          {
            required: true,
            message: this.$t("comm_select_org2"),
          },
        ],
        sourcePort: [{ required: true, validator: this.validPort, trigger: "blur" }],
        destPort: [{ required: true, validator: this.validPort, trigger: "blur" }],
        destMac: [{ validator: this.validMac, trigger: "change" }],
        destIp: [
          {
            validator: validIp,
            trigger: "change",
            name: this.$t("probe_tasks_add_target_ip_tips"),
          },
        ],
        destIpEdit: [
          {
            required: false,
            trigger: "change",
            name: this.$t("probe_tasks_add_target_ip_tips"),
          },
        ],
        // validator: validate.validateString, 特殊字符验证
        destIpName: [
          {
            required: false,
            trigger: "blur",
            minLength: 1,
            maxLength: 50,
            name: this.$t("probe_tasks_add_target_name_tips"),
          },
        ],
      },
      selectShow: true,
      threshold: {
        show: false,
        loading: true,
        data: {},
      },
      probeIpList: [],
    };
  },
  computed: {
    ...mapGetters(["dialFrequencyList"]),
    ...mapState("m_help", ["tipStatus", "tipActive"]),
    macDisabled() {
      return !!this.formData.destIp;
    },
    destIpDisabled() {
      return !!this.formData.destMac;
    },
  },
  watch: {
    taskData: {
      handler(val) {
        console.log("taskData-------------", val);
        // ;
        if (sessionStorage.hasOwnProperty("taskTaskAddList")) {
          this.getSessionList = JSON.parse(sessionStorage.getItem("taskTaskAddList"));
        }
        // ;
        if (JSON.stringify(val) === "{}") {
          this.formData = {};
          this.defaultDatas = {};
          this.tcpPort = "";
          this.destPort = "";
        } else {
          if (val.isSpecial === 1) {
            this.isSpecial = true;
          } else {
            this.isSpecial = false;
          }
          if (val.configType === "" || val.configType === 4 || val.configType === 7) {
            this.selectShow = true;
          } else {
            this.selectShow = false;
          }
          this.formData = Object.assign({}, val);
          console.log("this.formData--------------- ", this.formData);
          this.tcpPort = val.tcpPort;
          this.destPort = val.destPort;
          if (this.eventType == 1) {
            // TOS 值
            this.formData.tosValue = 0;
            this.formData.sourceIp = this.getSessionList.sourceIp;
            this.formData.destIp = this.getSessionList.destIp;
            this.formData.respondIp = this.getSessionList.respondIp;
            this.formData.destIpName = this.getSessionList.destIpName;
            this.formData.configType = this.getSessionList.configType;
            this.formData.orgId = this.getSessionList.orgId;
            this.formData.isSpecial = this.getSessionList.isSpecial;
            this.getherCode = this.getSessionList.getherCode;
            this.taskSessionList = {
              sourceIp: this.getSessionList.sourceIp,
              destIp: this.getSessionList.destIp,
              respondIp: this.getSessionList.respondIp,
              destIpName: this.getSessionList.destIpName,
              configType: this.getSessionList.configType,
              orgId: this.getSessionList.orgId,
              isSpecial: this.getSessionList.isSpecial,
            };
            if (this.getSessionList.isSpecial == 1) {
              this.isSpecial = true;
            } else if (this.getSessionList.isSpecial == 0) {
              this.isSpecial = false;
            }
            if (
              this.getSessionList.configType == 4 ||
              this.getSessionList.configType == 7
            ) {
              this.selectShow = true;
              this.rulesValidate.sourcePort = [
                { validator: this.validPort, trigger: "blur" },
              ];
              if (this.getSessionList.configType == 4) {
                this.formData.destPort = this.destPort;
                this.rulesValidate.destPort = [
                  {
                    validator: (rule, value, callback) => {
                      if (value == "" || value == undefined || value == null) {
                        callback();
                      }
                      if (Number(value) > 60000 || Number(value) < 20) {
                        callback(new Error(this.$t("warning_range_20_60000")));
                      } else {
                        callback();
                      }
                    },
                    trigger: "blur",
                  },
                ];
              } else if (this.getSessionList.configType == 7) {
                this.formData.destPort = this.tcpPort;
                this.rulesValidate.destPort = [
                  {
                    validator: (rule, value, callback) => {
                      if (value == "" || value == undefined || value == null) {
                        callback();
                      }
                      if (Number(value) > 60000 || Number(value) < 20) {
                        callback(new Error(this.$t("warning_range_20_60000")));
                      } else {
                        callback();
                      }
                    },
                    trigger: "blur",
                  },
                ];
              }
            } else if (
              this.getSessionList.configType == 2 ||
              this.getSessionList.configType == 5
            ) {
              this.selectShow = false;
              this.rulesValidate.destPort = [];
              this.rulesValidate.sourcePort = [];
              this.formData.destPort = this.destPort;
            }
          } else if (this.eventType == 2) {          
            if (val.configType == 4 || val.configType == 7) {
              this.selectShow = true;
            } else {
              this.selectShow = false;
            }
          }
          this.defaultDatas = Object.assign({}, val);
        }
      },
      deep: true,
    },
    "formData.destMac": {
      handler(val) {
        if (this.eventType == 1 && val) {
          this.formData.destMac = val.replace("-", ":").toUpperCase();
        }
      },
      deep: true,
    },
    "formData.tosValue": {
      handler(val) {
        console.log("formData.tosValue----",  val)
      },
      deep: true,
    },
  },
  created() {
        // 简单模式
    this.showParam =false;
    this.getTreeOrg();
    // this.getGroupList();
    top.document.addEventListener("click", (e) => {
      var boxList = top.document.getElementsByClassName("probetaskSelectBox2");
      var box = boxList[boxList.length - 1];
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    this.getDeviceTypeList();
    // 新建时获取默认值
    if (this.eventType == 1) {
      this.getParams();
    }
  },
  methods: {
    ...mapMutations("m_help", ["changeTipActive", "changeTipStatus"]),

    getProp(){
      // 新建才验证，编辑不验证
      if( this.eventType == 1){
          return "destIp";
      }
      return "destIpEdit";
    },
    // 界面引导，点击跳过当前
    closeCurrent(num) {
      if (num == 4) {
        // 关闭*提示
        this.isMustTip = false;
      }else if(num == 6){
            // 切换按钮 提示
        this.isToggleButtonTip = false
            
      } else {
        // 关闭拨测提示
        this.isTimeTip = false;
      }
    },
    // 切换条件选项 
    conditionClick(){
        this.showParam = !this.showParam;
        // 执行事件
        this.eventMethod();
        
    },
    handleDeviceTypeCreate(value) {
      var itemArrays = this.deviceTypeList.filter((item) => {
        return item.lable == value;
      });

      if (itemArrays.length > 0) {
        return;
      }

      this.deviceTypeList.push({
        isNew: true,
        value: value,
        lable: value,
      });
    },

    handleGroupCreate(value) {
      var itemArrays = this.groupList.filter((item) => {
        return item.name == value;
      });

      if (itemArrays.length > 0) {
        return;
      }

      this.groupList.push({
        isNew: true,
        id: value,
        name: value,
      });
    },
    validTtl(value) {
      let num = /(^[1-9]\d*$)/;
      let val = Number(value);
      if (value) {
        if (!num.test(val) || val == 0) {
          this.$Message.warning({ content: this.$t("comm_integers"), background: true });
          return false;
        } else if (val > Number(this.formData.limitHc)) {
          this.$Message.warning({ content: this.$t("comm_starting"), background: true });
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    validTtl2(value) {
      let num = /(^[1-9]\d*$)/;
      let val = Number(value);
      if (value) {
        if (!num.test(val)) {
          this.$Message.warning({ content: this.$t("comm_integers"), background: true });
          return false;
        } else if (val > 30) {
          this.$Message.warning({ content: this.$t("comm_greater30"), background: true });
          return false;
        } else if (val < Number(this.formData.startTTL)) {
          this.$Message.warning({
            content: this.$t("comm_restricted"),
            background: true,
          });
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    getPackSizeLabel(code) {
      return code == -1 ? this.$t("probe_byte") : code;
    },
    dialChange(val) {
      if (val) {
        this.packNumList = [];
        this.packSizeList = [];
        console.log("change", val);
        let _that = this,
          currentData = this.dialFrequencyList.filter(
            (item) => item.value == val.value
          )[0];
        currentData.option.num.forEach((item) => {
          this.packNumList.push({
            label: String(item),
            value: String(item),
          });
        });
        currentData.option.size.forEach((item) => {
          this.packSizeList.push({
            label: _that.getPackSizeLabel(item),
            value: String(item),
          });
        });
      }
    },
    // 参数验证
    paramVerify() {
      var regx = /^\d+$/;
      var value = "";
      // 目标ip和目标mac至少有一条需要填写
      if (!this.formData.destIp && !this.formData.destMac) {
        this.$Message.error({ content: this.$t("probe_ip_mac"), background: true });
        return false;
      }
      // 如果mac被填写  必须要勾选动态ip
      if (this.formData.destMac && this.formData.dynamicIp == 0) {
        this.$Message.error({ content: this.$t("probe_mac_ip"), background: true });
        return false;
      }
      // 中断生成阈值
      var beThrValue = this.formData.beThrValue;
      if (beThrValue === "") {
        this.$Message.warning({
          content: this.$t("warning_interrupt_empty"),
          background: true,
        });
        return false;
      }
      if (!regx.test(beThrValue)) {
        this.$Message.warning({
          content: this.$t("warning_interrupt_1000"),
          background: true,
        });
        return false;
      }
      value = Number(beThrValue);
      if (value < 0 || value > 1000) {
        this.$Message.warning({
          content: this.$t("warning_interrupt_1000"),
          background: true,
        });
        return false;
      }

      //  时延劣化生成阈值

      var deThrValue = this.formData.deThrValue;
      if (deThrValue === "") {
        this.$Message.warning({
          content: this.$t("warning_degradation_empty"),
          background: true,
        });
        return false;
      }
      if (!regx.test(deThrValue)) {
        this.$Message.warning({
          content: this.$t("warning_degradation_1000"),
          background: true,
        });
        return false;
      }
      value = Number(deThrValue);
      if (value < 0 || value > 1000) {
        this.$Message.warning({
          content: this.$t("warning_degradation_1000"),
          background: true,
        });
        return false;
      }

      //  时延劣化生成阈值

      var lrDeThrValue = this.formData.lrDeThrValue;
      if (lrDeThrValue === "") {
        this.$Message.warning({
          content: this.$t("warning_empty_once"),
          background: true,
        });
        return false;
      }
      if (!regx.test(lrDeThrValue)) {
        this.$Message.warning({ content: this.$t("warning_loss_100"), background: true });
        return false;
      }
      value = Number(lrDeThrValue);
      if (value < 1 || value > 100) {
        this.$Message.warning({ content: this.$t("warning_loss_100"), background: true });
        return false;
      }

      // 连续：
      var lrDeContinueNum = this.formData.lrDeContinueNum;
      if (lrDeContinueNum === "") {
        this.$Message.warning({
          content: this.$t("warning_loss_empty"),
          background: true,
        });
        return false;
      }
      if (!regx.test(lrDeContinueNum)) {
        this.$Message.warning({
          content: this.$t("warning_loss_1000"),
          background: true,
        });
        return false;
      }
      value = Number(lrDeContinueNum);
      if (value < 0 || value > 1000) {
        this.$Message.warning({
          content: this.$t("warning_loss_1000"),
          background: true,
        });
        return false;
      }

      // 连续达到
      var lrDeReachPer = this.formData.lrDeReachPer;
      if (lrDeReachPer === "") {
        this.$Message.warning({
          content: this.$t("warning_loss_continuously_empty"),
          background: true,
        });
        return false;
      }
      if (!regx.test(lrDeReachPer)) {
        this.$Message.warning({
          content: this.$t("warning_loss_continuously_100"),
          background: true,
        });
        return false;
      }
      value = Number(lrDeReachPer);
      if (value < 1 || value > 100) {
        this.$Message.warning({
          content: this.$t("warning_loss_continuously_100"),
          background: true,
        });
        return false;
      }

      // 时延劣化灵敏度
      var sens = this.formData.sens;
      if (sens === "") {
        this.$Message.warning({
          content: this.$t("probe_delay_sensitivity"),
          background: true,
        });
        return false;
      }
      if (!regx.test(sens)) {
        this.$Message.warning({
          content: this.$t("probe_delay_sensitivity_1"),
          background: true,
        });
        return false;
      }
      value = Number(sens);
      if (value < 1 || value > 10) {
        this.$Message.warning({
          content: this.$t("probe_delay_sensitivity_1"),
          background: true,
        });
        return false;
      }

      // 时延劣化经验值
      var delayThreshold = this.formData.delayThreshold;
      if (delayThreshold === "") {
        this.$Message.warning({
          content: this.$t("probe_delay_experience"),
          background: true,
        });
        return false;
      }
      if (!regx.test(delayThreshold)) {
        this.$Message.warning({
          content: this.$t("probe_delay_experience_3000"),
          background: true,
        });
        return false;
      }
      value = Number(delayThreshold);
      if (value < 1 || value > 3000) {
        this.$Message.warning({
          content: this.$t("probe_delay_experience_3000"),
          background: true,
        });
        return false;
      }

      // 起始TTL
      var startTTL = this.formData.startTTL;
      if (!this.validTtl(startTTL)) {
        return false;
      }
      var limitHc = this.formData.limitHc;
      if (!this.validTtl2(limitHc)) {
        return false;
      }
      return true;
    },
    onClear() {
      this.formData.orgId = null;
      this.treeValue = "";
    },
    focusFn() {
      this.getTreeOrg();
    },
    async getDeviceTypeList() {
      this.deviceTypeList = [];
      await this.$http
        .wisdomPost("/deviceType/queryList")
        .then(({ code, data, msg }) => {
          if (code === 1) {
            var newdeviceTypeList = [];
            for (let index = 0; index < data.length; index++) {
              var newdeviceType = {
                value: String(data[index].id),
                lable: data[index].name,
              };
              newdeviceTypeList.push(newdeviceType);
            }
            this.deviceTypeList = newdeviceTypeList ?? [];
            console.log("原始数据", this.deviceTypeList);
          } else {
            this.deviceTypeList = [];
            this.$Message.warning(msg);
          }
        })
        .catch((err) => {
          this.deviceTypeList = [];
          throw new Error(err);
        });
    },
    validMac(rule, value, callback) {
      if (value) {
        value.toUpperCase();
      }
      if (value == "" || value == undefined || value == null) {
        callback();
      } else if (value.length > 48) {
        callback(new Error(this.$t("probe_mac_48")));
      } else {
        var regex = "([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2}";
        //var regex = "(([A-Fa-f0-9]{2}-){5}[A-Fa-f0-9]{2})|(([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2})"; // 含冒号
        var regexp = new RegExp(regex);
        if (!regexp.test(value)) {
          callback(new Error(this.$t("probe_enter_mac")));
        }
      }
      callback();
    },
    validPort(rule, value, callback) {
      // 如果是icmp 不校验端口
      if (this.formData.configType == 2 || this.formData.configType == 5) {
        callback();
        return;
      }
      if (value == "" || value == undefined || value == null) {
        callback(new Error(this.$t("message_enter_port")));
      } else {
        if (rule.field == "sourcePort") {
          if (!Number.isInteger(Number(value))) {
            callback(new Error(this.$t("probe_enter_positive")));
          }
          if (value && (Number(value) > 60000 || Number(value) < 20)) {
            callback(new Error(this.$t("probe_port_60000")));
          }
        } else if (rule.field == "destPort") {
          // 新增时，可以多端口 但是编辑时  不能多端口
          if (this.eventType == 1) {
            let ports = String(value).split(",");
            if (ports.length > 10) {
              callback(new Error(this.$t("probe_port_10")));
            }
            ports.forEach((data, index) => {
              if (!Number.isInteger(Number(data))) {
                callback(new Error(this.$t("probe_enter_positive")));
              }
              if (data && (Number(data) > 60000 || Number(data) < 20)) {
                callback(new Error(this.$t("probe_target_port_60000")));
              }
            });
          } else {
            if (!Number.isInteger(Number(value))) {
              callback(new Error(this.$t("probe_enter_positive")));
            }
            if (value && (Number(value) > 60000 || Number(value) < 20)) {
              callback(new Error(this.$t("probe_target_port_60000")));
            }
          }
        }
        callback();
      }
    },
    setSession(dom, val, e) {
      if (dom === "destIp") {
        this.$http.wisdomPost("/logdeg/getDevInfo", { ip: val }).then((res) => {
          if (res.code == 1) {
            if (res.data) {
              this.formData.destIpName = res.data.name;
            } else {
              this.formData.destIpName = "";
            }
          }
        });
      }
      //设置sessionStorage
      if (this.modalStatus) {
        this.taskSessionList[dom] = val;
        this.taskSessionList.getherCode = this.getherCode;
        sessionStorage.setItem("taskTaskAddList", JSON.stringify(this.taskSessionList));
        console.log("taskSessionList", this.taskSessionList);
      } else {
        return;
      }
    },
    //选择探针ip
    selectSourceIp(val) {
      debugger
      // 赋值探针ip
      console.log(val);
      this.formData.sourceIp = val.tag;
    },
    // 选择目标类型
    selectType(val) {
      let _interruptState = "0";
      if (this.typeArry.includes(val.value)) {
        _interruptState = "0";
      } else {
        _interruptState = "1";
      }
      this.$set(this.formData, "interruptState", _interruptState);
    },
    // 查询mac和ip
    getMacToDestIp() {
       this.destIpArry = [];
      this.$http
        .wisdomPost("/probetask/findDestIpToMac", {
          mac:this.formData.destMac,
          getherCode:this.formData.getherCode,
          id:this.formData.id || ''
        })
        .then((res) => {
          if (res.code == 1) {
              this.destIpArry = res.data;
          } else {
              this.destIpArry = [];
          }
        });
    },
    getType() {
      this.$http
        .wisdomPost("/defvalue/getCodeDataTableConfigByKey", {
          key: "INTERRUPT_DEVICE_TYPE",
        })
        .then((res) => {
          if (res.code == 1) {
            if (res.data && res.data.value) {
              this.typeArry = res.data.value.split(",");
            } else {
              this.typeArry = [];
            }
          }
          this.chooseInterruptState();
        });
    },
    chooseInterruptState() {
      // 新建才会自动加载
      if (this.eventType == 1) {
        // 这里重新赋值一次
        if (this.formData.destType) {
          this.selectType({ value: this.formData.destType });
        }
      }
    },
    //根据destIP加载目标名称
    destIpChange() {
      this.$http
        .wisdomPost("/link/getByZIp", { specialZIp: this.formData.destIp })
        .then((res) => {
          if (res.code == 1) {
            if (res.data.length > 0) {
              this.formData.orgId = res.data[0].orgId;
              this.taskSessionList["orgId"] = res.data[0].orgId;
              sessionStorage.setItem(
                "taskTaskAddList",
                JSON.stringify(this.taskSessionList)
              );
            }
          }
        });
    },
    // mac 匹配地址
    destIpMacChange(val){
        // 
        console.log("=================" , val);
    },

    selectConfigType(val) {
      // 拨测类型为icmp时，不展示探针端口和目标端口
      if (val.value == 5 || val.value == 2) {
        this.targetShow = false;
      } else {
        this.targetShow = true;
      }
      // 如果拨测类型选择的是udp ,下一步操作默认为常规
      if (val.value == 4) {
        this.sipProShow = true;
        this.formData.sipPro = 0;
      } else {
        this.sipProShow = false;
      }
      // 类型等于7的时候  赋值目标端口
      if (val.value == 7) {
        this.formData.destPort = this.defaultData.dialTestTcpPort;
      }
    },
    //下拉框处理事件
    selectChange(event, param, val) {
      let _self = this;
      if (event === false) {
        if (param.label === this.$t("task_dial_type") && (val === 4 || val === 7)) {
          _self.selectShow = true;
          this.rulesValidate.sourcePort = [
            { validator: this.validPort, trigger: "blur" },
          ];
          if (this.formData["configType"] == "7") {
            _self.formData.destPort = this.tcpPort;
            this.rulesValidate.destPort = [
              {
                validator: (rule, value, callback) => {
                  if (value == "" || value == undefined || value == null) {
                    callback();
                  }
                  if (Number(value) > 60000 || Number(value) < 20) {
                    callback(new Error(this.$t("warning_range_20_60000")));
                  } else {
                    callback();
                  }
                },
                trigger: "blur",
              },
            ];
          } else {
            _self.formData.destPort = this.destPort;
            this.rulesValidate.destPort = [
              {
                validator: (rule, value, callback) => {
                  if (value == "" || value == undefined || value == null) {
                    callback();
                  }
                  if (Number(value) > 60000 || Number(value) < 20) {
                    callback(new Error(this.$t("warning_range_20_60000")));
                  } else {
                    callback();
                  }
                },
                trigger: "blur",
              },
            ];
          }
        } else if (
          param.label === this.$t("task_dial_type") &&
          (val === 2 || val === 5)
        ) {
          _self.selectShow = false;
          this.rulesValidate.destPort = [];
          this.rulesValidate.sourcePort = [];
        }
      }
    },
    // //  if (data[i].key == "UDP_TARGET_PORT") {//UDP目标端口
    //   defaultValue.dialTestUdpPort = data[i].value;
    //   }
    //   if (data[i].key == "TCP_TARGET_PORT") {//TCP目标端口
    //     defaultValue.dialTestTcpPort = data[i].value;
    //   }
    getParams() {
      if (this.formData.limitHc == "" || this.formData.limitHc == undefined) {
        this.formData.limitHc = this.defaultData.dialTestTrace;
      }
      // if (this.formData.destPort == "" || this.formData.destPort == undefined) {
      //   this.formData.destPort = this.defaultData.dialTestTcpPort;
      // }
      if (this.formData.startTTL == "" || this.formData.startTTL == undefined) {
        this.formData.startTTL = this.defaultData.dialTestStratTTL;
      }
      if (this.formData.dialInterval == "" || this.formData.dialInterval == undefined) {
        this.formData.dialInterval = parseInt(this.defaultData.dialTestFrequency);
      }
      if (this.formData.packetNum == "" || this.formData.packetNum == undefined) {
        this.formData.packetNum = parseInt(this.defaultData.dialTestNum);
      }
      if (this.formData.packetSize == "" || this.formData.packetSize == undefined) {
        this.formData.packetSize = parseInt(this.defaultData.dialTestSize);
      }
      if (this.formData.beThrValue == "" || this.formData.beThrValue == undefined) {
        this.formData.beThrValue = this.defaultData.dialTestBe_thr_value;
      }
      if (this.formData.deThrValue == "" || this.formData.deThrValue == undefined) {
        this.formData.deThrValue = this.defaultData.dialTestDe_thr_value;
      }
      if (this.formData.lrDeThrValue == "" || this.formData.lrDeThrValue == undefined) {
        this.formData.lrDeThrValue = this.defaultData.packetLossDegradation;
        this.formData.lrDeContinueNum = this.defaultData.packetLossDegradation2;
        this.formData.lrDeReachPer = this.defaultData.packetLossDegradation3;
      }
      this.formData.isSpecial = 0;
      if (
        this.formData.interruptState == "" ||
        this.formData.interruptState == undefined
      ) {
        this.formData.interruptState = "0";
      }

      // 时延劣化灵敏度
      if (this.formData.sens == "" || this.formData.sens == undefined) {
        this.formData.sens = this.defaultData.sens;
      }

      // 时延劣化经验值
      if (
        this.formData.delayThreshold == "" ||
        this.formData.delayThreshold == undefined
      ) {
        this.formData.delayThreshold = this.defaultData.delayThreshold;
      }

      // tos
      if (this.formData.tosValue == "" || this.formData.tosValue == undefined) {
        this.formData.tosValue = 0;
      }

      // 添加分组
      console.log(this.formData, "------------------");
      if (this.groupIds && this.groupIds.length > 0) {
        // 新建分組集合
        var newGroupItemArrays = [];
        // 选择的已存在的分组
        var existsGroupItemArrays = [];

        this.groupList.forEach((item) => {
          if (this.groupIds.indexOf(item.id) > -1) {
            if (item.isNew) {
              newGroupItemArrays.push(item);
            } else {
              existsGroupItemArrays.push(item);
            }
          }
        });
        // 是否有新增的数据
        if (newGroupItemArrays.length > 0) {
          // 设置具体的新增的分组名称
          this.formData.groupNames = newGroupItemArrays.map((item) => {
            return item.name;
          });
        } else {
          this.formData.groupNames = "";
        }

        // 已经存在的分组
        if (existsGroupItemArrays.length > 0) {
          this.formData.groupIds = existsGroupItemArrays.map((item) => {
            return item.id;
          });
        } else {
          this.formData.groupIds = "";
        }
      } else {
        // 不选分组的时候，需要清空 两个值
        this.formData.groupNames = "";
        this.formData.groupIds = "";
      }

      // 添加目标类型
      if (this.formData.destType) {
        // 是否有新增的数据
        var destTypeItemArrays = this.deviceTypeList.filter((item) => {
          return this.formData.destType == item.value && item.isNew;
        });

        //
        if (destTypeItemArrays.length > 0) {
          this.formData.addDestTypeName = destTypeItemArrays[0].lable;
          // this.formData.destType = "";
        } else {
          this.formData.addDestTypeName = "";
        }
      } else {
        this.formData.addDestTypeName = "";
      }

      // 如果输入的 addDestTypeName ，就自动将 destType 置为 ""
      var dataFormJson = Object.assign({}, this.formData);
      if (dataFormJson.addDestTypeName && dataFormJson.addDestTypeName.length > 0) {
        dataFormJson.destType = "";
      }

      // 判断是否修改了动态IP的值
      return dataFormJson;
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            return item;
          });
          _self.treeData = treeNodeList;
          // 新建设置默认值
          if (this.eventType == 1) {
            if(treeNodeList && treeNodeList.length > 0){
                  this.setOrg([treeNodeList[0]])
            }
          }
        }
      });
    },
    loadOrgTreeData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    choicesOrg() {
      this.orgTree = true;
      let allDropdownList = top.document.getElementsByClassName("ivu-select-dropdown");
      if (allDropdownList && allDropdownList.length > 0) {
        for (let index = 0; index < allDropdownList.length; index++) {
          allDropdownList[index].style.display = "none";
        }
      }
    },
    async setOrg(item) {
      console.log(item);
      this.treeValue = item[0].name;
      this.formData.orgId = item[0] ? item[0].id : null;
      this.orgList = item;
      this.orgTree = false;
      this.probeIpList = [];
      this.formData.groupIds = [];
      this.groupIds = "";
      this.groupCreate = true;
      this.$refs.serviceStatusClear.clearSingleSelect();
      await this.getGetherList(this.formData.orgId, item[0]);

      // 新建设置默认值
      if (this.eventType == 1) {
        this.formData.getherCode = this.probeIpList.length > 0 ? this.probeIpList[0].label : ""
        this.formData.sourceIp = this.probeIpList.length > 0 ? this.probeIpList[0].value : ""
      }

      if (item[0] != null && item[0].id != null && item[0].id != "") {
        this.getGroupList(item[0].id);
      }
    },
    async getGetherList(orgId, orgItem) {
     await this.$http
        .post("/sys/gether/getList", {
          type: 1,
          orgId: orgId,
        })
        .then(({ code, data, msg }) => {
          if (code === 1 && typeof data === "object") {
            this.probeIpList = data
              .filter((item) => {
                return item && typeof item.ip === "string";
              })
              .map(({ ip, code, labelName }) => {
                return { value: ip, label: code, labelName: labelName };
              });
          } else {
            this.probeIpList = [];
          }
        });
    },
    getGroupList(orgId) {
      const param = {
        orgId: orgId,
      };
      this.groupCreate = true;
      this.$http.wisdomPost("/group/groupList", param).then((res) => {
        if (res.code === 1) {
          if (res.data) {
            this.groupList = res.data;
          }
        } else {
          this.groupList = [];
        }
      });
    },
  },
  mounted() {
    this.getType();
    this.dynamicMacType = 0;
    if (this.eventType == 1) {
      this.orgBtn = true;
    } else {
      this.orgBtn = false;
    }
    if (sessionStorage.hasOwnProperty("taskTaskAddList")) {
      this.getSessionList = JSON.parse(sessionStorage.getItem("taskTaskAddList"));
    }
    if (JSON.stringify(this.taskData) === "{}") {
      this.formData = {};
      this.defaultDatas = {};
      this.tcpPort = "";
      this.destPort = "";
    } else {
      if (this.taskData.isSpecial === 1) {
        this.isSpecial = true;
      } else {
        this.isSpecial = false;
      }
      this.packNumList = [];
      this.packSizeList = [];

      // 设置分组信息
      if (this.taskData && this.taskData.groupIds) {
        if (this.taskData.groupIds.length > 0) {
          this.groupIds = this.taskData.groupIds;
        } else {
          this.groupIds = [];
        }
      }

      let _that = this,
        currentData = this.dialFrequencyList.filter(
          (item) => item.value == this.taskData.dialInterval
        )[0];

      currentData.option.num.forEach((item) => {
        this.packNumList.push({
          label: String(item),
          value: String(item),
        });
      });
      currentData.option.size.forEach((item) => {
        this.packSizeList.push({
          label: _that.getPackSizeLabel(item),
          value: String(item),
        });
      });
      console.log("this.taskData", this.taskData);
      this.treeValue = this.taskData.orgName;
      this.formData = Object.assign({}, this.taskData);
      if (this.eventType == 1) {
        this.formData.tosValue = 0;
          this.formData.maintainLevel = 1;
        this.tcpPort = this.taskData.tcpPort;
        this.destPort = this.taskData.destPort;
        this.formData.sourceIp = this.getSessionList.sourceIp;
        this.formData.destIp = this.getSessionList.destIp;
        this.formData.respondIp = this.getSessionList.respondIp;
        this.formData.destIpName = this.getSessionList.destIpName;
        this.formData.configType = this.getSessionList.configType;
        this.formData.orgId = this.getSessionList.orgId;
        this.formData.isSpecial = this.getSessionList.isSpecial;
        this.getherCode = this.getSessionList.getherCode;
        this.taskSessionList = {
          sourceIp: this.getSessionList.sourceIp,
          destIp: this.getSessionList.destIp,
          respondIp: this.getSessionList.respondIp,
          destIpName: this.getSessionList.destIpName,
          configType: this.getSessionList.configType,
          orgId: this.getSessionList.orgId,
          isSpecial: this.getSessionList.isSpecial,
        };
        if (this.getSessionList.isSpecial == 1) {
          this.isSpecial = true;
        } else if (this.getSessionList.isSpecial == 0) {
          this.isSpecial = false;
        }
        if (this.getSessionList.configType == 4 || this.getSessionList.configType == 7) {
          this.selectShow = true;
          this.rulesValidate.sourcePort = [
            { validator: this.validPort, trigger: "blur" },
          ];
          if (this.getSessionList.configType == 4) {
            this.formData.destPort = this.destPort;
            this.rulesValidate.destPort = [
              {
                validator: (rule, value, callback) => {
                  if (value == "" || value == undefined || value == null) {
                    callback();
                  }
                  if (Number(value) > 60000 || Number(value) < 20) {
                    callback(new Error(this.$t("warning_range_20_60000")));
                  } else {
                    callback();
                  }
                },
                trigger: "blur",
              },
            ];
          } else if (this.getSessionList.configType == 7) {
            this.formData.destPort = this.tcpPort;
            this.rulesValidate.destPort = [
              {
                validator: (rule, value, callback) => {
                  if (value == "" || value == undefined || value == null) {
                    callback();
                  }
                  if (Number(value) > 60000 || Number(value) < 20) {
                    callback(new Error(this.$t("warning_range_20_60000")));
                  } else {
                    callback();
                  }
                },
                trigger: "blur",
              },
            ];
          }
        } else if (
          this.getSessionList.configType == 2 ||
          this.getSessionList.configType == 5
        ) {
          this.selectShow = false;
          this.formData.destPort = this.destPort;
          this.rulesValidate.destPort = [];
          this.rulesValidate.sourcePort = [];
        }
      } else if (this.eventType == 2) {
         
        this.orgList = [{ name: this.formData.orgName, id: this.formData.orgId }];
        this.getGetherList(this.formData.orgId);
        this.getGroupList(this.formData.orgId);
        // 拨测类型为icmp时，不展示探针端口和目标端口
        if (this.taskData.configType == 5 || this.taskData.configType == 2) {
          this.targetShow = false;
          this.sipProShow = false;
        } else {
          this.targetShow = true;
          this.selectShow = true;
          this.sipProShow = false;
          // 如果 为 UDP Trace 则显示sipPro
          if (this.taskData.configType == 4) {
            this.sipProShow = true;
          }
        }
        if(this.formData.dynamicIp == 1 && this.formData.destMac){
          this.dynamicMacType = 1;
          this.getMacToDestIp();
        }else{
           this.dynamicMacType = 0;
        }
        
      }
      this.defaultDatas = Object.assign({}, this.taskData);
    }
  },
};
</script>
<style scoped lang="less">
.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}

.task-modal .ivu-modal-body .ivu-form-item .ivu-form-item-label {
  width: inherit !important;
}

.task-modal .ttlBox {
  display: flex;
  align-items: center;
}
</style>
