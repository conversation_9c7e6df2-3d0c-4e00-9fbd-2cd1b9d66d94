<template>
  <div :class="{ 'light-no-tabs': currentSkin == 0 }">
    <section class="sectionBox">
      <!-- 拨测任务管理页面 -->
      <div class="section-top">
        <div class="section-special">
          <!-- 第一排 -->
          <Row justify="space-between" class="fn_box" style="width: 100%">
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label">{{ $t("comm_org") }}：</label>
                <div class="fn_item_box">
                  <TreeSelect
                    v-model="treeValue"
                    ref="TreeSelect"
                    :data="treeData"
                    :placeholder="$t('snmp_pl_man')"
                    :loadData="loadData"
                    @onSelectChange="setOrg"
                    @onClear="onClear"
                    @onFocus="focusFn"
                  ></TreeSelect>
                </div>
              </div>
            </Col>
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label">{{ $t("task_status") }}：</label>
                <div class="fn_item_box">
                  <Select
                    clearable
                    filterable
                    :only-filter-with-text="true"
                    v-model="query.startState"
                  >
                    <Option
                      v-for="item in stateList"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.label }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label">{{ $t("task_type") }}：</label>
                <div class="fn_item_box">
                  <Select
                    clearable
                    filterable
                    :only-filter-with-text="true"
                    v-model="query.maintainLevel"
                  >
                    <Option
                      v-for="item in maintainList"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.label }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label">{{ $t("comm_group") }}：</label>
                <div class="fn_item_box">
                  <Select
                    v-model="query.groupIds"
                    multiple
                    :max-tag-count="1"
                    clearable
                    filterable
                    :only-filter-with-text="true"
                  >
                    <Option
                      v-for="item in groupList"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
          </Row>
          <!-- /第一排 -->
          <!-- 第二排 -->
          <Row justify="space-between" class="fn_box">
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label"
                  >{{ $t("peertopeer_monitoring") }}：</label
                >
                <div class="fn_item_box">
                  <Select
                    clearable
                    filterable
                    :only-filter-with-text="true"
                    v-model="query.runState"
                  >
                    <Option
                      v-for="item in runStateList"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.label }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label"
                  >{{ $t("task_dial_type") }}：</label
                >
                <div class="fn_item_box">
                  <Select
                    ref="typeConfig"
                    clearable
                    :disabled="typeConfigDisable"
                    filterable
                    :only-filter-with-text="true"
                    @on-change="dialTestTypeChange"
                    v-model="query.configType"
                    :placeholder="$t('comm_task_first')"
                  >
                    <Option
                      v-for="item in dialTestTypeList"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.label }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label"
                  >{{ $t("probe_frequency") }}：</label
                >
                <div class="fn_item_box">
                  <Select
                    clearable
                    filterable
                    :only-filter-with-text="true"
                    v-model="query.dialFrequency"
                  >
                    <Option
                      v-for="item in dialFrequencyList"
                      :value="item.value"
                      :key="item.value"
                      :id="item.id"
                      >{{ item.label }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label">{{ $t("probe_dynamic") }}：</label>
                <div class="fn_item_box">
                  <Select
                    clearable
                    filterable
                    :only-filter-with-text="true"
                    v-model="query.dynamicIp"
                  >
                    <Option
                      v-for="item in dynamicIp"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.label }}</Option
                    >
                  </Select>
                </div>
              </div>
            </Col>
          </Row>
          <!-- /第二排 -->

          <!-- 第三排 -->
          <Row class="fn_box" style="width: 100">
            <Col span="6">
              <div class="fn_item">
                <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
                <div class="fn_item_box">
                  <Input
                    v-model="query.source"
                    :placeholder="placeholderStr"
                    :title="$t('probe_1')"
                  />
                </div>
              </div>
            </Col>
          </Row>
        </div>

        <div class="tool-btn">
          <div>
            <Button
              class="query-btn"
              type="primary"
              v-if="permissionObj.list"
              icon="ios-search"
              @click="queryClick"
              :disabled="tipStatus == 'true'"
              :title="$t('common_query')"
            ></Button>
            <div class="add-tip">
              <!-- 界面导航 -->
              <Button
                class="query-btn"
                type="primary"
                v-if="permissionObj.add"
                icon="md-add"
                :disabled="this.tipStatus == 'true' && tipActive != 3"
                @click="addClick"
                :loading="addBtnLoading"
                :title="$t('common_new')"
              ></Button>
              <div
                v-if="tipStatus == 'true' && tipActive == 3"
                class="nav-tip task-add"
                style="flexdirection: column"
              >
                <div class="tip-text">{{ $t("nav_step3_tip") }}</div>
                <div class="tip-btn">
                  <Button class="all-btn" @click="closeTip">{{
                    $t("nav_all_btn")
                  }}</Button>
                </div>
              </div>
            </div>

            <Dropdown @on-click="moreBtnClick">
              <Button class="more-btn" :disabled="tipStatus == 'true'">
                {{ $t("dashboard_more") }}
                <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list" width="200px">
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="importClick"
                  v-if="permissionObj.import"
                  >{{ $t("access_exe_mode_import") }}</DropdownItem
                >
                <Dropdown
                  placement="right-start"
                  v-if="permissionObj.export"
                  @on-click="moreBtnClick"
                >
                  <DropdownItem :disabled="tipStatus == 'true'">
                    {{ $t("but_data_export") }}
                    <Icon type="ios-arrow-forward"></Icon>
                  </DropdownItem>
                  <DropdownMenu slot="list">
                    <DropdownItem
                      :disabled="tipStatus == 'true'"
                      name="exportClick"
                      >{{ $t("but_export_checked") }}</DropdownItem
                    >
                    <DropdownItem
                      :disabled="tipStatus == 'true'"
                      name="exportAllClick"
                      >{{ $t("but_export_all") }}</DropdownItem
                    >
                  </DropdownMenu>
                </Dropdown>
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="taskAdjust"
                  v-if="permissionObj.job"
                  >{{ $t("probe_adjustment") }}</DropdownItem
                >
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="alarmSetClick"
                  v-if="permissionObj.alarmconfig"
                  >{{ $t("but_alarm_setting") }}</DropdownItem
                >
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="excelTaskNodeExportClick"
                  v-if="permissionObj.exportTaskNode"
                  >{{ $t("probe_derived") }}</DropdownItem
                >
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="suspendClick"
                  v-if="permissionObj.status"
                  >{{ $t("but_pause") }}</DropdownItem
                >
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="startClick"
                  v-if="permissionObj.status"
                  >{{ $t("but_enable") }}</DropdownItem
                >
                <DropdownItem
                  :disabled="tipStatus == 'true'"
                  name="deleteClick"
                  v-if="permissionObj.delete"
                  >{{ $t("but_remove") }}</DropdownItem
                >
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
      <div class="section-body fault-tab contentBox_bg">
        <div class="section-body-content">
          <!-- <div class="tableTitle">任务列表</div> -->
          <div
            class="dialTest-tab-content"
            style="padding: 0; posizition: reactive"
          >
            <!-- 新增完后到界面导航 -->
            <!-- 界面导航 -->
            <div
              v-if="tipStatus == 'true' && tipActive == 7"
              class="nav-tip-bottom probtask-finish-tip"
            >
              <div class="tip-text">{{ $t("nav_step7_tip") }}</div>
              <div class="tip-btn">
                <Button class="all-btn" @click="closeTip">{{
                  $t("nav_all_btn")
                }}</Button>
                <Button class="tip-ok" @click="closeOne">{{
                  $t("nav_current_btn")
                }}</Button>
              </div>
            </div>
            <Loading :loading="loading"></Loading>
            <!-- <tree-table :columns="columns" :data="tabList" ref="treeTable" stripe @sortParent="sortSum" @boxClick="boxClickData">
          </tree-table> -->
            <Table
              ref="tableList"
              class="fixed-left-right"
              stripe
              :columns="columns"
              :data="tabList"
              :no-data-text="
                loading
                  ? ''
                  : tabList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              @on-select="handleSelect"
              @on-sort-change="sortSum"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
            >
              <template slot-scope="{ row }" slot="startState">
                <span v-if="row.isPaisheng == 2" style="color: #fc432a">{{
                  $t("rpmanager_deleting")
                }}</span>
                <span v-else-if="row.startState == 0" style="color: #c0c4cc">{{
                  $t("snmptask_suspended")
                }}</span>
                <span v-else-if="row.startState == 1" style="color: #07c5a3">{{
                  $t("but_enable")
                }}</span>
                <span v-else-if="row.startState == 2">{{
                  $t("probetask_ended")
                }}</span>
                <span v-else-if="row.startState == 3" style="color: #c0c4cc">{{
                  $t("probe_unmatch")
                }}</span>
                <span v-else>--</span>
                <Tooltip
                  max-width="2000"
                  :content="row.errorInfo"
                  placement="right"
                >
                  <Icon
                    type="ios-information-circle"
                    v-if="row.errorInfo && row.isPaisheng != 2"
                    style="color: red"
                  />
                </Tooltip>
              </template>
            </Table>
          </div>
          <div class="tab-page" v-if="tabList.length > 0">
            <Page
              v-page
              :current="pageNo"
              :page-size="pageSize"
              :page-size-opts="pageSizeOpts"
              :total="totalCount"
              show-sizer
              show-elevator
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
            >
            </Page>
          </div>
        </div>
      </div>

      <!--新增-->
      <Modal
        sticky
        v-model="taskModal.show"
        width="980"
        :style="{ top: '140px' }"
        class="task-modal threshold-modal add-task-modal operation-modal"
        :title="
          (btnType ? this.$t('common_new') : this.$t('common_update')) +
          this.$t('comm_task')
        "
        draggable
        :mask="true"
        @on-ok="addTaskOk('taskForm')"
        @on-visible-change="modalStatusChange"
        @on-cancel="addTaskCancel('taskForm')"
      >
        <section v-if="taskModal.show" class="taskStrategy-modal">
          <!-- 普通拨测 任务界面  -->
          <task-item
            ref="taskForm"
            :taskData="taskModal.taskParam"
            :defaultData="defaultConfigList"
            :modalStatus="modalStatus"
            :eventType="taskModal.type"
            :groupList="groupList"
            :event-method="taskItemEventChange"
          >
          </task-item>

          <!-- 作业周期调整 -->
          <div v-show="jobCycleParam">
            <div class="divider" style="margin-top: 20px">
              <div class="divider-title" style="min-width: 100px">
                {{ $t("probe_operation_plan_setting") }}
              </div>
              <div class="line"></div>
            </div>
            <Form
              ref="taskAdjustForm"
              :model="taskAdjustForm.param"
              :rules="taskAdjustValidate"
              @submit.native.prevent
              class="myForm labelHeight"
              :label-width="140"
            >
              <FormItem
                :label="$t('comm_start_time') + $t('comm_colon')"
                prop="start_date"
                class="labelContent"
                style="width: 100%"
              >
                <DatePicker
                  type="date"
                  v-model="taskAdjustForm.param.start_date"
                  :placeholder="$t('comm_select_date2')"
                  format="yyyy-MM-dd"
                  :options="startOption"
                ></DatePicker>
                <span style="padding: 0 10px">{{ $t("comm_to") }}</span>
                <DatePicker
                  type="date"
                  v-model="taskAdjustForm.param.end_date"
                  :placeholder="$t('comm_select_date2')"
                  format="yyyy-MM-dd"
                  :options="endOption"
                ></DatePicker>
              </FormItem>
              <FormItem
                :label="$t('comm_time_range') + $t('comm_colon')"
                class="labelContent"
                style="width: 100%"
              >
                <div
                  v-for="(item, index) in taskAdjustForm.param.timeList"
                  :key="index"
                  style="margin-bottom: 10px"
                  class="timeSlot"
                >
                  <TimePicker
                    type="time"
                    v-model="item.start"
                    :placeholder="$t('comm_select_date')"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playStartTimeChange2($event, index)"
                  ></TimePicker>
                  <span style="padding: 0 10px">{{ $t("comm_to") }}</span>
                  <TimePicker
                    type="time"
                    v-model="item.end"
                    :placeholder="$t('comm_select_date')"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playEndTimeChange2($event, index)"
                  ></TimePicker>
                  <Icon
                    :type="
                      taskAdjustForm.param.timeList.length > 1
                        ? 'ios-remove-circle-outline'
                        : ''
                    "
                    class="iconItem"
                    :class="
                      taskAdjustForm.param.timeList.length > 1
                        ? 'removeItem'
                        : ''
                    "
                    @click="speedTimeChangeRemove(index)"
                  />
                  <Icon
                    :type="
                      index === taskAdjustForm.param.timeList.length - 1
                        ? 'ios-add-circle-outline'
                        : ''
                    "
                    class="iconItem"
                    :class="
                      index === taskAdjustForm.param.timeList.length - 1
                        ? 'addItem'
                        : ''
                    "
                    @click="speedTimeChangeAdd(index)"
                  />
                </div>
              </FormItem>
              <FormItem
                :label="$t('comm_repeat_cycle') + $t('comm_colon')"
                class="labelContent"
                v-show="taskAdjustForm.weekShow"
              >
                <CheckboxGroup v-model="taskAdjustForm.param.week">
                  <Checkbox :label="1">{{ $t("comm_monday") }}</Checkbox>
                  <Checkbox :label="2">{{ $t("comm_tuesday") }}</Checkbox>
                  <Checkbox :label="3">{{ $t("comm_wednesday") }}</Checkbox>
                  <Checkbox :label="4">{{ $t("comm_thursday") }}</Checkbox>
                  <Checkbox :label="5">{{ $t("comm_friday") }}</Checkbox>
                  <Checkbox :label="6">{{ $t("comm_saturday") }}</Checkbox>
                  <Checkbox :label="7">{{ $t("comm_sunday") }}</Checkbox>
                </CheckboxGroup>
              </FormItem>
            </Form>
          </div>
        </section>
        <!-- 界面导航 -->

        <div slot="footer">
          <div v-if="tipStatus == 'true'" class="nav-tip-bottom">
            <div class="tip-text">{{ $t("nav_step6_tip") }}</div>
          </div>

          <Button class="cancel-btn" @click="addTaskCancel('taskForm')">{{
            $t("common_cancel")
          }}</Button>
          <Button :loading="btnLoading" @click="addTaskOk('taskForm')">{{
            $t("common_verify")
          }}</Button>
        </div>
      </Modal>

      <!--详情-->
      <DetailModal
        ref="detailModal"
        :detailShow="detailShow"
        :rowData="rowData"
        :indexData="indexModal.data"
        :isSpecial="echartIsSpecial"
        @closeModal="detailShow = false"
      >
      </DetailModal>

      <!--告警阈值-->
      <Modal
        sticky
        v-model="threshold.show"
        width="582"
        class="threshold-modal"
        :title="$t('probetask_alarm_threshold')"
        draggable
        :mask="true"
        :footer-hide="true"
        @on-cancel="thresholdCancel('thresholdForm')"
      >
        <threshold-item
          ref="thresholdForm"
          disabled
          :data="threshold.data"
        ></threshold-item>
      </Modal>
      <!--批量导入-->
      <div v-if="showImport" style="height: 1000%; width: 1000px">
        <import-area
          @closeImport="closeImport"
          :importUrl="importUrl"
          :licenceNum="licenceNum"
          :anpmVersion="anpmVersion"
          :defaultData="defaultConfigList"
        ></import-area>
      </div>
      <!--告警设置-->
      <Modal
        sticky
        v-model="alarm.modal.show"
        width="680"
        class="threshold-modal"
        :title="$t('probetask_alarm_setting')"
        draggable
        :mask="true"
        :loading="alarm.loading"
      >
        <div>
          <!-- <label class="itemTitle">任务类型：</label> -->
          <!-- <RadioGroup v-model="alarm.param.type">
          <Radio :label="1">普通拨测</Radio>
          <Radio :label="2" v-if="anpmVersion == '1'">高频监控</Radio>
        </RadioGroup> -->
        </div>
        <div class="threshold-content">
          <label class="itemTitle"
            >{{ $t("snmptask_alarm_setting") }}{{ $t("comm_colon") }}</label
          >
          <div v-show="alarm.param.type === 1">
            <div class="threshold-box">
              <div
                class="threshold-item mgT10"
                style="margin: 15px 0px 0px 0px"
              >
                <label
                  style="width: 260px; text-align: right; padding-right: 5px"
                  >{{ $t("comm_interrupt_threshold") }}：</label
                >
                <div class="threshold-item-content" style="margin-left: 260px">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.beThrValue"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <br />
              <div
                class="threshold-item mgT10"
                style="margin: 15px 0px 0px 0px"
              >
                <label
                  style="width: 260px; text-align: right; padding-right: 5px"
                  >{{ $t("comm_delay_degradation_threshold") }}：</label
                >
                <div class="threshold-item-content" style="margin-left: 260px">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.deThrValue"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <br />
              <div
                class="threshold-item mgT10"
                style="margin: 15px 0px 0px 0px"
              >
                <label style="width: auto; text-align: left; padding-right: 5px"
                  >{{ $t("comm_packet_loss_degradation_threshold") }}:</label
                >
              </div>
              <br />

              <div class="threshold-item mgT10" style="margin-left: 0px">
                <label style="width: 120px">
                  {{ $t("snmptask_once_achieved") }}</label
                >
                <div class="threshold-item-content" style="margin-left: 120px">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.lrDeThrValue"
                      style="width: 80%"
                    />
                  </div>
                  <div class="threshold-item-unit">%</div>
                </div>
              </div>
              <div class="threshold-item mgT10" style="margin-left: 10px">
                <label style="width: 110px">{{
                  $t("probetask_or_continuous")
                }}</label>
                <div class="threshold-item-content" style="margin-left: 110px">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.lrDeContinueNum"
                      style="width: 80%"
                    />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <div class="threshold-item mgT10" style="margin-left: 10px">
                <label style="width: 80px">{{ $t("rpmanager_reach1") }}</label>
                <div class="threshold-item-content" style="margin-left: 80px">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.lrDeReachPer"
                      style="width: 80%"
                    />
                  </div>
                  <div class="threshold-item-unit">%</div>
                </div>
              </div>
              <div class="threshold-item" style="margin-left: 130px">
                <label style="text-align: left; width: 160px; margin-top: 10px">
                  <Checkbox
                    v-model="alarm.param.surgeAlarm"
                    true-value="1"
                    false-value="0"
                    style="width: 200px"
                    >{{ $t("probe_route_alarm") }}
                  </Checkbox>
                </label>
              </div>
              <br />
              <div class="threshold-item" style="margin-left: 130px">
                <label style="text-align: left; width: 160px; margin-top: 10px">
                  <Checkbox
                    v-model.trim="alarm.param.interruptState"
                    true-value="0"
                    false-value="1"
                    style="width: 500px"
                  >
                    {{ $t("probe_no_occurs") }}</Checkbox
                  >
                </label>
              </div>
            </div>
          </div>
          <div v-show="alarm.param.type === 2">
            <div class="threshold-box">
              <div class="threshold-item">
                <label style="text-align: right; padding-right: 5px"
                  >{{ $t("probe_window_size") }}：</label
                >
                <div class="threshold-item-content">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.highWindow"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_second") }}</div>
                </div>
              </div>
              <div class="threshold-item" style="display: none">
                <label>{{ $t("probe_frequency_positioning") }}：</label>
                <div class="threshold-item-content">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.highSum"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                  <div class="threshold-item-unit">{{ $t("comm_times") }}</div>
                </div>
              </div>
              <br />
              <div
                class="threshold-item"
                style="margin: 15px 10px 0px 0px; margin-left: 100px"
              >
                <label style="text-align: right; width: 160px">
                  <Checkbox
                    v-model="alarm.param.lrEventReportFlag"
                    true-value="1"
                    false-value="0"
                    style="width: 150px"
                  >
                    {{ $t("warning_packet_loss_1") }}:</Checkbox
                  >
                </label>
                <div class="threshold-item-content" style="margin-left: 160px">
                  <div class="threshold-item-input">
                    <Input
                      v-model.trim="alarm.param.lrEventReportRate"
                      style="width: 100%"
                      :placeholder="$t('comm_enter_value')"
                    />
                  </div>
                  <div class="threshold-item-unit">%</div>
                </div>
              </div>
              <br />
              <div class="threshold-item mgT10" style="margin-left: 100px">
                <div class="threshold-item" style="margin-left: 4px">
                  <label style="text-align: left; width: 160px">
                    <Checkbox
                      v-model="alarm.param.highSurgeAlarm"
                      true-value="1"
                      false-value="0"
                      style="width: 200px"
                    >
                      {{ $t("probe_route_alarm") }}</Checkbox
                    >
                  </label>
                </div>
              </div>
              <br />
              <div class="threshold-item mgT10" style="margin-left: 100px">
                <div class="threshold-item" style="margin-left: 4px">
                  <label style="text-align: left; width: 160px">
                    <Checkbox
                      v-model.trim="alarm.param.highInterruptState"
                      true-value="0"
                      false-value="1"
                      style="width: 500px"
                      >{{ $t("probe_no_occurs") }}</Checkbox
                    >
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer">
          <Button type="primary" @click="alarmCancel('taskForm')">{{
            $t("common_cancel")
          }}</Button>
          <Button type="primary" @click="alarmOk('taskForm')">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>

      <!--作业调整-->
      <Modal
        sticky
        v-model="taskAdjustModal.show"
        width="582"
        class="threshold-modal"
        :title="$t('comm_operation_plan')"
        draggable
        :mask="true"
        :loading="taskAdjustModal.loading"
        @on-ok="taskAdjustOk"
        @on-cancel="taskAdjustCancel"
      >
        <Form
          ref="taskAdjustModal"
          :model="taskAdjustModal.param"
          :rules="taskAdjustValidate"
          @submit.native.prevent
          class="myForm labelHeight"
          :label-width="120"
        >
          <FormItem
            :label="$t('comm_start_time')"
            prop="start_date"
            class="labelContent"
          >
            <DatePicker
              type="date"
              v-model="taskAdjustModal.param.start_date"
              :placeholder="$t('comm_select_date2')"
              class="myDateClass"
              format="yyyy-MM-dd"
              @on-change="playStartDateChange"
            ></DatePicker>
            <label style="margin: 0 10px">—</label>
            <DatePicker
              type="date"
              v-model="taskAdjustModal.param.end_date"
              :placeholder="$t('comm_select_date2')"
              class="myDateClass"
              format="yyyy-MM-dd"
              :options="playEndDate"
            ></DatePicker>
          </FormItem>
          <!-- <FormItem
          label="—"
          prop="end_date"
          class="labelContentMargin"
          style="width: 50%; display: inline-block"
        > -->
          <!-- </FormItem> -->
          <FormItem :label="$t('comm_time_period')" class="labelContent">
            <div
              v-for="(item, index) in taskAdjustModal.param.timeList"
              :key="index"
            >
              <TimePicker
                type="time"
                v-model="item.start"
                :placeholder="$t('comm_select_date')"
                class="myDateClass"
                format="HH:mm"
                :clearable="false"
                @on-change="playStartTimeChange($event, index)"
              ></TimePicker>
              <label style="margin: 0 10px">{{ $t("comm_to") }}</label>
              <TimePicker
                type="time"
                v-model="item.end"
                :placeholder="$t('comm_select_date')"
                class="myDateClass"
                format="HH:mm"
                :clearable="false"
                @on-change="playEndTimeChange($event, index)"
              ></TimePicker>
              <Icon
                :type="
                  (taskAdjustModal.param.timeList.length > 1 &&
                    index === taskAdjustModal.param.timeList.length - 1) ||
                  taskAdjustModal.param.timeList.length === 1
                    ? 'ios-add-circle-outline'
                    : 'ios-remove-circle-outline'
                "
                class="iconItem"
                :class="
                  (taskAdjustModal.param.timeList.length > 1 &&
                    index === taskAdjustModal.param.timeList.length - 1) ||
                  taskAdjustModal.param.timeList.length === 1
                    ? 'addItem'
                    : 'removeItem'
                "
                @click="taskTimeChange(index)"
              />
            </div>

            <!-- <Checkbox
            v-model="taskAdjustModal.weekShow"
            @on-change="weekShowChange"
            style="margin-left: 20px"
          >重复</Checkbox> -->
          </FormItem>
          <!-- <FormItem
          label="—"
          prop="end_time"
          class="labelContentMargin"
          style="width: 140px; display: inline-block"
        >
      
        </FormItem> -->
          <!-- <FormItem style="width: 20%; display: inline-block">
          <Checkbox
            v-model="taskAdjustModal.weekShow"
            @on-change="weekShowChange"
            >重复</Checkbox
          >
        </FormItem> -->
          <FormItem
            :label="$t('comm_repeat_cycle') + $t('comm_colon')"
            v-show="taskAdjustModal.weekShow"
          >
            <CheckboxGroup v-model="taskAdjustModal.param.week">
              <Checkbox :label="1">{{ $t("comm_monday") }}</Checkbox>
              <Checkbox :label="2">{{ $t("comm_tuesday") }}</Checkbox>
              <Checkbox :label="3">{{ $t("comm_wednesday") }}</Checkbox>
              <Checkbox :label="4">{{ $t("comm_thursday") }}</Checkbox>
              <Checkbox :label="5">{{ $t("comm_friday") }}</Checkbox>
              <Checkbox :label="6">{{ $t("comm_saturday") }}</Checkbox>
              <Checkbox :label="7">{{ $t("comm_sunday") }}</Checkbox>
            </CheckboxGroup>
          </FormItem>
          <p class="note">{{ $t("discover_time_note") }}</p>
        </Form>
      </Modal>

      <!--自定义列表项 修改之后-->
      <Modal
        sticky
        v-model="customModalShow"
        width="540"
        height="720"
        class="threshold-modal modal-selected"
        :title="$t('custom_list_items')"
        draggable
        :mask="true"
        :loading="customModalShowLoading"
      >
        <Row class="modal-selected-row">
          <Col>
            <Table
              ref="fieldsModalTableData"
              height="550"
              width="400"
              :show-header="false"
              :columns="fieldsColumns"
              :data="allocationListFields"
              @on-row-click="getFieldsColumnsIndex"
              :row-class-name="rowClassName"
            >
            </Table>
          </Col>
          <Col>
            <div class="btn-box">
              <Tooltip :content="$t('dash_up')" placement="right">
                <div class="sort-btn" @click="moveUp">
                  <Icon
                    type="md-arrow-up"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
              <Tooltip :content="$t('dash_down')" placement="right">
                <div class="sort-btn" @click="moveDown">
                  <Icon
                    type="md-arrow-down"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
            </div>
          </Col>
        </Row>
        <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
import { mapGetters, mapActions,mapState,mapMutations } from "vuex";
import global from "@/common/global.js";
import "@/config/page.js";
import "@/timechange.js";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import thresholdItem from "./thresholdItem.vue";
import indexItem from "./indexItemCopy.vue";
import { addDraggable } from "@/common/drag.js";
import taskItem from "./taskItem.vue";
import highItem from "./highItem.vue";
import academicItem from "./academicItem.vue";
import automaticItem from "./automaticItem.vue";
import importArea from "./import.vue";
import axios from "axios";
import moment from "moment";
import locationreload from "@/common/locationReload";
import { type } from "os";
import {tableEditBtn} from '@/assets/base64Img/img'
import {tableEditLightBtn} from '@/assets/base64Img/img'
import ipv6Format from "@/common/ipv6Format";
  import langFn  from '@/common/mixins/langFn';
import { color } from 'echarts/lib/export';


/**
 * 时间转为秒
 * @param time 时间(00:00:00)
 * @returns {string} 时间戳（单位：秒）
 */
var time_to_sec = function (time) {
  var s = "";

  var hour = time.split(":")[0];
  var min = time.split(":")[1];
  var sec = time.split(":")[2] || 0;

  s = Number(hour * 3600) + Number(min * 60) + Number(sec);

  return s;
};
/**
 * 时间秒数格式化
 * @param s 时间戳（单位：秒）
 * @returns {*} 格式化后的时分秒
 */
var sec_to_time = function (s) {
  var t;
  if (s > -1) {
    var hour = Math.floor(s / 3600);
    var min = Math.floor(s / 60) % 60;
    // var sec = s % 60;
    if (hour < 10) {
      t = "0" + hour + ":";
    } else {
      t = hour + ":";
    }

    if (min < 10) {
      t += "0";
    }
    t += min;
    // if(sec < 10){t += "0";}
    // t += sec.toFixed(2);
  }
  return t;
};

export default {
  name: "index",
    mixins: [langFn],
  components: {
    automaticItem,
    taskItem,
    highItem,
    thresholdItem,
    indexItem,
    academicItem,
    importArea,
    TreeSelect ,
    DetailModal:()=>import('../components/DetailModal.vue')
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      // 作业周期是否显示
      jobCycleParam:false,
      editClass:'modify-btn',
      editClass2:'modify2-btn',
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      selectedDatas: new Set(),
      detailShow:false,
      anpmVersion:'1',// 默认为专业版
      addBtnLoading:false,
      btnLoading:false,
      targetTypeArry: [],
      treeValue:'',
      options: [ {
      id: 'a',
      label: 'a',
      children: [ {
          id: 'aa',
          label: 'aa',
      }, {
          id: 'ab',
          label: 'ab',
      } ],
      }, {
      id: 'b',
      label: 'b',
      }, {
      id: 'c',
      label: 'c',
      } ], 
      maintainList: [
      { label: this.$t('logback_first'), value: 1 },
        { label: this.$t('logback_second'), value: 2 },
        { label: this.$t('logback_tertiary'), value: 3 }
      ],
      runStateList:[
        { label: this.$t('common_Normal'), value: 1 },
        { label: this.$t('phytopo_deterioration'), value: 2 },
        { label: this.$t('phytopo_interrupt'), value: 3 },
        { label: this.$t('comm_unknown'), value: 4 }
      ],
      btnList:[
        { label: this.$t('access_exe_mode_import'), value: 1 },
        { label: this.$t('but_export_checked'), value: 2 },
        { label: this.$t('but_export_all'), value: 3 },
        { label: this.$t('probe_adjustment'), value: 4 },
        { label: this.$t('snmptask_alarm_setting'), value: 5 },
        { label: this.$t('dash_pause'), value: 6 },
        { label: this.$t('dash_enable'), value: 7 },
        { label: this.$t('common_delete'), value: 8 },
      ],
      dynamicIp:[
        { label: this.$t('comm_yes'), value: 1 },
        { label: this.$t('comm_no'), value: 0 }
      ],
      opretionBtn:'',
      groupList: [],
      //权限对象
      permissionObj: {},
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      taskNum: "",
      modalStatus: false,
      showImport: false,
      importUrl: {
        templateUrl: "/probetask/download",
        uploadUrl: "/probetask/readExcel",
        repeatDownUrl: "/probetask/repeatExcelExport",
        saveUrl: "/probetask/readExcelSave",
      },
      typeConfigDisable: false,
      default_data: {},
      stateList: [
        { value: 0, label: this.$t('snmptask_suspended') },
        { value: 1, label: this.$t('but_enable') },
        { value: 3, label: this.$t('probe_unmatch') },
        { value: 4, label: this.$t('rpmanager_deleting') }
      ],
      taskTypeList: [
        // {
        //     value:3,
        //     label:'A类遍历'
        // },
        // {
        //     value:0,
        //     label:'B类遍历'
        // },
        { value: 1, label: this.$t('comm_high_freq_monitoring') },
        { value: 2, label: this.$t('comm_ordinary_dial') },
      ],
      dialTestTypeList: [ {
              value: 2,
              label: "ICMP Ping",
            },
            {
              value: 4,
              label: "UDP Trace",
            },
            {
              value: 5,
              label: "ICMP Trace",
            },
            {
              value: 7,
              label: "TCP Trace",
            },],
      deviceTypeList: [],
      query: {
        groupIds: [],
        orgId: "",
        startState: "",
        taskType: "",
        maintainLevel: "",
        configType: "",
        source: "",
        dynamicIp: "",
        dialFrequency: "",
        fieldName: "update_ts",
        orderBy: "desc",
        pageNo: 1,
        pageSize: 10,
      },
      loading: false,
      customKey:"api:probetask/list+sys_func_id:15",
      customMoveIndex:0,
      customModalShow:false,
      customModalShowLoading:false,
      columns:[],
      fieldsJsonObjArr:[],
      allocationListFields:[],
      customFieldsColumnsKeyWidth:[],
      screenWidth:0,
      fieldsColumns: [
      {
        key: "showField",
        width: 35,
        render: (h, params) => {
          let row = params.row;
          return h("Checkbox", {
            props: {
              value: row.showField,
              disabled: row.fixedField // 当fixedField为true时禁用Checkbox
            },
            on: {
              input: (value) => {
                if (!row.fixedField) {
                  // 更新 showField 的值
                  this.allocationListFields[params.index].showField = value;
                }
              }
            }
          });
        }
      },
        {
          key: "parameterTitle",
          align: "left",
          // width: 160,
          render:(h,params) => {
                        let fontColor = 'var(--field_font_color ,#fff)'
            if(params.row.fixedField) {
              fontColor = '#5CA0D5'
            }
            return h('div',{
              style: {
                color:fontColor,
                fontWeight:400
              }
            },params.row.parameterTitle)
          }
        }
      ],
      fixedColumns: [
        {
          type: "selection",
          width: 30,
          align: "center",
          key: "firstColumnSelect",
          // fixed:'left'
        },
        //任务编号
        {
          title: this.$t('task_number'),
          key: "taskNum",
          align: "left",
          minWidth: 160,
          render: (h, params) => {
            let str = params.row.taskNum,
              i = h("i", {
                class: "icon-box icon-index myIconClass",
                on: {
                  click: () => {
                    // this.actionClick(params.row, "index", event);
                  },
                },
              }),
              span = h(
                "span",
                {
                  class: "icon-index-name",
                },
                str === undefined || str === null || str === "" ? "--" : str
              ),
              array = [span];
            if (params.row.tubiao) {
              array.unshift(i);
            }
            return h("div", array);
          },
        },
        // 最后活跃时间
        {
          title: this.$t('gether_last_time'),
          key: "updateTs",
          align: "left",
          // sortable: "custom",
          minWidth:160,
          render: (h, params) => {
            let str = params.row.updateTs;
            return h(
                "div",
                {
                  style: {
                    textAlign: "left",
                    width: "100%",
                    textIndent: "0px",
                    overflow: "hidden", //超出的文本隐藏
                    textOverflow: "ellipsis", //溢出用省略号显示
                    whiteSpace: "nowrap", //溢出不换行
                  },
                  attrs: {
                    title: str,
                  },
                },
                str
            );
          },
        },
        {
          title: this.$t('task_list_probe_ip'),
          key: "showSourceIp",
          align: "left",
          width: 200,
          render: (h, params) => {
            let str = params.row.showSourceIp;
            let maxWidth = params.column.width; // 获取动态传递的宽度

           
            str = ipv6Format.formatIPv6Address(str,maxWidth);
            return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },
        },
        // 探针端口
        {
          title: this.$t('comm_probe_port'),
          key: "sourcePort",
          align: "left",
          width:this.getColumnWidth(90,100),
          render: (h, params) => {
            let str = params.row.sourcePort;
            let routePath = params.row.routePath || "";
            let routePathType =
              routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
            let routePathType2 =
              routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
            let text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (
              params.row.configType == 1 ||
              params.row.configType == 2 ||
              params.row.configType == 5 ||
              params.row.configType == 6
            ) {
              text = "--";
            } else if (
              (params.row.configType == 3 && routePathType == 2) ||
              (params.row.configType == 3 && routePathType == 1 && str < 20) ||
              (params.row.configType == 3 && routePathType == 3 && str < 20) ||
              (params.row.configType == 8 && routePathType2 == 2)
            ) {
              text = "--";
            } else if (str < 20) {
              text = "--";
            } else {
              text = str;
            }
            return h("span", text);
          },
        },
        //分组
        {
          title: this.$t('comm_grouping'),
          key: "groupNames",
          align: "left",
          minWidth: 120,
          render: (h, params) => {
            let str = params.row.groupNames;
            if (str === undefined || str === null) {
              str = "--";
            }
            // let v = "";
            // if (str.length > 11) {
            //   v = str.substring(0, 9) + "...";
            //    return h('Tooltip',{
            //           props: {
            //             placement:'bottom-start'
            //           }
            //          },
            //          [v,h(
            //           'span',{
            //             slot:'content',
                       
                        
            //           },str
            //          )]);
            // } else {
            //   v = str;
            //    return h('span',v)
            // }

           if(str !== "--") {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'top',
                  content: str,
                }

              },str)

            ])
            }else {
              return h('div',str)
            }
            // let Arr1 = h('span', v);
            // return h('div', {
            //   class: {
            //     'text-ellipsis': true
            //   },
            //   style: {
            //     'word-break': 'keep-all',
            //     'white-space': 'nowrap',
            //     'overflow': 'hidden',
            //     'text-overflow': 'ellipsis'
            //   },
            //   domProps: {
            //     title: str
            //   }
            // }, [Arr1])
          },
        },
        // 运维等级
        {
          title: this.$t('task_type'),
          key: "maintainLevel",
          align: "left",
          width: this.getColumnWidth(90,100),
          render: (h, params) => {
            let str = params.row.maintainLevel;
            if (str == 1) {
              str = this.$t('logback_first')
            } else if (str == 2) {
              str = this.$t('logback_second')
            } else if (str == 3) {
              str = this.$t('logback_tertiary')
            }
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        //一个ipv6最宽宽度
        {
          title: this.$t('comm_IP(MAC)'),
          key: "showDestIp",
          align: "left",
          width: 340,
          ellipsis: true,
          render: (h, params) => {
            let maxWidth = params.column.width; // 获取动态传递的宽度

            let config_type = params.row.configType;
            let destMac = params.row.destMac??'--';
            let text = "";
            let showText = ''
            let str = params.row.showDestIp;
            if (str == undefined || str == null || str == "") {
              text = "--";
            } 
            // else if (str.length > 16) {
            //   text = str.substring(0, 16) + "...";
            // } 
            else {
              text = str;
            }
            showText =  text+'('+destMac+')'
            let dest_ip_name = params.row.destIpName;
            let nameText = "";
            if (
              dest_ip_name == undefined ||
              dest_ip_name == null ||
              dest_ip_name == ""
            ) {
              nameText = "--";
            } else {
              nameText = dest_ip_name;
            }
            let title =
              (showText != "--"
                ? (config_type != 8 ? this.$t('access_destination_ip')+"：" : this.$t('access_destination_acc')+"：") + showText
                : "") + (nameText != "--" ? ","+this.$t('probetask_target_name')+"：" + dest_ip_name : "");
            // return h(
            //   "span",
            //   {
            //     class: {
            //       "text-ellipsis": true,
            //     },
            //     domProps: {
            //       title:  showText,
            //     },
            //   },
            //  showText
            // );
            showText = ipv6Format.formatIPv6MAC(showText,maxWidth);
            return h('div', { style: { whiteSpace: 'pre-wrap' } }, showText);
          },
        },
        // 目标名称
        {
          title: this.$t('comm_target_name'),
          key: "destIpName",
          align: "left",
          width: 150,
          render: (h, params) => {
            let str = params.row.destIpName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            return h(
                "div",
                {
                  style: {
                    textAlign: "left",
                    width: "100%",
                    textIndent: "0px",
                    overflow: "hidden", //超出的文本隐藏
                    textOverflow: "ellipsis", //溢出用省略号显示
                    whiteSpace: "nowrap", //溢出不换行
                  },
                  attrs: {
                    title: str,
                  },
                },
                str
            );
          }
        },
        // 目标类型
        {
          title: this.$t('task_location'),
          key: "location",
          align: "left",
          width: 150,
          render: (h, params) => {
            let str = params.row.location;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            return h(
                "div",
                {
                  style: {
                    textAlign: "left",
                    width: "100%",
                    textIndent: "0px",
                    overflow: "hidden", //超出的文本隐藏
                    textOverflow: "ellipsis", //溢出用省略号显示
                    whiteSpace: "nowrap", //溢出不换行
                  },
                  attrs: {
                    title: str,
                  },
                },
                str
            );
          }
        },
        {
          title: this.$t('comm_target_type'),
          key: "destType",
          align: "left",
          width: this.getColumnWidth(90,100),
          render: (h, params) => {
            let str = params.row.destType;
            let typeList = this.deviceTypeList;
            for (var i = 0; i < typeList.length; i++) {
              if (parseInt(typeList[i].value) == str) {
                str = typeList[i].lable
                break
              }
            }
            return h(
              "span",
              str === undefined || str === null || str === "" || str == 0 ? "--" : str
            );
          },
        },
        // 目标端口
        {
          title: this.$t('access_des_port'),
          key: "destPort",
          width: this.getColumnWidth(90,100),
          align: "left",
          render: (h, params) => {
            let str = params.row.destPort;
            let routePath = params.row.routePath || "";
            let routePathType =
              routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
            let routePathType2 =
              routePath.indexOf("-") >= 0 ? routePath.split("-")[1] : "";
            // console.log(params.row)
            let text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (
              params.row.configType == 1 ||
              params.row.configType == 2 ||
              params.row.configType == 5
            ) {
              text = "--";
            } else if (params.row.configType == 3 && routePathType == 2) {
              text = "--";
            } else if (params.row.configType == 8 && routePathType2 == 2) {
              text = "--";
            } else {
              text = str;
            }
            return h("span", text);
          },
        },
            {
          title: this.$t('comm_org'),
          key: "orgName",
          align: "left",
          minWidth: 150,
          render: (h, params) => {
            let str = params.row.orgName;
            if(str === undefined || str === null || str === '' || str === 'null') {
              return h('span', '--');
            }
            
            return h('div', { class: 'table-ellipsis' }, [
              h('Tooltip', {
                props: {
                  placement: 'top',
                  content: str,
                  maxWidth: 300
                }
              }, str)
            ]);
          },
        },
      
        {
          title: this.$t('task_dial_type'),
          key: "configType",
          align: "left",
          width:90,
          render: (h, params) => {
            let str = params.row.configType,
              text = "--";
            let routePath = params.row.routePath || "";
            switch (str) {
              // case 0:
              //     text=' 集团遍历';
              //     break;
              case 1:
                text = " UDP Trace";
                break;
              case 2:
                text = "ICMP Ping";
                break;
              case 3:
                text =
                  routePath.indexOf("-") >= 0
                    ? routePath.split("-")[1] == 1
                      ? "UDP Ping"
                      : routePath.split("-")[1] == 2
                        ? "ICMP Ping"
                        : routePath.split("-")[1] == 3
                          ? "TCP Ping"
                          : "--"
                    : "--";
                break;
              case 4:
                text = "UDP Trace";
                break;
              case 5:
                text = "ICMP Trace";
                break;
              case 6:
                text = "TCP Trace";
                break;
              case 7:
                text = "TCP Trace";
                break;
              case 8:
                text =
                  routePath.indexOf("-") >= 0
                    ? routePath.split("-")[1] == 1
                      ? "UDP Trace"
                      : routePath.split("-")[1] == 2
                        ? "ICMP Trace"
                        : routePath.split("-")[1] == 3
                          ? "TCP Trace"
                          : "--"
                    : "--";
                break;
              default:
                text = "--";
                break;
            }
            return h("div", text);
          },
        },
        // 路径数
        {
          title: this.$t('probetask_num_paths'),
          key: "urlNum",
          align: "left",
          width: this.getColumnWidth(90,130),
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.urlNum;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        // {
        //   title: "拨测频率",
        //   key: "dialFrequency",
        //   align: "left",
        //   width: 100,
        // },
        // {
        //   title: "动态IP",
        //   key: "dynamicIp",
        //   align: "left",
        //   width: 100,
        // },
        {
          title: this.$t('task_status'),
          key: "startState",
          slot: 'startState',
          align: "left",
          width: this.getColumnWidth(90,110),
        },
        {
          title: this.$t('probetask_monitoring_state'),
          key: "runState",
          align: "center",
          width: this.getColumnWidth(100,130),
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.runState,
              text = "";
            switch (str) {
              case 1:
                text = h(
                  "span",
                  {
                    class: "runState runGreen"
                  }
                );
                break;
              case 2:
                text = h(
                  "span",
                  {
                    class: "runState runOrange"
                  }
                );
                break;
              case 3:
                text = h(
                  "span",
                  {
                    class: "runState runRed"
                  }
                );
                break;
              case 4:
                text = h(
                  "span",
                  {
                    class: "runState runGrey"
                  }
                );
                break;
            }
            return h("div", [text]);
          }
        },
        {
          title: this.$t('comm_operate'),
          key: "action",
          align: "center",
          width:150,
          // fixed: 'right',
          renderHeader: (h) => {
            const handleClick = () => {
              this.customModalShow = true;
              this.customModalShowLoading = true;
            }
            return h('div',[
              h('span',this.$t('comm_operate')),
              h('img', {
                attrs: {
                  src:this.currentSkin == 1 ? tableEditBtn:tableEditLightBtn
                },
                style: {
                  width: '18px', // 调整图片大小
                  height: '18px', // 考虑保持宽高比
                  marginLeft: '20px',  
                  verticalAlign: 'middle',// 使图片居中
                  cursor: 'pointer'

                },
                on: {
                click: handleClick,
               
              },
              })
            ])
          },
          render: (h, params) => {
            let flag = params.row.isPaisheng == 2 ? false : true;
            let array = [];
            let showDetail = h('Tooltip', 
                  {
                    props: {
                      placement: 'right-end'
                    }
                  }, [
                      h(
                        "span",
                        {
                          // class: "show-btn",
                          class: this.currentSkin == 1 ? "show-btn":"show2-btn",
                           style: {
                                display: this.permissionObj.look
                                  ? "inline-block"
                                  : "none",
                          },
                          on: {
                            click: () => {
                              if (flag) {
                                this.actionClick(params.row, "show");
                              } else {
                                this.$Message.warning({ content: this.$t('comm_deleting2'), background: true })
                              }
                            },
                          },
                        },
                      ),

                    h('span', {slot: 'content',class:'columnTooltip'}, this.$t('server_view'))
                ]);
            let modify = h('Tooltip', 
                  {
                    props: {
                      placement: 'right-end',
                      transfer:true
                    }
                  }, [
                      h(
                        "span",
                        {
                          class: this.currentSkin == 1 ? this.editClass:this.editClass2,
                           style: {
                                display: this.permissionObj.update
                                  ? "inline-block"
                                  : "none",
                          },
                          on: {
                            click: () => {
                        if (flag) {
                          this.actionClick(params.row, "modify");
                        } else {
                          this.$Message.warning({ content: this.$t('comm_deleting2'), background: true })
                        }
                            },
                          },
                        },
                      ),
                    h('span', {slot: 'content',class:'columnTooltip'}, this.$t('common_update'))
                ]);
             
            if (!params.row.tubiao) {
              array.push(showDetail,modify);
              if (params.row.isPaisheng == 1) {
                array = [];
              }
            }
            return h("div", array);
          },
        },
      ],
      tabList:  [] ,
      originalList: [], //保存原始列表数据
      orderTabList: [],
      totalCount: 0,
      pageNo: 1,
      pageSize: 10,
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      btnType: true, //true、显示按钮，false、隐藏按钮
      btnValue: 1, //1、自动，2、普通,3、高频,4、A类遍历
      playEndDate: {
        disabledDate(date) {
          // return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      playEndDate2: {
        disabledDate(date) {
          // return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      //当前修改的探针code
      editGetherCode: null,
      taskModal: {
        show: false,
        loading: true,
        data: {},
        automatic: {},
        taskParam: {},
        highParam: {},
        academicmatic: {},
        type: "",
      },
      taskAdjustModal: {
        show: false,
        weekShow: false,
        loading: true,
        param: {
          start_date: "",
          end_date: "",
          start_time: "",
          end_time: "",
          is_week: 0,
          week: [],
          timeList: [],
        },
      },
      taskAdjustValidate: {
        // start_date: [
        //   {
        //     required: true,
        //     type: "date",
        //     message: this.$t('comm_start_time'),
        //     // trigger: "change",
        //   },
        // ],
        // end_date: [
        //   {
        //     required: true,
        //     type: "date",
        //     message: this.$t('comm_deadline'),
        //     // trigger: "change",
        //   },
        // ],
        start_time: [
          {
            required: true,
            type: "string",
            message: this.$t('comm_start'),
            trigger: "change",
          },
        ],
        end_time: [
          {
            required: true,
            type: "string",
            message: this.$t('comm_deadline'),
            trigger: "change",
          },
        ],
      },
      taskAdjustForm: {
        weekShow: false,
        param: {
          start_date: "",
          end_date: "",
          is_week: 0,
          week: [],
          timeList: [{ start: "0000", end: "2359" }],
        },
      },
      rowData:{},
      indexModal: {
        data: {},
      },
      threshold: {
        show: false,
        data: {},
      },
      flag: true, //防重复点击
      licenceNum: "",
      editModal: {
        show: false,
        loading: true,
        data: {},
      },
      saveQuery: {},
      echartIsSpecial: false,
      High: false,
      /*告警修改参数*/
      alarm: {
        loading: true,
        modal: {
          show: false,
          loading: true,
        },
        param: {
          type: 1,
          ids: "",
          beThrValue: null,
          deThrValue: null,
          lrDeThrValue: null,
          lrDeContinueNum: null,
          lrDeReachPer: null,
          highWindow: null,
          highSum: null,
          lrEventReportRate: null,
          lrEventReportFlag: null,
          surgeAlarm: null,
          highSurgeAlarm: null,
        },
      },
      startOption: {},
      endOption: {}
    };
  },

  watch: {
   
    "taskAdjustForm.param.start_date"() {
      let self = this;
      this.endOption = {
        disabledDate(date) {
          if (self.taskAdjustForm.param.start_date) {
            return date && date.valueOf() <= self.taskAdjustForm.param.start_date - 86400000;
          }
        }
      };
    },
    "taskAdjustForm.param.end_date"() {
      let self = this;
      this.startOption = {
        disabledDate(date) {
          if (self.taskAdjustForm.param.end_date) {
            return date && date.valueOf() > self.taskAdjustForm.param.end_date;
          }
        }
      };
    }
  },
  created() {
    window.addEventListener('resize', this.handleResize);
    this.jobCycleParam = false;
    // 如果界面导航第四五六步刷新页面页面回到第三步
    if(this.tipStatus == 'true' && this.tipActive == 4) {
      this.changeTipActive(3)
    }
    if(this.tipStatus == 'true') {
       this.editClass = 'disabled-btn';
       this.editClass2 = 'disabled2-btn';
    }else {
      this.editClass = 'modify-btn';
      this.editClass2 = 'modify2-btn';
    }
    
     this.anpmVersion = JSON.parse(sessionStorage.getItem('accessToken')).anpmVersion;
     if (this.anpmVersion !='1') {
       this.taskTypeList.splice(1,1)
       this.columns.splice(13,1)
       this.query.taskType=1;
       this.query.taskStatus = 1;
       this.dialTestTypeList = [
            {
              value: 2,
              label: "ICMP Ping",
            },
            {
              value: 4,
              label: "UDP Trace",
            },
            {
              value: 5,
              label: "ICMP Trace",
            },
            {
              value: 7,
              label: "TCP Trace",
            },
          ];
     }
    this.getDeviceTypeList();
    this.$nextTick(() => {
    if (this.$route && this.$route.path) {
      locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    }
  });
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    if (this.permissionObj.update == false) {
      this.columns.pop();
    }
    this.$nextTick(() => {
      this.saveQuery = Object.assign({}, this.query);
    });
    this.getTreeOrg();
    this.getGroupList();
    this.getType()
    //获取屏幕总长度
    this.screenWidth = window.innerWidth-45;
    //保存原始的字段项展示长度
    this.setCustomFieldsColumnsKeyWidth();
  },
  computed: {
    // ...mapGetters(["ClientList"]),
    ...mapGetters({ defaultConfigList: "defaultConfigList" }),
    ...mapGetters(["dialFrequencyList"]),
    ...mapState('m_help',['tipStatus','tipActive']),
    placeholderStr() {
      if(localStorage.getItem('locale') === 'en') {
        return this.$t('probe_1').slice(0,36) + '...'
      } else {
         return this.$t('probe_1').slice(0,18) + '...'

      }
      
    }
  },
  methods: {
    handleResize() {
      // 重新计算表格宽度
      this.$nextTick(() => {
        // 重新计算屏幕宽度
        this.screenWidth = window.innerWidth - 45;
        // 重新设置列宽
        this.setCustomFieldsColumnsKeyWidth();
        // 重新获取表格数据以更新列配置
        this.getAllocationListFieldsByKey();
    });
    },
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    //保存原始的字段项展示长度
    setCustomFieldsColumnsKeyWidth(){
      if(this.customFieldsColumnsKeyWidth==0){
        this.fixedColumns.forEach(item=>{
          let customFieldsColumnsKeyWidthObj = {"key":item.key,"width":item.width};
          this.customFieldsColumnsKeyWidth.push(customFieldsColumnsKeyWidthObj);
        });
      }
    },
    //修改自定列表项
    customModalOk() {
        let param = {
          allocationListFields:this.allocationListFields,
          key:this.customKey
        }
        this.$http.PostJson("/allocationlistfields/update", param).then((res) => {
        if (res.code === 1) {
          this.$Message.success({ content: this.$t('comm_success'), background: true });
          this.customModalShow= false;
          this.customModalShowLoading= false;
          this.getList(this.query);
        }
      })
    },
    //取消修改
    customModalCancel() {
      this.customModalShow= false;
      this.customModalShowLoading= false;
    },
    //获取列表项下标并改变状态
    getFieldsColumnsIndex(row, index){
      this.customMoveIndex = index;
      // if(row.fixedField == false){
      //   if(row.showField == true){
      //   this.allocationListFields[index].showField =false;
      //   }else{
      //     this.allocationListFields[index].showField =true;
      //   }
      // }
    },
    rowClassName(row, index) {
      if (index === this.customMoveIndex) {
        return "selected-row";
      }
      return "";
    },

    //上移
    moveUp() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex > 0) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex - 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex - 1;
      }
    },
    //下移
    moveDown() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex < this.allocationListFields.length - 1) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex + 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex + 1;
      }
    },


    ...mapActions(["getDefaultConfigList",'getDialParam']),
    ...mapMutations('m_help',['changeTipActive','changeTipStatus']),
     // 界面引导，点击跳过
        closeTip() {
            this.changeTipStatus(false)
            this.changeTipActive(13)
              // 需要重新刷新一下页面
        parent.location.reload()

        },
        // 页面引导，点击结束当前导航
        closeOne() {
          this.changeTipActive(8)
          // 需要重新刷新一下页面
        parent.location.reload()
    
        },
    // ...mapActions({ getDefaultList: "getDefaultConfigList" }),
    moreBtnClick(val){
      eval(`this.${val}()`)
    },
    taskItemEventChange(){
      this.jobCycleParam = !this.jobCycleParam;
    },
    getType() {
      this.$http
        .wisdomPost("/defvalue/getCodeDataTableConfigByKey", { key: 'INTERRUPT_DEVICE_TYPE' })
        .then((res) => {
          if (res.code == 1) {
            if (res.data && res.data.value) {
              this.targetTypeArry = res.data.value.split(',')
            } else {
              this.targetTypeArry = []
            }
          }
        });
    },
    focusFn() {
      this.getTreeOrg()
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item
          });
          _self.treeData = treeNodeList;
        }
      })
    },
    loadData(item, callback) {
      // this.$refs.TreeSelect.$refs.Vinput.focus({ preventScroll: true})
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item
          });
          callback(childrenOrgList);
        }
      })
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.query.orgId = item[0] ? item[0].id : null;
    },
    onClear(){
      this.query.orgId = ''
      this.treeValue = ''
    },
    modalStatusChange(val) {
      this.modalStatus = val;
      // 如果在界面导航，没有新增的时候取消让导航进行到第三
      if(!val) {
        if(this.tipActive == '4') {
          this.changeTipActive(3)
        }
      }

    },
    //下拉框事件
    clientChange(val) {
      if (val === 0) {
        this.query.orgId = "";
      } else {
        this.query.orgId = val;
      }
    },
    taskTypeChange(val) {
      this.dialTestTypeList = [];
      this.$refs.typeConfig.clearSingleSelect();
      this.query.configType = "";
      this.query.routePath = "";
      if (val === -1) {
        this.query.taskStatus = "";
        this.typeConfigDisable = true;
      } else {
        this.query.taskStatus = val;
        this.typeConfigDisable = false;
        if (val == 0) {
          this.dialTestTypeList = [
            {
              value: 1,
              label: "UDP Trace",
            },
            {
              value: 6,
              label: "TCP Trace",
            },
          ];
        } else if (val == 1) {
          this.dialTestTypeList = [
            {
              value: 2,
              label: "ICMP Ping",
            },
            {
              value: 4,
              label: "UDP Trace",
            },
            {
              value: 5,
              label: "ICMP Trace",
            },
            {
              value: 7,
              label: "TCP Trace",
            },
          ];
        } else if (val == 2) {
          this.dialTestTypeList = [
            {
              value: 1,
              label: "UDP Ping",
            },
            {
              value: 3,
              label: "TCP Ping",
            },
            {
              value: 2,
              label: "ICMP Ping",
            },
          ];
        } else if (val == 3) {
          this.dialTestTypeList = [
            {
              value: 3,
              label: "TCP Trace",
            },
            {
              value: 1,
              label: "UDP Trace",
            },
            {
              value: 2,
              label: "ICMP Trace",
            },
          ];
        }
      }
    },
    dialTestTypeChange(val) {
      if (this.query.taskStatus == 0 || this.query.taskStatus == 1) {
        if (val === 0 || val === undefined) {
          // this.query.source_port="";
          this.query.configType = "";
        } else {
          // this.query.source_port=val;
          this.query.configType = val;
        }
      } else if (this.query.taskStatus == 2) {
        if (val === 0 || val === undefined) {
          this.query.routePath = "";
        } else {
          this.query.routePath = val;
        }
      } else if (this.query.taskStatus == 3) {
        this.query.configType = 8;
        if (val === 0 || val === undefined) {
          this.query.routePath = "";
        } else {
          this.query.routePath = val;
        }
      }
    },
    isSpecialChange(val) {
      if (val) {
        this.query.isSpecial = 1;
      } else {
        this.query.isSpecial = "";
      }
    },
    //默认数据接口
    getDefault() {
      let _self = this;
      _self.$http
        .post("/probetask/getSetingList")
        .then((res) => {
          _self.default_data = res.data || {};
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //获取列表数据
    getList(param) {
      this.getAllocationListFieldsByKey();
      param.source = param.source; //搜索去空格
      console.log('param.source==>',param);
      let _self = this;
      this.loading = true
      _self.$http.PostJson("/probetask/list", param).then((res) => {
        if (res.code === 1) {
          this.tabList  = res.data.records || [];
          _self.totalCount = res.data.total;
          this.loading = false
        } else {
          this.$Message.error({ content: res.msg, background: true })
        }
      }).catch(err => {
        this.columns = this.fixedColumns;
        this.loading = false
      }).finally(() => {
        this.loading = false
      });
    },
    getAllocationListFieldsByKey(){
      //key值
      let _self = this;
      _self.$http.PostJson("/allocationlistfields/getAllocationListFieldsByKey", { key: this.customKey }).then((res) => {
        if (res.code === 1) {
          if(res.data.allocationListFields){
            let screenWidthTemp = this.screenWidth;
            this.allocationListFields = res.data.allocationListFields;
            this.fieldsJsonObjArr = [];
            this.fieldsJsonObjArr.push("firstColumnSelect");//第一列选择列
            //中间自定义列
            res.data.allocationListFields.forEach(item=>{
                if(item.showField === true){
                  this.fieldsJsonObjArr.push(item.parameterName);
                }
            });
            this.fieldsJsonObjArr.push("action");//最后一列操作列
            this.columns = [];
            let customColumnsWidth = 0;//回显自定义字段总宽度
            let customColumnsAvgWidth = 0;//回显自定义字段平均宽度
            //从固定列中动态回显自定义列标头并且按照顺序
            if(this.fieldsJsonObjArr.length>0){
              this.fieldsJsonObjArr.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item === item2.key){
                    //计算需要展示自定义字段项总长度
                    let customFieldsColumnsKeyWidthTemp = this.customFieldsColumnsKeyWidth.find(item3 => item3.key === item2.key);
                    if(customFieldsColumnsKeyWidthTemp){
                      customColumnsWidth = customColumnsWidth + customFieldsColumnsKeyWidthTemp.width;
                      item2.width = customFieldsColumnsKeyWidthTemp.width;
                    }
                    this.columns.push(item2);
                    return;
                  }
                });
              });
              //赋值标头名称
              this.allocationListFields.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item.parameterName === item2.key){
                    item.parameterTitle = item2.title;
                    return;
                  }
                });
              });
              //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
              if(screenWidthTemp>customColumnsWidth ){
                if(this.columns.length>2){
                  let columnsLength = this.columns.length-2;
                  customColumnsAvgWidth = Math.floor((screenWidthTemp-customColumnsWidth)/columnsLength);
                }
              }          
              this.columns.forEach(item=>{
                if(item.key != "action" && item.key != "firstColumnSelect"){
                  item.width = item.width+customColumnsAvgWidth;
                }
              });

            }else{
              this.columns = this.fixedColumns;
            }
          }
        }
      })
    },
    //查询
    queryClick() {
      this.query.pageNo = 1;
      this.query.source = this.query.source.trim();
      //this.query.source = this.query.source.replace(/\s/g, '');
      this.saveQuery = Object.assign({}, this.query);
      this.pageNo = 1;
      this.getList(this.query);
    },
    //新增任务
    addClick() {
      //lisence验证
      this.addBtnLoading = true
      this.jobCycleParam = false;
      this.$http.post("/lisence/check").then((res) => {
        if (res.code == 1) {
          console.log(this.defaultConfigList);
          if(this.tipActive == 3) {
            this.changeTipActive(4)

          }
            
          this.addBtnLoading = false
          this.$Modal.remove();
          let _self = this;
          this.flag = true;
          _self.btnType = true;
          _self.taskModal.type = 1;
          _self.btnValue = 2;
          _self.taskModal.data = {
            configType: null,
            isSpecial: "",
            sourceIp: "",
            dialTestSourcePort: _self.defaultConfigList.dialTestSourcePort,
            destIp: "",
            respondIp: "",
            destIpName: "",
            netState:0,
            dialTestUdpPort: _self.defaultConfigList.dialTestUdpPort,
            dialTestTcpPort: _self.defaultConfigList.dialTestTcpPort,
            dialTestStratTTL: _self.defaultConfigList.dialTestStratTTL,
            dialTestRoutePath: _self.defaultConfigList.dialTestTrace,
            lrDeThrValue: _self.defaultConfigList.packetLossDegradation,
            lrDeContinueNum: _self.defaultConfigList.packetLossDegradation2,
            lrDeReachPer: _self.defaultConfigList.packetLossDegradation3,
            orgId: "",
            // alarm_for_type:"默认",
            dialTestBeThrValue: _self.defaultConfigList.dialTestBe_thr_value,
            dialTestDeThrValue: _self.defaultConfigList.dialTestDe_thr_value,
            dialTestReBeThrValue:
              _self.defaultConfigList.dialTestRe_be_thr_value,
            dialTestReDeThrValue:
              _self.defaultConfigList.dialTestRe_de_thr_value,
            highSourcePort: _self.defaultConfigList.highSourcePort,
            highUdpPort: _self.defaultConfigList.highUdpPort,
            highTcpPort: _self.defaultConfigList.highTcpPort,
            highStartTTL: _self.defaultConfigList.highStartTTL,
            highRoutePath: _self.defaultConfigList.highRoutePath,
            highBeThrValue: _self.defaultConfigList.highBe_thr_value,
            highDeThrValue: _self.defaultConfigList.highDe_thr_value,
            highReBeThrValue: _self.defaultConfigList.highRe_be_thr_value,
            highReDeThrValue: _self.defaultConfigList.highRe_de_thr_value,
            lrEventReportFlag: _self.defaultConfigList.highPlFlag,
            lrEventReportRate:
              _self.defaultConfigList.highPacketLossDegradation,
            highWindow: _self.defaultConfigList.highWindow,
            highSum: _self.defaultConfigList.highSum,
            surgeAlarm: _self.defaultConfigList.surgeAlarm,
            highSurgeAlarm: _self.defaultConfigList.highSurgeAlarm,
            dialInterval:String(_self.defaultConfigList.dialTestFrequency) ,
            dialTestNum:String(_self.defaultConfigList.dialTestNum),
            dialTestSize:String(_self.defaultConfigList.dialTestSize),
            sens:_self.defaultConfigList.sens,
            delayThreshold:_self.defaultConfigList.delayThreshold,
          };
          _self.taskModal.academicmatic = {
            configType: null,
            isSpecial: "",
            sourceIp: "",
            sourcePort: _self.defaultConfigList.dialTestSourcePort,
            destIp: "",
            respondIp: "",
            destIpName: "",
            netState:0,
            destPort: _self.defaultConfigList.dialTestUdpPort,
            orgId: "",
            hIps: "",
            // alarm_for_type:"默认",
            beThrValue: _self.defaultConfigList.dialTestBe_thr_value,
            deThrValue: _self.defaultConfigList.dialTestDe_thr_value,
            reBeThrValue: _self.defaultConfigList.dialTestRe_be_thr_value,
            reDeThrValue: _self.defaultConfigList.dialTestRe_de_thr_value,
            tcpPort: _self.defaultConfigList.dialTestTcpPort,
            // autoDestPort_tp:'默认',
            limitHc: _self.defaultConfigList.dialTestTrace,
            startTTL: _self.defaultConfigList.dialTestStratTTL,
              sens:_self.defaultConfigList.sens,
            delayThreshold:_self.defaultConfigList.delayThreshold,
          };

          let data = Object.assign({}, this.taskModal.data);
          _self.taskModal.taskParam = {
            configType: null,
            isSpecial: "",
            sourceIp: "",
            sourcePort: data.dialTestSourcePort,
            destIp: "",
            respondIp: "",
            destIpName: "",
             netState:0,
            destType: '5',// 设备类型默认是 未知
            dynamicIp:0,
            interruptState: this.targetTypeArry.includes('5') ? '0' : '1',// 若配置的目标类型中包含未知则 默认勾选
            destPort: data.dialTestUdpPort,
            orgId: "",
            // alarm_for_type:"默认",
            beThrValue: data.dialTestBeThrValue,
            deThrValue: data.dialTestDeThrValue,
            lrDeThrValue: data.lrDeThrValue,
            lrDeContinueNum: data.lrDeContinueNum,
            lrDeReachPer: data.lrDeReachPer,
            reBeThrValue: data.dialTestReBeThrValue,
            reDeThrValue: data.dialTestReDeThrValue,
            tcpPort: data.dialTestTcpPort,
            // autoDestPort_tp:'默认',
            startTTL: data.dialTestStratTTL,
            limitHc: data.dialTestRoutePath,
            surgeAlarm: data.surgeAlarm,
            // 拨测参数设置
            dialInterval:String(data.dialInterval) ,
            packetNum:String(data.dialTestNum) ,
            packetSize:String(data.dialTestSize) ,
            sens:String(data.sens),
            delayThreshold:String(data.delayThreshold),
          };

          (this.taskAdjustForm = {
            weekShow: true,
            param: {
              start_date: new Date().format("yyyy-MM-dd"),
              end_date: new Date().format("2099-01-01"),
              start_time: "0000",
              end_time: "2359",
              timeList: [{ start: "0000", end: "2359" }],
              is_week: 1,
              week: [1, 2, 3, 4, 5, 6, 7],
            },
          }),
            (_self.taskModal.show = true);
          
        } else {
           // 没有lisence关闭界面导航
         
      
          this.addBtnLoading = false
          this.$Modal.warning({ title: this.$t('comm_tip'), 
                                content: res.msg,
                                onOk:() => {
                                  debugger
                                  if(this.tipStatus == 'true') {
                                this.changeTipStatus(false)
                                this.changeTipActive(null)
                                // 需要重新刷新一下页面
                                parent.location.reload()
          
         }
                                  

                                }
                                 });
         
          
        }
      });
    },
    btnClick(val) {
      this.taskModal.automatic = {};
      this.taskModal.taskParam = {};
      this.taskModal.highParam = {};
      this.taskModal.academicmatic = {};
      let data = Object.assign({}, this.taskModal.data);
      if (val === 1) {
        // data.alarm_for_type="默认";
        this.taskModal.automatic = {
          configType: null,
          isSpecial: "",
          sourceIp: "",
          sourcePort: data.dialTestSourcePort,
          destIp: "",
          respondIp: "",
          destIpName: "",
          destPort: data.dialTestUdpPort,
          orgId: "",
          // alarm_for_type:"默认",
          beThrValue: data.dialTestBeThrValue,
          deThrValue: data.dialTestDeThrValue,
          reBeThrValue: data.dialTestReBeThrValue,
          reDeThrValue: data.dialTestReDeThrValue,
          tcpPort: data.dialTestTcpPort,
          // autoDestPort_tp:'默认',
          startTTL: data.dialTestStratTTL,
          limitHc: data.dialTestRoutePath,
                    tosValue: data.tosValue,
        };
      } else if (val == 2) {
        // 普通拨测
        // data.alarm_for_type=1;
        this.taskModal.taskParam = {
          configType: null,
          isSpecial: "",
          sourceIp: "",
          sourcePort: data.dialTestSourcePort,
          destIp: "",
          respondIp: "",
          destIpName: "",
          destPort: data.dialTestUdpPort,
          orgId: "",
          // alarm_for_type:"默认",
          destType: '5',// 设备类型默认是 未知
          interruptState: this.targetTypeArry.includes('5') ? '0' : '1',// 若配置的目标类型中包含未知则 默认勾选
          beThrValue: data.dialTestBeThrValue,
          deThrValue: data.dialTestDeThrValue,
          lrDeThrValue: data.lrDeThrValue,
          lrDeContinueNum: data.lrDeContinueNum,
          lrDeReachPer: data.lrDeReachPer,
          reBeThrValue: data.dialTestReBeThrValue,
          reDeThrValue: data.dialTestReDeThrValue,
          tcpPort: data.dialTestTcpPort,
          // autoDestPort_tp:'默认',
          startTTL: data.dialTestStratTTL,
          limitHc: data.dialTestRoutePath,
          surgeAlarm: data.surgeAlarm,
          tosValue: data.tosValue,
        };
      } else if (val == 3) {
        this.taskModal.highParam = {
          configType: null,
          isSpecial: "",
          sourceIp: "",
          sourcePort: data.highSourcePort,
          destIp: "",
          respondIp: "",
          destIpName: "",
          destPort: data.highUdpPort,
          destType: '5',// 设备类型默认是 未知
          interruptState: this.targetTypeArry.includes('5') ? '0' : '1',// 若配置的目标类型中包含未知则 默认勾选
          orgId: "",
          // alarm_for_type:"默认",
          beThrValue: data.highBeThrValue,
          deThrValue: data.highDeThrValue,
          reBeThrValue: data.highReBeThrValue,
          reDeThrValue: data.highReDeThrValue,
          tcpPort: data.highTcpPort,
          // autoDestPort_tp:'默认',
          startTTL: data.highStartTTL,
          limitHc: data.highRoutePath,
          lrEventReportFlag: data.lrEventReportFlag,
          lrEventReportRate: data.lrEventReportRate,
          highWindow: data.highWindow,
          highSum: data.highSum,
          highSurgeAlarm: data.highSurgeAlarm,
                    tosValue: data.tosValue,
        };
      } else if (val == 4) {
        this.taskModal.academicmatic = {
          configType: null,
          isSpecial: "",
          sourceIp: "",
          sourcePort: data.dialTestSourcePort,
          destIp: "",
          respondIp: "",
          destIpName: "",
          destPort: data.dialTestUdpPort,
          orgId: "",
          hIps: "",
          // alarm_for_type:"默认",
          beThrValue: data.dialTestBeThrValue,
          deThrValue: data.dialTestDeThrValue,
          reBeThrValue: data.dialTestReBeThrValue,
          reDeThrValue: data.dialTestReDeThrValue,
          tcpPort: data.dialTestTcpPort,
          // autoDestPort_tp:'默认',
          startTTL: data.dialTestStratTTL,
          limitHc: data.dialTestRoutePath,
                    tosValue: data.tosValue,
        };
      }
      this.btnValue = val;
    },
    addTaskOk(refName) {
      let _self = this;
      let paramVerifyState = true;
      this.$refs[refName].$refs[refName].validate((valid) => {
        if (valid) {
          let paramVerify = this.$refs[refName].paramVerify();
          if(!paramVerify){
              paramVerifyState = false;
          }else{
            paramVerifyState = true;
          }
        } else {
          paramVerifyState = false;
        }
      });

      debugger

      if(!paramVerifyState){
        return false;
      }

      let param = this.$refs[refName].getParams();
      console.log("提交的参数为：",param);
      
      if (_self.btnType == true) {
        // 新增
        this.$refs["taskAdjustForm"].validate((valid) => {
          if (valid) {
            let week = "";
            if (this.taskAdjustForm.weekShow) {
              if (this.taskAdjustForm.param.week.length > 0) {
                for (
                  let i = 0;
                  i < this.taskAdjustForm.param.week.length;
                  i++
                ) {
                  if (i === 0) {
                    week += this.taskAdjustForm.param.week[i];
                  } else {
                    week += "," + this.taskAdjustForm.param.week[i];
                  }
                }
              } else {
                this.$Message.warning({ content: this.$t('comm_tip2'), background: true });
                this.taskModal.loading = false;
                this.$nextTick(() => {
                  this.taskModal.loading = true;
                });
                return;
              }
            }
            //时间段验证
            const ruleTimeFlag = this.ruleTimes(this.taskAdjustForm.param.timeList);
            if (ruleTimeFlag === false) {
              this.$Message.warning({ content: this.$t('comm_tip3'), background: true });
              return
            }
            let timeList = this.taskAdjustForm.param.timeList.map(item => {
              let str = "";
              str = item.start.replace(":", "") + "-" + item.end.replace(":", "");
              return str
            })
            param.timeList = timeList;
            param.startDate = new Date(
              this.taskAdjustForm.param.start_date
            ).format("yyyyMMdd");
            param.endDate = new Date(this.taskAdjustForm.param.end_date).format(
              "yyyyMMdd"
            );
            param.isWeek = this.taskAdjustForm.weekShow ? 1 : 0;
            param.week = this.taskAdjustForm.weekShow ? week : "";

            let source_port = "";
            let dest_port = "";
            
            switch (this.btnValue) {
              case 1:
                source_port = "";
                break;
              case 2:
                switch (param.configType) {
                  case 2:
                    source_port = "";
                    param.sourcePort = '';
                    param.destPort = '';
                    param.sipPro='';
                    break;
                  case 5:
                    source_port = "";
                    param.sourcePort = '';
                    param.destPort = '';
                    param.sipPro='';
                    break;
                  case 4:
                    source_port = param.sourcePort;
                    //param.sipPro=1;
                    break;
                  case 7:
                    source_port = param.sourcePort;
                    param.sipPro='';
                    break;
                }
                break;
              case 3:
                switch (param.configType) {
                  case 2:
                    source_port = "";
                    break;
                  case 1:
                    source_port = param.sourcePort;
                    break;
                  case 3:
                    source_port = param.sourcePort;
                    break;
                }
                break;
              case 4:
                switch (param.configType) {
                  case 2:
                    source_port = "";
                    break;
                  case 1:
                    source_port = param.sourcePort;
                    break;
                  case 3:
                    source_port = param.sourcePort;
                    break;
                }
                break;
            }
            switch (this.btnValue) {
              case 1:
                switch (param.configType) {
                  case 1:
                    dest_port = "";
                    break;
                  case 6:
                    dest_port = param.destPort;
                    break;
                }
                break;
              case 2:
                switch (param.configType) {
                  case 2:
                    dest_port = "";
                    break;
                  case 5:
                    dest_port = "";
                    break;
                  case 4:
                    dest_port = param.destPort;
                    break;
                  case 7:
                    dest_port = param.destPort;
                    break;
                }
                break;
              case 3:
                switch (param.configType) {
                  case 2:
                    dest_port = "";
                    param.sourcePort = '';
                    param.destPort = '';
                    break;
                  case 1:
                    dest_port = param.destPort;
                    break;
                  case 3:
                    dest_port = param.destPort;
                    break;
                }
                break;
              case 4:
                switch (param.configType) {
                  case 2:
                    dest_port = "";
                    param.sourcePort = '';
                    param.destPort = '';
                    break;
                  case 1:
                    dest_port = param.destPort;
                    break;
                  case 3:
                    dest_port = param.destPort;
                    break;
                }
                break;
            }
            let parmaValidate = {
              sourceIp: param.sourceIp,
              sourcePort: source_port,
              destIp: param.destIp,
              respondIp: param.respondIp,
              destIpName: param.destIpName,
              destPort: dest_port,
              configType: param.configType,
              orgId: param.orgId,
              routePath: param.limitHc,
            };
            // ;
            console.log("param.sourceIp.key:" + param.sourceIp.key);
            let parmaValidate2 = this.deepClone(parmaValidate);
            if (this.btnValue == 3) {
              parmaValidate2.routePath =
                parmaValidate2.configType + "-" + parmaValidate2.routePath;
              parmaValidate2.configType = 3;
            } else if (this.btnValue == 4) {
              parmaValidate2.routePath =
                (param.hIps == undefined ||
                  param.hIps == null ||
                  param.hIps == ""
                  ? ""
                  : param.hIps) +
                "-" +
                parmaValidate2.configType +
                "-" +
                parmaValidate2.routePath;
              parmaValidate2.configType = 8;
            }

            let url = "";
            if (_self.btnType) {
              if (this.btnValue === 1) {
                url = "/target/addSysDialConfig";
              } else if (this.btnValue === 2) {
                url = "/probetask/addDialConfig";
              } else if (this.btnValue === 3) {
                url = "/probetask/addHighDialConfig";
              } else if (this.btnValue === 4) {
                url = "/sys/addDialConfigAClass";
              }

              if (this.flag) {
                if (this.btnValue != 4) {
                  const limitHcN = param.limitHc;
                  let { limitHc, ...param2 } = param;
                  param2.routePath = limitHcN;
                  this.$delete(param2, "createTime");
                  this.saveTask(url, param2, refName);
                } else if (this.btnValue == 4) {
                  let param2 = this.deepClone(param);
                  param2.routePath =
                    (param2.hIps == undefined ||
                      param2.hIps == null ||
                      param2.hIps == ""
                      ? ""
                      : param2.hIps) +
                    "-" +
                    param2.configType +
                    "-" +
                    param2.startTTL +
                    "-" +
                    param2.limitHc +
                    "--";
                  param2.routePath = param2.routePath.replace(
                    /,|，|。|;|；/g,
                    "|"
                  );
                  param2.configType = 8;
                  let { hIps, ...param3 } = param2;
                     this.$delete(param3, "createTime");
                  this.saveTask(url, param3, refName);
                }
              }

            }
          } else {
            this.$Message.error({ content: res.msg, background: true });
            this.getList(this.query);
            this.taskModal.loading = false;
            this.$nextTick(() => {
              this.taskModal.loading = true;
            });
          }
        });
      } else if (_self.btnType == false) {
        // 普通拨测的修改操作
        let url = "/probetask/update";

        if(param.taskType == 2){
          // 高频拨测的修改接口
            url =  "/probetask/updateHighDialConfig";
        }

        console.log("param---- " , param)

        param.destType = param.destType == "0" ?  "" : param.destType;

        if (this.btnValue != 4) {
          let week = "";
          if (this.taskAdjustForm.weekShow) {
            if (this.taskAdjustForm.param.week.length > 0) {
              for (let i = 0; i < this.taskAdjustForm.param.week.length; i++) {
                if (i === 0) {
                  week += this.taskAdjustForm.param.week[i];
                } else {
                  week += "," + this.taskAdjustForm.param.week[i];
                }
              }
            } else {
              this.$Message.warning({ content: this.$t('comm_tip2'), background: true });
              this.taskModal.loading = false;
              this.$nextTick(() => {
                this.taskModal.loading = true;
              });
              return;
            }
          }
          //时间段验证
          const ruleTimeFlag = this.ruleTimes(this.taskAdjustForm.param.timeList);
          console.log(this.taskAdjustForm.param.timeList);
          if (ruleTimeFlag === false) {
            this.$Message.warning({ content: this.$t('comm_tip3'), background: true });
            return
          }
          let timeList = this.taskAdjustForm.param.timeList.map(item => {
            let str = "";
            str = item.start.replace(":", "") + "-" + item.end.replace(":", "");
            return str
          })
          param.timeList = timeList;
          if (!this.taskAdjustForm.param.start_date) {
            this.$Message.warning(
              { content:  this.$t('comm_start_date'), background: true }
            );
            return
          }

          if (!this.taskAdjustForm.param.end_date) {
            this.$Message.warning(
              { content:  this.$t('comm_deadline'), background: true }
            );
            return
          }
          param.startDate = new Date(
            this.taskAdjustForm.param.start_date
          ).format("yyyyMMdd");
          param.endDate = new Date(this.taskAdjustForm.param.end_date).format(
            "yyyyMMdd"
          );


          param.isWeek = this.taskAdjustForm.weekShow ? 1 : 0;
          param.week = this.taskAdjustForm.weekShow ? week : "";
          if (param.taskType == 2) {
            param.surgeAlarm = param.highSurgeAlarm;
          }
          param.getherCode = this.editGetherCode;

          const limitHcN = param.limitHc;
          let { limitHc, ...param2 } = param;
          param2.routePath = limitHcN;
          param2.destType = param2.destType || "";
          this.$delete(param2, "updateTs");
          this.$delete(param2, "createTime");
          this.saveTask(url, param2, refName);
        } else if (this.btnValue == 4) {
          let param2 = this.deepClone(param);
          param2.routePath =
            (param2.hIps == undefined ||
              param2.hIps == null ||
              param2.hIps == ""
              ? ""
              : param2.hIps) +
            "-" +
            param2.configType +
            "-" +
            param2.startTTL +
            "-" +
            param2.routePath +
            "--";
          param2.routePath = param2.routePath.replace(/,|，|。|;|；/g, "|");
          param2.configType = 8;
          param2.destType = param2.destType || "";
          let { hIps, ...param3 } = param2;
           this.$delete(param3, "createTime");
          this.saveTask(url, param3, refName);
        }
      }
    },
    deepClone(source) {
      var t = this.type(source),
        o,
        i,
        ni;
      if (t === "array") {
        o = [];
      } else if (t === "object") {
        o = {};
      } else {
        return source;
      }
      if (t === "array") {
        for (i = 0, ni = source.length; i < ni; i++) {
          o.push(this.deepClone(source[i]));
        }
        return o;
      } else if (t === "object") {
        for (i in source) {
          o[i] = this.deepClone(source[i]);
        }
        return o;
      }
    },
    type(obj) {
      var toString = Object.prototype.toString;
      var map = {
        "[object Boolean]": "boolean",
        "[object Number]": "number",
        "[object String]": "string",
        "[object Function]": "function",
        "[object Array]": "array",
        "[object Date]": "date",
        "[object RegExp]": "regExp",
        "[object Undefined]": "undefined",
        "[object Null]": "null",
        "[object Object]": "object",
      };
      return map[toString.call(obj)];
    },
    saveTask(url, param, refName) {
      if (param.routePath == undefined || param.routePath == null) {
        param.routePath = "";
      }
      if (this.btnValue === 3) {
        param.routePath =
          "" -
          +param.configType +
          "-" +
          param.startTTL +
          "-" +
          param.routePath +
          "-" +
          (param.highSum || 3) +
          "-" +
          param.highWindow;
        param.configType = 3;
        param.surgeAlarm = param.highSurgeAlarm;
      } else if (this.btnValue === 2 || this.btnValue === 1) {
        param.routePath = "--" + param.startTTL + "-" + param.routePath + "--";
      }
      this.btnLoading = true
      this.$http.wisdomPost(url, param).then((res) => {
        if (res.code === 1) {
          if (this.btnValue === 1) {
            this.$Message.success(
              { content:  this.$t('task_add_edit_tips_automatic_dial') + (this.btnType ? this.$t('task_add_edit_tips_but_add') : this.$t('task_add_edit_tips_but_edit'))+ this.$t('comm_success'), background: true }
            );
          } else if (this.btnValue === 2) {
            this.$Message.success(
              { content:  this.$t('task_add_edit_tips_ordinary_dial') + (this.btnType ? this.$t('task_add_edit_tips_but_add') : this.$t('task_add_edit_tips_but_edit'))+this.$t('comm_success'), background: true }
            );
          } else if (this.btnValue === 3) {
            this.$Message.success(
              { content:  this.$t('comm_high_freq_monitoring') + (this.btnType ? ' '+this.$t('but_add') : ' '+this.$t('but_edit')) + ' '+this.$t('comm_success'), background: true }
            );
          } else if (this.btnValue === 4) {
            this.$Message.success(
              { content:  this.$t('group_ClassA') + (this.btnType ? ' '+this.$t('but_add') : ' '+this.$t('but_edit')) +' '+this.$t('comm_success'), background: true }
            );
          }
          this.flag = false;
          this.taskModal.loading = false;
          this.taskModal.show = false;
          this.changeTipActive(7)
          this.$nextTick(() => {
            //this.taskModal.loading=true;
          });
          this.pageNo = 1;
          this.getList(this.query);
          this.addTaskCancel(refName);
          this.getDeviceTypeList();
        } else {
          this.$Message.error({ content: res.msg, background: true });
          this.taskModal.loading = false;
          this.$nextTick(() => {
            this.taskModal.loading = true;
          });
        }
      }).finally(() => {
        this.btnLoading = false
        this.getGroupList()
      });
      //this.taskModal.loading=false;
      // this.taskModal.show=false;
      this.$nextTick(() => {
        //this.taskModal.loading=true;
      });
    },
    async getDeviceTypeList() {
      this.deviceTypeList = [];
      await this.$http
        .wisdomPost("/deviceType/queryList")
        .then(({ code, data, msg }) => {
          if (code === 1) {
            var newdeviceTypeList = [];
            for (let index = 0; index < data.length; index++) {
              var newdeviceType = {"value":data[index].id,"lable":data[index].name}
              newdeviceTypeList.push(newdeviceType);
            }
            this.deviceTypeList = newdeviceTypeList ?? [];
          } else {
            this.deviceTypeList = [];
            this.$Message.warning(msg);
          }
        })
        .catch((err) => {
          this.deviceTypeList = [];
          throw new Error(err);
        });
    },
    addTaskCancel(refName) {
      this.taskModal.show = false;
      this.jobCycleParam = false;
      this.$Modal.visible = false;
      this.$refs[refName].$refs[refName].resetFields();
      this.btnValue = 1;
      this.taskModal.data = {};
      this.taskModal.automatic = {};
      this.taskModal.taskParam = {};
      this.taskModal.type = "";
    },
    // 查看详情
    showDetail(rowData){
      this.$refs.detailModal.row=rowData
      this.$refs.detailModal.indexQueryClick()
      this.detailShow = true
    },
    //操作事件
    actionClick(row, type, event) {
      let _self = this;
      switch (type) {
        case "show": //指标
        // 查看详情
          if (sessionStorage.getItem("delayPs")) {
            sessionStorage.removeItem("delayPs");
          }
          this.$store.commit("setdelayLTlevel", "");
          this.$store.commit("updateDelayLossHistory", -1);
          this.$store.commit("setflowTlevel", "");
          this.$store.commit("updateFlowHistory", -1);
          _self.indexModal.data = [];
          _self.detailShow = true;
          _self.taskNum = row.taskNum;
          _self.rowData = {...row}
          // 先去 路径表的showSourceIp,然后获取 任务的 SourceIp 地址
          _self.indexModal.data = {
            flowChartType: 1,
            sourceIp: row.linkSourceIp || row.showSourceIp,
            destIp: row.showDestIp,
            respondIp: row.respondIp,
            startTime: "",
            endTime: "",
            taskType:row.configType,
            eventStart: row.eventStart || "",
            eventEnd: row.eventEnd || "",
            id:row.id,
          };
          this.echartIsSpecial = row.isSpecial == 1 ? true : false;
          break;
        case "threshold": //告警阈值
          _self.threshold.data = {
            beThrValue: row.beThrValue,
            deThrValue: row.deThrValue,
            reBeThrValue: row.reBeThrValue,
            reDeThrValue: row.reDeThrValue,
          };
          _self.threshold.show = true;
          break;
        case "modify": //修改
        // 如果在界面导航,禁止修改
        if(this.tipStatus == 'true') {
          return
        }
        // 却换到简单模式
          _self.jobCycleParam = false;
          _self.btnType = false;
          _self.taskModal.type = 2;
          _self.taskModal.automatic = {};
          _self.taskModal.taskParam = {};
          _self.taskModal.highParam = {};
          _self.taskModal.academicmatic = {};
          _self.editGetherCode = row.getherCode;
          let weekArr = [];
          if (row.week) {
            if (row.week.indexOf(",") > 0) {
              const arr = row.week.split(",");
              for (let i = 0; i < arr.length; i++) {
                weekArr.push(Number(arr[i]));
              }
            } else {
              weekArr = [Number(row.week)];
            }
          }
          if (row.startDate != null && row.startDate != "") {
            let startDateStr = String(row.startDate);
            let startDateValue =
              startDateStr.substring(0, 4) +
              "-" +
              startDateStr.substring(4, 6) +
              "-" +
              startDateStr.substring(6, 8);
            this.taskAdjustForm.param.start_date = new Date(startDateValue);
          }
          if (row.endDate != null && row.endDate != "") {
            let endDateStr = String(row.endDate);
            let endDateValue =
              endDateStr.substring(0, 4) +
              "-" +
              endDateStr.substring(4, 6) +
              "-" +
              endDateStr.substring(6, 8);
            this.taskAdjustForm.param.end_date = new Date(endDateValue);
          }
          const timeList = row.timeList ? row.timeList.split(',') : ['0000-2359'];
          this.taskAdjustForm.param.timeList = timeList.map(item => {
            let timeItem = {};
            timeItem.start = item.split("-")[0];
            timeItem.end = item.split("-")[1];
            return timeItem
          })
          this.taskAdjustForm.param.end_time = row.endTime;
          this.taskAdjustForm.param.is_week = row.isWeek;
          this.taskAdjustForm.weekShow = row.isWeek === 1 ? true : false;
          this.taskAdjustForm.param.week = weekArr;

          if (row.configType == 1 || row.configType == 6) {
            _self.btnValue = 1;
            let { children, ...param } = row;
            param.startTTL = param.routePath.split("-")[2];
            param.limitHc = param.routePath.split("-")[3];
            _self.taskModal.automatic = param;
          } else if (
            row.configType == 2 ||
            row.configType == 4 ||
            row.configType == 5 ||
            row.configType == 7
          ) {
            _self.btnValue = 2;
            row.surgeAlarm = String(row.surgeAlarm);
            row.dynamicIp = String(row.dynamicIp);
            let { children, ...param } = row;
            param.startTTL = param.routePath.split("-")[2];
            param.limitHc = param.routePath.split("-")[3];
            _self.taskModal.taskParam = param;
          } else if (row.configType == 3) {
            row.highSurgeAlarm = (row.surgeAlarm != undefined || row.surgeAlarm != null) ? String(row.surgeAlarm) : '0';

            _self.btnValue = 3;
            let { children, ...param } = row;

            if (param.routePath.indexOf("-") >= 0) {
              param.configType = Number(param.routePath.split("-")[1]) || 3;
              param.startTTL =
                param.routePath.split("-")[2] ||
                this.defaultConfigList.dialTestStratTTL;
              param.limitHc =
                param.routePath.split("-")[3] ||
                this.defaultConfigList.dialTestTrace;
              param.highSum =
                param.routePath.split("-")[4] || this.defaultConfigList.highSum;
              param.highWindow =
                param.routePath.split("-")[5] ||
                this.defaultConfigList.highWindow;
            } else {
              param.configType = 3;
              param.startTTL = this.defaultConfigList.dialTestStratTTL;
              param.limitHc = this.defaultConfigList.dialTestTrace;
              param.highSum = this.defaultConfigList.highSum;
              param.highWindow = this.defaultConfigList.highWindow;
            }
            _self.taskModal.highParam = param;
          } else if (row.configType == 8) {
            _self.btnValue = 4;
            // let param = this.deepClone(row);
            let { children, ...param } = row;
            if (param.routePath.indexOf("-") >= 0) {
              param.hIps = param.routePath.split("-")[0].replace(/\|/g, ",");
              param.configType = Number(param.routePath.split("-")[1]);
              param.startTTL = param.routePath.split("-")[2];
              param.limitHc = param.routePath.split("-")[3];
            } else {
              param.configType = 3;
              param.startTTL = this.defaultConfigList.dialTestStratTTL;
              param.limitHc = this.defaultConfigList.dialTestTrace;
              param.hIps = "";
            }
            _self.taskModal.academicmatic = param;
          }
          // //判断探针和目标端口
          if (
            row.sourcePort != _self.defaultConfigList.sr_port &&
            row.destPort === _self.defaultConfigList.dest_port
          ) {
            _self.taskModal.taskParam.sourcePortId = 2;
            _self.taskModal.taskParam.destPortId = 1;
          } else if (
            row.sourcePort === _self.defaultConfigList.sr_port &&
            row.destPort != _self.defaultConfigList.dest_port
          ) {
            _self.taskModal.taskParam.sourcePortId = 1;
            _self.taskModal.taskParam.destPortId = 2;
          } else if (
            row.sourcePort === _self.defaultConfigList.sr_port &&
            row.destPort === _self.defaultConfigList.dest_port
          ) {
            _self.taskModal.taskParam.sourcePortId = 1;
            _self.taskModal.taskParam.destPortId = 1;
          } else if (
            row.sourcePort != _self.defaultConfigList.sr_port &&
            row.destPort != _self.defaultConfigList.dest_port
          ) {
            _self.taskModal.taskParam.sourcePortId = 2;
            _self.taskModal.taskParam.destPortId = 2;
          }
          _self.taskModal.highParam.interruptState = String(row.interruptState ?? 0)
          _self.taskModal.highParam.destType = String(row.destType ?? "")
          _self.taskModal.taskParam.interruptState = String(row.interruptState ?? 0)
          _self.taskModal.taskParam.destType = String(row.destType ?? "")
          _self.taskModal.taskParam.sipPro = Number(row.sipPro)
          _self.taskModal.taskParam.dynamicIp = Number(row.dynamicIp)
          _self.taskModal.taskParam.dialInterval = String(row.dialInterval)
          _self.taskModal.taskParam.packetNum = String(row.packetNum)
          _self.taskModal.taskParam.packetSize = String(row.packetSize)
          _self.taskModal.taskParam.tosValue = Number(row.tosValue)
          console.log('_self.taskModal.taskParam',_self.taskModal.taskParam);
          _self.taskModal.show = true;
          break;
      }
    },
    //清除查询条件
    resetQuery() {
      this.query = {
        groupIds: [],
        orgId: "",
        startState: "",
        taskStatus: "",
        sourcePort: "",
        isSpecial: "",
        source: "",
        pageNo: 1,
        pageSize: 10,
      };
      this.pageNo = 1;
      this.pageSize = 10;
      this.getList(this.query);
    },
    //详情关闭
    thresholdCancel(refName) {
      this.$Modal.visible = false;
      this.$refs[refName].$refs[refName].resetFields();
      this.threshold.data = {};
    },
    //分页
    pageChange(page) {
      console.log(page);
      this.pageNo = page;
      this.query.pageNo = page;
      this.getList(this.query);
    },
    pageSizeChange(pageSize) {
      this.pageNo = this.query.pageNo = 1;
      this.pageSize = pageSize;
      this.query.pageSize = pageSize;
      this.getList(this.query);
    },
    sortSum(column) {
      console.log(column);
      // "normal"
      this.query.fieldName = "";
      this.query.orderBy = "";
      if(column.order !== "normal"){
        this.query.fieldName = column.key;
        this.query.orderBy = column.order;
      }
      if(this.query.fieldName == "runState"){
        this.query.sortRunState = "runStateSort";
        this.query.sortRunStateOrderBy =this.query.orderBy;
        this.query.fieldName = "";
        this.query.orderBy = "";
      }
      this.getList(this.query);

    },
    importClick() {
      this.opretionBtn = ''
      this.$http.post("/lisence/check").then((res) => {
        if (res.code == 1) {
          this.showImport = true;
          this.licenceNum = res.data.addNum;
        } else if(res.code == 54){
          // lisence 过期
           this.$Message.warning({ content: res.msg,background: true });
           this.showImport = false; 
        }else {
            this.showImport = true;
            let addNum = 0;
            if(res.data && res.data.addNum){
                addNum = res.data.addNum;
            }
            this.licenceNum = addNum;
            console.log(res.msg)
        }
      });
    },
    closeImport(v) {
      this.showImport = false;
      if (v === "ok") {
        this.getDeviceTypeList();
        this.getGroupList();
        // this.getClientList();
        this.pageNo = 1;
        this.getList(this.query);
      }
    },
       // 清空勾选框
    clearSelectData(){
      this.$refs.tableList.selectAll(false)
      this.selectedIds=new Set();
      this.selectedDatas=new Set();
    },
    //删除
    deleteClick() {
      let idArr = Array.from(this.selectedIds);
      let selectedDatas = Array.from(this.selectedDatas);
      let flag = true;
      let param = {
        ids: idArr.join(','),
      };
      selectedDatas.forEach(item=>{
        if (JSON.parse(item).isPaisheng == 2) {
            flag = false;
        }
      })
      if (idArr.length > 0) {
        if (!flag) {
          this.$Message.warning({ content: this.$t("spec_tip"), background: true });
          return
        }
        top.window.$iviewModal.confirm({
          title: this.$t('common_delete_prompt'),
          content: this.$t('discover_msg_delete'),
          onOk: () => {
            this.loading = true;
            this.$http.wisdomPost("/probetask/delete", param).then((res) => {
              if (res.code === 1) {
                this.$Message.success({ content: this.$t('comm_deleted_success'), background: true });
                this.query.pageNo = 1;
                this.pageNo = 1;
                this.clearSelectData()
                this.getList(this.query);
              } else {
                this.$Message.error({ content: res.msg, background: true });
              }
              this.loading = false;
            });
          },
        });
      } else {
        this.$Message.warning({ content: this.$t('group_select_delete'), background: true });
      }
    },
 
    //暂停
    suspendClick() {
      let idArr = Array.from(this.selectedIds);
      let selectedDatas = Array.from(this.selectedDatas);
      let flag = true;
      selectedDatas.forEach(item=>{
        if (JSON.parse(item).isPaisheng == 2) {
            flag = false;
        }
      })
      let param = {
        ids: idArr.join(','),
        startState: 0,
      };
      if (idArr.length > 0) {
        if (!flag) {
          this.$Message.warning({ content: this.$t('spec_tip'), background: true });
          return
        }
        this.changeStatus(param);
      } else {
        this.$Message.warning({ content: this.$t('please_select_suspended_data'), background: true });
      }
    },
    // 启动
    startClick() {
      let idArr = Array.from(this.selectedIds);
      let selectedDatas = Array.from(this.selectedDatas);
      let flag = true;
      let param = {
        ids: idArr.join(','),
        startState: 1,
      };
      if (idArr.length > 0) {
        selectedDatas.forEach(item=>{
          if (JSON.parse(item).isPaisheng == 2) {
              flag = false;
          }
        })
        if (!flag) {
          this.$Message.warning({ content: this.$t('spec_tip'), background: true });
          return
        }
        this.changeStatus(param);
      } else {
        this.$Message.warning({ content: this.$t('please_select_startup_data'), background: true });
      }
    },
    //告警批量设置
    alarmSetClick() {
      
      let idArr = Array.from(this.selectedIds);
      let selectedDatas = Array.from(this.selectedDatas);
      let flag = true;
      if (selectedDatas.length < 1) {
        this.$Message.warning({ content: this.$t('specquality_select'), background: true });
        return;
      } 
      selectedDatas.forEach(item=>{
        if (JSON.parse(item).isPaisheng == 2) {
            flag = false;
        }
      })
      if (!flag) {
        this.$Message.warning({ content: this.$t('spec_tip'), background: true });
        return
      }
      this.alarm.modal.show = true;
      this.alarm.loading = true;
      this.alarm.param.beThrValue = this.defaultConfigList.dialTestBe_thr_value;
      this.alarm.param.deThrValue = this.defaultConfigList.dialTestDe_thr_value;
      this.alarm.param.lrDeThrValue = this.defaultConfigList.packetLossDegradation;
      this.alarm.param.lrDeContinueNum = this.defaultConfigList.packetLossDegradation2;
      this.alarm.param.lrDeReachPer = this.defaultConfigList.packetLossDegradation3;
      this.alarm.param.highWindow = this.defaultConfigList.highWindow;
      this.alarm.param.highSum = this.defaultConfigList.highSum;
      this.alarm.param.lrEventReportRate = this.defaultConfigList.highPacketLossDegradation;
      this.alarm.param.lrEventReportFlag = String(
        this.defaultConfigList.highPlFlag
      );
      this.alarm.param.surgeAlarm = String(this.defaultConfigList.surgeAlarm);
      // 默认为 1 ,不选中
      let interruptState = '1';
      let highInterruptState = '1';
      if (selectedDatas.length == 1) {
        if (selectedDatas[0].taskType == 3) {
          // 高频
          highInterruptState = JSON.parse(selectedDatas[0]).interruptState;
        } else {
          interruptState = JSON.parse(selectedDatas[0]).interruptState;
        }
      }
      this.alarm.param.interruptState = String(interruptState);
      this.alarm.param.highInterruptState = String(highInterruptState);
      this.alarm.param.highSurgeAlarm = String(
        this.defaultConfigList.highSurgeAlarm
      );
    },
    //作业调整
    taskAdjust() {
      let idArr = Array.from(this.selectedIds);
      let selectedDatas = Array.from(this.selectedDatas);
      let flag = true;
      this.taskAdjustModal.show = false;
      this.taskAdjustModal.loading = true;
      if (idArr.length > 0) {
        selectedDatas.forEach(item=>{
          JSON.parse(item).isPaisheng == 2?flag = false:true
        })
        if (!flag) {
          this.$Message.warning({ content: this.$t('spec_tip'), background: true });
          return
        }
        this.$refs["taskAdjustModal"].resetFields();
        this.taskAdjustModal.weekShow = true;
        this.taskAdjustModal.param = {
          start_date: new Date().format("yyyyMMdd"),
          end_date: new Date("2099-01-01").format("yyyyMMdd"),
          start_time: "0000",
          end_time: "2359",
          is_week: 1,
          timeList: [{ start: "0000", end: "2359" }],
          week: [1, 2, 3, 4, 5, 6, 7],
        };
        this.taskAdjustModal.show = true;
      } else {
        this.$Message.warning({ content: this.$t('comm_select_data'), background: true });
        this.taskAdjustModal.show = false;
      }
    },
    // 高频告警参数验证
    highParamVerify(paramData){
      var regx = /^\d+$/;
      var value = "";

      // 窗口大小
      var highWindow = paramData.highWindow;
      if(highWindow === ''){
        this.$Message.warning({ content: this.$t('warning_window_not_none'), background: true })
        return false;
      }
      if(!regx.test(highWindow)){
        this.$Message.warning({ content: this.$t('warning_window_size'), background: true })
        return false;
      }
      value = Number(highWindow);
      if(value < 1 || value > 60){
        this.$Message.warning({ content: this.$t('warning_window_size'), background: true })
        return false;
      }


      // 丢包告警生成阈值

        var lrEventReportFlag = paramData.lrEventReportFlag;
        if(lrEventReportFlag == 1){
            var lrEventReportRate = paramData.lrEventReportRate;
            if(lrEventReportRate === ''){
              this.$Message.warning({ content: this.$t('warning_packet_loss'), background: true })
              return false;
            }
            if(!regx.test(lrEventReportRate)){
              this.$Message.warning({ content: this.$t('warning_packet_loss100'), background: true })
              return false;
            }
            value = Number(lrEventReportRate);
            if(value < 1 || value > 100){
              this.$Message.warning({ content: this.$t('warning_packet_loss100'), background: true })
              return false;
            }
        }

    

      return true;

    },
    // 参数验证
    paramVerify(paramData){
      var regx = /^\d+$/;
      var value = "";

      // 中断生成阈值
      var beThrValue = paramData.beThrValue;
      if(beThrValue === ''){
        this.$Message.warning({ content: this.$t('warning_interrupt_empty'), background: true })
        return false;
      }
      if(!regx.test(beThrValue)){
        this.$Message.warning({ content: this.$t('warning_interrupt_1000'), background: true })
        return false;
      }
      value = Number(beThrValue);
      if(value < 0 || value > 1000){
        this.$Message.warning({ content: this.$t('warning_interrupt_1000'), background: true })
        return false;
      }

      //  时延劣化生成阈值

      var deThrValue = paramData.deThrValue;
      if(deThrValue === ''){
        this.$Message.warning({ content: this.$t('warning_degradation_empty'), background: true })
        return false;
      }
      if(!regx.test(deThrValue)){
        this.$Message.warning({ content: this.$t('warning_degradation_1000'), background: true })
        return false;
      }
      value = Number(deThrValue);
      if(value < 0 || value > 1000){
        this.$Message.warning({ content: this.$t('warning_degradation_1000'), background: true })
        return false;
      }


        //  时延劣化生成阈值

      var lrDeThrValue = paramData.lrDeThrValue;
      if(lrDeThrValue === ''){
        this.$Message.warning({ content: this.$t('warning_empty_once'), background: true })
        return false;
      }
      if(!regx.test(lrDeThrValue)){
        this.$Message.warning({ content: this.$t('warning_loss_100'), background: true })
        return false;
      }
      value = Number(lrDeThrValue);
      if(value < 1 || value > 100){
        this.$Message.warning({ content: this.$t('warning_loss_100'), background: true })
        return false;
      }


      // 连续：
      var lrDeContinueNum = paramData.lrDeContinueNum;
      if(lrDeContinueNum ===''){
        this.$Message.warning({ content: this.$t('warning_loss_empty'), background: true })
        return false;
      }
      if(!regx.test(lrDeContinueNum)){
        this.$Message.warning({ content: this.$t('warning_loss_1000'), background: true })
        return false;
      }
      value = Number(lrDeContinueNum);
      if(value < 0 || value > 1000){
        this.$Message.warning({ content: this.$t('warning_loss_1000'), background: true })
        return false;
      }

      // 连续达到
      var lrDeReachPer = paramData.lrDeReachPer;
      if(lrDeReachPer === ''){
        this.$Message.warning({ content: this.$t('warning_loss_continuously_empty'), background: true })
        return false;
      }
      if(!regx.test(lrDeReachPer)){
        this.$Message.warning({ content: this.$t('warning_loss_continuously_100'), background: true })
        return false;
      }
      value = Number(lrDeReachPer);
      if(value < 1 || value > 100){
        this.$Message.warning({ content: this.$t('warning_loss_continuously_100'), background: true })
        return false;
      }


      return true;

    },

    alarmOk() {
      
      let paramVerifyState = true , 
      type = this.alarm.param.type;
      if(type === 1){
          paramVerifyState = this.paramVerify(this.alarm.param);
      }else if(type === 2){
        paramVerifyState = this.highParamVerify(this.alarm.param);
      }
      if(!paramVerifyState){
        // this.alarm.loading = false;
        // this.alarm.modal.show = true;
          return true;
      }
      let checkedDataArr = Array.from(this.selectedDatas).map(item=>JSON.parse(item));
      const getIdsData = (data) => {
        const dataArr = data || [];
        let getAccordData = [];
        if (type === 1) {
          //寻找普通拨测任务
          getAccordData = dataArr.filter((item) => {
            //1,3,6,8
            return (
              item.configType != 1 &&
              item.configType != 3 &&
              item.configType != 6 &&
              item.configType != 8
            );
          });
        } else if (type === 2) {
          //寻找高频任务
          getAccordData = dataArr.filter((item) => {
            return item.configType == 3;
          });
        }
        if (getAccordData.length < 1) {
          return [];
        } else {
          let idsArr = [];
          getAccordData.forEach((item) => {
            idsArr.push(item.id);
          });
          return idsArr;
        }
      };
      let idsArray = [];
      if (checkedDataArr.length < 1) {
        //没有选择则对整个列表数据进行修改
        // idsArray = getIdsData(this.originalList);
        this.$Message.warning({ content: this.$t('warning_select_want_export'), background: true });
        return
      } else {
        //对已选择的数据进行修改
        idsArray = getIdsData(checkedDataArr);
      }
      if (idsArray.length > 0) {
        let ids = "";
        idsArray.forEach((item) => {
          ids += item + ",";
        });
        ids = ids.substring(0, ids.length - 1);
        this.alarm.param.ids = ids;
        let newParam = {};
        if (type === 1) {
          let {
            highWindow,
            highSum,
            lrEventReportRate,
            lrEventReportFlag,
            highSurgeAlarm,
            ...params
          } = this.alarm.param;
          // newParam = this.alarm.param;
          newParam = params;
        } else if (type === 2) {
          let {
            beThrValue,
            deThrValue,
            lrDeThrValue,
            lrDeContinueNum,
            lrDeReachPer,
            surgeAlarm,
            ...params
          } = this.alarm.param;
          newParam = params;
          newParam.surgeAlarm = this.alarm.param.highSurgeAlarm
          newParam.highInterruptState = this.alarm.param.highInterruptState
        }
        let alarmEditFlag = true;

        this.$http
          .wisdomPost("/probetask/updateAlarmConfig", newParam)
          .then(({ code, data, msg }) => {
            if (code === 1) {
              this.$Message.success(
                {
                  content: (type === 1 ? this.$t('comm_ordinary_dial') : this.$t('probetask_high_dial')) +
                  this.$t('probetask_task_sucess'), background: true
                }
              );              
              this.alarm.loading = false;
              this.alarm.modal.show = false;
              this.clearSelectData();
              this.getList(this.query);
            } else {
              // alarmEditFlag = false;
               this.$Message.warning({
                content: msg,
                duration: 2,
                closable: true,
                background: true
              });
              this.alarm.loading = false;
              this.$nextTick(() => {
                this.alarm.loading = true;
              });
            }
          })
          .finally(() => {
            if (!alarmEditFlag) {
              this.$Message.warning({
                content:
                (type === 1 ? this.$t('comm_ordinary_dial') :  this.$t('probetask_high_dial')) +
                  this.$t('probetask_task_fail'),
                duration: 2,
                closable: true,
                background: true
              });
              this.alarm.loading = false;
              this.$nextTick(() => {
                this.alarm.loading = true;
              });
            }
          });
      } else if (idsArray.length < 1) {
        this.$Message.warning({
          content: this.$t('probetask_no') + (type === 1 ? this.$t('comm_ordinary_dial') : this.$t('probetask_high_dial')) + this.$t('probetask_related_task'),
          duration: 2,
          closable: true,
          background: true
        });
        this.alarm.loading = false;
        this.$nextTick(() => {
          this.alarm.loading = true;
        });
      }
    },
    alarmCancel() {
      this.alarm.param.type = 1;
      this.alarm.modal.show = false;
    },

    changeStatus(param) {
      top.window.$iviewModal.confirm({
        title: param.startState == 0 ? this.$t('comm_pause_prompt') : this.$t('comm_prompt'),
        content:
         this.$t('task_pause_tips_sure_to') +
          (param.startState == 0 ? this.$t('dash_pause') : this.$t('dash_enable')) +
          this.$t('task_pause_tips_sure_to1'),
        onOk: () => {
          this.$http.wisdomPost("/probetask/status", param).then((res) => {
            if (res.code === 1) {
              this.$Message.success(
                { content: (param.startState == 0 ? this.$t('comm_disable') : this.$t('comm_enable')) +" " +this.$t('comm_success'), background: true }
              );
              this.query.pageNo = 1;
              this.pageNo = 1;
              this.clearSelectData()
              this.getList(this.query);
            } else {
              if (this.$base.isShowPrompt()) {
                this.$Message.error({ content: res.msg, background: true });
              }
            }
          });
        },
      });
    },

      /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.tableList.data;
        data.forEach(item => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
            this.selectedDatas.delete(JSON.stringify(item));
          }
        });
      } else {
        slection.forEach(item => {
          this.selectedIds.add(item.id);
          this.selectedDatas.add(JSON.stringify(item));
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
      this.selectedDatas.add(JSON.stringify(row));
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
      this.selectedDatas.delete(JSON.stringify(row));
    },
    //导出勾选任务节点
    excelTaskNodeExportClick() {
      let dataArr = Array.from(this.selectedDatas);
      let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;
      if (dataArr.length == 0) {
        this.$Message.warning({ content: this.$t('warning_select_want_export'), background: true });
      } else {
        let taskIds = dataArr.map((element) => {
          let item = JSON.parse(element)
          console.log(item);
          return item.id;

        });
        let params = new URLSearchParams();
        params.append("taskIds", taskIds.join(','));
        params.append("token_id", token_id||"");
        this.$Loading.start();
        axios({
          url: "/probetask/excelTaskNodeExport?time=" + new Date(),
          method: "post",
          data: params,
          responseType: "blob", // 服务器返回的数据类型
        })
          .then((res) => {
            let data = res.data;
            const blob = new Blob([data], { type: "application/vnd.ms-excel" });
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, this.$t('probe_derived')+".xlsx");
            } else {
              var fileName = "";
              fileName = this.$t('probe_derived')+".xlsx";

              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.clearSelectData();
            this.$Loading.finish();
            if (dataArr.length > 500) {
              this.$Message.warning({ content: '注意：最多导出500任务的中间和目标节点，超过500任务会截断导出', background: true,duration:5, });
            }
          })
          .catch(error => {
            console.log(error);
            this.$Loading.finish();
          }).finally(() => {
            this.$Loading.finish();
          });
      }
    },
    //导出勾选
    exportClick() {
      let dataArr = Array.from(this.selectedDatas);
      let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;
      if (dataArr.length == 0) {
        this.$Message.warning({ content: this.$t('warning_select_want_export'), background: true });
      } else {
        let taskIds = dataArr.map((element) => {
          let item = JSON.parse(element)
          console.log(item);
          return item.id;

        });
        let params = new URLSearchParams();
        params.append("taskIds", taskIds.join(','));
        params.append("token_id", token_id||"");
        this.$Loading.start();
        axios({
          url: "/probetask/excelExport?time=" + new Date(),
          method: "post",
          data: params,
          responseType: "blob", // 服务器返回的数据类型
        })
          .then((res) => {
            let data = res.data;
            const blob = new Blob([data], { type: "application/vnd.ms-excel" });
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, this.$t('probetask_export_excel_file_name'));
            } else {
              var fileName = "";
              fileName = this.$t('probetask_export_excel_file_name');

              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.clearSelectData();
            this.$Loading.finish();
          })
          .catch(error => {
            console.log(error);
            this.$Loading.finish();
          }).finally(() => {
            this.$Loading.finish();
          });
      }
    },
    //导出全部
    
    exportAllClick() {
      let _self = this;
      let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;
      this.saveQuery = Object.assign({}, this.query);
      let param = Object.assign({}, this.saveQuery);
      param.token_id = token_id;
      if (param.groupIds != "" && param.groupIds != null && param.groupIds.length > 0) {
        param.groupList = param.groupIds.join(",");
        param.groupIds = null;
      }
      this.$Loading.start();
      axios({
        url: "/probetask/excelExportAll",
        method: "post",
        params: param,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.$t('task_export_all_excel_file_name'));
          } else {
            var fileName = this.$t('task_export_all_excel_file_name');
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.clearSelectData();
          this.$Loading.finish();
        })
        .catch(error => {
          console.log(error);
          this.$Loading.finish();
        }).finally(() => {
          this.$Loading.finish();
        });
    },
    taskAdjustCancel() {
      this.taskAdjustModal.param = {
        start_date: "",
        end_date: "",
        start_time: "",
        end_time: "",
        is_week: 0,
        week: [],
      };
      this.taskAdjustModal.show = false;
      this.taskAdjustModal.loading = true;
    },
    taskAdjustOk() {
      this.$refs["taskAdjustModal"].validate((valid) => {
        if (valid) {
          let idArr = Array.from(this.selectedIds);
          if (idArr.length > 0) {
            let week = "";
            if (this.taskAdjustModal.weekShow) {
              if (this.taskAdjustModal.param.week.length > 0) {
                for (
                  let i = 0;
                  i < this.taskAdjustModal.param.week.length;
                  i++
                ) {
                  if (i === 0) {
                    week += this.taskAdjustModal.param.week[i];
                  } else {
                    week += "," + this.taskAdjustModal.param.week[i];
                  }
                }
              } else {
                this.$Message.warning({ content: this.$t('comm_tip2'), background: true });
                this.taskAdjustModal.loading = false;
                this.$nextTick(() => {
                  this.taskAdjustModal.loading = true;
                });
                return;
              }
            }
            let endDate = typeof this.taskAdjustModal.param.end_date === 'object' ? new Date(this.taskAdjustModal.param.end_date).format(
              "yyyyMMdd"
            ) : this.taskAdjustModal.param.end_date;

            let param = {
              ids: idArr.join(','),
              startDate: new Date(this.taskAdjustModal.param.start_date).format(
                "yyyyMMdd"
              ),
              endDate: endDate,

              isWeek: this.taskAdjustModal.weekShow ? 1 : 0,
              week: this.taskAdjustModal.weekShow ? week : "",
            };
            //时间段验证
            const ruleTimeFlag = this.ruleTimes(this.taskAdjustModal.param.timeList);
            if (ruleTimeFlag === false) {
              this.$Message.warning({ content: this.$t('comm_tip3'), background: true });
              this.taskAdjustModal.loading = false;
              this.$nextTick(() => {
                this.taskAdjustModal.loading = true;
              });
              return
            }
            let timeList = this.taskAdjustModal.param.timeList.map(item => {
              let str = "";
              str = item.start.replace(":", "") + "-" + item.end.replace(":", "");
              return str
            })
            param.timeList = timeList;
            this.$http
              .wisdomPost("/probetask/updateTaskPlans ", param)
              .then((res) => {
                if (res.code === 1) {
                  this.$Message.success({ content: this.$t('comm_success'), background: true });
                  this.query.pageNo = 1;
                  this.$refs["taskAdjustModal"].resetFields();
                  this.clearSelectData();
                  this.getList(this.query);
                  this.taskAdjustModal.loading = false;
                  this.taskAdjustModal.show = false;
                } else {
                  this.$Message.error({ content: res.msg, background: true });
                }
              });
          } else {
            this.$Message.warning({ content: this.$t('warning_select_want_export'), background: true });
          }
        } else {
          //验证不过
          this.taskAdjustModal.loading = false;
          this.$nextTick(() => {
            this.taskAdjustModal.loading = true;
          });
          return;
        }
      }).catch((error) => {
        this.taskAdjustModal.loading = false;
        this.$nextTick(() => {
          this.taskAdjustModal.loading = true;
        });
      });
    },

    playStartDateChange(val) {
      this.playEndDate.disabledDate = (date) => {
        // return date && date.valueOf() < new Date(val).valueOf() - 86400000;
      };
    },
    playStartDateChange2(val) {
      this.playEndDate2.disabledDate = (date) => {
        return date && date.valueOf() < new Date(val).valueOf() - 86400000;
      };
    },
    playStartTimeChange(time, index) {
      const endTime = this.taskAdjustModal.param.timeList[index].end;
      if (time != undefined && time != null && time != '') {
        const endTimes = time_to_sec(endTime);
        const startTimes = time_to_sec(time);
        if (endTime == undefined || endTimes < startTimes) {
          this.taskAdjustModal.param.timeList[index].end = sec_to_time(startTimes);
        }
      }
    },
    playEndTimeChange(time, index) {
      const startTime = this.taskAdjustModal.param.timeList[index].start;
      if (time != undefined && time != null && time != '') {
        const endTimes = time_to_sec(time);
        const startTimes = time_to_sec(startTime);
        if (startTime == undefined || endTimes < startTimes) {
          this.taskAdjustModal.param.timeList[index].start = sec_to_time(endTimes);
        }
      }
    },
    playStartTimeChange2(time, index) {
      const endTime = this.taskAdjustForm.param.timeList[index].end;
      if (time != undefined && time != null && time != '') {
        const endTimes = time_to_sec(endTime);
        const startTimes = time_to_sec(time);
        if (endTime == undefined || endTimes < startTimes) {
          this.taskAdjustForm.param.timeList[index].end = sec_to_time(startTimes);
        }
      }
    },
    playEndTimeChange2(time, index) {
      const startTime = this.taskAdjustForm.param.timeList[index].start;
      if (time != undefined && time != null && time != '') {
        const endTimes = time_to_sec(time);
        const startTimes = time_to_sec(startTime);
        if (startTime == undefined || endTimes < startTimes) {
          this.taskAdjustForm.param.timeList[index].start = sec_to_time(endTimes);
        }
      }
    },
    weekShowChange(val) {
      this.taskAdjustModal.weekShow = val;
    },
    weekShowFormChange(val) {
      this.taskAdjustForm.weekShow = val;
    },
    getGroupList() {
      const param = {
        pageNo: 1,
        pageSize: 100000
      };
      this.$http.wisdomPost("/group/list", param).then(res => {
        if (res.code === 1) {
          if (res.data) {
            this.groupList = res.data.records;
          }
        }
      });
    },
    speedTimeChangeRemove(index) {
      const length = this.taskAdjustForm.param.timeList.length;
      if (length === 1) {
          this.$Message.warning({ content: this.$t('phytopo_add_time'), background: true });
          return
      }
      this.taskAdjustForm.param.timeList.splice(index, 1)
    },
    speedTimeChangeAdd(index) {
      const length = this.taskAdjustForm.param.timeList.length;
      if (index === length - 1) {//增加
        if (length >= 10) {
          this.$Message.warning({ content: this.$t('phytopo_10_time'), background: true });
          return
        } else {
          this.taskAdjustForm.param.timeList.push({ start: "0000", end: "2359" })
        }
      } 
    },
    //时间段操作（增加，删除）
    taskTimeChange(index) {
      const length = this.taskAdjustModal.param.timeList.length;
      if (index === length - 1) {//增加
        if (length >= 10) {
          this.$Message.warning({ content: this.$t('phytopo_10_time'), background: true });
          return
        } else {
          this.taskAdjustModal.param.timeList.push({ start: "0000", end: "2359" })
        }
      } else {//删除
        this.taskAdjustModal.param.timeList.splice(index, 1)
      }
    },
    //时间段校验是否存在交叉
    ruleTimes(times) {
      const timeArray = times;
      //结果返回值
      let timesFlag = true;
      //将每一项换算成分钟数
      let timeMinArray = timeArray.map(item => {
        let start = 0, end = 0;
        start = Number(item.start.split(':')[0]) * 60 + Number(item.start.split(':')[1]);
        end = Number(item.end.split(':')[0]) * 60 + Number(item.end.split(':')[1]);
        return [start, end]
      })
      for (let i = 0; i < timeMinArray.length - 1; i++) {
        for (let j = i + 1; j < timeMinArray.length; j++) {
          if ((timeMinArray[j][1] <= timeMinArray[i][0] || timeMinArray[j][0] >= timeMinArray[i][1])) {
            timesFlag = true;
          } else {
            timesFlag = false;
            break;
          }
        }
        if (timesFlag === false) {
          break;
        }
      }
      return timesFlag
    }

  },
  mounted() {
    
    // if (this.$base.power === 1) {
    //   this.columns.splice(8, 1);
    // }
    this.saveQuery = Object.assign({}, this.query);
    // this.getClientList();
    this.getDefaultConfigList();
    this.getDialParam();
    this.getList(this.query);
    addDraggable();
    // this.setRowSelectedByField('showField', true);
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    // 如果界面导航处在第4步，页面刷新就回到第三步
    
    if (sessionStorage.getItem("delayPs")) {
      sessionStorage.removeItem("delayPs");
    }
    this.$store.commit("setdelayLTlevel", "");
    this.$store.commit("updateDelayLossHistory", -1);
    this.$store.commit("setflowTlevel", "");
    this.$store.commit("updateFlowHistory", -1);

    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.clear();
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
};
</script>
<style scoped lang="less">
// 固定列修改表头样式
/deep/.ivu-table-fixed-header thead tr th {
  padding: 19px 0;
}
/deep/.ivu-table-fixed-right::before {
  height: unset;
}
.section-special {
  .fn_box {
    width: 100%;
    .fn_item_label {
      width: 200px;
    }
  }
  .fn_box_special {
    margin-bottom: 10px;
    text-align: left;
  }
}
/deep/ .show-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url("../../../assets/btn-img/btn-look.png");
  background-repeat: no-repeat;
}
/deep/ .show2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url("../../../assets/btn-img/light-btn-look.png");
  background-repeat: no-repeat;
}
/deep/ .modify-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  background-image: url("../../../assets/btn-img/btn-edit.png");
  background-repeat: no-repeat;
}
/deep/ .modify2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  background-image: url("../../../assets/btn-img/light-btn-edit.png");
  background-repeat: no-repeat;
}
/deep/ .disabled-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: not-allowed;
  background-image: url("../../../assets/btn-img/btn-edit.png");
  background-repeat: no-repeat;
}

/deep/ .disabled2-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: not-allowed;
  background-image: url("../../../assets/btn-img/btn-edit.png");
  background-repeat: no-repeat;
}

/deep/ .ivu-icon-ios-create-outline {
  color: #02b8fd;
  font-size: 27px;
  font-weight: 600;
}
/deep/ .ivu-modal-header-inner {
  color: var(--input_font_color, #303748) !important;
}

/deep/ .ivu-modal-mask {
  background-color: initial;
}

.itemTitle {
  display: inline-block;
  line-height: 38px;
  font-weight: bold;
}

/deep/ .ivu-spin-fix .ivu-spin-main {
  position: absolute;
  top: 200px;
  left: 50%;
  transform: translate(-50%, -50%);
}
.labelContent {
  // display: flex;
  // align-items: center;
  span {
    margin: 0 10px;
  }

  .myDateClass {
    width: 140px;
    font-size: 14px;
    height: 40px;
  }
}

.ivu-select-disabled .ivu-select-selection:hover {
}

/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}

/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}

.addItem {
  color: #4e7bff;
  margin-left: 10px;
  font-size: 22px;
  cursor: pointer;
  vertical-align: middle;
}

.removeItem {
  color: #fb204d;
  margin-left: 10px;
  font-size: 22px;
  cursor: pointer;
  vertical-align: middle;
}

.note {
  color: #fe5c5c;
  text-indent: 100px;
}
</style>
<style>
.ivu-table-row .selected-row {
  .ivu-table td,
  .ivu-table th {
    border-bottom: 1px solid #fff !important;
  }
}
.runState {
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.runRed {
  background: #ff0000;
}

.runGrey {
  background: darkgrey;
}

.runGreen {
  background: #07c5a3;
}

.runOrange {
  background: #f79232;
}
</style>
<style lang="less" scoped>
</style>