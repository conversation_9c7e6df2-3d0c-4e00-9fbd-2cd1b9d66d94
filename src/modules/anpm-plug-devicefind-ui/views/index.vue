<template>
  <div
    id="tabs-iframe-box"
    :class="currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin'"
    style="height: 100%"
  >
    <Tabs
      :type="currentSkin == 1 ? 'card' : 'line'"
      id="aa"
      v-model="checkedTab"
      :class="{ 'tabs-card-content-black': currentSkin == 1 }"
    >
      <TabPane
        v-for="item in pathMenu"
        :key="item.value"
        :label="item.name"
        :name="item.path.substr(1)"
      >
        <iframe
          v-if="checkedTab === item.path.substr(1)"
          :src="pathSrc(item.path.substr(1))"
          width="100%"
          height="100%"
          :id="item.path.substr(1)"
          frameborder="0"
          scrolling="no"
        ></iframe>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
function getThisModulesMenu(param, callback) {
    const fnStr = sessionStorage.getItem('fn');
    if (fnStr) {
        const fn = JSON.parse(fnStr);
        let parentMenuList = fn.filter(item => item.functionName == param.parentName);
        console.log('parentMenuList', parentMenuList);
        if (parentMenuList && parentMenuList.length === 1) {
            let thisModulesMenuList = parentMenuList[0].sysFunctionList.filter(item => item.functionName == param.functionName);
            console.log('thisModulesMenuList', thisModulesMenuList);
            if (thisModulesMenuList.length === 1) {
                const barList = thisModulesMenuList[0].sysFunctionList2.map(item => {
                    return { name: item.functionName, value: item.functionName, path: item.functionUrl };
                });
                return barList;
            }
        } else {
            callback();
        }
    } else {
        callback();
    }
}
const resizeIframeHeight = (iframeEl, resizeRadio) => {
    const iframeWindow = iframeEl.contentWindow || iframeEl.contentDocument.parentWindow;
    if (iframeWindow.document.body) {
        //获取主容器高度，iframe内div容器高度会自己撑开
        if (iframeWindow.document.querySelector('#app')) {
            const height = iframeWindow.document.querySelector('#app').offsetHeight;
            //获取窗口大小
            const windowHeight = document.body.clientHeight;
            const iframeMinheight = windowHeight - 100;
            // iframeEl.height = height * (1 + Math.abs(1-resizeRadio))  + "px";
            iframeEl.height = height * (1 + (1 - resizeRadio)) + 'px';
        }
    }
};
window.onload = function () {
    const windowHeight = top.document.body.clientHeight;
    document.getElementById('app').style.setProperty('height', windowHeight - 100 + 'px');
    const timeId = setInterval(() => {
        //选择iframe
        const iframeEl = document.getElementsByTagName('iframe')[0];
        resizeIframeHeight(iframeEl, 1);
    }, 200);
};
export default {
    name: 'index',
    data() {
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            pathMenu: [],
            checkedTab: '',
            oldcheckedTab: ''
        };
    },
    created() {
        const src = window.frames.frameElement.getAttribute('src');

        console.log(src , "----------------")
        if (src.indexOf('devicefind') > -1) {
            this.pathMenu = getThisModulesMenu({ parentName: this.$t('comm_device_manage_menu'), functionName: this.$t('comm_device_find_menu') }, () => {
                this.$Message.warning({content:'获取菜单失败！',background:true});
            });
            console.log('this.pathMenu', this.pathMenu);
            const localstorageMenus = JSON.parse(sessionStorage.getItem('menu'));
            let checkedMenu = '';
            if (localstorageMenus) {
                const functionUrl = localstorageMenus.functionUrl;
                const menu = functionUrl.substr(1);
                for (let i = 0; i < this.pathMenu.length; i++) {
                    if (checkedMenu) {
                        break;
                    }
                    if (this.pathMenu[i].path.substr(1) == menu) {
                        checkedMenu = menu;
                    }
                }
                console.log(checkedMenu);
            }
            if (checkedMenu) {
                this.checkedTab = checkedMenu;
                this.oldcheckedTab = checkedMenu;
            } else {
                this.checkedTab = this.pathMenu[0].path.substr(1);
                this.oldcheckedTab = this.pathMenu[0].path.substr(1);
            }
        }
    },
    methods: {
          handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        pathSrc(path) {
            const src = window.location.hostname == 'localhost' ? '/anpm-plug-' + path + '-ui.html' : '/' + path;
            return src;
        },
        iframeAutoFit(id) {
            let iframeObj = document.getElementById(id);
            var bHeight = iframeObj.contentWindow.document.body.scrollHeight;
            var dHeight = iframeObj.contentWindow.document.documentElement.scrollHeight;

            setTimeout(function () {
                if (!iframeObj) return;
                iframeObj.height = iframeObj.Document ? iframeObj.Document.body.scrollHeight : iframeObj.contentDocument.body.offsetHeight;
                console.log(iframeObj.height);
            }, 200);
        },
        proMsg(msg, type) {
            if (type == 'warning') {
                this.$Message.warning({ content: msg, background: true });
                return;
            }
            if (type == 'success') {
                this.$Message.success({ content: msg, background: true });
                return;
            }
            if (type == 'info') {
                this.$Message.info({ content: msg, background: true });
                return;
            }
            if (type == 'error') {
                this.$Message.error({ content: msg, background: true });
                return;
            }
            if (type == 'loading') {
                this.$Message.loading({ content: msg, background: true });
                return;
            }
        },
    },
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
        // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        window.addEventListener('message', e => {
            if (e && e.data) {
                if (e.data.type == 'msg') {
                    this.proMsg(e.data.msg, e.data.proType);
                    return;
                }
            }
        });
    }
};
</script>

<style scoped lang='less'>
.ivu-tabs {
  padding-top: 20px;
}
.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  margin-right: 8px;
}
/deep/ .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
  border-color: transparent !important;
}
/deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  border: 0;
}
</style>
<style>
.tabs-card {
  box-sizing: border-box;
}
/* .tabs-card #aa .ivu-tabs-content{
  height: 100%;
} */
.tabs-card .ivu-tabs-bar {
  margin-left: 20px;
  margin-right: 20px;
}
.tabs-card > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  /*border-radius: 0;*/
  background: #fff;
}
.tabs-card > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
  border-top: 0;
}
.tabs-card
  > .ivu-tabs.ivu-tabs-card
  > .ivu-tabs-bar
  .ivu-tabs-tab-active:before {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background: transparent;
  position: absolute;
  top: 0;
  left: 0;
}
</style>