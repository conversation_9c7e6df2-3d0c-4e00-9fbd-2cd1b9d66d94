<template>
  <section>
    <!--模态框-->
    <Modal
      sticky
      v-model="modal.show"
      width="990"
      class="task-modal"
      :title="modal.modalTitle"
      draggable
      @on-cancel="closeImport"
      :mask="true"
    >
      <div class="section-body" style="padding: 0">
        <Loading :loading="page.loading"></Loading>
        <div class="section-body-content">
          <div>
            <!--    列表      -->
            <Table
              ref="tableList"
              stripe
              :columns="columns"
              class="fixed-left-right"
              :data="pageData.list"
              :loading="page.pageLoading"
              :no-data-text="
                pageData.total > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              size="small"
            >
            </Table>
            <!--  分页  -->
            <div
              class="tab-page"
              style="border-top: 0"
              v-if="pageData.total > 0"
            >
              <Page
                v-page
                :current.sync="page.pageNo"
                :page-size="page.pageSize"
                :total="pageData.total"
                :page-size-opts="page.pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </section>
</template>

<script>

export default {
  name: "import",
  props: {
    deviceId: {
      type: Number,
      default: function () {
        return null;
      },
    },
  },
  watch: {},

  data() {
    return {
      currentSkin: sessionStorage.getItem("dark") || 1,
      modal: {
        show: true,
        modalTitle: this.$t("device_run_record"),
      },
    // "device_restart_time": "设备重启时间",
    // "device_collection_time": "本次采集时间",
    // "device_collection_value": "本次采集值",
    // "device_prev_collection_time": "上次采集时间",
    // "device_prev_collection_value": "上次采集值",
      columns: [
        {
          title: this.$t("device_restart_time"),
          key: "restartTime",
          width: 180,
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.restartTime;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: this.$t("device_collection_time"),
          key: "currentGetherTime",
          width: 180,
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.currentGetherTime;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: this.$t("device_collection_value"),
          key: "currentGetherValueStr",
          width: 180,
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.currentGetherValueStr;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },

        {
          title: this.$t("device_prev_collection_time"),
          width: 180,
          key: "lastGetherTime",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.lastGetherTime;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: this.$t("device_prev_collection_value"),
          key: "lastGetherValueStr",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.lastGetherValueStr;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
      ],
      //分页参数
      page: {
        pageNo: 1,
        pageSize: 10,
        pageSizeOpts: [10, 50, 100, 200, 500, 1000],
        pageLoading: false,
      },
      /** 列表数据 */
      pageData: {
        total: 0,
        list: [],
      },
    };
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("storage", this.handleStorageChange);
  },
  mounted() {
    // 监听 storage 事件
    window.addEventListener("storage", this.handleStorageChange);
    // 查询列表
    this.queryClick(1);
  },
  methods: {
    //获取列表数据
    getTableList(queryParam) {
      this.$http
       .PostJson("/device/restart/log", queryParam)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if (data) {
              this.pageData.list = data.records;
              this.pageData.total = data.total || 0;
            } else {
              this.setTableListEmpty();
            }
          } else {
            this.setTableListEmpty();
            this.$Message.warning({ content: msg, background: true });
          }
        })
        .catch((error) => {
          this.setTableListEmpty();
        })
        .finally(() => {
          this.page.pageLoading = false;
        });
    },
    setTableListEmpty(){
        this.pageData.list = [];
        this.pageData.total = 0;
    },
    //数据列表页码切换
    pageChange(pageNo) {
      this.queryClick(pageNo);
    },
    //数据列表页码大小改变
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.queryClick(this.page.pageNo);
    },
    //查询事件
    queryClick(pageNo) {
      //初始化页码
      this.page.pageNo = pageNo;
      //设置查询参数
      const queryParam = {
        deviceId: this.deviceId,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
      };
      //打开加载动画
      this.page.pageLoading = true;
      //请求数据
      this.getTableList(queryParam);
    },
    handleStorageChange(event) {
      if (event.key === "dark") {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    closeImport() {
      this.$emit("closeImport");
    },
  },
};
</script>

<style scoped lang='less'>
.labelContent {
  display: flex;
  align-items: center;
  span {
    margin: 0 10px;
  }
  .myDateClass {
    width: 140px;
    font-size: 14px;
    height: 40px;
  }
}
.repeatTable .ivu-table-tip table td {
  text-align: left;
  text-indent: 235px;
}
.orgDiv {
  position: absolute;
  height: 260px;
  overflow: hidden;
  width: 100%;
  padding: 0 10px;
  background: white;
  z-index: 9;
  border-width: 1px;
  border-style: solid;
  border-top: 0;
  border-color: var(--border_color, #ddd);
  box-shadow: 0 0 6px var(--border_color, #ddd);
}
.orgDiv > .title {
  background: var(
    --modal_header_b_color,
    radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9))
  );
  color: white;
  margin: 0 -10px;
  padding: 0 10px;
}
.orgTreeScroll {
  width: calc(100% + 20px);
  height: calc(100% - 32px);
  overflow-y: scroll;
  overflow-x: hidden;
}
.ivu-select-disabled .ivu-select-selection:hover {
}
/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}
/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}
.addItem {
  color: #4e7bff;
  margin-left: 10px;
  font-size: 22px;
  cursor: pointer;
  vertical-align: middle;
}
.removeItem {
  color: #fb204d;
  margin-left: 10px;
  font-size: 22px;
  cursor: pointer;
  vertical-align: middle;
}
.note {
  color: #fb204d;
  text-indent: 100px;
}
</style>