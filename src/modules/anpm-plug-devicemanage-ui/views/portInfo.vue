<template>
  <section class="sectionBox">
    <div class="section-top">
      <Row class="fn_box">
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 180px"
              >{{ $t("comm_Device_name") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.devId"
                style="width: 100%"
                clearable
                filterable
                :only-filter-with-text="true"
                @on-change="devChange"
                :placeholder="$t('comm_please_select')"
              >
                <Option
                  v-for="item in deviceList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.newDeviceName }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 180px"
              >{{ $t("comm_port") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.portId"
                style="width: 100%"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('comm_please_select')"
              >
                <Option
                  v-for="item in portList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.ifName }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="8">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 180px"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
                @on-change="timeChange"
              >
              </DatePicker>
            </div>
          </div>
        </Col>
      </Row>
      <div class="fn_tool" style="text-align: right">
        <Button
          class="jiaHao-btn"
          type="primary"
          @click="queryClick(1)"
          :title="$t('common_query')"
        >
          <i class="iconfont icon-icon-query" />
        </Button>
        <Button
          class="daoChu-btn"
          type="primary"
          @click="$router.go(-1)"
          :title="$t('but_return')"
        >
          <i class="iconfont icon-icon-return1" />
        </Button>
        <!-- <Button type="primary" class="skinPrimary"  @click="$router.go(-1)">返回</Button> -->
      </div>
    </div>
    <div
      :class="currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin'"
    >
      <Tabs
        :type="currentSkin == 1 ? 'card' : 'line'"
        id="aa"
        v-model="checkedTab"
        @on-click="queryClick"
        :class="{ 'tabs-card-content-black': currentSkin == 1 }"
      >
        <TabPane
          :label="$t('probetask_indicator_trends_tip')"
          name="IndicatorTrend"
        >
          <IndicatorTrend
            ref="IndicatorTrend"
            :startTime="startTime"
            :endTime="endTime"
            :devId="query.devId"
            :portId="query.portId"
          ></IndicatorTrend>
        </TabPane>
        <TabPane label="ARP/NDP" name="ArpList">
          <ArpList
            ref="ArpList"
            :startTime="startTime"
            :endTime="endTime"
            :devId="query.devId"
            :portId="query.portId"
          ></ArpList>
        </TabPane>
        <TabPane label="MAC" name="MacList">
          <MacList
            ref="MacList"
            :startTime="startTime"
            :endTime="endTime"
            :devId="query.devId"
            :portId="query.portId"
          ></MacList>
        </TabPane>
      </Tabs>
    </div>
  </section>
</template>

<script>
import "@/timechange";
import moment from "moment";
export default {
    name:'portInfo',
    components:{
        IndicatorTrend:()=>import('../components/IndicatorTrend.vue'),
        ArpList:()=>import('../components/ArpList.vue'),
        MacList:()=>import('../components/MacList.vue')
    },
    data(){
        return{
             currentSkin: sessionStorage.getItem('dark') || 1,
            checkedTab:'IndicatorTrend',
            timeRange:[new Date(new Date().getTime() - 60 * 60 * 1000).format("yyyy-MM-dd HH:mm:ss"),new Date().format("yyyy-MM-dd HH:mm:ss")],
            timeOptions:{
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value () {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return [ start.format("yyyy-MM-dd HH:mm:ss"),  end.format("yyyy-MM-dd HH:mm:ss")];
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value () {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [ new Date().format("yyyy-MM-dd 00:00:00"),  new Date().format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value () {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value () {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value () {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value () {
                            let nowDate = new Date();
                            let date = {
                            year: nowDate.getFullYear(),
                            month: nowDate.getMonth(),
                            date: nowDate.getDate(),
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value () {
                            let date = new Date();
                            let day = new Date( date.getFullYear(), date.getMonth(),0).getDate();
                            let end = new Date( new Date().getFullYear(),new Date().getMonth() - 1,day);
                            let start = new Date( new Date().getFullYear(),  new Date().getMonth() - 1, 1);
                            return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                        }
                    }
                ]
            },
            startTime: (new Date(new Date().getTime() - 60 * 60 * 1000)).format("yyyy-MM-dd HH:mm:ss"),
            endTime: new Date().format("yyyy-MM-dd HH:mm:ss"),
            query:{
                startTime: (new Date(new Date().getTime() - 60 * 60 * 1000)).format("yyyy-MM-dd HH:mm:ss"),
                endTime: new Date().format("yyyy-MM-dd HH:mm:ss"),
                devId:this.$route.query.devId,
                portId:this.$route.query.devPortId
            },
            deviceList:[],
            portList:[],
        }
    
    },
    mounted() {
        this.getDevice();
        this.getPort();
            // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    },
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    methods:{
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        timeChange(val){
             this.startTime = val[0]
             this.endTime = val[1]
        },
        queryClick(){
            let startVal = moment(
                this.timeRange[0] === "" || this.timeRange[0] == undefined
                ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0))
                : this.timeRange[0],
                "YYYY-MM-DD hh:mm:ss"
            ).valueOf();
            let endVal = moment(
                this.timeRange[1] === "" || this.timeRange[1] == undefined
                ? new Date()
                : this.timeRange[1],
                "YYYY-MM-DD hh:mm:ss"
            ).valueOf();
            if ((endVal - startVal) / 1000 / 3600 / 24 > 90) {
                this.$Message.warning(this.$t('device_portInfo_limit_time'));
                return;
            }
            this.$refs[this.checkedTab].getList()
        },
         // 获取设备
         getDevice(){
            this.$http.wisdomPost("/device/queryDevList").then(res => {
                if (res.code === 1) {
                    res.data.forEach((dev) => {
                        let newDeviceName=''; 
                        let deviceName = dev.deviceName;
                        let sysname = dev.sysname;
                        if(deviceName && sysname){
                            newDeviceName = sysname+"("+deviceName+")";
                        }else if(!deviceName && sysname){
                            newDeviceName = sysname+"(--)";
                        }else if(deviceName && !sysname){
                            newDeviceName = "--("+deviceName+")";
                        }else if(!deviceName && !sysname){
                            newDeviceName = "--";
                        }
                        dev.newDeviceName = newDeviceName;
                        this.deviceList.push(dev);
                    });
                    // this.deviceList = res.data
                }else{
                this.$Message.error({content:res.msg,background:true})
                }
            })
        },
        // 设备类型
        devChange() {
            this.query.portId = null;
            this.getPort();
        },
        // 获取设备端口
        getPort(){
            this.$http.wisdomPost("/device/queryPortList",{devId:this.query.devId}).then(res => {
                if (res.code === 1) {
                    this.portList = res.data
                }else{
                this.$Message.error({content:res.msg,background:true})
                }
            })
        },
        goback(){}
    }
}
</script>

<style>
#aa.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
  color: #060d15;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  background: transparent;
  border-color: transparent;
  z-index: 0 !important;
}
</style>