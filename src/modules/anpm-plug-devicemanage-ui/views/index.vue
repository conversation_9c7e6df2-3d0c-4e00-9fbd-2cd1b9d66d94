<template>
  <div :class="{ 'light-no-tabs': currentSkin == 0 }">
    <section class="sectionBox">
      <!-- 设备管理页面 -->
      <div class="section-top">
        <Row class="fn_box">
          <!-- 机构 -->
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg"
                  @onClear="onClear"
                  @onFocus="focusFn"
                >
                </TreeSelect>
              </div>
            </div>
          </Col>
          <!-- 设备类型 -->
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("discover_device_type") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.deviceType"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="(item, index) in deviceTypeList"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 设备厂商 -->
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("snmpoid_device_merchant")
                }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.factory"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  @on-change="searchDevFactoryChange"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in factoryList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 设备型号 -->
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("snmp_task_device_model") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.deviceModel"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="(item, index) in devModelList"
                    :value="item.showCode"
                    :key="index"
                    >{{ item.showCode }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("comm_om_level") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.maintainLevel"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.groupIds"
                  multiple
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :max-tag-count="1"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in groupSearchList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("probetask_monitoring_state")
                }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.status"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in statusList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>

          <Col span="6">
            <!-- 关键字 -->
            <div class="fn_item">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("comm_keywords") }}：</label
              >
              <div class="fn_item_box">
                <Input
                  v-model="query.queryKeyWords"
                  :placeholder="$t('comm_search_ip_name')"
                  :title="$t('comm_search_ip_name')"
                />
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <!-- <Button class="query-btn" type="primary" v-if="permissionObj.list" icon="ios-search" @click="queryClick(1)" :title="$t('common_query')"></Button> -->
            <Button
              class="jiaHao-btn"
              type="primary"
              v-if="permissionObj.list"
              @click="queryClick(1)"
              :title="$t('common_query')"
            >
              <i class="iconfont icon-icon-query" />
            </Button>
            <Button
              class="query-btn"
              type="primary"
              v-if="permissionObj.add"
              icon="md-add"
              @click="addClick"
              :title="$t('common_new')"
            ></Button>
            <Dropdown @on-click="moreBtnClick">
              <Button class="more-btn">
                {{ $t("common_more") }}
                <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem
                  name="importClick()"
                  v-if="permissionObj.import"
                  >{{ $t("but_import") }}</DropdownItem
                >
                <Dropdown
                  placement="right-start"
                  @on-click="moreBtnClick"
                  v-if="permissionObj.export"
                >
                  <DropdownItem>
                    {{ $t("but_data_export") }}
                    <Icon type="ios-arrow-forward"></Icon>
                  </DropdownItem>
                  <DropdownMenu slot="list">
                    <DropdownItem name='exportClick("notAll")'>{{
                      $t("but_export_checked")
                    }}</DropdownItem>
                    <DropdownItem name='exportClick("all")'>{{
                      $t("but_export_all")
                    }}</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
                <DropdownItem
                  name='deleteClick("deleteAll")'
                  v-if="permissionObj.delete"
                  >{{ $t("but_remove") }}</DropdownItem
                >
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              stripe
              class="fixed-left-right"
              :columns="columns"
              :data="tableData"
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              @on-sort-change="sortSum"
              :no-data-text="
                loading
                  ? ''
                  : tableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              size="small"
            >
            </Table>
            <div
              class="tab-page"
              style="border-top: 0"
              v-if="tableList.length > 0"
            >
              <Page
                v-page
                :current.sync="query.pageNo"
                :page-size="query.pageSize"
                :total="totalCount"
                :page-size-opts="pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
      <Modal
        sticky
        v-model="excelShow"
        width="1200"
        class="task-modal threshold-modal"
        :title="$t('but_import')"
        draggable
        :mask="true"
        @on-cancel="downCancel()"
      >
        <div style="padding-left: 40px">
          <div style="margin: 10px 0">
            <span> {{ $t("comm_download_tpl") }}{{ $t("comm_colon") }}</span>
            <span
              @click="templateDown"
              style="
                color: var(--download_template_color, #7b7bfd);
                text-decoration: underline;
                cursor: pointer;
              "
              >{{ $t("comm_download_tpl1") }}</span
            >，{{ $t("comm_use_download_tpl") }}
          </div>
          <div>
            <span
              >{{ $t("discover_upload_attachment")
              }}{{ $t("comm_colon") }}</span
            >
            <Input v-model="uploadFiles" style="width: 300px" />
            <div
              class="uploadBox"
              style="
                width: 140px;
                margin-left: 10px;
                background: var(--choose_data_btn_bg_color, #06324d);
                border-radius: 4px 4px 4px 4px;
                opacity: 1;
                border: 1px solid var(--choose_data_btn_border_color, #05eeff);
                color: var(--choose_data_btn_font_color, #05eeff);
              "
            >
              <div icon="ios-cloud-upload-outline">
                {{ $t("select_attachment") }}
              </div>
              <input
                icon="ios-cloud-upload-outline"
                @change="changeImport"
                type="file"
                afactoryept=".xlsx,.xls"
                ref="uploadFile"
                :title="$t('comm_no_select_file')"
              />
              <!-- <span>{{ uploadFiles }}</span> -->
            </div>
          </div>
          <div style="margin: 10px 0">
            <span class="padding20">{{ $t("data_validation") }}</span>
            {{ $t("common_data_verification_total_tip") }}
            {{
              excleList && repeatVoList
                ? excleList.length + repeatVoList.length
                : 0
            }}
            {{ $t("common_data_verification_data_tip") }}，{{
              $t("common_data_verification_where_tip")
            }}
            {{ repeatVoList ? repeatVoList.length : 0 }}
            {{ $t("server_strip") }}
            {{ $t("common_data_verification_error_tip") }}！
            <span style="color: red"
              >{{ $t("comm_import_right") }}
              <!-- <Button type="primary" @click="getExcel">导出错误</Button> -->
              <Button
                class="daoChu-btn"
                type="primary"
                @click="getExcel"
                :title="$t('comm_export_error')"
              >
                <i class="iconfont icon-icon-derive" />
              </Button>
            </span>
          </div>
          <div class="section-body-content" style="margin: 10px 0">
            <Table
              stripe
              :columns="columns1"
              :data="repeatVoData"
              style="min-height: 200px"
              :no-data-text="
                loading
                  ? ''
                  : repeatVoList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
            >
              <template slot-scope="{ row }" slot="err">
                <Tooltip :content="row.err" placement="left" :max-width="200">
                  <div
                    style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      width: 100px;
                      white-space: nowrap;
                      text-align: left;
                    "
                  >
                    {{ row.err }}
                  </div>
                </Tooltip>
              </template>
              <template slot-scope="{ row }" slot="deviceIp">
                <Tooltip :content="row.deviceIp" placement="left">
                  <div
                    style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      width: 100px;
                      white-space: nowrap;
                      text-align: left;
                    "
                  >
                    {{ row.deviceIp }}
                  </div>
                </Tooltip>
              </template>
              <template slot-scope="{ row }" slot="deviceName">
                <Tooltip :content="row.deviceName" placement="left">
                  <div
                    style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      width: 100px;
                      white-space: nowrap;
                      text-align: left;
                    "
                  >
                    {{ row.deviceName }}
                  </div>
                </Tooltip>
              </template>
              <template slot-scope="{ row }" slot="orgName">
                <Tooltip :content="row.orgName" placement="left">
                  <div
                    style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      width: 100px;
                      white-space: nowrap;
                      text-align: left;
                    "
                  >
                    {{ row.orgName }}
                  </div>
                </Tooltip>
              </template>
            </Table>
          </div>
        </div>

        <div slot="footer" style="text-align: center">
          <Button
            type="error"
            style="margin-right: 20px"
            @click="downCancel()"
            >{{ $t("common_cancel") }}</Button
          >
          <Button type="primary" @click="downOk()">{{
            $t("common_verify")
          }}</Button>
        </div>
      </Modal>
      <!--新建-->
      <Modal
        sticky
        v-model="fromShow"
        width="940"
        class="task-modal threshold-modal"
        :title="btnType ? this.$t('common_new') : this.$t('common_update')"
        draggable
        :mask="true"
        @on-visible-change="saveVisibleChange"
        @on-cancel="saveCancel('dataForm')"
      >
        <Form
          ref="dataForm"
          :model="dataForm"
          :rules="ruleValidate"
          :label-width="150"
        >
          <Row>
            <Col span="12">
              <!-- 机构 -->
              <FormItem
                :label="$t('comm_org') + $t('comm_colon')"
                prop="orgId"
                v-if="btnType ? true : false"
                style="position: relative"
              >
                <TreeSelect
                  style="width: 250px"
                  v-model="treeValue1"
                  ref="TreeSelect"
                  :data="treeData"
                  @onFocus="focusFn"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg2"
                  @onClear="onClear2"
                >
                </TreeSelect>
              </FormItem>
              <FormItem
                :label="$t('comm_org') + $t('comm_colon')"
                prop="orgName"
                v-if="!btnType"
                style="position: relative"
              >
                <i-input
                  v-model="dataForm.orgName"
                  disabled
                  style="width: 250px"
                ></i-input>
              </FormItem>
            </Col>
            <Col span="12">
              <!-- 采集器 -->
              <FormItem
                :label="$t('discover_gether_name') + $t('comm_colon')"
                prop="getherCode"
                class="inlineForm"
                v-if="btnType ? true : false"
              >
                <Select
                  v-model="dataForm.getherCode"
                  style="width: 250px"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    style="width: 250px"
                    v-for="item in getherList"
                    :value="item.code"
                    :key="item.id"
                    >{{ item.labelName }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem
                :label="$t('discover_gether_name') + $t('comm_colon')"
                prop="getherName"
                v-if="btnType ? false : true"
                style="position: relative"
              >
                <i-input
                  v-model="dataForm.getherName"
                  disabled
                  style="width: 250px"
                ></i-input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <!-- 分组 -->
              <FormItem
                :label="$t('comm_group') + $t('comm_colon')"
                prop="groupIds"
                class="inlineForm"
              >
                <Select
                  v-model="groupIds"
                  style="width: 250px"
                  filterable
                  :only-filter-with-text="true"
                  allow-create
                  multiple
                  :placeholder="$t('comm_please_select')"
                  @on-create="handleGroupCreate"
                >
                  <Option
                    v-for="item in groupSearchList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}
                  </Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="12">
              <!-- 设备编码 -->
              <FormItem
                :label="$t('device_code') + $t('comm_colon')"
                prop="deviceCode"
                class="inlineForm"
              >
                <i-input
                  v-model="dataForm.deviceCode"
                  maxlength="30"
                  :disabled="btnType ? false : true"
                  :placeholder="$t('device_code_fill')"
                  style="width: 250px"
                ></i-input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <!-- 设备名称 -->
              <FormItem
                :label="$t('comm_Device_name') + $t('comm_colon')"
                prop="deviceName"
                class="inlineForm"
              >
                <i-input
                  v-model="dataForm.deviceName"
                  maxlength="100"
                  :placeholder="$t('phytopo_enter_device_name')"
                  style="width: 250px"
                ></i-input>
              </FormItem>
            </Col>
            <Col span="12">
              <!-- 设备iP -->
              <FormItem
                :label="$t('phytopo_device_IP') + $t('comm_colon')"
                prop="deviceIp"
                class="inlineForm"
              >
                <i-input
                  v-model="dataForm.deviceIp"
                  :placeholder="$t('interface_enter_ip')"
                  style="width: 250px"
                ></i-input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <!-- 运维等级 -->
              <FormItem
                :label="$t('comm_om_level') + $t('comm_colon')"
                prop="maintainLevel"
                class="inlineForm"
              >
                <Select
                  v-model="dataForm.maintainLevel"
                  style="width: 250px"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
            <Col span="12">
              <!-- 设备类型 -->
              <FormItem
                :label="$t('discover_equipment_type') + $t('comm_colon')"
                prop="deviceType"
                class="inlineForm"
              >
                <!-- 有问题的地方 -->
                <!-- filterable -->
                <!-- allow-create -->
                <!-- @on-create="handleDeviceTypeCreate" -->
                <Select
                  v-model="dataForm.deviceType"
                  style="width: 250px"
                  :placeholder="$t('comm_please_select')"
                  @on-change="searchDevFactoryChange"
                >
                  <Option
                    v-for="item in deviceTypeList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem
                :label="$t('snmpoid_device_merchant') + $t('comm_colon')"
                prop="factory"
                class="inlineForm"
              >
                <Select
                  v-model="dataForm.factory"
                  style="width: 250px"
                  @on-change="searchDevFactoryChange"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in factoryList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
            <Col span="12">
              <!-- 设备型号 -->
              <FormItem
                :label="$t('snmp_task_device_model') + $t('comm_colon')"
                prop="showDeviceModel"
                class="inlineForm"
              >
                <Select
                  v-model="dataForm.showDeviceModel"
                  style="width: 250px"
                  @on-change="modelChange"
                  @on-open-change="changeDeviceModel"
                  filterable
                  :only-filter-with-text="true"
                  :loading="deviceModalLoading"
                >
                  <Option
                    v-for="(item, index) in devModelList"
                    :value="item.showCode"
                    :key="index"
                    :label="item.showCode"
                    >{{ item.showCode }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem
                :label="
                  $t('device_discovery_select_credential') + $t('comm_colon')
                "
                prop="voucherId"
                class="inlineForm"
              >
                <Select
                  v-model="dataForm.voucherId"
                  style="width: 250px"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    style="width: 250px"
                    v-for="(item, index) in voucherList"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
          </Row>
          <div>
            <Row style="margin-bottom: 10px">
              <Col span="4" style="text-align: right"
                >{{ $t("device_discovery_port_information")
                }}{{ $t("comm_colon") }}</Col
              >
              <Col span="6" style="padding-left: 15px">{{
                $t("device_discovery_port_name")
              }}</Col>
              <Col span="6" style="padding-left: 5px">{{
                $t("device_discovery_port_ip")
              }}</Col>
              <Col span="6">MAC</Col>
              <Col span="2">
                <Icon
                  type="ios-add-circle"
                  style="
                    font-size: 20px;
                    color: #2b85e4;
                    cursor: pointer;
                    padding-left: 5px;
                  "
                  @click="handleAdd"
                />
              </Col>
            </Row>
            <FormItem v-for="(item, index) in dataForm.devPorts" :key="index">
              <Row>
                <Col span="6">
                  <FormItem
                    :prop="'devPorts.' + index + '.ifName'"
                    :rules="{
                      required: true,
                      message: $t('discover_cannot_be_null'),
                      trigger: 'blur',
                    }"
                  >
                    <Input
                      type="text"
                      maxlength="20"
                      v-model="item.ifName"
                    ></Input>
                  </FormItem>
                </Col>
                <Col span="6" offset="1">
                  <FormItem
                    :prop="'devPorts.' + index + '.ifIp'"
                    :rules="rulesValidate"
                  >
                    <Input type="text" v-model="item.ifIp"></Input>
                  </FormItem>
                </Col>
                <Col span="6" offset="1">
                  <!-- <FormItem :prop="'devPorts.' + index + '.ifMac'" :rules="{required: true, message: '不能为空', trigger: 'blur'}"> -->
                  <FormItem
                    :prop="'devPorts.' + index + '.ifMac'"
                    :rules="macValidate"
                  >
                    <Input
                      type="text"
                      v-model="item.ifMac"
                      :onkeyup="
                        (item.ifMac = item.ifMac
                          ? item.ifMac.toUpperCase()
                          : item.ifMac)
                      "
                    ></Input>
                  </FormItem>
                </Col>
                <Col span="2" offset="1">
                  <Icon
                    type="md-remove-circle"
                    style="
                      font-size: 20px;
                      color: #2b85e4;
                      cursor: pointer;
                      padding-left: 23px;
                    "
                    @click="handleRemove(index)"
                  />
                </Col>
              </Row>
            </FormItem>
          </div>
        </Form>
        <div slot="footer" style="text-align: center">
          <Button
            type="error"
            style="margin-right: 20px"
            @click="saveCancel('dataForm')"
            >{{ $t("common_cancel") }}</Button
          >
          <Button type="primary" @click="saveOk('dataForm')">{{
            $t("common_verify")
          }}</Button>
        </div>
      </Modal>
      <!--自定义列表项 修改之后-->
      <Modal
        sticky
        v-model="customModalShow"
        width="540"
        height="720"
        class="threshold-modal modal-selected"
        :title="$t('custom_list_items')"
        draggable
        :mask="true"
        :loading="customModalShowLoading"
      >
        <Row class="modal-selected-row">
          <Col>
            <Table
              ref="fieldsModalTableData"
              height="550"
              width="400"
              :show-header="false"
              :columns="fieldsColumns"
              :data="allocationListFields"
              @on-row-click="getFieldsColumnsIndex"
              :row-class-name="rowClassName"
            >
            </Table>
          </Col>
          <Col>
            <div class="btn-box">
              <Tooltip :content="$t('dash_up')" placement="right">
                <div class="sort-btn" @click="moveUp">
                  <Icon
                    type="md-arrow-up"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
              <Tooltip :content="$t('dash_down')" placement="right">
                <div class="sort-btn" @click="moveDown">
                  <Icon
                    type="md-arrow-down"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
            </div>
          </Col>
        </Row>
        <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>

      <!--设备运行记录-->
      <div v-if="runRecordState" style="height: 1000%; width: 100%">
        <RunRecord
          @closeImport="runRecordClose"
          :deviceId="runRecordDeviceId"
        ></RunRecord>
      </div>
    </section>
  </div>
</template>

<script>
import axios from "axios";
import global from "@/common/global.js";
import "@/config/page.js";
import locationreload from "@/common/locationReload";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import { tableEditBtn } from "@/assets/base64Img/img";
import { tableEditLightBtn } from "@/assets/base64Img/img";
import ipv6Format from "@/common/ipv6Format";
import validate from "@/common/validate";
import langFn from "@/common/mixins/langFn";
import RunRecord from "./runRecord.vue";

function timeToString(times) {
  if (times === null || times === undefined) {
    return "--";
  }
  const regPos = /^[0-9]+.?[0-9]*/; //判断是否是数字。
  var comm_day1 = "天";
  var comm_hour = "小时";
  var comm_minutes = "分";
  var comm_second = "秒";
  if (localStorage.getItem("locale") === "en") {
    comm_day1 = "d";
    comm_hour = "h";
    comm_minutes = "m";
    comm_second = "s";
  }
  if (regPos.test(times)) {
    var time = times,
      timeStr = "";
    var day = parseInt(time / 60 / 60 / 24);
    var hours = parseInt((time / 60 / 60) % 24);
    var minuts = parseInt((time / 60) % 60);
    var second = parseInt(time % 60);
    if (day > 0) {
      timeStr = day + comm_day1;
    }
    if (hours > 0) {
      timeStr += hours + comm_hour;
    }
    if (minuts > 0) {
      timeStr += minuts + comm_minutes;
    }
    if (second >= 0) {
      timeStr += second + comm_second;
    }
    return timeStr;
  } else {
    return 0 + comm_second;
  }
}

export default {
  components: { TreeSelect, RunRecord },
  mixins: [langFn],
  data() {
    const validIp = (rule, value, callback) => {
      if (!value) {
        callback();
      }
      let ipv4Regex = validate.getIpv4Regex(),
        // /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
        ipv6Regex = validate.getIpv6Regex();
      // /\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;
      if (ipv4Regex.test(value) || ipv6Regex.test(value)) {
        callback();
      } else {
        callback(new Error(this.$t("device_enter_ip")));
      }
      // callback();
    };
    const validMac = (rule, value, callback) => {
      if (!value) {
        callback();
      }
      // let macRegex = /\[0-9A-Fa-f]{2}[:-]{5}([0-9A-Fa-f]{2})/;
      let macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      if (macRegex.test(value)) {
        callback();
      } else {
        callback(new Error(this.$t("device_enter_mac")));
      }
      // callback();
    };

    // 验证设备型号
    const validDeviceModel = (rule, value, callback) => {
      // debugger

      if (value == "" || value == undefined || value == null) {
        callback(new Error(this.$t("snmpoid_equipment_model_select")));
      } else {
        callback();
      }
    };

    return {
      resizeTimer: null,
      currentSkin: sessionStorage.getItem("dark") || 1,
      deviceModalLoading: false,
      runRecordDeviceId:'',
      runRecordState: false,
      treeValue: "",
      treeValue1: "",
      loginUserOrgId: "",
      loginUserOrgName: "",
      //权限对象
      permissionObj: {},
      loading: false,
      // 机构
      orgList: [],
      treeData: [],
      // 设备类型
      deviceTypeList: [
        // { value: 1, label: '服务器' },
        // { value: 2, label: '交换机' },
        // { value: 3, label: '路由器' },
        // { value: 4, label: '防火墙' }
      ],
      // 设备厂商
      factoryList: [],
      // 设备型号
      devModelList: [],
      // 采集器
      getherList: [],
      // 凭证
      voucherList: [],
      totalTable: [],
      maintainList: [
        { value: 1, label: this.$t("logback_first") },
        { value: 2, label: this.$t("logback_second") },
        { value: 3, label: this.$t("logback_tertiary") },
      ],
      groupSearchList: [],
      statusList: [
        { value: 0, label: this.$t("device_discovery_not_monitored") },
        { value: 1, label: this.$t("device_discovery_enabled") },
        { value: 2, label: this.$t("snmptask_suspended") },
      ],
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      totalCount: 0,
      query: {
        orgId: "",
        factory: "",
        deviceModel: "",
        maintainLevel: "",
        groupId: "",
        groupIds: "",
        status: "",
        queryKeyWords: "",
        keyWords: "",
        pageNo: 1,
        pageSize: 10,
        fieldName: "",
        orderBy: "",
      },
      // 表格
      tableList: [],
      repeatVoList: [],
      excleList: [],
      allExcel: {},
      single: false,
      importOkFlag: false,
      OkFlag: true,
      rulesValidate: { validator: validIp, trigger: "blur", name: "端口" },
      macValidate: { validator: validMac, trigger: "blur" },
      customKey: "api:device/list+sys_func_id:565",
      customMoveIndex: 0,
      customModalShow: false,
      customModalShowLoading: false,
      columns: [],
      fieldsJsonObjArr: [],
      allocationListFields: [],
      customFieldsColumnsKeyWidth: [],
      screenWidth: 0,
      fieldsColumns: [
        {
          key: "showField",
          width: 35,
          render: (h, params) => {
            let row = params.row;
            return h("Checkbox", {
              props: {
                value: row.showField,
                disabled: row.fixedField, // 当fixedField为true时禁用Checkbox
              },
              on: {
                input: (value) => {
                  if (!row.fixedField) {
                    // 更新 showField 的值
                    this.allocationListFields[params.index].showField = value;
                  }
                },
              },
            });
          },
        },
        {
          key: "parameterTitle",
          align: "left",
          // width: 160,
          render: (h, params) => {
            let fontColor = "var(--field_font_color ,#fff)";
            if (params.row.fixedField) {
              fontColor = "#5CA0D5";
            }
            return h(
              "div",
              {
                style: {
                  color: fontColor,
                  fontWeight: 400,
                },
              },
              params.row.parameterTitle
            );
          },
        },
      ],
      fixedColumns: [
        {
          type: "selection",
          key: "selection",
          width: 30,
          align: "center",
          // fixed:'left'
        },
        {
          title: this.$t("common_controls"),
          key: "action",
          width: 120,
          align: "center",
          // fixed:'right',
          renderHeader: (h) => {
            const handleClick = () => {
              this.customModalShow = true;
              this.customModalShowLoading = true;
            };
            return h("div", [
              h("span", this.$t("common_controls")),
              h("img", {
                attrs: {
                  src: this.currentSkin == 1 ? tableEditBtn : tableEditLightBtn,
                },
                style: {
                  width: "15px", // 调整图片大小
                  height: "15px", // 考虑保持宽高比
                  marginLeft: "10px",
                  verticalAlign: "middle", // 使图片居中
                  cursor: "pointer",
                },
                on: {
                  click: handleClick,
                },
              }),
            ]);
          },
          render: (h, params) => {
            return h("div", [
              h(
                "Tooltip",
                {
                  props: {
                    placement: "left-end",
                    size: "small",
                    transfer: true,
                  },
                },
                [
                  h("span", {
                    class:
                      this.currentSkin == 1 ? "edit1-btn" : "light-edit1-btn",
                    style: {
                      display: this.permissionObj.update
                        ? "inline-block"
                        : "none",
                    },
                    on: {
                      click: () => {
                        // this.dataForm = params.row;
                        let data = params.row;
                        this.dataForm.orgId = data.orgId;
                        // 请求分组数据
                        this.getGroupSearchList(data.orgId);

                        let list = this.orgList.filter((r) => {
                          return r.id == data.orgId;
                        });

                        this.getGetherList(data.orgId, list);
                        this.$nextTick(() => {
                          let groupIds = data.groupIds
                            ? data.groupIds.split(",").map(Number)
                            : "";
                          this.dataForm.getherCode = data.getherCode;
                          this.groupIds = groupIds;
                          this.dataForm.deviceCode = data.deviceCode || "";
                          this.dataForm.deviceName = data.sysname || "";
                          this.dataForm.deviceIp = data.deviceIp;
                          this.dataForm.maintainLevel = data.maintainLevel;
                          this.dataForm.deviceType = data.deviceType;

                          this.dataForm.factory = data.factory;
                          this.getEditDevModelByFac(
                            data.factory,
                            data.deviceType
                          );

                          this.dataForm.deviceModel = data.deviceModel;
                          this.dataForm.showDeviceModel = data.showDeviceModel;
                          this.dataForm.voucherId = data.voucherId;
                          this.dataForm.id = data.id;
                          this.dataForm.orgName = data.orgName;
                          this.dataForm.getherName = data.getherName;
                          this.dataForm.devPorts = data.devPorts || [];
                          this.dataForm.id = data.id;
                          this.btnType = false;
                          this.fromShow = true;
                        });
                      },
                    },
                  }),
                  h("span", { slot: "content" }, this.$t("common_update")),
                ]
              ),
              h(
                "Tooltip",
                {
                  props: {
                    placement: "left-end",
                    size: "small",
                    transfer: true,
                  },
                },
                [
                  h("span", {
                    class: "del1-btn",
                    style: {
                      display: this.permissionObj.delete
                        ? "inline-block"
                        : "none",
                    },
                    on: {
                      click: () => {
                        this.deleteClick("delete", params.row.id);
                      },
                    },
                  }),
                  h("span", { slot: "content" }, this.$t("common_delete")),
                ]
              ),
            ]);
          },
        },
        {
          title: this.$t("device_code"),
          key: "deviceCode",
          align: "left",
          minWidth: 180,
          tooltip: true,
        },
        {
          /*  */ title: this.$t("comm_Device_name"),
          key: "deviceName",
          align: "left",
          minWidth: 200,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.deviceName;
            let sysname = param.row.sysname;
            if (str && sysname) {
              str = sysname + "(" + str + ")";
            } else if (!str && sysname) {
              str = sysname + "(--)";
            } else if (str && !sysname) {
              str = "--(" + str + ")";
            } else if (!str && !sysname) {
              str = "--";
            }
            if (str !== "--") {
              return h(
                "div",
                {
                  class: "table-ellipsis",
                  style: {
                    cursor: "pointer",
                    color:
                      "var(--table_content_column_link_color , #05EEFF ) !important",
                  },

                  on: {
                    click: () => {
                      this.actionClick(param.row, "linkNum");
                    },
                  },
                },
                [
                  h(
                    "Tooltip",
                    {
                      props: {
                        placement: "top-start",
                        content: str,
                      },
                    },
                    str
                  ),
                ]
              );
            } else {
              return h(
                "div",
                {
                  style: {
                    cursor: "pointer",
                    color:
                      "var(--table_content_column_link_color , #05EEFF ) !important",
                  },
                  on: {
                    click: () => {
                      this.actionClick(param.row, "linkNum");
                    },
                  },
                },
                str
              );
            }
          },
        },
        {
          title: this.$t("phytopo_device_ip"),
          key: "deviceIp",
          align: "left",
          width: 200,
          tooltip: true,
          render: (h, params) => {
            let str = params.row.deviceIp;
            let maxWidth = params.column.width; // 获取动态传递的宽度
            str = ipv6Format.formatIPv6Address(str, maxWidth);
            return h("div", { style: { whiteSpace: "pre-wrap" } }, str);
          },
        },
        {
          title: this.$t("device_systemUpTime"),
          key: "systemUpTime",
          align: "left",
          width: 120,
          tooltip: true,
          sortable: "custom",
          // render: (h, params) => {
          //     let str = params.row.systemUpTime;
          //     str = timeToString(str);
          //     return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          // },
          render: (h, param) => {
            let str = param.row.systemUpTime;
            str = timeToString(str);
            if (str !== "--") {
              return h(
                "div",
                {
                  // class: "table-ellipsis",
                  style: {
                    cursor: "pointer",
                    whiteSpace: 'pre-wrap',
                    color:
                      "var(--table_content_column_link_color , #05EEFF ) !important",
                  },

                  on: {
                    click: () => {
                      this.showRunRecord(param.row);
                    },
                  },
                },
                [
                  h(
                    "Tooltip",
                    {
                      props: {
                        placement: "top-start",
                        content: str,
                      },
                    },
                    str
                  ),
                ]
              );
            } else {
              return h(
                "div",
                {
                  style: {
                    whiteSpace: 'pre-wrap',
                  },
                },
                str
              );
            }
          },
        },
        // 设备类型
        {
          title: this.$t("discover_device_type"),
          key: "deviceTypeName",
          align: "left",
          width: 120,
          render: (h, params) => {
            let str = params.row.deviceTypeName;
            str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;
            // let str1 = str;
            // if (str.length > 5) {
            //   str1 = str.substring(0, 5) + "...";
            // }
            // return h(
            //   "Tooltip",
            //   {
            //     props: {
            //       placement: "top",
            //     },
            //   },
            //   [
            //     str1,
            //     h(
            //       "span",
            //       {
            //         slot: "content", //slot属性
            //         style: {
            //           whiteSpace: "normal",
            //           wordBreak: "break-all",
            //         },
            //       },
            //       str
            //     ),
            //   ]
            // );
            if (str !== "--") {
              return h("div", { class: "table-ellipsis" }, [
                h(
                  "Tooltip",
                  {
                    props: {
                      placement: "top-start",
                      content: str,
                    },
                  },
                  str
                ),
              ]);
            } else {
              return h("div", str);
            }
          },
        },
        // 设备厂商
        {
          title: this.$t("snmpoid_device_merchant"),
          key: "factoryName",
          align: "left",
          width: this.getColumnWidth(110, 130),
          render: (h, params) => {
            let str = params.row.factoryName;
            str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;
            let str1 = str;
            if (str.length > 5) {
              str1 = str.substring(0, 7) + "...";
            }
            return h(
              "Tooltip",
              {
                props: {
                  placement: "top",
                },
              },
              [
                str1,
                h(
                  "span",
                  {
                    slot: "content", //slot属性
                    style: {
                      whiteSpace: "normal",
                      wordBreak: "break-all",
                    },
                  },
                  str
                ),
              ]
            );
          },
        },
        {
          title: this.$t("snmp_task_device_model"),
          key: "showDeviceModel",
          align: "left",
          width: 160,
          render: (h, params) => {
            let str = params.row.showDeviceModel;
            str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;
            // let str1 = str;
            // if (str.length > 5) {
            //   str1 = str.substring(0, 5) + "...";
            // }
            // return h(
            //   "div",
            //   {
            //     props: {
            //       placement: "top",
            //     },
            //   },
            //   [
            //     str1,
            //     h(
            //       "span",
            //       {
            //         slot: "content", //slot属性
            //         style: {
            //           whiteSpace: "normal",
            //           wordBreak: "break-all",
            //         },
            //       },
            //       str
            //     ),
            //   ]
            // );
            // return h("span", str);
            if (str !== "--") {
              return h("div", { class: "table-ellipsis" }, [
                h(
                  "Tooltip",
                  {
                    props: {
                      placement: "top-start",
                      content: str,
                    },
                  },
                  str
                ),
              ]);
            } else {
              return h("div", str);
            }
          },
        },
        {
          title: this.$t("comm_org"),
          key: "orgName",
          width: 120,
          align: "left",
          tooltip: true,
        },
        {
          title: this.$t("comm_group"),
          key: "groupName",
          align: "left",
          width: 150,
          render: (h, params) => {
            let str = params.row.groupName;
            str =
              str === undefined || str === null || str === "" || str === "null"
                ? "--"
                : str;
            // let str1 = str;
            // if (str.length > 7) {
            //   str1 = str.substring(0, 7) + "...";
            // }
            // return h(
            //   "Tooltip",
            //   {
            //     props: {
            //       placement: "top",
            //     },
            //   },
            //   [
            //     str1,
            //     h(
            //       "span",
            //       {
            //         slot: "content", //slot属性
            //         style: {
            //           whiteSpace: "normal",
            //           wordBreak: "break-all",
            //         },
            //       },
            //       str
            //     ),
            //   ]
            // );
            if (str !== "--") {
              return h("div", { class: "table-ellipsis" }, [
                h(
                  "Tooltip",
                  {
                    props: {
                      placement: "top-start",
                      content: str,
                    },
                  },
                  str
                ),
              ]);
            } else {
              return h("div", str);
            }
          },
        },
        {
          title: this.$t("discover_gether_name"),
          key: "getherName",
          align: "left",
          width: 150,
          tooltip: true,
        },
        {
          title: this.$t("comm_om_level"),
          key: "maintainLevel",
          align: "left",
          width: this.getColumnWidth(90, 100),
          render: (h, params) => {
            const maintainLevel = params.row.maintainLevel;
            let str = "--";
            if (maintainLevel === 1) {
              str = this.$t("logback_first");
            } else if (maintainLevel === 2) {
              str = this.$t("logback_second");
            } else if (maintainLevel === 3) {
              str = this.$t("logback_tertiary");
            }
            return h("span", str);
          },
        },
        // 端口
        {
          title: this.$t("message_server_port"),
          key: "portNum",
          align: "left",
          width: this.getColumnWidth(60, 60),
        },
        {
          title: this.$t("comm_status"),
          key: "status",
          align: "left",
          width: this.getColumnWidth(70, 110),
          className: "bgColor",
          render: (h, params) => {
            let text = "--";
            let colorZ = "";
            let status = params.row.status;
            if (status == 0) {
              text = this.$t("device_discovery_not_monitored");
              colorZ = "#FE5C5C";
            } else if (status == 1) {
              text = this.$t("device_discovery_enabled");
              colorZ = "#07C5A3 ";
            } else if (status == 2) {
              text = this.$t("snmptask_suspended");
              colorZ = "#FEA31B";
            }
            return h(
              "span",
              {
                style: {
                  color: colorZ,
                },
              },
              text
            );
          },
        },
      ],
      btnType: true,
      fromShow: false,
      dataForm: {
        // 自定义分组
        groupNames: "",
        // 自定义设备类型
        addDestTypeName: "",
        orgId: "",
        orgName: "",
        getherCode: "",
        groupIds: "",
        deviceCode: "",
        deviceName: "",
        deviceIp: "",
        maintainLevel: "",
        factory: "",
        deviceType: "",
        deviceModel: "",
        showDeviceModel: "",
        voucherId: "",
        devPorts: [
          // {
          //     ifName: '',
          //     ifIp: '',
          //     ifMac: ''
          // }
        ],
      },
      groupIds: "",
      ruleValidate: {
        orgId: [
          {
            required: true,
            message: this.$t("testspeed_select_org"),
          },
        ],
        maintainLevel: [
          {
            required: true,
            type: "number",
            message: this.$t("device_discovery_select_level"),
            change: "blur",
          },
        ],

        deviceModel: [
          {
            required: true,
            validator: validDeviceModel,
          },
        ],
        showDeviceModel: [
          {
            required: true,
            message: this.$t("snmpoid_equipment_model_select"),
            trigger: "blur",
          },
        ],
        deviceType: [
          {
            required: true,
            type: "number",
            message: this.$t("discover_select_device_type"),
            change: "change",
          },
        ],
        getherCode: [
          {
            required: true,
            message: this.$t("gether_select_collector"),
            change: "blur",
          },
        ],
        deviceIp: [
          {
            required: true,
            message: this.$t("interface_enter_ip"),
            change: "blur",
          },
        ],
        factory: [
          {
            required: true,
            type: "number",
            message: this.$t("snmpoid_device_merchant_select"),
            change: "blur",
          },
        ],
      },
      excelShow: false,
      uploadFiles: "",
      // 上传错误数量
      errNum: "0",
      // 上传正确数量
      sucNum: "0",
      columns1: [
        {
          title: this.$t("phytopo_row_num"),
          key: "num",
          align: "center",
        },
        {
          title: this.$t("comm_org"),
          slot: "orgName",
          align: "center",
        },
        {
          title: this.$t("phytopo_device_ip"),
          slot: "deviceIp",
          align: "center",
        },
        {
          title: this.$t("phytopo_device_Name"),
          slot: "deviceName",
          align: "center",
        },
        {
          title: this.$t("phytopo_error_details"),
          slot: "err",
          align: "center",
        },
      ],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
    };
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("storage", this.handleStorageChange);
    window.removeEventListener("resize", this.handleResize);
  },
  mounted() {
    // 监听 storage 事件
    window.addEventListener("storage", this.handleStorageChange);
  },
  created() {
    window.addEventListener("resize", this.handleResize);
    let nodeData = JSON.parse(localStorage.getItem("nodeData"));
    console.log(nodeData);
    if (nodeData) {
      // debugger
      //  await this.getTotalTable()
      //   console.log(this.totalTable,99999)
      //   debugger
      //  筛选出对应的行信息
      // const rowData = this.totalTable.filter(item => item.deviceIp == nodeData.value
      // )
      // console.log(rowData,88888)
      // this.actionClick(rowData)
      // this.actionClick(nodeData)
      // this.$store.
      this.$router.push({ path: "/deviceInfo" });
    } else {
    }
    // debugger
    this.getTreeOrg();
    this.$nextTick(() => {
      locationreload.loactionReload(
        this.$route.path.split("/")[1].toLowerCase()
      );
    });

    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.getDevModelByFac(null, null);
    this.getGroupSearchList();
    this.queryClick(1);
    this.getVoucher();
    this.getDeviceType();
    this.getDeviceFactory();
    //获取屏幕总长度
    this.screenWidth = window.innerWidth - 45;
    //保存原始的字段项展示长度
    this.setCustomFieldsColumnsKeyWidth();
  },
  watch: {
    deviceModalLoading(newVal) {
      if (newVal) {
        this.devModelList.push({});
      }
    },
  },
  computed: {
    repeatVoData: {
      get: function () {
        let arr = this.repeatVoList.map((item) => {
          for (const key in item) {
            let str =
              item[key] === undefined ||
              item[key] === null ||
              item[key] === "" ||
              item[key] === "null"
                ? "--"
                : item[key];
            item[key] = str;
          }
          return item;
        });
        return arr;
      },
    },
    tableData: {
      get: function () {
        let arr = this.tableList.map((item) => {
          return item;
        });
        return arr;
      },
    },
  },
  methods: {
    showRunRecord(row) {
      this.runRecordState = true;
      this.runRecordDeviceId = row.id;
    },
    runRecordClose() {
      this.runRecordState = false;
      this.runRecordDeviceId = "";
    },
    handleResize() {
      // 如果已经有待执行的重置操作，则取消它
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      // 设置300ms的节流延迟
      this.resizeTimer = setTimeout(() => {
        // 重新计算表格宽度
        this.$nextTick(() => {
          // 重新计算屏幕宽度
          this.screenWidth = window.innerWidth - 45;
          // 重新设置列宽
          this.setCustomFieldsColumnsKeyWidth();
          // 重新获取表格数据以更新列配置
          this.getAllocationListFieldsByKey();
        });
      }, 300);
    },
    handleStorageChange(event) {
      if (event.key === "dark") {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    //保存原始的字段项展示长度
    setCustomFieldsColumnsKeyWidth() {
      if (this.customFieldsColumnsKeyWidth == 0) {
        this.fixedColumns.forEach((item) => {
          let customFieldsColumnsKeyWidthObj = {
            key: item.key,
            width: item.width,
          };
          this.customFieldsColumnsKeyWidth.push(customFieldsColumnsKeyWidthObj);
        });
      }
    },
    saveVisibleChange(val) {
      if (val) {
        // 新增编辑弹框打开，请求获取分组数据
        // this.getGroupSearchList()
      }
    },
    changeDeviceModel(val) {
      if (val) {
        // this.searchDevFactoryChange()
        this.getDevModelByFac(this.dataForm.factory, this.dataForm.deviceType);
      }
    },

    // 树形选择聚焦
    focusFn() {
      this.getTreeOrg();
    },

    handleDeviceTypeCreate(value) {
      var itemArrays = this.deviceTypeList.filter((item) => {
        return item.name == value;
      });

      if (itemArrays.length > 0) {
        return;
      }

      this.deviceTypeList.push({
        isNew: true,
        name: value,
        id: Date.now(),
      });
    },
    // 分组select展开
    slectOpen(value) {
      console.log(value);
    },
    // 分组新建

    handleGroupCreate(value) {
      var itemArrays = this.groupSearchList.filter((item) => {
        return item.name == value;
      });

      if (itemArrays.length > 0) {
        return;
      }

      this.groupSearchList.push({
        isNew: true,
        id: value,
        name: value,
      });
    },

    moreBtnClick(val) {
      eval(`this.${val}`);
    },

    getRowData(row) {
      // debugger
      let str = Number(row.deviceType),
        text = "--";
      var items = this.deviceTypeList.filter((item) => {
        return item.id == str;
      });

      if (items.length > 0) {
        text = items[0].name;
      }

      row.deviceTypeName = text;

      str = Number(row.factory);
      text = "--";
      items = this.factoryList.filter((item) => {
        return item.id == str;
      });

      if (items.length > 0) {
        text = items[0].name;
      }

      row.factoryName = text;
      return row;
    },
    actionClick(row) {
      // console.log(row)
      // debugger
      row = this.getRowData(row);
      // console.log(row)

      // 重新赋值
      let str = row.deviceName;
      let sysname = row.sysname;
      if (str && sysname) {
        str = sysname + "(" + str + ")";
      } else if (!str && sysname) {
        str = sysname + "(--)";
      } else if (str && !sysname) {
        str = "--(" + str + ")";
      } else if (!str && !sysname) {
        str = "--";
      }

      row.deviceNameStr = str;

      this.$router.push({
        path: "/deviceInfo",
        query: {
          rowData: row,
        },
      });
      console.log(row, "---------------------------");
      this.$store.commit("updateRowData", row);
    },

    // 查询
    queryClick(pageNo) {
      // this.query.queryKeyWords = this.query.queryKeyWords.replace(/\s/g, '');

      this.query.queryKeyWords = this.query.queryKeyWords.trim();
      this.query.keyWords = this.query.queryKeyWords.trim();
      //初始化页码
      this.query.pageNo = pageNo;
      let groupIds = this.query.groupIds ? this.query.groupIds.join(",") : "";
      //设置查询参数
      const queryParam = {
        orgId: this.query.orgId,
        factory: this.query.factory,
        deviceModel: this.query.deviceModel,
        maintainLevel: this.query.maintainLevel,
        groupId: this.query.groupId,
        groupIds: groupIds,
        status: this.query.status,
        queryKeyWords: this.query.queryKeyWords,
        keyWords: this.query.queryKeyWords,
        pageNo: this.query.pageNo,
        pageSize: this.query.pageSize,
        deviceType: this.query.deviceType,
      };
      //打开加载动画
      this.loading = true;

      //请求数据
      this.getTableList(queryParam);
    },
    //修改自定列表项
    customModalOk() {
      this.$http
        .PostJson("/allocationlistfields/update", {
          allocationListFields: this.allocationListFields,
          key: this.customKey,
        })
        .then((res) => {
          if (res.code === 1) {
            this.$Message.success({
              content: this.$t("comm_success"),
              background: true,
            });
            this.customModalShow = false;
            this.customModalShowLoading = false;
            this.getTableList(this.query);
          }
        });
    },
    //取消修改
    customModalCancel() {
      this.customModalShow = false;
      this.customModalShowLoading = false;
    },
    //获取列表项下标并改变状态
    getFieldsColumnsIndex(row, index) {
      this.customMoveIndex = index;
      // if(row.fixedField == false){
      //   if(row.showField == true){
      //   this.allocationListFields[index].showField =false;
      //   }else{
      //     this.allocationListFields[index].showField =true;
      //   }
      // }
    },
    rowClassName(row, index) {
      if (index === this.customMoveIndex) {
        return "selected-row";
      }
      return "";
    },

    //上移
    moveUp() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex > 0) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex - 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex - 1;
      }
    },
    //下移
    moveDown() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex < this.allocationListFields.length - 1) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex + 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex + 1;
      }
    },
    getAllocationListFieldsByKey() {
      this.$http
        .PostJson("/allocationlistfields/getAllocationListFieldsByKey", {
          key: this.customKey,
        })
        .then((res) => {
          if (res.code === 1) {
            if (res.data.allocationListFields) {
              let screenWidthTemp = this.screenWidth;
              this.allocationListFields = res.data.allocationListFields;
              this.fieldsJsonObjArr = [];
              // 获取显示表头
              this.fieldsJsonObjArr.push("selection"); //第一列选择列

              res.data.allocationListFields.forEach((item) => {
                if (item.showField === true) {
                  this.fieldsJsonObjArr.push(item.parameterName);
                }
              });
              this.fieldsJsonObjArr.push("action"); //最后一列操作列
              // this.fieldsJsonObjArr.push("action");//最后一列操作列
              this.columns = [];
              //将显示的表头拿到函数放到columns中
              if (this.fieldsJsonObjArr.length > 0) {
                let customColumnsWidth = 0; //回显自定义字段总宽度
                let customColumnsAvgWidth = 0; //回显自定义字段平均宽度
                this.fieldsJsonObjArr.forEach((item) => {
                  this.fixedColumns.forEach((item2) => {
                    if (item === item2.key) {
                      //计算需要展示自定义字段项总长度
                      let customFieldsColumnsKeyWidthTemp =
                        this.customFieldsColumnsKeyWidth.find(
                          (item3) => item3.key === item2.key
                        );
                      if (customFieldsColumnsKeyWidthTemp) {
                        customColumnsWidth =
                          customColumnsWidth +
                          customFieldsColumnsKeyWidthTemp.width;
                        item2.width = customFieldsColumnsKeyWidthTemp.width;
                      }
                      this.columns.push(item2);
                      return;
                    }
                  });
                });
                //赋值标头名称
                this.allocationListFields.forEach((item) => {
                  this.fixedColumns.forEach((item2) => {
                    if (item.parameterName === item2.key) {
                      item.parameterTitle = item2.title;
                      return;
                    }
                  });
                });
                //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
                if (screenWidthTemp > customColumnsWidth) {
                  if (this.columns.length > 2) {
                    let columnsLength = this.columns.length - 2;
                    customColumnsAvgWidth = Math.floor(
                      (screenWidthTemp - customColumnsWidth) / columnsLength
                    );
                  }
                }
                this.columns.forEach((item) => {
                  if (item.key != "action" && item.key != "selection") {
                    item.width = item.width + customColumnsAvgWidth;
                  }
                });
              } else {
                this.columns = this.fixedColumns;
              }
            }
          } else {
            this.columns = this.fixedColumns;
          }
        });
    },
    sortSum(column) {
      this.query.fieldName = column.key;
      this.query.orderBy = column.order;
      this.getTableList(this.query);
    },
    getTableList(queryParam) {
      this.getAllocationListFieldsByKey();
      this.$http
        .wisdomPost("/device/list", queryParam)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.tableList = data.records || [];
            this.totalCount = data.total || 0;
            this.loading = false;
          } else {
            this.loading = false;
            this.$Message.warning(msg);
          }
        })
        .catch((error) => {
          this.loading = false;
        });
    },

    // 新建
    async addClick() {
      this.btnType = true;
      this.fromShow = true;

      // 置空
      this.treeValue1 = "";
      this.groupIds = "";
      this.dataForm = {
        orgId: "",
        getherCode: "",
        groupIds: "",
        deviceCode: "",
        deviceName: "",
        deviceIp: "",
        maintainLevel: "",
        deviceType: "",
        factory: "",
        deviceModel: "",
        showDeviceModel: "",
        id: "",
        treeValue1: "",
        devPorts: [
          // {
          //     ifName: '',
          //     ifIp: '',
          //     ifMac: ''
          // }
        ],
      };

      let token = JSON.parse(sessionStorage.getItem("accessToken"));
      if (token && token.user) {
        console.log(token.user);
        this.loginUserOrgId = token.user.orgId;
        this.loginUserOrgName = token.user.orgName;
        this.treeValue1 = token.user.orgName;
        this.dataForm.orgId = this.loginUserOrgId;
        await this.getGetherList(this.loginUserOrgId);
        // console.log(this.getherList,'this.getherList')
        this.dataForm.getherCode =
          this.getherList.length > 0 ? this.getherList[0].code : null;
      }
      this.dataForm.maintainLevel = 1;
      // 请求获取分组数据
      this.getGroupSearchList(this.dataForm.orgId);
    },
    // 新建按钮
    saveOk(name) {
      // 添加分组自定义选择
      console.log(this.dataForm, "------------------");
      if (this.groupIds && this.groupIds.length > 0) {
        var newGroupItemArrays = [];
        var existsGroupItemArrays = [];

        this.groupSearchList.forEach((item) => {
          if (this.groupIds.indexOf(item.id) > -1) {
            if (item.isNew) {
              newGroupItemArrays.push(item);
            } else {
              existsGroupItemArrays.push(item);
            }
          }
        });
        // 是否有新增的数据
        if (newGroupItemArrays.length > 0) {
          // 设置具体的新增的分组名称
          this.dataForm.groupNames = newGroupItemArrays.map((item) => {
            return item.name;
          });
        } else {
          this.dataForm.groupNames = [];
        }

        // 已经存在的分组
        if (existsGroupItemArrays.length > 0) {
          this.dataForm.groupIds = existsGroupItemArrays
            .map((item) => {
              return item.id;
            })
            .join(",");
        } else {
          this.dataForm.groupIds = "";
        }
      } else {
        // 不选分组的时候，需要清除两个值
        this.dataForm.groupNames = [];
        this.dataForm.groupIds = "";
      }

      // 添加设备类型自定义选择
      // 现在的 deviceType 是中文的 name 属性了
      if (this.dataForm.deviceType) {
        // 是否有新增的数据
        var destTypeItemArrays = this.deviceTypeList.filter((item) => {
          return this.dataForm.deviceType == item.name && item.isNew;
        });

        //
        if (destTypeItemArrays.length > 0) {
          this.dataForm.addDestTypeName = destTypeItemArrays[0].name;
        } else {
          this.dataForm.addDestTypeName = "";
        }
      } else {
        this.dataForm.addDestTypeName = "";
      }

      console.log(this.dataForm, "------------------");

      var dataFormJson = Object.assign({}, this.dataForm);
      if (dataFormJson.addDestTypeName) {
        dataFormJson.deviceType = "";
      } else {
        // isNew 等于 false 或者为空的 数据
        var destTypeExistsArrays = this.deviceTypeList.filter((item) => {
          return this.dataForm.deviceType == item.name;
        });
        if (destTypeExistsArrays.length > 0) {
          dataFormJson.deviceType = destTypeExistsArrays[0].id;
        }
      }

      // if(this.dataForm.showDeviceModel == '' ||
      // this.dataForm.showDeviceModel == undefined ||
      // this.dataForm.showDeviceModel == null){
      //     this.$Message.error(this.$t("snmpoid_equipment_model_select"));
      //     return;
      // }

      this.$refs[name].validate((valid) => {
        if (valid) {
          let groupIds = this.groupIds ? this.groupIds.join(",") : "";
          this.dataForm.groupIds = groupIds;

          if (this.btnType) {
            this.$http
              .PostJson("/device/add", dataFormJson)
              .then(({ code, data, msg }) => {
                if (code === 1) {
                  this.$Message.success(
                    this.$t("device_discovery_create_success")
                  );
                  this.queryClick(1);
                  this.fromShow = false;
                  this.handleReset(name);
                } else {
                  this.$Message.warning(msg);
                }
              });
          } else {
            this.$http
              .PostJson("/device/update", dataFormJson)
              .then(({ code, data, msg }) => {
                if (code === 1) {
                  this.$Message.success(this.$t("comm_changed_successful"));
                  this.queryClick(1);
                  this.fromShow = false;
                  this.handleReset(name);
                } else {
                  this.$Message.warning(msg);
                }
              });
          }
        } else {
          // this.$Message.error('Fail!');
        }
      });
    },
    //新建关闭
    saveCancel() {
      this.fromShow = false;
      this.treeValue1 = "";
      this.$refs["dataForm"].resetFields();
      this.queryClick(1);
    },
    // 增加
    handleAdd() {
      this.dataForm.devPorts.push({
        ifName: "",
        ifIp: "",
        ifMac: "",
      });
    },
    //删除
    handleRemove(index) {
      // if (this.dataForm.devPorts.length == 1) {
      //     this.$Message.warning({ content: '请至少保留一条指标', background: true });
      //     return;
      // } else {
      //     this.dataForm.devPorts.splice(index, 1);
      // }
      this.dataForm.devPorts.splice(index, 1);
    },

    // 重置
    handleReset(name) {
      this.groupIds = "";
      this.$refs[name].resetFields();
      this.getDeviceType();
    },
    // 导入
    importClick() {
      //清空选择附件input，不然无法触发下一次@chang事件
      this.$refs.uploadFile.value = "";
      this.excelShow = true;
    },
    // 导入关闭
    downCancel() {
      this.excelShow = false;
      this.uploadFiles = "";
      this.excleList = [];
      this.repeatVoList = [];
      this.getDeviceType();
    },
    // 导入确定
    downOk() {
      let _self = this;
      // let single = _self.single === false ? 0 : 1;

      let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;

      if (this.allExcel.excleList.length < 1) {
        //清空选择附件input，不然无法触发下一次@chang事件
        this.$refs.uploadFile.value = "";
        this.$Message.warning({
          content: this.$t("spec_no_valid"),
          background: true,
        });
        return;
      }
      let params = new URLSearchParams();
      params.append("jsonStr", JSON.stringify(this.allExcel));
      params.append("orgId", this.orgId);
      // if (this.importOkFlag == true) {
      // if (this.OkFlag === true) {
      // this.OkFlag = false;
      this.$Loading.start();
      axios({
        url: "/device/readExcelSave",
        method: "post",
        data: params,
      })
        .then((res) => {
          if (res.data.code === 1) {
            this.$Message.success({
              content: this.$t("interface_import_success"),
              background: true,
            });
            this.downCancel();
            this.getTableList(this.query);
            this.OkFlag = true;
            // this.clearData();
            this.$emit("closeImport", "ok");
          } else {
            this.$Message.error({
              content: this.$t("interface_import_failure"),
              background: true,
            });
            this.OkFlag = true;
            return;
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          this.$Loading.finish();
        })
        .finally(() => {
          this.$Loading.finish();
        });
      // } else {
      // this.$Message.info({content:this.$t('comm_upload_file_msg_1'),background:true});
      // }
      // } else if (this.importOkFlag == false) {
      //     this.$Message.info({content:this.$t('comm_upload_file_msg_2'),background:true});
      // }
    },
    // 删除
    deleteClick(type, data) {
      let ids = [];
      if (type == "delete") {
        ids.push(data);
      } else {
        // 将对象转换成数组
        ids = Array.from(this.selectedIds);
      }
      if (ids.length == 0) {
        this.$Message.warning(this.$t("device_discovery_select_del"));
        return;
      }

      top.window.$iviewModal.confirm({
        title: this.$t("common_delete_prompt"),
        content: this.$t("device_discovery_del_device"),
        onOk: () => {
          this.$http
            .PostJson("/device/delete", ids)
            .then(({ code, data, msg }) => {
              if (code === 1) {
                this.$Message.success(this.$t("common_controls_succ"));
                this.queryClick(1);
                this.selectedIds = new Set();
              } else {
                this.$Message.warning(msg);
              }
            })
            .catch((err) => {
              throw new Error(err);
            });
        },
      });
    },
    // 取消一项
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },
    // 单独勾选
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    // 全选
    handleSelectAll(slection) {
      if (slection.length === 0) {
        this.selectedIds = new Set();
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
        });
      }
    },
    // 翻页
    pageChange(val) {
      this.queryClick(val);
    },
    // 选择条数
    pageSizeChange(val) {
      this.query.pageSize = val;
      this.queryClick(this.query.pageNo);
    },
    // 导出
    exportClick(val) {
      let fileName = this.$t("device_all_info");
      if (val === "notAll") {
        let exportIds = Array.from(this.selectedIds);
        if (exportIds.length < 1) {
          this.$Message.warning({
            content: this.$t("specquality_select"),
            background: true,
          });
          return;
        }
        fileName = this.$t("device_info");
        this.query.ids = exportIds;
      }
      this.query.groupIds = this.query.groupIds
        ? this.query.groupIds.join(",")
        : this.query.groupIds;
      axios({
        url: "/device/excelExport",
        method: "post",
        data: this.query,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          const content = res.data;
          const blob = new Blob([content], {
            type: "application/vnd.ms-excel",
          });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // const fileName = '设备管理.xlsx'; // 导出文件名
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ("download" in document.createElement("a")) {
              // 支持a标签download的浏览器
              const _res = res.data;
              let blob = new Blob([_res]);
              let downloadElement = document.createElement("a");
              let href = window.URL.createObjectURL(blob); //创建下载的链接
              downloadElement.href = href;
              downloadElement.download = fileName; //下载后文件名
              document.body.appendChild(downloadElement);
              downloadElement.click(); //点击下载
              document.body.removeChild(downloadElement); //下载完成移除元素
              window.URL.revokeObjectURL(href); //释放掉blob对象
              if (val === "notAll") {
              }
            } else {
              // 其他浏览器
              navigator.msSaveBlob(blob, fileName);
            }
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          this.$Loading.finish();
        })
        .finally(() => {
          this.queryClick(1);
          this.selectedIds = new Set();
          this.query.ids = null;
          this.$Loading.finish();
        });
    },
    // 导出模板
    templateDown() {
      this.$Loading.start();
      //模板下载
      axios({
        url: "/device/download",
        method: "post",
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          const content = res.data;
          const blob = new Blob([content], {
            type: "application/vnd.ms-excel",
          });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(
              blob,
              this.$t("device_import_tmp")
            );
          } else {
            const fileName = this.$t("device_import_tmp"); // 导出文件名
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ("download" in document.createElement("a")) {
              // 支持a标签download的浏览器
              const _res = res.data;
              let blob = new Blob([_res]);
              let downloadElement = document.createElement("a");
              let href = window.URL.createObjectURL(blob); //创建下载的链接
              downloadElement.href = href;
              downloadElement.download = fileName; //下载后文件名
              document.body.appendChild(downloadElement);
              downloadElement.click(); //点击下载
              document.body.removeChild(downloadElement); //下载完成移除元素
              window.URL.revokeObjectURL(href); //释放掉blob对象
            } else {
              // 其他浏览器
              navigator.msSaveBlob(blob, fileName);
            }
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          this.$Loading.finish();
        })
        .finally(() => {
          this.$Loading.finish();
        });
    },
    // 选择附件
    changeImport(val) {
      this.excleList = [];
      this.repeatVoList = [];
      let fileFormData = new FormData();
      fileFormData.append("file", val.target.files[0]);
      this.uploadFiles = val.target.value.substr(
        val.target.value.lastIndexOf("\\") + 1
      );
      // fileFormData.append("orgId", this.orgId);
      let requestConfig = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };
      this.$Loading.start();
      axios
        .post("/device/readExcel", fileFormData, requestConfig)
        .then((res) => {
          if (res.data.code === 1) {
            console.log("res.data:", res.data);
            this.excleList = res.data.data.excleList;
            let repeatVoList = res.data.data.repeatVoList;
            if (repeatVoList) {
              for (let i = 0; i < repeatVoList.length; i++) {
                this.repeatVoList.push({
                  num: res.data.data.repeatVoList[i][12],
                  err: res.data.data.repeatVoList[i][11],
                  orgName: res.data.data.repeatVoList[i][0],
                  deviceName: res.data.data.repeatVoList[i][4],
                  deviceIp: res.data.data.repeatVoList[i][5],
                });
              }
            }
          } else {
            this.excleList = [];
            this.repeatVoList = [];
            this.$Message.error({
              content: this.$t("comm_prompt_file_uploaded_incorrect"),
              background: true,
            });
          }
          this.allExcel = res.data.data || {};
          this.$Loading.finish();
        })
        .catch((error) => {
          this.$Loading.finish();
        })
        .finally(() => {
          this.$Loading.finish();
        });
    },
    // 凭证
    getVoucher() {
      this.$http.post("deviceVoucher/getVoucher").then((res) => {
        if (res.code == 1) {
          this.voucherList = res.data;
        }
      });
    },
    modelChange(model) {
      this.dataForm.deviceModel = model || "";
    },
    // 设备类型
    searchDevFactoryChange() {
      this.dataForm.deviceModel = null;
      this.dataForm.showDeviceModel = null;
      this.getDevModelByFac(this.dataForm.factory, this.dataForm.deviceType);
    },
    // 获取设备厂商
    getDeviceFactory() {
      this.$http.wisdomPost("/deviceFactory/queryList").then((res) => {
        if (res.code === 1) {
          this.factoryList = res.data;
        } else {
          this.$Message.error({ content: res.msg, background: true });
        }
      });
    },
    async getDevModelByFac(factoryId, typeId) {
      console.log(factoryId);
      console.log(typeId);
      this.devModelList = [];
      // 不再清空选择框的值
      // this.dataForm.showDeviceModel = "";
      let url = "/deviceModel/queryList";

      try {
        var res = await this.$http.post(url, {
          deviceFactory: factoryId,
          deviceType: typeId,
        });

        if (res.code === 1) {
          this.$nextTick(() => {
            let str = "";
            this.devModelList = res.data.filter((d) => {
              if (str.indexOf(d.name) === -1) {
                str = d.name + "," + str;
                return true; // 返回 true 以保留该项
              }
              return false; // 过滤掉重复项
            });
            console.log(this.devModelList, "this.devModelList");
          });
        } else {
          this.devModelList = [];
        }
      } catch (error) {
        console.error("Error fetching device models:", error);
        this.devModelList = []; // 确保在出错时也能设置为空
      }
    },

    async getEditDevModelByFac(factoryId, typeId) {
      this.devModelList = [];
      let url = "/deviceModel/queryList";
      var res = await this.$http.post(url, {
        deviceFactory: factoryId,
        deviceType: typeId,
      });

      let str = "";
      if (res.code == 1) {
        this.$nextTick(() => {
          this.devModelList = res.data.filter((d) => {
            if (str.indexOf(d.name) == -1) {
              str = d.name + "," + str;
              return d.name;
            }
          });
          console.log(this.devModelList, "this.devModelList");
        });
      } else {
        this.devModelList = [];
      }
    },
    // 分组
    getGroupSearchList(orgId) {
      const param = {
        orgId: orgId,
      };
      console.log(param);
      // debugger
      this.$http.wisdomPost("/group/groupList", param).then((res) => {
        if (res.code === 1) {
          if (res.data) {
            this.groupSearchList = res.data;
          }
        }
      });
    },
    // 获取机构
    getTreeOrg(type) {
      let self = this;
      this.$http.PostJson("/org/tree", { orgId: null }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          self.orgList = treeNodeList;
          self.treeData = treeNodeList;
        }
      });
    },
    loadOrgTreeData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    setOrg(item) {
      this.treeValue = item[0].name;
      this.query.orgId = item[0] ? item[0].id : null;
    },
    onClear() {
      this.treeValue = "";
      this.query.orgId = "";
    },
    // 弹框选择
    async setOrg2(item) {
      this.treeValue1 = item[0].name;
      this.dataForm.orgId = item[0] ? item[0].id : null;
      this.orgList = item;
      await this.getGetherList(this.dataForm.orgId, item[0]);
      // 机构改变，重新获取分组数据
      await this.getGroupSearchList(this.dataForm.orgId);
      this.dataForm.getherCode =
        this.getherList.length > 0 ? this.getherList[0].code : null;
    },
    onClear2() {
      this.treeValue1 = "";
      this.dataForm.orgId = "";
    },
    async getGetherList(orgId, orgItem) {
      const res = await this.$http.wisdomPost(
        "/sys/gether/getList?type=2&orgId=" + orgId
      );

      if (res.code === 1) {
        if (res.data) {
          console.log(res.data, "res.data");
          this.getherList = res.data;
        }
      }
    },
    // 获取设备类型
    getDeviceType() {
      this.$http.wisdomPost("/deviceType/queryList").then((res) => {
        if (res.code === 1) {
          this.deviceTypeList = res.data;
          console.log(this.deviceTypeList, "-----------------------");
        } else {
          this.deviceTypeList = [];
          this.$Message.error({ content: res.msg, background: true });
        }
      });
    },

    getExcel() {
      //重复项下载
      if (this.repeatVoList && this.repeatVoList.length > 0) {
        let params = new URLSearchParams();
        params.append("jsonStr", JSON.stringify(this.allExcel));
        this.$Loading.start();
        axios({
          url: "/device/repeatExcelExport",
          method: "post",
          data: params,
          responseType: "blob", // 服务器返回的数据类型
        })
          .then((res) => {
            let data = res.data;
            const blob = new Blob([data], { type: "application/vnd.ms-excel" });
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, this.$t("device_error"));
            } else {
              var fileName = "";
              fileName = this.$t("device_error");
              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.$Loading.finish();
          })
          .catch((error) => {
            this.$Loading.finish();
          })
          .finally(() => {
            this.$Loading.finish();
          });
      } else {
        this.$Message.warning({
          content: this.$t("spec_no_duplicates"),
          background: true,
        });
      }
    },
    macChange(value) {
      return value.toUpperCase();
    },
  },
};
</script>
<style>
</style>
<style scoped lang="less">
</style>
