<template>
    <section class="sectionBox">
        <div class="deviceHeader">
            <div class="gobackBtn">
                <Button class="daoChu-btn" type="primary" @click="goBackFn" :title="$t('but_return')">
                    <i class="iconfont icon-icon-return1" />
                </Button>
                <!-- <Button type="primary" class="skinPrimary">返回</Button> -->
            </div>
            <div class="deviceInfo">
                <Row class="itemRow">
                    <Col span="8">
                    <div class="itemDiv">
                        <label>{{$t('phytopo_device_Name')}}{{$t('comm_colon')}}</label>
                        <span :title="deviceInfo.deviceName">{{deviceInfo.deviceName && deviceInfo.deviceName.length > 13 ? deviceInfo.deviceName.substring(0,13) + '...' : deviceInfo.deviceName  }}</span>
                    </div>
                    </Col>
                    <Col span="8">
                    <div class="itemDiv">
                        <label>{{$t('device_manage_ip')}}{{$t('comm_colon')}}</label>
                        <span :title="deviceInfo.deviceIp">{{ deviceInfo.deviceIp}}</span>
                    </div>
                    </Col>
                    <Col span="8">
                    <div class="itemDiv">
                        <label>{{$t('discover_equipment_type')}}{{$t('comm_colon')}}</label>
                        <span>{{ deviceInfo.deviceTypeName }}</span>
                    </div>
                    </Col>
                </Row>
                <Row class="itemRow">
                    <Col span="8">
                        <div class="itemDiv">
                            <label>{{$t('testspeed_affiliated_org')}}</label>
                            <span :title="deviceInfo.orgName">{{deviceInfo.orgName && deviceInfo.orgName.length > 13 ? deviceInfo.orgName.substring(0,13) + '...' : deviceInfo.orgName  }}</span>
                        </div>
                    </Col>
                    <Col span="8">
                        <div class="itemDiv">
                            <label>{{$t('discover_gether_name')}}{{$t('comm_colon')}}</label>
                            <span :title="deviceInfo.getherName">{{ deviceInfo.getherName && deviceInfo.getherName.length > 13 ? deviceInfo.getherName.substring(0,13) + '...' : deviceInfo.getherName  }}</span>
                        </div>
                        </Col>
                    <Col span="8">
                    <div class="itemDiv">
                        <label>{{$t('snmp_task_device_model')}}{{$t('comm_colon')}}</label>
                        <span>{{ deviceInfo.showDeviceModel }}</span>
                    </div>
                    </Col>
                    
                </Row>
                <Row class="itemRow">
                    <Col span="8">
                        <div class="itemDiv">
                            <label>{{$t('device_number_of_ports')}}{{$t('comm_colon')}}</label>
                            <span>{{ deviceInfo.portNum }}</span>
                        </div>
                    </Col>
                    <Col span="8">
                        <div class="itemDiv">
                            <label>{{$t('snmpoid_device_merchant')}}{{$t('comm_colon')}}</label>
                            <span>{{ deviceInfo.factoryName }}</span>
                        </div>
                    </Col>
                    <Col span="8">
                    <div class="itemDiv">
                        <label>{{$t('comm_om_level')}}{{$t('comm_colon')}}</label>
                        <span>{{ maintainLevel }}</span>
                    </div>

                    </Col>
                </Row>
                <Row class="itemRow">
                    <Col span="8">
                        <div class="itemDiv">
                            <label>{{$t('comm_group')}}{{$t('comm_colon')}}</label>
                            <span :title="deviceInfo.groupName">{{ deviceInfo.groupName && deviceInfo.groupName.length > 13 ? deviceInfo.groupName.substring(0,13) + '...' : deviceInfo.groupName  }}</span>
                        </div>
                    </Col>
                    <Col span="8">
                    <div class="itemDiv">
                        <label>{{$t('probetask_monitoring_state')}}{{$t('comm_colon')}}</label>
                        <span>{{ monitorStatus }}</span>
                    </div>
                    </Col>
                </Row>
            </div>

        </div>
        <div class="tabs-card">
            <Tabs :type="currentSkin == 1 ? 'card':'line'"  id="aa" v-model="checkedTab" @on-click='tabClick' :class="{'tabs-card-content-black':currentSkin == 1}">
                <TabPane :label="$t('device_interface_info')" name="InterfaceList">
                    <InterfaceList ref="InterfaceList"></InterfaceList>
                </TabPane>
                <TabPane :label="$t('device_network_neighbor')" name="NetworkList">
                    <NetworkList ref="NetworkList"></NetworkList>
                </TabPane>
            </Tabs>
        </div>
    </section>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    name: 'deviceInfo',
    components: {
        InterfaceList: () => import('../components/InterfaceList.vue'),
        NetworkList: () => import('../components/NetworkList.vue')
    },
    computed: {
        monitorStatus() {
            let obj = {
                0: this.$t('device_discovery_not_monitored'),
                1: this.$t('device_discovery_enabled'),
                2: this.$t('comm_paused')
            };
            return obj[this.deviceInfo.status];
        },
        maintainLevel() {
            let obj = {
                1: this.$t('logback_first'),
                2: this.$t('logback_second'),
                3: this.$t('logback_tertiary')
            };
            return obj[this.deviceInfo.maintainLevel];
        },
        ...mapGetters({ deviceInfo: 'getRowData' })
    },
    data() {
        return {
                  currentSkin: sessionStorage.getItem('dark') || 1,
            deviceName: this.$t('device_core_switch'),
            checkedTab: 'InterfaceList',
            totalTable:[]
            //   deviceInfo:this.$route.query.rowData
        };
    },
    methods: {
          handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    goBackFn() {
        // 区分是否从物理拓扑跳转过来
        if(localStorage.getItem('tabsId')) {
             let obj = {
          activeId: 288,
          functionUrl: "/phystopo",
          navName: "物理拓扑",
          node1: "",
          parentFunctionName: "拓扑管理",
          parentactiveId: 287,
          subMenuName: ""


        }
        sessionStorage.setItem('menu',JSON.stringify(obj))
        
         // 需要重新刷新一下页面
        parent.location.reload()

        }else {
            this.$router.go(-1)
        }
    },
    async getTotalTable(nodeData) {
    let  queryParam = {
        pageNo :1,
        pageSize:10000000
   }
   const res = await this.$http.wisdomPost("/device/list", queryParam)
//    debugger
   this.totalTable = res.data.records
   let nodeId = nodeData.id.split("_",1)[0]
   console.log(nodeId)
   console.log(this.totalTable)
   
    localStorage.removeItem('nodeData')
   const findResult = this.totalTable.find(item => {
    // debugger
    console.log(item.id,nodeId)
    return item.id == Number(nodeId)
   
   })
    let str = findResult.deviceName;
            let sysname = findResult.sysname;
            if (str && sysname) {
              str = sysname + "(" + str + ")";
            } else if (!str && sysname) {
              str = sysname + "(--)";
            } else if (str && !sysname) {
              str = "--(" + str + ")";
            } else if (!str && !sysname) {
              str = "--";
            }
            findResult.deviceName = str;
   this.$store.commit("updateRowData", findResult);
//    this.$refs.NetworkList.getList();
  


  

    },
        tabClick(name) {
            this.$refs[name].getList();
        }
    },
    created() {
        let nodeData =JSON.parse (localStorage.getItem("nodeData")) 
        if(nodeData) {
            this.getTotalTable(nodeData)
        }
    },
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
        this.deviceInfo.deviceName = this.deviceInfo.deviceNameStr;
        // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    }
};
</script>

<style lang='less' scoped>
.deviceHeader {
    .gobackBtn {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 10px;
    }
    .deviceInfo {
        background-color: var(--body_conent_b_color , #061824);
        padding: 10px 0;
        margin-bottom: 10px;
        label {
            width: 130px;
            display: flex;
            justify-content: flex-end;
            margin-right: 10px;
        }
        span {
            font-size: 13px;
            color: var(--th_font_color, #303748) !important;
        }
    }
    .itemRow {
        margin-bottom: 10px;
        .itemDiv {
            margin: auto;
            // width: 50%;
            display: flex;
            justify-content: flex-start;
        }
    }
}
/deep/
.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab{
    border: 0px solid #dcdee2 !important;
}
</style>