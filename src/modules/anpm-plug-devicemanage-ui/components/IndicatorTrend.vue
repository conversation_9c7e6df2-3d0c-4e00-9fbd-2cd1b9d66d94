<template>
  <div class="section-body contentBox_bg">
    <Loading :loading="isLoading" />
    <div class="snmp-content-right" style="float: right">
      <div class="content-right-box">
        <Row class="fn_box">
          <Col span="4">
            <div class="fn_item" style="width: 300px; margin-bottom: 20px">
              <div class="fn_item_label">
                <label
                  >{{ $t("flow_congestion_alarm")
                  }}{{ $t("comm_colon") }}</label
                >
              </div>
              <div class="fn_item_box" style="width: 100px">
                <Select
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  v-model="flowCongestionAlarm"
                  :placeholder="$t('comm_please_select')"
                  @on-change="handleFlowCongestionAlarmChange"
                >
                  >
                  <Option
                    v-for="item in flowCongestionAlarmSelect"
                    :value="item.value"
                    :key="item.value"
                  >
                    {{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col>
        </Row>
      </div>
      <div class="content-right-box">
        <IndexList :data="listData1"></IndexList>
      </div>
      <div class="content-right-box" style="margin-top: 50%">
        <IndexList :data="listData2"></IndexList>
      </div>
    </div>
    <div class="snmp-content-left" style="margin-right: 400px">
      <div class="lookBox">
        <div class="contain">
          <div style="position: relative" v-show="spinShow">
            <Spin fix style="width: 100%; height: 524px"
              >{{ $t("alarm_load")
              }}<Icon
                type="ios-loading"
                size="18"
                class="demo-spin-icon-load"
              ></Icon>
            </Spin>
          </div>
          <div
            ref="indexChart"
            id="indexChart"
            class="echartStyle"
            :style="'height:' + height + 'px'"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
let echarts = require('echarts/lib/echarts');
require('echarts/lib/chart/line');
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/lib/component/legend');
require('echarts/lib/component/dataZoom');
import axios from 'axios';
import echartFn from '@/common/mixins/echartFun';
import eConfig from '@/config/echart.config.js';
import moment from 'moment';
import '../../../timechange';
export default {
    name: 'IndicatorTrend',
    mixins: [echartFn],
    components: {
        IndexList: () => import('./IndexList.vue')
    },
    props: ['startTime', 'endTime', 'devId', 'portId'],
    data() {
        return {
             currentSkin: sessionStorage.getItem('dark') || 1,
            flowCongestionAlarm:'',
            flowCongestionAlarmSelect:[
                { label: this.$t('comm_no'), value: 0 },
                { label: this.$t('comm_yes'), value: 1 }
            ],
            query: {
                devId: null, // 设备id
                portId: null,
                level: '',
                rate: '',
                rateUnit: '',
                startTime: '',
                endTime: ''
            },
            spinShow: false,
            height: 500,
            listData1: {
                title: [this.$t('dashboard_inlet_velocity'), this.$t('dashboard_outflow_velocity'), this.$t('view_Leased_type_6'), this.$t('view_Leased_type_8')],
                maxValue: ['0', '0', '0', '0'],
                averageValue: ['0', '0', '0', '0'],
                currentValue: ['0', '0', '0', '0']
            },
            listData2: {
                title: [this.$t('device_receive_packet_loss'), this.$t('device_sending_packet_loss'), this.$t('device_receive_error'), this.$t('device_sending_error')],
                maxValue: ['0', '0', '0', '0'],
                averageValue: ['0', '0', '0', '0'],
                currentValue: ['0', '0', '0', '0']
            },
            delayLossData: {
                delay: [],
                loss: []
            },
            flowIntoSpeeds: [], // 入流速
            flowOutSpeeds: [], // 出流速
            intoUsages: [], // 入利用率
            outUsages: [], // 出利用率
            receiveLossPackages: [], // 接收丢包
            sendLossPackages: [], // 发送丢包
            receiveErrors: [], // 接收错误
            sendErrors: [], // 发送错误
            delayLossColor: ['#81C8FF', '#9BFBAE','#0290FD','#00FFEE'],
            flowColor: ['#81C8FF', '#9BFBAE','#0290FD','#00FFEE'],
            flowData: {
                enter: [],
                issue: []
            },
            indexChart: null,
            echart2: {
                show: true
            },
            startValue: '',
            endValue: '',
            scale: '',
            typeUnit: {
                [this.$t('dashboard_inlet_velocity')]: 'Kbps',
                [this.$t('dashboard_outflow_velocity')]: 'Kbps',
                [this.$t('view_Leased_type_6')]: '%',
                [this.$t('view_Leased_type_8')]: '%'
            },
            isdarkSkin: top.window.isdarkSkin,
            isNoData:true,
            level: 0,
            wheelDelta:-1,
            beforeLevel:'',
            isLoading:false,
            originalStartTime:'',
            originalEndTime:'',
        };
    },
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
    },
    mounted() {
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        top.window.addEventListener('message', e => {
            if (e) {
                if (e.data.type == 'msg') {
                    return;
                } else if (typeof e.data == 'object') {
                    this.isdarkSkin = e.data.isdarkSkin;
                    this.$Skin.skinChange(top.window.skin);
                } else if (typeof e.data == 'number') {
                    this.isdarkSkin = e.data;
                    this.$Skin.skinChange(top.window.skin);
                }
            }
        });
        this.initEchart();
        this.getList();
        this.getFlowCongestionAlarmByPortId();
    },
    methods: {
        handleStorageChange(event) {
            if (event.key === 'dark') {
                this.currentSkin = event.newValue; // 更新肤色
            }
        },
        handleFlowCongestionAlarmChange() {
        // 在这里处理Switch切换事件
        this.$http
            .wisdomPost('/deviceDetail/updateFlowCongestionAlarm', {portId:this.portId,flowCongestionAlarm: this.flowCongestionAlarm })
            .then(({ code, data, msg }) => {
                if (code === 1) {
                    this.$Message.success(this.$t('common_controls_succ'));
                    // this.query.pageNo = 1;
                    // this.getDialList();
                    this.selectedIds = new Set();
                    } else {
                    this.$Message.warning(msg);
                }
                }).catch(err => {
                throw new Error(err);
                });
        },
        initEchart() {
            if (this.indexChart) {
                this.indexChart.clear();
            }
            var that = this;
            this.indexChart = echarts.init(document.getElementById('indexChart'));
            this.indexChart.setOption(this.Option(), true);
            this.indexChart.getZr().on("mousewheel", (params) => {

                let wheelDelta = params.wheelDelta;

                // debugger
                    // console.log("aaaa");
                let beforeLevelTemp = this.beforeLevel; //获取delay保存的顶层粒度级别
                let startValue = this.indexChart.getModel().option.dataZoom[0].startValue;
                let endValue = this.indexChart.getModel().option.dataZoom[0].endValue;
                this.indexChart.startTime = new Date(startValue).format2(
                    "yyyy-MM-dd HH:mm:ss"
                );
                this.indexChart.endTime = new Date(endValue).format2(
                    "yyyy-MM-dd HH:mm:ss"
                );
                let differ = endValue - startValue;
                let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
                let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
                let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
                let start = this.indexChart.getModel().option.dataZoom[0].start;
                let end = this.indexChart.getModel().option.dataZoom[0].end;
                var zoomFlog = false;

                // 如果数据在加载中
                if(that.isLoading){
                    console.log("数据加载中，不能缩放")
                    return;
                }
                
                if(wheelDelta >= 0){
                    // 放大
                    // 传值给后端：1 秒，2 分钟，3 小时 ，0 天
                    if (that.level == 3) {
                        console.log("放大，分钟粒度------------------------")
                        // 小时粒度
                        if (differ < minute) {
                            that.level = 2;
                            zoomFlog = true;
                        }
                    }else if (that.level == 2) {
                        console.log("放大，原始粒度------------------------")
                        // 分钟粒度(不能在缩放了)
                        if (differ < original) {
                            that.level = 2;
                            zoomFlog = false;
                        }
                    }

                    console.log("放大 ----------------" , that.level , beforeLevelTemp , zoomFlog ,start,end); 

                    // 分钟已经是最小的粒度
                    if(zoomFlog){
                        setTimeout(() => {
                            that.getListTemp(that.indexChart.startTime,that.indexChart.endTime); 
                        }, 300);
                    }
              
                }else{

                     if (start == 0 && end == 100) {
                        // 判断当前处于什么粒度，1 秒，2 分钟，3 小时 ，0 天
                        if (that.level == beforeLevelTemp) {
                            console.log("粒度相等,不做缩小------------------------")
                        } else{
                            if (that.level == 1 || that.level == 0 ) {
                                that.level = 2;
                                zoomFlog = true;
                            }else if (that.level == 2) {
                                that.level = 3;
                                zoomFlog = true;
                            } else if (that.level == 3) {
                                that.level = 0;
                                zoomFlog = true;
                            }
                            
                            if(zoomFlog){
                                 setTimeout(() => {
                                    that.getListTemp(that.indexChart.startTime,that.indexChart.endTime); 
                                 }, 300);
                            }
                        }                  
                     }
                     console.log("缩小 ------------------------",  that.level, beforeLevelTemp , zoomFlog,start,end)  
                }
               
            //     if (this.wheelDelta >= 0) {
            //     console.log("wheellevel",this.level)   
            //     // level : 0 天,1 小时，2 分钟，3 秒
            //     if (this.level == 3) {
            //         // 小时粒度
            //         console.log("0differ",differ+"minute"+minute)   
            //         if (differ < minute) {
            //             // 小时粒度
            //             this.getListTemp(this.indexChart.startTime,this.indexChart.endTime); 
            //             this.wheelDelta = -1;           
            //         }
            //         //   }
            //     } else if (this.level == 2) {
            //         // 分钟粒度
            //         console.log("1differ",differ+"original"+original)   
            //         this.getListTemp(this.startTime,this.endTime);                             
            //     }
            // }else{
            //     console.log("6start",start+"end"+end)
                
            //     if (start == 0 && end == 100) {
            //         //是否处在缩放过程中
            //         if (this.level == beforeLevelTemp) {
            //             if (this.level != 2) {
            //                 this.wheelDelta = 1
            //             }
            //         } else {
            //             if (this.level == 3) {
            //                 this.level = 2;
            //                 this.wheelDelta = 1;
            //             }else{
            //                 if(this.originalStartTime == this.indexChart.startTime && this.originalEndTime == this.indexChart.endTime ){
            //                     this.getListTemp(this.startTime,this.endTime);
            //                 }
            //             }
            //         }
            //     }
            // }
            });
        },
        Option() {
            let optionArr = [
                {
                    title: [
                        {
                            show: this.isNoData,
                            textStyle: {
                            color: this.isdarkSkin == 1 ? '#5CA0D5' :"grey",
                            fontSize: 16
                            },
                            text: this.$t('comm_no_data'),
                            left: "center",
                            top: "90px"
                        },
                        {
                            show: this.isNoData,
                            textStyle: {
                            color: this.isdarkSkin == 1 ? '#5CA0D5' :"grey",
                            fontSize: 16
                            },
                            text: this.$t('comm_no_data'),
                            left: "center",
                            top: "310px"
                        }
                    ],
                    tooltip: this.setDelayLossTooltip(),
                    axisPointer: {
                        type: 'shadow',
                        link: {
                            xAxisIndex: 'all'
                        }
                    },
                    grid: [
                        {
                            left: '80',
                            right: '80',
                            top: '40px',
                            height: '150px'
                        },
                        {
                            left: '80',
                            right: '80',
                            top: '260px',
                            height: '150px'
                        }
                    ],
                    legend: [
                        {
                            show: true,
                            top: '0%',
                            right: '40%',
                            gridIndex: 0,
                            icon: 'roundRect',
                            itemWidth: 16,
                            itemHeight: 12,
                            textStyle: {
                                color: this.isdarkSkin == 1 ? '#fff' : '#484b56',
                                fontSize: 12,
                                fontFamily: 'MicrosoftYaHei-Bold'
                            },
                            color:this.delayLossColor,
                            data: [this.$t('dashboard_inlet_velocity'), this.$t('dashboard_outflow_velocity'), this.$t('view_Leased_type_6'), this.$t('view_Leased_type_8')]
                        },
                        {
                            show: this.echart2.show,
                            top: '230px',
                            right: '40%',
                            icon: 'roundRect',
                            gridIndex: 1,
                            itemWidth: 16,
                            itemHeight: 12,
                            textStyle: {
                                color: this.isdarkSkin == 1 ? '#fff' : '#484b56',
                                fontSize: 12,
                                fontFamily: 'MicrosoftYaHei-Bold'
                            },
                            color:this.flowColor,
                            data: [this.$t('device_receive_packet_loss'), this.$t('device_sending_packet_loss'), this.$t('device_receive_error'), this.$t('device_sending_error')]
                        }
                    ],
                    xAxis: [
                        {
                            type: 'time',
                            gridIndex: 0,
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dashed',
                                    color: this.isdarkSkin == 1 ? 'rgba(42, 56, 64, 1)' : '#e3e7f2'
                                }
                            },
                            axisLabel: { color: this.isdarkSkin == 1 ? '#617ca5' : '' },
                            axisLine: {
                                lineStyle: {
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#676f82',
                                    width: 1
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            type: 'time',
                            gridIndex: 1,
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dashed',
                                    color: this.isdarkSkin == 1 ? 'rgba(42, 56, 64, 1)' : '#e3e7f2'
                                }
                            },
                            axisLabel: { color: this.isdarkSkin == 1 ? '#617ca5' : '' },
                            axisLine: {
                                lineStyle: {
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#676f82',
                                    width: 1
                                }
                            }
                        }
                    ],
                    yAxis: [
                        {
                            name: this.$t('specialmonitor_flow_rate'),
                            type: 'value',
                            scale: true,
                            gridIndex: 0,
                            position: 'left',
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dashed',
                                    width: 0.5,
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#e3e7f2'
                                }
                            },
                            axisLabel: {
                              show: true,
                              formatter: value => {
                                return this.getUnit(value, true, true);
                              }
                            }
                        },
                        {
                            name: this.$t('specquality_utilization'),
                            type: 'value',
                            scale: true,
                            gridIndex: 0,
                            min: 0,
                            max: 100,
                            position: 'right',
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dashed',
                                    width: 0.5,
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#e3e7f2'
                                }
                            }
                        },
                        {
                            name: this.$t('device_packs_number'),
                            show: this.echart2.show,
                            type: 'value',
                            scale: true,
                            position: 'left',
                            gridIndex: 1,
                            axisTick: {
                                show: false
                            },
                            axisLine: {
                                symbol: ['none', 'arrow'],
                                symbolSize: [6, 10],
                                lineStyle: {
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#676f82',
                                    width: '1' //坐标线的宽度
                                }
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    type: 'dashed',
                                    width: 0.5,
                                    color: this.isdarkSkin == 1 ? '#617ca5' : '#e3e7f2'
                                }
                            },
                            axisLabel: {
                              show: true,
                              formatter: value => {
                                // return this.getUnit(value, true, true);
                                return value
                              }
                            }
                        }
                    ],
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: this.flowIntoSpeeds.length > 0 ? [0, 1] : [1],
                            startValue: this.startValue,
                            endValue: this.endValue
                        },
                        {
                            type: 'slider',
                            left: '5%',
                            right: '5%',
                            top: '460px',
                            xAxisIndex: this.receiveLossPackages.length > 0 ? [0, 1] : [1],
                            realtime: true,
                            startValue: this.startValue,
                            endValue: this.endValue,
                            fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // 'rgba(2, 29, 54, 1)',
                            borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // 'rgba(22, 67, 107, 1)',
                            handleStyle: {
                                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , // 'rgba(2, 67, 107, 1)'
                            },
                            textStyle: {
                                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , // this.isdarkSkin == 1 ? '#617ca5' : '#e3e7f2'
                            }
                        }
                    ],
                    series: [
                        {
                            type: 'line',
                            name: this.$t('dashboard_inlet_velocity'),
                            xAxisIndex: 0,
                            yAxisIndex: 0,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            color: this.delayLossColor[0],
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color:  'rgba(129,200,255, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color:  'rgba(129,200,255, 0.2)'
                                        }
                                    ]
                                }
                            },
                            data: this.flowIntoSpeeds
                        },
                        {
                            type: 'line',
                            name: this.$t('dashboard_outflow_velocity'),
                            xAxisIndex: 0,
                            yAxisIndex: 0,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            color: this.delayLossColor[1],
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color:  'rgba(155,251,174, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(155,251,174, 0.2)'
                                        }
                                    ]
                                }
                            },
                            data: this.flowOutSpeeds
                        },
                        {
                            type: 'line',
                            name: this.$t('view_Leased_type_6'),
                            xAxisIndex: 0,
                            yAxisIndex: 1,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            color: this.delayLossColor[2],
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(2,144,253, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(2,144,253, 0.2)'
                                        }
                                    ]
                                }
                            },
                            data: this.intoUsages
                        },
                        {
                            type: 'line',
                            name: this.$t('view_Leased_type_8'),
                            xAxisIndex: 0,
                            yAxisIndex: 1,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            color: this.delayLossColor[3],
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(0,255,238, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(0,255,238, 0.2)'
                                        }
                                    ]
                                }
                            },
                            data: this.outUsages
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('device_receive_packet_loss'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            xAxisIndex: 1,
                            yAxisIndex: 2, // 指定y轴
                            color: this.flowColor[0],
                            data: this.receiveLossPackages,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(129,200,255, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(129,200,255, 0.2)'
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('device_sending_packet_loss'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            xAxisIndex: 1,
                            yAxisIndex: 2,
                            color: this.flowColor[1],
                            data: this.sendLossPackages,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(155,251,174, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(155,251,174, 0.2)'
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('device_receive_error'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            xAxisIndex: 1,
                            yAxisIndex: 2,
                            color: this.flowColor[2],
                            data: this.receiveErrors,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(2,144,253, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(2,144,253, 0.2)'
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            show: this.echart2.show,
                            name: this.$t('device_sending_error'),
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 3,
                            xAxisIndex: 1,
                            yAxisIndex: 2,
                            color: this.flowColor[3],
                            data: this.sendErrors,
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 1
                                    }
                                }
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(0,255,238, 0.6)'
                                        },
                                        {
                                            offset: 0.8,
                                            color: 'rgba(0,255,238, 0.2)'
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            ];
            return optionArr[0];
        },
        setDelayLossTooltip() {
            let _self = this;
            return eConfig.tip('axis', function (param) {
                _self.scale = param[0].data[0];
                var obj = {};
                param = param.reduce(function (item, next) {
                    obj[next.seriesIndex] ? '' : (obj[next.seriesIndex] = true && item.push(next));
                    return item;
                }, []);
                let delayTime = '',
                    delayTip = '',
                    flowTime = '',
                    flowTip = '';
                for (let i = 0, len = param.length; i < len; i++) {
                    if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1 || param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
                        delayTime = param[i].data[0] + '<br />';
                        delayTip += '<span class="tooltip-round" style="background-color:' + param[i].color + '"></span>';
                        delayTip += param[i].seriesName + '：' +
                            (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] < 0
                                ? '--'
                                : param[i].seriesIndex === 0 || param[i].seriesIndex === 1
                                    ? _self.flowSize(param[i].value[1], true, true)
                                    : param[i].value[1] + _self.typeUnit[param[i].seriesName]) + '<br />';
                    //   console.log(_self.typeUnit[param[i].seriesName]);
                    }

                    if (param[i].seriesIndex === 4 || param[i].seriesIndex === 5 || param[i].seriesIndex === 6 || param[i].seriesIndex === 7) {
                        flowTime = param[i].data[0] + '<br />';
                        flowTip += '<span class="tooltip-round" style="background-color:' + param[i].color + '"></span>';
                        flowTip += param[i].seriesName + '：' + (param[i].value[1] === undefined || param[i].value[1] === null || param[i].value[1] === '' || param[i].value[1] < 0 ? '--' : param[i].value[1]) + '<br />';
                    }
                }
                return delayTime + delayTip + flowTime + flowTip;
            });
        },
        getList() {
            let param = { ...this.query };
            param.startTime = this.startTime;
            param.endTime = this.endTime;
            param.devId = this.devId;
            param.portId = this.portId;
            this.isNoData = false;
            this.isLoading = true;
            this.$http
                .PostJson('/deviceDetail/querySnmpFlow', param)
                .then(res => {
                    if (res.code === 1) {
                        if (res.data && JSON.stringify(res.data) != '{}') {
                            this.dataHandle(res.data);
                            this.beforeLevel = res.data.level;
                            this.level = res.data.level;
                            this.startValue = res.data.flowIntoSpeeds[0][0];
                            this.endValue = res.data.flowIntoSpeeds[res.data.flowIntoSpeeds.length - 1][0];
                            this.isNoData = false;
                            this.originalStartTime = param.startTime;
                            this.originalEndTime = param.endTime;
                        } else {
                            this.clearDara();
                             this.isNoData = true;
                        }
                        this.initEchart();
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        getFlowCongestionAlarmByPortId() {
            this.$http
                .wisdomPost('/deviceDetail/getFlowCongestionAlarmByPortId', {portId:this.portId})
                .then(res => {
                    if (res.code === 1) {
                        if (res.data && JSON.stringify(res.data) != '{}') {
                            this.flowCongestionAlarm = res.data.flowCongestionAlarm;
                        } 
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .finally(() => {});
        },
        getListTemp(startTime,endTime) {
            if(!this.isLoading){
                this.isLoading = true;
                let param = { ...this.query };
                // 赋值操作
                if(this.level >=0){
                    param.level = this.level;
                }
                param.startTime = startTime;
                param.endTime = endTime;
                param.devId = this.devId;
                param.portId = this.portId;
                this.isNoData = false;
                this.$http
                    .PostJson('/deviceDetail/querySnmpFlow', param)
                    .then(res => {
                        if (res.code === 1) {
                            if (res.data && JSON.stringify(res.data) != '{}') {
                                this.dataHandle(res.data);
                                this.level = res.data.level;
                                this.startValue = res.data.flowIntoSpeeds[0][0];
                                this.endValue = res.data.flowIntoSpeeds[res.data.flowIntoSpeeds.length - 1][0];
                                this.isNoData = false;
                            } else {
                                this.clearDara();
                                 this.isNoData = true;
                            }
                            this.initEchart();
                        } else {
                            this.$Message.error(res.msg);
                        }
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            }
            
        },
        clearDara() {
            
            this.flowIntoSpeeds = []; // 入流速趋势图数据
            this.flowOutSpeeds = []; // 出流速趋势图数据
            this.intoUsages = []; // 入利用率趋势图数据
            this.outUsages = []; // 出利用率趋势图数据

            this.receiveLossPackages = []; // 接收丢包趋势图数据
            this.sendLossPackages = []; // 发送丢包趋势图数据
            this.receiveErrors = []; // 接收错误趋势图数据
            this.sendErrors = []; // 发送错误趋势图数据

            this.listData1.maxValue = ['--', '--', '--', '--'];
            this.listData1.averageValue = ['--', '--', '--', '--'];
            this.listData1.currentValue = ['--', '--', '--', '--'];

            this.listData2.maxValue = ['--', '--', '--', '--'];
            this.listData2.averageValue = ['--', '--', '--', '--'];
            this.listData2.currentValue = ['--', '--', '--', '--'];
        },
        //记录滑块的位置
        setPs(level, position) {
        //记录滚动条的位置
        if (level == 3) {
            this.delayPs.psD.start = position[0];
            this.delayPs.psD.end = position[1];
            sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
        } else if (level == 2) {
            this.delayPs.psM.start = position[0];
            this.delayPs.psM.end = position[1];
            sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
        }
        },
        dataHandle(data) {
            //this.flowIntoSpeeds = data.flowIntoSpeeds.map(item => { return [item[0],item[1]/1000] }); // 入流速趋势图数据
            //this.flowOutSpeeds = data.flowOutSpeeds.map(item => { return [item[0],item[1]/1000] }); // 出流速趋势图数据
            this.flowIntoSpeeds = data.flowIntoSpeeds.map(item => {
                return [item[0], item[1]];
            }); // 入流速趋势图数据
            this.flowOutSpeeds = data.flowOutSpeeds.map(item => {
                return [item[0], item[1]];
            }); // 出流速趋势图数据
            this.intoUsages = data.intoUsages.map(item => {
                return [item[0], item[1]];
            }); // 入利用率趋势图数据
            this.outUsages = data.outUsages.map(item => {
                return [item[0], item[1]];
            }); // 出利用率趋势图数据

            this.receiveLossPackages = data.receiveLossPackages.map(item => {
                return [item[0], item[1]];
            }); // 接收丢包趋势图数据
            this.sendLossPackages = data.sendLossPackages.map(item => {
                return [item[0], item[1]];
            }); // 发送丢包趋势图数据
            this.receiveErrors = data.receiveErrors.map(item => {
                return [item[0], item[1]];
            }); // 接收错误趋势图数据
            this.sendErrors = data.sendErrors.map(item => {
                return [item[0], item[1]];
            }); // 发送错误趋势图数据

            // console.log('this.flowIntoSpeeds', data.maxFlowIntoSpeed);
            // console.log('this.maxFlowOutSpeed', data.maxFlowOutSpeed);
            this.listData1.maxValue = [data.maxFlowIntoSpeed == null ? '--' : this.flowSize(data.maxFlowIntoSpeed), data.maxFlowOutSpeed == null ? '--' : this.flowSize(data.maxFlowOutSpeed), data.maxIntoUsage == null ? '--' : data.maxIntoUsage + '%', data.maxOutUsage == null ? '--' : data.maxOutUsage + '%'];
            this.listData1.averageValue = [data.avgFlowIntoSpeed == null ? '--' : this.flowSize(data.avgFlowIntoSpeed), data.avgFlowOutSpeed == null ? '--' : this.flowSize(data.avgFlowOutSpeed), data.avgIntoUsage == null ? '--' : data.avgIntoUsage + '%', data.avgOutUsage == null ? '--' : data.avgOutUsage + '%'];
            this.listData1.currentValue = [data.curFlowIntoSpeed == null ? '--' : this.flowSize(data.curFlowIntoSpeed), data.curFlowOutSpeed == null ? '--' : this.flowSize(data.curFlowOutSpeed), data.curIntoUsage == null ? '--' : data.curIntoUsage + '%', data.curOutUsage == null ? '--' : data.curOutUsage + '%'];

            this.listData2.maxValue = [data.maxReceiveLossPackage == null ? '--' : data.maxReceiveLossPackage, data.maxSendLossPackage == null ? '--' : data.maxSendLossPackage, data.maxReceiveError == null ? '--' : data.maxReceiveError, data.maxSendError == null ? '--' : data.maxSendError];
            this.listData2.averageValue = [data.avgReceiveLossPackage == null ? '--' : data.avgReceiveLossPackage, data.avgSendLossPackage == null ? '--' : data.avgSendLossPackage, data.avgReceiveError == null ? '--' : data.avgReceiveError, data.avgSendError == null ? '--' : data.avgSendError];
            this.listData2.currentValue = [data.curReceiveLossPackage == null ? '--' : data.curReceiveLossPackage, data.curSendLossPackage == null ? '--' : data.curSendLossPackage, data.curReceiveError == null ? '--' : data.curReceiveError, data.curSendError == null ? '--' : data.curSendError];
        }
    }
};
</script>
<style scoped>
.section-body {
  /*background: #fff;*/
  padding: 20px;
  border-radius: 4px;
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.fn_item {
  display: flex;
  align-items: center;
}

.fn_item_label {
  margin-right: 10px;
}
</style>
