<template>
  <div>
    <Table
      ref="pathList"
      stripe
      :columns="columns"
      :data="tableData"
      size="small"
      :no-data-text="
        loading
          ? ''
          : tableList.length > 0
          ? ''
        : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
      "
    >
    </Table>
    <Loading :loading="loading"></Loading>
    <div class="tab-page" v-if="tableList.length > 0">
      <Page
        v-page
        :current.sync="currentNum"
        :page-size="pageSize"
        :total="totalCount"
        :page-size-opts="pageSizeOpts"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-elevator
        show-sizer
      >
      </Page>
    </div>
  </div>
</template>

<script>
import '@/config/page.js';
import { mapGetters } from 'vuex';
import ipv6Format from "@/common/ipv6Format";

export default {
    name: 'NetworkList',
    data() {
        return {
                  currentSkin: sessionStorage.getItem('dark') || 1,
            loading: false,
            query: {
                pageNo: 1, //页数
                pageSize: 10, //每页展示多少数据
                devId:null
            },
            tableList: [],
            currentNum: 1,
            pageNo: 1,
            pageSize: 10,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            columns: [
                {
                    title: this.$t('device_local_port'),
                    key: 'ifName',
                    align: 'left',
                    minWidth: 100,
                    render: (h, params) => {
                        let str = params.row.ifName,
                            span = h(
                                'span',
                                {
                                    class: 'icon-index-name'
                                },
                                str === undefined || str === null || str === '' ? '--' : str
                            ),
                            array = [span];
                        return h('div', array);
                    }
                },
                {
                    title: this.$t('device_local_mac'),
                    key: 'ifMac',
                    align: 'left',
                    minWidth: 120,
                    tooltip:true
                },
                {
                    title: this.$t('device_local_ip'),
                    key: 'deviceIp',
                    align: 'left',
                    width: 200,
                    tooltip:true,
                    render: (h, params) => {
                        let str = params.row.deviceIp;
                        let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                    }
                },
                {
                    title: this.$t('device_peer_device_model'),
                    key: 'toModelName',
                    align: 'left',
                    minWidth: 100,
                    tooltip:true
                },
                {
                    title: this.$t('device_peer_device'),
                    key: 'toDeviceName',
                    align: 'left',
                    ellipsis: true,
                    minWidth: 100,
                    tooltip:true,
                    render: (h, params) => {
                        // let gatherDeviceName =params.row.gatherDeviceName;
                        // let aliasDeviceName = params.row.aliasDeviceName,
                        let gatherDeviceName =params.row.toGatherDeviceName;
                        let aliasDeviceName = params.row.toDeviceName;

                        var str = aliasDeviceName+'('+gatherDeviceName+')';

                        let str1 = str;
                        if (str.length > 15) {
                            str1 = str.substring(0, 15) + '...';
                        }

                         return h(
                                    'Tooltip',
                                    {
                                        props: {
                                            placement: 'left'
                                        },
                                    },
                                    [
                                        str1,
                                        h(
                                            'span',
                                            {
                                                slot: 'content', //slot属性
                                                style: {
                                                    whiteSpace: 'normal',
                                                    wordBreak: 'break-all'
                                                }
                                            },
                                            str
                                        )
                                    ]                   
                );

                        // let  span = h(
                        //         'span',
                        //         {
                        //             class: 'icon-index-name',
                        //             style: {
                        //                 textAlign: "left",
                        //                 width: "100%",
                        //                 textIndent: "0px",
                        //                 overflow: "hidden", //超出的文本隐藏
                        //                 textOverflow: "ellipsis", //溢出用省略号显示
                        //                 whiteSpace: "nowrap", //溢出不换行
                        //             },
                        //             attrs: {
                        //                 title: str,
                        //             },
                        //         },
                        //         str
                        //     );
                        // let array = [span];
                        // return h('div', array);
                    }
                },
                {
                    title: this.$t('device_peer_port'),
                    key: 'toIfName',
                    align: 'left',
                    minWidth: 100,
                    tooltip:true
                },
                {
                    title: this.$t('device_peer_mac'),
                    key: 'toIfMac',
                    minWidth: 120,
                    align: 'left',
                    tooltip:true
                },
                {
                    title: this.$t('device_peer_ip'),
                    key: 'toDeviceIp',
                    width: 200,
                    align: 'left',
                    tooltip:true,
                    render: (h, params) => {
                        let str = params.row.toDeviceIp;
                        let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                    }
                }
            ]
        };
    },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
            // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        // console.log(this.deviceInfo)
        // debugger
        // this.getList();
    },
    computed:{
         ...mapGetters({ deviceInfo: 'getRowData' }),
        tableData:{
            get : function() {
                let arr = this.tableList.map(item=>{
                    for (const key in item) {
                        let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                        item[key] = str
                    }
                   return item
                })
                return arr
            },
        },
    },
    methods: {
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        pageChange(val) {
            //表格页码切换
            this.currentNum = val;
            this.query.pageNo = this.currentNum;
            this.getList();
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList();
        },
        getList() {
            this.loading = true;
            this.query.devId = this.deviceInfo.id;
            this.$http
                .PostJson('/deviceDetail/networkNeighbor', this.query)
                .then(res => {
                    
                    if (res.code === 1) {
                        this.tableList = res.data.records;
                        this.totalCount = res.data.total || 0;
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    }
};
</script>

<style>
</style>