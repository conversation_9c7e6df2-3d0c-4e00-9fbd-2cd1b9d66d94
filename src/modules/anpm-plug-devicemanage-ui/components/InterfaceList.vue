<template>
  <div>
    <Table
      ref="pathList"
      stripe
      :columns="columns"
      :data="tableData"
      size="small"
      @on-sort-change="sortSum"
      :no-data-text="
        loading
          ? ''
          : tableList.length > 0
          ? ''
          : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
      "
    >
      <template slot-scope="{ row }" slot="ifName">
        {{
          row.ifName === undefined || row.ifName === null || row.ifName === ""
            ? "--"
            : row.ifName
        }}
      </template>
      <template slot-scope="{ row }" slot="typeLable">
        {{
          row.typeLable === undefined ||
          row.typeLable === null ||
          row.typeLable === ""
            ? "--"
            : row.typeLable
        }}
      </template>
    </Table>
    <Loading :loading="loading"></Loading>
    <div class="tab-page" v-if="tableList.length > 0">
      <Page
        v-page
        :current.sync="currentNum"
        :page-size="pageSize"
        :total="totalCount"
        :page-size-opts="pageSizeOpts"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-elevator
        show-sizer
      >
      </Page>
    </div>
  </div>
</template>

<script>
import '@/config/page.js';
import { mapGetters } from 'vuex';
import echartFn from '@/common/mixins/echartFun';
import ipv6Format from "@/common/ipv6Format";

export default {
    name: 'InterfaceList',
    mixins: [echartFn],
    data() {
        return {
                  currentSkin: sessionStorage.getItem('dark') || 1,
            loading: false,
            query: {
                pageNo: 1, //页数
                pageSize: 10, //每页展示多少数据
                devId: '',
                fieldName:'',
                orderBy:"",
            },
            tableList: [{}],
            currentNum: 1,
            pageNo: 1,
            pageSize: 10,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            columns: [
               
                {
                    title: this.$t('device_interface_index'),
                    key: 'ifIdx',
                    align: 'left',
                    minWidth: 160,
                        sortable: "custom",
                    render: (h, params) => {
                        let str = params.row.ifIdx,
                            span = h(
                                'span',
                                {
                                    class: 'icon-index-name'
                                },
                                str === undefined || str === null || str === '' ? '--' : str
                            ),
                            array = [span];
                        return h('div', array);
                    }
                },
                {
                    title: this.$t('interface_name'),
                    key: 'ifName',
                    align: 'left',
                    minWidth: 200,
                    sortable: "custom",
                    tooltip:true
                },
                {
                    title: this.$t('interface_ip'),
                    key: 'ifIp',
                    align: 'left',
                    minWidth: 200,
                    tooltip:true,
                    sortable: "custom",
                    render: (h, params) => {
                        let str = params.row.ifIp;
                        let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                         if(str !== "--") {
              return h('div',{class: 'row2-tooltip'},[
                      h('Tooltip',{
                        props: {
                          placement:'top-start',
                          content: str,
                        }

                      },str)

                    ])
                    }else {
                      return h('div',str)
                    }
                        // return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                    }
                },
                {
                    title: this.$t('device_running_state'),
                    key: 'runLable',
                    align: 'left',
                    minWidth: 150,
                    tooltip:true,
                        sortable: "custom",
                },
                {
                    title: this.$t('device_manage_state'),
                    key: 'manageLable',
                    align: 'left',
                    minWidth: 160,
                    tooltip:true,
                        sortable: "custom",
                },
                {
                    title: this.$t('duplex_status_lable'),
                    key: 'duplexStatusLable',
                    align: 'left',
                    minWidth: 160,
                    tooltip:true,
                    sortable: "custom",
                },
                {
                    title: this.$t('device_interface_type'),
                    key: 'typeLable',
                    align: 'left',
                    width: 150,
                    tooltip:true,
                        sortable: "custom",
                },
                {
                    title: 'ARP',
                    key: 'arpCount',
                    align: 'left',
                    width: 100,
                     sortable: "custom",
                    tooltip:true
                },
                {
                    title: 'MAC',
                    key: 'macCount',
                    minWidth: 120,
                    align: 'left',
                     sortable: "custom",
                    tooltip:true
                },
                {
                    title: this.$t('device_interface_bandwidth'),
                    key: 'bandwidthValue',
                    width: 190,
                    align: 'left',
                    tooltip:true,
                        sortable: "custom",
                },
                {
                    title: this.$t('comm_inflow'),
                    key: 'flowIntoSpeed',
                    width: 160,
                    align: 'left',
                        sortable: "custom",
                    render: (h, params) => {
                        let str = params.row.flowIntoSpeed;
                        return h('span', str === undefined || str === null || str === '' ? '--' : this.flowSize(str));
                    }
                },
                {
                    title: this.$t('comm_outflow'),
                    key: 'flowOutSpeed',
                    width: 165,
                    align: 'left',
                        sortable: "custom",
                    render: (h, params) => {
                        let str = params.row.flowOutSpeed;
                        return h('span', str === undefined || str === null || str === '' ? '--' : this.flowSize(str));
                    }
                },
                {
                    title: this.$t('device_incoming_utilization'),
                    key: 'intoUsage',
                    width: '150',
                    align: 'left',
                        sortable: "custom",
                    render: (h, params) => {
                      let str = params.row.intoUsage;
                      if(str === undefined || str === null || str === '' || Number(str)<= 0 || str === '--' ){
                        if(params.row.flowIntoSpeed === undefined || params.row.flowIntoSpeed === null || params.row.flowIntoSpeed === '--'){
                            str='--';
                        }else{
                            str='0';
                        }
                        return h('span', str);
                      }else {
                        return h('span', str + '%');
                      }
                    }
                },
                {
                    title: this.$t('device_outgoing_utilization'),
                    key: 'outUsage',
                    width: 150,
                    align: 'left',
                        sortable: "custom",
                    render: (h, params) => {
                        let str = params.row.outUsage;
                        if(str === undefined || str === null || str === '' || Number(str)<= 0 || str === '--' ){
                             if(params.row.flowOutSpeed === undefined || params.row.flowOutSpeed === null || params.row.flowIntoSpeed === '--'){
                                str='--';
                            }else{
                                str='0';
                            }
                            return h('span', str);
                        }else {
                            return h('span', str + '%');
                        }
                    }
                },
                {
                    title: this.$t('device_receive_packet_loss'),
                    key: 'receiveLossPackage',
                    width: 130,
                    align: 'left',
                        sortable: "custom",
                    tooltip:true
                },
                {
                    title: this.$t('device_sending_packet_loss'),
                    key: 'sendLossPackage',
                    width: 150,
                    align: 'left',
                        sortable: "custom",
                    tooltip:true
                },
                {
                    title: this.$t('device_receive_error'),
                    key: 'receiveError',
                    width: 150,
                    align: 'left',
                        sortable: "custom",
                    tooltip:true
                },
                {
                    title: this.$t('device_sending_error'),
                    key: 'sendError',
                    width: 150,
                    align: 'left',
                        sortable: "custom",
                    tooltip:true
                },
                 {
                    title: this.$t('server_view'),
                    key: 'taskNum',
                    align: 'center',
                    width: 100,
                    fixed:'right',
                    render: (h, params) => {
                        let str = params.row.taskNum,
                            i = h('i', {
                                // class: 'icon-box icon-index myIconClass',
                                class: this.currentSkin == 1 ? "icon-box icon-index myIconClass":"icon-box light-icon-index myIconClass",

                                style: 'cursor: pointer;',
                                on: {
                                    click: () => {
                                        this.actionClick(params.row, 'index', event);
                                    }
                                }
                            }),
                            array = [i];
                        return h('div', array);
                    }
                },
            ]
        };
    },
    computed: {
        ...mapGetters({ rowData: 'getRowData' }),
        tableData:{
            get : function() {
                let arr = this.tableList.map(item=>{
                    for (const key in item) {
                        let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                        item[key] = str
                    }
                   return item
                })
                return arr
            },
        },
        devId() {
            return this.rowData.id
        }
    },
    watch: {
        devId(val) {
            this.getList();
        }
    },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
            // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        // console.log(this.rowData)
        // debugger
       if(this.rowData.id) {
        // debugger
        this.getList();

       }
             

        
       
    },
    activated() {
        this.getList();
    },
    methods: {
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
            sortSum(column) {
                console.log(column);
                // "normal"
                this.query.fieldName = "";
                this.query.orderBy = "";
                if(column.order !== "normal"){
                    this.query.fieldName = column.key;
                    this.query.orderBy = column.order;
                }
                this.getList();

    },
        actionClick(row) {
            this.$router.push({
                path: '/portInfo',
                query: {
                    devPortId: row.id,
                    devId: row.devId
                }
            });
        },
        pageChange(val) {
            //表格页码切换
            this.currentNum = val;
            this.query.pageNo = this.currentNum;
            this.getList();
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList();
        },
        getList() {
            //  debugger
            this.loading = true;
            this.query.devId = this.rowData.id;
            console.log(this.query);

         
            this.$http
                .PostJson('/deviceDetail/queryInterfaceInfo', this.query)
                .then(res => {
                    
                    if (res.code === 1) {
                        this.tableList = res.data.records;
                        this.totalCount = res.data.total || 0;
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    }
};
</script>

<style>
</style>