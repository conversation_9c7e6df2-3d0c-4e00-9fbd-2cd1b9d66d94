<template>
    <section>
        <table class="ourTableStyle">
            <thead>
                <tr>
                    <th class="out"><b></b> <em></em></th>
                    <th v-for="(item,index) in data.title" :key="index">{{item}}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{$t('rphealthy_max')}}</td>
                    <td v-for="(item,index) in data.maxValue" :key="index"><div :title="item" class="tableTd">{{ item }}</div></td>
                </tr>
                <tr>
                    <td>{{$t('rphealthy_aver')}}</td>
                    <td v-for="(item,index) in data.averageValue" :key="index"><div :title="item" class="tableTd">{{ item }}</div></td>
                </tr>
                <tr>
                    <td>{{$t('device_current_value')}}</td>
                    <td v-for="(item,index) in data.currentValue" :key="index"><div :title="item" class="tableTd">{{ item }}</div></td>
                </tr>
            </tbody>
        </table>
    </section>
</template>

<script>
export default {
    name: 'IndexList',
    props: {
        data: {
            typeof: Object,
            default: {
                title: [],
                maxValue: [],
                averageValue: [],
                currentValue: []
            }
        }
    }
};
</script>

<style scoped>
table.ourTableStyle {
    border: 1px solid #fff;
    /*去掉表格之间的空隙*/
    border-collapse: collapse;
}
table.ourTableStyle th,
table.ourTableStyle tr > td:first-child {
    background: #f8f8f9;
}
table.ourTableStyle th,
table.ourTableStyle td {
    border: 1px solid #ccc;
    width: 80px;
    font-size: 14px;
    line-height: 40px;
    text-align: left;
  }
  table.ourTableStyle .out {
    text-align: center;
}
table.ourTableStyle .out {

    position: relative; /*让里面的两个子容器绝对定位*/
    overflow: hidden;
}
table.ourTableStyle .out b {
    font-style: normal;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 40px;
    border-top: 40px solid transparent;
    border-left: 80px solid transparent;
}

table.ourTableStyle .out em {
    font-style: normal;
    display: block;
    position: absolute;
    top: -1px;
    left: 0;
    width: 78px;
    height: 39px;
    border-top: 39px solid transparent;
    border-left: 78px solid transparent;
}
.tableTd{
  text-overflow: ellipsis;
  overflow: hidden;
  width:90%;
  white-space: nowrap;
  text-align: center;
}
</style>