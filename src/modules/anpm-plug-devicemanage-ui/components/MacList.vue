<template>
    <div>
        <Table ref="pathList" stripe :columns="columns" :data="tableData" size="small"
        :no-data-text=" loading ? '' : tableList.length > 0 ? ''  : 
        currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'">
        </Table>
        <div class="tab-page" v-if="tableList.length > 0">
            <Page v-page :current.sync="currentNum" :page-size="pageSize" :total="totalCount" :page-size-opts="pageSizeOpts" :prev-text="$t('common_previous')"
                  :next-text="$t('common_next_page')" @on-change="pageChange" @on-page-size-change="pageSizeChange" show-elevator show-sizer>
            </Page>
        </div>

    </div>
</template>

<script>
import '@/config/page.js';
export default {
    name: 'MacList',
    props: ['startTime', 'endTime', 'devId', 'portId'],
    data() {
        return {
                 currentSkin: sessionStorage.getItem('dark') || 1,
            loading: false,
            query: {
                pageNo: 1, //页数
                pageSize: 10, //每页展示多少数据
                devPortId: this.$route.query.devPortId
            },
            tableList: [],
            currentNum: 1,
            pageNo: 1,
            pageSize: 10,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            columns: [
                {
                    title: this.$t('device_port_index'),
                    key: 'ifIdx',
                    align: 'left',
                    minWidth: 100,
                    tooltip:true
                },
                {
                    title: this.$t('device_discovery_port_name'),
                    key: 'ifName',
                    align: 'left',
                    minWidth: 100,
                    tooltip:true
                },

                {
                    title: 'MAC',
                    key: 'ifMac',
                    minWidth: 120,
                    align: 'left',
                    tooltip:true
                }
            ]
        };
    },
    mounted() {
        this.getList();
         // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    computed:{
        tableData:{
            get : function() {
                let arr = this.tableList.map(item=>{
                    for (const key in item) {
                        let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                        item[key] = str
                    }
                   return item
                })
                return arr
            },
        },
    },
    methods: {
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        pageChange(val) {
            //表格页码切换
            this.currentNum = val;
            this.query.pageNo = this.currentNum;
            this.getList();
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList();
        },
        getList() {
            let param = { ...this.query };
            param.startTime = this.startTime;
            param.endTime = this.endTime;
            param.devId = this.devId;
            param.devPortId = this.portId;
            // if (this.portId && param.devPortId != this.portId) {
            //     param.devPortId = this.portId;
            // }

            this.$http
                .PostJson('/deviceDetail/queryMac', param)
                .then(res => {
                    this.loading = true;
                    if (res.code === 1) {
                        this.tableList = res.data.records;
                        this.totalCount = res.data.total || 0;
                    } else {
                        this.$Message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    }
};
</script>

<style>
</style>