const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/devicemanage',
  },
  {
    path: "/devicemanage",
    name: lan.getLabel("src.DeviceManagement"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-devicemanage-ui/views/index.vue"], resolve)
  },
  {
    path: "/deviceInfo",
    name: lan.getLabel("src.DeviceInformation"),
    meta: {
      authority: true
    },
		component: () =>
      import("@/modules/anpm-plug-devicemanage-ui/views/deviceInfo.vue"),
  },
  {
    path: "/portInfo",
    name: lan.getLabel("src.PortDetails"),
    meta: {
      authority: true
    },
		component: () =>
      import("@/modules/anpm-plug-devicemanage-ui/views/portInfo.vue"),
  },
];
