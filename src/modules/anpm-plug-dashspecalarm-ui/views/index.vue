<template>
  <div
    class="itemEcharts"
    :style=" 'position: relative; height: 100%;background:'+(isbigscreen?'#060D15':isdarkSkin==1?'#00000000':'#ffffff') "
  >
    <Loading :loading="loading"></Loading>
    <div class="lineBox" v-if="!loading && line_List.length>0">
      <!--<Loading :loading="loading"></Loading>-->
      <draw-line
        :node="'line' + index"
        :lineData="item"
        :isbigscreen="isbigscreen"
        :isdarkSkin="isdarkSkin"
        v-for="(item, index) in line_List"
        :key="index"
      ></draw-line>
    </div>
    <div v-else-if="!loading && line_List.length<1" :class="{'fillempty':isdarkSkin == 1 , 'fillempty2':isdarkSkin == 0  }">
      <p class="emptyText">{{$t('common_No_data')}}</p>
    </div>
  </div>
</template>

<script>
  import drawLine from "@/common/echarts/dashboardLine.vue";
  import '@/timechange'
  export default {
    name: "dashSpecialAlarm",
    components: {
      drawLine
    },
    data() {
      return {
        //刷新时间间隔
        intervalTime:null,
        //刷新参数
        interrefresh:null,
        nowDate:new Date(),
        //时间参数
        startTime:null,
        endTime:null,
        pieLoading: false,
        height: 260,
        loading:true,
        param: {
          // startTime:new Date().format2('yyyy-MM-dd'),
          // endTime:new Date().format2('yyyy-MM-dd'),
          componentId:"",
          groupIdsStr:"",
          statisticsType:"",
        },
        line_List: [],
        typeCode:"special_alarm_trend" ,//此参数由调用的时候传进来，传进来的是构件的code例（route_alarm_trend，special_alarm_trend，snmp_alarm_trend）
        isbigscreen:false,
        isdarkSkin: sessionStorage.getItem('dark') || 1,
      };
    },
    created() {
      if (parent.window.isEdit) {
        const params = window.frames.frameElement.getAttribute('params');
        this.intervalTime = params.split('&&topoId=')[0].split('?intervalTime=')[1];
        this.param.componentId = this.getReqParam(params,1);
        this.param.groupIdsStr = this.getReqParam(params,2);
        this.param.statisticsType = this.getReqParam(params,3);
      }else{
        this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
        this.param.componentId = this.getReqParam(window.frames.name,1);
        this.param.groupIdsStr = this.getReqParam(window.frames.name,2);
        this.param.statisticsType = this.getReqParam(window.frames.name,3);
        if (window.frames.frameElement) {
          window.frames.frameElement.contentWindow.close();
        }
      }
      this.getLine(this.param);
    },
    mounted() {
        // 监听 storage 事件
      window.addEventListener('storage', this.handleStorageChange);
      this.isbigscreen = window.isbigscreen;
      if (this.intervalTime&& Number(this.intervalTime)) {
        this.interrefresh = setInterval(()=>{this.loading = true;this.getLine(this.param);},this.intervalTime * 1000);
      }
    },
    methods: {
         // 获取参数 componentId
      getReqParam(url , type){
         var reg = /componentId=(\d+)/;
          if(type == 2){
            reg = /groupIds=([^&]*)/;
          }else if(type == 3){
            reg = /statisticsType=(\d+)/;
          }
          var match = url.match(reg);
          if (match) {
            return match[1];
          } else {
            return "";
          }
      },
      handleStorageChange(event) {
        if (event.key === 'dark') {
          this.currentSkin = event.newValue; // 更新肤色
        }
      },
      // 获取折线图数据
      getLine(param) {
        let _self = this;
        this.loading = true; // 开始加载
        this.line_List = [];
        let httpRequest = _self.$http.wisdomPost("/home/<USER>", param)
        httpRequest.then(res => {
          let datas = res.data;
          if (res.code === 1) {
            if(this.typeCode=="special_alarm_trend"){//最近30天专线告警走势
              this.line_List.push(datas[0]);
            }
          }else{
            this.line_List = []
          }
        }).catch((error)=>{
          // this.loading = false;
          this.line_List = [];
          if (parent.loading){
            parent.loading['dashspecalarm'] = false;
          }
        }).finally(()=>{
          this.loading = false; // 结束加载
          if (parent.loading){
            parent.loading['dashspecalarm'] = false;
          }
        });
        httpRequest = null
      },

    },
    beforeDestroy(){
      // 移除事件监听
      window.removeEventListener('storage', this.handleStorageChange);
      if (this.interrefresh) {
        clearInterval(this.interrefresh);
        this.interrefresh = null;
      }
    }
  };
</script>
<style scoped>
  .pieSearch{
    width: 42%;
    text-align: center;
  }
  .pieSearch .pieTime{
    display: inline-block;
    width: 100%;
  }
  /deep/.pieSearch .pieTime .ivu-input:hover{
    border-color: #dddddd;
  }
  .pieSearch .pieTime >label{
    font-weight: bold;
  }
  .tableBox {
    margin: 20px 20px 40px 20px;
  }

  .dialTest-tab-title {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .homeBox {
    padding-top: 10px;
    height: 100%;
  }

  .itemEcharts {
    display: flex;
    align-items: center;
  }

  .pieBox {
    /*flex: 1;*/
    width: 42%;
  }

  .cake,
  .brokenLine {
    height: 280px;
  }

  .lineBox {
    width: 100%;
    height: 100%;
  }

  html,
  body {
    position: relative;
    height: 100%;
  }

  body {
    background: #eee;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #000;
    margin: 0;
    padding: 0;
  }
</style>
