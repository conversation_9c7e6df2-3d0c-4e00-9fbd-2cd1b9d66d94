<template>
  <!-- 柱状图 -->
  <div class="bar-graph-box">
    <div v-if="flowTop10.length > 0" class="bar-graph" id="bargraphId">
      <!-- 头部 -->
      <div class="bar-graph-title">
        <div class="bar-graph-title-left">{{ barGraphTitle }}</div>
        <div class="bar-graph-title-right">{{ $t("flow") }}</div>
      </div>
      <!-- /头部 -->
      <!-- 内容 -->
      <div class="bar-graph-content">
        <div
          class="bar-graph-item"
          v-for="(item, index) in flowTop10"
          :key="index"
        >
          <div class="bar-grah-item-left" :style="{ width: titleWidth }">
            <div
              class="bar-grah-item-left-legend"
              :style="{
                backgroundColor:
                  currentSkin == 1 ? colorArr[index] : colorArrLight[index],
              }"
            ></div>

            <div class="bar-grah-item-left-title">
              <Tooltip :content="item.tooltip" placement="bottom-start">{{
                item.name
              }}</Tooltip>
            </div>
          </div>
          <div class="bar-grah-item-center">
            <div
              class="bar-grah-item-center-inner"
              :style="setStyle(item, index)"
            ></div>
          </div>
          <div class="bar-grah-item-right">
            <div class="bar-grah-item-right-percent">{{ item.ratio }}</div>
            <div class="bar-grah-item-right-value">{{ item.flow }}</div>
          </div>
        </div>
      </div>
      <!-- 内容 -->
    </div>
    <div v-else class="annular-no-data">
      <img :src="getImgUrl()" alt="" />
      <div class="emptyText">{{ $t("comm_no_data") }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    flowTop10: {
      type: Array,
      default: () => []
    },
    barGraphTitle: {
      type: String,
      default: ''
    },
    titleWidth: {
      type: String,
      default: '100px'

    }
  },
 data() {
  return {
    currentSkin: sessionStorage.getItem("dark") || 0,
    colorArr:['#02B8FD','#9BFBAE','#D3E4FF','#FEA31B','#FE5C5C','#9560E7','#00FFEE','#0299FD','#07C5A3','#81C8FF'],
    // colorArr:['#02B8FD','#0299FD','#07C5A3','#81C8FF','#02B8FD','#9BFBAE','#D3E4FF','#FEA31B','#FE5C5C','#9560E7'],
    colorArrLight:['#0290FC','#34C5AB','#9BFBAE','#9CD871','#64BCFC','#D3E4FE','#AEBBFD','#FEA326','#FE5C5C','#9560E7'],
    // graphData:[
    //                     { value: 1048, name: 'Search Engine' },
    //                     { value: 735, name: 'Direct' },
    //                     { value: 580, name: 'Email' },
    //                     { value: 484, name: 'Union Ads' },
    //                     { value: 300, name: 'Video Ads' },
    //                     { value: 245, name: 'Engine'},
    //                     { value: 220, name: 'Direct2' },
    //                     { value: 180, name: 'Email2' },
    //                     { value: 140, name: 'Union Ads2' },
    //                     { value: 88, name: 'Video Ads2' }
    //                 ],
  }
 },
 methods: {
    getImgUrl() {
        if(this.currentSkin==0){
            return require('../../../../assets/dashboard/fileempty2.png')
        }else{
            return require('../../../../assets/dashboard/fileempty.png')
        }
    },
    setStyle(item, index) {
        console.log(item, index);
        // let width = item.value / this.graphData[0].value * 100;
        console.log(`${item.ratioDouble}` + '%','width');
        return {
            width: `${item.ratioDouble}` + '%',
            // width: `${width}%`,
            backgroundColor: this.currentSkin == 1 ? this.colorArr[index] : this.colorArrLight[index],
        }
    }
 }
 
}
</script>

<style scoped lang="less">
/deep/.ivu-tooltip-inner {
  max-width: unset;
}
.bar-graph-box {
  height: 432px;
}
.bar-graph {
  width: 100%;
  height: 100%;
  //   background-color: pink;
  //   padding: 20px 62px 0 62px;
  .bar-graph-title {
    display: flex;
    justify-content: space-between;
    color: var(--pathtopo_logo_box_font_color, #5ca0d5);
    margin-bottom: 18px;
    .bar-graph-title-left {
      width: 100px;
      text-align: left;
    }
    .bar-graph-title-right {
      width: 160px;
      text-align: left;
    }
  }
  .bar-graph-content {
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    // height: 350px;
    overflow: hidden;
  }
  .bar-graph-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    // margin-top: 20px;
    .bar-grah-item-left {
      // width: 100px;
      display: flex;
      justify-content: start;
      align-items: center;
      height: 14px;

      .bar-grah-item-left-legend {
        width: 6px;
        height: 14px;
        margin-right: 8px;
      }
      .bar-grah-item-left-title {
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-size: 14px;
        text-align: left;
        color: var(--search_lable_font_color, #fff);
      }
    }
    .bar-grah-item-center {
      flex: 1;
      height: 14px;
      margin: 0 20px;
      margin-right: 40px;
      .bar-grah-item-center-inner {
        height: 14px;
      }
    }
    .bar-grah-item-right {
      width: 160px;
      text-align: left;
      display: flex;
      div {
        color: var(--search_lable_font_color, #fff);
        font-size: 14px;
      }
      .bar-grah-item-right-percent {
        margin-right: 8px;
        width: 60px;
      }
      .bar-grah-item-right-value {
        flex: 1;
      }
    }
  }
  .bar-graph-item:last-child {
    margin-bottom: 0;
  }
}
.annular-no-data {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: 121px;
    height: 101px;
  }
  // background-color: #fff;
}
</style>
