<template>
  <!-- 环形图 -->
  <div class="annular-chart-box">
    <div v-if="flowTop10.length > 0" class="annular-chart" id="annalarId"></div>
    <div v-else class="annular-no-data">
      <img :src="getImgUrl()" alt="" />
      <div class="emptyText">{{ $t("comm_no_data") }}</div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'

export default {
    data() {
        return {
            currentSkin: 1,
            colorArr:['#02B8FD','#9BFBAE','#D3E4FF','#FEA31B','#FE5C5C','#9560E7','#00FFEE','#0299FD','#07C5A3','#81C8FF'],
            colorArrLight:['#0290FC','#34C5AB','#9BFBAE','#9CD871','#64BCFC','#D3E4FE','#AEBBFD','#FEA326','#FE5C5C','#9560E7'],
            flowTop10:[],
            annularChart:null
        }
    },
    methods: {
    getImgUrl() {
        if(this.currentSkin==0){
            return require('../../../../assets/dashboard/fileempty2.png')
        }else{
            return require('../../../../assets/dashboard/fileempty.png')
        }
    },
    initEchart(data) {
        // debugger
             
    
                this.flowTop10 = data || []

                // this.colorArr = this.colorArr.slice(0,this.flowTop10.length)
                console.log(this.flowTop10)
                if(this.flowTop10.length == 0 || !this.flowTop10) {
                    if(this.annularChart) {
                    this.annularChart.clear();
                    this.annularChart.dispose();
                    this.annularChart = null;
            }
                    return
                }
                console.log(11)
             this.$nextTick(() => {
                const annularId = document.getElementById("annalarId");
                if (annularId) {
                    this.annularChart = echarts.init(annularId);
                    this.annularChart.setOption(this.setOption());
                }
    })
    },
    setOption() {
       
      
    let option = {
              
                tooltip: {
                    trigger: 'item',
                    backgroundColor: this.currentSkin==1 ? 'rgba(18,55,127, 0.82)' : 'rgba(255, 255, 255, 0.9)',
                    textStyle: {
                        color: this.currentSkin==1 ? '#fff' : '#515A6E'
                },
                formatter: '{b}: {c}%',
                extraCssText: this.currentSkin == 1? '' : 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);', // 添加阴影效果
                },
                
                series: [
                    {
                    // name: 'Access From',
                    type: 'pie',
                   radius: ['41%', '68%'],
                    data: this.flowTop10,
                    emphasis: {
                        itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                       textStyle: {
                            color: this.currentSkin==1 ? '#DFF1FF' : '#515A6E' // 修改标签文字颜色
                        },
                         position: 'outside',
                            // alignTo: 'edge',
                            margin: 20,  // 增加文字和引导线的距离
                            // edgeDistance: '10%',  // 增加标签与图表的距离
                            // formatter: '{b}: {c}%',
                            distanceToLabelLine: 4
                    },
                    
                  
                    // 设置颜色
                    itemStyle: {
                            color: (params) => {
                                if(this.currentSkin==0){
                                    return this.colorArrLight[params.dataIndex % this.colorArrLight.length]
                                }else{
                                    return this.colorArr[params.dataIndex % this.colorArr.length]
                                }
                            }
                    }
                    }
                ]
            };
            return option;
        }
    },
    created() {
        this.currentSkin = sessionStorage.getItem("dark") || 0;
    },

    mounted() {
        // this.initEchart();
    },
};
</script>

<style scoped lang="less">
.annular-chart-box {
  height: 100%;
  width: 90%;
  //   background-color: pink;
  .annular-chart {
    height: 100%;
    width: 100%;
  }
  .annular-no-data {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      width: 121px;
      height: 101px;
    }
    // background-color: #fff;
  }
}
</style>
