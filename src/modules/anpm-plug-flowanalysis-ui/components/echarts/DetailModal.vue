<template>
  <!-- 详情弹窗 -->
  <Modal
    v-model="localShowDetail"
    :width="modalWdith"
    :title="modalTitle"
    @on-visible-change="onVisibleChange"
    mask
    footer-hide
  >
    <div class="detail-modal-box" style="position: relative">
      <Loading :loading="detailLoading"></Loading>
      <div style="height: 308px; width: 100%" ref="flowChart"></div>
    </div>
  </Modal>
</template>


<script>
import echarts from 'echarts'
import echartFn from "@/common/mixins/echartFun";
import eConfig from "@/config/echart.config.js";

export default {
     mixins: [echartFn],
    props: {
        detailLoading: {
            type: Boolean,
            default: false
        },
        modalTitle:{
            type: String,
            default: ''
        }
    },
    watch: {
        
    },
    data() {
        return {
             modalWdith:null,
             localShowDetail: false,
             chartInstance: null,
             flowTrendChartData:[],
             currentSkin:sessionStorage.getItem('dark') || 1,
             query:{},
             startValue:0,
             endValue:100,
             lastData:[],
             lastDaysDiff:0,
             isHandleZoom:false,
             zoomTime:false,
             isZoom:false,
             
             

        }
       

    },
    created() {
        this.modalWdith = localStorage.getItem('modalWidth') * 0.98  ||  '98%'
        
    },
    methods: {
       handleZoomChange() {
        console.log(this.query,'this.query')
        const diffTime = Math.abs(new Date(this.query.endTime) - new Date(this.query.startTime));
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        console.log(diffDays,'diffDays')
        diffDays > 7 ? this.isHandleZoom = true : this.isHandleZoom = false
        console.log(this.isHandleZoom,'this.isHandleZoom')

        if(!this.isHandleZoom){
          return
        }
        this.chartInstance.on('dataZoom',(params) => {
          // console.log(params,'params')
          const dataZoom = this.chartInstance.getOption().dataZoom[0];
          console.log(dataZoom,'dataZoom')
          let currentStart = new Date(dataZoom.startValue).format("yyyy-MM-dd HH:mm:ss");
          let currentEnd = new Date(dataZoom.endValue).format("yyyy-MM-dd HH:mm:ss");
          console.log(currentStart,currentEnd,'currentStart,currentEnd')
          if(this.zoomTime){
            // 处理请求
            let queryobj = {}
            queryobj.startTime = currentStart
            queryobj.endTime = currentEnd
            this.startValue = currentStart
            this.endValue = currentEnd
            this.lastData= this.flowTrendChartData
            this.isZoom = true
           this.$emit('getFlowData',queryobj)
           
            
           
           
          }
           const diffTime = Math.abs(new Date(currentEnd) - new Date(currentStart));
          
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
           if ((this.lastDaysDiff > 7 && diffDays <= 7 )) {
                  console.log('处理请求')
              this.zoomTime = true
            }
          this.lastDaysDiff = diffDays
          console.log(diffDays,'diffDays')
          
        
          
              })
        
        
         
        },
        handleZoomOut() {
          this.chartInstance.getZr().on("mousewheel", (params) => {
            if (params.event.deltaY < 0) {
              console.log('Zooming in (放大)');
            } else {
              let start = this.chartInstance.getModel().option.dataZoom[0].start;
              let end = this.chartInstance.getModel().option.dataZoom[0].end;
              
              if(start == 0 && end == 100 && this.isZoom) {
                // 保存当前显示的时间范围
                const currentOption = this.chartInstance.getOption();
                const dataZoom = currentOption.dataZoom[0];
                const startValue = dataZoom.startValue;
                const endValue = dataZoom.endValue;
                
                // 恢复原始数据
                this.flowTrendChartData = this.lastData
                this.isZoom = false;
                
                // 初始化图表前设置时间范围
                this.startValue = startValue;
                this.endValue = endValue;
                
                // 重新初始化图表
                this.init();
                
                // 确保图表完全渲染后设置显示范围
                this.$nextTick(() => {
                  const option = this.chartInstance.getOption();
                  
                  // 计算开始和结束时间在数据中的百分比位置
                  const totalData = this.flowTrendChartData.length > 0 ? this.flowTrendChartData : this.flowTrendChartData;
                  if (totalData.length > 0) {
                    const firstTime = new Date(totalData[0][0]).getTime();
                    const lastTime = new Date(totalData[totalData.length - 1][0]).getTime();
                    const startTime = new Date(startValue).getTime();
                    const endTime = new Date(endValue).getTime();
                    
                    const startPercent = ((startTime - firstTime) / (lastTime - firstTime)) * 100;
                    const endPercent = ((endTime - firstTime) / (lastTime - firstTime)) * 100;
                    
                    // 设置数据缩放的起始和结束位置
                    option.dataZoom.forEach(zoom => {
                      zoom.start = Math.max(0, Math.min(startPercent, 100));
                      zoom.end = Math.max(0, Math.min(endPercent, 100));
                    });
                    
                    this.chartInstance.setOption(option);
                  }
                });
              }
            }
          });
        },
        setModalShow() {
            // debugger
            this.localShowDetail = true
            

        },
        onVisibleChange(visible) {
            // debugger
            if(!visible) {
                this.localShowDetail = false
                if (this.chartInstance) {
                    this.chartInstance.dispose()
                    this.chartInstance = null
                }
            }else {
               this.$nextTick(() => {
                  
                        // this.initEcharts()
                    
                })
              
          

            }
            
            
        },
        initEcharts(data,query) {
            this.flowTrendChartData = data
            console.log(this.flowTrendChartData,'this.flowTrendChartData')
             this.query = query
            this.init()
            // debugger
           
          


        },
        init() {
            if (this.chartInstance) {
                this.chartInstance.dispose()
                this.chartInstance = null
            }
           
            const chartElement = this.$refs.flowChart
            if (!chartElement) {
                console.error('Chart element not found')
                return
            }
            this.chartInstance = echarts.init(chartElement)
            this.chartInstance.setOption(this.setOption())
            this.handleZoomChange()
            this.handleZoomOut()
        },
        setOption() {
            let option = {
                 title: {
                    show:
                    this.flowTrendChartData.length === 0,
                    text: this.$t("common_No_data"),
                    left: "center",
                    top: "90px",
                    textStyle: {
                    color: this.currentSkin == 1 ? "#465b7a" : "grey",
                    fontFamily: "serif",
                    fontWeigth: "400",
                    fontSize: 18,
                    },
          },
                xAxis:  {
              type: "time",
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor:this.currentSkin == 1 ? 'rgba(12, 56, 123,0.7)' : '#FFFFFF',
                    textStyle: {
                        color: this.currentSkin == 1 ? '#FFFFFF' : '#515A6E'  // 添加这个配置控制文字颜色
                    },
                   extraCssText: this.currentSkin == 0 ? 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);' : '',
                     formatter: (params) => {
                        // console.log(params,'params')
                        let value = params[0].value[1]
                        // Format the timestamp
                        const timeStr = params[0].value[0]
                        
                        
                        if(value.length > 0) {
                            return timeStr + '<br/>' + params[0].seriesName + ':' + this.getUnit(value,true,true)
                        }else {
                            return timeStr + '<br/>' + params[0].seriesName + ':' + '--'
                        }
                    }
                    
                        // return result;
                    
                },
                yAxis:  {
                    name: this.$t("flow"),
                    type: "value",
                    scale: true,
                    left: '8%',
                    right: '3%',
                    position: "left",
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        symbol: ["none", "arrow"],
                        symbolSize: [6, 10],
                        lineStyle: {
                        color: this.currentSkin == 1 ? "#5CA0D5" : "#676f82",
                        width: "1", //坐标线的宽度
                        },
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                        type: "dashed",
                        color: this.currentSkin == 1 ? "#2A3840" : "#e3e7f2",
                        },
                    },
                    axisLabel: {
                      show: true,
                      formatter: value => {
                        // console.log(value);
                        return this.flowSize(value, true, true);
                      }
                    }
                    
                   
              },

                legend: {
                    data: [this.$t("server_flow_rate")],
                        icon: 'roundRect',
                        itemWidth: 18,
                        itemHeight: 10,
                        textStyle: {
                        color: this.currentSkin == 1 ? "#DFF1FF" : "#0e2a5f",
                        },
                    },
                 grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '14%',
                            containLabel: true
                        },
                  series: [
                            {
                    type: "line",
                    name: this.$t("server_flow_rate"),
                    
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 1,
                    color: '#05EEFF',
                    itemStyle: {
                        normal: {
                        lineStyle: {
                            width: 1,
                        },
                        },
                    },
                    areaStyle: {
                        color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                            offset: 0,
                            color: "rgba(5, 238, 255, 0.60)",
                            },
                            {
                            offset: 0.8,
                            color: "rgba(5, 238, 255, 0.2)",
                            },
                        ],
                        },
                    },
                    data: this.flowTrendChartData,
                            }
                        
                        ],
                   dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                         {
              type: "slider",
              left: "7%",
              right: "3%",
              bottom: 10,
              
             
              height: 20,
              xAxisIndex: 0,
              realtime: true,
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin]  ,//"rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin]  ,//"rgba(22, 67, 107, 1)",
              handleStyle: {
                color:eConfig.dataZoom.handleStyle.color[this.currentSkin]  ,// "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin]  ,//top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            },
                    ]
                    };
            return option
        }
    },
    mounted() {
        // this.initEcharts()
    }

}
</script>

<style>
</style>