<template>
  <!-- 详情弹窗 -->
  <Modal
    v-model="localShowDetail"
    :width="modalWdith"
    :title="modalTitle"
    @on-visible-change="onVisibleChange"
    mask
    footer-hide
  >
    <div class="detail-modal-box" style="position: relative">
      <Loading :loading="detailLoading"></Loading>
      <div style="height: 308px; width: 100%" ref="flowChart"></div>
    </div>
  </Modal>
</template>


<script>
import echarts from 'echarts'
import echartFn from "@/common/mixins/echartFun";
import eConfig from "@/config/echart.config.js";

export default {
     mixins: [echartFn],
    props: {
        detailLoading: {
            type: Boolean,
            default: false
        },
        modalTitle:{
            type: String,
            default: ''
        },
        level: {
            type:Number
        }
    },
    watch: {
        
    },
    data() {
        return {
             modalWdith:null,
             localShowDetail: false,
             chartInstance: null,
             flowTrendChartData:[],
             currentSkin:sessionStorage.getItem('dark') || 1,
             startValue:0,
             endValue:100,
             query:{
              startTime:'',
              endTime:''
             },
             delayPs: {
                //记录滚动条位置的对象，保存在sessionStorage中
                psD: {},
                psH: {},
                psM: {},
                psS: {}
              },
               delayLossLevel: 0,

        }
       

    },
    created() {
        this.modalWdith = localStorage.getItem('modalWidth') * 0.98  ||  '98%'
        
    },
    methods: {
       //记录滑块的位置
      setPs(level, position) {
        //记录滚动条的位置
        if (level == 0) {
          this.delayPs.psD.start = position[0];
          this.delayPs.psD.end = position[1];
          sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
        } else if (level == 1) {
          this.delayPs.psH.start = position[0];
          this.delayPs.psH.end = position[1];
          sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
        } else if (level == 2) {
          this.delayPs.psM.start = position[0];
          this.delayPs.psM.end = position[1];
          sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
        }
      },
      handleScroll(){
        this.delayLossLevel = this.level
        this.chartInstance.getZr().on("mousewheel", (params) => {
          // let getTlevel = this.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
          // let getflowTlevel = this.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
          // let getSaveData = this.$store.state.delayLossHistory; //获取保存的数据
          // let getflowSaveData = this.$store.state.flowHistory; //获取保存的数据
          // let getRightValue = this.$store.state.snmpDetails; //获取保存的数据
          // let getflowSaveUnit = this.$store.state.flowUnit; //获取流量单位数据

          var pointInPixel = [params.offsetX, params.offsetY];
          console.log(pointInPixel, this.chartInstance.containPixel(
              { gridIndex: [0, 1, 2] },
              pointInPixel
            ))
          if (
            this.chartInstance.containPixel(
              { gridIndex: [0, 1, 2] },
              pointInPixel
            )
          ) {
            let startValue = this.chartInstance.getModel().option.dataZoom[0].startValue;
            let endValue = this.chartInstance.getModel().option.dataZoom[0].endValue;
            console.log(startValue,endValue,'startValue,endValue')
            this.query.startTime = new Date(startValue).format2("yyyy-MM-dd HH:mm:ss");
            this.query.endTime = new Date(endValue).format2("yyyy-MM-dd HH:mm:ss");
            console.log(this.query,'this.query')
            this.setPs(this.delayLossLevel, [
              this.timeChange(startValue),
              this.timeChange(endValue),
            ]);
            let differ = endValue - startValue;
            let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
            let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
            let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
            var levelNum = "";
            let start = this.chartInstance.getModel().option.dataZoom[0].start;
            let end = this.chartInstance.getModel().option.dataZoom[0].end;

            if (params.wheelDelta >= 0 ) {
              console.log("放大");
              if (this.delayLossLevel == 1) {
                // 小时粒度
                if (differ < minute) {
                  if (!this.startScale) {
                    this.startScale = true;
                    levelNum = 2;
                    let delayParam = Object.assign(this.query, {
                      level: levelNum,
                    });
                    let flowParam = Object.assign(delayParam, {
                      snmp: this.dataSource == 1 ? true : false,
                    });
                    this.getDelayLoss(delayParam, flowParam);
                  }
                }
              } else if (this.delayLossLevel == 2) {
                // 分钟粒度
                if (differ < original) {
                  if (!this.startScale) {
                    this.startScale = true;
                    if (this.High) {
                      levelNum = 4;
                      let delayParam = Object.assign(this.query, {
                        level: levelNum,
                      });
                      let flowParam = Object.assign(delayParam, {
                        snmp: this.dataSource == 1 ? true : false,
                      });
                      this.getDelayLoss(delayParam, flowParam);
                    }
                  }
                }
              }
            } else {
              console.log("缩小");
              if (start == 0 && end == 100) {
                  //是否处在缩放过程中
                  if (this.delayLossLevel == getTlevel) {
                    this.setPs(this.delayLossLevel, [
                      this.timeChange(startValue),
                      this.timeChange(endValue),
                    ]);
                    this.startScale = false;
                  } else {
                    let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                    if (this.flowLevel == getflowTlevel) {
                      this.startScale = false;
                    } else {
                      if (this.flowLevel == 2) {
                        this.flowLevel = 1;
                        this.startScale = true;
                        this.flowUnit = getflowSaveUnit.HoursUnit;
                        this.flowData.enter = getflowSaveData.HoursData.enter;
                        this.flowData.issue = getflowSaveData.HoursData.issue;
                      } else if (this.flowLevel == 3 || this.flowLevel == 4) {
                        this.flowLevel = 2;
                        this.startScale = true;
                        this.flowUnit = getflowSaveUnit.minuteUnit;
                        this.flowData.enter = getflowSaveData.minuteData.enter;
                        this.flowData.issue = getflowSaveData.minuteData.issue;
                      }
                    }

                    if (this.delayLossLevel == getTlevel) {
                      this.startScale = false;
                    } else if (this.delayLossLevel == 2) {
                      this.delayLossLevel = 1;
                      this.startScale = true;
                      this.delayLossData.delay = getSaveData.HoursData.delay;
                      this.delayLossData.loss = getSaveData.HoursData.loss;
                      this.delayStart = getSite.psH.start;
                      this.startValue = getSite.psH.start;
                      this.delayEnd = getSite.psH.end;
                      this.endValue = getSite.psH.end;
                    } else if (
                        this.delayLossLevel == 3 ||
                        this.delayLossLevel == 4
                    ) {
                      this.delayLossLevel = 2;
                      this.startScale = true;
                      this.delayLossData.delay = getSaveData.minuteData.delay;
                      this.delayLossData.loss = getSaveData.minuteData.loss;
                      this.delayStart = getSite.psM.start;
                      this.startValue = getSite.psM.start;
                      this.delayEnd = getSite.psM.end;
                      this.endValue = getSite.psM.end;
                    }

                    setTimeout(() => {
                      this.startScale = false;
                      this.initEchart();
                    }, 300);
                  }
              }
            }
          }
        });
      },
        setModalShow() {
            // debugger
            this.localShowDetail = true
            

        },
        onVisibleChange(visible) {
            // debugger
            if(!visible) {
                this.localShowDetail = false
                if (this.chartInstance) {
                    this.chartInstance.dispose()
                    this.chartInstance = null
                }
            }else {
               this.$nextTick(() => {
                  
                        // this.initEcharts()
                    
                })
              
          

            }
            
            
        },
        initEcharts(data) {
            this.flowTrendChartData = data
            this.startValue = this.flowTrendChartData[0][0]
            this.endValue = this.flowTrendChartData[this.flowTrendChartData.length - 1][0]
            console.log(this.flowTrendChartData,'this.flowTrendChartData')
            // debugger
            if (this.chartInstance) {
                this.chartInstance.dispose()
                this.chartInstance = null
            }
           
            const chartElement = this.$refs.flowChart
            if (!chartElement) {
                console.error('Chart element not found')
                return
            }
            this.chartInstance = echarts.init(chartElement)
            this.chartInstance.setOption(this.setOption())
            this.handleScroll()


        },
        setOption() {
            let option = {
                 title: {
                    show:
                    this.flowTrendChartData.length === 0,
                    text: this.$t("common_No_data"),
                    left: "center",
                    top: "90px",
                    textStyle: {
                    color: this.currentSkin == 1 ? "#465b7a" : "grey",
                    fontFamily: "serif",
                    fontWeigth: "400",
                    fontSize: 18,
                    },
          },
                xAxis:  {
              type: "time",
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor:this.currentSkin == 1 ? 'rgba(12, 56, 123,0.7)' : '#FFFFFF',
                    textStyle: {
                        color: this.currentSkin == 1 ? '#FFFFFF' : '#515A6E'  // 添加这个配置控制文字颜色
                    },
                   extraCssText: this.currentSkin == 0 ? 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);' : '',
                     formatter: (params) => {
                        // console.log(params,'params')
                        let value = params[0].value[1]
                        // Format the timestamp
                        const timeStr = params[0].value[0]
                        
                        
                        if(value.length > 0) {
                            return timeStr + '<br/>' + params[0].seriesName + ':' + this.getUnit(value,true,true)
                        }else {
                            return timeStr + '<br/>' + params[0].seriesName + ':' + '--'
                        }
                    }
                    
                        // return result;
                    
                },
                yAxis:  {
                    name: this.$t("flow"),
                    type: "value",
                    scale: true,
                    left: '8%',
                    right: '3%',
                    position: "left",
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        symbol: ["none", "arrow"],
                        symbolSize: [6, 10],
                        lineStyle: {
                        color: this.currentSkin == 1 ? "#5CA0D5" : "#676f82",
                        width: "1", //坐标线的宽度
                        },
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                        type: "dashed",
                        color: this.currentSkin == 1 ? "#2A3840" : "#e3e7f2",
                        },
                    },
                    axisLabel: {
                      show: true,
                      formatter: value => {
                       
                        return this.flowSize(value, true, true);
                      }
                    }
                    
                   
              },

                legend: {
                    data: [this.$t("server_flow_rate")],
                        icon: 'roundRect',
                        itemWidth: 18,
                        itemHeight: 10,
                        textStyle: {
                        color: this.currentSkin == 1 ? "#DFF1FF" : "#0e2a5f",
                        },
                    },
                 grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '14%',
                            containLabel: true
                        },
                  series: [
                            {
                    type: "line",
                    name: this.$t("server_flow_rate"),
                    
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 1,
                    color: '#05EEFF',
                    itemStyle: {
                        normal: {
                        lineStyle: {
                            width: 1,
                        },
                        },
                    },
                    areaStyle: {
                        color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                            offset: 0,
                            color: "rgba(5, 238, 255, 0.60)",
                            },
                            {
                            offset: 0.8,
                            color: "rgba(5, 238, 255, 0.2)",
                            },
                        ],
                        },
                    },
                    data: this.flowTrendChartData,
                            }
                        
                        ],
                   dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                         {
              type: "slider",
              left: "7%",
              right: "3%",
              bottom: 10,
              
             
              height: 20,
              xAxisIndex: 0,
              realtime: true,
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin]  ,//"rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin]  ,//"rgba(22, 67, 107, 1)",
              handleStyle: {
                color:eConfig.dataZoom.handleStyle.color[this.currentSkin]  ,// "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin]  ,//top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            },
                    ]
                    };
            return option
        }
    },
    mounted() {
        // this.initEcharts()
    }

}
</script>

<style>
</style>