<template>
  <!-- 头部组件封装 -->
  <div class="page-tool">
    <Row class="query_row">
      <Col :span="22">
        <Row :gutter="30">
          <!-- 设备名称 -->
          <Col :span="6">
            <div class="query_item">
              <div class="query_item_label">
                {{ $t("comm_Device_name") + $t("comm_colon") }}
              </div>
              <Select v-model="query.deviceId" @on-change="changeDevice">
                <Option
                  v-for="item in findDeviceList"
                  :value="item.deviceId"
                  :key="item.deviceId"
                  >{{ item.deviceName }}</Option
                >
              </Select>
            </div>
          </Col>
          <Col :span="6">
            <!-- 端口 -->
            <div class="query_item">
              <div class="query_item_label">
                {{ $t("comm_port1") + $t("comm_colon") }}
              </div>
              <Select v-model="query.interfaceId">
                <Option
                  v-for="item in findDeviceInterfaceList"
                  :value="item.interfaceId"
                  :key="item.interfaceId"
                  >{{ item.interfaceName }}</Option
                >
              </Select>
            </div>
          </Col>
          <Col :span="7">
            <!-- 时间段 -->
            <div class="query_item">
              <div class="query_item_label">
                {{ $t("comm_time_period") + $t("comm_colon") }}
              </div>
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="true"
                :confirm="false"
                style="width: 100%"
                @on-change="dateChange"
              >
              </DatePicker>
            </div>
          </Col>
          <Col :span="5">
            <!-- 流量方向 -->
            <div class="query_item">
              <div class="query_item_label">
                {{ $t("flow_query_direction") + $t("comm_colon") }}
              </div>
              <RadioGroup
                v-model="flowDirection"
                @on-change="changeFlowDirection"
              >
                <Radio :label="$t('flow_query_infow')"></Radio>
                <Radio :label="$t('flow_query_outflow')"></Radio>
              </RadioGroup>
            </div>
          </Col>
        </Row>
      </Col>
    </Row>
    <Row class="query_row">
      <Col span="22">
        <Row :gutter="30">
          <Col span="6">
            <div
              class="query_item"
              :style="{ 'padding-left': lang === 'zh' ? '13px' : '18px' }"
            >
              <div class="query_item_label">
                {{ $t("comm_keywords") + $t("comm_colon") }}
              </div>
              <Input
                v-model.trim="query.keyWords"
                :placeholder="$t('comm_search_ip_name')"
                :title="$t('comm_search_ip_name')"
                clearable
              />
            </div>
          </Col>
        </Row>
      </Col>
      <Col span="2">
        <div class="query_btn">
          <Tooltip :content="$t('common_query')">
            <Button class="jiaHao-btn" type="primary" @click="queryClick">
              <i class="iconfont icon-icon-query" />
            </Button>
          </Tooltip>
          <slot name="additional-filters"></slot>
        </div>
      </Col>
    </Row>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
    data() {
        return {
            flowDirection:this.$t('flow_query_infow'),
          
           query: {
            deviceId:this.$store.state.m_select.deviceId,
            interfaceId:'',
            startTime:'',
            endTime:'',
            keyWords:'',
            flowDirection:0
           },
          
            lang: localStorage.getItem('locale'),
            
                 timeRange: [
                    new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
                 ],
                 timeOptions: {
              shortcuts: [
                {
                  text: this.$t('comm_half_hour'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 30 * 60 * 1000);
                    return [start.format("yyyy-MM-dd HH:mm:ss"), end.format("yyyy-MM-dd HH:mm:ss")];
                  }
                },
                {
                  text: this.$t('comm_today'),
                  value() {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime());
                    return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_yesterday'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24);
                    end.setTime(end.getTime() - 3600 * 1000 * 24);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_last_7'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_last_30'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_curr_month'),
                  value() {
                    let nowDate = new Date();
                    let date = {
                      year: nowDate.getFullYear(),
                      month: nowDate.getMonth(),
                      date: nowDate.getDate(),
                    };
                    let end = new Date(date.year, date.month + 1, 0);
                    let start = new Date(date.year, date.month, 1);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_preced_month'),
                  value() {
                    let date = new Date();
                    let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                    let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                }
              ]
            },
        }
    },
    computed: {
        ...mapState('m_select',['deviceId','interfaceId','findDeviceList','findDeviceInterfaceList'])
    },
    watch: {
     
     
    },
    methods:{
         dateChange(val) {
        console.log(val)
     
        this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")]
        this.query.startTime = val[0]
        this.query.endTime = new Date(val[1]).format("yyyy-MM-dd 23:59:59")

      
     
        
     
    },
      changeDevice(newVal){
      
       this.$store.dispatch('m_select/findDeviceInterfaceListSync', this.query.deviceId)
       this.query.interfaceId = ''
       this.$emit('changeDevice')
      },
       getLastHour() {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000); // 1 hour ago
          
          this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
          this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
          this.timeRange = [start, end];
          },
      queryClick(){
        console.log(this.query,'query');
        this.$emit('queryClick',this.query)
      },
      changeFlowDirection(newVal){
        this.query.flowDirection = this.query.flowDirection == 0 ? 1 : 0
      },
    },
    created() {
      this.getLastHour()
    }

}
</script>

<style scoped>
.query_item_kw {
  padding-left: 13px;
}
.query_row {
  margin-bottom: 20px;
}
.query_btn {
  display: flex;
  width: 100%;
  /* background-color: #fff; */
  justify-content: flex-end;
}
.jiaHao-btn {
  height: 38px !important;
}
</style>
