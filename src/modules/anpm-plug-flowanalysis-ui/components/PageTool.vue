<template>
  <!-- 头部组件封装 -->
  <section>
    <!-- 流量分析 -->
    <div class="section-top">
      <Row class="fn_box">
        <!-- 设备名称 -->
        <Col span="6">
          <div class="fn_item">
            <label
              class="fn_item_label"
              :style="{ width: lang == 'en' ? '110px' : '100px' }"
              >{{ $t("comm_Device_name") + $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select v-model="query.deviceId" @on-change="changeDevice">
                <Option
                  v-for="item in findDeviceList"
                  :value="item.deviceId"
                  :key="item.deviceId"
                  >{{ item.deviceName }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <!-- 端口 -->
        <Col span="6">
          <div class="fn_item">
            <label
              class="fn_item_label"
              :style="{ width: lang == 'en' ? '85px' : '100px' }"
            >
              {{ $t("comm_port1") + $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select v-model="query.interfaceId" clearable>
                <Option
                  v-for="item in findDeviceInterfaceList"
                  :value="item.interfaceId"
                  :key="item.interfaceId"
                  >{{ item.interfaceName }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <!-- 时间段 -->
        <Col span="7">
          <div class="fn_item">
            <label
              class="fn_item_label"
              :style="{ width: lang == 'en' ? '110px' : '100px' }"
            >
              {{ $t("comm_time_period") + $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                :confirm="false"
                style="width: 100%"
                @on-change="dateChange"
              >
              </DatePicker>
            </div>
          </div>
        </Col>
        <!-- 流量方向 -->
        <Col span="5">
          <div class="fn_item">
            <label
              class="fn_item_label"
              :style="{ width: lang == 'en' ? '120px' : '100px' }"
            >
              {{ $t("flow_query_direction") + $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="display: flex; align-items: center">
              <RadioGroup
                v-model="flowDirection"
                @on-change="changeFlowDirection"
              >
                <Radio :label="$t('flow_query_infow')"></Radio>
                <Radio :label="$t('flow_query_outflow')"></Radio>
              </RadioGroup>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label
              class="fn_item_label"
              :style="{ width: lang == 'en' ? '110px' : '100px' }"
              >{{ $t("comm_keywords") + $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Input
                v-model="query.keyWords"
                :placeholder="keyPlaceholder"
                :title="keyPlaceholder"
                clearable
              />
            </div>
          </div>
        </Col>
      </Row>
      <div class="tool-btn">
        <div>
          <Tooltip :content="$t('common_query')">
            <Button class="jiaHao-btn" type="primary" @click="queryClick">
              <i class="iconfont icon-icon-query" />
            </Button>
          </Tooltip>
          <slot name="additional-filters"></slot>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    keyPlaceholder: {
      type: String,
      default: ''
    }
  },
    data() {
        return {
            flowDirection:this.$t('flow_query_infow'),
          
          
           query: {
            deviceId:this.$store.state.m_select.deviceId,
            interfaceId:'',
            startTime:'',
            endTime:'',
            keyWords:'',
            flowDirection:0
           },
          
            lang: localStorage.getItem('locale'),
            
                 timeRange: [
                    new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
                 ],
                 timeOptions: {
              shortcuts: [
               
                {
                  text: this.$t('comm_today'),
                  value() {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime());
                    return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_yesterday'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24);
                    end.setTime(end.getTime() - 3600 * 1000 * 24);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_last_7'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_last_30'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_curr_month'),
                  value() {
                    let nowDate = new Date();
                    let date = {
                      year: nowDate.getFullYear(),
                      month: nowDate.getMonth(),
                      date: nowDate.getDate(),
                    };
                    let end = new Date(date.year, date.month + 1, 0);
                    let start = new Date(date.year, date.month, 1);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_preced_month'),
                  value() {
                    let date = new Date();
                    let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                    let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                }
              ]
            },
        }
    },
    computed: {
        ...mapState('m_select',['deviceId','interfaceId','findDeviceList','findDeviceInterfaceList'])
    },
    watch: {
     
     
    },
    methods:{
         dateChange(val) {
              if (val && val.length === 2) {
              const [startDate, endDate] = val;
              
              // Format start time
              this.query.startTime = startDate;
              
              // Check if end time has hours/minutes/seconds
              const endDateTime = new Date(endDate);
              if (endDateTime.getHours() === 0 && 
                  endDateTime.getMinutes() === 0 && 
                  endDateTime.getSeconds() === 0) {
                // If no time specified, set to end of day
                endDateTime.setHours(23, 59, 59);
                this.query.endTime = endDateTime.format("yyyy-MM-dd HH:mm:ss");
                this.timeRange = [startDate, endDateTime];
              } else {
                // Keep original end time if it includes time component
                this.query.endTime = endDate;
                this.timeRange = val;
              }
        }

            
            
          
        
     
    },
      changeDevice(newVal){
      
       this.$store.dispatch('m_select/findDeviceInterfaceListSync', this.query.deviceId)
       this.query.interfaceId = ''
       this.$emit('changeDevice')
      },
       getLastHour() {
            // 修改为获取当天时间范围
            const end = new Date();
            const start = new Date();
            
            // 设置开始时间为当天 00:00:00
            start.setHours(0, 0, 0, 0);
            
            // 设置结束时间为当天 23:59:59
            end.setHours(23, 59, 59, 999);
            
            this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
            this.timeRange = [start, end];
        },
      queryClick(){
     
        console.log(this.query,'query');
        this.query.keyWords = this.query.keyWords.trim()
        this.$emit('queryClick',this.query)
      },
      changeFlowDirection(newVal){
        this.query.flowDirection = this.query.flowDirection == 0 ? 1 : 0
      },
    },
    created() {
      this.getLastHour()
    }

}
</script>

<style scoped lang="less">
.setionBox {
  width: unset !important;
}
.query_item_kw {
  padding-left: 13px;
}
.query_row {
  margin-bottom: 20px;
}
.query_btn {
  display: flex;
  width: 100%;
  /* background-color: #fff; */
  justify-content: flex-end;
}
.jiaHao-btn {
  height: 38px !important;
}
.fn_item_box {
  overflow: hidden;
  flex: 1;
}
.fn_item {
  height: 35px;
}
.sectionBox .section-top .fn_item .fn_item_box {
  height: 35px;
}
.tool-btn button {
  margin-right: unset !important;
}
.section-top {
  padding: unset !important;
  margin: unset !important;
}
/deep/.ivu-select {
  .ivu-icon-ios-close-circle {
    // 添加具体的图标类名
    color: var(--reset_export_del_button_font_color, #05eeff) !important;
  }
}
</style>
