<template>
  <Modal
    v-model="modalShow"
    :title="$t('but_import')"
    :width="lang == 'en' ? 1200 : 940"
    @on-cancel="closeModal()"
    class="map-import-modal all-border-table"
  >
    <div>
      <!-- 下载模版 -->
      <div class="import-item">
        <div
          class="import-item-label"
          :style="{ width: lang == 'en' ? '150px' : '90px' }"
        >
          {{ $t("comm_download_tpl") + $t("comm_colon") }}
        </div>
        <div class="import-item-content">
          {{ $t("reachability_please") }}
          <span class="import-btn" @click="downloadClick">{{
            $t("comm_download_tpl")
          }}</span>
          {{ $t("comm_use_download_tpl") }}
        </div>
      </div>
      <!-- 上传附件 -->
      <div class="import-item">
        <div
          class="import-item-label"
          :style="{ width: lang == 'en' ? '150px' : '90px' }"
        >
          {{ $t("discover_upload_attachment") + $t("comm_colon") }}
        </div>
        <div class="import-item-content">
          <Input readonly style="width: 320px" v-model="uploadFiles" />
          <div
            class="uploadBox"
            style="
              margin-left: 10px;
              padding: 0 14px;
              background: var(--choose_data_btn_bg_color, #06324d);
              border-radius: 4px 4px 4px 4px;
              opacity: 1;
              border: 1px solid var(--help_font_hover_color, #05eeff);
              color: var(--choose_data_btn_font_color, #05eeff);
              cursor: pointer;
            "
          >
            {{ $t("select_attachment") }}

            <input
              icon="ios-cloud-upload-outline"
              @change="changeImport"
              type="file"
              :key="fileInputKey"
              accept=".xlsx,.xls"
              ref="uploadFile"
              :title="$t('comm_no_select_file')"
            />
            <!-- <span>{{ uploadFiles }}</span> -->
          </div>
        </div>
      </div>
      <!-- 数据校验 -->
      <div class="import-item">
        <div
          class="import-item-label"
          :style="{ width: lang == 'en' ? '150px' : '90px' }"
        >
          {{ $t("data_validation") + $t("comm_colon") }}
        </div>
        <div class="import-item-content">
          {{ $t("server_total") }}{{ totalCount
          }}{{ $t("common_data_verification_data_tip")
          }}{{ $t("punctuation_comma") }}
          {{ $t("common_data_verification_where_tip") }}
          <span style="color: #fe5c5c"
            >{{ errorCount }}{{ $t("server_strip") }}</span
          >
          {{ $t("common_data_verification_error_tip") }}
          {{ $t("exclamation_point") }}

          <span
            :style="{
              color: '#fe5c5c',
              marginLeft: lang == 'en' ? '0' : '10px',
            }"
          >
            {{ $t("punctuation_bracket_left") }}
            {{ $t("common_data_verification_upload_again_tip") }}
            {{ $t("punctuation_bracket_right") }}
          </span>
        </div>
        <div
          class="uploadBox"
          @click="getErrorExcel"
          style="
            cursor: pointer;
            margin-left: 10px;
            padding: 0 14px;
            background: var(--choose_data_btn_bg_color, #06324d);
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px solid var(--help_font_hover_color, #05eeff);
            color: var(--choose_data_btn_font_color, #05eeff);
          "
        >
          {{ $t("export_error") }}
        </div>
        <!-- <div>
          <Button type="primary" @click="importClick">{{
            $t("comm_import")
          }}</Button>
        </div> -->
      </div>
      <!-- 表格 -->
      <Table
        height="230"
        width="100%"
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :no-data-text="
          loading
            ? ''
            : tableData.length > 0
            ? ''
            : currentSkin == 1
            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
              $t('common_No_data') +
              '</p></div>'
            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
              $t('common_No_data') +
              '</p></div>'
        "
      ></Table>
    </div>
    <div slot="footer">
      <Button class="cancel-btn" @click="closeModal">{{
        $t("common_cancel")
      }}</Button>
      <Button class="primary-btn" :loading="saveLong" @click="importOk">{{
        $t("common_verify")
      }}</Button>
    </div>
  </Modal>
</template>
<script>
import axios from "axios";
import langFn  from '@/common/mixins/langFn';
    export default {
      mixins: [langFn],
        data () {
            return {
                lang:localStorage.getItem('locale') || 'zh',
                loading:false,
                saveLong:false,
                fileInputKey:Date.now(),
                currentSkin:sessionStorage.getItem('dark') || 1,
                modalShow: false,
                totalCount:0,
                errorCount:0,
                tableData:[],
                allExcel:{},
                columns:[
                    // 行数
                    {
                        title:this.$t('dash_rows'),
                        key:'rows',
                        width:this.getColumnWidth(100,110)
                    },
                    // 应用名称
                    {
                        title:this.$t('message_application_name'),
                        key:'applicationName',
                       width:this.getColumnWidth(190,240)
                    },
                    // 端口
                    {
                        title:this.$t('comm_port1'),
                        key:'portNumber',
                        width:this.getColumnWidth(190,240)
                    },
                    // IP
                    {
                        title:"IP",
                        key:'ip',
                        width:this.getColumnWidth(190,240)
                    },
                    // 错误详情
                    {
                        title:this.$t('snmpoid_error_details'),
                        key:'errorDetails',
                       
                        render: (h, params) => {
                          let str = params.row.errorDetails;
                          return h(
                            "span",
                            {
                              style: {
                                color: "#ed4014"
                              }
                            },
                            str === undefined || str === null || str === "" ? "--" : str
                          );
                        }
                    }
                ],
                updata: {
                    files: "",
                    client_name: "",
                },
                uploadFiles: "",
            }
        },
       
        methods: {
            importOk() {
                 let _self = this;
            let token_id = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;

            if (this.allExcel.excleList.length < 1) {
                //清空选择附件input，不然无法触发下一次@chang事件
                this.$refs.uploadFile.value = "";
                this.$Message.warning({
                content: this.$t("spec_no_valid"),
                background: true,
                });
                return;
            }
            let params = new URLSearchParams();
            params.append("jsonStr", JSON.stringify(this.allExcel));
            this.$Loading.start();
            this.saveLong = true;
            axios({
                url: "/flowanalysis/applicationMapping/readExcelSave",
                method: "post",
                data: params,
            })
                .then((res) => {
                if (res.data.code === 1) {
                    this.$Message.success({
                    content: this.$t("interface_import_success"),
                    background: true,
                    });
                    this.closeModal();
                    this.$emit("getTableData");
                } else {
                    this.$Message.error({
                    content: this.$t("interface_import_failure"),
                    background: true,
                    });
                    return;
                }
                 this.saveLong = false;;
                this.$Loading.finish();
                })
                .catch((error) => {
                   this.saveLong = false;;
                this.$Loading.finish();
                })
                .finally(() => {
                   this.saveLong = false;;
                this.$Loading.finish();
                });
                    },

            changeImport(obj) {
              this.repeatExcel = [];
              this.titleNameList = [];
              this.excelDataList = [];
              this.allExcel = {};
              this.fileInputKey = Date.now();
                this.updata.files = obj;
                this.uploadFiles = obj.target.value.substr(
                    obj.target.value.lastIndexOf("\\") + 1
                );
                let fileFormData = new FormData();
                fileFormData.append("file", obj.target.files[0]);
                this.readExcel(fileFormData)
            },
            readExcel(fileFormData) {
               let requestConfig = {
                    headers: {
                    "Content-Type": "multipart/form-data",
                    },
                };
                this.$Loading.start();
                axios
                    .post("/flowanalysis/applicationMapping/readExcel", fileFormData, requestConfig)
                    .then((res) => {
                    if (res.data.code === 1) {
                        console.log("res.data:", res.data);
                       this.handleExcelData(res.data.data)
                        
                    } else {
                        this.$Message.error({
                        content: res.data.msg,
                        background: true,
                        });
                    }
                    this.allExcel = res.data.data || {};
                    this.$Loading.finish();
                    })
                    .catch((error) => {
                    this.$Loading.finish();
                    })
                    .finally(() => {
                    this.$Loading.finish();
                    });
            },
            handleExcelData(data) {
                this.totalCount =  data.repeatVoList.length + data.excleList.length
                this.errorCount = data.repeatVoList.length
               if(data.repeatVoList.length > 0){
                 this.tableData = data.repeatVoList.map((row, index) => {
                    return {
                        rows: row[0],
                        applicationName: row[1],
                        portNumber: row[2],
                        ip: row[3],
                        errorDetails: row[4]
                    }
    })

               }else{
                this.tableData = [];
               }

            },
           
           changeModal(){
            this.modalShow = true
             //清空选择附件input，不然无法触发下一次@chang事件
            this.$refs.uploadFile.value = "";
           },
            closeModal(){
                 //清空选择附件input，不然无法触发下一次@chang事件
              this.$refs.uploadFile.value = "";
              this.modalShow = false;
              this.uploadFiles = "";
              this.excleList = [];
              this.repeatVoList = [];
              this.allExcel = {};
              this.totalCount=0;
              this.errorCount=0;
              this.tableData=[];
            },
            downloadClick(){
                //模板下载
                this.$Loading.start();
                axios({
                    url: "/flowanalysis/applicationMapping/download",
                    method: "post",
                    responseType: "blob", // 服务器返回的数据类型
                })
                    .then((res) => {
                        const content = res.data;
                        const blob = new Blob([content], {
                        type: "application/vnd.ms-excel",
                        });
                        if ("msSaveOrOpenBlob" in navigator) {
                        window.navigator.msSaveOrOpenBlob(blob, this.$t('map_import_template')+".xlsx");
                        } else {
                        const fileName = this.$t('map_import_template')+".xlsx"; // 导出文件名
                        // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
                        // IE10以上支持blob但是依然不支持download
                        if ("download" in document.createElement("a")) {
                            // 支持a标签download的浏览器
                            const _res = res.data;
                            let blob = new Blob([_res]);
                            let downloadElement = document.createElement("a");
                            let href = window.URL.createObjectURL(blob); //创建下载的链接
                            downloadElement.href = href;
                            downloadElement.download = fileName; //下载后文件名
                            document.body.appendChild(downloadElement);
                            downloadElement.click(); //点击下载
                            document.body.removeChild(downloadElement); //下载完成移除元素
                            window.URL.revokeObjectURL(href); //释放掉blob对象
                        } else {
                            // 其他浏览器
                            navigator.msSaveBlob(blob, fileName);
                        }
                        }
                        this.$Loading.finish();
                    })
                    .catch(error => {
                        console.log(error);
                        this.$Loading.finish();
                    }).finally(()=>{
                    this.$Loading.finish();
                });
                        },
            // 导出错误选项
             getErrorExcel() {
                //重复项下载
                if (this.tableData && this.tableData.length > 0) {
                    let params = new URLSearchParams();
                    params.append("jsonStr", JSON.stringify(this.allExcel));
                    this.$Loading.start();
                    axios({
                    url: "/flowanalysis/applicationMapping/repeatExcelExport",
                    method: "post",
                    data: params,
                    responseType: "blob", // 服务器返回的数据类型
                    })
                    .then((res) => {
                        let data = res.data;
                        const blob = new Blob([data], { type: "application/vnd.ms-excel" });
                        if ("msSaveOrOpenBlob" in navigator) {
                        window.navigator.msSaveOrOpenBlob(blob, this.$t("device_error"));
                        } else {
                        var fileName = "";
                        fileName = this.$t("map_import_error");
                        const elink = document.createElement("a"); //创建一个元素
                        elink.download = fileName; //设置文件下载名
                        elink.style.display = "none"; //隐藏元素
                        elink.href = URL.createObjectURL(blob); //元素添加href
                        document.body.appendChild(elink); //元素放入body中
                        elink.click(); //元素点击实现
                        URL.revokeObjectURL(elink.href); // 释放URL 对象
                        document.body.removeChild(elink);
                        }
                        this.$Loading.finish();
                    })
                    .catch((error) => {
                        this.$Loading.finish();
                    })
                    .finally(() => {
                        this.$Loading.finish();
                    });
                } else {
                    this.$Message.warning({
                    content: this.$t("spec_no_duplicates"),
                    background: true,
                    });
                }
                },
        }
    }
</script>
