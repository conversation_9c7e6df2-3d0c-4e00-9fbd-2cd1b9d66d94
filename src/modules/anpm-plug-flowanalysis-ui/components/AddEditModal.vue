<template>
  <!-- 新增编辑弹窗 -->
  <Modal
    v-model="addEditModal"
    :title="modalTitle"
    width="940"
    class="pading-40-modal"
    @on-visible-change="visibleChange"
  >
    <Form
      ref="addForm"
      :model="formCustom"
      :rules="ruleCustom"
      :label-width="120"
    >
      <!-- 应用名称 -->
      <FormItem
        :label="$t('message_application_name') + $t('comm_colon')"
        prop="name"
      >
        <Input
          v-model="formCustom.name"
          :maxlength="60"
          @on-change="validateFormDataName"
        ></Input>
      </FormItem>
      <FormItem :label="$t('comm_port1') + $t('comm_colon')" prop="port">
        <Input v-model="formCustom.port"></Input>
      </FormItem>
      <FormItem :label="'IP' + $t('comm_colon')" prop="ip">
        <Input v-model="formCustom.ip"></Input>
        <div class="ip-placeholder">{{ $t("flow_maping_ip_placeholder") }}</div>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button class="cancel-btn" @click="operateCancel">{{
        $t("common_cancel")
      }}</Button>
      <Button class="primary-btn" @click="operateOk">{{
        $t("common_verify")
      }}</Button>
    </div>
  </Modal>
</template>

<script>
import { totalLength } from "@/units/validate";

export default {
    data(){
        return {
            addEditModal:false,
            type:'',
           
            modalTitle:'',
            formCustom:{
                name:'',
                port:'',
                ip:''

            },
            ruleCustom: {
                name:[
                    {required:true,message:this.$t('comm_enter') + this.$t('message_application_name'),trigger:'change'},
                    
                ],
                port:[
                    {
                       required: false,
                      trigger: "blur",
                      validator: (rule, value, callback) => {
                        if (!value) {
                          callback();
                        } else if (!/^[0-9]\d*$/.test(value)) {
                          callback(new Error(this.$t("map_application_post_error_tip")));
                        } else {
                          if(value < 1 || value > 65535){
                               callback(new Error(this.$t("map_application_post_error_tip2")));
                          }else{
                            callback();
                          }
                        }
                      },
                    }
                ],
                ip:[
                    {required:true,message:this.$t('comm_enter') + 'IP',trigger:'blur'}
                ]
            }
        }
    },
    methods: {
      visibleChange(type) {
        if(!type){
          this.resetForm()
        }
      },
        validateFormDataName(event) {
          console.log(this.formCustom.name)
            let name = this.formCustom.name;
            if (name != "") {
                // 因为下标是从0开始了
                var index = totalLength(name, 50);
                name = name.substring(0, index);
                this.formCustom.name = name;
                

      }
    },
        changeModal(type,row = null){
            this.addEditModal = true
            this.type = type
            if(row){
              this.formCustom = row
            }
            this.modalTitle = type == 'add' ? this.$t('but_add') : this.$t('but_edit')
        },
        resetForm(){
          this.$refs.addForm.resetFields()
          this.formCustom = {
            name:'',
            port:'',
            ip:''
          }
        },
        operateOk(){
        // debugger
        
            
            this.$refs.addForm.validate(async isOk => {
            //  校验
              if(isOk){
                // 区分新增编辑
             
                  // 新增
                  if(this.type == 'add'){
                   const res =  await this.$http.PostJson('/flowanalysis/applicationMapping/add',this.formCustom)
                   console.log(res.code)
                  //  debugger
                   if(res.code == 1) {
                     this.addEditModal = false
                     this.$Message.success(this.$t('icon_add_success'))
                     this.$emit('getTableData')
                    

                   }else {
                 
                    this.$Message.error(res.msg)
                    
                    
                   }

                }else {
                  // 编辑
                  const res =  await this.$http.PostJson('/flowanalysis/applicationMapping/update',this.formCustom)
                   if(res.code == 1) {
                     this.addEditModal = false
                     this.$Message.success(this.$t('comm_changed_successful'))
                      this.$emit('getTableData')
                    

                   }else {
                 
                    this.$Message.error(res.msg)
                    
                   }
                }

                
               

              }else {
                //  return isOk

              }


            })
           
            
        },
       
       
        operateCancel(){
            this.addEditModal = false
           
        }
    }

}
</script>

<style>
</style>