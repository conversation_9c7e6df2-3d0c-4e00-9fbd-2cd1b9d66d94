<template>
  <!-- 图表头部组件 -->
  <div
    :class="[
      'title-tool',
      currentSkin == 0 ? 'title-tool-light' : 'title-tool-dark',
    ]"
  >
    {{ TitleTool }}
  </div>
</template>

<script>
export default {
    props: {
        TitleTool: {
            type: String,
            default: ''
        }
    },
    data() {
       
        return {
            currentSkin: sessionStorage.getItem("dark") || 0
        }
    }

}
</script>

<style scoped lang="less">
.title-tool {
  height: 46px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  line-height: 46px;
  padding-left: 16px;
  text-align: left;
  color: var(--quality_report_left_ivu_menu_font_color, #fff);
  font-weight: 700;
  font-size: 14px;
}
.title-tool-light {
  background-image: url("../../../assets/wisdom/img-bg-zj2.png");
}
.title-tool-dark {
  background-image: url("../../../assets/wisdom/img-bg-zj.png");
}
</style>
