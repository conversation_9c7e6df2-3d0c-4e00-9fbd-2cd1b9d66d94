.query_item {
    width: 100%;
    display: flex;
    align-items: center;
    height: 38px;
    // background-color: pink;
    .query_item_label {
        text-wrap: nowrap;
        color: var(--search_lable_font_color, #fff) ;
    }
    .ivu-radio-wrapper {
         color: var(--search_lable_font_color, #fff) ;
    }
}
.chart-box {
    border: 1px solid var(--body_table_content_border_color, #06254B);
    margin-bottom: 20px;
    background-color:var(--spectendency_chart_bg_color,#06121C) ;
}
.chart-content {
    height: 432px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .chart-pie {
        height: 100%;
        width: 50%;
    }
    .chart-graph {
        height: 100%;
        width: 50%;
        padding-left:30px;
        // padding:0 30px 0 60px;
        // padding-top:17px;
    }
}
.daoChu-btn {
    width: 60px !important;
    height: 38px !important;
  }
.ivu-select-item {
    text-align: left;
}
.ivu-radio-wrapper {
    color: var(--search_lable_font_color, #fff) ;
}
.fn_item {
    display: flex;
    // flex:1;
    width: 100%;
    height: 32px;
    // margin-right: 18px;
    margin-bottom: 20px;
    padding-right: 10px;
    > label {
        display: inline-block;
        font-size: 14px;
        line-height: 1;
        padding: 9px 0;
        text-align: right;
        min-width: 75px;
    }
    .fn_item_label {
        display: inline-block;
        font-size: 14px;
        line-height: 1;
        padding: 9px 0;
        text-align: right;
        min-width: 85px;
        // color: #fff;
        color: var(--search_lable_font_color, #fff);
    }
    .fn_item_box {
        display: inline-block;
        // margin-left: 8px;
        height: inherit;
        width: 100%;
    }
}
.light-index {
    background-color: #fff;
    padding: 20px;
}

.ivu-select-selected-value ,.ivu-select-placeholder{
    text-align: left;
}
.ivu-tooltip-popper .ivu-tooltip-inner {
    max-width: 1200px !important;
    // word-break: break-all;
    // white-space: normal;
  }