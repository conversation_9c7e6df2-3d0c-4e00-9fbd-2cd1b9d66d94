const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: lan.get<PERSON>abel("src.flowAnalysis"),
    meta: {
      authority: true
    },
    redirect: '/flowanalysis',
  },
  {
    path: "/flowanalysis",
    name: lan.get<PERSON>abel("src.flowAnalysis"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-flowanalysis-ui/views/index.vue"], resolve)
  },
  {
    path: "/deviceInfo",
    name: lan.get<PERSON>abel("src.flowAnalysis"),
    meta: {
      authority: true
    },
    component: () =>
      import("@/modules/anpm-plug-flowanalysis-ui/views/deviceInfo.vue"),
  },
  {
    path: "/map",
    name: lan.getLabel("src.flowAnalysis"),
    meta: {
      authority: true
    },
    component: () => import("@/modules/anpm-plug-flowanalysis-ui/views/MapIndex.vue"),
  }
];
