
import { $http } from "@/server/http";
export default {
    // 搜索下来选择数据
    namespaced: true,
    state: {
        findDeviceList: [],
        findDeviceInterfaceList: [],
        deviceId: '',
        interfaceId: '',
        rowData: {}
    },
    mutations: {
        setRowData(state, rowData) {
            state.rowData = rowData
        },
        setDeviceId(state, deviceId) {
            state.deviceId = deviceId
        },
        setInterfaceId(state, interfaceId) {
            state.interfaceId = interfaceId
        },
        setFindDeviceList(state, list) {
            state.findDeviceList = list
        },
        setFindDeviceInterfaceList(state, list) {
            state.findDeviceInterfaceList = list
        }
    },
    actions: {
        // 获取设备列表
        async findDeviceListSync(context) {

            const res = await $http.PostJson('/flowanalysis/findDeviceList')
            if (res.code == 1) {
                context.commit('setFindDeviceList', res.data)

            }
            console.log(res)
        },

        // 获取端口列表
        async findDeviceInterfaceListSync(context, deviceId) {

            const res = await $http.PostJson('/flowanalysis/findDeviceInterfaceList', { deviceId })
            if (res.code == 1) {
                context.commit('setFindDeviceInterfaceList', res.data)
            }
        }
    }
}