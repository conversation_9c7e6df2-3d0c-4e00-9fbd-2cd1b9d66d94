<template>
  <!-- 映射页面 -->
  <div :class="['map-index', currentSkin == 0 ? 'map-light-index' : '']">
    <Row>
      <Col span="12">
        <div class="query_item">
          <div class="query_item_label">
            {{ $t("comm_keywords") + $t("comm_colon") }}
          </div>
          <Input
            v-model="query.searchKey"
            :placeholder="$t('flow_map_keywords_placeholder')"
            style="width: 300px"
            clearable
            @on-enter="queryTale"
            @on-clear="queryTale"
          />
        </div>
      </Col>
      <Col span="12">
        <!-- 搜索 -->
        <div class="query_btn">
          <Tooltip :content="$t('common_query')">
            <Button class="jiaHao-btn" type="primary" @click="queryTale">
              <i class="iconfont icon-icon-query" />
            </Button>
          </Tooltip>
          <!-- 新增 -->
          <Tooltip :content="$t('but_add')">
            <Button class="jiaHao-btn" type="primary" @click="addClick">
              <i class="iconfont icon-icon-add" />
            </Button>
          </Tooltip>
          <!-- 删除 -->
          <Tooltip :content="$t('but_delete')">
            <Button class="delete-btn" type="primary" @click="batchDelete">
              <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
            </Button>
          </Tooltip>
          <!-- 导出 -->
          <Tooltip :content="$t('but_export')">
            <Button class="daoChu-btn" type="primary" @click="exportClick">
              <img
                :src="
                  currentSkin == 1
                    ? require('../../../assets/btn-img/icon-derive.png')
                    : require('../../../assets/btn-img/icon-derive-light.png')
                "
                alt=""
              />
            </Button>
          </Tooltip>
          <!-- 导入 -->
          <Tooltip :content="$t('but_import')">
            <Button type="primary" class="daoChu-btn" @click="importClick">
              <img
                :src="
                  currentSkin == 1
                    ? require('../../../assets/wisdom/icon-import.png')
                    : require('../../../assets/btn-img/icon-import-light.png')
                "
                alt=""
              />
            </Button>
          </Tooltip>
          <Tooltip :content="$t('but_return')">
            <Button type="primary" class="daoChu-btn" @click="returnClick">
              <i class="iconfont icon-icon-return1" />
            </Button>
          </Tooltip>
        </div>
      </Col>
    </Row>
    <!-- 表格 -->
    <div class="table-box">
      <Loading :loading="pageLoading"></Loading>
      <Table
        ref="tableList"
        :columns="columns"
        stripe
        :data="tableData"
        @on-selection-change="handleSelectionChange"
        :no-data-text="
          tableData.length > 0 || pageLoading
            ? ''
            : currentSkin == 1
            ? '<div class=&quot;table_empty&quot;><p class=&quot;emptyText&quot; > ' +
              $t('common_No_data') +
              '</p></div>'
            : '<div class=&quot;table2_empty&quot;><p class=&quot;emptyText&quot; > ' +
              $t('common_No_data') +
              '</p></div>'
        "
      >
        <!--    列表操作列功能        -->
        <template slot-scope="{ row }" slot="action" class="tableTools">
          <Tooltip
            :content="$t('common_update')"
            placement="left"
            :transfer="true"
          >
            <span
              :class="currentSkin == 1 ? 'edit1-btn' : 'light-edit1-btn'"
              @click="openEditForm(row)"
            ></span>
          </Tooltip>
          <Tooltip
            :content="$t('common_delete')"
            placement="left"
            :transfer="true"
          >
            <span class="del1-btn" @click="rowRemove(row)"></span>
          </Tooltip>
        </template>
      </Table>
    </div>

    <!-- /表格 -->
    <!-- 分页 -->
    <div class="tab-page" style="border-top: 0" v-if="tableData.length > 0">
      <Page
        v-page
        :current.sync="query.pageNo"
        :page-size="query.pageSize"
        :total="totalCount"
        :page-size-opts="pageSizeOpts"
        :prev-text="$t('common_previous')"
        :next-text="$t('common_next_page')"
        @on-change="pageChange"
        @on-page-size-change="pageSizeChange"
        show-elevator
        show-sizer
      >
      </Page>
    </div>
    <!-- /分页 -->
    <!-- 新增编辑弹窗 -->
    <AddEditModal ref="addEditModal" @getTableData="queryTale" />
    <!-- /新增编辑弹窗 -->
    <!-- 导入弹窗 -->
    <ImportModal ref="importModal" @getTableData="queryTale" />
    <!-- /导入弹窗 -->
  </div>
</template>

<script>
import "@/config/page.js";
import axios from "axios";
export default {
    components:{
        AddEditModal:()=>import('../components/AddEditModal.vue'),
        ImportModal:()=>import('../components/ImportModal.vue')
    },
    data() {
        return {
            value: '',
            pageLoading:false,
            ids:[],
            
            currentSkin:sessionStorage.getItem('dark') || 1,
            columns:[ {
                    type: "selection",
                    width: 30,
                    align: "center",
                },
                {
                    title: this.$t("message_application_name"),
                    key: "name",
                    align: "left",
                    width: 200,
                    tooltip: true,
                },
                 {
                    title: this.$t("alarm_port_num"),
                    key: "port",
                    align: "left",
                    render: (h, params) => {
                      let str = params.row.port;
                      str =
                          str === undefined || str === null || str === "" || str === "null"
                              ? "--"
                              : str;
                      return h('div',str)
                    }
                 
                },
                {
                    title: 'IP',
                    key: "ip",
                    align: "left",
                   
                    tooltip: true,
                },
                 { title: this.$t('comm_operate'),align: "center", width: 120, slot: "action", fixed:'right' },
            ],

            tableData:[],
            query:{
                pageNo:1,
                pageSize:10,
                searchKey:''
            },
            totalCount:0,
            pageSizeOpts:[10, 50, 100, 200, 500, 1000],
        }
    },
    computed: {
        selectedIds() {
            return this.$store.state.m_maping.selectedIds
        }
    },
    created() {
      this.getTableData()
    },
    methods:{
      returnClick() {
        this.$router.push('/deviceInfo')
      },
       clearSelectData(){
        this.$refs.tableList.selectAll(false)
        this.ids = []
        this.$store.commit('m_maping/setSelectedIds', [])
      },
      // 导出
      exportClick() {
        let token_id = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
        let params = new URLSearchParams();
        params.append("token_id", token_id||"");
        params.append("searchKey", this.query.searchKey || "");
        // if(this.ids.length > 0){
        //     params.append("ids", this.ids.join(','));
        // }else{
        //     params.append("searchKey", this.query.searchKey || "");
        // }
        this.$Loading.start();
        axios({
          url: "/flowanalysis/applicationMapping/excelExport?time="+Date.now(),
          method: "post",
          data: params,
          responseType: "blob", // 服务器返回的数据类型
        })
          .then((res) => {
            let data = res.data;
            const blob = new Blob([data], { type: "application/vnd.ms-excel" });
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, this.$t('maping_data')+".xlsx");
            } else {
              var fileName = "";
              fileName = this.$t('maping_data')+".xlsx"; 

              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.clearSelectData();
            this.$Loading.finish();
          })
          .catch(error => {
            console.log(error);
            this.$Loading.finish();
          }).finally(() => {
            this.$Loading.finish();
          });

      },
      // 批量删除
      batchDelete() {
        if(this.ids.length == 0){
          this.$Message.warning({ content: this.$t('comm_select_data'), background: true });
          return;
        }
        this.deleteClick()
      },
      handleSelectionChange(selection){
        let arr = selection.map(item => item.id)
        // 先删除这一页有的
        let tableListArr = this.tableData.map(item => item.id)
        this.ids = this.ids.filter(item => !tableListArr.includes(item))

        this.ids = [...this.ids, ...arr]
       
      },
      queryTale(){
        this.query.pageNo = 1;
        this.query.pageSize = 10;
        this.query.searchKey = this.query.searchKey.trim();
        this.getTableData()
      },
      async getTableData(){
        this.ids = this.selectedIds
        this.pageLoading = true
        this.query.searchKey = this.query.searchKey.trim();
        const res = await this.$http.PostJson('/flowanalysis/applicationMapping/list',this.query)
        console.log(res.data)
        if(res.code === 1){
          this.pageLoading = false
          this.tableData = res.data.records
          this.totalCount = res.data.total
          this.tableData.forEach(item => {
            
           if (this.selectedIds.includes(item.id)) {
              item._checked = true;
            }

          })
        }else{
          this.$Message.error(res.msg)
        }
      },
      importClick() {
        this.$refs.importModal.changeModal()
      },
        addClick(){
            this.$refs.addEditModal.changeModal('add')
        },
        openEditForm(row){
          let obj = {
            id:row.id,
            name:row.name,
            port:row.port,
            ip:row.ip
          }
          this.$refs.addEditModal.changeModal('edit',obj)
        },
        editClick(row){
            this.$refs.addEditModal.changeModal('edit',row)
        },
        rowRemove(row){
            this.$store.commit('m_maping/setSelectedIds', [row.id])
            this.deleteClick(row.id)
        },
        // 删除数据
       async deleteClick(id){
        var ids = [];
        if(id){
          ids.push(id);
        }else{
          ids = this.ids;
        }
        top.window.$iviewModal.confirm({
          title: this.$t('common_delete_prompt'),
          content: this.$t('device_discovery_del_flow_model_tips'),
          onOk: () => {
            this.$http.PostJson("/flowanalysis/applicationMapping/delete", {ids:ids}).then(res => {
              if (res.code === 1) {
                this.$Message.success({content:this.$t('comm_deleted_success'),background:true});
                this.query = {
                  pageNo:1,
                  pageSize:10,
                  searchKey:''
                }
                this.getTableData()
                this.$store.commit('m_maping/setSelectedIds', [])
              } else {
                this.$Message.error({content:res.msg,background:true});
              }
            })
          }
        })
        
        },
        pageChange(pageNo){
            this.query.pageNo = pageNo
               this.$store.commit('m_maping/setSelectedIds', this.ids)
            this.getTableData()
         
        },
        pageSizeChange(pageSize){
            this.query.pageNo = 1
            this.query.pageSize = pageSize
            this.$store.commit('m_maping/setSelectedIds', this.ids)
            this.getTableData()
        }
    }

}
</script>

<style scoped lang="less">
.map-index {
  padding: 20px;
  // background-color: var(--body_bg_color, #fff);
}
.map-light-index {
  margin: 20px;
  background-color: #fff;
}
.query_btn {
  display: flex;
  justify-content: flex-end;
  .ivu-btn {
    margin-left: 10px;
    height: 38px !important;
    line-height: 1 !important;
  }
}
.table-box {
  margin-top: 20px;
}
</style>
