<template>
  <!-- 应用tab -->
  <div
    :class="
      currentSkin == 1 ? 'application-index' : 'application-index light-index'
    "
  >
    <Loading :loading="loading"></Loading>
    <!-- 头部 -->
    <PageTool
      @queryClick="queryClick"
      @changeDevice="query.interfaceId = ''"
      :keyPlaceholder="$t('flow_tabs_label_application')"
    >
      <div
        class="ys-slot"
        slot="additional-filters"
        v-if="permissionObj.applyMapping"
      >
        <Tooltip :content="$t('flow_btn_tooltip_map')">
          <Button
            type="primary"
            class="daoChu-btn"
            @click="$router.push('/map')"
          >
            <img
              :src="
                currentSkin == 1
                  ? require('../../../assets/wisdom/icon-ys.png')
                  : require('../../../assets/wisdom/icon-mapping.png')
              "
              class="button-image"
            />
          </Button>
        </Tooltip>
      </div>
    </PageTool>
    <!-- /头部 -->
    <!-- 中间图表区域 -->

    <div class="chart-box">
      <!-- 图表标题 -->
      <TitleTool :TitleTool="$t('application_flow_top10')" />
      <!-- 图表标题 -->
      <div class="chart-content">
        <div class="chart-pie">
          <AnnularChart ref="annularChart" />
        </div>
        <div class="chart-graph">
          <BarGraph
            ref="barGraph"
            :flowTop10="barGraphData"
            :barGraphTitle="$t('flow_tabs_label_application')"
          />
        </div>
      </div>
    </div>
    <!-- /中间图表区域 -->
    <!-- 底部表格 -->
    <div class="table-box">
      <Table
        :columns="columns"
        :data="tableData"
        stripe
        :no-data-text="
          tableData.length > 0 || loading
            ? ''
            : currentSkin == 1
            ? '<div class=&quot;table_empty&quot;><p class=&quot;emptyText&quot; > ' +
              $t('common_No_data') +
              '</p></div>'
            : '<div class=&quot;table2_empty&quot;><p class=&quot;emptyText&quot; > ' +
              $t('common_No_data') +
              '</p></div>'
        "
      ></Table>
      <div class="tab-page" style="border-top: 0" v-if="tableData.length > 0">
        <Page
          v-page
          :current="pageNo"
          :page-size="pageSize"
          :total="pageTotal"
          :page-size-opts="pageSizeOpts"
          :prev-text="$t('common_previous')"
          :next-text="$t('common_next_page')"
          @on-change="pageChange"
          @on-page-size-change="pageSizeChange"
          show-elevator
          show-sizer
        >
        </Page>
      </div>
    </div>
    <!-- /底部表格 -->
    <!-- 查看详情 -->

    <DetailModal
      ref="detailModal"
      :detailLoading="detailLoading"
      :modalTitle="modalTitle"
      @getFlowData="getFlowData"
    />

    <!-- /查看详情 -->
  </div>
</template>

<script>
import PageTool from "../components/PageTool.vue";
import TitleTool from "../components/TitleTool.vue";
import AnnularChart from "../components/echarts/AnnularChart.vue";
import BarGraph from "../components/echarts/Bargraph.vue";

import "@/config/page.js";
export default {
  data() {
    return {
      queryRow:{},
      modalTitle: "",
      detailLoading: false,
      loading: false,
      showDetail: false,
      TitleTool: this.$t("flow_tabs_label_application"),

      currentSkin: sessionStorage.getItem("dark") || 1,
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      pageNo: 1,
      pageSize: 10,
      columns: [
        {
          title: this.$t("device_discovery_port_name"),
          key: "portName",
          tooltip: true,
         
          },
        {
          title: this.$t("flow_table_title_application_name"),
          key: "appName",
           tooltip: {
            placement: 'right-start'
          }
        },
        {
          title: this.$t("flow"),
          key: "appFlow", 
          tooltip: {
            placement: 'right-start'

          }
        },
        {
          title: this.$t("flow_table_title_traffic_percentage"),
          key: "appRatio",
          tooltip: {
            placement: 'right-start'

          }
        },
        {
          title: this.$t("comm_operate"),
          key: "operate",
          width: 110,
          render: (h, { row }) => {
            let modify = h(
              "Tooltip",
              {
                props: {
                  placement: "left-end",
                  transfer: true,
                },
              },
              [
                h("span", {
                  // class:'look-icon',
                  class:
                    this.currentSkin == 1 ? "icon-zhibiao" : "light-look-icon",
                  style: {
                    cursor: this.permissionObj.applyLook
                      ? "pointer"
                      : "not-allowed",
                  },
                  on: {
                    click: () => {
                      // this.showDetail = true
                      this.$nextTick(() => {
                        if (this.permissionObj.applyLook) {
                          // this.showDetail = true;
                          this.modalTitle = row.appName;
                          this.$refs.detailModal.setModalShow();
                          this.getAnnularChart(row);
                          this.modalTitle = row.appName;
                        }
                      });
                      // this.$refs.detailModal.setModalShow()
                    },
                  },
                }),
                h("span", { slot: "content" }, this.$t("comm_view_details")),
              ]
            );
            let array = [];
            array.push(modify);
            return h("div", array);
          },
        },
      ],
      tableData: [],
      tableQuery: {
        pageNo: 1,
        pageSize: 10,
        keyWords: "",
        flowDirection: 0,
        deviceId: this.$store.state.m_select.rowData.deviceId,
        interfaceId: "",
        startTime: "",
        endTime: "",
      },
      pageTotal: 0,
      flowTop10: [],
      flowTrendChartData: [],
      barGraphData: [],
    };
  },
  computed: {
    permissionObj() {
      return this.$store.state.m_permission.permissionObj;
    },
  },
  components: {
    PageTool,
    TitleTool,
    AnnularChart,
    BarGraph,
    DetailModal: () => import("../components/echarts/DetailModal.vue"),
  },
  methods: {
   
     getFlowData(queryobj){
        this.queryRow.startTime = queryobj.startTime
        this.queryRow.endTime = queryobj.endTime
       
        this.getAnnularChart(this.queryRow,1)

      },
    //获取弹框趋势图
    async getAnnularChart(row,type = 0) {
      this.$refs.detailModal.zoomTime = false
      this.detailLoading = true;
      console.log(row, "row");
      let obj = {
        appId: row.appId,
        startTime: type == 0 ? this.tableQuery.startTime : this.queryRow.startTime,
        endTime: type == 0 ? this.tableQuery.endTime : this.queryRow.endTime,
        flowDirection: this.tableQuery.flowDirection,
        deviceId: this.tableQuery.deviceId,
      };
      // debugger
      try {
        let res = await this.$http.PostJson(
          "/flowanalysis/findApplyFlowSpendTrend",
          obj
        );
        if (res.code == 1) {
          this.flowTrendChartData = res.data.flows;
          this.$refs.detailModal.initEcharts(this.flowTrendChartData,this.tableQuery);

        }else{
          this.$Message.error(res.msg)
        }
      } catch (e) {
        // this.$Message.error(e.message)
      }
      this.detailLoading = false;

      // console.log(res,'res')
    },
    getLastHour() {
            // 修改为获取当天时间范围
            const end = new Date();
            const start = new Date();
            
            // 设置开始时间为当天 00:00:00
            start.setHours(0, 0, 0, 0);
            
            // 设置结束时间为当天 23:59:59
            end.setHours(23, 59, 59, 999);
            
            this.tableQuery.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            this.tableQuery.endTime = end.format("yyyy-MM-dd HH:mm:ss");
            // this.timeRange = [start, end];
        },
    queryClick(query) {
      console.log(query, "query");
      this.pageNo = 1;
      this.pageSize = 10;
      this.tableQuery.keyWords = query.keyWords.trim();
      this.tableQuery.startTime = query.startTime;
      this.tableQuery.endTime = query.endTime;
      this.tableQuery.flowDirection = query.flowDirection;
      this.tableQuery.deviceId = query.deviceId;
      this.tableQuery.interfaceId = query.interfaceId;
      this.tableQuery.pageNo = this.pageNo;
      this.tableQuery.pageSize = this.pageSize;
      this.getTableList();
    },
    async getTableList() {
      this.tableQuery.keyWords = this.tableQuery.keyWords.trim();
      this.loading = true;
      try {
        let res = await this.$http.PostJson(
          "flowanalysis/applyList",
          this.tableQuery
        );
        if (res.code == 1) {
          // debugger
          this.tableData = res.data.records;
          this.pageTotal = res.data.total;
          this.flowTop10 = res.data.flowTop10.map((item) => {
            return {
              value: item.appRatioDouble,

              name: item.appName,
            };
          });
          this.barGraphData = res.data.flowTop10.map((item) => {
            return {
              ratio: item.appRatio,
              ratioDouble: item.appRatioDouble,
              name: item.appName,
              tooltip: item.appName,
              flow: item.appFlow,
            };
          });

          if (this.$refs.detailModal) {
            await this.$nextTick();
            this.$refs.annularChart.initEchart(this.flowTop10);
          }
          //  this.$refs.barGraph.initEchart(this.flowTop10)
        } else {
          this.$Message.warning(res.msg);
        }
        this.loading = false;
      } catch (e) {
        // this.$Message.error(e.message)
        this.loading = false;
      }
    },
    pageChange(page) {
      console.log(page);
      this.tableQuery.pageNo = page;
      this.pageNo = page;
      this.getTableList();
    },
    pageSizeChange(pageSize) {
      this.tableQuery.pageSize = pageSize;
      this.tableQuery.pageNo = 1;
      this.pageNo = 1;
      this.pageSize = pageSize;
      this.getTableList();
    },
    handleView(row) {
      // 处理查看详情
      console.log("View details:", row);
    },
  },
  created() {
    this.$store.commit(
      "m_select/setDeviceId",
      this.$store.state.m_select.rowData.deviceId
    );
    this.$store.commit("m_select/setInterfaceId", "");
    this.getLastHour();
    this.getTableList();

    console.log("permissionObj---", this.permissionObj);
  },
};
</script>

<style lang="less" scoped>
.application-index {
  height: 100%;
  // padding: 16px;
  position: relative;

  .chart-box {
    margin: 16px 0;
    // background: #fff;
    // padding: 16px;

    .chart-content {
      display: flex;
      margin-top: 16px;

      .chart-pie {
        flex: 1;
      }

      .chart-graph {
        flex: 1;
      }
    }
  }

  .table-box {
    // padding: 16px;

    .tab-page {
      margin-top: 16px;
      text-align: right;
    }
  }
}
/deep/.ivu-btn {
  line-height: 1 !important;
}
.ys-slot {
  margin-left: 10px;
}
/deep/.ivu-table-cell {
  padding-left: 30px !important;
}
</style>