<template>
  <div :class="{ 'light-no-tabs': currentSkin == 0 }">
    <section class="sectionBox">
      <!-- 流量分析 -->
      <div class="section-top">
        <Row class="fn_box">
          <!-- 机构 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg"
                  @onClear="onClear"
                  @onFocus="focusFn"
                >
                </TreeSelect>
              </div>
            </div>
          </Col>
          <!-- 设备类型 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("discover_device_type") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.deviceType"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="(item, index) in deviceTypeList"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 设备厂商 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("snmpoid_device_merchant")
                }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.deviceFactory"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in factoryList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 设备型号 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("snmp_task_device_model") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.deviceModel"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="(item, index) in devModelList"
                    :value="item.showCode"
                    :key="index"
                    >{{ item.showCode }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("comm_om_level") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.maintainLevel"
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in maintainList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col>
          <!-- 分组 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.groupIdStrs"
                  multiple
                  clearable
                  filterable
                  :only-filter-with-text="true"
                  :max-tag-count="1"
                  :placeholder="$t('comm_please_select')"
                >
                  <Option
                    v-for="item in groupSearchList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 关键字 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{
                  width: currentSkin == 0 && lang == 'en' ? '220px' : '200px',
                }"
                >{{ $t("comm_keywords") }}：</label
              >
              <div class="fn_item_box">
                <Input
                  v-model.trim="query.keyWords"
                  :placeholder="$t('comm_search_ip_name')"
                  :title="$t('comm_search_ip_name')"
                  clearable
                />
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <Tooltip :content="$t('common_query')" v-if="permissionObj.look">
              <Button
                class="jiaHao-btn"
                type="primary"
                @click="queryClick"
                :title="$t('common_query')"
              >
                <i class="iconfont icon-icon-query" />
              </Button>
            </Tooltip>
            <Tooltip :content="$t('common_delete')" v-if="permissionObj.delete">
              <Button class="delete-btn" type="primary" @click="deleteClick">
                <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
              </Button>
            </Tooltip>
          </div>
        </div>
      </div>
      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              stripe
              :columns="columns"
              :data="tableList"
              class="fixed-left-right"
              @on-selection-change="handleSelectionChange"
              @on-sort-change="sortSum"
              :no-data-text="
                loading
                  ? ''
                  : tableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              size="small"
            >
            </Table>
            <div
              class="tab-page"
              style="border-top: 0"
              v-if="tableList.length > 0"
            >
              <Page
                v-page
                :current.sync="query.pageNo"
                :page-size="query.pageSize"
                :total="totalCount"
                :page-size-opts="pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
      <!-- 自定义操作列 -->

      <Modal
        sticky
        v-model="customModalShow"
        width="540"
        height="720"
        class="threshold-modal modal-selected"
        :title="$t('custom_list_items')"
        draggable
        :mask="true"
        :loading="customModalShowLoading"
      >
        <Row class="modal-selected-row">
          <Col>
            <Table
              ref="fieldsModalTableData"
              height="550"
              width="400"
              :show-header="false"
              :columns="fieldsColumns"
              :data="allocationListFields"
              @on-row-click="getFieldsColumnsIndex"
              :row-class-name="rowClassName"
            >
            </Table>
          </Col>
          <Col>
            <div class="btn-box">
              <Tooltip :content="$t('dash_up')" placement="right">
                <div class="sort-btn" @click="moveUp">
                  <Icon
                    type="md-arrow-up"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
              <Tooltip :content="$t('dash_down')" placement="right">
                <div class="sort-btn" @click="moveDown">
                  <Icon
                    type="md-arrow-down"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
            </div>
          </Col>
        </Row>
        <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
// import axios from "axios";
import global from "@/common/global.js";
import "@/config/page.js";
import locationreload from "@/common/locationReload";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import {tableEditBtn} from '@/assets/base64Img/img'
import {tableEditLightBtn} from '@/assets/base64Img/img'
import ipv6Format from "@/common/ipv6Format";
import langFn  from '@/common/mixins/langFn';
function skinChange(obj) {
  const property = Object.keys(obj);
  const color = Object.keys(obj).map(function (i) {
    return obj[i];
  });
  let root = document.documentElement;
  for (let i = 0; i < property.length; i++) {
    root.style.setProperty(property[i], color[i]);
  }
}


export default {
    mixins: [langFn],
    components: { TreeSelect },
    data() {
        return {
          tableWidth: 0,
          lang: localStorage.getItem('locale') || 'zh',
           customMoveIndex:0,
          customModalShow:false,
          customModalShowLoading:false,
            currentSkin:sessionStorage.getItem('dark') || 1,
            customKey:"api:flowanalysis/list+sys_func_id:1974",
              screenWidth:0,
              fieldsJsonObjArr:[],
              allocationListFields:[],
              customFieldsColumnsKeyWidth:[],
          
            treeValue: "",
            treeValue1: "",
            loginUserOrgId: "",
            loginUserOrgName: "",
            //权限对象
            permissionObj: {},
            loading: false,
            // 机构
            orgList: [],
            treeData: [],
            // 设备类型
            deviceTypeList: [ ],
            // 设备厂商
            factoryList: [],
            // 设备型号
            devModelList: [],
            // 采集器
            getherList: [],
            totalTable: [],
            maintainList: [
                { value: 1, label: this.$t("logback_first") },
                { value: 2, label: this.$t("logback_second") },
                { value: 3, label: this.$t("logback_tertiary") },
            ],
            groupSearchList: [],
           
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            
            query: {
                orgId: "",
                deviceType:'',
                deviceFactory:'',
                deviceModel:'',
                maintainLevel:'',
                groupIds:'',
                groupIdStrs:[],
                keyWords:'',
                pageNo: 1,
                pageSize: 10,
                fieldName:'',
                orderBy:'',
            },
            // 表格
            tableList: [],
            repeatVoList: [],
            columns:[],
            fixedColumns: [
                {
                    type: "selection",
                    width: 30,
                    key:'selection',
                    align: "center",
                    // fixed: 'left',
                },
                {
                    title: this.$t("device_code"),
                    key: "deviceCode",
                    align: "left",
                    width: 180,
                    tooltip: true,
                },
                {
                    title: this.$t("comm_Device_name"),
                    key: "deviceName",
                    align: "left",
                    minWidth: 180,
                    tooltip: true,
                    render: (h, param) => {
                        let str = param.row.deviceName;
                        if(!str || str == 'null' || str == '') {
                          str = '--'
                        }
                        return h(
                            "Tooltip",
                            {
                                props: {
                                    placement: "top-start",
                                    transfer: true,
                                },
                            },
                            [
                                h(
                                    "span",
                                    {
                                        // class: "action-btn",
                                        style: { 
                                          cursor:this.permissionObj.look ? "pointer" : "not-allowed",
                                          color: this.currentSkin == 1 ? "#05EEFF" : "#0290FD",
                                          whiteSpace: "nowrap",
                                          overflow: "hidden", 
                                          textOverflow: "ellipsis",
                                          display: "block",
                                          marginTop:'8px'
                                      },
                                        on: {
                                            click: () => {
                                              if(this.permissionObj.look) {
                                                this.actionClick(param.row, "linkNum");
                                              }
                                            },
                                        },
                                    },
                                    str
                                ),
                                h(
                                    "span",
                                    {
                                        slot: "content", //slot属性
                                        style: {
                                            whiteSpace: "normal",
                                            wordBreak: "break-all",
                                        },
                                    },
                                    str
                                ),
                            ]
                        );
                    },
                },
                // 设备ip
                {
                    title: this.$t("phytopo_device_ip"),
                    key: "deviceIp",
                    align: "left",
                    width: 200,
                     render: (h, params) => {
                    let str = params.row.deviceIp;
                    let maxWidth = params.column.width; // 获取动态传递的宽度

                  
                    str = ipv6Format.formatIPv6Address(str,maxWidth);
                    return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },
                },
                // 设备类型
                {
                    title: this.$t("discover_device_type"),
                    key: "deviceType",
                    align: "left",
                    width: 120,
                    render: (h, params) => {
                        let str = params.row.deviceType;
                        str =
                            str === undefined || str === null || str === "" || str === "null"
                                ? "--"
                                : str;
                        // let str1 = str;
                        // if (str.length > 5) {
                        //     str1 = str.substring(0, 5) + "...";
                        // }
                        // return h(
                        //     "Tooltip",
                        //     {
                        //         props: {
                        //             placement: "top",
                        //         },
                        //     },
                        //     [
                        //         str1,
                        //         h(
                        //             "span",
                        //             {
                        //                 slot: "content", //slot属性
                        //                 style: {
                        //                     whiteSpace: "normal",
                        //                     wordBreak: "break-all",
                        //                 },
                        //             },
                        //             str
                        //         ),
                        //     ]
                        // );
                        if(str !== "--") {
                        return h('div',{class:'table-ellipsis'},[
                        h('Tooltip',{
                          props: {
                            placement:'top-start',
                            content: str,
                          }

                        },str)

                      ])
                      }else {
                        return h('div',str)
                      }
                    },
                },
                // 设备厂商
                {
                    title: this.$t("snmpoid_device_merchant"),
                    key: "deviceFactory",
                    align: "left",
                    width: this.getColumnWidth(120,130),
                    render: (h, params) => {
                        let str = params.row.deviceFactory;
                        str =
                            str === undefined || str === null || str === "" || str === "null"
                                ? "--"
                                : str;
                          if(str !== "--") {
                            return h('div',{class:'table-ellipsis'},[
                            h('Tooltip',{
                              props: {
                                placement:'top-start',
                                content: str,
                              }

                            },str)

                          ])
                          }else {
                            return h('div',str)
                          }
                        // let str1 = str;
                        // if (str.length > 7) {
                        //     str1 = str.substring(0, 7) + "...";
                        // }
                        // return h(
                        //     "Tooltip",
                        //     {
                        //         props: {
                        //             placement: "top",
                        //         },
                        //     },
                        //     [
                        //         str1,
                        //         h(
                        //             "span",
                        //             {
                        //                 slot: "content", //slot属性
                        //                 style: {
                        //                     whiteSpace: "normal",
                        //                     wordBreak: "break-all",
                        //                 },
                        //             },
                        //             str
                        //         ),
                        //     ]
                        // );
                    },
                },
                // 设备型号
                {
                    title: this.$t("snmp_task_device_model"),
                    key: "deviceModel",
                    align: "left",
                    width: 130,
                    render: (h, params) => {
                        let str = params.row.deviceModel;
                        str =
                            str === undefined || str === null || str === "" || str === "null"
                                ? "--"
                                : str;
                        if(str !== "--") {
                            return h('div',{class:'table-ellipsis'},[
                            h('Tooltip',{
                              props: {
                                placement:'top-start',
                                content: str,
                              }

                            },str)

                          ])
                          }else {
                            return h('div',str)
                          }
                    },
                },
                {
                    title: this.$t("comm_org"),
                    key: "orgName",
                    width: 120,
                    align: "left",
                    tooltip: true,
                },
                // 分组
                {
                    title: this.$t("comm_group"),
                    key: "groupName",
                    align: "left",
                    width: 150,
                    render: (h, params) => {
                        let str = params.row.groupName;
                        str =
                            str === undefined || str === null || str === "" || str === "null"
                                ? "--"
                                : str;
                        if(str !== "--") {
                          return h('div',{class:'table-ellipsis'},[
                          h('Tooltip',{
                            props: {
                              placement:'top-start',
                              content: str,
                            }

                          },str)

                        ])
                        }else {
                          return h('div',str)
                        }
                    },
                },
                {
                    title: this.$t("discover_gether_name"),
                    key: "getherName",
                    align: "left",
                    width: 150,
                    tooltip: true,
                },
                // 运维等级
                {
                    title: this.$t("comm_om_level"),
                    key: "maintainLevel",
                    align: "left",
                    sortable: "custom",
                    width: this.getColumnWidth(100,80),
                    render: (h, params) => {
                        const maintainLevel = params.row.maintainLevel;
                        let str = "--";
                        if (maintainLevel == 1) {
                            str = this.$t("logback_first");
                        } else if (maintainLevel == 2) {
                            str = this.$t("logback_second");
                        } else if (maintainLevel == 3) {
                            str = this.$t("logback_tertiary");
                        }
                        return h("span", str);
                    },
                },
                {
                    title: this.$t("message_server_port"),
                    key: "portNum",
                    align: "left",
                     sortable: "custom",
                    width: 90,
                    render: (h, params) => {
                      let str = params.row.portNum;
                      str =
                          str === undefined || str === null || str === "" || str === "null"
                              ? "--"
                              : str;
                      return h('div',str)
                    }
                },
                {
                    title: this.$t('flow_number'),
                    key: "flowNum",
                    align: "left",
                     sortable: "custom",
                    width: this.getColumnWidth(100,120),
                    render: (h, params) => {
                      let str = params.row.flowNum;
                      str =
                          str === undefined || str === null || str === "" || str === "null"
                              ? "--"
                              : str;
                      return h('div',str)
                    }
                },
                 {
                    title: this.$t('comm_operate'),
                    key: 'action',
                    width: this.getColumnWidth(100,130),
                    // fixed: 'right',
                    align: 'center',
                    renderHeader: (h) => {
                  const handleClick = () => {
                    this.customModalShow = true;
                    this.customModalShowLoading = true;
                  }
                  return h('div',[
                    h('span',this.$t('comm_operate')),
                    h('img', {
                      attrs: {
                        src:this.currentSkin == 1 ? tableEditBtn:tableEditLightBtn
                      },
                      style: {
                        width: '18px', // 调整图片大小
                        height: '18px', // 考虑保持宽高比
                        marginLeft: '10px',  
                        verticalAlign: 'middle',// 使图片居中
                        cursor: 'pointer'

                      },
                      on: {
                      click: handleClick,
                    
                    },
                    })
                  ])
          },
                    
                  render: (h,{ row }) => {
                  let modify = h(
                    'Tooltip',
                    {
                      props:{
                        placement:'top',
                        transfer: true
                      }
                    },
                    [
                      h('span',{
                        // class:'look-icon',
                        class: this.currentSkin == 1 ? 'icon-zhibiao':'light-look-icon',
                        style:{
                          cursor:this.permissionObj.applyLook ? 'pointer' : 'not-allowed',
                         
                          verticalAlign: 'middle',
                        },
                        on:{
                        click: () => {
                            // this.showDetail = true
                            this.$nextTick(() => {
                              if(this.permissionObj.applyLook) {
                                // this.showDetail = true;
                                this.actionClick(row, "linkNum");
                              }
                            });
                            // this.$refs.detailModal.setModalShow()
                          },

                      }
                      }),
                      h('span', { slot: 'content' }, this.$t('comm_view_details'))
                    ]
                  )
                  let array = [];
                  array.push(modify);
                  return h('div', array);
                }
                }
            ],
            fieldsColumns: [
      {
        key: "showField",
        width: 35,
        render: (h, params) => {
          let row = params.row;
          return h("Checkbox", {
            props: {
              value: row.showField,
              disabled: row.fixedField // 当fixedField为true时禁用Checkbox
            },
            on: {
              input: (value) => {
                if (!row.fixedField) {
                  // 更新 showField 的值
                  this.allocationListFields[params.index].showField = value;
                }
              }
            }
          });
        }
      },
        {
          key: "parameterTitle",
          align: "left",
          // width: 160,
          render:(h,params) => {
            let fontColor = 'var(--field_font_color ,#fff)'
            if(params.row.fixedField) {
              fontColor = '#5CA0D5'
            }
            return h('div',{
              style: {
                color:fontColor,
                fontWeight:400
              }
            },params.row.parameterTitle)
          }
        }
      ],
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: [],
            selectedIdsTemp: [],
        };
    },
    created() {
     
        window.addEventListener('resize', this.handleResize);
      let permission = global.getPermission();
      this.permissionObj = Object.assign(permission, {})
      this.$store.commit('m_permission/setPermissionObj', permission)

      // console.log(Object.assign(permission, {}))
    
         this.screenWidth = window.innerWidth-45;
       
        this.setCustomFieldsColumnsKeyWidth()
        this.handler(this.currentSkin);
        let nodeData = JSON.parse(localStorage.getItem("nodeData"))
        console.log(nodeData)
        if (nodeData) {
            this.$router.push({ path: "/deviceInfo" })

        } else {


        }
        this.getGroupSearchList();
        this.getTreeOrg();
        locationreload.loactionReload(this.$route.path.split("/")[1].toLowerCase());
        
        this.getDeviceType();
        this.getDeviceFactory();
        this.queryClick()
        this.getDevModelList()

    },
    watch: {
      
    },
   
    methods: {
       handleResize() {
        // alert(1)
        console.log(111)
            // 重新计算表格宽度
            this.$nextTick(() => {
            // 重新计算屏幕宽度
            this.screenWidth = window.innerWidth - 45;
            
            // 重新设置列宽
            this.setCustomFieldsColumnsKeyWidth();
            
            // 重新获取表格数据以更新列配置
            this.getAllocationListFieldsByKey();
    });
        },
      queryClick() {
        this.query.fieldName = ''
        this.query.orderBy = ''
        this.query.pageNo = 1
        this.getTableList()
      },
      // 排序
       sortSum(column) {
        console.log(column)
        this.query.fieldName = column.key
        this.query.orderBy = column.order
        this.getTableList()
      

    },
       //取消修改
    customModalCancel() {
      this.customModalShow= false;
      this.customModalShowLoading= false;
    },
       //修改自定列表项
    customModalOk() {
        this.$http.PostJson("/allocationlistfields/update", {allocationListFields:this.allocationListFields, key:this.customKey}).then((res) => {
        if (res.code === 1) {
          this.$Message.success({ content: this.$t('comm_success'), background: true });
          this.customModalShow= false;
          this.customModalShowLoading= false;
         this.queryClick()
        }
      })
    },
     //获取列表项下标并改变状态
    getFieldsColumnsIndex(row, index){
      this.customMoveIndex = index;
      
    },
     rowClassName(row, index) {
      if (index === this.customMoveIndex) {
        return "selected-row";
      }
      return "";
    },
     //上移
    moveUp() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex > 0) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex - 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex - 1;
      }
    },
    //下移
    moveDown() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex < this.allocationListFields.length - 1) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex + 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex + 1;
      }
      
    },
       //保存原始的字段项展示长度
    setCustomFieldsColumnsKeyWidth(){
      if(this.customFieldsColumnsKeyWidth==0){
        this.fixedColumns.forEach(item=>{
          let customFieldsColumnsKeyWidthObj = {"key":item.key,"width":item.width};
          this.customFieldsColumnsKeyWidth.push(customFieldsColumnsKeyWidthObj);
        });
      }
    },
      // 自定义列
      getAllocationListFieldsByKey(){
      this.$http.PostJson("/allocationlistfields/getAllocationListFieldsByKey", { key: this.customKey }).then((res) => {
        if (res.code === 1) {
          if(res.data.allocationListFields){
            let screenWidthTemp = this.screenWidth;
            this.allocationListFields = res.data.allocationListFields;
            this.fieldsJsonObjArr = [];
            // 获取显示表头
            this.fieldsJsonObjArr.push("selection");//第一列选择列
           
            res.data.allocationListFields.forEach(item=>{
                if(item.showField === true){
                  this.fieldsJsonObjArr.push(item.parameterName);
                }
            });
             this.fieldsJsonObjArr.push("action");//最后一列操作列
            // this.fieldsJsonObjArr.push("action");//最后一列操作列
            this.columns = [];
            //将显示的表头拿到函数放到columns中
            if(this.fieldsJsonObjArr.length>0){
              let customColumnsWidth = 0;//回显自定义字段总宽度
              let customColumnsAvgWidth = 0;//回显自定义字段平均宽度
                this.fieldsJsonObjArr.forEach(item=>{
                    this.fixedColumns.forEach(item2=>{
                        if(item === item2.key){
                        //计算需要展示自定义字段项总长度
                        let customFieldsColumnsKeyWidthTemp = this.customFieldsColumnsKeyWidth.find(item3 => item3.key === item2.key);
                        if(customFieldsColumnsKeyWidthTemp){
                          customColumnsWidth = customColumnsWidth + customFieldsColumnsKeyWidthTemp.width;
                          item2.width = customFieldsColumnsKeyWidthTemp.width;
                        }
                        this.columns.push(item2);
                        return;
                        }
                    });
                });
             //赋值标头名称
             this.allocationListFields.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item.parameterName === item2.key){
                    item.parameterTitle = item2.title;
                    return;
                  }
                });
              });
              //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
              if(screenWidthTemp>customColumnsWidth ){
                if(this.columns.length>2){
                  let columnsLength = this.columns.length-2;
                  customColumnsAvgWidth = Math.floor((screenWidthTemp-customColumnsWidth)/columnsLength);
                }
              }              
              this.columns.forEach(item=>{
                if(item.key != "action" && item.key != "selection"){
                  item.width = item.width+customColumnsAvgWidth;
                }
              });
            }else{
              this.columns = this.fixedColumns;
            }
          }
        }else{
             this.columns = this.fixedColumns;
        }
      })
    },
      // 获取设备型号
     async getDevModelList() {
      // this.devModelLoading = true
      let obj = {
         
        deviceFactory: this.query.deviceFactory,
        deviceType: this.query.deviceType,
      
      }
      try {
        const res = await this.$http.PostJson('/deviceModel/queryList',obj)
     if(res.code == 1) {
      this.devModelList = res.data
     }

      }catch(err) {
        console.log(err)
      }finally {
        // this.devModelLoading = false
      }
     

      },
      // 获取列表数据
      async getTableList() {
        this.selectedIds = this.selectedIdsTemp
         this.getAllocationListFieldsByKey()
         this.query.keyWords = this.query.keyWords.trim();
        this.loading = true
        if(this.query.groupIdStrs.length > 0){
          this.query.groupIds = this.query.groupIdStrs.join(",")
        }else{
          this.query.groupIds = "";
        }
        try {
         const res =  await this.$http.PostJson('/flowanalysis/list',this.query)
         if(res.code === 1) {
          this.tableList = res.data.records
          this.totalCount = res.data.total
          if(this.selectedIds.length > 0  ) {
            this.tableList.forEach(item => {
              if(this.selectedIds.includes(item.deviceId)) {
                item._checked = true
              }
            })  
          }
         }

        } catch (err) {
         
          console.log(err)  
        } finally {
          this.loading = false
        }
       

      },
      // 批量删除操作
      deleteClick() {
        // 1。校验是否选中
        // 2.请求删除
        // 3.刷新列表
        if (this.selectedIds.length === 0) {
          this.$Message.warning({ content: this.$t('comm_select_data'), background: true });
          return;
        }
        this.deleteData()
       
      },
     async deleteData() {
      let deviceIds = this.selectedIds.join(',')
      top.window.$iviewModal.confirm({
          title: this.$t('common_delete_prompt'),
          content: this.$t('device_discovery_del_flow_model_tips'),
          onOk: () => {
            this.$http.wisdomPost("/flowanalysis/delete", {deviceIds}).then(res => {
              if (res.code === 1) {
                this.$Message.success({content:this.$t('comm_deleted_success'),background:true});
                
                this.queryClick()
                this.selectedIds = []
              } else {
                this.$Message.error({content:res.msg,background:true});
              }
            })
          }
        })
      // try {
      //   const res = await this.$http.PostJson('/flowanalysis/delete',{deviceIds})
      //   if(res.code == 1) {
      //     this.$Message.success({ content: res.msg, background: true });
      //     this.getTableList()
      //   }
      // }catch(err) {
      //   console.log(err)
      // }

      },
      handleSelectionChange(selection) {
        console.log(selection, "--------------------------");
        let arr = selection.map(item => item.deviceId)
        let tableListArr = this.tableList.map(item => item.deviceId)
        this.selectedIds = arr.filter(item => !tableListArr.includes(item))
        this.selectedIds = [...this.selectedIds, ...arr]
        console.log(this.selectedIds, "--------------------------");
      },
      // 分组
      getGroupSearchList(orgId) {
        const param = {
        orgId:orgId,
      };
      console.log(param)
      // debugger
      this.$http.wisdomPost("/group/groupList", param).then((res) => {
        if (res.code === 1) {
          if (res.data) {
            this.groupSearchList = res.data;
          }
        }
      });
    },
      

        // 树形选择聚焦
        focusFn() {
            this.getTreeOrg();
        },

        handleDeviceTypeCreate(value) {
            var itemArrays = this.deviceTypeList.filter((item) => {
                return item.name == value;
            });

            if (itemArrays.length > 0) {
                return;
            }

            this.deviceTypeList.push({
                isNew: true,
                name: value,
                id: Date.now(),
            });
        },
        // 分组select展开
        slectOpen(value) {
            console.log(value);
        },
       

      

        getRowData(row) {
            // debugger
            let str = Number(row.deviceType),
                text = "--";
            var items = this.deviceTypeList.filter((item) => {
                return item.id == str;
            });

            if (items.length > 0) {
                text = items[0].name;
            }

            row.deviceTypeName = text;

            str = Number(row.factory);
            text = "--";
            items = this.factoryList.filter((item) => {
                return item.id == str;
            });

            if (items.length > 0) {
                text = items[0].name;
            }

            row.factoryName = text;
            return row;
        },
        actionClick(row) {
            row = this.getRowData(row);
            // 重新赋值
            let str = row.deviceName;
            let sysname = row.sysname;
            if (str && sysname) {
                str = sysname + "(" + str + ")";
            } else if (!str && sysname) {
                str = sysname + "(--)";
            } else if (str && !sysname) {
                str = "--(" + str + ")";
            } else if (!str && !sysname) {
                str = "--";
            }
            row.deviceNameStr = str;
            this.$router.push({
                path: "/deviceInfo",
                query: {
                    rowData: row,
                },
            });
            
        },

      
      
        // 单独勾选
     
        // 翻页
        pageChange(val) {
            this.query.pageNo = val
            this.selectedIdsTemp = this.selectedIds
            this.getTableList()
        },
        // 选择条数
        pageSizeChange(val) {
            this.query.pageSize = val;
            this.query.pageNo = 1
            this.selectedIdsTemp = this.selectedIds
            this.getTableList()
        },
        // 获取设备厂商
        getDeviceFactory() {
            this.$http.wisdomPost("/deviceFactory/queryList").then((res) => {
                if (res.code === 1) {
                    this.factoryList = res.data;
                } else {
                    this.$Message.error({ content: res.msg, background: true });
                }
            });
        },
        // 获取机构
        getTreeOrg(type) {
            let self = this;
            this.$http.PostJson("/org/tree", { orgId: null }).then((res) => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                        }
                        return item;
                    });
                    self.orgList = treeNodeList;
                    self.treeData = treeNodeList;
                }
            });
        },
        loadOrgTreeData(item, callback) {
            this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                        }
                        return item;
                    });
                    callback(childrenOrgList);
                }
            });
        },
        setOrg(item) {
            this.treeValue = item[0].name;
            this.query.orgId = item[0] ? item[0].id : null;
        },
        onClear() {
            this.treeValue = "";
            this.query.orgId = "";
        },
        // 获取设备类型
        getDeviceType() {
            this.$http.wisdomPost("/deviceType/queryList").then((res) => {
                if (res.code === 1) {
                    this.deviceTypeList = res.data;
                    console.log(this.deviceTypeList, "-----------------------");
                } else {
                    this.deviceTypeList = [];
                    this.$Message.error({ content: res.msg, background: true });
                }
            });
        },
        handler(val) {
            let pie_legend_normal_color_arr = ['#03B999','#00FFEE']
        let pie_legend_deterioration_color_arr = ['#FEA31B','#FEA31B']
        let pie_legend_break_color_arr = ['#FE5C5C','#FE5C5C']
        let pie_legend_suspend_color_arr = ['#97C5E9','#4EAED2']
        let pie_count_color_arr = ['#0290FD','#ffffff']
        let point_name_bg_arr = ['#F5F7FA','rgba(46, 72, 93, 0.2)']
        // 以上仪表盘饼图
        let body_b_color_arr = ["#F5F6FA", "#060D15"];//整体背景颜色
        let sidebar_b_color_mr_arr = ["#F5F6FA", "#061824"];//左边菜单栏背景颜色
        let body_conent_b_color_arr = ["#ffffff", "#061824"];//表格内容的背景颜色
        let search_lable_font_color_arr = ["#515A6E", "#fff"];//查询条件lable标题字体颜色
        let button_background_color_arr = ["#0290FD", "#05EBEB"];//查询按钮背景颜色
        let button_background_color_arr_two = ["#0290FD", "#049DEC"];//查询按钮背景颜色
        let button_background_color_arr_three = ["#FFFFFF", "#151106"];//查询按钮背景颜色
        let reset_export_del_button_font_color_arr = ["#0290FD", "#05EEFF"];//重置，导出按钮字体颜色
        let del_button_font_color_arr = ["#FEA31B", "#fea31b"];//重置按钮字体颜色
        let reset_export_button_border_1_color_arr = ["#0290FD", "#015197"];//重置导出按钮边框颜色
        let reset_export_button_border_2_color_arr = ["#0290FD", "#31F0FE"];//重置导出按钮边框颜色
        let reset_export_button_border_3_color_arr = ["#0290FD", "#015197"];//重置导出按钮边框颜色
        let scrollbar_thumb_bg_color_arr=["#DCDFE6", "#015197"];//滚动条里面小方块背景颜色
        let scrollbar_track_bg_color_arr=["#F5F7FA", "#06324D"];//滚动条里面轨道背景颜色
        let scrollbar_thumb_shadow_color_arr=["#fff", "rgba(6, 50, 77, 1)"];//滚动条里面小方块背景颜色
        let scrollbar_track_shadow_color_arr=["#fff", "rgba(0, 0, 0, 0.2)"];//滚动条里面轨道背景颜色
        let query_btn_hover_bg_1_color_arr = ["#5CADFF","#049DEC"];//查询按钮悬浮上去后变化背景颜色
        let query_btn_hover_bg_2_color_arr = ["#5CADFF","#05EBEB"];//查询按钮悬浮上去后变化背景颜色
        let query_btn_active_bg_1_color_arr = ["rgba(6, 132, 233, 1)","rgba(4, 157, 236, 0.8)"];//查询按钮点击之后变化背景颜色
        let query_btn_active_bg_2_color_arr = ["rgba(6, 132, 233, 1)","rgba(5, 235, 235, 0.8)"];//查询按钮点击之后变化背景颜色
        let modal_b_color_arr = ["#ffffff", "#08101A"];
        let modal_input_border_color_arr = ["#dcdee2", "#35455d"];
        let header_b_color_arr = ["#1252c8", "#253142"];
        let header_font_color_arr = ["#ffffff", "#ffffff"];//最上面菜单栏名字文字颜色
        let search_check_box_background_color_arr = ["#0290FD", "#02b8fd"];//查询条件复选框背景图
        let search_check_box_font_color_arr = ["#FFFFFF", "#060d15"];//查询条件复选框文字颜色
        let search_check_box_bg_tabStyle_color_arr=["#FFFFFF", "#061824"];//故障清单故障历时复选框背景图
        let search_check_box_font_tabStyle_color_arr=["#0290FD", "#fff"];//故障清单故障历时选中后文字复选框背景图
        let search_check_box_font_tabStyle_two_color_arr=["#515A6E", "#5ca0d5"];//故障清单故障历时文字复选框背景图
        let search_ivu_select_input_boder_color_1_arr=["#DCDFE6", "#060d15"];//搜索栏查询条件输入框颜色
        let search_ivu_select_input_boder_color_2_arr=["#DCDFE6", "#015197"];//搜索栏查询条件输入框颜色
        let search_ivu_select_input_boder_color_3_arr=["#DCDFE6", "#31f0fe"];//搜索栏查询条件输入框颜色
        let search_ivu_select_input_boder_color_4_arr=["#DCDFE6", "#ffffff"];//搜索栏查询条件输入框颜色
        let button_networdFault_del_bg_color_arr=["#FEA31B","#8a5505"];//故障清单-清除按钮颜色
        let button_networdFault_del_bg_2_color_arr=["#ffffff","#061824"];//故障清单-清除按钮颜色
        let button_networdFault_del_bg_3_color_arr=["#FEA31B","#b77006"];//故障清单-清除按钮颜色
        let button_networdFault_del_bg_4_color_arr=["#FEA31B","#fea31b"];//故障清单-清除按钮颜色
        let networdFault_type_border_color_arr=["#fff","#04478e"];//故障清单-故障类型复选框边框颜色
        let networdFault_type_border_normal_color_arr=["#DCDFE6","#04478e"];//故障清单-故障类型正常复选框边框颜色
        let networdFault_type_border_normal_font_color_arr=["#515A6E","#5ca0d5"];//故障清单-故障类型正常复选框字体颜色
        let networdFault_duration_border_select_color_arr=["#0290FD","#05eeff"];//故障清单-故障类型正常复选框字体颜色
        let query_org_select_color_arr=["#808695","#05eeff"];//查询条件：组织查询方向颜色
        let more_btn_font_color_arr = ["#515A6E","#02b8fd"];//查询栏-更多按钮-文字颜色
        let more_btn_background_image_1_color_arr = ["#DCDFE6","#015197"];//查询栏-更多按钮-背景和边框颜色
        let more_btn_background_image_2_color_arr = ["#DCDFE6","#31f0fe"];//查询栏-更多按钮-背景和边框颜色
        let more_btn_ivu_dropdown_item_font_color_arr=["#515A6E","#dff1ff"];//查询栏-更多按钮-下拉列表-正常字体颜色
        let more_btn_ivu_dropdown_item_hover_bg_color_arr=["#DCDFE6","#06324d"];//查询栏-更多按钮-下拉列表-悬浮后背景颜色
        let more_btn_ivu_dropdown_item_hover_font_color_arr=["#0290FD","#00ffee"];//查询栏-更多按钮-下拉列表-悬浮后字体颜色
        let ivu_tooltip_inner_bg_color_arr = ["rgba(255, 255, 255, 1)","rgba(22, 67, 107, 1)"];//提示框背景颜色
        let ivu_tooltip_inner_font_color_arr=["#515A6E","#FFF"];//提示框字体颜色
        let table_text_click_color_arr=["#0290FD","#05EEFF"];//表格-文字-点击颜色
        let ivu_select_visible_border_color_arr=["#0290FD","#05eeff"];//查询条件-点中输入框-边框颜色
        let ivu_picker_confirm_bg_color_arr=["#FFFFFF","#06324d"];//查询条件-时间选择器-文字背景颜色
        let ivu_picker_confirm_border_color_arr=["#DCDFE6","#06324d"];//查询条件-时间选择器-边框颜色
        let ivu_picker_confirm_font_color_arr=["#515A6E","#c5c8ce"];//查询条件-时间选择器-文字颜色
        let ivu_picker_confirm_hover_bg_color_arr=["#0290FD","#06324d"];//查询条件-时间选择器-按钮悬浮背景颜色
        let ivu_picker_confirm_hover_font_color_arr=["#FFFFFF","#57a3f3"];//查询条件-时间选择器-按钮悬浮文字颜色
        let ivu_picker_confirm_select_font_color_arr=["#515A6E","#c5c8ce"];//查询条件-时间选择器-选择时间按钮文字颜色
        let inintEchart_dataZoom_filler_color_arr=["rgba(228, 231, 237, 1)","rgba(2, 29, 54, 1)"];//趋势图-时间选择-背景颜色
        let inintEchart_dataZoom_border_color_arr=["rgba(228, 231, 237, 1)","rgba(22, 67, 107, 1)"];//趋势图-时间选择-背景颜色
        let rpapth_a_link_lTitle_color_arr=["#17233D","#05eeff"];//中继线路监测-趋势图-标头文字颜色
        let quality_report_left_menu_bg_color_arr=["#FFFFFF","#011B2D"];//质量报告-详情-左边菜单栏-背景颜色
        let quality_report_left_ivu_menu_font_color_arr=["#17233D","#fff"];//质量报告--详情-左边菜单栏-字体颜色
        let defaultmanager_left_menu_bg_color_arr=["#F5F6FA","#16436b"];//默认配置-左边菜单选中后背景颜色
        let defaultmanager_left_menu_font_color_arr=["#0290FD","#05eeff"];//默认配置-左边菜单选中后字体颜色
        let dashboard_button_hover_font_color_arr=["#0290FD","#ffffff"];//仪表盘-按钮-悬浮-字体颜色
        let dashboard_component_title_font_color_arr=["#0290FD","#00ffee"];//仪表盘-构件-里面标题字体颜色
        let dashboard_bigscale_bg1_color_arr=["#0290FC","#049DEC"];//仪表盘-放大图标-背景颜色
        let dashboard_bigscale_bg2_color_arr=["#0290FC","#05EBEB"];//仪表盘-放大图标-背景颜色
        let dashboard_userDefined_btn_border1_color_arr = ["#0290FD","#015197"];//查询栏-更多按钮-背景和边框颜色
        let dashboard_userDefined_btn_border2_color_arr = ["#0290FD","#31f0fe"];//查询栏-更多按钮-背景和边框颜色
        let pathtopo_tabs_box_select_font_color_arr = ["#808695","#05eeff"];//路径拓扑-选择框-字体颜色
        let pathtopo_tabs_box_select_border_color_arr = ["#DCDFE6","#045b8e"];//路径拓扑-选择框-边框颜色
        let pathtopo_tabs_box_select_bg_color_arr = ["#fff","#061824"];//路径拓扑-选择框-背景颜色
        let pathtopo_logo_box_bg_color_arr=["#fff","#061824"];//路径拓扑-拓扑图上的log标志的背景图
        let pathtopo_logo_box_font_color_arr=["#808695","#5ca0d5"];//路径拓扑-拓扑图上的log标志的字体颜色图
        let pathtopo_fiber_routine_model_font_color_arr=["#808695","#5ca0d5"];//路径拓扑-选择展示方式没有悬浮情况下的字体颜色
        let pathtopo_fiber_routine_model_active_font_color_arr=["#0290FC","#05eeff"];//路径拓扑-选择展示方式悬浮情况下的字体颜色
        let pathtopo_icon_box_border_color_arr=["#DCDFE6","#045b8e"];//路径拓扑-图标-边框颜色
        let pathtopo_icon_special_suspend_border_color_arr=["#0290FD","#05eeff"];//路径拓扑-图标-悬浮-边框颜色
        let pathtopo_my_tooltip_bg_color_arr=["#FFFFFF","#06324d"];//路径拓扑-图标-提示框背景颜色
        let pathtopo_my_tooltip_left_box_drag_step_bg_color_arr=["#F5F7FA","#22465d"];//路径拓扑-图标-收缩右边滚动条背景颜色
        let pathtopo_my_tooltip_left_box_step_bg_color_arr=["#DCDFE6","#05eeff"];//路径拓扑-图标-收缩左边滚动条背景颜色
        let pathtopo_my_tooltip_left_box_step_circle_bg_color_arr=["#F5F7FA","#06324d"];//路径拓扑-图标-收缩节点背景颜色
        let pathtopo_my_tooltip_left_box_num_tip_bg_color_arr=["#F5F7FA","#015197"];//路径拓扑-图标-收缩节点数字颜色
        let comm_back_icon_bg_color_arr=["#0290FD","#31f0fe"];//返回按钮字体颜色
        let pathtopo_list_select_bg_color_arr=["rgba(255, 255, 255, 1)","rgba(0, 0, 0, 0)"];//路径拓扑-列表，下拉选择背景颜色
        let data_sync_primary_bg_arr = ["#FFFFFF", "transparent"];//数据同步按钮背景颜色
        let ivu_radio_inner_color_arr = ["#C0C4CC", "#ffffff"];//多个radio选择框
        let specialmonitor_checkedItem_bg_color_arr=["#fff","#06324d"];//专线监测选择专线背景图
        let probetaskModal_item_line_bg_color_arr=["#DCDFE6","#06324D"];//拨测任务管理-新增-分割线背景色
        let picker_panel_content_after_color_arr =["#F5F6FA","#154b81"];//时间选择器-分割线背景色
        let pathtopo_legend_splitline_border_color_arr=["#DCDFE6","#22465d"];//路径拓扑-图例弹框-分割线-颜色
        let dashboard_component_loading_bg_color_arr=["#FFFFFF","#000000"];//仪表盘-构件-加载中-背景颜色
        let testspeed_skinPrimary_bg_color_arr = ["#fff", "transparent"];//在线测速页面按钮背景颜色
        let testspeed_skinPrimary_font_color_arr = ["#0290FD", "#2d8cf0"];//在线测速页面按钮字体颜色
        let testspeed_restStartBut_bg_color_arr = ["#0290FD", "#169bd5"];//在线测速页面按钮字体颜色
        let header_active_bg_color_arr = [
          "rgba(125, 165, 232, 0.16)",
          "rgba(97, 124, 165, 0.16)",
        ];
        let header_list_bg_color_arr = ["#ffffff", "#253142"];
        let header_list_font_color_arr = ["#515A6E", "#5CA0D5"];
        let header_list_activefont_color_arr = ["#ffffff", "#ffffff"];
        let header_list_activebg_color_arr = [
          "#7da5e8",
          "rgba(97, 124, 165, 0.16)",
        ];
        let link_b_color_arr = ["#f4f6f9", "#1a222e"];
        let chartBak_arr = ["#303748", "#617ca5"];
        let dash_tabsActive_color_arr = ["#2d8cf0", "#1E9AFF"];
        let message_bg_arr = ["rgba(255,255,255)", "rgba(44, 37, 48, .6)"];
        let message_border_success_arr = ["#fff", "#19be6b"];
        let message_border_err_arr = ["#fff", "#ed3f13"];
        let message_border_warning_arr = ["#fff", "#f90"];
        let message_font_success_arr = ["##515a6e", "#19be6b"];
        let message_font_err_arr = ["##515a6e", "#ed3f13"];
        let message_font_warning_arr = ["##515a6e", "#f90"];
        let table_checkbox_border_color_arr = ["#e8eaec", "#04478E"];//table表格选择框的颜色
        let primary_bcg_arr = ["#2d8cf0", "transparent"];
        let primary_border_arr = ["#2d8cf0", "#2d8cf0"];
        let primary_font_arr = ["#fff", "#2d8cf0"];
        let warning_bcg_arr = ["#ffad33", "transparent"];
        let warning_border_arr = ["#ffad33", "#ffad33"];
        let warning_font_arr = ["#fff", "#ffad33"];
        let success_bcg_arr = ["#47cb89", "transparent"];
        let success_border_arr = ["#47cb89", "#47cb89"];
        let success_font_arr = ["#fff", "#47cb89"];
        let error_bcg_arr = ["#f16643", "transparent"];
        let error_border_arr = ["#f16643", "#f16643"];
        let error_font_arr = ["#fff", "#f16643"];
        let ivu_tag_border_arr = ["#e8eaec", "#06324D"];
        let ivu_select_arrow_arr = ["#808695", "#05EEFF"];
        let font_color_arr = ["#303748", "#fff"];
        let modal_header_color_arr = ["#fff", "#D1E4FF"];
        let org_btn_bg_color_arr = ["#57c5f7", "#465b7a"];
        let contentBox_bgcolor_arr = ["#fff", "#060D15"];
        let fonthandle_color_arr = ["#2d8cf0", "#1E9AFF"];
        let input_placeholder_color_arr = ["#c5c8ce", "#5CA0D5"];
        let topo_menu_region_border_arr = ["#E4E7ED", "#22465D"];
        let path_fiber_g6_color_arr = ["#17233D", "#ccc"];
        let path_tooltip_g6_color_arr = ["#17233D", "#fff"];
        let path_tooltip_g6_bg_color_arr = ["#fff", "#16436b"];

        let btnConfirm_bg1_color_arr=["#fff","#049dec"];//消息弹框背景颜色
        let btnConfirm_bg2_color_arr=["#fff","#05ebeb"];//消息弹框背景颜色
        let btnConfirm_font_color_arr=["#0290FD","#042039"];//消息弹框字体颜色
        let btnConfirm_border_color_arr=["#0290FD","none"];//消息探针边框颜色

        // 弹窗的标题背景色
        let modal_header_b_color_arr = [
          "#ffffff",
          "#08101A"
        ];
        // 弹窗的标题字体颜色
        let modal_header_font_color_arr = [
          "#17233D",
          "#31F0FE"

        ];
        let modal_title_name_color_arr = ["#17233D","#00FFEE"];
        let modal_title_name_color_two_arr = ["#515A6E","#fff"];
        // 弹窗的取消按钮颜色设置
        let modal_footer_but_b_color_arr = ["#515A6E", "#5CA0D5"];
        let modal_footer_but_cancel_background_color_arr = ["#FFFFFF", "#061824"];
        let modal_footer_but_cancel_background_border_color_arr = ["#C0C4CC", "#015197"];

        // 弹窗的确定按钮颜色设置
        let modal_footer_butok_b_color_arr = ["#FFFFFF", "#060d15"];
        let modal_footer_but_ok_background_color_arr = ["#0290FD", "linear-gradient(357deg, #049dec 0%, #05ebeb 100%)"];
        let modal_footer_but_ok_background_border_color_arr = ["none", "#05ebeb"];

        // 
        // 弹窗的内容背景色设置
        let modal_background_color_arr = ["#FFFFFF", "linear-gradient(to right, #08101A, #08101A), linear-gradient(80deg, #015197 37%, #31F0FE 46%, #015197 65%, #015197 100%);"];
        let modal_icon_close_btn_color_arr = ["#808695", "00ffee"];
        let modal_background_color_arr2 = ["#DCDFE6", "#31F0FE"];
        // 
        let confirmmodal_footer_cancel_b_color_arr = ["transparent", "#061824"];
        let border_color_arr = ["#C0C4CC", "#06324D"];//查询条件是复选框的边框颜色
        let icon_tree_arr = ["#e8eaec", "#04478E"];
        let th_b_color_arr = ["#F5F7FA", "#032A4D"];//table表格标题头的那行的背景颜色
        let th_b_stripe_color_arr = ["#F8F8F9", "#011B2D"];//table表格间隔单行的背景颜色
        let td_b_color_arr = ["#FFFFFF", "#060D15"];//table表格间隔双行的背景颜色
        let th_font_color_arr = ["#515A6E", "#FFFFFF"];//table表格标题文字颜色
        let td_font_color_arr = ["#17233D", "#FFFFFF"];//table表格里文字颜色
        let thd_border_color_arr = ["#e8eaec", "#2e3c51"];
        let table_content_column_link_color_arr = ["#0290FD", "#05eeff"];//table表格里文字是连接的颜色

        let tdd_border_color_arr = ["#e8eaec", "#222d3d"];
        let table_sort_color_arr = ["#c5c8ce", "#465b7a"];
        let table_sorton_color_arr = ["#2d8cf0", "#8fd4ff"];
        let input_font_color_arr = ["#515A6E", "#fff"];//列表分页：末页俩个字的字体颜色
        let input_border_color_arr = ["#dcdee2", "#2e3c51"];
        let input_b_color_arr = ["#ffffff", "#061824"];
        let input_checkbox_b_color_arr = ["#ffffff", "#1A222E"];
        // 下拉框选中的背景色颜色设置
        let selectdrop_b_color_arr = ["#E5F4FF", "#06324D"];
        // 下拉框选中的字体颜色设置
        let selectdrop_font_color_arr = ["#0290FD", "#00FFEE"];
        let confirmModal_font_color_arr = ["#17233d", "#5CA0D5"];
        let dash_h_b_color_arr = ["#f4f6f9", "#060D15"];
        let dash_h_f_color_arr = ["#303748", "#00FFEE"];
        let dash_b_b_color_arr = ["#ffffff", "#1a222e"];
        let dash_border_color_arr = ["#dddddd", "#202A39"];
        let page_b_color_arr = ["#0290FD", "#06324D"];
        let page_font_color_arr = ["#FFFFFF", "#ffffff"];
        let page_border_color_arr = ["#0290FD", "#05eeff"];
        let alarm_modal_border_color_arr = ["#d7d7d7", "#35455d"];
        let time_disabled_b_color_arr = ["#f7f7f7", "#06324D"];
        let time_hover_b_color_arr = ["#e1f0fe", "#465b7a"];
        let reset_but_b_color_arr = ["#999", "#465b7a"];
        let topo_model_head_color_arr = ["#1e97fa", "#465b7a"];
        let topo_checkbox_b_color_arr = ["#fff", "#1A222E"];
        let oid_l_m_b_color_arr = ["#fff", "#1F2A38"];
        // 质量报告颜色变量
        let menu_active_color_arr = ["#F0FAFF", "#253142"];
        let left_menu_color_arr = ["#ffffff", "#1A222E"];
        let report_bg_color_arr = ["#F4F6F9", "#0D151F"];
        let wifi_boder_color_arr = ["#dcdfe6", "#465B7A"];
        let wifi_tip_color_arr = ["#303748", "#808695"];
        let wifi_tip_content_color_arr = ["#808695", "#DFF1FF"];
        let wifi_device_color_arr = ["#303748", "#fff"];
        let monitorItem_color_arr2 = ["#17233D", "#fff"];
        let monitorItem_color_arr = ["#17233D", "#D3E4FF"];
        let monitorItem_bg_color_arr = ["#ffffff", "#060D15"];
        let monitorItem_border_color_arr = ["#DCDFE6", "#013164"];


        // 表格冻结 右边的列的颜色
        let tableFixedRight_arr = ["#ffffff" , "#011B2D"];
        let table_header_bg_color_arr = ["#ffffff" , "#032A4D"];
        let table_td_th_border_color_arr = ["#E4E7ED" , "#032A4D"];
        let table_td_bg_color_arr = ["#fff" , "#061824"];
        let page_total_color_arr=["#515A6E" , "#fff"];
        let select_dropdown_border_color_arr = ["#DCDFE6" , "#04478e"];
        let date_picker_cells_cell_range_bg_color_arr=["#F5F6FA","#06324d"];
        let sidebar_bg_color_arr = ['#F5F6FA','none']
        let body_table_content_border_color_arr=["#DCDFE6","#06254b"];
        let spectendency_chart_bg_color_arr=["#ffffff","#06121c"];
        // 拨测任务查询路径表格的颜色
        let task_path_list_table_color_arr=["#17233D","#ffffff"];
        let border_none_color_arr = ["#DCDFE6" , "#015197"];
        // switch 样式
        let switch_close_color_arr = ["#fff" , "#032a4d"];
        let switch_close_font_color_arr = ["#808695" , "#5ca0d5"];
        let switch_close_after_bg_color_arr = ["#E4E7ED" , "#015197"];
        let switch_close_border_color_arr = ["#DCDFE6" , "#015197"];
        // form ivu_tag 背景色样式
        let ivu_tag_bg_color_arr = ["#ffffff", "#06324D"];
        // checkbox 样式
         let from_checkbox_color_arr = ["#ffffff", "#06324D"];
         // 表单不可用的样式
         let from_input_disabled_color_arr = ["#515A6E", "#ccc"];
         // 选中的颜色
         let from_checkbox_check_color_arr = ["#0290FD", "#05eeff"];
         // 选中的边框颜色
         let from_checkbox_check_border_color_arr = ["#0290FD", "#06324D"];
         // 
         let from_time_picker_cells_cell_hover_color_arr = ["#E5F4FF","#06324d"];
         let from_time_picker_cells_cell_select_bg_color_arr = ["#E5F4FF","#06324d"];
         let from_time_picker_cells_cell_border_color_arr = ["#ffffff","#06324d"];
         // 高级更多按钮样式颜色
         let from_condition_btn_font_color_arr = ["#0290FD","#05eeff"];
         let from_condition_btn_border_color_arr = ["#0290FD","#05eeff"];
         let from_condition_btn_bg_color_arr = ["#fff","#06324d"];
         // 默认值配置
         let default_value_remarks_color_arr = ["#515A6E","#5CA0D5"];
         let default_value_font_color_arr = ["#515A6E","#fff"];
         let default_value_group_append_bg_color_arr = ["#ffffff","#061824"];
         let default_value_group_append_border_color_arr = ["#DCDFE6","#015197"];
         let default_value_menu_color_arr = ["#97C5E9","#5ca0d5"];//质量报告-详情-左边标题-悬浮选中的字体颜色
         let default_value_menu_select_font_color_arr = ["#64BCFF","#05eeff"];//质量报告-详情-左边标题-选中以后的字体颜色
         let default_value_menu_select_bg_color_arr = ["#64BCFF","#16436b"];
         // 默认值重置按钮样式
         let default_value_result_btn_bg_color_arr = ["#ffffff","#061824"];
         let default_value_result_btn_color_arr = ["#515A6E","#5ca0d5"];
         let default_value_result_btn_border_color_arr = ["#C0C4CC","#015197"];
         // oid 颜色配置
        let oid_left_bg_color_arr = ["#ffffff" , "#011B2D"];
        // radio 颜色配置
        let form_radio_border_color_arr = ["#C0C4CC" , "#04478e"];
        let form_radio_check_color_arr = ["#0290FD" , "#05eeff"];
        // 
        let spectendency_choose_btn_color_arr=["#ffffff","#05eeff"];
        let spectendency_choose_btn_border_color_arr=["#0290FD","#05eeff"];
        let spectendency_choose_btn_bg_color_arr=["#0290FD","#06324d"];
        
        // 选择按钮样式
        let choose_data_btn_font_color_arr=["#FFFFFF","#05EEFF"];
        let choose_data_btn_bg_color_arr=["#0290FD","#06324D"];
        let choose_data_btn_border_color_arr=["#0290FD","#05EEFF"];
        let choose_data_btn_bg_color_arr2=["#fff","#06324D"];
        let choose_data_btn_font_color_arr2=["#0290FD","#05EEFF"];

        // 删除按钮颜色
        let delete_data_btn_font_color_arr=["#FE5C5C","#FE5C5C"];
        let delete_data_btn_bg_color_arr=["#fff","#1F1414"];
        let delete_data_btn_border_color_arr=["#FE5C5C","#FE5C5C"];

        // 选择文件上传按钮
        let choose_file_btn_font_color_arr=["#FFFFFF","#060D15"];
        let choose_file_btn_bg_color_arr=["#0290FD","linear-gradient(357deg, #049DEC 0%, #05EBEB 100%)"];
        let choose_file_btn_border_color_arr=["#0290FD","#2d8cf0"];

        // 升级平台跳转颜色
        let upgrade_btn_font_color_arr=["#0290FC","#05EEFF"];
        let upgrade_btn_bg_color_arr=["#fff","#06324D"];
        let upgrade_btn_border_color_arr=["#0290FC","#05EEFF"];
        let upgrade_lisenceInfo_color_arr=["#17233D","#05EEFF"];

        // 下载模板文字按钮颜色
        let download_template_color_arr=["#0290FD","#05EBEB"];

        // 故障清单查看故障详情弹窗的文本颜色
        let fault_phenomenon_content_color_arr=["#515A6E","#05eeff"];

        // 拨测页面详情页面切换菜单颜色
        let btnChange_color_arr= ["#515A6E","#5CA0D5"];
        let btnChange_border_color_arr= ["#C0C4CC","#02B8FD"];
        let btnChange_active_color_arr= ["#fff","#060D15"];
        let btnChange_active_bg_color_arr= ["#0290FD","#4e7bff"];
        let btnChange_active_border_color_arr= ["#C0C4CC","#4e7bff"];

        // 选择显示字段颜色配置
        let field_font_color_arr = ["#515A6E" , "#fff"]
        let field_table_td_bg_color_arr = ["#E5F4FF" , "#06324D"]
        let field_table_border_color_arr = ["#DCDFE6 " , "#015197"]
        let field_table_bg_color_arr = ["#fff" , "#061824"]
        let field_table_td_hover_color_arr = ["#E5F4FF" , "#061824"]

        let field_sort_btn_hover_font_color_arr = ["#0290FD" , "#05EEFF"]
        let field_sort_btn_hover_border_color_arr = ["#0290FD" , "#0290FD"]
        let field_sort_btn_border_color_arr = ["#C1C4CC" , "#04478E"]
        let field_sort_btn_bg_color_arr = ["#fff" , "#071B31"]
        let field_sort_btn_font_color_arr = ["#808695" , "#1FA2FF"]


        // tab 样式
        let tab_nav_font_color_arr = ["#808695" , "#5CA0D5"]
        let tab_nav_margin_left_arr = ["0px" , "-20px"]
        let tab_nav_active_font_color_arr = ["#0290FD" , "#060D15"]
        let tab_nav_active_font_weight_arr = ["200" , "700"]

        // 下一步按钮样式
        let next_btn_font_color_arr = ["#FFFFFF","#060d15"];
        let next_btn_border_color_arr = ["#0290FD","#57a3f3"];
        let next_btn_bg_color_arr = ["#0290FD","#049dec"];
        let next_btn_bg_color_2_arr = ["#0290FD","#05ebeb"];


        // wifi 详情页面颜色
        let wifi_name_font_color_arr=["#0290FD" , "#00FFEE"];
        let wifi_name_border_color_arr=["#0290FD" , "#00FFEE"];
        let wifi_name_bg_color_arr=["#fff" , "#22465D"];

        let wifi_point_speed_item_process_color_arr=["#DCDFE6" , "#51636F"];

        // wifi 内网颜色设置
        let wifi_internal_bg_color_arr=["#fff" , "#061824"];
        let wifi_internal_border_color_arr=["#DCDFE6" , "#22465D"];

        let wifi_internal_title_font_color_arr=["#0290FD" , "#02B8FD"];
        let wifi_internal_title_border_color_arr=["#0290FD" , "#22465D"];
        let wifi_internal_title_bg_color_arr=["#fff" , "#061824"];

        // 帮助手册样式
        let help_font_color_arr = ["#17233D","#5ca0d5"];
        let help_font_hover_color_arr = ["#0290FD","#05eeff"];
        let help_font_hover_bg_color_arr = ["#E5F4FF","#253142"];


        // 仪表盘趋势图 tooltip 样式
        let dashboard_tooltip_bg_color_arr= ["#fff","rgba(18,55,127,.8)"];
        let dashboard_tooltip_color_arr= ["#5A5F6C","#d1e4ff"];
        let dashboard_tooltip_border_color_arr= ["#E4E7ED","rgba(18,55,127,.8)"];
        let ivu_radio_wrapper_checked_bg_arr = ['#E5F4FF','#00466e'];
        let ivu_radio_wrapper_checked_font_arr=['#0290FD','#2d8cf0'];

         let license_upgrade_btn_border_color_arr=["#0290FC","transparent"];
         let license_upgrade_btn_font_color_arr=["#0290FD","#02b8fd"];
         let license_contentBox_bgcolor_arr = ["#F5F6FA ", "#060D15"];
         let license_text_color_arr = ["#808695 ", "#5CA0D5"];
         let table_border_bottom_color_arr = ["#E4E7ED","#06254b"];
        //  let choose_data_btn_border_color_arr=["#0290FD","#05eeff"];





        let body_b_color = "#f4f6f9",
          font_color = "#303748",
          border_color = "#dddddd",
          icon_tree = "#dddddd",
          th_b_color = "#f1f6fe",
          th_b_stripe_color = "#e8eaec";
        let td_b_color = "#ffffff",
          th_font_color = "#303748",
          td_font_color = "#303748",
          thd_border_color = "#e8eaec";
        let table_sort_color = "#c5c8ce",
          table_sorton_color = "#2d8cf0",
          input_font_color = "#303748",
          input_b_color = "#ffffff";
        let selectdrop_b_color = "#ffffff",
          selectdrop_font_color = "#303748",
          confirmModal_font_color = "#17233d";
        let dash_h_b_color = "#f4f6f9",
          dash_h_f_color = "#303748",
          dash_b_b_color = "#ffffff",
          dash_border_color = "#dddddd";
        let input_placeholder_color = "#c5c8ce",
          org_btn_bg_color = "#57c5f7",
          table_checkbox_border_color = "#e8eaec";
        let primary_bcg = "#2d8cf0",
          primary_border = "#2d8cf0",
          primary_font = "#fff";
        let warning_bcg = "#ffad33",
          warning_border = "#ffad33",
          warning_font = "#fff";
        let success_bcg = "#47cb89",
          success_border = "#47cb89",
          success_font = "#fff";
        let error_bcg = "#f16643",
          error_border = "#f16643",
          error_font = "#f16643";
        let ivu_tag_border = "#e8eaec",
          ivu_select_arrow = "#808695",
          modal_header_b_color =
            "radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9))";
        let modal_b_color = "#ffffff",
          modal_input_border_color = "#dcdee2",
          contentBox_bgcolor = "none",
          confirmmodal_footer_cancel_b_color = "transparent";
        let header_b_color = "#1252c8",
          header_font_color = "#ffffff",
          header_active_bg_color = "rgba(125, 165, 232, 0.16)";
        let header_list_bg_color = "#ffffff",
          header_list_font_color = "#303748",
          header_list_activefont_color = "#ffffff";
        let header_list_activebg_color = "#7da5e8",
          message_bg = "rgba(255,255,255,.9)",
          chartBak = "#303748",
          link_b_color = "#f4f6f9";
        let input_checkbox_b_color = "#2d8cf0",
          message_success_color,
          message_err_color,
          message_warning_color = "#fff";
        let message_font_success,
          message_font_err,
          message_font_warning = "#515a6e",
          page_b_color = "#fff",
          modal_header_color = "#fff";
        let alarm_modal_border_color = "#d7d7d7",
          time_disabled_b_color = "#f7f7f7",
          time_hover_b_color = "#e1f0fe";
        let reset_but_b_color = "#999",
          topo_model_head_color = "#1e97fa",
          topo_checkbox_b_color = "#fff",
          oid_l_m_b_color = "#fff";
        let menu_active_color = "#F0FAFF";
        let left_menu_color = "#ffffff";
        let report_bg_color = "#F4F6F9";
        let wifi_boder_color = "#dcdfe6";
        let wifi_tip_color = "#303748";
        let wifi_device_color = "#303748";
        let wifi_tip_content_color = "808695";
        let monitorItem_color = "#e8eaec";
        let monitorItem_bg_color = "#ffffff";
        let search_lable_font_color = "#fff";
        let table_content_column_link_color = "#05eeff";
        let button_background_color = "#05eeff";
        let button_background_two_color = "#049DEC";
        let search_check_box_background_color = "#02b8fd";
        let search_check_box_font_color="#060d15";
        let search_check_box_bg_tabStyle_color = "#061824";
        let search_check_box_font_tabStyle_color="#fff";
        let search_check_box_font_tabStyle_two_color = "#5ca0d5";
        let search_ivu_select_input_boder_1_color = "#060d15";
        let search_ivu_select_input_boder_2_color = "#015197";
        let search_ivu_select_input_boder_3_color = "#31f0fe";
        let search_ivu_select_input_boder_4_color = "#ffffff";
        let button_networdFault_del_bg_color = "#8a5505";
        let button_networdFault_del_bg_2_color="#ffffff";
        let button_networdFault_del_bg_3_color="#b77006";
        let button_networdFault_del_bg_4_color="#fea31b";
        let networdFault_type_border_color="#04478e";
        let networdFault_type_border_normal_color="#04478e";
        let networdFault_type_border_normal_font_color="#5ca0d5";
        let networdFault_duration_border_select_color = "#05eeff";
        let query_org_select_color="#05eeff";
        let button_background_three_color = "#151106";
        let reset_export_del_button_font_color = "#05EEFF";
        let del_button_font_color="#fea31b";
        let reset_export_button_border_1_color="#015197";
        let reset_export_button_border_2_color="#31F0FE";
        let reset_export_button_border_3_color="#015197";
        let scrollbar_thumb_bg_color="#015197";
        let scrollbar_track_bg_color="#06324D";
        let scrollbar_thumb_shadow_color="rgba(6, 50, 77, 1)";
        let scrollbar_track_shadow_color="rgba(0, 0, 0, 0.2)";
        let query_btn_hover_bg_1_color="#049DEC";
        let query_btn_hover_bg_2_color="#05EBEB";
        let query_btn_active_bg_1_color="rgba(4, 157, 236, 0.8)";
        let query_btn_active_bg_2_color="rgba(5, 235, 235, 0.8)";
        let more_btn_font_color = "#02b8fd";
        let more_btn_background_image_1_color="#015197";
        let more_btn_background_image_2_color="#31f0fe";
        let more_btn_ivu_dropdown_item_hover_bg_color="#06324d";
        let more_btn_ivu_dropdown_item_hover_font_color="#00ffee";
        let more_btn_ivu_dropdown_item_font_color="#dff1ff";
        let ivu_tooltip_inner_bg_color="rgba(22, 67, 107, 1)";
        let ivu_tooltip_inner_font_color="#ffffff";
        let ivu_select_visible_border_color="#05eeff";
        let inintEchart_dataZoom_filler_color="rgba(2, 29, 54, 1)";
        let inintEchart_dataZoom_border_color="rgba(22, 67, 107, 1)";
        let ivu_picker_confirm_bg_color="#06324d";
        let ivu_picker_confirm_border_color="#06324d";
        let ivu_picker_confirm_font_color="#c5c8ce";
        let ivu_picker_confirm_hover_bg_color="#06324d";
        let ivu_picker_confirm_hover_font_color="#57a3f3";
        let ivu_picker_confirm_select_font_color="#c5c8ce";
        let rpapth_a_link_lTitle_color="#05eeff";
        let quality_report_left_menu_bg_color="#011B2D";
        let quality_report_left_ivu_menu_font_color="#fff";
        let defaultmanager_left_menu_bg_color="#16436b";
        let defaultmanager_left_menu_font_color="#05eeff";
        let dashboard_button_hover_font_color="#ffffff";
        let dashboard_component_title_font_color="#00ffee";
        let dashboard_bigscale_bg1_color="#049DEC";
        let dashboard_bigscale_bg2_color="#05EBEB";
        let dashboard_userDefined_btn_border1_color="#015197";
        let dashboard_userDefined_btn_border2_color="#31F0FE";
        let pathtopo_tabs_box_select_font_color="#05eeff";
        let pathtopo_tabs_box_select_bg_color="#fff";
        let pathtopo_tabs_box_select_border_color="#045b8e";
        let pathtopo_logo_box_bg_color="#061824";
        let pathtopo_logo_box_font_color="#5ca0d5";
        let pathtopo_fiber_routine_model_font_color="#5ca0d5";
        let pathtopo_fiber_routine_model_active_font_color="#05eeff";
        let pathtopo_icon_box_border_color="#045b8e";
        let pathtopo_icon_special_suspend_border_color="#05eeff";
        let pathtopo_my_tooltip_bg_color="#06324d";
        let pathtopo_my_tooltip_left_box_drag_step_bg_color="#22465d";
        let pathtopo_my_tooltip_left_box_step_bg_color="#05eeff";
        let pathtopo_my_tooltip_left_box_step_circle_bg_color="#06324d";
        let pathtopo_my_tooltip_left_box_num_tip_bg_color="#015197";
        let comm_back_icon_bg_color="#31f0fe";
        let pathtopo_list_select_bg_color="rgba(0, 0, 0, 0)";
        let data_sync_primary_bg="#2d8cf0";

        let ivu_radio_inner_color = "#ffffff";
        let specialmonitor_checkedItem_bg_color = "#06324d";
        let probetaskModal_item_line_bg_color="#06324D";

        let pie_count_color = pie_count_color_arr[val];
       
        let pie_legend_normal_color = pie_legend_normal_color_arr[val];
        let pie_legend_deterioration_color = pie_legend_deterioration_color_arr[val];
        let pie_legend_break_color = pie_legend_break_color_arr[val];
        let pie_legend_suspend_color = pie_legend_suspend_color_arr[val];
        let table_text_click_color = table_text_click_color_arr[val];
        let ivu_radio_wrapper_checked_bg = ivu_radio_wrapper_checked_bg_arr[val]
        let ivu_radio_wrapper_checked_font = ivu_radio_wrapper_checked_font_arr[val]
      
        body_b_color = body_b_color_arr[val];
       
        search_lable_font_color = search_lable_font_color_arr[val];
        table_content_column_link_color = table_content_column_link_color_arr[val];
        button_background_color = button_background_color_arr[val];
        button_background_two_color = button_background_color_arr_two[val];
        button_background_three_color = button_background_color_arr_three[val];
        search_check_box_background_color = search_check_box_background_color_arr[val];
        search_check_box_font_color = search_check_box_font_color_arr[val];
        search_check_box_bg_tabStyle_color = search_check_box_bg_tabStyle_color_arr[val];
        search_check_box_font_tabStyle_color=search_check_box_font_tabStyle_color_arr[val];
        search_check_box_font_tabStyle_two_color=search_check_box_font_tabStyle_two_color_arr[val];
        search_ivu_select_input_boder_1_color = search_ivu_select_input_boder_color_1_arr[val];
        search_ivu_select_input_boder_2_color = search_ivu_select_input_boder_color_2_arr[val];
        search_ivu_select_input_boder_3_color = search_ivu_select_input_boder_color_3_arr[val];
        search_ivu_select_input_boder_4_color = search_ivu_select_input_boder_color_4_arr[val];
        button_networdFault_del_bg_color = button_networdFault_del_bg_color_arr[val];
        button_networdFault_del_bg_2_color = button_networdFault_del_bg_2_color_arr[val];
        button_networdFault_del_bg_3_color_arr = button_networdFault_del_bg_3_color_arr[val];
        button_networdFault_del_bg_4_color_arr = button_networdFault_del_bg_4_color_arr[val];
        networdFault_type_border_color = networdFault_type_border_color_arr[val];
        networdFault_type_border_normal_color = networdFault_type_border_normal_color_arr[val];
        networdFault_type_border_normal_font_color = networdFault_type_border_normal_font_color_arr[val];
        networdFault_duration_border_select_color = networdFault_duration_border_select_color_arr[val];
        query_org_select_color= query_org_select_color_arr[val];
        reset_export_del_button_font_color = reset_export_del_button_font_color_arr[val];
        del_button_font_color = del_button_font_color_arr[val];
        reset_export_button_border_1_color = reset_export_button_border_1_color_arr[val];
        reset_export_button_border_2_color = reset_export_button_border_2_color_arr[val];
        reset_export_button_border_3_color = reset_export_button_border_3_color_arr[val];
        scrollbar_thumb_bg_color = scrollbar_thumb_bg_color_arr[val];
        scrollbar_track_bg_color = scrollbar_track_bg_color_arr[val];
        scrollbar_thumb_shadow_color = scrollbar_thumb_shadow_color_arr[val];
        scrollbar_track_shadow_color = scrollbar_track_shadow_color_arr[val];
        query_btn_hover_bg_1_color = query_btn_hover_bg_1_color_arr[val];
        query_btn_hover_bg_2_color = query_btn_hover_bg_2_color_arr[val];
        query_btn_active_bg_1_color = query_btn_active_bg_1_color_arr[val];
        query_btn_active_bg_2_color = query_btn_active_bg_2_color_arr[val];
        more_btn_font_color = more_btn_font_color_arr[val];
        more_btn_background_image_1_color = more_btn_background_image_1_color_arr[val];
        more_btn_background_image_2_color = more_btn_background_image_2_color_arr[val];
        more_btn_ivu_dropdown_item_hover_bg_color =more_btn_ivu_dropdown_item_hover_bg_color_arr[val];
        more_btn_ivu_dropdown_item_hover_font_color=more_btn_ivu_dropdown_item_hover_font_color_arr[val];
        more_btn_ivu_dropdown_item_font_color=more_btn_ivu_dropdown_item_font_color_arr[val];
        ivu_tooltip_inner_bg_color = ivu_tooltip_inner_bg_color_arr[val];
        
        ivu_tooltip_inner_font_color=ivu_tooltip_inner_font_color_arr[val];
        ivu_select_visible_border_color = ivu_select_visible_border_color_arr[val];
        inintEchart_dataZoom_filler_color = inintEchart_dataZoom_filler_color_arr[val];
        inintEchart_dataZoom_border_color = inintEchart_dataZoom_border_color_arr[val];
        ivu_picker_confirm_bg_color = ivu_picker_confirm_bg_color_arr[val];
        ivu_picker_confirm_border_color = ivu_picker_confirm_border_color_arr[val];
        ivu_picker_confirm_font_color = ivu_picker_confirm_font_color_arr[val];
        ivu_picker_confirm_hover_bg_color = ivu_picker_confirm_hover_bg_color_arr[val];
        ivu_picker_confirm_hover_font_color = ivu_picker_confirm_hover_font_color_arr[val];
        ivu_picker_confirm_select_font_color=ivu_picker_confirm_select_font_color_arr[val];
        rpapth_a_link_lTitle_color = rpapth_a_link_lTitle_color_arr[val];
        quality_report_left_menu_bg_color = quality_report_left_menu_bg_color_arr[val];
        quality_report_left_ivu_menu_font_color = quality_report_left_ivu_menu_font_color_arr[val];
        defaultmanager_left_menu_bg_color = defaultmanager_left_menu_bg_color_arr[val];
        defaultmanager_left_menu_font_color = defaultmanager_left_menu_font_color_arr[val];
        dashboard_button_hover_font_color = dashboard_button_hover_font_color_arr[val];
        dashboard_component_title_font_color = dashboard_component_title_font_color_arr[val];
        dashboard_bigscale_bg1_color = dashboard_bigscale_bg1_color_arr[val];
        dashboard_bigscale_bg2_color = dashboard_bigscale_bg2_color_arr[val];
        dashboard_userDefined_btn_border1_color = dashboard_userDefined_btn_border1_color_arr[val];
        dashboard_userDefined_btn_border2_color = dashboard_userDefined_btn_border2_color_arr[val];
        pathtopo_tabs_box_select_font_color=pathtopo_tabs_box_select_font_color_arr[val];
        pathtopo_tabs_box_select_bg_color = pathtopo_tabs_box_select_bg_color_arr[val];
        pathtopo_tabs_box_select_border_color = pathtopo_tabs_box_select_border_color_arr[val];
        pathtopo_logo_box_bg_color = pathtopo_logo_box_bg_color_arr[val];
        pathtopo_logo_box_font_color = pathtopo_logo_box_font_color_arr[val];
        pathtopo_fiber_routine_model_font_color = pathtopo_fiber_routine_model_font_color_arr[val];
        pathtopo_fiber_routine_model_active_font_color = pathtopo_fiber_routine_model_active_font_color_arr[val];
        pathtopo_icon_box_border_color = pathtopo_icon_box_border_color_arr[val];
        pathtopo_icon_special_suspend_border_color = pathtopo_icon_special_suspend_border_color_arr[val];
        pathtopo_my_tooltip_bg_color = pathtopo_my_tooltip_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_drag_step_bg_color = pathtopo_my_tooltip_left_box_drag_step_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_step_bg_color = pathtopo_my_tooltip_left_box_step_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_step_circle_bg_color = pathtopo_my_tooltip_left_box_step_circle_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_num_tip_bg_color = pathtopo_my_tooltip_left_box_num_tip_bg_color_arr[val];
        comm_back_icon_bg_color = comm_back_icon_bg_color_arr[val];
        pathtopo_list_select_bg_color = pathtopo_list_select_bg_color_arr[val];
        data_sync_primary_bg = data_sync_primary_bg_arr[val];
        font_color = font_color_arr[val];
        border_color = border_color_arr[val];
        icon_tree = icon_tree_arr[val];
        th_b_color = th_b_color_arr[val];
        th_b_stripe_color = th_b_stripe_color_arr[val];

        td_b_color = td_b_color_arr[val];
        th_font_color = th_font_color_arr[val];
        td_font_color = td_font_color_arr[val];
        thd_border_color = thd_border_color_arr[val];

        table_sort_color = table_sort_color_arr[val];
        table_sorton_color = table_sorton_color_arr[val];
        input_font_color = input_font_color_arr[val];
        input_b_color = input_b_color_arr[val];
        input_checkbox_b_color = input_checkbox_b_color_arr[val];
        selectdrop_b_color = selectdrop_b_color_arr[val];
        selectdrop_font_color = selectdrop_font_color_arr[val];
        confirmModal_font_color = confirmModal_font_color_arr[val];

        dash_h_b_color = dash_h_b_color_arr[val];
        dash_h_f_color = dash_h_f_color_arr[val];
        dash_b_b_color = dash_b_b_color_arr[val];
        dash_border_color = dash_border_color_arr[val];
        input_placeholder_color = input_placeholder_color_arr[val];
        org_btn_bg_color = org_btn_bg_color_arr[val];
        table_checkbox_border_color = table_checkbox_border_color_arr[val];

        primary_bcg = primary_bcg_arr[val];
        primary_border = primary_border_arr[val];
        primary_font = primary_font_arr[val];
        warning_bcg = warning_bcg_arr[val];
        warning_border = warning_border_arr[val];
        warning_font = warning_font_arr[val];
        success_bcg = success_bcg_arr[val];
        success_border = success_border_arr[val];
        success_font = success_font_arr[val];
        error_bcg = error_bcg_arr[val];
        error_border = error_border_arr[val];
        error_font = error_font_arr[val];
        ivu_tag_border = ivu_tag_border_arr[val];
        ivu_select_arrow = ivu_select_arrow_arr[val];
        modal_header_b_color = modal_header_b_color_arr[val];
        modal_b_color = modal_b_color_arr[val];
        modal_input_border_color = modal_input_border_color_arr[val];
        contentBox_bgcolor = contentBox_bgcolor_arr[val];
        header_b_color = header_b_color_arr[val];
        header_font_color = header_font_color_arr[val];
        header_active_bg_color = header_active_bg_color_arr[val];

        header_list_bg_color = header_list_bg_color_arr[val];
        header_list_font_color = header_list_font_color_arr[val];
        header_list_activefont_color = header_list_activefont_color_arr[val];
        header_list_activebg_color = header_list_activebg_color_arr[val];
        message_bg = message_bg_arr[val];
        message_success_color = message_border_success_arr[val];
        message_err_color = message_border_err_arr[val];
        message_warning_color = message_border_warning_arr[val];
        message_font_success = message_font_success_arr[val];
        message_font_err = message_font_err_arr[val];
        message_font_warning = message_font_warning_arr[val];
        chartBak = chartBak_arr[val];
        link_b_color = link_b_color_arr[val];
        page_b_color = page_b_color_arr[val];
        modal_header_color = modal_header_color_arr[val];
        alarm_modal_border_color = alarm_modal_border_color_arr[val];
        time_disabled_b_color = time_disabled_b_color_arr[val];
        time_hover_b_color = time_hover_b_color_arr[val];
        confirmmodal_footer_cancel_b_color =
          confirmmodal_footer_cancel_b_color_arr[val];
        reset_but_b_color = reset_but_b_color_arr[val];
        topo_model_head_color = topo_model_head_color_arr[val];
        topo_checkbox_b_color = topo_checkbox_b_color_arr[val];
        oid_l_m_b_color = oid_l_m_b_color_arr[val];
        menu_active_color = menu_active_color_arr[val];
        left_menu_color = left_menu_color_arr[val];
        report_bg_color = report_bg_color_arr[val];

        wifi_boder_color = wifi_boder_color_arr[val];
        wifi_tip_color = wifi_tip_color_arr[val];
        wifi_tip_content_color = wifi_tip_content_color_arr[val];
        wifi_device_color = wifi_device_color_arr[val];
        monitorItem_color = monitorItem_color_arr[val];
        monitorItem_bg_color = monitorItem_bg_color_arr[val];
        ivu_radio_inner_color = ivu_radio_inner_color_arr[val];
        specialmonitor_checkedItem_bg_color = specialmonitor_checkedItem_bg_color_arr[val];
        probetaskModal_item_line_bg_color = probetaskModal_item_line_bg_color_arr[val];

        let modal_title_name_color = modal_title_name_color_arr[val];
        let modal_title_name_color_two = modal_title_name_color_two_arr[val];
        // 取消按钮颜色
        let modal_footer_but_cancel_color = modal_footer_but_b_color_arr[val];
        let modal_footer_but_cancel_background_color = modal_footer_but_cancel_background_color_arr[val];
        let modal_footer_but_cancel_background_border_color = modal_footer_but_cancel_background_border_color_arr[val];
        
        // 弹窗的确定按钮颜色设置
        let modal_footer_butok_b_color = modal_footer_butok_b_color_arr[val];
        let modal_footer_but_ok_background_color = modal_footer_but_ok_background_color_arr[val];
        let modal_footer_but_ok_background_border_color = modal_footer_but_ok_background_border_color_arr[val];

        let modal_background_color = modal_background_color_arr[val];
        let modal_icon_close_btn_color = modal_icon_close_btn_color_arr[val];

        //
         let table_fixed_right_color = tableFixedRight_arr[val];
         let table_header_bg_color = table_header_bg_color_arr[val];
         let table_td_th_border_color = table_td_th_border_color_arr[val];
        let monitorItem_border_color = monitorItem_border_color_arr[val];
        let monitorItem_color2 = monitorItem_color_arr2[val];

        let page_font_color = page_font_color_arr[val];
        let page_border_color = page_border_color_arr[val];
        let page_total_color = page_total_color_arr[val];
         let modal_header_font_color = modal_header_font_color_arr[val];
         let select_dropdown_border_color = select_dropdown_border_color_arr[val];
         let date_picker_cells_cell_range_bg_color = date_picker_cells_cell_range_bg_color_arr[val];
         let body_conent_b_color = body_conent_b_color_arr[val];
         let body_table_content_border_color = body_table_content_border_color_arr[val];
         let spectendency_chart_bg_color = spectendency_chart_bg_color_arr[val];
         let task_path_list_table_color = task_path_list_table_color_arr[val];
         let border_none_color = border_none_color_arr[val];
        let switch_close_color = switch_close_color_arr[val];
        let switch_close_font_color = switch_close_font_color_arr[val];
        let switch_close_after_bg_color = switch_close_after_bg_color_arr[val];
        let switch_close_border_color = switch_close_border_color_arr[val];
        let from_input_disabled_color = from_input_disabled_color_arr[val];
        let ivu_tag_bg_color = ivu_tag_bg_color_arr[val];
        let from_checkbox_check_color = from_checkbox_check_color_arr[val];
        let from_checkbox_check_border_color = from_checkbox_check_border_color_arr[val];
        let from_time_picker_cells_cell_hover_color = from_time_picker_cells_cell_hover_color_arr[val];
        let from_time_picker_cells_cell_border_color = from_time_picker_cells_cell_border_color_arr[val];
        let from_time_picker_cells_cell_select_bg_color = from_time_picker_cells_cell_select_bg_color_arr[val];
        let default_value_remarks_color = default_value_remarks_color_arr[val];
        let default_value_group_append_bg_color = default_value_group_append_bg_color_arr[val];
        let default_value_group_append_border_color = default_value_group_append_border_color_arr[val];
        let default_value_menu_color = default_value_menu_color_arr[val];
        let default_value_menu_select_font_color = default_value_menu_select_font_color_arr[val];
        let default_value_menu_select_bg_color = default_value_menu_select_bg_color_arr[val];

            let default_value_result_btn_bg_color = default_value_result_btn_bg_color_arr[val];
         let default_value_result_btn_color =default_value_result_btn_color_arr[val];
         let default_value_result_btn_border_color = default_value_result_btn_border_color_arr[val];
         let oid_left_bg_color = oid_left_bg_color_arr[val];
          let form_radio_border_color =form_radio_border_color_arr[val];
          let form_radio_check_color = form_radio_check_color_arr[val];
          let table_td_bg_color = table_td_bg_color_arr[val];
          let default_value_font_color = default_value_font_color_arr[val];

          let spectendency_choose_btn_color = spectendency_choose_btn_color_arr[val];
          let spectendency_choose_btn_border_color = spectendency_choose_btn_border_color_arr[val];
          let spectendency_choose_btn_bg_color = spectendency_choose_btn_bg_color_arr[val];


          let choose_data_btn_font_color = choose_data_btn_font_color_arr[val];
          let choose_data_btn_bg_color = choose_data_btn_bg_color_arr[val];
          let choose_data_btn_border_color = choose_data_btn_border_color_arr[val];
          let choose_data_btn_bg_color2 = choose_data_btn_bg_color_arr2[val];
          let choose_data_btn_font_color2 = choose_data_btn_font_color_arr2[val];

          let delete_data_btn_font_color = delete_data_btn_font_color_arr[val];
          let delete_data_btn_bg_color = delete_data_btn_bg_color_arr[val];
          let delete_data_btn_border_color = delete_data_btn_border_color_arr[val];


          let choose_file_btn_font_color = choose_file_btn_font_color_arr[val];
          let choose_file_btn_bg_color = choose_file_btn_bg_color_arr[val];
          let choose_file_btn_border_color = choose_file_btn_border_color_arr[val];

          let upgrade_btn_font_color = upgrade_btn_font_color_arr[val];
          let upgrade_btn_bg_color = upgrade_btn_bg_color_arr[val];
          let upgrade_btn_border_color = upgrade_btn_border_color_arr[val];
          let upgrade_lisenceInfo_color = upgrade_lisenceInfo_color_arr[val];

          let download_template_color = download_template_color_arr[val];

          let fault_phenomenon_content_color = fault_phenomenon_content_color_arr[val];

          let modal_background_color2 = modal_background_color_arr2[val];





          let btnChange_color = btnChange_color_arr[val];
          let btnChange_border_color = btnChange_border_color_arr[val];
          let btnChange_active_color = btnChange_active_color_arr[val];
          let btnChange_active_bg_color = btnChange_active_bg_color_arr[val];
          let btnChange_active_border_color = btnChange_active_border_color_arr[val];



          let field_font_color = field_font_color_arr[val];
          let field_table_td_bg_color = field_table_td_bg_color_arr[val];
          let field_table_border_color = field_table_border_color_arr[val];
          let field_table_bg_color = field_table_bg_color_arr[val];
          let field_table_td_hover_color = field_table_td_hover_color_arr[val];

          let picker_panel_content_after_color = picker_panel_content_after_color_arr[val];
          let pathtopo_legend_splitline_border_color = pathtopo_legend_splitline_border_color_arr[val];

          let dashboard_component_loading_bg_color = dashboard_component_loading_bg_color_arr[val];
          let field_sort_btn_hover_font_color = field_sort_btn_hover_font_color_arr[val];
          let field_sort_btn_hover_border_color = field_sort_btn_hover_border_color_arr[val];
          let field_sort_btn_border_color = field_sort_btn_border_color_arr[val];
          let field_sort_btn_bg_color = field_sort_btn_bg_color_arr[val];
          let field_sort_btn_font_color = field_sort_btn_font_color_arr[val];

          let testspeed_skinPrimary_bg_color = testspeed_skinPrimary_bg_color_arr[val];
          let testspeed_skinPrimary_font_color = testspeed_skinPrimary_font_color_arr[val];
          let testspeed_restStartBut_bg_color = testspeed_restStartBut_bg_color_arr[val];
          let tab_nav_font_color = tab_nav_font_color_arr[val];
          let tab_nav_margin_left = tab_nav_margin_left_arr[val];
          let tab_nav_active_font_color = tab_nav_active_font_color_arr[val];
          let tab_nav_active_font_weight = tab_nav_active_font_weight_arr[val];


          let next_btn_font_color = next_btn_font_color_arr[val];
          let next_btn_border_color = next_btn_border_color_arr[val];
          let next_btn_bg_color = next_btn_bg_color_arr[val];
          let next_btn_bg_color_2 = next_btn_bg_color_2_arr[val];


          let from_condition_btn_font_color = from_condition_btn_font_color_arr[val];
          let from_condition_btn_border_color = from_condition_btn_border_color_arr[val];
          let from_condition_btn_bg_color = from_condition_btn_bg_color_arr[val];


          let wifi_name_font_color = wifi_name_font_color_arr[val];
          let wifi_name_border_color = wifi_name_border_color_arr[val];
          let wifi_name_bg_color = wifi_name_bg_color_arr[val];
          let wifi_point_speed_item_process_color = wifi_point_speed_item_process_color_arr[val];


          let wifi_internal_bg_color = wifi_internal_bg_color_arr[val];
          let wifi_internal_border_color = wifi_internal_border_color_arr[val];
          let wifi_internal_title_font_color = wifi_internal_title_font_color_arr[val];
          let wifi_internal_title_border_color = wifi_internal_title_border_color_arr[val];
          let wifi_internal_title_bg_color = wifi_internal_title_bg_color_arr[val];


                  // 帮助手册样式
        let help_font_color = help_font_color_arr[val];
        let help_font_hover_color = help_font_hover_color_arr[val];
        let help_font_hover_bg_color = help_font_hover_bg_color_arr[val];

        // 仪表盘趋势图样式
        let dashboard_tooltip_bg_color = dashboard_tooltip_bg_color_arr[val];
        let dashboard_tooltip_color = dashboard_tooltip_color_arr[val];
        let dashboard_tooltip_border_color = dashboard_tooltip_border_color_arr[val];
        let topo_menu_region_border = topo_menu_region_border_arr[val];
        let path_fiber_g6_color = path_fiber_g6_color_arr[val];
        let path_tooltip_g6_color = path_tooltip_g6_color_arr[val];
        let path_tooltip_g6_bg_color = path_tooltip_g6_bg_color_arr[val];


         let license_upgrade_btn_border_color = license_upgrade_btn_border_color_arr[val];
         let license_upgrade_btn_font_color = license_upgrade_btn_font_color_arr[val];
         let license_contentBox_bgcolor = license_contentBox_bgcolor_arr[val];
         let license_text_color = license_text_color_arr[val];
         let sidebar_bg_color = sidebar_bg_color_arr[val];
         let sidebar_b_color_mr = sidebar_b_color_mr_arr[val];
         let  point_name_bg =  point_name_bg_arr[val];

         let btnConfirm_bg1_color = btnConfirm_bg1_color_arr[val];
         let btnConfirm_bg2_color = btnConfirm_bg2_color_arr[val];
         let btnConfirm_font_color = btnConfirm_font_color_arr[val];
         let btnConfirm_border_color = btnConfirm_border_color_arr[val];
         let table_border_bottom_color = table_border_bottom_color_arr[val];

        // 获取根
        let root = document.documentElement;

        // root.style.setProperty('--body_b_color', body_b_color);
        window.skin = {
       
          "--topo_menu_region_border": topo_menu_region_border,
          "--body_b_color": body_b_color,
          "--font_color": font_color,
          "--border_color": border_color,
          "--icon_tree": icon_tree,
          "--th_b_color": th_b_color,
          "--th_b_stripe_color": th_b_stripe_color,
          "--td_b_color": td_b_color,
          "--th_font_color": th_font_color,
          "--td_font_color": td_font_color,
          "--thd_border_color": thd_border_color,
          "--table_sort_color": table_sort_color,
          "--table_sorton_color": table_sorton_color,
          "--input_font_color": input_font_color,
          "--input_b_color": input_b_color,
          "--input_checkbox_b_color": input_checkbox_b_color,
          "--selectdrop_b_color": selectdrop_b_color,
          "--selectdrop_font_color": selectdrop_font_color,
          "--select_dropdown_border_color": select_dropdown_border_color,
          "--confirmModal_font_color": confirmModal_font_color,

          "--dash_h_b_color": dash_h_b_color,
          "--dash_h_f_color": dash_h_f_color,
          "--dash_b_b_color": dash_b_b_color,
          "--dash_border_color": dash_border_color,
          "--input_placeholder_color": input_placeholder_color,
          "--org_btn_bg_color": org_btn_bg_color,
          "--table_checkbox_border_color": table_checkbox_border_color,

          "--primary_bcg": primary_bcg,
          "--primary_border": primary_border,
          "--primary_font": primary_font,
          "--warning_bcg": warning_bcg,
          "--warning_border": warning_border,
          "--warning_font": warning_font,
          "--success_bcg": success_bcg,
          "--success_border": success_border,
          "--success_font": success_font,
          "--error_bcg": error_bcg,
          "--error_border": error_border,
          "--error_font": error_font,
          "--message_success_color": message_success_color,
          "--message_err_color": message_err_color,
          "--message_warning_color": message_warning_color,
          "--message_font_success": message_font_success,
          "--message_font_err": message_font_err,
          "--message_font_warning": message_font_warning,

          "--ivu_tag_border": ivu_tag_border,
          "--ivu_select_arrow": ivu_select_arrow,
          "--modal_header_b_color": modal_header_b_color,
          "--modal_footer_but_b_color": modal_footer_but_cancel_color,
          "--modal_footer_butok_b_color": modal_footer_butok_b_color,
          "--modal_footer_but_ok_background_color": modal_footer_but_ok_background_color,
          "--modal_footer_but_ok_background_border_color": modal_footer_but_ok_background_border_color,
          "--modal_b_color": modal_b_color,
          "--modal_input_border_color": modal_input_border_color,
          "--modal_footer_but_cancel_color": modal_footer_but_cancel_color,
          "--modal_footer_but_cancel_background_color": modal_footer_but_cancel_background_color,
          "--modal_footer_but_cancel_background_border_color": modal_footer_but_cancel_background_border_color,
          "--modal_icon_close_btn_color": modal_icon_close_btn_color,
          "--contentBox_bgcolor": contentBox_bgcolor,
          "--header_b_color": header_b_color,
          "--header_font_color": header_font_color,
          "--header_active_bg_color": header_active_bg_color,
          "--header_list_bg_color": header_list_bg_color,
          "--header_list_font_color": header_list_font_color,
          "--header_list_activefont_color": header_list_activefont_color,
          "--header_list_activebg_color": header_list_activebg_color,
          "--message_bg": message_bg,
          "--chartBak": chartBak,
          "--link_b_color": link_b_color,
          "--page_b_color": page_b_color,
          "--page_font_color": page_font_color,
          "--page_border_color": page_border_color,
          "--modal_header_color": modal_header_color,
          "--alarm_modal_border_color": alarm_modal_border_color,
          "--time_disabled_b_color": time_disabled_b_color,
          "--time_hover_b_color": time_hover_b_color,
          "--confirmmodal_footer_cancel_b_color":confirmmodal_footer_cancel_b_color,
          "--reset_but_b_color": reset_but_b_color,
          "--topo_model_head_color": topo_model_head_color,
          "--topo_checkbox_b_color": topo_checkbox_b_color,
          "--oid_l_m_b_color": oid_l_m_b_color,
          "--menu_active_color": menu_active_color,
          "--left_menu_color": left_menu_color,
          "--report_bg_color": report_bg_color,
          "--wifi_boder_color": wifi_boder_color,
          "--wifi_tip_color": wifi_tip_color,
          "--wifi_device_color": wifi_device_color,
          "--wifi_tip_content_color": wifi_tip_content_color,
          "--monitorItem_color": monitorItem_color,
          "--monitorItem_bg_color": monitorItem_bg_color,
          "--search_lable_font_color": search_lable_font_color,
          "--table_content_column_link_color":table_content_column_link_color,
          "--button_background_color":button_background_color,
          "--search_check_box_background_color":search_check_box_background_color,
          "--search_check_box_font_color":search_check_box_font_color,
          "--button_background_two_color":button_background_two_color,
          "--search_check_box_bg_tabStyle_color":search_check_box_bg_tabStyle_color,
          "--search_check_box_font_tabStyle_color":search_check_box_font_tabStyle_color,
          "--search_check_box_font_tabStyle_two_color":search_check_box_font_tabStyle_two_color,
          "--search_ivu_select_input_boder_1_color":search_ivu_select_input_boder_1_color,
          "--search_ivu_select_input_boder_2_color":search_ivu_select_input_boder_2_color,
          "--search_ivu_select_input_boder_3_color":search_ivu_select_input_boder_3_color,
          "--search_ivu_select_input_boder_4_color":search_ivu_select_input_boder_4_color,
          "--button_networdFault_del_bg_color":button_networdFault_del_bg_color,
          "--button_networdFault_del_bg_2_color":button_networdFault_del_bg_2_color,
          "--button_networdFault_del_bg_3_color":button_networdFault_del_bg_3_color,
          "--button_networdFault_del_bg_4_color":button_networdFault_del_bg_4_color,
          "--networdFault_type_border_color":networdFault_type_border_color,
          "--networdFault_type_border_normal_color":networdFault_type_border_normal_color,
          "--networdFault_type_border_normal_font_color":networdFault_type_border_normal_font_color,
          "--networdFault_duration_border_select_color":networdFault_duration_border_select_color,
          "--query_org_select_color":query_org_select_color,
          "--button_background_three_color":button_background_three_color,
          "--reset_export_del_button_font_color":reset_export_del_button_font_color,
          "--del_button_font_color":del_button_font_color,
          "--reset_export_button_border_1_color":reset_export_button_border_1_color,
          "--reset_export_button_border_2_color":reset_export_button_border_2_color,
          "--reset_export_button_border_3_color":reset_export_button_border_3_color,
          "--scrollbar_thumb_bg_color":scrollbar_thumb_bg_color,
          "--scrollbar_track_bg_color":scrollbar_track_bg_color,
          "--scrollbar_thumb_shadow_color":scrollbar_thumb_shadow_color,
          "--scrollbar_track_shadow_color":scrollbar_track_shadow_color,
          "--query_btn_hover_bg_1_color":query_btn_hover_bg_1_color,
          "--query_btn_hover_bg_2_color":query_btn_hover_bg_2_color,
          "--query_btn_active_bg_1_color":query_btn_active_bg_1_color,
          "--query_btn_active_bg_2_color":query_btn_active_bg_2_color,
          "--more_btn_font_color":more_btn_font_color,
          "--more_btn_background_image_1_color":more_btn_background_image_1_color,
          "--more_btn_background_image_2_color":more_btn_background_image_2_color,
          "--more_btn_ivu_dropdown_item_hover_bg_color":more_btn_ivu_dropdown_item_hover_bg_color,
          "--more_btn_ivu_dropdown_item_hover_font_color":more_btn_ivu_dropdown_item_hover_font_color,
          "--more_btn_ivu_dropdown_item_font_color":more_btn_ivu_dropdown_item_font_color,
          "--ivu_tooltip_inner_bg_color":ivu_tooltip_inner_bg_color,
          "--table_text_click_color":table_text_click_color,
          "--ivu_tooltip_inner_font_color":ivu_tooltip_inner_font_color,
          "--ivu_select_visible_border_color":ivu_select_visible_border_color,
          "--inintEchart_dataZoom_filler_color":inintEchart_dataZoom_filler_color,
          "--inintEchart_dataZoom_border_color":inintEchart_dataZoom_border_color,
          "--ivu_picker_confirm_bg_color":ivu_picker_confirm_bg_color,
          "--ivu_picker_confirm_border_color":ivu_picker_confirm_border_color,
          "--ivu_picker_confirm_font_color":ivu_picker_confirm_font_color,
          "--ivu_picker_confirm_hover_bg_color":ivu_picker_confirm_hover_bg_color,
          "--ivu_picker_confirm_hover_font_color":ivu_picker_confirm_hover_font_color,
          "--ivu_picker_confirm_select_font_color":ivu_picker_confirm_select_font_color,
          "--rpapth_a_link_lTitle_color":rpapth_a_link_lTitle_color,
          "--quality_report_left_menu_bg_color":quality_report_left_menu_bg_color,
          "--quality_report_left_ivu_menu_font_color":quality_report_left_ivu_menu_font_color,
          "--defaultmanager_left_menu_bg_color":defaultmanager_left_menu_bg_color,
          "--defaultmanager_left_menu_font_color":defaultmanager_left_menu_font_color,
          "--dashboard_button_hover_font_color":dashboard_button_hover_font_color,
          "--dashboard_component_title_font_color":dashboard_component_title_font_color,
          "--dashboard_bigscale_bg1_color":dashboard_bigscale_bg1_color,
          "--dashboard_bigscale_bg2_color":dashboard_bigscale_bg2_color,
          "--dashboard_userDefined_btn_border1_color":dashboard_userDefined_btn_border1_color,
          "--dashboard_userDefined_btn_border2_color":dashboard_userDefined_btn_border2_color,
          "--pathtopo_tabs_box_select_font_color":pathtopo_tabs_box_select_font_color,
          "--pathtopo_tabs_box_select_bg_color":pathtopo_tabs_box_select_bg_color,
          "--pathtopo_tabs_box_select_border_color":pathtopo_tabs_box_select_border_color,
          "--pathtopo_logo_box_bg_color":pathtopo_logo_box_bg_color,
          "--pathtopo_logo_box_font_color":pathtopo_logo_box_font_color,
          "--pathtopo_fiber_routine_model_font_color":pathtopo_fiber_routine_model_font_color,
          "--pathtopo_fiber_routine_model_active_font_color":pathtopo_fiber_routine_model_active_font_color,
          "--pathtopo_icon_box_border_color":pathtopo_icon_box_border_color,
          "--pathtopo_icon_special_suspend_border_color":pathtopo_icon_special_suspend_border_color,
          "--pathtopo_my_tooltip_bg_color":pathtopo_my_tooltip_bg_color,
          "--pathtopo_my_tooltip_left_box_drag_step_bg_color":pathtopo_my_tooltip_left_box_drag_step_bg_color,
          "--pathtopo_my_tooltip_left_box_step_bg_color":pathtopo_my_tooltip_left_box_step_bg_color,
          "--pathtopo_my_tooltip_left_box_step_circle_bg_color":pathtopo_my_tooltip_left_box_step_circle_bg_color,
          "--pathtopo_my_tooltip_left_box_num_tip_bg_color":pathtopo_my_tooltip_left_box_num_tip_bg_color,
          "--comm_back_icon_bg_color":comm_back_icon_bg_color,
          "--pathtopo_list_select_bg_color":pathtopo_list_select_bg_color,
          "--data_sync_primary_bg":data_sync_primary_bg,
          "--comm_strong": this.$t("comm_strong"),
          "--comm_medium": this.$t("comm_medium"),
          "--comm_weak": this.$t("comm_weak"),

          // zxq ----
          "--table_fixed_right_color":table_fixed_right_color,
          "--table_td_th_border_color":table_td_th_border_color,
          "--modal_background_color":modal_background_color,
          "--modal_header_font_color":modal_header_font_color,
          "--table_header_bg_color":table_header_bg_color,
          "--date_picker_cells_cell_range_bg_color":date_picker_cells_cell_range_bg_color,
          "--page_total_color":page_total_color,
          "--modal_title_name_color":modal_title_name_color,
          "--modal_title_name_color_two":modal_title_name_color_two,
          "--body_conent_b_color":body_conent_b_color,
          "--body_table_content_border_color":body_table_content_border_color,
          "--spectendency_chart_bg_color":spectendency_chart_bg_color,
          "--task_path_list_table_color":task_path_list_table_color,
          "--border_none_color":border_none_color,
          "--switch_close_color":switch_close_color,
          "--switch_close_font_color":switch_close_font_color,
          "--switch_close_after_bg_color":switch_close_after_bg_color,
          "--switch_close_border_color":switch_close_border_color,
          "--ivu_tag_bg_color":ivu_tag_bg_color,
          "--from_checkbox_check_color":from_checkbox_check_color,
          "--from_checkbox_check_border_color":from_checkbox_check_border_color,
          "--from_time_picker_cells_cell_hover_color":from_time_picker_cells_cell_hover_color,
          "--from_time_picker_cells_cell_select_bg_color":from_time_picker_cells_cell_select_bg_color,
          "--from_time_picker_cells_cell_border_color":from_time_picker_cells_cell_border_color,
          "--from_input_disabled_color":from_input_disabled_color,
          "--default_value_remarks_color":default_value_remarks_color,
          "--default_value_group_append_bg_color":default_value_group_append_bg_color,
          "--default_value_group_append_border_color":default_value_group_append_border_color,
          "--default_value_menu_color":default_value_menu_color,
          "--default_value_menu_select_font_color":default_value_menu_select_font_color,
          "--default_value_menu_select_bg_color":default_value_menu_select_bg_color,
          "--default_value_result_btn_bg_color":default_value_result_btn_bg_color,
          "--default_value_result_btn_color":default_value_result_btn_color,
          "--default_value_result_btn_border_color":default_value_result_btn_border_color,
          "--default_value_font_color":default_value_font_color,
          "--oid_left_bg_color":oid_left_bg_color,
          "--form_radio_check_color":form_radio_check_color,
          "--form_radio_border_color":form_radio_border_color,
          "--table_td_bg_color":table_td_bg_color,

          "--spectendency_choose_btn_color":spectendency_choose_btn_color,
          "--spectendency_choose_btn_border_color":spectendency_choose_btn_border_color,
          "--spectendency_choose_btn_bg_color":spectendency_choose_btn_bg_color,

          "--choose_data_btn_font_color":choose_data_btn_font_color,
          "--choose_data_btn_bg_color":choose_data_btn_bg_color,
          "--choose_data_btn_border_color":choose_data_btn_border_color,
          "--choose_data_btn_bg_color2":choose_data_btn_bg_color2,
          "--choose_data_btn_font_color2":choose_data_btn_font_color2,


          "--delete_data_btn_font_color":delete_data_btn_font_color,
          "--delete_data_btn_bg_color":delete_data_btn_bg_color,
          "--delete_data_btn_border_color":delete_data_btn_border_color,


          "--choose_file_btn_font_color":choose_file_btn_font_color,
          "--choose_file_btn_bg_color":choose_file_btn_bg_color,
          "--choose_file_btn_border_color":choose_file_btn_border_color,


          "--upgrade_btn_font_color":upgrade_btn_font_color,
          "--upgrade_btn_bg_color":upgrade_btn_bg_color,
          "--upgrade_btn_border_color":upgrade_btn_border_color,
          "--upgrade_lisenceInfo_color":upgrade_lisenceInfo_color,

          "--download_template_color":download_template_color,
          "--fault_phenomenon_content_color":fault_phenomenon_content_color,

          "--btnChange_color":btnChange_color,
          "--btnChange_border_color":btnChange_border_color,
          "--btnChange_active_color":btnChange_active_color,
          "--btnChange_active_bg_color":btnChange_active_bg_color,
          "--btnChange_active_border_color":btnChange_active_border_color,


          "--field_font_color":field_font_color,
          "--field_table_td_bg_color":field_table_td_bg_color,
          "--field_table_border_color":field_table_border_color,
          "--field_table_bg_color":field_table_bg_color,
          "--field_table_td_hover_color":field_table_td_hover_color,


          "--field_sort_btn_hover_font_color":field_sort_btn_hover_font_color,
          "--field_sort_btn_hover_border_color":field_sort_btn_hover_border_color,
          "--field_sort_btn_border_color":field_sort_btn_border_color,
          "--field_sort_btn_bg_color":field_sort_btn_bg_color,
          "--field_sort_btn_font_color":field_sort_btn_font_color,
          "--modal_background_color2":modal_background_color2,
          "--testspeed_skinPrimary_bg_color":testspeed_skinPrimary_bg_color,
          "--testspeed_skinPrimary_font_color":testspeed_skinPrimary_font_color,
          "--testspeed_restStartBut_bg_color":testspeed_restStartBut_bg_color,
          "--tab_nav_font_color":tab_nav_font_color,
          "--tab_nav_margin_left":tab_nav_margin_left,
          "--tab_nav_active_font_color":tab_nav_active_font_color,
          "--tab_nav_active_font_weight":tab_nav_active_font_weight,


          "--next_btn_font_color":next_btn_font_color,
          "--next_btn_border_color":next_btn_border_color,
          "--next_btn_bg_color":next_btn_bg_color,
          "--next_btn_bg_color_2":next_btn_bg_color_2,

          "--btnConfirm_bg1_color":btnConfirm_bg1_color,
          "--btnConfirm_bg2_color":btnConfirm_bg2_color,
          "--btnConfirm_font_color":btnConfirm_font_color,
          "--btnConfirm_border_color":btnConfirm_border_color,


          "--from_condition_btn_font_color":from_condition_btn_font_color,
          "--from_condition_btn_border_color":from_condition_btn_border_color,
          "--from_condition_btn_bg_color":from_condition_btn_bg_color,

          "--wifi_name_font_color":wifi_name_font_color,
          "--wifi_name_border_color":wifi_name_border_color,
          "--wifi_name_bg_color":wifi_name_bg_color,
          "--wifi_point_speed_item_process_color":wifi_point_speed_item_process_color,
          "--pathtopo_legend_splitline_border_color":pathtopo_legend_splitline_border_color,
          "--dashboard_component_loading_bg_color":dashboard_component_loading_bg_color,
          "--wifi_internal_bg_color":wifi_internal_bg_color,
          "--wifi_internal_border_color":wifi_internal_border_color,
          "--wifi_internal_title_font_color":wifi_internal_title_font_color,
          "--wifi_internal_title_border_color":wifi_internal_title_border_color,
          "--wifi_internal_title_bg_color":wifi_internal_title_bg_color,


          "--help_font_color":help_font_color,
          "--help_font_hover_color":help_font_hover_color,
          "--help_font_hover_bg_color":help_font_hover_bg_color,

          "--dashboard_tooltip_bg_color":dashboard_tooltip_bg_color,
          "--dashboard_tooltip_color":dashboard_tooltip_color,

          "--pie_legend_normal_color":pie_legend_normal_color,
          "--pie_legend_deterioration_color":pie_legend_deterioration_color,
          "--pie_legend_break_color":pie_legend_break_color,
          "--pie_legend_suspend_color":pie_legend_suspend_color,
          "--pie_count_color":pie_count_color,
          "--ivu_radio_wrapper_checked_bg":ivu_radio_wrapper_checked_bg,
          "--ivu_radio_wrapper_checked_font":ivu_radio_wrapper_checked_font,
          "--ivu_radio_inner_color":ivu_radio_inner_color,
          "--specialmonitor_checkedItem_bg_color":specialmonitor_checkedItem_bg_color,
          "--probetaskModal_item_line_bg_color":probetaskModal_item_line_bg_color,
          "--picker_panel_content_after_color":picker_panel_content_after_color,

          "--license_upgrade_btn_border_color":license_upgrade_btn_border_color,
          "--license_upgrade_btn_font_color":license_upgrade_btn_font_color,
          "--license_contentBox_bgcolor":license_contentBox_bgcolor,
          "--license_text_color":license_text_color,
          "--monitorItem_border_color":monitorItem_border_color,
          "--monitorItem_color2":monitorItem_color2,
          "--dashboard_tooltip_border_color":dashboard_tooltip_border_color,
          "--path_fiber_g6_color":path_fiber_g6_color,
          "--path_tooltip_g6_color":path_tooltip_g6_color,
          "--path_tooltip_g6_bg_color":path_tooltip_g6_bg_color,
          "--sidebar_bg_color":sidebar_bg_color,
          "--sidebar_bg_color_mr":sidebar_b_color_mr,
          "--point_name_bg_color":point_name_bg,
          "--table_border_bottom_color":table_border_bottom_color,
           // zxq -------
  };
    skinChange(window.skin);
  },
    },
    beforeDestroy() {
        // 移除 resize 事件监听
        window.removeEventListener('resize', this.handleResize);
      },
};
</script>
<style>
.sectionBox {
  height: 100vh !important;
}
</style>
<style scoped lang="less">
/deep/.ivu-table-cell {
  .ivu-tooltip {
    width: 100%;
  }
  .ivu-tooltip-rel {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
}

/deep/ .ivu-icon-ios-close-circle {
  // 添加具体的图标类名
  color: var(--reset_export_del_button_font_color, #05eeff) !important;
}
</style>
