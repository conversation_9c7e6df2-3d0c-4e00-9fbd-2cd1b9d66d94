<template>
  <!-- 流量tab -->
  <div :class="currentSkin == 1 ? 'flow-index' : 'flow-index light-index'">
    <!-- 头部搜索 -->
    <section>
      <!-- 流量分析 -->
      <div class="section-top">
        <Row class="fn_box">
          <!-- 设备名称 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{ width: lang == 'en' ? '120px' : '70px' }"
                >{{ $t("comm_Device_name") + $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.deviceId"
                  @on-change="updateInterfaceList"
                >
                  <Option
                    v-for="item in findDeviceList"
                    :value="item.deviceId"
                    :key="item.deviceId"
                    >{{ item.deviceName }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 端口 -->
          <Col span="6">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{ width: lang == 'en' ? '60px' : '80px' }"
              >
                {{ $t("comm_port1") + $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  clearable
                  v-model="query.interfaceId"
                  @on-change="changeInterface"
                  @on-clear="changeInterface"
                >
                  <Option
                    v-for="item in findDeviceInterfaceList"
                    :value="item.interfaceId"
                    :key="item.interfaceId"
                    >{{ item.interfaceName }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <!-- 时间段 -->
          <Col span="9">
            <div class="fn_item">
              <label
                class="fn_item_label"
                :style="{ width: lang == 'en' ? '110px' : '90px' }"
              >
                {{ $t("comm_time_period") + $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <DatePicker
                  format="yyyy-MM-dd HH:mm:ss"
                  type="daterange"
                  @on-change="dateChange"
                  :options="timeOptions"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="false"
                  :confirm="false"
                  style="width: 100%; max-width: 360px"
                >
                </DatePicker>
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <Tooltip :content="$t('common_query')">
              <Button class="jiaHao-btn" type="primary" @click="queryClick">
                <i class="iconfont icon-icon-query" />
              </Button>
            </Tooltip>
            <slot name="additional-filters"></slot>
          </div>
        </div>
      </div>
    </section>

    <!-- 头部搜索 -->
    <div>
      <Loading :loading="loading"></Loading>
      <!-- 中间趋势图 -->
      <div class="flow-index-center" id="flowEcharts"></div>
    </div>

    <!-- 中间趋势图 -->
    <!-- 底部表格 -->
    <div class="flow-index-bottom">
      <!-- <Loading :loading="loading"></Loading> -->
      <div class="table-title">
        <div
          class="table-title-item"
          v-for="(item, index) in totalFlow"
          :key="index"
        >
          {{ item }}
        </div>
      </div>
      <div class="data-box" v-if="flowAnalysisFlowSummaryVo">
        <!-- 入流速 -->
        <div class="table-in-flow">
          <div class="table-title-item" style="padding-left: 30px">
            {{ $t("dashboard_inlet_velocity") }}
          </div>
          <!-- 最大值 -->
          <div class="table-title-item">
            {{ flowAnalysisFlowSummaryVo.inflowMax || "--" }}
          </div>

          <!-- 最小值 -->
          <div class="table-title-item">
            {{ flowAnalysisFlowSummaryVo.inflowMin || "--" }}
          </div>
          <!-- 平均值 -->
          <div class="table-title-item">
            {{ flowAnalysisFlowSummaryVo.inflowAvg || "--" }}
          </div>
          <!-- 总和 -->
          <div class="table-title-item" v-if="isTotalShow">
            {{ flowAnalysisFlowSummaryVo.inflowTotal || "--" }}
          </div>
        </div>
        <!-- 出流速 -->
        <div class="table-in-flow">
          <div class="table-title-item" style="padding-left: 30px">
            {{ $t("dashboard_outflow_velocity") }}
          </div>

          <!-- 最大值 -->
          <div class="table-title-item">
            {{ flowAnalysisFlowSummaryVo.outflowMax || "--" }}
          </div>
          <!-- 最小值 -->
          <div class="table-title-item">
            {{ flowAnalysisFlowSummaryVo.outflowMin || "--" }}
          </div>
          <!-- 平均值 -->
          <div class="table-title-item">
            {{ flowAnalysisFlowSummaryVo.outflowAvg || "--" }}
          </div>
          <!-- 总和 -->
          <div class="table-title-item" v-if="isTotalShow">
            {{ flowAnalysisFlowSummaryVo.outflowTotal || "--" }}
          </div>
        </div>
        <!-- 入利用率 -->
        <div class="table-in-flow" v-if="isPortShow">
          <div class="table-title-item" style="padding-left: 30px">
            {{ $t("dashboard_inflow_rate") }}
          </div>

          <!-- 最大值 -->
          <div class="table-title-item">
            {{
              formatUtilizationRate(
                flowAnalysisFlowSummaryVo.inUtilizationRateMax
              )
            }}
          </div>
          <!-- 最小值 -->
          <div class="table-title-item">
            {{
              formatUtilizationRate(
                flowAnalysisFlowSummaryVo.inUtilizationRateMin
              )
            }}
          </div>
          <!-- 平均值 -->
          <div class="table-title-item">
            {{
              formatUtilizationRate(
                flowAnalysisFlowSummaryVo.inUtilizationRateAvg
              )
            }}
          </div>
        </div>
        <!-- 出利用率 -->
        <div class="table-in-flow" v-if="isPortShow">
          <div class="table-title-item" style="padding-left: 30px">
            {{ $t("dashboard_outflow_rate") }}
          </div>

          <!-- 最大值 -->
          <div class="table-title-item">
            {{
              formatUtilizationRate(
                flowAnalysisFlowSummaryVo.outUtilizationRateMax
              )
            }}
          </div>
          <!-- 最小值 -->
          <div class="table-title-item">
            {{
              formatUtilizationRate(
                flowAnalysisFlowSummaryVo.outUtilizationRateMin
              )
            }}
          </div>
          <!-- 平均值 -->
          <div class="table-title-item">
            {{
              formatUtilizationRate(
                flowAnalysisFlowSummaryVo.outUtilizationRateAvg
              )
            }}
          </div>
        </div>
      </div>
      <div class="no-data" v-else>
        <img :src="getImgUrl()" alt="" />
        <div class="emptyText">{{ $t("comm_no_data") }}</div>
      </div>
    </div>
    <!-- 底部表格 -->
  </div>
</template>

<script>
import echarts from 'echarts'
import mock from './mock'
import eConfig from "@/config/echart.config.js";
import echartFn from "@/common/mixins/echartFun";
import { mapState } from 'vuex'


export default {
   mixins: [echartFn],
   props: {
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    ...mapState('m_select', ['findDeviceList', 'findDeviceInterfaceList'])
  },
    data() {
        return {
        lastDaysDiff: 0,
        zoomLevel:1,
        zoomTime:false,
        currentSkin: sessionStorage.getItem('dark') || 1,
        isHandleZoom:false,
        lang: localStorage.getItem('locale'),
        startValue: '',
        endValue: '',
        flowEchart:null,
        loading:false,
        // 查询条件
        query:{
            deviceId:'',
            interfaceId:'',
            startTime:'',
            endTime:''

        },
        isPortShow:false,
        flowIntoSpeeds:[],
        flowOutSpeeds:[],
        intoUsages:[],
        outUsages:[],
        flowAnalysisFlowSummaryVo:{},

       
 

             timeRange: [
                    new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
                    "yyyy-MM-dd 00:00:00"
                    ),
                    new Date().format2("yyyy-MM-dd 23:59:59"),
                 ],
             timeOptions: {
              shortcuts: [
               
                {
                  text: this.$t('comm_today'),
                  value() {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime());
                    return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_yesterday'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24);
                    end.setTime(end.getTime() - 3600 * 1000 * 24);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_last_7'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_last_30'),
                  value() {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_curr_month'),
                  value() {
                    let nowDate = new Date();
                    let date = {
                      year: nowDate.getFullYear(),
                      month: nowDate.getMonth(),
                      date: nowDate.getDate(),
                    };
                    let end = new Date(date.year, date.month + 1, 0);
                    let start = new Date(date.year, date.month, 1);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                },
                {
                  text: this.$t('comm_preced_month'),
                  value() {
                    let date = new Date();
                    let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                    let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                    let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                    return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                  }
                }
              ]
            },
       
        tableTitle:['',this.$t('rphealthy_max'),this.$t('rphealthy_min'),this.$t('rphealthy_aver'),this.$t('rphealthy_total')],
        isTotalShow:false,
        totalFlow:[],
        lastData:{},
        isZoom:false
        }
       
       
    },
   
  
     methods: {
      queryClick(){
       const startDate = new Date(this.query.startTime);
      const endDate = new Date(this.query.endTime);
      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      this.lastDaysDiff = diffDays
      
      console.log(diffDays,'diffDays')

      // Check if difference is greater than 7 days
      if(diffDays > 7) {
        
        this.isHandleZoom = true
      }else {
        this.isHandleZoom = false
      }
        this.getFlowData()
      },
      formatUtilizationRate(value) {
        if (value === undefined || value === null || value === '') {
          return '--';
        }
        if(value <= 0.0001) {
          return '<0.0001%'

        }else {
          return value.toFixed(2) + '%';

        }
        
      },
      getImgUrl() {
        if(this.currentSkin==0){
            return require('../../../assets/dashboard/fileempty2.png')
        }else{
            return require('../../../assets/dashboard/fileempty.png')
        }
    },
      dateChange(val) {
        if (val && val.length === 2) {
        const [startDate, endDate] = val;
        
        // Format start time
        this.query.startTime = startDate;
        
        // Check if end time has hours/minutes/seconds
        const endDateTime = new Date(endDate);
        if (endDateTime.getHours() === 0 && 
            endDateTime.getMinutes() === 0 && 
            endDateTime.getSeconds() === 0) {
          // If no time specified, set to end of day
          endDateTime.setHours(23, 59, 59);
          this.query.endTime = endDateTime.format("yyyy-MM-dd HH:mm:ss");
          this.timeRange = [startDate, endDateTime];
        } else {
          // Keep original end time if it includes time component
          this.query.endTime = endDate;
          this.timeRange = val;
        }
  }

      
     
        
     
    },
      changeInterface() {
        this.$store.commit('m_select/setInterfaceId',this.query.interfaceId)

      },
       updateInterfaceList(){
        this.query.interfaceId = ''
        this.$store.dispatch('m_select/findDeviceInterfaceListSync',this.query.deviceId)
        this.$store.commit('m_select/setInterfaceId',this.query.interfaceId)
        this.$store.commit('m_select/setDeviceId',this.query.deviceId)
       },
       getLastHour() {
            // 修改为获取当天时间范围
            const end = new Date();
            const start = new Date();
            
            // 设置开始时间为当天 00:00:00
            start.setHours(0, 0, 0, 0);
            
            // 设置结束时间为当天 23:59:59
            end.setHours(23, 59, 59, 999);
            
            this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
            this.timeRange = [start, end];
        },
            async getFlowData(){
              this.loading = true
               this.zoomTime = false
                 if(!this.query.interfaceId || this.query.interfaceId === ''){
                    this.isTotalShow = true
                    this.totalFlow = this.tableTitle
                     this.isPortShow = false
                  }else{
                    this.isTotalShow = false
                    this.totalFlow = this.tableTitle.slice(0,4)
                  }
                 
              let currentStart = this.startValue;
              let currentEnd = this.endValue;
              try{
              const res =  await this.$http.PostJson('/flowanalysis/findFlowanalysisByDevIdAndInterfaceId',this.query)
                if(res.code == 1){
                  // debugger
                 
                  this.flowAnalysisFlowSummaryVo = res.data.flowAnalysisFlowSummaryVo
                  console.log(this.flowAnalysisFlowSummaryVo,111111111)
                  this.flowIntoSpeeds = res.data.flowIntoSpeeds || []
                  this.flowOutSpeeds = res.data.flowOutSpeeds || []
                  this.intoUsages = res.data.intoUsages 
                  this.outUsages = res.data.outUsages 
                   this.zoomLevel = res.data.level
                  
                   if(!this.zoomTime && res.data.flowIntoSpeeds ){
                    this.startValue = res.data.flowIntoSpeeds[0][0];
                    this.endValue = res.data.flowIntoSpeeds[res.data.flowIntoSpeeds.length - 1][0];

                   }else{
                    this.startValue = currentStart
                    this.endValue = currentEnd
                   }
                   
                    
                  if(this.intoUsages || this.outUsages){
                    this.isPortShow = true
                  }else {
                    this.isPortShow = false
                  }
                   this.initEcharts()
                  console.log(this.flowAnalysisFlowSummaryVo,111111111)
                  
                  
                 
                }else {
                  this.$Message.error(res.msg)
                } 
              }catch(err){
                // this.loading = false
                
              }finally{
                console.log(111)
                 this.loading = false
                
              }
              


            },
            initEcharts(){
                if(this.flowEchart){
                    this.flowEchart.dispose()
                    this.flowEchart = null
                }
                // debugger
                const flowId = document.getElementById('flowEcharts')
                console.log(flowId)
                 this.flowEchart = echarts.init(flowId);
                // let echarts = echarts.init(flowEchart)
                this.flowEchart.setOption(this.setOption())
               
                // this.flowEchart.on('dataZoom', this.handleZoomChange)
                this.handleZoomChange()
                 this.handleZoomOut()
                this.$nextTick(() => this.setZoomPosition());
               
            },
             setOption() {
              
                
            let optionArr = [
              {
                title: {
                  show: this.flowIntoSpeeds.length === 0 && this.flowOutSpeeds.length === 0,
                  text: this.$t('common_No_data'),
                  left: "center",
                  top: "90px",
                  textStyle: {
                    color: top.window.isdarkSkin == 1 ? "#465b7a" : "grey",
                    fontFamily: "serif",
                    fontWeigth: "400",
                    fontSize: 18
                  }
                },
                tooltip: this.setDelayLossTooltip(),
                axisPointer: {
                  type: "shadow",
                  link: {
                    xAxisIndex: "all"
                  }
                },
                grid: [
                  {
                    left: "6%",
                    top: "40px",
                    width: "92%",
                    height: "140px"
                  },
                
                ],
                legend: [
                
                  // 入流速 出流速
                  {
                  
                    right: "center",
                    icon: "roundRect",
                    gridIndex: 1,
                    itemWidth: 16,
                    itemHeight: 12,
                    textStyle: {
                      color: top.window.isdarkSkin == 1 ? "#FFFFFF" : "#484b56",
                      fontSize: 12,
                      fontFamily: "MicrosoftYaHei-Bold"
                    },
                   data: this.isPortShow 
                        ? [
                            this.$t('dashboard_inlet_velocity'), 
                            this.$t('dashboard_outflow_velocity'),
                            this.$t('dashboard_inflow_rate'),
                            this.$t('dashboard_outflow_rate')
                          ]
                        : [
                            this.$t('dashboard_inlet_velocity'), 
                            this.$t('dashboard_outflow_velocity')
                          ]
                  }
                ],
                xAxis: [
                
                  {
                
                    type: "time",
                    gridIndex: 0,
                    splitLine: {
                      lineStyle: {
                        type: "dotted",
                        color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2"
                      }
                    },
                    axisLabel: {
                      textStyle: {
                        color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                      }
                    },
                    axisLine: {
                      lineStyle: {
                        color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                        width: 1,
                      },
                    },
                  }
                ],
                yAxis: [
                  {
                  
                    name: this.$t('server_flow_rate'),
                    type: "value",
                    scale: true,
                    position: "left",
                    gridIndex: 0,
                    axisTick: {
                      show: false
                    },
                    axisLine: {
                      symbol: ["none", "arrow"],
                      symbolSize: [6, 10],
                      lineStyle: {
                        color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                        width: "1" //坐标线的宽度
                      }
                    },
                    splitLine: {
                      show: false,
                      lineStyle: {
                        type: "dashed",
                        color: top.window.isdarkSkin == 1 ? "#2A3840" : "#e3e7f2"
                      }
                    },
                    axisLabel: {
                      show: true,
                      formatter: value => {
                      
                        return this.flowSize(value, true, true);
                      }
                    }
                  },
                  {
                        name: this.$t('specquality_utilization'),
                        type: "value",
                        scale: true,
                        min:0,
                        max:100,
                        position: "right", 
                        show: this.isPortShow, // Only show when utilization rates exist
                        gridIndex: 0,
                        axisTick: {
                          show: false
                        },
                        axisLine: {
                          symbol: ["none", "arrow"],
                          symbolSize: [6, 10],
                          lineStyle: {
                            color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                            width: "1"
                          }
                        },
                        splitLine: {
                          show: false
                        },
                        axisLabel: {
                          show: true,
                          
                        }
                      }

                ],
                dataZoom: [
                  {
                    type: "inside",
                    xAxisIndex: [0],
                    // startValue: this.startValue == this.endValue ? '' : this.startValue,
                    // endValue: this.startValue == this.endValue ? '' : this.endValue
                    startValue: this.startValue,
                    endValue: this.endValue,
                    
                  },
                  {
                    type: "slider",
                    // left: "5%",
                    // right: "5%",
                    height: 20,
                    top: 220,
                    xAxisIndex: [0],
                    realtime: true,
                    startValue: this.startValue,
                    endValue: this.endValue,
                    // startValue: this.startValue == this.endValue ? '' : this.startValue,
                    // endValue: this.startValue == this.endValue ? '' : this.endValue,
                    fillerColor:eConfig.dataZoom.fillerColor[this.currentSkin],// "rgba(2, 29, 54, 1)",
                    borderColor: eConfig.dataZoom.borderColor[this.currentSkin], // "rgba(22, 67, 107, 1)",
                    
                    handleStyle: {
                      color: eConfig.dataZoom.handleStyle.color[this.currentSkin] // "rgba(2, 67, 107, 1)"
                    },
                    textStyle: {
                      color: eConfig.dataZoom.textStyle.color[this.currentSkin]  //top.window.isdarkSkin == 1 ? '#617ca5' : "#e3e7f2"
                    }
                  }
                ],
                series: [
                  // 入流速
             
                
                  {
                  
                    name: this.$t('dashboard_inlet_velocity'),
                    type: "line",
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 1,
                    // symbol: this.flowData.enter.length>1?"none":"none",
                    // xAxisIndex: 1,
                    yAxisIndex: 0, // 指定y轴
                    color: this.currentSkin == 1 ? '#00FFEE' : '#03B999',
                    data: this.flowIntoSpeeds,
                    itemStyle: {
                      normal: {
                        lineStyle: {
                          width: 1
                        }
                      }
                    },
                    areaStyle: {
                      color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: this.currentSkin == 1 ? "rgba(0,255,238, 0.6)" : "rgba(3,185,153, 0.6)" // "rgba(71,142,233, 0.8)"
                          },
                          {
                            offset: 0.8,
                            color: this.currentSkin == 1 ? "rgba(0,255,238, 0.38)" : "rgba(3,185,153, 0.38)"  
                          },
                          {
                            offset: 1,
                           color: this.currentSkin == 1 ? "rgba(0,255,238, 0)" : "rgba(3,185,153, 0)" 
                          }
                        ]
                      }
                    }
                  },
                  // 出流速
                  {
                  
                    name: this.$t('dashboard_outflow_velocity'),
                    type: "line",
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 1,
                    //symbol: this.flowData.issue.length>1?"none":"none",
                    // xAxisIndex: 1,
                    yAxisIndex: 0,
                    color: this.currentSkin == 1 ? '#0290FD' : '#0290FD',
                    data: this.flowOutSpeeds,
                    itemStyle: {
                      normal: {
                        lineStyle: {
                          width: 1
                        }
                      }
                    },
                    areaStyle: {
                      color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: "rgba(2,144,253, 0.6)" // "rgba(71,142,233, 0.8)"
                          },
                          {
                            offset: 0.8,
                            color: "rgba(2,144,253, 0.38)" // "rgba(71,142,233, 0.2)"
                          },
                          {
                            offset: 1,
                            color: "rgba(2,144,253, 0)" // "rgba(71,142,233, 0.2)"
                          }
                        ]
                      }
                    }
                  },
                  {
                      name: this.$t('dashboard_inflow_rate'),
                      type: "line",
                      smooth: true,
                      symbol: "circle", 
                      symbolSize: 1,
                      yAxisIndex: 1, // Use right y-axis
                      color: this.currentSkin == 1 ? '#9BFBAE' : '#AEBBFE',
                      data: this.intoUsages,
                      show: this.isPortShow,
                       areaStyle: {
                      color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color:this.currentSkin == 1 ? "rgba(155,251,174, 0.6)" : "rgba(174,187,254, 0.6)" // "rgba(71,142,233, 0.8)"
                          },
                          {
                            offset: 0.8,
                            color: this.currentSkin == 1 ? "rgba(155,251,174, 0.38)" : "rgba(174,187,254, 0.38)" // "rgba(71,142,233, 0.2)"
                          },
                          {
                            offset: 1,
                            color: this.currentSkin == 1 ? "rgba(155,251,174, 0)" : "rgba(174,187,254, 0)" // "rgba(71,142,233, 0.2)"
                          }
                        ]
                      }
                    }
                    },
                    {
                      name: this.$t('dashboard_outflow_rate'), 
                      type: "line",
                      smooth: true,
                      symbol: "circle",
                      symbolSize: 1,
                      yAxisIndex: 1, // Use right y-axis
                      color: this.currentSkin == 1 ? '#81C8FF' : '#9DD871',
                      data: this.outUsages,
                      show: this.isPortShow,
                       areaStyle: {
                      color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color:this.currentSkin == 1 ? "rgba(130,200,255, 0.6)" :  "rgba(157,216,113, 0.6)" // "rgba(71,142,233, 0.8)"
                          },
                          {
                            offset: 0.8,
                            color: this.currentSkin == 1 ? "rgba(130,200,255, 0.38)" :  "rgba(157,216,113, 0.38)" // "rgba(71,142,233, 0.2)"
                          },
                          {
                            offset: 1,
                            color: this.currentSkin == 1 ? "rgba(130,200,255, 0)" :  "rgba(157,216,113, 0)" // "rgba(71,142,233, 0.2)"
                          }
                        ]
                      }
                    }
                    }
                                  ]


                
              }
            ];
            return optionArr[0];
          

            
        },
     setDelayLossTooltip() {
            let _self = this;
            return {
                trigger: 'axis',
                backgroundColor:this.currentSkin ==1 ? 'rgba(18,55,127,.8)':"#FFFFFF",
                              textStyle: {
                    color: this.currentSkin == 1 ? '#ffffff' : '#515A6E'
                  },
                   extraCssText: this.currentSkin == 0 
                                ? 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16); text-align: left;' 
                                : 'text-align: left;',
                formatter: function(param) {
                    let result = param[0].data[0] + '<br/>';  // 时间
                    
                    // 遍历所有数据系列
                    param.forEach(item => {
                      
                        result += `<span class="tooltip-round" style="background-color:${item.color}"></span>`;
                        if(item.seriesName ==  _self.$t('dashboard_inflow_rate') || item.seriesName ==  _self.$t('dashboard_outflow_rate') ){
                        result += `${item.seriesName}：${
                            item.value[1] === undefined || 
                            item.value[1] === null || 
                            item.value[1] === '' || 
                            item.value[1] === -1 
                                ? '--' 
                                : item.value[1] + '%'
                        }<br/>`;

                        }else {
                           result += `${item.seriesName}：${
                            item.value[1] === undefined || 
                            item.value[1] === null || 
                            item.value[1] === '' || 
                            item.value[1] === -1 
                                ? '--' 
                                : _self.flowSize(item.value[1], true, true)
                        }<br/>`;
                         
                        }
                    });
                    
                    return result;
                }
            };
        },
      handleZoomChange() {
        if(!this.isHandleZoom){
          return
        }
        this.flowEchart.on('dataZoom',(params) => {
          // console.log(params,'params')
          const dataZoom = this.flowEchart.getOption().dataZoom[0];
          console.log(dataZoom,'dataZoom')
          let currentStart = new Date(dataZoom.startValue).format("yyyy-MM-dd HH:mm:ss");
          let currentEnd = new Date(dataZoom.endValue).format("yyyy-MM-dd HH:mm:ss");
          console.log(currentStart,currentEnd,'currentStart,currentEnd')
          if(this.zoomTime){
            // 处理请求
            
            this.query.startTime = currentStart
            this.query.endTime = currentEnd
            this.startValue = currentStart
            this.endValue = currentEnd
            this.lastData= {
              flowIntoSpeeds:this.flowIntoSpeeds,
              flowOutSpeeds:this.flowOutSpeeds,
              intoUsages:this.intoUsages || [],
              outUsages:this.outUsages || []
            }
            this.isZoom = true
            this.getFlowData()
            console.log(this.timeRange,'this.timeRange')
            this.query.startTime= this.timeRange[0]
            this.query.endTime= this.timeRange[1]
            
           
           
          }
           const diffTime = Math.abs(new Date(currentEnd) - new Date(currentStart));
          
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
           if ((this.lastDaysDiff > 7 && diffDays <= 7 )) {
                  console.log('处理请求')
              this.zoomTime = true
            }
          this.lastDaysDiff = diffDays
          console.log(diffDays,'diffDays')
          
        
          
              })
        
        
         
        },
handleZoomOut() {
  this.flowEchart.getZr().on("mousewheel", (params) => {
    if (params.event.deltaY < 0) {
      console.log('Zooming in (放大)');
    } else {
      let start = this.flowEchart.getModel().option.dataZoom[0].start;
      let end = this.flowEchart.getModel().option.dataZoom[0].end;
      
      if(start == 0 && end == 100 && this.isZoom) {
        // 保存当前显示的时间范围
        const currentOption = this.flowEchart.getOption();
        const dataZoom = currentOption.dataZoom[0];
        const startValue = dataZoom.startValue;
        const endValue = dataZoom.endValue;
        
        // 恢复原始数据
        this.flowIntoSpeeds = this.lastData.flowIntoSpeeds;
        this.flowOutSpeeds = this.lastData.flowOutSpeeds;
        this.intoUsages = this.lastData.intoUsages;
        this.outUsages = this.lastData.outUsages;
        this.isZoom = false;
        
        // 初始化图表前设置时间范围
        this.startValue = startValue;
        this.endValue = endValue;
        
        // 重新初始化图表
        this.initEcharts();
        
        // 确保图表完全渲染后设置显示范围
        this.$nextTick(() => {
          const option = this.flowEchart.getOption();
          
          // 计算开始和结束时间在数据中的百分比位置
          const totalData = this.flowIntoSpeeds.length > 0 ? this.flowIntoSpeeds : this.flowOutSpeeds;
          if (totalData.length > 0) {
            const firstTime = new Date(totalData[0][0]).getTime();
            const lastTime = new Date(totalData[totalData.length - 1][0]).getTime();
            const startTime = new Date(startValue).getTime();
            const endTime = new Date(endValue).getTime();
            
            const startPercent = ((startTime - firstTime) / (lastTime - firstTime)) * 100;
            const endPercent = ((endTime - firstTime) / (lastTime - firstTime)) * 100;
            
            // 设置数据缩放的起始和结束位置
            option.dataZoom.forEach(zoom => {
              zoom.start = Math.max(0, Math.min(startPercent, 100));
              zoom.end = Math.max(0, Math.min(endPercent, 100));
            });
            
            this.flowEchart.setOption(option);
          }
        });
      }
    }
  });
},
setZoomPosition() {
  // const option = this.flowEchart.getOption();
  
  // // Set zoom to original time range
  // option.dataZoom[0].startValue = this.startValue;
  // option.dataZoom[0].endValue = this.endValue;
  // option.dataZoom[1].startValue = this.startValue; 
  // option.dataZoom[1].endValue = this.endValue;

  // this.flowEchart.setOption(option);
}
                    
    },
    mounted() {
      this.getLastHour()
      this.$store.commit('m_select/setDeviceId', this.$store.state.m_select.rowData.deviceId)
      this.$store.commit('m_select/setInterfaceId', '')
      this.query.deviceId = this.$store.state.m_select.rowData.deviceId
      this.getFlowData()
        
        // this.initEcharts()
       
    },
    beforeDestroy() {
    if (this.flowEchart) {
        this.flowEchart.off('dataZoom', this.handleZoomChange)
        this.flowEchart.dispose()
        this.flowEchart = null
    }
}

}
</script>

<style scoped lang="less">
.flow-index-center {
  height: 275px;
  width: 100%;
  margin-top: 35px;
}
/deep/.ivu-table-tbody {
  .ivu-table-cell {
    border-bottom: 1px solid var(--table_border_bottom_color, #06324d);
    height: 60px;
    line-height: 60px;
    // color: rgba(241, 145, 73, 0.2);
  }
}
.query_btn {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  // padding-right: 18px;
}
.flow-index-bottom {
  .table-title {
    height: 60px;
    line-height: 60px;
    display: flex;
    background-color: var(--th_b_color, #032a4d);
    .table-title-item:first-child {
      padding-left: 30px;
    }

    .table-title-item {
      flex: 1;
      text-align: left;
      color: var(--th_font_color, #fff);
    }
  }
  .table-in-flow {
    height: 60px;
    display: flex;
    line-height: 60px;
    border-bottom: 1px solid var(--table_border_bottom_color, #06324d);
    .table-title-item {
      flex: 1;
      text-align: left;
      color: var(--th_font_color, #fff);
    }
  }
}
/deep/.ivu-select-selected-value {
  text-align: left;
}
/deep/.ivu-select-placeholder {
  text-align: left;
}
.fn_item {
  display: flex;
  .fn_item_box {
    flex: 0 1 auto; // Change from flex: 1 to flex: 0 1 auto
    min-width: 0; // Add to prevent overflow
    width: 240px; // Add fixed width
  }
}
.fn_item .fn_item_label {
  min-width: unset !important;
  white-space: nowrap;
}
.tool-btn button {
  margin-right: 0 !important;
}
/deep/.ivu-input-icon {
  color: var(--reset_export_del_button_font_color, #05eeff) !important;
}
/deep/.ivu-select {
  .ivu-select-clear {
    color: var(--reset_export_del_button_font_color, #05eeff) !important;
  }
}
/deep/.ivu-select {
  .ivu-icon-ios-close-circle {
    // 添加具体的图标类名
    color: var(--reset_export_del_button_font_color, #05eeff) !important;
  }
}
</style>
