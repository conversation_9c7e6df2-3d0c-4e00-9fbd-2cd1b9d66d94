const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/flowanalysis',
  },
  {
    path: "/flowanalysis",
    name: '流量分析',
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-flowanalysis-ui/views/index.vue"], resolve)
  },
  {
    path: "/deviceInfo",
    name: "222",
    meta: {
      authority: true
    },
		component: () =>
      import("@/modules/anpm-plug-flowanalysis-ui/views/deviceInfo.vue"),
  }
];
