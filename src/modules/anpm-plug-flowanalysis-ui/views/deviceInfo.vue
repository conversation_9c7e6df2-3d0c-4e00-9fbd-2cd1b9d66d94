<template>
  <div
    :class="
      currentSkin == 1 ? 'device-info sectionBox' : 'device-info  light-skin'
    "
  >
    <!-- 返回按钮 -->
    <div class="return-btn">
      <Tooltip :content="$t('but_return')">
        <Button type="primary" class="daoChu-btn" @click="goBack">
          <i class="iconfont icon-icon-return1" />
        </Button>
      </Tooltip>
    </div>
    <!-- /返回按钮 -->
    <Tabs
      :value="currentTab"
      id="aa"
      capture-focus
      @on-click="changeTab"
      :lazy="true"
      :type="currentSkin == 1 ? 'card' : 'line'"
      :class="{
        'tabs-card-content-black': currentSkin == 1,
        'white-tabs-bar': currentSkin == 0,
      }"
    >
      <TabPane
        :label="$t('flow')"
        name="FlowIndex"
        v-if="permissionObj.flowList"
      >
        <FlowIndex
          ref="FlowIndex"
          :rowData="rowData"
          v-if="currentTab == 'FlowIndex'"
        />
      </TabPane>
      <TabPane
        :label="$t('flow_tabs_label_application')"
        name="ApplicationIndex"
        v-if="permissionObj.applyList"
      >
        <ApplicationIndex
          ref="ApplicationIndex"
          v-if="currentTab == 'ApplicationIndex'"
        />
      </TabPane>
      <TabPane
        :label="$t('flowChart_sou')"
        name="SourceIndex"
        v-if="permissionObj.sourceList"
      >
        <SourceIndex ref="SourceIndex" v-if="currentTab == 'SourceIndex'" />
      </TabPane>
      <TabPane
        :label="$t('nodequality_target')"
        name="TargetIndex"
        v-if="permissionObj.destList"
      >
        <TargetIndex ref="TargetIndex" v-if="currentTab == 'TargetIndex'" />
      </TabPane>
      <TabPane
        :label="$t('flow_tabs_label_conversation')"
        name="ConversationIndex"
        v-if="permissionObj.dialogList"
      >
        <ConversationIndex
          v-if="currentTab == 'ConversationIndex'"
          ref="ConversationIndex"
        ></ConversationIndex>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import FlowIndex from './FlowIndex.vue'
import ApplicationIndex from './ApplicationIndex.vue'
import SourceIndex from './SourceIndex.vue'
import TargetIndex from './TargetIndex.vue'
import ConversationIndex from './ConversationIndex.vue'

export default {
    data() {
        return {
            currentSkin:sessionStorage.getItem('dark') || 1,
            rowData:this.$route.query.rowData,
            currentTab:'FlowIndex'
        }
    },
    components: {
      FlowIndex,
      ApplicationIndex,
      SourceIndex,
      TargetIndex,
      ConversationIndex
        
      },
      computed: {
        permissionObj() {
          return this.$store.state.m_permission.permissionObj
        }
      },
    methods: {
      handleActiveName(){
        if(!this.permissionObj.flowList){
          if (this.permissionObj.applyList) {
            this.currentTab = 'ApplicationIndex';
          } else if (this.permissionObj.sourceList) {
            this.currentTab = 'SourceIndex';
          } else if (this.permissionObj.destList) {
            this.currentTab = 'TargetIndex';
          } else if (this.permissionObj.dialogList) {
            this.currentTab = 'ConversationIndex';
          }

        }
      },
     async changeTab(name){
        console.log(name)
        // this.$refs[name].initStart()
       await new Promise(resolve => setTimeout(resolve, 200));
        this.currentTab = name
      },
      goBack() {
        this.$router.push('/');
      }

    },
        created() {
          console.log(this.$route.query.rowData)
          this.handleActiveName()
          if(this.$route.query.rowData) {
            // 主页面跳转
          this.$store.dispatch('m_select/findDeviceListSync')
          this.$store.dispatch('m_select/findDeviceInterfaceListSync', this.$route.query.rowData.deviceId)
          this.$store.commit('m_select/setDeviceId', this.$route.query.rowData.deviceId)
          this.$store.commit('m_select/setRowData', this.$route.query.rowData)
        }else {
          // 映射返回
          this.currentTab = 'ApplicationIndex'


        }
        }
    
}
</script>
<style lang="less" scoped>
.device-info {
  position: relative;
  .return-btn {
    position: absolute;
    top: 9px;
    right: 18px;
    z-index: 1000;
    .daoChu-btn {
      height: 32px !important;
    }
  }
}
/deep/ .white-tabs-bar {
  background-color: #f5f6fa !important;
  .ivu-tabs-tabpane {
    padding: 20px;
    padding-top: unset;
    // background-color: #fff !important;
  }
  .ivu-tabs-bar {
    background-color: #fff !important;
    border-bottom: none !important;
    margin-bottom: 0px !important;

    .ivu-tabs-nav .ivu-tabs-tab {
      padding-top: 15px;
      padding-bottom: 15px;
    }

    .ivu-tabs-nav-right {
      margin-top: 10px;
    }
  }

  .ivu-tabs-content {
    margin-top: 20px !important;
    // padding: 0 20px;
  }
}
.light-skin {
  .return-btn {
    top: 9px;
  }
}
</style>
