<template>
  <div :class="{ 'light-no-tabs': currentSkin == 0 }">
    <section class="sectionBox">
      <div class="section-top">
        <div class="fn_box">
          <div class="fn_item">
            <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
            <div class="fn_item_box">
              <Input
                v-model="query.keyWord"
                :placeholder="$t('device_factory_fuzzy_search')"
                style="width: 300px"
                clearable
              />
            </div>
          </div>
        </div>
        <div class="tool-btn">
          <div>
            <Button
              class="query-btn"
              type="primary"
              icon="ios-search"
              @click="queryClick"
              :title="$t('common_query')"
            ></Button>
            <Button
              class="query-btn"
              type="primary"
              v-if="permissionObj.add"
              icon="md-add"
              @click="actionClick('add')"
              :title="$t('common_new')"
            ></Button>
            <Button
              class="delete-btn"
              type="primary"
              v-if="permissionObj.delete"
              @click="deleteClick"
              :title="$t('common_delete')"
            >
              <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
            </Button>
          </div>
        </div>
      </div>

      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              ref="tableList"
              stripe
              class="fixed-left-right"
              :columns="columns"
              :data="tableList"
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              :no-data-text="
                loading
                  ? ''
                  : tableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              size="small"
            ></Table>

            <div class="tab-page" v-if="tableList.length > 0">
              <Page
                v-page
                :current.sync="currentNum"
                :page-size="query.pageSize"
                :total="totalCount"
                :page-size-opts="pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
      <!--新建/修改模态框-->
      <Modal
        sticky
        v-model="modalParameter.show"
        width="940"
        class="task-modal operation-modal"
        :title="modalParameter.modalTitle"
        :loading="modalParameter.loading"
        draggable
        :mask="true"
        @on-ok="editOk()"
        @on-cancel="editCancel"
      >
        <section>
          <Form
            :model="modalList"
            :rules="ruleValidate"
            ref="editForm"
            :label-width="lang == 'zh' ? 140 : 150"
          >
            <div style="margin-bottom: 20px">
              <FormItem
                :label="$t('snmpoid_device_merchant') + $t('comm_colon')"
                prop="name"
              >
                <Input
                  v-model.trim="modalList.name"
                  :placeholder="$t('device_fill_factory')"
                  :title="modalList.name"
                  maxlength="60"
                />
              </FormItem>
              <FormItem
                :label="
                  $t('discover_device_factory_showCode') + $t('comm_colon')
                "
                prop="showCode"
              >
                <Input
                  v-model.trim="modalList.showCode"
                  :placeholder="$t('factory_code_info')"
                  :title="modalList.showCode"
                  maxlength="5"
                />
              </FormItem>
              <FormItem :label="'OID' + $t('comm_colon')" prop="oid">
                <Input
                  v-model.trim="modalList.oid"
                  :placeholder="$t('snmpoid_fill_OID')"
                  :title="modalList.oid"
                  maxlength="60"
                />
              </FormItem>
            </div>
          </Form>
        </section>
      </Modal>
    </section>
  </div>
</template>

<script>
import "@/config/page.js";
import global from "@/common/global.js";
import {addDraggable} from "@/common/drag.js";
import locationreload from "@/common/locationReload";
export default {
  name: "devicetype",
  data() {
    return {
      lang: localStorage.getItem('locale') || 'zh',
      currentSkin: sessionStorage.getItem('dark') || 1,
      //权限对象
      permissionObj : {},
      //搜索字段
      query: {
        keyWord: "", //关键字
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
      },
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100,200,500,1000],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //新建修改Modal参数
      modalParameter: {
        modalTitle: "", //标题(新建/修改)
        show: false, //控制是否显示
        loading: true
      },
      //新建/修改Modal数据
      modalList: {
        oid: "", //oid
        name: "", //设备厂商
        showCode: "" //设备厂商编码（生成设备编码用）
      },
      //验证规则
      ruleValidate: {
        name: [
          { required: true,
            message: this.$t('device_fill_factory')
          },
          {
            validator: (rule, value, callback) => {
              if (value) {
                // 验证空格 ，英文状态可以 输入空格
                // let pattern = /\s/
                // let str = pattern.test(value)
                // if (str) {
                //   callback(new Error(this.$t("discover_device_type_tips")));
                // } else {
                  // 计算字符长度
                  let length = value.length;
                  let chineseCount = value.replace(/[\x00-\xff]/g, "").length;
                  let totalLength = length + chineseCount;
                  if (totalLength > 60) {
                    callback(new Error(this.$t("common_length_60")));
                  } else {
                    callback();
                  }
                // }
              } else {
                callback();
              }
            },
          },
        ],
        showCode: [
          {
            required: true,
            message: this.$t('factory_code_info'),
          },
          {
            pattern: /^[A-Z]{1,5}$/,
            message: this.$t('factoryCode_uppercase'),
          }
        ],
        oid: [
          {
            required: true,
            message: this.$t('snmpoid_fill_OID')
          },
          {
            validator: (rule, value, callback) => {
              // 使用正则表达式验证是否为数字和小数点
              const reg = /^[0-9.]+$/;
              if (!reg.test(value)) {
                callback(new Error(this.$t("device_fill_oid")));
              } else {
                callback();
              }
            },
          },
        ],
      },
      //表格数据
      tableList: [],
      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 30,
          className: "bgColor",
          align: "center",
          // fixed: 'left',
        },
        {
          title: this.$t('snmpoid_device_merchant'),
          key: "name",
          align: "left",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.name;
            return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                    ? "--"
                    : str
            );
          }
        },
        {
          title: this.$t('discover_device_factory_showCode'),
          key: "showCode",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.showCode;
            return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                    ? "--"
                    : str
            );
          }
        },
        {
          title: "OID",
          key: "oid",
          align: "center",
          className: "bgColor",
          render: (h, params) => {
            let str = params.row.oid;
            str =
                str === undefined || str === null || str === "" || str === "null"
                    ? "--"
                    : str;
            let str1 = str;
            if (str.length > 30) {
              str1 = str.substring(0, 30) + "...";
            }
            return h(
                "Tooltip",
                {
                  props: {
                    placement: "top",
                  },
                },
                [
                  str1,
                  h(
                      "span",
                      {
                        slot: "content", //slot属性
                        style: {
                          whiteSpace: "normal",
                          wordBreak: "break-all",
                        },
                      },
                      str
                  ),
                ]
            );
          },
        },

        {
          title: this.$t('comm_operate'),
          width: "100",
          align: "center",
          // fixed:'right',
          className: "bgColor",
          render: (h, params) => {
            // 0是可编辑，1不可编辑
            // let isBuiltin = params.row.builtin === 1; // 检查 builtin 字段的值是否为 1
            // let className = params.row.builtin == 0 ? this.currentSkin == 1 ? "edit1-btn":"light-edit1-btn" : 'edit2-btn'
            let className = 'edit1-btn'
            if(this.currentSkin == 0){
              params.row.builtin == 0 ? 
               className = "light-edit1-btn" :
               className = "light-edit2-btn"

            }else {
              params.row.builtin == 0 ? 
               className = "edit1-btn" :
               className = "edit2-btn"
            }
            let modify = h(
                    'Tooltip',
                    {
                      props: {
                        placement: 'left-end',
                        transfer: true
                      }
                    },
                    [
                      h('span', {
                        class: className,
                        style: {
                          display: this.permissionObj.update ? 'inline-block' : 'none',
                          marginRight: '0px',
                        },
                        on: {
                          click: () => {
                            this.actionClick('edit', params.row);
                          }
                        },
                        // attrs: {
                        //   disabled: isBuiltin
                        // }
                      }),
                      h('span', { slot: 'content' }, this.$t('common_update'))
                    ]
                ),
                array = [];
            array.push(modify);
            // return h('div', array);
            return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
          }
        }
      ]
    };
  },
  created() {
    this.$nextTick(() => {
      locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    })
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission,{});
    this.$nextTick(() => {
      this.getList(this.query);
    });

  },
  computed: {

  },
  mounted() {
    this.query.pageNo = this.currentNum;
    addDraggable();
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
 },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    queryClick() {
      //点击搜索
     this.query.keyWord = this.query.keyWord.trim()
      this.query.pageNo = this.currentNum = 1;
      this.getList(this.query);
    },
    actionClick(type, data) {
    
      if (type === "add") {
        //新建
          this.modalParameter.show = true;
      this.modalParameter.loading = true;
      this.$refs["editForm"].resetFields();
        this.modalParameter.modalTitle = this.$t('common_new');
        this.modalList = {
          oid: "",
          name: "",
          showCode: "",
        };
      } else if (type === "edit") {
        //修改
        // console.log('修改了',data)
        if(data.builtin == 1) {
        
          this.$Message.warning(this.$t("firm_forbid_edit"))
        }else {
            this.modalParameter.show = true;
      this.modalParameter.loading = true;
      this.$refs["editForm"].resetFields();
            this.modalParameter.modalTitle = this.$t('common_update');
        this.modalList = {
          id: data.id,
          oid: data.oid,
          name: data.name,
          showCode: data.showCode,
        };

        }
     
      
      }
    },
    //新建或者修改提交
    editOk() {
      let param = Object.assign({}, this.modalList);
      let url = "";
      if (this.modalParameter.modalTitle === this.$t('common_new')) {
        //新建
        url = "/deviceFactory/add";
      } else if (this.modalParameter.modalTitle === this.$t('common_update')) {
        //修改
        url = "/deviceFactory/update";
      }
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          this.submitModal(url, param, this.modalParameter.modalTitle);

        } else {
          this.modalParameter.loading = false;
          this.$nextTick(() => {
            this.modalParameter.loading = true;
          });
        }
      });
    },
    submitModal(url, param, type) {
      this.$http.PostJson(url, param).then(res => {
        if (res.code === 1) {
          if (type === this.$t('common_new')) {
            this.$Message.success({content:this.$t('common_add_success'),background:true});
          } else if (type === this.$t('common_update')) {
            this.$Message.success({content:this.$t('common_update_success'),background:true});
          }
          this.modalParameter.show = false;
          this.modalParameter.loading = false;
          this.getList(this.query);
        } else {
          this.modalParameter.loading = false;
          this.$nextTick(() => {
            this.modalParameter.loading = true;
          });
          this.$Message.warning({content:res.msg,background:true});
          // ;
          if (type === this.$t('common_update')) {
            if(res.code === 34){//说明在数据已删除情况下的关闭弹框，加载列表
              this.modalParameter.show = false;
              this.getList(this.query);
            }
          }
        }
        this.selectedIds = new Set();
      });
    },
    //新建/修改模态框取消
    editCancel() {
      this.modalList = {
        name: "",
        oid: ""
      };
    },
    deleteClick() {
       let selectedIdsArrary  = Array.from(this.selectedIds)
      let param = {
        ids:  selectedIdsArrary.join(',')
      };
      if (selectedIdsArrary.length > 0) {
        top.window.$iviewModal.confirm({
          title: this.$t('common_delete_prompt'),
          content: this.$t('device_discovery_del_device_factory'),
          onOk: () => {
            this.$http.wisdomPost("/deviceFactory/delete", param).then(res => {
              if (res.code === 1) {
                this.$Message.success({content:this.$t('comm_deleted_success'),background:true});
              } else {
                this.$Message.error({content:res.msg,background:true});
              }
              this.currentNum = this.query.pageNo = 1;
              this.getList(this.query);
              this.selectedIds = new Set();
            });
          }
        });
      } else {
        this.$Message.warning({content:this.$t('specquality_select'),background:true});
      }
    },
    getList(param) {
      //设备厂商列表查询请求
      this.loading = true;
      this.$http.wisdomPost("/deviceFactory/list", param).then(res => {
        if (res.code === 1) {
          if (res.data) {
            this.tableList = res.data.records;
            this.totalCount = res.data.total || 0;
            //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
            let _that = this;
            setTimeout(function() {
              let objData = _that.$refs.tableList.$refs.tbody.objData;
              for (let key in objData) {
                if (_that.selectedIds.has(objData[key].id)) {
                  objData[key]._isChecked = true;
                }
              }
            }, 0);
          } else {
            this.tableList = [];
          }
          this.loading = false;
        }else{
          this.$Message.error({content:res.msg,background:true})
        }
      }).catch((err)=>{
        console.log(err)
        this.loading = false;
      }).finally(()=>{
        this.loading = false;
      });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      this.getList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      this.getList(this.query);
    },

    //选中table的项目
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.tableList.data;
        data.forEach(item => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach(item => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },

  }
};
</script>

<style scoped>
</style>
