const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/discoverdata',
  },
  {
    path: "/discoverdata",
    name: lan.getLabel("src.autodiscover"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-discoverdata-ui/views/index.vue"], resolve)
  },
];
