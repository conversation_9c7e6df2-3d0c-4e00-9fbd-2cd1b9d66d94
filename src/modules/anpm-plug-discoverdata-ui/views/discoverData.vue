<template>
  <section
      class="sectionBox"
  >
    <!--  查询面板  -->
    <div class="section-top">
      <!--  查询条件    -->
      <div class="fn_box">
        <div class="fn_item">
          <label class="fn_item_label">{{$t('discover_time')}}{{$t('comm_colon')}}</label>
          <div class="fn_item_box disabledTime">
            <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :placeholder="$t('specquality_start_time')"
                :options="startoptions"
                v-model="startTime"
                :editable="false"
                @on-change="startTimeChange"
            >
            </DatePicker>
            <span style="padding:0 15px">{{$t('comm_to')}}</span>
            <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :placeholder="$t('comm_end_time')"
                v-model="endTime"
                :options="endoptions"
                :editable="false"
                @on-change="endTimeChange"
            >
            </DatePicker>
          </div>
        </div>
        <div class="fn_item">
          <label class="fn_item_label">{{$t('discover_rule')}}{{$t('comm_colon')}}</label>
          <div class="fn_item_box">
            <Select
              v-model="ruleId"
              :placeholder="$t('comm_please_select')"
              filterable
              :only-filter-with-text="true"
              clearable
            >
              <Option
                style="width:200px"
                v-for="(item, index) in ruleList"
                :value="item.ruleId"
                :key="index"
                >{{ item.name }}</Option
              >
            </Select>
          </div>
        </div>
        <div class="fn_item" style="width: 100px">
          <Checkbox v-model="isMonitor" style="line-height:1;float: inherit;">{{$t('comm_please_select')}}</Checkbox>
        </div>
        <div class="fn_item">
          <label class="fn_item_label">{{$t('discover_device_type')}}{{$t('comm_colon')}}</label>
          <div class="fn_item_box">
            <Select
              v-model="deviceType"
              :placeholder="$t('comm_please_select')"
              clearable
            >
              <Option
                  v-for="item in deviceList"
                  :value="item.value"
                  :key="item.value"
              >{{ item.lable }}</Option>
            </Select>
          </div>
        </div>
        <div class="fn_item">
          <label class="fn_item_label">{{$t('comm_keywords')}}：</label>
          <div class="fn_item_box">
            <Input
              v-model="keyWord"
              :placeholder="$t('comm_IP_collector')"
              style="width: 200px"
            />
          </div>
        </div>
      </div>
      <!--面板功能按键区域-->
      <div class="fn_tool">
        <Button type="primary" v-if="permissionObj.list" icon="ios-search" @click="queryClick(1)"
          >{{$t('common_query')}}</Button
        >
<!--        <Button type="primary" icon="md-add" @click="openAddForm('plural',selectedIds)"-->
<!--          >新建任务</Button-->
<!--        >-->
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body">
      <div class="section-body-content">
        <div>
          <!--    列表      -->
          <Table
              ref="tablelist"
              border
              :columns="tableColumn"
              :data="pageData.list"
              :loading="page.pageLoading"
              :no-data-text='page.pageLoading ? "" : pageData.total > 0 ? "" : ("<div class=\"table_empty\"><p class=\"emptyText\" >" + $t('common_No_data') + "</p></div>")
            '
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
            <!--    列表操作列功能        -->
            <template
              slot-scope="{ row }"
              slot="action"
              class="tableTools"
            >
              <span
                style="margin-right: 6px; color: #57a3f3; cursor: pointer"
                @click="openEditForm(row)"
                >{{$t('comm_edit')}}</span
              >
              
              <span
                style=" color: #f16643; cursor: pointer"
                :style="'margin-right:'+(!row.taskCode?' 6px;':'0px;')"
                @click="rowRemove(row)"
                >{{$t('but_remove')}}</span
              >
              <span
                  v-if="!row.taskCode"
                style="color: #57a3f3; cursor: pointer"
                @click="openAddForm('singular',row)"
                >{{$t('discover_create_task')}}</span
              >
            </template>
          </Table>
          <!--   分页      -->
          <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
            <Page
              v-page
              :current.sync="page.pageNo"
              :page-size="page.pageSize"
              :total="pageData.total"
              :page-size-opts="page.pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建任务弹框  -->
    <Modal :title="$t('discover_create_task')" sticky
        v-model="addOpen"
        width="622"
        draggable
        :mask="true"
           :loading="addLoading"
        @on-ok="addSubmit"
        @on-cancel="cancleForm('addForm')">
        <Form
              ref="addForm"
              :model="addForm"
              :rules="addFormRule"
              @submit.native.prevent
              :label-width="120"
          >
          <FormItem :label="$t('comm_group')+ $t('comm_colon')">
          <Select
                v-model="addForm.groupId"
                filterable
                :only-filter-with-text="true"
                clearable
                :placeholder="$t('discover_select_maintain_level')"
              >  
                <Option
                  v-for="item in groupList"
                  :value="item.value"
                  :key="item.value"
                >{{ item.label }}</Option>
              </Select>
        </FormItem>
        <FormItem :label="$t('discover_maintain_level')" prop="maintainLevel">
              <Select
                v-model="addForm.maintainLevel"
                filterable
                :only-filter-with-text="true"
                clearable
                :placeholder="$t('discover_select_maintain_level')"
              >  
                <Option
                  v-for="item in maintainList"
                  :value="item.value"
                  :key="item.value"
                >{{ item.lable }}</Option>
              </Select>
            </FormItem>
          <p>{{$t('comm_operation_scheduling')}}：</p>
          <FormItem
                :label="$t('comm_start_time')+ $t('comm_colon')"
                prop="startTime"
                class="labelContent"
                style="width: 100%"
            >
              <DatePicker
                  type="date"
                  v-model="addForm.startTime"
                  :placeholder="$t('comm_select_date2')" 
                  class="myDateClass"
                  format="yyyy-MM-dd"
                  @on-change="playStartDateChange"
              ></DatePicker>
              <span>—</span>
              <DatePicker
                  type="date"
                  v-model="addForm.endTime"
                  :placeholder="$t('comm_select_date2')" 
                  class="myDateClass"
                  format="yyyy-MM-dd"
                  :options="playEndDate"
              ></DatePicker>
            </FormItem>
          <FormItem
                :label="$t('comm_time_period')+ $t('comm_colon')"
                class="labelContent"
                style="width: 100%"
                prop="timeList"
            >
              <div v-for="(item,index) in addForm.timeList" :key="index">
                <TimePicker
                    type="time"
                    v-model="item.start"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playStartTimeChange($event,index)"
                ></TimePicker>
                <span>—</span>
                <TimePicker
                    type="time"
                    v-model="item.end"
                    :placeholder="$t('comm_select_date')"
                    class="myDateClass"
                    format="HH:mm"
                    :clearable="false"
                    @on-change="playEndTimeChange($event,index)"
                ></TimePicker>
                <Icon :type="addForm.timeList.length > 1 && index===addForm.timeList.length-1 || addForm.timeList.length===1 ? 'ios-add-circle-outline' : 'ios-remove-circle-outline'" class="iconItem" :class="addForm.timeList.length > 1 && index===addForm.timeList.length-1 || addForm.timeList.length===1 ? 'addItem':'removeItem'" @click="taskTimeChange(index)" />

              </div>
            </FormItem>
          <FormItem
                :label="$t('comm_repeat_cycle')+ $t('comm_colon')"
                class="labelContent"
                prop="repeatWeek"
            >
              <CheckboxGroup v-model="addForm.repeatWeek">
                <Checkbox :label="1">{{$t('comm_monday')}}</Checkbox>
                <Checkbox :label="2">{{$t('comm_tuesday')}}</Checkbox>
                <Checkbox :label="3">{{$t('comm_wednesday')}}</Checkbox>
                <Checkbox :label="4">{{$t('comm_thursday')}}</Checkbox>
                <Checkbox :label="5">{{$t('comm_friday')}}</Checkbox>
                <Checkbox :label="6">{{$t('comm_saturday')}}</Checkbox>
                <Checkbox :label="7">{{$t('comm_sunday')}}</Checkbox>
              </CheckboxGroup>
            </FormItem>
            <p class="note">{{$t('discover_time_note')}}</p>
          </Form>
        </Modal>
        <!-- 修改弹框  -->
    <Modal :title="$t('common_update')" sticky
        v-model="editOpen"
        width="622"
        draggable
        :mask="true"
        :loading="editLoading"
        @on-ok="editSubmit"
        @on-cancel="cancleForm('editForm')">
        <Form
              ref="editForm"
              :model="editForm"
              :rules="editFormRule"
              class="width_50_Form"
              @submit.native.prevent
              :label-width="120"
          >
          <FormItem label="IP:">
           <Input v-model="editForm.ip" disabled></Input>
        </FormItem>
        <FormItem :label="$t('comm_group')+ $t('comm_colon')">
           <Input v-model="editForm.groupName" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_level')">
           <Input v-model="editForm.maintainLevel" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_rule')">
           <Input v-model="editForm.rule" disabled></Input>
        </FormItem>
        <FormItem :label="$t('comm_dial_protocol')">
           <Input v-model="editForm.protocol" disabled></Input>
        </FormItem>
        <FormItem :label="$t('comm_port')">
           <Input v-model="editForm.port" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_time')">
           <Input v-model="editForm.startTime" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_has_monitor')">
           <Input v-model="editForm.taskCode" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_device_type')" prop="deviceType" class="width_100_item">
              <Select
                v-model="editForm.deviceType"
                filterable
                :only-filter-with-text="true"
                clearable
                multiple
                :placeholder="$t('discover_select_maintain_level')"
              >  
                <Option
                  v-for="item in deviceList"
                  :value="item.value"
                  :key="item.value"
                >{{ item.lable }}</Option>
              </Select>
        </FormItem>
        </Form>
        </Modal>
  </section>
</template>
<script>
/**
 * 时间转为秒
 * @param time 时间(00:00:00)
 * @returns {string} 时间戳（单位：秒）
 */
var time_to_sec = function (time) {
  var s = "";

  var hour = time.split(":")[0];
  var min = time.split(":")[1];
  var sec = time.split(":")[2] || 0;

  s = Number(hour * 3600) + Number(min * 60) + Number(sec);

  return s;
};
/**
 * 时间秒数格式化
 * @param s 时间戳（单位：秒）
 * @returns {*} 格式化后的时分秒
 */
var sec_to_time = function (s) {
  var t;
  if (s > -1) {
    var hour = Math.floor(s / 3600);
    var min = Math.floor(s / 60) % 60;
    // var sec = s % 60;
    if (hour < 10) {
      t = "0" + hour + ":";
    } else {
      t = hour + ":";
    }

    if (min < 10) {
      t += "0";
    }
    t += min;
    // if(sec < 10){t += "0";}
    // t += sec.toFixed(2);
  }
  return t;
};
import moment from "moment";
import "@/config/page.js";
import global from "../../../common/global.js";
import { mapGetters, mapActions, mapState } from "vuex";
import { addDraggable } from "@/common/drag.js";
import locationreload from "@/common/locationReload";
export default {
  name: "discoverData",
  components: {},
  props: {
    tabData: {
      type: String,
      default: "",
    },
  },
  watch: {
    tabData: {
      handler(value) {
        if (this.pageData.list.length === 0 && value === "discoverData") {
          this.getRuleList();
          this.getDeviceList();
          this.getGroupList();
          this.getmaintainLevelList();
          this.queryClick(1);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.$nextTick(() => {
      locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    })
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    moment.locale("zh-cn");
  },
  data() {
    return {
      permissionObj:{},
      //时间限制
      startoptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const start = new Date();
              return start;
            },
            onClick: (picker) => {
              // this.startDate = new Date().format("yyyy-MM-dd");
              // this.endDate = new Date().format("yyyy-MM-dd");
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              const end = new Date();
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let start = new Date(date.year, date.month, 1);
              return start;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();

              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return start;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
        ],
        disabledDate: (date) => {
          return (
            (date && date.valueOf() > Date.now()) || date.getFullYear() <= 2017
          );
          // let data = this.endDate == '' ? Date.now() : this.endDate
          // return date > data
        },
      },
      endoptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              // this.startDate = new Date().format("yyyy-MM-dd");
              // this.endDate = new Date().format("yyyy-MM-dd");
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              const end = new Date();
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              return end;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              return end;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              // this.startDate = start.format("yyyy-MM-dd");
              // this.endDate = end.format("yyyy-MM-dd");
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
        ],
        disabledDate: (date) => {
          // return date && date.valueOf() > Date.now();
          let data = this.startTime == "" ? "" : this.startTime;
          return date < data || date > Date.now();
        },
      },
      //当前时间
      currentTime: Date.now(),
      /* 参数列表 */
      //时间
      startTime: new Date().format("yyyy-MM-dd 00:00:00"),
      endTime: new Date().format("yyyy-MM-dd 23:59:59"),
      //规则
      ruleId: null,
      //规则列表
      ruleList: [],
      //是否监测
      isMonitor: false,
      //设备类型
      deviceType: null,
      deviceList: [],
      //关键字
      keyWord: "",
      //分页参数
      page: {
        pageNo: 1,
        pageSize: 10,
        pageSizeOpts: [10, 50, 100, 200, 500, 1000],
        pageLoading: false,
      },
      /** 列表数据 */
      pageData: {
        total: 0,
        list: [],
      },
      /** 列表选中数据 */
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      selectedDatas: [],
      /** 新建表单 */
      addForm: {
        groupId: null,
        maintainLevel: null,
        startDate: null,
        endDate: null,
        timeList: [{ start: "0000", end: "2359" }],
        repeatWeek: [],
      },
      //新建动画
      addLoading:true,
      addIds: "",
      //分组数据列表
      groupList: [],
      //运维等级数据列表
      maintainList:[],
      //新建弹框
      addOpen: false,
      //新建表单验证规则
      addFormRule: {
        groupId: [
          {
            required: true,
            type: "number",
            message: this.$t('comm_select_group'),
            trigger: "change",
          },
        ],
        maintainLevel: [
          {
            required: true,
            type: "string",
            message: this.$t('discover_select_maintain_level'),
            trigger: "change",
          },
        ],
        startTime: [
          {required: true, type: "date", message: this.$t('comm_start_time'), trigger: "change",},
        ],
        endTime: [
          {
            required: true,
            type: "date",
            message: this.$t('comm_end_time'),
            trigger: "change",
          },
        ],
        timeList: [{required: true, type: "array", message: this.$t('discover_date_rang'), trigger: "change",},],
        repeatWeek: [
          {
            required: true,
            type: "array",
            message: this.$t('comm_select_cycle'),
            trigger: "change",
          },
        ],
      },
      /** 修改表单 */
      editForm: {
        id: null,
        ip: "",
        groupId: null,
        groupName: "",
        maintainLevel: null,
        rule: null,
        protocol: null,
        port: "",
        startTime: null,
        taskCode: null,
        deviceType: [],
      },
      //修改验证规则
      editFormRule:{
        deviceType:[{required: true, type: "array", message: this.$t('snmptask_device_type'), trigger: "change",}]
      },
      //修改弹框
      editOpen: false,
      //修改加载
      editLoading:true,
      /** 表格标题列 */
      tableColumn: [
        {type: "selection",width: 60,className: "bgColor",align: "center"},
        { title: "IP", align: "center", width: 110, key: "ip" },
        { title: this.$t('discover_gether_name'), align: "center", minWidth: 60, key: "getherName" },
        { title: this.$t('comm_grouping'), align: "center", minWidth: 60, key: "groupName" },
        { title: this.$t('discover_level'), align: "center", minWidth: 60, key: "maintainLevel" },
        { title: this.$t('discover_rule'), align: "center", minWidth: 80, key: "name" },
        { title: this.$t('device_discovery_dial_test_protocol'), align: "center", minWidth: 80, key: "protocol",
          render: (h, param) => {
            return h('span', param.row.protocol == 4 ?'UDP' : param.row.protocol == 5 ?'ICMP' : param.row.protocol == 7 ?'TCP':'--')
          }
        },
        { title: this.$t('message_server_port'), align: "center", width: 80, key: "port" },
        { title: this.$t('discover_time'), align: "center", width: 100, key: "createTime" },
        { title: "是否已监测", align: "center", minWidth: 80, key: "taskCode" ,
          render: (h, param) => {
            return h('span', param.row.taskCode ? param.row.taskCode : this.$t('device_discovery_not_monitored'))
          }
        },
        { title: this.$t('discover_device_type'), align: "center", minWidth: 80, key: "deviceName" },
        { title: this.$t('comm_operate'), align: "center", width: 160, slot: "action" },
      ],

      playEndDate: {
        disabledDate(date) {
          // return date && date.valueOf() < Date.now() - 86400000;
        },
      },
    };
  },
  mounted() {
    let dome = document.getElementsByClassName("disabledTime");
    dome[0]
      .getElementsByClassName("ivu-picker-confirm")[0]
      .getElementsByTagName("button")[0]
      .setAttribute("disabled", "disabled");
    dome[0]
      .getElementsByClassName("ivu-picker-confirm")[1]
      .getElementsByTagName("button")[0]
      .setAttribute("disabled", "disabled");
  },
  methods: {
    //查询事件
    queryClick(pageNo) {
      this.keyWord = this.keyWord.trim();
      //初始化页码
      this.page.pageNo = pageNo;
      //设置查询参数
      const queryParam = {
        startDate: this.startTime?new Date(this.startTime).format('yyyy-MM-dd'):null,
        endDate: this.endTime?new Date(this.endTime).format('yyyy-MM-dd'):null,
        ruleId: this.ruleId,
        taskCode: this.isMonitor ? 1 : 0,
        deviceType: this.deviceType,
        keyword: this.keyWord,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
      };
      //打开加载动画
      this.pageLoading = true;
      //请求数据
      this.getTableList(queryParam);
    },
    //获取列表数据
    getTableList(queryParam) {
      this.$http
        .wisdomPost("/netDiscoverData/list", queryParam)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if (data) {
              this.pageData.list = data.records;
              this.pageData.total = data.total || 0;
            } else {
              this.setTableListEmpty();
            }
          } else {
            this.setTableListEmpty();
            this.$Message.warning({content:msg,background:true});
          }
        })
        .catch((error) => {
          this.setTableListEmpty();
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    //设置列表数据为空列表
    setTableListEmpty() {
      this.pageData.list = [];
      this.pageData.total = 0;
    },
    /** 新建 */
    //打开新建表单
    openAddForm(type, data) {
      if (type === "plural") {
        //多选
        const idArr = Array.from(data);
        if (idArr.length < 1) {
          this.$Message.warning({content:this.$t('warning_first_operation_data'),background:true});
          return;
        } else {
          this.addIds = idArr.join(",");
        }
      } else if (type === "singular") {
        //单选
        this.addIds = data.id;
      }
      this.$refs["addForm"].resetFields();
      (this.addForm = {
        groupId: null,
        maintainLevel: null,
        startDate: null,
        endDate: null,
        timeList: [{ start: "0000", end: "2359" }],
        repeatWeek: [],
      }),
        (this.addOpen = true);
    },
    //新建请求接口
    addSubmit() {
      this.$refs["addForm"].validate(valid => {
        if (valid){

          //时间段验证
          const ruleTimeFlag = this.ruleTimes(this.addForm.timeList);
          if(ruleTimeFlag === false){
            this.$Message.warning({content:this.$t('comm_tip3'),background:true});
            //取消按钮加载效果
            this.addLoading = false;
            this.$nextTick(() => {
              this.addLoading = true;
            });
            return
          }else if (ruleTimeFlag){
            const addParam = {
              id: this.addIds,
              groupId: this.addForm.groupId,
              maintainLevel: this.addForm.maintainLevel,
              startDate: new Date(this.addForm.startTime).format('yyyyMMdd'),
              endDate: new Date(this.addForm.endTime).format('yyyyMMdd'),
              repeatWeek: this.addForm.repeatWeek.join(","),
              timeSlot: this.addForm.timeList
                  .map((item) => {
                    return item.start.replace(':','') + "-" + item.end.replace(':','');
                  })
                  .join(","),
            };
            this.$http
                .wisdomPost("/netDiscoverData/createTask", addParam)
                .then(({ code, data, msg }) => {
                  if (code === 1) {
                    this.$Message.success({content:this.$t('device_discovery_create_success'),background:true});
                    this.addOpen = false;
                  } else {
                    this.$Message.warning({content:msg,background:true});
                  }
                })
                .catch((err) => {
                  throw new Error(err);
                })
                .finally(() => {
                  //取消按钮加载效果
                  this.addLoading = false;
                  this.$nextTick(() => {
                    this.addLoading = true;
                  });
                  this.queryClick(1);
                });
          }
        }else{
          //取消按钮加载效果
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
        }
      })

    },
    /** 编辑 */
    //打开编辑表单
    openEditForm(param) {
      this.$refs["addForm"].resetFields();
      this.$http
          .wisdomPost("/netDiscoverData/getDetail", {id:param.id}).then(({code,data,msg})=>{
            if(code === 1){
              this.editForm.ip = data.ip;
              this.editForm.groupId = data.groupId;
              this.editForm.groupName = data.groupName;
              this.editForm.maintainLevel = data.maintainLevel == 1 ?this.$t('logback_first'):data.maintainLevel == 2 ?this.$t('logback_second'):data.maintainLevel == 3 ?this.$t('logback_tertiary'):'--';
              this.editForm.rule = data.name;
              this.editForm.protocol = data.protocol == 4 ?'UDP' : data.protocol == 5 ?'ICMP' : data.protocol == 7 ?'TCP':'--';
              this.editForm.port = data.port||'--';
              this.editForm.startTime = data.createTime;
              this.editForm.taskCode = data.taskCode??this.$t('device_discovery_not_monitored');
              this.editForm.deviceType = data.deviceType? data.deviceType.split(',') : null;
              this.editForm.id = data.id;
              this.editOpen = true;
            }else{
              this.$Message.warning({content:this.$t('common_get_data_error'),background:true})
            }

      }).catch((error)=>{
        throw new Error(error)
      })
    },
    //修改请求接口
    editSubmit() {
      this.$refs["editForm"].validate(valid => {
        if (valid){
          const editParam = {
            id: this.editForm.id,
            deviceType: this.editForm.deviceType.join(','),
          };
          this.$http
              .wisdomPost("/netDiscoverData/update", editParam)
              .then(({ code, data, msg }) => {
                if (code === 1) {
                  this.$Message.success({content:this.$t('comm_changed_successful'),background:true});
                  this.editOpen = false;
                } else {
                  this.$Message.warning({content:msg,background:true});
                }
              })
              .catch((err) => {
                throw new Error(err);
              })
              .finally(() => {
                //取消按钮加载效果
                this.editLoading = false;
                this.$nextTick(() => {
                  this.editLoading = true;
                });
                this.queryClick(1);
              });
        }else{
          //取消按钮加载效果
          this.editLoading = false;
          this.$nextTick(() => {
            this.editLoading = true;
          });
        }
      })

    },
    /** 删除 */
    rowRemove(data) {
      top.window.$iviewModal.confirm({
        title: this.$t('common_delete_prompt'),
        content: this.$t('discover_msg_delete'),
        onOk: () => {
          this.$http
            .wisdomPost("/netDiscoverData/delete", { ids: data.id })
            .then(({ code, data, msg }) => {
              if (code === 1) {
                this.$Message.success({content:this.$t('common_controls_succ'),background:true})
                this.queryClick(1);
              } else {
                this.$Message.warning({content:msg,background:true});
              }
            })
            .catch((err) => {
              throw new Error(err);
            });
        },
      });
    },
    /** 取消事件 */
    cancleForm(formObj) {
      this.$refs[formObj].resetFields();
    },

    /** 获取基础数据 */
    //获取规则数据
    getRuleList() {
      this.ruleList = [];
      this.$http
        .wisdomPost("/netDiscoverData/queryRuleDiscover")
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.ruleList = data ?? [];
          } else {
            this.ruleList = [];
            this.$Message.warning({content:msg,background:true});
          }
        })
        .catch((err) => {
          this.ruleList = [];
          throw new Error(err);
        });
    },
    //获取设备数据
    getDeviceList() {
      this.deviceList = [];
      this.$http
        .wisdomPost("/dataTable/queryCode",{key:'netDeviceType'})
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.deviceList = data ?? [];
          } else {
            this.deviceList = [];
            this.$Message.warning({content:msg,background:true});
          }
        })
        .catch((err) => {
          this.deviceList = [];
          throw new Error(err);
        });
    },
    //获取分组数据
    getGroupList() {
      this.groupList = [];
      this.$http
        .wisdomPost("/group/list", { pageNo: 1, pageSize: 10000 })
        .then(({ code, data, msg }) => {
          if (code === 1) {
            this.groupList = data ? data.records : [];
          } else {
            this.groupList = [];
            this.$Message.warning({content:msg,background:true});
          }
        })
        .catch((err) => {
          this.groupList = [];
          throw new Error(err);
        });
    },
    //获取运维等级数据
    getmaintainLevelList() {
      this.maintainList = [];
      this.$http
          .wisdomPost("/dataTable/queryCode",{key:'maintainLevel'})
          .then(({ code, data, msg }) => {
            if (code === 1) {
              this.maintainList = data ?? [];
            } else {
              this.maintainList = [];
              this.$Message.warning({content:msg,background:true});
            }
          })
          .catch((err) => {
            this.maintainList = [];
            throw new Error(err);
          });
    },

    //时间事件
    startTimeChange(val) {
      if (val == "") {
        this.endTime = "";
        this.startoptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
        this.endoptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        var now = new Date(val),
          y = now.getFullYear(),
          m = now.getMonth() + 1,
          h = now.getHours(),
          min = now.getMinutes(),
          s = now.getSeconds(),
          d = now.getDate();
        let ss =
          y +
          "-" +
          (m < 10 ? "0" + m : m) +
          "-" +
          (d < 10 ? "0" + d : d) +
          " " +
          now.toTimeString().substr(0, 8);
        this.endoptions.disabledDate = (date) => {
          let checkedDay = new Date(
            y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let checkedTime = new Date(this.startTime).valueOf();
          let interTime = checkedTime - checkedDay;
          let startTime = this.startTime
            ? new Date(this.startTime).valueOf()
            : "";
          let endTime = val
            ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000
            : "";
          return (
            (date && date.valueOf() < startTime - interTime) ||
            (date &&
              date.valueOf() >
                (endTime > this.currentTime ? this.currentTime : endTime))
          );
        };
      }
    },
    endTimeChange(val) {
      if (val == "") {
        this.startoptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        if (this.startTime == "" || this.startTime == undefined) {
          this.endTime = "";
          this.$Message.warning({content:this.$t('specquality_strat'),background:true});
        } else {
          var now = new Date(val),
            y = now.getFullYear(),
            m = now.getMonth() + 1,
            h = now.getHours(),
            min = now.getMinutes(),
            s = now.getSeconds(),
            d = now.getDate();
          let ss =
            y +
            "-" +
            (m < 10 ? "0" + m : m) +
            "-" +
            (d < 10 ? "0" + d : d) +
            " " +
            now.toTimeString().substr(0, 8);
          let checkedDayTimes = new Date(
            y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let nowDayTimes = moment().startOf("day").valueOf();
          if (ss.slice(-8) == "00:00:00") {
            if (checkedDayTimes == nowDayTimes) {
              this.endTime = new Date(this.currentTime).format(
                "yyyy-MM-dd HH:mm:ss"
              );
            } else {
              this.endTime = new Date(val).format("yyyy-MM-dd 23:59:59");
            }
          }
          this.startoptions.disabledDate = (date) => {
            let endTime = this.endTime ? new Date(this.endTime).valueOf() : "";
            let startTime = val
              ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000
              : "";
            return (
              (date && date.valueOf() < startTime) ||
              (date &&
                date.valueOf() >
                  (endTime > this.currentTime ? this.currentTime : endTime))
            );
          };
        }
      }
    },
    //数据列表页码切换
    pageChange(pageNo) {
      this.queryClick(pageNo);
    },
    //数据列表页码大小改变
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.queryClick(this.page.pageNo);
    },
    //选中table的项目
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        let data = this.$refs.tableList.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
            this.selectedDatas.splice(
              this.selectedDatas.findIndex((current) => item.id === current.id),
              1
            );
            // this.selectedDatas.remove(JSON.stringify(item));
          }
        });
        console.log(this.selectedDatas);
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
          this.selectedDatas.push(item);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
      this.selectedDatas.push(row);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
      this.selectedDatas.splice(
        this.selectedDatas.findIndex((item) => item.id === row.id),
        1
      );
    },

    /** 新建时时间选择控制 */
    playStartTimeChange(time, index) {
      const endTime = this.addForm.timeList[index].end;
      if (time != undefined && time != null && time != "") {
        const endTimes = time_to_sec(endTime);
        const startTimes = time_to_sec(time);
        if (endTime == undefined || endTimes < startTimes) {
          this.addForm.timeList[index].end = sec_to_time(startTimes);
        }
      }
    },
    playEndTimeChange(time, index) {
      const startTime = this.addForm.timeList[index].start;
      if (time != undefined && time != null && time != "") {
        const endTimes = time_to_sec(time);
        const startTimes = time_to_sec(startTime);
        if (startTime == undefined || endTimes < startTimes) {
          this.addForm.timeList[index].start = sec_to_time(endTimes);
        }
      }
    },
    playStartDateChange(val) {
      this.playEndDate.disabledDate = (date) => {
        // return date && date.valueOf() < new Date(val).valueOf() - 86400000;
      };
    },
    //时间段改变事件
    //时间段操作（增加，删除）
    taskTimeChange(index) {
      const length = this.addForm.timeList.length;
      if (index === length - 1) {
        //增加
        if (length >= 10) {
          this.$Message.warning({content:this.$t('discover_msg_add_10'),background:true});
          return;
        } else {
          this.addForm.timeList.push({ start: "0000", end: "2359" });
        }
      } else {
        //删除
        this.addForm.timeList.splice(index, 1);
      }
    },
    //时间段校验是否存在交叉
    ruleTimes(times){
      const timeArray = times;
      //结果返回值
      let timesFlag = true;
      //将每一项换算成分钟数
      let timeMinArray = timeArray.map(item=>{
        let start =0,end =0;
        start = Number(item.start.split(':')[0])*60+Number(item.start.split(':')[1]);
        end = Number(item.end.split(':')[0])*60+Number(item.end.split(':')[1]);
        return [start,end]
      })
      for(let i = 0;i<timeMinArray.length-1;i++){
        for(let j = i+1;j<timeMinArray.length;j++){
          if((timeMinArray[j][1]<=timeMinArray[i][0] || timeMinArray[j][0]>=timeMinArray[i][1])){
            timesFlag = true ;
          }else{
            timesFlag = false ;
            break;
          }
        }
        if(timesFlag === false){
          break;
        }
      }
      return timesFlag
    }
  },
  destroyed() {},
};
</script>

<style lang='less'>
</style>
<style scoped lang="less">
</style>


