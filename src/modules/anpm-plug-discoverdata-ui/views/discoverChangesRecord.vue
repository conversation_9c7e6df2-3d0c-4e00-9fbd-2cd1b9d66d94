<template>
  <section>
    <Modal
      v-model="taskModal.show"
      width="1200"
      class="task-modal border-table"
      :title="taskModal.title"
      :loading="taskModal.loading"
      :mask="true"
      sticky
      draggable
      :footer-hide="true"
      @on-cancel="closeBtn"
    >
      <div class="section-body2">
        <div class="section-body-content">
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              ref="tableList"
              height="400"
              width="100%"
              :columns="taskColumns"
              :data="tableList"
              :no-data-text="
                loading
                  ? ''
                  : tableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              size="small"
            ></Table>
            <div class="tab-page" v-if="tableList.length > 0">
              <Page
                v-page
                :current.sync="currentNum"
                :page-size="query.pageSize"
                :total="totalCount"
                :page-size-opts="pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </section>
</template>

<script>
import '@/config/page.js';
import axios from 'axios';
import global from '../../../common/global.js';

export default {
    props: {
        changesRecordListParam: {}
    },
    components: {
    },
    data() {
        return {
            currentSkin:sessionStorage.getItem('dark') || 1,
            taskModal: {
                show: true,
                title: this.$t('net_data_discover_change_record_details'),
                loading: false
            },
            //权限对象
            permissionObj: {},
            //搜索字段
            query: {
                netDataDiscoverId: '', //分组id
                pageNo: 1, //页数
                pageSize: 10, //每页展示多少数据
                url: '' //查询的接口url
            },
            showChangesRecordList: false,
            //当前页数
            currentNum: 1,
            //总条数
            totalCount: 0,
            //表格每页多少条数据
            loading: false,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: new Set(),
            tableList: [],
            taskColumns: [],
            probeTaskColumns: [
                {
                    title: this.$t('net_data_discover_change_time'),
                    key: 'discoverTime',
                    align: 'right',
                    width: 500,
                    className: 'bgColor'
                },
                {
                    title: this.$t('net_data_discover_change_operation'),
                    key: 'changeContent',
                    align: 'center',
                    className: 'bgColor'
                }
            ]
        };
    },
    created() {
        this.init();
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
    },
    methods: {
        init() {
            this.taskModal.title = this.$t('net_data_discover_change_record_details');
            this.taskColumns = this.probeTaskColumns;
            this.query.url = '/netDiscoverData/discoverChangesRecordList';
            this.query.netDataDiscoverId = this.changesRecordListParam.netDataDiscoverId;
            this.getList(this.query);
        },
        getList(param) {
            let _self = this;
            _self.$http.wisdomPost(this.query.url, param).then(res => {
                _self.loading = true;
                if (res.code === 1) {
                    if (res.data) {
                        _self.tableList = res.data.records;
                        _self.totalCount = res.data.total || 0;
                    } else {
                        _self.tableList = [];
                    }
                    _self.loading = false;
                } else {
                    _self.loading = false;
                }
            });
        },
        closeBtn() {
            this.$emit('closeBtn', 'close');
        },
        pageChange(val) {
            //表格页码切换
            this.currentNum = val;
            this.query.pageNo = this.currentNum;
            this.getList(this.query);
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.currentNum = 1;
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList(this.query);
        },
    }
};
</script>

<style>
.repeatTable .ivu-table-tip table td {
  text-align: center;
  text-indent: 235px;
}
.task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
  height: auto !important;
  min-height: 32px;
}
.ivu-modal .ivu-modal-content .ivu-table td,
.ivu-modal .ivu-modal-content .ivu-table th {
  text-align: center;
}
</style>
