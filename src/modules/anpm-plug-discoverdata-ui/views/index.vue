<template>
  <!-- 发现数据 server_sure_delete-->
  <section class="sectionBox">
    <!--  查询面板  -->
    <div class="section-top">
      <Row class="fn_box">
        <!--时间范围-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="true"
                style="width: 100%"
                :confirm="false"
              >
              </DatePicker>
            </div>
          </div>
        </Col>
        <!--企业组织-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label">{{ $t("comm_org") }}：</label>
            <div class="fn_item_box">
              <TreeSelect
                v-model="treeValue"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadData"
                @onSelectChange="setOrg"
                @onClear="onClear"
                @onFocus="focusFn"
              ></TreeSelect>
            </div>
          </div>
        </Col>
        <!-- 发现规则 -->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("discover_rule") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="ruleId"
                :placeholder="$t('comm_please_select')"
                style="width: 100%"
                filterable
                :only-filter-with-text="true"
                clearable
              >
                <Option
                  v-for="(item, index) in ruleList"
                  :value="item.ruleId"
                  :key="index"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <!--设备类型-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("discover_device_type") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="deviceType"
                :placeholder="$t('comm_please_select')"
                clearable
                filterable
                :only-filter-with-text="true"
                style="width: 100%"
              >
                <Option
                  v-for="item in deviceList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.lable }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>

        <!--是否监测-->
        <!-- <Col span="2">
                <div class="fn_item" >
                    <Checkbox v-model="isMonitor" style="line-height:1;float: inherit;margin-left:40px;">{{$t('discover_has_monitor')}}</Checkbox>
                </div>
            </Col> -->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("net_data_discover_whether_monitor")
              }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="isMonitor"
                :placeholder="$t('comm_please_select')"
                clearable
                filterable
                :only-filter-with-text="true"
                style="width: 100%"
              >
                <Option
                  v-for="item in monitorStatisList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.lable }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <!--是否识别-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("net_data_discover_whether_identify")
              }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="identifyStatus"
                :placeholder="$t('comm_please_select')"
                clearable
                filterable
                :only-filter-with-text="true"
                style="width: 100%"
              >
                <Option
                  v-for="item in identifyStatisList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.lable }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <!--是否变化-->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("net_data_discover_whether_change")
              }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="changeStatus"
                :placeholder="$t('comm_please_select')"
                clearable
                filterable
                :only-filter-with-text="true"
                style="width: 100%"
              >
                <Option
                  v-for="item in changeStatisList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.lable }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>

        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
            <div class="fn_item_box">
              <Input
                v-model="keyWord"
                :placeholder="$t('comm_IP_collector')"
                style="width: 100%"
              />
            </div>
          </div>
        </Col>
      </Row>
      <!--面板功能按键区域-->
      <div class="tool-btn">
        <div>
          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.list"
            icon="ios-search"
            @click="queryClick(1)"
            :title="$t('common_query')"
          ></Button>
        </div>
        <Dropdown @on-click="moreBtnClick">
          <Button class="more-btn">
            {{ $t("dashboard_more") }}
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list" width="200px">
            <DropdownItem
              name="batchOpenAddForm"
              v-if="permissionObj.createtask"
              >{{ $t("discover_create_task") }}</DropdownItem
            >
            <DropdownItem name="exportClick" v-if="permissionObj.export">{{
              $t("but_export")
            }}</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body" style="padding: 0">
      <Loading :loading="pageLoading"></Loading>
      <div class="section-body-content">
        <div>
          <!--    列表      -->
          <Table
            ref="tablelist"
            stripe
            :columns="tableColumn"
            class="fixed-left-right"
            :data="pageData.list"
            :no-data-text="
              pageData.total > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
            <!--    是否已监测	   -->
            <template slot-scope="{ row }" slot="taskCode">
              <span>{{
                row.taskCode
                  ? row.taskCode
                  : $t("device_discovery_not_monitored")
              }}</span>
              <Tooltip
                :content="row.failInfo"
                v-if="row.failInfo"
                placement="right-start"
                max-width="200"
              >
                <Icon type="md-alert" class="toop" />
              </Tooltip>
            </template>

            <!--    列表操作列功能        -->
            <template slot-scope="{ row }" slot="action" class="tableTools">
              <Tooltip
                :content="$t('common_update')"
                placement="left"
                :transfer="true"
              >
                <span
                  :class="currentSkin == 1 ? 'edit1-btn' : 'light-edit1-btn'"
                  v-if="permissionObj.update"
                  @click="openEditForm(row)"
                ></span>
              </Tooltip>

              <Tooltip
                :content="$t('common_delete')"
                placement="left"
                :transfer="true"
              >
                <span
                  class="del1-btn"
                  v-if="permissionObj.delete"
                  @click="rowRemove(row)"
                ></span>
              </Tooltip>
              <Tooltip
                :content="$t('discover_create_task')"
                placement="right"
                :transfer="true"
              >
                <span
                  class="create1-btn"
                  v-if="permissionObj.createtask && !row.taskCode"
                  @click="openAddForm('singular', row)"
                ></span>
              </Tooltip>
            </template>
          </Table>
          <!--   分页      -->
          <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
            <Page
              v-page
              :current.sync="page.pageNo"
              :page-size="page.pageSize"
              :total="pageData.total"
              :page-size-opts="page.pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建任务弹框  -->
    <Modal
      :title="$t('discover_create_task')"
      sticky
      v-model="addOpen"
      width="940"
      draggable
      :mask="true"
      :loading="addLoading"
      @on-ok="addSubmit"
      @on-cancel="cancleForm('addForm')"
    >
      <Form
        ref="addForm"
        :model="addForm"
        :rules="addFormRule"
        @submit.native.prevent
        :label-width="100"
      >
        <Row>
          <Col span="12">
            <FormItem
              :label="$t('comm_group') + $t('comm_colon')"
              prop="groupId"
              :label-width="150"
            >
              <Select
                v-model="addForm.groupId"
                filterable
                :only-filter-with-text="true"
                clearable
                :placeholder="$t('comm_select_group')"
              >
                <Option
                  v-for="item in groupList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem
              :label="$t('discover_maintain_level') + $t('comm_colon')"
              prop="maintainLevel"
              :label-width="150"
            >
              <Select
                v-model="addForm.maintainLevel"
                filterable
                :only-filter-with-text="true"
                clearable
                :placeholder="$t('discover_select_maintain_level')"
                :label-width="50"
              >
                <Option
                  v-for="item in maintainList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.lable }}
                </Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <div class="divider">
          <div
            class="divider-title"
            style="width: 170px; text-align: right; margin-left: -10px"
          >
            {{ $t("comm_operation_scheduling") }}：
          </div>
          <div class="line"></div>
        </div>

        <div class="flx-com" style="display: flex">
          <FormItem
            :label="$t('comm_start_time') + $t('comm_colon')"
            prop="startDate"
            class="labelContent"
            :label-width="150"
          >
            <DatePicker
              type="date"
              v-model="addForm.startDate"
              :placeholder="$t('comm_select_date2')"
              format="yyyy-MM-dd"
              :options="molStartOpt"
            ></DatePicker>
          </FormItem>
          <FormItem
            label="—"
            prop="endDate"
            class="labelContent"
            :label-width="16"
          >
            <DatePicker
              type="date"
              v-model="addForm.endDate"
              :placeholder="$t('comm_select_date2')"
              format="yyyy-MM-dd"
              :options="molEndOpt"
            ></DatePicker>
          </FormItem>
        </div>

        <FormItem
          :label="$t('comm_time_period') + $t('comm_colon')"
          class="labelContent"
          prop="timeList"
          :label-width="150"
        >
          <div
            v-for="(item, index) in addForm.timeList"
            :key="index"
            style="margin-bottom: 10px"
          >
            <TimePicker
              type="time"
              v-model="item.start"
              :placeholder="$t('comm_select_date')"
              format="HH:mm"
              :clearable="false"
              @on-change="playStartTimeChange($event, index)"
            >
            </TimePicker>
            <span>—</span>
            <TimePicker
              type="time"
              v-model="item.end"
              :placeholder="$t('comm_select_date')"
              format="HH:mm"
              :clearable="false"
              @on-change="playEndTimeChange($event, index)"
            ></TimePicker>
            <Icon
              :type="
                addForm.timeList.length > 1 ? 'ios-remove-circle-outline' : ''
              "
              class="iconItem"
              :class="addForm.timeList.length > 1 ? 'removeItem' : ''"
              @click="speedTimeChangeRemove(index)"
            />
            <Icon
              :type="
                index === addForm.timeList.length - 1
                  ? 'ios-add-circle-outline'
                  : ''
              "
              class="iconItem"
              :class="index === addForm.timeList.length - 1 ? 'addItem' : ''"
              @click="speedTimeChangeAdd(index)"
            />
          </div>
        </FormItem>
        <FormItem
          :label="$t('comm_repeat_cycle') + $t('comm_colon')"
          class="labelContent"
          prop="repeatWeek"
          :label-width="150"
        >
          <CheckboxGroup v-model="addForm.repeatWeek">
            <Checkbox :label="1">{{ $t("comm_monday") }}</Checkbox>
            <Checkbox :label="2">{{ $t("comm_tuesday") }}</Checkbox>
            <Checkbox :label="3">{{ $t("comm_wednesday") }}</Checkbox>
            <Checkbox :label="4">{{ $t("comm_thursday") }}</Checkbox>
            <Checkbox :label="5">{{ $t("comm_friday") }}</Checkbox>
            <Checkbox :label="6">{{ $t("comm_saturday") }}</Checkbox>
            <Checkbox :label="7">{{ $t("comm_sunday") }}</Checkbox>
          </CheckboxGroup>
        </FormItem>
        <p class="note">{{ $t("discover_time_note") }}</p>
      </Form>
    </Modal>
    <!-- 修改弹框  -->
    <Modal
      :title="$t('common_update')"
      sticky
      v-model="editOpen"
      class="operation-modal"
      width="960"
      draggable
      :mask="true"
      :loading="editLoading"
      @on-ok="editSubmit"
      @on-cancel="cancleForm('editForm')"
    >
      <Form
        ref="editForm"
        :model="editForm"
        :rules="editFormRule"
        class="width_50_Form"
        @submit.native.prevent
        :label-width="140"
      >
        <FormItem label="IP:">
          <Input v-model="editForm.ip" disabled></Input>
        </FormItem>
        <FormItem :label="$t('comm_group') + $t('comm_colon')">
          <Input v-model="editForm.groupName" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_level') + $t('comm_colon')">
          <Input v-model="editForm.maintainLevel" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_rule') + $t('comm_colon')">
          <Input v-model="editForm.rule" disabled></Input>
        </FormItem>
        <FormItem :label="$t('comm_dial_protocol') + $t('comm_colon')">
          <Input v-model="editForm.protocol" disabled></Input>
        </FormItem>
        <!-- 当拨测协议为icmp时，隐藏端口字段 -->
        <FormItem
          :label="$t('comm_port') + $t('comm_colon')"
          v-if="!(editForm.protocol == 'ICMP')"
        >
          <Input v-model="editForm.port" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_time') + $t('comm_colon')">
          <Input v-model="editForm.startTime" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_has_monitor') + $t('comm_colon')">
          <Input v-model="editForm.taskCode" disabled></Input>
        </FormItem>
        <FormItem
          :label="$t('discover_device_type') + $t('comm_colon')"
          prop="deviceType"
          :class="[editForm.protocol == 'ICMP' ? '' : 'width_100_item']"
        >
          <Select
            v-model="editForm.deviceType"
            filterable
            allow-create
            :only-filter-with-text="true"
            clearable
            :placeholder="$t('discover_select_device_type')"
            @on-create="handleDeviceTypeCreate"
          >
            <Option
              v-for="item in deviceList"
              :value="item.value"
              :key="item.value"
              >{{ item.lable }}</Option
            >
          </Select>
        </FormItem>
      </Form>
    </Modal>
    <!--任务列表-->
    <div v-if="showChangesRecordList" style="height: 1000%; width: 100%">
      <discoverChangesRecord
        @closeBtn="closeBtn"
        :changesRecordListParam="changesRecordListParam"
      ></discoverChangesRecord>
    </div>
  </section>
</template>
<script>
// 企业网络监测 / 自动发现 / 发现数据


/**
 * 时间转为秒
 * @param time 时间(00:00:00)
 * @returns {string} 时间戳（单位：秒）
 */
var time_to_sec = function (time) {
    var s = '';

    var hour = time.split(':')[0];
    var min = time.split(':')[1];
    var sec = time.split(':')[2] || 0;

    s = Number(hour * 3600) + Number(min * 60) + Number(sec);

    return s;
};
/**
 * 时间秒数格式化
 * @param s 时间戳（单位：秒）
 * @returns {*} 格式化后的时分秒
 */
var sec_to_time = function (s) {
    var t;
    if (s > -1) {
        var hour = Math.floor(s / 3600);
        var min = Math.floor(s / 60) % 60;
        // var sec = s % 60;
        if (hour < 10) {
            t = '0' + hour + ':';
        } else {
            t = hour + ':';
        }

        if (min < 10) {
            t += '0';
        }
        t += min;
        // if(sec < 10){t += "0";}
        // t += sec.toFixed(2);
    }
    return t;
};
import moment from 'moment';
import '@/config/page.js';
import global from '../../../common/global.js';
import { mapGetters, mapActions, mapState } from 'vuex';
import { addDraggable } from '@/common/drag.js';
import locationreload from '@/common/locationReload';
import langFn  from '@/common/mixins/langFn';
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import discoverChangesRecord from './discoverChangesRecord.vue';
import axios from "axios";

export default {
    name: 'discoverData',
      mixins: [langFn],
    components: {
      discoverChangesRecord,
      TreeSelect
    },
    props: {
        tabData: {
            type: String,
            default: ''
        }
    },
    watch: {
        // tabData: {
        //   handler(value) {
        //     if (this.pageData.list.length === 0 && value === "discoverData") {
        //       this.getRuleList();
        //       this.getDeviceList();
        //       this.getGroupList();
        //       this.getmaintainLevelList();
        //       this.queryClick(1);
        //     }
        //   },
        //   deep: true,
        //   immediate: true,
        // },
    },
    created() {
        this.getmaintainLevelList();
        this.$nextTick(() => {
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
        })
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        moment.locale('zh-cn');
        this.getRuleList();
        this.getDeviceList();
        this.getGroupList();
        this.queryClick(1);
    },
    data() {
        let _this = this;
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            ids:'',
            createType:0,
            //是否监测
            // isMonitor: false,
            isMonitor: '',
            //是否识别
            identifyStatus:'',
            //是否变化
            changeStatus:'',
            monitorStatisList: [
                { value: 0, lable: this.$t('device_discovery_not_monitored')},
                { value: 1, lable: this.$t('device_discovery_monitored') }
            ],
            identifyStatisList: [
                { value: 0, lable: this.$t('net_data_discover_identify_no')},
                { value: 1, lable: this.$t('net_data_discover_identify_yes') }
            ],
            changeStatisList: [
                { value: 0, lable: this.$t('net_data_discover_change_no')},
                { value: 1, lable: this.$t('net_data_discover_change_yes') }
            ],
            orgId: "",
            treeValue:'',
            //机构参数
            treeData: [],
            orgTree: false,
            orgLists: [],
            showChangesRecordList: false, //控制变化列表弹窗显示
            changesRecordListParam: {
                netDataDiscoverId: '',
            },
            pageLoading: true,
            isdarkSkin: top.window.isdarkSkin,
            permissionObj: {},
            timeRange: [],
            timeOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return [start.format("yyyy-MM-dd HH:mm:ss"), end.format("yyyy-MM-dd HH:mm:ss")];
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate(),
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
                        }
                    }
                ]
            },
            //当前时间
            currentTime: Date.now(),
            /* 参数列表 */
            //时间
            startTime: '',
            endTime: '',
            //规则
            ruleId: null,
            //规则列表
            ruleList: [],
            //是否监测
            // isMonitor: false,
            //设备类型
            deviceType: null,
            deviceList: [],
            //关键字
            keyWord: '',
            //分页参数
            page: {
                pageNo: 1,
                pageSize: 10,
                pageSizeOpts: [10, 50, 100, 200, 500, 1000],
                pageLoading: false
            },
            /** 列表数据 */
            pageData: {
                total: 0,
                list: []
            },
            /** 列表选中数据 */
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: new Set(),
            selectedDatas: [],
            /** 新建表单 */
            addForm: {
                groupId: '',
                maintainLevel: '',
                startDate: '',
                endDate: '',
                timeList: [{ start: '0000', end: '2359' }],
                repeatWeek: []
            },
            //新建动画
            addLoading: true,
            addIds: '',
            //分组数据列表
            groupList: [],
            //运维等级数据列表
            maintainList: [],
            //新建弹框
            addOpen: false,
            //新建表单验证规则
            addFormRule: {
                // groupId: [
                //     {
                //         required: true,
                //         type: 'number',
                //         message: '请选择分组',
                //         trigger: 'change'
                //     }
                // ],
                maintainLevel: [
                    {
                        required: true,
                        type: 'string',
                        message: this.$t('discover_select_maintain_level'),
                        trigger: 'change'
                    }
                ],
                startTime: [{ required: true, type: 'date', message: this.$t('comm_select_start_time'), trigger: 'change', pattern: /.+/ }],
                endTime: [
                    {
                        required: true,
                        type: 'date',
                        message: this.$t('comm_select_end_time'),
                        trigger: 'change',
                        pattern: /.+/
                    }
                ],
                timeList: [{ required: true, type: 'array', message: this.$t('discover_date_lang'), trigger: 'change' }],
                repeatWeek: [
                    {
                        required: true,
                        type: 'array',
                        message: this.$t('comm_select_cycle'),
                        trigger: 'change'
                    }
                ]
            },
            /** 修改表单 */
            editForm: {
                id: null,
                ip: '',
                groupId: null,
                groupName: '',
                maintainLevel: null,
                rule: null,
                protocol: null,
                port: '',
                startTime: null,
                taskCode: null,
                deviceType: ''
            },
            //修改验证规则
            editFormRule: {
                deviceType: [{ required: true, message: this.$t('discover_select_device_type'), trigger: 'change' }]
            },
            //修改弹框
            editOpen: false,
            //修改加载
            editLoading: true,
            /** 表格标题列 */
            tableColumn: [
                { type: 'selection', width: 30, className: 'bgColor', align: 'center' },
                {
                    title: this.$t('discover_data_ip'), align: 'left', minWidth: 200, key: 'ip',
                    render: (h, param) => {
                        let str = param.row.showIp || '--';
                        // let IP = param.row.showLastDeviceIp || '--'
                        // return h('span', ` ${str}(${IP})`);
                        return h('span',` ${str}`);
                    }
                },
                { title: this.$t('comm_org'), align: "left", minWidth: 100, key: "orgName", tooltip: true },
                // 采集器,
                { title: this.$t('discover_gether_name'), align: 'left', minWidth: this.getColumnWidth(100,100), key: 'getherName', tooltip: true },
                {
                    title: this.$t('comm_group'),
                    align: 'left',
                    minWidth: 150,
                    key: 'groupName',
                    render: (h, param) => {
                        let str = param.row.groupName;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        let str1 = str;
                        if (str.length > 9) {
                            str1 = str.substring(0, 9) + '...';
                        }
                        return h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'top-end',
                                    transfer: true
                                },
                            },
                            [
                                str1,
                                h(
                                    'span',
                                    {
                                        slot: 'content', //slot属性
                                        style: {
                                            whiteSpace: 'normal',
                                            wordBreak: 'break-all'
                                        }
                                    },
                                    str
                                )
                            ]
                        );
                    }
                },
                // 等级
                {
                    title: this.$t('discover_level'),
                    align: 'left',
                    width:this.getColumnWidth(80,90),
                    key: 'maintainLevel',
                    render: (h, param) => {
                        let str = param.row.maintainLevel;
                        var items = this.maintainList.filter((item) => {
                            return item.value == str;
                        });

                        if (items.length > 0) {
                            str = items[0].lable;
                        } else {
                            str = '--';
                        }


                        return h('span', str);
                    }
                },
                // 发现规则
                {
                    title: this.$t('discover_rule'),
                    align: 'left',
                    minWidth: 200,
                    key: 'name',
                    // tooltip: true
                },
                // 拨测协议
                {
                    title: this.$t('comm_dial_protocol'),
                    align: 'left',
                    minWidth: this.getColumnWidth(90,100),
                    key: 'protocol',
                    render: (h, param) => {
                        return h('span', param.row.protocol == 4 ? 'UDP' : param.row.protocol == 5 ? 'ICMP' : param.row.protocol == 7 ? 'TCP' : '--');
                    }
                },
                {
                    title: this.$t('comm_port'),
                    align: 'left',
                    width:  this.getColumnWidth(60,60),
                    key: 'port',
                    render: (h, param) => {
                        // 拨测协议为icmp时，端口显示为--
                        return h('span', param.row.protocol == 5 ? '--' : param.row.port);
                    }
                },
                // 发现时间
                { title: this.$t('discover_data_discovery_time'), align: 'left', width: 150, key: 'createTime',tooltip:true },
                { title: this.$t('net_data_discover_change'), align: 'left', width: 180, key: 'latestChanges',
                    render: (h, params) => {
                        let str = params.row.latestChanges;
                        return h(
                            'a',
                            {
                                class: 'action-btn action-blue',
                                 style: { 
                                         
                                          color: this.currentSkin == 1 ? "#05EEFF" : "#0290FD",
                                          whiteSpace: "nowrap",
                                          overflow: "hidden", 
                                          textOverflow: "ellipsis",
                                          display: "block",
                                         
                                      },
                                on: {
                                    click: () => {
                                        this.changesRecordList(params.row.id);
                                    }
                                }
                            },
                            str === undefined || str === null || str === '' ? '--' : str
                        );
                    }
                },
                {
                    title: this.$t('comm_collected_or_not'),
                    align: 'left',
                    minWidth: 160,
                    key: 'taskCode',
                    slot: 'taskCode'
                    // render: (h, param) => {
                    //     let arr = [h('p', param.row.taskCode ? param.row.taskCode : this.$t('device_discovery_not_monitored'))];
                    //     if (param.row.failInfo) {
                    //         arr.push(
                    //             h(
                    //                 'span',
                    //                 {
                    //                     style: {
                    //                         color: 'red'
                    //                     }
                    //                 },
                    //                 '!'+param.row.failInfo
                    //             )
                    //         );
                    //         arr.push(h('span', param.row.failInfo));
                    //     }
                    //     return arr;
                    // }
                },
                {
                    title: this.$t('discover_device_type'),
                    align: 'left',
                    minWidth: 120,
                    key: 'deviceName',
                    render: (h, param) => {
                        let str = param.row.deviceName || '--';
                        return h('span', str);
                    }
                },
                { title: this.$t('common_controls'), align: 'center', width: 160, slot: 'action' }
            ],
            molStartOpt: {
                disabledDate: date => {
                    if (_this.addForm.endDate) {
                        let end = new Date(_this.addForm.endDate);
                        return date > end;
                    } else {
                        return date > Date.now();
                    }
                }
            },
            molEndOpt: {
                disabledDate(date) {
                    if (_this.addForm.startDate) {
                        let end = new Date(_this.addForm.startDate);
                        return date < end;
                    } else {
                        return date > Date.now();
                    }
                }
            }
        };
    },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
          // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        // this.$Skin.skinChange(top.window.skin);
        top.window.addEventListener('message', e => {
            if (e) {
                if (e.data.type == 'msg') {
                    return;
                } else if (typeof e.data == 'object') {
                    this.isdarkSkin = e.data.isdarkSkin;
                    this.$Skin.skinChange(top.window.skin);
                } else if (typeof e.data == 'number') {
                    this.isdarkSkin = e.data;
                    this.$Skin.skinChange(top.window.skin);
                }
            }
        });
         // 30秒定时更新数据
        this.timerInterval = setInterval(() => {
        let pageNo = this.page.pageNo|| 1;
        this.queryClick(pageNo);
        }, 1000 * 30);
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
    beforeDestroy() {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
    },
    methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
      //导出
    exportClick() {
      if (this.pageData.list.length > 0) {
        this.$Loading.start();
        this.keyWord = this.keyWord.trim();
        //设置查询参数
        let start = '';
        let end = '';
        if (this.timeRange[0]) {
            start = moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
        }
        if (this.timeRange[1]) {
            end = moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
        }
        let param = {
            startDate: start,
            endDate: end,
            ruleId: this.ruleId,
            taskCode: this.isMonitor,
            deviceType: this.deviceType,
            keyword: this.keyWord,
            identifyStatus: this.identifyStatus,
            changeStatus: this.changeStatus,
            orgId:this.orgId
        };
        axios({
          url: "/netDiscoverData/excelExport",
          method: "get",
          params: param,
          responseType: "blob", // 服务器返回的数据类型
          //data: {}
        }).then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.$t('net_data_discover_export_data') + ".xls");
          } else {
            var fileName = "";
            fileName = this.$t('net_data_discover_export_data') + ".xlsx";

            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        }).catch(error => {
          console.log(error);
          this.$Loading.finish();
        }).finally(() => {
          this.$Loading.finish();
        });
      } else {
        this.$Message.warning(this.$t('comm_no_data'));
      }
      },
      
        moreBtnClick(val){
        eval(`this.${val}()`)
        },
        focusFn() {
        this.getTreeOrg()
        },
        getTreeOrg(param = null) {
        let _self = this;
        _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
            if (res.code === 1) {
            let treeNodeList = res.data.map((item, index) => {
                item.title = item.name;
                item.id = item.id;
                // item.expand=false;
                item.loading = false;
                item.children = [];
                if (index === 0) {
                // item.expand=true;
                }
                return item
            });
            _self.treeData = treeNodeList;
            }
        })
        },
        loadData(item, callback) {
        this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
            if (res.code === 1) {
            let childrenOrgList = res.data.map((item, index) => {
                item.title = item.name;
                item.id = item.id;
                item.loading = false;
                item.children = [];
                return item
            });
            callback(childrenOrgList);
            }
        })
        },
        setOrg(item) {
        console.log(item);
        this.treeValue = item[0].name
        this.orgId = item[0] ? item[0].id : null;
        },
        onClear(){
        this.orgId = ''
        this.treeValue = ''
        },
        //查询事件
        queryClick(pageNo) {
            this.keyWord = this.keyWord.trim();
            //初始化页码
            this.page.pageNo = pageNo;
            //设置查询参数
            let start = '',
                end = '';
            if (this.timeRange[0]) {
                start = moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
            }
            if (this.timeRange[1]) {
                end = moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
            }
            const queryParam = {
                startDate: start,
                endDate: end,
                ruleId: this.ruleId,
                // taskCode: this.isMonitor ? 1 : 0,
                taskCode: this.isMonitor,
                deviceType: this.deviceType,
                keyword: this.keyWord,
                pageNo: this.page.pageNo,
                pageSize: this.page.pageSize,
                identifyStatus: this.identifyStatus,
                changeStatus: this.changeStatus,
                orgId:this.orgId
            };
            //打开加载动画
            this.pageLoading = true;
            //请求数据
            this.getTableList(queryParam);
        },
          setTableSelection(data) {
            // conaole.log(this.selectedDatas,'表格选中数据')
            console.log(this.pageData.list,'表格数据')
            console.log(this.selectedIds, Array.from(this.selectedIds),'选中数据')
        const selectedIds = Array.from(this.selectedIds);
        console.log(selectedIds,'selectedIds')
        if(selectedIds.length > 0){
          let arr = []
         this.pageData.list.forEach(item => {
          selectedIds.forEach(element=>{
            if (item.id == element) {
              item['_checked']=true
            }
          })
         })

           
          				// return item
		
        }
  
            // this.pageData.list = data.map(item => ({
            //   ...item,
            //   _checked: selectedIdsSet.has(item.id)
            // }));
            
            console.log(this.pageData.list, '更新后的表格数据');
  },
        //获取列表数据
        getTableList(queryParam) {
            console.log('3333');
            this.$http
                .wisdomPost('/netDiscoverData/list', queryParam)
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        if (data) {
                            data.records.forEach(item => {
                                item.name = item.name || '--';
                            });
                            this.pageData.list = data.records;
                            this.pageData.total = data.total || 0;
                            this.setTableSelection()
                        } else {
                            this.setTableListEmpty(data.records);
                        }
                    } else {
                        this.setTableListEmpty();
                        this.$Message.warning({ content: msg, background: true });
                    }
                })
                .catch(error => {
                    this.setTableListEmpty();
                })
                .finally(() => {
                    this.pageLoading = false;
                });
        },
        //设置列表数据为空列表
        setTableListEmpty() {
            this.pageData.list = [];
            this.pageData.total = 0;
        },
        /** 新建 */
        //打开新建表单
        openAddForm(type, data) {
            console.log(data);
            if (type === 'plural') {
                //多选
                const idArr = Array.from(data);
                if (idArr.length < 1) {
                    this.$Message.warning({ content: this.$t('phytopo_select_data'), background: true });
                    return;
                } else {
                    this.addIds = idArr.join(',');
                }
            } else if (type === 'singular') {
                //单选
                this.addIds = data.id;
            }
            this.addOpen = true;
            this.$nextTick(() => {
                this.$refs['addForm'].resetFields();
                this.addForm = {
                    groupId: data.groupId ? Number(data.groupId) : '',
                    maintainLevel: String(data.maintainLevel || ''),
                    startDate: new Date(),
                    endDate: new Date('2099-01-01'),
                    timeList: [{ start: '0000', end: '2359' }],
                    repeatWeek: [1, 2, 3, 4, 5, 6, 7]
                };
            });
        },
        
        //打开新建表单
        batchOpenAddForm() {
            let selectedDatas = Array.from(this.selectedDatas);
            if (selectedDatas.length <= 0) {
                this.$Message.warning({ content: this.$t('net_data_discover_select_data'), background: true });
                return;
            } 
            let idsArr = [];
            let errorIdsArr = [];
            selectedDatas.forEach(item=>{
                if (item.taskCode) {
                    errorIdsArr.push(item.id);
                }
                idsArr.push(item.id);
            });
            if (errorIdsArr.length > 0) {
                this.$Message.warning({ content: this.$t('net_data_discover_select_data'), background: true });
                return;
            }
            this.ids = Array.from(idsArr).join(',');
            this.createType = 1;
            this.addOpen = true;
            this.$nextTick(() => {
                this.$refs['addForm'].resetFields();
                this.addForm = {
                    groupId:'',
                    maintainLevel:'',
                    startDate: new Date(),
                    endDate: new Date('2099-01-01'),
                    timeList: [{ start: '0000', end: '2359' }],
                    repeatWeek: [1, 2, 3, 4, 5, 6, 7]
                };
            });
        },
        //新建请求接口
        addSubmit() {
            this.$refs['addForm'].validate(valid => {
                if (valid) {
                    //时间段验证
                    const ruleTimeFlag = this.ruleTimes(this.addForm.timeList);
                    if (ruleTimeFlag === false) {
                        this.$Message.warning({content:this.$t('comm_tip3'), background:true});
                        //取消按钮加载效果
                        this.addLoading = false;
                        this.$nextTick(() => {
                            this.addLoading = true;
                        });
                        return;
                    } else if (ruleTimeFlag) {
                        console.log(this.addForm);
                        // return false
                        let addParam ={};
                        let url = '/netDiscoverData/createTask';
                        if(1 == this.createType){
                            url = '/netDiscoverData/batchCreateTask';
                            addParam = {
                            ids: this.ids,
                            groupId: this.addForm.groupId,
                            maintainLevel: this.addForm.maintainLevel,
                            startDate: new Date(this.addForm.startDate).format('yyyyMMdd'),
                            endDate: new Date(this.addForm.endDate).format('yyyyMMdd'),
                            repeatWeek: this.addForm.repeatWeek.join(','),
                            timeSlot: this.addForm.timeList
                                .map(item => {
                                    return item.start.replace(':', '') + '-' + item.end.replace(':', '');
                                })
                                .join(',')
                            };
                        }else{
                            addParam = {
                            id: this.addIds,
                            groupId: this.addForm.groupId,
                            maintainLevel: this.addForm.maintainLevel,
                            startDate: new Date(this.addForm.startDate).format('yyyyMMdd'),
                            endDate: new Date(this.addForm.endDate).format('yyyyMMdd'),
                            repeatWeek: this.addForm.repeatWeek.join(','),
                            timeSlot: this.addForm.timeList
                                .map(item => {
                                    return item.start.replace(':', '') + '-' + item.end.replace(':', '');
                                })
                                .join(',')
                            };
                        }
                        console.log(addParam);
                        this.$http
                            .wisdomPost(url, addParam)
                            .then(({ code, data, msg }) => {
                                if (code === 1) {
                                    this.$Message.success({content:this.$t('message_add_success'), background:true});
                                    this.addOpen = false;
                                } else {
                                    this.$Message.warning({content:msg, background:true});
                                }
                            })
                            .catch(err => {
                                throw new Error(err);
                            })
                            .finally(() => {
                                //取消按钮加载效果
                                this.addLoading = false;
                                this.$nextTick(() => {
                                    this.addLoading = true;
                                });
                                this.queryClick(1);
                            });
                    }
                } else {
                    //取消按钮加载效果
                    this.addLoading = false;
                    this.$nextTick(() => {
                        this.addLoading = true;
                    });
                }
            });
        },
        // moreBtnClick(val) {
        //     eval(`this.${val}()`)
        // },
        addClick() {
            console.log('add===');
            let selectedIds = Array.from(this.selectedIds);
            let ids = "";
            for (let i = 0; i < selectedIds.length; i++) {
                if (i === 0) {
                    ids += selectedIds[i];
                } else {
                    ids += "," + selectedIds[i];
                }
            }
            this.addIds = ids;
            if (this.addIds.length == 0) {
                this.$Message.warning(this.$t('device_discovery_error_select'));
                return;
            }
            this.addOpen = true;
            this.$nextTick(() => {
                this.$refs['addForm'].resetFields();
                this.addForm = {
                    groupId: '',
                    maintainLevel: '',
                    startDate: new Date('2021-04-14'),
                    endDate: new Date('2099-01-01'),
                    timeList: [{ start: '0000', end: '2359' }],
                    repeatWeek: [1, 2, 3, 4, 5, 6, 7]
                };
            });
        },

        /** 编辑 */
        //打开编辑表单
        openEditForm(param) {
            this.$refs['addForm'].resetFields();
            this.$http
                .wisdomPost('/netDiscoverData/getDetail', { id: param.id })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.editForm.ip = data.showIp;
                        this.editForm.groupId = data.groupId;
                        this.editForm.groupName = data.groupName;
                        this.editForm.maintainLevel = data.maintainLevel == 1 ? this.$t('logback_first') : data.maintainLevel == 2 ? this.$t('logback_second') : data.maintainLevel == 3 ? this.$t('logback_tertiary') : '--';
                        this.editForm.rule = data.name;
                        this.editForm.protocol = data.protocol == 4 ? 'UDP' : data.protocol == 5 ? 'ICMP' : data.protocol == 7 ? 'TCP' : '--';
                        this.editForm.port = data.port || '--';
                        this.editForm.startTime = data.createTime;
                        this.editForm.taskCode = data.taskCode ?? this.$t('device_discovery_not_monitored');
                        this.editForm.deviceType = data.deviceType;
                        this.editForm.id = data.id;
                        this.editOpen = true;
                    } else {
                        this.$Message.warning({ content: msg, background: true });
                    }
                })
                .catch(error => {
                    throw new Error(error);
                });
        },
        //修改请求接口
        editSubmit() {
            this.$refs['editForm'].validate(valid => {
                if (valid) {
                    const editParam = {
                        id: this.editForm.id,
                        deviceType: this.editForm.deviceType
                    };
                    this.$http
                        .wisdomPost('/netDiscoverData/update', editParam)
                        .then(({ code, data, msg }) => {
                            // debugger
                            if (code === 1) {
                                this.$Message.success({ content: this.$t('comm_changed_successful'), background: true });
                                this.editOpen = false;
                            } else {
                                this.$Message.warning({ content: msg, background: true });
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        })
                        .finally(() => {
                            //取消按钮加载效果
                            this.editLoading = false;
                            this.$nextTick(() => {
                                this.editLoading = true;
                            });
                            this.queryClick(1);
                            this.getDeviceList();
                        });
                } else {
                    //取消按钮加载效果
                    this.editLoading = false;
                    this.$nextTick(() => {
                        this.editLoading = true;
                    });
                }
            });
        },
        /** 删除 */
        rowRemove(data) {
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: this.$t('server_sure_delete'),
                onOk: () => {
                    this.$http
                        .wisdomPost('/netDiscoverData/delete', { ids: data.id })
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                                this.queryClick(1);
                            } else {
                                this.$Message.warning({ content: msg, background: true });
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        });
                }
            });
        },
        /** 取消事件 */
        cancleForm(formObj) {
            this.$refs[formObj].resetFields();
        },

        /** 获取基础数据 */
        //获取规则数据
        getRuleList() {
            this.ruleList = [];
            this.$http
                .wisdomPost('/netDiscoverData/queryRuleDiscover')
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.ruleList = data ?? [];
                    } else {
                        this.ruleList = [];
                        this.$Message.warning({ content: msg, background: true });
                    }
                })
                .catch(err => {
                    this.ruleList = [];
                    throw new Error(err);
                });
        },
        handleDeviceTypeCreate(value){

            var itemArrays= this.deviceList.filter((item)=>{
                return item.lable == value
            });

            if(itemArrays.length > 0){
                return;
            }

            this.deviceList.push({
                isNew:true,
                value:value,
                lable:value
            });

        },
        //获取设备数据
        getDeviceList() {
            this.deviceList = [];
            // /dataTable/queryCode
            // 现在必须要 device_type 表数据
            this.$http
                .wisdomPost('/dataTable/queryDeviceType', { key: 'netDeviceType' })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.deviceList = data ?? [];
                    } else {
                        this.deviceList = [];
                        this.$Message.warning({ content: msg, background: true });
                    }
                })
                .catch(err => {
                    this.deviceList = [];
                    throw new Error(err);
                });
        },
        //获取分组数据
        getGroupList() {
            this.groupList = [];
            this.$http
                .wisdomPost('/group/list', { pageNo: 1, pageSize: 10000 })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        console.log('-------------------------------');
                        console.log(data.records);
                        this.groupList = data ? data.records : [];
                    } else {
                        this.groupList = [];
                        this.$Message.warning({ content: msg, background: true });
                    }
                })
                .catch(err => {
                    this.groupList = [];
                    throw new Error(err);
                });
        },
        //获取运维等级数据
        getmaintainLevelList() {
            this.maintainList = [];
            this.$http
                .wisdomPost('/dataTable/queryCode', { key: 'maintainLevel' })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.maintainList = data ?? [];
                    } else {
                        this.maintainList = [];
                        this.$Message.warning({ content: msg, background: true });
                    }
                })
                .catch(err => {
                    this.maintainList = [];
                    throw new Error(err);
                });
        },

        //数据列表页码切换
        pageChange(pageNo) {
            this.queryClick(pageNo);
        },
        //数据列表页码大小改变
        pageSizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.queryClick(this.page.pageNo);
        },
        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
            if (slection.length === 0) {
                let data = this.$refs.tableList.data;
                data.forEach(item => {
                    if (this.selectedIds.has(item.id)) {
                        this.selectedIds.delete(item.id);
                        this.selectedDatas.splice(
                            this.selectedDatas.findIndex(current => item.id === current.id),
                            1
                        );
                        // this.selectedDatas.remove(JSON.stringify(item));
                    }
                });
                console.log(this.selectedDatas);
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                    this.selectedDatas.push(item);
                });
            }
        },
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
            this.selectedDatas.push(row);
        },
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
            this.selectedDatas.splice(
                this.selectedDatas.findIndex(item => item.id === row.id),
                1
            );
        },
        changesRecordList(netDataDiscoverId) {
            this.showChangesRecordList = true;
            this.changesRecordListParam.netDataDiscoverId = netDataDiscoverId;
        },
        closeBtn(v) {
            this.showChangesRecordList = false;
        },

        /** 新建时时间选择控制 */
        playStartTimeChange(time, index) {
            const endTime = this.addForm.timeList[index].end;
            if (time != undefined && time != null && time != '') {
                const endTimes = time_to_sec(endTime);
                const startTimes = time_to_sec(time);
                if (endTime == undefined || endTimes < startTimes) {
                    this.addForm.timeList[index].end = sec_to_time(startTimes);
                }
            }
        },
        playEndTimeChange(time, index) {
            const startTime = this.addForm.timeList[index].start;
            if (time != undefined && time != null && time != '') {
                const endTimes = time_to_sec(time);
                const startTimes = time_to_sec(startTime);
                if (startTime == undefined || endTimes < startTimes) {
                    this.addForm.timeList[index].start = sec_to_time(endTimes);
                }
            }
        },
        //时间段改变事件
        //时间段操作（增加，删除）

        speedTimeChangeRemove(index) {
            const length = this.addForm.timeList.length;
            if (length === 1) {
                this.$Message.warning({ content: this.$t('phytopo_add_time'), background: true });
                return
            }
            this.addForm.timeList.splice(index, 1)
        },
        speedTimeChangeAdd(index) {
            const length = this.addForm.timeList.length;
            if (index === length - 1) {//增加
                if (length >= 10) {
                    this.$Message.warning({ content: this.$t('phytopo_10_time'), background: true });
                    return
                } else {
                    this.addForm.timeList.push({ start: "0000", end: "2359" })
                }
            }
        },
        // taskTimeChange(index) {
        //     const length = this.addForm.timeList.length;
        //     if (index === length - 1) {
        //         //增加
        //         if (length >= 10) {
        //             this.$Message.warning({content:this.$t('phytopo_10_time'), background:true});
        //             return;
        //         } else {
        //             this.addForm.timeList.push({ start: '0000', end: '2359' });
        //         }
        //     } else {
        //         //删除
        //         this.addForm.timeList.splice(index, 1);
        //     }
        // },
        //时间段校验是否存在交叉
        ruleTimes(times) {
            const timeArray = times;
            //结果返回值
            let timesFlag = true;
            //将每一项换算成分钟数
            let timeMinArray = timeArray.map(item => {
                let start = 0,
                    end = 0;
                start = Number(item.start.split(':')[0]) * 60 + Number(item.start.split(':')[1]);
                end = Number(item.end.split(':')[0]) * 60 + Number(item.end.split(':')[1]);
                return [start, end];
            });
            for (let i = 0; i < timeMinArray.length - 1; i++) {
                for (let j = i + 1; j < timeMinArray.length; j++) {
                    if (timeMinArray[j][1] <= timeMinArray[i][0] || timeMinArray[j][0] >= timeMinArray[i][1]) {
                        timesFlag = true;
                    } else {
                        timesFlag = false;
                        break;
                    }
                }
                if (timesFlag === false) {
                    break;
                }
            }
            return timesFlag;
        }
    },
    destroyed() { }
};
</script>

<style lang='less'></style>
<style scoped lang="less">
.toop {
  color: red;
  font-size: 16px;
  cursor: pointer;
  padding-left: 5px;
  vertical-align: middle;
}

.sectionBox .section-top .fn_item .fn_item_label {
  min-width: 140px;
}

.ivu-select {
  width: 120%;
}

.sectionBox .section-top .fn_item > label {
  min-width: 160px;
}

// /deep/
// .sectionBox .section-top {
//     height: auto;
//     border-bottom: 1px solid var(--border_color, #dddddd);
//     margin: -18px -18px 0 -18px;
//     padding: 18px;
//     background: red;
// }
</style>
