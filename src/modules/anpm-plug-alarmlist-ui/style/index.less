//@import "../../../style/custom/index.less";
//@import "../../../style/default.less";
//@import "../../../style/custom.less";
//@import "../../../style/modal.less";
//@import "../../../style/topology.less";
//@import "../../../style/pageCss/echartLook.less";
//@import "../../../style/pageCss/wisdomFault.less";
@import "../../../style/skin.less";
@import "../../../style/custom/common";
@import "../../../style/custom/lightTabs.less";
@import "../../../style/table.less";

.query-btn{
    width: 60px !important;
    height: 38px !important;
    // background: linear-gradient(357deg, #049DEC 0%, #05EBEB 100%)  !important;
    background: linear-gradient(357deg, var(--button_background_two_color, #049DEC) 0%, var(--button_background_color, #05EBEB) 100%)  !important;
    border-radius: 4px !important;
    font-size: 23px !important;
    // color:#151106 !important;
    color:var(--button_background_three_color,#151106) !important;
  }
  .query-btn:hover{
    // background: linear-gradient(357deg, #049DEC 0%, #05EBEB 69%)  !important;
    background: linear-gradient(357deg,var(--query_btn_hover_bg_1_color,#049DEC) 0%,var(--query_btn_hover_bg_2_color,#05EBEB) 69%)  !important;
  }
  .query-btn:active{
    // opacity: 0.8 !important;
    background: linear-gradient(357deg,var(--query_btn_active_bg_1_color,rgba(4, 157, 236, 0.8)) 0%,var(--query_btn_active_bg_2_color,rgba(5, 235, 235, 0.8)) 69%)  !important;

  }
  .reset-btn{
    width: 60px !important;
    height: 38px !important;
    border: 1px solid transparent !important;
    border-radius: 4px ;
    background-clip: padding-box, border-box !important;
    background-origin: padding-box, border-box !important;
    // background-image: linear-gradient(to right,  var(--input_b_color,#ffffff),  var(--input_b_color,#ffffff)), linear-gradient(31deg, #015197 0%, #31F0FE 51%, #015197 100%) !important;
    background-image: linear-gradient(to right,  var(--input_b_color,#ffffff),  var(--input_b_color,#ffffff)), linear-gradient(31deg, var(--reset_export_button_border_1_color,#015197) 0%, var(--reset_export_button_border_2_color,#31F0FE) 51%, var(--reset_export_button_border_3_color,#015197) 100%) !important;
    font-size: 23px !important;
    // color:#05EEFF !important;
    color:var(--reset_export_del_button_font_color,#05EEFF) !important;
  }
  .export-btn{
    width: 60px !important;
    height: 35px !important;
    border: 1px solid transparent !important;
    border-radius: 4px ;
    background-clip: padding-box, border-box !important;
    background-origin: padding-box, border-box !important;
    // background-image: linear-gradient(to right,  var(--input_b_color,#ffffff),  var(--input_b_color,#ffffff)), linear-gradient(31deg, #015197 0%, #31F0FE 51%, #015197 100%) !important;
    background-image: linear-gradient(to right,  var(--input_b_color,#ffffff),  var(--input_b_color,#ffffff)), linear-gradient(31deg, var(--reset_export_button_border_1_color,#015197) 0%, var(--reset_export_button_border_2_color,#31F0FE) 51%, var(--reset_export_button_border_3_color,#015197) 100%) !important;
    font-size: 23px !important;
    // color:#05EEFF !important;
    color:var(--reset_export_del_button_font_color,#05EEFF) !important;
  }
  .del-btn{
    width: 60px !important;
    height: 35px !important;
    border: 1px solid transparent !important;
    border-radius: 4px ;
    background-clip: padding-box, border-box !important;
    background-origin: padding-box, border-box !important;
    // background-image: linear-gradient(to right, var(--input_b_color, #ffffff), var(--input_b_color, #ffffff)), linear-gradient(37deg, #8a5505 0%, #b77006 51%, #fea31b 100%) !important;
    background-image: linear-gradient(to right,var(--button_networdFault_del_bg_2_color, #ffffff), var(--button_networdFault_del_bg_2_color, #ffffff)), linear-gradient(37deg, var(--inputbutton_networdFault_del_bg_color_b_color,#b77006) 0%, var(--button_networdFault_del_bg_3_color,#b77006) 51%, var(--button_networdFault_del_bg_4_color,#fea31b) 100%) !important;
    font-size: 23px !important;
    color:var(--del_button_font_color,#fea31b) !important;
  }
  .ivu-tree ul li  {
    text-align: left !important;
  }