/**
 * jtopo工具栏
 */

import global from "../../../common/global.js";
import $ from "../../../common/jtopo/jquery.min.js";
// 页面工具栏 type0新的，1老的
export function getJtopoNodeImgByValue(value,type){
    //图片base64
    var hostImg = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAAGFBMVEX////h6f6kuvxYg/9oj/9chv/P3P+ovv/83vANAAAAXklEQVRIx2NQIgyIU8NACIyqGVVDPzVszsaowCQBQw2LMTpwwFDDjKHGAIsaQVQwqmZUzaBVg5GeicsXhPMXjfIye7AxJjAtQFHDaowNBBBjziAvx0bVjDA11GmPAQD2aGar+zvXoQAAAABJRU5ErkJggg==';
    var routeImg = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAMAAABG8BK2AAAAflBMVEX////h6f6kuvw/cP+Nqv+et/9FdP/X4v7K1/+Zs/9Yg/9chv94mv9Ccv+xxf5Ne//c5f7I1v/C0f+9zv/S3v5pj//a5P6PrP9iiv9Id//L2f7G1P5yl/9gif9KeP/3+f/l6/9Rfv+qwP6Hpv97nf+5y/+Bof+AoP/O2/5tk//uZYheAAACaklEQVRYw+2Y6ZKqMBCFp4clhDWAuG8ILvP+L3gPCVQsrmLwzli3pjy/JCSfSXenu/Xj81v0fZiPf9Yb88b8eszO43WQuG4S1NzbPYnxzq51JffsPYHJ5tZfmmcjMfu1XLex45QJwdLY3siB9X4MJt9iyWK2pCstZwsMbnNzjIP5kc2oJ2ZHeOGYYmaY7K/ohlY+XtlmGI6p4ZRuahriJTfB5Jh4FnRH4oTX+WPMHtYN6Z6wyRDG3z/EwNMBJid+UdtOnHXLD7wK52Vk42MAvz/CZPBRY13YEooO1KqypHhjZ/gre4CZwxMEFXKVoxh6QGJtxPMwxsPBGUG1XOVPOopjSeUEMcShN4iBH2akvlFzNMW6yCcE1mkIs8OdXrbrNEc/RULdC9z33QAGZ9qQVBw5fsfpKE5VktIGpxrAcGVgKDvQRHEKmiw6e8PC3ZH5AAahFVMnxSk9oqMrKVoxQnQAg8hK6ZojKZIDilaKGB3AJJbF6JpTgCI5Dl2LWVYygMHmBRlIwFU/j+kfin3l7ekq0T+UuYnZ3HKPra3XomdiY4ezphq4GLg0nv8SPYcbhd8nTbA3qBSkPhRTOqY6/Mwug8VVECcronyrOLEb68sw6mqWqdwbOFKOvppjEkWpoLRK1LPdJYrzmLRVXqhVqjh1l7bSMUm00s7hyjw6iY5I6UC2OkQq++iUblRggiKs+CFfdpgcFaYu/MSowED7xVC5I13uDIrv6T5FFt/XtAK6MQluNyZBY/YXtkm6adv2mzY5eHy2hSQybyHNG9rXt9e62Q+DJIqSIGya/d/xC+aNeWN+FvN//bf1B92qKuEPaDRkAAAAAElFTkSuQmCC';
    var serverImg = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAAGFBMVEX////h6f4/cP+kuvxSfv9ljP/I1v/H1v/UkOGnAAAAWklEQVRIx2MwJgyIU8NACIyqoa8aNiclTKCSgKKGRQkbcEBRw6QoiAmEFGilRkgJBAWVwKqDlJQCiVRDTzeTHIbsWOOiYDT9jKaf0fQzmn5G08+oGogaarXHANFniMCvamuQAAAAAElFTkSuQmCC';

    var routeSubsImg = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAMAAABG8BK2AAAATlBMVEX////u8Pbm6O6ps8/k5/Dr7fS1vtbS1+att9Ln6vK4wNiwudPHzuDDyt7L0eLM0uLh5e/AyNy7w9nX3Onb3+vz9fje4u3P1eXN0+Pp6/OGfNK+AAACJUlEQVRYw+2Y6ZYaIRCFhxRLA03vtjPv/6K5spp4pJmJxyRzvL/Ehs9apKr07cdD9DjM2x/rhXlhvj9GK2cskTVO6a9idN/RlbpefwHDDd3I8E9iTnM4N2xcQHwbAnU+fQYzdRcvRs6uxEf/5tSOUQQNgv0mMRCkWjHv2Cxhya24xKPxCFNsmb0ptxJzsaeOmbCxZ3fV4/F0jDkhkO4uBEY6xPl0iIHVEputXNyg1ikdX8/9bOzFTCHh8xGGw+ZLdCV5rcUXrzNe+i0HGINMM2jxpxTLWgoWeTd1jIbjPknOn5I6URR57d5DhE9XMbB9ZOETC6dQKCxH5LKK6eB2Plc4iqIClMPmGkbDa+a1kpKRkymqtyzIwK4KRoUAQ9PKdOAsTHcp3ilxI1YVDAK7saTAsXBy7zylaCNyFYwMoSkcT/GcSMnBkRWMJRLsmrNE6u4pWQJWVjCUMXUBQ4/CtDsllj1614uaU/UQC0PdFmM9i0qIqwn/METgxMwvojnhKl0pNin2gcOQFbFqgLPz8vVrvAzvkcLB7AJn7bZyGVqvZqYkDqTy1WwuFIUCjg3rIReK5rJVKBAPHJfLVnMR/bXRnEN4KkW0UtKBTCH3kqmkNzYYucz9ed2zUzs6jFukrTWY9nbHYrt7RvMto4C7NwpcMqieMJg0j0nPHtrKCDluXMAMvo1phHz6QNs+XrcP+/Iy7Eun9N//6fHCvDD/CeYf+m/rJ090HVpAR5JGAAAAAElFTkSuQmCC';
    var serverInvalidImg = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAAGFBMVEX////u8Pbm6fHm6O65wdiyu9SxutSps8/GVZiBAAAAW0lEQVR42mMwJgwYiFLDQAiMqqGvGqb0ckxQpoCihq0cG0hAUcNeKIgJxAtopUa8HAQFy8Gq3cvLHclUQzc3ExOGWOOiVGE0/Yymn9H0M5p+RtPPqBqYGuq0xwA4vfe0qDwT7AAAAABJRU5ErkJggg==';
    let imgtype = '';
    if(value == "route"){
        imgtype = (type==0?routeImg:routeSubsImg);
    }else if(value == "host"){
        imgtype = hostImg;
    }else if(value == "server"){
        imgtype = (type==0?serverImg:serverInvalidImg);
    }else {
        imgtype = routeImg;
    }
    return imgtype;
};

/**
 * 路由波动数据
 * @param row 获取数据
 * @returns {*[]} 结果
 */
export function rearrangementData(row,cf){

    let dataLists = [];
    //获取源设备与目的设备
    let sourceIp = null;
    let targetIp = null;
    console.log(row)
    const uniNo = row.uniNo;
    if (uniNo) {
        if (/^([\w|\.|\*]+):\d+/.test(uniNo)) {
            sourceIp = RegExp.$1;
        }
        if (/-([\w|\.|\*]+):\d+$/.test(uniNo)) {
            targetIp = RegExp.$1;
        }
    }
    if (!sourceIp || !targetIp) {
        const fault_desc = row.faultDesc;
        if (fault_desc) {
            if (/从\s*([\w|\.|\*]+)\s*/.test(fault_desc)) {
                sourceIp = RegExp.$1;
            }
            if (/到\s*([\w|\.|\*]+)\s*/.test(fault_desc)) {
                targetIp = RegExp.$1;
            }
        }
    }
    let errorIps = row.errorIps.split(','),errorIpsDetail = row.errorIpsDetail.split(','),isSame = false,sameIp="";
    if (!sourceIp) {
        // sourceIp = 'S-IP*';
        sourceIp = errorIpsDetail[0];
    }
    if (!targetIp) {
        // targetIp = 'E-IP*';
        // targetIp = errorIps[errorIps.length- 1]
        if (errorIps.slice(-1)[0]==errorIpsDetail.slice(-1)[0]){//判断旧、新路径最后一个是否相同
            isSame = true;
            targetIp = errorIpsDetail.slice(-1);//相同则设定目的节点（不相同则在各自路径添加最后一个节点）;
            sameIp = errorIpsDetail.slice(-1);
        }
    }
    var startEndNode = [];
        if (isSame){
            startEndNode = [{
                value: 'S-' + sourceIp,
                type : 1,
                name: sourceIp+'(源)',
                ip:sourceIp,
                tooltip: null,
                image: 'host',
                linkList: []
            },{
                value: 'E-' + targetIp,
                type : 4,
                name: targetIp+'(目的)',
                ip:targetIp,
                tooltip: null,
                image: 'server',
                linkList: []
            }]
    }else{
            startEndNode = [{
                value: 'S-' + sourceIp,
                type : 1,
                name: sourceIp+'(源)',
                ip:sourceIp,
                tooltip: null,
                image: 'host',
                linkList: []
            }]
        }


    dataLists.push(startEndNode);
    //获取老路径
    let oldSrc = row.errorIps.split( ',' );
    let oldarr = []
    for(var i=1; i<oldSrc.length-1; i++){//组装旧路径节点（排除第一个为源节点）
        var oldNode = {
            value: 'CD-' + oldSrc[i],
            type : 2,
            name: oldSrc[i],
            ip:oldSrc[i],
            tooltip: null,
            image: 'route'
        }
        oldarr.push(oldNode);
    }

    // oldarr.push(startEndNode[1]);
    // dataLists.push(oldarr);
    //获取新路径
    let newSrc = row.errorIpsDetail.split( ',' );
    let newarr = []
    for(var i=1; i<newSrc.length-1; i++){//组装新路径节点（排除第一个为源节点）

        var newNode = {
            value: 'LD-' + newSrc[i],
            type : 3,
            name: newSrc[i],
            ip:newSrc[i],
            tooltip: null,
            image: 'route'
        }
        newarr.push(newNode);
    }
    // newarr.push(startEndNode[1]);
    if (!isSame){//如果新旧路径最后一个节点不同则 把新旧路径的最后一个当做目的节点 各自拼接到新旧路径
        oldarr.push(
            {
                value: 'E-' + oldSrc.slice(-1)[0],
                type : 4,
                name: oldSrc.slice(-1)[0]+'(目的)',
                ip:oldSrc.slice(-1)[0],
                tooltip: null,
                image: 'server',
                linkList: []
            }
        );
        newarr.push(
            {
                value: 'E-' + newSrc.slice(-1)[0],
                type : 4,
                name: newSrc.slice(-1)[0]+'(目的)',
                ip:newSrc.slice(-1)[0],
                tooltip: null,
                image: 'server',
                linkList: []
            }
        )
    }
    dataLists.push(oldarr,newarr);
    return cf(startEndNode,oldarr,newarr);
}
