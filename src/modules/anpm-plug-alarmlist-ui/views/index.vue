<template>
  <div
    :class="currentSkin == 1 ? 'tabs-card dark-skin' : 'tabs-card light-skin'"
  >
    <Tabs
      id="aa"
      :type="currentSkin == 1 ? 'card' : 'line'"
      v-model="checkedTab"
      :class="{ 'tabs-card-content-black': currentSkin == 1 }"
    >
      <TabPane :label="$t('alarm_network_fault')" name="networkFault"
        ><network-fault
          v-if="checkedTab === 'networkFault'"
          :tabData="checkedTab"
        ></network-fault
      ></TabPane>
      <!-- <TabPane label="紧急通知" name="urgentNotice"><urgent-notice v-if="checkedTab==='urgentNotice'" :tabData="checkedTab"></urgent-notice></TabPane> -->
      <TabPane :label="$t('alarm_route_undulate')" name="routeUndulate"
        ><route-undulate
          v-if="checkedTab === 'routeUndulate'"
          :tabData="checkedTab"
        ></route-undulate
      ></TabPane>
      <TabPane :label="$t('alarm_special_congestion')" name="specialCongestion"
        ><special-congestion
          v-if="checkedTab === 'specialCongestion'"
          :tabData="checkedTab"
        ></special-congestion
      ></TabPane>
      <TabPane :label="$t('alarm_system_alarm')" name="systemAlarm"
        ><systemAlarm
          v-if="checkedTab === 'systemAlarm'"
          :tabData="checkedTab"
        ></systemAlarm
      ></TabPane>
      <!-- 端口告警  alarmlist_port_aram_title-->
      <TabPane :label="$t('alarmlist_port_aram_title')" name="portAlarm"
        ><portAlarm
          v-if="checkedTab === 'portAlarm'"
          :tabData="checkedTab"
        ></portAlarm
      ></TabPane>
      <!-- <TabPane :label="$t('legalResPool_switch_illegal_access_alarm')" name="illegalAccessAlarm"><illegalAccessAlarm v-if="checkedTab==='illegalAccessAlarm'" :tabData="checkedTab"></illegalAccessAlarm></TabPane> -->
      <!-- <TabPane label="专线拥塞" name="specialCongestion"><special-congestion v-if="checkedTab==='specialCongestion'" :tabData="checkedTab"></special-congestion></TabPane> -->
    </Tabs>
  </div>
</template>

<script>
import networkFault from './networkFault.vue'
import urgentNotice from './urgentNotice.vue'
import routeUndulate from './routeUndulate.vue'
import specialCongestion from './specialCongestion.vue'
import systemAlarm from './systemAlarm.vue'
import illegalAccessAlarm from './illegalAccessAlarm.vue'
import portAlarm from './portAlarm.vue'
export default {
  name: "index",
  components:{
    networkFault,
    urgentNotice,
    routeUndulate,
    specialCongestion,
    systemAlarm,
    illegalAccessAlarm,
    portAlarm
  },
  data(){
    return{
      currentSkin: sessionStorage.getItem('dark') || 1,
      checkedTab:"networkFault",
    }
  },
  created() {
    const src = window.frames.frameElement.getAttribute('src');
    if (src && src.indexOf('?checkedTab=')>-1){
      this.checkedTab = src.split('?checkedTab=')[1].replace(/^\s*|\s*$/g,"");
    }
  },
   mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  methods:{
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
  }
}
</script>

<style scoped lang='less'>
.tabs-card {
  height: 100%;
}
.ivu-tabs {
  padding-top: 20px;
  overflow-y: auto;
  height: calc(100% - 20px);
}
.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  margin-right: 8px;
}
/deep/ .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
  border-color: transparent !important;
}
/deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  border: 0;
}
</style>
<style>
.tabs-card .ivu-tabs-bar {
  margin-left: 20px;
  margin-right: 20px;
}
.tabs-card > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  /*border-radius: 0;*/
  background: #fff;
}
/* .tabs-card > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
  border-top: 1px solid #3399ff;
} */
.tabs-card
  > .ivu-tabs.ivu-tabs-card
  > .ivu-tabs-bar
  .ivu-tabs-tab-active:before {
  background: transparent;
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  position: absolute;
  top: 0;
  left: 0;
}
</style>