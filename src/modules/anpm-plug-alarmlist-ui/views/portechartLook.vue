<template>
  <div class="lookBox" style="margin-top: 0px">
    <div slot="header">
      <span class="title-name">{{ $t("alarm_congestion") }}</span>
      <span class="title-content">
        {{ specialDetails }}
      </span>
    </div>
    <div style="margin: 15px 0">
      <h4>{{ $t("alarm_index_trend") }}：</h4>
    </div>

    <div class="contain" style="position: relative">
      <div
        ref="spechealthy-delayLoss"
        id="spechealthy-delayLoss"
        class="echartStyle"
        :style="'height:' + height + 'px'"
      ></div>
      <!-- <div class="delayLoading loading-content" v-show="delayLoading">
        <Spin fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>{{$t('alarm_load')}}</div>
        </Spin>
      </div> -->
      <div class="flowLoading loading-content" v-show="flowLoading">
        <Spin fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>{{ $t("alarm_load") }}</div>
        </Spin>
      </div>
      <!-- <div class="rateLoading loading-content" v-show="rateLoading">
        <Spin fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>{{$t('alarm_load')}}</div>
        </Spin>
      </div> -->
    </div>
  </div>
</template>

<script>
const pointGreen =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC";
const pointRed =
  "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC";
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/dataZoom");
require("echarts/lib/component/markLine");
import eConfig from "@/config/echart.config.js";
import echartFn from "@/common/mixins/echartFun";
import { mapState, mapMutations } from "vuex";
export default {
  name: "echartLook",
  mixins: [echartFn],
  props: {
    chartData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    clickTime: {
      type: [String, Number],
      default: function () {
        return Math.random();
      },
    },
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      title: "",
      alarmType: "",
      // 0：Z端匹配，1：A端匹配
      matchingType:"",
      delayLoading: true,
      flowLoading: true,
      rateLoading: true,
      height: 300,
      echart1: {
        show: true,
      },
      echart2: {
        show: true,
      },
      echart3: {
        show: true,
      },
      query: {},
      query2: {},
      delayParam: {},
      flowParam: {},
      rateParam: {},
      markPoint: "",
      markPointGreen: "",
      specialDetails: "",
      startTime: 0,
      endTime: 0,
      preAlias: {
        specName: "",
        name: "",
        port: "",
      },
      zAlias: {
        name: "",
        port: "",
      },
      delayLossColor:["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "bps",
        出流速: "bps",
        入利用率: "%",
        出利用率: "%",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%",
      },
      flowColor: ["#0290FD", "#FEA31B", "#00FFEE", "#FE5C5C"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: [],
      },
      flowData: {
        enter: [],
        issue: [],
        inUse: [],
        outUse: [],
      },
      goodRateData: {
        nrUseRateList: [],
        nrGoodRateList: [],
        useRateList: [],
        goodRateList: [],
      },
      delayLossScale: true,
      startScale: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayEnd: 100,
      startValue: "",
      endValue: "",
      scale: "",
      hoverDelayTime: "",
      hoverFlowTime: "",
      hoverUseTime: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {},
      },
    };
  },
  watch: {
    chartData: {
      handler(val) {
        if (JSON.stringify(val) !== "{}") {
          this.delayParam = Object.assign({}, val.delayParam);
          this.flowParam = Object.assign({}, val.flowParam);
          this.rateParam = Object.assign({}, val.rateParam);
          this.startTime = new Date(this.delayParam.startTime).getTime();
          this.endTime = new Date(this.delayParam.endTime).getTime();
          this.markPoint = val.markPoint;
          this.markPointGreen = val.markPointGreen;
          this.specialDetails = val.specialDetails;
          this.title = val.title;
          this.preAlias.specName = val.specialName;
        }
      },
      deep: true,
    },
    clickTime: {
      handler(val) {
        console.log(val, "ssssssss");
        this.delayParam = Object.assign({}, this.chartData.delayParam);
        this.flowParam = Object.assign({}, this.chartData.flowParam);
        this.rateParam = Object.assign({}, this.chartData.rateParam);
        this.startTime = new Date(this.delayParam.startTime).getTime();
        this.endTime = new Date(this.delayParam.endTime).getTime();
      },
      deep: true,
    },
  },
  mounted() {
        // 设置颜色
    this.delayLossColor = [eConfig.legend.delayColor[this.currentSkin] ,eConfig.legend.lossColor[this.currentSkin] ];
    this.flowColor = [eConfig.legend.flowInColor[this.currentSkin] ,eConfig.legend.flowOutColor[this.currentSkin],
     eConfig.legend.flowInRateColor[this.currentSkin] ,eConfig.legend.flowOutRateColor[this.currentSkin]];
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
   beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  computed: {
    params() {
      const params = {
        level: null,
      };
      return Object.assign({}, this.delayParam, params);
    },
    params2() {
      const params = {
        level: null,
      };
      return Object.assign({}, this.flowParam, params);
    },
    params3() {
      const params = {
        level: null,
      };
      return Object.assign({}, this.rateParam, params);
    },
  },
  created() {

      let delayLossUnitTemp =  {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] =  "ms";
    // 丢包率
    delayLossUnitTemp[this.$t('comm_packet_loss')] =  "%";
    // 入流速
    delayLossUnitTemp[this.$t("dashboard_inlet_velocity")] =  "bps";
    // 出流速
    delayLossUnitTemp[this.$t("dashboard_outflow_velocity")] =  "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("view_Leased_type_6")] =  "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("view_Leased_type_8")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t('specquality_availability_exemption')] =  "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t('specquality_good_rate_exemption')] =  "%";
    // 可用率
    delayLossUnitTemp[this.$t('specquality_availability')] =  "%";
    // 优良率
    delayLossUnitTemp[this.$t('specquality_good_rate')] =  "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit,delayLossUnitTemp);

    this.$watch(
      () => {
        return JSON.stringify(this.params) + Math.random();
      },
      () => {
        this.height = 300;
        if (this.delayLossChart) {
          this.delayLossChart.clear();
          this.delayLossChart = null;
        }
        this.load();
      }
    );
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    async initDelayAndLostTrend() {
      let delayLoss = {};
      let delaytrendParam = JSON.parse(JSON.stringify(this.params));

      delaytrendParam.startTime = new Date(delaytrendParam.startTime).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      delaytrendParam.endTime = new Date(delaytrendParam.endTime).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      // 时延丢包
      await this.$http
        .PostJson("/trendData/getDelayAndLostTrend", delaytrendParam)
        .then((res) => {
          //时延丢包趋势数据
          if (res.code === 1) {
            delayLoss = res.data;
          }
        })
        .catch(() => {
          this.delayLoading = false;
        })
        .finally(() => {
          this.delayLoading = false;
        });

      return delayLoss;
    },

    async initSpecialFlowTrend() {
      let flowDatas = {};
      let trendParam = JSON.parse(JSON.stringify(this.params2));

      trendParam.startTime = new Date(trendParam.startTime).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      trendParam.endTime = new Date(trendParam.endTime).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      // 流速趋势
      await this.$http
        .PostJson("/trendData/getSpecialFlowTrend", trendParam)
        .then((res) => {
          //流速趋势数据
          if (res.code === 1) {
            flowDatas = res.data;
            this.matchingType = res.data.matchingType;
          }
        })
        .catch(() => {
          this.flowLoading = false;
        })
        .finally(() => {
          this.flowLoading = false;
        });

      return flowDatas;
    },
    async initGoodRateTrend() {
      let Rate = {};
      let goodparams = JSON.parse(JSON.stringify(this.params3));

      goodparams.startTime = new Date(goodparams.startTime).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      goodparams.endTime = new Date(goodparams.endTime).format(
        "yyyy-MM-dd HH:mm:ss"
      );
      // 优良率趋势
      await this.$http
        .wisdomPost("/specialIndex/getGoodRateTrend", goodparams)
        .then((res) => {
          //优良率趋势数据
          if (res.code === 1) {
            Rate = res.data;
          }
        })
        .catch(() => {
          this.rateLoading = false;
        })
        .finally(() => {
          this.rateLoading = false;
        });
      return Rate;
    },
    async load() {
      this.preAlias.specName = "";
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.flowData.inUse = [];
      this.flowData.outUse = [];
      this.goodRateData.nrUseRateList = [];
      this.goodRateData.nrGoodRateList = [];
      this.goodRateData.useRateList = [];
      this.goodRateData.goodRateList = [];
      //top.document.getElementById("spechealthy-delayLoss")
      this.delayLossChart = echarts.init(this.$refs["spechealthy-delayLoss"]);
      this.delayLoading = true;
      this.flowLoading = true;
      this.rateLoading = true;
      this.title = this.chartData.title;
      this.alarmType = this.chartData.alarmType;
      let delayLoss = {},
        flowDatas = {},
        Rate = {};

      //   delayLoss = await this.initDelayAndLostTrend();
        flowDatas = await this.initSpecialFlowTrend();
      //   Rate = await this.initGoodRateTrend();

      //   delayLoss = this.isDefaultData(delayLoss);

      //路径标识
      this.preAlias.name = delayLoss.preName;
      this.preAlias.port = delayLoss.prePort;
      this.zAlias.name = delayLoss.name;
      this.zAlias.port = delayLoss.port;
      //时延数据
      this.delayLossLevel = delayLoss.level;
      if (!delayLoss.lineOne || delayLoss.lineOne.length < 1) {
        this.echart1.show = false;
      }
      if (delayLoss.lineOne && delayLoss.lineOne.length > 0) {
          this.echart1.show = true;
        this.delayStart = 0;
        // if (delayLoss.lineOnetotal <= 300) {
        this.delayEnd = 100;
        this.startValue = delayLoss.lineOne[0][0];
        this.endValue = delayLoss.lineOne[delayLoss.lineOne.length - 1][0];
        // } else {
        //   this.delayEnd = (300 * 100) / delayLoss.lineOnetotal;
        //   this.startValue = delayLoss.lineOne[0][0];
        //   this.endValue = delayLoss.lineOne[299][0];
        // }
        this.startScale = false;
        this.delayLossScale = true;
        this.delayLossData.delay = delayLoss.lineOne;
      }
      if (delayLoss.lineTwo && delayLoss.lineTwo.length > 0) {
        this.delayLossData.loss = delayLoss.lineTwo;
      }
      this.$store.commit("updateDelayLossHistory", {
        level: delayLoss.level,
        datas: [this.delayLossData.delay, this.delayLossData.loss],
      }); //保存数据
      this.$store.commit("setdelayLTlevel", delayLoss.level); //保存首次加载的顶层数据粒度级别
      this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
      this.delayLoading = false;

      //流速数据
      let flowUnit = 1;
      this.flowLevel = flowDatas.level;
      if (!flowDatas.lineTwo || flowDatas.lineTwo.length < 1) {
          this.echart2.show = false;
        this.flowLevel = 99;
       
      }
      if (flowDatas.lineTwo && flowDatas.lineTwo.length > 0) {
        this.echart2.show = true;
        let unitChange = this.getFlowUnit(flowDatas.lineOne);
        this.flowUnit = unitChange[0];
        this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
        this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
        flowUnit = unitChange[1];
        this.flowData.inUse = flowDatas.inUseRateList;
        this.flowData.outUse = flowDatas.outUseRateList;
        this.flowData.enter = flowDatas.lineTwo.map((item) => {
          return [item[0], item[1]];
        });
        this.flowData.issue = flowDatas.lineOne.map((item) => {
          return [item[0], item[1]];
        });
        // this.startValue = flowDatas.lineTwo[0][0];
        //   // this.endValue = Rate.goodRateList[Rate.goodRateList.length - 1][0];
        //    this.endValue =   flowDatas.lineTwo[flowDatas.lineTwo.length - 1][0];
      }
      this.$store.commit("updateFlowHistory", {
        level: flowDatas.level,
        datas: [
          this.flowData.enter,
          this.flowData.issue,
          this.flowData.inUse,
          this.flowData.outUse,
        ],
      }); //保存数据
      this.$store.commit("setflowTlevel", flowDatas.level); //保存首次加载的顶层数据粒度级别
      this.$store.commit("setflowUnit", {
        level: flowDatas.level,
        unit: this.flowUnit,
      }); //保存单位
      this.flowLoading = false;
      //可用率数据
      console.log('Rate',Rate);
      this.goodRateLevel = Rate.level;
      if (!Rate.goodRateList || Rate.goodRateList.length < 1) {
        this.echart3.show = false;
        this.goodRateLevel = 99;
      }
      if (Rate.goodRateList && Rate.goodRateList.length > 0) {
          this.echart3.show = true;
        this.goodRateData.nrUseRateList = Rate.nrUseRateList;
        this.goodRateData.nrGoodRateList = Rate.nrGoodRateList;
        this.goodRateData.useRateList = Rate.useRateList;
        this.goodRateData.goodRateList = Rate.goodRateList;
        // if (Rate.goodRateList.length <= 300) {
        this.delayEnd = 100;
        // this.startValue = Rate.goodRateList[0][0];
        // // this.endValue = Rate.goodRateList[Rate.goodRateList.length - 1][0];
        //  this.endValue =  Rate.goodRateList[Rate.goodRateList.length - 1][0];
        // let newEndValue = '';
        // if(new Date(this.endValue).getTime() < new Date(RateEndValue).getTime()){
        //   newEndValue = RateEndValue;
        // }else{
        //   newEndValue = this.endValue;
        // }
        // this.endValue = RateEndValue;
        // } else {
        //   this.delayEnd = (300 * 100) / Rate.goodRateList.length;
        //   this.startValue = Rate.goodRateList[0][0];
        //   this.endValue = Rate.goodRateList[299][0];
        // }
      }
      this.$store.commit("updategoodRateHistory", {
        level: Rate.level,
        datas: [
          this.goodRateData.nrUseRateList,
          this.goodRateData.nrGoodRateList,
          this.goodRateData.useRateList,
          this.goodRateData.goodRateList,
        ],
      }); //保存数据
      this.$store.commit("setgoodRateTlevel", Rate.level); //保存首次加载的顶层数据粒度级别

      this.rateLoading = false;
      // if (this.echart2.show && this.echart3.show) {
      //   this.height = 720;
      // } else if (
      //   (!this.echart2.show && this.echart3.show) ||
      //   (this.echart2.show && !this.echart3.show)
      // ) {
      //   this.height = 500;
      // } else if (!this.echart2.show && !this.echart3.show) {
      //   this.height = 300;
      // }
      if (this.delayLossChart) {
        this.delayLossChart.hideLoading();
      }
      console.log(this.delayLossData.delay);
      console.log("开始时间：" + this.startValue);
      console.log("结束时间：" + this.endValue);
      this.initEchart();
    },
    setDelayLossTooltip() {
      let _self = this;
      return eConfig.tip("axis", function (param) {
        _self.scale = param[0].data[0];
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex]
            ? ""
            : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let src = "",
          delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "",
          rateTime = "",
          rateTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.getFlowLegendUnit(param[i].seriesName) +
              "<br />";
          }
          if (
            param[i].seriesIndex === 2 ||
            param[i].seriesIndex === 3 ||
            param[i].seriesIndex === 4 ||
            param[i].seriesIndex === 5
          ) {
            var isvalue = "";
            if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3){
              isvalue = _self.flowSize(param[i].value[1], true, true);
            }
            if (param[i].seriesIndex === 4 || param[i].seriesIndex === 5){
              isvalue =
                param[i].value[1] + _self.getFlowLegendUnit(param[i].seriesName);
            }
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : isvalue) +
              "<br />";
          }
          if (
            param[i].seriesIndex === 6 ||
            param[i].seriesIndex === 7 ||
            param[i].seriesIndex === 8 ||
            param[i].seriesIndex === 9
          ) {
            rateTime = param[i].data[0] + "<br />";
            rateTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            rateTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === ""
                ? "--"
                : param[i].value[1]) +
              _self.getFlowLegendUnit(param[i].seriesName) +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip + rateTime + rateTip;
      });
    },
    // 获取流速的单位
    getFlowLegendUnit(seriesName){
      // 这里做个 取值的转换操作
      let seriesNameStr = seriesName;
        if(seriesName === this.$t('dashboard_inlet_velocity') || seriesName == this.$t('dashboard_outflow_velocity')){
            seriesNameStr = "入流速";
        }else if(seriesName === this.$t('view_Leased_type_6') || seriesName == this.$t('view_Leased_type_6')){
          seriesNameStr = "入利用率";
        }
        return this.delayLossUnit[seriesNameStr];
    },
    // 获取流速信息
    getFlowLegendOption(){
      
      let flowLegendArray = [this.$t('dashboard_inlet_velocity'), this.$t('dashboard_outflow_velocity'), this.$t('view_Leased_type_6'), this.$t('view_Leased_type_8')];
      // // matchingType => 0：Z端匹配，1：A端匹配 
      // if(this.matchingType == 0){
      //   // 出流速=>上行流速 , 入流速 => 下行流速
      //   flowLegendArray = [this.$t('dashboard_down'), this.$t('dashboard_upstream'), this.$t('dashboard_downstream'), this.$t('dashboard_uplink')];
      // }else if(this.matchingType == 1){
      //   // 出流速=>下行流速 , 入流速 => 上行流速
      //   flowLegendArray = [this.$t('dashboard_upstream'), this.$t('dashboard_down'), this.$t('dashboard_uplink'), this.$t('dashboard_downstream')];
      // }
      return flowLegendArray;

    },
    Option() {
      let flowLegendOption = this.getFlowLegendOption();
      let optionArr = [
        {
          title: {
            show:this.flowData.inUse.length<1,
            text: this.$t('common_No_data'),
            top: "40px",
            left:"center",
            textStyle:{
              color:this.isdarkSkin == 1 ? '#617ca5':"#333",
              fontFamily:"serif",
              fontWeigth:"400",
              fontSize:18
            }
          },
          tooltip: this.setDelayLossTooltip(),
          grid: [
            {
              left: "8%",
              top: "40px",
              width: "88%",
              height: this.echart1.show ? "140px" : 0,
            },
            {
              left: "8%",
              // 暂时注释 ， 等到 4.1版本放开
              // top: "280px",
               top: "80px",
              width: "88%",
              height: this.echart2.show ? "140px" : 0,
            },
            {
              left: "8%",
              top:
                this.echart2.show && this.echart3.show
                  ? "530px"
                  : !this.echart2.show && this.echart3.show
                  ? "280px"
                  : 0,
              width: "88%",
              height: this.echart3.show ? "140px" : 0,
            },
          ],
          legend: [
            {
              show: this.echart1.show,
              top: "0%",
              right: "40%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t('speed_delay'), this.$t('comm_packet_loss')]
            },
            // 上行流速，下行流速
            {
              show: this.echart2.show,
              top: "0px",
              // top: "240px",
              right: "40%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              data: flowLegendOption,
            },
            {
              show: this.echart3.show,
              top:
                this.echart2.show && this.echart3.show
                  ? "460px"
                  : !this.echart2.show && this.echart3.show
                  ? "240px"
                  : "460px",
              right: "40%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              data: [this.$t('specquality_availability_exemption'), this.$t('specquality_good_rate_exemption'),this.$t('specquality_availability'),this.$t('specquality_good_rate')]

            },
          ],
          xAxis: [
            {
              show: this.echart1.show,
              type: "time",
              gridIndex: 0,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart3.show,
              type: "time",
              gridIndex: 2,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              show: this.echart1.show,
              name: this.$t('comm_delay(ms)'),
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                 show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
            {
              show: this.echart1.show,
              name: this.$t('comm_loss_rate'),
              type: "value",
              scale: true,
              gridIndex: 0,
              max: 100,
              min: 0,
              position: "right",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                 show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t('server_flow_rate'),
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                 show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
              axisLabel: {
                show: true,
                formatter: (value) => {
                  let resualt = this.getUnit(value, true, true);
                  // if (text.length > 6) {
                  //   resualt = text.substring(0, 6) + "..";
                  // } else {
                  //   resualt = text;
                  // }
                  return resualt;
                },
              },
            },
            {
              show: this.echart3.show,
              name: this.$t('specquality_utilization'),
              type: "value",
              scale: true,
              gridIndex: 1,
              max: 100,
              position: "right",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                 show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
            {
              show: this.echart3.show,
              name: this.$t('comm_unit') + "("+this.goodRateUnit + ")",
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 2,
              max: 100,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                 show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1, 2],
              start: 0,
              end: 100,
              // startValue: this.startValue,
              // endValue: this.endValue
            },
            {
              type: "slider",
              // left: "5%",
              // right: "5%",
              top:
                this.echart2.show && this.echart3.show
                  ? "690px"
                  : (!this.echart2.show && this.echart3.show) ||
                    (this.echart2.show && !this.echart3.show)
                  ? "470px"
                  : !this.echart2.show && !this.echart3.show
                  ? "250px"
                  : "690px",
              xAxisIndex: [0, 1, 2],
              realtime: true,
              start: 0,
              end: 100,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , //  "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // "rgba(22, 67, 107, 1)",
              handleStyle: {
                  color:eConfig.dataZoom.handleStyle.color[this.currentSkin] , //  "rgba(2, 67, 107, 1)"
              },
              textStyle:{
                  color: eConfig.dataZoom.textStyle.color[this.currentSkin] , // top.window.isdarkSkin == 1 ? '#5CA0D5' :"#e3e7f2"
               },
            },
          ],
          series: [
            {
              show: this.echart1.show,
              type: "line",
              name: this.$t('speed_delay'),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.delayColor_0[this.currentSkin] // "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.delayColor_8[this.currentSkin] // "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.delay,
            },
            {
              show: this.echart1.show,
              type: "line",
              name: this.$t('comm_packet_loss'),
              xAxisIndex: 0,
              yAxisIndex: 1,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color:eConfig.areaStyle.lossColor_0[this.currentSkin] // "rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.lossColor_8[this.currentSkin] // "rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.loss,
            },
            {
              show: this.echart2.show,
              // name: this.$t('specquality_incoming_velocity'),
              name: flowLegendOption[0],
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowInColor_0[this.currentSkin] // "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowInColor_8[this.currentSkin] // "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              markLine: {
                data: [
                  {
                    symbol: "image://" + pointRed,
                    // symbol: "rect" ,
                    symbolSize: 10,
                    xAxis: this.markPoint || "",
                    symbolRotate: "0",
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 1,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#FE5C5C",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint;
                      },
                      // backgroundColor: "#fff",
                    },
                  },
                  {
                    symbol: "image://" + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen || "",
                    symbolRotate: "0",
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: "#fff",
                      formatter: () => {
                        return this.markPointGreen;
                      },
                    },
                  },
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1,
                  },
                },
              },
            },
            {
              show: this.echart2.show,
               name: flowLegendOption[1],
              // name: this.$t('specquality_exit_velocity'),
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color:  eConfig.areaStyle.flowOutColor_0[this.currentSkin]// "rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color:  eConfig.areaStyle.flowOutColor_8[this.currentSkin] // "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
            // 上行带宽
            {
              show: this.echart2.show,
              name: flowLegendOption[2],
              // name: "入利用率",
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              xAxisIndex: 1,
              yAxisIndex: 3,
              color: this.flowColor[2],
              data: this.flowData.inUse,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowInRateColor_0[this.currentSkin] // "rgba(0,255,238, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color:eConfig.areaStyle.flowInRateColor_8[this.currentSkin] //  "rgba(0,255,238, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              name: flowLegendOption[3],
              // name: "出利用率",
              type: "line",
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              xAxisIndex: 1,
              yAxisIndex: 3,
              color: this.flowColor[3],
              data: this.flowData.outUse,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color:eConfig.areaStyle.flowOutRateColor_0[this.currentSkin] //  "rgba(254,92,92, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowOutRateColor_8[this.currentSkin] // "rgba(254,92,92, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart3.show,
              name: this.$t('specquality_availability_exemption'),
              type: "line",
              z: 1,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              xAxisIndex: 2,
              yAxisIndex: 4,
              color: this.goodRateColor[0],
              data: this.goodRateData.nrUseRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart3.show,
              name: this.$t('specquality_good_rate_exemption'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 2,
              yAxisIndex: 4,
              symbol: "circle",
              symbolSize: 1,
              color: this.goodRateColor[1],
              data: this.goodRateData.nrGoodRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(70,183,89, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(70,183,89, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart3.show,
              name: this.$t('specquality_availability'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 2,
              yAxisIndex: 4,
              symbol: "circle",
              symbolSize: 1,
              color: this.goodRateColor[2],
              data: this.goodRateData.useRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart3.show,
              name: this.$t('specquality_good_rate'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 2,
              yAxisIndex: 4,
              symbol: "circle",
              symbolSize: 1,
              color: this.goodRateColor[3],
              data: this.goodRateData.goodRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(36,195,197, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(36,195,197, 0.2)",
                    },
                  ],
                },
              },
            },
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart) {
        that.delayLossChart.clear();
      }
      /*设置时延丢包率echart图*/
      that.delayLossChart.setOption(this.Option());
      that.delayLossChart.getZr().on("mousewheel", function (params) {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getgoodTlevel = that.$store.state.goodRateTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据
        let getgoodSaveData = that.$store.state.goodRateHistory; //获取保存的数据

        var pointInPixel = [params.offsetX, params.offsetY];
        if (
          that.delayLossChart.containPixel(
            { gridIndex: [0, 1, 2] },
            pointInPixel
          )
        ) {
          var xIndex = that.delayLossChart.convertFromPixel(
            { xAxisIndex: 0 },
            params.offsetX
          );
          if (params.wheelDelta < 0 && that.startScale == false) {
            //滚轮缩小 =1放大
            if (!that.startScale) {
              //是否处在缩放过程中

              if (
                that.delayLossLevel == 0 ||
                that.delayLossLevel == getTlevel
              ) {
                //判断当前处于什么粒度，4|3：秒，2：分，1：小时，0：天
                var opt = that.delayLossChart.getOption();
                var delayLossZoom = opt.dataZoom;
                var startValue =
                  that.delayLossChart.getModel().option.dataZoom[0].startValue;
                var endValue =
                  that.delayLossChart.getModel().option.dataZoom[0].endValue;
                that.setPs(that.delayLossLevel, [
                  that.timeChange(startValue),
                  that.timeChange(endValue),
                ]);
                that.startScale = false;
              } else {
                var opt = that.delayLossChart.getOption();
                let getSite = JSON.parse(sessionStorage.getItem("delayPs"));

                if (that.flowLevel == getflowTlevel) {
                  that.startScale = false;
                } else {
                  if (that.flowLevel == 2 && that.delayLossLevel == 2) {
                    that.startScale = true;
                    that.flowLevel = 1;
                    that.flowUnit = getflowSaveUnit.hoursUnit;
                    that.flowData.enter = getflowSaveData.HoursData.enter;
                    that.flowData.issue = getflowSaveData.HoursData.issue;
                  } else if (that.flowLevel == 1 && that.delayLossLevel == 1) {
                    that.startScale = true;
                    that.flowLevel = 0;
                    that.flowUnit = getflowSaveUnit.dayUnit;
                    that.flowData.enter = getflowSaveData.dayData.enter;
                    that.flowData.issue = getflowSaveData.dayData.issue;
                  }
                }
                if (that.goodRateLevel == getgoodTlevel) {
                  that.startScale = false;
                } else {
                  if (that.goodRateLevel == 1 && that.delayLossLevel == 1) {
                    that.startScale = true;
                    that.goodRateLevel = 0;
                    that.goodRateData.nrUseRateList =
                      getgoodSaveData.dayData.nrUseRateList;
                    that.goodRateData.nrGoodRateList =
                      getgoodSaveData.dayData.nrGoodRateList;
                    that.goodRateData.useRateList =
                      getgoodSaveData.dayData.useRateList;
                    that.goodRateData.goodRateList =
                      getgoodSaveData.dayData.goodRateList;
                  }
                }

                if (that.delayLossLevel == 1) {
                  that.startScale = true;
                  that.delayLossLevel = 0;
                  that.delayLossData.delay = getSaveData.dayData.delay;
                  that.delayLossData.loss = getSaveData.dayData.loss;
                  that.delayStart = getSite.psD.start;
                  that.startValue = getSite.psD.start;
                  that.delayEnd = getSite.psD.end;
                  that.endValue = getSite.psD.end;
                } else if (that.delayLossLevel == 2) {
                  that.startScale = true;
                  that.delayLossLevel = 1;
                  that.delayLossData.delay = getSaveData.HoursData.delay;
                  that.delayLossData.loss = getSaveData.HoursData.loss;
                  that.delayStart = getSite.psH.start;
                  that.startValue = getSite.psH.start;
                  that.delayEnd = getSite.psH.end;
                  that.endValue = getSite.psH.end;
                } else if (
                  that.delayLossLevel == 3 ||
                  that.delayLossLevel == 4
                ) {
                  that.startScale = true;
                  that.delayLossLevel = 2;
                  that.delayLossData.delay = getSaveData.minuteData.delay;
                  that.delayLossData.loss = getSaveData.minuteData.loss;
                  that.delayStart = getSite.psM.start;
                  that.startValue = getSite.psM.start;
                  that.delayEnd = getSite.psM.end;
                  that.endValue = getSite.psM.end;
                }

                setTimeout(() => {
                  that.startScale = false;
                  that.initEchart();
                }, 300);
              }
            }
          }
        }
      });
      that.delayLossChart.off("showTip");
      that.delayLossChart.on("showTip", function (param) {
        let x = param.x; //当前点相对于echart dom的左上角的像素偏移
        let delayTime = that.delayLossChart.convertFromPixel(
          {
            xAxisIndex: 0,
          },
          x
        );
        let flowTime = that.delayLossChart.convertFromPixel(
          {
            xAxisIndex: 1,
          },
          x
        );
        let useTime = that.delayLossChart.convertFromPixel(
          {
            xAxisIndex: 2,
          },
          x
        );
        that.hoverDelayTime = parseInt(delayTime);
        that.hoverFlowTime = parseInt(flowTime);
        that.hoverUseTime = parseInt(useTime);
      });

      let lastdiff = this.delayEnd - this.delayStart;
      that.delayLossChart.off("dataZoom");
      that.delayLossChart.on("dataZoom", (params) => {
        let st = params.batch ? params.batch[0].start : params.start;
        let en = params.batch ? params.batch[0].end : params.end;
        if (params.batch) {
          //滚轮操作缩放
          let start = params.batch[0].start;
          let end = params.batch[0].end;
          let diff = Number(end) - Number(start);
          let mirdiff = diff - lastdiff;
          let goodRateParam = JSON.parse(JSON.stringify(that.params3));
          let delayParam = JSON.parse(JSON.stringify(that.params));
          let flowParam = JSON.parse(JSON.stringify(that.params2));
          if (mirdiff < 0) {
            console.log("放大");
            console.log('goodRateLevel',this.goodRateLevel);
            //放大
            if (!that.startScale) {
              //是否处在缩放过程中,调用接口中
              let cachD = true;
              let cachF = true;
              let cachG = true;
              that.startScale = true;
              if (that.delayLossLevel == 4 || that.delayLossLevel == 3) {
                //判断当前处于什么粒度，4|3：秒，2：分，1：小时，0：天
                delayParam.level = 99;
                cachD = false;
              } else if (that.delayLossLevel == 0) {
                delayParam.level = 1;
                cachD = true;
              } else if (that.delayLossLevel == 1) {
                delayParam.level = 2;
                cachD = true;
              } else if (that.delayLossLevel == 2 && delayParam.High == true) {
                delayParam.level = 4;
                cachD = true;
              } else if (that.delayLossLevel == 2 && delayParam.High == false) {
                delayParam.level = 99;
                cachD = false;
              }
              if (that.flowLevel == 2 || that.flowLevel == 99) {
                flowParam.level = 99;
                cachF = false;
              } else if (that.flowLevel == 0) {
                flowParam.level = 1;
                cachF = true;
              } else if (that.flowLevel == 1) {
                flowParam.level = 2;
                cachF = true;
              }

              if (that.goodRateLevel == 1 || that.goodRateLevel == 99) {
                goodRateParam.level = 99;
                cachG = false;
              } else if (that.goodRateLevel == 0) {
                cachG = true;
                goodRateParam.level = 1;
              }else{
                goodRateParam.level = that.goodRateLevel
              }
              let delayTime = that.setTime(
                that.delayLossLevel,
                that.hoverDelayTime
              );
              let flowTime = that.setTime(that.flowLevel, that.hoverFlowTime);
              let useTime = that.setTime(that.goodRateLevel, that.hoverUseTime);
              delayParam.startTime = delayTime[0];
              flowParam.startTime = flowTime[0];
              delayParam.endTime = delayTime[1];
              flowParam.endTime = flowTime[1];
              goodRateParam.startTime = useTime[0];
              goodRateParam.endTime = useTime[1];
              that.getDelayLoss(
                delayParam,
                flowParam,
                goodRateParam,
                cachD,
                cachF,
                cachG,
                that.hoverDelayTime,
                that.hoverFlowTime,
                that.hoverUseTime
              );
            } else {
            }
          } else if (mirdiff > 0) {
            console.log("缩小");
            //缩小
          }
        } else {
          //滑块操作
          let startValue =
            that.delayLossChart.getModel().option.dataZoom[0].startValue;
          let endValue =
            that.delayLossChart.getModel().option.dataZoom[0].endValue;
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);

          that.delayStart = params.start;
          that.delayEnd = params.end;
        }
        if (that.delayLossLevel != 4 || that.delayLossLevel != 3) {
          lastdiff = en - st;
        }
      });
      console.log(this.startValue);
      console.log(this.endValue);
    },

    async getDelayLoss(
      delayParam,
      flowParam,
      goodRateParam,
      cachD,
      cachF,
      cachG,
      hoverDelayTime,
      hoverFlowTime,
      hoverUseTime
    ) {
      let delayLoss = {},
        flowDatas = {},
        Rate = {},
        isUpdate = false;
      if (delayParam.level != 99) {
        isUpdate = true;
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        this.delayLoading = true;
        let delaytrendParam = JSON.parse(JSON.stringify(delayParam));
        delaytrendParam = Object.assign(delaytrendParam, {
          snmp: this.dataSource == 1 ? true : false,
        });
        await this.$http
          .PostJson("/trendData/getDelayAndLostTrend", delaytrendParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              delayLoss = res.data;
            }
          });
      }

      if (flowParam.level != 99) {
        isUpdate = true;
        this.flowData.enter = [];
        this.flowData.issue = [];
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, {
          snmp: this.dataSource == 1 ? false : false,
        });
        await this.$http
          .PostJson("/trendData/getSpecialFlowTrend", trendParam)
          .then((res) => {
            //流速趋势数据
            if (res.code === 1) {
              flowDatas = res.data;
              this.matchingType = res.data.matchingType;
            }
          });
      }
      if (goodRateParam.level != 99) {
        isUpdate = true;
        this.goodRateData.nrUseRateList = [];
        this.goodRateData.nrGoodRateList = [];
        this.goodRateData.useRateList = [];
        this.goodRateData.goodRateList = [];
        this.rateLoading = true;
        if (this.height > 700) {
          this.rateLoading = true;
        } else if (this.height < 700 && this.height > 400) {
          this.rateLoading = true;
        }
        await this.$http
          .wisdomPost("/specialIndex/getGoodRateTrend", goodRateParam)
          .then((res) => {
            //优良率趋势数据
            if (res.code === 1) {
              Rate = res.data;
            }
          });
      }
      if (delayParam.level != 99) {
        this.delayLossLevel = delayLoss.level;
        let index = this.closest(delayLoss.lineOne, hoverDelayTime);
        if (!delayLoss.lineOne && !delayLoss.lineTwo) {
          this.delayEnd = 100;
        }
        if (delayLoss.lineOne && delayLoss.lineOne.length > 0) {
          this.delayStart = 0;
          // if (delayLoss.lineOnetotal <= 300) {
          this.delayEnd = 100;
          this.startValue = delayLoss.lineOne[0][0];
          this.endValue = delayLoss.lineOne[delayLoss.lineOne.length - 1][0];
          // } else {
          //   this.delayEnd = (300 * 100) / delayLoss.lineOnetotal;
          //   if (index <= 150) {
          //     this.startValue = delayLoss.lineOne[0][0];
          //     if (index + 150 < delayLoss.lineOnetotal - 1) {
          //       this.endValue = delayLoss.lineOne[index + 150][0];
          //     } else {
          //       this.endValue =
          //         delayLoss.lineOne[delayLoss.lineOne.length - 1][0];
          //     }
          //   } else {
          //     this.startValue = delayLoss.lineOne[index - 150][0];
          //     if (index + 150 < delayLoss.lineOnetotal) {
          //       this.endValue = delayLoss.lineOne[index + 150][0];
          //     } else {
          //       this.endValue =
          //         delayLoss.lineOne[delayLoss.lineOne.length - 1][0];
          //     }
          //   }
          // }
          this.delayLossScale = true;
          this.delayLossData.delay = delayLoss.lineOne;
        }
        if (delayLoss.lineTwo && delayLoss.lineTwo.length > 0) {
          this.delayLossData.loss = delayLoss.lineTwo;
        }
        this.$store.commit("updateDelayLossHistory", {
          level: delayLoss.level,
          datas: [this.delayLossData.delay, this.delayLossData.loss],
        }); //保存数据
        this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
        this.delayLoading = false;
      }
      if (flowParam.level != 99) {
        let flowUnit = 1;
        this.flowLevel = flowDatas.level;
        if (flowDatas.lineTwo && flowDatas.lineTwo.length > 0) {
          let unitChange = this.getFlowUnit(flowDatas.lineOne);
          this.flowUnit = unitChange[0];
          this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
          this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
          flowUnit = unitChange[1];
          this.flowData.inUse = flowDatas.inUseRateList;
          this.flowData.outUse = flowDatas.outUseRateList;
          this.flowData.enter = flowDatas.lineTwo.map((item) => {
            return [item[0], item[1]];
          });
          this.flowData.issue = flowDatas.lineOne.map((item) => {
            return [item[0], item[1]];
          });
        }
        if (cachF) {
          this.$store.commit("updateFlowHistory", {
            level: flowDatas.level,
            datas: [
              this.flowData.enter,
              this.flowData.issue,
              this.flowData.inUse,
              this.flowData.outUse,
            ],
          }); //保存数据
          this.$store.commit("setflowUnit", {
            level: flowDatas.level,
            unit: this.flowUnit,
          }); //保存单位
        }
        this.flowLoading = false;
      }
      if (goodRateParam.level != 99) {
        this.goodRateLevel = Rate.level;
        if (Rate.goodRateList && Rate.goodRateList.length > 0) {
          this.goodRateData.nrUseRateList = Rate.nrUseRateList;
          this.goodRateData.nrGoodRateList = Rate.nrGoodRateList;
          this.goodRateData.useRateList = Rate.useRateList;
          this.goodRateData.goodRateList = Rate.goodRateList;
        }
        if (cachG) {
          this.$store.commit("updategoodRateHistory", {
            level: Rate.level,
            datas: [
              this.goodRateData.nrUseRateList,
              this.goodRateData.nrGoodRateList,
              this.goodRateData.useRateList,
              this.goodRateData.goodRateList,
            ],
          }); //保存数据
        }
        if (this.height > 700) {
          this.rateLoading = false;
        } else if (this.height < 700 && this.height > 400) {
          this.flowLoading = false;
        }
      }
      this.startScale = false;
      if (isUpdate) {
        this.initEchart();
      }
    },
    //时间推算
    setTime(level, time) {
      let Interval = 0,
        start = this.startTime,
        end = this.endTime,
        newStart = 0,
        newEnd = 0;
      if (level == 0) {
        Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 1) {
        Interval = 12 * 60 * 60 * 1000;
      } else if (level == 2) {
        Interval = 30 * 60 * 1000;
      }
      newStart = time - Interval;
      newEnd = time + Interval;

      if (newStart < start) {
        newStart = start;
      }
      if (newEnd > end) {
        newEnd = end;
      }
      newStart = this.timeChange(newStart);
      newEnd = this.timeChange(newEnd);
      return [newStart, newEnd];
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },
  },
};
</script>

<style scoped>
.loading-content {
  width: 665px;
  height: 141px;
  background: rgba(245, 245, 245, 0.9);
  position: absolute;
  left: 50%;
  margin-left: -332px;
}
.delayLoading {
  top: 39px;
}
.flowLoading {
  /* top: 279px; */
  top: 39px;
}
.rateLoading {
  top: 499px;
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
