<template>
  <Form ref="actionForm" class="action-content">
    <div class="action-top">
      <div
        class="action-top-right"
        style="display: flex; align-items: center; margin-bottom: 15px"
        @click="indexClick"
      >
        <i
          :class="
            currentSkin == 1
              ? 'icon-box alarm-actionItem-icon-index myIconClass'
              : 'icon-box light-icon-index myIconClass'
          "
          style="margin-right: 0px; cursor: pointer"
        ></i>
        &nbsp;<span
          style="
            margin-left: 4px;
            color: var(--time_picker_suffix_color, #5ca0d5);
          "
          >{{ $t("alarm_index") }}</span
        >
      </div>
      <div class="action-top-left" style="margin-bottom: 12px">
        {{ $t("alarm_historical") }}
      </div>
    </div>
    <div class="fault-tab action-tab">
      <div class="dialTest-tab-content">
        <Loading :loading="loading"></Loading>
        <Table
          :columns="columns"
          stripe
          :data="tabList"
          :min-height="300"
          :no-data-text="
            loading
              ? ''
              : currentSkin == 1
              ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                $t('common_No_data') +
                '</p></div>'
              : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                $t('common_No_data') +
                '</p></div>'
          "
        >
          <template slot-scope="{ row }" slot="orgName">
            <Tooltip :content="row.orgName" placement="top-start">
              <div
                style="
                  text-overflow: ellipsis;
                  overflow: hidden;
                  width: 100px;
                  white-space: nowrap;
                  text-align: left;
                "
              >
                {{ row.orgName }}
              </div>
            </Tooltip>
          </template>
          <template slot-scope="{ row }" slot="content">
            <Tooltip
              :content="row.content"
              :placement="
                row._index == tabList.length - 1 ? 'top-start' : 'left-start'
              "
              max-width="600"
            >
              <div
                style="
                  text-overflow: ellipsis;
                  overflow: hidden;
                  width: 150px;
                  white-space: nowrap;
                "
              >
                {{ row.content }}
              </div>
            </Tooltip>
          </template>
          <!-- <template slot-scope="{row,index}" slot="content">
                <div @mouseenter="iconMoveIn(row.content,index)" @mouseleave="iconMoveOut()"  style="text-overflow: ellipsis;overflow: hidden;width:100px;white-space: nowrap;text-align: left;position:absolute;margin: -10px;" >
                    {{row.content}}
                </div>
                <div v-show="showDiv" :style="showDivSty" >
                    {{showDivTile}}
                </div>
          </template> -->
        </Table>
      </div>
      <div class="tab-page" v-if="totalCount > tabObj.pageSize">
        <Page
          v-page
          :current="tabObj.pageNo"
          :page-size="tabObj.pageSize"
          :total="totalCount"
          :prev-text="$t('common_previous')"
          :next-text="$t('common_next_page')"
          @on-change="pageChange"
        >
        </Page>
      </div>
    </div>
    <div class="action-bottom" style="margin-top: 15px">
      <div class="action-bottom-box action-bottom-left">
        <Input
          v-model="workOrder.content"
          :rows="3"
          type="textarea"
          :placeholder="$t('alarm_content')"
          maxlength="200"
        />
      </div>
      <div class="action-bottom-box" style="margin-top: 20px">
        <div class="action-bottom-checkbox">
          <Checkbox
            v-model="dealStatus"
            :disabled="disabled"
            @on-change="checkboxChange"
            >{{ $t("alarm_resolved") }}</Checkbox
          >
        </div>
      </div>
    </div>
    <!--指标    在这个地方-->
    <Modal
      sticky
      v-model="index.show"
      :width="modalWidth"
      :styles="{ top: '150px' }"
      class="action-index-modal"
      draggable
      :mask="true"
      :footer-hide="true"
      @on-cancel="indexCancel('indexEvent')"
    >
      <div slot="header">
        <span class="title-name">{{ $t("alarm_fault_index") }}</span>
        <span class="title-content">
          {{ titles }}
        </span>
      </div>
      <index-item
        v-show="!isSnmp2"
        :time="time"
        :dataSource="data.dataSource"
        ref="indexEvent"
        :data="index.data"
      ></index-item>
    </Modal>
    <Modal
      sticky
      v-model="snmpindex.show"
      :width="modalWidth"
      :styles="{ top: '150px' }"
      class="action-index-modal"
      :title="$t('alarm_failure_index')"
      draggable
      :mask="true"
      :footer-hide="true"
      @on-cancel="indexCancel('indexEvent')"
    >
      <snmp-item
        v-show="isSnmp2"
        :snmp="isSnmp2"
        :dataSource="data.dataSource"
        ref="indexEvent1"
        :data="snmpindex.data"
        :time="time"
      ></snmp-item>
    </Modal>
  </Form>
</template>

<script>
import indexItem from "./indexItem.vue";
import snmpItem from "./snmpItem.vue";
import { addDraggable } from "@/common/drag.js";
import handlePassword from "@/common/handlePassword";
export default {
  name: "actionItem",
  components: {
    indexItem,
    snmpItem
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    titles: {
      type: String
    },
    time: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      modalWidth:0,
      isSnmp2: false,
      loading: false,
      showDiv:false,
      showDivTile:'',
      showDivSty:{'position': 'absolute', 'top':'33%','right':'39%','z-index': '99999',
                    'border-radius': '10px','background-color': 'var(--body_b_color,#032a4d)',
                    'border': '1px solid var(--border_color,#032a4d)','max-width':'400px',
                    'padding': '10px 16px 10px 24px','background-color':'#032a4d'},
      columns: [
        {
          title: this.$t('alarmlist_operator'),
          key: "operator",
          align: "left",
          render: (h, params) => {
            let str = handlePassword.decrypt(params.row.operator);
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          }
        },
        {
          title: this.$t('alarmlist_operate_time'),
          key: "createTime",
          align: "left",
          width: 200,
          render: (h, params) => {
            let str = params.row.createTime;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          }
        },
        {
          title: this.$t('comm_org'),
          slot: "orgName",
          align: "left"
        },
        {
          title: this.$t('alarmlist_operate_process'),
          slot: "content",
          align: "left",
        },
        {
          title: this.$t('alarmlist_operate_result'),
          key: "state",
          align: "left",
          render: (h, params) => {
            let str = params.row.state,
              text = "--";
            switch (str) {
              case "2":
                text = this.$t('server_process');
                break;
              case "4":
                text = this.$t('server_closed_loop');
                break;
              default:
                text = "--";
                break;
            }
            return h("span", text);
          }
        }
      ],
      tabList: [],
      tabObj: {
        reportId: "",
        pageNo: 1,
        pageSize: 4
      },
      totalCount: 0,
      workOrder: {
        reportId: "",
        content: "",
        dealStatus: ""
      },
      dealStatus: false,
      disabled: false,
      index: {
        show: false,
        data: {}
      },
      snmpindex: {
        show: false,
        data: {}
      }
    };
  },
  watch: {
    data: {
      handler(val) {
        if (!(JSON.stringify(val) === "{}")) {
          if (val.dealStatus === 4) {
            this.disabled = true;
            this.dealStatus = true;
            this.workOrder.dealStatus = val.dealStatus;
          } else {
            this.disabled = false;
            this.dealStatus = false;
            this.workOrder.dealStatus = 2;
          }
          this.tabObj.reportId = val.id;
          this.workOrder.reportId = val.id;
          this.tabObj.pageNo = 1;
          this.getRecord(this.tabObj);
        } else {
          this.resetRegion();
        }
      },
      deep: true
    },
    time: {
      handler(val) {
        this.time = val;
      },
      deep: true
    }
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    //指标
    indexClick() {
      let _self = this;
      if (this.data.dataSource == 1) {
        this.isSnmp2 = true;
        _self.snmpindex.show = true;
        this.snmpindex.data = Object.assign({}, this.data);
      } else {
        this.isSnmp2 = false;
        _self.index.show = true;
        this.index.data = Object.assign({}, this.data);
      }
    },
    //复选框事件
    checkboxChange(val) {
      if (val) {
        this.workOrder.dealStatus = 4;
      } else {
        this.workOrder.dealStatus = 2;
      }
    },
    //获取历史处理记录
    getRecord(param) {
      let _self = this;
      _self.$http
        .wisdomPost("/fault/getDealReportList", param)
        .then(res => {
          if (res.code === 1) {
            _self.tabList = res.data.records || [];
            _self.totalCount = res.data.total;
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    //分页
    pageChange(page) {
      this.tabObj.pageNo = page;
      this.getRecord(this.tabObj);
    },
    getParams() {
      return this.workOrder;
    },
    //清除数据
    resetRegion() {
      this.workOrder = {
        reportId: "",
        content: "",
        dealStatus: ""
      };
      this.dealStatus = false;
      this.disabled = false;
    },
    //关闭
    indexCancel(refName) {
      this.$Modal.visible = false;
      if (!this.isSnmp2) {
        this.$refs["indexEvent"].resetRegion();
      } else if (this.isSnmp2) {
      }
      this.index.data = {};
    },
    //移入事件
    iconMoveIn(content,index) {
      // this.showDivSty.top = "calc(33.5% + "+ (index * 60 )+"px)";
      this.showDivSty.top = "calc(23.5% + "+ (index * 60 )+"px)";
      this.showDivSty.left = "38%"
      console.log("width:",this.showDivSty)
      this.showDivTile = content;
      this.showDiv = true;
    },
    //移出事件
    iconMoveOut() {
      this.showDivTile = '';
      this.showDiv = false;
    },
  },
  mounted() {
    this.modalWidth = localStorage.getItem('modalWidth') * 0.98
    if (this.data.dealStatus === 4) {
      this.disabled = true;
      this.dealStatus = true;
    } else {
      this.disabled = false;
      this.dealStatus = false;
    }
    addDraggable();
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  
};
</script>

<style scoped lang="less">
.action-index-modal {
  /deep/.ivu-modal-body {
    overflow: auto;
    max-height: 550px;
  }
}
.contentSty {
  position: fixed;
  top: 0;
  left: -20px;
  z-index: 99999;
  padding: 10px 16px;
  background-color: var(--body_b_color, #f4f6f9);
  border: 1px solid var(--border_color, #f4f6f9);
  border-radius: 10px;
}
</style>