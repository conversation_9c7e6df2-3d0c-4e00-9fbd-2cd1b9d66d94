<template>
  <!-- 系统告警 -->
  <section
    class="sectionBox"
    style="min-width: 100% !important"
    @click="bodyClick"
  >
    <div class="section-top">
      <Row class="fn_box">
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="position: relative">
              <TreeSelect
                v-model="treeValue"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadData"
                @onSelectChange="setOrg"
                @onClear="onClear"
                @onFocus="focusFn"
              ></TreeSelect>
            </div>
          </div>
        </Col>
        <!-- <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">故障类型：</label>
          <div class="fn_item_box">
            <Select v-model="query.faultType" clearable filterable>
              <Option
                v-for="item in flowFaultList"
                :value="item.value"
                :key="item.value"
                >{{ item.label }}</Option
              >
            </Select>
          </div>
        </div>
      </Col> -->

        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Input
                v-model.trim="query.keyword"
                :placeholder="$t('alarm_symptom_placeholder')"
                maxlength="200"
              />
            </div>
          </div>
        </Col>
      </Row>
      <div
        class="fn_tool"
        style="padding-bottom: 0; display: flex; justify-content: space-between"
      >
        <div class="butBoxStyle">
          <ul>
            <li
              :class="tabActiove == index ? 'tabStyle' : ''"
              v-for="(itme, index) in bussTypeList"
              :key="index"
              @click="getTabState(index, itme.value)"
            >
              {{ itme.label }}
            </li>
          </ul>
        </div>
        <div style="display: flex; align-items: center">
          <div class="fn_item" style="margin-bottom: 0px; width: 450px">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="width: 100%">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
                @on-change="dateChange"
              >
              </DatePicker>
            </div>
          </div>

          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.list"
            icon="ios-search"
            @click="queryClick"
            :loading="btnLoading"
            :title="$t('common_query')"
          ></Button>
          <!-- <Button
            type="primary"
            v-if="permissionObj.list"
            class="skinPrimary reset-btn"
            icon="md-refresh"
            @click="restClick"
            title="重置"
          ></Button> -->
          <Button
            type="error"
            v-if="permissionObj.clear"
            class="skinWarning del-btn"
            icon="ios-eye-off-outline"
            @click="shieldByIds()"
            :title="this.$t('but_clear')"
          ></Button>
          <Button
            class="daoChu-btn"
            id="exportData"
            v-if="permissionObj.export"
            type="primary"
            @click="exportClick"
            :title="this.$t('but_export')"
          >
            <i class="iconfont icon-icon-derive" />
          </Button>

          <!-- <Dropdown @on-click="moreBtnClick">
              <Button class="more-btn">
                  更多
                  <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                  <Dropdown v-if="permissionObj.export" placement="right-start"  @on-click="moreBtnClick">
                      <DropdownItem>
                          {{$t('but_data_export')}}
                          <Icon type="ios-arrow-forward"></Icon>
                      </DropdownItem>
                      <DropdownMenu slot="list"  style="text-align: center;">
                          <DropdownItem name='exportClick("notAll")'>{{$t('but_export_checked')}}</DropdownItem>
                          <DropdownItem name='exportClick("all")'>{{$t('but_export_all')}}</DropdownItem>
                      </DropdownMenu>
                  </Dropdown>
                  <DropdownItem style="text-align: left;" v-if="permissionObj.list" name='shieldByIds()'>清除</DropdownItem>
              </DropdownMenu>
          </Dropdown> -->
        </div>
      </div>
    </div>

    <div class="section-body contentBox_bg">
      <div class="section-body-content">
        <div class="dialTest-tab-content">
          <div class="privateLine-tab-content">
            <Loading :loading="loading"></Loading>
            <Table
              ref="specialtableList"
              stripe
              :columns="columns"
              class="fixed-left-right"
              :data="specialtableList"
              :no-data-text="
                loading
                  ? ''
                  : specialtableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              @on-sort-change="sortChange"
              size="small"
            >
              <template slot-scope="{ row }" slot="orgName">
                {{
                  row.orgName === undefined ||
                  row.orgName === null ||
                  row.orgName === "" ||
                  row.dealStatus == 8 ||
                  row.faultType == 8
                    ? "--"
                    : row.orgName
                }}
              </template>
              <template slot-scope="{ row }" slot="faultDesc">
                <div class="fault-phenomenon">
                  <div v-if="row.faultDesc" @click="faultPhenomenonOpen(row)">
                    {{ row.faultDesc }}
                  </div>
                  <div v-else class="empty">——</div>
                </div>
              </template>
            </Table>
          </div>
        </div>
        <div
          class="tab-page"
          style="border-top: 0"
          v-if="specialtableList.length > 0"
        >
          <Page
            v-page
            :current.sync="currentNum"
            :page-size="query.pageSize"
            :total="totalCount"
            :page-size-opts="pageSizeOpts"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
            show-elevator
            show-sizer
          >
          </Page>
        </div>
      </div>
    </div>
    <!-- 故障现象详情 -->
    <Modal
      class="detail-modal"
      v-model="faultPhenomenon.open"
      :title="faultPhenomenon.title"
      width="460"
      :mask="true"
      sticky
      draggable
    >
      <div
        v-if="faultPhenomenon.content"
        style="
          text-align: center;
          color: var(--table_content_column_link_color, #05eeff);
          word-wrap: break-word;
          word-break: break-all;
        "
      >
        <p v-for="(item, index) in faultPhenomenon.content" :key="index">
          {{ item }}
        </p>
      </div>
      <div slot="footer">
        <!-- class="btnConfirm" -->
        <Button
          type="primary"
          size="small"
          @click="faultPhenomenon.open = false"
          >{{ $t("but_confirm") }}</Button
        >
      </div>
    </Modal>
  </section>
</template>
<script>
window.onload = function () {
  // const windowHeight = top.document.body.clientHeight;
  // document.getElementById("app").style.setProperty("height", windowHeight - 100 + "px");
  // top.window.onresize = function () {
  //   const windowHeight = top.document.body.clientHeight;
  //   document.getElementById("app").style.setProperty("height", windowHeight - 100 + "px");
  // };
  window.document.onmousedown = function () {
    localStorage.setItem("lastTime", new Date().getTime());
  };
};

function getQueryVariable(variable) {
  let src = window.frames.frameElement.getAttribute('src')
  if (src && src.indexOf('?' + variable + '=') > -1) {
    var temp = src.split('?' + variable + '=')[1].replace(/^\s*|\s*$/g, "");
    if (temp.indexOf('&') > -1) {
      temp = temp.substring(0, temp.indexOf('&'));
    }
    return temp;
  }
  if (src && src.indexOf('&' + variable + '=') > -1) {
    var temp = src.split('&' + variable + '=')[1].replace(/^\s*|\s*$/g, "");
    if (temp.indexOf('&') > -1) {
      temp = temp.substring(0, temp.indexOf('&'));
    }
    return temp;
  }
}


import moment from "moment";
import "@/config/page.js";
import global from "../../../common/global.js";
import locationreload from "@/common/locationReload";
// 专线告警页面
import specialechart from "./specialechartLook";
// 端口告警页面
import portechart from "./portechartLook";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
const synth = window.speechSynthesis;
const msg = new SpeechSynthesisUtterance();
import langFn  from '@/common/mixins/langFn'
export default {
  name: "systemAlarm",
   mixins: [langFn],
  components: {
    specialechart,
    portechart,
    TreeSelect
  },
  props: {
    tabData: {
      type: String,
      default: "",
    },
  },
  watch: {
    tabData: {
      handler(value) {
        if (
          this.specialtableList.length === 0 &&
          value === "systemAlarm"
        ) {
          this.initWebSocket(); //支持socket的方法
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.$nextTick(() => {
            // 说明是弹窗进来的界面，就不跳转了。
      if(getQueryVariable('days')){
            // 如果是跳转 
         
      }else{
      locationreload.loactionReload(this.$route.path.split("/")[1].toLowerCase());
      }
    })
    //第一次进来给操作时间赋值
    this.operationTime = new Date();
    this.initWebSocket();//支持socket的方法
    this.dataRefreh(); //启动定时器30秒检查一次，判断当前页不是第一页时，如果10分钟没有操作，则要刷新页面返回到第一页
    moment.locale("zh-cn");
    //获取分组下拉
    this.getGroupingSelect();
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.saveQuery.startTime = this.query.startTime = new Date(
      new Date().getTime() - 3600 * 1000 * 24 * 6
    ).format("yyyy-MM-dd 00:00:00");
    this.saveQuery.endTime = this.query.endTime = new Date().format(
      "yyyy-MM-dd 23:59:59"
    );
    // this.getSystemAlarmList(this.query); //直接查询故障清单列表
    //
    this.getTreeOrg();
    this.getSystemAlarmList(this.query);
    this.recordLog();
  },
  data() {
    return {
            currentSkin: sessionStorage.getItem('dark') || 1,
      interrefresh:null,
      flowFaultList: [
        { value: 11,label: this.$t('alarm_offline')}
        ],
      soundType: false,
      btnLoading:false,
      groupingList: [],
      //权限对象
      permissionObj: {},
      websock: null,
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      saveQuery: {
        startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        endTime: this.timeChange(Date.now()),
      },
      operatorList: [
        { value: 1, label: this.$t('server_China_Mobile') },
        { value: 2, label: this.$t('server_China_Unicom') },
        { value: 3, label: this.$t('server_China_Telecom') },
        { value: 4, label: this.$t('server_China_Broadcasting') },
        { value: 6, label: this.$t('comm_other') },
      ],
      bussTypeList: [
        { value: 1, label: this.$t('comm_not_recovered') },
        { value: 0, label: this.$t('comm_has_recovered') },
        { value: "", label: this.$t('comm_all') },
      ],
      tabActiove: 0,
      faultPhenomenon: {
        open: false,
        title: "",
        content: [],
      },
      routeFluctuation: {
        open: false,
        title: this.$t('server_fluctuation'),
        content: {
          text: "",
          title: "",
          topology: {},
        },
      },
      isSnmp: false,
      dataSource: 0,
      affectedCount: 0,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },
      treeValue:'',
      timeRange:[new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"),new Date().format2("yyyy-MM-dd 23:59:59")],
      timeOptions:{
        shortcuts: [
            {
                text: this.$t('comm_half_hour'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 30 * 60 * 1000);
                    return [ start.format("yyyy-MM-dd HH:mm:ss"),  end.format("yyyy-MM-dd HH:mm:ss")];
                }
            },
            {
                text: this.$t('comm_today'),
                value () {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime());
                    return [ new Date().format("yyyy-MM-dd 00:00:00"),  new Date().format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_yesterday'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24);
                    end.setTime(end.getTime() - 3600 * 1000 * 24);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_last_7'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_last_30'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_curr_month'),
                value () {
                    let nowDate = new Date();
                    let date = {
                      year: nowDate.getFullYear(),
                      month: nowDate.getMonth(),
                      date: nowDate.getDate(),
                    };
                    let end = new Date(date.year, date.month + 1, 0);
                    let start = new Date(date.year, date.month, 1);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_preced_month'),
                value () {
                    let date = new Date();
                    let day = new Date( date.getFullYear(), date.getMonth(),0).getDate();
                    let end = new Date( new Date().getFullYear(),new Date().getMonth() - 1,day);
                    let start = new Date( new Date().getFullYear(),  new Date().getMonth() - 1, 1);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            }
        ]
      },
      startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
        "yyyy-MM-dd 00:00:00"
      ), // new Date(2021, 2, 18),
      endTime: new Date().format2("yyyy-MM-dd 23:59:59"), // new Date(2021, 2, 19),
      currentTime: Date.now(),
      interValCurTime: null,
      //搜索字段
      query: {
        userId: "",
        startTime: "",
        endTime: "",
        orgId: "",
        fieldName: "", //排序字段名
        orderBy: "", //排序方式
        faultType: "30", //工单类型(流量拥塞总类型 ，9--专线拥塞 ， 10--端口拥塞 30--系统告警),
        isSpecialLine: "", //故障分类
        opt: "", //运营商
        recoveryed: "1", //是否恢复（1未恢复，0已恢复）
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
      },
      // keepState:0,
      //界面操作的时间
      operationTime: "",
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //表格数据
      specialtableList: [],
      title: {
        faultType: 0,
        errorIps: "",
        reason: "",
      },
      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 30,
          className: "bgColor",
          align: "center",
          // fixed: 'left',
        },
        {
          title: this.$t('alarm_start_time'),
          key: "faultStartTime",
          align: "left",
          className: "bgColor",
          sortable: "custom",
          width: 180,
          render: (h, params) => {
            let str = params.row.faultStartTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : str
            );
          },
        },
         {
          title: this.$t('comm_org'),
          key: "orgName",
          align: "left",
          minWidth: 100,
           tooltip:true,
        },
        // {
        //   title: this.$t('alarm_fault_type'),
        //   key: "faultType",
        //   align: "left",
        //   minWidth: 120,
        //   render: (h, params) => {
        //     let str = Number(params.row.faultType),
        //       text = "";
        //     switch (str) {
        //       case 11:
        //         text = "采集器离线";
        //         break;
        //       default:
        //         text = "--";
        //         break;
        //     }
        //     return h("span", text);
        //   },
        // },
        {
          title: this.$t('alarm_symptom'),
          key: "faultDesc",
          slot: "faultDesc",
          align: "left",
          minWidth: 260,
        },
        {
          title: this.$t('alarm_failure_duration'),
          key: "dealDuration",
          align: "left",
          minWidth: 120,
          render: (h, params) => {
            let str = params.row.dealDuration;
            return h(
              "span",
              str === undefined ||
                str === null ||
                str === "" 
                ? "--"
                : str
            );
          },
        },
        {
          title: this.$t('alarm_failure_status'),
          key: "recoveryed",
          align: "left",
          minWidth: 100,
          render: (h, params) => {
            let str = Number(params.row.recoveryed),
              text = "--";
            switch (str) {
              case 1:
                text = this.$t('common_unrecovered');
                break;
              case 0:
                text = this.$t('common_recovered');
                break;
              default:
                text = "--";
                break;
            }
            return h(
              "span",
              {
                class:
                  str === 1 ? "action-red" : str === 0 ? "action-green" : "",
              },
              text
            );
          },
        },
        {
          title: this.$t('comm_operate'),
          width: "100",
          align: "center",
          className: "bgColor",
          // fixed: 'right',
          render: (h, params) => {
            const row = params.row;
            return h('Tooltip', 
                  {
                    props: {
                      placement: 'left-end',
                      transfer: true
                    }
                  }, [
                      h(
                        "span",
                        {
                          class: this.currentSkin == 1 ? "look1-btn":"light-look1-btn",
                           style: {
                            display: this.permissionObj.look
                              ? "inline-block"
                              : "none",
                            marginRight: '0px',
                          },
                          on: {
                            click: () => {
                              if( this.permissionObj.look){
                                this.faultPhenomenonOpen(row)
                              }else{
                                console.log("无权限")
                              }
                              
                            },
                          },
                        },
                      ),
                    h('span', {slot: 'content', }, this.permissionObj.look ? this.$t('server_view'):"")
                ])
          },
        },
      ],
      audioType: {
        id: "",
        defValue: "",
        creatorId: "",
      },
      specialChart: {
        //专线趋势图
        show: false,
        clickTime: new Date().getTime() + "_" + Math.random(),
        data: {},
        delayParam: {
          preNodeIp: "",
          nodeIp: "",
          linkId: "",
          startTime: "",
          endTime: "",
          queryType: 2,
          special: true,
          High: false,
          level: null,
          snmp: false,
        },
        flowParam: {
          specialId: null,
          startTime: "",
          endTime: "",
          level: null,
        },
        rateParam: {
          startTime: "",
          endTime: "",
          specialAIp: "",
          specialZIp: "",
          specialId: "",
          linkIds: "",
          dataSource: "",
          specialIp: "",
          level: null,
        },
      },
      portChart: {
        //端口趋势图
        show: false,
        clickTime: new Date().getTime() + "_" + Math.random(),
        data: {},
        delayParam: {
          preNodeIp: "",
          nodeIp: "",
          linkId: "",
          startTime: "",
          endTime: "",
          queryType: 2,
          special: true,
          High: false,
          level: null,
          snmp: false,
        },
        flowParam: {
          specialId: null,
          startTime: "",
          endTime: "",
          level: null,
        },
        rateParam: {
          startTime: "",
          endTime: "",
          specialAIp: "",
          specialZIp: "",
          specialId: "",
          linkIds: "",
          dataSource: "",
          specialIp: "",
          level: null,
        },
      },
    };
  },
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    this.query.pageNo = this.currentNum;
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    this.setInterTime = setInterval(this.checkTimeout, 1000);
    this.interrefresh = setInterval(()=>{
      this.getSystemAlarmList(this.query);
    }, 1000*60);
    // this.initWebSocket();//支持socket的方法
    document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    top.document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    moreBtnClick(val) {
      eval(`this.${val}`);
    },
    dateChange(val,type){
      // if (val[0] === val[1]) {
        if(type === 'date') {
           this.timeRange = [val[0],new Date(val[1]).format("yyyy-MM-dd 23:59:59")]

        }
       
      // }
    },
    // 列表排序
    sortChange(e) {
      if (e.order == "normal") {
        this.query.orderBy = "";
        this.query.fieldName = "";
      } else {
        this.query.orderBy = e.order;
        this.query.fieldName = e.key;
      }
      this.getSystemAlarmList(this.query);
    },
    // 点击故障状态切换获取类型
    getTabState(index, val) {
      if (this.tabActiove != index) {
        this.tabActiove = index;
        this.query.recoveryed = val;
        this.queryClick();
      }
    },
    checkTimeout() {
      this.currentsTime = new Date().getTime(); //更新当前时间
      this.lastTime = localStorage.getItem("lastTime");
    },
    focusFn() {
      this.getTreeOrg()
      },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    onClear(){
      this.query.orgId = ''
      this.treeValue = ''
    },
    choicesOrg() {
      this.orgTree = true;
    },
    // 获取分组下拉
    getGroupingSelect() {
      this.$http
        .post("/group/list", { pageNo: 1, pageSize: 10000 })
        .then((res) => {
          this.groupingList = res.data.records;
        });
    },
    queryClick() {
      this.btnLoading = true
      this.recordLog();
      //点击搜索
      this.query.pageNo = this.currentNum = 1;

      let startVal = moment(
        this.timeRange[0] === "" || this.timeRange[0] == undefined
          ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0))
          : this.timeRange[0],
        "YYYY-MM-DD hh:mm:ss"
      ).valueOf();
      let endVal = moment(
        this.timeRange[1] === "" || this.timeRange[1] == undefined
          ? new Date()
          : this.timeRange[1],
        "YYYY-MM-DD 23:59:59"
      ).valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t('warning_time_not_exceed_62'));
        this.btnLoading = false;
        return;
      }
      this.saveQuery.startTime = this.query.startTime = new Date(
        this.timeRange[0]
      ).format("yyyy-MM-dd HH:mm:ss");
      this.saveQuery.endTime = this.query.endTime = new Date(
        this.timeRange[1]
      ).format("yyyy-MM-dd HH:mm:ss");
      if (this.timeRange[0] == "" || this.timeRange[0] == undefined) {
        this.saveQuery.startTime = this.query.startTime = new Date(
          new Date().getTime() - 3600 * 1000 * 24 * 6
        ).format("yyyy-MM-dd 00:00:00");
      }
      if (this.timeRange[1] == "" || this.timeRange[1] == undefined) {
        this.saveQuery.endTime = this.query.endTime = this.timeChange(
          Date.now()
        );
      }
      if (this.query.faultType == undefined) {
        this.query.faultType = 20;
      }
      this.getSystemAlarmList(this.query);
    },
    restClick() {
      this.timeRange = [new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"),new Date().format2("yyyy-MM-dd 23:59:59")]
      this.saveQuery.startTime = new Date(
        new Date().getTime() - 3600 * 1000 * 24 * 6
      ).format("yyyy-MM-dd 00:00:00");
      this.saveQuery.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.startTime = new Date(
        new Date().getTime() - 3600 * 1000 * 24 * 6
      ).format("yyyy-MM-dd 00:00:00");
      this.query.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.faultType = 20;
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.orgId = "";
      this.query.isSpecialLine = "";
      this.query.opt = "";
      this.treeValue = "";
    },
    actionClick(row, type) {
      let _self = this;
      _self.indexHome.show = false;
      _self.snmpindexHome.show = false;
      _self.title.faultType =
          row.faultType == 1
              ? this.$t('dash_interrupt')
              : row.faultType == 2
                  ? this.$t('dash_delay_deterioration')
                  : row.faultType == 3
                      ? this.$t('dash_packet_loss')
                      : "";
      _self.title.errorIps = row.errorIps;
      _self.title.reason =
          row.faultCauseType == 0
              ? this.$t('comm_unknown')
              : row.faultCauseType == 1
                  ? this.$t('comm_Blackout')
                  : row.faultCauseType == 2
                      ? this.$t('comm_cut')
                      : row.faultCauseType == 3
                          ? this.$t('comm_congestion')
                          : "";
      switch (type) {
        case "linkNum": //影响链路数
          _self.dataSource = row.dataSource;
          _self.affectedCount = Number(row.affectedCount);
          if (row.dataSource == 1) {
            this.isSnmp = true;
            _self.snmpindexHome.data = row;
            _self.snmpindexHome.show = true;
          } else {
            this.isSnmp = false;
            _self.indexHome.data = row;
            _self.indexHome.show = true;
          }
          break;
        case "handle": //处理
          _self.action.type = 2;
          _self.action.data = row;
          _self.action.show = true;
          break;
        case "append": //追加
          _self.action.type = 1;
          _self.action.data = row;
          _self.action.show = true;
          break;
      }
    },
    getSystemAlarmList(param) {
      let _self = this;
      _self.loading = true;
      _self.$http
        .PostJson("/fault/getAlarmList", param)
        .then((res) => {
          if (res.code === 1) {
            if (res.data) {
              _self.specialtableList = res.data.records;
              _self.totalCount = res.data.total || 0;
              //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
              let _that = this;
              setTimeout(function () {
                let objData = _that.$refs.specialtableList.$refs.tbody.objData;
                for (let key in objData) {
                  if (_that.selectedIds.has(objData[key].id)) {
                    objData[key]._isChecked = true;
                  }
                }
              }, 0);
            } else {
              _self.specialtableList = [];
            }
          } else {
            this.$Message.warning(res.msg);
          }
        })
        .finally(() => {
          _self.loading = false;
          _self.btnLoading = false
        });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getSystemAlarmList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getSystemAlarmList(this.query);
    },
    // 打开故障现象详情
    faultPhenomenonOpen(row, sign) {
    console.log(row);
      this.faultPhenomenon.title = this.$t('comm_symptom');
      const list = (this.faultPhenomenon.content = []);
      if (row && row.faultDesc) {
        Array.prototype.push.apply(
          list,
          String(row.faultDesc)
            .split(";")
            .map((text, index) => {
              return text.trim();
            })
        );
      }
      this.faultPhenomenon.open = true;
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        now.toTimeString().substr(0, 8)
      );
    },
    //屏蔽清除
    shieldByIds(id) {
      var selectedIdsArrary = Array.from(this.selectedIds);
      var ids = "";
      if (id) {
        ids = id;
      } else {
        if (selectedIdsArrary.length == 0) {
          this.$Message.warning(this.$t('warning_select_clear_first'));;
          return;
        }
        for (var i = 0; i < selectedIdsArrary.length; i++) {
          if (i === 0) {
            ids += selectedIdsArrary[i];
          } else {
            ids += "," + selectedIdsArrary[i];
          }
        }
      }
      var param = {
        ids: ids,
        type: 1,
      };
      top.window.$iviewModal.confirm({
                title: this.$t('comm_tip'),
        content: '<p>'+this.$t('dash_clear')+'</p>',
        onOk: () => {
          this.$http.wisdomPost("/fault/shieldByIds", param).then((res) => {
            if (res && res.code === 1) {
                    this.$Message.success(this.$t('dash_clear_successfully'));

              this.query.pageNo = this.currentNum = 1;
              this.selectedIds = new Set();
              this.getSystemAlarmList(this.query);
            } else {
              this.$Message.warning(res.msg);
            }
          });
        },
      });
    },
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        var data = this.$refs.specialtableList.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },
    //导出
    exportClick(val) {
      this.saveLog();
      let _self = this;
      let fileName = this.$t('server_faults') + ".xlsx";
      // let fileName = '全部故障清单.xlsx';
      // if (val === 'notAll') {
      //     let exportIds = Array.from(this.selectedIds);
      //     if (exportIds.length < 1) {
      //         this.$Message.warning(this.$t('specquality_select'));
      //         return;
      //     }
      //     fileName = this.$t('server_faults') + ".xlsx";
      //     this.query.ids = exportIds;
      //     this.query.idArr = exportIds;
      // }
      this.$Loading.start();
      let param = Object.assign({}, this.query);
      this.$axios({
        url: "/fault/exportFaultXlsx",
        method: "post",
        data: this.query,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // var fileName = "";
            // fileName = "故障清单.xlsx";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          this.selectedIds = new Set();
          this.query.ids = null;
          this.queryClick(); 
          this.$Loading.finish();
        });
    },

    setQuery(flag, e) {
      if (flag) {
        this.$refs[e].query = "";
      }
    },
    optChange(val) {
      if (val === 0) {
        this.query.opt = "";
      } else {
        this.query.opt = val;
      }
    },
    initWebSocket() {
      //初始化weosocket
      let token = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
      const wsuri =
        "wss://" +
        this.$baseUrl.replace(top.window.location.protocol + "//", "") +
        "/faultSocket/" +
        token;
      if ("WebSocket" in window) {
        this.websock && this.websock.close();
        if (!this.websock) {
          this.websock = new WebSocket(wsuri);
          this.websock.onopen = this.websocketonopen;
          this.websock.onmessage = this.websocketonmessage;
          this.websock.onerror = this.websocketonerror;
          this.websock.onclose = this.websocketclose;
        } else {
        }
      } else {
        //不支持websoket时只展示列表
        this.getSystemAlarmList(this.query);
      }
    },
    websocketsend(Data) {
      //数据发送
      if (this.websock.readyState === 0) {
        this.initWebSocket();
      } else {
        this.websock.send(Data);
      }
    },
    websocketclose(e) {
      //关闭
      console.log("socket断开连接", e);
      if (
        this.$route.name == this.$t('alarm_fault_list') &&
        this.tabData === "systemAlarm"
      ) {
        this.getSystemAlarmList(this.query);
      }
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("socket已建立链接");
      let param = Object.assign({}, this.query);
      param.userId = JSON.parse(sessionStorage.getItem("accessToken")).user.id;
      this.websocketsend(JSON.stringify(param));
    },
    websocketonerror() {
      //连接建立失败重连
      this.initWebSocket();
    },
    websocketonmessage(e) {
      //数据接收
      let redata = JSON.parse(e.data);
      this.loading = false;

      //判断如果当前页数大于1，则不能刷新列表
      let datas = redata.data.records[0];
      if (this.query.pageNo == 1) {
        if (datas || datas != "") {
          // this.query.compareType = "";
          // this.query.faultStartTime = "";
          // this.query.faultEndTime = "";
          this.$http
            .PostJson("/fault/getAlarmList", this.query)
            .then((res) => {
              if (res.code === 1) {
                if (res.data) {
                  this.specialtableList = res.data.records;
                  this.totalCount = res.data.total || 0;
                  //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                  setTimeout(() => {
                    let objData =
                      this.$refs.specialtableList.$refs.tbody.objData;
                    for (let key in objData) {
                      if (this.selectedIds.has(objData[key].id)) {
                        objData[key]._isChecked = true;
                      }
                    }
                  }, 0);
                } else {
                  this.specialtableList = [];
                }
              } else {
                this.$Message.warning(res.msg);
              }
            });
        }
      }
    },
    // 定时刷新数
    dataRefreh() {
      if (this.tabData === "systemAlarm") {
        // 计时器为空，操作
        setInterval(() => {
          console.log("systemAlarm");
          //如果10分钟没有操作，则要刷新页面返回到第一页
          if (this.query.pageNo != 1) {
            let nowDate = new Date();
            let z = nowDate.getTime() - this.operationTime.getTime();
            if (z > 600000) {
              this.pageChange(1);
            } else {
            }
          }
        }, 30000);
      }
    },
    //点击当前页面触发事件
    bodyClick() {
      let nowDate = new Date();
      this.operationTime = nowDate; //刷新操作时间
    },

    // 端口告警
    getPort(rowDta){

      this.specialChart.show = false;
    //点击时间
      this.portChart.clickTime = new Date().getTime() + "_" + Math.random();
      console.log("点击时间：" + this.specialChart.clickTime);
      /*时延丢包趋势图参数设置*/
      this.portChart.delayParam.preNodeIp = rowDta.specialAip;
      this.portChart.delayParam.nodeIp = rowDta.specialIp;
      this.portChart.delayParam.linkId = rowDta.linkIds;
      this.portChart.delayParam.startTime = new Date(
        new Date(rowDta.faultStartTime).getTime() - 3 * 60 * 60 * 1000
      ).format("yyyy-MM-dd HH:mm:ss");
      if (rowDta.recoveryed == 1) {
        this.portChart.delayParam.endTime = new Date().format(
          "yyyy-MM-dd HH:mm:ss"
        );
      } else if (rowDta.recoveryed == 0) {
        this.portChart.delayParam.endTime = new Date(
          new Date(rowDta.faultEndTime).getTime() + 3 * 60 * 60 * 1000
        ).format("yyyy-MM-dd HH:mm:ss");
      }
      this.portChart.delayParam.queryType = 2;
      this.portChart.delayParam.special = true;
      this.portChart.delayParam.High = false;
      this.portChart.delayParam.level = null;
      this.portChart.delayParam.snmp = false;
      /*流量趋势图参数*/
      this.portChart.flowParam.specialId = rowDta.bizDataId;
      this.portChart.flowParam.startTime = new Date(
        new Date(rowDta.faultStartTime).getTime() - 3 * 60 * 60 * 1000
      ).format("yyyy-MM-dd HH:mm:ss");
      if (rowDta.recoveryed == 1) {
        this.portChart.flowParam.endTime = new Date().format(
          "yyyy-MM-dd HH:mm:ss"
        );
      } else if (rowDta.recoveryed == 0) {
        this.portChart.flowParam.endTime = new Date(
          new Date(rowDta.faultEndTime).getTime() + 3 * 60 * 60 * 1000
        ).format("yyyy-MM-dd HH:mm:ss");
      }
      this.portChart.flowParam.level = null;
      /*可用率趋势图参数*/
      this.portChart.rateParam.startTime = new Date(
        new Date(rowDta.faultStartTime).getTime() - 3 * 60 * 60 * 1000
      ).format("yyyy-MM-dd HH:mm:ss");
      if (rowDta.recoveryed == 1) {
        this.portChart.rateParam.endTime = new Date().format(
          "yyyy-MM-dd HH:mm:ss"
        );
      } else if (rowDta.recoveryed == 0) {
        this.portChart.rateParam.endTime = new Date(
          new Date(rowDta.faultEndTime).getTime() + 3 * 60 * 60 * 1000
        ).format("yyyy-MM-dd HH:mm:ss");
      }
      this.portChart.rateParam.specialAIp = rowDta.specialAip;
      this.portChart.rateParam.specialZIp = rowDta.specialZip;
      this.portChart.rateParam.specialId = rowDta.bizDataId;
      this.portChart.rateParam.linkIds = rowDta.linkIds;
      this.portChart.rateParam.dataSource = rowDta.dataSource;
      this.portChart.rateParam.specialIp = rowDta.specialIp;
      this.portChart.rateParam.level = null;
      this.portChart.data = {
        delayParam: this.portChart.delayParam,
        flowParam: this.portChart.flowParam,
        rateParam: this.portChart.rateParam,
        markPoint: rowDta.faultStartTime,
        markPointGreen: rowDta.faultEndTime,
        specialDetails: rowDta.faultDesc,
        title: rowDta.title,
        alarmType: rowDta.faultType,
      };

      // 端口告警的 特殊参数
      // queryObjectType : 1 专线，2 端口
      if (rowDta.faultType == 10) {
        this.portChart.flowParam.deviceId = rowDta.deviceAId;
        this.portChart.flowParam.interfaceId = rowDta.interfaceAId;
        this.portChart.flowParam.queryObjectType = 2;
        this.portChart.delayParam.showState = 0;
        this.portChart.rateParam.showState = 0;
      }

      this.portChart.show = true;
    },
    //获取趋势图参数
    getSpecial(rowDta) {
      this.portChart.show = false;
      //点击时间
      this.specialChart.clickTime = new Date().getTime() + "_" + Math.random();
      console.log("点击时间：" + this.specialChart.clickTime);
      /*时延丢包趋势图参数设置*/
      this.specialChart.delayParam.preNodeIp = rowDta.specialAip;
      this.specialChart.delayParam.nodeIp = rowDta.specialIp;
      this.specialChart.delayParam.linkId = rowDta.linkIds;
      this.specialChart.delayParam.startTime = new Date(
        new Date(rowDta.faultStartTime).getTime() - 3 * 60 * 60 * 1000
      ).format("yyyy-MM-dd HH:mm:ss");
      if (rowDta.recoveryed == 1) {
        this.specialChart.delayParam.endTime = new Date().format(
          "yyyy-MM-dd HH:mm:ss"
        );
      } else if (rowDta.recoveryed == 0) {
        this.specialChart.delayParam.endTime = new Date(
          new Date(rowDta.faultEndTime).getTime() + 3 * 60 * 60 * 1000
        ).format("yyyy-MM-dd HH:mm:ss");
      }
      this.specialChart.delayParam.queryType = 2;
      this.specialChart.delayParam.special = true;
      this.specialChart.delayParam.High = false;
      this.specialChart.delayParam.level = null;
      this.specialChart.delayParam.snmp = false;
      /*流量趋势图参数*/
      this.specialChart.flowParam.specialId = rowDta.bizDataId;
      // this.specialChart.flowParam.startTime=this.saveQuery.startTime;
      // this.specialChart.flowParam.endTime=this.saveQuery.endTime;
      this.specialChart.flowParam.startTime = new Date(
        new Date(rowDta.faultStartTime).getTime() - 3 * 60 * 60 * 1000
      ).format("yyyy-MM-dd HH:mm:ss");
      if (rowDta.recoveryed == 1) {
        this.specialChart.flowParam.endTime = new Date().format(
          "yyyy-MM-dd HH:mm:ss"
        );
      } else if (rowDta.recoveryed == 0) {
        this.specialChart.flowParam.endTime = new Date(
          new Date(rowDta.faultEndTime).getTime() + 3 * 60 * 60 * 1000
        ).format("yyyy-MM-dd HH:mm:ss");
      }
      this.specialChart.flowParam.level = null;
      /*可用率趋势图参数*/
      // this.specialChart.rateParam.startTime=this.saveQuery.startTime;
      // this.specialChart.rateParam.endTime=this.saveQuery.endTime;
      this.specialChart.rateParam.startTime = new Date(
        new Date(rowDta.faultStartTime).getTime() - 3 * 60 * 60 * 1000
      ).format("yyyy-MM-dd HH:mm:ss");
      if (rowDta.recoveryed == 1) {
        this.specialChart.rateParam.endTime = new Date().format(
          "yyyy-MM-dd HH:mm:ss"
        );
      } else if (rowDta.recoveryed == 0) {
        this.specialChart.rateParam.endTime = new Date(
          new Date(rowDta.faultEndTime).getTime() + 3 * 60 * 60 * 1000
        ).format("yyyy-MM-dd HH:mm:ss");
      }
      this.specialChart.rateParam.specialAIp = rowDta.specialAip;
      this.specialChart.rateParam.specialZIp = rowDta.specialZip;
      this.specialChart.rateParam.specialId = rowDta.bizDataId;
      this.specialChart.rateParam.linkIds = rowDta.linkIds;
      this.specialChart.rateParam.dataSource = rowDta.dataSource;
      this.specialChart.rateParam.specialIp = rowDta.specialIp;
      this.specialChart.rateParam.level = null;
      this.specialChart.data = {
        delayParam: this.specialChart.delayParam,
        flowParam: this.specialChart.flowParam,
        rateParam: this.specialChart.rateParam,
        markPoint: rowDta.faultStartTime,
        markPointGreen: rowDta.faultEndTime,
        // specialName:rowDta.specialName,
        specialDetails: rowDta.faultDesc,
        title: rowDta.title,
        alarmType: rowDta.faultType,
      };
      this.specialChart.show = true;
    },
    recordLog() {
      this.$http
        .wisdomPost("/audit/queryList", { tab: "流量拥塞" })
        .then((res) => {});
    },
    saveLog() {
      //直接添加安全审计日志 开始
      this.$http
        .wisdomPost("/audit/saveLog", {
          url: "/fault/exportFaultXlsx",
          funName: "流量拥塞",
          description: "导出流量拥塞",
          operationType: 13,
          eventType: 7,
          alarmType: 0,
          logType: 2,
          isDel: 0,
        })
        .then((res) => {});
      //直接添加安全审计日志 结束
    },
  },
  destroyed() {
    // this.websocketclose();
    if (this.websock) {
      this.websock.close();
    }

    if (this.interrefresh) {
      clearInterval(this.interrefresh);
      this.interrefresh = null;
    }
  },
};
</script>

<style lang='less' scoped>
/deep/ .action-red {
  color: #fe5c5c;
}
/deep/ .action-green {
  color: #07c5a3;
}
/deep/ .ivu-table-cell a {
  color: #05eeff;
}

.urgentTitle {
  text-align: left;
  margin-bottom: 10px;
}
.butBoxStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20px;
  .fault-top-box {
    margin-bottom: 0 !important;
  }
  ul {
    display: flex;
    align-items: center;
    list-style: none;
    border-radius: 5px;
    // border: 1px solid #04478e;
    border: 1px solid var(--networdFault_type_border_color, #04478e);
  }
  li {
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    color: #5ca0d5;
    // color: var(--font_color,#dcdee2);
    color: var(--networdFault_type_border_normal_font_color, #5ca0d5);
    cursor: pointer;
    // border: 1px solid #04478e;
    border: 1px solid var(--networdFault_type_border_normal_color, #04478e);
    // border: 1px solid var(--border_color,#dcdee2);
  }
  li:last-child {
    // border-right: 1px solid var(--border_color,#dcdee2);
  }
  .tabStyle {
    // background: #02b8fd;
    // color: #060d15;
    // border-color: #02b8fd;

    background: var(--search_check_box_background_color, #02b8fd);
    // color: #060d15;
    color: var(--search_check_box_font_color, #060d15);
    border-color: #02b8fd;
  }
}
.faultDuration {
  align-items: center;
  min-width: 22%;
  .fault-top-item {
    margin-left: 0 !important;
  }
  .faultCustomize {
    display: inline-block;
    //align-items: center;
    //margin-left:5px;
    .ivu-input-wrapper {
      width: 90px;
      margin: 0 5px;
    }
  }
  ul {
    //display: flex;
    align-items: center;
    display: inline-block;
    list-style: none;
  }
  li {
    display: inline-block;
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    color: var(--font_color, #dcdee2);
    cursor: pointer;
    border: 1px solid var(--border_color, #dcdee2);
    border-right: none;
  }
  li:last-child {
    border-right: 1px solid var(--border_color, #dcdee2);
  }
  .tabStyle {
    background: #2d8cf0;
    color: #fff;
    border-color: #2d8cf0;
  }
}
.ivu-input::-webkit-input-placeholder {
  font-size: 12px !important;
}
.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}
.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  background: #f1f6fe !important;
}
.task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
  height: auto !important;
  min-height: 32px;
}
.multipleSelect .ivu-select-input {
  padding: 0 !important;
}
.fault-phenomenon {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  color: var(--table_content_column_link_color, #05eeff);
}

.fault-phenomenon > div {
  cursor: pointer;
}

.fault-phenomenon > div.empty {
  color: black;
  cursor: default;
}
.zhibiao {
  background-image: url("../../../assets/wisdom/zhibiao.png") !important;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 22px 22px;
}
</style>
<style scoped lang="less">
.screenCondition {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .fault-top-box {
    width: 22% !important;
    margin-right: 35px;
    label {
      min-width: 70px;
      text-align: right;
    }
    .fault-top-item {
      margin-left: 70px;
      /deep/.ivu-input {
        padding-left: 10px !important;
      }
    }
  }
  .timeBox {
    width: calc(44% + 35px) !important;
    .timeStyle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .reach {
        text-align: center;
        padding: 0px 45px;
      }
    }
    /deep/.ivu-btn-default {
      display: none;
    }
  }
}
.soundPlay {
  margin-right: 24px;
  cursor: pointer;
}
.action-index-modal {
  /deep/.ivu-modal-body {
    overflow: auto;
    max-height: 550px;
  }
}
.wisdom-fault .wisdom-fault-top .select-box {
  width: 20%;
}
.wisdom-fault .wisdom-fault-top .keyword-box {
  width: 25%;
}

/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}
/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}
.daoChu-btn {
  width: 60px !important;
}
.more-btn {
  width: 100% !important;
  height: 35px !important;
  color: #02b8fd;
  background-color: #061824;
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(
      to right,
      var(--input_b_color, #ffffff),
      var(--input_b_color, #ffffff)
    ),
    linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
</style>


