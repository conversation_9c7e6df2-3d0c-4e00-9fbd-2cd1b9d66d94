<template>
  <!-- 紧急通知 -->
  <section
    class="sectionBox"
    style="min-width: 100% !important"
    @click="bodyClick"
  >
    <div class="section-top">
      <Row class="fn_box">
        <Col span="9">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
              >
              </DatePicker>
            </div>
          </div>
        </Col>
      </Row>
      <div
        class="butBoxStyle fn_tool"
        style="justify-content: right; padding-bottom: 0"
      >
        <div style="display: flex; align-items: center">
          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.list"
            icon="ios-search"
            @click="queryClick"
            :title="$t('common_query')"
          ></Button>
          <Button
            type="primary"
            v-if="permissionObj.reset"
            class="skinPrimary reset-btn"
            icon="md-refresh"
            @click="restClick"
            :title="this.$t('but_reset')"
          ></Button>
          <!-- <Button type="primary" v-if="permissionObj.export" id="exportData" 
          class="skinPrimary export-btn" icon="md-open" @click="exportClick" :title="this.$t('but_export')"
          ></Button > -->
          <Button
            class="daoChu-btn"
            id="exportData"
            v-if="permissionObj.export"
            type="primary"
            @click="exportClick"
            :title="this.$t('but_export')"
          >
            <i class="iconfont icon-icon-derive" />
          </Button>
          <Button
            type="error"
            v-if="permissionObj.clear"
            class="skinError del-btn"
            icon="ios-trash-outline"
            @click="shieldByIds()"
            :title="this.$t('but_clear')"
          ></Button>
        </div>
      </div>
    </div>

    <div class="section-body contentBox_bg">
      <div class="section-body-content">
        <div class="dialTest-tab-content">
          <div class="privateLine-tab-content">
            <Loading :loading="loading"></Loading>
            <Table
              ref="urgenttableList"
              stripe
              :columns="columns"
              :data="urgenttableList"
              :no-data-text="
                loading
                  ? ''
                  : urgenttableList.length > 0
                  ? ''
                 : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
              "
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              @on-sort-change="sortChange"
              size="small"
            >
              <template slot-scope="{ row }" slot="faultDesc">
                <div class="fault-phenomenon">
                  <div v-if="row.faultDesc" @click="faultPhenomenonOpen(row)">
                    {{ row.faultDesc }}
                  </div>
                  <div v-else class="empty">——</div>
                </div>
              </template>
            </Table>
          </div>
        </div>
        <div
          class="tab-page"
          style="border-top: 0"
          v-if="urgenttableList.length > 0"
        >
          <Page
            v-page
            :current.sync="currentNum"
            :page-size="query.pageSize"
            :total="totalCount"
            :page-size-opts="pageSizeOpts"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
            show-elevator
            show-sizer
          >
          </Page>
        </div>
      </div>
    </div>
    <!-- 故障现象详情 -->
    <Modal
       class="detail-modal"
      v-model="faultPhenomenon.open"
      :title="faultPhenomenon.title"
      width="460"
      :mask="true"
      sticky
      draggable
    >
      <div
        v-if="faultPhenomenon.content"
        style="
          text-align: center;
          color: var(--table_content_column_link_color, #05EEFF);
          word-wrap: break-word;
          word-break: break-all;
        "
      >
        <p
          v-for="(item, index) in faultPhenomenon.content"
          :key="index"
          style="text-align: left"
        >
          {{ item }}
        </p>
      </div>
      <div slot="footer">
        <Button
          type="primary"
          size="small"
          @click="faultPhenomenon.open = false"
          >{{ $t("but_confirm") }}</Button
        >
      </div>
    </Modal>

    <!-- 紧急通知模态框 -->
    <Modal
      v-model="associationModal.show"
      title="紧急通知"
      :width="modalWidth"
      :styles="{ top: '100px' }"
      :footer-hide="true"
      :mask="true"
      sticky
      draggable
      class="associationModal"
    >
      <association
        v-if="associationModal.show"
        :rowData="associationModal.rowData"
      ></association>
    </Modal>
  </section>
</template>
<script>
window.onload = function () {
  // const windowHeight = top.document.body.clientHeight;
  // document.getElementById("app").style.setProperty("height", windowHeight - 100 + "px");
  // top.window.onresize = function () {
  //   const windowHeight = top.document.body.clientHeight;
  //   document.getElementById("app").style.setProperty("height", windowHeight - 100 + "px");
  // };
  window.document.onmousedown = function () {
    localStorage.setItem("lastTime", new Date().getTime());
  };
};

import moment from "moment";
import "@/config/page.js";
import Qs from "qs";
import global from "../../../common/global.js";
import { mapGetters, mapActions, mapState } from "vuex";
import { addDraggable } from "@/common/drag.js";
import association from "./association.vue";
import locationreload from "@/common/locationReload";
const synth = window.speechSynthesis;
const msg = new SpeechSynthesisUtterance();
export default {
  name: "urgentNotice",
  components: {
    association,
  },
  props:{
    tabData:{
      type: String,
      default:''
    }
  },
  watch:{
    tabData:{
      handler(value) {
        if (this.urgenttableList.length === 0 && value === 'urgentNotice') {
          this.initWebSocket();//支持socket的方法
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    //第一次进来给操作时间赋值
    this.operationTime = new Date();
    //this.initWebSocket();//支持socket的方法
    this.dataRefreh(); //启动定时器30秒检查一次，判断当前页不是第一页时，如果10分钟没有操作，则要刷新页面返回到第一页
    moment.locale("zh-cn");
    //获取分组下拉
    this.getGroupingSelect();
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.query.startTime =  new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
    this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
    // this.getUrgentNoticeList(this.query); //直接查询故障清单列表
    //
    this.getTreeOrg();
    this.recordLog();
  },
  data() {
    return {
            currentSkin: sessionStorage.getItem('dark') || 1,
      modalWidth:0,
      soundType:false,
      tabActiove: 0,
      groupingList: [],
      //权限对象
      permissionObj: {},
      websock: null,
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      saveQuery: {
        startTime:  new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"),
        endTime: this.timeChange(Date.now()),
      },
      faultPhenomenon: {
        open: false,
        title: "",
        content: [],
      },
      isSnmp: false,
      dataSource: 0,
      affectedCount: 0,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },
      treeValue:'',
      timeRange:[new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"),new Date().format2("yyyy-MM-dd 23:59:59")],
      timeOptions:{
        shortcuts: [
            {
                text: this.$t('comm_half_hour'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 30 * 60 * 1000);
                    return [ start.format("yyyy-MM-dd HH:mm:ss"),  end.format("yyyy-MM-dd HH:mm:ss")];
                }
            },
            {
                text: this.$t('comm_today'),
                value () {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime());
                    return [ new Date().format("yyyy-MM-dd 00:00:00"),  new Date().format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_yesterday'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24);
                    end.setTime(end.getTime() - 3600 * 1000 * 24);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_last_7'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_last_30'),
                value () {
                    const start = new Date();
                    const end = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_curr_month'),
                value () {
                    let nowDate = new Date();
                    let date = {
                      year: nowDate.getFullYear(),
                      month: nowDate.getMonth(),
                      date: nowDate.getDate(),
                    };
                    let end = new Date(date.year, date.month + 1, 0);
                    let start = new Date(date.year, date.month, 1);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            },
            {
                text: this.$t('comm_preced_month'),
                value () {
                    let date = new Date();
                    let day = new Date( date.getFullYear(), date.getMonth(),0).getDate();
                    let end = new Date( new Date().getFullYear(),new Date().getMonth() - 1,day);
                    let start = new Date( new Date().getFullYear(),  new Date().getMonth() - 1, 1);
                    return [  start.format("yyyy-MM-dd 00:00:00"),  end.format("yyyy-MM-dd 23:59:59")];
                }
            }
        ]
      },
      startOptions: {
        shortcuts: [
          {
            text: this.$t('comm_half_hour'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              const end = new Date();
              this.startTime = start;
              this.endTime = end;
              this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
              this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            },
          },
          {
            text: this.$t('comm_today'),
            value() {
              const start = new Date().format("yyyy-MM-dd 00:00:00");
              return start;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date(new Date().getTime() - 3600 * 1000 * 24).format("yyyy-MM-dd 00:00:00");
              // start.setTime(start.getTime() - 3600 * 1000 * 24);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date(new Date().getTime() - 3600 * 1000 * 24 * 29).format("yyyy-MM-dd 00:00:00");
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let start = new Date(date.year, date.month, 1);
              return start;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();

              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              return start;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
        ],
        disabledDate: (date) => {
          let endTime = this.endTime == '' ? Date.now() : this.endTime
          return date > Date.now() || date > new Date(endTime).valueOf()
        },
      },
      endOptions: {
        shortcuts: [
          {
            text: this.$t('comm_half_hour'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              const end = new Date();
              this.startTime = start;
              this.endTime = end;
              this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
              this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            },
          },
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date().format("yyyy-MM-dd 23:59:59");
              return end;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const end = new Date(new Date().getTime() - 3600 * 1000 * 24).format("yyyy-MM-dd 23:59:59");
              // end.setTime(end.getTime() - 3600 * 1000 * 24);
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const end = new Date().format("yyyy-MM-dd 23:59:59");
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const end = new Date().format("yyyy-MM-dd 23:59:59");
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0).format("yyyy-MM-dd 23:59:59");
              console.log(end,1111)
              return end;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              ).format("yyyy-MM-dd 23:59:59");
              return end;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  0
              ).getDate();
              let end = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  day
              );
              let start = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth() - 1,
                  1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
        ],
        disabledDate: (date) => {
          let startTime = this.startTime == '' ? '' : this.startTime
          return date > Date.now() || date < new Date(startTime).valueOf() - 8 * 3600 * 1000
        },
      },
      startTime:  new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"), // new Date(2021, 2, 18),
      endTime: new Date().format2("yyyy-MM-dd 23:59:59"), // new Date(2021, 2, 19),
      currentTime: Date.now(),
      interValCurTime: null,
      //搜索字段
      query: {
        userId: "",
        // tencentIds: "",
        startTime: "",
        endTime: "",
        // orgId: "",
        fieldName: "", //排序字段名
        orderBy: "", //排序方式
        faultType: "8", //工单类型
        // recoveryed: null, //故障状态
        // dataSource: "", //数据源
        // dealStatus: "", //工单状态
        // isSpecialLine: "", //故障分类
        // opt: "", //运营商
        // keyword: "", //关键字
        // groupId: "", //分组
        // delFlag: '0',//0正常，1删除，2清除
        // compareType:"",//1小于等于60,2大于60,3自定义,null全部
        // faultStartTime:"",//故障历时自定义开始时间
        // faultEndTime:"",//故障历时自定义结束时间
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
      },
      returnShow:false,
      // keepState:0,
      //界面操作的时间
      operationTime: "",
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100,200,500,1000],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //新建修改Modal参数
      modalParameter: {
        modalTitle: "", //标题(新建/修改)
        show: false, //控制是否显示
        loading: true,
      },
      //新建/修改Modal数据
      modalList: {
        factory: "", //设备厂家
        model: [], //设备型号
        // hierarchy:'',
        remarks: "", //备注
        oidNameCn: "", //名称
        oid: "", //OID
      },
      oidNameList: [], //新建修改名称下拉选项
      //表格数据
      urgenttableList: [],
      indexHome: {
        show: false,
        data: {},
      },
      snmpindexHome: {
        show: false,
        data: {},
      },
      title: {
        faultType: 0,
        errorIps: "",
        reason: "",
      },
      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 60,
          className: "bgColor",
          align: "left",
          fixed: 'left',
        },
        {
          title: this.$t('alarm_start_time'),
          key: "faultStartTime",
          align: "left",
          className: "bgColor",
          width: 180,
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.faultStartTime;
            return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                    ? "--"
                    : str
            );
          },
        },
        {
          title: this.$t('alarm_fault_type'),
          key: "faultType",
          align: "left",
          width: 140,
          render: (h, params) => {
            let str = Number(params.row.faultType),
                text = "";
            switch (str) {
              case 8:
                text = this.$t('dash_urgent_notice');
                break;
              default:
                text = "--";
                break;
            }
            return h("span", text);
          },
        },
        {
          title: this.$t('alarm_symptom'),
          key: "faultDesc",
          slot: "faultDesc",
          align: "left",
          minWidth: 260,
        },
        {
          title: this.$t('comm_operate'),
          width: "140",
          align: "left",
          className: "bgColor",
          render: (h, params) => {
            const row = params.row;
            const faultType = row.faultType;
            const array = [];
            array.push(
                h('Tooltip', 
                  {
                    props: {
                      placement: 'left-end'
                    }
                  }, [
                      h(
                        "span",
                        {
                          class: "look1-btn",
                          on: {
                            click: () => {
                              if(faultType == 8){
                                this.associationList(params.row);
                              }
                            },
                          },
                        },
                      ),
                    h('span', {slot: 'content', }, this.$t('server_view'))
                ])
            );
            return h("div", array);
          },
        },
      ],
      audioType:{
        id:'',
        defValue:'',
        creatorId:''
      },
      //紧急通知弹框
      associationModal:{
        show:false,
        rowData:{},
      }
    };
  },
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    this.modalWidth = document.body.clientWidth * 0.98
    this.query.pageNo = this.currentNum;
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    this.setInterTime = setInterval(this.checkTimeout, 1000);
    this.initWebSocket();//支持socket的方法
    document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    top.document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
  },
  methods: {
   handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 列表排序
    sortChange(e){
      if(e.order == 'normal'){
        this.query.orderBy = ''
        this.query.fieldName = ''
      }else{
        this.query.orderBy = e.order
        this.query.fieldName = e.key
      }
      this.getUrgentNoticeList(this.query)
    },
    checkTimeout() {
      this.currentsTime = new Date().getTime(); //更新当前时间
      this.lastTime = localStorage.getItem("lastTime");
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    renderContent(h, { root, node, data }) {
      return h(
          "span",
          {
            style: {
              display: "inline-block",
              width: "100%",
              cursor: "pointer",
            },
            on: {
              click: () => {
                this.clickTree1(data);
              },
            },
          },
          [
            h(
                "Tooltip",
                {
                  props: {
                    placement: "top-end",
                    transfer: true,
                  },
                },
                [
                  data.name, //控制树形显示的内容
                  h(
                      "span",
                      { slot: "content", style: { whiteSpace: "normal" } },
                      data.title //控制Tooltip显示的内容
                  ),
                ]
            ),
            h("span", {
              style: {
                display: "inline-block",
                float: "left",
                marginLeft: "32px",
              },
            }),
          ]
      );
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    setOrg(item) {
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    choicesOrg() {
      this.orgTree = true;
    },
    // 获取分组下拉
    getGroupingSelect(){
      this.$http.post('/group/list',{pageNo:1,pageSize:10000}).then(res=>{
        this.groupingList = res.data.records
      })
    },
    queryClick() {
      this.recordLog();
      //点击搜索
      this.query.pageNo = this.currentNum = 1;

      let startVal = moment(
          this.timeRange[0] === "" || this.timeRange[0] == undefined
              ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0))
              : this.timeRange[0],
          "YYYY-MM-DD hh:mm:ss"
      ).valueOf();
      let endVal = moment(
          this.timeRange[1] === "" || this.timeRange[1] == undefined
              ? new Date()
              : this.timeRange[1],
          "YYYY-MM-DD 23:59:59"
      ).valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t('warning_time_not_exceed_62'));
        return;
      }
      this.saveQuery.startTime = this.query.startTime = new Date(
          this.timeRange[0]
      ).format("yyyy-MM-dd HH:mm:ss");
      this.saveQuery.endTime = this.query.endTime = new Date(
          this.timeRange[1]
      ).format("yyyy-MM-dd hh:mm:ss");
      if (this.timeRange[0] == "" || this.timeRange[0] == undefined) {
        this.saveQuery.startTime = this.query.startTime =  new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
      }
      if (this.timeRange[1] == "" || this.timeRange[1] == undefined) {
        this.saveQuery.endTime = this.query.endTime = this.timeChange(
            Date.now()
        );
      }
      this.getUrgentNoticeList(this.query);
    },
    restClick() {
      this.timeRange = [new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"),new Date().format2("yyyy-MM-dd 23:59:59")]
      this.saveQuery.startTime = new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
      this.saveQuery.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.startTime = new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
      this.query.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.faultType = "8";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.treeValue = "";
      // this.query.keyword = "";
      // this.query.dataSource = "";
      // this.query.orgId = "";
      // this.query.dealStatus = "";
      // this.query.isSpecialLine = "";
      // this.query.opt = "";
      // this.query.tencentIds = "";
      // this.query.compareType = "";
    },
    getUrgentNoticeList(param) {
      let _self = this;
      _self.loading = true;
      _self.$http
          .PostJson("/fault/getAlarmList", param)
          .then((res) => {
            if (res.code === 1) {
              if (res.data) {
                _self.urgenttableList = res.data.records;
                _self.totalCount = res.data.total || 0;
                //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                let _that = this;
                setTimeout(function () {
                  let objData = _that.$refs.urgenttableList.$refs.tbody.objData;
                  for (let key in objData) {
                    if (_that.selectedIds.has(objData[key].id)) {
                      objData[key]._isChecked = true;
                    }
                  }
                }, 0);
              } else {
                _self.urgenttableList = [];
              }
            } else {
              this.$Message.warning(res.msg);
            }
          })
          .finally(() => {
            _self.loading = false;
          });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
            "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
            "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getUrgentNoticeList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
            "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
            "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getUrgentNoticeList(this.query);
    },
    // 打开故障现象详情
    faultPhenomenonOpen(row) {
      this.faultPhenomenon.title =this.$t('comm_urgent_notice');
      const list = (this.faultPhenomenon.content = []);
      if (row && row.faultDesc) {
        Array.prototype.push.apply(
            list,
            String(row.faultDesc)
                .split(/[;；]/)
                .map((text, index) => {
                  return text.trim();
                })
        );
        console.log(this.faultPhenomenon.content)
      }
      this.faultPhenomenon.open = true;
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
          y = now.getFullYear(),
          m = now.getMonth() + 1,
          d = now.getDate();
      return (
          y +
          "-" +
          (m < 10 ? "0" + m : m) +
          "-" +
          (d < 10 ? "0" + d : d) +
          " " +
          now.toTimeString().substr(0, 8)
      );
    },
    //屏蔽清除
    shieldByIds(id) {
      var selectedIdsArrary = Array.from(this.selectedIds);
      var ids = "";
      if(id){
        ids = id;
      }else{
        if(selectedIdsArrary.length==0){
          this.$Message.warning(this.$t('warning_select_clear_first'));;
          return;
        }
        for (var i = 0; i < selectedIdsArrary.length; i++){
          if (i === 0){
            ids += selectedIdsArrary[i];
          } else{
            ids += "," + selectedIdsArrary[i];
          }
        }
      }
      var param = {
        ids: ids,
        type : 2
      };
      top.window.$iviewModal.confirm({
        title: this.$t('comm_tip'),
        content: '<p>'+this.$t('dash_clear')+'</p>',
        onOk: () => {
          this.$http.wisdomPost("/fault/shieldByIds", param).then((res) => {
            if (res&&res.code === 1) {
                    this.$Message.success(this.$t('dash_clear_successfully'));

              this.query.pageNo = this.currentNum = 1;
              this.selectedIds = new Set();
              this.getUrgentNoticeList(this.query);
            } else {
              this.$Message.warning(res.msg);
            }
          })
        }
      });
    },
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        var data = this.$refs.urgenttableList.data;
        data.forEach(item => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach(item => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },
    //导出
    exportClick() {
      this.saveLog();
      this.$Loading.start();
      let _self = this;
      let param = Object.assign({}, this.query);
      this.$axios({
        url: "/fault/exportFaultXlsx",
        method: "post",
        params: param,
        responseType: "blob", // 服务器返回的数据类型
      })
          .then((res) => {
            let data = res.data;
            const blob = new Blob([data], { type: "application/vnd.ms-excel" });
            if ("msSaveOrOpenBlob" in navigator) {
              window.navigator.msSaveOrOpenBlob(blob, this.$t('server_faults') + ".xls");
            } else {
              var fileName = "";
              fileName = this.$t('server_faults') + ".xlsx";
              const elink = document.createElement("a"); //创建一个元素
              elink.download = fileName; //设置文件下载名
              elink.style.display = "none"; //隐藏元素
              elink.href = URL.createObjectURL(blob); //元素添加href
              document.body.appendChild(elink); //元素放入body中
              elink.click(); //元素点击实现
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            }
            this.$Loading.finish();
          })
          .catch((error) => {
            console.log(error);
          }).finally(()=>{
        this.$Loading.finish();
      });
    },
    // 查看关联数据列表
    associationList(row) {
      this.associationModal.show = true;
      this.associationModal.rowData = row;
    },

    // 紧急通知返回页面
    returnPage(){
      this.returnShow = false;
      this.getUrgentNoticeList(this.query)
    },
    setQuery(flag, e) {
      if (flag) {
        this.$refs[e].query = "";
      }
    },
    //清除查询条件
    resetQuery() {
      this.query.startTime = null;
      this.query.endTime = null;
      this.query.keyword = "";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.dealStatus = "";
      this.query.faultType = "8";
      this.query.opt = "";
      this.query.orgId = "";
      this.query.pageNo = 1;
      this.query.pageSize = 10;
      this.getUrgentNoticeList(this.query);
    },
    initWebSocket() {
      //初始化weosocket
      let token = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
      const wsuri =
          "wss://" +
          this.$baseUrl.replace(top.window.location.protocol+"//", "") +
          "/faultSocket/" +
          token;
      if ("WebSocket" in window) {
        this.websock && this.websock.close();
        if (!this.websock) {
          this.websock = new WebSocket(wsuri);
          this.websock.onopen = this.websocketonopen;
          this.websock.onmessage = this.websocketonmessage;
          this.websock.onerror = this.websocketonerror;
          this.websock.onclose = this.websocketclose;
        } else {
        }
      } else {
        //不支持websoket时只展示列表
        this.getUrgentNoticeList(this.query);
      }
    },
    websocketsend(Data) {
      //数据发送
      if (this.websock.readyState === 0) {
        this.initWebSocket();
      } else {
        this.websock.send(Data);
      }
    },
    websocketclose(e) {
      //关闭
      console.log("socket断开连接", e);
      if (this.$route.name == this.$t('alarm_fault_list') && this.tabData === 'urgentNotice') {
        this.getUrgentNoticeList(this.query);
      }
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("socket已建立链接");
      let param = Object.assign({}, this.query);
      param.userId = JSON.parse(sessionStorage.getItem("accessToken")).user.id;
      this.websocketsend(JSON.stringify(param));
    },
    websocketonerror() {
      //连接建立失败重连
      this.initWebSocket();
    },
    websocketonmessage(e) {
      //数据接收
      let redata = JSON.parse(e.data);
      this.loading = false;

      //判断如果当前页数大于1，则不能刷新列表
      let datas = redata.data.records[0]
      if (this.query.pageNo == 1) {
        if(datas || datas != ""){
          // this.query.compareType = "";
          // this.query.faultStartTime = "";
          // this.query.faultEndTime = "";
          this.$http
              .PostJson("/fault/getAlarmList", this.query)
              .then((res) => {
                if (res.code === 1) {
                  if (res.data) {
                    this.urgenttableList = res.data.records;
                    this.totalCount = res.data.total || 0;
                    //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                    setTimeout(()=> {
                      let objData = this.$refs.urgenttableList.$refs.tbody.objData;
                      for (let key in objData) {
                        if (this.selectedIds.has(objData[key].id)) {
                          objData[key]._isChecked = true;
                        }
                      }
                    }, 0);
                  } else {
                    this.urgenttableList = [];
                  }
                } else {
                  this.$Message.warning(res.msg);
                }
              })
        }
      }
    },
    // 定时刷新数
    dataRefreh() {
      if(this.tabData==='urgentNotice'){
        // 计时器为空，操作
        setInterval(() => {
          //如果10分钟没有操作，则要刷新页面返回到第一页
          if (this.query.pageNo != 1) {
            let nowDate = new Date();
            let z = nowDate.getTime() - this.operationTime.getTime();
            if (z > 600000) {
              this.pageChange(1);
            } else {
            }
          }
        }, 30000);
      }

    },
    //点击当前页面触发事件
    bodyClick() {
      let nowDate = new Date();
      this.operationTime = nowDate; //刷新操作时间
    },
    recordLog(){
      this.$http.wisdomPost("/audit/queryList", {tab:"紧急通知"}).then(res => {});
    },
    saveLog(){
      //直接添加安全审计日志 开始
      this.$http.wisdomPost("/audit/saveLog", {
        url:"/fault/exportFaultXlsx",
        funName:"紧急通知",
        description:"导出紧急通知",
        operationType:13,
        eventType:7,
        alarmType:0,
        logType:2,
        isDel:0
      }).then(res => {});
      //直接添加安全审计日志 结束
    }
  },
  destroyed(){
    // this.websocketclose();
    if(this.websock){
      this.websock.close()
    }
  }
};
</script>

<style lang='less' scoped>
/deep/ .ivu-table-cell a {
  color: var(--table_content_column_link_color,#05EEFF);
}

.urgentTitle {
  text-align: left;
  margin-bottom: 10px;
}
.butBoxStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20px;
  .fault-top-box {
    margin-bottom: 0 !important;
  }
  ul {
    display: flex;
    align-items: center;
    list-style: none;
  }
  li {
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    color: var(--font_color, #dcdee2);
    cursor: pointer;
    border: 1px solid var(--border_color, #dcdee2);
    border-right: none;
  }
  li:last-child {
    border-right: 1px solid var(--border_color, #dcdee2);
  }
  .tabStyle {
    background: #2d8cf0;
    color: #fff;
    border-color: #2d8cf0;
  }
}
.faultDuration {
  align-items: center;
  min-width: 22%;
  .fault-top-item {
    margin-left: 0 !important;
  }
  .faultCustomize {
    display: inline-block;
    //align-items: center;
    //margin-left:5px;
    .ivu-input-wrapper {
      width: 90px;
      margin: 0 5px;
    }
  }
  ul {
    //display: flex;
    align-items: center;
    display: inline-block;
    list-style: none;
  }
  li {
    display: inline-block;
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    color: var(--font_color, #dcdee2);
    cursor: pointer;
    border: 1px solid var(--border_color, #dcdee2);
    border-right: none;
  }
  li:last-child {
    border-right: 1px solid var(--border_color, #dcdee2);
  }
  .tabStyle {
    background: #2d8cf0;
    color: #fff;
    border-color: #2d8cf0;
  }
}
.ivu-input::-webkit-input-placeholder {
  font-size: 12px !important;
}
.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}
.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  background: #f1f6fe !important;
}
.task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
  height: auto !important;
  min-height: 32px;
}
.multipleSelect .ivu-select-input {
  padding: 0 !important;
}
.fault-phenomenon {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  // color: #05eeff;
  color: var(--table_content_column_link_color, #05eeff);
}

.fault-phenomenon > div {
  cursor: pointer;
}

.fault-phenomenon > div.empty {
  color: black;
  cursor: default;
}
.zhibiao {
  background-image: url("../../../assets/wisdom/zhibiao.png") !important;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 22px 22px;
}
</style>
<style scoped lang="less">
.screenCondition {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .fault-top-box {
    width: 22% !important;
    margin-right: 35px;
    label {
      min-width: 70px;
      text-align: right;
    }
    .fault-top-item {
      margin-left: 70px;
      /deep/.ivu-input {
        padding-left: 10px !important;
      }
    }
  }
  .timeBox {
    width: calc(44% + 35px) !important;
    .timeStyle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .reach {
        text-align: center;
        padding: 0px 45px;
      }
    }
    /deep/.ivu-btn-default {
      display: none;
    }
  }
}
.soundPlay {
  margin-right: 24px;
  cursor: pointer;
}
// .action-index-modal {
//   /deep/.ivu-modal-body {
//     overflow: auto;
//     max-height: 550px;
//   }
// }
.wisdom-fault .wisdom-fault-top .select-box {
  width: 20%;
}
.wisdom-fault .wisdom-fault-top .keyword-box {
  width: 25%;
}
//.orgDiv {
//  position: absolute;
//  height: 260px;
//  overflow: hidden;
//  width: 100%;
//  padding: 0 10px;
//  background: white;
//  z-index: 9;
//  border-width: 1px;
//  //border-style: solid;
//  border-top: 0;
//  border-color: #dcdee2;
//  box-shadow: 0 0 6px #dcdee2;
//}
//.orgDiv > .title {
//  background: radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9));
//  color: white;
//  margin: 0 -10px;
//  padding: 0 10px;
//}
//.orgTreeScroll {
//  width: calc( 100% + 20px);
//  height: calc(100% - 32px);
//  overflow-y: scroll;
//}
/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}
/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}
.daoChu-btn {
  width: 60px !important;
}
</style>


