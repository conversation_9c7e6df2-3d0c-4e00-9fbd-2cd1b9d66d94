<!--  -->
<template>
  <section class="sectionBox special" style="min-width: 100% !important">
    <div class="portalarm-container">
      <!-- 标题 -->
      <Row>
        <Col span="12">
          <Row :gutter="30">
            <Col span="9">
              <div class="item-box">
                <label class="fn_item_label"
                  >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
                >
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadData"
                  @onSelectChange="setOrg"
                  @onFocus="foucusFn"
                  @onClear="onClear"
                ></TreeSelect>
              </div>
            </Col>
            <Col span="9">
              <div class="item-box">
                <label class="fn_item_label"
                  >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
                >

                <Input
                  :placeholder="$t('alarm_symptom_placeholder')"
                  v-model="query.keyword"
                  maxlength="200"
                  clearable
                />
              </div>
            </Col>
          </Row>
        </Col>
        <Col span="12">
          <Row :gutter="10" type="flex" justify="end">
            <Col span="9">
              <div class="item-box">
                <label class="fn_item_label"
                  >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
                >

                <DatePicker
                  format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  :options="timeOptions"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="false"
                  style="width: 100%"
                  :confirm="false"
                  @on-change="dateChange"
                >
                </DatePicker>
              </div>
            </Col>
            <Col>
              <div class="btn-box">
                <Tooltip :content="$t('common_query')">
                  <Button
                    class="query-btn"
                    type="primary"
                    icon="ios-search"
                    :loading="btnLoading"
                    @click="queryClick"
                    v-if="permissionObj.list"
                    :title="$t('common_query')"
                  ></Button>
                </Tooltip>
                <Tooltip :content="$t('but_reset')">
                  <Button
                    type="primary"
                    v-if="permissionObj.reset"
                    class="skinPrimary reset-btn"
                    icon="md-refresh"
                    @click="restClick"
                  ></Button>
                </Tooltip>
                <Tooltip :content="$t('but_clear')">
                  <Button
                    type="error"
                    class="skinWarning del-btn"
                    icon="ios-eye-off-outline"
                    v-if="permissionObj.clear"
                    @click="shieldByIds()"
                  ></Button>
                </Tooltip>

                <Tooltip :content="$t('but_export')">
                  <Button
                    class="daoChu-btn"
                    id="exportData"
                    type="primary"
                    @click="exportClick"
                    v-if="permissionObj.export"
                  >
                    <i class="iconfont icon-icon-derive" />
                  </Button>
                </Tooltip>
              </div>
            </Col>
          </Row>
        </Col>
      </Row>
      <!-- /标题 -->
      <!-- 表格 -->
      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <div class="dialTest-tab-content">
            <div class="privateLine-tab-content">
              <Loading :loading="loading"></Loading>
              <Table
                ref="dataList"
                stripe
                :columns="columns"
                :data="dataList"
                :no-data-text="
                  loading
                    ? ''
                    : dataList.length > 0
                    ? ''
                    : currentSkin == 1
                    ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                      $t('common_No_data') +
                      '</p></div>'
                    : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                      $t('common_No_data') +
                      '</p></div>'
                "
                @on-select="handleSelect"
                @on-select-cancel="handleCancel"
                @on-select-all="handleSelectAll"
                @on-select-all-cancel="handleSelectAll"
                @on-sort-change="sortChange"
                size="small"
              >
                <template slot-scope="{ row }" slot="orgName">
                  {{
                    row.orgName === undefined ||
                    row.orgName === null ||
                    row.orgName === ""
                      ? "--"
                      : row.orgName
                  }}
                </template>
                <template slot-scope="{ row }" slot="faultDesc">
                  <div class="fault-phenomenon">
                    <div v-if="row.faultDesc" @click="faultPhenomenonOpen(row)">
                      {{ row.faultDesc }}
                    </div>
                    <div v-else class="empty">——</div>
                  </div>
                </template>
              </Table>
            </div>
            <div
              class="tab-page"
              style="border-top: 0"
              v-if="dataList.length > 0"
            >
              <Page
                v-page
                :current.sync="currentNum"
                :page-size="query.pageSize"
                :total="totalCount"
                :page-size-opts="pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
      <!-- /表格 -->
      <!-- 分页 -->
      <!-- /分页 -->

      <!-- 故障现象详情 -->
      <Modal
        class="detail-modal"
        v-model="faultPhenomenon.open"
        :title="faultPhenomenon.title"
        width="460"
        :mask="true"
        sticky
        draggable
      >
        <div
          v-if="faultPhenomenon.content"
          style="
            text-align: center;
            color: var(--table_content_column_link_color, #05eeff);
            word-wrap: break-word;
            word-break: break-all;
          "
        >
          <p v-for="(item, index) in faultPhenomenon.content" :key="index">
            {{ item }}
          </p>
        </div>
        <div slot="footer">
          <Button
            type="primary"
            size="small"
            @click="faultPhenomenon.open = false"
            >{{ $t("but_confirm") }}</Button
          >
        </div>
      </Modal>
    </div>
  </section>
</template>

<script>
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import moment from "moment";
import global from "../../../common/global.js";
import locationreload from "@/common/locationReload";

export default {
  name: "",
  components: {
    TreeSelect,
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      treeData: [],
      //loading状态
      loading: false,
      btnLoading: false,
      // 表格数据
      dataList: [],
      treeValue: "",
      //搜索字段
      query: {
        startTime: "",
        endTime: "",
        orgId: "",
        keyword: "",
        fieldName: "", //排序字段名
        orderBy: "", //排序方式
        faultType: "13", //工单类型(流量拥塞总类型 , 30--端口状态监控),
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
      },
      interrefresh: null,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      saveQuery: {
        startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        endTime: this.timeChange(Date.now()),
      },
      // 故障详情窗口
      faultPhenomenon: {
        open: false,
        title: "",
        content: [],
      },
      timeOptions: {
        shortcuts: [
          {
            text: this.$t("comm_half_hour"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return [
                start.format("yyyy-MM-dd HH:mm:ss"),
                end.format("yyyy-MM-dd HH:mm:ss"),
              ];
            },
          },
          {
            text: this.$t("comm_today"),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [
                new Date().format("yyyy-MM-dd 00:00:00"),
                new Date().format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_yesterday"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_7"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_30"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_curr_month"),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_preced_month"),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
        ],
      },
      timeRange: [
        new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
      ],
      pageLoading: false,

      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 30,
           align: "center",
          className: "bgColor",
          fixed: 'left',
        },
        // 故障开始时间
        {
          title: this.$t("alarm_start_time"),
          key: "faultStartTime",
          align: "left",
          className: "bgColor",
          sortable: "custom",
          width: 180,
          render: (h, params) => {
            let str = params.row.faultStartTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : str
            );
          },
        },
        // 故障类型
        {
          title: this.$t("alarm_fault_type"),
          key: "faultType",
          align: "left",
          width: 180,
          render: (h, params) => {
            let str = Number(params.row.faultType),
              text = "";
            switch (str) {
              case 12:
                text = this.$t("legalResPool_illegal_access");
                break;
               case 13:
                text = this.$t("port_status_change_alarm");
                break;
               case 14:
                text = this.$t("port_status_change_alarm");
                break;
              default:
                text = "--";
                break;
            }
            return h("span", text);
          },
        },
        // 机构
        {
          title: this.$t("comm_org"),
          key: "orgName",
          slot: "orgName",
          align: "left",
          width: 180,
          tooltip: true,
        },
        {
          title: this.$t("alarm_symptom"),
          key: "faultDesc",
          slot: "faultDesc",
          align: "left",
          minWidth: 260,
        },

        {
          title: this.$t("comm_operate"),
          width: "100",
          align: "center",
          fixed: 'right',
          className: "bgColor",
          render: (h, params) => {
            const row = params.row;
            return h(
              "Tooltip",
              {
                props: {
                  placement: "left-end",
                   transfer: true
                },
              },
              [
                h("span", {
                 class:  this.currentSkin == 1 ?"look1-btn":"light-look1-btn",
                 style: {
                          display: this.permissionObj.look
                            ? "inline-block"
                            : "none",
                          marginRight: '0px',
                    },
                  on: {
                    click: () => {
                      this.faultPhenomenonOpen(row);
                    },
                  },
                }),
                h("span", { slot: "content" }, this.permissionObj.look ? this.$t("server_view"):""),
              ]
            );
          },
        },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    dateChange(val, type) {
      // if (val[0] === val[1]) {
      if (type === "date") {
        this.timeRange = [
          val[0],
          new Date(val[1]).format("yyyy-MM-dd 23:59:59"),
        ];
      }

      // }
    },
    setOrg(item) {
      this.treeValue = item[0].name;
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    onClear() {
      this.query.orgId = "";
      this.treeValue = "";
    },
    choicesOrg() {
      this.orgTree = true;
    },
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        var data = this.$refs.dataList.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },
    //导出
    exportClick(val) {
      this.saveLog();
      let _self = this;
      let fileName = this.$t("alarmlist_port_aram_file_name") + ".xlsx";
      this.$Loading.start();
      let param = Object.assign({}, this.query);
      this.$axios({
        url: "/fault/exportFaultXlsx",
        method: "post",
        data: this.query,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // var fileName = "";
            // fileName = "故障清单.xlsx";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          this.selectedIds = new Set();
          this.query.ids = null;
          this.queryClick();
          this.$Loading.finish();
        });
    },
    //屏蔽清除
    shieldByIds(id) {
      var selectedIdsArrary = Array.from(this.selectedIds);
      var ids = "";
      if (id) {
        ids = id;
      } else {
        if (selectedIdsArrary.length == 0) {
          this.$Message.warning(this.$t("warning_select_clear_first"));
          return;
        }
        for (var i = 0; i < selectedIdsArrary.length; i++) {
          if (i === 0) {
            ids += selectedIdsArrary[i];
          } else {
            ids += "," + selectedIdsArrary[i];
          }
        }
      }
      var param = {
        ids: ids,
        type: 1,
      };
      top.window.$iviewModal.confirm({
        title: this.$t("comm_tip"),
        content: "<p>" + this.$t("dash_clear") + "</p>",
        onOk: () => {
          this.$http.wisdomPost("/fault/shieldByIds", param).then((res) => {
            if (res && res.code === 1) {
              this.$Message.success(this.$t("dash_clear_successfully"));

              this.query.pageNo = this.currentNum = 1;
              this.selectedIds = new Set();
              this.getAlarmList(this.query);
            } else {
              this.$Message.warning(res.msg);
            }
          });
        },
      });
    },
    queryClick() {
      if(undefined == this.query.keyword){
        this.query.keyword ='';
      }
      console.log('key',this.query.keyword)
      this.query.keyword = this.query.keyword.trim();

      this.btnLoading = true;
      // this.recordLog();
      //点击搜索
      this.query.pageNo = this.currentNum = 1;
      // 去掉关键字首尾空格
      this.query.keyword = this.query.keyword.trim();

      let startVal = moment(
        this.timeRange[0] === "" || this.timeRange[0] == undefined
          ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0))
          : this.timeRange[0],
        "YYYY-MM-DD hh:mm:ss"
      ).valueOf();
      let endVal = moment(
        this.timeRange[1] === "" || this.timeRange[1] == undefined
          ? new Date()
          : this.timeRange[1],
        "YYYY-MM-DD 23:59:59"
      ).valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t("warning_time_not_exceed_62"));
        this.btnLoading = false;
        return;
      }
      this.saveQuery.startTime = this.query.startTime = new Date(
        this.timeRange[0]
      ).format("yyyy-MM-dd HH:mm:ss");
      this.saveQuery.endTime = this.query.endTime = new Date(
        this.timeRange[1]
      ).format("yyyy-MM-dd HH:mm:ss");
      if (this.timeRange[0] == "" || this.timeRange[0] == undefined) {
        this.saveQuery.startTime = this.query.startTime = new Date(
          new Date().getTime() - 3600 * 1000 * 24 * 6
        ).format("yyyy-MM-dd 00:00:00");
      }
      if (this.timeRange[1] == "" || this.timeRange[1] == undefined) {
        this.saveQuery.endTime = this.query.endTime = this.timeChange(
          Date.now()
        );
      }
      if (this.query.faultType == undefined) {
        this.query.faultType = 20;
      }
      this.getAlarmList(this.query);
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getAlarmList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getAlarmList(this.query);
    },

    // 列表排序
    sortChange(e) {
      if (e.order == "normal") {
        this.query.orderBy = "";
        this.query.fieldName = "";
      } else {
        this.query.orderBy = e.order;
        this.query.fieldName = e.key;
      }
      this.getAlarmList(this.query);
    },

    getAlarmList(param) {
      let _self = this;
      // debugger
      _self.loading = true;
      _self.$http
        .PostJson("/fault/getAlarmList", param)
        .then((res) => {
          if (res.code === 1) {
            if (res.data) {
              _self.dataList = res.data.records;
              _self.totalCount = res.data.total || 0;
            } else {
              _self.dataList = [];
            }
          } else {
            this.$Message.warning(res.msg);
          }
        })
        .finally(() => {
          _self.loading = false;
          _self.btnLoading = false;
        });
    },
    // 打开故障现象详情
    faultPhenomenonOpen(row, sign) {
      console.log(row);
      this.faultPhenomenon.title = this.$t("comm_portStatus");
      const list = (this.faultPhenomenon.content = []);
      if (row && row.faultDesc) {
        Array.prototype.push.apply(
          list,
          String(row.faultDesc)
            .split(";")
            .map((text, index) => {
              return text.trim();
            })
        );
      }
      this.faultPhenomenon.open = true;
    },
    restClick() {
      this.timeRange = [
        new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
      ];
      this.saveQuery.startTime = new Date(
        new Date().getTime() - 3600 * 1000 * 24 * 6
      ).format("yyyy-MM-dd 00:00:00");
      this.saveQuery.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.startTime = new Date(
        new Date().getTime() - 3600 * 1000 * 24 * 6
      ).format("yyyy-MM-dd 00:00:00");
      this.query.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.faultType = 13;
      this.query.keyword = "";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.orgId = "";
      this.treeValue = "";
    },
    foucusFn() {
      this.getTreeOrg();
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        now.toTimeString().substr(0, 8)
      );
    },
    recordLog() {
      this.$http
        .wisdomPost("/audit/queryList", { tab: "端口状态监控" })
        .then((res) => {});
    },
    saveLog() {
      //直接添加安全审计日志 开始
      this.$http
        .wisdomPost("/audit/saveLog", {
          url: "/fault/exportFaultXlsx",
          funName: "端口状态监控",
          description: "导出端口状态监控",
          operationType: 13,
          eventType: 7,
          alarmType: 0,
          logType: 2,
          isDel: 0,
        })
        .then((res) => {});
      //直接添加安全审计日志 结束
    },
  },
  created() {
    // moment.locale("zh-cn");
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.saveQuery.startTime = this.query.startTime = new Date(
      new Date().getTime() - 3600 * 1000 * 24 * 6
    ).format("yyyy-MM-dd 00:00:00");
    this.saveQuery.endTime = this.query.endTime = new Date().format(
      "yyyy-MM-dd 23:59:59"
    );
    // 加载组织机构
    this.getTreeOrg();
    this.getAlarmList(this.query);
    // 记录日志
    this.recordLog();
  },

  destroyed() {
    if (this.interrefresh) {
      clearInterval(this.interrefresh);
      this.interrefresh = null;
    }
  },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    // 初始化页面
    this.query.pageNo = this.currentNum;
    this.interrefresh = setInterval(() => {
      this.getAlarmList(this.query);
    }, 1000 * 60);
    document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    top.document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
  },
};
</script>
<style scoped lang='less'>
.portalarm-container {
  width: 100%;
  padding: 20px;
  padding-top: 0;
  .item-box {
    display: flex;
    align-items: center;
    .fn_item_label {
      min-width: 30px;
      margin-right: 10px;
      color: var(--search_lable_font_color, #fff);
      white-space: nowrap;
    }
  }
  .btn-box {
    display: flex;
    button {
      margin-right: 10px;
    }
    /deep/.daoChu-btn {
      width: 60px !important;
      margin-right: 0;
    }
  }
  /deep/.ivu-input {
    height: 35px !important;
  }
  .privateLine-tab-content {
    margin-top: 20px;
  }

  .fault-phenomenon {
    position: relative;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    color: var(--table_content_column_link_color, #05eeff);
  }

  .fault-phenomenon > div {
    cursor: pointer;
  }

  .fault-phenomenon > div.empty {
    color: black;
    cursor: default;
  }
}
/deep/ .ivu-spin-fix {
  left: auto !important;
}
</style>
