<template>
<div class="illegalAlarm-container">
  <Form ref="illegalAccessAlarmForm" class="action-content">
   <div class="action-top">
      <div class="action-top-right">
        <span>{{faultDesc}}</span>
      </div>
      <div class="index-flowChart" style="margin-left:-100px;">
      <topology-item
          :data="topologyList"
          ref="topologyChart"
          :dashShow='false'
         :linkData ='topologyList'
         :isPoToPo="true"
      ></topology-item>
       </div>
    </div>
    <div class="fault-tab action-tab">
      <div class="dialTest-tab-content">
        <Loading :loading="loading"></Loading>
        <Table :columns="columns" stripe :data="tabList"
          :min-height='500'
          :no-data-text="loading ? '' 
          : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'">
          <template slot-scope="{row}" slot="orgName">
            <Tooltip :content="row.orgName" placement="top-start">
                <div style="text-overflow: ellipsis;overflow: hidden;width:100px;white-space: nowrap;text-align: left;" >
                    {{row.orgName||'--'}}
                </div>
            </Tooltip>
        </template>
       <template slot-scope="{row}" slot="remarks">
             <Tooltip :content="row.remarks" :placement="'left'" max-width="750"   >
                <div style="text-overflow: ellipsis;overflow: hidden;width:150px;white-space: nowrap" >
                  {{row.remarks||'--'}}
                </div>
            </Tooltip>
          </template>

        </Table>
        
      </div>
      <div class="tab-page" v-if="totalCount > tabObj.pageSize">
        <Page v-page :current="tabObj.pageNo" :page-size="tabObj.pageSize" :total="totalCount" :prev-text="$t('common_previous')"
          :next-text="$t('common_next_page')" @on-change="pageChange">
        </Page>
      </div>
    </div>
    <div class="action-bottom" style="margin-top:15px;">
      <div class="action-bottom-box action-bottom-left">
        <Input v-model="workOrder.content" :rows="3" type="textarea" :placeholder="$t('legalResPool_alarm_please_enter_procedure')" maxlength="400" />
      </div>
      <div class="action-bottom-box">
        <div class="action-bottom-checkbox">
          <Checkbox v-model="dealStatus" :disabled="disabled" @on-change="checkboxChange">{{$t('legalResPool_verified')}}</Checkbox>
          <Checkbox v-model="intoResourcePoolStatus" :disabled="disabled" @on-change="checkboxIntoResourcePoolChange">{{$t('legalResPool_alarm_into_legal_resource_pool')}}</Checkbox>
        </div>
      </div>
    </div>
    
  </Form>
  </div>
</template>

<script>


import { addDraggable } from "@/common/drag.js";
import handlePassword from "@/common/handlePassword";
import topologyItem from "@/common/flowChart/topologyIllegalAccess.vue";
export default {
  name: "actionItem",
  components: {
    topologyItem,
  },
  props: {
    data: {
      default: function () {
        return {};
      }
    },
    titles: {
    },
    time: {
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      topologyList:[],
      data_source:0,
      flow_chart: [],
      loading1: false,
      isSnmp2: false,
      loading: false,
      showDiv:false,
      showDivTile:'',
      faultDesc:'',
      columns: [
        {
          title: this.$t('alarmlist_operator'),
          key: "processUserName",
          align: "left",
          width: 230,
          render: (h, params) => {
            let str = handlePassword.decrypt(params.row.processUserName);
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          }
        },
        {
          title: this.$t('legalResPool_alarm_handle_time'),
          key: "processTimeStr",
          align: "left",
          width: 280,
          render: (h, params) => {
            let str =  params.row.processTimeStr;
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          }
        },
        {
          title: this.$t('comm_org'),
          slot: "orgName",
          align: "left",
          width: 240,
        },
        {
          title: this.$t('alarmlist_operate_process'),
          slot: "remarks",
          align: "left",
        
        },
        {
          title: this.$t('alarmlist_operate_result'),
          key: "processStatus",
          align: "left",
          width: 180,
          render: (h, params) =>  {
            let str = params.row.processStatus,
              text = "--";
              
            switch (str) {
              case 0:
                text = this.$t('legalResPool_not_verified');
                break;
              case 1:
                text = this.$t('legalResPool_verified');
                break;
              default:
                text = "--";
                break;
            }
            return h("span", text);
          }
        }
      ],
      tabList: [],
      tabObj: {
        reportId: "",
        pageNo: 1,
        pageSize: 4
      },
      totalCount: 0,
      workOrder: {
        reportId: "",
        content: "",
        dealStatus: "",
        intoResourcePoolStatus:""
      },
      dealStatus: false,
      intoResourcePoolStatus:false,
      disabled: false,
      index: {
        show: false,
        data: {}
      },
    };
  },
  watch: {
    data: {
      handler(val) {
        console.log('sss',val);
        if (!(JSON.stringify(val) === "{}")) {
          this.getTopology(val.id);
          if (val.dealStatus === 0) {
            this.disabled = true;
            this.dealStatus = true;
            this.workOrder.dealStatus = val.dealStatus;

            this.intoResourcePoolStatus = true;
            this.workOrder.intoResourcePoolStatus = val.intoResourcePoolStatus;
          } else {
            this.disabled = false;
            this.dealStatus = false;
            this.workOrder.dealStatus = 1;
            this.intoResourcePoolStatus = false;
            this.workOrder.intoResourcePoolStatus = 0;
          }
          this.tabObj.reportId = val.id;
          this.workOrder.reportId = val.id;
          this.tabObj.pageNo = 1;
          this.getRecord(this.tabObj);
          this.faultDesc = val.faultDesc;
        } else {
          this.resetRegion();
        }
      },
      deep: true
    },
    time: {
      handler(val) {
        this.time = val;
      },
      deep: true
    }
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    //复选框事件
    checkboxChange(val) {
      if (val) {
        this.workOrder.dealStatus = 0;
      } else {
        this.workOrder.dealStatus = 1;
      }
    },
    checkboxIntoResourcePoolChange(val) {
      if (val) {
        this.workOrder.intoResourcePoolStatus = 1;
      } else {
        this.workOrder.intoResourcePoolStatus = 0;
      }
    },
    //获取历史处理记录
    getRecord(param) {
      let _self = this;
      _self.$http
        .wisdomPost("/legalresourcepool/getIllegalAccessAlarmProcessRecordsListByAlarmId", param)
        .then(res => {
          if (res.code === 1) {
            _self.tabList = res.data.records || [];
            _self.totalCount = res.data.total;
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    //分页
    pageChange(page) {
      this.tabObj.pageNo = page;
      this.getRecord(this.tabObj);
    },
    getParams() {
      return this.workOrder;
    },
    //清除数据
    resetRegion() {
      this.workOrder = {
        reportId: "",
        content: "",
        dealStatus: "",
        intoResourcePoolStatus:""
      };
      this.dealStatus = false;
      this.intoResourcePoolStatus = false;
      this.disabled = false;
    },
    //关闭
    indexCancel(refName) {
      this.$Modal.visible = false;
      this.$refs["illegalAccessAlarmForm"].resetRegion();
      this.index.data = {};
    },

    
    
    //获取拓扑图数据
    getTopology(alarmId) {
      let _self = this;
      _self.flow_chart = [];
      _self.loading1 = true;
      _self.$http
        .wisdomPost("/legalresourcepool/getTopologyByAlarmId", {alarmId:alarmId})
        .then((res) => {
          if (res.code === 1) {
            let list = res.data || [];
            this.topologyList = res.data || [];
            if (this.topologyList.length > 0) {
              for (let i = 0, len = this.topologyList.length; i < len; i++) {
                this.topologyList[i].nodeColor =
                  this.topologyList[i].isBroken === 3 ? "y" : "b";
              }
            }
            // _self.getTrend(_self.echartLookParama2);
            _self.loading1 = false;
          }
        })
        .catch(() => {
          _self.loading1 = false;
        })
        .finally(() => {
          _self.loading1 = false;
        });
    },
  },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    if (this.data.dealStatus === 4) {
      this.disabled = true;
      this.dealStatus = true;
      this.intoResourcePoolStatus = true;
    } else {
      this.disabled = false;
      this.dealStatus = false;
      this.intoResourcePoolStatus = false;
    }
    addDraggable();
  },
  
};
</script>

<style scoped lang="less">
.action-index-modal {
  /deep/.ivu-modal-body {
    overflow: auto;
    max-height: 550px;
  }
}
.contentSty{
    position: fixed;
    top:0;
    left:-20px;
    z-index: 99999;
    padding: 10px 16px;
    background-color: var(--body_b_color,#f4f6f9);
    border: 1px solid var(--border_color,#f4f6f9);
    border-radius: 10px;
    
}
.topology .topology-content :first-child {
    margin-left: -20px;
}
.ivu-table-row{
    height: 80px !important;
}
</style>