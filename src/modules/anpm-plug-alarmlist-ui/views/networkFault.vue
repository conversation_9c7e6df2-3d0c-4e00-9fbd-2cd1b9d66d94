<template>
  <!-- 网络故障页面 -->
  <section
    class="sectionBox special"
    style="min-width: 100% !important"
    @click="bodyClick"
  >
    <div class="section-top">
      <Row class="fn_box">
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("alarm_fault_type") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.faultType"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('comm_please_select')"
              >
                <Option
                  v-for="item in networkfaultList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_isp") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                ref="operatorSelect"
                v-model="query.opt"
                style="width: 100%"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('specinfo_select_carrier')"
                @on-open-change="setQuery($event, 'operatorSelect')"
                @on-change="optChange"
              >
                <Option
                  v-for="item in operatorList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item" v-if="anpmVersion == '1'">
            <label class="fn_item_label" style="width: 40%"
              >{{ $t("comm_data_source") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="width: 70%">
              <Select
                v-model="query.dataSource"
                clearable
                filterable
                :only-filter-with-text="true"
                style="width: 100%"
                :placeholder="$t('comm_source')"
              >
                <Option value="0">{{ $t("alarm_dial_probe") }}</Option>
                <Option value="1">{{ $t("alarm_relay_probe") }}</Option>
                <!-- <Option value="4">高频探针</Option> -->
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("alarm_ticket_status") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.dealStatus"
                style="width: 100%"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('comm_please_select')"
              >
                <Option
                  v-for="item in workOrderStatusList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="position: relative">
              <TreeSelect
                v-model="treeValue"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadData"
                @onSelectChange="setOrg"
                @onFocus="foucusFn"
                @onClear="onClear"
              ></TreeSelect>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("alarm_failure_boundary") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.isSpecialLine"
                clearable
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('comm_please_select')"
              >
                <Option
                  v-for="item in bussList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.label }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 40%"
              >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="width: 70%">
              <Input
                v-if="anpmVersion == '1'"
                v-model.trim="query.keyword"
                :title="$t('comm_faulty')"
                :placeholder="$t('comm_faulty')"
              />
              <Input
                v-if="anpmVersion != '1'"
                v-model.trim="query.keyword"
                :title="$t('comm_faulty2')"
                :placeholder="$t('comm_faulty2')"
              />
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.groupId"
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('snmp_pl_man')"
                clearable
              >
                <Option
                  v-for="item in groupingList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}
                </Option>
              </Select>
            </div>
          </div>
        </Col>
        <!-- <Col span="12">

        </Col> -->
        <Col span="14">
          <div class="fn_item faultDuration">
            <label class="fn_item_label"
              >{{ $t("alarm_fault_duration") }}{{ $t("comm_colon") }}</label
            >
            <ul class="">
              <li
                :class="durationTab == index ? 'tabStyle' : ''"
                v-for="(itme, index) in durationList"
                :key="index"
                @click="faultDurationChoice(index, itme.value)"
              >
                {{ itme.label }}
              </li>
            </ul>
            <div v-if="durationShow" class="faultCustomize">
              <Input
                v-model="customizeStartTime"
                maxlength="4"
                :placeholder="$t('alarm_9999')"
                @on-keyup="
                  customizeStartTime =
                    customizeStartTime == ''
                      ? null
                      : customizeStartTime.replace(/\D|\b(0+)/g, '') == '' &&
                        customizeStartTime.replace(/\D|\b(0+)/g, '') != 0
                      ? ''
                      : Number(customizeStartTime.replace(/\D|\b(0+)/g, ''))
                "
              />
              —
              <Input
                v-model="customizeEndTime"
                maxlength="4"
                :placeholder="$t('alarm_9999')"
                @on-keyup="
                  customizeEndTime =
                    customizeEndTime == ''
                      ? null
                      : customizeEndTime.replace(/\D|\b(0+)/g, '') == '' &&
                        customizeEndTime.replace(/\D|\b(0+)/g, '') != 0
                      ? ''
                      : Number(customizeEndTime.replace(/\D|\b(0+)/g, ''))
                "
              />
              <span>{{ $t("alarm_minute") }}</span>
            </div>
          </div>
        </Col>
      </Row>
      <div
        class="fn_tool"
        style="padding-bottom: 0; display: flex; justify-content: space-between"
      >
        <div class="butBoxStyle">
          <ul>
            <li
              :class="tabActiove == index ? 'tabStyle' : ''"
              v-for="(itme, index) in bussTypeList"
              :key="index"
              @click="getTabState(index, itme.value)"
            >
              {{ itme.label }}
            </li>
          </ul>
        </div>

        <div style="display: flex; align-items: center">
          <div class="fn_item" style="margin-bottom: 0px; width: 450px">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="width: calc(100% + 12px)">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
                @on-change="dateChange"
              >
              </DatePicker>
            </div>
          </div>

          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.list"
            icon="ios-search"
            @click="queryClick"
            :title="$t('common_query')"
          ></Button>
          <Button
            type="primary"
            v-if="permissionObj.reset"
            class="skinPrimary reset-btn"
            icon="md-refresh"
            @click="restClick"
            :title="this.$t('but_reset')"
          ></Button>
          <!-- <Button type="primary" v-if="permissionObj.export" class="skinPrimary export-btn" 
          id="exportData" icon="md-open" @click="exportClick" :title="this.$t('but_export')"
          ></Button> -->
          <Button
            class="daoChu-btn"
            id="exportData"
            v-if="permissionObj.export"
            type="primary"
            @click="exportClick"
            :title="this.$t('but_export')"
          >
            <i class="iconfont icon-icon-derive" />
          </Button>
          <Button
            v-if="permissionObj.clear"
            class="skinWarning del-btn"
            icon="ios-eye-off-outline"
            @click="shieldByIds()"
            :title="this.$t('but_clear')"
          ></Button>
        </div>
      </div>
    </div>

    <div class="section-body contentBox_bg">
      <div class="section-body-content">
        <div class="dialTest-tab-content">
          <div class="privateLine-tab-content">
            <Loading :loading="loading"></Loading>
            <Table
              stripe
              ref="networktableList"
              :columns="columns"
              :data="networktableList"
              class="fixed-left-right"
              :no-data-text="
                loading
                  ? ''
                  : networktableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              @on-sort-change="sortChange"
            >
              <template slot-scope="{ row }" slot="orgName">
                {{
                  row.orgName === undefined ||
                  row.orgName === null ||
                  row.orgName === ""
                    ? "--"
                    : row.orgName
                }}
              </template>
              <template slot-scope="{ row }" slot="faultDesc">
                <div class="fault-phenomenon">
                  <div v-if="row.faultDesc" @click="faultPhenomenonOpen(row)">
                    {{ row.faultDesc }}
                  </div>
                  <div v-else class="empty">——</div>
                </div>
              </template>
            </Table>
          </div>
        </div>
        <div
          class="tab-page"
          style="border-top: 0"
          v-if="networktableList.length > 0"
        >
          <Page
            v-page
            :current.sync="currentNum"
            :page-size="query.pageSize"
            :total="totalCount"
            :page-size-opts="pageSizeOpts"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
            show-elevator
            show-sizer
          >
          </Page>
        </div>
      </div>
    </div>
    <!-- 故障现象详情 -->
    <Modal
      class="detail-modal"
      v-model="faultPhenomenon.open"
      :title="faultPhenomenon.title"
      width="460"
      :mask="true"
      sticky
      draggable
    >
      <div
        v-if="faultPhenomenon.content"
        style="
          text-align: center;
          color: var(--table_content_column_link_color, #05eeff);
          word-wrap: break-word;
          word-break: break-all;
        "
      >
        <p v-for="(item, index) in faultPhenomenon.content" :key="index">
          {{ item }}
        </p>
      </div>
      <div slot="footer">
        <Button
          type="primary"
          size="small"
          @click="faultPhenomenon.open = false"
          >{{ $t("but_confirm") }}</Button
        >
      </div>
    </Modal>
    <!--追加、处理-->
    <Modal
      v-model="action.show"
      width="1180"
      :styles="{ top: '100px' }"
      class="action-modal"
      :title="$t('comm_work')"
      :mask="true"
      sticky
      draggable
      :loading="action.loading"
      @on-ok="taskOk('actionForm')"
      @on-cancel="taskCancel('actionForm')"
    >
      <action-item
        ref="actionForm"
        :time="saveQuery"
        :data="action.data"
        :titles="
          '(' +
          $t('alarmlist_fault_type') +
          title.faultType +
          $t('alarmlist_sfl') +
          title.errorIps +
          ')'
        "
      ></action-item>
    </Modal>

    <!--指标-->
    <!-- :styles ="{top: '140px' }" -->
    <Modal
      v-model="indexHome.show"
      :styles="{ top: '100px' }"
      class="action-index-modal"
      :mask="true"
      :width="modalWidth"
      sticky
      draggable
      :footer-hide="true"
      @on-cancel="indexHomeCancel('indexEvent')"
    >
      <div slot="header">
        <span class="title-name">{{ $t("alarm_failure_index") }}</span>
        <span class="title-content">
          {{
            "(" +
            $t("alarmlist_fault_type") +
            title.faultType +
            $t("alarmlist_sfl") +
            title.errorIps +
            ")"
          }}
        </span>
      </div>
      <index-item
        v-show="!isSnmp"
        :dataSource="dataSource"
        ref="indexEvent"
        :data="indexHome.data"
        :time="saveQuery"
      ></index-item>
    </Modal>

    <Modal
      v-model="snmpindexHome.show"
      :width="modalWidth"
      class="action-index-modal"
      :mask="true"
      sticky
      draggable
      :styles="{ top: '100px' }"
      :footer-hide="true"
      @on-cancel="indexHomeCancel('indexEvent')"
    >
      <snmp-item
        v-show="isSnmp"
        :snmp="isSnmp"
        :dataSource="dataSource"
        :time="saveQuery"
        ref="indexEvent1"
        :data="snmpindexHome.data"
      ></snmp-item>
    </Modal>
  </section>
</template>
<script>

import moment from "moment";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import "@/config/page.js";
import Qs from "qs";
import global from "../../../common/global.js";
import $ from "../../../common/jtopo/jquery.min.js";
import "../../../common/jtopo/jtopo-s2.js";
   import langFn  from '@/common/mixins/langFn';
// import { showJTopoToobar } from "../../../common/jtopo/toolbar1.js";
import {
  getJtopoNodeImgByValue,
  rearrangementData,
} from "../style/jtopo-editor.js";
import { mapGetters, mapActions, mapState } from "vuex";
import actionItem from "./actionItem.vue";
import { addDraggable } from "@/common/drag.js";
import indexItem from "./indexItem.vue";
import snmpItem from "./snmpItem.vue";
import locationreload from "@/common/locationReload";
import ipv6Format from "@/common/ipv6Format";

const synth = window.speechSynthesis;
const msg = new SpeechSynthesisUtterance();

function getQueryVariable(variable) {
  let src = window.frames.frameElement.getAttribute('src')
  if (src && src.indexOf('?' + variable + '=') > -1) {
    var temp = src.split('?' + variable + '=')[1].replace(/^\s*|\s*$/g, "");
    if (temp.indexOf('&') > -1) {
      temp = temp.substring(0, temp.indexOf('&'));
    }
    return temp;
  }
  if (src && src.indexOf('&' + variable + '=') > -1) {
    var temp = src.split('&' + variable + '=')[1].replace(/^\s*|\s*$/g, "");
    if (temp.indexOf('&') > -1) {
      temp = temp.substring(0, temp.indexOf('&'));
    }
    return temp;
  }
}
export default {
  name: "index",
      mixins: [langFn],
  components: {
    actionItem,
    indexItem,
    snmpItem,
    TreeSelect
  },
  props: {
    tabData: {
      type: String,
      default: ''
    }
  },
  watch: {
    tabData: {
      handler(value) {
        if (this.networktableList.length === 0 && value === 'networkFault') {
          this.initWebSocket();//支持socket的方法
        }
      },
      deep: true,
      immediate: true,
    },
    "query.keyword":{
       handler(value) {
        if (value == "") {
          this.query.faultIds = "";
          this.query.keyword = "";
        }
      },
      deep: true,
    }
  },
  async created() {
      let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
     this.modalWidth = localStorage.getItem('modalWidth') * 0.98 || document.body.clientWidth * 0.98

    this.operatorList = await this.getListInternationaControlOperator();
    this.anpmVersion = JSON.parse(sessionStorage.getItem('accessToken')).anpmVersion;
    if (getQueryVariable('faultIds')) {
      this.query.faultIds = getQueryVariable('faultIds');
    }
    // 处理 路径拓扑 故障节点单击 后的 查询条件。
    if (getQueryVariable('keyword')) {
      this.query.keyword = getQueryVariable('keyword');
    }
    // locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
    //第一次进来给操作时间赋值
    this.operationTime = new Date();
    this.initWebSocket();//支持socket的方法
    this.dataRefreh(); //启动定时器30秒检查一次，判断当前页不是第一页时，如果10分钟没有操作，则要刷新页面返回到第一页
    moment.locale("zh-cn");
    //获取分组下拉
    this.getGroupingSelect();
  
    this.query.startTime = new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
    this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
    // 如果是跳转  默认90天
    if (getQueryVariable('days')) {
      let days = getQueryVariable('days');
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * (days - 1));
      const end = new Date();
      this.timeRange = [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")]
      this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
      this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
    }
    this.getTreeOrg();
    this.getNetWorkFaultList(this.query);
    this.recordLog();

  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      modalWidth:0,
      isFirstLoad:true,
      internationOperatorListType:'0',
      treeValue: '',
      timeRange: [new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"), new Date().format2("yyyy-MM-dd 23:59:59")],
      soundType: false,
      faultStateTab: [this.$t('comm_not_recovered'), this.$t('comm_has_recovered'), this.$t('comm_all')],
      tabActiove: 0,
      durationTab: 0,
      groupingList: [],
      anpmVersion: '1',
      durationShow: false,
      //权限对象
      permissionObj: {},
      websock: null,
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      saveQuery: {
        startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"),
        endTime: this.timeChange(Date.now()),
      },
      networkfaultList: global.networkfaultList,
      customizeStartTime: "",
      customizeEndTime: "",
      durationList: [
        { value: "", label: this.$t('alarm_all') },
        { value: 1, label: this.$t('alarm_l60m') },
        { value: 2, label: this.$t('alarm_g60m') },
        { value: 3, label: this.$t('alarm_custom') },
      ],
      bussTypeList: [//（1未恢复，0已恢复）
        { value: 1, label: this.$t('comm_not_recovered') },
        { value: 0, label: this.$t('comm_has_recovered') },
        { value: "", label: this.$t('comm_all') },
      ],
      workOrderStatusList: [
        { value: 1, label: this.$t('comm_untreated') },
        { value: 4, label: this.$t('comm_closed_loop') },
        { value: 2, label: this.$t('comm_process') },
      ],
      bussList: [
        { value: 0, label: this.$t('comm_enterprise_network') },
        { value: 1, label: this.$t('comm_special_line_network') },
        { value: 2, label: this.$t('comm_other') }
      ],
      operatorList: [
        { value: 1, label: this.$t('server_China_Mobile') },
        { value: 2, label: this.$t('server_China_Unicom') },
        { value: 3, label: this.$t('server_China_Telecom') },
        { value: 4, label: this.$t('server_China_Broadcasting') },
        { value: 6, label: this.$t('comm_other') },
      ],
      faultPhenomenon: {
        open: false,
        title: "",
        content: [],
      },
      isSnmp: false,
      dataSource: 0,
      affectedCount: 0,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },
      timeOptions: {
        shortcuts: [
          {
            text: this.$t('comm_half_hour'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return [start.format("yyyy-MM-dd HH:mm:ss"), end.format("yyyy-MM-dd HH:mm:ss")];
            }
          },
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
              let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
              let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          }
        ]
      },
      currentTime: Date.now(),
      interValCurTime: null,
      //搜索字段
      query: {
        faultIds: "",
        userId: "",
        tencentIds: "",
        startTime: "",
        endTime: "",
        orgId: "",
        fieldName: "", //排序字段名
        orderBy: "", //排序方式
        faultType: "0", //工单类型
        recoveryed: 1, //故障状态（1未恢复，0已恢复）
        dataSource: "", //数据源
        dealStatus: "", //工单状态
        isSpecialLine: "", //故障分类
        opt: "", //运营商
        keyword: "", //关键字
        groupId: "", //分组
        delFlag: '0',//0正常，1删除，2清除
        compareType: "",//1小于等于60,2大于60,3自定义,null全部
        faultStartTime: "",//故障历时自定义开始时间
        faultEndTime: "",//故障历时自定义结束时间
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
        tab: "网络故障",
        ids: []
      },
      returnShow: false,
      // keepState:0,
      //界面操作的时间
      operationTime: "",
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //新建修改Modal参数
      modalParameter: {
        modalTitle: "", //标题(新建/修改)
        show: false, //控制是否显示
        loading: true,
      },
      //新建/修改Modal数据
      modalList: {
        factory: "", //设备厂家
        model: [], //设备型号
        // hierarchy:'',
        remarks: "", //备注
        oidNameCn: "", //名称
        oid: "", //OID
      },
      oidNameList: [], //新建修改名称下拉选项
      //表格数据
      networktableList: [],
      action: {
        show: false,
        loading: true,
        type: 1, //1、追加，2、处理
        data: {},
      },
      indexHome: {
        show: false,
        data: {},
      },
      snmpindexHome: {
        show: false,
        data: {},
      },
      title: {
        faultType: 0,
        errorIps: "",
        reason: "",
      },
      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 35,
          className: "bgColor",
          align: "center",
          //  fixed: 'left'
        },
        // 故障开始时间
        {
          title: this.$t('alarm_start_time'),
          key: "faultStartTime",
          align: "left",
          className: "bgColor",
          width: 160,
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.faultStartTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : str
            );
          },
        },
        // 要修改的列
        {
          title: this.$t('alarm_fault_type'),
          key: "faultType",
          align: "left",
          width: this.getColumnWidth(80, 120),
          render: (h, params) => {
            let str = Number(params.row.faultType),
              text = "";
            switch (str) {
              case 1:
                text = this.$t('dash_interrupt');
                break;
              case 2:
                text = this.$t('dash_delay_deterioration');
                break;
              case 3:
                text = this.$t('dash_packet_loss');
                break;
              case 7:
                text = this.$t('dash_routing_fluctuation');
                break;
              case 8:
                text = this.$t('dash_urgent_notice');
                break;
              default:
                text = "--";
                break;
            }
            let text2 = ''
            if(text.length > 10 ) {
               text2 = text.slice(0,8) + '...'
                let Arr1 =  h("span", text);

                     return h('Tooltip',{
                      props: {
                        placement:'top-start'
                      }
                     },
                     [text2,h(
                      'span',{
                        slot:'content',
                       
                        
                      },text
                     )]);
            
            } else {
              text2 = text
              return h('span',text2)
            }
          
          },
        },
        // 故障定界
        {
          title: this.$t('alarm_failure_boundary'),
          key: "isSpecialLine",
          align: "left",
          width: this.getColumnWidth(100,110),
          render: (h, params) => {
            let str = "";
            if (params.row.isSpecialLine == 0) {
              str = this.$t('comm_enterprise_network')
            } else {
              str = this.$t('dash_Special_line')
            }
            let Arr1 =  h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
            let str2 = ''
            if(str.length > 14) {
              str2 = str.slice(0,12) + '...'
                  return h('Tooltip',{
                      props: {
                        placement:'top-start'
                      }
                     },
                     [str2,h(
                      'span',{
                        slot:'content',
                       
                        
                      },str
                     )]);
            }else {
              str2 = str
              return h('span',str2)
            }

            //   return h(
            //   "div",
            //   {
            //     class: {
            //       "text-ellipsis": true,
            //     },
            //     style: {
            //       "word-break": "keep-all",
            //       "white-space": "nowrap",
            //       "overflow": "hidden",
            //       "text-overflow": "ellipsis",
            //     },
            //     domProps: {
            //       title: str,
            //     },
            //   },
            //   [Arr1]
            // );
          },
        },
        // 运营商
        {
          title: this.$t('comm_operators'),
          key: "operator",
          align: "left",
          width: 100,
          render: (h, params) => {
            let str = Number(params.row.operator),text = "--";
            if (params.row.faultType == this.$t('comm_enterprise_network')) {
              text = "--";
            } else {


                let items = this.operatorList.filter((item)=>{
                  return item.value == str && item.value > 0;

                });
                if(items.length > 0){
                    text =  items[0].label;
                }else{
                  text = "--";
                }
            
            }

            if(text !== "--") {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'top',
                  content: text,
                }

              },text)

            ])
            }else {
              return h('div',text)
            }
          },
        },
        {
          title: this.$t('comm_org'),
          key: "orgName",
          align: "left",
          width: 90,
          tooltip: true,
          solt: 'orgName',
        },
        // 疑似故障链路
        {
          title: this.$t('alarm_susp_failed_link'),
          key: "errorIps",
          width: 330,
          align: "left",
          // ellipsis: true,
          sortable: "custom",
          render: (h, params) => {
            let maxWidth = params.column.width; // 获取动态传递的宽度
            console.log("maxWidth", maxWidth);
            let str = params.row.errorIps;
            let Arr1 = h(
              "span",
              str === undefined || str === null || str === ""
                ? "--"
                : str
            );
            // return h(
            //   "div",
            //   {
            //     class: {
            //       "text-ellipsis": true,
            //     },
            //     style: {
            //       "word-break": "keep-all",
            //       "white-space": "nowrap",
            //       "overflow": "hidden",
            //       "text-overflow": "ellipsis",
            //     },
            //     domProps: {
            //       title: str,
            //     },
            //   },
            //   [Arr1]
            // );
            let str2 = ''
            // if(str.length > 24) {
              // str2 = str.slice(0,22) + '...'
                  // return h('Tooltip',{
                  //     props: {
                  //       placement:'top-start'
                  //     }
                  //    },
                  //    [str2,h(
                  //     'span',{
                  //       slot:'content',                    
                  //     },str
                  //    )]);
            //   return h('span',str2)
            // }else {
            //   str2 = str
            //   return h('span',str2)
            // }
            str2 = ipv6Format.formatIpv6Double(str,maxWidth);
            return h('div', { style: { whiteSpace: 'pre-wrap' } }, str2);
          },
        },
        {
          title: this.$t('alarm_symptom'),
          key: "faultDesc",
          slot: "faultDesc",
          align: "left",
         
          minWidth: 260,
        },
        // 影响链路数
        {
          title: this.$t('server_affected'),
          key: "linkNum",
          align: "left",
          width: this.getColumnWidth(100,130),
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.linkNum;
            const idS = params.row.dealStatus != 7 && params.row.faultType != 7;
            return h(
              idS ? "a" : "span",
              {
                class: "action-btn action-blue",
                style: {
                  color: `var(--table_content_column_link_color, #05eeff)`,
                },
                on: {
                  click: () => {
                    if (idS) {
                      this.actionClick(params.row, "linkNum");
                    }
                  },
                },
              },
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: this.$t('alarm_failure_duration'),
          key: "dealDuration",
          align: "left",
          width: this.getColumnWidth(110,130),
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.dealDuration;
            return h(
              "span",
              str === undefined ||
                str === null ||
                str === "" ||
                params.row.dealStatus == 7 ||
                params.row.faultType == 7
                ? "--"
                : str
            );
          },
        },
        {
          title: this.$t('alarm_failure_status'),
          key: "recoveryed",
          align: "left",
          width: this.getColumnWidth(80,110),
          render: (h, params) => {
            let str = Number(params.row.recoveryed),
              text = "--";
            if (params.row.dealStatus != 7 && params.row.faultType != 7) {
              switch (str) {
                case 1:
                  text = this.$t('common_unrecovered');
                  break;
                case 0:
                  text = this.$t('common_recovered');
                  break;
                default:
                  text = "--";
                  break;
              }
            }
            return h(
              "span",
              {
                class:
                  str === 1 ? "action-red" : str === 0 ? "action-green" : "",
              },
              text
            );
          },
        },
        {
          title: this.$t('alarm_ticket_status'),
          key: "dealStatus",
          align: "left",
          width: this.getColumnWidth(80,110),
          render: (h, params) => {
            let str = Number(params.row.dealStatus),
              text = "--";
            switch (str) {
              case 4:
                text = this.$t('server_closed_loop');
                break;
              case 2:
                text = this.$t('server_process');
                break;
              case 1:
                text = this.$t('server_unprocessed');
                break;
              default:
                text = "--";
                break;
            }
            return h(
              "span",
              {
                class:
                  str === 4 ? "" : str === 2 ? "action-green" : "action-red",
              },
              text
            );
          },
        },
        {
          title: this.$t('comm_operate'),
          width: "110",
          align: "center",
          //  fixed: 'right',
          className: "bgColor",
          render: (h, params) => {
            // console.log(this.currentSkin,'this.currentSkin9999999999999999999999')
            const row = params.row;
            const faultType = row.faultType;
            const status = row.dealStatus;
            const array = [];
            let iconClass = "zhibiao";
            let str = params.row.linkNum;
            if (str === undefined || str === null || str === "") {
              iconClass = "";
            }
            const idS =
              params.row.dealStatus != 7 && params.row.faultType != 7;
            array.push(

              h('Tooltip',
                {
                  props: {
                    placement: 'left-end',
                     transfer: true
                  }
                }, [
                h(
                  "span",
                  {
                    class: this.currentSkin == 1 ? "look1-btn":"look2-btn",
                    // class: "look1-btn",
                    style: {
                          display: this.permissionObj.look
                            ? "inline-block"
                            : "none",
                    },
                    on: {
                      click: () => {
                        if(this.permissionObj.look){
                          this.actionClick(params.row, "linkNum")
                        }else{
                          console.log("无权限")
                        }
                        
                      },
                    },
                  },
                ),
                h('span', { slot: 'content', },  this.permissionObj.look ? this.$t('server_view') : "")
              ])
            );
            if (faultType == 7 || faultType == 8) {
              array.push(
                h("a", ""))
            } else {
              if (status == 4) {
                array.push(
                  h('Tooltip',
                    {
                      props: {
                        placement: 'left-end',
                         transfer: true
                      }
                    }, [
                    h(
                      "span",
                      {
                        class: "create1-btn",
                        style: {
                          display: this.permissionObj.process
                            ? "inline-block"
                            : "none",
                        },
                        on: {
                          click: () => {
                            this.actionClick(params.row, "append")
                          },
                        },
                      },
                    ),
                    h('span', { slot: 'content', }, this.$t('network_append'))
                  ])
                );
              } else if (status == 1 || status == 2) {
                array.push(

                  h('Tooltip',
                    {
                      props: {
                        placement: 'left-end',
                         transfer: true
                      }
                    }, [
                    h(
                      "span",
                      {
                        class: "handle1-btn",
                        style: {
                          display: this.permissionObj.process
                            ? "inline-block"
                            : "none",
                        },
                        on: {
                          click: () => {
                            this.actionClick(params.row, "handle")
                          },
                        },
                      },
                    ),
                    h('span', { slot: 'content'}, this.$t('comm_deal'))
                  ])
                );
              }
            }
            return h("div", array);
          },
        },
      ],
      audioType: {
        id: '',
        defValue: '',
        creatorId: ''
      },
    };s
  },
  mounted() {
    this.$nextTick(() => {
      // 说明是弹窗进来的界面，就不跳转了。
      if(getQueryVariable('days')){
            // 如果是跳转 
         
      }else{
          locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
      }
    })
   
    this.query.pageNo = this.currentNum;
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    this.setInterTime = setInterval(this.checkTimeout, 1000);
    this.interrefresh = setInterval(() => {
      this.getNetWorkFaultList(this.query);
    }, 1000 * 60);
    // this.initWebSocket();//支持socket的方法
    document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    top.document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },


  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    foucusFn() {
      this.getTreeOrg()

    },
    moreBtnClick(val) {
      eval(`this.${val}`);
    },
    dateChange(val, type) {
      // console.log(val,type,'什么东西')
      if(type == 'date') {
        this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")]

      }
    
        
      
    
    },
    // 故障历时选择tab切换
    faultDurationChoice(index, val) {
      if (this.durationTab != index) {
        if (index == 3) {
          this.durationShow = true;
        } else {
          this.durationShow = false;
        }
        this.durationTab = index;
        this.query.compareType = val;
      }
    },

    // 列表排序
    sortChange(e) {
      if (e.order == 'normal') {
        this.query.orderBy = ''
        this.query.fieldName = ''
      } else {
        this.query.orderBy = e.order
        this.query.fieldName = e.key
      }
      this.getNetWorkFaultList(this.query)
    },
    // 点击故障状态切换获取类型
    getTabState(index, val) {
      if (this.tabActiove != index) {
        this.tabActiove = index;
        this.query.recoveryed = val;
        this.queryClick();
      }
    },
    checkTimeout() {
      this.currentsTime = new Date().getTime(); //更新当前时间
      this.lastTime = localStorage.getItem("lastTime");
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    renderContent(h, { root, node, data }) {
      return h(
        "span",
        {
          style: {
            display: "inline-block",
            width: "100%",
            cursor: "pointer",
          },
          on: {
            click: () => {
              this.clickTree1(data);
            },
          },
        },
        [
          h(
            "Tooltip",
            {
              props: {
                placement: "top-end",
                transfer: true,
              },
            },
            [
              data.name, //控制树形显示的内容
              h(
                "span",
                { slot: "content", style: { whiteSpace: "normal" } },
                data.title //控制Tooltip显示的内容
              ),
            ]
          ),
          h("span", {
            style: {
              display: "inline-block",
              float: "left",
              marginLeft: "32px",
            },
          }),
        ]
      );
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    onClear() {
      this.query.orgId = ''
      this.treeValue = ''
    },
    choicesOrg() {
      this.orgTree = true;
    },
    // 获取分组下拉
    getGroupingSelect() {
      this.$http.post('/group/list', { pageNo: 1, pageSize: 10000 }).then(res => {
        this.groupingList = res.data.records
      })
    },
    queryClick() {
      this.recordLog();
      //点击搜索
      this.query.pageNo = this.currentNum = 1;
      this.query.faultIdStrs = this.query.faultIds;


      // 故障历时为自定义时输入框的值处理
      if (this.durationTab == 3) {
        // if(this.customizeStartTime == '' || this.customizeEndTime == ''){
        //   this.$Message.warning("自定义时间不能为空")
        //   return;
        // }
        console.log(this.customizeEndTime)
        if (this.customizeStartTime && this.customizeEndTime && Number(this.customizeStartTime) >= Number(this.customizeEndTime)) {
          this.$Message.warning(this.$t('warning_latter_greater_previous'))
          return;
        }
        if (this.customizeEndTime === 0 && (this.customizeStartTime > 0 || this.customizeStartTime === 0)) {
          this.$Message.warning(this.$t('warning_latter_greater_previous'))
          return;
        }
        this.query.faultStartTime = this.customizeStartTime;
        this.query.faultEndTime = this.customizeEndTime;
      } else {
        this.query.faultStartTime = ""
        this.query.faultEndTime = ""
      }



      let startVal = moment(new Date(this.timeRange[0]).format("yyyy-MM-dd HH:mm:ss"), "YYYY-MM-DD hh:mm:ss").valueOf();
      let endVal = moment(new Date(this.timeRange[1]).format("yyyy-MM-dd HH:mm:ss"), "YYYY-MM-DD 23:59:59").valueOf();
      // this.query.faultIds 不能空 ， 就是路径拓扑页面过来的数据
      if (this.query.faultIds == "") {
        if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
          this.$Message.warning(this.$t('warning_time_not_exceed_62'));
          return;
        }
      }

      this.saveQuery.startTime = this.query.startTime = new Date(this.timeRange[0]).format("yyyy-MM-dd HH:mm:ss");
      this.saveQuery.endTime = this.query.endTime = new Date(this.timeRange[1]).format("yyyy-MM-dd HH:mm:ss");
      this.getNetWorkFaultList(this.query);
    },
    restClick() {
      this.timeRange = [new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00"), new Date().format2("yyyy-MM-dd 23:59:59")]
      this.saveQuery.startTime = new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
      this.saveQuery.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.startTime = new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format("yyyy-MM-dd 00:00:00");
      this.query.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.faultIds = "";
      this.query.faultIdStrs = "";
      this.query.faultType = "0";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.keyword = "";
      this.query.dataSource = "";
      this.query.orgId = "";
      this.query.groupId = "";
      this.query.dealStatus = "";
      this.query.isSpecialLine = "";
      this.query.opt = "";
      this.query.tencentIds = "";
      this.query.compareType = "";
      this.durationTab = 0;
      this.durationShow = false;
      this.customizeStartTime = "";
      this.customizeEndTime = "";
      this.treeValue = "";
    },
    actionClick(row, type) {
      let _self = this;
      _self.indexHome.show = false;
      _self.snmpindexHome.show = false;
      _self.title.faultType =
        row.faultType == 1
          ? this.$t('dash_interrupt')
          : row.faultType == 2
            ? this.$t('dash_delay_deterioration')
            : row.faultType == 3
              ? this.$t('dash_packet_loss')
              : row.faultType == 4
                ? this.$t('dash_deterioration')
                : "";

      let str = row.errorIps;
      let text = str;
      // if (str.indexOf("（") >= 0 && str.indexOf("-") >= 0) {
      //   str = str.slice(str.indexOf("-")+1);
      // }
      // let textArr =str ? str.split(','): [],text = '';
      // if (row.faultType !=7 && row.faultType != 8) {
      //   text = textArr.slice(0,textArr.length-1).join(',');
      // }else{
      //   text = textArr.join(',');
      // }
      _self.title.errorIps = text;
      _self.title.reason =
        row.faultCauseType == 0
          ? this.$t('comm_unknown')
          : row.faultCauseType == 1
            ? this.$t('comm_Blackout')
            : row.faultCauseType == 2
              ? this.$t('comm_cut')
              : row.faultCauseType == 3
                ? this.$t('comm_congestion')
                : "";
      switch (type) {
        case "linkNum": //影响链路数
          _self.dataSource = row.dataSource;
          _self.affectedCount = Number(row.affectedCount);
          if (row.dataSource == 1) {
            this.isSnmp = true;
            _self.snmpindexHome.data = row;
            _self.snmpindexHome.show = true;
          } else {
            this.isSnmp = false;
            row.topoShow = false;
            _self.indexHome.data = row;
            _self.indexHome.show = true;
          }
          break;
        case "handle": //处理
          _self.action.type = 2;
          _self.action.data = row;
          _self.action.show = true;
          break;
        case "append": //追加
          _self.action.type = 1;
          _self.action.data = row;
          _self.action.show = true;
          break;
      }
    },
    async getNetWorkFaultList(param) {
      let _self = this;
      if (!_self.query.faultType) {
        _self.query.faultType = 0
      }
      _self.query.faultIdStrs = _self.query.faultIds;
      _self.loading = true;
      await _self.$http
        .PostJson("/fault/getAlarmList", param)
        .then((res) => {
          if (res.code === 1) {
            if (res.data) {
              _self.networktableList = res.data.records;
              _self.totalCount = res.data.total || 0;
              //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
              let _that = this;
              setTimeout(function () {
                let objData = _that.$refs.networktableList.$refs.tbody.objData;
                for (let key in objData) {
                  if (_that.selectedIds.has(objData[key].id)) {
                    objData[key]._isChecked = true;
                  }
                }
              }, 0);
            } else {
              _self.networktableList = [];
            }
          } else {
            this.$Message.warning(res.msg);
          }
        })
        .finally(() => {
          _self.loading = false;
        });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getNetWorkFaultList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getNetWorkFaultList(this.query);
    },
    // 打开故障现象详情
    faultPhenomenonOpen(row, sign) {
      this.faultPhenomenon.title = this.$t('comm_symptom');
      const list = (this.faultPhenomenon.content = []);
      if (row && row.faultDesc) {
        Array.prototype.push.apply(
          list,
          String(row.faultDesc)
            .split(";")
            .map((text, index) => {
              return text.trim();
            })
        );
      }
      this.faultPhenomenon.open = true;
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        now.toTimeString().substr(0, 8)
      );
    },
    //追加、处理事件
    taskOk(refName) {
      let _self = this,
        param = this.$refs[refName].getParams();
      _self.$http.wisdomPost("/fault/saveProceRecord", param).then((res) => {
        if (res.code === 1) {
          this.$Message.success(
             (_self.action.type === 1 ? this.$t('comm_add_on') : this.$t('comm_deal')) + this.$t('comm_success')
          );
          this.action.loading = false;
          this.action.show = false;
          this.$nextTick(() => {
            this.action.loading = true;
          });
          // this.resetQuery();
          this.getNetWorkFaultList(this.query);
          this.taskCancel(refName);
        } else {
          this.action.loading = false;
          this.$nextTick(() => {
            this.action.loading = true;
          });
        }
      });
    },
    taskCancel(refName) {
      this.$Modal.visible = false;
      this.$refs[refName].resetRegion();
      this.action.data = {};
    },
    //影响链路数事件
    indexHomeCancel(refName) {
      this.$Modal.visible = false;
      if (!this.isSnmp) {
        this.$refs["indexEvent"].resetRegion();
      } else if (this.isSnmp) {
      }
      this.indexHome.data = {};
      this.snmpindexHome.data = {};
    },
    //屏蔽清除
    shieldByIds(id) {
      var selectedIdsArrary = Array.from(this.selectedIds);
      var ids = "";
      if (id) {
        ids = id;
      } else {
        if(selectedIdsArrary.length==0){
          this.$Message.warning(this.$t('warning_select_clear_first'));
          return;
        }
        for (var i = 0; i < selectedIdsArrary.length; i++) {
          if (i === 0) {
            ids += selectedIdsArrary[i];
          } else {
            ids += "," + selectedIdsArrary[i];
          }
        }
      }
      var param = {
        ids: ids,
        type: 1,
        tab: this.query.tab
      };
      top.window.$iviewModal.confirm({
        title: this.$t('comm_tip'),
        content: '<p>'+this.$t('dash_clear')+'</p>',
        onOk: () => {
          this.$http.wisdomPost("/fault/shieldByIds", param).then((res) => {
            if (res&&res.code === 1) {
              this.$Message.success(this.$t('dash_clear_successfully'));
              this.query.pageNo = this.currentNum = 1;
              this.selectedIds = new Set();
              this.getNetWorkFaultList(this.query);
            } else {
              this.$Message.warning(res.msg);
            }
          })
        }
      });
    },
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        var data = this.$refs.networktableList.data;
        data.forEach(item => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach(item => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },

    // 获取运营商类型国际化信息
    async getListInternationaControlOperator() {
       await this.$http.wisdomPost('/internationaControl/queryCode',{key:"internation_operator_type"}).then(res => {
          if (res.code == 1) {
            if(res.data){
                this.internationOperatorListType = res.data.value;
            }
          }
      });
      //获取运营商
      let operatorList = [];
      var internationOperatorTypeTemp = 'chinaOperator';
      if(this.internationOperatorListType == '1'){
          internationOperatorTypeTemp = 'philippinesOperator';
         operatorList.push({value:0,label:'All'});
      }else{
         operatorList.push({value:0,label:this.$t('comm_all')});
      }
     await this.$http
        .wisdomPost("/dataTable/queryCode",{key:internationOperatorTypeTemp})
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if(data){
              data.map(item => {
                operatorList.push({value:parseInt(item.value),label:item.lable});
              });
            }
          } else {
            operatorList = [];
          }
        })
        .catch((err) => {
          operatorList = [];
        });

        return operatorList;
    },
     
    //导出
    exportClick(val) {
      this.saveLog();
      let _self = this;
      let fileName = this.$t('server_faults') + ".xlsx";
      // let fileName = '全部故障清单.xlsx';
      // if (val === 'notAll') {
      //     let exportIds = Array.from(this.selectedIds);
      //     if (exportIds.length < 1) {
      //         this.$Message.warning(this.$t('specquality_select'));
      //         return;
      //     }
      //     fileName = this.$t('server_faults') + ".xlsx";
      //     this.query.ids = exportIds;
      //     this.query.idArr = exportIds;
      // }
      this.$Loading.start();
      let param = Object.assign({}, this.query);
      this.$axios({
        url: "/fault/exportFaultXlsx",
        method: "post",
        data: this.query,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // var fileName = "";
            // fileName = "故障清单.xlsx";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => {
          this.selectedIds = new Set();
          this.query.ids = null;
          this.queryClick();
          this.$Loading.finish();
        });
    },
    setQuery(flag, e) {
      if (flag) {
        this.$refs[e].query = "";
      }
    },
    optChange(val) {
      if (val === 0) {
        this.query.opt = "";
      } else {
        this.query.opt = val;
      }
    },
    //清除查询条件
    resetQuery() {
      this.query.startTime = null;
      this.query.endTime = null;
      this.query.keyword = "";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.dealStatus = "";
      if (!this.query.faultType) {
        this.query.faultType = "0";
      }
      this.query.opt = "";
      this.query.orgId = "";
      this.query.pageNo = 1;
      this.query.pageSize = 10;
      this.getNetWorkFaultList(this.query);
    },
    initWebSocket() {
      //初始化weosocket
      let token = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
      const wsuri =
        "wss://" +
        this.$baseUrl.replace(top.window.location.protocol + "//", "") +
        "/faultSocket/" +
        token;
      if ("WebSocket" in window) {
        this.websock && this.websock.close();
        if (!this.websock) {
          this.websock = new WebSocket(wsuri);
          this.websock.onopen = this.websocketonopen;
          this.websock.onmessage = this.websocketonmessage;
          this.websock.onerror = this.websocketonerror;
          this.websock.onclose = this.websocketclose;
        } else {
        }
      } else {
        //不支持websoket时只展示列表
        this.getNetWorkFaultList(this.query);
      }
    },
    websocketsend(Data) {
      //数据发送
      if (this.websock.readyState === 0) {
        this.initWebSocket();
      } else {
        this.websock.send(Data);
      }
    },
    websocketclose(e) {
      //关闭
      console.log("socket断开连接", e);
      if (this.$route.name == this.$t('alarm_fault_list') && this.tabData === 'networkFault') {
        if(!this.isFirstLoad){
            this.getNetWorkFaultList(this.query);
        }
        this.isFirstLoad = false;
      }
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("socket已建立链接");
      let param = Object.assign({}, this.query);
      param.userId = JSON.parse(sessionStorage.getItem("accessToken")).user.id;
      this.websocketsend(JSON.stringify(param));
    },
    websocketonerror() {
      //连接建立失败重连
      this.initWebSocket();
    },
    websocketonmessage(e) {
      //数据接收
      let redata = JSON.parse(e.data);
      this.loading = false;

      //判断如果当前页数大于1，则不能刷新列表
      let datas = redata.data.records[0]
      if (this.query.pageNo == 1) {
        if (datas || datas != "") {
          // this.query.compareType = "";
          // this.query.faultStartTime = "";
          // this.query.faultEndTime = "";
          this.$http
            .PostJson("/fault/getAlarmList", this.query)
            .then((res) => {
              if (res.code === 1) {
                if (res.data) {
                  this.networktableList = res.data.records;
                  this.totalCount = res.data.total || 0;
                  //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                  setTimeout(() => {
                    let objData = this.$refs.networktableList.$refs.tbody.objData;
                    for (let key in objData) {
                      if (this.selectedIds.has(objData[key].id)) {
                        objData[key]._isChecked = true;
                      }
                    }
                  }, 0);
                } else {
                  this.networktableList = [];
                }
              } else {
                this.$Message.warning(res.msg);
              }
            })
        }
      }
    },
    // 定时刷新数
    dataRefreh() {
      if (this.tabData === 'networkFault') {
        // 计时器为空，操作
        setInterval(() => {
          //如果10分钟没有操作，则要刷新页面返回到第一页
          if (this.query.pageNo != 1) {
            let nowDate = new Date();
            let z = nowDate.getTime() - this.operationTime.getTime();
            if (z > 600000) {
              this.pageChange(1);
            } else {
            }
          }
        }, 30000);
      }

    },
    //点击当前页面触发事件
    bodyClick() {
      let nowDate = new Date();
      this.operationTime = nowDate; //刷新操作时间
    },
    recordLog() {
      this.$http.wisdomPost("/audit/queryList", { tab: "网络故障" }).then(res => { });
    },
    saveLog() {
      //直接添加安全审计日志 开始
      this.$http.wisdomPost("/audit/saveLog", {
        url: "/fault/exportFaultXlsx",
        funName: "网络故障",
        description: "导出网络故障",
        operationType: 13,
        eventType: 7,
        alarmType: 0,
        logType: 2,
        isDel: 0
      }).then(res => { });
      //直接添加安全审计日志 结束
    }
  },
  destroyed() {
    // this.websocketclose();
    if (this.websock) {
      this.websock.close()
    }
  },

};
</script>

<style lang='less' scoped>
/deep/ .ivu-table-cell a {
  color: #05eeff;
}

.urgentTitle {
  text-align: left;
  margin-bottom: 10px;
}

.butBoxStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .fault-top-box {
    margin-bottom: 0 !important;
  }

  ul {
    display: flex;
    align-items: center;
    list-style: none;
    border-radius: 5px;
    // border: 1px solid #04478e;
    border: 1px solid var(--networdFault_type_border_color, #04478e);
  }

  li {
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    // color: #5ca0d5;
    color: var(--networdFault_type_border_normal_font_color, #5ca0d5);
    // color: var(--font_color,#dcdee2);
    cursor: pointer;
    // border: 1px solid #04478e;
    border: 1px solid var(--networdFault_type_border_normal_color, #04478e);
    // border: 1px solid var(--border_color,#dcdee2);
  }

  li:last-child {
    // border-right: 1px solid var(--border_color,#dcdee2);
  }

  .tabStyle {
    // background: #02b8fd;
    background: var(--search_check_box_background_color, #02b8fd);
    // color: #060d15;
    color: var(--search_check_box_font_color, #060d15);
    border-color: #02b8fd;
  }
}

.faultDuration {
  align-items: center;
  min-width: 22%;

  .fault-top-item {
    margin-left: 0 !important;
  }

  .faultCustomize {
    display: flex;
    align-items: center;

    .ivu-input-wrapper {
      width: 100px;
      margin: 0 5px;
    }
  }

  ul {
    //display: flex;
    align-items: center;
    display: inline-block;
    list-style: none;
  }

  li {
    display: inline-block;
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    // color: #5ca0d5;
    color: var(--search_check_box_font_tabStyle_two_color, #5ca0d5);
    // color: var(--font_color,#dcdee2);
    cursor: pointer;
    border: 1px solid var(--border_color, #dcdee2);
  }

  li:last-child {
    // border-right: 1px solid var(--border_color,#dcdee2);
  }

  .tabStyle {
    // background: #061824;
    background: var(--search_check_box_bg_tabStyle_color, #061824);
    // color: #fff;
    color: var(--search_check_box_font_tabStyle_color, #fff);
    // border-color: #05eeff;
    border-color: var(--networdFault_duration_border_select_color, #05eeff);
  }
}

.ivu-input::-webkit-input-placeholder {
  font-size: 12px !important;
}

.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  background: #f1f6fe !important;
}

.task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
  height: auto !important;
  min-height: 32px;
}

.multipleSelect .ivu-select-input {
  padding: 0 !important;
}

.fault-phenomenon {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  // color: #05eeff;
  color: var(--table_content_column_link_color, #05eeff);
}

// .fault-phenomenon-linkNum{
//   color: var(--table_content_column_link_color, '#05eeff') !important;
// }
.fault-phenomenon > div {
  cursor: pointer;
}

.fault-phenomenon > div.empty {
  color: black;
  cursor: default;
}

.zhibiao {
  background-image: url("../../../assets/wisdom/zhibiao.png") !important;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 22px 22px;
}
</style>
<style scoped lang="less">
.screenCondition {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .fault-top-box {
    width: 22% !important;
    margin-right: 35px;

    label {
      min-width: 70px;
      text-align: right;
    }

    .fault-top-item {
      margin-left: 70px;

      /deep/.ivu-input {
        padding-left: 10px !important;
      }
    }
  }

  .timeBox {
    width: calc(44% + 35px) !important;

    .timeStyle {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .reach {
        text-align: center;
        padding: 0px 45px;
      }
    }

    /deep/.ivu-btn-default {
      display: none;
    }
  }
}

.soundPlay {
  margin-right: 24px;
  cursor: pointer;
}

.wisdom-fault .wisdom-fault-top .select-box {
  width: 20%;
}

.wisdom-fault .wisdom-fault-top .keyword-box {
  width: 25%;
}

/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}

/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}

.daoChu-btn {
  width: 60px !important;
}

.more-btn {
  width: 100% !important;
  height: 35px !important;
  color: #02b8fd;
  background-color: #061824;
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(
      to right,
      var(--input_b_color, #ffffff),
      var(--input_b_color, #ffffff)
    ),
    linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
</style>


