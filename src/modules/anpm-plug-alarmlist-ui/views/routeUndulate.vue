<template>
  <!-- 路由波动页面 -->
  <section
    class="sectionBox"
    style="min-width: 100% !important"
    @click="bodyClick"
  >
    <div class="section-top">
      <Row class="fn_box">
        <!-- <Col span="6">
        <div class="fn_item" v-if="anpmVersion == '1'">
          <label class="fn_item_label">数据源：</label>
          <div class="fn_item_box">
            <Select
              v-model="query.dataSource"
              clearable
              filterable
              style="width: 100%"
              placeholder="数据源"
              <Option value="0">拨测探针</Option>
                      <Option value="1">中继探针</Option>
              <Option value="4">高频探针</Option> 
            </Select>
          </div>
        </div>
      </Col> -->
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="position: relative">
              <TreeSelect
                v-model="treeValue"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadData"
                @onSelectChange="setOrg"
                @onFocus="focusFn"
                @onClear="onClear"
              ></TreeSelect>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="query.groupId"
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('snmp_pl_man')"
                clearable
              >
                <Option
                  v-for="item in groupingList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}
                </Option>
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label"
              >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Input
                :placeholder="$t('alarm_symptom_placeholder')"
                v-model="query.keyword"
                maxlength="200"
                clearable
              />
            </div>
          </div>
        </Col>
      </Row>
      <div class="fn_tool">
        <div
          style="display: flex; align-items: center; justify-content: flex-end"
        >
          <div class="fn_item" style="margin-bottom: 0px; width: 450px">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box" style="width: 100%">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
                @on-change="dateChange"
              >
              </DatePicker>
            </div>
          </div>
          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.list"
            icon="ios-search"
            @click="queryClick"
            :title="$t('common_query')"
          ></Button>
          <Button
            type="primary"
            v-if="permissionObj.reset"
            class="skinPrimary reset-btn"
            icon="md-refresh"
            @click="restClick"
            :title="this.$t('but_reset')"
          ></Button>
          <!-- <Button
            type="primary"
            v-if="permissionObj.export"
            id="exportData"
            class="skinPrimary export-btn"
            icon="md-open"
            @click="exportClick"
            title="导出"
          ></Button> -->
          <Button
            class="daoChu-btn"
            id="exportData"
            v-if="permissionObj.export"
            type="primary"
            @click="exportClick"
            :title="this.$t('but_export')"
          >
            <i class="iconfont icon-icon-derive" />
          </Button>
          <Button
            type="error"
            v-if="permissionObj.clear"
            class="skinWarning del-btn"
            icon="ios-eye-off-outline"
            @click="shieldByIds()"
            :title="this.$t('but_clear')"
          ></Button>
          <!-- <Dropdown @on-click="moreBtnClick">
              <Button class="more-btn">
                  更多
                  <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                  <Dropdown v-if="permissionObj.export" placement="right-start"  @on-click="moreBtnClick">
                      <DropdownItem>
                          {{$t('but_data_export')}}
                          <Icon type="ios-arrow-forward"></Icon>
                      </DropdownItem>
                      <DropdownMenu slot="list"  style="text-align: center;">
                          <DropdownItem name='exportClick("notAll")'>{{$t('but_export_checked')}}</DropdownItem>
                          <DropdownItem name='exportClick("all")'>{{$t('but_export_all')}}</DropdownItem>
                      </DropdownMenu>
                  </Dropdown>
                  <DropdownItem style="text-align: left;" v-if="permissionObj.list" name='shieldByIds()'>清除</DropdownItem>
              </DropdownMenu>
          </Dropdown> -->
        </div>
      </div>
    </div>

    <div class="section-body contentBox_bg">
      <div class="section-body-content">
        <div class="dialTest-tab-content">
          <div class="privateLine-tab-content">
            <Loading :loading="loading"></Loading>
            <Table
              ref="routetableList"
              stripe
              class="fixed-left-right"
              :columns="columns"
              :data="routetableList"
              :no-data-text="
                loading
                  ? ''
                  : routetableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              @on-sort-change="sortChange"
              size="small"
            >
              <template slot-scope="{ row }" slot="faultDesc">
                <div class="fault-phenomenon">
                  <div v-if="row.faultDesc" @click="faultPhenomenonOpen(row)">
                    {{ row.faultDesc }}
                  </div>
                  <div v-else class="empty">——</div>
                </div>
              </template>
            </Table>
          </div>
        </div>
        <div
          class="tab-page"
          style="border-top: 0"
          v-if="routetableList.length > 0"
        >
          <Page
            v-page
            :current.sync="currentNum"
            :page-size="query.pageSize"
            :total="totalCount"
            :page-size-opts="pageSizeOpts"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
            show-elevator
            show-sizer
          >
          </Page>
        </div>
      </div>
    </div>
    <!-- 故障现象详情 -->
    <Modal
      class="detail-modal"
      v-model="faultPhenomenon.open"
      :title="faultPhenomenon.title"
      width="460"
      :mask="true"
      sticky
      draggable
    >
      <div
        v-if="faultPhenomenon.content"
        style="
          text-align: center;
          color: var(--table_content_column_link_color, #05eeff);
          word-wrap: break-word;
          word-break: break-all;
        "
      >
        <p v-for="(item, index) in faultPhenomenon.content" :key="index">
          {{ item }}
        </p>
      </div>
      <div slot="footer">
        <Button
          type="primary"
          size="small"
          @click="faultPhenomenon.open = false"
          >{{ $t("but_confirm") }}</Button
        >
      </div>
    </Modal>
    <!-- 路由波动详情 -->
    <Modal
      v-model="routeFluctuation.open"
      :title="routeFluctuation.title"
      :width="modalWidth"
      :styles="{ top: '120px' }"
      :mask="true"
      sticky
      draggable
    >
      <div v-if="routeFluctuation.content" class="routeFluctuation">
        <div
          v-if="routeFluctuation.content.text"
          style="
            text-align: left;
            word-wrap: break-word;
            word-break: break-word;
          "
        >
          <span>{{ routeFluctuation.content.text }}</span>
          <span v-if="routeFluctuation.content.title">
            （<span class="starting-path">{{
              routeFluctuation.content.title
            }}</span
            >）。
          </span>
        </div>
        <div v-if="routeFluctuation.content.topology">
          <p class="title">{{ $t("alarmlist_r_topology") }}</p>
          <div class="topologys">
            <div
              id="drawing-board"
              class="drawing-board"
              style="height: 600px"
            ></div>
            <!--<span class="no-data-available">'+$t('common_No_data')+'</span>-->
          </div>
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
  </section>
</template>
<script>
import { newJtopo } from "../style/jtopo-editor3.js";
window.onload = function () {
  // const windowHeight = top.document.body.clientHeight;
  // document.getElementById("app").style.setProperty("height", windowHeight - 100 + "px");
  // top.window.onresize = function () {
  //   const windowHeight = top.document.body.clientHeight;
  //   document.getElementById("app").style.setProperty("height", windowHeight - 100 + "px");
  // };
  window.document.onmousedown = function () {
    localStorage.setItem("lastTime", new Date().getTime());
  };
};

function getQueryVariable(variable) {
  let src = window.frames.frameElement.getAttribute('src')
  if (src && src.indexOf('?' + variable + '=') > -1) {
    var temp = src.split('?' + variable + '=')[1].replace(/^\s*|\s*$/g, "");
    if (temp.indexOf('&') > -1) {
      temp = temp.substring(0, temp.indexOf('&'));
    }
    return temp;
  }
  if (src && src.indexOf('&' + variable + '=') > -1) {
    var temp = src.split('&' + variable + '=')[1].replace(/^\s*|\s*$/g, "");
    if (temp.indexOf('&') > -1) {
      temp = temp.substring(0, temp.indexOf('&'));
    }
    return temp;
  }
}

import moment from "moment";
import "@/config/page.js";
import global from "../../../common/global.js";
import "../../../common/jtopo/jtopo-s2.js";
import locationreload from "@/common/locationReload";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
const synth = window.speechSynthesis;
const msg = new SpeechSynthesisUtterance();
export default {
  name: "routeUndulate",
  components: {
    TreeSelect,
  },
  props: {
    tabData: {
      type: String,
      default: "",
    },
  },
  watch: {
    tabData: {
      handler(value) {
        if (this.routetableList.length === 0 && value === "routeUndulate") {
          this.initWebSocket(); //支持socket的方法
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.findInUseIconManageConfigureVoByType()
    this.anpmVersion = JSON.parse(
      sessionStorage.getItem("accessToken")
    ).anpmVersion;
    this.$nextTick(() => {

      // 说明是弹窗进来的界面，就不跳转了。
      if(getQueryVariable('days')){
            // 如果是跳转 
         
      }else{

        locationreload.loactionReload(this.$route.path.split("/")[1].toLowerCase());
      }
    })
    //第一次进来给操作时间赋值
    this.operationTime = new Date();
    this.initWebSocket(); //支持socket的方法
    this.dataRefreh(); //启动定时器30秒检查一次，判断当前页不是第一页时，如果10分钟没有操作，则要刷新页面返回到第一页
    moment.locale("zh-cn");
    //获取分组下拉
    this.getGroupingSelect();
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.query.startTime = new Date(
      new Date().getTime() - 3600 * 1000 * 24 * 6
    ).format("yyyy-MM-dd 00:00:00");
    this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
    // this.getRouteUndulateList(this.query); //直接查询故障清单列表
    //
    this.getTreeOrg();
    this.getRouteUndulateList(this.query);
    this.recordLog();
  },
  data() {
    return {
            currentSkin: sessionStorage.getItem('dark') || 1,
      modalWidth:0,
      configData:{},
      treeValue: "",
      anpmVersion: "1",
      soundType: false,
      groupingList: [],
      durationShow: false,
      //权限对象
      permissionObj: {},
      websock: null,
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      saveQuery: {
        startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        endTime: this.timeChange(Date.now()),
      },
      faultPhenomenon: {
        open: false,
        title: "",
        content: [],
      },
      routeFluctuation: {
        open: false,
        title: this.$t("server_fluctuation"),
        content: {
          text: "",
          title: "",
          topology: {},
        },
      },
      isSnmp: false,
      dataSource: 0,
      affectedCount: 0,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },
      timeRange: [
        new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
      ],
      timeOptions: {
        shortcuts: [
          {
            text: this.$t("comm_half_hour"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return [
                start.format("yyyy-MM-dd HH:mm:ss"),
                end.format("yyyy-MM-dd HH:mm:ss"),
              ];
            },
          },
          {
            text: this.$t("comm_today"),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [
                new Date().format("yyyy-MM-dd 00:00:00"),
                new Date().format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_yesterday"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_7"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_last_30"),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_curr_month"),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
          {
            text: this.$t("comm_preced_month"),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return [
                start.format("yyyy-MM-dd 00:00:00"),
                end.format("yyyy-MM-dd 23:59:59"),
              ];
            },
          },
        ],
      },
      startTime: new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
        "yyyy-MM-dd 00:00:00"
      ), // new Date(2021, 2, 18),
      endTime: new Date().format2("yyyy-MM-dd 23:59:59"), // new Date(2021, 2, 19),
      currentTime: Date.now(),
      interValCurTime: null,
      //搜索字段
      query: {
        userId: "",
        // tencentIds: "",
        startTime: "",
        endTime: "",
        orgId: "",
        keyword: "",
        fieldName: "", //排序字段名
        orderBy: "", //排序方式
        faultType: "7", //工单类型
        // recoveryed: 0, //故障状态
        dataSource: "", //数据源
        // dealStatus: "", //工单状态
        // isSpecialLine: "", //故障分类
        // opt: "", //运营商
        // keyword: "", //关键字
        groupId: "", //分组
        // delFlag: '0',//0正常，1删除，2清除
        // compareType:"",//1小于等于60,2大于60,3自定义,null全部
        // faultStartTime:"",//故障历时自定义开始时间
        // faultEndTime:"",//故障历时自定义结束时间
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
      },
      returnShow: false,
      // keepState:0,
      //界面操作的时间
      operationTime: "",
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //新建修改Modal参数
      modalParameter: {
        modalTitle: "", //标题(新建/修改)
        show: false, //控制是否显示
        loading: true,
      },
      //新建/修改Modal数据
      modalList: {
        factory: "", //设备厂家
        model: [], //设备型号
        // hierarchy:'',
        remarks: "", //备注
        oidNameCn: "", //名称
        oid: "", //OID
      },
      oidNameList: [], //新建修改名称下拉选项
      //表格数据
      routetableList: [],
      title: {
        faultType: 0,
        errorIps: "",
        reason: "",
      },
      columns: [
        //设置表格展示格式
        {
          //多选
          type: "selection",
          width: 30,
          className: "bgColor",
          align: "center",
          // fixed: 'left',
        },
        {
          title: this.$t("alarm_start_time"),
          key: "faultStartTime",
          align: "left",
          className: "bgColor",
          width: 180,
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.faultStartTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : str
            );
          },
        },
        {
          title: this.$t("alarm_fault_type"),
          key: "faultType",
          align: "left",
          width: 180,
          render: (h, params) => {
            let str = Number(params.row.faultType),
              text = "";
            switch (str) {
              case 7:
                text = this.$t("dash_routing_fluctuation");
                break;
              default:
                text = "--";
                break;
            }
            if(text !== "--") {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'top',
                  content: text,
                }

              },text)

            ])
            }else {
              return h('div',text)
            }
            // let text2 = "";
            // if (text.length > 16) {
            //   text2 = text.slice(0, 15) + "...";
            //   let Arr1 = h("span", text);

            //   return h(
            //     "Tooltip",
            //     {
            //       props: {
            //         placement: "top-start",
            //       },
            //     },
            //     [
            //       text2,
            //       h(
            //         "span",
            //         {
            //           slot: "content",
            //         },
            //         text
            //       ),
            //     ]
            //   );
            // } else {
            //   text2 = text;
            //   return h("span", text2);
            // }
            //  let Arr1 =  h("span", text);

            //   return h(
            //     "div",
            //     {
            //       class: {
            //         "text-ellipsis": true,
            //       },
            //       style: {
            //         "word-break": "keep-all",
            //         "white-space": "nowrap",
            //         "overflow": "hidden",
            //         "text-overflow": "ellipsis",
            //       },
            //       domProps: {
            //         title: text,
            //       },
            //     },
            //     [Arr1]
            //   );
          },
        },
        {
          title: this.$t("alarm_failure_boundary"),
          key: "isSpecialLine",
          align: "left",
          width: 200,
          render: (h, params) => {
            if (params.row.dealStatus == 8 || params.row.faultType == 8) {
              return h("span", "--");
            }
            let str = "";
            if (params.row.isSpecialLine == 0) {
              str = this.$t("comm_enterprise_network");
            } else {
              str = this.$t("dash_Special_line");
            }
            let Arr1 = h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
            // let str2 = "";
            // if (str.length > 14) {
            //   str2 = str.slice(0, 12) + "...";
            //   return h(
            //     "Tooltip",
            //     {
            //       props: {
            //         placement: "top-start",
            //       },
            //     },
            //     [
            //       str2,
            //       h(
            //         "span",
            //         {
            //           slot: "content",
            //         },
            //         str
            //       ),
            //     ]
            //   );
            // } else {
            //   str2 = str;
            //   return h("span", str2);
            // }
            if(str !== "--") {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'top',
                  content: str,
                }

              },str)

            ])
            }else {
              return h('div',str)
            }

            // return h(
            //   "div",
            //   {
            //     class: {
            //       "text-ellipsis": true,
            //     },
            //     style: {
            //       "word-break": "keep-all",
            //       "white-space": "nowrap",
            //       "overflow": "hidden",
            //       "text-overflow": "ellipsis",
            //     },
            //     domProps: {
            //       title: str,
            //     },
            //   },
            //   [Arr1]
            // );
          },
        },
        {
          title: this.$t("comm_org"),
          key: "orgName",
          align: "left",
          width: 130,
          tooltip: true,
        },
        {
          title: this.$t("alarm_symptom"),
          key: "faultDesc",
          slot: "faultDesc",
          align: "left",
          minWidth: 260,
        },
        {
          title: this.$t("comm_operate"),
          width: "110",
          align: "center",
          // fixed: 'right',
          className: "bgColor",
          render: (h, params) => {
            const row = params.row;
            const faultType = row.faultType;
            const array = [];
            array.push(
              h(
                "Tooltip",
                {
                  props: {
                    placement: "left-end",
                     transfer: true
                  },
                },
                [
                  h("span", {
                    class: this.currentSkin == 1 ?"look1-btn":"light-look1-btn",
                     style: {
                          display: this.permissionObj.look
                            ? "inline-block"
                            : "none",
                          marginRight: '0px',
                    },
                    on: {
                      click: () => {
                        if (faultType == 7) {
                          if(this.permissionObj.look){
                             this.routeFluctuationOpen(params.row);
                          }else{
                            console.log("无权限")
                          }
                         
                        }
                      },
                    },
                  }),
                  h("span", { slot: "content" },this.permissionObj.look? this.$t("server_view"):""),
                ]
              )
            );
            return h("div", array);
          },
        },
      ],
      audioType: {
        id: "",
        defValue: "",
        creatorId: "",
      },
    };
  },
      beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    this.modalWidth = document.body.clientWidth * 0.98
    this.query.pageNo = this.currentNum;
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    this.setInterTime = setInterval(this.checkTimeout, 1000);
    // this.initWebSocket();//支持socket的方法
    document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
    top.document.addEventListener("click", (e) => {
      var box = document.getElementById("selectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    });
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
     // 获取图标配置
    async findInUseIconManageConfigureVoByType() {
      // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标
      try {
        const res = await this.$http.post('/iconmanage/findInUseIconManageConfigureVoByType', { type: 2,currentSkin:this.currentSkin })
        if (res.code === 1) {

          this.configData = res.data
         

        }
      } catch (err) { }

    },
    moreBtnClick(val) {
      eval(`this.${val}`);
    },
    dateChange(val, type) {
      if (type == "date") {
        this.timeRange = [
          val[0],
          new Date(val[1]).format("yyyy-MM-dd 23:59:59"),
        ];
      }
    },
    // 故障历时选择tab切换
    faultDurationChoice(index, val) {
      if (this.durationTab != index) {
        if (index == 3) {
          this.durationShow = true;
        } else {
          this.durationShow = false;
        }
        this.durationTab = index;
        this.query.compareType = val;
      }
    },

    // 列表排序
    sortChange(e) {
      if (e.order == "normal") {
        this.query.orderBy = "";
        this.query.fieldName = "";
      } else {
        this.query.orderBy = e.order;
        this.query.fieldName = e.key;
      }
      this.getRouteUndulateList(this.query);
    },
    checkTimeout() {
      this.currentsTime = new Date().getTime(); //更新当前时间
      this.lastTime = localStorage.getItem("lastTime");
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    focusFn() {
      this.getTreeOrg();
    },
    setOrg(item) {
      this.treeValue = item[0].name;
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },

    onClear() {
      this.query.orgId = "";
      this.treeValue = "";
    },
    choicesOrg() {
      this.orgTree = true;
    },
    // 获取分组下拉
    getGroupingSelect() {
      this.$http
        .post("/group/list", { pageNo: 1, pageSize: 10000 })
        .then((res) => {
          this.groupingList = res.data.records;
        });
    },
    queryClick() {
      this.recordLog();
      //点击搜索
      this.query.pageNo = this.currentNum = 1;
      // 去掉关键字首尾空格
      this.query.keyword = this.query.keyword.trim();

      let startVal = moment(
        this.timeRange[0] === "" || this.timeRange[0] == undefined
          ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0))
          : this.timeRange[0],
        "YYYY-MM-DD hh:mm:ss"
      ).valueOf();
      let endVal = moment(
        this.timeRange[1] === "" || this.timeRange[1] == undefined
          ? new Date()
          : this.timeRange[1],
        "YYYY-MM-DD 23:59:59"
      ).valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t("warning_time_not_exceed_62"));
        return;
      }
      this.saveQuery.startTime = this.query.startTime = new Date(
        this.timeRange[0]
      ).format("yyyy-MM-dd HH:mm:ss");
      this.saveQuery.endTime = this.query.endTime = new Date(
        this.timeRange[1]
      ).format("yyyy-MM-dd HH:mm:ss");
      if (this.timeRange[0] == "" || this.timeRange[0] == undefined) {
        this.saveQuery.startTime = this.query.startTime = new Date(
          new Date().getTime() - 3600 * 1000 * 24 * 6
        ).format("yyyy-MM-dd 00:00:00");
      }
      if (this.timeRange[1] == "" || this.timeRange[1] == undefined) {
        this.saveQuery.endTime = this.query.endTime = this.timeChange(
          Date.now()
        );
      }
      this.getRouteUndulateList(this.query);
    },
    restClick() {
      this.saveQuery.startTime = new Date(
        new Date().getTime() - 3600 * 1000 * 24 * 6
      ).format("yyyy-MM-dd 00:00:00");
      this.timeRange = [
        new Date(new Date().getTime() - 3600 * 1000 * 24 * 6).format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().format2("yyyy-MM-dd 23:59:59"),
      ];
      this.saveQuery.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.startTime = new Date(
        new Date().getTime() - 3600 * 1000 * 24 * 6
      ).format("yyyy-MM-dd 00:00:00");
      this.query.endTime = new Date().format2("yyyy-MM-dd 23:59:59");
      this.query.faultType = "7";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.keyword = "";
      this.query.dataSource = "";
      this.query.orgId = "";
      this.query.groupId = "";
      this.treeValue = "";
    },
    getRouteUndulateList(param) {
      let _self = this;
      _self.loading = true;
      _self.$http
        .PostJson("/fault/getAlarmList", param)
        .then((res) => {
          if (res.code === 1) {
            if (res.data) {
              _self.routetableList = res.data.records;
              _self.totalCount = res.data.total || 0;
              //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
              let _that = this;
              setTimeout(function () {
                let objData = _that.$refs.routetableList.$refs.tbody.objData;
                for (let key in objData) {
                  if (_that.selectedIds.has(objData[key].id)) {
                    objData[key]._isChecked = true;
                  }
                }
              }, 0);
            } else {
              _self.routetableList = [];
            }
          } else {
            this.$Message.warning(res.msg);
          }
        })
        .finally(() => {
          _self.loading = false;
        });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getRouteUndulateList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd 23:59:59"
        );
      }
      this.getRouteUndulateList(this.query);
    },
    // 打开故障现象详情
    faultPhenomenonOpen(row) {
      this.routeFluctuationOpen(row);
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        now.toTimeString().substr(0, 8)
      );
    },
    //屏蔽清除
    shieldByIds() {
      var selectedIdsArrary = Array.from(this.selectedIds);
      if (selectedIdsArrary.length == 0) {
        this.$Message.warning(this.$t("warning_select_clear_first"));
        return;
      }
      var param = {
        ids: selectedIdsArrary,
        type: 1,
      };
      top.window.$iviewModal.confirm({
        title: this.$t("comm_tip"),
        content: "<p>" + this.$t("dash_clear") + "</p>",
        onOk: () => {
          this.$http.PostJson("/fault/routeShieldByIds", param).then((res) => {
            if (res && res.code === 1) {
              this.$Message.success(this.$t("dash_clear_successfully"));
              this.query.pageNo = this.currentNum = 1;
              this.selectedIds = new Set();
              this.getRouteUndulateList(this.query);
            } else {
              this.$Message.warning(res.msg);
            }
          });
        },
      });
    },
    /*全选*/
    handleSelectAll(slection) {
      if (slection.length === 0) {
        var data = this.$refs.routetableList.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
        });
      }
    },
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
    },
    //导出
    exportClick(val) {
      this.saveLog();
      let _self = this;
      let fileName = this.$t("server_faults") + ".xlsx";
      // let fileName = '全部故障清单.xlsx';
      // if (val === 'notAll') {
      //     let exportIds = Array.from(this.selectedIds);
      //     if (exportIds.length < 1) {
      //         this.$Message.warning(this.$t('specquality_select'));
      //         return;
      //     }
      //     fileName = this.$t('server_faults') + ".xlsx";
      //     this.query.ids = exportIds;
      //     this.query.idArr = exportIds;
      // }
      this.$Loading.start();
      let param = Object.assign({}, this.query);
      this.$axios({
        url: "/fault/exportFaultXlsx",
        method: "post",
        data: this.query,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // var fileName = "";
            // fileName = "故障清单.xlsx";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          this.selectedIds = new Set();
          this.query.ids = null;
          this.queryClick();
          this.$Loading.finish();
        });
    },
    //获取路由波动topo图画图数据
    getRouteCanvarsList(param) {
      console.log(param);
      this.$http
        .PostJson("/fault/fluctuationsTopo", param)
        .then(({ code, data, msg }) => {
          if (code === 1 && data) {
            let newRoue = param[1].routePath.split(",");
            data.nodeList.map((item, index) => {
              if (item.value == newRoue[0]) {
                item.type = 2;
              }
              if (item.value == newRoue[newRoue.length - 1]) {
                item.type = 3;
              }
              return item;
            });
            let datas = {
              dataList: data,
              // topoType: this.showShuxing,
              contId: "drawing-board",
              msgModal: true,
              isDashBoard: false,
              // showWarning: this.showWarning,
              // ispath: true,
              permissionObj: { exppng: true },
              // thistopo: this.thistopo
            };
            // debugger
            console.log("this.datas",datas);
            let scene = newJtopo(datas, top.window.isdarkSkin,this.configData);
          }
        })
        .finally(() => {
          this.routeFluctuation.open = true;
        });
    },
    routeFluctuationOpen(row) {
      row = Object.assign({}, row);
      const msg = [];
      if (row.sourceIp) {
        msg.push("源：");
        msg.push(row.sourceIp);
      }
      msg.push(row.faultDesc);
      let text = msg.join("");
      if (String(text).includes("初始路径")) {
        this.routeFluctuation.content.title = "起始路径";
        text = text.replace(/(\s|\。)+$/g, "");
      } else {
        this.routeFluctuation.content.title = "";
      }
      this.routeFluctuation.content.text = text;

      var linkId = "";
      if (row.linkIds) {
        var linkIds = row.linkIds.split(",");
        if (linkIds.length > 0) {
          linkId = linkIds[0];
        }
      }

      let params = [
        {
          orgId: row.orgId || "",
          probeIp: "root",
          probeName: "root",
          linkId: linkId,
          routePath: row.errorIps,
        },
        {
          orgId: row.orgId || "",
          probeIp: "root",
          probeName: "root",
          linkId: linkId,
          routePath: row.errorIpsDetail,
        },
      ];
      this.getRouteCanvarsList(params);
    },
    initWebSocket() {
      //初始化weosocket
      let token = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
      const wsuri =
        "wss://" +
        this.$baseUrl.replace(top.window.location.protocol + "//", "") +
        "/faultSocket/" +
        token;
      if ("WebSocket" in window) {
        this.websock && this.websock.close();
        if (!this.websock) {
          this.websock = new WebSocket(wsuri);
          this.websock.onopen = this.websocketonopen;
          this.websock.onmessage = this.websocketonmessage;
          this.websock.onerror = this.websocketonerror;
          this.websock.onclose = this.websocketclose;
        } else {
        }
      } else {
        //不支持websoket时只展示列表
        this.getRouteUndulateList(this.query);
      }
    },
    websocketsend(Data) {
      //数据发送
      if (this.websock.readyState === 0) {
        this.initWebSocket();
      } else {
        this.websock.send(Data);
      }
    },
    websocketclose(e) {
      //关闭
      console.log("socket断开连接", e);
      if (
        this.$route.name == this.$t("alarm_fault_list") &&
        this.tabData === "routeUndulate"
      ) {
        this.getRouteUndulateList(this.query);
      }
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      console.log("socket已建立链接");
      let param = Object.assign({}, this.query);
      param.userId = JSON.parse(sessionStorage.getItem("accessToken")).user.id;
      this.websocketsend(JSON.stringify(param));
    },
    websocketonerror() {
      //连接建立失败重连
      this.initWebSocket();
    },
    websocketonmessage(e) {
      //数据接收
      let redata = JSON.parse(e.data);
      this.loading = false;

      //判断如果当前页数大于1，则不能刷新列表
      let datas = redata.data.records[0];
      if (this.query.pageNo == 1) {
        if (datas || datas != "") {
          // this.query.compareType = "";
          // this.query.faultStartTime = "";
          // this.query.faultEndTime = "";
          this.$http.PostJson("/fault/getAlarmList", this.query).then((res) => {
            if (res.code === 1) {
              if (res.data) {
                this.routetableList = res.data.records;
                this.totalCount = res.data.total || 0;
                //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
                setTimeout(() => {
                  let objData = this.$refs.routetableList.$refs.tbody.objData;
                  for (let key in objData) {
                    if (this.selectedIds.has(objData[key].id)) {
                      objData[key]._isChecked = true;
                    }
                  }
                }, 0);
              } else {
                this.routetableList = [];
              }
            } else {
              this.$Message.warning(res.msg);
            }
          });
        }
      }
    },
    // 定时刷新数
    dataRefreh() {
      if (this.tabData === "routeUndulate") {
        // 计时器为空，操作
        setInterval(() => {
          //如果10分钟没有操作，则要刷新页面返回到第一页
          if (this.query.pageNo != 1) {
            let nowDate = new Date();
            let z = nowDate.getTime() - this.operationTime.getTime();
            if (z > 600000) {
              this.pageChange(1);
            } else {
            }
          }
        }, 30000);
      }
    },
    //点击当前页面触发事件
    bodyClick() {
      let nowDate = new Date();
      this.operationTime = nowDate; //刷新操作时间
    },
    recordLog() {
      this.$http
        .wisdomPost("/audit/queryList", { tab: "路由波动" })
        .then((res) => {});
    },
    saveLog() {
      //直接添加安全审计日志 开始
      this.$http
        .wisdomPost("/audit/saveLog", {
          url: "/fault/exportFaultXlsx",
          funName: "路由波动",
          description: "导出路由波动",
          operationType: 13,
          eventType: 7,
          alarmType: 0,
          logType: 2,
          isDel: 0,
        })
        .then((res) => {});
      //直接添加安全审计日志 结束
    },
  },
  destroyed() {
    // this.websocketclose();
    if (this.websock) {
      this.websock.close();
    }
  },
};
</script>

<style lang='less' scoped>
/deep/ .ivu-table-cell a {
  color: var(--table_content_column_link_color, #05eeff);
}

.urgentTitle {
  text-align: left;
  margin-bottom: 10px;
}

.butBoxStyle {
  padding-bottom: 20px;

  .fault-top-box {
    margin-bottom: 0 !important;
  }

  .tabStyle {
    background: #2d8cf0;
    color: #fff;
    border-color: #2d8cf0;
  }
}

.faultDuration {
  align-items: center;
  min-width: 22%;

  .fault-top-item {
    margin-left: 0 !important;
  }

  .faultCustomize {
    display: inline-block;

    //align-items: center;
    //margin-left:5px;
    .ivu-input-wrapper {
      width: 90px;
      margin: 0 5px;
    }
  }

  ul {
    //display: flex;
    align-items: center;
    display: inline-block;
    list-style: none;
  }

  li {
    display: inline-block;
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    color: var(--font_color, #dcdee2);
    cursor: pointer;
    border: 1px solid var(--border_color, #dcdee2);
    border-right: none;
  }

  li:last-child {
    border-right: 1px solid var(--border_color, #dcdee2);
  }

  .tabStyle {
    background: #2d8cf0;
    color: #fff;
    border-color: #2d8cf0;
  }
}

.ivu-input::-webkit-input-placeholder {
  font-size: 12px !important;
}

.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  background: #f1f6fe !important;
}

.task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
  height: auto !important;
  min-height: 32px;
}

.multipleSelect .ivu-select-input {
  padding: 0 !important;
}

.fault-phenomenon {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  // color: #05eeff;
  color: var(--table_content_column_link_color, #05eeff);
}

.fault-phenomenon > div {
  cursor: pointer;
}

.fault-phenomenon > div.empty {
  color: black;
  cursor: default;
}

.zhibiao {
  background-image: url("../../../assets/wisdom/zhibiao.png") !important;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 22px 22px;
}
</style>
<style scoped lang="less">
.screenCondition {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .fault-top-box {
    width: 22% !important;
    margin-right: 35px;

    label {
      min-width: 70px;
      text-align: right;
    }

    .fault-top-item {
      margin-left: 70px;

      /deep/.ivu-input {
        padding-left: 10px !important;
      }
    }
  }

  .timeBox {
    width: calc(44% + 35px) !important;

    .timeStyle {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .reach {
        text-align: center;
        padding: 0px 45px;
      }
    }

    /deep/.ivu-btn-default {
      display: none;
    }
  }
}

.soundPlay {
  margin-right: 24px;
  cursor: pointer;
}

.action-index-modal {
  /deep/.ivu-modal-body {
    overflow: auto;
    max-height: 550px;
  }
}

.wisdom-fault .wisdom-fault-top .select-box {
  width: 20%;
}

.wisdom-fault .wisdom-fault-top .keyword-box {
  width: 25%;
}

/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: default;
  color: #515a6e;
}

/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}

.daoChu-btn {
  width: 60px !important;
}

.more-btn {
  width: 100% !important;
  height: 35px !important;
  color: #02b8fd;
  background-color: #061824;
  border: 1px solid transparent !important;
  border-radius: 3px !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(
      to right,
      var(--input_b_color, #ffffff),
      var(--input_b_color, #ffffff)
    ),
    linear-gradient(31deg, #015197 37%, #31f0fe 46%, #015197 65%, #015197 100%) !important;
}
</style>


