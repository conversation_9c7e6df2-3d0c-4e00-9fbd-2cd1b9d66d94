<template>
  <section class="sectionBox urgentModal" style="min-width: 100% !important" @click="bodyClick">
    <div class="section-top" style="background: transparent">

      <Row  class="fn_box">
        <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">{{ $t('alarm_fault_type') }}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <Select v-model="query.faultType" style="width: 100%" clearable filterable :placeholder="$t('comm_please_select')">
              <Option v-for="item in faultList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </div>
        </Col>
        <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">{{ $t('comm_isp') }}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <Select ref="operatorSelect" v-model="query.opt" style="width: 100%" clearable filterable  :placeholder="$t('specinfo_select_carrier')"
              @on-open-change="setQuery($event, 'operatorSelect')" @on-change="optChange">
              <Option v-for="item in operatorList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </div>
        </Col>
        <Col span="6">
        <div class="fn_item" v-if="anpmVersion == '1'">
          <label class="fn_item_label">{{ $t('comm_data_source') }}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <Select v-model="query.dataSource" clearable filterable style="width: 100%" :placeholder="$t('comm_source')">
              <Option value="0">{{ $t('alarm_dial_probe') }}</Option>
              <Option value="1">{{ $t('alarm_relay_probe') }}</Option>
              <!-- <Option value="4">高频探针</Option> -->
            </Select>
          </div>
        </div>
        </Col>
        <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">{{ $t('alarm_order_status') }}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <Select 
             v-model="query.dealStatus" 
             style="width: 100%" clearable filterable :placeholder="$t('comm_please_select')">
              <Option v-for="item in workOrderStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </div>
        </Col>
        <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">{{ $t('comm_org') }}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <TreeSelect 
            v-model="treeValue" 
            ref="TreeSelect" 
            :data='treeData' 
            :placeholder="$t('comm_select_org')"
            :loadData="loadData"
             @onSelectChange="setOrg" 
             @onClear='onClear'
              @onFocus="focusFn"></TreeSelect>
          </div>
        </div>
        </Col>
        <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">{{$t('alarm_failure_boundary')}}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <Select v-model="query.isSpecialLine" style="width: 100%" clearable filterable :placeholder="$t('comm_please_select')">
              <Option v-for="item in bussList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </div>
        </div>
        </Col>
        <Col span="6">
        <div class="fn_item">
          <label class="fn_item_label">{{ $t('alarm_failure_boundary') }}{{ $t('comm_colon') }}</label>
          <div class="fn_item_box">
            <Input v-model.trim="query.keyword" :title="$t('alarm_fault_suspected')" :placeholder="$t('alarm_fault_suspected')" style="width: 100%" />
          </div>
        </div>
        </Col>
      </Row>
      <div class="fn_tool">
        <div style="display: flex; align-items: center; justify-content: flex-end">
          <Button class="query-btn" type="primary" icon="ios-search" @click="queryClick"
            :title="$t('common_query')"></Button>
          <Button type="primary" class="skinPrimary reset-btn" icon="md-refresh" @click="restClick" :title="this.$t('but_reset')"
            style="width:60px"></Button>
        </div>
      </div>
    </div>

    <div class="section-body">
      <div class="section-body-content">
        <div class="dialTest-tab-content">
          <div class="privateLine-tab-content">
            <!-- <h2 class="urgentTitle">紧急通知关联告警列表</h2> -->
            <Loading :loading="loading"></Loading>
            <Table ref="tableList" stripe :columns="columns" :data="tableList" :no-data-text="loading
              ? ''
              : tableList.length > 0
                ? ''
                : currentSkin == 1 ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>':'<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
              " @on-sort-change="sortChange">
              <template slot-scope="{row}" slot="orgName">
                <Tooltip :content="row.orgName" placement="top-start">
                  <div style="text-overflow: ellipsis;overflow: hidden;width:130px;white-space: nowrap">
                    {{ row.orgName === undefined || row.orgName === null || row.orgName === "" ? "--" : row.orgName }}
                  </div>
                </Tooltip>
              </template>
              <template slot-scope="{ row }" slot="faultDesc">
                <div class="fault-phenomenon">
                  <div v-if="row.faultDesc" @click="
                    faultPhenomenonOpen(
                      row,
                      row.dealStatus == 7 || row.faultType == 7
                    )
                    ">
                    {{ row.faultDesc }}
                  </div>
                  <div v-else class="empty">——</div>
                </div>
              </template>
            </Table>
          </div>
        </div>
        <div class="tab-page" style="border-top: 0" v-if="tableList.length > 0">
          <Page v-page :current.sync="currentNum" :page-size="query.pageSize" :total="totalCount"
            :page-size-opts="pageSizeOpts" :prev-text="$t('common_previous')" :next-text="$t('common_next_page')"
            @on-change="pageChange" @on-page-size-change="pageSizeChange" show-elevator show-sizer>
          </Page>
        </div>
      </div>
    </div>
    <!-- 故障现象详情 -->
    <Modal class="detail-modal" v-model="faultPhenomenon.open" :title="faultPhenomenon.title" width="360" :mask="true" sticky draggable>
      <div v-if="faultPhenomenon.content" style="
          text-align: center;
          color: var(--table_content_column_link_color, #05EEFF);
          word-wrap: break-word;
          word-break: break-all;
        ">
        <p v-for="(item, index) in faultPhenomenon.content" :key="index">
          {{ item }}
        </p>
      </div>
      <div slot="footer">
        <Button type="primary" size="small" @click="faultPhenomenon.open = false">{{$t('but_confirm')}}</Button>
      </div>
    </Modal>
    <!--追加、处理-->
    <Modal v-model="action.show" width="1280" class="action-modal" :title="$t('comm_work')" :mask="true" sticky draggable
      :loading="action.loading" @on-ok="taskOk('actionForm')" @on-cancel="taskCancel('actionForm')">
      <action-item ref="actionForm" :time="saveQuery" :data="action.data" :titles="'(' +
        $t('alarmlist_fault_type') +
        title.faultType +
        $t('alarmlist_sfl') +
        title.errorIps +
        $t('alarmlist_cof') +
        title.reason +
        ')'
        "></action-item>
    </Modal>

    <!--指标-->
    <Modal v-model="indexHome.show" width="1280" class="action-index-modal" :mask="true" sticky draggable
      :footer-hide="true" @on-cancel="indexHomeCancel('indexEvent')">
      <div slot="header">
        <span class="title-name">{{ $t('alarm_fault_index') }}</span>
        <span class="title-content">
          {{ '(' + this.$t('dash_fault_type') + title.faultType + ','+this.$t('dash_link_faulty')+':' + title.errorIps + ',故障原因:' + title.reason + ')' }}
        </span>
      </div>
      <index-item v-show="!isSnmp" :dataSource="dataSource" ref="indexEvent" :data="indexHome.data"
        :time="saveQuery"></index-item>
    </Modal>
    <Modal v-model="snmpindexHome.show" width="1280" class="action-index-modal" :title="$t('alarm_fault_index')"
      :mask="true" sticky draggable :footer-hide="true" @on-cancel="indexHomeCancel('indexEvent')">
      <snmp-item v-show="isSnmp" :snmp="isSnmp" :dataSource="dataSource" :time="saveQuery" ref="indexEvent1"
        :data="snmpindexHome.data"></snmp-item>
    </Modal>
    <!-- 路由波动详情 -->
    <Modal v-model="routeFluctuation.open" :title="routeFluctuation.title" width="95%" :mask="true" sticky draggable>
      <div v-if="routeFluctuation.content" class="routeFluctuation">
        <div v-if="routeFluctuation.content.text">
          <span>{{ routeFluctuation.content.text }}</span>
          <span v-if="routeFluctuation.content.title">
            （<span class="starting-path">{{
              routeFluctuation.content.title
            }}</span>）。
          </span>
        </div>
        <div v-if="routeFluctuation.content.topology">
          <p class="title">{{ $t('alarm_topology') }}}{{ $t('comm_colon') }}</p>
          <div class="topologys">
            <div id="drawing-board" style="height: 600px"></div>
            <!--<span class="no-data-available">'+$t('common_No_data')+'</span>-->
          </div>
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
  </section>
</template>
<script>
window.onload = function () {
  const windowHeight = top.document.body.clientHeight;
  document
    .getElementById("app")
    .style.setProperty("height", windowHeight - 100 + "px");
  top.window.onresize = function () {
    const windowHeight = top.document.body.clientHeight;
    document
      .getElementById("app")
      .style.setProperty("height", windowHeight - 100 + "px");
  };
  window.document.onmousedown = function () {
    localStorage.setItem("lastTime", new Date().getTime());
  };
};
import moment from "moment";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import "@/config/page.js";
import Qs from "qs";
import global from "../../../common/global.js";
import $ from "../../../common/jtopo/jquery.min.js";
import "../../../common/jtopo/jtopo-s2.js";
import { showJTopoToobar } from "../../../common/jtopo/toolbar.js";
import {
  getJtopoNodeImgByValue,
  rearrangementData,
} from "../style/jtopo-editor.js";
import { mapGetters, mapActions, mapState } from "vuex";
import actionItem from "./actionItem.vue";
import { addDraggable } from "@/common/drag.js";
import indexItem from "./indexItem.vue";
import snmpItem from "./snmpItem.vue";
import ipv6Format from "@/common/ipv6Format";
const synth = window.speechSynthesis;
const msg = new SpeechSynthesisUtterance();
export default {
  name: "index",
  components: {
    actionItem,
    indexItem,
    snmpItem,
    TreeSelect
  },
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  created() {
    this.anpmVersion = JSON.parse(
      sessionStorage.getItem("accessToken")
    ).anpmVersion;
    //第一次进来给操作时间赋值
    this.operationTime = new Date();
    moment.locale("zh-cn");

    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.query.startTime = new Date(
      new Date().format("yyyy-MM-dd 00:00:00")
    ).format("yyyy-MM-dd HH:mm:ss");
    this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
    this.associationList();
  },
  watch: {
    rowData: {
      handler(val) {
        this.restClick();
        this.theFirst = true;
        if (!(JSON.stringify(val) === "{}")) {
          this.getTreeOrg();
          this.associationList(val);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      treeValue: '',
      anpmVersion: "1",
      soundType: false,
      faultStateTab: [this.$t('comm_not_recovered'), this.$t('comm_has_recovered'), this.$t('comm_all')],
      tabActiove: 0,
      //权限对象
      permissionObj: {},
      websock: null,
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      saveQuery: {
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: this.timeChange(Date.now()),
      },
      faultList: [
        { value: 1, label: this.$t('comm_interruption') },
        { value: 2, label: this.$t('comm_delay') },
        { value: 3, label: this.$t('comm_loss_package_simple') },
        // { value: 4, label: this.$t('dash_deterioration') },
        // { value: 7,label: '路由波动'},
      ],
      bussTypeList: [
        { value: 0, label: this.$t('comm_not_recovered') },
        { value: 1, label: this.$t('comm_has_recovered') },
        { value: "", label: this.$t('comm_all') },
      ],
      workOrderStatusList: [
        { value: 1, label: this.$t('comm_untreated') },
        { value: 4, label: this.$t('comm_closed_loop') },
        { value: 2, label: this.$t('comm_process') },
      ],
      bussList: [
        { value: 0, label: this.$t('comm_enterprise_network') },
        { value: 1, label: this.$t('comm_special_line_network') },
        { value: 2, label: this.$t('comm_other') }
      ],
      operatorList: [
        { value: 1, label: this.$t('server_China_Mobile') },
        { value: 2, label: this.$t('server_China_Unicom') },
        { value: 3, label: this.$t('server_China_Telecom') },
        { value: 4, label: this.$t('server_China_Broadcasting') },
        { value: 6, label: this.$t('comm_other') }
      ],
      faultPhenomenon: {
        open: false,
        title: "",
        content: [],
      },
      routeFluctuation: {
        open: false,
        title: this.$t('server_fluctuation'),
        content: {
          text: "",
          title: "",
          topology: {},
        },
      },
      isSnmp: false,
      dataSource: 0,
      affectedCount: 0,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },
      startOptions: {
        shortcuts: [
          {
            text: this.$t('comm_half_hour'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              const end = new Date();
              this.startTime = start;
              this.endTime = end;
              this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
              this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            },
          },
          {
            text: this.$t('comm_today'),
            value() {
              const start = new Date();
              return start;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let start = new Date(date.year, date.month, 1);
              return start;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();

              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return start;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
        ],
        disabledDate: (date) => {
          return date && date.valueOf() > this.currentTime;
        },
      },
      endOptions: {
        shortcuts: [
          {
            text: this.$t('comm_half_hour'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 30 * 60 * 1000);
              const end = new Date();
              this.startTime = start;
              this.endTime = end;
              this.query.endTime = end.format("yyyy-MM-dd HH:mm:ss");
              this.query.startTime = start.format("yyyy-MM-dd HH:mm:ss");
            },
          },
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              return end;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              return end;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
            },
          },
        ],
        disabledDate: (date) => {
          return date && date.valueOf() > this.currentTime;
        },
      },
      startTime: null, // new Date(2021, 2, 18),
      endTime: null, // new Date(2021, 2, 19),
      currentTime: Date.now(),
      interValCurTime: null,
      //搜索字段
      query: {
        // 区分是否是 紧急通知查看详情
        inform: 1,
        userId: "",
        tencentIds: "",
        startTime: "",
        endTime: "",
        orgId: "",
        fieldName: "", //排序字段名
        orderBy: "", //排序方式
        faultType: "0", //工单类型
        recoveryed: 1, //故障状态（1未恢复，0已恢复）
        dataSource: "", //数据源
        dealStatus: "", //工单状态
        isSpecialLine: "", //故障分类
        opt: "", //运营商
        keyword: "", //关键字
        delFlag: "0", //0正常，1删除，2清除
        pageNo: 1, //页数
        pageSize: 10, //每页展示多少数据
      },
      returnShow: false,
      // keepState:0,
      //界面操作的时间
      operationTime: "",
      //表格参数设置
      //loading状态
      loading: false,
      //当前页数
      currentNum: 1,
      //总条数
      totalCount: 0,
      //表格每页多少条数据
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      //多选（为了支持翻页多选，保存的数据）
      selectedIds: new Set(),
      //新建修改Modal参数
      modalParameter: {
        modalTitle: "", //标题(新建/修改)
        show: false, //控制是否显示
        loading: true,
      },
      //新建/修改Modal数据
      modalList: {
        factory: "", //设备厂家
        model: [], //设备型号
        // hierarchy:'',
        remarks: "", //备注
        oidNameCn: "", //名称
        oid: "", //OID
      },
      oidNameList: [], //新建修改名称下拉选项
      //表格数据
      tableList: [],
      action: {
        show: false,
        loading: true,
        type: 1, //1、追加，2、处理
        data: {},
      },
      indexHome: {
        show: false,
        data: {},
      },
      snmpindexHome: {
        show: false,
        data: {},
      },
      title: {
        faultType: 0,
        errorIps: "",
        reason: "",
      },
      columns: [
        {
          title: this.$t('alarm_start_time'),
          key: "faultStartTime",
          align: "left",
          className: "bgColor",
          minWidth: 200,
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.faultStartTime;
            return h(
              "span",
              str === undefined || str === null || str == "null" || str === ""
                ? "--"
                : str
            );
          },
        },
        {
          title: this.$t('alarm_fault_type'),
          key: "faultType",
          align: "left",
          width: 120,
          render: (h, params) => {
            let str = Number(params.row.faultType),
              text = "";
            switch (str) {
              case 1:
                text = this.$t('dash_interrupt');
                break;
              case 2:
                text = this.$t('dash_delay_deterioration');
                break;
              case 3:
                text = this.$t('dash_packet_loss');
                break;
              case 7:
                text = this.$t('dash_routing_fluctuation');
                break;
              case 8:
                text = this.$t('dash_urgent_notice');
                break;
              default:
                text = "--";
                break;
            }
            return h("span", text);
          },
        },
        {
          title: this.$t('alarm_failure_boundary'),
          key: "isSpecialLine",
          align: "left",
          width: 200,
          render: (h, params) => {
            // if (params.row.dealStatus == 8 || params.row.faultType == 8) {
            //   return h("span", "--");
            // }
            let str = "";
            if (params.row.isSpecialLine == 0) {
              str = this.$t('comm_enterprise_network')
            } else {
              str = this.$t('dash_Special_line')
            }
            return h(
              "span",
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: this.$t('comm_operators'),
          key: "operator",
          align: "left",
          width: 100,
          render: (h, params) => {
            // if (params.row.dealStatus == 8 || params.row.faultType == 8) {
            //   return h("span", "--");
            // }
            let str = Number(params.row.operator), text = "--";
            switch (str) {
              case 1:
                text = this.$t('server_China_Mobile');
                break;
              case 2:
                text = this.$t('server_China_Unicom');
                break;
              case 3:
                text = this.$t('server_China_Telecom');
                break;
              case 4:
                text = this.$t('server_China_Broadcasting');
                break;
              case 6:
                text = this.$t('server_China_other');
                break;
              default:
                text = "--";
                break;
            }
            return h("span", text);
          },
        },
        {
          title: this.$t('comm_org'),
          slot: "orgName",
          align: "left",
          width: 130,
          tooltip: true,
        },
        {
          title: this.$t('alarm_susp_failed_link'),
          key: "errorIps",
          width: 200,
          align: "left",
          ellipsis: true,
          render: (h, params) => {
            let str = params.row.errorIps;
            str = str === undefined || str === null || str === "" ? "--" : str;
            // let Arr1 = h(
            //   "span",
            //   str === undefined || str === null || str === "" ? "--" : str
            // );
            let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
            // return h(
            //   "div",
            //   {
            //     class: {
            //       "text-ellipsis": true,
            //     },
            //     style: {
            //       "word-break": "keep-all",
            //       "white-space": "nowrap",
            //       overflow: "hidden",
            //       "text-overflow": "ellipsis",
            //     },
            //     domProps: {
            //       title: str,
            //     },
            //   },
            //   [Arr1]
            // );
          },
        },
        {
          title: this.$t('alarm_symptom'),
          key: "faultDesc",
          slot: "faultDesc",
          align: "left",
          minWidth: 260,
        },
        // {
        //   title: "故障原因",
        //   key: "faultCauseType",
        //   align: "left",
        //   width: 120,
        //   sortable: "custom",
        //   render: (h, params) => {
        //     if (params.row.dealStatus == 8 || params.row.faultType == 8 || params.row.faultType == 7 || params.row.dealStatus == 7) {
        //       return h("span", "--");
        //     }
        //     let str = Number(params.row.faultCauseType),
        //       text = "";
        //     if (str === undefined || str === null || str === "") {
        //       text = "--";
        //     } else if (str == 0) {
        //       text = "未知";
        //     } else if (str == 1) {
        //       text = "停电";
        //     } else if (str == 2) {
        //       text = "割接";
        //     } else if (str == 3) {
        //       text = "拥塞";
        //     } else {
        //       text = "--";
        //     }
        //     return h("span", text);
        //   },
        // },
        {
          title: this.$t('server_affected'),
          key: "linkNum",
          align: "left",
          width: 140,
          sortable: "custom",
          render: (h, params) => {
            let str = params.row.linkNum;
            const idS = params.row.dealStatus != 7 && params.row.faultType != 7;
            return h(
              idS ? "a" : "span",
              {
                class: "action-btn ",
                style: 'color:#05EEFF',
                on: {
                  click: () => {
                    if (idS) {
                      this.actionClick(params.row, "linkNum");
                    }
                  },
                },
              },
              str === undefined || str === null || str === "" ? "--" : str
            );
          },
        },
        {
          title: this.$t('alarm_failure_duration'),
          key: "dealDuration",
          align: "left",
          minWidth: 170,
          sortable: "custom",
          render: (h, params) => {
            // if (params.row.dealStatus == 8 || params.row.faultType == 8) {
            //   return h("span", "--");
            // }
            let str = params.row.dealDuration;
            return h(
              "span",
              str === undefined ||
                str === null ||
                str === "" ||
                params.row.dealStatus == 7 ||
                params.row.faultType == 7
                ? "--"
                : str
            );
          },
        },
        {
          title: this.$t('alarm_failure_status'),
          key: "recoveryed",
          align: "left",
          width: 120,
          render: (h, params) => {
            // if (params.row.dealStatus == 8 || params.row.faultType == 8) {
            //   return h("span", "--");
            // }
            let str = Number(params.row.recoveryed),
              text = "--";
            if (params.row.dealStatus != 7 && params.row.faultType != 7) {
              switch (str) {
                case 1:
                  text = this.$t('common_unrecovered');
                  break;
                case 0:
                  text = this.$t('common_recovered');
                  break;
                default:
                  text = "--";
                  break;
              }
            }
            return h(
              "span",
              {
                class:
                  str === 1 ? "action-red" : str === 0 ? "action-green" : "",
              },
              text
            );
          },
        },
        {
          title: this.$t('alarm_ticket_status'),
          key: "dealStatus",
          align: "left",
          width: 120,
          render: (h, params) => {
            // if (params.row.dealStatus == 8 || params.row.faultType == 8 || params.row.faultType == 7 || params.row.dealStatus == 7) {
            //   return h("span", "--");
            // }
            let str = Number(params.row.dealStatus),
              text = "--";
            switch (str) {
              case 4:
                text = this.$t('server_closed_loop');
                break;
              case 2:
                text = this.$t('server_process');
                break;
              case 1:
                text = this.$t('server_unprocessed');
                break;
              default:
                text = "--";
                break;
            }
            return h(
              "span",
              {
                class:
                  str === 4 ? "" : str === 2 ? "action-green" : "action-red",
              },
              text
            );
          },
        },
        {
          title: this.$t('comm_operate'),
          minWidth: 160,
          align: "left",
          render: (h, params) => {
            const row = params.row;
            const faultType = row.faultType;
            const status = row.dealStatus;
            const array = [];
            let iconClass = "zhibiao";
            let str = params.row.linkNum;
            if (str === undefined || str === null || str === "") {
              iconClass = "";
            }
            const idS = params.row.dealStatus != 7 && params.row.faultType != 7;
            array.push(
              h('Tooltip',
                {
                  props: {
                    placement: 'left-end'
                  }
                }, [
                h(
                  "span",
                  {
                    class: "look1-btn",
                    on: {
                      click: () => {
                        if (faultType == 7) {
                          this.routeFluctuationOpen(params.row);
                        } else if (faultType == 8) {
                          this.associationList(params.row);
                        } else {
                          this.actionClick(params.row, "linkNum");
                        }
                      },
                    },
                  },
                ),
                h('span', { slot: 'content', }, this.$t('server_view'))
              ])
            );
            if (faultType == 7 || faultType == 8) {
              array.push(h("a", ""));
            } else {
              if (status == 4) {
                array.push(

                  h('Tooltip',
                    {
                      props: {
                        placement: 'left-end'
                      }
                    }, [
                    h(
                      "span",
                      {
                        class: "create1-btn",
                        style: {
                          display: this.permissionObj.process
                            ? "inline-block"
                            : "none",
                        },
                        on: {
                          click: () => {
                            this.actionClick(params.row, "append")
                          },
                        },
                      },
                    ),
                    h('span', { slot: 'content', }, this.$t('comm_add_on'))
                  ])
                );
              } else if (status == 1 || status == 2) {
                array.push(

                  h('Tooltip',
                    {
                      props: {
                        placement: 'left-end'
                      }
                    }, [
                    h(
                      "span",
                      {
                        class: "handle1-btn",
                        style: {
                          display: this.permissionObj.process
                            ? "inline-block"
                            : "none",
                        },
                        on: {
                          click: () => {
                            this.actionClick(params.row, "handle")
                          },
                        },
                      },
                    ),
                    h('span', { slot: 'content', }, this.$t('comm_deal'))
                  ])
                );
              }
            }
            return h("div", array);
          },
        },
      ],
      audioType: {
        id: "",
        defValue: "",
        creatorId: "",
      },
      //是否第一次进入
      theFirst: true,
    };
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    this.query.pageNo = this.currentNum;
    top.document.addEventListener("click", (e) => {
      var box = top.document.getElementById("associationselectBox");
      if (box && box.contains(e.target)) {
      } else {
        this.orgTree = false;
      }
    })
  },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 列表排序
    sortChange(e) {
      if (e.order == "normal") {
        this.query.orderBy = "";
        this.query.fieldName = "";
      } else {
        this.query.orderBy = e.order;
        this.query.fieldName = e.key;
      }
      this.getList(this.query);
    },

    // 点击故障状态切换获取类型
    getTabState(index, val) {
      if (this.tabActiove != index) {
        this.tabActiove = index;
        this.query.recoveryed = val;
        this.queryClick();
      }
    },
    checkTimeout() {
      this.currentsTime = new Date().getTime(); //更新当前时间
      this.lastTime = localStorage.getItem("lastTime");
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    renderContent(h, { root, node, data }) {
      return h(
        "span",
        {
          style: {
            display: "inline-block",
            width: "100%",
            cursor: "pointer",
          },
          on: {
            click: () => {
              this.clickTree1(data);
            },
          },
        },
        [
          h(
            "Tooltip",
            {
              props: {
                placement: "top-end",
                transfer: true,
              },
            },
            [
              data.name, //控制树形显示的内容
              h(
                "span",
                { slot: "content", style: { whiteSpace: "normal" } },
                data.title //控制Tooltip显示的内容
              ),
            ]
          ),
          h("span", {
            style: {
              display: "inline-block",
              float: "left",
              marginLeft: "32px",
            },
          }),
        ]
      );
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            item.loading = false;
            item.children = [];
            if (index === 0) {
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
       focusFn() {
      this.getTreeOrg()
    }, 
    onClear() {
      this.query.orgId = ''
      this.treeValue = ''
    },
    choicesOrg() {
      this.orgTree = true;
    },
    queryClick() {
      //点击搜索
      this.query.pageNo = this.currentNum = 1;

      let startVal = moment(
        this.startTime === "" || this.startTime == undefined
          ? new Date(new Date(Date.now()).setHours(0, 0, 0, 0))
          : this.startTime,
        "YYYY-MM-DD hh:mm:ss"
      ).valueOf();
      let endVal = moment(
        this.endTime === "" || this.endTime == undefined
          ? new Date()
          : this.endTime,
        "YYYY-MM-DD hh:mm:ss"
      ).valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t('warning_time_not_exceed_62'));
        return;
      }
      this.saveQuery.startTime = this.query.startTime = new Date(
        this.startTime
      ).format("yyyy-MM-dd HH:mm:ss");
      this.saveQuery.endTime = this.query.endTime = new Date(
        this.endTime
      ).format("yyyy-MM-dd HH:mm:ss");
      if (this.startTime == "" || this.startTime == undefined) {
        this.saveQuery.startTime = this.query.startTime = null;
      }
      if (this.endTime == "" || this.endTime == undefined) {
        this.saveQuery.endTime = this.query.endTime = null;
      }
      this.theFirst = false;
      this.getList(this.query);
    },
    restClick() {
      this.startTime = null;
      this.endTime = null;
      this.saveQuery.startTime = null;
      this.saveQuery.endTime = null;
      this.query.startTime = null;
      this.query.endTime = null;
      this.query.faultType = "0";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.keyword = "";
      this.query.dataSource = "";
      this.query.orgId = "";
      this.query.dealStatus = "";
      this.query.isSpecialLine = "";
      this.query.opt = "";
      this.treeValue = "";
    },
    actionClick(row, type) {
      let _self = this;
      _self.indexHome.show = false;
      _self.snmpindexHome.show = false;
      _self.title.faultType =
        row.faultType == 1
          ? this.$t('dash_interrupt')
          : row.faultType == 2
            ? this.$t('dash_delay_deterioration')
            : row.faultType == 3
              ? this.$t('dash_packet_loss')
              : "";
      _self.title.errorIps = row.errorIps;
      _self.title.reason =
        row.faultCauseType == 0
          ? this.$t('comm_unknown')
          : row.faultCauseType == 1
            ? this.$t('comm_Blackout')
            : row.faultCauseType == 2
              ? this.$t('comm_cut')
              : row.faultCauseType == 3
                ? this.$t('comm_congestion')
                : "";
      switch (type) {
        case "linkNum": //影响链路数
          _self.dataSource = row.dataSource;
          _self.affectedCount = Number(row.affectedCount);
          if (row.dataSource == 1) {
            this.isSnmp = true;
            _self.snmpindexHome.data = row;
            _self.snmpindexHome.show = true;
          } else {
            this.isSnmp = false;
            _self.indexHome.data = row;
            _self.indexHome.show = true;
          }
          break;
        case "handle": //处理
          _self.action.type = 2;
          _self.action.data = row;
          _self.action.show = true;
          break;
        case "append": //追加
          _self.action.type = 1;
          _self.action.data = row;
          _self.action.show = true;
          break;
      }
    },
    getList(param) {
      let _self = this;
      _self.loading = true;
      //inform参数是为了区分紧急通知查询时间后端时间至为null的问题
      let params = this.theFirst
        ? Object.assign({}, { inform: 1 }, param)
        : param;
      _self.$http
        .PostJson("/fault/getAlarmList", params)
        .then((res) => {
          if (res.code === 1) {
            if (res.data) {
              _self.tableList = res.data.records;
              _self.totalCount = res.data.total || 0;
              //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
              let _that = this;
              setTimeout(function () {
                let objData = _that.$refs.tableList.$refs.tbody.objData;
                for (let key in objData) {
                  if (_that.selectedIds.has(objData[key].id)) {
                    objData[key]._isChecked = true;
                  }
                }
              }, 0);
            } else {
              _self.tableList = [];
            }
          } else {
            this.$Message.warning(res.msg);
          }
        })
        .finally(() => {
          _self.loading = false;
        });
    },
    pageChange(val) {
      //表格页码切换
      this.currentNum = val;
      this.query.pageNo = this.currentNum;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getList(this.query);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.query.pageNo = 1;
      this.query.pageSize = e;
      if (this.query.startTime != null && this.query.startTime != "") {
        this.query.startTime = new Date(this.query.startTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      if (this.query.endTime != null && this.query.endTime != "") {
        this.query.endTime = new Date(this.query.endTime).format(
          "yyyy-MM-dd HH:mm:ss"
        );
      }
      this.getList(this.query);
    },
    // 打开故障现象详情
    faultPhenomenonOpen(row, sign) {
      if (sign) {
        this.routeFluctuationOpen(row);
      } else {
        this.faultPhenomenon.title =
          row.dealStatus == 8 || row.faultType == 8
            ? this.$t('comm_urgent_notice')
            : this.$t('comm_symptom');
        const list = (this.faultPhenomenon.content = []);
        if (row && row.faultDesc) {
          Array.prototype.push.apply(
            list,
            String(row.faultDesc)
              .split(";")
              .map((text, index) => {
                return text.trim();
              })
          );
        }
        this.faultPhenomenon.open = true;
      }
    },
    timeChange(date) {
      //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
      var now = new Date(date),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        now.toTimeString().substr(0, 8)
      );
    },
    //追加、处理事件
    taskOk(refName) {
      let _self = this,
        param = this.$refs[refName].getParams();
      _self.$http.wisdomPost("/fault/saveProceRecord", param).then((res) => {
        if (res.code === 1) {
          this.$Message.success(
            (_self.action.type === 1 ? this.$t('comm_add_on') : this.$t('comm_deal')) + this.$t('comm_success')
          );
          this.action.loading = false;
          this.action.show = false;
          this.$nextTick(() => {
            this.action.loading = true;
          });
          this.resetQuery();
          this.taskCancel(refName);
        } else {
          this.action.loading = false;
          this.$nextTick(() => {
            this.action.loading = true;
          });
        }
      });
    },
    taskCancel(refName) {
      this.$Modal.visible = false;
      this.$refs[refName].resetRegion();
      this.action.data = {};
    },
    //影响链路数事件
    indexHomeCancel(refName) {
      this.$Modal.visible = false;
      if (!this.isSnmp) {
        this.$refs["indexEvent"].resetRegion();
      } else if (this.isSnmp) {
      }
      this.indexHome.data = {};
      this.snmpindexHome.data = {};
    },
    //时间事件
    startTimeChange(val) {
      if (val == "") {
        this.endTime = "";
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
        this.endOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        var now = new Date(val),
          y = now.getFullYear(),
          m = now.getMonth() + 1,
          h = now.getHours(),
          min = now.getMinutes(),
          s = now.getSeconds(),
          d = now.getDate();
        let ss =
          y +
          "-" +
          (m < 10 ? "0" + m : m) +
          "-" +
          (d < 10 ? "0" + d : d) +
          " " +
          now.toTimeString().substr(0, 8);
        this.endOptions.disabledDate = (date) => {
          let checkedDay = new Date(
            y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let checkedTime = new Date(this.startTime).valueOf();
          let interTime = checkedTime - checkedDay;
          let startTime = this.startTime
            ? new Date(this.startTime).valueOf()
            : "";
          let endTime = val
            ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000
            : "";
          // let inter = new Date(val) - new Date(y+'-'+m+'-'+d+' 00:00:00').getTime()
          return (
            (date && date.valueOf() < startTime - interTime) ||
            (date &&
              date.valueOf() >
              (endTime > this.currentTime ? this.currentTime : endTime))
          );
        };
        let hourArr = [];
        let hourMax = 0;
        for (let hour = 0; hour < h; hour++) {
          hourArr.push(hour);
          hourMax = hour;
        }
        this.savetimePickerOptions.disabledHours =
          this.timePickerOptions.disabledHours = hourArr;
        this.handleSendTimeMin(val, hourMax);
      }
    },
    handleSendTimeMin(val, hourMax) {
      let checkHour = new Date(val).getHours();
      let checkMinute = new Date(val).getMinutes();
      let checkSeconds = new Date(val).getSeconds();
      if (checkHour <= hourMax + 1) {
        let minArr = [];
        for (let min = 0; min < checkMinute; min++) {
          minArr.push(min);
        }
        this.savetimePickerOptions.disabledMinutes =
          this.timePickerOptions.disabledMinutes = minArr;
      } else {
        this.savetimePickerOptions.disabledMinutes =
          this.timePickerOptions.disabledMinutes = [];
      }
      this.handleSecondTimeMin(val, checkMinute);
    },
    handleSecondTimeMin(val, MinuteMax) {
      let checkMinute = new Date(val).getMinutes();
      let checkSeconds = new Date(val).getSeconds();
      if (checkMinute <= MinuteMax + 1) {
        let minArr = [];
        for (let min = 0; min < checkSeconds; min++) {
          minArr.push(min);
        }
        this.savetimePickerOptions.disabledSeconds =
          this.timePickerOptions.disabledSeconds = minArr;
        this.timePickerOptions = JSON.parse(
          JSON.stringify(this.timePickerOptions)
        );
        this.savetimePickerOptions = JSON.parse(
          JSON.stringify(this.timePickerOptions)
        );
      } else {
        this.savetimePickerOptions.disabledSeconds =
          this.timePickerOptions.disabledSeconds = [];
      }
    },
    endTimeChange(val) {
      let timePickerOptions = JSON.parse(
        JSON.stringify(this.savetimePickerOptions)
      );
      if (val == "") {
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        if (this.startTime == "" || this.startTime == undefined) {
          this.endTime = "";
          this.$Message.warning(this.$t('specquality_strat'));
        } else {
          var now = new Date(val),
            y = now.getFullYear(),
            m = now.getMonth() + 1,
            h = now.getHours(),
            min = now.getMinutes(),
            s = now.getSeconds(),
            d = now.getDate();
          let ss =
            y +
            "-" +
            (m < 10 ? "0" + m : m) +
            "-" +
            (d < 10 ? "0" + d : d) +
            " " +
            now.toTimeString().substr(0, 8);
          let checkedDayTimes = new Date(
            y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let nowDayTimes = moment().startOf("day").valueOf();
          if (ss.slice(-8) == "00:00:00") {
            if (checkedDayTimes == nowDayTimes) {
              this.endTime = new Date(this.currentTime).format2(
                "yyyy-MM-dd HH:mm:ss"
              );
            } else {
              this.endTime = new Date(val).format2("yyyy-MM-dd 23:59:59");
            }
          }
          this.startOptions.disabledDate = (date) => {
            let inter =
              new Date(val) -
              new Date(y + "-" + m + "-" + d + " 00:00:00").getTime();
            let startTime = val
              ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000
              : "";
            return (
              (date && date.valueOf() <= startTime) ||
              (date &&
                date.valueOf() >
                (Date.now() > new Date(val) ? new Date(val) : Date.now()))
            );
          };
          let checkedStart = this.startTime.format2("yyyy-MM-dd HH:mm:ss"),
            replaceStart = checkedStart.split(" ")[0] + " 00:00:00";
          let checkedStartTime = new Date(replaceStart).valueOf();
        }
      }
    },
    // 查看关联数据列表
    associationList(row) {
      const time = moment(row.faultStartTime, "YYYY-MM-DD HH:mm:ss");
      let startTime = time.toDate();
      let endTime = time.toDate();
      this.$http
        .PostJson("/defvalue/getCodeTableChildInfos", { parentType: "URGENT_NOTICE" })
        .then(({ data: list }) => {
          let value = 0;
          let unit = null;
          if (
            list.some((item) => {
              if (
                String(item.key).includes("CYCLE") &&
                /^\d+$/.test(item.value)
              ) {
                value = Number.parseInt(item.value);
                unit =
                  {
                    s: "seconds",
                    m: "minutes",
                    h: "hours",
                    d: "days",
                  }[String(item.unit).toLowerCase()] || "minutes";
                return true;
              }
            })
          ) {
            startTime = time.subtract(value, unit).toDate();
          }
        })
        .finally(() => {
          this.resetting(() => {
            this.returnShow = true;
            this.tabActiove = 2;
            this.query.faultType = "0"; // row.fault_type;
            this.query.recoveryed = ""; // row.report_state;
            this.query.dataSource = ""; // 3
            this.query.dealStatus = ""; // row.deal_status;
            this.query.orgId = "";
            this.query.isSpecialLine = ""; // buss_type;
            this.query.tencentIds = row.tencentIds;
            this.query.opt = ""; // row.opt;
            return true;
          });
        });
    },
    // 重置
    resetting(callback) {
      this.query.keyword = "";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.opt = "";
      this.query.tencentIds = "";
      this.query.faultType = "0";
      this.query.recoveryed = "";
      this.query.dataSource = "";
      this.query.dealStatus = "";
      this.query.orgId = "";
      this.query.isSpecialLine = "";
      Object.assign(this.query, this.defaultQuery);
      if (typeof callback === "function") {
        if (callback()) {
          this.getList(this.query);
        }
      } else {
        this.resetQuery();
      }
    },
    setQuery(flag, e) {
      if (flag) {
        this.$refs[e].query = "";
      }
    },
    optChange(val) {
      if (val === 0) {
        this.query.opt = "";
      } else {
        this.query.opt = val;
      }
    },
    //清除查询条件
    resetQuery() {
      this.query.startTime = null;
      this.query.endTime = null;
      this.query.keyword = "";
      this.query.fieldName = "";
      this.query.orderBy = "";
      this.query.dealStatus = "";
      this.query.faultType = "0";
      this.query.opt = "";
      this.query.orgId = "";
      this.query.pageNo = 1;
      this.query.pageSize = 10;
      this.getList(this.query);
    },
    //点击当前页面触发事件
    bodyClick() {
      let nowDate = new Date();
      this.operationTime = nowDate; //刷新操作时间
    },
    //导出
    exportClick2() {
      this.$Loading.start();
      let _self = this;
      let param = Object.assign({}, this.query);
      this.$axios({
        url: "/fault/exportFaultXlsx",
        method: "post",
        params: param,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.$t('server_faults') + ".xls");
          } else {
            var fileName = "";
            fileName = this.$t('server_faults') + ".xls";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          this.$Loading.finish();
        });
    },
  },
  destroyed() { },
};
</script>

<style lang='less' scoped>
.urgentTitle {
  text-align: left;
  margin-bottom: 10px;
}

.butBoxStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20px;

  .fault-top-box {
    margin-bottom: 0 !important;
  }

  ul {
    display: flex;
    align-items: center;
    list-style: none;
    border: 1px solid var(--networdFault_type_border_color,#04478e);
  }

  li {
    padding: 0 15px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    // color: #333333;
     color: var(--networdFault_type_border_normal_font_color,#5ca0d5);
       border: 1px solid var(--networdFault_type_border_normal_color,#04478e);
    cursor: pointer;
    // border: 1px solid #dcdee2;
    border-right: none;
  }

  li:last-child {
    border-right: 1px solid #dcdee2;
  }

  .tabStyle {
    // background: #2d8cf0;
    // color: #fff;
    // border-color: #2d8cf0;
     background:var(--search_check_box_background_color,#02b8fd);
    // color: #060d15;
    color:var(--search_check_box_font_color,#060d15);
    border-color: #02b8fd;
  }
}

.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  background: #f1f6fe !important;
}

.task-modal .ivu-modal-body .ivu-form-item.multipleSelect .ivu-select-selection {
  height: auto !important;
  min-height: 32px;
}

.multipleSelect .ivu-select-input {
  padding: 0 !important;
}

.fault-phenomenon {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  color: var(--table_content_column_link_color,#05EEFF);
  cursor: pointer;
}

.fault-phenomenon>div {
  cursor: pointer;
}

.fault-phenomenon>div.empty {
  color: black;
  cursor: default;
}

.routeFluctuation {
  color: var(--table_content_column_link_color,#05EEFF);
  text-align: center;
}

.routeFluctuation .title {
  color: black;
  text-align: left;
  font-weight: bold;
  padding: 10px 0px;
}

.routeFluctuation .topologys {
  position: relative;
  width: 100%;
  min-height: 420px;
  display: block;
  overflow: auto;
  color: black;
  margin: 8px 0 0 0;
  box-sizing: border-box;
  // border: 1px #cdcdc1 solid;
}

.routeFluctuation .topologys .no-data-available {
  position: relative;
  display: inline-block;
  opacity: 0.7;
}

.routeFluctuation .starting-path {
  color: crimson;
}

.zhibiao {
  background-image: url("../../../assets/wisdom/zhibiao.png") !important;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 22px 22px;
}
</style>
<style scoped lang="less">
.screenCondition {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .fault-top-box {
    width: 22% !important;
    margin-right: 35px;

    label {
      min-width: 70px;
      text-align: right;
    }

    .fault-top-item {
      margin-left: 70px;

      /deep/.ivu-input {
        padding-left: 10px !important;
      }
    }
  }

  .timeBox {
    width: calc(44% + 35px) !important;

    .timeStyle {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .reach {
        text-align: center;
        padding: 0px 45px;
      }
    }
  }
}

.soundPlay {
  margin-right: 24px;
  cursor: pointer;
}

.action-index-modal {
  /deep/.ivu-modal-body {
    overflow: auto;
    max-height: 550px;
  }
}

.wisdom-fault .wisdom-fault-top .select-box {
  width: 20%;
}

.wisdom-fault .wisdom-fault-top .keyword-box {
  width: 25%;
}

.orgDiv {
  position: absolute;
  height: 260px;
  overflow: hidden;
  width: 195px;
  padding: 0 10px;
  background: white;
  z-index: 9;
  border-width: 1px;
  border-style: solid;
  border-top: 0;
  border-color: #dcdee2;
  box-shadow: 0 0 6px #dcdee2;
}

.orgDiv>.title {
  background: radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9));
  color: white;
  margin: 0 -10px;
  padding: 0 10px;
}

.orgTreeScroll {
  width: 215px;
  height: calc(100% - 32px);
  overflow-y: scroll;
}

/deep/.noIcon.ivu-select-disabled .ivu-select-selection {
  background-color: #fff;
  cursor: pointer;
  color: #515a6e;
}

/deep/.noIcon .ivu-icon-ios-arrow-down:before {
  content: "";
}
</style>


