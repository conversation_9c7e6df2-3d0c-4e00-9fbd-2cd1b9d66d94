<template>
  <div>
    <section class="sectionBox">
      <!-- 专线线路报表 页面-->
      <div class="section-top">
        <Row class="fn_box">
          <!-- <Col span="4">
            <div class="fn_item" style="width: 320px">
              <label class="fn_item_label"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('comm_please_select')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg"
                  @onClear="onClear"
                  @onFocus="focusFn(1)"
                >
                </TreeSelect>
              </div>
            </div>
          </Col> -->
          <Col span="4">
            <div class="fn_item">
              <label class="fn_item_label" style="min-width: 70px !important;"
                >{{ $t("shieldlist_fgt") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <DatePicker
                  format="yyyy-MM-dd"
                  type="daterange"
                  :options="timeOptionsTwo"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="true"
                  style="width: 300px"
                  :confirm="false"
                  @on-change="dateChange"
                >
                </DatePicker>
              </div>
            </div>
          </Col>
          <!-- <Col span="4">
            <div class="fn_item" style="width: 320px; margin-left: 180px">
              <label class="fn_item_label"
                >{{ $t("comm_status") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.status"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('gether_pl_status')"
                >
                  <Option
                    v-for="item in statusList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col> -->
          <Col span="4" style="width: 380px; margin-left: 240px">
            <div class="fn_item" style="width: 380px">
              <label class="fn_item_label"
                >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Input
                  v-model.trim="query.keyWord"
                  :placeholder="$t('comm_report_name')"
                />
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <Button
              class="jiaHao-btn"
              type="primary"
              v-if="permissionObj.list"
              @click="queryDialList"
              :title="$t('common_query')"
            >
              <i class="iconfont icon-icon-query" />
            </Button>
            <Button
              class="skinPrimary btn-quickly"
              type="primary"
              @click="eidtClick('add')"
              :title="$t('generate_immediately')"
              v-if="permissionObj.add"
            >
              <img
                :src="
                  currentSkin == 1
                    ? require('../../../assets/btn-img/btn-quickly-light.png')
                    : require('../../../assets/btn-img/btn-quickly-dark.png')
                "
                alt=""
              />
            </Button>
            <Button
              class="delete-btn"
              type="primary"
              v-if="permissionObj.delete"
              @click="deleteData(2)"
              :title="$t('common_delete')"
            >
              <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
            </Button>
          </div>
        </div>
      </div>

      <div class="section-body" style="padding: 0">
        <div class="section-body-content">
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              ref="tableList"
              stripe
              :columns="columns"
              :data="tabList"
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              @on-sort-change="sortSum"
              :no-data-text="
                loading
                  ? ''
                  : tabList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
            >
              <template slot-scope="{ row }" slot="status">
                <span v-if="row.status == 3" style="color: #fc432a">{{
                  $t("test_report_build_failure")
                }}</span>
                <span v-else-if="row.status == 1" style="color: #0285bd"
                  >{{ $t("test_report_generating")
                  }}{{ row.estimatedTimeStr }}</span
                >
                <span v-else-if="row.status == 2" style="color: #07c5a3">{{
                  $t("test_report_generated")
                }}</span>
                <span v-else-if="row.status == 0" style="color: #fc432a">{{
                  $t("test_report_noStart")
                }}</span>
                <span v-else>--</span>

                <Tooltip
                  max-width="180"
                  :content="row.failInfo"
                  placement="right"
                >
                  <Icon
                    type="ios-information-circle"
                    v-if="row.failInfo && row.status == 0"
                    style="color: red"
                  />
                </Tooltip>
              </template>
              <!-- 进度条列 -->
              <template slot-scope="{ row }" slot="progressPercentage">
                <div
                  :class="[
                    'progress-box',
                    currentSkin == 0 ? 'progress-box-border' : '',
                  ]"
                >
                  <div
                    :class="[
                      'progress',
                      getActive(row) ? 'progress-animation' : '',
                    ]"
                    :style="getStyle(row)"
                  ></div>
                  <div class="progress-text">{{ formateText(row) }}</div>
                </div>
              </template>
              <!-- /进度条列 -->
            </Table>
          </div>
        </div>
        <div class="tab-page" style="border-top: 0" v-if="tabList.length > 0">
          <Page
            v-page
            :current.sync="query.pageNo"
            :page-size="query.pageSize"
            :total="totalCount"
            :page-size-opts="pageSizeOpts"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
            show-elevator
            show-sizer
          >
          </Page>
        </div>
      </div>

      <!--新建 编辑 弹窗-->
      <Modal
        sticky
        v-model="editShow"
        :title="editTitle"
        draggable
        :mask="true"
        width="700"
        class="eidtModal checked-tag-modal"
      >
        <Form
          ref="editForm"
          :model="editFormData"
          :rules="editFormValidate"
          :label-width="140"
        >
          <Row class="fn_box">
            <Col span="12">
              <FormItem :label="$t('comm_report_name')+ $t('comm_colon')" prop="name">
                <Input
                  v-model="editFormData.name"
                  :placeholder="$t('phytopo_enter_name')"
                  style="width: 450px"
                  maxlength="50"
                ></Input>
              </FormItem>
            </Col>
          </Row>
          <Row class="fn_box">
            <Col span="12">
              <!--  prop="orgId" -->
              <FormItem class="" :label="$t('comm_org')+ $t('comm_colon')" prop="orgId">
                <TreeSelect
                  v-model="treeValue1"
                  ref="TreeSelect"
                  :data="treeData2"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg2"
                  @onClear="onClear2"
                  @onFocus="focusFn(2)"
                  style="width: 450px"
                >
                </TreeSelect>
              </FormItem>
            </Col>
          </Row>

          <FormItem :label="$t('statistical_scope')+ $t('comm_colon')" prop="statisticalScope">
            <Select
              ref="serviceStatusClear"
              v-model="editFormData.groupIds"
              multiple
              filterable
              :max-tag-count="3"
              clearable
              :placeholder="$t('comm_select_group')"
              :filter-method="filterGroup"
              style="width: 450px"
            >
              <Option
                v-for="item in filteredGroupList"
                :value="item.id"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem :label="$t('comm_time_range')+ $t('comm_colon')" prop="timeRange">
            <DatePicker
              format="yyyy-MM-dd"
              type="daterange"
              :options="timeOptions"
              v-model="editFormData.timeRange"
              :editable="false"
              :clearable="false"
              style="width: 450px"
              :confirm="false"
            >
            </DatePicker>
          </FormItem>

          <!-- <FormItem
            :label="$t('customer_name') + $t('comm_colon')"
            prop="customerName"
            style="width: 650px"
          >
            <Input
              v-model="editFormData.customerName"
              :placeholder="$t('please_input_customer_name')"
              maxlength="50"
            ></Input>
          </FormItem>
          <FormItem
            :label="$t('comm_report_provider') + $t('comm_colon')"
            prop="reportProvider"
            style="width: 650px"
          >
            <Input
              v-model="editFormData.reportProvider"
              :placeholder="$t('report_please_provider')"
              maxlength="50"
            ></Input>
          </FormItem> -->
        </Form>
        <div slot="footer">
          <Button
            type="error"
            style="margin-right: 20px; width: 100px"
            @click="editShow = false"
            >{{ $t("common_cancel") }}</Button
          >
          <Button
            class="custom-button"
            @click="submitEditForm"
            :loading="submitLoading"
            style="width: 100px"
            >{{ $t("generate_immediately_short") }}</Button
          >
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
// 质量报告 / 报告生成策略
import global from '@/common/global.js';
import TreeSelect from '@/common/treeSelect/treeSelect.vue';
import validate from '@/common/validate';
import moment from 'moment';
import '@/config/page.js';

import axios from "axios";
export default {
    components: {TreeSelect },
    watch: {
        startTime(val) {
            if (val) {
                this.editFormData.startTime = new Date(val).format('yyyy-MM-dd hh:mm:ss');
            }
        },
        endTime(val) {
            if (val) {
                this.editFormData.endTime = new Date(val).format('yyyy-MM-dd hh:mm:ss');
            }
        }
    },
    data() {
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            submitLoading:false,
            timerInterval: null,
            step: true,
            importModal: {
                show: false,
                loading: true,
                data: {},
            },
            hasClick: false,
            groupList: [],
            treeValue: '',
            treeValue1: '',
            //权限对象
            permissionObj: {},
            treeData: [],
            treeData2: [],

            orgLists: [],
            readonly: true,
            statusList: [
                { label: this.$t('test_report_build_failure'), value: 3 },
                { label: this.$t('test_report_generating'), value: 1 },
                { label: this.$t('test_report_generated'), value: 2 },
                { label: this.$t('test_report_noStart'), value: 0 },
            ],
            loading: false,
            tabList: [],
            query: {
                type:3,
                orgId: '',
                status: '',
                keyWord: '',
                pageNo: 1,
                pageSize: 10,
                fieldName: "",
                orderBy: "",
            },
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'left',
                    fixed:'left'
                },
                {
                    title: this.$t('comm_report_name'),
                    key: 'name',
                    align: 'left',
                    minWidth: 140,
                    className: 'bgColor',
                    render: (h, params) => {
                          let str = params.row.name;
                          if (str === undefined || str === null) {
                              str = "--";
                          }
                          if(str !== "--") {
                              return h('div', {
                                  class: 'table-ellipsis',
                                  style: {
                                      position: 'relative',
                                      width: '100%',
                                      textAlign: 'left'
                                  }
                              }, [
                                  h('Tooltip', {
                                      props: {
                                          placement: 'top-start',
                                          transfer: true,
                                          maxWidth: 200,
                                          content: str
                                      },
                                      style: {
                                          display: 'block',
                                          width: '100%'
                                      }
                                  }, [
                                      h('span', {
                                          style: {
                                              display: 'inline-block',
                                              width: '100%',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap'
                                          }
                                      }, str)
                                  ])
                              ]);
                          }else {
                            return h('div',str)
                          }
                      },
                },
                {
                    title: this.$t('comm_org'),
                    key: 'orgName',
                    align: 'left',
                    minWidth: 120,
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.orgName;
                        if (str === undefined || str === null) {
                          str = "--";
                        }
                      if(str !== "--") {
                              return h('div', {
                                  class: 'table-ellipsis',
                                  style: {
                                      position: 'relative',
                                      width: '100%',
                                      textAlign: 'left'
                                  }
                              }, [
                                  h('Tooltip', {
                                      props: {
                                          placement: 'top-start',
                                          transfer: true,
                                          maxWidth: 200,
                                          content: str
                                      },
                                      style: {
                                          display: 'block',
                                          width: '100%'
                                      }
                                  }, [
                                      h('span', {
                                          style: {
                                              display: 'inline-block',
                                              width: '100%',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap'
                                          }
                                      }, str)
                                  ])
                              ]);
                          }else {
                            return h('div',str)
                          }
                      },
                },
                {
                    title: this.$t('statistical_scope'),
                    key: 'groupNames',
                    align: 'left',
                    width: 150,
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.groupNames;
                        if (str === undefined || str === null) {
                          str = "--";
                        }
                      if(str !== "--") {
                              return h('div', {
                                  class: 'table-ellipsis',
                                  style: {
                                      position: 'relative',
                                      width: '100%',
                                      textAlign: 'left'
                                  }
                              }, [
                                  h('Tooltip', {
                                      props: {
                                          placement: 'top-start',
                                          transfer: true,
                                          maxWidth: 200,
                                          content: str
                                      },
                                      style: {
                                          display: 'block',
                                          width: '100%'
                                      }
                                  }, [
                                      h('span', {
                                          style: {
                                              display: 'inline-block',
                                              width: '100%',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap'
                                          }
                                      }, str)
                                  ])
                              ]);
                          }else {
                            return h('div',str)
                          }
                      },
                },
                {
                    title: this.$t('comm_time_range'),
                    key: 'timeRange',
                    align: 'left',
                    width: 200,
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.startTime + ' - ' + params.row.endTime;
                        return h('span', str === undefined || str === null || str == 'null' || str === '' ? '--' : str);
                    }
                },
                {
                    title: this.$t('shieldlist_fgt'),
                    key: 'generateTime',
                    align: 'left',
                    width: 160,
                    className: 'bgColor',
                    tooltip: true,
                    sortable: "custom",
                    render: (h, params) => {
                          let str = params.row.generateTime;
                          return h('span', str === undefined || str === null || str == 'null' || str === '' ? '--' : str);
                      }
                },
                {
                    title: this.$t('backup_file_size'),
                    key: 'fileSize',
                    align: 'left',
                    width: 130,
                    className: 'bgColor',
                    sortable: "custom",
                    render: (h, params) => {
                        let str = params.row.fileSize;
                        return h('span', str === undefined || str === null || str == 'null' || str === '' ? '--' : str);
                    }
                },
                // {
                //     title: this.$t('comm_status'),
                //     key: 'status',
                //     width: 130,
                //     align: 'left',
                //     className: 'bgColor',
                //     slot: 'status',
                // },
                { 
                    title: this.$t('comm_status'), 
                    align: "left", 
                    minWidth: 120, 
                    key: "progressPercentage",
                    slot: "progressPercentage",
                  },
                {
                    title: this.$t('common_controls'),
                    width: '120',
                    align: 'center',
                    className: 'bgColor',
                    fixed:'right',
                    render: (h, params) => {
                        let className = params.row.status == '2' ? 'dow-btn' : this.currentSkin == 1 ? 'dow-btn-dis' : 'light-dow-btn-dis'
                        let down = h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'left-end',
                                    transfer: true
                                }
                            },
                            [
                                h('span', {
                                    class: className,
                                    style: {
                                        display: this.permissionObj.download ? 'inline-block' : 'none',
                                        marginRight: '0px',

                                      },
                                    on: {
                                        click: () => {
                                            this.handledownloadFile(params.row.id, params.row.fileName)
                                        }
                                    }
                                }),
                                h('span', { slot: 'content' }, this.$t('wifi_download'))
                            ]
                        );
                        let del = h(
                          'Tooltip',
                          {
                              props: {
                                  placement: 'left-end',
                                  transfer: true,
                              }
                          },
                          [
                              h('span', {
                                  class: 'del1-btn',
                                  style: {
                                      display: this.permissionObj.delete ? 'inline-block' : 'none',
                                      marginRight: '0px',
                                    },
                                  on: {
                                      click: () => {
                                          this.deleteData(1, params.row);
                                      }
                                  }
                              }),
                              h('span', { slot: 'content' }, this.$t('common_delete'))
                          ]
                        );
                        let array = [down, del];
                        //return h('div', array);
                        return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                    }
                }
            ],
            editShow: false,
            editTitle: '',
            selectedIds: new Set(),
            editFormData: {
                name: '',
                startTime: '',
                timeRange: '',
                endTime: '',
                customerName: '',
                reportProvider: '',
                orgId: '',
                groupIds: [],
                type:3
                // treeValue1:'',
            },
            editFormValidate: {
                orgId: [{ required: true, trigger: 'change', type: 'number', message: this.$t('comm_select_org'), }],
                name: [{ required: true, trigger: 'blur', minLength: 1, maxLength: 50, validator: validate.validateStrContainsSpecialChars, name: this.$t('comm_report_name2') }],
                timeRange: [{ required: true, trigger: 'change', validator: validate.validateDate, }],
                statisticalScope: [{ required: true, trigger: 'blur', validator: (rule, value, callback) => {
                  if (!this.editFormData.groupIds || this.editFormData.groupIds.length === 0) {
                    callback(new Error(this.$t('comm_select_group')));
                  } else {
                    if (this.editFormData.groupIds.length > 10) {
                    callback(
                      new Error(this.$t("dashboard_component_real_alarm_group_limit_tip"))
                    );
                    } else {
                        callback();
                    }
                  }
                }}],
                reportProvider: [{ 
                  required: true, 
                  trigger: 'blur', 
                  validator: (rule, value, callback) => {
                    if (!value) {
                      callback(new Error(this.$t('test_report_provider') + this.$t('discover_cannot_be_null')));
                    }else if (value.length > 50) {
                      callback(new Error(this.$t('test_report_provider') + this.$t('comm_length50')));
                    }else {
                      callback();
                    }
                  }
                }],
                customerName: [{ 
                  required: true, 
                  trigger: 'blur', 
                  validator: (rule, value, callback) => {
                    if (!value) {
                      callback(new Error(this.$t('customer_name') + this.$t('discover_cannot_be_null')));
                    }else if(value.length > 50){
                      callback(new Error(this.$t('customer_name') + this.$t('comm_length50')));
                    } else {
                      callback();
                    }
                  }
                }],
            },
            timeOptions: {
                disabledDate(date) {
                    return date && date.valueOf() > Date.now();
                },
                shortcuts: [
                    // {
                    //     text: this.$t('comm_half_hour'),
                    //     value() {
                    //         const start = new Date();
                    //         const end = new Date();
                    //         start.setTime(start.getTime() - 30 * 60 * 1000);
                    //         return [start.format('yyyy-MM-dd HH:mm:ss'), end.format('yyyy-MM-dd HH:mm:ss')];
                    //     }
                    // },
                    {
                          text: this.$t('comm_today'),
                          value() {
                              return [
                                  moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                  moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                              ];
                          }
                      },
                      {
                          text: this.$t('comm_yesterday'),
                          value() {
                              return [
                                  moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                  moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD HH:mm:ss')
                              ];
                          }
                      },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            return [
                                moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            return [
                                moment().subtract(29, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            return [
                                moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            return [
                                moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                                moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    }
                ]
            },

            timeOptionsTwo: {
                shortcuts: [
                {
                          text: this.$t('comm_today'),
                          value() {
                              return [
                                  moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                  moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                              ];
                          }
                      },
                      {
                          text: this.$t('comm_yesterday'),
                          value() {
                              return [
                                  moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                  moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD HH:mm:ss')
                              ];
                          }
                      },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            return [
                                moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            return [
                                moment().subtract(29, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            return [
                                moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            return [
                                moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                                moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD HH:mm:ss')
                            ];
                        }
                    }
                ]
            },

            timeRange: [],
            defaultTimeRange: [
                moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD HH:mm:ss')
            ],
            //  机构类型
            orgLevel: null,
            companyList: [],
            filterGroupValue: '', // 用于存储过滤关键字
            filteredGroupList: [], // 用于存储过滤后的分组列表
             statusRefreshTimer:null,
              isRequest:false,
        };
    },
    created() {
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        this.getDialList();
        this.getTreeOrg(1);
        this.orgLevel = JSON.parse(sessionStorage.getItem('accessToken')).orgLevel;
    },
    mounted() {
        // 一分钟定时刷新
        // this.timerInterval = setInterval(() => {
        //     this.getDialList();
        // }, 1000 * 5);
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
    beforeDestroy() {
        // 清除定时器
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        this.stopStatusRefresh();
        window.removeEventListener('storage', this.handleStorageChange);
    },
    methods: {
      sortSum(column) {
          this.query.fieldName = column.key;
          this.query.orderBy = column.order;
          this.queryDialList();
        },
      startStatusRefresh() {
          if(this.statusRefreshTimer) {
            clearInterval(this.statusRefreshTimer);
          }
          this.statusRefreshTimer = setInterval(() => {
            if(!this.isRequest) {
              this.refreshStatusColumn();

            }
              
          }, 1000 * 5);
        },
        stopStatusRefresh() {
          if (!this.statusRefreshTimer) return;
          clearInterval(this.statusRefreshTimer);
          this.statusRefreshTimer = null;
        },
        refreshStatusColumn() {
          this.isRequest = true;
          let ids = this.tabList.filter(item => item.status == 0 || item.status == 1).map(item => item.id).join(',')
          if(!ids || ids == ''){
                  this.isRequest = false;
                  this.stopStatusRefresh()
                  return

                } 
          this.$http
              .PostJson('/addSnmpAlarm/findProgressBarlist', { ids })
              .then(res => {
                this.isRequest = false;
                  if (res.code === 1) {
                    let data = res.data;
                    this.tabList.forEach(item => {
                      data.forEach(item2 => {
                        if(item.id == item2.id){
                           this.$set(item, 'progressVal', item2.progressVal)
                           this.$set(item, 'status', item2.status)
                           this.$set(item,'fileSize',item2.fileSize)
                           this.$set(item,'generateTime',item2.generateTime)
                           this.$set(item,'fileName',item2.fileName)
                        }
                      })
                      
                    })
                  }
             console.log(this.tabList,'刷新后的数据')
                
                })
                .catch(err => {
                 this.isRequest = false
                });
                    
 
        },  

      getActive(row) {
      let progressVal = row.progressVal || 0; // 默认值为0
      let isActive = false
      let status = row.status||0;
      let progress = 0;
      if(1 == status){
        isActive = true;
        progress = progressVal;
      }
      return isActive
    },
    formateText(row) {
        let progressVal = row.progressVal || 0;
        let status = row.status || 0;
        let text = '';
        let progress = 0;
        
        if(status == 2) {
            text = '100%';
        } else {
            progress = progressVal;
            text = progress + '%';
            
            // 为不同状态添加状态文本
            if(status == 0) {
                text = text;
            } else if(status == 1) {
                text = text; 
            } else if(status == 3) {
                text = this.$t('test_report_build_failure');
            }
        }
        return text;
    },
    getStyle(row) {
      let obj = {
        backgroundColor: 'red',
        width:'50%',
      }
      let status = row.status || 0; // 默认值为0
      let progress = 0;
      let progressVal = row.progressVal || 0; // 默认值为0
      if(0 == status){
        progress = progressVal;
        obj.backgroundColor = '#2d8cf0'
        obj.width = progress+'%'      
      }else if(1 == status){
        progress = progressVal;    
        obj.backgroundColor = '#2d8cf0'
        obj.width = progress+'%'      
      }else if(2 == status){
        progress = 100;
        obj.backgroundColor = '#19be6b'
        obj.width = '100%'
      }else if(3 == status){
        progress = progressVal;
        obj.backgroundColor = '#FE5C5C'
        obj.width = '100%'      
      }else if(4 == status){
        obj.backgroundColor = '#FE5C5C'
        obj.width = '100%'     
      }
        // let overTime = this.$t('net_data_discover_overtime');
        // if(progress<100){
        //   obj.backgroundColor = '#2d8cf0'
        //   obj.width = progress+'%'      
        // }else if(progress==100){
        //   obj.backgroundColor = '#19be6b'
        //   obj.width = '100%'
        // }else{
        //   obj.backgroundColor = '#ed4014'
        //   obj.width = '100%'
        // }
         return obj
    },
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        dateChange(val, type) {
            if (val[0] === val[1]) {
                this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")]
            }
        },
        moreBtnClick(val) {
            eval(`this.${val}`);
        },
        getTreeOrg(type) {
            let _self = this;
            _self.$http.PostJson('/org/tree', { orgId: null }).then(res => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item;
                    });
                    if (type == 1) {
                        _self.treeData = treeNodeList;
                      
                    } else {
                        _self.treeData2 = treeNodeList;
                    }
                    let orgId = JSON.parse(sessionStorage.getItem('accessToken')).user.orgId
                    let currOrg = treeNodeList.find(item => item.id == orgId);
                    if(currOrg){
                      this.setOrg2([currOrg]);
                      this.getGroupList(orgId);
                    }
                }
            });
        },
        loadOrgTreeData(item, callback) {
            this.$http.PostJson('/org/tree', { orgId: item.id }).then(res => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item;
                    });
                    callback(childrenOrgList);
                }
            });
        },
        setOrg(item) {
            console.log('=====>',item[0]);
            this.treeValue = item[0].name;
            this.query.orgId = item[0] ? item[0].id : null;
        },
        getGroupList(orgId) {
            const param = {
                orgId: orgId
            };
            this.$http.wisdomPost("/group/groupList", param).then(res => {
                if (res.code === 1) {
                    if (res.data) {
                        this.groupList = res.data;
                        this.filteredGroupList = res.data; // 初始化过滤后的列表
                    }
                }
            });
        },
         focusFn(num) {
            this.getTreeOrg(num)
            }, 
        onClear() {
            this.treeValue = '';
            this.query.orgId = '';
        },
        setOrg2(item) {
            this.treeValue1 = item[0].name;
            this.editFormData.orgId = item[0] ? item[0].id : null;
            // this.$refs.serviceStatusClear.clearSingleSelect()
            this.editFormData.groupIds = '';
            console.log(item);
            if (item[0] != null && item[0].id != null && item[0].id != '') {
                this.getGroupList(item[0].id);
            }
        },
        onClear2() {
            this.treeValue1 = '';
            this.editFormData.orgId = '';
        },
        queryDialList() {
            this.query.startTime = ''
            this.query.endTime = ''
            if (this.timeRange[0]) {
                this.query.startTime = moment(this.timeRange[0]).format("YYYY-MM-DD HH:mm:ss");
            }
            if (this.timeRange[1]) {
                this.query.endTime = moment(this.timeRange[1]).format("YYYY-MM-DD HH:mm:ss");
            }
            let startVal = moment(this.query.startTime, "YYYY-MM-DD hh:mm:ss").valueOf();
            let endVal = moment(this.query.endTime , "YYYY-MM-DD hh:mm:ss").valueOf();
            if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
                this.$Message.warning({
                    content: this.$t("warning_time_not_exceed_62"),
                    background: true,
                });
                return;
            }
            // this.query.startTime = moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
            // this.query.endTime = moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');

            this.query.pageNo = 1;
            this.getDialList();
        },
        getDialList() {
            this.loading = true;
            if(this.query.endTime){
              this.query.endTime = moment(this.query.endTime).format('YYYY-MM-DD 23:59:59');
            }
            this.$http.PostJson('/addSnmpAlarm/list', this.query).then(res => {
                if (res.code === 1) {
                    res.data.records.forEach(item => {
                        item.remarks = item.remarks || '--';
                    });
                    this.tabList = res.data.records;
                     this.startStatusRefresh();
                    this.totalCount = res.data.total;
                    this.loading = false;
                }
            });
        },
        // 新建、修改弹窗
        eidtClick(type, rowData) {
            this.editShow = true;
            this.getTreeOrg(2);
            this.$refs['editForm'].resetFields();
            if (type == 'add') {
                this.editFormData.id = undefined;
                this.treeValue1 = '',
                this.editTitle = this.$t('generate_immediately');
                // 设置默认时间范围为1个月
                this.editFormData.timeRange = [...this.defaultTimeRange];
            } else if (type == 'modify') {
                this.editTitle = this.$t('common_update');
                const { id, name, fileFormat, remarks, provider, startTime, endTime, orgId, groupIds } = rowData;

                this.editFormData = { id, name, fileFormat, remarks, provider, startTime, endTime, orgId, groupIds: !groupIds ? [] : groupIds.split(',').map((item) => Number(item)) };
                console.log(this.editFormData);
                this.editFormData.remarks = this.editFormData.remarks == '--' ? '' : this.editFormData.remarks;
                this.treeValue1 = rowData.orgName;
                this.getGroupList(orgId);
                if (type == 4) {
                    this.timeRange = [startTime, endTime];
                }
            }
        },
        // 新建、修改提交
        submitEditForm() {
            console.log('this.editFormData', this.editFormData);
            const param = { ...this.editFormData };
            if (param.groupIds) {
                param.groupIds = param.groupIds.join(',');
            }
            this.submitLoading = true;
            // param.strFileFormat = param.strFileFormat.join(',');
            const url = this.editTitle === this.$t('common_update') ? '/addSnmpAlarm/update' : '/addSnmpAlarm/insert';
            let startMoment = moment(this.editFormData.timeRange[0]);
            let endMoment = moment(this.editFormData.timeRange[1]);
            let daysDiff = endMoment.diff(startMoment, 'days');                   
            if (daysDiff > 31) {
                this.$Message.warning(this.$t('test_report_limit31_time'));
                this.submitLoading = false;
                return;
            }
            this.$refs['editForm'].validate(valid => {
                if (valid) {
                  // debugger
                  // console.log('this.editFormData.timeRange', this.editFormData.timeRange);
                  //   let startVal = moment(new Date(this.editFormData.timeRange[0]).format("yyyy-MM-dd HH:mm:ss"), "YYYY-MM-DD hh:mm:ss").valueOf();
                  //   let endVal = moment(new Date(this.editFormData.timeRange[1]).format("yyyy-MM-dd HH:mm:ss"), "YYYY-MM-DD 23:59:59").valueOf();
                  //   if ((endVal - startVal) / 1000 / 3600 / 24 > 92) {
                  //       this.$Message.warning(this.$t('test_report_limit_time'));
                  //       return;
                  //   }
                    this.submitLoading = true;
                    param.startTime = moment(this.editFormData.timeRange[0]).format('YYYY-MM-DD 00:00:00');
                    param.endTime = moment(this.editFormData.timeRange[1]).format('YYYY-MM-DD 23:59:59');
                    this.$http
                        .wisdomPost(url, param)
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success(this.editTitle + this.$t('comm_success'));
                                 this.editShow = false;
                            } else {
                                this.$Message.warning(msg);
                                this.submitLoading = false;
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        })
                        .finally(() => {
                            this.submitLoading = false;
                            this.query.pageNo = 1;
                            this.getDialList();
                        });
                } else {
                     this.submitLoading = false;
                }
            });
        },
        // 获取登录账号的默认报告提供方名称
        // getProviderName() {
        //     let _self = this;
        //     _self.$http.wisdomPost('/qualityReportStrategy/getProviderName').then(res => {
        //         if (res.code === 1) {
        //             _self.editFormData.provider = res.msg;
        //         }
        //     });
        // },
        // 删除数据
      deleteData(type, row) {
          let ids;
          const idsArr = Array.from(this.selectedIds);
          if (type == 1) {
              ids = row.id;
          } else if (type == 2) {
              if (idsArr.length < 1) {
                  this.$Message.warning(this.$t('group_select_delete'));
                  return;
              } else if (idsArr.length > 0) {
                  ids = idsArr.join(',');
              }
          }
          top.window.$iviewModal.confirm({
              title: this.$t('common_delete_prompt'),
              content: this.$t('server_sure_delete'),
              onOk: () => {
                  //调用删除接口
                  this.deleteApi(ids);
              }
          });
      },
        // 删除数据接口请求
        deleteApi(ids) {
            this.$http
                .wisdomPost('/addSnmpAlarm/delete', { ids })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.$Message.success(this.$t('common_controls_succ'));
                        this.query.pageNo = 1;
                        this.getDialList();
                        this.selectedIds = new Set();
                    } else {
                        this.$Message.warning(msg);
                    }
                })
                .catch(err => {
                    throw new Error(err);
                });
        },

        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
            if (slection.length === 0) {
                var data = this.$refs.tableList.data;
                data.forEach(item => {
                    if (this.selectedIds.has(item.id)) {
                        this.selectedIds.delete(item.id);
                    }
                });
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                });
            }
        },
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
        },
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
        },
        pageChange(val) {
            //表格页码切换
            this.query.pageNo = val;
            this.getDialList(this.query);
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getDialList(this.query);
        },
        handledownloadFile(id, fileNames) {
            if (!fileNames) {
                this.$Message.warning(this.$t('test_report_prompt_no_down_file'));
                return;
            }
            var fileType = fileNames.split(".")[1];
            this.btnSendTem = true;
            this.$Loading.start();
            
            axios({
                url: "/addSnmpAlarm/download",
                method: "get",
                responseType: "blob",
                params: { id },
                validateStatus: function(status) {
                    return status >= 200 && status < 600; // 允许500错误状态码
                }
            })
            .then((res) => {
                // 检查响应状态码
                if (res.status === 500) {
                    this.$Message.warning(this.$t('test_report_prompt_no_down_file'));
                    return;
                }
                // 构造一个blob对象来处理数据
                const blob = new Blob([res.data], {
                    type: "application/vnd.ms-" + fileType,
                });
                if ("msSaveOrOpenBlob" in navigator) {
                    window.navigator.msSaveOrOpenBlob(blob, fileNames);
                } else {
                    const fileName = fileNames; // 导出文件名
                    // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
                    // IE10以上支持blob但是依然不支持download
                    if ("download" in document.createElement("a")) {
                        // 支持a标签download的浏览器
                        const _res = res.data;
                        let blob = new Blob([_res]);
                        let downloadElement = document.createElement("a");
                        let href = window.URL.createObjectURL(blob); //创建下载的链接
                        downloadElement.href = href;
                        downloadElement.download = fileName; //下载后文件名
                        document.body.appendChild(downloadElement);
                        downloadElement.click(); //点击下载
                        document.body.removeChild(downloadElement); //下载完成移除元素
                        window.URL.revokeObjectURL(href); //释放掉blob对象
                    } else {
                        // 其他浏览器
                        navigator.msSaveBlob(blob, fileName);
                    }
                }
                this.btnSendTem = false;
                this.$Loading.finish();
            })
            .catch((error) => {
                console.log(error);
                this.btnSendTem = false;
                this.$Loading.finish();
                this.$Message.warning(this.$t('test_report_prompt_no_down_file'));
            })
            .finally(() => {
                this.$Loading.finish();
            });
        },
    },
};
</script>
<style lang='less'>
.progress-animation:before {
  content: "";
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 8px;
  animation: progress-active 2s ease-in-out infinite;
  box-sizing: border-box;
}
@keyframes progress-active {
  0% {
    opacity: 0.5;
    width: 0;
  }
  100% {
    opacity: 0;
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
.btn-quickly {
  width: 60px !important;
  height: 38px !important;
  border: 1px solid transparent !important;
  border-radius: 4px;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  // background-image: linear-gradient(to right,  var(--input_b_color,#ffffff),  var(--input_b_color,#ffffff)), linear-gradient(31deg, #015197 0%, #31F0FE 51%, #015197 100%) !important;
  background-image: linear-gradient(
      to right,
      var(--input_b_color, #ffffff),
      var(--input_b_color, #ffffff)
    ),
    linear-gradient(
      31deg,
      var(--reset_export_button_border_1_color, #015197) 0%,
      var(--reset_export_button_border_2_color, #31f0fe) 51%,
      var(--reset_export_button_border_3_color, #015197) 100%
    ) !important;
  font-size: 23px !important;
  // color:#05EEFF !important;
  color: var(--reset_export_del_button_font_color, #05eeff) !important;
}
.show-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url("../../../assets/btn-img/btn-look.png");
  background-repeat: no-repeat;
}

.daoChu-btn {
  width: 60px !important;
}

.custom-button {
  height: 36px !important;
  width: 120px !important;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%) !important;
  border-radius: 4px 4px 4px 4px !important;
  opacity: 1 !important;
  color: #060d15 !important;
  font-weight: normal !important;
  border-color: #57a3f3 !important;
}
.sectionBox .section-top {
  height: 80px;
}
.progress-box-border {
  border: 1px solid #c0c4cc;
}
.progress-box {
  width: 100%;
  height: 16px;
  background-color: var(--speciallinereport_progress_bg_color, #032a4d);
  border-radius: 8px;
  position: relative;
  .progress-text {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 16px;
    color: var(--speciallinereport_progress_font_color, #ffffff);
    text-align: center;
    line-height: 16px;
    font-size: 10px;
    z-index: 1;
  }
  .progress {
    height: 16px;
    border-radius: 8px;
    // color: #fff;
    text-align: center;
    line-height: 16px;
    position: relative;
    z-index: 0;

    // background: pink;
  }
}
.table-ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  padding: 0 8px;

  .ivu-tooltip {
    display: block;
    width: 100%;
  }
}
.sectionBox .section-top .fn_box {
    margin-bottom: 5px !important;
}
</style>
