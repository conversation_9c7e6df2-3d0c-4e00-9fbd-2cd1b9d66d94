<template>
  <div v-if="currentSkin == 1" class="homePage">
    <Menu
      @on-select="groupServe"
      active-name="1"
      width="230px"
      :class="'menu-tables'"
    >
      <MenuGroup :title="$t('special_line_report')">
        <MenuItem name="1" style="width: 220px;">{{ $t("line_monitoring_report") }}</MenuItem>
        <MenuItem name="5" style="width: 220px;">{{ $t("flow_over_limit_list") }}</MenuItem>
        <MenuItem name="2" style="width: 220px;">{{ $t("line_alarm_list") }}</MenuItem>
        <MenuItem name="3" style="width: 220px;">{{ $t("line_assessment_checklist") }}</MenuItem>
        <MenuItem name="4" style="width: 220px;">{{ $t("add_snmp_alarm") }}</MenuItem>
      </MenuGroup>
    </Menu>
    <div ref="fContent" id="fContent" class="menu-content">
      <component :is="currentComponent" :style="{ height: bodyHeight + 'px' }" />
    </div>
  </div>
  <div v-else :class="'tabs-card light-skin'">
    <Tabs
      type="line"
      v-model="activeTab"
      @on-click="handleTabClick"
      :class="'tabs-card-content'"
    >
      <TabPane :label="$t('line_monitoring_report')" name="1">
        <component :is="currentComponent" v-if="activeTab === '1'" />
      </TabPane>
      <TabPane :label="$t('flow_over_limit_list')" name="5">
        <component :is="currentComponent" v-if="activeTab === '5'" />
      </TabPane>
      <TabPane :label="$t('line_alarm_list')" name="2">
        <component :is="currentComponent" v-if="activeTab === '2'" />
      </TabPane>
      <TabPane :label="$t('line_assessment_checklist')" name="3">
        <component :is="currentComponent" v-if="activeTab === '3'" />
      </TabPane>
      <TabPane :label="$t('add_snmp_alarm')" name="4">
        <component :is="currentComponent" v-if="activeTab === '4'" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import global from '@/common/global.js';
import locationreload from '@/common/locationReload';
import lineMonitoringReport from './lineMonitoringReport.vue';
import lineAlarmList from './lineAlarmList.vue';
import lineAssessmentChecklist from './lineAssessmentChecklist.vue';
import addSnmpAlarm from './addSnmpAlarm.vue';
import flowOverLimitList from './flowOverLimitList.vue';

export default {
  name: 'msgserve',
  components: {
    lineMonitoringReport,
    lineAlarmList,
    lineAssessmentChecklist,
    addSnmpAlarm,
    flowOverLimitList,
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      permissionObj: {},
      childData: {},
      currentComponent: 'lineMonitoringReport', // 默认组件
      theme3: 'light',
      menuType: '1',
      bodyHeight: 0,
      activeTab: '1',
    };
  },
  methods: {
    handleResize() {
      this.bodyHeight = document.documentElement.clientHeight || document.body.clientHeight;
    },
    groupServe(name) {
      console.log(name, '----------groupServe-name-------');
      switch (name) {
        case '1':
          this.currentComponent = 'lineMonitoringReport';
          break;
        case '2':
          this.currentComponent = 'lineAlarmList';
          break;
        case '3':
          this.currentComponent = 'lineAssessmentChecklist';
          break;
        case '4':
          this.currentComponent = 'addSnmpAlarm';
          break;
        case '5':
          this.currentComponent = 'flowOverLimitList';
          break;
        default:
          this.currentComponent = 'lineMonitoringReport';
          break;
      }
    },
    setHeight(v) {
      document.getElementById('fContent').style.setProperty('height', v + 'px');
    },
    handleTabClick(tab) {
      this.activeTab = tab;
      this.groupServe(tab);
    },
    // setWidth(v) {
    //   document.getElementById('fContent').style.setProperty('width', v + 'px');
    // },
  },
  created() {
    this.handleResize();
    console.log(this.bodyWidth, '----------bodyHeight-------');
    this.$nextTick(() => {
      locationreload.loactionReload(
        this.$route.path.split('/')[1].toLowerCase()
      );
    });
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    for (var key in this.permissionObj) {
      if (this.permissionObj[key]) {
        if (key == 'mailser') {
          this.groupServe('1');
          break;
        }
        if (key == 'linkser') {
          this.groupServe('2');
          break;
        }
        if (key == 'wechatser') {
          this.groupServe('3');
          break;
        }
        if (key == 'ddser') {
          this.groupServe('4');
          break;
        }
        if (key == 'chatser') {
          this.groupServe('5');
          break;
        }
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
    window.addEventListener('message', (e) => {
      if (e && e.data) {
        if (e.data.type == 'msg') {
          this.proMsg(e.data.msg, e.data.proType);
          return;
        }
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('message', (e) => {});
  },
};
</script>

<style scoped lang="less">
.homePage {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  // height: 100%;
  overflow-y: auto;
  position: relative;
  // padding: 20px;
  background: var(--body_b_color, #f4f6f9);
}

.menu-tables {
  // height: 700px;
  margin-top: 20px;
  // border-radius: 8px;
  //overflow: auto;
}
.menu-tables-light {
  height: 700px;
  margin-top: 20px;
  // border-radius: 8px;
}

.menu-content {
  position: relative;
  width: calc(100% - 230px); // 修改为减去菜单宽度
  background-color: var(--body_b_color, #f4f6f9);
  padding: 20px;
  box-sizing: border-box;
  text-align: left;
  flex: 1; // 添加弹性布局
}

.menu-content,
.menu-content * {
  user-select: none;
}

/deep/ .ivu-menu-vertical .ivu-menu-item-group-title {
  font-family: "Arial Negreta", "Arial";
  padding-left: 0;
  font-weight: 700;
  font-style: normal;
  font-size: 16px;
  text-align: center;
  color: var(--input_font_color, #303748);
  //   border-width: 0 0 2px;
  //   border-style: solid;
  border-color: var(--border_color, #303748);
}
.ivu-menu-light {
  background-color: var(--body_conent_b_color, #061824) !important;
}

/deep/ .ivu-menu-vertical.ivu-menu-light:after {
  width: 0px !important;
}

.ivu-menu-item {
  //   border-width: 0 0 1px;
  //   border-style: solid;
  border-color: var(--default_value_menu_color, #5ca0d5);
  color: var(--input_font_color, #5ca0d5);
  width: 180px;
}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  // background: var(--default_value_menu_select_bg_color,#16436b) !important;
  // color: var(--default_value_menu_select_font_color , #05eeff) !important;
  background: var(--defaultmanager_left_menu_bg_color, #16436b) !important;
  color: var(--defaultmanager_left_menu_font_color, #05eeff) !important;
}

.tabs-card {
  height: 100%;

  .tabs-card-content {
    /deep/ .ivu-tabs {
      padding-top: 20px;
      overflow-y: auto;
      height: calc(100% - 20px);
    }

    /deep/ .ivu-tabs-bar {
      margin-left: 20px;
      margin-right: 20px;
      border: none;
      background: var(--defaultmanager_left_menu_bg_color, #16436b);
      border-radius: 4px;
    }

    /deep/ .ivu-tabs-nav-container {
      font-size: 14px;
      height: 36px;
      line-height: 36px;
      padding: 0 10px;
    }

    /deep/ .ivu-tabs-tab {
      padding: 0 20px;
      margin-right: 8px;
      border: 0;
      color: var(--input_font_color, #5ca0d5);
      
      &:hover {
        color: var(--defaultmanager_left_menu_font_color, #05eeff);
      }
      
      &.ivu-tabs-tab-active {
        color: var(--defaultmanager_left_menu_font_color, #05eeff);
      }
    }

    /deep/ .ivu-tabs-ink-bar {
      background-color: var(--defaultmanager_left_menu_font_color, #05eeff);
    }
  }
}
</style>
