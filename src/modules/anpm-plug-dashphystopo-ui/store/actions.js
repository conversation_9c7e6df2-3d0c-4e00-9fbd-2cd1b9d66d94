const lan = require('../../../common/language')
"use strict";
import { $http } from "@/server/http";

// 获取人员管理
export const getPerson = ({ commit, state }, param) => {
  $http
    .post("user/getUserList", param)
    .then(res => {
      let data = res.data;
      commit("setPerson", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取客户列表
export const getClientList = ({ commit, state }, param) => {
  if (!param) {
    param = {
      pageNo:1,
      pageSize:10000,
      province:'',
      city:'',
      area:'',
      keyword:'',
    }
  }
  $http
    .post("org/list", param)
    .then(res => {
      let data = res.data;
      if (res.code === 1) {
        
        let orgList=[];
        data.records.forEach(element => {
          element.name=element.orgName;
        });
        commit("setClientList", data.records);
      }else{
        throw new Error(lan.getLabel("src.EGI"))
      }
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取客户列表（用于故障管理）
export const getClientList1 = ({ commit, state }, param) => {
  $http
    .post("client/getClientList1", param)
    .then(res => {
      let data = res.data;
      commit("setClientList1", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//策略配置中修改选择目的ip
export const listProbeTargetName = ({ commit, state }, param) => {
  $http
    .post("target/getAllProbeTargetName", param)
    .then(res => {
      let data = res.data;
      commit("setListProbeTargetName", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//策略配置中修改根据选择目的ip选择名称
export const getProbeTargetNameByIP = ({ commit, state }, param) => {
  $http
    .post("target/getProbeTargetNameByIP", param)
    .then(res => {
      let data = res.data;
      commit("setListProbeTargetNameByIP", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 链路类型配置列表
export const getLinkTypeList = ({ commit, state }, param) => {
  $http
    .post("config/listLinkForNameAndClientKey", param)
    .then(res => {
      let data = res.data;
      commit("setLinkTypeList", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路类型列表
export const getLinkTypeListss = ({ commit, state }, param) => {
  $http
    .post("linktype/getLinkTypeList", param)
    .then(res => {
      let data = res.data;
      commit("setLinkTypeListss", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路配置列表
export const getListLinks = ({ commit, state }, param) => {
  $http
    .post("link/listLink", param)
    .then(res => {
      let data = res.data;
      commit("setListLinks", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取告警等级
export const getAramLIst = ({ commit, state }, param) => {
  $http
    .post("alarm/levelList", param)
    .then(res => {
      let data = res.data;
      commit("setAramLIst", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取告警规则列表
export const getAramRoleList = ({ commit, state }, param) => {
  $http
    .post("alarm/list", param)
    .then(res => {
      let data = res.data;
      commit("setAramRoleList", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取公共节点对比分析
export const getPublicNode = ({ commit, state }, param) => {
  $http
    .post("node/nodesList", param)
    .then(res => {
      let data = res.data;
      commit("setPublicNode", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取公共节点对比分析承载链路总数
export const getChengzai = ({ commit, state }, param) => {
  $http
    .post("node/hostLinkList", param)
    .then(res => {
      let data = res.data;
      commit("setChengzai", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取公共节点对比分析中断链路总数
export const getZhongduan = ({ commit, state }, param) => {
  $http
    .post("node/brokenList", param)
    .then(res => {
      let data = res.data;
      commit("setZhongduan", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取公共节点对比分析劣化链路总数

// 获取公共节点对比分析链路树图
export const getlinkMore = ({ commit, state }, param) => {
  $http
    .post("node/nodeIpList", param)
    .then(res => {
      let data = res.data;
      commit("setlinkMore", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取公共节点对比分析 单链路图
export const getLinks = ({ commit, state }, param) => {
  $http
    .post("node/oneRouteList", param)
    .then(res => {
      let data = res.data;
      commit("setLinks", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取实时告警
export const getRealTimeAlarm = ({ commit, state }, param) => {
  $http
    .post("alarmlog/realTimeList", param)
    .then(res => {
      let data = res.data;
      commit("setRealTimeAlarm", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

export const getAlarmDetail = ({ commit, state }, param) => {
  $http
    .post("alarmlog/getRealTime", param)
    .then(res => {
      let data = res.data;
      commit("setAlarmDetail", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取历史告警
export const gethistoryAlarm = ({ commit, state }, param) => {
  $http
    .post("alarmlog/historyAlarmList", param)
    .then(res => {
      let data = res.data;
      console.log(res.data);
      commit("sethistoryAlarm", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路查询列表
export const getLinkSearch = ({ commit, state }, param) => {
  $http
    .post("statlink/listStatLinkDay", param)
    .then(res => {
      let data = res.data;
      commit("setLinkSearch", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//查看链路详情
export const getLinkSearchDetail = ({ commit, state }, param) => {
  $http
    .post("statlink/getDetailStatLinkDay", param)
    .then(res => {
      let data = res.data;
      commit("setLinkSearchDetail", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取链路数据统计
export const getLinkNumber = ({ commit, state }, param) => {
  $http
    .post("statlink/getLinkNumber", param)
    .then(res => {
      let data = res.data;
      commit("setLinkNumber", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 获取中断链路信息
export const getListBrokenEvent = ({ commit, state }, param) => {
  $http
    .post("broken/listBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setListBrokenEvent", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//中断时长分布
export const getBrokenTime = ({ commit, state }, param) => {
  $http
    .post("broken/listTimeForBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenTime", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//中断次数分布
export const getBrokenCount = ({ commit, state }, param) => {
  $http
    .post("broken/listCountForBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenCount", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
//中断链路统计
export const getBrokenAll = ({ commit, state }, param) => {
  $http
    .post("broken/getBrokenEventNumber", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenAll", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
// 获取中断链路详细信息
export const getBrokenEvent = ({ commit, state }, param) => {
  $http
    .post("broken/getBrokenEvent", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenEvent", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getBrokenEventB = ({ commit, state }, param) => {
  $http
    .post("broken/getBrokenEvent2", param)
    .then(res => {
      let data = res.data;
      commit("setBrokenEventB", data);
    })
    .catch(err => {
      //console.log(err)
    });
};

// 劣化链路分析
export const getDeglist = ({ commit, state }, param) => {
  $http
    .post("logdeg/deglist", param)
    .then(res => {
      let data = res.data;
      commit("setDeglist", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getdegDetails = ({ commit, state }, param) => {
  $http
    .post("logdeg/degDetails", param)
    .then(res => {
      let data = res.data;
      commit("setdegDetails", data);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getDevModel = ({ commit, state }, param) => {
  $http
    .post("snmpoid/getDevModel", param)
    .then(res => {
      let data = res.data;
      var resultArr = [];
      resultArr = data.filter(item => {
        return item !== "" && item != undefined;
      });
      commit("setDevModel", resultArr);
    })
    .catch(err => {
      //console.log(err)
    });
};
export const getDialParam =  ({ commit, state }, param) => {
  $http
    .post("/probetask/getTaskFrequency", param)
    .then(res => {
      let data = res.data;
      // 拨测频率
      let dialFrequencyList = []
      // 发包个数
      let packNumList = []
      // 包大小
      let packSizeList = []
      data.forEach(item=>{
        dialFrequencyList.push({
          label:item.lable,
          value:String(item.value),
          id:item.id,
          option:item.option
        })
      })
      commit("setDialFrequencyList", dialFrequencyList);
      commit("setPackNumList", packNumList);
      commit("setPackSizeList", packSizeList);
    })
    .catch(err => {
      //console.log(err)
    });
};
// const getUnit = (value, type) => {
//   let data = value;
//   const transformSecond = [1, 60, 3600, 24 * 3600, 24 * 7 * 3600];
//   switch (type) {
//     case 1:
//       if (data % transformSecond[4] == 0) {
//         if (data / transformSecond[4] != 0) {
//           return [data / transformSecond[4], 5];
//         }
//       } else if (data % transformSecond[3] == 0) {
//         if (data / transformSecond[3] != 0) {
//           return [data / transformSecond[3], 4];
//         }
//       } else if (data % transformSecond[2] == 0) {
//         if (data / transformSecond[2] != 0) {
//           return [data / transformSecond[2], 3];
//         }
//       } else if (data % transformSecond[1] == 0) {
//         if (data / transformSecond[1] != 0) {
//           return [data / transformSecond[1], 2];
//         }
//       } else {
//         return [data / transformSecond[0], 1];
//       }
//       break;
//     case 2:
//       if (data % transformSecond[1] == 0) {
//         if (data / transformSecond[1] != 0) {
//           return [data / transformSecond[1], 2];
//         }
//       } else {
//         return [data / transformSecond[0], 1];
//       }
//       break;
//   }
// };

const getUnit = (value) => {
  switch (value) {
    case 'SECOND':
      return 1;
      break;
    case 'MINUTE':
      return 2;
      break;
    case 'HOUR':
      return 3;
      break;
    case 'DAY':
      return 4;
      break;
    case 'WEEK':
      return 5;
      break;
  }
};

export const getDefaultConfigList = ({ commit, state }, param) => {
  let defaultValue = {};
  //查询普通拨测默认值
  $http.PostJson('/defvalue/getCodeTableChildInfos',{parentType:"ORDINARY_DIAL_TEST"}).then(({ code, data, msg }) => {
    for (let i = 0, len = data.length; i < len; i++) {
      let unit = getUnit(data[i].unit);
      if (data[i].key == "PROBE_PORT") {//探针端口
        defaultValue.dialTestSourcePort = data[i].value;
      }
      if (data[i].key == "UDP_TARGET_PORT") {//UDP目标端口
        defaultValue.dialTestUdpPort = data[i].value;
      }
      if (data[i].key == "TCP_TARGET_PORT") {//TCP目标端口
        defaultValue.dialTestTcpPort = data[i].value;
      }
      if (data[i].key == "DIAL_FREQUENCY") {//拨测频率
        defaultValue.dialTestFrequency = data[i].value;
      }
      if (data[i].key == "DIAL_NUM") {//拨测发包个数
        defaultValue.dialTestNum = data[i].value;
      }
      if (data[i].key == "DIAL_SIZE") {//拨测发包大小
        defaultValue.dialTestSize = data[i].value;
      }
      if (data[i].key == "START_TTL") {//起始TTL
        defaultValue.dialTestStratTTL = data[i].value;
      }
      if (data[i].key == "TRACE_LIMIT_HOPS") {//Trace受限跳数
        defaultValue.dialTestTrace = data[i].value;
      }
      if (data[i].key == "INTERRUPT_GENERATION_THRESHOLD") {//中断生成阈值
        defaultValue.dialTestBe_thr_value = data[i].value;
      }
      if (data[i].key == "DELAY_DEGR_GENERATION_THRESHOLD") {//时延劣化生成阈值
        defaultValue.dialTestDe_thr_value = data[i].value;
      }
      if (data[i].key == "LOSS_DEGR_GENERATION_THRESHOLD") {//丢包劣化生成阈值
        defaultValue.packetLossDegradation = data[i].value;
      }
      if (data[i].key == "LOSS_DEGR_GENERATION_DIAL_CON") {//连续
        defaultValue.packetLossDegradation2 = data[i].value;
      }
      if (data[i].key == "LOSS_DEGR_GENERATION_DIAL_REACH") {//达到
        defaultValue.packetLossDegradation3 = data[i].value;
      }
      if (data[i].key == "ROUTE_FLAG") {//路由波动告警开关
        if(data[i].value=='ENABLE'){
          defaultValue.surgeAlarm ='1';
        }
        if(data[i].value=='DISABLE'){
          defaultValue.surgeAlarm ='0';
        }
      }
    }

    commit("setDefaultConfigList", defaultValue);
  })
  .catch(err => {
    //console.log(err)
  });

  //查询高频拨测默认值
  $http.PostJson('/defvalue/getCodeTableChildInfos',{parentType:"HIGH_FREQUENCY_MONITOR"}).then(({ code, data, msg }) => {
    for (let i = 0, len = data.length; i < len; i++) {
      let unit = getUnit(data[i].unit);
      if (data[i].key == "PROBE_PORT") {//探针端口
        defaultValue.highSourcePort = data[i].value;
      }
      if (data[i].key == "UDP_TARGET_PORT") {//UDP目标端口
        defaultValue.highUdpPort = data[i].value;
      }
      if (data[i].key == "TCP_TARGET_PORT") {//TCP目标端口
        defaultValue.highTcpPort = data[i].value;
      }
      if (data[i].key == "START_TTL") {//起始TTL
        defaultValue.highStartTTL = data[i].value;
      }
      if (data[i].key == "TRACE_LIMIT_HOPS") {//Trace受限跳数
        defaultValue.highRoutePath = data[i].value;
      }
      if (data[i].key == "WINDOW_SIZE") {//窗口大小W
        defaultValue.highWindow = data[i].value;
      }
      if (data[i].key == "LOCATION_COUNT") {//定位次数M
        defaultValue.highSum = data[i].value;
      }
      if (data[i].key == "DETERIOR_ATIONALARM_STATE") {//劣化告警状态
        if(data[i].defaultValue=='ENABLE'){
          defaultValue.highPlFlag ='1';
        }
        if(data[i].defaultValue=='DISABLE'){
          defaultValue.highPlFlag ='0';
        }
      }
      if (data[i].key == "DETERIOR_ATIONALARM") {//劣化告警
          defaultValue.highPacketLossDegradation =data[i].value;
      }
      if (data[i].key == "ROUTE_FLAG") {//路由波动告警开关
        if(data[i].value=='ENABLE'){
          defaultValue.highSurgeAlarm ='1';
        }
        if(data[i].value=='DISABLE'){
          defaultValue.highSurgeAlarm ='0';
        }
      }
    }

    commit("setDefaultConfigList", defaultValue);
  })
  .catch(err => {
    //console.log(err)
  });


  // $http
  //   .post("probetask/getSetingList", param)
  //   .then(res => {
  //     let defaultValue = {};
  //     let data = res.data;
  //     for (let i = 0, len = res.data.length; i < len; i++) {
        // if (data[i].type == 2 && data[i].key == "探针端口") {
        //   defaultValue.dialTestSourcePort = data[i].value;
        // }
        // if (data[i].type == 2 && data[i].key == "UDP目标端口") {
        //   defaultValue.dialTestUdpPort = data[i].value || 53;
        // }
        // if (data[i].type == 2 && data[i].key == "TCP目标端口") {
        //   defaultValue.dialTestTcpPort = data[i].value || 80;
        // }
        // if (data[i].type == 2 && data[i].key == "拨测频率") {
        //   let dataArr = getUnit(data[i].value, 2);
        //   defaultValue.dialTestFrequency = dataArr[0];
        //   defaultValue.dialTestFrequencyUnit = dataArr[1];
        // }
        // if (data[i].type == 2 && data[i].key == "起始TTL") {
        //   defaultValue.dialTestStratTTL = data[i].value;
        // }
        // if (data[i].type == 2 && data[i].key == "Trace受限跳数") {
        //   defaultValue.dialTestTrace = data[i].value;
        // }
        // if (data[i].type == 2 && data[i].key == "告警周期T") {
        //   defaultValue.dialTestAlarmCycle = data[i].value;
        //   defaultValue.dialTestAlarmCycleUnit = data[i].unit || "h";
        // }
        // if (data[i].type == 2 && data[i].key == "告警次数N") {
        //   defaultValue.dialTestAlarmNumber = data[i].value;
        // }
        // if (data[i].type == 2 && data[i].key == "中断生成阈值") {
        //   defaultValue.dialTestBe_thr_value = data[i].value;
        // }
        // if (data[i].type == 2 && data[i].key == "时延劣化生成阈值") {
        //   defaultValue.dialTestDe_thr_value = data[i].value;
        // }
        // if (data[i].type == 2 && data[i].key == "丢包劣化生成阈值") {
        //   defaultValue.packetLossDegradation = data[i].value;
        // }


    //     if (data[i].type == 3 && data[i].key == "探针端口") {
    //       defaultValue.highSourcePort = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "UDP目标端口") {
    //       defaultValue.highUdpPort = data[i].value || 53;
    //     }
    //     if (data[i].type == 3 && data[i].key == "TCP目标端口") {
    //       defaultValue.highTcpPort = data[i].value || 80;
    //     }
    //     if (data[i].type == 3 && data[i].key == "起始TTL") {
    //       defaultValue.highStartTTL = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "Trace受限跳数") {
    //       defaultValue.highRoutePath = data[i].value;
    //     }

       
    //     if (data[i].type == 3 && data[i].key == "窗口大小W") {
    //       defaultValue.highWindow = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "定位次数M") {
    //       defaultValue.highSum = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "丢包劣化生成阈值启用状态") {
    //       defaultValue.highPlFlag = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "丢包劣化生成阈值") {
    //       defaultValue.highPacketLossDegradation = data[i].value;
    //     }

    //     if (data[i].type == 4 && data[i].key == "时延/丢包") {
    //       let dataArr = getUnit(data[i].value, 1);
    //       defaultValue.relayDelayLoss = dataArr[0];
    //       defaultValue.relayDelayLossUnit = dataArr[1];
    //     }
    //     if (data[i].type == 4 && data[i].key == "流量") {
    //       let dataArr = getUnit(data[i].value, 1);
    //       defaultValue.relayFlow = dataArr[0];
    //       defaultValue.relayFlowUnit = dataArr[1];
    //     }
    //     if (data[i].type == 4 && data[i].key == "设备信息") {
    //       let dataArr = getUnit(data[i].value, 1);
    //       defaultValue.relayEqInfor = dataArr[0];
    //       defaultValue.relayEqInforUnit = dataArr[1];
    //     }

    //     if (data[i].type == 4 && data[i].key == "告警周期T") {
    //       defaultValue.relayAlarmCycle = data[i].value;
    //       defaultValue.relayAlarmCycleUnit = data[i].unit || "h";
    //     }
    //     if (data[i].type == 4 && data[i].key == "告警次数N") {
    //       defaultValue.relayAlarmNumber = data[i].value;
    //     }

    //     if (data[i].type == 4 && data[i].key == "中断生成阈值") {
    //       defaultValue.relayBe_thr_value = data[i].value;
    //     }
    //     if (data[i].type == 4 && data[i].key == "时延劣化生成阈值") {
    //       defaultValue.relayDe_thr_value = data[i].value;
    //     }
    //     if (data[i].type == 4 && data[i].key == "丢包劣化生成阈值") {
    //       defaultValue.relayPacketLoss = data[i].value;
    //     }
    //     if (data[i].type == 4 && data[i].key == "拥塞阈值") {
    //       defaultValue.relayJam = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "中断生成阈值") {
    //       defaultValue.highBe_thr_value = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "劣化生成阈值") {
    //       defaultValue.highDe_thr_value = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "中断恢复阈值") {
    //       defaultValue.highRe_be_thr_value = data[i].value;
    //     }
    //     if (data[i].type == 3 && data[i].key == "劣化恢复阈值") {
    //       defaultValue.highRe_de_thr_value = data[i].value;
    //     }

    //     if (data[i].type == 5 && data[i].key == "告警周期T") {
    //       defaultValue.AlarmCycle = data[i].value;
    //       defaultValue.AlarmCycleUnit = data[i].unit || "h";
    //     }
    //     if (data[i].type == 5 && data[i].key == "告警次数N") {
    //       defaultValue.AlarmNumber = data[i].value;
    //     }
    //     if (data[i].type == 1 && data[i].key == "路由波动告警开关") {
    //       defaultValue.surgeAlarm = data[i].value;
    //     }
    //   }
    //   ;
    //   commit("setDefaultConfigList", defaultValue);
    // })
    // .catch(err => {
    //   //console.log(err)
    // });
};
