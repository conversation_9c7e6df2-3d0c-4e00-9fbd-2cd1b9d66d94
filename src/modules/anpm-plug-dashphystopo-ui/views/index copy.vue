<template>
  <section class="" style="position: relative; height: 100%">
    <Loading :loading="loading"></Loading>
    <div style="height: 100%">
      <div v-if="baImg" :class="{'fillempty':currentSkin==1 , 'fillempty2':currentSkin==0}" style="height: 100%">
        <span class="fillemptyTitle">{{ $t("common_No_data") }}</span>
      </div>
      <div class="topologys" style="height: 100%">
        <phytopoVue
          ref="phytopoVue"
          :msgModal="msgModal"
          @combo-click="down"
          @node-dragend="updateCoo"
          :tabNlist="tabNlist"
          :checkBoxSelect="checkBoxSelect"
          @link-click="linkClick"
          @node-click="nodeClick"
          @saveNoteInfo="saveNoteInfo"
        ></phytopoVue>
      </div>
    </div>
  </section>
</template>
<script>
import '@/config/page.js';
import $ from '../../../common/jtopo/jquery.min.js';
import { Stage, Layer, Node, TextNode, Link, CurveLink,PopupMenu } from '../style/jtopo-1.4.0_trial-esm-min.js';
import '@/timechange';
let stage = null;
let layer = null;
let popupMenu = null;
import ipv6Format from "@/common/ipv6Format";
import phytopoVue from '@/common/topoG6/phytopo/phytopo.vue'
export default {
    name: 'index',
    components: {
        DetailModal: () => import('@/common/topoG6/phytopo/DetailModal.vue'),
        phytopoVue
    },
    data() {
        return {
              currentSkin: sessionStorage.getItem('dark') || 1,
            backgroundImage: null, //背景图
            configData: {},
            //刷新时间间隔
            intervalTime: null,
            //刷新参数
            interrefresh: null,
            loading: false,
            baImg: false,
            topoId: 0,
            // 区域数据
            groupeList: [],
            // 外部节点数据
            dataList: [],
            // 连线
            childrenLink: [],
            allNode: {},
            
        };
    },

    created() {
        
        
        const src = window.frames.frameElement.getAttribute('src'),
            name = window.frames.frameElement.getAttribute('name');
        if (parent.window.isEdit) {
            // }
            if (unescape(src).split('=')[1]) {
                this.topoId = JSON.parse(unescape(src).split('=')[1]).topoId;
            }
            this.intervalTime = parent.window.dashphystopo.split('&&topoId=')[0].split('?intervalTime=')[1];
        } else {
            if (window.frames.name && window.frames.name.indexOf('&&topoId=') > -1) {
                this.topoId = window.frames.name.split('&&topoId=')[1].split('&&limitNum=')[0];
            }
            this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
            if (window.frames.frameElement) {
                window.frames.frameElement.contentWindow.close();
            }
        }
    },
    mounted() {
                 // 监听 storage 事件
         window.addEventListener('storage', this.handleStorageChange);
        window.thisVm = this;
        // stage = new Stage('divId');
        // // 隐藏工具条
        // stage.hideToolbar();
        // 执行一次空数据
        // this.init();
        this.$nextTick(() => {
            this.getPath();
        });
        if (this.intervalTime && Number(this.intervalTime)) {
            this.interrefresh = setInterval(() => {
                this.getPath();
            }, this.intervalTime * 1000);
        }
    },
    methods: {
          handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
         // 获取图标配置
    async findInUseIconManageConfigureVoByType() {
      // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标
      try {
        const res = await this.$http.post('/iconmanage/findInUseIconManageConfigureVoByType', { type: 1,currentSkin:this.currentSkin  })
        if (res.code === 1) {

          this.configData = res.data
         

        }
      } catch (err) { }

    },
        getPath() {
            this.backgroundImage = null;
            stage.removeChild(layer);
            let self = this;
            this.loading = true;
            // 先置空在重绘
            this.groupeList = [];
            this.childrenLink = [];
            this.dataList = [];
            this.findInUseIconManageConfigureVoByType()
            let httpRequest = this.$http.PostJson('/physTopoLinks/getPhysTopologyResult', { topoId: this.topoId, topId: this.topoId })
            httpRequest.then(res => {
                    console.log(res);
                    this.backgroundImage = res.data.backgroundImage ? 'data:image/png;base64,' + res.data.backgroundImage : res.data.backgroundImage;
                    if (res.code === 1) {
                        this.getNodeInfo({ topoId: this.topoId, topId: this.topoId })
                        this.groupeList = res.data?.groupeList || [];
                        this.childrenLink = res.data?.childrenLink || [];
                        this.dataList = res.data?.dataList || [];
                        if (this.dataList && this.dataList.length == 0 && this.groupeList.length == 0) {
                            self.baImg = true;
                        } else {
                            self.baImg = false;
                        }
                        self.loading = false;
                        // self.init();
                         let obj = {
                                dataList: this.dataList,
                                groupeList: this.groupeList,
                                childrenLink: this.childrenLink,
                                configData: this.configData,
                                backgroundImage: this.backgroundImage,
                                with:this.jtopoWidth,
                                height:this.canvasH,
                                showWarning: this.showWarning,
                                pathTopologyLocXLocYMinMax:this.pathTopologyLocXLocYMinMax,
                                topoId:this.topoId,
                                topId:this.topoId

                            }
                            this.$nextTick(() => {
                                this.$refs.phytopoVue.initData(obj)

                            })
                    } else {
                        self.loading = false;
                        self.baImg = true;
                    }
                })
                .finally(() => {
                    self.loading = false;
                    if (parent.loading) {
                        parent.loading['dashphystopo'] = false;
                    }
                });
            httpRequest = null
        },
        init() {
            layer = new Layer();
            popupMenu = new PopupMenu(stage);
            stage.addChild(layer);
            // 循环块和节点
            if (this.groupeList && this.groupeList.length > 0) {
                this.groupeList.forEach(r => {
                    let group = this.getGroup(r);
                    r.nodeList.forEach(e => {
                        let groupNode = this.getNode(e);
                        if (!this.allNode.id) {
                            this.allNode[e.id] = groupNode;
                        }
                        group.addChild(groupNode);
                    });
                    layer.addChild(group);
                });
            }
            let num = null;
            if (this.dataList && this.dataList.length > 0 && this.groupeList.length == 0) {
                num = this.dataList.length;
            }
            if (this.dataList && this.dataList.length > 0) {
                // 外部连线
                this.dataList.forEach(item => {
                    let groupNode = this.getNode(item);
                    if (!this.allNode.id) {
                        this.allNode[item.id] = groupNode;
                    }
                    layer.addChild(groupNode);
                });
            }
            if (this.childrenLink && this.childrenLink.length > 0) {
                this.childrenLink.forEach(r => {
                    let backLink = this.getLink(r.devType, this.allNode[r.source], this.allNode[r.target], r.text, r);
                    layer.addChild(backLink);
                });
            }

            // 缩放到画布1：1并居中
            stage.zoomFullStage();
            stage.translateToCenter();
            // 判断节点数少于4
            this.getNum(num);
            // 隐藏工具条
            stage.hideToolbar();
            // 加载时缩小0.8
            stage.zoomOut();
            stage.show();
        },
        // 备注信息节点
        createNoteNode(value,coordinateX,coordinateY){
            let text = this.$t('comm_remarks')+"："+value
            let textNode = new TextNode(text, coordinateX, coordinateY);
            // 节点的尺寸随文本内容自动调整
            textNode.autoSize = true;
            textNode.resizeTo(400, 200);
            textNode.css({
                padding: 15,
                lineHeight: 12,
                border: 'solid 1px #06324D',
                font: 'italic 12px arial',
                color: 'rgba(0, 154, 147,1)'
            });
            layer.addChild(textNode);
        },
        // 获取备注信息
        getNodeInfo(params){
            popupMenu.hide();
            popupMenu.setHtml('');
            this.$http.PostJson('/physTopo/queryNotes', params).then(res => {
                if (res.code === 1) {
                    var coordinateX = 0;
                    var coordinateY = 0;
                    if(res.data){
                      coordinateX = res.data.coordinateX;
                      coordinateY = res.data.coordinateY;   
                    }
                    this.createNoteNode(res.data?.notes??'',coordinateX,coordinateY)
                }else{
                    this.$Message.error(res.msg)
                }
            })
        },
        getNum(num) {
            if (num && num <= 4) {
                for (let index = 0; index < 7; index++) {
                    stage.zoomOut();
                }
            }
        },
        // 封装图块
        getGroup(data) {
            let { locX, locY, width, height, name, colorType } = data;
            let group = new Node('', locX, locY, width, height);
            let colors = '';
            // 中断颜色
      let breakLineColor = this.configData.breakLineColor
      // 正常颜色
      let normalLineColor = this.configData.normalLineColor
      // 裂化颜色
      let degradationLineColor = this.configData.degradationLineColor
            // 中断
            if (colorType == 0) {
                colors = breakLineColor;
            } else if (colorType == 1) {
                // 劣化
                colors = degradationLineColor;
            } else {
                // 正常
                colors = normalLineColor;
            }
            group.css({
                color: '#fff',
                backgroundColor: 'rgb(2 22 37 / 30%)',
                border: `solid 1px ${colors}`
            });
            let txt = new TextNode(name, 10, 10);
            txt.css({
                color: `${colors}`
                // zIndex: 2
            });
            group.addChild(txt);
            return group;
        },
        //封装节点
        getNode(data) {
            let { devName, value, locX, locY, width = 25, height = 25, devType } = data;
            // let { value, locX, locY, width = 25, height = 25 } = data;
             let value2 = ipv6Format.abbreviateIPv6(value)
            let node = new Node(value2 + '\n' + devName, locX, locY, width, height);
            node.textOffsetY = 10;
            let dev0 = devType.split(',')[0];
            let dev1 = devType.split(',')[1];
            // 0：正常，1：中断，2：劣化
            this.configData.inUseIconManageConfigures.forEach(item => {

        if (item.deviceTypeId == dev0) {
          item.types.forEach(item2 => {
            if (item2.type == dev1) {
              console.log(item2.image, 123)
              node.setImage('data:image/png;base64,' + item2.image)

            }
          })
        }

      })
            // if (dev0 == 1 && dev1 == 0) {
            //     node.setImage(require('../../../assets/png/10.png'));
            // } else if (dev0 == 1 && dev1 == 1) {
            //     node.setImage(require('../../../assets/png/11.png'));
            // } else if (dev0 == 1 && dev1 == 2) {
            //     node.setImage(require('../../../assets/png/12.png'));
            // } else if (dev0 == 2 && dev1 == 0) {
            //     node.setImage(require('../../../assets/png/20.png'));
            // } else if (dev0 == 2 && dev1 == 1) {
            //     node.setImage(require('../../../assets/png/21.png'));
            // } else if (dev0 == 2 && dev1 == 2) {
            //     node.setImage(require('../../../assets/png/22.png'));
            // } else if (dev0 == 3 && dev1 == 0) {
            //     node.setImage(require('../../../assets/png/30.png'));
            // } else if (dev0 == 3 && dev1 == 1) {
            //     node.setImage(require('../../../assets/png/31.png'));
            // } else if (dev0 == 3 && dev1 == 2) {
            //     node.setImage(require('../../../assets/png/32.png'));
            // } else if (dev0 == 4 && dev1 == 0) {
            //     node.setImage(require('../../../assets/png/40.png'));
            // } else if (dev0 == 4 && dev1 == 1) {
            //     node.setImage(require('../../../assets/png/41.png'));
            // } else if (dev0 == 4 && dev1 == 2) {
            //     node.setImage(require('../../../assets/png/42.png'));
            // } else if (dev0 == 5 && dev1 == 0) {
            //     node.setImage(require('../../../assets/png/50.png'));
            // } else if (dev0 == 5 && dev1 == 1) {
            //     node.setImage(require('../../../assets/png/51.png'));
            // } else if (dev0 == 5 && dev1 == 2) {
            //     node.setImage(require('../../../assets/png/52.png'));
            // }
            // 节点颜色
            node.css({
                color: '#fff'
            });
            return node;
        },
        // 封装连线
        getLink(devType, node, toNode, text = null, linkInfo = null) {
            let link = new Link(text, node, toNode);
            link.userData = linkInfo;
            let color = '';
            let breakLineColor = this.configData.breakLineColor
      // 正常颜色
      let normalLineColor = this.configData.normalLineColor
      // 裂化颜色
      let degradationLineColor = this.configData.degradationLineColor

            // 连线偏移
            if (linkInfo.sameFlag) {
                if (linkInfo.sameFlag == -1) {
                    link.beginOffset = {
                        x: 0,
                        y: -14
                    };
                    link.endOffset = {
                        x: 0,
                        y: -14
                    };
                } else if (linkInfo.sameFlag == 1) {
                    link.beginOffset = {
                        x: 0,
                        y: 14
                    };
                    link.endOffset = {
                        x: 0,
                        y: 14
                    };
                } else if (linkInfo.sameFlag == -2) {
                    link.beginOffset = {
                        x: 0,
                        y: -7
                    };
                    link.endOffset = {
                        x: 0,
                        y: -7
                    };
                } else if (linkInfo.sameFlag == 2) {
                    link.beginOffset = {
                        x: 0,
                        y: 7
                    };
                    link.endOffset = {
                        x: 0,
                        y: 7
                    };
                }
            }
            if (devType == 0) {
                color = breakLineColor;
            } else if (devType == 1) {
                color = degradationLineColor;
            } else {
                color = normalLineColor;
            }
            const bandwidth = Math.max(linkInfo.bandwidth, linkInfo.bandwidth);
            if (bandwidth >= 1000000000) {
                // 流速大于1000M
                link.css({
                border: `solid 1.3px ${color}`
                });
            }
            else if (bandwidth >= 1000000 && bandwidth < 1000000000) {
                // 流速1M~1000M
                link.css({
                border: `solid 0.7px ${color}`
                });
            } else {
                // 流速小于1M
                link.css('lineDash', [6, 2]); //虚线
                link.css({
                border: `solid 0.7px ${color}`
                });
            };
            if (text) {
                link.label.css({
                    font: 'normal 8px arial',
                    color
                });
            }
            return link;
        }
    },
    beforeDestroy() {
          // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
        if (this.interrefresh) {
            clearInterval(this.interrefresh);
            this.interrefresh = null;
        }
    }
};
</script>
<style>
.bigscreen .topologys {
  /* background-color: rgba(2, 35, 97, 0.15); */
  background: #060d15;
}
.topologys {
  position: relative;
  width: 100%;
  min-height: 420px;
  display: block;
  overflow: auto;
  color: black;
  box-sizing: border-box;
  background: #060d15;
}

.topologys button {
  margin: 10px;
}
</style>
<style scoped lang="less">
#drawing-board {
  background: var(--topo_checkbox_b_color, #fff);
}
.fillempty {
  text-align: center;
  line-height: 40px;
  background-image: url("../../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  // background-size: 290px 200px;
  background-position: center;
  .fillemptyTitle {
    position: absolute;
    display: block;
    width: 100%;
    top: calc(50% + 45px);
    color: #5ca0d5;
  }
}
.bigscreen {
  .fillemptyTitle {
    color: #8fd4ff;
  }
}
.bg-box {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}
</style>