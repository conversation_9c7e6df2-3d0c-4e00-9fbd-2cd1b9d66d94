import Vue from 'vue'
import App from './App.vue'
import "./style/index.less";
import ViewUI from "view-design";
import "view-design/dist/styles/iview.css";
import axios from "axios";
import deplay from "../../server/deploy";
import base from "@/config/base.config.js";
import skin from "../../common/skinchange";
import $http from "../../server/http";
import i18n from '../../language';
import store from './store/index'
Vue.config.productionTip = false;

Vue.use(ViewUI);
Vue.use($http);
Vue.prototype.$axios = axios;
Vue.prototype.$baseUrl = deplay.baseUrl;
Vue.prototype.$base = base;
import router from "./router/index";
import "../../common";
router.beforeEach((to, from, next) => {
  ViewUI.LoadingBar.start();
  if (to.matched[0].meta.authority &&
    !sessionStorage.hasOwnProperty("accessToken")) {
    top.location.href = (window.location.hostname === 'localhost' ? '/anpm-plug-login-ui.html' : '/');
  } else {
    // top.document.title = to.name;
    if (to.path === "/dashphystopo") {
      next()
    }
  }
});
router.afterEach((to, from, next) => {
  ViewUI.LoadingBar.finish();
});
new Vue({
  i18n,
  router,
  render: h => h(App)
}).$mount('#dashphystopo')