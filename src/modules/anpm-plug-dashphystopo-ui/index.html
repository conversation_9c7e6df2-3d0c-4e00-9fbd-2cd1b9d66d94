<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>icon_logo.ico">
  <title></title>
</head>
<body>

<div id="dashphystopo"></div>
<!-- built files will be auto injected -->
<script>
    //二级iframe页面改变颜色函数
    function skinChange(obj) {
        console.log(obj)
        const property = Object.keys(obj);
        const color = Object.keys(obj).map(function (i) {
            return obj[i]
        });
        let root = document.documentElement;
        for (let i = 0; i < property.length; i++) {
            root.style.setProperty(property[i], color[i]);
        }
        window.thisVm.$data.isdarkSkin = top.window.isdarkSkin;
    }
  let iframestr = window.name;
  let isbigscreen = iframestr.search("bigscreen") != -1 ;
  if (isbigscreen) {
    document.body.className = 'bigscreen';
    window.isbigscreen = isbigscreen
  }else{
    window.isbigscreen = false
  }
</script>
</body>
<style>
  html,body{
    height:100%;
    /*background-color: #fff!important;*/
  }
  /*body.bigscreen{*/
    /*background: rgba(1,22,56,.9);*/
  /*}*/
  #dashphystopo{
    height:100%;
  }
  .bigscreen{
    background-color: transparent;
  }
</style>
</html>
