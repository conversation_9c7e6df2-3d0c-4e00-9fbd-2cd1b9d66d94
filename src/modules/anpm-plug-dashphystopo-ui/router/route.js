const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: "",
    meta: {
      authority: true
    },
    redirect:'/dashphystopo',
  },
  {
    path: "/dashphystopo",
    name: lan.getLabel("src.pathTopology"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-dashphystopo-ui/views/index.vue"], resolve)
  },
];
