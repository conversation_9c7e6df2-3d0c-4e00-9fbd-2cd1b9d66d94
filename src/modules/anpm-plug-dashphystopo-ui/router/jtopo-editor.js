/**
 * jtopo工具栏
 */
import $ from "../../../common/jtopo/jquery.min.js";
import "../../../common/jtopo/jtopo-s.js";

import jiedianImgG from "../../../common/jtopo/img/jiedian_green.png";
import tanzhenImg from "../../../common/jtopo/img/tanzhen.png";
import jiedianImgH from "../../../common/jtopo/img/jiedian_grey.png";
import serverInvalidImg  from "../../../common/jtopo/img/suolvtu.png";
//物理图标
import fanghuoqiangG  from "../../../common/jtopo/img/icon_fanghuoqiang_g.png";
import fuwuqiG  from "../../../common/jtopo/img/icon_fuwuqi_g.png";
import jioahuanjiG  from "../../../common/jtopo/img/icon_jiaohuanji_g.png";
import luyouqiG  from "../../../common/jtopo/img/icon_luyouqi_g.png";

import pathChakanW from "../../../common/jtopo/img/path_chakan_w.png";
import pathChakanY from "../../../common/jtopo/img/path_chakan_y.png";
import pathXXW from "../../../common/jtopo/img/path_xx_w.png";
import pathXXY from "../../../common/jtopo/img/path_xx_y.png";
import pathSXW from "../../../common/jtopo/img/path_sx_w.png";
import pathSXY from "../../../common/jtopo/img/path_sx_y.png";
import pathDaochuW from "../../../common/jtopo/img/path_daochu_w.png";
import pathDaochuY from "../../../common/jtopo/img/path_daochu_y.png";
import pathFangdaW from "../../../common/jtopo/img/path_fangda_w.png";
import pathFangdaY from "../../../common/jtopo/img/path_fangda_y.png";
import pathHuanyuanW from "../../../common/jtopo/img/path_huanyuan_w.png";
import pathHuanyuanY from "../../../common/jtopo/img/path_huanyuan_y.png";
import pathSuoxiaoW from "../../../common/jtopo/img/path_suoxiao_w.png";
import pathSuoxiaoY from "../../../common/jtopo/img/path_suoxiao_y.png";
import pathTuliW from "../../../common/jtopo/img/path_tuli_w.png";
import pathTuliY from "../../../common/jtopo/img/path_tuli_y.png";
import pathXxszW from "../../../common/jtopo/img/path_xxsz_w.png";
import pathXxszY from "../../../common/jtopo/img/path_xxsz_y.png";

var foldOpenStatus = {};


var maxy = 100;
var yx= 100;

/**
 * 画布渲染主方法
 * @param dataList 渲染数据
 * @param type xx星形结构  sx树形结构
 * @param divId 创建画布的容器id
 * @param msgModal 需要展示的数据字段s
 * @returns {c}
 */
export function newJtopo(datas){
    // console.log(dataList,"---dataList-");
    // var dataList = data;
    maxy = 100;
    var dataList = datas.dataList;
    var type = datas.topoType;
    var contId = datas.contId;
    var msgModalData = datas.msgModal;
    var isDashBoard = datas.isDashBoard;
    var showWarning = datas.showWarning;
    var ispath = datas.ispath;
    var thistopo = datas.thistopo;
    //点位
    var nodelist = setDataList(dataList.nodeList);
    //线路
    var linklist = dataList.nodeRouteList;
    let d1 = new Date().getTime()
    //初始化容器
    //创建画布对象
    const canvasW = document.body.clientWidth;
    const canvasH = document.body.clientHeight;

    if(!isDashBoard){
        $("#"+contId).html(`<div  style="width:100%;height:100%" ><div id="jtopo_toolbar" style="padding:10px 10px"></div>
<div name="topo_tips" class="panel-default" style="color: white;background: rgba(51, 122, 183, 0.5);position: absolute;padding:10px;" id="topoTips">
<div class="panel-body" style=""></div></div>
<canvas width= `+(canvasW-20)+` height=`+(canvasH-60)+` id="canvas" style="border-top: 1px solid #d8dbe6;"></canvas></div>`);
    }else{
        $("#"+contId).html(`<div  style="width:100%;height:100%" >
<canvas width= `+(canvasW-20)+` height=`+(canvasH-60)+` id="canvas" style="border-top: 1px solid #d8dbe6;"></canvas></div>`);
    }
    var canvas = document.getElementById('canvas');
    var stage = new JTopo.Stage(canvas);
    stage.wheelZoom = null; // 设置鼠标缩放比例
  stage.addEventListener("click", function(event){
    var iframSrc = (window.location.hostname === 'localhost' ? '/anpm-plug-phystopo-ui.html' : '/phystopo');
    let funcs = JSON.parse(sessionStorage.getItem('accessToken')).funcs;
    let UrlObj = funcs.filter((item)=>{
      return item.routeUrl === '/phystopo'
    }) ;
    let parUrlObj = funcs.filter((item)=>{
      return item.fnName === '拓扑管理'
    }) ;
    let navData = {
      navName: UrlObj[0].fnName,
      functionCode: null,
      subMenuName: null,
      functionUrl: UrlObj[0].routeUrl,
      node1: null,
      parentactiveId: UrlObj[0].parentFnId,
      activeId: UrlObj[0].fnId,
      parentFunctionName: parUrlObj[0].fnName,
    };
    top.window.vm.parentactiveId = navData.parentactiveId;
    top.window.vm.activeId = navData.activeId;
    top.window.vm.preMenu = navData.parentFunctionName;
    top.window.vm.activeName = navData.navName;
    sessionStorage.setItem("menu", JSON.stringify(navData));
top.document.getElementById("sub-content-page").src = iframSrc;
  });
    if(!isDashBoard){
        // stage.eagleEye.visible = true;//鹰眼
        showJTopoToobar(stage,datas);
    }
    let maxNodeNum = 11;
    let nodearr = [];
    var scene = new JTopo.Scene(stage);
    scene.alpha = 1;//透明度
    var tdata0 = {};
    tdata0.isDashBoard = isDashBoard;
    tdata0.unfoldFlag = "0";
    // let linkarr = [];
    // //根节点为拓扑图名称
    // let tanzhenNode = addNode(scene,thistopo.topologyName||"--",fuwuqiG,null,"0",tdata0,ispath);
    // let tanzhenNodes = [];
    //
    // for(var i=0; i<nodelist.length; i++){
    //     if(nodelist[i].type ==1){
    //         var node = new JTopo.Node(nodelist[i].aliases);
    //         node.setSize(42, 42);
    //         node.setLocation(200,200+i*200);
    //         node.imgSrcS =  getJtopoNodeImgByValue(nodelist[i].type,ispath);;
    //         scene.add(node);
    //         tanzhenNodes.push(nodelist[i]);
    //     }
    // }

    // return scene;
    if(nodelist.length>0){
        //创建节点
        for(var i=0; i<nodelist.length; i++){
            var thisData = nodelist[i];
            let arr = [];
            let nodeimg = getJtopoNodeImgByValue(thisData.type,ispath);
            var gaojing = showWarning?thisData.state:null;
            var text = '';
            var id = thisData.id;
            var tdata = {};
            tdata.isDashBoard = isDashBoard;
            // for(var j=0; j<linklist.length; j++){
            //     if(linklist[j].target == nodelist[i].id){
            //         tdata.linkIds = linklist[j].linkIds;
            //         tdata.preIp = linklist[j].sourceIp;
            //         tdata.curIp = linklist[j].targetIp;
            //     }
            // }
            //判断节点字段的展示
            for(let msg in msgModalData){
                if(msgModalData[msg].type == 0 &&msgModalData[msg].isShow){
                    if(thisData[msgModalData[msg].filedName]){
                        if(text){
                            text += ("\n"+thisData[msgModalData[msg].filedName])
                        }else{
                            text = thisData[msgModalData[msg].filedName];
                        }
                    }
                }
            }
            // if(nodelist[i].type == 1){
            //     tanzhenNodeIndex.push(i);
            // }
            tdata.unfoldFlag =thisData.unfoldFlag;
            let node = addNode(scene,text,nodeimg,gaojing,id,tdata,ispath);
            arr.push(node);
            nodearr[i] = arr;
        }
        //节点连线
        for(var i=0; i<linklist.length; i++){
            var thisNode = null;
            var lastNode = null;
            for(var j=0; j<nodearr.length; j++){
                if(linklist[i].source == nodearr[j][0].nodeId){
                    thisNode = nodearr[j][0];
                }
                if(linklist[i].target == nodearr[j][0].nodeId){
                    lastNode = nodearr[j][0];
                }
            }

            if(thisNode!=null&&lastNode!=null){
                // var iscontinue = false;
                // for(var j=0; j<linkarr.length; j++){
                //     if(linkarr[j].thisNodeId==lastNode.nodeId && linkarr[j].lastNodeId==thisNode.nodeId ){
                //         iscontinue =true;
                //         continue;
                //     }
                // }
                // if(iscontinue)continue
                // var linkss = {
                //     thisNodeId:thisNode&&thisNode.nodeId,
                //     lastNodeId:lastNode&&lastNode.nodeId
                // }
                // linkarr.push(linkss);


                var tdata = {};
                var gaojing = showWarning?linklist[i].state:null;;
                tdata.linkIds = linklist[i].linkIds;
                tdata.preIp = linklist[i].sourceIp;
                tdata.curIp = linklist[i].targetIp;
                tdata.text = linklist[i].id;
                addLink(scene,thisNode, lastNode,type,gaojing,tdata);
            }
            // ;
        }
        //将根节点与探针节点进行连接
        // for(var i=0; i<tanzhenNodeIndex.length; i++){
        //     var tdata = {};
        //     tdata.linkIds = nodearr[tanzhenNodeIndex[i]][0].linkIds;
        //     tdata.preIp = nodearr[tanzhenNodeIndex[i]][0].sourceIp;
        //     tdata.curIp = nodearr[tanzhenNodeIndex[i]][0].targetIp;
        //     tdata.text = nodearr[tanzhenNodeIndex[i]][0].id;
        //     var lastnodes = nodearr[tanzhenNodeIndex[i]][0];
        //     addLink(scene,tanzhenNode,lastnodes ,type,null,tdata);
        // }
    }

    // 树形布局
    // if(type=='sx'){
    //     scene.doLayout(JTopo.layout.TreeLayout('right', 150, 120));
    // }else if(type=='xx'){
    //     // scene.doLayout(JTopo.layout.TreeLayout('down', 150, 160));
    //     scene.doLayout(JTopo.layout.CircleLayout( 500));
    // }
    // scene.doLayout(JTopo.layout.FlowLayout(150, 150));
    // scene.doLayout(JTopo.layout.layoutNode(200, 200));
    //默认收缩


    // 第一层以当前节点名称为 key 区分折叠状态
    var indexnode = [];
    var nodearrs = [];
    var childs = scene.childs;
    for (var j = 0; j < childs.length; j++) {
        if(childs[j].elementType == 'node'&&childs[j].unfoldFlag==1){
            childs[j].setImage(serverInvalidImg, true);
            childs[j].isshow = false;
            var thisNode = childs[j].nodeId;
            var status = [];
            var tarlink = childs[j].outLinks;
            for (var i = 0; i < tarlink.length; i++) {
                status[i] = {node: childs[j].visible, link: tarlink[i].visible};
                foldOpenStatus[thisNode] = status;
                // 收缩下一层级节点
                if (childs[j].outLinks.length != 0) {
                    dbfold(childs[j].outLinks, foldOpenStatus[thisNode][i]);
                }
            }
        }
        if((childs&&childs[j]&&childs[j].elementType == 'node'&&childs[j].inLinks==null)||(childs[j].inLinks&&childs[j].inLinks.length==0)){
            indexnode.push(childs[j])
        }
    }
    var xx = 100;
    var yy = 100;
    for (var i = 0; i < indexnode.length; i++) {
        yy=maxy+yx
        // ;
        var arrs = [];
        indexnode[i].setLocation(xx,yy);
        layoutCircle(indexnode[i].outLinks,arrs,xx,yy);
        console.log(arrs);
    }
    //获取头第一个节点多少个，多少条链路
    // ;

    // var xx = 100;
    // var yy = 100;

    // for (var i = 0; i < indexnode.length; i++) {
    //     var nodeId = indexnode[i].nodeId;
    //     xx  = 100
    //     yy =  yy+maxy
    //     indexnode[i].setLocation(xx,yy);
    //     //负责所有点位的当前级
    //     for (var j =0; j < childs.length; j++) {
    //         if(nodeId == childs[j].nodeId){
    //             var outLinks =childs[j].outLinks;
    //             //获取下一级的nodeId
    //             for(var k =0; k < outLinks.length; k++){
    //                 if(outLinks[k].nodeA.nodeId == nodeId){
    //                     yy = yy+100*k;
    //                     if(yy>maxy)maxy = yy
    //                     nodeId = outLinks[k].nodeZ.nodeId
    //                     //负责将下一级位置进行定位
    //                     for (var v =0; v < childs.length; v++) {
    //                         if(nodeId == childs[v].nodeId){
    //                             xx = xx+100;
    //                             if(childs[v].nodeId=='167772308-10.0.0.41'){
    //                                 
    //                             }
    //                             childs[v].setLocation(xx,yy);
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    // ;
    let d2 = new Date().getTime()
    console.log(d2 - d1,"----------耗时ms-----");
    stage.centerAndZoom();
    return scene;
}

var iii = 0;
/**
 * 布局
 * @param {ht.Node} root - 根节点
 */
function layoutCircle(outLinks,arrs,x,y) {
    if(!outLinks){
        maxy = y+yx;
        return;
    }
    let xx= x+yx;
    for (var i =0; i < outLinks.length; i++) {
// 获取到所有的孩子节点对象数组
        var children = outLinks[i].nodeZ;
        // console.log(children.nodeId,iii++);
        if(children.nodeId == '167772308_20_8'){
        }
        let yy= y+yx*i;
        for (var j =0; j < arrs.length; j++) {
            if(arrs[j].nodeId == children.nodeId){
                return;
            }
            if(arrs[j].x==xx&&arrs[j].y==yy){
                yy= yy+yx;
            }
        }
        // if(children.nodeId == '167772308_10_7'){
        //     ;
        // }
        console.log(iii++,"-------");
        children.setLocation(xx,yy);
        arrs.push(children)
        if(maxy<yy) maxy=yy ;
        // if(children.outLinks.length==0)return;
        layoutCircle(children.outLinks,arrs,xx,yy);
    }
}


function showToolip(options) {
    var temp = $(document);
    var tipsWin = temp.find('div[name=topo_tips]').hide();
    //var tipsWin = $("#topoTips")
    var tips_body = tipsWin.find('.panel-body');
    var op = options || {};
    var x = op.x;
    var y = op.y;
    var width = op.width || 200;
    var content = op.content || '';
    if (content) {
        tips_body.show();
        tips_body.html(content);
        tipsWin.css({
            "left": x,
            "top": y,
            "width": width
        }).show();
    } else {
        tips_body.hide();
        tipsWin.hide();
    }
}

// 页面工具栏
export function getJtopoNodeImgByValue(value,ispath){
    let imgtype = '';
    if(ispath){
        if(value&&value == 0){
            imgtype =routeSubsImg
        }else if(value&&value == 1){
            imgtype = tanzhenImg;
        }else if(value&&value == 2){
            imgtype = jiedianImgG;
        }else {
            imgtype = jiedianImgH;
        }
    }else{
        if(value&&value == 1){
            imgtype =fuwuqiG
        }else if(value&&value == 2){
            imgtype = jioahuanjiG;
        }else if(value&&value == 3){
            imgtype = luyouqiG;
        }else {
            imgtype = fanghuoqiangG;
        }
    }
    return imgtype;
};


/**
 * 2.1拓扑图数据排序
 *
 */
export function setDataList(dataList){
    let tanz = [];
    if(dataList){
        for(let i=0; i<dataList.length; i++){
            if(dataList[i].type == 1){
                tanz.push(dataList[i]);
            }
        }
        for(let i=0; i<dataList.length; i++){
            if(dataList[i].type != 1){
                tanz.push(dataList[i]);
            }
        }
    }
    // console.log(tanz,"---tanz-");
    return tanz;
}

//1.循环层级，找到节点最多的层级下标maxIndex
function getMaxIndex(dataList){
    let maxIndex = 0;
    for(let i=1; i<dataList.length; i++){
        if(dataList[i-1].length<dataList[i].length){
            maxIndex = i;
        }
    }
    return maxIndex;
}

//计算最外层最大节点个数
function getMaxLeafCnt(data) {
    var maxcnt = 0;//本身
    var children = data.children == undefined ? new Array() : data.children;
    if (children.length > 0) {
        for (var i = 0; i < children.length; i++) {
            var f = children[i];
            if (f.children == undefined) {
                maxcnt++;
            } else {
                maxcnt += getMaxLeafCnt(f);
            }
        }
    }
    return maxcnt;

}


/**
 *创建node节点
 */
function addNode(scene,text,img,gaojing,id,tdata,ispath){
    var node = new JTopo.Node(text);
    node.setSize(42, 42);
    node.isshow = true;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("isshow"); // 把自定义属性加入进节点的属性组
    //点击收缩功能
    if(tdata&&!tdata.isDashBoard&&ispath){
        node.addEventListener("dbclick",function(event){
            if(node.isshow){
                node.setImage(serverInvalidImg, true);
                node.isshow = false;
            }else{
                node.setImage(node.imgSrcS, true);
                node.isshow = true;
            }
            foldOpen(event,node);
            // 此事件不可删除
        });
    }
    node.paintText = function(a){
        a.beginPath(),
            a.font = this.font,
            a.wrapText(this.text||'--',this.height/2,this.height);
        a.closePath()
    };
    //创建图片类型
    node.imgSrcS = img;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("imgSrcS"); // 把自定义属性加入进节点的属性组
    node.nodeId = id;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("nodeId"); // 把自定义属性加入进节点的属性组
    node.unfoldFlag = tdata.unfoldFlag;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("unfoldFlag"); // 把自定义属性加入进节点的属性组

    // node.setLocation(x ,y);
    node.setImage(img, true);
    let color = "237,146,28";
    let gjmsg = null;
    if(gaojing == 1){
        gjmsg = "";
        color = '237,146,28'
    }else if(gaojing == 2){
        gjmsg = "";
        color = '240,28,28'
    }
    node.fontColor = color;
    node.alarm = gjmsg;
    node.prop =  {"name":text,"告警":gjmsg,"nodeId":id,"时延":"--","linkIds":tdata.linkIds,"preIp":tdata.preIp,"丢包":"--","curIp":tdata.curIp};
    node.setSize(42, 42);
    // let x = $("#canvas").width()/3;
    // node.setLocation(x,250);
    scene.add(node);
    return node;
}
/**
 * CurveLink 曲线
 * FlexionalLink 二次折线
 * FoldLink 折线
 * Link 简单连线
 *
 * 参数：对象，第一个node对象，第二个node对象，虚实线，颜色，线名称或数据
 */
function addLink(scene,nodeA, nodeZ,type,gaojing,tdata){
    // let texts = text+"ms "+diubaol+"%";
    // console.log(texts)
    let color = "0, 102, 0";
    let gjmsg = '';
    if(gaojing == 1){
        gjmsg = "①";
        color = '237,146,28'
    }else if(gaojing == 2){
        gjmsg = "②";
        color = '240,28,28'
    }
    var link = null;
    // if(type=='sx'){
    link = new JTopo.CurveLink(nodeA, nodeZ,gjmsg);
    // }else if(type=='xx'){
    //     link = new JTopo.FlexionalLink(nodeA, nodeZ,gjmsg);
    // }
    link.fontColor = color;
    link.font = '20px Consolas';
    link.strokeColor = color;
    link.lineWidth = 1; // 线宽
    link.prop =  {"name":tdata.text,"告警":gjmsg,"时延":"--","linkIds":tdata.linkIds,"preIp":tdata.preIp,"丢包":"--","curIp":tdata.curIp};
    link.serializedProperties.push("prop"); // 把自定义属性加入进节点的属性组

    scene.add(link);
    return link;
}



/**
 * 页面工具栏
 * 必须调取
 * 参数：stage对象，容器id，是否显示工具栏
 */
export function showJTopoToobar(stage,datas){
    var toobarDiv = '<div style="float:left;" />';
    toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="zoomOutButton" title="放大" src="data:image/png;base64,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"/>'
    toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="zoomInButton" title="缩小" src="data:image/png;base64,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"/>'
    toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="yemianhuanyuan" title="页面还原" src="data:image/png;base64,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"/>'
    if(datas.topoType == 'sx'){
        //树形选中，星形白色
        toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="shuxingButton" title="树形" src="data:image/png;base64,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"/>'
        toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="xingxingButton" title="星型" src="data:image/png;base64,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"/>'
    }else{
        toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="shuxingButton" title="树形" src="data:image/png;base64,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"/>'
        toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="xingxingButton" title="星型" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkEwMTExMUQ4RDI3MzExRUJBNTQ0RTFGMzVFNkJGNkFFIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkEwMTExMUQ5RDI3MzExRUJBNTQ0RTFGMzVFNkJGNkFFIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTAxMTExRDZEMjczMTFFQkE1NDRFMUYzNUU2QkY2QUUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTAxMTExRDdEMjczMTFFQkE1NDRFMUYzNUU2QkY2QUUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6ePY7LAAADlklEQVR42uxXSU9TURT+XufSgaEqAgJCEAdsxCgiKAhGY0BM1IVEoyuNK9fuTfwJJi7UlQvR6MoZTSQMJkgQBxQVGZwoEoZC6Wtfy2s95zIoGmOq5enCk9ykvfe+e77znfOd+57U3h+pAnCBRhq0NQ+NoxIBGGh6PpY2MhHW1LvLaUSZO9mj48i1ds424zNNh79s/wEYfvfB1CQDKtbaYDRIaH0to9sT0o6BxAQ9ju1MwRICYTXpcLgyGbmpJu0A5KWbEFajOHtrFOfqR/FuKAT3cot2AFRyrpMAiZ6W+BBJghKOalcDL94rKF6ZgBPVLoSmoiINVx+Ox58BCkwUW6JNP2+e6T9fP4YPw2FM0e8zN4fh9avz9tjMOmQuMsJkkH6PAZtFhyMVSUhLNor/D1/JuNvhm1vPcBmwyKkX+3KXmtD1QcFsEgpzLNizyQkD5SkYjuDiAy8+joRjY2C72y7yevrKEOqavNiyOoGcToMpyrPiKKmA1cAR1pYloabIKdaMegm7NzrR/FLGqbrPeNYfxK719tgZSHHo0fc5JOjuGZzWePEKKzrIYdUGB1pe+lH/ZFLMF2RZULs1UexnVsxGCd0DCiJESS89uzbLEjsAPqx0VQLlGMghilU6beUyMwpzrWK9iSL8WpRByIoDBwgE1wQzt6PQLtJSnG9F/1AodgAtXbLIbylRLysRXGocx1uPghICtWu9AyaKMjBzLksyStF29AVwo80HF7FXQ2modNtEINfbfLED4Ihvt/vE+NZa3wRQQhLcW+zENZIep2jHOjssJgkNz/0IkywHx6Zw/t7owvQBpvhy8wQOlifi5P7Fc3P3n/oxNqlq04jWZJqF00uNXuiJ/+xUIzaSMtq6ZdGYFgQA5zV7iQlmUgEXZ12zF10fFbHWS3kuyLRg32YnhsanMDyhopMKMxqNE4AV6WYcKk+CPxiBw6qjq1cRFT5rXKTtPQFsK7AhU1Zpjx7pKcZ5jeuPAHAjeUT0ckG6synSEqdoOFyAs8ZaZ+WwU5YqF2njCz8pJfLntyFHPSFPFxj3fG6x+vnXAwHC3H3go70sTZZqXBh42hckTdsFrcupDt7S208wFP1Bnjup+WQtnt7D+h/3q/EBcOexDyM+VVw6T6jZNFEb/t6aaY4VkJ9hFv2/oXMyfirgnt76RhbjZxbFr/f8fy3/ZwF4+ENRa5vxOchfx9Uzn+dLNcbwicbxLwIMAHxVf/3riPqSAAAAAElFTkSuQmCC"/>'
    }
    toobarDiv +='&nbsp;&nbsp;<img  style="cursor:pointer;" id="exportButton" title="导出PNG" src="data:image/png;base64,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"/>'
    toobarDiv +='</div>'
    toobarDiv +='<div style="float:right;" />'
    toobarDiv +='&nbsp;&nbsp;<input type="checkbox" name="modeRadio" value="select" id="r2"/><label for="r2"> 框选</label>'
    toobarDiv +='&nbsp;&nbsp;<input type="checkbox" id="zoomCheckbox" checked="checked"/><label for="zoomCheckbox">鼠标缩放</label>'
    toobarDiv +='&nbsp;&nbsp;<input type="text" id="findText" style="width: 200px;border:1px solid #eaebf0;padding:6px;border-radius:4px;outline:#00FF00" placeholder="请输入节点名称" value="" onkeydown="enterPressHandler(event)">'
    toobarDiv +='&nbsp;&nbsp;<input type="button" id="findButton" style="padding:6px 20px;color:#fff;background: #2d8cf0;border: none;border-radius:3px;" value="查询">'
    toobarDiv +='&nbsp;&nbsp;<input type="button" id="fullScreenButton" style="padding:6px 20px;color:#fff;background: #2d8cf0;border: none;border-radius:3px;" value="全屏">'
    toobarDiv +='</div>'
    $('#jtopo_toolbar').html(toobarDiv);
    // 工具栏按钮处理

    $("input[name='modeRadio']").click(function(){
        // console.log(stage.mode,"----- stage.mode-------");
        if($(`input[name='modeRadio']`).is(':checked')){
            console.log(JSON.stringify(stage.mode),"----- stage.mode-------");
            stage.mode = $("input[name='modeRadio']:checked").val();
        }else{
            stage.mode ="normal";

        }
    });

    $('#shuxingButton').click(function(){
        if(datas.topoType != 'sx'){
            datas.topoType = "sx"
        }
        newJtopo(datas);
    });

    $('#xingxingButton').click(function(){
        if(datas.topoType != 'xx'){
            datas.topoType = "xx"
        }
        newJtopo(datas);
    });
    $('#yemianhuanyuan').click(function(){
        newJtopo(datas);
    });
    $('#centerButton').click(function(){
        stage.centerAndZoom(); //缩放并居中显示
    });
    $('#zoomOutButton').click(function(){
        stage.zoomOut();
    });
    $('#zoomInButton').click(function(){
        stage.zoomIn();
    });
    $('#cloneButton').click(function(){
        stage.saveImageInfo();
    });
    $('#exportButton').click(function() {
        stage.saveImageInfo();
    });
    $('#printButton').click(function() {
        stage.saveImageInfo();
    });
    $('#topoList').click(function(){
        //直接跳转
        this.$router.push({path:"/topolist"})
        // window.parent.postMessage({type:'ifrUrl',msg:"msg"}, '*');
    });
    $('#zoomCheckbox').click(function(){
        if($('#zoomCheckbox').is(':checked')){
            stage.wheelZoom = 1.2; // 设置鼠标缩放比例
        }else{
            stage.wheelZoom = null; // 取消鼠标缩放比例
        }
    });
    $('#fullScreenButton').click(function(){
        runPrefixMethod(stage.canvas, "RequestFullScreen")
    });

    window.enterPressHandler = function (event){
        if(event.keyCode == 13 || event.which == 13){
            $('#findButton').click();
        }
    };

    // 查询
    $('#findButton').click(function(){
        var text = $('#findText').val().trim();
        //var nodes = stage.find('node[text="'+text+'"]');
        var scene = stage.childs[0];
        var nodes = scene.childs.filter(function(e){
            return e instanceof JTopo.Node;
        });
        nodes = nodes.filter(function(e){
            if(e.text == null) return false;
            return e.text.indexOf(text) != -1;
        });

        if(nodes.length > 0){
            var node = nodes[0];
            node.selected = true;
            var location = node.getCenterLocation();
            // 查询到的节点居中显示
            stage.setCenter(location.x, location.y);

            function nodeFlash(node, n){
                if(n == 0) {
                    node.selected = false;
                    return;
                };
                node.selected = !node.selected;
                setTimeout(function(){
                    nodeFlash(node, n-1);
                }, 300);
            }
            // 闪烁几下
            nodeFlash(node, 6);
        }
    });
    return stage;
}

var runPrefixMethod = function(element, method) {
    var usablePrefixMethod;
    ["webkit", "moz", "ms", "o", ""].forEach(function(prefix) {
            if (usablePrefixMethod) return;
            if (prefix === "") {
                // 无前缀，方法首字母小写
                method = method.slice(0,1).toLowerCase() + method.slice(1);
            }
            var typePrefixMethod = typeof element[prefix + method];
            if (typePrefixMethod + "" !== "undefined") {
                if (typePrefixMethod === "function") {
                    usablePrefixMethod = element[prefix + method]();
                } else {
                    usablePrefixMethod = element[prefix + method];
                }
            }
        }
    );

    return usablePrefixMethod;
};


function foldOpen(e,node){ 	 			// 折叠展开
    var thisNode = e.target.nodeId;  	// 第一层以当前节点名称为 key 区分折叠状态
    var tarlink = e.target.outLinks;
    if(tarlink == undefined){
        return
    }

    if(tarlink.length != 0 && tarlink[0].visible === true){
        var status = [];
        for (var i = 0; i < tarlink.length; i++){
            status[i] = {node: tarlink[i].nodeZ.visible, link: tarlink[i].visible};
            foldOpenStatus[thisNode] = status;
            tarlink[i].nodeZ.visible = false;
            tarlink[i].visible = false;
            // 下一层还有节点
            if( tarlink[i].nodeZ.outLinks.length != 0){
                dbfold(tarlink[i].nodeZ.outLinks, foldOpenStatus[thisNode][i]);
            }
        }
    }else if(tarlink.length != 0 && tarlink[0].visible === false){
        for (var k = 0; k < tarlink.length; k++){
            tarlink[k].nodeZ.visible =  foldOpenStatus[thisNode][k].node;
            tarlink[k].visible = foldOpenStatus[thisNode][k].link;
            // 下一层还有节点
            if( tarlink[k].nodeZ.outLinks.length != 0){
                dbOpen(tarlink[k].nodeZ.outLinks, foldOpenStatus[thisNode][k]);
            }
        }
        //维护foldOpenStatus对象中的数据
        for (var ent in  foldOpenStatus){
            if(ent == thisNode){
                delete foldOpenStatus[thisNode];
            }
        }
    }
}
//收缩
function dbfold(dblink,foldStatus){
    var status = [];  // 下层以 status 为 key 记录
    for(var j = 0; j < dblink.length; j++){
        status[j] = {node: dblink[j].nodeZ.visible, link: dblink[j].visible};
        foldStatus.status = status;
        dblink[j].nodeZ.visible = false;
        dblink[j].visible = false;
        if( dblink[j].nodeZ.outLinks.length != 0){
            dbfold(dblink[j].nodeZ.outLinks, foldStatus.status[j]);
        }
    }
}

//打开
function dbOpen(dblink, openStatus){
    for(var j = 0; j < dblink.length; j++){
        dblink[j].nodeZ.visible = openStatus.status[j].node;
        dblink[j].visible = openStatus.status[j].link;
        if( dblink[j].nodeZ.outLinks.length != 0){
            dbOpen(dblink[j].nodeZ.outLinks, openStatus.status[j]);
        }
    }
}