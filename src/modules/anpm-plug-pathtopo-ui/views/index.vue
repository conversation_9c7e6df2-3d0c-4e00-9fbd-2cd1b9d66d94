<template>
  <div :class="{ 'light-no-tabs': currentSkin != 1 }">
    <section class="topo-box" style="padding: 0">
      <!-- 顶部tabs盒子 -->
      <div :class="menuHeight > 780 ? 'tabs-box' : 'tabs-box-small'">
        <Select
          v-model="tabsId"
          @on-change="ulChange"
          @on-open-change="selectOpen"
          size="small"
          style="width: 145px"
        >
          <Option
            v-for="item in topoTabList"
            :value="item.id"
            :key="item.id"
            :label="item.name"
          >
            <Tooltip v-if="item.name.length > 7" placement="right" transfer>
              <div style="width: 100%">{{ item.name }}</div>
              <div slot="content">
                {{ filterName(item.id) }}
              </div>
            </Tooltip>
            <div v-else>{{ item.name }}</div>
          </Option>
        </Select>
      </div>
      <div class="lujintopo">
        <div class="bottom-box">
          <div
            :class="[
              'left-menu',
              menuHeight > 780 ? 'left-menu-big' : 'left-menu-small',
            ]"
            :style="{ height: menuHeight + 'px' }"
          >
            <div :class="menuHeight > 780 ? 'top-menu' : 'top-menu-small'">
              <!-- 纤维图/常规图切换 -->
              <div
                v-show="!isEndToEnd"
                :class="[
                  menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                  hoverActive === 1 ? 'icon-special' : '',
                ]"
              >
                <Tooltip placement="right">
                  <img
                    @mouseover="handleHoverActive(1)"
                    @mouseout="handleHoverActive(null)"
                    :src="getBtnTop1ImageSrc"
                    alt=""
                  />
                  <!-- 右侧选择 -->
                  <div class="tooltip-box" slot="content">
                    <p
                      @click="changeRadio(1)"
                      :class="[
                        'tooltip-normal',
                        activeValue === 1 ? 'active' : '',
                      ]"
                    >
                      {{ $t("path_routine") }}
                    </p>
                    <p
                      @click="changeRadio(2)"
                      :class="[
                        'tooltip-special',
                        activeValue === 2 ? 'active' : '',
                      ]"
                    >
                      {{ $t("path_fiber") }}
                    </p>
                  </div>
                </Tooltip>
              </div>
              <!-- 搜索 -->
              <div
                v-show="btnShow"
                :class="[
                  menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                  hoverActive === 2 ? 'icon-special' : '',
                  'close-box',
                ]"
              >
                <Tooltip placement="right" :content="$t('but_search')">
                  <img
                    @mouseover="handleHoverActive(2)"
                    @mouseout="handleHoverActive(null)"
                    @click="handleMouseOver('search')"
                    :src="getBtnTop2ImageSrc"
                    alt=""
                  />
                </Tooltip>
                <!-- 搜索 -->
                <div
                  v-if="isSearchShow"
                  :class="[
                    'my-tooltip',
                    currentSkin == 0 ? 'my-tooltip-light' : '',
                  ]"
                >
                  <div class="search-top">
                    <span class="search-title">{{ $t("but_search") }}</span>
                    <Icon
                      @click.stop="closeClick('search')"
                      color="#FE5C5C"
                      type="ios-close"
                      size="24"
                    />
                  </div>
                  <div class="search-bottom">
                    <Input v-model="inputValue" style="width: 160px" />
                    <Button class="search-btn" @click="search">{{
                      $t("btn_search")
                    }}</Button>
                  </div>
                </div>
              </div>
            </div>

            <div
              v-if="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                'layout-box',
                hoverActive === 3 ? 'icon-special' : '',
                clickActive === 3 ? 'icon-special-bg' : '',
                isEndToEnd ? '' : 'layout-special',
              ]"
            >
              <Tooltip placement="right-start">
                <img
                  @mouseover="handleHoverActive(3)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(3)"
                  :src="getBtnTop3ImageSrc"
                  alt=""
                />
                <!--树形星形切换 1树，2星-->
                <div class="tooltip-box" slot="content">
                  <div
                    @click="layoutModalChange(0)"
                    :class="[
                      'img-box',
                      'tree',
                      layoutModel == 0 ? 'layout-active icon-special-bg ' : '',
                    ]"
                  >
                    <img :src="getBtnTop3Image1Src" alt="" />
                  </div>
                  <div
                    @click="layoutModalChange(1)"
                    :class="[
                      'img-box',
                      'tree',
                      layoutModel == 1 ? 'layout-active icon-special-bg ' : '',
                    ]"
                  >
                    <img :src="getBtnTop3Image3Src" alt="" />
                  </div>
                  <div
                    @click="layoutModalChange(2)"
                    :class="[
                      'img-box',
                      layoutModel == 2 ? 'layout-active icon-special-bg ' : '',
                    ]"
                  >
                    <img :src="getBtnTop3Image2Src" alt="" />
                  </div>
                </div>
              </Tooltip>
            </div>
            <!-- 纤维图横向纵向布局 -->
            <div
              v-else
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                'layout-box',
                hoverActive === 3 ? 'icon-special' : '',
              ]"
            >
              <Tooltip placement="right-start">
                <img
                  @mouseover="handleHoverActive(3)"
                  @mouseout="handleHoverActive(null)"
                  :src="getBtnTop16Image1Src"
                  alt=""
                />
                <!-- 0 横向 1竖向 -->
                <div class="tooltip-box" slot="content">
                  <div
                    :class="[
                      'img-box',
                      'tree',
                      fibreType === 0 ? 'layout-active ' : '',
                    ]"
                    @click="fibreTypeChange(0)"
                  >
                    <img :src="getBtnTop16Image1Src" alt="" />
                  </div>
                  <div
                    :class="[
                      'img-box',
                      fibreType === 1 ? 'layout-active ' : '',
                    ]"
                    @click="fibreTypeChange(1)"
                  >
                    <img :src="getBtnTop16Image2Src" alt="" />
                  </div>
                </div>
              </Tooltip>
            </div>
            <!-- /纤维图横向纵向布局 -->
            <!-- 放大 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 4 ? 'icon-special' : '',
                clickActive === 4 ? 'icon-special-bg' : '',
              ]"
              @click="zoomInButton"
            >
              <Tooltip :content="$t('phytopo_blow_up')" placement="right">
                <img
                  @mouseover="handleHoverActive(4)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(4)"
                  :src="getBtnTop4Image1Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 放大 -->
            <!-- 缩小 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 5 ? 'icon-special' : '',
                clickActive === 5 ? 'icon-special-bg' : '',
              ]"
              @click="zoomOutButton"
            >
              <Tooltip :content="$t('phytopo_minification')" placement="right">
                <img
                  @mouseover="handleHoverActive(5)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(5)"
                  :src="getBtnTop5Image1Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 缩小 -->
            <!-- 页面还原 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 6 ? 'icon-special' : '',
                clickActive === 6 ? 'icon-special-bg' : '',
              ]"
              @click="goBack"
            >
              <Tooltip :content="$t('phytopo_page_restore')" placement="right">
                <img
                  @mouseover="handleHoverActive(6)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(6)"
                  :src="getBtnTop6Image1Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 页面还原 -->
            <!-- 告警显示配置 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 7 ? 'icon-special' : '',
                showWarning == true ? 'icon-special-bg' : '',
              ]"
              @click="showWarnings"
            >
              <Tooltip :content="$t('phytopo_alarm_show')" placement="right">
                <img
                  @mouseover="handleHoverActive(7)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(7)"
                  v-if="showWarning"
                  :src="getBtnTop7Image2Src"
                  alt=""
                />
                <img
                  @mouseover="handleHoverActive(7)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(null)"
                  v-else
                  :src="getBtnTop7Image1Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 告警显示配置 -->
            <!-- 导出图片 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 8 ? 'icon-special' : '',
                clickActive === 8 ? 'icon-special-bg' : '',
              ]"
              @click="exportButton"
            >
              <Tooltip :content="$t('phytopo_exporting_PNG')" placement="right">
                <img
                  @mouseover="handleHoverActive(8)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(8)"
                  :src="getBtnTop8Image1Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 导出图片 -->
            <!-- 信息展示设置 -->
            <div
              v-show="btnShow"
              @click="handleMouseOver('information')"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                'close-box',
                hoverActive === 9 ? 'icon-special' : '',
                clickActive === 9 ? 'icon-special-bg' : '',
              ]"
            >
              <Tooltip :content="$t('phytopo_information')" placement="right">
                <img
                  @mouseover="handleHoverActive(9)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(9)"
                  :src="getBtnTop9Image1Src"
                  alt=""
                />
              </Tooltip>
              <div
                v-if="informationShow"
                :class="[
                  'my-tooltip',
                  currentSkin == 0 ? 'my-tooltip-light' : '',
                ]"
              >
                <div class="search-top">
                  <span class="search-title">{{
                    $t("phytopo_information")
                  }}</span>
                  <Icon
                    @click.stop="closeClick('information')"
                    color="#5CA0D5"
                    type="ios-close"
                    size="24"
                  />
                </div>
                <div class="information-bottom">
                  <CheckboxGroup v-model="msgModal" @on-change="msgModalChange">
                    <Checkbox
                      :class="item.className"
                      v-for="item in msgModalData"
                      :key="item.id"
                      :label="item.name"
                    ></Checkbox>
                  </CheckboxGroup>
                </div>
              </div>
            </div>
            <!-- 信息展示设置 -->
            <!-- 图例展示 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                'close-box',
                hoverActive === 10 ? 'icon-special' : '',
                clickActive === 10 ? 'icon-special-bg' : '',
              ]"
            >
              <Tooltip :content="$t('phytopo_legend')" placement="right">
                <img
                  @mouseover="handleHoverActive(10)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(10)"
                  @click="handleMouseOver('legend')"
                  :src="getBtnTop10Image1Src"
                  alt=""
                />
              </Tooltip>
              <div
                v-if="legendShow"
                :class="[
                  'my-tooltip',
                  currentSkin == 0 ? 'my-tooltip-light' : '',
                ]"
              >
                <div class="search-top">
                  <span class="search-title">{{ $t("phytopo_legend") }}</span>
                  <Icon
                    @click.stop="closeClick('legend')"
                    color="#5CA0D5"
                    type="ios-close"
                    size="24"
                  />
                </div>
                <div class="legend-bottom" :style="{ width: legendWidth }">
                  <div class="legend-color">
                    <div class="top-one">
                      <div class="legend-color-item">
                        <div
                          class="color-item"
                          :style="{
                            width: '24px',
                            height: '16px',
                            background: configData.normalLineColor,
                          }"
                        ></div>
                        <div class="color-title">
                          {{ $t("common_Normal") }}
                        </div>
                      </div>
                      <div class="legend-color-item">
                        <div
                          class="color-item"
                          :style="{
                            width: '24px',
                            height: '16px',
                            background: configData.degradationLineColor,
                          }"
                        ></div>
                        <div class="color-title">
                          {{ $t("phytopo_deterioration") }}
                        </div>
                      </div>
                    </div>
                    <div class="top-one">
                      <div class="legend-color-item">
                        <div
                          class="color-item"
                          :style="{
                            width: '24px',
                            height: '16px',
                            background: configData.breakLineColor,
                          }"
                        ></div>
                        <div class="color-title">
                          {{ $t("phytopo_interrupt") }}
                        </div>
                      </div>
                      <div class="legend-color-item">
                        <div
                          class="color-item"
                          :style="{
                            width: '24px',
                            height: '16px',
                            background: configData.unknownLineColor,
                          }"
                        ></div>
                        <div class="color-title">
                          {{ $t("comm_unknown") }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="legend-icon">
                    <div
                      class="legend-icon-item"
                      v-for="item in legendData"
                      :key="item.deviceTypeId"
                    >
                      <img
                        style="
                          cursor: pointer;
                          width: 20px;
                          margin-right: 10px;
                          height: 20px;
                        "
                        :src="item.showImage"
                      />
                      <span>{{ item.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 图例展示 -->
            <!-- 故障路径 -->
            <div
              v-show="btnShow"
              @click="getFaultPathTopologyResult"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 11 ? 'icon-special' : '',
                checkFaultPathTopology == true ? 'icon-special-bg' : '',
              ]"
            >
              <Tooltip :content="$t('phytopo_fault_path')" placement="right">
                <img
                  @mouseover="handleHoverActive(11)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(11)"
                  v-if="!checkFaultPathTopology"
                  :src="getBtnTop11Image1Src"
                  alt=""
                />
                <img
                  @mouseover="handleHoverActive(11)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(null)"
                  v-else
                  :src="getBtnTop11Image2Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 故障路径 -->
            <!-- 框选 -->
            <div
              v-show="btnShow"
              :class="[
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                hoverActive === 12 ? 'icon-special' : '',
                checkBoxSelect == true ? 'icon-special-bg' : '',
              ]"
            >
              <Tooltip :content="$t('pathtopo_box_select')" placement="right">
                <img
                  @mouseover="handleHoverActive(12)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(12)"
                  @click="getBoxSelectResult(false)"
                  v-if="checkBoxSelect"
                  :src="getBtnTop12Image2Src"
                  alt=""
                />

                <img
                  @mouseover="handleHoverActive(12)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(null)"
                  v-else
                  @click="getBoxSelectResult(true)"
                  :src="getBtnTop12Image1Src"
                  alt=""
                />
              </Tooltip>
            </div>
            <!-- 框选 -->
            <!-- 数据缩略 -->
            <!-- 123 -->
            <!-- <div class="step-total" ref="stepTotal" >
                        <div  class="step-left" :style="{width:leftWidth + 'px','height':'6px'}">
                          <div class="step-circle"  >
                            <div class = "drag-box" @mousedown.self="handleMouseDown" @mouseup.self="handleMouseUp" @mouseleave.self="handleLeft">
                              -->

            <!-- /123 -->
            <div
              v-show="btnShow"
              :class="[
                'data-format',
                menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                'close-box',
                hoverActive === 13 ? 'icon-special' : '',
                clickActive === 13 ? 'icon-special-bg' : '',
              ]"
              :style="{ marginBottom: marginBottom + 'px' }"
            >
              <Tooltip
                placement="right-end"
                :content="$t('topo_data_abbreviation')"
              >
                <img
                  @mouseover="handleHoverActive(13)"
                  @mouseout="handleHoverActive(null)"
                  @mousedown="handleClickActive(13)"
                  @click.self="handleMouseOver('abbreviation')"
                  :src="getBtnTop13Image1Src"
                  alt=""
                />
              </Tooltip>
              <!-- 配置modal -->
              <div
                v-if="isDataType"
                :class="[
                  'my-tooltip',
                  currentSkin == 0 ? 'my-tooltip-light' : '',
                ]"
              >
                <div class="search-top">
                  <span class="search-title">{{
                    $t("topo_data_abbreviation")
                  }}</span>
                  <Icon
                    @click.self="dataTypeClose"
                    color="#5CA0D5"
                    type="ios-close"
                    size="24"
                  />
                </div>
                <div class="data-bottom">
                  <div class="left-drag">
                    <div class="drag-title">{{ $t("drag_left_title") }}</div>
                    <div class="left-box">
                      <span>1</span>
                      <div
                        class="drag-step"
                        @mousemove="handleMouseMove"
                        @mouseup="handleLeft"
                        @mouseleave="handleLeft"
                      >
                        <div
                          class="step-bg"
                          :style="{ width: leftWidth + 'px' }"
                        ></div>
                        <div class="step-circle">
                          <div
                            class="drag-mouse"
                            @mousedown.self="handleMouseDown"
                          ></div>
                          <div class="num-tip">{{ leftNum }}</div>
                        </div>
                      </div>
                      <span>{{ totalNum }}</span>
                    </div>
                  </div>
                  <div class="left-drag">
                    <div class="drag-title">{{ $t("drag_right_title") }}</div>
                    <div class="left-box">
                      <span>1</span>

                      <div
                        class="drag-step"
                        @mousemove="handleMouseMoveRight"
                        @mouseup="handleRight"
                        @mouseleave="handleRight"
                      >
                        <div
                          class="step-bg"
                          :style="{ width: rightWidth + 'px' }"
                        ></div>
                        <div class="step-circle">
                          <div
                            class="drag-mouse"
                            @mousedown.self="handleRightDown"
                          ></div>
                          <div class="num-tip">{{ rightNum }}</div>
                        </div>
                      </div>
                      <span>{{ totalNum }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 配置modal -->
            </div>
            <!-- 数据缩略 -->
            <div
              :class="menuHeight > 780 ? 'bottom-menu' : 'bottom-menu-small'"
            >
              <!-- 拓扑图管理 -->
              <div
                @click="queryClick"
                :class="[
                  menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                  hoverActive === 14 ? 'icon-special' : '',
                  clickActive === 14 ? 'icon-special-bg' : '',
                ]"
              >
                <Tooltip placement="right" :content="$t('phytopo_management')">
                  <img
                    @mouseover="handleHoverActive(14)"
                    @mouseout="handleHoverActive(null)"
                    @mousedown="handleClickActive(14)"
                    :src="getBtnTop14ImageSrc"
                    alt=""
                  />
                </Tooltip>
              </div>
              <!-- 拓扑图管理 -->
              <!-- 全屏 -->
              <div
                v-show="btnShow"
                @click="fullScreenButton"
                :class="[
                  menuHeight > 780 ? 'icon-box' : 'icon-box-small',
                  hoverActive === 15 ? 'icon-special' : '',
                  clickActive === 15 ? 'icon-special-bg' : '',
                ]"
              >
                <Tooltip placement="right" :content="$t('but_full_screen')">
                  <img
                    @mouseover="handleHoverActive(15)"
                    @mouseout="handleHoverActive(null)"
                    @mousedown="handleClickActive(15)"
                    :src="getBtnTop15ImageSrc"
                    alt=""
                  />
                </Tooltip>
              </div>
              <!-- 全屏 -->
            </div>
          </div>
          <div class="right-box">
            <Loading v-show="loading" fix></Loading>
            <div v-show="baImg && !loading" class="backgroundImg">
              <img :src="getFileemptyImageSrc" alt="" />
              <p>{{ $t("common_No_data") }}</p>
            </div>
            <div
              v-show="jtopoShow && !baImg"
              id="drawing-board"
              class="bg-box"
              :style="{
                width: '100%',
              }"
            >
              <PathTopo
                v-if="jtopoShow && !baImg"
                ref="PathTopo"
                :pathTopoData="pathTopoData"
                :tabsId="tabsId"
                :showType="showType"
                :showWarning="showWarning"
                :searchValue="inputValue"
                @getLinkTable="getLinkTable"
                @getDetailData="getDetailData"
                :configData="configData"
                :checkBoxSelect="checkBoxSelect"
                :backgroundImage="backgroundImage"
                :showPeerToPeerLinkDetail="showPeerToPeerLinkDetail"
                :pathTopologyLocXLocYMinMax="pathTopologyLocXLocYMinMax"
                :layoutModel="layoutModel"
                :topoTabList="topoTabList"
                @setPreIp="setPreIp"
                @updatePathResult="getPathTopologyResult(tabsId)"
                @updateCompleteNodesShow="updateCompleteNodesShow"
                :pathTopoCoorType="0"
                :containerWidth="jtopoWidth"
                :containerHeight="echartsHeight"
                :forbidUpdate.sync="forbidUpdate"
              >
              </PathTopo>
            </div>
            <div
              class="bg-box"
              v-if="eChartShow && !baImg"
              :style="{
                ' width': '100%',
                height: echartsHeight + 'px',
                backgroundImage: `url(${echartsBg})`,
              }"
            >
              <!-- backgroundImage: `url(${echartsBg})`, -->
              <!-- <div
              id="mainChart"
              :style="{ 'width': '100%', height: canvasH + 'px','marginTop': marginHeight + 'px' }"
            ></div> -->
              <!-- 纤维图使用jtopo组件 -->

              <FiberTopo
                ref="fiberTopo"
                :fiberData="nodeData"
                :fiberTopoHeight="echartsHeight"
                :fiberTopoWidth="jtopoWidth"
                :fibreTopoLineLengthPercentage="fibreTopoLineLengthPercentage"
                :fibreType="fibreType"
                @nodeClick="nodesClick"
                :topoTabList="topoTabList"
                :tabsId="tabsId"
                :configData="configData"
              ></FiberTopo>
            </div>
          </div>
        </div>
      </div>

      <!-- 点击纤维图弹窗 -->
      <Modal
        v-model="fibreShow"
        :width="modalWidth"
        class="action-index-modal modal-scroll"
        content="adasdadsadas"
        :title="modalTitle"
        :mask="true"
        sticky
        draggable
        :footer-hide="true"
        @on-cancel="onCancel"
      >
        <p slot="header" style="text-align: left; font-size: 16px">
          <span :title="modalTitle">{{
            modalOmitTitle ? modalOmitTitle : modalTitle
          }}</span>
        </p>
        <ModalContent
          v-if="fibreShow"
          ref="modalContent"
          @setTitle="setTitle"
          :data="contentData"
        ></ModalContent>
      </Modal>
      <!--详情-->
      <DetailModal
        ref="detailModal"
        :detailShow="detailShow"
        :rowData="rowData"
        :indexData="indexModal.data"
        :isSpecial="echartIsSpecial"
        @closeModal="detailShow = false"
      >
      </DetailModal>
      <!-- 链路趋势详情弹窗 -->
      <LinkDetail
        ref="linkDetail"
        :linkDetailShow="linkDetailShow"
        :tabListModal="tabListModal"
        :indexData="tabListModal[0]"
        :tabsId="tabsId"
        :preIp="preIp"
        @closeLinkModal="closeLinkModal"
      ></LinkDetail>

      <!-- 端对端趋势详情弹窗 -->
      <PeerToPeerLinkDetail
        ref="peerToPeerLinkDetail"
        :peerToPeerLinkDetailShow="peerToPeerLinkDetailShow"
        :tabListModal="tabListModal"
        :indexData="tabListModal[0]"
        :tabsId="tabsId"
        @closePeerToPeerLinkModal="closePeerToPeerLinkModal"
      ></PeerToPeerLinkDetail>
      <!-- 收缩节点弹框 -->
      <!-- 收缩节点弹框，路径拓扑在此显示 -->

      <CompleteNodes
        ref="completeNodes"
        v-if="completeNodesShow"
        :completeNodeData="completeNodeData"
        :containerWidth="jtopoWidth + 60"
        :containerHeight="echartsHeight"
        @closeNodeModal="completeNodesShow = false"
        @getTaskList="getTaskList"
      ></CompleteNodes>
    </section>
  </div>
</template>
<script>
import '@/config/page.js';
import $ from '../../../common/jtopo/jquery.min.js';
import '../../../common/jtopo/jtopo-s.js';
import Loading from '../../../common/loading/loading.vue';
import global from '@/common/global.js';
import { newJtopo } from '../style/jtopo-editor.js';
import '@/timechange';
import locationreload from '@/common/locationReload';
// import * as echarts from 'echarts';
import * as echarts5 from 'echarts5';
import validate from "@/common/validate";
import topoBg from '@/assets/base64Img/img.js'
export default {
    name: 'index',
    components: {
        Loading,
        PathTopo: () => import('@/common/topoG6/pathtopo/PathTopo.vue'),
        ModalContent: () => import('@/common/topoG6/pathtopo/ModalContent.vue'),
        LinkDetail: () => import ('@/common/topoG6/pathtopo/LinkDetail.vue'),
        DetailModal:()=>import('@/common/topoG6/pathtopo/DetailModal.vue'),
        FiberTopo: () => import('@/common/topoG6/pathtopo/FiberTopoG6.vue'), 
        PeerToPeerLinkDetail: () => import ('@/common/topoG6/pathtopo/PeerToPeerLinkDetail.vue'),
        CompleteNodes: () => import('@/common/topoG6/pathtopo/CompleteNodes.vue'),
    },


    data() {
        return {
          currentSkin: sessionStorage.getItem('dark') || 1,
          forbidUpdate:false,
          modalWidth:null,
          completeNodesShow:false,
          showPeerToPeerLinkDetail:false,
          peerToPeerLinkDetailShow:false,
          fibreTopoLineLengthPercentage:100,
          isFirstStar:false,
          fibreType:0,
          hoverActive:null,
          clickActive:null,
          preIp:null,
        
          // 布局类型
          timer:null,
          leftTimer:null,
          rigthTimer:null,
          startLeftX:null,
          startRightX:null,
          leftWidth:14,
          rightWidth:210,
          leftNum:1,
          startLeft:1,
          totalNum:31,
          rightNum:30,
          startRight:1,
          isLeftDrag:false,
          isRightDrag:false,
          layoutType:1,
          isSearchShow:false,
          isLayoutShow:false,
          // 显示配置
          informationShow:false,
          // 配置缩略数据
          isDataType:false,
          // 图例显示
          legendShow:false,
          topoTabList:[],
          topoTabList2:[],
          activeTab:null,
          echartsHeight:500,
          menuHeight:null,
          isMore:true,
         backgroundImage:topoBg,
         pathTopologyLocXLocYMinMax:null,
         echartsBg:null,
         marginHeight:0,
          configData: {},
          legendData:[],
           tabListModal:[],
            linkDetailShow:false,
            checkFaultPathTopology: false,
            checkBoxSelect:false,
            fibreShow: false,
            contentData: {
                taskId: '',
                reportIds: []
            },
            activeValue:1,
            modalTitle: '',
            modalOmitTitle: '',
            inputValue: '',
            tabNlist: [],
            tabsShow: false,
            baImg: true,
            canvasH: '600',
            leftTrue: false,
            radioValue: '',
            topochakan1: 'topo_chakan1.png',
            topochakan2: 'topo_chakan2.png',
            topshezhi1: 'topo_shezhi1.png',
            topshezhi2: 'topo_shezhi2.png',
            topotulie1: 'topo_tulie1.png',
            topotulie2: 'topo_tulie2.png',
            topogaojing1: 'topo_gaojing1.png',
            topogaojing2: 'topo_gaojing2.png',
            dataList: {},
            topoData: {},
            pathTopoData: {},
            ipList: '',
            pre: '',
            permissionObj: {},
            defId: '0',
            setInter1: null,
            showWarning: true,
            loading: false,
            linkId: '',
            link: {},
            thistopo: {},
            showShuxing: 'sx',
            msgButtonModal: false,
            layoutButtonModal: false,
            configShowFieldResult:"",
            msgModal: [
              this.$t('comm_topo_device_ip'),
              this.$t('comm_topo_device_name'),
              this.$t('comm_topo_device_flow'),
              this.$t('comm_topo_device_delay'),
              this.$t('comm_topo_device_loss')
              ],
            layoutModel: 0,
            //type:0节点字段，1线路字段
            // 1 设备IP，2设备名称，3线路流速，4线路时延 ，5 线路丢包
            showType: 'all',
            msgModalData: [
                {
                    id: 1,
                    name: this.$t('comm_topo_device_ip'),
                    filedName: 'value',
                    className:"width90",
                    type: 0,
                    util: '',
                    isShow: true
                },
                {
                    id: 2,
                    name: this.$t('comm_topo_device_name'),
                    filedName: 'aliases',
                    className:"width110",
                    type: 0,
                    util: '',
                    isShow: true
                },
                {
                  // 流速
                    id: 3,
                    name: this.$t('comm_topo_device_flow'),
                    filedName: 'flowIntoSpeed',
                    className:"width90",
                    type: 0,
                    util: '',
                    isShow: true
                },
                 
                {
                  // 时延
                    id: 4,
                    name: this.$t('comm_topo_device_delay'),
                    filedName: 'delay',
                    className:"width110",
                    type: 0,
                    util: '',
                    isShow: true
                },
                 {
                  // 丢包
                    id: 5,
                    name: this.$t('comm_topo_device_loss'),
                    filedName: 'lossRate',
                    type: 0,
                    util: '',
                    isShow: true
                },
                
                 
            ],
            layoutModalData: [
                {
                    id: 1,
                    name: this.$t('树型图'),
                    filedName: 'value',
                    type: 0,
                    util: '',
                    isShow: true
                },
                {
                    id: 2,
                    name: this.$t('环型图'),
                    filedName: 'aliases',
                    type: 0,
                    util: '',
                    isShow: true
                },
                {
                    id: 3,
                    name: this.$t('星型图'),
                    filedName: 'aliases',
                    type: 0,
                    util: '',
                    isShow: true
                },
                {
                    id: 4,
                    name: this.$t('混合图'),
                    filedName: 'aliases',
                    type: 0,
                    util: '',
                    isShow: true
                }
            ],
            jtopoWidth:null,
            legendButtonModal: false,
            selectTopoButtonModal: false,
            selectModal: '',
            selectModalData: [],
            isdarkSkin: 0,
            tabList: [],
            tabList1: [],
            tabsId: null,
            seleId: null,
            myChart: null,
            // 纤维图数据
            nodeData: [],
            jtopoShow: true,
            eChartShow: false,
            btnShow: true,
            isEndToEnd: false,
            // 判断是否是第一次请求
            flag: true,
            timerInterval: null,
            alarmAutoTimerInterval: null,
            topologyMd5:"",
            rowData:{},
            indexModal: {
              data: {},
            },
            detailShow:false,
            echartIsSpecial: false,
            taskNum: "",
            topHeight:null,
            completeNodeData:{}
        };
    },
    computed: {
      marginBottom() {
        if(this.menuHeight > 780) {
          return 20
        }else {
          return 0
        }

      },
      legendWidth() {
        if(localStorage.getItem('locale') == 'en') {
          return '350px'
        }else {
          return '230px'
        }
      },
      getBtnTop1ImageSrc() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top1.png')
        : require('@/assets/light-skin-icon/topo-menu/light-btn-top1.png');
      },
      getBtnTop2ImageSrc() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top2.png')
        : require('@/assets/light-skin-icon/topo-menu/light-btn-top2.png');
      },
      getBtnTop3ImageSrc() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top3-1.png')
        : this.clickActive==3?
        require('@/assets/light-skin-icon/topo-menu/light-btn-top3-1.png')
        : require('@/assets/light-skin-icon/topo-menu/light-btn-top3-1.png');
      },
      getBtnTop3Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top3-1.png')
        : this.layoutModel==0
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top3-2.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top3-1.png')
      },
      getBtnTop3Image2Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top3-2.png')
        : this.layoutModel==2
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top3-3-3.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top3-4.png');
      },
      getBtnTop3Image3Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top3-3.png')
        : this.layoutModel==1
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top3-6.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top3-5.png');
      },
      getBtnTop16Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top16-1.png')
        : require('@/assets/light-skin-icon/topo-menu/light-btn-top17-1.png');
      },
      getBtnTop16Image2Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top16-2.png')
        : require('@/assets/light-skin-icon/topo-menu/light-btn-top17-3.png');
      },
      getBtnTop14ImageSrc() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top14.png')
        : this.clickActive==14
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top14.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top14.png');
      },
      getBtnTop4Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top4-1.png')
        : this.clickActive==4
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top4-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top4-1.png');
      },
      getBtnTop5Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top5-1.png')
        : this.clickActive==5
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top5-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top5-1.png');
      },
      getBtnTop6Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top6-1.png')
        : this.clickActive==6
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top6-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top6-1.png');
      },
      getBtnTop7Image2Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top7-2.png')
        : this.showWarning==true
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top7-2.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top7-2.png');
      },
      getBtnTop7Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top7-1.png')
        : this.showWarning==true
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top7-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top7-1.png');
      },
      getBtnTop8Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top8-1.png')
        : this.clickActive==8
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top8-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top8-1.png');
      },
      getBtnTop9Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top9-1.png')
        : this.informationShow
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top9-2.png'):
        require('@/assets/light-skin-icon/topo-menu/light-btn-top9-1.png');
      },
      getBtnTop10Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top10-1.png')
        : this.legendShow
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top10-2.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top10-1.png');
      },
      getBtnTop11Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top11-1.png')
        : !this.checkFaultPathTopology
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top11-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top11-1.png')
      },
      getBtnTop11Image2Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top11-2.png')
        : !this.checkFaultPathTopology
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top11-2.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top11-2.png')
      },
      getBtnTop12Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top12-1.png')
        : this.checkBoxSelect == true
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top12-1.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top12-1.png')
      },
      getBtnTop12Image2Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top12-2.png')
        : this.checkBoxSelect == true
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top12-2.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top12-2.png');
      },
      getBtnTop13Image1Src() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top13-1.png')
        : this.isDataType
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top13-2.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top13-1.png')
      },
      getBtnTop15ImageSrc() {
      return this.currentSkin == 1
        ? require('@/assets/topo-menu-icon/btn-top15.png')
        : this.clickActive === 15 
        ?require('@/assets/light-skin-icon/topo-menu/light-btn-top15.png')
        :require('@/assets/light-skin-icon/topo-menu/light-btn-top15.png');
      },
      
      getFileemptyImageSrc() {
      return this.currentSkin == 1
        ? require('@/assets/dashboard/fileempty.png')
        : require('@/assets/dashboard/fileempty2.png');
      }
    },
    watch: {
      
        // 监听数据
        configShowFieldResult:{
          handler(val) {
              var showTypeIdArray = [];
              var showTypeNameArray = [];
              if(val === 'all'){
                showTypeIdArray = this.msgModalData.map((item)=>{
                  return  item.id+"";
                });
              }else if(val === '0'){
                showTypeIdArray = [];
              }else{
                showTypeIdArray = val.split(",");
              }
              this.msgModalData.forEach((item)=>{
                 if(showTypeIdArray.indexOf(item.id + "") > -1){
                    showTypeNameArray.push(item.name);
                 }
              });
              this.msgModal = showTypeNameArray;
              this.msgModalChange(showTypeNameArray);
          },
          deep: true
        }
    },
    methods: {
      handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
       // 请求获链路详情数据
    async getTaskList(data) {
      // debugger
    
      console.log( data,'pathId, data');
      debugger
      // alert(1111111)
    

      const arr = data.target.split("_");
      let obj = {
        pageNo: 1,
        pageSize: 10,
        sourceIp: arr[0],
        topoId: this.tabsId,
        targetIp: data.targetIp,
        linkIds:data.linkIds,
        preIp: data.sourceIp,
      };
      let url = '/pathTopo/getTaskList';
      if(this.showPeerToPeerLinkDetail){
         obj.sourceIp= data.sourceIp;
         obj.targetIp= data.curIp;
         obj.preIp= data.preIp;
         url = '/pathTopo/getPeerToPeerTaskList';
      }
      try {
        const res = await this.$http.PostJson(url, obj);
        //    debugger
        const linkTable = res.data.records;

        //   // console.log(res.data[data.sourceIp],'得出来的数据')
        let usea = {
          preNodeIp: data.preIp,
          nodeIp: data.curIp,
          sourceIp: arr[0],
          topoId: this.tabsId,
          targetIp: data.targetIp,
          linkIds:data.linkIds,
          total: res.data.total,
        };
        // this.$emit('setPreIp',data.sourceIp)
          //  debugger
        this.setPreIp(data.sourceIp)

    
        // this.$emit("getLinkTable", linkTable, usea);
        this.getLinkTable(linkTable, usea)

        //    this.$emit('getQuery',obj)
      } catch (error) {
     ;
      }
    },
      updateCompleteNodesShow(data) {
        console.log(data,'data')
        this.completeNodesShow = true
        this.completeNodeData = data
      
        
      },
      setPreIp(ip) {
        this.preIp = ip

      },
      filterName(id) {
        // this.topoTabList2 = JSON.parse(JSON.stringify(this.topoTabList))
        // console.log(this.topoTabList2,'22222')
       let findResult = this.topoTabList2.find(item => item.id === id)
      //  console.log(findResult.topologyName,'findResult.name')
       return findResult.topologyName

      },
      // selectChange(value) {
      //   console.log(value)

      // },
      selectOpen(val) {
        if(val) {
          this.isSearchShow = false
        }

      },
      // 纤维图切换
     async fibreTypeChange(num) {
      if(this.fibreType == num) {
        return
      }
        this.fibreType = num
        let obj = {
          id:this.tabsId,
          directionVerticalTransverse:num

        }
       const res = await this.$http.PostJson('/pathTopo/updateTopoDirectionVerticalTransverse',obj)
       if(res.code == 1) {
        this.getECharts(this.tabsId)

       }


      },
      handleHoverActive(num) {
        this.hoverActive = num;
      },
      handleClickActive(num) {
        this.clickActive = num
      },
      // 获取首尾节点详情
    async getTopoHeadEndShowNodeNumById(id) {
     const res = await  this.$http.wisdomPost('/pathTopo/getTopoHeadEndShowNodeNumById',{id})
     if(res.code === 1 && res.data) {
      if(res.data.headShowNodeNum === null || res.data.endShowNodeNum === null ) {
         this.leftNum = this.totalNum
        this.rightNum = this.totalNum
        console.log(1)

      }else {
        // debugger
         this.leftNum = res.data.headShowNodeNum 
      this.rightNum = res.data.endShowNodeNum  
      console.log(this.leftNum,this.rightNum)

      }
     
     }
     let itemWidth = 206/(this.totalNum - 1)
     
     if(this.leftNum  === this.totalNum) {
      this.leftWidth = 206
    
     
     }else {
      this.leftWidth = (this.leftNum - 1) * itemWidth 
      

     }
     if(this.rightNum  === this.totalNum) {
      this.rightWidth = 206
      
     
     }else {
     
        this.rightWidth = (this.rightNum - 1) * itemWidth 

     }


     this.startLeft = this.leftNum
     this.startRight = this.rightNum
    
        

      },
      async saveTopoHeadEndShowNodeNum() {
        console.log(111)
        // debugger
        let obj = {
          headShowNodeNum:this.leftNum,
          endShowNodeNum:this.rightNum,
          id:this.tabsId
        }

        // debugger
        // this.$refs.PathTopo.forbidUpdate = true
        this.forbidUpdate = true
        
        await this.$http.PostJson('/pathTopo/saveTopoHeadEndShowNodeNum',obj)
        await this.goBack()
        setTimeout(() => {
          this.forbidUpdate = false
        },1000)
        // this.$refs.PathTopo.forbidUpdate = false
        

      },
      handleLeft() {
        console.log('左边')
        if(!this.isLeftDrag) {
          return
        }
        console.log('左边2')
        this.isLeftDrag = false
        // alert('左')
        if(this.leftNum != this.startLeft) {
          // 数据变化了才请求数据
          // debugger
          console.log('左边3')
        this.leftTimer =  setTimeout(() => {this.saveTopoHeadEndShowNodeNum()},3000)

        }
        

      },
      // handleMouseUp(e) {
      //    if(!this.leftDrag) {
      //     return
      //   }
       
      //   this.isLeftDrag = false
       
      //   // console.log(this.leftNum,this.startLeft)

      //   if(this.leftNum != this.startLeft) {
      //     // 数据变化了才请求数据
      //     // debugger
      //   this.leftTimer =  setTimeout(() => {this.saveTopoHeadEndShowNodeNum()},3000)

      //   }
        

      // },
       handleMouseDown(e) {
        // console.log(111,'zuobian')
        console.log(e,'eeeeeeeeeeeeeeeeeeee')
        this.isLeftDrag = true
          this. startLeftX = e.clientX
         

          
         
      },
      // 左节点拖动
      handleMouseMove(e) {
        // console.log(e,11111)

        
       
        if(this.isLeftDrag) {
           console.log(e,11111)
           if(this.leftTimer) {
            console.log('暂不保存数据')
            clearTimeout(this.leftTimer)
          }
        
          // if(e.clientX - this.startLeftX < 0) {
          //   // 往左拉减小
          //   // debugger
          //   this.leftWidth = this.leftWidth - (this.startLeftX - e.clientX )
          //   // console.log(22222)
            

          // }else {
          //    this.leftWidth = e.clientX - this.startLeftX + this.leftWidth;
          //     // console.log(22222)

          // }
          this.leftWidth = e.pageX - 104
          console.log(this.leftWidth,'this.leftNum')
          // if(this.leftWidth <= 14) {
          //   this.leftWidth = 14
          // }
          // 计算出每个刻度的距离
          let itemWidth = 206/(this.totalNum - 1 )
         let num = this.leftWidth /itemWidth
         this.leftNum = Math.ceil(num)
          
          if(this.leftWidth >= 206) {
            this.leftNum = this.totalNum
            this.leftWidth = 206

          }
          if(this.leftNum <= 1) {
            this.leftNum = 1
          }
        
          
          
         

        }
     
       
      },
      // 又节点拖动
      handleMouseMoveRight(e) {
          if(this.isRightDrag) {
             if(this.rightTimer) {
            clearTimeout(this.rightTimer)
          }
        
          // if(e.clientX - this.startRightX < 0) {
          //   // 往左拉减小
          //   // debugger
          //   this.rightWidth = this.rightWidth - (this.startRightX - e.clientX )
          //   console.log(22222)
            

          // }else {
          //    this.rightWidth = e.clientX - this.startRightX + this.rightWidth;
          //     console.log(22222)

          // }
           this.rightWidth = e.pageX - 104

          // if(this.leftWidth <= 14) {
          //   this.leftWidth = 14
          // }
          // 计算出每个刻度的距离
          let itemWidth = 206/(this.totalNum - 1 )
          console.log(itemWidth,this.rightWidth,'111')
         let num = this.rightWidth /itemWidth
         this.rightNum = Math.ceil(num)
         
        
          if(this.rightWidth >= 206) {
            this.rightNum = this.totalNum
            this.rightWidth = 206

          }
          if(this.rightNum <= 1) {
            this.rightNum = 1
          }
          // console.log(this.leftWidth,itemWidth,this.leftNum,'11')
          
          
         

        }
        

      },
    
      handleRightDown(e) {
        this.isRightDrag = true
        this.startRightX = e.clientX

      },
      handleRight(e) {
        if(!this.isRightDrag) {
          return
        }
        
        this.isRightDrag = false
         if(this.rightNum != this.startRight) {
          // 数据变化了才请求数据
          console.log('右边开始请求')
         
        this.rightTimer =  setTimeout(() => {this.saveTopoHeadEndShowNodeNum()},3000)

        }

      },
      
     
      
      
      dataTypeClose() {
        
    
        this.isDataType = false
        console.log(this.isDataType)
      },
      layoutChange(num) {
        this.layoutType = num

      },
      closeClick(str) {
      
         if(str === 'search') {
          this.isSearchShow = false
        }else if(str === 'information') {
          this.informationShow = false
        }else if(str === 'legend') {
          this.legendShow = false
        }
        console.log(this.informationShow,'dkdhg')

      },
      handleMouseOver(str) {
        if(str === 'search') {
          this.isSearchShow = true
          this.inputValue = ''
          this.isLayoutShow = false
          this.informationShow = false
          this.legendShow = false
           this.isDataType = false
        }else if (str === 'layout') {
          this.isLayoutShow = true
          this.isSearchShow = false
          this.informationShow = false
          this.legendShow = false
           this.isDataType = false
        }else if (str === 'information') {
          this.informationShow = true
          this.isSearchShow = false
          this.isLayoutShow = false
          this.legendShow = false
           this.isDataType = false
        }else if (str === 'legend') {
          this.legendShow = true
          this.isSearchShow = false
          this.isLayoutShow = false
          this.informationShow = false
           this.isDataType = false
        }else if (str === 'abbreviation') {
          this.isDataType = true
          this.legendShow = false
          this.isSearchShow = false
          this.isLayoutShow = false
          this.informationShow = false

        }

      },
      handleMouseLeave(str) {
       
        if(str === 'layout') {
        
          this.isLayoutShow = false
         
        }

      },
      closeLinkModal() {
        this.linkDetailShow = false
        // this.$refs.PathTopo.update()
      },
      closePeerToPeerLinkModal() {
        this.peerToPeerLinkDetailShow = false
        // this.$refs.peerToPeerLinkDetail.update()
      },
            // 获取图标配置
     async findInUseIconManageConfigureVoByType() {
      // debugger
      // type图标类型0路径拓扑图标，1物理拓扑图标,2路径图标
      try {
       const res = await this.$http.post('/iconmanage/findInUseIconManageConfigureVoByType',{type:0,currentSkin:this.currentSkin})
       if(res.code === 1) {
        this.configData = res.data
        this.handleData()
        
        // debugger
        
        
       


       }
      }catch(err) {}

      },
     
       // 处理图标数据
      handleData() {
        // debugger
        // debugger
        // showImage为null不显示图例
        this.legendData = this.configData.inUseIconManageConfigures.filter(item => {
          return item.showImage 

        })
         this.legendData.forEach(item => {
          item.showImage =  'data:image/png;base64,' + item.showImage 

        })
        // console.log(this.legendData,'SSSSSSSS')
        },
      // 点击link查看链路详情
      getDetailData(obj) {
        // console.log(obj)
        // debugger






      },

      getLinkTable(arr,obj) {
        // debugger
        if(this.showPeerToPeerLinkDetail){
          this.peerToPeerLinkDetailShow = true
          // debugger
          console.log(arr)
          this.tabListModal = arr
          setTimeout(() => {
            this.$refs.peerToPeerLinkDetail.getPathChartData(1,obj)
          }, 100)
        }else{
        this.linkDetailShow = true
        // debugger
        // console.log(arr)
        this.tabListModal = arr
        setTimeout(() => {
          this.$refs.linkDetail.getPathChartData(1,obj)
        }, 100)
        }

      },
        setTitle(val) {
            if (val && val.length > 50) {
                this.modalTitle = val;
                this.modalOmitTitle = val.substring(0, 50) + '...';
            } else {
                this.modalOmitTitle = val;
                this.modalTitle = val;
            }
        },
        onCancel() {
         
            this.fibreShow = false;
            this.contentData = {};
            this.$refs.modalContent.resetRegion();
            
        },
        // 全屏
        fullScreenButton() {
          
            this.$refs.PathTopo.fullScreen();
        },
        // 放大
        zoomInButton() {
            this.$refs.PathTopo.zoomIn();
        },
        // 缩小
        zoomOutButton() {
            this.$refs.PathTopo.zoomOut();
        },
        // 还原
       async goBack() {
         if(this.layoutModel == 2) {
            
            // 此方法暂时没用
              //  this.$refs.PathTopo.firstStarFn()

            }
            // 此方法暂时没用
            // this.$refs.PathTopo.reset();
            this.$store.dispatch('deleteNodeCoordinates',this.tabsId)
            this.getPathTopologyResult(this.tabsId)
            // this.getTopoHeadEndShowNodeNumById(this.tabsId)
            // 如果星形，探针剧中
            console.log(this.layoutModel,'layoutModel')
           
           
           
        },
          // 页面还原删除历史坐标

        // 导出
        exportButton() {
                    
            this.$refs.PathTopo.export();

        },
        // 搜索
        search() {
            this.$refs.PathTopo.search(this.inputValue);
        },
        // 框选
        getBoxSelectResult(val) {
          this.checkBoxSelect = val 
          this.$refs.PathTopo.toggleBoxSelectMode(val)
            // console.log(this.checkBoxSelect,11111111)
            

        },
        getFaultPathTopologyResult() {
          this.checkFaultPathTopology = !this.checkFaultPathTopology 
            if(this.tabsId){
            var faultFlag = 0;
            if (this.checkFaultPathTopology) {
                faultFlag = 1;
            }
            let self = this;
            this.$http
                .wisdomPost('/pathTopo/setFaultPathTopo', { id: this.tabsId, faultFlag: faultFlag })
                .then(res => {
                    if (res.code === 1) {
                        this.getPathTopologyResult(this.tabsId);
                        // this.getTopoHeadEndShowNodeNumById(this.tabsId)
                    } else {
                        this.$Messages.warning('勾选故障路径失败')
                        return
                    }
                })
                .finally(() => {
                });
            }
        },
        changeRadio(num) {
          // alert(1)
          
          let v = ''
          if(num === 2) {
           
           
            v = this.$t('path_fiber')
            this.radioValue = this.$t('path_fiber')
            this.activeValue = 2
            // 纤维图屏蔽端到端
            // this.topoTabList2 = this.topoTabList.filter(item => item.managemode != 2)
            this.updateTopoFiberOrRoutine(1);
          }else {
            v = this.$t('path_routine')
            this.radioValue = this.$t('path_routine')
            this.activeValue = 1
            // this.getPathTopoPulldown()
            // this.topoTabList2 = this.topoTabList
            this.updateTopoFiberOrRoutine(0);
          }
            this.msgButtonModal = false;
            this.legendButtonModal = false;
            this.layoutButtonModal = false;
            if (v == this.$t('path_fiber')) {
                this.eChartShow = true;
                this.jtopoShow = false;
                this.btnShow = false;
                this.getECharts(this.tabsId);
            } else {
                this.eChartShow = false;
                this.jtopoShow = true;
                this.btnShow = true;
                this.getPathTopologyResult(this.tabsId);
                // this.getTopoHeadEndShowNodeNumById(this.tabsId)
            }
            // 定时刷新
            // clearInterval(this.setInter1);
            // this.setInter1 = setInterval(function () {
            //     if (this.radioValue == this.$t('path_routine')) {
            //         self.getECharts(this.tabsId);
            //     } else {
            //         self.getPathTopologyResult(this.tabsId);
            //     }
            // }, 1000 * 120);
        },
        updateTopoFiberOrRoutine(fiberRoutineModel){
          let obj = {
            id:this.tabsId,
            fiberRoutineModel:fiberRoutineModel
          }
          const res = this.$http.PostJson('/pathTopo/updateTopoFiberOrRoutine',obj)
          if(res.code == 1) {
          }
        },
        getECharts(id) {
            let self = this;
            this.loading = true;
            this.baImg = true;
            this.findInUseIconManageConfigureVoByType()
            this.$http
                .wisdomPost('/pathTopo/getPathFibreTopoResult', { topologyId: id })
                .then(res => {
                    if (res.code === 1) {
                        if (res.data) {
                          this.echartsBg = res.data.backgroundImage ? 'data:image/png;base64,' + res.data.backgroundImage : res.data.backgroundImage 
                            self.nodeData = res.data;
                            self.fibreType = res.data.directionVerticalTransverse 
                            if(res.data.fibreTopoLineLengthPercentage){
                              self.fibreTopoLineLengthPercentage = res.data.fibreTopoLineLengthPercentage;
                            }
                            let height = 0;
                            if (self.nodeData && self.nodeData.children.length > 0) {
                                self.nodeData.children.map(r => {
                                    if (r.children) {
                                        height += r.children.length;
                                        self.leftTrue = true;
                                        return height;
                                    }
                                });
                                self.leftTrue = false;
                                self.baImg = false;
                                height += self.nodeData.children.length;
                            } else {
                                self.baImg = true;
                            }
                            if (height > 40) {
                                self.canvasH = height * 14;
                            }else{
                                self.canvasH='600'
                            }
                            if(self.isMore) {
                              self.canvasH = self.echartsHeight;
                            }
                            // 计算图形距离顶部距离
                          
                            self.marginHeight = (self.echartsHeight - self.canvasH) / 2;
                           
                        } else {
                            self.nodeData = [];
                        }
                    } else {
                        self.baImg = true;
                        self.loading = false;
                    }
                })
                .finally(() => {
                    self.loading = false;
                });
        },
        // 纤维图
        // init() {
        //     let data = {};
        //     if (this.nodeData && this.nodeData.children.length > 0) {
        //         this.nodeData.label = {
        //             color: '#fff'
        //         };
        //         data = this.nodeData.children.map(r => {
        //             r.label = {
        //                 color: this.configData.normalLineColor
        //             };
        //             if (r.children) {
        //                 r.children.map(e => {
        //                     if (e.state == 1000) {
        //                         e.lineStyle = {
        //                             color: this.configData.normalLineColor

        //                         };
        //                         // 节点字体颜色
        //                         e.label = {
        //                             color: this.configData.normalLineColor
        //                         };
        //                         // 圆圈颜色
        //                         e.itemStyle = {
        //                             color: this.configData.normalLineColor,
        //                             borderColor: this.configData.normalLineColor
        //                         };
        //                     } else if (e.state == 1) {
        //                       // 中断
        //                         e.lineStyle = {
        //                             color:  this.configData.breakLineColor

        //                         };
        //                         // 节点字体颜色
        //                         e.label = {
        //                             color: this.configData.breakLineColor
        //                         };
        //                         // 圆圈颜色
        //                         e.itemStyle = {
        //                             color: this.configData.breakLineColor,
        //                             borderColor: this.configData.breakLineColor
        //                         };
        //                     } else {
        //                         e.lineStyle = {
        //                             color: this.configData.degradationLineColor
        //                         };
        //                         // 节点字体颜色
        //                         e.label = {
        //                             color:this.configData.degradationLineColor
        //                         };
        //                         // 圆圈颜色
        //                         e.itemStyle = {
        //                             color: this.configData.degradationLineColor,
        //                             borderColor: this.configData.degradationLineColor
        //                         };
        //                     }
        //                     return e;
        //                 });
        //                 return r;
        //             } else {
        //                 if (r.state == 1000) {
        //                     r.lineStyle = {
        //                         color: this.configData.normalLineColor
        //                     };
        //                     // 节点字体颜色
        //                     r.label = {
        //                         color: this.configData.normalLineColor
        //                     };
        //                     // 圆圈颜色
        //                     r.itemStyle = {
        //                         color: this.configData.normalLineColor,
        //                         borderColor: this.configData.normalLineColor
        //                     };
        //                 } else if (r.state == 1) {
        //                     r.lineStyle = {
        //                         color: this.configData.breakLineColor,
        //                     };
        //                     // 节点字体颜色
        //                     r.label = {
        //                         color: this.configData.breakLineColor,
        //                     };
        //                     // 圆圈颜色
        //                     r.itemStyle = {
        //                         color: this.configData.breakLineColor,
        //                         borderColor: this.configData.breakLineColor,
        //                     };
        //                 } else {
        //                     r.lineStyle = {
        //                         color: this.configData.degradationLineColor
        //                     };
        //                     // 节点字体颜色
        //                     r.label = {
        //                         color: this.configData.degradationLineColor
        //                     };
        //                     // 圆圈颜色
        //                     r.itemStyle = {
        //                         color: this.configData.degradationLineColor,
        //                         borderColor: this.configData.degradationLineColor
        //                     };
        //                 }
        //                 return r;
        //             }
        //         });
        //     }

        //     if (this.myChart) {
        //         if(echarts5){
        //             echarts5.dispose(this.myChart)
        //         }
        //         this.myChart.clear();
        //     }
        //     this.myChart = echarts5.init(document.getElementById('mainChart'));
        //     let option = {
        //         tooltip: {
        //             trigger: 'item',
        //             borderWidth: '0',
        //             backgroundColor: '#252729',
        //             triggerOn: 'mousemove',
        //             formatter: '{b}',
        //             textStyle: {
        //                 color: '#fff'
        //             }
        //         },
        //         series: [
        //             {
        //                 type: 'tree',
        //                 data: [this.nodeData],
        //                 top: '5%',
        //                 // this.leftTrue == true ? '7%' : '15%',
        //                 left: '10%',
        //                 bottom: this.fibreType === 1 ? '33%' : '2%',//需要根据横向竖向动态设置大小
        //                 right: this.fibreTopoLineLengthPercentage+'%',
        //                 symbolSize: 7,
        //                 layout: 'orthogonal',
        //                 orient: this.fibreType === 1 ?   "vertical" : "horizontal",
        //                 //控制父节点
        //                 label: {
        //                     position: this.fibreType === 1 ? 'right' : 'left',  //需要根据横向竖向判断位置
        //                     verticalAlign: 'middle',
        //                     align:this.fibreType === 1 ? 'left' : 'right',
        //                     rotate:this.fibreType === 1 ? 270 : 0, //需要根据横向竖向判断位置
        //                     distance: this.fibreType === 1 ? 15 : 0  //垂直排列才需要
        //                     // color: '#00FFEE',
        //                 },
        //                 // 子节点
        //                 leaves: {
        //                     label: {
        //                       //  width:300,
        //                         position:this.fibreType === 1 ? 'bottom' : 'right',
                               
                              
        //                         align: 'left',
        //                         rotate:this.fibreType === 1 ? 270 : 0,
        //                         distance: this.fibreType === 1 ? 3 : 5,
        //                         formatter: function (value, index) {
        //                             return value.data.name.length > 30 ? value.data.name.substring(0, 30) + '...' : value.data.name;
        //                         }
        //                     }
        //                 },
        //                 //实心圈
        //                 symbol: 'circle',
        //                 // itemStyle: {
        //                 //     borderColor: '#ccc'
        //                 // },
        //                 expandAndCollapse: true,
        //                 animationDuration: 550,
        //                 animationDurationUpdate: 750
        //             }
        //         ]
        //     };
        //     this.myChart.setOption(option, true, true);
        //     this.myChart.on('click', param => {
        //       // // console.log(param)
        //       // debugger
        //         this.contentData.name = param.data.name;
        //         this.contentData.taskId = param.data.taskId;
        //         this.contentData.reportIds = param.data.reportIds;
        //         if (param.data.state == 1 || param.data.state == 2 || param.data.state == 3) {
        //             this.fibreShow = true;
        //         }else{
        //             if(param.data.state){
        //                 // this.$Messages.warning(this.$t('pathtopo_path_not_faulty'))
        //                 let _self = this;
        //                 // 查看详情
        //                 if (sessionStorage.getItem("delayPs")) {
        //                   sessionStorage.removeItem("delayPs");
        //                 }
        //                 this.$store.commit("setdelayLTlevel", "");
        //                 this.$store.commit("updateDelayLossHistory", -1);
        //                 this.$store.commit("setflowTlevel", "");
        //                 this.$store.commit("updateFlowHistory", -1);
        //                 let destIp = param.data.name;
        //                 _self.indexModal.data = [];
        //                 // 暂时屏蔽
        //                 if(param.data.taskId && param.data.taskId != '') {

        //                       _self.detailShow = true;

        //                 }else {
        //                   // 收起的禁止点击
        //                    this.$Messages.warning(this.$t('task_not_support'))

        //                 }
                     
        //                 // _self.taskNum = param.data.taskNum;
        //                 _self.rowData = {"taskId":param.data.taskId}
        //               // console.log("indexData destIp",destIp)
        //                 _self.indexModal.data = {
        //                   flowChartType: 1,
        //                   // respondIp: param.data.respondIp,
        //                   sourceIp: param.data.sourceIp,
        //                   // destip通过空格分割并且取最后一位
        //                   destIp: destIp.split(" ")[destIp.split(" ").length - 1],
        //                   respondIp: '',
        //                   startTime: "",
        //                   endTime: "",
        //                   // eventStart: param.data.eventStart || "",
        //                   // eventEnd: param.data.eventEnd || "",
        //                   eventStart: "",
        //                   eventEnd: "",
        //                   id:param.data.taskId,
        //                 };
        //                 // this.echartIsSpecial = param.data.isSpecial == 1 ? true : false;
        //                 this.echartIsSpecial = false;
        //             }
        //         }
        //     });
        // },
        nodesClick(param) {
            
                this.contentData.name = param.data.name;
                this.contentData.taskId = param.data.taskId;
                this.contentData.reportIds = param.data.reportIds;
                if (param.data.state == 1 || param.data.state == 2 || param.data.state == 3) {
                    this.fibreShow = true;
                }else{
                    if(param.data.state){
                        // this.$Messages.warning(this.$t('pathtopo_path_not_faulty'))
                        let _self = this;
                        // 查看详情
                        if (sessionStorage.getItem("delayPs")) {
                          sessionStorage.removeItem("delayPs");
                        }
                        this.$store.commit("setdelayLTlevel", "");
                        this.$store.commit("updateDelayLossHistory", -1);
                        this.$store.commit("setflowTlevel", "");
                        this.$store.commit("updateFlowHistory", -1);
                        let destIp = param.data.name;
                        _self.indexModal.data = [];
                        // 暂时屏蔽
                        if(param.data.taskId && param.data.taskId != '') {

                              _self.detailShow = true;

                        }else {
                          // 收起的禁止点击
                           this.$Messages.warning(this.$t('task_not_support'))

                        }
                     
                        _self.rowData = {"taskId":param.data.taskId}
                        _self.indexModal.data = {
                          flowChartType: 1,
                          sourceIp: param.data.sourceIp,
                          // destip通过空格分割并且取最后一位
                          destIp: destIp.split(" ")[destIp.split(" ").length - 1],
                          respondIp: '',
                          startTime: "",
                          endTime: "",
                          taskType:param.data.configType,
                          eventStart: "",
                          eventEnd: "",
                          id:param.data.taskId,
                        };
                        this.echartIsSpecial = false;
                    }
                }
          
        },
       windowResize() {
        if(this.myChart) {
            this.myChart.resize()
        }
    },

        // 路劲常规
        tuliclear() {
            this.legendButtonModal = false;
        },
        xxzsclear() {
            this.msgButtonModal = false;
        },
        layoutclear() {
            this.layoutButtonModal = false;
        },
        tptclear() {
            this.selectTopoButtonModal = false;
        },
        queryClick() {
            // console.log(123)
            clearInterval(this.setInter1);
            this.$router.push({ path: '/pathtopo-topolist' });
        },
        selectClick() {
            this.selectTopoButtonModal = true;
            this.msgButtonModal = false;
            this.legendButtonModal = false;
            this.layoutButtonModal = false;
        },
        showWarnings() {
          
            this.showWarning = !this.showWarning;
           
        },
        msgClick() {
            this.selectTopoButtonModal = false;
            this.msgButtonModal = true;
            this.legendButtonModal = false;
            this.layoutButtonModal = false;
        },
        layoutClick() {
            this.selectTopoButtonModal = false;
            this.msgButtonModal = false;
            this.legendButtonModal = false;
            this.layoutButtonModal = true;
        },
        legendClick() {
            this.selectTopoButtonModal = false;
            this.msgButtonModal = false;
            this.legendButtonModal = true;
            this.layoutButtonModal = false;
        },
        // selectModalChange(e, topoId) {
        //     let id = '';
        //     for (let i = 0; i < this.selectModalData.length; i++) {
        //         if (this.selectModalData[i].topologyName == e) {
        //             id = this.selectModalData[i].id;
        //         }
        //         if (topoId && topoId == this.selectModalData[i].id) {
        //             this.selectModal = this.selectModalData[i].topologyName;
        //         }
        //     }
        //     if (topoId) {
        //         id = topoId;
        //     }
        //     //
        //     let that = this;
        //     if (this.radioValue == this.$t('path_routine')) {
        //         this.getECharts(id);
        //     } else {
        //         //重新获取拓扑图数据
        //         this.getPathTopologyResult(id);
        //     }

        //     clearInterval(that.setInter1);
        //     that.setInter1 = setInterval(function () {
        //         that.getPathTopologyResult(id);
        //     }, 1000 * 120);
        //     //重新渲染
        //     // this.getmousemove();
        // },
        msgModalChange(val) {
          var _this = this;
          var size = val.length;
          var configShowField = '0';
            if (size == 0) {
                this.showType = '';
            }else if(size === this.msgModalData.length){
                this.showType = 'all';
                configShowField = "all";
            }else{
              this.showType = val;
              var configFieldList = [];
              this.msgModalData.forEach((item)=>{
                  if(val.indexOf(item.name) > -1){
                      configFieldList.push(item.id);
                  }
              });
              configShowField = configFieldList.join(",");
            }
            this.getmousemove(this.tabsId);
            this.$refs.PathTopo.showType = this.showType;

            setTimeout(() => {
                _this.saveConfigShowField(configShowField);
            }, 1000);
            
        },
        saveConfigShowField(configShowField){
            this.$http
                .wisdomPost('/pathTopo/updateConfigShowField',{"topologyId" : this.tabsId, "configShowField" : configShowField})
                .then(res => {
                  console.log("保存成功" , res);
                }).finally(() => {
             });
        },
        // 布局方式
        layoutModalChange(val) {
          if(this.layoutModel == val) {
            return
          }
         
          this.layoutModel = val;
          if(val === 2) {
            // this.$refs.PathTopo.firstStarFn()
          }
            // 切换布局方式
            // let id = '';
            // if (val === this.$t('树型图')) {
            //   id = 0
            // } else if (val === this.$t('环型图')) {
            //   id = 1
            // } else if (val === this.$t('星型图')) {
            //   id = 2
            // } else if (val === this.$t('混合图')) {
            //   id = 3
            // }
            this.$http
                .wisdomPost('/pathTopo/updateLayoutModel',{topologyId : this.tabsId, layoutModel : val})
                .then(res => {
                  if (res && res.code === 1) {
                    // this.$Message.success(this.$t("comm_modified_successfully"));
                    // this.$refs.PathTopo.reset();
                    // g6开发暂时注释
                    // this.$refs.PathTopo.reset();
                    this.$store.dispatch('deleteNodeCoordinates',this.tabsId)
                    this.getPathTopologyResult(this.tabsId)
                    // this.getTopoHeadEndShowNodeNumById(this.tabsId)
                    
                  } else {
                    this.$Message.warning(res.msg);
                  }
                })
        },
        //获取下拉任务
        getPathTopoPulldown() {
          // debugger
            let self = this;
            this.tabsShow = false;
            
            self.$http
                .wisdomPost('/pathTopo/getPathTopoPulldown')
                .then(res => {
                    if (res && res.code === 1) {
                        if (res.data.list && res.data.list.length > 0) {
                          // debugger
                            self.tabList = res.data.list;
                            self.tabNlist = res.data.list;
                            self.topoTabList = res.data.list;
                            self.topoTabList2 = JSON.parse(JSON.stringify(this.topoTabList))
                           
                            let defId = res.data.def?.defTopoid;
                            var def = '';
                            if (res.data.def) {
                                def = res.data.def.defTopoid;
                                res.data.list.forEach(v => {
                                  if (v.id == def) {
                                      if (v.managemode == 2) {
                                          this.isEndToEnd = true;
                                      }
                                  }
                                })
                            } else {
                                def = res.data.list[0].id;
                                if(res.data.list[0].managemode == 2) {
                                  self.isEndToEnd = true;
                                }
                            }
                            for (let i = 0; i < res.data.list.length; i++) {
                                if (res.data.list[i].id == def) {
                                    self.thistopo = res.data.list[i];
                                    self.defId = res.data.list[i].id;
                                }
                            }
                            self.tabList = res.data.list;
                            if (this.documentW > 1536) {
                                if (self.tabList.length >= 5) {
                                    let len = self.tabList.length;
                                    self.tabList1 = self.tabList.slice(4, len);
                                    self.tabList = self.tabList.slice(0, 4);
                                    self.tabList1.map(r => {
                                        if (r.topologyName.length > 7) {
                                            r.name = validate.getSizeIndexToStr(r.topologyName,6) + '...';
                                        } else {
                                            r.name = r.topologyName;
                                        }
                                    });
                                    self.tabList.map(r => {
                                        if (r.topologyName.length > 7) {
                                            r.name = validate.getSizeIndexToStr(r.topologyName,6) + '...';
                                        } else {
                                            r.name = r.topologyName;
                                        }
                                    });
                                    self.seleId = self.tabList1[0].id;
                                } else {
                                    self.tabList = self.tabList.slice(0, self.tabNlist.length);
                                    self.tabList.map(r => {
                                        if (r.topologyName.length > 7) {
                                            r.name =validate.getSizeIndexToStr(r.topologyName,6) + '...';
                                        } else {
                                            r.name = r.topologyName;
                                        }
                                    });
                                }
                            } else {
                                if (self.tabList.length >= 3) {
                                    let len = self.tabList.length;
                                    self.tabList1 = self.tabList.slice(2, len);
                                    self.tabList = self.tabList.slice(0, 2);
                                    self.tabList1.map(r => {
                                        if (r.topologyName.length > 7) {
                                            r.name = validate.getSizeIndexToStr(r.topologyName,6) + '...';
                                        } else {
                                            r.name = r.topologyName;
                                        }
                                    });
                                    self.tabList.map(r => {
                                        if (r.topologyName.length > 7) {
                                            r.name = validate.getSizeIndexToStr(r.topologyName,6) + '...';
                                        } else {
                                            r.name = r.topologyName;
                                        }
                                    });
                                    self.seleId = self.tabList1[0].id;
                                } else {
                                    self.tabList = self.tabList.slice(0, self.tabNlist.length);
                                    self.tabList.map(r => {
                                        if (r.topologyName.length > 7) {
                                            r.name = validate.getSizeIndexToStr(r.topologyName,6) + '...';
                                        } else {
                                            r.name = r.topologyName;
                                        }
                                    });
                                }
                            }
                            let jtopoId = JSON.parse(sessionStorage.getItem('jtopo'));
                            let jtopoType = JSON.parse(sessionStorage.getItem('jtopoType'));
                            if (jtopoId) {
                                // self.jump(jtopoId, jtopoType);
                                // debugger
                               
                                this.jumpGetTopoFiberOrRoutineById(jtopoId,jtopoType);
                               
                            } else if (defId) {
                             
                                this.jumpGetTopoFiberOrRoutineById(defId,null);
                                
                                // 从图例拓扑管理进入
                                // self.jump(defId, null);
                            } else {
                                self.seleId = self.tabList1[0]?.id;
                                self.tabsId = self.tabList[0].id;
                                self.getPathTopologyResult(self.tabList[0].id);
                                // self.getTopoHeadEndShowNodeNumById(self.tabList[0].id)
                            }
                            // 显示切换
                            self.tabsShow = true;
                            sessionStorage.removeItem('jtopo')
                              sessionStorage.removeItem('jtopoType')
                              // self.topoTabList2 = self.topoTabList
                            

                        } else {
                            self.tabsShow = false;
                            self.jtopoShow = false;
                            // self.topoTabList2 = self.topoTabList.fillter(item => item.managemode != 2)
                        }
                    }
                })
                .finally(() => { });
        },
        jumpGetTopoFiberOrRoutineById(id, jtopoType) {
          // debugger
          // if(jtopoType && jtopoType != 2){
          //   this.getPathTopologyResult(id)
          //   this.radioValue = this.$t('path_routine')
          //   this.jtopoShow = true
          //   this.eChartShow = false
          //   this.activeValue = 1
          //   return
          // }
         
          this.$http
            .wisdomPost('/pathTopo/getTopoFiberOrRoutineById', { id })
            .then(res => {
                if (res.code === 1) {
                    if(res.data.fiberRoutineModel==1){
                      this.activeValue = 2
                      this.radioValue = this.$t('path_fiber');
                      this.jump(id, 2,jtopoType);
                    }else{
                      this.activeValue = 1
                      this.radioValue = this.$t('path_routine')
                      this.jump(id, null,jtopoType);
                    }
                } else {
                      this.activeValue = 1
                      this.radioValue = this.$t('path_routine')
                      this.jump(id, null,jtopoType);
                }
            })
            .finally(() => {
            });
      },
        // 反向渲染 切换页
        jump(vId, type,jtopoType = null) {
          // debugger
         
            let b = this.tabList.find(r => {
                if (r.id == vId) {
                    this.tabsId = vId;
                    return r;
                }
            });
            let a = this.tabList1.find(r => {
                if (r.id == vId) {
                    this.tabsId = vId;
                    this.seleId = vId;
                    return r;
                }
            });
            // 显示按键
            this.btnShow = true;
            // 如果type==2  是纤维图 其他情况是拓扑图
            if(jtopoType) {
              // 仪表盘构件跳转
              // 1,常规 2，纤维
              if(jtopoType == 1) {
                this.radioValue = this.$t('path_routine')
                this.jtopoShow = true
                this.eChartShow = false
                this.activeValue = 1
                this.getPathTopologyResult(vId,jtopoType)
            
                
              }else {
                this.getECharts(vId)
                this.jtopoShow = false
                this.eChartShow = true
                this.activeValue = 2
                
                
              }

            } else {
               if (type == 2 && !this.isEndToEnd) {
                this.eChartShow = true;
                this.jtopoShow = false;
                this.btnShow = false;
                this.radioValue=this.$t('path_fiber')
                this.activeValue = 2;
                if (a || b) {
                    this.getECharts(vId);
                } else {
                    this.tabsId = this.tabList[0].id;
                    this.getECharts(this.tabList[0].id);
                }
            } else {
                this.eChartShow = false;
                this.jtopoShow = true;
                this.btnShow = true;
                this.radioValue=this.$t('path_routine')
                this.activeValue = 1

                if (a || b) {
                    this.getPathTopologyResult(vId);
                    // this.getTopoHeadEndShowNodeNumById(vId)
                } else {
                    this.tabsId = this.tabList[0].id;
                    this.getPathTopologyResult(this.tabList[0].id);
                    // this.getTopoHeadEndShowNodeNumById(this.tabList[0].id)
                }
            }
              
            }
           
        },
        getPathTopologyResult(id,jtopoType=null) {
          // debugger
            this.$http.wisdomPost('/audit/detail', { tab: this.$t('topo_path_topology') }).then(res => { });
            let self = this;
            self.loading = true;
            this.baImg = false;
            this.showPeerToPeerLinkDetail = false;
            this.findInUseIconManageConfigureVoByType()
            let obj = {

              topologyId:id,
              flag: jtopoType ? false : this.flag,
              width:this.jtopoWidth,
              height:this.menuHeight,
              pathTopoCoorType:0,

            }
            // this.menuHeight = top.document.body.clientHeight - 108;
            this.$http
                .wisdomPost('/pathTopo/getPathTopologyResult', obj)
                .then(res => {
                    if (res.code === 1) {
                        if (res.data) {
                          // 配置显示字段信息
                          this.configShowFieldResult = res.data.configShowField;
                          this.totalNum = res.data.maxNodeHopsNum
                          this.getTopoHeadEndShowNodeNumById(id)



                            self.dataList = res.data;
                            console.log('res.data==>',res.data);
                            this.layoutModel = res.data.layoutModel;
                           if(this.layoutModel == 3) {
                            this.layoutModel = 0
                           }
                         
                            console.log('this.layoutModel==>',this.layoutModel);
                            this.pathTopologyLocXLocYMinMax = res.data.pathTopologyLocXLocYMinMax || null
                            this.backgroundImage = res.data.backgroundImage ? 'data:image/png;base64,' + res.data.backgroundImage : res.data.backgroundImage 
                            this.pathTopoData = [];
                            this.pathTopoData = res.data;
                            let pathTopologyCoordinate = res.data.pathTopologyCoordinate;
                            if(pathTopologyCoordinate && pathTopologyCoordinate.peertopeerTopology){
                                this.showPeerToPeerLinkDetail = true;
                            }
                            if (1 == res.data.faultFlag) {
                                this.checkFaultPathTopology = true;
                            } else {
                                this.checkFaultPathTopology = false;
                            }
                            if (res.data.nodeList.length == 0 || res.data.nodeList === null) {
                      
                                self.baImg = true;
                            } else {
                            
                                self.baImg = false;
                            }
                            if( res.data.nodeList == null) {
                               self.baImg = true;

                            }
                            if (this.flag == true) {
                              // debugger
                                this.flag = false;
                                // 判断是不是大于50
                                if (res.data.showFlag == 1 && !jtopoType) {
                                    self.eChartShow = true;
                                    self.jtopoShow = false;
                                    self.btnShow = false;
                                    self.radioValue = this.$t('path_fiber');
                                    self.activeValue = 2
                                    self.getECharts(this.tabsId);
                                } else {
                                    self.eChartShow = false;
                                    self.jtopoShow = true;
                                    self.btnShow = true;
                                    self.radioValue = this.$t('path_routine');
                                    self.activeValue = 1
                                    // this.getmousemove(this.tabsId);
                                }
                            } else {
                                self.eChartShow = false;
                                self.jtopoShow = true;
                                self.btnShow = true;
                                self.radioValue = this.$t('path_routine');
                                // this.getmousemove(this.tabsId);
                            }
                        } else {
                            self.dataList = [];
                            self.baImg = true;
                        }
                    }else {
                       self.baImg = true;
                    }
                })
                .catch(err => {
                    self.loading = false;
                })
                .finally(() => {
                    self.loading = false;
                    // let jtopoId = JSON.parse(sessionStorage.getItem('jtopo'));
                    // if (jtopoId) {
                    //     sessionStorage.removeItem('jtopo');
                    // }
                });
        },
        getmousemove(val) {
            this.pathTopoData = [];
            this.pathTopoData = this.dataList;
        },
        getPathTopoData(scene) {
            let that = this;
            if (scene) {
                scene.mousemove(function (e) {
                    var element = e.target;
                    if (element && element.elementType == 'link') {
                        var linkIds = element.prop.linkIds && element.prop.linkIds.join(',');
                        if (that.topoData && that.topoData.linkIds == linkIds && that.topoData.curIp == element.prop.curIp) {
                            showToolips(that.topoData, element, e);
                            return;
                        }
                        if (element.nodeZ.prop.name.indexOf('不可信') != -1 || element.nodeZ.prop.name == '*') {
                            showToolips(null, element, e);
                            return;
                        }
                        var nodeAip = gitlastnodea(element.nodeA);
                        if (nodeAip.indexOf('\n') != -1) {
                            nodeAip = nodeAip.substring(0, nodeAip.indexOf('\n'));
                        }
                        var iplist = nodeAip + '-' + element.prop.curIp;
                        that.ipList = iplist;

                        that.pre = nodeAip;
                        that.topoData = data;
                        this.loading = true;
                        let data = {
                            linkIds: linkIds,
                            preIp: nodeAip,
                            curIp: element.prop.curIp,
                            ipList: that.ipList
                        };
                        that.$http.wisdomPost('/pathTopo/getPathTopoData', data).then(res => {
                            if (res.code === 1) {
                                if (res.data) {
                                    that.link = res.data;
                                    showToolips(res.data, element, e);
                                }
                            }
                            that.loading = false;
                        });
                    } else {
                        showToolip('hide');
                    }
                });
            }
            //判断后一个点是否是不可信,遇到*号再往下，返回true则存在，线路后为不可信则不显示数据
            function gitnextnodez(nodeZ) {
                var nodezname = nodeZ.prop.name;
                if (!nodezname) {
                    return true;
                }
                if (nodezname == '*') {
                    return gitnextnodez(nodeZ.outLinks[0].nodeZ);
                } else {
                    if (nodezname.indexOf('不可信') != -1) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            //判断前一个点是否是不可信,遇到*号再往上，返回true则是，线路前为不可信则显示不可信前一数据
            function gitlastnodea(nodeA) {
                var nodeAname = nodeA.prop.name;
                // if(!nodeAname){
                //     return true;
                // }
                if (nodeAname == '*' || nodeAname.indexOf('不可信') != -1) {
                    return gitlastnodea(nodeA.inLinks[0].nodeA);
                } else {
                    return nodeAname;
                }
            }
            //获取不可信节点后一条链路的所有可信链路
            function gitnextnodeA(nodeA, iplist, zip) {
                var nodeAname = nodeA.prop.name;
                if (!nodeAname) {
                    return;
                }
                //链路上一个节点关联的链路获取可信节点
                var links = nodeA.inLinks;
                var nodess = [];
                links.forEach(item => {
                    var node = item.nodeA.prop.name;
                    if (node.indexOf('不可信') == -1 && node != '*') {
                        nodess.push(node);
                    }
                });
                //判断是否存在可信节点，存在则追加返回
                if (nodess.length > 0) {
                    nodess.forEach(item => {
                        if (iplist) {
                            iplist += ',';
                        }
                        iplist += item + '-' + zip;
                    });
                    that.ipList = iplist;
                    return;
                } else {
                    //不存在则继续
                    links.forEach(item => {
                        var node = item.nodeA.prop.name;
                        if (node.indexOf('不可信') != -1 || node == '*') {
                            gitnextnodeA(item.nodeA, iplist, zip);
                        }
                    });
                }
            }

            function showToolips(links, element, e) {
                /**
                     delay: 延时
                     flowIntoSpeed: 入流速
                     flowOutSpeed: 出流速
                     lossRate: 丢包率
                     */
                let delay = '';
                let flowIntoSpeed = '';
                let flowOutSpeed = '';
                let lossRate = '';
                let curip = '';
                let preip = '';

                if (links) {
                    delay = that.link.delay;
                    flowIntoSpeed = that.link.flowIntoSpeed;
                    flowOutSpeed = that.link.flowOutSpeed;
                    lossRate = that.link.lossRate;
                    curip = links.curIp ? links.curIp : element.prop.curIp;
                    preip = links.preIp ? links.preIp : that.pre;
                } else {
                    delay = null;
                    flowIntoSpeed = null;
                    flowOutSpeed = null;
                    lossRate = null;
                }
                var info = {
                    // "ip": element.prop.name || '无',
                    上联ip: preip || '--',
                    下联ip: curip || '--',
                    时延: (delay || '--') + 'ms',
                    丢包: lossRate == null ? '--' + '%' : lossRate + '%',
                    入流速: flowIntoSpeed != null ? flowSize(flowIntoSpeed) : '--',
                    出流速: flowOutSpeed != null ? flowSize(flowOutSpeed) : '--'
                };
                var content = '';
                var width = 0;
                var len;
                $.each(info, function (k, v) {
                    content += '<h5>&nbsp;&nbsp;' + k + ':' + v + '</h5>';
                    len = 300;
                });
                showToolip({
                    x: e.pageX,
                    y: e.pageY,
                    width: width,
                    content: content
                });
            }

            function flowSize(size, unit = true, local = false) {
                if (typeof size !== 'number' && size < +0) {
                    throw new TypeError('The flow must be a positive number.');
                }
                size = Math.max(Math.round(size), 0);
                const result = size > 1000 ? (size / 1000 > 1000 ? (size / (1000 * 1000) > 1000 ? (size / (1000 * 1000 * 1000) > 1000 ? (size / (1000 * 1000 * 1000 * 1000) > 1000 ? [(size / (1000 * 1000 * 1000 * 1000 * 1000)).toFixed(2), 'Pbps'] : [(size / (1000 * 1000 * 1000 * 1000)).toFixed(2), 'Tbps']) : [(size / (1000 * 1000 * 1000)).toFixed(2), 'Gbps']) : [(size / (1000 * 1000)).toFixed(2), 'Mbps']) : [(size / 1000).toFixed(2), 'Kbps']) : [size, 'bps'];
                result[0] = Number.parseFloat(result[0]);
                if (local) {
                    result[0] = result[0].toLocaleString();
                }
                return unit ? result.join('') : result[0];
            }

            function showToolip(options) {
                var temp = $(document);
                var tipsWin = temp.find('div[name=topo_tips]').hide();
                //var tipsWin = $("#topoTips")
                var tips_body = tipsWin.find('.panel-body');
                var op = options || {};
                var x = op.x;
                var y = op.y;
                var width = op.width || 200;
                var content = op.content || '';
                if (content) {
                    tips_body.show();
                    tips_body.html(content);
                    tipsWin
                        .css({
                            left: x,
                            top: y,
                            width: width,
                            padding: '10px',
                            'font-size': '14px',
                            color: '#636c82',
                            border: '1px solid #d8dbe6',
                            background: '#f1f6fe',
                            'text-align': 'left'
                        })
                        .show();
                } else {
                    tips_body.hide();
                    tipsWin.hide();
                }
            }
        },
        // 切换功能
        // ulChange(id) {
        //     //端到端不需要纤维图
        //     const findResult =  this.tabList.find(item => item.id === id)
        //     if(findResult) {
        //       this.isEndToEnd = findResult.managemode == 2 ? true : false
        //     }
        //     this.tabsId = id;
        //     // // console.log(this.tabsId,'id---------------------')
        //     if (this.radioValue == this.$t('path_fiber')) {
        //         this.eChartShow = true;
        //         this.jtopoShow = false;
        //         this.$nextTick(() => {
        //             this.getECharts(this.tabsId);
        //         });
        //     } else {
        //         this.eChartShow = false;
        //         this.jtopoShow = true;
        //         this.$nextTick(() => {
        //             this.getPathTopologyResult(this.tabsId);
        //             // this.getTopoHeadEndShowNodeNumById(this.tabsId)
        //         });
        //     }
        //     // debugger
        //     // 切换隐藏所有操作
        //      this.isDataType = false
        //   this.legendShow = false
        //   this.isSearchShow = false
        //   this.isLayoutShow = false
        //   this.informationShow = false

        // }
        // 切换功能
        ulChange(id) {
         
            //端到端不需要纤维图
            let findResult =  this.topoTabList.find(item => item.id === id);
            this.isEndToEnd = false;
            if(findResult) {
              this.isEndToEnd = findResult.managemode == 2 ? true : false
            }
            this.tabsId = id;
            this.$http
            .wisdomPost('/pathTopo/getTopoFiberOrRoutineById', { id })
            .then(res => {
                if (res.code === 1) {     
                  //  debugger            
                    if(res.data.fiberRoutineModel==1){
                      // this.activeValue = 2
                      // this.radioValue = this.$t('path_fiber');
                      // this.eChartShow = true;
                      // this.jtopoShow = false;
                      // this.$nextTick(() => {
                      //     this.getECharts(this.tabsId);
                      // });
                      this.activeValue = 2
                      this.radioValue = this.$t('path_fiber');
                      this.jump(id, 2);
                    }else{
                      // this.activeValue = 1
                      // this.radioValue = this.$t('path_routine')
                      // this.eChartShow = false;
                      // this.jtopoShow = true;
                      // this.$nextTick(() => {
                      //     this.getPathTopologyResult(this.tabsId);
                      // });
                      this.activeValue = 1
                      this.radioValue = this.$t('path_routine')
                      this.jump(id, null);
                    }
                } else {
                      // this.activeValue = 1
                      // this.radioValue = this.$t('path_routine')
                      // this.eChartShow = false;
                      // this.jtopoShow = true;
                      // this.$nextTick(() => {
                      //     this.getPathTopologyResult(this.tabsId);
                      // });
                      this.activeValue = 1
                      this.radioValue = this.$t('path_routine')
                      this.jump(id, null);
                }
            })
            .finally(() => {
              // 切换隐藏所有操作
              this.isDataType = false
              this.legendShow = false
              this.isSearchShow = false
              this.isLayoutShow = false
              this.informationShow = false
            });
        }
    },
    beforeCreate() {
     
      document.querySelector('body').setAttribute('style', 'overflow: hidden;') 
    },
    created() {
      this.modalWidth = localStorage.getItem('modalWidth') * 0.98
      console.log(this.modalWidth,'modalWidth')
       let jtopoId = JSON.parse(sessionStorage.getItem('jtopo'));
       let jtopoType = JSON.parse(sessionStorage.getItem('jtopoType'));
       console.log(jtopoId,jtopoType,'00')
      //  console.log(jtopoId,jtopoType,'00')
      // 2纤维图 
       if(jtopoType === 2) {
        this.btnShow = false
       }
      //  debugger
      this.echartsHeight = this.currentSkin == 1 ? document.body.clientHeight : document.body.clientHeight - 40;
      this.menuHeight = this.currentSkin == 1 ? document.body.clientHeight : document.body.clientHeight - 40;
      this.jtopoWidth = this.currentSkin == 1 ? top.document.body.clientWidth - 60 : top.document.body.clientWidth - 100 - 200
      

          
      // this.findInUseIconManageConfigureVoByType()
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        if (window.frames.name && window.frames.name.indexOf('?topoId=') > -1) {
            this.topoId = window.frames.name.split('?topoId=')[1].split('&&limitNum=')[0];
        }
        this.$http.wisdomPost('/audit/detail', { tab: this.$t('topo_path_topology') }).then(res => { });
        this.documentW = top.document.body.clientWidth;
        // this.findInUseIconManageConfigureVoByType();
    },
    mounted() {
      
      document.querySelector('body').setAttribute('style', 'overflow: hidden;') 
    
        // window.parent.addEventListener('message', e => {
        //     if (e) {
        //         if (e.data.type == 'msg') {
        //             return;
        //         } else if (typeof e.data == 'object') {
        //             this.tabsId = e.data.isdarkSkin;
        //         } else if (typeof e.data == 'number') {
        //             this.tabsId = e.data;
        //         }
        //     }
        // });
        // 一分钟定时更新数据
        this.timerInterval = setInterval(() => {
            if (this.radioValue == this.$t('path_fiber')) {
                this.$nextTick(() => {
                    this.getECharts(this.tabsId);
                });
            } else {
              // 这里改成jtopo渲染完成后开始两分钟定时刷新
                this.$nextTick(() => {
                  if(!this.forbidUpdate) {
                     this.getPathTopologyResult(this.tabsId);
                  }
                   
                    // this.getTopoHeadEndShowNodeNumById(this.tabsId);
                });
            }
        }, 1000 * 120);


        // 应急部专用代码。不能删除
        // 10 秒定时查看任务告警是否恢复，恢复了就通知前端更新数据
        // this.alarmAutoTimerInterval = setInterval(() => {
        //     if (this.radioValue == this.$t('path_fiber')) {
        //         // this.$nextTick(() => {
        //         //     this.getECharts(this.tabsId);
        //         // });
        //     } else {
        //       let that = this;
        //       let obj = {
        //         md:this.topologyMd5,
        //         topologyId:this.tabsId
        //       };
        //       this.$http
        //     .wisdomPost('/pathTopo/autoRefresh', obj)
        //     .then(res => {
        //           if(res.code === 2){
        //               that.topologyMd5 = res.data;
        //               console.log("需要前端自动加载数据", res);
        //               // 这里改成jtopo渲染完成后开始两分钟定时刷新
        //               that.$nextTick(() => {
        //                   that.getPathTopologyResult(that.tabsId);
        //               });
        //           }else{
        //             console.log("不自动加载数据", res);
        //             that.topologyMd5 = res.data;
        //           }
        //       });
        //     }
        // }, 1000 * 10);


        this.getPathTopoPulldown();
        //悬浮弹窗
        window.getPathTopoData = this.getPathTopoData;
        // this.init();
        // 左右拖动自动重新渲染
        window.addEventListener('resize', this.windowResize);
        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
    },
    beforeDestroy() {
        clearInterval(this.timerInterval);
        this.timerInterval = null;

        clearInterval(this.alarmAutoTimerInterval);
        this.alarmAutoTimerInterval = null;

        window.removeEventListener('resize', this.windowResize);
        document.querySelector('body').setAttribute('style', 'overflow: unset;') 
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
           // 清理所有定时器
        if(this.timer) clearTimeout(this.timer)
        if(this.leftTimer) clearTimeout(this.leftTimer) 
        if(this.rightTimer) clearTimeout(this.rightTimer)
        if(this.timerInterval) clearInterval(this.timerInterval)
         if(this.alarmAutoTimerInterval) clearInterval(this.alarmAutoTimerInterval)
        
        // 移除事件监听器
        window.removeEventListener('resize', this.windowResize)
        document.querySelector('body').setAttribute('style', 'overflow: unset;')
    }
};
</script>
<style scoped >
</style>
<style lang="less" >
body {
  min-height: 560px;
}

.clickProp {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}

.panel-default {
  background-color: var(--body_b_color, #f4f6f9) !important;
  color: var(--font_color, #303748) !important;
  border: 1px solid var(--thd_border_color, #e8eaec) !important;
}

.topoTipsBox {
  display: none;
  left: 10px;
  top: 10px;
  position: absolute;
  z-index: 8;
  padding: 10px 0;
  max-width: 400px;
  word-break: break-word;
  white-space: pre;
  white-space: pre-line;
  border: 1px solid var(--thd_border_color, #e8eaec);
  text-align: left;
  background-color: var(--body_b_color, #f4f6f9) !important;
  color: var(--font_color, #303748) !important;
}

.topoTipsBox .topoTipsTitle {
  position: relative;
  border-bottom: 1px solid var(--thd_border_color, #e8eaec);
  margin-bottom: 5px;
  padding: 0 10px 5px 10px;
  font-size: 14px;
}

.topoTipsBox .topoTipsTitle a {
  margin-left: 10px;
}

.topoTipsBox .topoTipsTitle span {
  position: absolute;
  right: 10px;
  top: -5px;
  font-size: 18px;
  cursor: pointer;
  display: block;
  width: 20px;
  text-align: center;
}

.topoTipsBox .topoTipsContent {
  padding: 0 10px;
}

.backgroundImg {
  position: relative;
  height: 120px;
  margin-top: 40px;
}

.backgroundImg > p {
  margin-top: 10px;
  color: #5ca0d5 !important;
}

.topoTopBut {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  /* padding-bottom: 20px; */
  justify-content: flex-end;
}

.topologys {
  position: relative;
  width: 100%;
  min-height: 420px;
  display: block;
  overflow: auto;
  color: black;
  margin: 8px 0 0 0;
  box-sizing: border-box;
  overflow-x: hidden;
  background-color: var(--body_b_color, #f4f6f9) !important;
  color: var(--font_color, #303748) !important;
}

.option .ivu-select-visible {
  outline: none !important;
  border: none !important;
}

.option .ivu-select {
  border: none !important;
}

.option .ivu-icon {
  display: none !important;
}

.option .ivu-select-selection,
.select-content .select-input input,
.ivu-radio-inner,
.ivu-select-selection,
.ivu-picker-panel-sidebar {
  background-color: rgba(0, 0, 0, 0) !important;
}

.option .ivu-select-selection {
  color: unset !important;
}

.option .ivu-select .ivu-select-selection {
  box-shadow: none !important;
  /*清除select的边框样式*/
  border: none !important;
  background-color: rgba(0, 0, 0, 0);
  /*清除select聚焦时候的边框颜色*/
  outline: none !important;
  /*将select的宽高等于div的宽高*/
  min-width: 160px !important;
  height: 32px !important;
  line-height: 32px !important;
  /*隐藏select的下拉图标*/
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  text-align: center !important;
  /* font-weight: bold !important; */
}

.option2 {
  background-image: url("../../../assets/dashboard/btn-tabs3-02.png") !important;
  background-size: 100% 100% !important;
  color: #5ca0d5 !important;
}

.option1 {
  font-weight: bolder;
  color: #000 !important;
  z-index: 3000;
  background-image: url("../../../assets/dashboard/btn-tabs3-01.png") !important;
  background-size: 100% 100% !important;
}

.bgWh {
  min-width: 140px;
  height: 32px;
  color: #000;
  line-height: 32px;
  /* font-weight: bold; */
  margin-right: -15px;
  cursor: pointer;
}

.tabs-card .ivu-select-dropdown {
  min-width: 250px !important;
}

.bg1 {
  background-image: url("../../../assets/dashboard/btn-tabs1-01.png") !important;
  background-size: 100% 100% !important;
  color: #5ca0d5;
}

.bg2 {
  background-image: url("../../../assets/dashboard/btn-tabs1-02.png") !important;
  background-size: 100% 100% !important;
  z-index: 3000;
  font-weight: bolder;
}

.bg3 {
  background-image: url("../../../assets/dashboard/btn-tabs2-01.png") !important;
  background-size: 100% 100% !important;
  z-index: 2999;
  color: #5ca0d5;
}

.bg4 {
  background-image: url("../../../assets/dashboard/btn-tabs2-02.png") !important;
  background-size: 100% 100% !important;
  z-index: 3000;
  font-weight: bolder;
}

.topologys button {
  margin: 10px;
}

.title {
  color: black;
  text-align: left;
  font-weight: bold;
}

.title {
  background: radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9));
  color: white;
  margin: 0 -10px;
  padding: 0 10px;
}

.imgDivIcon {
  /* padding:10px 0 0 0; */
  display: flex;
  align-items: center;
}

.imgDivIcon img {
  cursor: pointer;

  /* width: 25px;
    height: 25px; */
}

/* 常规图和纤维图切换 */
.ivu-radio-group-button .ivu-radio-wrapper {
  background: #060d15 !important;
}

.ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus {
  box-shadow: none !important;
}

.ivu-radio-group-button .ivu-radio-wrapper-checked {
  color: #05eeff !important;
  border: 1px solid #05eeff !important;
}

.ivu-radio-group-button .ivu-radio-wrapper:first-child {
  border-left: 1px solid #015197;
  border: 1px solid #015197;
  color: #5ca0d5;
}

.ivu-radio-group-button .ivu-radio-wrapper:last-child {
  border-left: 1px solid #015197;
  border: 1px solid #015197;
  color: #5ca0d5;
}

.ivu-radio-group-button .ivu-radio-wrapper:after {
  background: #5ca0d5;
}

.ivu-radio-group-button .ivu-radio-wrapper:before {
  background: #5ca0d5;
}

.rightButDiv {
  display: flex;
  align-items: center;
}

.btnDiv {
  display: flex;
  align-items: center;
}

.img-btn {
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 16px;
  border-radius: 5px;
  border: 1px #045b8e solid;
  margin-left: 5px;
}

.rightButDiv #findText {
  color: var(--input_font_color, #303748) !important;
  background-color: var(--input_b_color, #fff) !important;
}

#fullScreenButton {
  background-color: var(--primary_bcg, #2d8cf0) !important;
  color: var(--primary_font, #fff) !important;
  border: 1px solid var(--primary_border, #2d8cf0) !important;
}

.modelDiv {
  z-index: 5000;
  width: 180px;
  max-height: 500px;
  position: fixed;
  top: 80px;
  /* left: 605px; */
  /* background: #fff; */
  background-color: var(--body_b_color, #f4f6f9) !important;
  border-color: var(--thd_border_color, #e8eaec) !important;
}

.modelDiv .modelHead {
  background: var(--topo_model_head_color, #465b7a) !important;
  color: #fffdef;
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
}

#canvas {
  border-top: 1px solid var(--thd_border_color, #e8eaec) !important;
}

.modelDiv .modelcont {
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #9c9c9c;
  text-align: left;
}

.trendNoData {
  height: 260px;
  width: 100%;
  background: url("../../../common/loading/noData.png") no-repeat center;
  background-size: 290px 200px;
}

.bigscale {
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%);
  border-radius: 2px;
  cursor: pointer;
}

.bigscale > img {
  width: 16px !important;
  height: 16px !important;
  margin: 8px !important;
}

.fn_item {
  display: flex;
  width: 55%;
  height: 26px;
  margin-bottom: 0px;
  padding-right: 0px;
  align-items: center;
}
.bg-box {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}
.tabs-box {
  z-index: 999;
  position: fixed;
  top: 0;
  left: 0;
  background-color: transparent;
  padding-left: 10px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-self: start;
  .ivu-select {
    // border: 1px solid #045b8e;
    border: 1px solid var(--pathtopo_tabs_box_select_border_color, #045b8e);
    height: 38px !important;
    background-image: unset;
    .ivu-select-placeholder {
      height: 36px !important;
      line-height: 36px !important;
    }
    .ivu-select-selection {
      box-shadow: unset;
      height: 100% !important;
      // background-color: #061824 !important;
      background-color: var(
        --pathtopo_tabs_box_select_bg_color,
        #061824
      ) !important;
    }
    .ivu-select-selected-value {
      // color: #05eeff;
      color: var(--pathtopo_tabs_box_select_font_color, #05eeff);
      height: 100% !important;
      font-weight: 700;
      line-height: 36px !important;
    }
    .ivu-select-arrow {
      color: #5ca0d5;
    }
    .ivu-select-item {
      text-align: left;
    }
  }
}
.tabs-box-small {
  z-index: 999;
  position: fixed;
  top: 0;
  left: 0;
  background-color: transparent;
  padding-left: 10px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-self: start;
  .ivu-select {
    border: 1px solid var(--pathtopo_tabs_box_select_border_color, #045b8e);
    height: 28px !important;
    background-image: unset;
    .ivu-select-placeholder {
      height: 26px !important;
      line-height: 26px !important;
    }
    .ivu-select-selection {
      box-shadow: unset;
      height: 100% !important;
      // background-color: #061824 !important;
      background-color: var(
        --pathtopo_tabs_box_select_bg_color,
        #061824
      ) !important;
    }
    .ivu-select-selected-value {
      color: var(--pathtopo_tabs_box_select_font_color, #05eeff);
      height: 100% !important;
      font-weight: 700;
      line-height: 26px !important;
    }
    .ivu-select-arrow {
      color: #5ca0d5;
    }
    .ivu-select-item {
      text-align: left;
    }
  }
}
.left-menu-small {
  width: 50px;
}
.left-menu-big {
  width: 60px;
}
.left-menu {
  // .layout-special {
  //    .ivu-tooltip-popper {
  //     top:170px !important;
  //   }
  // }

  .layout-box {
    .layout-active {
      border: 1px solid
        var(--pathtopo_icon_special_suspend_border_color, #05eeff) !important ;
    }
    .img-box {
      width: 34px;
      height: 34px;

      border-radius: 4px;
      border: 1px solid var(--pathtopo_icon_box_border_color, #045b8e);
      img {
        width: 32px;
        height: 32px;
      }
    }
    .tree {
      margin-right: 8px;
    }
  }
  .bottom-menu {
    margin-top: 20px;
    margin-left: 5px;
    padding-top: 10px;
    width: 50px;
    height: 106px;
    border-top: 1px solid var(--topo_menu_region_border, #22465d);
  }
  .bottom-menu-small {
    margin-left: 5px;
    width: 40px;
    padding-top: 4px;
    margin-top: 10px;

    border-top: 1px solid var(--topo_menu_region_border, #22465d);
  }

  .top-menu {
    margin-left: 5px;
    margin-top: 60px;
    margin-bottom: 20px;
    width: 50px;

    border-bottom: 1px solid var(--topo_menu_region_border, #22465d);
    padding-bottom: 20px;
  }
  .top-menu-small {
    margin-left: 5px;
    margin-top: 40px;
    margin-bottom: 10px;
    width: 40px;

    border-bottom: 1px solid var(--topo_menu_region_border, #22465d);
    padding-bottom: 10px;
  }

  background-color: var(--pathtopo_tabs_box_select_bg_color, #061824);
  .ivu-tooltip-inner {
    cursor: pointer;
  }

  .icon-box {
    width: 34px;
    height: 34px;
    margin: 0 auto;
    border-radius: 4px;
    // border: 1px solid #045b8e;
    border: 1px solid var(--pathtopo_icon_box_border_color, #045b8e);
    margin-top: 10px;
    img {
      width: 32px;
      height: 32px;
    }
    .tooltip-box {
      display: flex;
      .tooltip-normal {
        display: inline-block;
        border-right: 1px solid #345c75;
        // color: #5ca0d5;
        color: var(--pathtopo_fiber_routine_model_font_color, #5ca0d5);
        padding: 0 5px;
      }
      .tooltip-special {
        display: inline-block;
        // color: #5ca0d5;
        color: var(--pathtopo_fiber_routine_model_font_color, #5ca0d5);
        padding: 0 5px;
      }
      .active {
        font-weight: bold;
        // color: #05eeff;
        color: var(--pathtopo_fiber_routine_model_active_font_color, #05eeff);
      }
    }
  }
  .icon-box-small {
    width: 20px;
    height: 20px;
    margin: 0 auto;
    border-radius: 4px;
    border: 1px solid var(--pathtopo_icon_box_border_color, #045b8e);
    margin-top: 6px;
    img {
      width: 18px;
      height: 18px;
    }
    .tooltip-box {
      display: flex;
      .tooltip-normal {
        display: inline-block;
        border-right: 1px solid #345c75;
        // color: #5ca0d5;
        color: var(--pathtopo_fiber_routine_model_font_color, #5ca0d5);
        padding: 0 5px;
      }
      .tooltip-special {
        display: inline-block;
        // color: #5ca0d5;
        color: var(--pathtopo_fiber_routine_model_font_color, #5ca0d5);
        padding: 0 5px;
      }
      .active {
        font-weight: bold;
        // color: #05eeff;
        color: var(--pathtopo_fiber_routine_model_active_font_color, #05eeff);
      }
    }
  }
  // 111
  .icon-special {
    // border: 1px solid #05eeff;
    border: 1px solid var(--pathtopo_icon_special_suspend_border_color, #05eeff);
  }
  .icon-special-bg {
    // border: 1px solid #05eeff;
    background-color: #0290fd;
  }
  .data-format {
    .my-tooltip {
      top: -200% !important;
    }
    .my-tooltip::before {
      top: 70px !important;
    }
  }
  .close-box {
    position: relative;
    .my-tooltip {
      z-index: 1000;
      position: absolute;
      top: 0;
      left: 60px;
      background: var(--pathtopo_my_tooltip_bg_color, #06324d);
      padding: 8px 8px;
      border-radius: 4px;
      box-align: 0 1px 6px rgba(0, 0, 0, 0.2);
      .search-bottom {
        display: flex;
        height: 32px;
        align-items: center;
        .search-btn {
          height: 32px;
          border: unset;
          margin-right: unset !important;
          // background: linear-gradient(to bottom, #05ebeb, #049dec);
          background: linear-gradient(
            to bottom,
            var(--button_background_color, #05ebeb),
            var(--button_background_two_color, #049dec)
          );
        }
        .ivu-btn {
          color: var(--search_check_box_font_color, #060d15);
          margin-left: 8px;
        }
      }
      .search-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .search-title {
          // color: #05eeff;
          color: var(--pathtopo_fiber_routine_model_active_font_color, #05eeff);
        }
        .ivu-icon-ios-close {
          color: #5ca0d5 !important;
        }
      }
    }
    .my-tooltip-light {
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    }
    .my-tooltip::before {
      content: "";
      position: absolute;
      left: -5px;

      top: 10px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 5px 5px 5px 0;
      border-color: transparent;
      border-right-color: #06324d;
    }
    .my-tooltip-layout {
      top: 10px;
      display: flex;
      padding: 8px;
      .tree {
        margin-right: 10px;
      }
    }
    .information-bottom {
      min-width: 205px;

      .ivu-checkbox-group {
        text-align: left;

        .width90 {
          width: 90px;
        }

        .width110 {
          width: 110px;
        }
        .ivu-checkbox-group-item {
          // width: 110px;
          // border: 1px solid red;
          text-align: left;
          margin-right: 0px;
        }
      }
    }
    .legend-bottom {
      // min-width:170px;

      overflow-y: auto;
      .legend-color {
        padding: 8px;
        width: 100%;
        border-bottom: 1px solid
          var(--pathtopo_legend_splitline_border_color, #22465d);
        .top-one {
          display: flex;
          justify-content: space-between;
        }
        .legend-color-item {
          display: flex;
          align-items: center;

          width: 50%;
          margin-bottom: 6px;

          .color-item {
            border-radius: 4px;
            margin-right: 5px;
          }
          .color-title {
            font-size: 14px;
          }
        }
      }
      .legend-icon {
        padding: 8px;
        display: flex;
        width: 100%;

        flex-wrap: wrap; /* 允许项目换行 */
        justify-content: space-between; /* 在两列间均匀分配空间 */
      }
      .legend-icon-item {
        flex-basis: calc(
          50% - 5px
        ); /* 每个项目占据50%宽度减去gap的一半，以保持间距 */

        box-sizing: border-box; /* 包含padding和border的宽度计算 */

        display: flex;
        align-items: center;
        margin-bottom: 6px;
      }
    }
    .data-bottom {
      width: 250px;
      .drag-title {
        width: 100%;
        text-align: left;
        padding-left: 10px;
        margin-bottom: 25px;
      }
      .left-box {
        width: 244px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .drag-step {
          width: 220px;
          height: 6px;
          background-color: var(
            --pathtopo_my_tooltip_left_box_drag_step_bg_color,
            #22465d
          );
          display: flex;
          align-items: center;
          border-radius: 6px;
          margin: 0 5px;
          .step-bg {
            position: relative;
            height: 100%;
            background-color: var(
              --pathtopo_my_tooltip_left_box_step_bg_color,
              #05eeff
            );
            border-radius: 6px 0 0 6px;
          }
          .step-circle {
            width: 14px;
            height: 14px;
            border: 2px solid
              var(--pathtopo_my_tooltip_left_box_step_bg_color, #05eeff);
            background-color: var(
              --pathtopo_my_tooltip_left_box_step_circle_bg_color,
              #06324d
            );
            border-radius: 14px;
            position: relative;
            .num-tip {
              position: absolute;
              top: -29px;
              left: -5px;
              width: 24px;
              height: 22px;
              background-color: var(
                --pathtopo_my_tooltip_left_box_num_tip_bg_color,
                #015197
              );
              border-radius: 4px;
            }
            .num-tip::before {
              content: "";
              position: absolute;
              left: 8px;

              top: 23px;
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 5px 5px 0 5px;
              border-color: transparent;
              border-top-color: var(
                --pathtopo_my_tooltip_left_box_num_tip_bg_color,
                #015197
              );
            }
            .drag-mouse {
              position: absolute;
              top: -17px;
              left: -15px;
              width: 40px;
              height: 40px;
              background-color: transparent;
              // border: 1px solid pink;
              z-index: 999;
            }
          }
        }
      }

      .step-total {
        width: 224px;
        height: 6px;
        border-radius: 6px;
        background-color: #22465d;
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        margin-left: 10px;

        .step-circle {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 2px solid #05eeff;
          background-color: #06324d;
          position: relative;
          .num-tip-bt {
            position: absolute;
            top: 19px;
            left: -5px;
            width: 24px;
            height: 22px;
            background-color: #015197;
            border-radius: 4px;
          }

          .num-tip-bt::before {
            content: "";
            position: absolute;
            left: 8px;
            top: -9px;
            width: 0;
            height: 0;
            border: 5px solid transparent;
            border-bottom: 5px solid #015197;
          }
          .drag-box-right {
            background-color: transparent;
            position: absolute;
            top: -30px;
            left: -5px;
            width: 24px;
            height: 70px;
            z-index: 999;
          }
          .drag-box {
            background-color: transparent;
            position: absolute;
            top: -30px;
            left: -5px;
            width: 24px;
            height: 70px;
            z-index: 999;
          }
          .num-tip {
            position: absolute;
            top: -29px;
            left: -5px;
            width: 24px;
            height: 22px;
            background-color: #015197;
            border-radius: 4px;
          }
          .num-tip::before {
            content: "";
            position: absolute;
            left: 8px;

            top: 23px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 5px 5px 0 5px;
            border-color: transparent;
            border-top-color: #015197;
          }
        }
        .step-left {
          border-bottom-left-radius: 6px;
          border-top-left-radius: 6px;
          background-color: #05eeff;
          display: flex;
          align-items: center;
          justify-content: end;
        }
        .step-right {
          border-bottom-right-radius: 6px;
          border-top-right-radius: 6px;
          background-color: #05eeff;
          display: flex;
          align-items: center;
          justify-content: start;
        }
      }
      .step-num {
        width: 224px;
        margin-left: 10px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
.bottom-box {
  display: flex;
}
.right-box {
  flex-grow: 1;
}
.light-no-tabs .tabs-box {
  top: 20px;
  left: 5px;
}
.light-no-tabs .topo-box {
  background-color: #fff;
}
</style>
