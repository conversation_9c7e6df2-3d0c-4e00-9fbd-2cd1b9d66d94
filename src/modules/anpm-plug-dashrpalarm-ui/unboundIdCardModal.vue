<template>
	<div>
		<Modal sticky :value="show" width="700" :title="$t('login_update_idcard')" draggable :mask="true" :mask-closable="false"
			@on-cancel="editCancel">
			<Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="120">
				<Row style="margin-bottom: 20px;">
					{{$t('login_update_idcard_tip')}}

					{{$t('login_update_idcard_tip2')}}
				</Row>

				<Row>
					<FormItem :label="$t('user_full_name')+$t('comm_colon')" class="inlineForm" prop="name">
						<i-input v-model="formData.name"></i-input>
					</FormItem>
				</Row>
				<Row>
					<FormItem :label="$t('comm_identification_card')+$t('comm_colon')" class="inlineForm" prop="idCard">
						<i-input v-model="formData.idCard" @paste.native.capture.prevent="handleFalse"
							@copy.native.capture.prevent="handleFalse" maxlength="20"
							@cut.native.capture.prevent="handleFalse"></i-input>
					</FormItem>
				</Row>
			</Form>
			<div slot="footer">
				<Button type="error" :loading="btnLoading" @click="editCancel()">{{$t('common_cancel')}}</Button>
				<Button type="primary" :loading="btnLoading" @click="submitBtn()">{{$t('common_verify')}}</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
import "@/timechange";
import validate from "@/common/validate";
import handlePassword from "@/common/handlePassword";

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		const valiIdCard = (rule, value, callback) => {
			if (!value) {
				var _msg = this.$t('login_update_idcard_error');
				callback(new Error(_msg))
			} else {
				const valiIdCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
				if (!valiIdCardReg.test(value)) {
					var _msg = this.$t('login_update_idcard_format_error');
					callback(new Error(_msg))
				} else {
					callback()
				}
			}
		}
		return {
			btnLoading: false,
			formData: {
				name: '',
				idCard: ''
			},
			ruleValidate: {
				// idCard: [
				// 	{ trigger: 'blur', validator: valiIdCard, required: true }
				// ],
				idCard: [
                    {
						required: true,
                        trigger: 'blur',
                        validator: (rule, value, callback) => {
                            if(this.verificationIdCardChina){
                                const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
                                if (value == '' || value == undefined || value == null) {
                                    callback(new Error(this.$t('login_update_idcard_error')))
                                } else {
                                    if ((!reg.test(value)) && value != '') {
                                        callback(new Error(this.$t('please_enter_correct_idcard')));
                                    } else {
                                        callback();
                                    }
                                }
                            }else{
                                if (value == '' || value == undefined || value == null) {
                                    callback(new Error(this.$t('login_update_idcard_error')))
                                } else {
                                      var containSpecial = RegExp(/[\~\!\@\#\$\%\^\&\*\+\=\|\\\;\:\'\"\,\.\/\·\`\<\>\?\[\]\{\}]+/);
                                      if (containSpecial.test(value)){
                                        callback(new Error(this.$t('idcard_cannot_contain_special_characters')))
                                      }
                                      //是否符合长度
                                      if (value.length > 20) {
                                        callback(new Error(this.$t('idcard_allows_20_characters')));
                                      }
                                      callback();
                                }
                            }
                        }
                    }
                    
                ],
				name: [
					{ trigger: 'blur', maxLength: 20, validator: validate.validateContacts },
				],
			}
		};
	},
	created() { 
		
	},
	methods: {
		// 关闭弹框  如果传false  则没有认证  需要清除登录信息   
		// 如果传true  则不需要清除登录信息
		editCancel(unbound) {
			if (!unbound) {
				this.logout()
			}
			this.$emit("closeModal");
		},
		handleFalse() {
			return false
		},
		// 确认
		submitBtn() {
			let _self = this
			let vaildResult = false
			this.$refs.formData.validate(valid => {
				vaildResult = valid
			})
			if (!vaildResult) {
				return
			}
			let param = {
				name: _self.formData.name,
				idCard: handlePassword.encryption(_self.formData.idCard)
			};
			_self.$http.post("/user/boundIdCard", param).then((res) => {
				if (res.code === 1) {
					this.$Message.success({ content: res.msg, background: true });
					// 绑定成功后直接跳转主页面
					setTimeout(() => {
						location.href = location.hostname === 'localhost'
							? '/anpm-plug-server-ui.html'
							: '/server/index.html';
					}, 200);
					this.editCancel(true)
				} else {
					this.$Message.warning({ content: res.msg, background: true });
				}
			})
		},
		//退出
		logout() {
			setTimeout(() => {
				this.$http
					.post("/loginOut", {})
					.then((res) => {
						if (res.code == 1) {
							sessionStorage.clear();
						} else {
							console.log("退出失败.");
						}
					})
					.catch((err) => {
						console.log(err);
					});
			}, 500);
		}
	},

};
</script>

<style scoped>
.inlineForm {
	display: inline-block;
	width: 50%;
}

.pwdShow {
	display: inline-block;
	vertical-align: top;
	height: 32px;
	line-height: 32px;
	cursor: pointer;
	margin-left: 10px;
	font-size: 12px;
}

#one {
	border-top-left-radius: 5px;
	border-bottom-left-radius: 5px;
	border-right: 0px solid;
	margin-right: 3px;
}

#two {
	border-left: 0px solid;
	border-right: 0px solid;
	margin-left: -5px;
	margin-right: 3px;
}

#three {
	border-top-right-radius: 5px;
	border-bottom-right-radius: 5px;
	border-left: 0px solid;
	margin-left: -5px;
}

.color1 {
	color: red !important;
	background: red !important;
}

.color2 {
	color: orange !important;
	background: orange !important;
}

.color3 {
	color: #19be6b !important;
	background: #19be6b !important;
}
</style>
