<template>
  <section class=" nodeTop" :style=" 'background:'+(isbigscreen?'#1a222e':isdarkSkin==1?'#1a222e':'#ffffff') ">
    <!--<div class="title">最近一周质差节点 TOPN</div>-->
    <div class="section-body">
      <div class="section-body-content">
        <div>
          <!--<Loading :loading="loading"></Loading>-->
          <Table
            ref="tableList"
            border
            :columns="columns"
            :data="tableList"
            :no-data-text="
              loading ? '' : tableList.length > 0 ? '' : $t('alarm_no_data')
            "
            :loading="loading"
            size="small"
          ></Table>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import "@/config/page.js";
  export default {
    name: "dashTop",
    data() {
      return {
        //刷新时间间隔
        intervalTime:null,
        //刷新参数
        interrefresh:null,
        //搜索字段
        query: {
          limitNum: 10, //状态
          pageNo: 1, //页数
          pageSize: 10 //每页展示多少数据
        },
        //loading状态
        loading: false,
        //表格数据
        tableList: [],
        columns: [
          {
            title: this.$t('dash_node_ip'),
            key: "nodeIp",
            align: "center"
          },
          {
            title: this.$t('dash_node_name'),
            key: "nodeName",
            align: "center"
          },
          {
            title: this.$t('comm_org'),
            key: "clientName",
            align: "center"
          },
          {
            title: this.$t('server_node_type'),
            key: "nodeType",
            sortable: true,
            width: 150,
            align: "center"
          },
          {
            title: this.$t('comm_interrup_total'),
            key: "brokenDuration",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 180,
            align: "center",
            render: (h, params) => {
              let str = params.row.brokenDuration;
              return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                  ? "--"
                  : this.handleDuration(str)
              );
            }
          },
          {
            title: this.$t('comm_interrup_times'),
            key: "brokenTimes",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 160,
            align: "center"
          },
          {
            title: this.$t('comm_interrup_times'),
            key: "degradationDuration",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 180,
            align: "center",
            render: (h, params) => {
              let str = params.row.degradationDuration;
              return h(
                "span",
                str === undefined || str === null || str == "null" || str === ""
                  ? "--"
                  : this.handleDuration(str)
              );
            }
          },
          {
            title: this.$t('comm_deterioration_times'),
            key: "degradationTimes",
            sortable: true,
            sortMethod: (a, b, type) => {
              if (type === "desc") {
                return parseInt(a) > parseInt(b) ? -1 : 1;
              } else return parseInt(a) > parseInt(b) ? 1 : -1;
            },
            width: 160,
            align: "center"
          }
        ],
        isbigscreen:false,
        isdarkSkin:0
      };
    },
    created() {
      this.isdarkSkin = top.window.isdarkSkin;
      const src = window.frames.frameElement.getAttribute('src'),name=window.frames.frameElement.getAttribute('name');
      if (parent.window.isEdit) {
        this.intervalTime = parent.window.dashqualitytop.split('&&topoId=')[0].split('?intervalTime=')[1];
      }else{
        this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
        if (window.frames.frameElement) {
          window.frames.frameElement.contentWindow.close();
        }
      }
      this.getList(this.query);
    },
    methods: {
      getList(param) {
        //OID列表查询请求
        let _self = this;
        let httpRequest = _self.$http.wisdomPost("/home/<USER>", param)
        httpRequest.then(res => {
          if (res.code === 1) {
            _self.tableList = this.getTop(res.data);
          }else{
            _self.tableList = [];
          }
        }).catch((err)=>{
          _self.tableList = [];
          _self.loading = false;
          if (parent.loading){
            parent.loading['dashqualitytop'] = false;
          }
        }).finally(()=>{
          _self.loading = false;
          if (parent.loading){
            parent.loading['dashqualitytop'] = false;
          }
        });
        httpRequest = null
      },
      //四个类型取前五
      getTop(data) {
        let brokenTimesTop = [],
          brokenDurationTop = [],
          degradationDurationTop = [],
          degradationTimesTop = [],
          conect = [];
        brokenTimesTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("brokenTimes")
        ); //中断次数排序
        brokenDurationTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("brokenDuration")
        ); //中断时长排序
        degradationDurationTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("degradationDuration")
        ); //时延时长排序
        degradationTimesTop = JSON.parse(JSON.stringify(data)).sort(
          this.compare("degradationTimes")
        ); //时延次数排序
        //长度大于5，取前五条，否则取全部
        if (brokenTimesTop.length >= 5) {
          brokenTimesTop = brokenTimesTop.slice(0, 5);
        }
        if (brokenDurationTop.length >= 5) {
          brokenDurationTop = brokenDurationTop.slice(0, 5);
        }
        if (degradationDurationTop.length >= 5) {
          degradationDurationTop = degradationDurationTop.slice(0, 5);
        }
        if (degradationTimesTop.length >= 5) {
          degradationTimesTop = degradationTimesTop.slice(0, 5);
        }
        //合并数组（每个排序项top5数组）
        conect = [
          ...brokenTimesTop,
          ...brokenDurationTop,
          ...degradationDurationTop,
          ...degradationTimesTop
        ];
        //去除重复项
        let hash = {};
        const newListArr = conect.reduceRight((item, next) => {
          hash[next.nodeIp] ? "" : (hash[next.nodeIp] = true && item.push(next));
          return item;
        }, []);
        let newList = newListArr.sort(function(pre, next) {
          //默认以中断时长排序
          return -(pre.brokenDuration - next.brokenDuration);
        });
        //返回新的数组List
        return newList;
      },
      compare(prop) {
        return function(pre, next) {
          let val1 = pre[prop];
          let val2 = next[prop];
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1);
            val2 = Number(val2);
          }
          if (val1 < val2) {
            return 1;
          } else if (val1 > val2) {
            return -1;
          } else {
            return 0;
          }
        };
      },
      handleDuration(data) {
        //时间处理函数
        var theTime = parseInt(data); // 需要转换的时间秒
        var theTime1 = 0; // 分
        var theTime2 = 0; // 小时
        var theTime3 = 0; // 天
        if (theTime > 60) {
          theTime1 = parseInt(theTime / 60);
          theTime = parseInt(theTime % 60);
          if (theTime1 > 60) {
            theTime2 = parseInt(theTime1 / 60);
            theTime1 = parseInt(theTime1 % 60);
            if (theTime2 > 24) {
              //大于24小时
              theTime3 = parseInt(theTime2 / 24);
              theTime2 = parseInt(theTime2 % 24);
            }
          }
        }
        var result = "";
        if (theTime == 0) {
          result = "" + parseInt(theTime);
        }
        if (theTime == 0 && (theTime1 > 0 || theTime2 > 0 || theTime3 > 0)) {
          result = "" + parseInt(theTime) + this.$t('comm_second');
        }
        if (theTime > 0) {
          result = "" + parseInt(theTime) + this.$t('comm_second');
        }
        if (theTime1 > 0) {
          result = "" + parseInt(theTime1) + this.$t('comm_minutes') + result;
        }
        if (theTime2 > 0) {
          result = "" + parseInt(theTime2) + this.$t('comm_hour') + result;
        }
        if (theTime3 > 0) {
          result = "" + parseInt(theTime3) + this.$t('comm_day1') + result;
        }
        return result;
      }
    },
    mounted(){
      window.thisVm = this;
      this.isbigscreen = window.isbigscreen;
      if (this.intervalTime&& Number(this.intervalTime)) {
        this.interrefresh = setInterval(()=>{this.loading = true;this.getList(this.query)},this.intervalTime * 1000);
      }
    },
    beforeDestroy(){
      if (this.interrefresh) {
        clearInterval(this.interrefresh);
        this.interrefresh = null;
      }
    }
  };
</script>

<style>
  .searchTop .ivu-select-input {
    height: 32px;
    line-height: 32px !important;
  }
  .fault-tab .dialTest-tab-content .ivu-table th.bgColor {
    /*background: #f1f6fe !important;*/
  }
  .task-modal
  .ivu-modal-body
  .ivu-form-item.multipleSelect
  .ivu-select-selection {
    height: auto !important;
    min-height: 32px;
  }
  .multipleSelect .ivu-select-input {
    padding: 0 !important;
  }
</style>
<style scoped lang="less">
  .nodeTop{
    /*padding:0 20px;*/
    .title{
      font-size: 16px;
      text-align: left;
      font-weight: bold;
      line-height: 38px;
    }
  }
  .sectionBox {
    background: white!important;
    .section-body{
      margin: 0!important;
    }
  }
</style>
