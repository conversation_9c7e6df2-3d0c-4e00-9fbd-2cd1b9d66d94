/**
 * jtopo工具栏
 */
import $ from "../../../common/jtopo/jquery.min.js";
import "../../../common/jtopo/jtopo-s2.js";

import noImg from "../../../common/loading/noData.png";
import jiedianImgG from "../../../common/jtopo/img/jiedian_green.png";
import tanzhenImg from "../../../common/jtopo/img/tanzhen.png";
import jiedianImgH from "../../../common/jtopo/img/jiedian_grey.png";
import jiedianImgR from "../../../common/jtopo/img/jiedian_red.png";
import jiedianImgY from "../../../common/jtopo/img/jiedian_yell.png";
import serverInvalidImg  from "../../../common/jtopo/img/suolvtu.png";
//物理图标
import fanghuoqiangG  from "../../../common/jtopo/img/icon_fanghuoqiang_g.png";
import fanghuoqiangR  from "../../../common/jtopo/img/icon_fanghuoqiang_r.png";
import fanghuoqiangY  from "../../../common/jtopo/img/icon_fanghuoqiang_y.png";
import fuwuqiG  from "../../../common/jtopo/img/icon_fuwuqi_g.png";
import fuwuqiR  from "../../../common/jtopo/img/icon_fuwuqi_r.png";
import fuwuqiY  from "../../../common/jtopo/img/icon_fuwuqi_y.png";
import jiaohuanjiG  from "../../../common/jtopo/img/icon_jiaohuanji_g.png";
import jiaohuanjiR  from "../../../common/jtopo/img/icon_jiaohuanji_r.png";
import jiaohuanjiY  from "../../../common/jtopo/img/icon_jiaohuanji_y.png";
import luyouqiG  from "../../../common/jtopo/img/icon_luyouqi_g.png";
import luyouqiR  from "../../../common/jtopo/img/icon_luyouqi_r.png";
import luyouqiY  from "../../../common/jtopo/img/icon_luyouqi_y.png";
// import server  from "../../../assets/topology/icon-server.png";
import servero  from "../../../assets/topology/icon-server-subs.png";
// import source  from "../../../assets/topology/icon-source.png";
import sourceo  from "../../../assets/topology/icon-server-subs.png";
// import route  from "../../../assets/topology/icon-route.png";
// import routeo  from "../../../assets/topology/icon-route-subs.png";
import source  from "../../../assets/topology/icon-y-1.png";
// import source2  from "../../../assets/topology/icon-y-2.png";
import source2  from "../../../assets/topology/icon-zt01.png";
import server  from "../../../assets/topology/icon-jx-1.png";
// import server2  from "../../../assets/topology/icon-jx-2.png";
import server2  from "../../../assets/topology/icon-server2.png";
import route  from "../../../assets/topology/icon-ip-1.png";
// import route2  from "../../../assets/topology/icon-ip-2.png";
import route2  from "../../../assets/topology/icon-route2.png";
import routeo  from "../../../assets/topology/icon-ip-grey1.png";
// import routeo2  from "../../../assets/topology/icon-ip-grey2.png";
import routeo2  from "../../../assets/topology/icon-routeo2.png";


// 二节点
import twoRoute  from "../../../assets/topology/icon-two-device-blue.png";
import twoRoute2  from "../../../assets/topology/icon-switch-grey.png";

// 虚拟节点
import virtualNode  from "../../../assets/topology/icon-virtual-blue.png";
import virtualNode2  from "../../../assets/topology/icon-virtual-grey.png";




import pathChakanW from "../../../common/jtopo/img/path_chakan_w.png";
import pathChakanY from "../../../common/jtopo/img/path_chakan_y.png";
import pathXXW from "../../../common/jtopo/img/path_xx_w.png";
import pathXXY from "../../../common/jtopo/img/path_xx_y.png";
import pathSXW from "../../../common/jtopo/img/path_sx_w.png";
import pathSXY from "../../../common/jtopo/img/path_sx_y.png";
import pathDaochuW from "../../../common/jtopo/img/path_daochu_w.png";
import pathDaochuY from "../../../common/jtopo/img/path_daochu_y.png";
import pathFangdaW from "../../../common/jtopo/img/path_fangda_w.png";
import pathFangdaY from "../../../common/jtopo/img/path_fangda_y.png";
import pathHuanyuanW from "../../../common/jtopo/img/path_huanyuan_w.png";
import pathHuanyuanY from "../../../common/jtopo/img/path_huanyuan_y.png";
import pathSuoxiaoW from "../../../common/jtopo/img/path_suoxiao_w.png";
import pathSuoxiaoY from "../../../common/jtopo/img/path_suoxiao_y.png";
import pathTuliW from "../../../common/jtopo/img/path_tuli_w.png";
import pathTuliY from "../../../common/jtopo/img/path_tuli_y.png";
import pathXxszW from "../../../common/jtopo/img/path_xxsz_w.png";
import pathXxszY from "../../../common/jtopo/img/path_xxsz_y.png";

var foldOpenStatus = {};
var maxy = 100;
var yx= 150;
var istop = true;

/**
 * 画布渲染主方法
 * @param dataList 渲染数据
 * @param type xx星形结构  sx树形结构
 * @param divId 创建画布的容器id
 * @param msgModal 需要展示的数据字段s
 * @returns {c}
 */
export function newJtopo(datas,isdarkSkin){
    var isdarkSkin = sessionStorage.getItem("dark")

    // console.log(dataList,"---dataList-");
    // var dataList = data;
    maxy = 100;
    yx= 150;
    var dataList = datas.dataList;
    var type = datas.topoType;
    var contId = datas.contId;
    var msgModalData = datas.msgModal;
    var isDashBoard = datas.isDashBoard;
    var showWarning = datas.showWarning;
    var ispath = datas.ispath;
    var thistopo = datas.thistopo;
    // var permissionObj = datas.permissionObj;
    //点位
    var nodelist = setDataList(dataList.nodeList);
    //线路
    var linklist = dataList.nodeRouteList;
    let d1 = new Date().getTime()
    //初始化容器
    //创建画布对象
    const canvasW = document.body.clientWidth;
    const canvasH = document.body.clientHeight;


//     if(!isDashBoard){
//         $("#"+contId,top.window).html(`<div  style="width:100%;height:100%" >
// <div name="topo_tips" class="panel-default" style="color: white;background: rgba(51, 122, 183, 0.5);position: absolute;" id="topoTips">
// <div class="panel-body" style=""></div></div><div id="canvasdiv">
// <canvas width= `+(canvasW-20)+` height=`+(canvasH-60)+` id="canvas" style="border-top: 1px solid #d8dbe6;"></canvas></div></div>`);
//     }else{
//         $("#"+contId,top.window).html(`<div  style="width:100%;height:100%" ><canvas width= `+(canvasW-20)+` height=`+(canvasH-60)+` id="canvas" style="border-top: 1px solid #d8dbe6;"></canvas></div>`);
//     }
    var jtopo_toolbar = top.document.createElement('div');
    jtopo_toolbar.setAttribute('id','jtopo_toolbar');
    var canvasDome = top.document.createElement('canvas');
    canvasDome.setAttribute('id','canvas');
    canvasDome.setAttribute('width','1700px');
    canvasDome.setAttribute('height','550px');
    top.document.getElementById('drawing-board').innerHTML = '';
    top.document.getElementById('drawing-board').appendChild(jtopo_toolbar);
    // top.document.getElementById('drawing-board').appendChild(canvasDome);
    let allConfirmList = top.document.getElementsByClassName('drawing-board');
    let dome = allConfirmList[allConfirmList.length - 1];
    allConfirmList[allConfirmList.length - 1].innerHTML= '';
    allConfirmList[allConfirmList.length - 1].appendChild(canvasDome);
    // var canvas = top.document.getElementById('canvas');
    var canvas = dome.lastElementChild;
    var stage = new JTopo.Stage(canvas);
    stage.wheelZoom = 1.1; // 设置鼠标缩放比例
    if(!isDashBoard){
        // stage.eagleEye.visible = true;//鹰眼
        showJTopoToobar(stage,datas);
    }
    var scene = new JTopo.Scene(stage);
    scene.alpha = 1;//透明度
    scene.backgroundColor = isdarkSkin == 1?'8,16,26':"255,255,255";
    if(nodelist&&nodelist.length==0){
        let imgdata = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAkQAAAGQCAYAAABPp3/+AAAgAElEQVR4Xu3df6ynWV0f8PN8Z7l3hmwKYmFtLRFSGpP+ASikP7Qy34WoWUVLWyHamBZTs/YX1RWZO9Oa7OwfZPYOCKi0kW0bMI2RlBogbaUx1fkO2mqTEn60Gikmktiiy2qztMjMvbv3Ps2dXegO3Lvzfb5zzvOcz3Ne8w9/7POc8zmv99nZN/dnl/whQGCQwHJ3/+Vd339bv+i+NfX9c1Pqn5dSd/S/D/epe7jr06dS133o1Kn+13/5x7Z/a9DiHiZAgACBSQS6SXa1KYGAAq+4tP8Ni0V/LqX0vQPGf+/hYXf5wxe2PjrgHY8SIECAwMgCCtHI4LaLKbC8dO1it1i8qU/pmUNP0KX0hf7w8C2rC2cuDn3X8wQIECAwjoBCNI6zXQILnN29/q4udffe9hG6brU6t3X3ba9jAQIECBDILqAQZSe14JwElrt7fe7zrHa2/XuXG9V6BAgQuE0BfzHfJqDX5yuwvLx/JfX9MvcJ+9Q/dHXn9A/lXtd6BAgQILC5gEK0uZ03Zyxw9DVDabG4v9gRDw8f8DVFxXQtTIAAgcECCtFgMi/MXeDou8lOLfpf2+QLqNe16VL6zONbWy//1fu631/3Hc8RIECAQDkBhaicrZWDCix3935+4LfWb3ZSHyXazM1bBAgQKCCgEBVAtWRcgVe9de/PHxyk3xzpBJ9e7Wy/cKS9bEOAAAECTyOgELkeBJ4isNzd/zsp9f9iLJS+S6+7em77fWPtZx8CBAgQOF5AIXIzCDy1ED24929Sl/7GaCh9et/q/PbrRtvPRgQIECBwrIBC5GIQeIrA2d29j3cpvXgslD6lT1zd2X7JWPvZhwABAgR8hMgdIHBLgeXu9T9Iqbvrlg9me6B/eLVz+muyLWchAgQIENhIwEeINmLz0lwFSvxk6ltZ+cnVtxLyzwkQIFBeQCEqb2yHQAI+QhQoLKMSIEAgo4BClBHTUvEFfA1R/AydgAABApsIKESbqHlntgJL32U222wdjAABAk8noBC5HwSeIuDnELkOBAgQaFNAIWozd6c+QcBPqnY1CBAg0KaAQtRm7k79NAJ+l5nrQYAAgfYEFKL2MnfiWwj4bfeuCAECBNoTUIjay9yJ1xBYXrp2MS0W96/x6GaP+E33m7l5iwABAoUEFKJCsJaNL7C8vH8l9f0y90n61D90def0D+Ve13oECBAgsLmAQrS5nTcbECjxk6v9ZOoGLo4jEiAQTkAhCheZgccWOLt7/V1d6u697X27brU6t3X3ba9jAQIECBDILqAQZSe14BwFjr6mqFss3tSn9Myh5+tS+kJ/ePiW1YUzF4e+63kCBAgQGEdAIRrH2S4zEDj67rPFoj+XUvreAcd57+Fhd/nDF7Y+OuAdjxIgQIDAyAIK0cjgtosvsNzdf3nX99/WL7pvTX3/3JT656XUHf3vw33qHu769KnUdR86dar/9V/+se3fin9iJyBAgMD8BRSi+WfshAQIECBAgMAtBBQiV4QAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************/goAIECAAAECBBQid4AAAQIECBBoXkAhav4KACBAgAABAgQUIneAAAECBAgQaF5AIWr+********************************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'
        $("#canvasdiv").html(`<div style="position: absolute;text-align: center;line-height: 40px;width: 100%;margin-top:100px;">
         <img src="`+imgdata+`" 
         style=" left:40%;
             width: 290px;
             height: 200px;" />
             <div >暂无数据，请配置拓扑图</div>
      </div>`);
        return null;
    }
    // if(nodelist&&nodelist.length==0){
    //     var node = new JTopo.Node("暂无数据，请配置拓扑图！");
    //     node.setImage(noImg, true);
    //     node.setSize(580, 400);
    //     node.fontSize = 30
    //     node.showSelected = false;
    //     node.dragable = false;
    //     scene.add(node);
    //     stage.centerAndZoom();
    //     return null;
    // }
    let maxNodeNum = 11;
    let nodearr = [];
    var defLoc = 0;
    scene.alpha = 1;//透明度
    var tdata0 = {};
    tdata0.isDashBoard = isDashBoard;
    tdata0.unfoldFlag = "0";
    let screenArr = [];
    // return scene;
    if(nodelist.length>0){
        //创建节点
        screenArr = []
        for(var i=0; i<nodelist.length; i++){
            var thisData = nodelist[i];
            defLoc = nodelist[i].defLoc;
            let arr = [];
            //控制告警节点是否显示
            //节点图片
            // let nodeimg = null;
            // let nodeimg = getJtopoNodeImgByValue(thisData.type,ispath,null);
            let nodeimg = getJtopoNodeImgByValue(thisData.type,ispath,null,isdarkSkin);

            // 判断二层设备的图标
            if(thisData.twoDeviceType){
                nodeimg = getJtopoNodeTwoDeviceImgByValue(thisData.type,thisData.twoDeviceType,isdarkSkin);
            }
            // 判断虚拟节点的图标
            if(thisData.virtualNode == 1){
                nodeimg = getJtopoNodeVirtualNodeImgByValue(thisData.type,thisData.virtualNode,isdarkSkin);
            }

            var text = '';
            var id = thisData.id;
            var tdata = {};
            tdata.isDashBoard = isDashBoard;
            //判断节点字段的展示
            // for(let msg in msgModalData){
            //     if(msgModalData[msg].type == 0 &&msgModalData[msg].isShow){
            //         if(thisData[msgModalData[msg].value]){
            //             if(text){
            //                 text += ("\n"+thisData[msgModalData[msg].value])
            //             }else{
            //                 text = thisData[msgModalData[msg].value];
            //             }
            //         }
            //     }
            // }
            // if (msgModalData){
            //     text += ("\n"+thisData.value)
                
            // }
            // 设置虚拟节点的名称为 ‘*’ 
            // thisData.virtualNode  == 1
            if (msgModalData){
                text += ( thisData.virtualNode == 1 ? '   *    ':thisData.value)+"\n"
                if(thisData.aliases != null){
                    text += thisData.aliases+"\n"
                }
            }

            screenArr.push(thisData.locY)
            tdata.unfoldFlag =thisData.unfoldFlag;
            tdata.tztype =thisData.type;
            tdata.defLoc =thisData.defLoc;
            tdata.locX =thisData.locX;
            tdata.locY =thisData.locY;
            tdata.twoDeviceType = thisData.twoDeviceType;
            tdata.virtualNode = thisData.virtualNode;
            if(thisData.suspect){
                text = text+"(不可信)";
            }

            let node = addNode(scene,text,nodeimg,id,tdata,ispath,isdarkSkin);
            node.type = thisData.type;
            arr.push(node);
            nodearr[i] = arr;
        }
        sessionStorage.setItem('maxNum',Math.max(...screenArr)+100)
        //节点连线
        for(var i=0; i<linklist.length; i++){
            var thisNode = null;
            var lastNode = null;
            for(var j=0; j<nodearr.length; j++){
                if(linklist[i].source == nodearr[j][0].nodeId){
                    thisNode = nodearr[j][0];
                }
                if(linklist[i].target == nodearr[j][0].nodeId){
                    lastNode = nodearr[j][0];
                }
            }
            // var iscont = false;
            // for(var j=0; j<linklist.length; j++){
            //     if(linklist[i].source == linklist[j].target&&linklist[j].source == linklist[i].target){
            //         iscont= true;
            //         break;
            //     }
            // }
            // if(iscont){
            //     continue;
            // }
            var state = showWarning?linklist[i].state:null;
            if(thisNode!=null&&lastNode!=null){
                var tdata = {};
                tdata.gaojing = showWarning?linklist[i].faultLink:null;
                tdata.linkIds = linklist[i].linkIds;
                tdata.state = showWarning?linklist[i].state:null;
                tdata.preIp = linklist[i].preIp;
                tdata.curIp = linklist[i].curIp;
                tdata.text = linklist[i].id;
                tdata.faultNum = linklist[i].faultLink;
                var  faultDesc  = linklist[i].faultDesc;
                // var  faultCont = "**********到*********中断；疑似故障节点:**************，影响1条路径，其目标节点分别为：*********(华为loopback)#$**********到*********中断；疑似故障节点:**************，影响1条路径，其目标节点分别为：*********";
                var faultarr = faultDesc&&faultDesc.split("#$");
                tdata.faultCont = faultarr;

                let nodeimg = getJtopoNodeImgByValue(lastNode.tztype,ispath,state,isdarkSkin);

                 // 判断二层设备的图标
                 if(lastNode.twoDeviceType){
                    nodeimg = getJtopoNodeTwoDeviceImgByValue(lastNode.tztype,lastNode.twoDeviceType,isdarkSkin);
                }
                // 判断虚拟节点的图标
                if(lastNode.virtualNode == 1){
                    nodeimg = getJtopoNodeVirtualNodeImgByValue(thisData.type,thisData.virtualNode,isdarkSkin);
                }

                lastNode.setImage(nodeimg, true);
                lastNode.imgSrcS = nodeimg;    // 在创建节点的时候添加自定义属性
                lastNode.serializedProperties.push("imgSrcS"); // 把自定义属性加入进节点的属性组
                addLink(scene,thisNode, lastNode,type,tdata);
            }
            // ;
        }
    }
    // 第一层以当前节点名称为 key 区分折叠状态
    var indexnode = [];
    var nodearrs = [];
    var childs = scene.childs;
    for (var j = 0; j < childs.length; j++) {
        if(childs[j].elementType == 'node'&&childs[j].unfoldFlag==1){
            childs[j].setImage(serverInvalidImg, true);
            childs[j].isshow = false;
            var thisNode = childs[j].nodeId;
            var status = [];
            var tarlink = childs[j].outLinks;
            for (var i = 0; i < tarlink.length; i++) {
                status[i] = {node: childs[j].visible, link: tarlink[i].visible};
                foldOpenStatus[thisNode] = status;
                // 收缩下一层级节点
                if (childs[j].outLinks.length != 0) {
                    dbfold(childs[j].outLinks, foldOpenStatus[thisNode][i]);
                }
            }
        }
        if(childs&&childs[j]&&childs[j].inLinks&&childs[j].elementType == 'node'&&childs[j].inLinks.length==0){
            indexnode.push(childs[j])
        }
    }
    var xx = 100;
    var yy = 100;

    try {
        if(indexnode.length>1){
            throw "开始节点大于一个，不符合规定型号"
        }
        throw "自定义布局";
        // 树形布局
        if(type=='sx'){
            scene.doLayout(JTopo.layout.TreeLayout('right', 150, 120));
        }else if(type=='xx') {
            scene.doLayout(JTopo.layout.CircleLayout(500));
        }
    }catch(err){
        console.log("throw:",err);
        // for (var i = 0; i < indexnode.length; i++) {
        //     yy=maxy+yx
        //     var arrs = [];
        //     indexnode[i].setLocation(xx,yy);
        //     istop = true;
        //     layoutCircle(indexnode[i].outLinks,arrs,xx,yy);
        // }
    }
    let d2 = new Date().getTime()
    console.log(d2 - d1,"----------耗时ms-----");
    stage.centerAndZoom();
    // getPathTopoData(scene);
    return scene;
}

var iii = 0;
/**
 * 布局
 * @param {ht.Node} root - 根节点
 */
function layoutCircle(outLinks,arrs,x,y) {
    if(!outLinks){
        if(istop){
            maxy = y;
        }else{
            maxy = y+yx;
        }
        return;
    }
    istop = true;
    let xx= x+yx;
    for (var i =0; i < outLinks.length; i++) {
// 获取到所有的孩子节点对象数组
        var children = outLinks[i].nodeZ;
        // console.log(children.nodeId,iii++);
        if(children.nodeId == '167772308_20_8'){
        }
        let yy= y+yx*i;
        for (var j =0; j < arrs.length; j++) {
            if(arrs[j].nodeId == children.nodeId){
                return;
            }
            if(arrs[j].x==xx&&arrs[j].y==yy){
                yy= yy+yx;
            }
        }
        // if(children.nodeId == '167772308_10_7'){
        //     ;
        // }
        // console.log(iii++,"-------");
        children.setLocation(xx,yy);
        arrs.push(children)
        if(maxy<yy) maxy=yy ;
        // if(children.outLinks.length==0)return;
        layoutCircle(children.outLinks,arrs,xx,yy);
    }
}


/**
 *创建node节点
 */
function addNode(scene,text,img,id,tdata,ispath,isdarkSkin){
    var node = new JTopo.Node(text);
    // node.setSize(70, 70);
    node.isshow = true;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("isshow"); // 把自定义属性加入进节点的属性组
    let fontColor = isdarkSkin == 1 ? '#D1E4FF':"#333"
    node.paintText = function(a){
        a.beginPath(),
            a.font = this.font,
            a.wrapText(this.text||'--',this.height/2,this.height,fontColor);
        a.closePath()
    };
    //创建图片类型
    // node.textOffsetX  = 20;
    node.imgSrcS = img;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("imgSrcS"); // 把自定义属性加入进节点的属性组
    node.tztype = tdata.tztype;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("tztype"); // 把自定义属性加入进节点的属性组
    node.nodeId = id;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("nodeId"); // 把自定义属性加入进节点的属性组
    node.unfoldFlag = tdata.unfoldFlag;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("unfoldFlag"); // 把自定义属性加入进节点的属性组

    
    node.twoDeviceType = tdata.twoDeviceType;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("twoDeviceType"); // 把自定义属性加入进节点的属性组

    node.virtualNode = tdata.virtualNode;    // 在创建节点的时候添加自定义属性
    node.serializedProperties.push("virtualNode"); // 把自定义属性加入进节点的属性组

    node.setImage(img, true);
    node.prop =  {"name":text,"nodeId":id,"locX":tdata.locX,"locY":tdata.locY,"defLoc":tdata.defLoc,twoDeviceType:tdata.twoDeviceType,virtualNode: tdata.virtualNode};
    node.serializedProperties.push("prop"); // 把自定义属性加入进节点的属性组
    // node.setSize(70, 70);
    node.setLocation(tdata.locX ,tdata.locY );
    // 不可信节点
    // node.alarm = tdata.alarm;
    scene.add(node);
    return node;
}
/**
 * CurveLink 曲线
 * FlexionalLink 二次折线
 * FoldLink 折线
 * Link 简单连线
 *
 * 参数：对象，第一个node对象，第二个node对象，虚实线，颜色，线名称或数据
 */
function addLink(scene,nodeA, nodeZ,type,tdata){
    // let texts = text+"ms "+diubaol+"%";
    // console.log(texts)
    tdata.state = 2
    let color = "63, 112, 255";
    var gjmsg = '';
    if(nodeZ.type == 0 || nodeA.type == 0){
        color = '211,211,211'
        // gjmsg = tdata.gaojing+'';
    }
    var link = new JTopo.Link(nodeA, nodeZ,gjmsg);
    link.fontColor = color;
    link.font = '15px Consolas';
    link.textOffsetY = 12;
    link.strokeColor = color;
    link.lineWidth = 1; // 线宽
    link.prop =  {"name":tdata.text,"告警":gjmsg,"时延":"--","linkIds":tdata.linkIds,"preIp":tdata.preIp,"丢包":"--","curIp":tdata.curIp};
    link.serializedProperties.push("prop"); // 把自定义属性加入进节点的属性组

    link.addEventListener('mouseup', function(event) {
        if(event.button == 0){
            event.scene.stage.mouseDown =false;//取消按下效果
            var info = {
                "告警数量":  tdata.faultNum || '0',
            };
            var widths = "200px";
            var width = 0;
            if(tdata&&tdata.faultCont){
                for(var i=0;i<tdata.faultCont.length;i++){
                    info['详情'+(i+1)] = tdata.faultCont[i];
                }
                if(tdata.faultCont.length>0){
                    widths = "400px";
                    width = 415;
                }
            }
            var content = '';
            var len;
            $.each(info, function (k, v) {
                content += '<h5>&nbsp;&nbsp;' + k + ' : ' + v + '</h5>';
                len = 300;
            });
            var contents = '<div style="width:'+widths+';;word-break: break-word;white-space: pre;white-space: pre-line;">'+content+'</div>'
            showToolip({
                x: event.scene.stage.pageX ,
                y: event.scene.stage.pageY ,
                width: width,
                content: contents
            });
        }
    });
    scene.add(link);
    return link;
}



function showToolip(options) {
    var temp = $(document);
    var tipsWin = temp.find('div[name=topo_tips]').hide();
    //var tipsWin = $("#topoTips")
    var tips_body = tipsWin.find('.panel-body');
    var op = options || {};
    var x = op.x;
    var y = op.y;
    var width = op.width || 200;
    var content = op.content || '';
    if (content) {
        tips_body.show();
        tips_body.html(content);
        tipsWin.css({
            "left": x,
            "top": y,
            "width": width
        }).show();
    } else {
        tips_body.hide();
        tipsWin.hide();
    }
}

/**
 * 节点显示图片
 * value：区分节点类型
 * ispath:是否是路径tuopo
 * gaojing告警：null正常；1中断；2时延劣化；3丢包劣化；其他；
 */
export function getJtopoNodeImgByValue(value,ispath,gaojing,isdarkSkin){

    let imgtype = '';
    //区分图标类型节点，探针
    if(value == 0){
        if(isdarkSkin == 1){
            imgtype = routeo2;
        }else{
            imgtype = routeo;
        }
    }else if(value == 1){
        if(isdarkSkin == 1){
            imgtype = route2;
        }else{
            imgtype = route;
        }
    }else if(value == 2){
        if(isdarkSkin == 1){
            imgtype = source2;
        }else{
            imgtype = source;
        }
    }else if(value == 3){
        if(isdarkSkin == 1){
            imgtype = server2;
        }else{
            imgtype = server;
        }
    }
    return imgtype;
};


/**
 * 判断是否是二层设备节点数据
 * value：区分节点类型
 */
export function getJtopoNodeTwoDeviceImgByValue(value,type,isdarkSkin){

    let imgtype = '';
    //  二层设备图标类型（服务器	1，交换机	2，路由器	3，防火墙	4，未知	5）
    // 二层设备图标类型（服务器	two-icon-1，交换机	two-icon-2，路由器	two-icon-3，防火墙	two-icon-4，未知	two-icon-5）
    // imgtype = twoRoute;  twoRoute2
    if(value == 0){
        if(type && type > 0){
           imgtype = twoRoute;
        }
    }else if(value == 1){
        if(type && type > 0){
            imgtype = twoRoute;
        }
    }else if(value == 2){
        if(isdarkSkin == 1){
            imgtype = source2;
        }else{
            imgtype = source;
        }
    }else if(value == 3){
        if(isdarkSkin == 1){
            imgtype = server2;
        }else{
            imgtype = server;
        }
    }
    return imgtype;
};



/**
 * 判断是否是虚拟节点数据
 * value：区分节点类型
 */
export function getJtopoNodeVirtualNodeImgByValue(value,type,isdarkSkin){

    let imgtype = '';
    //  二层设备图标类型（服务器	1，交换机	2，路由器	3，防火墙	4，未知	5）
    // 二层设备图标类型（服务器	two-icon-1，交换机	two-icon-2，路由器	two-icon-3，防火墙	two-icon-4，未知	two-icon-5）
    // imgtype = twoRoute;  twoRoute2

    debugger
    if(value == 0){
        if(type && type > 0){
           imgtype = virtualNode;
        }
    }else if(value == 1){
        if(type && type > 0){
            imgtype = virtualNode;
        }
    }else if(value == 2){
        if(isdarkSkin == 1){
            imgtype = virtualNode;
        }else{
            imgtype = virtualNode;
        }
    }else if(value == 3){
        if(isdarkSkin == 1){
            imgtype = virtualNode;
        }else{
            imgtype = virtualNode;
        }
    }
    return imgtype;
};


/**
 * 2.1拓扑图数据排序
 *
 */
export function setDataList(dataList){
    let tanz = [];
    if(dataList){
        for(let i=0; i<dataList.length; i++){
            if(dataList[i].type == 1){
                tanz.push(dataList[i]);
            }
        }
        for(let i=0; i<dataList.length; i++){
            if(dataList[i].type != 1){
                tanz.push(dataList[i]);
            }
        }
    }
    // console.log(tanz,"---tanz-");
    return tanz;
}

//1.循环层级，找到节点最多的层级下标maxIndex
function getMaxIndex(dataList){
    let maxIndex = 0;
    for(let i=1; i<dataList.length; i++){
        if(dataList[i-1].length<dataList[i].length){
            maxIndex = i;
        }
    }
    return maxIndex;
}

//计算最外层最大节点个数
function getMaxLeafCnt(data) {
    var maxcnt = 0;//本身
    var children = data.children == undefined ? new Array() : data.children;
    if (children.length > 0) {
        for (var i = 0; i < children.length; i++) {
            var f = children[i];
            if (f.children == undefined) {
                maxcnt++;
            } else {
                maxcnt += getMaxLeafCnt(f);
            }
        }
    }
    return maxcnt;

}


/**
 * 页面工具栏
 * 必须调取
 * 参数：stage对象，容器id，是否显示工具栏
 */
export function showJTopoToobar(stage,datas){
    var permissionObj = datas.permissionObj.exppng;
    var toobarDiv = '';
    toobarDiv +='<img  style="cursor:pointer;" id="zoomOutButton" title="放大" src="data:image/png;base64,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"/>'
    toobarDiv +='<img  style="cursor:pointer;" id="zoomInButton" title="缩小" src="data:image/png;base64,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"/>'
    toobarDiv +='<img  style="cursor:pointer;" id="yemianhuanyuan" title="页面还原" src="data:image/png;base64,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"/>'
    // if(datas.topoType == 'sx'){
    //     //树形选中，星形白色
    //     toobarDiv +='<img  style="cursor:pointer;" id="shuxingButton" title="树形" src="data:image/png;base64,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"/>'
    //     toobarDiv +='<img  style="cursor:pointer;" id="xingxingButton" title="星型" src="data:image/png;base64,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"/>'
    // }else{
    //     toobarDiv +='<img  style="cursor:pointer;" id="shuxingButton" title="树形" src="data:image/png;base64,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"/>'
    //     toobarDiv +='<img  style="cursor:pointer;" id="xingxingButton" title="星型" src="data:image/png;base64,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"/>'
    // }
    if(permissionObj){
        toobarDiv +='<img  style="cursor:pointer;" id="exportButton" title="导出PNG" src="data:image/png;base64,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"/>'
    }
    var toobarDiv2 = ''
    toobarDiv2 +='<input type="checkbox" name="modeRadio" value="select" id="r2"/><label for="r2"> 框选</label>'
    toobarDiv2 +='<input type="checkbox" id="zoomCheckbox" checked="checked"/><label for="zoomCheckbox"> 鼠标缩放</label>'
    toobarDiv2 +='<input type="text" id="findText" style="width: 200px;border:1px solid #eaebf0;padding:6px;border-radius:4px;outline:#00FF00" placeholder="请输入IP/名称查询" value="" onkeydown="enterPressHandler(event)">'
    toobarDiv2 +='<input type="button" id="findButton" style="padding:6px 20px;color:#fff;background: #2d8cf0;border: none;border-radius:3px;cursor:pointer;" value="查询">'
    toobarDiv2 +='<input type="button" id="fullScreenButton" style="padding:6px 20px;color:#fff;background: #2d8cf0;border: none;border-radius:3px;cursor:pointer;" value="全屏">'

    $('#butLeft').html(toobarDiv);
    $('#butRight').html(toobarDiv2);
    // 工具栏按钮处理

    $("input[name='modeRadio']").click(function(){
        // console.log(stage.mode,"----- stage.mode-------");
        if($(`input[name='modeRadio']`).is(':checked')){
            console.log(JSON.stringify(stage.mode),"----- stage.mode-------");
            stage.mode = $("input[name='modeRadio']:checked").val();
        }else{
            stage.mode ="normal";

        }
    });

    $('#shuxingButton').click(function(){
        if(datas.topoType != 'sx'){
            datas.topoType = "sx"
        }
        newJtopo(datas);
    });

    $('#xingxingButton').click(function(){
        if(datas.topoType != 'xx'){
            datas.topoType = "xx"
        }
        newJtopo(datas);
    });
    $('#yemianhuanyuan').click(function(){
        newJtopo(datas);
    });
    $('#centerButton').click(function(){
        stage.centerAndZoom(); //缩放并居中显示
    });
    $('#zoomOutButton').click(function(){
        stage.zoomOut();
    });
    $('#zoomInButton').click(function(){
        stage.zoomIn();
    });
    $('#cloneButton').click(function(){
        stage.saveImageInfo();
    });
    $('#exportButton').click(function() {
        // var canvasData = $('#canvasdiv').children('canvas');
        // var a = document.createElement('a');
        // a.href = canvasData[0].toDataURL();
        // a.download = '路径拓扑图.png';
        // a.click();a.remove();

        stage.saveImageInfo();
    });
    $('#printButton').click(function() {
        stage.saveImageInfo();
    });
    $('#topoList').click(function(){
        //直接跳转
        this.$router.push({path:"/topolist"})
        // window.parent.postMessage({type:'ifrUrl',msg:"msg"}, '*');
    });
    $('#zoomCheckbox').click(function(){
        if($('#zoomCheckbox').is(':checked')){
            stage.wheelZoom = 1.1; // 设置鼠标缩放比例
        }else{
            stage.wheelZoom = null; // 取消鼠标缩放比例
        }
    });
    $('#fullScreenButton').click(function(){
        if(datas.dataList.nodeList==null||datas.dataList.nodeList.length==0){
            $("#"+datas.contId).html("");
            setTimeout(function(){newJtopo(datas);},5);
            return;
        }


        runPrefixMethod(stage.canvas, "RequestFullScreen")
    });

    window.enterPressHandler = function (event){
        if(event.keyCode == 13 || event.which == 13){
            $('#findButton').click();
        }
    };

    // 查询
    $('#findButton').click(function(){
        if(datas.dataList.nodeList==null||datas.dataList.nodeList.length==0){
            $("#"+datas.contId).html("");
            setTimeout(function(){newJtopo(datas);},5);
            return;
        }
        var text = $('#findText').val().trim();
        //var nodes = stage.find('node[text="'+text+'"]');
        var scene = stage.childs[0];
        var nodes = scene.childs.filter(function(e){
            return e instanceof JTopo.Node;
        });
        nodes = nodes.filter(function(e){
            if(e.text == null) return false;
            return e.text.indexOf(text) != -1;
        });

        if(nodes.length > 0){
            var node = nodes[0];
            node.selected = true;
            var location = node.getCenterLocation();
            // 查询到的节点居中显示
            stage.setCenter(location.x, location.y);

            function nodeFlash(node, n){
                if(n == 0) {
                    node.selected = false;
                    return;
                };
                node.selected = !node.selected;
                setTimeout(function(){
                    nodeFlash(node, n-1);
                }, 300);
            }
            // 闪烁几下
            nodeFlash(node, 6);
        }
    });
    return stage;
}

var runPrefixMethod = function(element, method) {
    var usablePrefixMethod;
    ["webkit", "moz", "ms", "o", ""].forEach(function(prefix) {
            if (usablePrefixMethod) return;
            if (prefix === "") {
                // 无前缀，方法首字母小写
                method = method.slice(0,1).toLowerCase() + method.slice(1);
            }
            var typePrefixMethod = typeof element[prefix + method];
            if (typePrefixMethod + "" !== "undefined") {
                if (typePrefixMethod === "function") {
                    usablePrefixMethod = element[prefix + method]();
                } else {
                    usablePrefixMethod = element[prefix + method];
                }
            }
        }
    );

    return usablePrefixMethod;
};


function foldOpen(e,node){ 	 			// 折叠展开
    var thisNode = e.target.nodeId;  	// 第一层以当前节点名称为 key 区分折叠状态
    var tarlink = e.target.outLinks;
    if(tarlink == undefined){
        return
    }
    if(tarlink.length != 0 && tarlink[0].visible === true){
        var status = [];
        for (var i = 0; i < tarlink.length; i++){
            status[i] = {node: tarlink[i].nodeZ.visible, link: tarlink[i].visible};
            foldOpenStatus[thisNode] = status;
            tarlink[i].nodeZ.visible = false;
            tarlink[i].visible = false;
            // 下一层还有节点
            if( tarlink[i].nodeZ.outLinks.length != 0){
                dbfold(tarlink[i].nodeZ.outLinks, foldOpenStatus[thisNode][i]);
            }
        }
    }else if(tarlink.length != 0 && tarlink[0].visible === false){
        for (var k = 0; k < tarlink.length; k++){
            tarlink[k].nodeZ.visible =  true;
            tarlink[k].visible = true;
            tarlink[k].nodeZ.setImage(tarlink[k].nodeZ.imgSrcS, true);
            // 下一层还有节点
            if( tarlink[k].nodeZ.outLinks.length != 0){
                dbOpen(tarlink[k].nodeZ.outLinks, foldOpenStatus[thisNode][k]);
            }
        }
        //维护foldOpenStatus对象中的数据
        for (var ent in  foldOpenStatus){
            if(ent == thisNode){
                delete foldOpenStatus[thisNode];
            }
        }
    }
}
//收缩
function dbfold(dblink,foldStatus){
    var status = [];  // 下层以 status 为 key 记录
    for(var j = 0; j < dblink.length; j++){
        status[j] = {node: dblink[j].nodeZ.visible, link: dblink[j].visible};
        foldStatus.status = status;
        dblink[j].nodeZ.visible = false;
        dblink[j].visible = false;
        if( dblink[j].nodeZ.outLinks.length != 0){
            dbfold(dblink[j].nodeZ.outLinks, foldStatus.status[j]);
        }
    }
}

//打开
function dbOpen(dblink, openStatus){
    for(var j = 0; j < dblink.length; j++){
        dblink[j].nodeZ.visible = true;
        dblink[j].visible = true;
        dblink[j].nodeZ.setImage(dblink[j].nodeZ.imgSrcS, true);
        if( dblink[j].nodeZ.outLinks.length != 0){
            dbOpen(dblink[j].nodeZ.outLinks, openStatus.status[j]);
        }
    }
}