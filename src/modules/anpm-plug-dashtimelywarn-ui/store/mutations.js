"use strict";
//获取字典oid型号
export const setDevModel = (state, data) => {
  state.devModelList = data;
};
// 获取客户列表
export const setClientList = (state, data) => {
  state.ClientList = data;
};

export const updateDelayLossHistory = (state, data) => {
  if (data.level == 0) {
    state.delayLossHistory.dayData.delay = data.datas[0];
    state.delayLossHistory.dayData.loss = data.datas[1];
  }
  if (data.level == 1) {
    state.delayLossHistory.HoursData.delay = data.datas[0];
    state.delayLossHistory.HoursData.loss = data.datas[1];
  }
  if (data.level == 2) {
    state.delayLossHistory.minuteData.delay = data.datas[0];
    state.delayLossHistory.minuteData.loss = data.datas[1];
  }
  if (data.level == 3 || data.level == 4) {
    state.delayLossHistory.sencondData.delay = data.datas[0];
    state.delayLossHistory.sencondData.loss = data.datas[1];
  }
  if (data === -1) {
    //清空所有数据
    state.delayLossHistory.dayData.delay = [];
    state.delayLossHistory.dayData.loss = [];
    state.delayLossHistory.HoursData.delay = [];
    state.delayLossHistory.HoursData.loss = [];
    state.delayLossHistory.minuteData.delay = [];
    state.delayLossHistory.minuteData.loss = [];
    state.delayLossHistory.sencondData.delay = [];
    state.delayLossHistory.sencondData.loss = [];
  }
};

export const updateFlowHistory = (state, data) => {
  if (data.level == 0) {
    state.flowHistory.dayData.enter = data.datas[0];
    state.flowHistory.dayData.issue = data.datas[1];
  }
  if (data.level == 1) {
    state.flowHistory.HoursData.enter = data.datas[0];
    state.flowHistory.HoursData.issue = data.datas[1];
  }
  if (data.level == 2) {
    state.flowHistory.minuteData.enter = data.datas[0];
    state.flowHistory.minuteData.issue = data.datas[1];
  }
  if (data.level == 3 || data.level == 4) {
    state.flowHistory.sencondData.enter = data.datas[0];
    state.flowHistory.sencondData.issue = data.datas[1];
  }
  if (data === -1) {
    //清空所有数据
    state.flowHistory.dayData.enter = [];
    state.flowHistory.dayData.issue = [];
    state.flowHistory.HoursData.enter = [];
    state.flowHistory.HoursData.issue = [];
    state.flowHistory.minuteData.enter = [];
    state.flowHistory.minuteData.issue = [];
    state.flowHistory.sencondData.enter = [];
    state.flowHistory.sencondData.issue = [];
  }
};
export const updategoodRateHistory = (state, data) => {
  if (data.level == 0) {
    state.goodRateHistory.dayData.nrUseRateList = data.datas[0];
    state.goodRateHistory.dayData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.dayData.useRateList = data.datas[2];
    state.goodRateHistory.dayData.goodRateList = data.datas[3];
  }
  if (data.level == 1) {
    state.goodRateHistory.HoursData.nrUseRateList = data.datas[0];
    state.goodRateHistory.HoursData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.HoursData.useRateList = data.datas[2];
    state.goodRateHistory.HoursData.goodRateList = data.datas[3];
  }
  if (data.level == 2) {
    state.goodRateHistory.minuteData.nrUseRateList = data.datas[0];
    state.goodRateHistory.minuteData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.minuteData.useRateList = data.datas[2];
    state.goodRateHistory.minuteData.goodRateList = data.datas[3];
  }
  if (data.level == 3 || data.level == 4) {
    state.goodRateHistory.sencondData.nrUseRateList = data.datas[0];
    state.goodRateHistory.sencondData.nrGoodRateList = data.datas[1];
    state.goodRateHistory.sencondData.useRateList = data.datas[2];
    state.goodRateHistory.sencondData.goodRateList = data.datas[3];
  }
  if (data === -1) {
    //清空所有数据
    state.goodRateHistory.dayData.nrUseRateList = [];
    state.goodRateHistory.dayData.nrGoodRateList = [];
    state.goodRateHistory.dayData.useRateList = [];
    state.goodRateHistory.dayData.goodRateList = [];
    state.goodRateHistory.HoursData.nrUseRateList = [];
    state.goodRateHistory.HoursData.nrGoodRateList = [];
    state.goodRateHistory.HoursData.useRateList = [];
    state.goodRateHistory.HoursData.goodRateList = [];
    state.goodRateHistory.minuteData.nrUseRateList = [];
    state.goodRateHistory.minuteData.nrGoodRateList = [];
    state.goodRateHistory.minuteData.useRateList = [];
    state.goodRateHistory.minuteData.goodRateList = [];
    state.goodRateHistory.sencondData.nrUseRateList = [];
    state.goodRateHistory.sencondData.nrGoodRateList = [];
    state.goodRateHistory.sencondData.useRateList = [];
    state.goodRateHistory.sencondData.goodRateList = [];
  }
};

export const setdelayLTlevel = (state, data) => {
  state.delayTlevel = data;
};
export const setflowTlevel = (state, data) => {
  state.flowTlevel = data;
};
export const setflowUnit = (state, data) => {
  if (data.level == -1) {
    state.flowUnit.dayUnit = "";
    state.flowUnit.hoursUnit = "";
    state.flowUnit.minuteUnit = "";
  }
  if (data.level == 0) {
    state.flowUnit.dayUnit = data.unit;
  }
  if (data.level == 1) {
    state.flowUnit.hoursUnit = data.unit;
  }
  if (data.level == 2) {
    state.flowUnit.minuteUnit = data.unit;
  }
};
export const setgoodRateTlevel = (state, data) => {
  state.goodRateTlevel = data;
};
export const setsnmpDetails = (state, data) => {
  if (data == -1) {
    state.snmpDetails.day = {};
    state.snmpDetails.hours = {};
    state.snmpDetails.minute = {};
    state.snmpDetails.sencond = {};
  }
  if (data.level == 0) {
    state.snmpDetails.day.valueParam = data.valueParam;
    state.snmpDetails.day.flowParam = data.flowParam;
  }
  if (data.level == 1) {
    state.snmpDetails.hours.valueParam = data.valueParam;
    state.snmpDetails.hours.flowParam = data.flowParam;
  }
  if (data.level == 2) {
    state.snmpDetails.minute.valueParam = data.valueParam;
    state.snmpDetails.minute.flowParam = data.flowParam;
  }
  if (data.level == 3) {
    state.snmpDetails.sencond.valueParam = data.valueParam;
    state.snmpDetails.sencond.flowParam = data.flowParam;
  }
};
