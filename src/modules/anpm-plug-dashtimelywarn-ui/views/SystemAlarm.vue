<template>
    <!-- 故障现象详情 -->
    <Modal class="detail-modal" v-model="faultPhenomenon.open" :title="faultPhenomenon.title"
        :class="isbigscreen ? 'map-detail-model-bigscreen' : ''" width="460" :mask="true" sticky draggable
        @on-cancel="closeCancelEvent">
        <div v-if="faultPhenomenon.content" style="
        text-align: center;
          color: var(--table_content_column_link_color, #05eeff);
        word-wrap: break-word;
        word-break: break-all;
      ">
            <p v-for="(item, index) in faultPhenomenon.content" :key="index">
                {{ item }}
            </p>
        </div>
        <div slot="footer">
            <Button type="primary" size="small" @click="closeCancelEvent">{{ $t("but_confirm") }}</Button>
        </div>
    </Modal>
</template>

<script>
export default {
    name: 'SystemAlarm',
    props: {
        rowData: {
            type: Object,
            default: () => { }
        },
        isbigscreen: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            faultPhenomenon: {
                open: false,
                title: "",
                content: [],
            },
        }
    },
    watch: {
        'rowData': {
            handler(row) {

                this.faultPhenomenon.title = this.$t('comm_symptom');
                const list = (this.faultPhenomenon.content = []);
                if (row && row.faultDesc) {
                    Array.prototype.push.apply(
                        list,
                        String(row.faultDesc)
                            .split(";")
                            .map((text, index) => {
                                return text.trim();
                            })
                    );
                }
                this.faultPhenomenon.open = true;
            },
            deep: true
        }
    },
    mounted() {
        this.$emit('closeEvent');
    },
    methods: {
        closeCancelEvent() {
            this.faultPhenomenon.open = false;
            this.$emit('closeEvent');
        }
    }
}
</script>

<style></style>