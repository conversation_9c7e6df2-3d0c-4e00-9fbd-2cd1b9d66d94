<template>
    <div>
        <!--指标-->
        <!-- ,top:'20px' -->
        <Modal v-model="indexHome.show" :class="isbigscreen ? 'map-detail-model-bigscreen' : 'action-index-modal'"
            :mask="true" :width="modalWidth" sticky draggable :footer-hide="true"
            @on-cancel="indexHomeCancel('indexEvent')">
            <div slot="header">
                <span class="title-name">{{ $t("alarm_fault_index") }}</span>
                <span class="title-content">
                    {{
                        "(" +
                        this.$t("dash_fault_type") +
                        title.faultType +
                        "," +
                        this.$t("dash_link_faulty") +
                        ":" +
                    title.errorIps +
                    ")"
                    }}
                </span>
            </div>
            <index-item v-show="!isSnmp" :dataSource="dataSource" :isbigscreen="isbigscreen" ref="indexEvent"
                :data="indexHome.data" :time="saveQuery"></index-item>
        </Modal>
        <!-- :title="$t('alarm_failure_index')" -->
        <Modal v-model="snmpindexHome.show" :width="modalWidth"
            :class="isbigscreen ? 'map-detail-model-bigscreen' : 'action-index-modal'" :mask="true" sticky draggable
            :styles="{ top: '140px' }" :footer-hide="true" @on-cancel="indexHomeCancel('indexEvent')">
            <snmp-item v-show="isSnmp" :snmp="isSnmp" :dataSource="dataSource" :isbigscreen="isbigscreen"
                :time="saveQuery" ref="indexEvent1" :data="snmpindexHome.data"></snmp-item>
        </Modal>
    </div>
</template>

<script>
export default {
    name: 'NetworkFault',
    components: {
        indexItem: () => import('../components/indexItem.vue'),
        snmpItem: () => import('../components/snmpItem.vue')
    },
    props: {
        rowData: {
            type: Object,
            default: () => { }
        },
        isbigscreen: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        'rowData': {
            handler(val) {
                this.dataSource = val.dataSource;
                this.saveQuery.startTime = val.faultStarttime
                this.saveQuery.endTime = val.faultEndtime
                this.title.faultType = this.faultType[val.faultType] ?? ''
                this.title.errorIps = val.errorIps;
                if (val.dataSource == 1) {
                    this.isSnmp = true;
                    this.snmpindexHome.data = val;
                    this.snmpindexHome.show = true;
                } else {
                    this.isSnmp = false;
                    this.indexHome.data = val;
                    this.indexHome.show = true;
                }
                console.log(val);
            },
            deep: true
        }
    },
    data() {
        return {
            modalWidth: localStorage.getItem('modalWidth') * 0.98 || '100%',
            faultType: {
                '1': this.$t('dash_interrupt'),
                '2': this.$t('common_degradation'),
                '3': this.$t('common_loss_degradation'),
                '4': this.$t('dash_deterioration'),
            },
            dataSource: 0,
            saveQuery: {
                startTime: '',
                endTime: '',
            },
            isSnmp: false,
            indexHome: {
                show: false,
                data: {},
            },
            snmpindexHome: {
                show: false,
                data: {},
            },
            title: {
                faultType: 0,
                errorIps: "",
                reason: "",
            },
        }
    },
    created() {

    },
    methods: {
        //影响链路数事件
        indexHomeCancel(refName) {
            this.$Modal.visible = false;
            if (!this.isSnmp) {
                this.$refs["indexEvent"].resetRegion();
            } else if (this.isSnmp) {
            }
            this.indexHome.data = {};
            this.snmpindexHome.data = {};
            this.$emit('closeEvent');
        },
        timeChange(date) {
            //毫秒时间转化'YYYY-MM-dd HH:mm:ss'
            var now = new Date(date),
                y = now.getFullYear(),
                m = now.getMonth() + 1,
                d = now.getDate();
            return (
                y +
                "-" +
                (m < 10 ? "0" + m : m) +
                "-" +
                (d < 10 ? "0" + d : d) +
                " " +
                now.toTimeString().substr(0, 8)
            );
        },
    }
}
</script>

<style></style>