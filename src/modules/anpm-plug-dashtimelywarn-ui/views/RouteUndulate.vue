<template>
    <div>
        <Modal v-model="routeFluctuation.open" :title="routeFluctuation.title"
            :class="isbigscreen ? 'map-detail-model-bigscreen' : ''" width="100%" :styles="{ top: '140px' }"
            :mask="true" sticky draggable @on-cancel="closeEvent">
            <Loading :loading="loading"></Loading>
            <div v-if="routeFluctuation.content" class="routeFluctuation">
                <div v-if="routeFluctuation.content.text"
                    style="text-align: left;word-wrap: break-word;word-break: break-word;">
                    <span>{{ routeFluctuation.content.text }}</span>
                    <span v-if="routeFluctuation.content.title">
                        （<span class="starting-path">{{
                            routeFluctuation.content.title
                            }}</span>）。
                    </span>
                </div>
                <div v-if="routeFluctuation.content.topology">
                    <p class="title">{{ $t("topo_path") }}：</p>
                    <div class="topologys">
                        <div id="drawing-board" class="drawing-board" style="height: 600px"></div>
                        <!--<span class="no-data-available">'+$t('common_No_data')+'</span>-->
                    </div>
                </div>
            </div>
            <div slot="footer"></div>
        </Modal>
    </div>
</template>

<script>
import { newJtopo } from "../style/jtopo-editor3.js";
export default {
    name: 'RouteUndulate',
    props: {
        rowData: {
            type: Object,
            default: () => { }
        },
        isbigscreen: {
            type: Boolean,
            default: false,
        }
    },

    data() {
        return {
            loading: false,
            routeFluctuation: {
                open: false,
                title: this.$t('server_fluctuation'),
                content: {
                    text: "",
                    title: "",
                    topology: {},
                },
            },
            isSnmp: false,
            dataSource: 0,
        }
    },
    watch: {
        'rowData': {
            handler(val) {
                let row = Object.assign({}, val);
                const msg = [];
                if (row.sourceIp) {
                    msg.push("源：");
                    msg.push(row.sourceIp);
                }
                msg.push(row.faultDesc);
                let text = msg.join("");
                if (String(text).includes("初始路径")) {
                    this.routeFluctuation.content.title = "起始路径";
                    text = text.replace(/(\s|\。)+$/g, "");
                } else {
                    this.routeFluctuation.content.title = "";
                }
                this.routeFluctuation.content.text = text;

                var linkId = "";
                if (row.linkIds) {
                    var linkIds = row.linkIds.split(",");
                    if (linkIds.length > 0) {
                        linkId = linkIds[0];
                    }
                }

                let params = [
                    {
                        orgId: row.orgId || "",
                        probeIp: "root",
                        probeName: "root",
                        linkId: linkId,
                        routePath: row.errorIps,
                    },
                    {
                        orgId: row.orgId || "",
                        probeIp: "root",
                        probeName: "root",
                        linkId: linkId,
                        routePath: row.errorIpsDetail,
                    },
                ];
                this.getRouteCanvarsList(params);
            },
            deep: true
        }
    },
    created() {

    },
    methods: {
        closeEvent() {
            this.$emit('closeEvent');
        },
        //获取路由波动topo图画图数据
        getRouteCanvarsList(param) {
            console.log(param);
            this.loading = true;
            this.$http
                .PostJson("/fault/fluctuationsTopo", param)
                .then(({ code, data, msg }) => {
                    if (code === 1 && data) {
                        let newRoue = param[1].routePath.split(",");
                        data.nodeList.map((item, index) => {
                            if (item.value == newRoue[0]) {
                                item.type = 2;
                            }
                            if (item.value == newRoue[newRoue.length - 1]) {
                                item.type = 3;
                            }
                            return item;
                        });
                        let datas = {
                            dataList: data,
                            // topoType: this.showShuxing,
                            contId: "drawing-board",
                            msgModal: true,
                            isDashBoard: false,
                            // showWarning: this.showWarning,
                            // ispath: true,
                            permissionObj: { exppng: true },
                            // thistopo: this.thistopo
                        };
                        newJtopo(datas, top.window.isdarkSkin);
                        this.loading = false;
                    }
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                    this.routeFluctuation.open = true;
                });
        },
    }
}
</script>

<style></style>