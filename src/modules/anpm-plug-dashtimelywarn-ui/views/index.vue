<template>
    <section ref="sectionDiv" class="sectionDiv" :style="'background:' +
        (isbigscreen ? '#060D15' : currentSkin == 1 ? 'transparent' : '#ffffff')
        ">
        <div class="section-body">
            <div class="section-body-content" style="padding-top: 0">
                <Loading :loading="loadingData"></Loading>
                <div :style="allDiv">
                    <div class="header-box" v-if="horizontalShowType && horizontalRollAlarmListData.length > 0">
                        <div class="scroll-container" @click="clickup($event)">
                            <vue-seamless-scroll :data="horizontalRollAlarmListData" :class-option="scrollOptions1"
                                style="height: 58px; white-space: nowrap">
                                <div v-for="(item, index) in horizontalRollAlarmListData" :key="index"
                                    class="scroll-item" :data-obj="JSON.stringify(item)">
                                    <span style="cursor: pointer" :data-obj="JSON.stringify(item)">{{
                                        item.faultStarttime }}</span>
                                    <span :data-obj="JSON.stringify(item)" :class="{ 'white-text': isbigscreen }"
                                        :style="{
                                            color: item.faultType == 1 ? '#FE5C5E' : '#FEA31B',
                                        }">{{ getFaultTypeText(item.faultType) }}</span>
                                    <span :data-obj="JSON.stringify(item)" :class="{ 'white-text': isbigscreen }">{{
                                        item.errorIps
                                    }}</span>
                                    <span :data-obj="JSON.stringify(item)" :class="{ 'white-text': isbigscreen }">{{
                                        item.faultDesc
                                    }}</span>
                                    <span :data-obj="JSON.stringify(item)" :style="{
                                        color: item.reportState == 1 ? '#FE5C5E' : '#05EEFF',
                                        'margin-right': '40ch',
                                    }">{{ getreportStateText(item.reportState) }}
                                    </span>
                                </div>
                            </vue-seamless-scroll>
                        </div>
                    </div>
                    <div v-if="alarmListShowType && tableList.length > 0">
                        <!-- 表头：固定不滚动 -->
                        <Table class="headerTable" :columns="columns" :show-header="true" no-data-text="">
                        </Table>
                        <!-- 表格内容（可滚动） -->
                        <div v-if="!scrollShow" :style="tableStyle">
                            <vue-seamless-scroll :data="tableList" style="height: 100%;" :class-option="scrollOptions">
                                <!-- 使用自定义表格结构替代 Table 组件 -->
                                <div class="custom-table">
                                    <div v-for="(row, index) in tableList" :key="index" class="custom-table-row">
                                        <!-- 时间列 -->
                                        <div class="custom-table-cell time-cell" @click="actionClick(row)">
                                            <span style="color: #05eeff; cursor: pointer">
                                                {{ row.faultStarttime }}
                                            </span>
                                        </div>
                                        <!-- 故障类型列 -->
                                        <div class="custom-table-cell type-cell">
                                            <div class="text-ellipsis" :style="{
                                                color: row.faultType == 1 ? '#FE5C5E' : '#FEA31B',
                                                'word-break': 'keep-all',
                                                'white-space': 'nowrap',
                                                'overflow': 'hidden',
                                                'text-overflow': 'ellipsis'
                                            }" :title="getFaultTypeText(row.faultType)">
                                                {{ getFaultTypeText(row.faultType) }}
                                            </div>
                                        </div>
                                        <!-- 故障链路列 -->
                                        <div class="custom-table-cell link-cell">
                                            <div :class="{ 'white-text': isbigscreen }" style="white-space: pre-wrap">
                                                {{ row.dealStatus == 7 || row.faultType == 7 || row.dealStatus == 8 ||
                                                    row.faultType == 8 ||
                                                    row.faultType == 13 || row.faultType == 12 || row.faultType == 11 ? '--'
                                                    : row.errorIps }}
                                            </div>
                                        </div>
                                        <!-- 故障现象列 -->
                                        <div class="custom-table-cell desc-cell">
                                            <div :class="{ 'white-text': isbigscreen }" class="text-ellipsis"
                                                style="word-break: keep-all; white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                                :title="row.faultDesc || '--'"
                                                @mouseover="showTooltip($event, row.faultDesc)">
                                                {{ row.faultDesc || '--' }}
                                            </div>
                                        </div>
                                        <!-- 故障状态列 -->
                                        <div class="custom-table-cell state-cell">
                                            <span :style="{
                                                color: row.reportState == 1 ? '#FE5C5E' : '#05EEFF'
                                            }">
                                                {{ getreportStateText(row.reportState) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </vue-seamless-scroll>
                        </div>
                        <div v-if="scrollShow" :style="tableStyle">
                            <Table :columns="columns" style="pointer-events: auto" :data="tableList"
                                :show-header="false">
                                <template slot-scope="{ row }" slot="faultStarttime">
                                    <span @click="actionClick(row)" style="color: #05eeff; cursor: pointer">
                                        {{ row.faultStarttime }}
                                    </span>
                                </template>
                            </Table>
                        </div>
                    </div>

                    <!-- <Table v-if="tableList.length > 0" class="dashTimelyWarn" ref="tableList" border
                        :height="tablHeight" :columns="columns" :data="tableList" :loading="loading" size="small">
                    </Table> -->
                </div>
            </div>
        </div>
        <div v-if="txtLoading" class="no-data-box">
            <div :class="{
                'no-data-tip': true,
                'no-data-tip-white': currentSkin == 0,
            }"></div>
            <p>{{ $t("alarm_no_data") }}</p>
        </div>

        <!-- 网络故障 -->
        <NetworkFault :rowData="netWorkData" :isbigscreen="isbigscreen" @closeEvent="closeEvent"></NetworkFault>
        <!-- 路由波动 -->
        <RouteUndulate :rowData="routeUndulateData" :isbigscreen="isbigscreen" @closeEvent="closeEvent"></RouteUndulate>
        <!-- 流量拥塞 -->
        <SpecialCongestion :rowData="specialCongestionData" :isbigscreen="isbigscreen" @closeEvent="closeEvent">
        </SpecialCongestion>
        <!-- 系统告警 -->
        <SystemAlarm :rowData="systemAlarmData" :isbigscreen="isbigscreen" @closeEvent="closeEvent"></SystemAlarm>
    </section>
</template>

<script>
import "@/config/page.js";
import ipv6Format from "@/common/ipv6Format";
import vueSeamlessScroll from "vue-seamless-scroll";

export default {
    name: "dashTimelyWarn",
    components: {
        NetworkFault: () => import('./NetworkFault.vue'),
        RouteUndulate: () => import('./RouteUndulate.vue'),
        SpecialCongestion: () => import('./SpecialCongestion.vue'),
        SystemAlarm: () => import('./SystemAlarm.vue'),
        vueSeamlessScroll
    },
    data() {
        return {
            scrollShow: false,
            allDiv: {
                width: "100%"
            },
            currentSkin: sessionStorage.getItem('dark') || 1,
            faultPhenomenon: {
                open: false,
                title: "",
                content: [],
            },
            loadingData: false,
            txtLoading: false,
            netWorkData: {},
            routeUndulateData: {},
            specialCongestionData: {},
            systemAlarmData: {},
            illegalAccessAlarmData: {},
            tablHeight: '',
            //刷新时间间隔
            intervalTime: null,
            //刷新参数
            interrefresh: null,
            //搜索字段
            query: {
                componentId: null,
                componentRealAlarmConfigs: [
                    // {
                    //     "alarmNoRecoverStatus": 0, //告警状态未恢复 ,0选择，1不选择
                    //     "alarmRecoverStatus": 0, //告警状态恢复 ,0选择，1不选择
                    //     "alarmTypeBroken": 0, //告警类型中断 ,0选择，1不选择
                    //     "alarmTypeDelay": 0, //告警类型时延劣化 ,0选择，1不选择
                    //     "alarmTypePackLoss": 0, //告警类型丢包时延 ,0选择，1不选择
                    //     "maintainLevelOne": 0, //运维等级
                    //     "maintainLevelThree": 0, //运维等级三级,0选择，1不选择
                    //     "maintainLevelTwo": 0, //运维等级二级,0选择，1不选择
                    //     "showRecentlyAlarmNum": 0, //显示最新告警条数
                    //     "showType": 0, //实时告警配置是否显示:默认0显示，1不显示
                    //     "type": 0 //实时告警配置类型:默认0告警列表，1横向滚动消息栏,2公共链路告警
                    // }
                ],
                groupIds: "",
            },
            //loading状态
            loading: false,
            //表格数据
            tableList: [
                // {
                //     "id": 11128,
                //     "orgId": 1,
                //     "faultStarttime": "2025-03-07 10:38:00",
                //     "faultType": 1,
                //     "errorIps": "***********-************",
                //     "faultDesc": "疑似链路***********-************发生中断故障，影响1条路径，源ip为：**********,其目标节点分别为：************",
                //     "reportState": 1,
                //     "extraFlag": 0,
                //     "dataSource": 0,
                //     "bizDataId": "1000",
                //     "linkIds": "1025",
                //     "recoveryed": 1,
                //     "dealDuration": "3分46秒",
                //     "dealStatus": 1
                // }
            ],
            // 滚动
            horizontalRollAlarmListData: [],
            columns: [
                {
                    title: this.$t('dash_alarm_time'),
                    slot: "faultStarttime",
                    width: 160,
                    align: "left",
                    className: "bgColor",
                },
                {
                    title: this.$t('alarm_fault_type'),
                    key: 'faultType',
                    align: 'center',
                    width: 120,

                    render: (h, params) => {
                        let str = params.row.faultType, text = '';
                        let color = "#fff";
                        switch (str) {
                            case 1:
                                text = this.$t('dash_interrupt');
                                color = "#FE5C5E";
                                break;
                            case 2:
                                text = this.$t('dash_delay_deterioration');
                                color = "#FEA31B";
                                break;
                            case 3:
                                text = this.$t('dash_packet_loss');
                                color = "#FEA31B";
                                break;
                            case 7:
                                text = this.$t('dash_routing_fluctuation');
                                break;
                            case 8:
                                text = this.$t('dash_urgent_notice');
                                break;
                            case 9:
                                text = this.$t('dash_line_congestion');
                                break;
                            case 10:
                                text = this.$t('dash_port_congestion');
                                break;
                            case 11:
                                text = this.$t('alarm_offline');
                                break;
                            case 12:
                                text = this.$t('legalResPool_illegal_access');
                                break;
                            case 13:
                                text = this.$t('port_status_change_alarm');
                                break;
                            default:
                                text = '--';
                                break
                        }
                        let Arr1 = h('span', text);

                        return h(
                            "div",
                            {
                                class: {
                                    "text-ellipsis": true,
                                },
                                style: {
                                    color: color,
                                    "word-break": "keep-all",
                                    "white-space": "nowrap",
                                    "overflow": "hidden",
                                    "text-overflow": "ellipsis",
                                },
                                domProps: {
                                    title: text,
                                },
                            },
                            [Arr1]
                        );
                    }
                },
                {
                    title: this.$t('dash_faulty_link'),
                    key: 'errorIps',
                    width: 320,
                    align: 'center',
                    ellipsis: true,
                    render: (h, params) => {
                        if (params.row.dealStatus == 7 || params.row.faultType == 7 || params.row.dealStatus == 8 || params.row.faultType == 8 || params.row.faultType == 13 || params.row.faultType == 12 || params.row.faultType == 11) {
                            return h("div", "--");
                        }
                        let str = params.row.errorIps;
                        str =
                            (str === undefined || str === null || str === ""
                                ? "--"
                                : str);
                        //  ziu
                        let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIpv6Double(str, maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                    }
                },
                {
                    title: this.$t('alarm_symptom'),
                    key: "faultDesc",
                    align: "left",
                    minWidth: 100,
                    className: "bgColor",
                    render: (h, params) => {
                        let str = params.row.faultDesc;
                        return h('div', {
                            class: {
                                'text-ellipsis': true
                            },
                            style: {
                                'word-break': 'keep-all',
                                'white-space': 'nowrap',
                                'overflow': 'hidden',
                                'text-overflow': 'ellipsis'
                            },
                            domProps: {
                                title: str
                            }
                        }, str);
                    }
                },
                {
                    title: this.$t('dash_fault_state'),
                    key: 'reportState',
                    align: 'center',
                    width: 100,
                    render: (h, params) => {
                        if (params.row.dealStatus == 8 || params.row.faultType == 8 || params.row.faultType == 13) {
                            return h('span', '--');
                        }
                        let str = Number(params.row.reportState), text = '--';
                        let colorV = "";
                        if (params.row.dealStatus != 7 && params.row.faultType != 7) {
                            if (params.row.faultType != 12 && params.row.faultType != 13) {
                                switch (str) {
                                    case 1:
                                        text = this.$t('common_unrecovered');
                                        colorV = "#FE5C5E";
                                        break;
                                    case 0:
                                        text = this.$t('common_recovered');
                                        colorV = this.currentSkin == 1 ? "#05EEFF" : "#0290FD";
                                        break;
                                    default:
                                        text = '--';
                                        break;
                                }
                            } else {
                                str = params.row.dealStatus;
                                switch (str) {
                                    case 1:
                                        text = this.$t('legalResPool_not_verified');
                                        colorV = "#FE5C5E";
                                        break;
                                    case 4:
                                        text = this.$t('legalResPool_verified');
                                        colorV = this.currentSkin == 1 ? "#05EEFF" : "#0290FD";
                                        break;
                                    default:
                                        text = '--';
                                        break;
                                }
                            }

                        }
                        return h('span', {
                            class: (str === 0 ? 'action-red' : (str === 1 ? 'action-green' : '')),
                            style: {
                                'color': colorV
                            },
                        }, text)
                    }
                }
            ],
            isbigscreen: false,
            tableStyle: {
                height: "100%",
                overflowY: "hidden",
                position: "relative"
            },
            scrollOptions: {
                step: 1,
                limitMoveNum: 4,
                hoverStop: true,
                direction: 1, // -1停止滚动 0向下 1向上 2向左 3向右
                singleHeight: 50,
                waitTime: 5000,
            },
            scrollOptions1: {
                step: 0.5,
                limitMoveNum: 1,
                hoverStop: true,
                direction: 2, // 2 表示从右到左滚动
                singleWidth: 1275,
                waitTime: 5000,
            },
            debounceTimer: null,
            horizontalShowType: true,
            alarmListShowType: true,
        };
    },
    created() {
        console.log(parent.window.isEdit, 'parent.window.isEdit');
        // isEdit为true表示在大屏编辑模式下,false 展示模式下 第一次创建也属于编辑
        if (parent.window.isEdit) {
            let editName = window.frames.frameElement.getAttribute('editName')
            let groupIds = editName.split('&&groupIds=')[1].split('&&')[0];
            let data = editName.split('&&componentRealAlarmConfigs=')[1];
            // console.log("editName------------", editName);
            // console.log("groupIds------------", groupIds);
            // console.log("data------------", data);
            // debugger

            let componentRealAlarmConfigs = JSON.parse(data);
            console.log(componentRealAlarmConfigs, 'componentRealAlarmConfigs');
            if (groupIds) {
                this.query.groupIds = groupIds;
            }
            if (componentRealAlarmConfigs && componentRealAlarmConfigs.length > 0) {
                this.query.componentRealAlarmConfigs = componentRealAlarmConfigs;
            }
            delete this.query.componentId;
            console.log(this.query, 'this.query');
            // debugger
        } else {
            // 读取
            let componentId = window.frames.name.split('&&componentId=')[1].split('&&')[0];
            console.log(componentId, 'componentId');
            if (componentId) {
                this.query.componentId = componentId;
                delete this.query.componentRealAlarmConfigs;
                delete this.query.groupIds;
            }
            // debugger
            if (window.frames.frameElement) {
                window.frames.frameElement.contentWindow.close();
            }
        }

        this.getList(this.query);
    },
    mounted() {
        window.addEventListener('message', e => {
            if (e.data && e.data.type === 'mouseEnterState') {
                // 使用防抖控制状态更新
                this.debounceMouseState(e.data.data);
            }
        });

        // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        this.tablHeight = document.body.clientHeight
        console.log(document.body.clientWidth);
        const sectionDiv = this.$refs.sectionDiv;
        if (document.body.clientWidth < 840) {
            this.allDiv.width = '840px';
            sectionDiv.style.overflowX = 'auto';
            if (this.tablHeight >= 261) {
                sectionDiv.style.overflowY = 'hidden';
            } else {
                sectionDiv.style.overflowY = 'auto';
            }
        }
        this.isbigscreen = window.name.search("bigscreen") != -1;
        if (this.intervalTime && Number(this.intervalTime)) {
            this.interrefresh = setInterval(() => {
                this.loadingData = true;
                this.getList(this.query)
            }, this.intervalTime * 1000);
        }
    },
    methods: {
        showTooltip(event, content) {
            if (!content) return;

            // 确保元素有title属性
            event.target.setAttribute('title', content);

            // 可选：如果需要自定义悬浮提示，可以使用下面的代码
            // 注意：这需要引入iView的Tooltip组件
            /*
            this.$Notice.info({
                title: '故障现象',
                desc: content,
                duration: 4.5
            });
            */
        },
        clickup(e) {
            let target = e.target;
            if (target.tagName == "DIV" || target.tagName == "SPAN") {
                console.log(JSON.parse(target.dataset.obj));
                this.actionClick(JSON.parse(target.dataset.obj))
            }
        },
        // clickup1(e) {
        //     let target = e.target;
        //     if (target.tagName == "SPAN") {
        //         console.log(JSON.parse(target.dataset.obj));
        //         this.actionClick(JSON.parse(target.dataset.obj))
        //     }
        // },
        getreportStateText(str) {
            switch (str) {
                case 1:
                    return this.$t('common_unrecovered');
                case 0:
                    return this.$t('common_recovered');
                case 4:
                    return this.$t('legalResPool_verified');
                default:
                    return '--';
            }
        },
        getFaultTypeText(faultType) {
            switch (faultType) {
                case 1:
                    return this.$t('dash_interrupt');
                case 2:
                    return this.$t('dash_delay_deterioration');
                case 3:
                    return this.$t('dash_packet_loss');
                case 7:
                    return this.$t('dash_routing_fluctuation');
                case 8:
                    return this.$t('dash_urgent_notice');
                case 9:
                    return this.$t('dash_line_congestion');
                case 10:
                    return this.$t('dash_port_congestion');
                case 11:
                    return this.$t('alarm_offline');
                case 12:
                    return this.$t('legalResPool_illegal_access');
                case 13:
                    return this.$t('port_status_change_alarm');
                default:
                    return '--';
            }
        },
        handleStorageChange(event) {
            if (event.key === 'dark') {
                this.currentSkin = event.newValue; // 更新肤色
            }
        },
        closeEvent() {
            this.loadingData = false;
        },
        actionClick(row) {
            console.log(row, 'row')
            const netWorkArray = [1, 2, 3, 4]
            const type = row.faultType
            console.log(type, '故障详情type。。。。')
            this.loadingData = true;
            if (netWorkArray.includes(type)) {
                this.netWorkData = { ...row }
                this.loadingData = false;
            }
            if (type == 7) {
                this.loadingData = false;
                this.routeUndulateData = { ...row }
            }
            if (type == 9 || 10) {
                this.loadingData = false;
                this.specialCongestionData = { ...row }
            }
            if (type == 11 || type == 12 || type == 13) {
                this.loadingData = false;
                this.systemAlarmData = { ...row }
            }
        },

        // 打开故障现象详情
        faultPhenomenonOpen(row) {
            console.log(row);
            this.faultPhenomenon.title = this.$t("comm_symptom");
            const list = (this.faultPhenomenon.content = []);
            if (row && row.faultDesc) {
                Array.prototype.push.apply(
                    list,
                    String(row.faultDesc)
                        .split(";")
                        .map((text, index) => {
                            return text.trim();
                        })
                );
            }
            this.faultPhenomenon.open = true;
        },
        faultPhenomenonClose() {
            this.faultPhenomenon.open = false;
            this.loadingData = false;
        },
        getList(param) {
            //OID列表查询请求
            this.loadingData = true;
            this.txtLoading = false;
            //this.$http.wisdomPost("/dashboard/getFaultList", param).then(res => {
            this.$http.PostJson("/dashboard/getFaultListByQuery", param).then(res => {
                if (res.code === 1) {
                    this.loadingData = false;
                    // 表格
                    this.tableList = res.data.alarmListData;
                    console.log(this.tableList, '表格内容-------');
                    // 判断是否滚动
                    if (res.data.alarmListData && res.data.alarmListData.length > 0) {
                        console.log('this.tablHeight', this.tablHeight);
                        let H = Math.floor((this.tablHeight - 118) / 50);
                        // 通过计算显示表格内容/高度,若长度小于等于计算的数量,则不滚动
                        if (res.data.alarmListData.length <= H) {
                            this.scrollShow = true;
                        } else {
                            this.scrollShow = false;
                            this.scrollOptions.limitMoveNum = res.data.alarmListData.length;
                            this.tableStyle.height = res.data.alarmListData.length * 50 + 'px';
                        }
                        // debugger
                    }
                    this.alarmListShowType = res.data.alarmListShowType == 1 ? false : true;
                    // 滚动
                    this.horizontalRollAlarmListData = res.data.horizontalRollAlarmListData;
                    console.log(res.data.horizontalRollAlarmListData, '滚动内容--------');
                    this.scrollOptions1.limitMoveNum = res.data.horizontalRollAlarmListData.length;
                    this.horizontalShowType = res.data.horizontalShowType == 1 ? false : true;
                    if (res.data.alarmListData.length == 0 && res.data.horizontalRollAlarmListData.length == 0) {
                        // 没有数据不加滚动条,且显示暂无数据
                        this.txtLoading = true;
                        this.allDiv.width = '100%';
                        const sectionDiv = this.$refs.sectionDiv;
                        sectionDiv.style.overflowX = 'hidden';
                        sectionDiv.style.overflowY = 'hidden';
                    }
                } else {
                    this.loadingData = false
                    this.txtLoading = true;
                    this.tableList = [];
                    this.horizontalRollAlarmListData = [];
                }
            }).catch((error) => {
                this.loadingData = false;
                // this.tableList = [];
                // this.horizontalRollAlarmListData = [];
                if (parent.loading) {
                    parent.loading['dashtimelywarn'] = false;
                }
            }).finally(() => {
                this.loadingData = false;
                if (parent.loading) {
                    parent.loading['dashtimelywarn'] = false;
                }
            });
        },
        // 改为防抖函数
        debounceMouseState(newState) {
            // console.log('收到状态更新请求:', newState);

            // 清除之前的定时器
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
                console.log('清除之前的定时器，重新计时');
            }

            // 设置新的定时器
            this.debounceTimer = setTimeout(() => {
                console.log('防抖时间结束，更新鼠标状态为:', newState);
                this.scrollOptions.direction = newState == true ? -1 : 1;
                this.scrollOptions1.direction = newState == true ? -1 : 2;
                this.debounceTimer = null;
            }, 1000); // 500毫秒防抖
        }
    },
    watch: {},
    computed: {},
    beforeDestroy() {
        // 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
        if (this.interrefresh) {
            clearInterval(this.interrefresh);
            this.interrefresh = null;
        }
        // 清理定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
    }
};
</script>

<style lang='less'>
.no-data-box {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.sectionDiv {
    height: 100%;
}

/* 隐藏滚动条交界区域 */
.sectionDiv::-webkit-scrollbar-corner {
    background: transparent;
}

.white-text {
    color: white;
}

.scroll-container {
    margin: 0 30px;
    overflow: hidden;
}

.scroll-item {
    display: inline-block;
    pointer-events: auto;
    margin-right: 0;
    /* 移除右边距 */

    span {
        margin-right: 25px;

        &:last-child {
            margin-right: 0;
        }
    }
}

.header-box {
    text-align: left;
    // padding: 0 20px;
    width: 100%;
    height: 58px;
    font-size: 16px;
    font-weight: 400;
    line-height: 58px;
    margin-bottom: 20px;
    box-shadow: inset 0px 0px 10px 1px rgba(61, 215, 255, 0.84);
    border: 1px solid #056672;
    border-radius: 5px;
}

.header-box span:first-child {
    margin-left: 30px;
    color: #05eeff;
}

.headerTable {
    .ivu-table-tip {
        height: 0px !important;
    }
}

.header-box span {
    margin-right: 25px;
    /* 调整间距 */
}

.header-box span:last-child {
    margin-right: 0;
    /* 去掉最后一个 `span` 的间距 */
}

.no-data-tip {
    position: relative;
    height: 120px;
    width: 101px;

    background-image: url("../../../assets/dashboard/fileempty.png");
    background-repeat: no-repeat;
    background-size: 120px 101px;
    background-position: center top;
}

.no-data-tip-white {
    background-image: url("../../../assets/dashboard/fileempty2.png") !important;
}

.no-data-tip p {
    display: block;
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 0;
}

.searchTop .ivu-select-input {
    height: 32px;
    line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
    background: #f1f6fe !important;
}

.task-modal .ivu-modal-body .ivu-form-item.multipleSelect .ivu-select-selection {
    height: auto !important;
    min-height: 32px;
}

.multipleSelect .ivu-select-input {
    padding: 0 !important;
}
</style>
<style lang="less" scoped>
.text-ellipsis {
    cursor: pointer;
    max-width: 100%;
    display: inline-block;
    vertical-align: middle;
    position: relative;

    /* 确保悬浮提示可以显示 */
    &:hover {
        overflow: visible;
        z-index: 1;
    }
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
}

.custom-table-row {
    display: flex;
    height: 50px;
    align-items: center;
    border-bottom: 1px solid var(--thd_border_color, #061c2b);
}

.custom-table-cell {
    padding: 0 10px;
    overflow: hidden;
}

.time-cell {
    width: 160px;
    text-align: left;
}

.type-cell {
    width: 120px;
    text-align: center;
}

.link-cell {
    width: 320px;
    text-align: center;
}

.desc-cell {
    flex: 1;
    text-align: left;
}

.state-cell {
    width: 100px;
    text-align: center;
}

/deep/ .ivu-table th {
    background-color: var(--th_b_color, #061c2b) !important;
    font-weight: 700;
    font-size: 14px;
}

/deep/ .ivu-table-wrapper-with-border {
    border: none;
}

/deep/ .ivu-table td,
.ivu-table th {
    border-bottom: 1px solid var(--thd_border_color, #061c2b) !important;
}

/deep/ .ivu-table-header thead tr th {
    padding: 14px 0 !important;
}

/deep/ .ivu-table td {
    height: 50px !important;
}

/deep/ .ivu-table-overflowX {
    overflow: hidden !important;
}

//  /deep/.dashTimelyWarn ::-webkit-scrollbar-track {
//     box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//     // box-shadow: inset 0 0 5px var(--scrollbar_track_shadow_color,rgba(0, 0, 0, 0.2));
//     border-radius: 10px;
//     background: #0e1012;
//     // background: var(--scrollbar_track_bg_color,#06324D);
//   }
// /deep/ .dashTimelyWarn ::-webkit-scrollbar-thumb {
//     border-radius: 10px;
//     box-shadow: inset 0 0 5px rgba(6, 50, 77, 1);
//     // box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color,rgba(6, 50, 77, 1));
//     background: #015197;
//     // background: var(--scrollbar_thumb_bg_color,#015197);
//   }
/* 使用 ::v-deep 选择器来设置滚动条样式 */
::v-deep .ivu-table-wrapper {
    overflow: auto;
    /* 确保可以滚动 */
}

/* 滚动条轨道 */
::v-deep .ivu-table-wrapper ::-webkit-scrollbar-track {
    border-radius: 10px;
    /* 轨道圆角 */
}

/* 滚动条滑块 */
::v-deep .ivu-table-wrapper ::-webkit-scrollbar-thumb {
    background: var(--scrollbar_thumb_bg_color, #015197);
    /* 滑块背景颜色 */
    box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color, rgba(6, 50, 77, 1));
    /* 滑块阴影 */
}

/* 滚动条交叉区域 */
::v-deep .ivu-table-wrapper ::-webkit-scrollbar-corner {
    background: transparent;
    /* 交叉区域背景颜色 */
}

/* 滚动条的宽度 */
::v-deep .ivu-table-wrapper ::-webkit-scrollbar {
    width: 10px;
    /* 滚动条宽度 */
}

/* 如果需要，设置垂直和水平滚动条的样式 */
::v-deep .ivu-table-wrapper ::-webkit-scrollbar {
    height: 10px;
    /* 水平滚动条高度 */
}
</style>
