<template>
    <div>
        <!--  专线趋势图  -->
        <Modal width="1200" :mask="true" sticky footer-hide :class="isbigscreen ? 'map-detail-model-bigscreen' : ''"
            draggable v-model="specialChart.show" :styles="{ top: '140px' }" @on-cancel="closeEvent">
            <specialechart v-show="specialChart.show" :isbigscreen="isbigscreen" :clickTime="specialChart.clickTime"
                :chartData="specialChart.data"></specialechart>
        </Modal>

        <!--  端口趋势图  -->
        <Modal width="1200" :mask="true" sticky footer-hide :class="isbigscreen ? 'map-detail-model-bigscreen' : ''"
            draggable v-model="portChart.show" :styles="{ top: '140px' }" @on-cancel="closeEvent">
            <portechartLook v-show="portChart.show" :isbigscreen="isbigscreen" :clickTime="portChart.clickTime"
                :chartData="portChart.data"></portechartLook>
        </Modal>
    </div>
</template>

<script>
export default {
    name: 'SpecialCongestion',
    components: {
        specialechart: () => import('../components/specialechartLook.vue'),
        portechartLook: () => import('../components/portechartLook.vue')
    },
    props: {
        rowData: {
            type: Object,
            default: () => { }
        },
        isbigscreen: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            specialChart: {
                //专线趋势图
                show: false,
                clickTime: new Date().getTime() + "_" + Math.random(),
                data: {},
                delayParam: {
                    preNodeIp: "",
                    nodeIp: "",
                    linkId: "",
                    startTime: "",
                    endTime: "",
                    queryType: 2,
                    special: true,
                    High: false,
                    level: null,
                    snmp: false,
                },
                flowParam: {
                    specialId: null,
                    startTime: "",
                    endTime: "",
                    level: null,
                },
                rateParam: {
                    startTime: "",
                    endTime: "",
                    specialAIp: "",
                    specialZIp: "",
                    specialId: "",
                    linkIds: "",
                    dataSource: "",
                    specialIp: "",
                    level: null,
                },
            },
            portChart: {
                //端口趋势图
                show: false,
                clickTime: new Date().getTime() + "_" + Math.random(),
                data: {},
                delayParam: {
                    preNodeIp: "",
                    nodeIp: "",
                    linkId: "",
                    startTime: "",
                    endTime: "",
                    queryType: 2,
                    special: true,
                    High: false,
                    level: null,
                    snmp: false,
                },
                flowParam: {
                    specialId: null,
                    startTime: "",
                    endTime: "",
                    level: null,
                },
                rateParam: {
                    startTime: "",
                    endTime: "",
                    specialAIp: "",
                    specialZIp: "",
                    specialId: "",
                    linkIds: "",
                    dataSource: "",
                    specialIp: "",
                    level: null,
                },
            },
        }
    },
    watch: {
        'rowData': {
            handler(val) {
                console.log('rowData', val);
                // 9:专线拥塞,10 端口告警
                if (val.faultType == 9) {
                    this.getSpecial(val);
                } else if (val.faultType == 10) {
                    this.getPort(val);
                }
            },
            deep: true
        }
    },
    created() {

    },
    methods: {
        closeEvent() {
            this.$emit('closeEvent');
        },
        getSpecial(rowDta) {
            this.portChart.show = false;
            //点击时间
            this.specialChart.clickTime = new Date().getTime() + "_" + Math.random();
            console.log("点击时间：" + this.specialChart.clickTime);
            /*时延丢包趋势图参数设置*/
            this.specialChart.delayParam.preNodeIp = rowDta.specialAip;
            this.specialChart.delayParam.nodeIp = rowDta.specialIp;
            this.specialChart.delayParam.linkId = rowDta.linkIds;
            this.specialChart.delayParam.startTime = new Date(
                new Date(rowDta.faultStarttime).getTime() - 3 * 60 * 60 * 1000
            ).format("yyyy-MM-dd HH:mm:ss");
            if (rowDta.recoveryed == 1) {
                this.specialChart.delayParam.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                );
            } else if (rowDta.recoveryed == 0) {
                this.specialChart.delayParam.endTime = new Date(
                    new Date(rowDta.faultEndtime).getTime() + 3 * 60 * 60 * 1000
                ).format("yyyy-MM-dd HH:mm:ss");
            }
            this.specialChart.delayParam.queryType = 2;
            this.specialChart.delayParam.special = true;
            this.specialChart.delayParam.High = false;
            this.specialChart.delayParam.level = null;
            this.specialChart.delayParam.snmp = false;
            /*流量趋势图参数*/
            this.specialChart.flowParam.specialId = rowDta.bizDataId;
            // this.specialChart.flowParam.startTime=this.saveQuery.startTime;
            // this.specialChart.flowParam.endTime=this.saveQuery.endTime;
            this.specialChart.flowParam.startTime = new Date(
                new Date(rowDta.faultStarttime).getTime() - 3 * 60 * 60 * 1000
            ).format("yyyy-MM-dd HH:mm:ss");
            if (rowDta.recoveryed == 1) {
                this.specialChart.flowParam.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                );
            } else if (rowDta.recoveryed == 0) {
                this.specialChart.flowParam.endTime = new Date(
                    new Date(rowDta.faultEndtime).getTime() + 3 * 60 * 60 * 1000
                ).format("yyyy-MM-dd HH:mm:ss");
            }
            this.specialChart.flowParam.level = null;
            /*可用率趋势图参数*/
            // this.specialChart.rateParam.startTime=this.saveQuery.startTime;
            // this.specialChart.rateParam.endTime=this.saveQuery.endTime;
            this.specialChart.rateParam.startTime = new Date(
                new Date(rowDta.faultStarttime).getTime() - 3 * 60 * 60 * 1000
            ).format("yyyy-MM-dd HH:mm:ss");
            if (rowDta.recoveryed == 1) {
                this.specialChart.rateParam.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                );
            } else if (rowDta.recoveryed == 0) {
                this.specialChart.rateParam.endTime = new Date(
                    new Date(rowDta.faultEndtime).getTime() + 3 * 60 * 60 * 1000
                ).format("yyyy-MM-dd HH:mm:ss");
            }
            this.specialChart.rateParam.specialAIp = rowDta.specialAip;
            this.specialChart.rateParam.specialZIp = rowDta.specialZip;
            this.specialChart.rateParam.specialId = rowDta.bizDataId;
            this.specialChart.rateParam.linkIds = rowDta.linkIds;
            this.specialChart.rateParam.dataSource = rowDta.dataSource;
            this.specialChart.rateParam.specialIp = rowDta.specialIp;
            this.specialChart.rateParam.level = null;
            this.specialChart.data = {
                delayParam: this.specialChart.delayParam,
                flowParam: this.specialChart.flowParam,
                rateParam: this.specialChart.rateParam,
                markPoint: rowDta.faultStarttime,
                markPointGreen: rowDta.faultEndtime,
                // specialName:rowDta.specialName,
                specialDetails: rowDta.faultDesc,
                title: rowDta.title,
                alarmType: rowDta.faultType,
            };
            this.specialChart.show = true;
        },
        // 端口告警
        getPort(rowDta) {
            console.log('rowDta', rowDta);
            this.specialChart.show = false;
            //点击时间
            this.portChart.clickTime = new Date().getTime() + "_" + Math.random();
            console.log("点击时间：" + this.specialChart.clickTime);
            /*时延丢包趋势图参数设置*/
            this.portChart.delayParam.preNodeIp = rowDta.specialAip;
            this.portChart.delayParam.nodeIp = rowDta.specialIp;
            this.portChart.delayParam.linkId = rowDta.linkIds;
            this.portChart.delayParam.startTime = new Date(
                new Date(rowDta.faultStarttime).getTime() - 3 * 60 * 60 * 1000
            ).format("yyyy-MM-dd HH:mm:ss");
            if (rowDta.recoveryed == 1) {
                this.portChart.delayParam.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                );
            } else if (rowDta.recoveryed == 0) {
                this.portChart.delayParam.endTime = new Date(
                    new Date(rowDta.faultEndtime).getTime() + 3 * 60 * 60 * 1000
                ).format("yyyy-MM-dd HH:mm:ss");
            }
            this.portChart.delayParam.queryType = 2;
            this.portChart.delayParam.special = true;
            this.portChart.delayParam.High = false;
            this.portChart.delayParam.level = null;
            this.portChart.delayParam.snmp = false;
            /*流量趋势图参数*/
            this.portChart.flowParam.specialId = rowDta.bizDataId;
            this.portChart.flowParam.startTime = new Date(
                new Date(rowDta.faultStarttime).getTime() - 3 * 60 * 60 * 1000
            ).format("yyyy-MM-dd HH:mm:ss");
            if (rowDta.recoveryed == 1) {
                this.portChart.flowParam.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                );
            } else if (rowDta.recoveryed == 0) {
                this.portChart.flowParam.endTime = new Date(
                    new Date(rowDta.faultEndtime).getTime() + 3 * 60 * 60 * 1000
                ).format("yyyy-MM-dd HH:mm:ss");
            }
            this.portChart.flowParam.level = null;
            /*可用率趋势图参数*/
            this.portChart.rateParam.startTime = new Date(
                new Date(rowDta.faultStarttime).getTime() - 3 * 60 * 60 * 1000
            ).format("yyyy-MM-dd HH:mm:ss");
            if (rowDta.recoveryed == 1) {
                this.portChart.rateParam.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                );
            } else if (rowDta.recoveryed == 0) {
                this.portChart.rateParam.endTime = new Date(
                    new Date(rowDta.faultEndtime).getTime() + 3 * 60 * 60 * 1000
                ).format("yyyy-MM-dd HH:mm:ss");
            }
            this.portChart.rateParam.specialAIp = rowDta.specialAip;
            this.portChart.rateParam.specialZIp = rowDta.specialZip;
            this.portChart.rateParam.specialId = rowDta.bizDataId;
            this.portChart.rateParam.linkIds = rowDta.linkIds;
            this.portChart.rateParam.dataSource = rowDta.dataSource;
            this.portChart.rateParam.specialIp = rowDta.specialIp;
            this.portChart.rateParam.level = null;
            this.portChart.data = {
                delayParam: this.portChart.delayParam,
                flowParam: this.portChart.flowParam,
                rateParam: this.portChart.rateParam,
                markPoint: rowDta.faultStarttime,
                markPointGreen: rowDta.faultEndtime,
                specialDetails: rowDta.faultDesc,
                title: rowDta.title,
                alarmType: rowDta.faultType,
            };

            // 端口告警的 特殊参数
            // queryObjectType : 1 专线，2 端口
            if (rowDta.faultType == 10) {
                this.portChart.flowParam.deviceId = rowDta.deviceAId;
                this.portChart.flowParam.interfaceId = rowDta.interfaceAId;
                this.portChart.flowParam.queryObjectType = 2;
                this.portChart.delayParam.showState = 0;
                this.portChart.rateParam.showState = 0;
            }

            this.portChart.show = true;
        },
    }
}
</script>

<style></style>