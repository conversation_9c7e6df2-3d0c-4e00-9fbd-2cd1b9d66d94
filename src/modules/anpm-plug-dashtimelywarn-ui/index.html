<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title></title>
</head>
<body >
<div id="dashTimelyWarn"></div>
<!-- built files will be auto injected -->
</body>
</html>
<script>
    //二级iframe页面改变颜色函数
    function skinChange(obj) {
        console.log(obj)
        const property = Object.keys(obj);
        const color = Object.keys(obj).map(function (i) {
            return obj[i]
        });
        let root = document.documentElement;
        for (let i = 0; i < property.length; i++) {
            root.style.setProperty(property[i], color[i]);
        }
        window.thisVm.$data.isdarkSkin = top.window.isdarkSkin;
    }
  let iframestr = window.name;
  let isbigscreen = iframestr.search("bigscreen") != -1 ;
  if (isbigscreen) {
    document.body.className = 'bigscreen';
    window.isbigscreen = isbigscreen
  }else{
    window.isbigscreen = false
  }
</script>
<style>
  html{
    height:100%
  }
  ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;
  }
  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    /* box-shadow: inset 0 0 5px rgba(6, 50, 77, 1); */
    box-shadow: inset 0 0 5px var(--scrollbar_thumb_shadow_color,rgba(6, 50, 77, 1));
    /* background: #015197; */
    background: var(--scrollbar_thumb_bg_color,#015197);

  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
    box-shadow: inset 0 0 5px var(--scrollbar_track_shadow_color,rgba(0, 0, 0, 0.2));
    border-radius: 10px;
    /* background: #06324D; */
    background: var(--scrollbar_track_bg_color,#06324D);
  }
</style>
