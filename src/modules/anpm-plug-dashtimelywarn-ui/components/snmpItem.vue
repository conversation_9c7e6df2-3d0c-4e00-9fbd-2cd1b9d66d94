<template>
  <section>
    <div style="position: relative">
      <Spin fix v-show="spinShow" style="width: 100%"
        >{{ $t("alarm_load")
        }}<Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon
      ></Spin>

      <!--趋势图-->
      <div class="lookBox" v-show="!noData" style="margin-top: 0px">
        <div slot="header">
          <span class="title-name">{{ $t("alarm_failure_index") }}</span>
          <span class="title-content">
            {{ $t("alarm_addressIP") + ":" + (data.devIp || "--") }},
            {{ $t("alarm_address") + ":" + echartLookParama2.preNodeIp
            }}<span class="alias" v-if="preAlias.name"
              >({{
                preAlias.port
                  ? $t("alarm_device_name") +
                    ":" +
                    preAlias.name +
                    "," +
                    $t("alarm_port_num") +
                    ":" +
                    preAlias.port
                  : $t("alarm_alias") + ":" + preAlias.name
              }})</span
            >——>{{ echartLookParama2.nodeIp
            }}<span class="alias" v-if="zAlias.name"
              >({{
                zAlias.port
                  ? $t("alarm_device_name") +
                    ":" +
                    zAlias.name +
                    "," +
                    $t("alarm_port_num") +
                    ":" +
                    zAlias.port
                  : $t("alarm_alias") + ":" + zAlias.name
              }})</span
            >
          </span>
        </div>

        <div class="contain">
          <div
            ref="delayLosssnmp"
            id="delayLosssnmp"
            class="echartStyle"
            :style="'height:' + height + 'px'"
          ></div>
        </div>
      </div>
      <div
        v-show="noData"
        style="width: 100%; height: 100%; font-size: 18px; text-align: center"
      >
        <div
          :class="{
            table_empty: currentSkin == 1,
            table2_empty: currentSkin == 0,
          }"
          style="margin-top: 130px"
        ></div>
        <div>{{ $t("alarm_no_data") }}！</div>
      </div>
    </div>
  </section>
</template>

<script>
  const pointGreen = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC';
  const pointRed = 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC';
import echartFn from "@/common/mixins/echartFun";
let echarts = require('echarts/lib/echarts')
  require('echarts/lib/chart/line')
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')
  require('echarts/lib/component/legend')
  require('echarts/lib/component/dataZoom');
import eConfig from "@/config/echart.config.js";
import moment from 'moment';
export default {
  name: "snmpItem",
  mixins: [echartFn],
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    },
    time: {
      type: Object,
      default() {
        return {};
      }
    },
    snmp: {
      type: Boolean
    },
    dataSource: {
      type: Number
    },
    isbigscreen:{
      type:Boolean,
      default:false
    }
  },
  watch: {
    data: {
      handler(val) {
        this.data = val;
        if (JSON.stringify(val) != "{}" && this.snmp === true) {
          this.getLineData();
        }
      },
      deep: true
    },
    snmp: {
      handler(val) {
        this.snmp = val;
      },
      deep: true
    },
    time: {
      handler(val) {
        this.time = val;
      },
      deep: true
    }
  },
  data() {
    return {
      // echartsHeight:300,
      currentSkin: sessionStorage.getItem('dark') || 1,
      noData: false,
      spinShow: true,

      //以下为趋势图有关参数
      echartLookParama2: {
        orgId:"",
        preNodeIp: "",
        nodeIp: "",
        linkId: "",
        startTime: "",
        endTime: "",
        queryType: "",
        level: '',
        special: false
      },
      height: 300,
      echart1: {
        show: true
      },
      echart2: {
        show: true
      },
      query: {},
      query2: {},
      startTime: 0,
      endTime: 0,
      preAlias: {
        name: "",
        port: ""
      },
      zAlias: {
        name: "",
        port: ""
      },
      delayLossColor:["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "bps",
        出流速: "bps",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%"
      },
      flowColor: ["#478EE9", "#F19149"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: []
      },
      flowData: {
        enter: [],
        issue: []
      },
      delayLossScale: true,
      startScale: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayEnd: 100,
      startValue: "",
      endValue: "",
      scale: "",
      hoverDelayTime: "",
      hoverFlowTime: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {}
      },
      markPoint: "",
      markPointGreen: "",
      //以上为趋势图有关参数
    };
  },
  created() {

    
       let delayLossUnitTemp =  {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] =  "ms";
    // 丢包率
    delayLossUnitTemp[this.$t('comm_packet_loss')] =  "%";
    // 入流速
    delayLossUnitTemp[this.$t("dashboard_upstream")] =  "bps";
    // 出流速
    delayLossUnitTemp[this.$t("dashboard_down")] =  "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_uplink")] =  "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_downstream")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t('specquality_availability_exemption')] =  "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t('specquality_good_rate_exemption')] =  "%";
    // 可用率
    delayLossUnitTemp[this.$t('specquality_availability')] =  "%";
    // 优良率
    delayLossUnitTemp[this.$t('specquality_good_rate')] =  "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit,delayLossUnitTemp);

    moment.locale('zh-cn');
    if (!this.delayLossChart1) {
      return;
    }
    // this.delayLossChart1.clear();
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
  mounted() {
     // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    if (JSON.stringify(this.data) != "{}" && this.snmp === true) {
      this.markPoint = "";
      this.markPointGreen = "";
      this.getLineData();
    }
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    getLineData() {
      let sixHours = 6 * 60 * 60 * 1000;
      let deal_durationArrs = this.data.dealDuration
        .replace(/(天)|(小时)|(分)|(秒)/g, "-")
        .split("-");
      let deal_durationTime = 0;
      switch (deal_durationArrs.length - 1) {
        case 4:
          deal_durationTime =
            deal_durationArrs[0] * 24 * 60 * 60 * 1000 +
            deal_durationArrs[1] * 60 * 60 * 1000 +
            deal_durationArrs[2] * 60 * 1000 +
            deal_durationArrs[3] * 1000;
          break;
        case 3:
          deal_durationTime =
            deal_durationArrs[0] * 60 * 60 * 1000 +
            deal_durationArrs[1] * 60 * 1000 +
            deal_durationArrs[2] * 1000;
          break;
        case 2:
          deal_durationTime =
            deal_durationArrs[0] * 60 * 1000 + deal_durationArrs[1] * 1000;
          break;
        case 1:
          deal_durationTime = deal_durationArrs[0] * 1000;
          break;
      }
      let startTime = new Date(
        new Date(this.data.faultStarttime).getTime() - sixHours
      ).format("yyyy-MM-dd HH:mm:ss");
      this.markPoint = this.data.faultStarttime || '';
      this.markPointGreen = this.data.faultEndtime || '';
      this.echartLookParama2.startTime =moment(this.data.faultStarttime).subtract(3, "hours").format("YYYY-MM-DD HH:mm:ss");
      if (this.data.recoveryed == 1){
        this.echartLookParama2.endTime = new Date().format('yyyy-MM-dd HH:mm:ss')
      } else{
        this.echartLookParama2.endTime =  moment(this.data.faultEndtime,"YYYY-MM-DD HH:mm:ss").add(3, "hours").format("YYYY-MM-DD HH:mm:ss");
      }
      this.echartLookParama2.orgId = this.data.orgId;
      this.echartLookParama2.preNodeIp = this.data.sourceIp;
      this.echartLookParama2.nodeIp = this.data.destIp;
      this.echartLookParama2.devIp = this.data.devIp;
      this.echartLookParama2.linkId = this.data.linkIds;
      this.echartLookParama2.level = '';
      this.echartLookParama2.queryType = 2;
      if (this.data.bussType == this.$t('spec_line')) {
        this.echartLookParama2.special = true;
      } else {
        this.echartLookParama2.special = false;
      }
      this.getLine(this.echartLookParama2);
    },
    //获取趋势图数据
    async getLine(param) {
      let _self = this;
      _self.spinShow = true;
      _self.noData = false;
      _self.preAlias.name = "";
      _self.preAlias.port = "";
      _self.zAlias.name = "";
      _self.zAlias.port = "";
      _self.delayLossData.delay = [];
      _self.delayLossData.loss = [];
      _self.flowData.enter = [];
      _self.flowData.issue = [];

      await this.$http
        .PostJson("/trend/getDelayAndLostTrend", param)
        .then(res => {
          //时延丢包趋势数据
          if (res.code === 1) {
            this.preAlias.name = res.data.preName;
            this.preAlias.port = res.data.prePort;
            this.zAlias.name = res.data.name;
            this.zAlias.port = res.data.port;
            this.delayLossLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              this.echart1.show = false;
            }
            let lineData = [];
            if (res.data.lineOne){
              lineData = res.data.lineOne
            } else if (res.data.lineTwo) {
              lineData = res.data.lineTwo
            }
            let index = this.closest(lineData, this.markPoint);
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.echart1.show = true;
              this.delayStart = 0;
              // if (res.data.lineOnetotal <= 300) {
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue =
                  res.data.lineOne[res.data.lineOne.length - 1][0];
              // } else {
              //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
              //   if (index !== 'noPoint') {
              //     if (index <= 150) {
              //       this.startValue = res.data.lineOne[0][0];
              //       if (index + 150 < res.data.lineOnetotal - 1) {
              //         this.endValue = res.data.lineOne[index + 150][0];
              //       } else {
              //         this.endValue =
              //           res.data.lineOne[res.data.lineOne.length - 1][0];
              //       }
              //     } else {
              //       this.startValue = res.data.lineOne[index - 150][0];
              //       if (index + 150 < res.data.lineOnetotal) {
              //         this.endValue = res.data.lineOne[index + 150][0];
              //       } else {
              //         this.endValue =
              //           res.data.lineOne[res.data.lineOne.length - 1][0];
              //       }
              //     }
              //   }
              // }
              this.delayLossScale = true;
              this.delayLossData.delay = res.data.lineOne;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.delayLossData.loss = res.data.lineTwo;
            }
            this.$store.commit("updateDelayLossHistory", {
              level: res.data.level,
              datas: [this.delayLossData.delay, this.delayLossData.loss],
            }); //保存数据
            this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
          }
        }).catch(err=>{
          if (this.$axios.isCancel(err)) {
            console.log('Request canceled!')
          } else {
            this.$Message.error(err.message)
          }
        });

      let trendParam = JSON.parse(JSON.stringify(param));
      trendParam = Object.assign(trendParam, {
        snmp: this.dataSource == 1 ? true : false,info:this.$t('alarm_fault_list')
      });
      await this.$http
        .PostJson("/trend/getDataTrend", trendParam)
        .then(res => {
          //流速趋势数据
          if (res.code === 1) {
            let flowUnit = 1;
            this.flowLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              this.echart2.show = false;
              // this.echartsHeight = 300; 
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.echart2.show = true;
              // this.echartsHeight = 500; 
              this.flowData.enter = res.data.lineTwo
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.flowData.issue = res.data.lineOne
            }
          }
        });
      if (
        this.delayLossData.delay.length < 1 &&
        this.flowData.enter.length < 1
      ) {
        this.noData = true;
      } else {
        this.noData = false;
      }
      this.startScale = false;
      if (this.echart2.show) {
        this.height = 500;
      } else if (!this.echart2.show) {
        this.height = 260;
      }
      _self.spinShow = false;
      this.$nextTick(()=>{
      this.initEchart();
      })
    },
    setDelayLossTooltip() {
      let _self = this;
      return {
         trigger:'axis',
        backgroundColor:this.currentSkin == 1 ? 'rgba(12, 56, 123,0.7)' : '#FFFFFF',
         textStyle: {
            color: this.currentSkin == 1 ? '#FFFFFF' : '#515A6E'  // 添加这个配置控制文字颜色
        },
         extraCssText: this.currentSkin == 0 ? 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);' : '',
        formatter:function(param) {
           _self.scale = param[0].data[0];
        var obj = {};
        param = param.reduce(function(item, next) {
          obj[next.seriesIndex]
            ? ""
            : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                :
                _self.flowSize(param[i].value[1], true, true)
              ) +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip;
        }

      }
      // return eConfig.tip("axis", function(param) {
      //   _self.scale = param[0].data[0];
      //   var obj = {};
      //   param = param.reduce(function(item, next) {
      //     obj[next.seriesIndex]
      //       ? ""
      //       : (obj[next.seriesIndex] = true && item.push(next));
      //     return item;
      //   }, []);
      //   let delayTime = "",
      //     delayTip = "",
      //     flowTime = "",
      //     flowTip = "";
      //   for (let i = 0, len = param.length; i < len; i++) {
      //     if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
      //       delayTime = param[i].data[0] + "<br />";
      //       delayTip +=
      //         '<span class="tooltip-round" style="background-color:' +
      //         param[i].color +
      //         '"></span>';
      //       delayTip +=
      //         param[i].seriesName +
      //         "：" +
      //         (param[i].value[1] === undefined ||
      //         param[i].value[1] === null ||
      //         param[i].value[1] === "" ||
      //         param[i].value[1] == -1
      //           ? "--"
      //           : param[i].value[1]) +
      //         _self.delayLossUnit[param[i].seriesName] +
      //         "<br />";
      //     }
      //     if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
      //       flowTime = param[i].data[0] + "<br />";
      //       flowTip +=
      //         '<span class="tooltip-round" style="background-color:' +
      //         param[i].color +
      //         '"></span>';
      //       flowTip +=
      //         param[i].seriesName +
      //         "：" +
      //         (param[i].value[1] === undefined ||
      //         param[i].value[1] === null ||
      //         param[i].value[1] === "" ||
      //         param[i].value[1] == -1
      //           ? "--"
      //           :
      //           _self.flowSize(param[i].value[1], true, true)
      //         ) +
      //         "<br />";
      //     }
      //   }
      //   return delayTime + delayTip + flowTime + flowTip;
      // });
    },
    Option() {
      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all"
            }
          },
          grid: [
            {
              left: "8%",
              top: "40px",
              width: "84%",
              height: this.echart2.show ? "140px" : "140px"
            },
            {
              left: "8%",
              top: "280px",
              width: "84%",
              height: this.echart2.show ? "140px" : "0px"
            }
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                // color: "#484b56",
                color: top.window.isdarkSkin == 1 || this.isbigscreen ? "#617ca5" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold"
              },
              color: this.delayLossColor,
              data: [this.$t('speed_delay'), this.$t('comm_packet_loss')]
            },
            {
              show: this.echart2.show,
              top: "220px",
              right: "43%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                // color: "#484b56",
                color: top.window.isdarkSkin == 1 || this.isbigscreen ? "#617ca5" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold"
              },
              data: [this.$t('specquality_incoming_velocity'), this.$t('specquality_exit_velocity')]
            }
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              // splitLine: {
              //   lineStyle: {
              //     type: "dashed",
              //     color: "#e3e7f2"
              //   }
              // }
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen  ?"#5CA0D5" : "#676f82",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen?"rgba(42, 56, 64, 1)" : "#e3e7f2"
                },
              },
              axisLine: {
                //  show:false,
                lineStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              // splitLine: {
              //   lineStyle: {
              //     type: "dashed",
              //     color: "#e3e7f2"
              //   }
              // }
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                },
              },
              splitLine: {
                show:false,
                lineStyle: {
                  type: "dashed",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"rgba(42, 56, 64, 1)" : "#e3e7f2"
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            }
          ],
          yAxis: [
            {
              name: this.$t('comm_delay(ms)'),
              type: "value",
              scale: true,
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show:false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                }
              }
            },
            {
              name: this.$t('comm_loss_rate'),
              type: "value",
              scale: true,
              gridIndex: 0,
              min:0,
              max:100,
              position: "right",
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show:false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                }
              }
            },
            {
              show: this.echart2.show,
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                  width: "1" //坐标线的宽度
                }
              },
              splitLine: {
                show:false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2"
                  color: top.window.isdarkSkin == 1 || this.isbigscreen ?"#5CA0D5" : "#676f82",
                }
              },
              axisLabel: {
                show: true,
                formatter: value => {
                  return this.getUnit(value, true, true);
                }
              }
            }
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue==this.endValue?null:this.startValue,
              endValue: this.startValue==this.endValue?null:this.endValue
            },
            {
              type: "slider",
              left: "5%",
              right: "5%",
              top: this.echart2.show ? "460px" : "230px",
              height:20,
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue==this.endValue?null:this.startValue,
              endValue: this.startValue==this.endValue?null:this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , //  "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: top.window.isdarkSkin == 1 || this.isbigscreen ? "#5CA0D5" : "#676f82",
              }
            }
          ],
          series: [
            {
              type: "line",
              name: this.$t('speed_delay'),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbolSize:1,
              symbol: this.delayLossData.delay.length>1?"circle":"circle",
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0, 255, 238, 0.2)",
                    },
                  ]
                }
              },
              data: this.delayLossData.delay,
              markLine: {
                data: [
                  {
                    symbol: 'image://'+pointRed,
                    symbolSize: 10,
                    xAxis:this.markPoint,
                    symbolRotate:'0',
                    // symbolOffset:[0,'70px'],
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter:()=>{
                        return this.markPoint
                      },
                      // backgroundColor: "#fff",
                    }
                  },
                  {
                    symbol: 'image://'+pointGreen,
                    symbolSize: 10,
                    xAxis:this.markPointGreen,
                    symbolRotate:'0',
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: "#fff",
                      formatter:()=>{
                        return this.markPointGreen
                      },
                    }
                  }
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1
                  }
                }
              }
            },
            {
              type: "line",
              name: this.$t('comm_packet_loss'),
              xAxisIndex: 0,
              yAxisIndex: 1,
              smooth: true,
              symbolSize:1,
              symbol: this.delayLossData.loss.length>1?"circle":"circle",
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(2, 144, 253, 0.2)",
                    },
                  ]
                }
              },
              data: this.delayLossData.loss
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_incoming_velocity'),
              type: "line",
              smooth: true,
              symbolSize:1,
              symbol: this.flowData.enter.length>1?"circle":"circle",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(71,142,233, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: "rgba(71,142,233, 0.2)"
                    }
                  ]
                }
              }
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_exit_velocity'),
              type: "line",
              smooth: true,
              symbolSize:1,
              symbol: this.flowData.issue.length>1?"circle":"circle",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1
                  }
                }
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)"
                    }
                  ]
                }
              }
            }
          ]
        }
      ];
      return optionArr[0];
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart1) {
        that.delayLossChart1.clear();
      }
      that.delayLossChart1 = echarts.init(this.$refs["delayLosssnmp"]);

      /*设置时延丢包率echart图*/
      that.delayLossChart1.setOption(this.Option());
     that.delayLossChart1.getZr().on("mousewheel", (params) => {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        console.log(getTlevel);
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getgoodTlevel = that.$store.state.goodRateTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据
        let getgoodSaveData = that.$store.state.goodRateHistory; //获取保存的数据

        var pointInPixel = [params.offsetX, params.offsetY];
        if (
          that.delayLossChart1.containPixel(
            { gridIndex: [0, 1, 2] },
            pointInPixel
          )
        ) {
          let startValue =
            that.delayLossChart1.getModel().option.dataZoom[0].startValue;
          let endValue =
            that.delayLossChart1.getModel().option.dataZoom[0].endValue;
          this.echartLookParama2.startTime = new Date(startValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          this.echartLookParama2.endTime = new Date(endValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);
          let differ = endValue - startValue;
          let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
          let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
          let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
          var levelNum = "";
          let start = that.delayLossChart1.getModel().option.dataZoom[0].start;
          let end = that.delayLossChart1.getModel().option.dataZoom[0].end;

          if (params.wheelDelta >= 0) {
            if (this.delayLossLevel == 1) {
              // 小时粒度
              if (differ < minute) {
                if (!that.startScale) {
                  that.startScale = true;
                  levelNum = 2;
                  let delayParam = Object.assign(this.echartLookParama2, {
                    level: levelNum,
                  });
                  let flowParam = Object.assign(delayParam, {
                    snmp: this.dataSource == 1 ? true : false,
                  });
                  this.getDelayLoss(delayParam, flowParam);
                }
              }
            } else if (this.delayLossLevel == 2) {
              // 分钟粒度
              if (differ < original) {
                if (!that.startScale) {
                  that.startScale = true;
                  if (this.ishigh == 4) {
                    levelNum = 4;
                    let delayParam = Object.assign(this.echartLookParama2, {
                      level: levelNum,
                    });
                    let flowParam = Object.assign(delayParam, {
                      snmp: this.dataSource == 1 ? true : false,
                    });
                    this.getDelayLoss(delayParam, flowParam);
                  }
                }
              }
            }
          } else {
            if (start == 0 && end == 100) {
                //是否处在缩放过程中
                if (that.delayLossLevel == getTlevel) {
                  that.setPs(that.delayLossLevel, [
                    that.timeChange(startValue),
                    that.timeChange(endValue),
                  ]);
                  that.startScale = false;
                } else {
                  let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                  if (that.flowLevel == getflowTlevel) {
                    that.startScale = false;
                  } else {
                    if (that.flowLevel == 2) {
                      that.flowLevel = 1;
                      that.startScale = true;
                      that.flowUnit = getflowSaveUnit.HoursUnit;
                      that.flowData.enter = getflowSaveData.HoursData.enter;
                      that.flowData.issue = getflowSaveData.HoursData.issue;
                    } else if (that.flowLevel == 3 || that.flowLevel == 4) {
                      that.flowLevel = 2;
                      that.startScale = true;
                      that.flowUnit = getflowSaveUnit.minuteUnit;
                      that.flowData.enter = getflowSaveData.minuteData.enter;
                      that.flowData.issue = getflowSaveData.minuteData.issue;
                    }
                  }
                  if (that.goodRateLevel == getgoodTlevel) {
                    that.startScale = false;
                  } else {
                    if (that.goodRateLevel == 2) {
                      that.goodRateLevel = 1;
                      that.startScale = true;
                      that.goodRateData.nrUseRateList =
                        getgoodSaveData.HoursUnit.nrUseRateList;
                      that.goodRateData.nrGoodRateList =
                        getgoodSaveData.HoursUnit.nrGoodRateList;
                      that.goodRateData.useRateList =
                        getgoodSaveData.HoursUnit.useRateList;
                      that.goodRateData.goodRateList =
                        getgoodSaveData.HoursUnit.goodRateList;
                    } else if (
                      that.delayLossLevel == 3 ||
                      that.delayLossLevel == 4
                    ) {
                      that.delayLossLevel = 2;
                      that.startScale = true;
                      that.goodRateData.nrUseRateList =
                        getgoodSaveData.minuteUnit.nrUseRateList;
                      that.goodRateData.nrGoodRateList =
                        getgoodSaveData.minuteUnit.nrGoodRateList;
                      that.goodRateData.useRateList =
                        getgoodSaveData.minuteUnit.useRateList;
                      that.goodRateData.goodRateList =
                        getgoodSaveData.minuteUnit.goodRateList;
                    }
                  }

                  if (that.delayLossLevel == getTlevel) {
                    that.startScale = false;
                  } else if (that.delayLossLevel == 2) {
                    that.delayLossLevel = 1;
                    that.startScale = true;
                    that.delayLossData.delay = getSaveData.HoursData.delay;
                    that.delayLossData.loss = getSaveData.HoursData.loss;
                    that.delayStart = getSite.psH.start;
                    that.startValue = getSite.psH.start;
                    that.delayEnd = getSite.psH.end;
                    that.endValue = getSite.psH.end;
                  } else if (
                    that.delayLossLevel == 3 ||
                    that.delayLossLevel == 4
                  ) {
                    that.delayLossLevel = 2;
                    that.startScale = true;
                    that.delayLossData.delay = getSaveData.minuteData.delay;
                    that.delayLossData.loss = getSaveData.minuteData.loss;
                    that.delayStart = getSite.psM.start;
                    that.startValue = getSite.psM.start;
                    that.delayEnd = getSite.psM.end;
                    that.endValue = getSite.psM.end;
                  }

                  setTimeout(() => {
                    that.startScale = false;
                    that.initEchart();
                  }, 300);
                }
            }
          }
        }
      }); 
    },
    resetRegion() {
      //清除数据
      this.spinShow = true;
    },
    async getDelayLoss(
      delayParam,
      flowParam,
      goodRateParam,
      cachD,
      cachF,
      cachG,
      hoverDelayTime,
      flowTime,
      useTime
    ) {
      let isUpdate = false;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        this.spinShow = true;
        await this.$http
          .PostJson("/trend/getDelayAndLostTrend", delayParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.delayLossLevel = res.data.level;
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              let index = this.closest(lineData, hoverDelayTime);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.delayStart = 0;
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            }
          });
      }

      if (flowParam.level != 99) {
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, { snmp: true,info:this.$t('alarm_fault_list') });
        await this.$http
          .PostJson("/trend/getDataTrend", trendParam)
          .then((res) => {
            //流速趋势数据
            if (res.code === 1) {
              let flowUnit = 1;
              this.flowLevel = res.data.level;
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                let unitChange = this.getFlowUnit(res.data.lineOne);
                this.flowUnit = unitChange[0];
                this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
                this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
                flowUnit = unitChange[1];
                this.flowData.enter = res.data.lineTwo.map((item) => {
                  // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                  return [item[0], item[1]];
                });
              }
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.flowData.issue = res.data.lineOne.map((item) => {
                  // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                  return [item[0], item[1]];
                });
              }
              if (cachF) {
                this.$store.commit("updateFlowHistory", {
                  level: res.data.level,
                  datas: [this.flowData.enter, this.flowData.issue],
                }); //保存数据
                this.$store.commit("setflowUnit", {
                  level: res.data.level,
                  unit: this.flowUnit,
                }); //保存单位
              }
            }
          });
      }

      this.delayLoading = false;
      this.spinShow = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      if (isUpdate) {
        this.initEchart();
      }
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },

  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.clear();
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  }
};
</script>

<style scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
