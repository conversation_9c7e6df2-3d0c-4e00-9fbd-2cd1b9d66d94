<template>
  <section class="index-content" ref="indexEvent" style="position: relative">
    <div class="index-tab-content">
      <div class="index-tab">
        <div class="index-tab-box">
          <Loading :loading="loading"></Loading>
          <Table
            class="eventTable"
            height="210px"
            :columns="columns"
            :data="tableData"
            :no-data-text="
              loading
                ? ''
                : tabList.length > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
          >
            <template slot-scope="{ row }" slot="triggerTime">
              <Tooltip :content="row.triggerTime" placement="top-start">
                <div
                  style="
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 150px;
                    white-space: nowrap;
                  "
                >
                  {{
                    row.triggerTime === undefined ||
                    row.triggerTime === null ||
                    row.triggerTime === "" ||
                    row.triggerTime === "null"
                      ? "--"
                      : row.triggerTime
                  }}
                </div>
              </Tooltip>
            </template>
            <template slot-scope="{ row }" slot="endTime">
              <Tooltip :content="row.endTime" placement="top-start">
                <div
                  style="
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 150px;
                    white-space: nowrap;
                  "
                >
                  {{
                    row.endTime === undefined ||
                    row.endTime === null ||
                    row.endTime === "" ||
                    row.endTime === "null"
                      ? "--"
                      : row.endTime
                  }}
                </div>
              </Tooltip>
            </template>
            <template slot-scope="{ row }" slot="srcIp">
              <Tooltip :content="row.srcIp" placement="top-start">
                <div
                  style="
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 150px;
                    white-space: pre-wrap;
                  "
                >
                  {{
                    row.srcIp === undefined ||
                    row.srcIp === null ||
                    row.srcIp === "" ||
                    row.srcIp === "null"
                      ? "--"
                      : formatIPv6Address(row.srcIp)
                  }}
                </div>
              </Tooltip>
            </template>
            <template slot-scope="{ row }" slot="destIp">
              <Tooltip :content="row.destIp" placement="top-start">
                <div
                  style="
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 150px;
                    white-space: pre-wrap;
                  "
                >
                  {{
                    row.destIp === undefined ||
                    row.destIp === null ||
                    row.destIp === "" ||
                    row.destIp === "null"
                      ? "--"
                      : formatIPv6Address(row.destIp)
                  }}
                </div>
              </Tooltip>
            </template>
            <template slot-scope="{ row }" slot="destName">
              <Tooltip :content="row.destName" placement="top-start">
                <div
                  style="
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 130px;
                    white-space: nowrap;
                  "
                >
                  {{
                    row.destName === undefined ||
                    row.destName === null ||
                    row.destName === "" ||
                    row.destName === "null"
                      ? "--"
                      : row.destName
                  }}
                </div>
              </Tooltip>
            </template>
          </Table>
        </div>
        <div class="tab-page" v-if="tabList.length > 0">
          <Page
            v-page
            :current="tabParam.pageNo"
            :page-size="tabParam.pageSize"
            :page-size-opts="pageSizeOpts"
            :total="totalCount"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
          >
          </Page>
        </div>
      </div>
    </div>
    <div class="index-flowChart mgT10">
      <Loading :loading="loading1"></Loading>
      <topology-item
        :data="flow_chart"
        :degradation_type="
          data.faultType === 1
            ? '中断'
            : data.faultType === 2
            ? '时延劣化'
            : '丢包劣化'
        "
        :data_source="data_source"
        :iconType="false"
        lastType
        @on-click="flowChartClick"
      ></topology-item>
    </div>
    <!--趋势图-->
    <div class="lookBox">
      <div class="title">
        {{ echartLookParama2.preNodeIp
        }}<span class="alias" v-if="preAlias.name"
          >({{
            preAlias.port
              ? $t("comm_Device_name") +
                ":" +
                preAlias.name +
                $t("comm_port_number") +
                ":" +
                preAlias.port
              : $t("comm_Device_name") + ":" + preAlias.name
          }})</span
        >——>{{ echartLookParama2.nodeIp
        }}<span class="alias" v-if="zAlias.name"
          >({{
            zAlias.port
              ? $t("comm_Device_name") +
                ":" +
                zAlias.name +
                $t("comm_port_number") +
                ":" +
                zAlias.port
              : $t("comm_Device_name") + ":" + zAlias.name
          }})</span
        >
      </div>
      <div class="contain" style="position: relative" v-if="!topoShow">
        <Spin
          fix
          v-show="spinShow"
          :style="'width: 100%;height:' + height + 'px;'"
          >{{ $t("alarm_load")
          }}<Icon
            type="ios-loading"
            size="18"
            class="demo-spin-icon-load"
          ></Icon
        ></Spin>
        <div
          ref="alarm-delayLoss"
          id="alarm-delayLoss"
          class="echartStyle"
          :style="'height:' + height + 'px'"
        ></div>
      </div>
      <div
        :class="{
          table_empty: currentSkin == 1,
          table2_empty: currentSkin == 0,
        }"
        v-else
      >
        <p class="emptyText">{{ $t("common_No_data") }}</p>
      </div>
    </div>
  </section>
</template>

<script>
const pointGreen =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC";
const pointRed =
  "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC";
import topologyItem from "@/common/flowChart/topologyX.vue";
import Loading from "../../../common/loading/loading";
import echartFn from "@/common/mixins/echartFun";

let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/markLine");
require("echarts/lib/component/dataZoom");
import eConfig from "@/config/echart.config.js";
import moment from "moment";
import ipv6Format from "@/common/ipv6Format";
import global from "@/common/global.js";

export default {
  name: "indexItem",
  mixins: [echartFn],
  components: {
    Loading,
    topologyItem,
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      },
    },
    time: {
      type: Object,
      default() {
        return {};
      },
    },
    dataSource: {
      type: Number,
    },
    isbigscreen:{
      type:Boolean,
      default:false
    }
  },
    computed:{
        tableData:{
            get : function() {



              
              return this.tabList

              // 不能这样写，不然后面取值的时候，会有错误
                // let arr = this.tabList.map(item=>{
                //     for (const key in item) {
                //         let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                //         item[key] = str
                //     }
                //    return item
                // })
                // return arr
            },
        },
    },
  data() {
    return {
            currentSkin: sessionStorage.getItem('dark') || 1,
      topoShow: false,
      //趋势图加载
      spinShow: false,
      //是否是不可信节点
      suspect: false,
      faultIp: "",
      data_source: "",
      ishigh: "",
      loading: false,
      loading1: false,
      tabParam: {
        reportId: "",
        pageNo: 1,
        pageSize: 3,
      },
      currentid: 1,
      columns: [
        {
          title:this.$t('but_choose'),
          type: "chose",
          width: 100,
          align: "left",
          className: "modalTable",
          render: (h, params) => {
            let id = params.row.eventId;
            let flag = false;
            if (this.currentid === id) {
              flag = true;
            } else {
              flag = false;
            }
            return h("div", [
              h("Radio", {
                props: {
                  value: flag,
                },
                class: "index-tab-radio",
                on: {
                  "on-change": () => {
                    this.currentid = id;
                    this.choiceChange(params.row);
                  },
                },
              }),
            ]);
          },
        },
        {
          title: this.$t('alarmlist_iln'),
          key: "associatedLinkNum",
          align: "left",
          width: 300,
          className: "modalTable",
          tooltip:true,
        },
        {
          title: this.$t('alarmlist_st'),
          align: "left",
          className: "modalTable",
          slot:'triggerTime',
        },
        {
          title: this.$t('alarmlist_rt'),
          align: "left",
          className: "modalTable",
           slot:'endTime',
        },
        {
          title: this.$t('specquality_recovery_cause'),
          key: "recoveryType",
          align: "left",
          render: (h, params) => {
             // 0故障升级、1故障降级、2路径切换、3故障恢复、4其他,5故障链路恢复
            let str = params.row.recoveryType,
              text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (str == 0) {
              text = this.$t('comm_failure_up');
            } else if (str == 1) {
              text = this.$t('comm_failure_dwon');
            } else if (str == 2) {
              text = this.$t('comm_path_switching');
            } else if (str == 3) {
              text = this.$t('comm_failure_re');
            }else if (str == 5) {
              text = this.$t('comm_failure_link_recovery');
            } else {
              text = this.$t('comm_other');
            }
            return h("span", text);
          },
        },
        {
          title: this.$t('comm_source_ip'),
          align: "left",
          className: "modalTable",
           slot:'srcIp',
        },
        {
          title: this.$t('comm_target_ip'),
          align: "left",
          className: "modalTable",
          slot:'destIp',
        },
        {
          title: this.$t('comm_target_name'),
          align: "left",
          className: "modalTable",
          slot:'destName',
        },
          {
          title: this.$t('task_dial_type'),
          align: "left",
          className: "modalTable",
           key: "taskType",
           render: (h, params) => {
             return h("span", global.convertTaskType(params.row.taskType));
          },
        },
      ],
      tabList: [],
      totalCount: 0,
      pageSizeOpts: [3,10, 50, 100, 200, 500, 1000],
      topologyParam: {
        reportId: "",
        linkId: "",
        faultType: "",
        eventId: "",
        triggerTime: "",
      },
      trendParam: {
        reportId: "",
        pre_ip: "",
        cur_ip: "",
        linkId: "",
        triggerTime: "",
      },

      flow_chart: [],
      delay_line: [], //时延走势数据
      loss_line: [], //丢包走势数据

      highCurIp: "",
      //以下为趋势图有关参数
      echartLookParama2: {
        preNodeIp: "",
        nodeIp: "",
        linkId: "",
        startTime: "",
        endTime: "",
        queryType: "",
        level: "",
        High: false,
        special: false,
      },
      //保存当前故障时间
      saveStartTime: null,
      saveEndTime: null,
      markPoint: "",
      markPointGreen: "",
      height: 500,
      echart1: {
        show: true,
      },
      echart2: {
        show: true,
      },
      query: {},
      query2: {},
      startTime: 0,
      endTime: 0,
      preAlias: {
        name: "",
        port: "",
      },
      zAlias: {
        name: "",
        port: "",
      },
      // delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "bps",
        出流速: "bps",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%",
      },
      flowColor: ["#478EE9", "#F19149"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: [],
      },
      flowData: {
        enter: [],
        issue: [],
      },
      delayLossScale: true,
      startScale: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayEnd: 100,
      startValue: "",
      endValue: "",
      scale: "",
      hoverDelayTime: "",
      hoverFlowTime: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {},
      },
      //以上为趋势图有关参数
    };
  },  
   mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
   },
   beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  created() {

    let delayLossUnitTemp =  {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] =  "ms";
    // 丢包率
    delayLossUnitTemp[this.$t('comm_packet_loss')] =  "%";
    // 入流速
    delayLossUnitTemp[this.$t("dashboard_upstream")] =  "bps";
    // 出流速
    delayLossUnitTemp[this.$t("dashboard_down")] =  "bps";
    // 上行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_uplink")] =  "%";
    // 下行带宽利用率
    delayLossUnitTemp[this.$t("dashboard_downstream")] = "%";
    // 可用率（免责）
    delayLossUnitTemp[this.$t('specquality_availability_exemption')] =  "%";
    // 优良率（免责）
    delayLossUnitTemp[this.$t('specquality_good_rate_exemption')] =  "%";
    // 可用率
    delayLossUnitTemp[this.$t('specquality_availability')] =  "%";
    // 优良率
    delayLossUnitTemp[this.$t('specquality_good_rate')] =  "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit,delayLossUnitTemp);
    
    moment.locale("zh-cn");
    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
  watch: {
    data: {
      handler(val) {
        this.markPoint = "";
        this.markPointGreen = "";
        if (!(JSON.stringify(val) === "{}")) {
          this.topoShow = val.topoShow;
          //a类标识参数
          this.data_source = val.dataSource;
          //表格参数
          this.tabParam.reportId = val.id;
          this.tabParam.bizDataId = val.bizDataId;

          //拓扑图参数
          this.topologyParam.reportId = val.id;
          this.topologyParam.faultType = val.faultType;
          //趋势图参数
          this.trendParam.reportId = val.id;
          if (this.data.bussType == this.$t('spec_line')) {
            this.echartLookParama2.special = true;
          } else {
            this.echartLookParama2.special = false;
          }

          this.getList(this.tabParam);
        } else {
          this.resetRegion();
        }
      },
      deep: true,
    },
    time: {
      handler(val) {
        this.time = val;
      },
      deep: true,
    },
    "echart2.show": {
      handler(val) {
        if (val) {
          this.height = 500;
        } else {
          this.height = 260;
        }
      },
      deep: true,
    },
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    formatIPv6Address(str){
      return ipv6Format.formatIPv6Address(str,200);
    },
    pageSizeChange(e) {
      //表格每页展示多少条数据切换
      this.currentNum = 1;
      this.tabParam.pageNo = 1;
      this.tabParam.pageSize = e;
      this.getList(this.tabParam);
    },
    //获取列表数据
    getList(param) {
      let _self = this;
      // 任务的频率参数
      let dialInterval = _self.data.dialInterval ?? 1;
      _self.tabList = [];
      _self.loading = true;
      _self.$http
        .PostJson("/fault/getFaultLinkList", param)
        .then((res) => {
          if (res.code === 1) {
            let list = res.data.records || [];
            _self.tabList = list;
            if (list.length > 0) {
              _self.currentid = list[0].eventId;
              //拓扑图参数
              _self.topologyParam.linkId = list[0].linkId;
              _self.topologyParam.eventId = list[0].eventId;
              _self.topologyParam.triggerTime = list[0].triggerTime;
              this.markPoint = list[0].triggerTime;
              this.markPointGreen = list[0].endTime;
              //趋势图参数
              if (list[0].faultIp.indexOf("-") > -1) {
                let faultIpsArr = list[0].faultIp.split("-");

                _self.echartLookParama2.nodeIp =
                  faultIpsArr[faultIpsArr.length - 1];
                _self.trendParam.cur_ip = faultIpsArr[faultIpsArr.length - 1];
              } else {
                _self.echartLookParama2.nodeIp = list[0].faultIp;
                _self.trendParam.cur_ip = list[0].faultIp;
              }

              _self.echartLookParama2.linkId = list[0].linkId;
              this.ishigh = list[0].dataSource;
              if (list[0].triggerTime) {
                const timeInterval = list[0].endTime
                  ? new Date(list[0].endTime) - new Date(list[0].triggerTime)
                  : new Date() - new Date(list[0].triggerTime);
                const handradSevenTyMinutes = 170 * 60 * 1000,
                  handradSixtyTwoHours = 162 * 60 * 60 * 1000;
                if (this.ishigh == 4) {
                  if (timeInterval <= handradSevenTyMinutes) {
                    _self.echartLookParama2.startTime = moment(
                      list[0].triggerTime
                    )
                      .subtract(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      list[0].endTime,
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  } else if (
                    timeInterval > handradSevenTyMinutes &&
                    timeInterval <= handradSixtyTwoHours
                  ) {
                    _self.echartLookParama2.startTime = moment(
                      list[0].triggerTime
                    )
                      .subtract(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      list[0].endTime,
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  } else {
                    _self.echartLookParama2.startTime = moment(
                      list[0].triggerTime
                    )
                      .subtract(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      list[0].endTime,
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  }
                } else {
                  if (timeInterval > handradSixtyTwoHours) {
                    _self.echartLookParama2.startTime = moment(
                      list[0].triggerTime
                    )
                      .subtract(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      list[0].endTime,
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  } else {
                    _self.echartLookParama2.startTime = moment(
                      list[0].triggerTime
                    )
                      .subtract(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      list[0].endTime,
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3 * dialInterval, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  }
                }
                if (list[0].endTime === "" || list[0].endTime == null) {
                  this.echartLookParama2.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                  );
                }
              }
              this.saveStartTime = this.echartLookParama2.startTime;
              this.saveEndTime = this.echartLookParama2.endTime;
              _self.trendParam.linkId = list[0].linkId;

              _self.trendParam.triggerTime = list[0].triggerTime;
              _self.defaultTime = list[0].triggerTime;
              _self.echartLookParama2.queryType = 2;

              if (this.ishigh == 4) {
                this.echartLookParama2.High = true;
                this.echartLookParama2.level = "";
              } else if (this.ishigh != 4) {
                this.echartLookParama2.High = false;
                this.echartLookParama2.level = null;
              }
              _self.getTopology(_self.topologyParam, list[0].extraFlag);
            }
            _self.faultIp = list.length > 0 ? list[0].faultIp : "--";
            _self.totalCount = res.data.total;
            _self.echartLookParama2.queryType = 2;
            if (this.ishigh == 4 && list.length > 0) {
              _self.highCurIp = list[0].destIp;
            } else {
              _self.highCurIp = "";
            }
            _self.loading = false;
          }
        })
        .catch(() => {
          _self.loading = false;
        })
        .finally(() => {
          _self.loading = false;
        });
    },
    //获取拓扑图数据
    getTopology(param, extraFlag) {
      let _self = this;
      _self.flow_chart = [];
      _self.loading1 = true;
      _self.$http
        .PostJson("/fault/getFaultLinkRoute", param)
        .then((res) => {
          if (res.code === 1) {
            let list = res.data || [];
            let pre_ip = "";
            let index = list
              .map((item) => item.ip1)
              .indexOf(_self.trendParam.cur_ip);
            for (let i = index - 1; i >= 0; i--) {
              if (list[i].ip1 != "*" && list[i].suspect != true) {
                pre_ip = list[i].ip1;
                break;
              }
            }
            let node_ip = "";
            for (let i = index + 1; i < list.length; i++) {
              if (list[i].ip1 != "*") {
                node_ip = list[i].ip1;
                break;
              }
            }
            if (this.ishigh != 4) {
              _self.trendParam.pre_ip = pre_ip;
              if (extraFlag === 1) {
                this.echartLookParama2.nodeIp = node_ip;
              } else {
                this.echartLookParama2.preNodeIp = pre_ip;
              }
            } else if (this.ishigh == 4) {
              _self.trendParam.pre_ip = list[0].ip1;
              _self.echartLookParama2.preNodeIp = list[0].ip1;
            }

            _self.topologyHandle(_self.trendParam.cur_ip, extraFlag, list);
            if (this.ishigh == 4) {
              _self.trendParam.cur_ip = this.highCurIp;
              _self.echartLookParama2.nodeIp = this.highCurIp;
              _self.echartLookParama2.queryType = 1;
            } else {
              _self.echartLookParama2.queryType = 2;
            }
            _self.getTrend(_self.echartLookParama2);
            _self.loading1 = false;
          }
        })
        .catch(() => {
          _self.loading1 = false;
        })
        .finally(() => {
          _self.loading1 = false;
        });
    },
    //处理拓扑图数据
    topologyHandle(ip, extraFlag, data) {
      let ipArray = ip.split(",")[0],
        list = data;
      let index = list.map((item) => item.ip1).indexOf(ipArray);
      let number = 0,
        brokenNum = 0,
        isIpduan = false;
      if (list.length > 0) {
        let isBrokenF = false,
          isDelay = false,
          borkenIndex = 0,
          delayIndex = 0;

        list.forEach((item, index) => {
          if (
            item.isBroken === 1 ||
            item.isBroken === 3 ||
            item.isBroken === 7
          ) {
            isBrokenF = true;
            borkenIndex = index;
          }
          if (item.isBroken === 4) {
            isIpduan = true;
          }
          if (item.isBroken === 2) {
            isDelay = true;
            delayIndex = index;
          }
        });
        for (let i = 0, len = list.length; i < len; i++) {
          /*重新组装数据start*/
          /*nodeColor:r,y,b;lineColor:r,y,b,linkPoint:true*/
          if (list[i].isBroken != 0) {
            brokenNum = list[i].isBroken;
          }
          list[i].nodeColor =
            list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
          list[i].lineColor =
            list[i].isBroken === 0 ? "b" : list[i].isBroken === 2 ? "y" : "r";
          list[i].linkPoint = list[i].isBroken == 1 ? true : false;
        }
        //处理中断ip段 前的ip为*的处理
        let thisIndex = true;
        if (isBrokenF) {
          for (let index = borkenIndex; index > 0; index--) {
            if (thisIndex && list[index - 1].ip1 == "*") {
              list[index - 1].nodeColor = "r";
              list[index - 1].lineColor = "r";
              thisIndex = true;
            } else if (thisIndex && list[index - 1].suspect === true) {
              list[index - 1].nodeColor = "r";
              list[index - 1].lineColor = "r";
              thisIndex = true;
            } else {
              thisIndex = false;
            }
          }
        }
        //处理时延ip段 前的ip为*的处理
        let delayFlag = true;
        if (isDelay) {
          for (let index = delayIndex; index > 0; index--) {
            if (delayFlag && list[index - 1].ip1 == "*") {
              list[index - 1].nodeColor = "y";
              list[index - 1].lineColor = "y";
              delayFlag = true;
            } else if (delayFlag && list[index - 1].suspect === true) {
              list[index - 1].nodeColor = "y";
              list[index - 1].lineColor = "y";
              delayFlag = true;
            } else {
              delayFlag = false;
            }
          }
        }
        let indexBroken = list.map((item) => item.isBroken != 0).indexOf(true),
          indexX = 0;
        for (let i = indexBroken - 1; i >= 0; i--) {
          if (i === indexBroken - 1 && list[i].ip1 === "*" && i - 1 >= 0) {
            indexX = i - 1;
            list[i].lineColor = brokenNum === 2 ? "y" : "r";
          } else if (indexX === i && list[i].ip1 === "*" && i - 1 >= 0) {
            indexX = i - 1;
            list[i].lineColor = brokenNum === 2 ? "y" : "r";
          }
        }

        if (
          list.length === 2 &&
          (list[0].isBroken === 1 || list[1].isBroken === 1)
        ) {
          list[1].lineColor = "r";
          list[1].nodeColor = "r";
          list[1].linkPoint = true;
        }
        list.forEach((item, index) => {
          if (index > borkenIndex && borkenIndex != 0) {
            item.nodeColor = "g";
            item.lineColor = "g";
          }
        });
        this.flow_chart = list;
      } else {
        this.flow_chart = [];
      }
    },
    //拓扑图事件
    flowChartClick(param) {
      let _self = this;
      this.echartLookParama2.level = "";
      this.echartLookParama2.startTime = this.saveStartTime;
      this.echartLookParama2.endTime = this.saveEndTime;
      if (this.ishigh != 4) {
        if (param.suspect) {
          this.suspect = true;
        } else {
          this.suspect = false;
        }
        // if (param.type === 1 && param.index === this.flow_chart.length - 1) {
        if (param.type === 1 && param.index != 0) {
          _self.echartLookParama2.preNodeIp = param.data[0].ip1;
          _self.echartLookParama2.nodeIp = param.data[1].ip1;
          _self.trendParam.pre_ip = param.data[0].ip1;
          _self.trendParam.cur_ip = param.data[1].ip1;
          _self.echartLookParama2.queryType = 1;
          if (_self.echartLookParama2.nodeIp != "*") {
            this.topoShow = false;
            _self.getTrend(_self.echartLookParama2);
          } else {
            this.topoShow = true;
          }
        } else if (param.type === 2) {
          this.topoShow = false;
          _self.echartLookParama2.queryType = 2;
          _self.echartLookParama2.preNodeIp = param.data[0].ip1;
          _self.echartLookParama2.nodeIp = param.data[1].ip1;
          _self.trendParam.pre_ip = param.data[0].ip1;
          _self.trendParam.cur_ip = param.data[1].ip1;
          _self.getTrend(_self.echartLookParama2);
        }
      }
    },

    //选择
    choiceChange(row) {
      this.ishigh = row.dataSource;
      if (this.ishigh == 4) {
        this.echartLookParama2.High = true;
        this.echartLookParama2.level = "";
      } else if (this.ishigh != 4) {
        this.echartLookParama2.High = false;
        this.echartLookParama2.level = null;
      }
      let _self = this;
      //拓扑图参数
      _self.topologyParam.linkId = row.linkId;
      _self.topologyParam.eventId = row.eventId;
      _self.topologyParam.triggerTime = row.triggerTime;
      this.markPoint = row.triggerTime;
      this.markPointGreen = row.endTime;
      //趋势图参数
      _self.trendParam.linkId = row.linkId;
      _self.echartLookParama2.linkId = row.linkId;
      if (this.ishigh == 4) {
        _self.highCurIp = row.destIp;
      } else {
        _self.highCurIp = "";
      }
      if (row.extraFlag == 1) {
        _self.echartLookParama2.preNodeIp = row.faultIp;
      } else if (row.extraFlag != 1) {
        _self.echartLookParama2.nodeIp = row.faultIp;
      }
      if (row.triggerTime) {
        const timeInterval = row.endTime
          ? new Date(row.endTime) - new Date(row.triggerTime)
          : new Date() - new Date(row.triggerTime);
        const handradSevenTyMinutes = 170 * 60 * 1000,
          handradSixtyTwoHours = 162 * 60 * 60 * 1000;
        if (this.ishigh == 4) {
          if (timeInterval <= handradSevenTyMinutes) {
            _self.echartLookParama2.startTime = moment(row.triggerTime)
              .subtract(5, "minutes")
              .format("YYYY-MM-DD HH:mm:ss");
            this.echartLookParama2.endTime = moment(
              row.endTime,
              "YYYY-MM-DD HH:mm:ss"
            )
              .add(5, "minutes")
              .format("YYYY-MM-DD HH:mm:ss");
          } else if (
            timeInterval > handradSevenTyMinutes &&
            timeInterval <= handradSixtyTwoHours
          ) {
            _self.echartLookParama2.startTime = moment(row.triggerTime)
              .subtract(3, "hours")
              .format("YYYY-MM-DD HH:mm:ss");
            this.echartLookParama2.endTime = moment(
              row.endTime,
              "YYYY-MM-DD HH:mm:ss"
            )
              .add(3, "hours")
              .format("YYYY-MM-DD HH:mm:ss");
          } else {
            _self.echartLookParama2.startTime = moment(row.triggerTime)
              .subtract(7, "days")
              .format("YYYY-MM-DD HH:mm:ss");
            this.echartLookParama2.endTime = moment(
              row.endTime,
              "YYYY-MM-DD HH:mm:ss"
            )
              .add(7, "days")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        } else {
          if (timeInterval > handradSixtyTwoHours) {
            _self.echartLookParama2.startTime = moment(row.triggerTime)
              .subtract(7, "days")
              .format("YYYY-MM-DD HH:mm:ss");
            this.echartLookParama2.endTime = moment(
              row.endTime,
              "YYYY-MM-DD HH:mm:ss"
            )
              .add(7, "days")
              .format("YYYY-MM-DD HH:mm:ss");
          } else {
            _self.echartLookParama2.startTime = moment(row.triggerTime)
              .subtract(3, "hours")
              .format("YYYY-MM-DD HH:mm:ss");
            this.echartLookParama2.endTime = moment(
              row.endTime,
              "YYYY-MM-DD HH:mm:ss"
            )
              .add(3, "hours")
              .format("YYYY-MM-DD HH:mm:ss");
          }
        }
        if (row.endTime === "" || row.endTime == null) {
          this.echartLookParama2.endTime = new Date().format(
            "yyyy-MM-dd HH:mm:ss"
          );
        }
      }
      this.saveStartTime = this.echartLookParama2.startTime;
      this.saveEndTime = this.echartLookParama2.endTime;
      if (row.faultIp.indexOf("-") > -1) {
        let faultIpsArr = row.faultIp.split("-");
        let reg =
          /(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])/;

        _self.echartLookParama2.nodeIp = faultIpsArr[faultIpsArr.length - 1];
        _self.trendParam.cur_ip = faultIpsArr[faultIpsArr.length - 1];
      } else {
        _self.echartLookParama2.nodeIp = row.faultIp;
        _self.trendParam.cur_ip = row.faultIp;
      }
      _self.trendParam.triggerTime = row.triggerTime;
      _self.getTopology(_self.topologyParam, row.extraFlag);
    },
    //分页
    pageChange(page) {
      this.tabParam.pageNo = page;
      this.getList(this.tabParam);
    },
    //清除数据
    resetRegion() {
      this.defaultTime = "";
      this.tabParam = {
        reportId: "",
        pageNo: 1,
        pageSize: 3,
      };
      this.currentid = 1;
      this.tabList = [];
      this.totalCount = 0;
      this.topologyParam = {
        reportId: "",
        linkId: "",
        faultType: "",
        eventId: "",
        triggerTime: "",
      };
      this.trendParam = {
        reportId: "",
        pre_ip: "",
        cur_ip: "",
        linkId: "",
        triggerTime: "",
      };
      this.echartLookParama2 = {
        preNodeIp: "",
        nodeIp: "",
        linkId: "",
        startTime: "",
        endTime: "",
        queryType: "",
        special: false,
        level: 2,
      };
      this.flow_chart = [];
      if (this.delayLossChart1) {
        this.delayLossChart1.clear();
      }
    },

    /*趋势图有关*/
    async getTrend(param) {
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.spinShow = true;
      await this.$http
        .PostJson("/trend/getDelayAndLostTrend", param)
        .then((res) => {
          //时延丢包趋势数据
          if (res.code === 1) {
            this.preAlias.name = res.data.preName;
            this.preAlias.port = res.data.prePort;
            this.zAlias.name = res.data.name;
            this.zAlias.port = res.data.port;
            this.delayLossLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              this.echart1.show = false;
            }
            let lineData = [];
            if (res.data.lineOne) {
              lineData = res.data.lineOne;
            } else if (res.data.lineTwo) {
              lineData = res.data.lineTwo;
            }
            let index = this.closest(lineData, this.markPoint);
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.echart1.show = true;
              this.delayStart = 0;
              // if (res.data.lineOne.length <= 300) {
              this.delayEnd = 100;
              this.startValue = res.data.lineOne[0][0];
              this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
              // } else {
              //   this.delayEnd = (300 * 100) / res.data.lineOne.length;
              //   if (index !== "noPoint") {
              //     if (index <= 150) {
              //       this.startValue = res.data.lineOne[0][0];
              //       if (index + 150 < res.data.lineOnetotal - 1) {
              //         this.endValue = res.data.lineOne[index + 150][0];
              //       } else {
              //         this.endValue =
              //           res.data.lineOne[res.data.lineOne.length - 1][0];
              //       }
              //     } else {
              //       this.startValue = res.data.lineOne[index - 150][0];
              //       if (index + 150 <= res.data.lineOne.length - 1) {
              //         this.endValue = res.data.lineOne[index + 150][0];
              //       } else {
              //         this.endValue =
              //           res.data.lineOne[res.data.lineOne.length - 1][0];
              //       }
              //     }
              //   }
              // }
              this.delayLossScale = true;
              this.delayLossData.delay = res.data.lineOne;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.delayLossData.loss = res.data.lineTwo;
            }
            this.$store.commit("updateDelayLossHistory", {
              level: res.data.level,
              datas: [this.delayLossData.delay, this.delayLossData.loss],
            }); //保存数据
            this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
          }
        })
        .finally(() => (this.spinShow = false));

      let trendParam = JSON.parse(JSON.stringify(param));
      // trendParam.level = 2;
      trendParam = Object.assign(trendParam, {
        snmp: this.dataSource == 1 ? true : false,
      });
      await this.$http
        .PostJson("/trend/getDataTrend", trendParam)
        .then((res) => {
          //流速趋势数据
          if (res.code === 1) {
            let flowUnit = 1;
            this.flowLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              this.echart2.show = false;
              this.flowLevel = 99;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.echart2.show = true;
              this.flowData.enter = res.data.lineTwo;
              this.flowData.issue = res.data.lineOne;
            }
          }
        })
        .finally(() => {});
      this.startScale = false;
      // if (this.echart2.show) {
      //   this.height = 500;
      // } else if (!this.echart2.show) {
      //   this.height = 260;
      // }
      this.initEchart();
    },
    setDelayLossTooltip() {
      let _self = this;
      // 修改颜色
      return {
        trigger:'axis',
          backgroundColor:this.currentSkin == 1 ? 'rgba(12, 56, 123,0.7)' : '#FFFFFF',
         textStyle: {
            color: this.currentSkin == 1 ? '#FFFFFF' : '#515A6E'  // 添加这个配置控制文字颜色
        },
         extraCssText: this.currentSkin == 0 ? 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);' : '',
        formatter:function(param) {
            _self.scale = param[0].data[0];
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex]
            ? ""
            : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let src = "",
          delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
              param[i].value[1] === null ||
              param[i].value[1] === "" ||
              param[i].value[1] == -1
                ? "--"
                : _self.flowSize(param[i].value[1], true, true)) +
              // param[i].value[1]
              // _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip;
          
    }

      }
      // return eConfig.tip("axis", function (param) {
      //   console.log(param,'这是个什么东西')
      //   _self.scale = param[0].data[0];
      //   var obj = {};
      //   param = param.reduce(function (item, next) {
      //     obj[next.seriesIndex]
      //       ? ""
      //       : (obj[next.seriesIndex] = true && item.push(next));
      //     return item;
      //   }, []);
      //   let src = "",
      //     delayTime = "",
      //     delayTip = "",
      //     flowTime = "",
      //     flowTip = "";
      //   for (let i = 0, len = param.length; i < len; i++) {
      //     if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
      //       delayTime = param[i].data[0] + "<br />";
      //       delayTip +=
      //         '<span class="tooltip-round" style="background-color:' +
      //         param[i].color +
      //         '"></span>';
      //       delayTip +=
      //         param[i].seriesName +
      //         "：" +
      //         (param[i].value[1] === undefined ||
      //         param[i].value[1] === null ||
      //         param[i].value[1] === "" ||
      //         param[i].value[1] == -1
      //           ? "--"
      //           : param[i].value[1]) +
      //         _self.delayLossUnit[param[i].seriesName] +
      //         "<br />";
      //     }
      //     if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
      //       flowTime = param[i].data[0] + "<br />";
      //       flowTip +=
      //         '<span class="tooltip-round" style="background-color:' +
      //         param[i].color +
      //         '"></span>';
      //       flowTip +=
      //         param[i].seriesName +
      //         "：" +
      //         (param[i].value[1] === undefined ||
      //         param[i].value[1] === null ||
      //         param[i].value[1] === "" ||
      //         param[i].value[1] == -1
      //           ? "--"
      //           : _self.flowSize(param[i].value[1], true, true)) +
      //         // param[i].value[1]
      //         // _self.delayLossUnit[param[i].seriesName] +
      //         "<br />";
      //     }
      //   }
      //   return delayTime + delayTip + flowTime + flowTip;
      // });
    },
    Option() {
      console.log(this.markPoint);
      console.log(this.markPointGreen);
      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "5%",
              right:'5%',
              top: "40px",
              // width: "84%",
              height: this.echart2.show ? "140px" : "140px",
            },
            {
              left: "5%",
              right:'5%',
              top: "280px",
              height: this.echart2.show ? "140px" : "0px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                // color: "#484b56",
                color: top.window.isdarkSkin == 1 || this.isbigscreen? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t('speed_delay'), this.$t('comm_packet_loss')]
            },
            {
              show: this.echart2.show,
              top: "220px",
              right: "43%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                // color: "#484b56",
                color: top.window.isdarkSkin == 1 || this.isbigscreen? "#FFFFFF" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              data: [this.$t('specquality_incoming_velocity'), this.$t('specquality_exit_velocity')],
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              // splitLine: {
              //   lineStyle: {
              //     type: "dashed",
              //     color: "#e3e7f2",
              //   },
              // },
              axisLabel: {
                textStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#0e2a5f",
                },
              },
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: this.$t('comm_delay(ms)'),
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#2A3840" : "#e3e7f2",
                },
              },
            },
            {
              name: this.$t('comm_loss_rate'),
              type: "value",
              scale: true,
              gridIndex: 0,
              max: 100,
              min: 0,
              position: "right",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
            {
              show: this.echart2.show,
              // name: "流速(" + this.flowUnit + ")",
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  // color: "#676f82",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  // color: "#e3e7f2",
                  color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#e3e7f2",
                },
              },
              axisLabel: {
                show: true,
                formatter: (value) => {
                  return this.getUnit(value, true, true);
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue,
              endValue: this.endValue,
              // start:this.delayStart,
              // end:this.delayEnd
            },
            {
              type: "slider",
              left: "5%",
              right: "5%",
              top: this.echart2.show ? "460px" : "230px",
              height: 20,
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] ,//"rgba(2, 29, 54, 1)",
              borderColor:eConfig.dataZoom.borderColor[this.currentSkin] ,// "rgba(22, 67, 107, 1)",
              handleStyle: {
                color:eConfig.dataZoom.handleStyle.color[this.currentSkin] ,// "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: top.window.isdarkSkin == 1 || this.isbigscreen? "#5CA0D5" : "#676f82",
              }
            },
          ],
          series: [
            {
              type: "line",
              name: this.$t('speed_delay'),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbolSize:1,
              symbol: this.delayLossData.delay.length > 1 ? "circle" : "circle",
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(0,255,238, 0.8)"
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0,255,238, 0.2)"
                    }
                  ],
                },
              },
              data: this.delayLossData.delay,
              markLine: {
                data: [
                  {
                    symbol: "image://" + pointRed,
                    // symbol: "rect" ,
                    symbolSize: 10,
                    xAxis: this.markPoint || "",
                    symbolRotate: "0",
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint;
                      },
                      // backgroundColor: "#fff",
                    },
                  },
                  {
                    symbol: "image://" + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen || "",
                    symbolRotate: "0",
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: "#fff",
                      formatter: () => {
                        return this.markPointGreen;
                      },
                    },
                  },
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1,
                  },
                },
              },
            },
            {
              type: "line",
              name: this.$t('comm_packet_loss'),
              xAxisIndex: 0,
              yAxisIndex: 1,
              smooth: true,
              symbolSize:1,
              symbol: this.delayLossData.loss.length > 1 ? "circle" : "circle",
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(2, 158, 255, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(2, 158, 255, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.loss,
              markLine: {
                data: [
                  {
                    symbol: "image://" + pointRed,
                    // symbol: "rect" ,
                    symbolSize: 10,
                    xAxis: this.markPoint || "",
                    symbolRotate: "0",
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint;
                      },
                      // backgroundColor: "#fff",
                    },
                  },
                  {
                    symbol: "image://" + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen || "",
                    symbolRotate: "0",
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: "#fff",
                      formatter: () => {
                        return this.markPointGreen;
                      },
                    },
                  },
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1,
                  },
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_incoming_velocity'),
              type: "line",
              smooth: true,
              symbolSize:1,
              symbol: this.flowData.enter.length > 1 ? "circle" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(71,142,233, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(71,142,233, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_exit_velocity'),
              type: "line",
              smooth: true,
              symbolSize:1,
              symbol: this.flowData.issue.length > 1 ? "circle" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart1) {
        echarts.dispose(that.delayLossChart1);
        that.delayLossChart1.clear();
      }
      top.document.getElementById("alarm-delayLoss").style.height =
        this.height + "px";
      that.delayLossChart1 = echarts.init(this.$refs["alarm-delayLoss"]);

      /*设置时延丢包率echart图*/
      that.delayLossChart1.setOption(this.Option());
      that.delayLossChart1.getZr().on("mousewheel", (params) => {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getgoodTlevel = that.$store.state.goodRateTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据
        let getgoodSaveData = that.$store.state.goodRateHistory; //获取保存的数据

        var pointInPixel = [params.offsetX, params.offsetY];
        if (
          that.delayLossChart1.containPixel(
            { gridIndex: [0, 1, 2] },
            pointInPixel
          )
        ) {
          let startValue =
            that.delayLossChart1.getModel().option.dataZoom[0].startValue;
          let endValue =
            that.delayLossChart1.getModel().option.dataZoom[0].endValue;
          this.echartLookParama2.startTime = new Date(startValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          this.echartLookParama2.endTime = new Date(endValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);
          let differ = endValue - startValue;
          let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
          let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
          let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
          var levelNum = "";
          let start = that.delayLossChart1.getModel().option.dataZoom[0].start;
          let end = that.delayLossChart1.getModel().option.dataZoom[0].end;

          if (params.wheelDelta >= 0) {
            if (this.delayLossLevel == 1) {
              // 小时粒度
              if (differ < minute) {
                if (!that.startScale) {
                  that.startScale = true;
                  levelNum = 2;
                  let delayParam = Object.assign(this.echartLookParama2, {
                    level: levelNum,
                  });
                  let flowParam = Object.assign(delayParam, {
                    snmp: this.dataSource == 1 ? true : false,
                  });
                  this.getDelayLoss(delayParam, flowParam);
                }
              }
            } else if (this.delayLossLevel == 2) {
              // 分钟粒度
              if (differ < original) {
                if (!that.startScale) {
                  that.startScale = true;
                  if (this.ishigh == 4) {
                    levelNum = 4;
                    let delayParam = Object.assign(this.echartLookParama2, {
                      level: levelNum,
                    });
                    let flowParam = Object.assign(delayParam, {
                      snmp: this.dataSource == 1 ? true : false,
                    });
                    this.getDelayLoss(delayParam, flowParam);
                  }
                }
              }
            }
          } else {
            if (start == 0 && end == 100) {
              //是否处在缩放过程中
              if (that.delayLossLevel == getTlevel) {
                that.setPs(that.delayLossLevel, [
                  that.timeChange(startValue),
                  that.timeChange(endValue),
                ]);
                that.startScale = false;
              } else {
                let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                if (that.flowLevel == getflowTlevel) {
                  that.startScale = false;
                } else {
                  if (that.flowLevel == 2) {
                    that.flowLevel = 1;
                    that.startScale = true;
                    that.flowUnit = getflowSaveUnit.HoursUnit;
                    that.flowData.enter = getflowSaveData.HoursData.enter;
                    that.flowData.issue = getflowSaveData.HoursData.issue;
                  } else if (that.flowLevel == 3 || that.flowLevel == 4) {
                    that.flowLevel = 2;
                    that.startScale = true;
                    that.flowUnit = getflowSaveUnit.minuteUnit;
                    that.flowData.enter = getflowSaveData.minuteData.enter;
                    that.flowData.issue = getflowSaveData.minuteData.issue;
                  }
                }
                if (that.goodRateLevel == getgoodTlevel) {
                  that.startScale = false;
                } else {
                  if (that.goodRateLevel == 2) {
                    that.goodRateLevel = 1;
                    that.startScale = true;
                    that.goodRateData.nrUseRateList =
                      getgoodSaveData.HoursUnit.nrUseRateList;
                    that.goodRateData.nrGoodRateList =
                      getgoodSaveData.HoursUnit.nrGoodRateList;
                    that.goodRateData.useRateList =
                      getgoodSaveData.HoursUnit.useRateList;
                    that.goodRateData.goodRateList =
                      getgoodSaveData.HoursUnit.goodRateList;
                  } else if (
                    that.delayLossLevel == 3 ||
                    that.delayLossLevel == 4
                  ) {
                    that.delayLossLevel = 2;
                    that.startScale = true;
                    that.goodRateData.nrUseRateList =
                      getgoodSaveData.minuteUnit.nrUseRateList;
                    that.goodRateData.nrGoodRateList =
                      getgoodSaveData.minuteUnit.nrGoodRateList;
                    that.goodRateData.useRateList =
                      getgoodSaveData.minuteUnit.useRateList;
                    that.goodRateData.goodRateList =
                      getgoodSaveData.minuteUnit.goodRateList;
                  }
                }

                if (that.delayLossLevel == getTlevel) {
                  that.startScale = false;
                } else if (that.delayLossLevel == 2) {
                  that.delayLossLevel = 1;
                  that.startScale = true;
                  that.delayLossData.delay = getSaveData.HoursData.delay;
                  that.delayLossData.loss = getSaveData.HoursData.loss;
                  that.delayStart = getSite.psH.start;
                  that.startValue = getSite.psH.start;
                  that.delayEnd = getSite.psH.end;
                  that.endValue = getSite.psH.end;
                } else if (
                  that.delayLossLevel == 3 ||
                  that.delayLossLevel == 4
                ) {
                  that.delayLossLevel = 2;
                  that.startScale = true;
                  that.delayLossData.delay = getSaveData.minuteData.delay;
                  that.delayLossData.loss = getSaveData.minuteData.loss;
                  that.delayStart = getSite.psM.start;
                  that.startValue = getSite.psM.start;
                  that.delayEnd = getSite.psM.end;
                  that.endValue = getSite.psM.end;
                }

                setTimeout(() => {
                  that.startScale = false;
                  that.initEchart();
                }, 300);
              }
            }
          }
        }
      });
    },

    async getDelayLoss(
      delayParam,
      flowParam,
      goodRateParam,
      cachD,
      cachF,
      cachG,
      hoverDelayTime,
      flowTime,
      useTime
    ) {
      let isUpdate = false;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        this.spinShow = true;
        await this.$http
          .PostJson("/trend/getDelayAndLostTrend", delayParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.delayLossLevel = res.data.level;
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              let index = this.closest(lineData, hoverDelayTime);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.delayStart = 0;
                // if (res.data.lineOnetotal <= 300) {
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue =
                  res.data.lineOne[res.data.lineOne.length - 1][0];
                // } else {
                //   this.delayEnd = (300 * 100) / res.data.lineOnetotal;
                //   if (index <= 150) {
                //     this.startValue = res.data.lineOne[0][0];
                //     if (index + 150 < res.data.lineOnetotal - 1) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //         res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   } else {
                //     this.startValue = res.data.lineOne[index - 150][0];
                //     if (index + 150 < res.data.lineOnetotal) {
                //       this.endValue = res.data.lineOne[index + 150][0];
                //     } else {
                //       this.endValue =
                //         res.data.lineOne[res.data.lineOne.length - 1][0];
                //     }
                //   }
                // }
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            }
          });
      }

      if (flowParam.level != 99) {
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, { snmp: true });
        await this.$http
          .PostJson("/trend/getDataTrend", trendParam)
          .then((res) => {
            //流速趋势数据
            if (res.code === 1) {
              let flowUnit = 1;
              this.flowLevel = res.data.level;
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                let unitChange = this.getFlowUnit(res.data.lineOne);
                this.flowUnit = unitChange[0];
                this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
                this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
                flowUnit = unitChange[1];
                this.flowData.enter = res.data.lineTwo.map((item) => {
                  // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                  return [item[0], item[1]];
                });
              }
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.flowData.issue = res.data.lineOne.map((item) => {
                  // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                  return [item[0], item[1]];
                });
              }
              if (cachF) {
                this.$store.commit("updateFlowHistory", {
                  level: res.data.level,
                  datas: [this.flowData.enter, this.flowData.issue],
                }); //保存数据
                this.$store.commit("setflowUnit", {
                  level: res.data.level,
                  unit: this.flowUnit,
                }); //保存单位
              }
            }
          });
      }

      this.delayLoading = false;
      this.spinShow = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      if (isUpdate) {
        this.initEchart();
      }
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },
  },
  beforeDestroy() {
    if (!this.delayLossChart1) {
      return;
    }
    this.delayLossChart1.clear();
    this.delayLossChart1.dispose();
    this.delayLossChart1 = null;
  },
};
</script>
