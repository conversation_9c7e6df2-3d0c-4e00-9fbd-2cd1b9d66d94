<template>
  <div :class="{ 'light-no-tabs': currentSkin == 0 }">
    <section class="sectionBox">
      <!-- 数据备份页面 -->
      <div class="section-top">
        <Row class="fn_box">
          <!-- <Col span="9"> -->
          <div class="fn_item" style="width: 400px">
            <label class="fn_item_label"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 317px"
                :confirm="false"
              >
              </DatePicker>
            </div>
          </div>
          <!-- </Col> -->
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("backup_path") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select v-model="backup.type">
                  <Option value="0">{{ $t("backup_path_local") }}</Option>
                  <Option value="1">{{ $t("backup_path_remote") }}</Option>
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <Button
                v-if="permissionObj.add"
                type="primary"
                class="jiaHao-btn add-icon"
                @click="execute"
                :disabled="btnFlag"
                :title="$t('reachability_carry_out')"
                style="margin-left: 35px"
              >
                <i class="iconfont icon-icon-execute" />
              </Button>
            </div>
          </Col>
        </Row>
        <Row class="fn_box" v-show="backup.type == 1">
          <Col span="6">
            <div class="fn_item backupServerItem">
              <label class="fn_item_label">IP：</label>
              <div class="fn_item_box">
                <Input
                  v-model.trim="server.serverIp"
                  :placeholder="$t('logback_enter_ip')"
                />
              </div>
              <div>
                <div
                  v-show="errorFlag.ip"
                  class="error_label"
                  style="position: fixed; left: 100px; margin-top: 38px"
                >
                  {{ $t("backup_incorrect_ip_format") }}
                </div>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item backupServerItem">
              <label class="fn_item_label"
                >{{ $t("backup_user_name1") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Input
                  v-model.trim="server.serverUserName"
                  :placeholder="$t('logback_fill_userName')"
                />
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item backupServerItem">
              <label class="fn_item_label"
                >{{ $t("backup_password") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Input
                  type="password"
                  v-model="server.serverPassword"
                  :placeholder="$t('backup_prompt_fill_password')"
                />
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item backupServerItem">
              <label class="fn_item_label" style="width: 200px"
                >{{ $t("backup_path") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Input
                  v-model.trim="server.serverDestDir"
                  :placeholder="$t('backup_prompt_fill_path')"
                />
              </div>
            </div>
          </Col>
        </Row>
        <Row class="fn_box" v-if="progressShow">
          <Col span="12">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("backup_schedule") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <div class="progress">
                  <Progress
                    :percent="percentNum"
                    :stroke-width="20"
                    status="active"
                    text-inside
                  />
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>
      <Loading :loading="pageLoading"></Loading>
      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <!-- <div class="tableTitle">自动备份记录</div> -->
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              ref="tableList"
              stripe
              :columns="columns"
              :data="tableList"
              :no-data-text="
                loading
                  ? ''
                  : tableList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
              size="small"
            ></Table>
            <div
              class="tab-page"
              style="border-top: 0"
              v-if="tableList.length > 0"
            >
              <Page
                v-page
                :current.sync="query.pageNo"
                :page-size="query.pageSize"
                :total="totalCount"
                :page-size-opts="pageSizeOpts"
                :prev-text="$t('common_previous')"
                :next-text="$t('common_next_page')"
                @on-change="pageChange"
                @on-page-size-change="pageSizeChange"
                show-elevator
                show-sizer
              >
              </Page>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import locationreload from '@/common/locationReload';
import validate from '@/common/validate';

const validIp = value => {
    let ipv4Regex =  validate.getIpv4Regex(),
    
    // /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
        ipv6Regex = validate.getIpv6Regex();
            // /\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;
    if (ipv4Regex.test(value) || ipv6Regex.test(value)) {
        return true;
    } else {
        return false;
    }
};
import global from '@/common/global.js';
import axios from 'axios';
import '@/config/page.js';
import cryptoAES from '@/common/crypto.js';
import handlePassword from '@/common/handlePassword';
export default {
    data() {
        return {
            currentSkin:sessionStorage.getItem('dark') || 1,
            pageLoading: false,
            //权限对象
            permissionObj: {},
            timer: '',
            timer1: '',
            file: '',
            btnFlag: false,
            butState: true,
            percentNum: localStorage.getItem('percentNum') | 0,
            progressShow: false,
            loading: false,
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            query: {
                pageNo: 1,
                pageSize: 10
            },
            timeOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return [start.format('yyyy-MM-dd HH:mm:ss'), end.format('yyyy-MM-dd HH:mm:ss')];
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate()
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    }
                ]
            },
            timeRange: [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')],
            currentTime: Date.now(),
            backup: {
                startTime: '',
                endTime: '',
                dateValue: '',
                type: '0'
            },
            server: {
                serverUserName: '',
                serverPassword: '',
                serverIp: '',
                serverDestDir: ''
            },
            columns: [
                {
                    title: this.$t('backup_time'),
                    key: 'createTime',
                    align: 'left',
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.createTime;
                        return h('span', str === undefined || str === null || str === '' ? '--' : str);
                    }
                },
                {
                    title: this.$t('backup_file_size'),
                    key: 'fileSize',
                    align: 'left',
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.fileSize;
                        str = this.getSizeUnit(str);
                        return h('span', str === undefined || str === null || str === '' || str === '0 B' ? '--' : str);
                    }
                },
                {
                    title: this.$t('backup_address'),
                    key: 'filePath',
                    align: 'left',
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.filePath;
                        return h('span', str === undefined || str === null || str === '' ? '--' : str);
                    }
                },
                {
                    title: this.$t('comm_status'),
                    key: 'status',
                    align: 'left',
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.status;
                        let colorValue = 'red';
                        if (str === 0 || str === '0') {
                            str = this.$t('backup_ing');
                            colorValue = '#FFFF00';
                        } else if (str === 1 || str === '1') {
                            str = this.$t('backup_sucess');
                            colorValue = '#23D692';
                        } else if (str === 2 || str === '2') {
                            str = this.$t('backup_fail');
                        }
                        return h(
                            'span',
                            {
                                style: {
                                    color: colorValue
                                }
                            },
                            str === undefined || str === null || str == 'null' || str === '' ? '--' : str
                        );
                    }
                },
                {
                    title: this.$t('snmpoid_error_details'),
                    key: 'message',
                    align: 'left',
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.message;
                        return h('span', str === undefined || str === null || str === '' ? '--' : str);
                    }
                },
                {
                    title: this.$t('common_controls'),
                    width: 120,
                    key: 'userName',
                    align: 'center',
                    className: 'bgColor',
                    // fixed:'right',
                    render: (h, params) => {
                        let className = ''
                        if(this.currentSkin == 1) {
                            // 深色
                            className = params.row.status == '1' ? 'del1-btn' : 'del2-btn'
                        }else {
                            // 浅色
                            className = params.row.status == '1' ? 'del1-btn' : 'light-del2-btn'


                        }
                        let del = h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'left-end',
                                    transfer: true
                                }
                            },
                            [
                                h('span', {
                                    // this.btnFlag ? params.row.status == '0' ? 'del2-btn' : 'del1-btn' : 'del1-btn',
                                    class: className,
                                    style: {
                                        display: this.permissionObj.delete ? 'inline-block' : 'none',
                                        marginRight: '0px',
                                    },
                                    on: {
                                        click: () => {
                                            if (params.row.status == '1') {
                                                this.delClick(params.row);
                                            }
                                        }
                                    }
                                }),
                                h('span', { slot: 'content' }, this.$t('common_delete'))
                            ]
                        ),
                            down = h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                        transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: params.row.status == '1' ? 'dow-btn' : 'dow-btn-dis',
                                        style: {
                                            display: this.permissionObj.download ? 'inline-block' : 'none',
                                            color: this.btnFlag ? '#D3D3D3' : '#D3D3D3',
                                            marginRight: '0px',
                                        },
                                        on: {
                                            click: () => {
                                                if (params.row.status == '1') {
                                                    if (params.row.filePath) {
                                                        this.$http
                                                            .wisdomPost('/audit/saveLog', {
                                                                url: '/backupData/download',
                                                                funName: this.$t('backup_data_backup'),
                                                                description: this.$t('backup_down_data_backup') + params.row.filePath + ')',
                                                                operationType: 26,
                                                                eventType: 7,
                                                                alarmType: 0,
                                                                logType: 1,
                                                                isDel: 0
                                                            })
                                                            .then(res => { });
                                                        //直接添加安全审计日志 结束
                                                        this.downFile(params.row);
                                                    } else {
                                                        this.$Message.warning({ content: this.$t('暂无数据'), background: true });
                                                    }
                                                }
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('wifi_download'))
                                ]
                            ),
                            // down = h(
                            //     'a',
                            //     {
                            //         class: 'action-btn',
                            //         style: {
                            //             color: 'green',
                            //             marginRight: '15px',
                            //             fontWeight: '600',
                            //             display: this.permissionObj.download ? 'inline-block' : 'none'
                            //         },
                            //         on: {
                            //             click: () => {
                            //                 //直接添加安全审计日志 开始
                            //                 this.$http
                            //                     .wisdomPost('/audit/saveLog', {
                            //                         url: '/backupData/download',
                            //                         funName: '数据备份',
                            //                         description: '下载数据备份(名称：' + params.row.filePath + ')',
                            //                         operationType: 26,
                            //                         eventType: 7,
                            //                         alarmType: 0,
                            //                         logType: 1,
                            //                         isDel: 0
                            //                     })
                            //                     .then(res => {});
                            //                 //直接添加安全审计日志 结束
                            //                 this.downFile(params.row);
                            //             }
                            //         }
                            //     },
                            //     '下载'
                            // ),
                            array = [down, del];
                        // return h('div', array);
                        return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                    }
                }
            ],
            tableList: [],
            //远程备份时必填验证
            errorFlag: {
                ip: false
            }
        };
    },
    created() {
        this.$nextTick(() => {
            locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
        })
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        // this.timeRange = [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
        this.getList(this.query);
        // 查看是否有正在进行的任务
        if (localStorage.getItem('dateValue') && localStorage.getItem('isProcess')) {
            this.progressShow = true; //显示进度条
            this.percentNumRefreh(localStorage.getItem('dateValue')); //刷新进度条
            this.processRefreh(localStorage.getItem('dateValue')); //查看线程进度
            this.btnFlag = true; //禁止按钮
        }

    },
    methods: {
        //获取列表数据
        getList(param) {
            this.loading = true;
            this.$http
                .wisdomPost('/backupData/list', param)
                .then(res => {
                    if (res.code === 1) {
                        if (res.data.records) {
                            this.tableList = res.data.records;
                            this.totalCount = res.data.total || 0;
                        } else {
                            _self.tableList = [];
                        }
                        this.loading = false;
                    } else {
                        this.$Message.error({ content: res.msg, background: true });
                    }
                })
                .catch(err => {
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        //删除
        delClick(param) {
            console.log('param===>', param);
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: '<p>' + this.$t('backup_prompt_delete') + '？</p>',
                onOk: () => {
                    this.$http.wisdomPost('/backupData/delete', { id: param.taskId }).then(res => {
                        if (res.code === 1) {
                            this.$Message.success({ content: this.$t('common_delete_success'), background: true });
                            this.query.pageNo = 1;
                            this.getList(this.query);
                        } else {
                            this.$Message.error({ content: res.msg, background: true });
                            this.getList(this.query);
                        }
                    });
                }
            });
        },
        //切换页码
        pageChange(page) {
            this.query.pageNo = page;
            this.getList(this.query);
        },
        //切换每页条数
        pageSizeChange(e) {
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getList(this.query);
        },
        timeChange(time) {
            let timeArr = String(time).split(',');
            this.backup.startTime = timeArr[0];
            this.backup.endTime = timeArr[1];
        },
        // 按钮切换
        downFile(fileObj) {
            this.pageLoading = true;
            let fileName = fileObj.filePath.substring(fileObj.filePath.lastIndexOf('/') + 1);
            if (0 != fileObj.type) {
                let downParam = {
                    filePath: fileObj.filePath,
                    fileName: fileName,
                    serverIp: fileObj.serverIp,
                    serverUserName: fileObj.serverUserName,
                    serverPassword: fileObj.serverPassword,
                    type: fileObj.type,
                    id: fileObj.id,
                    taskId: fileObj.taskId
                };
                axios({
                    timeout: 36000000,
                    method: 'post',
                    url: '/backupData/remoteDownload',
                    data: downParam
                })
                    .then(res => {
                        if (2 == res.data.code) {
                            this.$Message.warning({ content: res.data.msg, background: true });
                        } else {
                            let downFilePath = res.data.data.filePath;
                            let downFileName = res.data.data.fileName;
                            this.downloadHandle(fileObj, downFileName, downFilePath);
                        }
                    })
                    .catch(error => {
                        this.pageLoading = false;
                        this.getList(this.query);
                    })
                    .finally(() => {
                        this.pageLoading = false;
                        this.getList(this.query);
                    });
            } else {
                let downParam = {
                    filePath: fileObj.filePath,
                    fileName: fileName,
                    serverIp: fileObj.serverIp,
                    serverUserName: fileObj.serverUserName,
                    serverPassword: fileObj.serverPassword,
                    type: fileObj.type,
                    id: fileObj.id,
                    taskId: fileObj.taskId
                };
                // 判断文件是否被删除了
                axios({
                    method: 'post',
                    url: '/backupData/downloadVerify',
                    data: downParam
                })
                    .then(res => {
                        if (2 == res.data.code) {
                            this.$Message.warning({ content: res.data.msg, background: true });
                        } else {
                            this.downloadHandle(fileObj, fileName);
                        }
                    })
                    .catch(error => {
                        this.pageLoading = false;
                        this.getList(this.query);
                    })
                    .finally(() => {
                        this.pageLoading = false;
                        this.getList(this.query);
                    });
            }
        },

        downloadHandle(fileObj, fileName, downFilePath) {
            console.log(fileObj);
            if (!downFilePath) {
                downFilePath = fileObj.filePath;
            }
            let downParam = {
                filePath: downFilePath,
                fileName: fileName,
                serverIp: fileObj.serverIp,
                serverUserName: fileObj.serverUserName,
                serverPassword: fileObj.serverPassword,
                type: fileObj.type,
                id: fileObj.id,
                taskId: fileObj.taskId
            };
            this.$Loading.start();
            axios({
                timeout: 36000000,
                method: 'post',
                url: '/backupData/download',
                data: downParam,
                responseType: 'blob'
            })
                .then(res => {
                    let url = window.URL.createObjectURL(new Blob([res.data]));
                    let link = document.createElement('a');
                    link.style.display = 'none';
                    link.href = url;
                    link.setAttribute('download', fileName); //指定下载后的文件名，防跳转
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link); // 下载完成移除元素
                    window.URL.revokeObjectURL(url); // 释放掉blob对象
                    this.$Loading.finish();
                    this.getList(this.query);
                })
                .catch(error => {
                    console.log(error);
                    this.$Loading.finish();
                    this.getList(this.query);
                })
                .finally(() => {
                    this.$Loading.finish();
                    this.pageLoading = false;
                    this.getList(this.query);
                });
        },
        //  执行操作
        execute() {
            this.percentNum = 0;
            localStorage.setItem('percentNum', 0);
            if (this.timeRange[0] == '' || this.timeRange[1] == '') {
                this.$Message.warning({ contentexecute: this.$t('warning_date_not_null'), background: true });
                return;
            }
            let dateValue;
            if (localStorage.getItem('isProcess')) {
                this.$Message.warning({ content: this.$t('warning_task_repeat'), background: true });
                return;
            }
            else {
                dateValue = new Date().getTime(); //创建时间戳，作为后台缓存map中的key
                localStorage.setItem('dateValue', dateValue);
            }

            this.backup.dateValue = dateValue;
            let param = {};
            param.startTime = new Date(this.timeRange[0]).format('yyyy-MM-dd');
            param.endTime = new Date(new Date(this.timeRange[1]).getTime() + 86400000).format('yyyy-MM-dd');

            param.type = this.backup.type;
            param.dateValue = this.backup.dateValue;
            if (this.backup.type == 1) {
                if (this.server.serverUserName && this.server.serverPassword && this.server.serverIp && this.server.serverDestDir) {
                    param.serverUserName = handlePassword.encryption(this.server.serverUserName);
                    param.serverPassword = handlePassword.encryption(cryptoAES.encrypt(this.server.serverPassword) + '');
                    param.serverIp = this.server.serverIp;
                    param.serverDestDir = this.server.serverDestDir;
                    if (validIp(param.serverIp) == false) {
                        this.errorFlag.ip = true;
                        localStorage.removeItem('dateValue'); //移除本次任务key
                        localStorage.removeItem('percentNum'); //移除本次任务进度
                        localStorage.removeItem('isProcess'); //移除备份标志
                        return;
                    } else {
                        this.btnFlag = true;
                        this.errorFlag.ip = false;
                    }
                } else {
                    localStorage.removeItem('dateValue'); //移除本次任务key
                    localStorage.removeItem('percentNum'); //移除本次任务进度
                    localStorage.removeItem('isProcess'); //移除备份标志
                    this.$Message.warning({ content: this.$t('backup_prompt_remote_no_information'), background: true });
                    return;
                }
            } else if (this.backup.type == 0) {
                this.btnFlag = true;
            }

            axios({
                timeout: 36000000,
                url: '/backupData/backup',
                method: 'post',
                params: param
            })
                .then(res => {
                    // console.log("备份响应："+res);
                    if (res.data.code === 1) {
                        // this.percentNum = 100;
                        // this.$Message.success({ content: this.$t('comm_backup'), background: true });
                        this.btnFlag = true;
                        localStorage.setItem('isProcess', 1) // 是否开始备份
                        this.percentNumRefreh(dateValue); // 定时刷新进度条
                        this.processRefreh(dateValue); //查看线程进度
                        this.progressShow = true; //显示进度条
                        this.getList(this.query);
                    } else {
                        clearInterval(this.timer); //清除定时器
                        clearInterval(this.timer1); //清除定时器
                        localStorage.removeItem('dateValue'); //移除本次任务key
                        localStorage.removeItem('percentNum'); //移除本次任务进度
                        localStorage.removeItem('isProcess'); //移除备份标志
                        this.progressShow = false; //隐藏进度条
                        this.btnFlag = false; //启用执行按钮
                        this.getList(this.query);
                        this.percentNum = 0;
                        this.$Message.warning({ content: res.data.msg || this.$t('dash_anomaly'), background: true });
                    }
                })
                .catch(function (error) {
                    // console.log('异常：' + error); //可以拿到后端返回的信息
                    clearInterval(this.timer); //清除定时器
                    clearInterval(this.timer1); //清除定时器
                    localStorage.removeItem('dateValue'); //移除本次任务key
                    localStorage.removeItem('percentNum'); //移除本次任务进度
                    localStorage.removeItem('isProcess'); //移除备份标志
                    this.progressShow = false; //隐藏进度条
                    this.btnFlag = false; //启用执行按钮
                    this.getList(this.query);
                    this.percentNum = 0;
                    console.log('刷新异常===>');
                })
            // .finally(res => {
            //     console.log('finally：' + res); //可以拿到后端返回的信息
            //     clearInterval(this.timer); //清除定时器
            //     localStorage.removeItem('dateValue'); //移除本次任务key
            //     this.progressShow = false; //隐藏进度条
            //     this.btnFlag = false; //启用执行按钮
            //     this.percentNum = 0;
            //     this.getList(this.query);
            // });
            // this.$http.wisdomPost("/backupData/backup", this.backup)
            //           .then(({ code, data, msg }) => {
            //             if (code === 1) {
            //               this.percentNum=100;
            //               this.$Message.success("备份成功");
            //               this.getList(this.query);
            //             } else {
            //               this.$Message.warning(msg);
            //             }
            //             clearInterval(this.timer);//清除定时器
            //             this.progressShow=false;//隐藏进度条
            //             this.btnFlag=false;//启用执行按钮
            //             this.percentNum=0;
            //           })
        },
        // 定时刷新进度条
        percentNumRefreh(dateValue) {
            this.timer1 = setInterval(() => {
                //间隔时间去后台取缓存中的百分比值
                this.$http.wisdomPost('/backupData/getPercent', { dateValue: dateValue }).then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.percentNum = data;
                        localStorage.setItem('percentNum', data);
                    }
                });
            }, 2000);
        },
        // 定时查看线程执行情况
        processRefreh(dateValue) {
            this.timer = setInterval(() => {
                //间隔时间去后台取缓存中的百分比值
                this.$http.wisdomPost('/backupData/checkTask', { dateValue: dateValue }).then((res) => {
                    console.log('定时查看线程执行情况=>', res);
                    if (res.code === 1) {
                        if (res.data === 1) {
                            // 执行完成
                            this.percentNum = 100;
                            this.$Message.success({ content: this.$t('comm_backup'), background: true });
                            clearInterval(this.timer); //清除定时器
                            clearInterval(this.timer1); //清除定时器
                            localStorage.removeItem('dateValue'); //移除本次任务key
                            localStorage.removeItem('percentNum'); //移除本次任务进度
                            localStorage.removeItem('isProcess'); //移除备份标志
                            this.progressShow = false; //隐藏进度条
                            this.btnFlag = false; //启用执行按钮
                            this.getList(this.query);
                            this.percentNum = 0;
                            this.getList(this.query);
                        }
                    } else {
                        // 异常
                        this.$Message.error({ content: this.$t('backup_fail'), background: true });
                        clearInterval(this.timer); //清除定时器
                        clearInterval(this.timer1); //清除定时器
                        localStorage.removeItem('dateValue'); //移除本次任务key
                        localStorage.removeItem('percentNum'); //移除本次任务进度
                        localStorage.removeItem('isProcess'); //移除备份标志
                        this.progressShow = false; //隐藏进度条
                        this.btnFlag = false; //启用执行按钮
                        this.getList(this.query);
                        this.percentNum = 0;
                    }

                }).catch(function (error) {
                    // console.log('异常：' + error); //可以拿到后端返回的信息
                    clearInterval(this.timer); //清除定时器
                    localStorage.removeItem('dateValue'); //移除本次任务key
                    localStorage.removeItem('percentNum'); //移除本次任务进度
                    localStorage.removeItem('isProcess'); //移除备份标志
                    this.progressShow = false; //隐藏进度条
                    this.btnFlag = false; //启用执行按钮
                    this.percentNum = 0;
                    this.getList(this.query);
                })
            }, 2000);
        },
        getSizeUnit(val) {
            val = Number(val);
            if (val / 1024 < 1) {
                return val + ' B';
            }
            if (val / 1024 / 1024 < 1) {
                return (val / 1024).toFixed(2) + ' KB';
            }
            if (val / 1024 / 1024 / 1024 < 1) {
                return (val / 1024 / 1024).toFixed(2) + ' MB';
            }
            if (val / 1024 / 1024 / 1024 / 1024 < 1) {
                return (val / 1024 / 1024 / 1024).toFixed(2) + ' GB';
            }
            if (val / 1024 / 1024 / 1024 / 1024 / 1024 < 1) {
                return (val / 1024 / 1024 / 1024 / 1024).toFixed(2) + ' TB';
            }
        }
    }
};
</script>
<style scoped lang="less">
.progressBox {
  padding: 20px 0 30px 0;
  display: flex;
  align-items: center;

  label {
    width: 75px;
  }

  .progress {
    width: 30%;
    margin-right: 10px;
  }

  button {
    margin-left: 20px;
  }
}

.backupItem {
  padding-bottom: 20px;
  display: inline-flex;
  align-items: center;
  margin-right: 12px;

  button {
    margin-left: 20px;
  }

  label {
    width: 75px;
    text-align: right;
    margin-right: 5px;
  }
}

.backupServerItem {
  label:before {
    content: "*";
    color: #ed4014;
    margin-right: 3px;
    vertical-align: middle;
    font-size: 16px;
  }

  .error_label {
    color: #ed4014;
    cursor: default;
  }
}
</style>
