<template>
  <!-- 拨测质差分析 -->
  <div
    :class="{ 'light-no-tabs': currentSkin == 0, padding0: reportState == 1 }"
  >
    <section class="sectionBox">
      <div class="section-top">
        <Row class="fn_box">
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadData"
                  @onSelectChange="setOrg"
                  @onClear="onClear"
                  @onFocus="focusFn"
                ></TreeSelect>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.groupId"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('comm_select_group')"
                >
                  <Option
                    v-for="item in groupList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("specinfo_perspective") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.type"
                  filterable
                  :only-filter-with-text="true"
                >
                  <Option
                    v-for="item in statisTypeList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <DatePicker
                  format="yyyy-MM-dd "
                  type="daterange"
                  :options="timeOptions"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="true"
                  style="width: 100%"
                  :confirm="false"
                >
                </DatePicker>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("alarm_fault_type") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.faultType"
                  filterable
                  clearable
                  :only-filter-with-text="true"
                >
                  <Option
                    v-for="item in faultTypeList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
              <div class="fn_item_box">
                <Input
                  v-model="query.keyWord"
                  :title="$t('specquality_1')"
                  :placeholder="$t('specquality_1')"
                />
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <Button
              class="query-btn"
              :loading="queryLoading"
              type="primary"
              v-if="permissionObj.list"
              icon="ios-search"
              @click="queryClick"
              :title="$t('common_query')"
            ></Button>
          </div>
          <div class="btn-placehoder" v-if="!permissionObj.list"></div>
        </div>
      </div>

      <div class="section-body contentBox_bg">
        <div class="section-body-content">
          <div>
            <Row class="pie-div">
              <i-col span="7" style="padding: 0 5px 0 0">
                <div>
                  <div
                    class="pie-box"
                    v-for="(item, index) in pie_List"
                    :key="index"
                  >
                    <div class="pie-content">
                      <div class="echarts-pie">
                        <echarts-pie
                          :node="item.node"
                          :pieData="item"
                          :isdarkSkin="isdarkSkin"
                        ></echarts-pie>
                      </div>
                      <div class="pie-example">
                        <Tooltip
                          :content="items.content"
                          v-for="(items, indexes) in item.data"
                          :key="indexes"
                          max-width="200"
                        >
                          <div
                            class="pie-example-box"
                            :class="[
                              items.name === $t('common_Normal') ||
                              items.name === $t('common_line')
                                ? ''
                                : 'pie-example-hover',
                            ]"
                          >
                            <i
                              class="icon-box"
                              :class="[
                                indexes === 0
                                  ? currentSkin == 1
                                    ? 'icon-normal'
                                    : 'light-icon-normal'
                                  : indexes === 1
                                  ? 'icon-degradation'
                                  : 'icon-end',
                              ]"
                            ></i>
                            <div class="example-right">
                              <div class="example-header">
                                <div class="example-title">
                                  {{ items.name }}
                                </div>
                                <span class="example-value">{{
                                  items.value
                                }}</span>
                              </div>
                              <div class="example-beliel">
                                {{ items.Percent }}%
                              </div>
                            </div>
                          </div>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </i-col>
              <i-col span="17" style="padding: 0 0 0 -3px">
                <div style="padding-bottom: 5px">
                  <Row :gutter="5">
                    <i-col span="12" style="position: relative">
                      <Loading :loading="loading1"></Loading>
                      <p style="text-align: left; line-height: 32px">
                        <b>{{ $t("comm_interruption_top5") }}</b>
                      </p>
                      <Table
                        class="topTable"
                        ref="tableList"
                        stripe
                        :columns="columns2"
                        :data="tableData2"
                        height="290"
                        :no-data-text="
                          loading2
                            ? ''
                            : tabList2.length > 0
                            ? ''
                            : currentSkin == 1
                            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                        "
                        size="small"
                      >
                        <template slot-scope="{ row }" slot="sourceIp">
                          {{
                            row.sourceIp === undefined ||
                            row.sourceIp === null ||
                            row.contacts === "" ||
                            row.contacts === "null"
                              ? "--"
                              : row.contacts
                          }}
                        </template>
                      </Table>
                    </i-col>
                    <i-col span="12" style="position: relative">
                      <Loading :loading="loading1"></Loading>
                      <p style="text-align: left; line-height: 32px">
                        <b>{{ $t("comm_degradation_top5") }}</b>
                      </p>
                      <Table
                        class="topTable"
                        ref="tableList"
                        stripe
                        :columns="columns3"
                        :data="tableData3"
                        height="290"
                        :loading="loading3"
                        :no-data-text="
                          loading3
                            ? ''
                            : tabList3.length > 0
                            ? ''
                            : currentSkin == 1
                            ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                            : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                              $t('common_No_data') +
                              '</p></div>'
                        "
                        size="small"
                      ></Table>
                    </i-col>
                  </Row>
                </div>
              </i-col>
            </Row>
            <Row style="margin-top: 10px">
              <i-col span="24" class="tableBorder" style="position: relative">
                <Loading :loading="loading1"></Loading>
                <div
                  style="height: 32px; width: 100%; margin: 10px 0"
                  class="table-item"
                >
                  <b class="fleft" style="line-height: 32px">{{
                    $t("specquality_task_statistics")
                  }}</b>
                  <!--<Button type="info" class="fright" icon="ios-download-outline" @click="exportClick">{{$t('but_export')}}</Button>-->
                  <!-- <Button 
                  class="skinPrimary export-btn" 
                  v-if="permissionObj.export"
                  type="primary"  
                  icon="md-open" 
                  @click="exportClick" :title="this.$t('but_export')">
                </Button> -->
                  <Button
                    class="daoChu-btn"
                    v-if="permissionObj.export"
                    type="primary"
                    id="exportData"
                    @click="exportClick"
                    :title="this.$t('but_export')"
                  >
                    <i class="iconfont icon-icon-derive" />
                  </Button>
                </div>
                <Table
                  ref="tableList"
                  class="my-table-fixed fixed-right"
                  stripe
                  :columns="taskStatisColumns"
                  :data="tableData1"
                  @on-sort-change="sortChange"
                  :no-data-text="
                    loading1
                      ? ''
                      : tabList1.length > 0
                      ? ''
                      : currentSkin == 1
                      ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                        $t('common_No_data') +
                        '</p></div>'
                      : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                        $t('common_No_data') +
                        '</p></div>'
                  "
                  size="small"
                >
                  <template slot-scope="{ row }" slot="sourceIp">
                    {{
                      row.sourceIp === undefined ||
                      row.sourceIp === null ||
                      row.sourceIp === "" ||
                      row.sourceIp === "null"
                        ? "--"
                        : row.sourceIp
                    }}
                  </template>
                </Table>
                <div class="tab-page" v-if="tabList1.length > 0">
                  <Page
                    v-page
                    v-if="tabList1.length > 0"
                    :current.sync="pageNo"
                    :page-size="pageSize"
                    :total="totalCount"
                    :page-size-opts="pageSizeOpts"
                    :prev-text="$t('common_previous')"
                    :next-text="$t('common_next_page')"
                    @on-change="pageChange"
                    @on-page-size-change="pageSizeChange"
                    show-elevator
                    show-sizer
                  >
                  </Page>
                </div>
              </i-col>
            </Row>
          </div>
        </div>
      </div>

      <!--查看-->

      <Modal
        v-model="look.show"
        :width="modalWidth"
        :styles="{ top: '100px' }"
        class="task-modal no-padding-modal"
        :mask="true"
        sticky
        draggable
        @on-visible-change="resetData"
        footer-hide
      >
        <div slot="header">
          <span class="title-name">{{ this.look.title }}</span>
          <span class="title-content">
            {{
              "(" +
              this.$t("qualityreport_source_IP") +
              ":" +
              this.look.titleSourceIP +
              "," +
              this.$t("flowChart_aim") +
              ":" +
              this.look.titleDestIP +
              ")"
            }}
          </span>
        </div>

        <section class="taskStrategy-modal" v-if="modelShow">
          <Row type="flex" justify="end" class="btn-row-bg">
            <Button
              class="daoChu-btn"
              id="exportData"
              type="primary"
              @click="exportViewClick()"
              :title="this.$t('but_export')"
            >
              <i class="iconfont icon-icon-derive" />
            </Button>
          </Row>
          <Table
            ref="tableListModal"
            :height="computedHeight()"
            stripe
            :columns="columnsModal"
            :data="tabListModal"
            :loading="ModalTableLoading"
            @on-sort-change="sortModal"
            class="modalTable modalTableover"
            :no-data-text="
              ModalTableLoading
                ? ''
                : tabListModal.length > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
          >
          </Table>
          <div class="tab-page">
            <Page
              v-page
              class="infoListPage"
              :current="tabParam.pageNo"
              :page-size="tabParam.pageSize"
              :total="tabParam.totalCount"
              @on-page-size-change="tabParamPageSizeChange"
              :page-size-opts="[3, 10, 50, 100, 200, 500, 1000]"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChangeModal"
              show-total
              show-sizer
            >
            </Page>
          </div>
          <div v-if="tabListModal.length > 0">
            <topology-item
              :data="topologyList"
              :linkData="tabListModal"
              :degradationType="ipTitleParam.degradationType"
              :iconType="false"
              lastType
              :data_source="ipTitleParam.data_source"
              @on-click="topologyClick"
            ></topology-item>
            <div style="height: 30px"></div>
            <div class="wisdom-snmp-content" style="position: relative">
              <Loading :loading="echarLoading"></Loading>
              <div class="btnChange">
                <a
                  href="javascript:void(0);"
                  :class="[btnValue == 1 ? 'active' : '']"
                  @click="btnClick(1)"
                  >{{ $t("specquality_basic_index") }}</a
                >
                <a
                  href="javascript:void(0);"
                  :class="[btnValue == 2 ? 'active' : '']"
                  @click="btnClick(2)"
                  >{{ $t("specquality_operating_index") }}</a
                >
              </div>
              <!--趋势图-->
              <div class="lookBox">
                <div
                  class="title"
                  style="margin-left: 0px"
                  v-show="btnValue == 1"
                >
                  {{ echartLookParama2.preNodeIp
                  }}<span class="alias" v-if="preAlias.name"
                    >({{
                      preAlias.port
                        ? $t("comm_Device_name") +
                          ":" +
                          preAlias.name +
                          $t("comm_port_number") +
                          ":" +
                          preAlias.port
                        : $t("comm_Device_name") + ":" + preAlias.name
                    }})</span
                  >——>{{ echartLookParama2.nodeIp
                  }}<span class="alias" v-if="zAlias.name"
                    >({{
                      zAlias.port
                        ? $t("comm_Device_name") +
                          ":" +
                          zAlias.name +
                          $t("comm_port_number") +
                          ":" +
                          zAlias.port
                        : $t("comm_Device_name") + ":" + zAlias.name
                    }})</span
                  >
                </div>
                <div
                  class="title"
                  style="margin-left: 0px"
                  v-show="btnValue == 2"
                >
                  {{ usea.preNodeIp
                  }}<span class="alias" v-if="usea.name"
                    >({{
                      usea.port
                        ? this.$t("comm_Device_name") +
                          ":" +
                          usea.name +
                          "," +
                          this.$t("comm_port_number") +
                          ":" +
                          usea.port
                        : this.$t("comm_Device_name") + ":" + usea.name
                    }})</span
                  >——>{{ usez.nodeIp
                  }}<span class="alias" v-if="usez.name"
                    >({{
                      usez.port
                        ? this.$t("comm_Device_name") +
                          ":" +
                          usez.name +
                          "," +
                          this.$t("comm_port_number") +
                          ":" +
                          usez.port
                        : this.$t("comm_Device_name") + ":" + usez.name
                    }})</span
                  >
                </div>
                <div class="contain" v-if="!topoShow">
                  <div
                    ref="pathquality-delayLoss"
                    v-show="btnValue == 1"
                    id="pathquality-delayLoss"
                    class="echartStyle"
                    :style="'height:' + height + 'px;width:100%'"
                  ></div>
                  <div
                    ref="pathquality-delayLoss2"
                    v-show="btnValue == 2"
                    id="pathquality-delayLoss2"
                    class="echartStyle"
                    :style="'height:' + height2 + 'px;width:100%'"
                  ></div>
                </div>
                <div
                  :class="{
                    table_empty: currentSkin == 1,
                    table2_empty: currentSkin == 0,
                  }"
                  v-else
                >
                  <p class="emptyText">{{ $t("common_No_data") }}</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Modal>
      <!--自定义列表项 修改之后-->

      <Modal
        sticky
        v-model="customModalShow"
        width="540"
        height="720"
        class="threshold-modal modal-selected"
        :title="$t('custom_list_items')"
        draggable
        :mask="true"
        :loading="customModalShowLoading"
      >
        <Row class="modal-selected-row">
          <Col>
            <Table
              ref="fieldsModalTableData"
              height="550"
              width="400"
              :show-header="false"
              :columns="fieldsColumns"
              :data="allocationListFields"
              @on-row-click="getFieldsColumnsIndex"
              :row-class-name="rowClassName"
            >
            </Table>
          </Col>
          <Col>
            <div class="btn-box">
              <Tooltip :content="$t('dash_up')" placement="right">
                <div class="sort-btn" @click="moveUp">
                  <Icon
                    type="md-arrow-up"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
              <Tooltip :content="$t('dash_down')" placement="right">
                <div class="sort-btn" @click="moveDown">
                  <Icon
                    type="md-arrow-down"
                    size="24"
                    color="var(--field_sort_btn_font_color , #1FA2FF)"
                  />
                </div>
              </Tooltip>
            </div>
          </Col>
        </Row>
        <div slot="footer">
          <Button @click="customModalCancel">{{ $t("common_cancel") }}</Button>
          <Button type="primary" @click="customModalOk">{{
            $t("but_confirm")
          }}</Button>
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
import locationreload from "@/common/locationReload";

const pointGreen =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAVJJREFUeNpiZJjpwkAmCAdibiCeR45mFgbyQS4QCwHxCiD+RqpmJjItTQBiayDWhDqAgR4WCwBxNRK/FIgV6WFxARCrIPGFgbiK1harQy1GB8lAbE9Li2uAmB+LOCMQNwExMy0sdgbiaDzydkCcSG2LQeoaoD7DByqAWJSaFoN8YkOEOmUgLqGWxUJo2YcQyAZiQ2pYXERiPuWGRgtFFmsAcT4Zed0PiP0psbgWiHnILFZB2YuTHItB1VYUBZWIHhDnkWoxCzHxRAQoA2IFUixOgtY+lAKcOQKbxSLQgoBaIAlaqhG0uIScao5AAm5Gb3SgW6wNxDkM1Ad20MYDTotroQUALUAlcjmObLE7tAFHK6AEba2gWMxGpexDCOTAynGYxXFAbEEHi0ElWT3MYlBVVs5APwAqw8NBSdwNiG8D8XUKDfwHxB+hND7AAcRyAAEGAGNbI9MYOlwUAAAAAElFTkSuQmCC";
const pointRed =
  "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAaCAYAAACgoey0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAW1JREFUeNpi/CvHQC4IB2JuIJ5HjmZGCiw+AsRCQGwCxN9I1cxEpqUJQGwNxJpAnEsvHwsA8WkgVoHy3wKxKRDfp7WPC5AsBQFhIK6itY/VgfgkEPOjif8HYkcgPkgrH9dgsRTsASBuAmJmWljsDMTReOTtgDiR2haD1DVAfYYPVACxKDUtBvnEhgh1ykBcQq3EBSokzgCxIpGO/ArEtkB8nlIfF5FgKQO0GG2g1Mca0MKCh4z8HgDEG8n1cS2ZljJAsxcnORa7AHEUA/lAD4jzSA1qFiA+AK0IKAHvgNgYiB8Q6+MkKlgKyxHVxPpYBIhPkZiS8YF/0HL8ECEfl1DRUpgdzdDow2mxNhDnMFAf2EEbDzgtroUWALQAlcjlOLLF7tAGHK2AEhCXoicuNmglbsFAW/AdmlvOw3wcRwdLGaAlWT3Mx6CqbAdaO4rWIAKUxN2A+DYQXye1vQaNop9I+fUjlMYHOIBYDiDAACc8OtNv4mitAAAAAElFTkSuQmCC";
import echartsPie from "@/common/echarts/base-pieHealth.vue";
import echartFn from "@/common/mixins/echartFun";
import global from "@/common/global.js";
import topologyItem from "@/common/flowChart/topologyX.vue";
import TreeSelect from "@/common/treeSelect/treeSelect.vue";
import "../../../timechange";
import { mapGetters, mapActions } from "vuex";
import axios from "axios";
import Qs from "qs";
import "@/config/page.js";
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/dataZoom");
require("echarts/lib/component/markLine");
import eConfig from "@/config/echart.config.js";
import { addDraggable } from "@/common/drag.js";
import moment from "moment";
import {tableEditBtn} from '@/assets/base64Img/img'
import {tableEditLightBtn} from '@/assets/base64Img/img';
import ipv6Format from "@/common/ipv6Format";
  import langFn  from '@/common/mixins/langFn';


function timeToString(times) {
  const regPos = /^[0-9]+.?[0-9]*/; //判断是否是数字。
  var comm_day1 = "天";
  var comm_hour = "小时";
  var comm_minutes = "分";
  var comm_second = "秒";
  if (localStorage.getItem('locale') === 'en') {
    comm_day1 = "d";
    comm_hour = "h";
    comm_minutes = "m";
    comm_second = "s";
  }
  if (regPos.test(times)) {
    var time = times,
      timeStr = "";
    var day = parseInt(time / 60 / 60 / 24);
    var hours = parseInt((time / 60 / 60) % 24);
    var minuts = parseInt((time / 60) % 60);
    var second = parseInt(time % 60);
    if (day > 0) {
      timeStr = day + comm_day1;
    }
    if (hours > 0) {
      timeStr += hours + comm_hour;
    }
    if (minuts > 0) {
      timeStr += minuts + comm_minutes;
    }
    if (second >= 0) {
      timeStr += second + comm_second;
    }
    return timeStr;
  } else {
    return 0 + comm_second;
  }
}
function getQueryVariable(variable) {
  // var query = window.location.search.substring(1);
  var url = top.document.getElementById("sub-content-page").src;
  var query = url.indexOf("?") > 0 ? url.split("?")[1] : null;
  if (query) {
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      console.log(pair[1].replace("%20", " "));
      if (pair[0] == variable) {
        return pair[1].replace("%20", " ");
      }
    }
  } else {
    // 处理质量报告页面的参数
    var element = top.document.getElementById("pathquality");
    if (element) {
      url = element.src;
      query = url.indexOf("?") > 0 ? url.split("?")[1] : null;
      if (query) {
        var params = query.split("param=");
        if (params && params.length > 0) {
          params = params[1];
        }
        if (params) {
          query = unescape(params);
          query = JSON.parse(query);
          if (query) {
            return query[variable];
          }
        }


      }
    }
  }
  return false;
}
export default {
  name: "healthManagement",
  mixins: [echartFn,langFn],
  components: {
    echartsPie,
    topologyItem,
    TreeSelect
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      // 是否 报表跳转过来的页面
      reportState:0,
      modalWidth:0,
      modelShow:false,
      treeValue: '',
      //是否是不可信节点
      suspect: false,
      //权限对象
      permissionObj: {},
      //机构参数
      treeData: [],
      orgTree: false,
      orgLists: [],
      readonly: true,
      groupList: [],
      tabParam: {
        pageNo: 1,
        pageSize: 3,
        totalCount: 0
      },
      pie_List: [
        {
          node: "linkPie",
          title: this.$t('comm_task'),
          totalValue: 0,
          data: [
            { value: 0, name: this.$t('dash_normal'), Percent: 0, content: this.$t('probetask_interrupt_deterioration_tip') },
            { value: 0, name: this.$t('dash_deterioration'), Percent: 0, content: this.$t('probetask_deterioration_tip') },
            { value: 0, name: this.$t('dash_interrupt'), Percent: 0, content: this.$t('probetask_interrupt_tip') },
          ],
        },
      ],
      // 1：中断 2：时延劣化 3：丢包劣化
      faultTypeList:[{
           value: 1,
          label: this.$t('comm_interruption')
      },{
           value: 2,
          label: this.$t('common_degradation'),

      },{
          value: 3,
          label: this.$t('comm_loss_package'),

      }],
      statisTypeList: [
        {
          value: 1,
          label: this.$t('comm_angle_event'),
        },
        {
          value: 2,
          label: this.$t('comm_angle_failure'),
        },
      ],
      query: {
        groupId: "",
        keyWord: "",
        orgId: "",
        faultType:"",
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: new Date(new Date().getTime() + 86400000).format("yyyy-MM-dd 00:00:00"),
        type: 2,
        pageNo: 1,
        pageSize: 10,
      },
      //排序参数
      useableRateOrder: null,
      goodRateOrder: null,
      latestTimeOrder: null,
      brokenDurationOrder: null,
      degradationDurationOrder: null,
      status: null,

      //排序界面参数
      //排序字段
      orderKey: [
        "useableRate",
        "goodRate",
        "latestEventTime",
        "brokenDuration",
        "degradationDuration",
      ],
      // 排序类别（asc，desc）
      orderType: ["desc", "desc", "desc", "desc", "desc"],
      //排序的字段顺序（从0开始）（设置样式时使用）
      sortIndex: [0, 1, 2, 3, 4],
      // 排序类别（asc，desc，normal）（设置样式时使用）
      sortType: ["normal", "normal", "normal", "normal", "normal"],

      client_list: [],
      operatorList: [
        {
          value: 0,
          label: this.$t('comm_all'),
        },
        {
          value: 1,
          label: this.$t('server_China_Mobile'),
        },
        {
          value: 2,
          label: this.$t('server_China_Unicom'),
        },
        {
          value: 3,
          label: this.$t('server_China_Telecom'),
        },
        {
          value: 4,
          label: this.$t('server_China_Broadcasting'),
        },
        {
          value: 6,
          label: this.$t('comm_other'),
        },
        {
          value: 7,
          label: "--",
        },
      ],
      currentTime: Date.now(),
      startTime: new Date().format2("yyyy-MM-dd 00:00:00"),
      endTime: new Date().format2("yyyy-MM-dd 00:00:00"),
      interValCurTime: null,
      timePickerOptions: { steps: [1, 1, 1] },
      savetimePickerOptions: { steps: [1, 1, 1] },
      timeRange: [new Date().format2("yyyy-MM-dd 00:00:00"), new Date().format2("yyyy-MM-dd 00:00:00")],
      timeOptions: {
        shortcuts: [
          // {
          //     text: this.$t('comm_half_hour'),
          //     value () {
          //         const start = new Date();
          //         const end = new Date();
          //         start.setTime(start.getTime() - 30 * 60 * 1000);
          //         return [ start.format("yyyy-MM-dd HH:mm:ss"),  end.format("yyyy-MM-dd HH:mm:ss")];
          //     }
          // },
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              return [new Date().format("yyyy-MM-dd 00:00:00"), new Date().format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              const end = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
              let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
              let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
              return [start.format("yyyy-MM-dd 00:00:00"), end.format("yyyy-MM-dd 23:59:59")];
            }
          }
        ]
      },
      queryModal: {
        keyWord: "",
        startTime: "",
        endTime: "",
        linkId: "",
        pageNo: 1,
        pageSize: 1000000,
      },
      edit: {
        dev_ip: "",
      },
      states: [
        { value: 0, label: this.$t('testspeed_unactivated') },
        { value: 1, label: this.$t('testspeed_activated') },
      ],
      dateValue: [
        new Date().format("yyyy-MM-dd"),
        new Date().format("yyyy-MM-dd"),
      ],
      dateValueCopy: [],
      pageNo: 1,
      pageSize: 10,
      pageSizeOpts: [10, 50, 100, 200, 500, 1000],
      modalPage_no: 1,
      modalPageSize: 3,
      totalCount: 0,
      totalCountModal: 0,
      taskId: '',
      noDataLoading: false,
      echarLoading: false,
      loading: false,
      loading1: false,
      loading2: false,
      loading3: false,
      queryLoading: false,
      ModalTableLoading: false,
      ModalTopoLoading: false,
      ModalTrendLoading: false,
      currentid: "",
      tabList: [],
      tabList1: [],
      originalList: [], //保存原始列表数据
      orderTabList: [],
      originalModalList: [], //保存原始列表数据
      orderTabModalList: [],
      tabList2: [],
      tabList3: [],
      tabListModal: [],
      eventTypes: 0,
      columnsModal: [
        {
          title: this.$t('but_choose'),
          align: 'left',
          className: "bgColor",
          width: 80,
          // slot: 'action'
          render: (h, params) => {
            let id = params.row.rowId;
            let flag = false;
            if (this.currentid === id) {
              flag = true;
            } else {
              flag = false;
            }
            let self = this;
            return h("div", [
              h("Radio", {
                style: {
                  marginRight: 0,
                },
                props: {
                  value: flag,
                },
                on: {
                  "on-change": () => {
                    this.usea.preNodeIp = params.row.sourceIp;
                    this.usez.nodeIp = params.row.destIp;
                    //判断是否是故障段
                    this.isIpduan = params.row.preFurthestIp ? true : false;
                    //设置选中
                    self.currentid = id;
                    this.id = params.row.rowId;

                    //topo图参数设置，默认第一条
                    this.topologyParam = {
                      sourceIp: params.row.sourceIp,
                      destIp: params.row.destIp,
                      linkId: params.row.linkId,
                      logTracertId: params.row.logTracertId,
                      taskType: params.row.taskType,
                      startTime: params.row.startTs,
                      createTs: params.row.createTs,
                    };
                    if (Number(params.row.status) === 1) {
                      this.topologyParam.endTime = new Date().format2(
                        "yyyy-MM-dd hh:mm:ss"
                      );
                    } else {
                      this.topologyParam.endTime = new Date(
                        Number(params.row.lastActiveTs) * 1000
                      ).format2("yyyy-MM-dd hh:mm:ss");
                    }
                    //趋势图参数设置

                    this.echartLookParama2.devIp = params.row.devIp == '0.0.0.0' ? null : params.row.devIp;
                    this.echartLookParama2.preNodeIp = params.row.sourceIp;
                    this.echartLookParama2.nodeIp = params.row.destIp;
                    this.echartLookParama2.linkId = params.row.linkId;
                    this.echartLookParama2.level = 2;
                    this.echartLookParama2.isSpecial =
                      params.row.isSpecial || false;
                    this.echartLookParama2.queryType = 1; //1是链路，2是节点
                    this.echartLookParama2.High =
                      params.row.taskType == 3 ? true : false;


                    if (params.row.startTs) {
                      const timeInterval = params.row.durationOfTime * 1000;
                      const handradSevenTyMinutes = 170 * 60 * 1000, handradSixtyTwoHours = 162 * 60 * 60 * 1000;
                      if (this.echartLookParama2.High) {
                        if (timeInterval <= handradSevenTyMinutes) {
                          this.echartLookParama2.startTime = moment(params.row.startTs)
                            .subtract(5, "minutes")
                            .format("YYYY-MM-DD HH:mm:ss");
                          this.echartLookParama2.endTime = moment(
                            new Date(Number(params.row.lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                            "YYYY-MM-DD HH:mm:ss"
                          )
                            .add(5, "minutes")
                            .format("YYYY-MM-DD HH:mm:ss");
                        } else if (timeInterval > handradSevenTyMinutes && timeInterval <= handradSixtyTwoHours) {
                          this.echartLookParama2.startTime = moment(params.row.startTs)
                            .subtract(3, "hours")
                            .format("YYYY-MM-DD HH:mm:ss");
                          this.echartLookParama2.endTime = moment(
                            new Date(Number(params.row.lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                            "YYYY-MM-DD HH:mm:ss"
                          )
                            .add(3, "hours")
                            .format("YYYY-MM-DD HH:mm:ss");
                        } else {
                          this.echartLookParama2.startTime = moment(params.row.startTs)
                            .subtract(7, "days")
                            .format("YYYY-MM-DD HH:mm:ss");
                          this.echartLookParama2.endTime = moment(
                            new Date(Number(params.row.lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                            "YYYY-MM-DD HH:mm:ss"
                          )
                            .add(7, "days")
                            .format("YYYY-MM-DD HH:mm:ss");
                        }
                      } else {
                        if (timeInterval > handradSixtyTwoHours) {
                          this.echartLookParama2.startTime = moment(params.row.startTs)
                            .subtract(7, "days")
                            .format("YYYY-MM-DD HH:mm:ss");
                          this.echartLookParama2.endTime = moment(
                            new Date(Number(params.row.lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                            "YYYY-MM-DD HH:mm:ss"
                          )
                            .add(7, "days")
                            .format("YYYY-MM-DD HH:mm:ss");
                        } else {
                          this.echartLookParama2.startTime = moment(params.row.startTs)
                            .subtract(3, "hours")
                            .format("YYYY-MM-DD HH:mm:ss");
                          this.echartLookParama2.endTime = moment(
                            new Date(Number(params.row.lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                            "YYYY-MM-DD HH:mm:ss"
                          )
                            .add(3, "hours")
                            .format("YYYY-MM-DD HH:mm:ss");
                        }
                      }
                      if (params.row.status == 1) {
                        this.echartLookParama2.endTime = new Date().format(
                          "yyyy-MM-dd HH:mm:ss"
                        );
                      }
                    }
                    this.saveStartTime = this.echartLookParama2.startTime;
                    this.saveEndTime = this.echartLookParama2.endTime;

                    console.log(this.topologyParam);
                    //趋势图故障点定位
                    this.markPoint = params.row.startTs;
                    this.markPointGreen =
                      params.row.status == 1
                        ? ""
                        : new Date(
                          Number(params.row.lastActiveTs) * 1000
                        ).format2("yyyy-MM-dd HH:mm:ss");
                    //获取topo图数据
                    this.getTopology(this.topologyParam, params.row);
                  },
                },
              }),
            ]);
          },
        },
        {
          renderHeader: (h) => {
            return h("span", this.typeName + this.$t('specquality_start_time'));
          },
          // title: "事件发生时间",
          key: "startTs",
          align: 'left',
          sortable: "custom",
          width: 200,
        },
        {
          renderHeader: (h) => {
            return h("span", this.typeName + this.$t('specquality_recovery_time'));
          },
          key: "lastActiveTs",
          align: 'left',
          sortable: "custom",
          width: 200,
          render: (h, params) => {
            let str = new Date(Number(params.row.lastActiveTs) * 1000).format2(
              "yyyy-MM-dd hh:mm:ss"
            );
            return h("span", params.row.status == 1 ? "--" : str);
          },
        },
        {
          renderHeader: (h) => {
            return h("span", this.typeName + this.$t('specquality_duration'));
          },
          //title: "事件历时",
          key: "durationOfTime",
          align: 'left',
          sortable: "custom",
          width: 180,
          render: (h, param) => {
            return h("span", timeToString(param.row.durationOfTime));
          },
        },
        {
          title: this.$t('comm_type'),
          width: 200,
          renderHeader: (createElement, { col }) => {
            return createElement(
              "Select",
              {
                props: {
                  value: this.eventTypes,
                  // transfer: true,
                },
                on: {
                  "on-change": (value) => {
                    if (value == 0) {
                      this.look.listParam.eventType = "";
                    } else {
                      this.look.listParam.eventType = value;
                    }
                    this.eventTypes = value;
                    this.look.listParam.pageNo = this.modalPage_no = 1;
                    this.getModalList(this.look.listParam);
                  },
                },
                style: {
                  width: "100%",
                },
                class: "headerSelectArea",
              },
              [
                createElement(
                  "Option",
                  {
                    props: {
                      value: 0,
                    },
                    class: "modalSelect",
                  },
                  this.$t('comm_type')
                ),
                createElement(
                  "Option",
                  {
                    props: {
                      value: 1,
                    },
                  },
                  this.$t('dash_interrupt')
                ),
                createElement(
                  "Option",
                  {
                    props: {
                      value: 2,
                    },
                  },
                  this.$t('common_degradation')
                ),
                createElement(
                  "Option",
                  {
                    props: {
                      value: 3,
                    },
                  },
                  this.$t('common_loss_degradation')
                ),
              ]
            );
          },
          key: "status",
          align: 'left',
          render: (h, params) => {
            return h(
              "span",
              params.row.eventType == 1
                ? this.$t('dash_interrupt')
                : params.row.eventType == 2
                  ? this.$t('common_degradation')
                  : params.row.eventType == 3
                    ? this.$t('common_loss_degradation')
                    : "--"
            );
          },
        },
        {
          title: this.$t('alarm_susp_failed_link'),
          key: "errorIp",
          align: 'left',
          render: (h, param) => {
            let str = param.row.errorIp;
            let preFurthestIp = param.row.preFurthestIp;
            if (str === undefined || str === null || str === "") {
              str = "--";
            } else {
              // str = preFurthestIp ? `${preFurthestIp}-${str}` : `${str}`;
              if(preFurthestIp){
                let maxWidth = param.column.width; // 获取动态传递的宽度
                var t = ipv6Format.formatIPv6Address(preFurthestIp,maxWidth);
                t  += "-";
                t += ipv6Format.formatIPv6Address(str,maxWidth);
                str = t;
              }else{
                 let maxWidth = param.column.width; // 获取动态传递的宽度
                 str = ipv6Format.formatIPv6Address(`${str}`,maxWidth);
              }
             
            }
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          },
        },
        // {
        //   title: "产生原因",
        //   key: "eventReason",
        //   align: 'left',
        //   className: "bgColor",
        //   render: (h, params) => {
        //     let str = params.row.eventCauseType,
        //       text = "";
        //     if (str === undefined || str === null || str === "") {
        //       text = "--";
        //     } else if (str == 0) {
        //       text = "未知";
        //     } else if (params.row.eventType == 1 && str == 1) {
        //       text = "停电";
        //     } else if (params.row.eventType == 1 && str == 2) {
        //       text = "割接";
        //     } else if (
        //       (params.row.eventType == 2 || params.row.eventType == 3) &&
        //       str == 1
        //     ) {
        //       text = "拥塞";
        //     } else {
        //       text = "--";
        //     }
        //     return h("span", text);
        //   },
        // },
        {
          title: this.$t('specquality_recovery_cause'),
          key: "recoveryType",
          align: "left",
           width:250,
          render: (h, params) => {
             // 0故障升级、1故障降级、2路径切换、3故障恢复、4其他,5故障链路恢复
            let str = params.row.recoveryType,
              text = "";
            if (str === undefined || str === null || str === "") {
              text = "--";
            } else if (str == 0) {
              text = this.$t('comm_failure_up');
            } else if (str == 1) {
              text = this.$t('comm_failure_dwon');
            } else if (str == 2) {
              text = this.$t('comm_path_switching');
            } else if (str == 3) {
              text = this.$t('comm_failure_re');
            }else if (str == 5) {
              text = this.$t('comm_failure_link_recovery');
            } else {
              text = this.$t('comm_other');
            }
            return h("span", text);
          },
        },
        {
          title: this.$t('specquality_current_state'),
          key: "status",
          width: 200,
          align: 'left',
          className: "bgColor",
          render: (h, params) => {
            if (params.row.status == 0) {
              return h("span", { style: { color: "#23D692" } }, this.$t('common_recovered'));
            } else if (params.row.status == 1) {
              return h("span", { style: { color: "#fb204d" } }, this.$t('common_unrecovered'));
            }
          },
        },
        {
          title: this.$t('task_dial_type'),
          key: "taskType",
          width: 200,
          align: 'left',
          className: "bgColor",
          render: (h, params) => {
            return h("span", global.convertTaskType(params.row.taskType));
          },
        },
      ],
      usea: {
        preNodeIp: '',
        name: '',
        port: '',
      },
      usez: {
        nodeIp: '',
        name: '',
        port: '',
      },
      //故障段
      isIpduan: false,
      indeterminate: true,
      checkAll: -1,
      ModalcheckAll: 0,
      typeName: this.$t('comm_angle_failure'),
      customKey:"api:taskanalysis/getTaskAnalysisList+sys_func_id:8",
      customMoveIndex:0,
      customModalShow:false,
      customModalShowLoading:false,
      taskStatisColumns:[],
      fieldsJsonObjArr:[],
      allocationListFields:[],
      customFieldsColumnsKeyWidth:[],
      screenWidth:0,
      fieldsColumns: [
      {
        key: "showField",
        width: 35,
        render: (h, params) => {
          let row = params.row;
          return h("Checkbox", {
            props: {
              value: row.showField,
              disabled: row.fixedField // 当fixedField为true时禁用Checkbox
            },
            on: {
              input: (value) => {
                if (!row.fixedField) {
                  // 更新 showField 的值
                  this.allocationListFields[params.index].showField = value;
                }
              }
            }
          });
        }
      },
        {
          key: "parameterTitle",
          align: "left",
          // width: 160,
          render:(h,params) => {
            let fontColor = 'var(--field_font_color ,#fff)'
            if(params.row.fixedField) {
              fontColor = '#5CA0D5'
            }
            return h('div',{
              style: {
                color:fontColor,
                fontWeight:400
              }
            },params.row.parameterTitle)
          }
        }
      ],
      fixedColumns: [
        // 修改图标


        {
          title: this.$t('qualityreport_task_number'),
          key: "taskNum",
          minWidth: 140,
          align: 'left',
          tooltip: true,
          // fixed: "left",
        },
        {
          title: this.$t('comm_source_ip'),
          key: "sourceIp",
          minWidth: 200,
          align: 'left',
          tooltip: true,
          render: (h, params) => {
              let str = params.row.sourceIp;
              let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('comm_target_ip'),
          key: "destIp",
          minWidth: 200,
          align: 'left',
          tooltip: true,
          render: (h, params) => {
              let str = params.row.destIp;
              let maxWidth = params.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
          // fixed: "left",
        },
        {
          title: this.$t('comm_target_name'),
          key: "destName",
          minWidth: 200,
          align: 'left',
          tooltip: true,
          // fixed: "left",
        },
        {
          title: this.$t('comm_org'),
          key: "orgName",
          minWidth: 120,
          align: 'left',
          tooltip: true,
          // fixed: "left",
        },
        {
          title: this.$t('comm_grouping'),
          key: "groupNames",
          minWidth: 150,
          align: 'left',
          tooltip: true,
          // fixed: "left",
        },
        // 可用率
        {
          title: this.$t('dashboard_availability'),
          key: "useableRate",
          width: this.getColumnWidth(80,140),
          align: 'left',
          sortable: 'custom',
          sortNumber: 0,
          render: (h, params) => {
            let str = params.row.useableRate;
            return h(
              "span",
              str === undefined || str === null || str === ""
                ? "--"
                // : str.toFixed(2) + "%"
                : str + "%"
            );
          },
        },
        // 优良率
        {
          title: this.$t('dashboard_excellent_rate'),
          key: "goodRate",
          width:this.getColumnWidth(80,140),
          align: 'left',
          sortable: 'custom',
          sortNumber: 1,
          render: (h, params) => {
            let str = params.row.goodRate;
            return h(
              "span",
              str === undefined || str === null || str === ""
                ? "--"
                // : str.toFixed(2) + "%"
                : str + "%"
            );
          },
        },
        // 中断累计次数
        {
          title: this.$t('comm_interrup_times'),
          key: "brokenCount",
          width: this.getColumnWidth(120,180),
          align: 'left',
          sortable: 'custom',
          // fixed: "left",
        },
        // 中断平均时长
        {
          title: this.$t('comm_break_avg_long_time'),
          key: "avgBrokenTime",
          width:200,
          align: 'left',
          sortable: 'custom',
          render: (h, params) => {
            return h("span", timeToString(params.row.avgBrokenTime));
          },
        },
        // 中断累计时长
        {
          title: this.$t('comm_duration'),
          key: "brokenDuration",
          width:200,
          align: 'left',
          sortable: 'custom',
          sortNumber: 3,
          render: (h, params) => {
            return h("span", timeToString(params.row.brokenDuration));
          },
        },

        // 丢包劣化累计次数
        {
          title: this.$t('comm_packetLossDegradation_times'),
          key: "packetLossDegradationCount",
          width: this.getColumnWidth(160,300),
          align: 'left',
          sortable: 'custom',
          // fixed: "left",
        },
        // 丢包劣化平均时长
        {
          title: this.$t('comm_packetLossDegradation_avg'),
          key: "avgPacketLossDegradationTime",
          width:this.getColumnWidth(200,300),
          align: 'left',
          sortable: 'custom',
          render: (h, params) => {
            return h("span", timeToString(params.row.avgPacketLossDegradationTime));
          },
        },
        // 丢包劣化累计时长
        {
          title: this.$t('comm_packetLossDegradation_duration'),
          key: "packetLossDegradationDuration",
          width:this.getColumnWidth(200,280),
          align: 'left',
          sortable: 'custom',
          sortNumber: 4,
          render: (h, params) => {
            return h("span", timeToString(params.row.packetLossDegradationDuration));
          },
        },
        // 时延劣化累计次数
        {
          title: this.$t('comm_delayDegradation_times'),
          key: "degradationCount",
          width: this.getColumnWidth(160,260),
          align: 'left',
          sortable: 'custom',
          // fixed: "left",
        },
          // 时延劣化平均时长
        {
          title: this.$t('comm_delayDegradation_avg'),
          key: "avgDegradationTime",
          width: this.getColumnWidth(200,260),
          align: 'left',
          sortable: 'custom',
          render: (h, params) => {
            return h("span", timeToString(params.row.avgDegradationTime));
          },
        },
          //时延劣化累计时长
        {
          title: this.$t('comm_delayDegradation_duration'),
          key: "degradationDuration",
          width:this.getColumnWidth(200,240),
          align: 'left',
          sortable: 'custom',
          sortNumber: 4,
          render: (h, params) => {
            return h("span", timeToString(params.row.degradationDuration));
          },
        },
         {
          title: this.$t('comm_operate'),
          key: "action",
          align: 'center',
          width: 120,
          // fixed:'right',
          className: "bgColor",
          renderHeader: (h) => {
            const handleClick = () => {
              this.customModalShow = true;
              this.customModalShowLoading = true;
            }
            return h('div',[
              h('span',this.$t('comm_operate')),
              h('img', {
                attrs: {
                  src:this.currentSkin == 1 ? tableEditBtn:tableEditLightBtn
                },
                style: {
                  width: '15px', // 调整图片大小
                  height: '15px', // 考虑保持宽高比
                  marginLeft: '10px',
                  verticalAlign: 'middle',// 使图片居中
                  cursor: 'pointer'

                },
                on: {
                click: handleClick,
              },
              })
            ])
          },
         render: (h,{ row }) => {
          let modify = h(
            'Tooltip',
            {
              props:{
                placement:'left-end',
                transfer: true

              }
            },
            [
              h('span',{
                class: this.currentSkin == 1 ? 'look-icon':'light-look-icon',
                 style: {
                      display: this.permissionObj.look
                        ? "inline-block"
                        : "none",
                },
                 on:{
                 click: () => {

                 if(this.permissionObj.look){
                   this.actionClick(row, "look");
               }else{
                 console.log("无权限")
               }

                  },

              }
              }),
               h('span', { slot: 'content' }, this.$t('comm_view_details'))
            ]
          )
          let array = [];
           array.push(modify);
           return h('div', array);
         }
          // render: (h, params) => {
          //   return h(
          //     "span",
          //     {
          //       class: "action-btn ",
          //       on: {
          //         click: () => {
          //           this.actionClick(params.row, "look");
          //         },
          //       },
          //     },
          //     this.$t('comm_view_details')
          //   );
          // },
        },
      ],
      columns2: [
        // 目标名称
        {
          title: this.$t('comm_target_name'),
          key: "destName",
          align: 'left',
          minWidth: 250,
          render: (h, param) => {
            let str = param.row.destName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            // let str1 = str;
            // if (str.length > 20) {
            //   str1 = str.substring(0, 20) + '...';
            // }
            // if (str.length > 3) {
            //   str1 = str.substring(0, 3) + '...';
            // }
            // return h(
            //   'Tooltip',
            //   {
            //     props: {
            //       placement: 'top-end',
            //       transfer: true
            //     },
            //   },
            //   [
            //     h('span', {
            //       class: "action-btn",
            //        style: {
            //         cursor: 'pointer', color: this.permissionObj.look ? '#05EEFF' :"#ffffff" ,
            //               display: this.permissionObj.look
            //                 ? "inline-block"
            //                 : "none",
            //         },
            //       on: {
            //         click: () => {
            //           if(this.permissionObj.look){
            //             this.actionClick(param.row, "discontinue");
            //           }else{
            //             console.log("无权限")
            //           }

            //         }
            //       }
            //     }, str1),
            //     h(
            //       'span',
            //       {
            //         slot: 'content', //slot属性
            //         style: {
            //           whiteSpace: 'normal',
            //           wordBreak: 'break-all'
            //         }
            //       },
            //       str
            //     ),
            //   ]
            // );
             if(str !== "--") {
              return h('div',
              {class:'table-ellipsis' ,
              style: {
                cursor: "pointer", 
                color: this.currentSkin == 1 ? "#05EEFF" : "#0290FD"

              },
              
               on: {
                    click: () => {
                      if(this.permissionObj.look){
                        this.actionClick(param.row, "discontinue");
                      }else{
                        console.log("无权限")
                      }

                    }
                  }
              },[
              h('Tooltip',{
                props: {
                  placement:'top-start',
                  content: str,
                }

              },str)

            ])
            }else {
              return h('div',{
                style: {
                cursor: "pointer", 
                color:this.currentSkin == 1 ? "#05EEFF" : "#0290FD"

              },
               on: {
                    click: () => {
                      if(this.permissionObj.look){
                        this.actionClick(param.row, "discontinue");
                      }else{
                        console.log("无权限")
                      }

                    }
                  }
              },str)
            }
          }
        },
        {
          title: this.$t('comm_source_ip'),
          key: "sourceIp",
          align: 'left',
          width: 200,
          render: (h, param) => {
            let str = param.row.sourceIp;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);
            let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('comm_target_ip'),
          key: "destIp",
          align: 'left',
          width: 200,
          render: (h, param) => {
            let str = param.row.destIp;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);
            let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('rphealthy_cumulative_number'),
          key: "times",
          align: 'left',
          width: this.getColumnWidth(90,140),
        },
        {
          title: this.$t('comm_duration1'),
          key: "duration",
          align: 'left',
          minWidth: 180,
          render: (h, param) => {
            return h("span", timeToString(param.row.duration));
          },
        },
      ],
      columns3: [
        {
          title: this.$t('comm_target_name'),
          key: "destName",
          align: 'left',
          minWidth: 250,
          tooltip: true,
          render: (h, param) => {
            let str = param.row.destName;
            str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
            // let str1 = str;
            // if (str.length > 20) {
            //   str1 = str.substring(0, 20) + '...';
            // }
            // if (str.length > 3) {
            //   str1 = str.substring(0, 3) + '...';
            // }
            // return h(
            //   'Tooltip',
            //   {
            //     props: {
            //       placement: 'top-end',
            //       transfer: true
            //     },
            //   },
            //   [
            //     h('span', {
            //       class: "action-btn",
            //       style: {
            //         cursor: 'pointer', color: this.permissionObj.look ? '#05EEFF' :"#ffffff" ,
            //               display: this.permissionObj.look
            //                 ? "inline-block"
            //                 : "none",
            //         },
            //       on: {
            //         click: () => {
            //            if(this.permissionObj.look){
            //             this.actionClick(param.row, "worsen");
            //           }else{
            //             console.log("无权限")
            //           }

            //         }
            //       }
            //     }, str1),
            //     h(
            //       'span',
            //       {
            //         slot: 'content', //slot属性
            //         style: {
            //           whiteSpace: 'normal',
            //           wordBreak: 'break-all'
            //         }
            //       },
            //       str
            //     ),
            //   ]
            // );
              if(str !== "--") {
              return h('div',
              {class:'table-ellipsis' ,
              style: {
                cursor: "pointer", 
                color: this.currentSkin == 1 ? "#05EEFF" : "#0290FD"

              },
              
               on: {
                    click: () => {
                      if(this.permissionObj.look){
                        this.actionClick(param.row, "worsen")
                      }else{
                        console.log("无权限")
                      }

                    }
                  }
              },[
              h('Tooltip',{
                props: {
                  placement:'top-start',
                  content: str,
                }

              },str)

            ])
            }else {
              return h('div',{
                style: {
                cursor: "pointer", 
                color:this.currentSkin == 1 ? "#05EEFF" : "#0290FD"

              },
               on: {
                    click: () => {
                      if(this.permissionObj.look){
                        this.actionClick(param.row, "discontinue");
                      }else{
                        console.log("无权限")
                      }

                    }
                  }
              },str)
            }
          }
        },
        {
          title: this.$t('comm_source_ip'),
          key: "sourceIp",
          align: 'left',
          width: 200,
           render: (h, param) => {
            let str = param.row.sourceIp;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);
            let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('comm_target_ip'),
          key: "destIp",
          align: 'left',
          width: 200,
           render: (h, param) => {
            let str = param.row.destIp;
            str = (str === undefined || str === null || str === '' || str === 'null' ? '--' : str);
            let maxWidth = param.column.width; // 获取动态传递的宽度
              str = ipv6Format.formatIPv6Address(str,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
          }
        },
        {
          title: this.$t('rphealthy_cumulative_number'),
          key: "times",
          width: this.getColumnWidth(90,140),
          align: 'left',
        },
        {
          title: this.$t('comm_duration1'),
          key: "duration",
          align: 'left',
          minWidth: 180,
          render: (h, param) => {
            return h("span", timeToString(param.row.duration));
          },
        },
      ],
      title: {
        deviceIp: "",
        sourceName: "",
        sourceIp: "",
        port: "",
      },
      editType: 0,
      healthLook: {},
      look: {
        show: false,
        title: "",
        titleSourceIP: "",
        titleDestIP: "",
        listParam: {
          taskId: null,
          startTime: "",
          endTime: "",
          lastEventTimeOrder: null,
          startTimeOrder: "desc",
          eventDurationOrder: null,
          eventType: 0,
          pageNo: 1,
          pageSize: 3,
        },
      },
      //topo图参数
      topologyParam: {
        linkId: "",
        startTime: "",
        endTime: "",
        logTracertId: "",
        sourceIp: "",
        destIp: "",
        taskType: null,
        createTs: "",
      },
      //topo数据
      topologyList: [],
      ipTitleParam: {
        sourceIp: "",
        destIp: "",
        name: "",
        degradationType: "",
        data_source: "",
      },
      /*趋势图参数设置*/
      /*趋势图按钮*/
      btnValue: 1,
      echartLookParama2: {
        level: 2,
        devIp: "",
        preNodeIp: "",
        nodeIp: "",
        linkId: "",
        startTime: new Date().format("yyyy-MM-dd 00:00:00"),
        endTime: new Date(new Date().setTime(new Date().getTime())).format(
          "yyyy-MM-dd 00:00:00"
        ),
        queryType: 2,
        High: false,
        special: false,
      },
      //保存当前故障时间
      saveStartTime: null,
      saveEndTime: null,
      markPoint: "",
      markPointGreen: "",
      height: 500,
      height2: 260,
      echart1: {
        show: true,
      },
      echart2: {
        show: true,
      },
      preAlias: {
        name: "",
        port: "",
      },
      zAlias: {
        name: "",
        port: "",
      },
      delayLossColor: ["#00FFEE", "#0290FD"],
      delayLossUnit: {
        时延: "ms",
        丢包率: "%",
        入流速: "bps",
        出流速: "bps",
        "可用率（免责）": "%",
        "优良率（免责）": "%",
        可用率: "%",
        优良率: "%",
      },
      flowColor: ["#478EE9", "#F19149"],
      flowUnit: "B",
      goodRateColor: ["#478EE9", "#46B759", "#F19149", "#24C3C5"],
      goodRateUnit: "%",
      delayLossData: {
        delay: [],
        loss: [],
      },
      flowData: {
        enter: [],
        issue: [],
      },
      goodRateData: {
        nrUseRateList: [],
        nrGoodRateList: [],
        useRateList: [],
        goodRateList: [],
      },
      delayLossScale: true,
      startScale: false,
      startScale2: false,
      delayLossLevel: 0,
      flowLevel: 0,
      goodRateLevel: 0,

      delayStart: 0,
      delayStart2: 0,
      delayEnd: 100,
      delayEnd2: 100,
      scale: "",
      startValue: "",
      startValue2: "",
      endValue: "",
      endValue2: "",
      scale2: "",
      delayPs: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
        psM: {},
        psS: {},
      },
      delayPs2: {
        //记录滚动条位置的对象，保存在sessionStorage中
        psD: {},
        psH: {},
      },
      //以上为趋势图有关参数
      isdarkSkin: sessionStorage.getItem('dark') || 1,
    };
  },
  created() {
     this.modalWidth = localStorage.getItem('modalWidth') * 0.98

    let delayLossUnitTemp = {};
    // 时延
    delayLossUnitTemp[this.$t("speed_delay")] = "ms";
    // 丢包率
    delayLossUnitTemp[this.$t('comm_packet_loss')] = "%";
    // 入流速
    delayLossUnitTemp[this.$t("specquality_incoming_velocity")] = "bps";
    // 出流速
    delayLossUnitTemp[this.$t("specquality_exit_velocity")] = "bps";
    delayLossUnitTemp[this.$t('specquality_availability_exemption')] = "%";
    // 可用率
    delayLossUnitTemp[this.$t('specquality_availability')] = "%";
    // 优良率
    delayLossUnitTemp[this.$t('specquality_good_rate')] = "%";

    this.delayLossUnit = Object.assign(this.delayLossUnit, delayLossUnitTemp);

    const src = window.frames.frameElement.getAttribute('src')
    let frameDataStr = unescape(src).split('=')[1] ?? ''
    console.log(frameDataStr);
    if (frameDataStr && JSON.parse(frameDataStr).from == 'report') {
      this.reportState = 1;
      this.orgLists = []
      // frameDataStr有值且from ==report 表明是从质量报告加载的iframe
      let frameData = JSON.parse(frameDataStr)
      this.orgLists.push({
        id: frameData.orgId,
        name: frameData.orgName
      })
      console.log(this.orgLists);
      this.startTime = frameData.startTime
      this.endTime = frameData.showEndTime
      this.query.startTime = frameData.startTime
      this.query.endTime = frameData.endTime
      this.query.orgId = frameData.orgId
      this.query.type = frameData.type
    } else {
       this.reportState = 0;
      // 不是从质量报告加载iframe时，才重定向
      this.$nextTick(() => {
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());

      })
      
    }
    moment.locale("zh-cn");
    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});
    this.getTreeOrg();
    this.getGroupList();
    this.$nextTick(() => {
      this.dateValueCopy = [];
      if (getQueryVariable("start")) {
        this.startTime = this.query.startTime = getQueryVariable("start");
        this.endTime = this.query.endTime = getQueryVariable("end");
        this.dateValueCopy = [this.query.startTime, this.query.endTime];
      }
      if (getQueryVariable("startTime")) {
        this.startTime = this.query.startTime = getQueryVariable("startTime");
        this.endTime = this.query.endTime = getQueryVariable("endTime");
        this.dateValueCopy = [this.query.startTime, this.query.endTime];

        this.timeRange = [new Date(this.query.startTime), new Date(this.query.endTime).getTime() - 86400000];
      }
      if (getQueryVariable("type")) {
        this.query.type = Number(getQueryVariable("type"));
      }
      if (getQueryVariable("linkNum")) {
        this.query.keyWord = getQueryVariable("linkNum");
      }
      if (getQueryVariable("screening")) {
        this.query.screening = getQueryVariable("screening");
      }
      this.dateValueCopy = [this.query.startTime, this.query.endTime];

      // this.getStatistics(this.query);
      // this.getInterruptTop(this.query);
      // this.getdegradationTop(this.query);
      this.getTask(this.query);
      document.addEventListener("click", (e) => {
        var box = document.getElementById("selectBox");
        if (box && box.contains(e.target)) {
        } else {
          this.orgTree = false;
        }
      });
      top.document.addEventListener("click", (e) => {
        var box = document.getElementById("selectBox");
        if (box && box.contains(e.target)) {
        } else {
          this.orgTree = false;
        }
      });
    });
    //获取屏幕总长度
    this.screenWidth = window.innerWidth-45;
    //保存原始的字段项展示长度
    this.setCustomFieldsColumnsKeyWidth();
  },
  watch: {
    // ClientList(val) {
    //   let array = [...val];
    //   array.unshift({
    //     id: 0,
    //     orgName: this.$t('comm_all'),
    //   });
    //   this.client_list = array;
    // },
  },
   beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
    
    window.parent.addEventListener("message", (e) => {
      // if (e) {
      //   if (e.data.type == 'msg') {
      //     return;
      //   } else if (typeof e.data == 'object') {
      //     this.isdarkSkin = e.data.isdarkSkin;
      //   } else if (typeof e.data == 'number') {
      //     this.isdarkSkin = e.data;
      //   }

      // }
    });
    this.interValCurTime = setInterval(this.setCurrentTime, 10000);
    addDraggable();
        // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    // 设置颜色
    this.delayLossColor = [eConfig.legend.delayColor[this.currentSkin] ,eConfig.legend.lossColor[this.currentSkin] ];
    this.flowColor = [eConfig.legend.flowInColor[this.currentSkin] ,eConfig.legend.flowOutColor[this.currentSkin] ];
  },
  computed: {
    // ...mapGetters(["ClientList"]),
    tableData1: {
      get: function () {
        let arr = this.tabList1.map(item => {
          for (const key in item) {
            let str = item[key] === undefined || item[key] === null || item[key] === '' || item[key] === 'null' ? '--' : item[key]
            item[key] = str
          }
          return item
        })
        return arr
      },
    },
    tableData2: {
      get: function () {
        let arr = this.tabList2.map(item => {
          for (const key in item) {
            let str = item[key] === undefined || item[key] === null || item[key] === '' || item[key] === 'null' ? '--' : item[key]
            item[key] = str
          }
          return item
        })
        return arr
      },
    },
    tableData3: {
      get: function () {
        let arr = this.tabList3.map(item => {
          for (const key in item) {
            let str = item[key] === undefined || item[key] === null || item[key] === '' || item[key] === 'null' ? '--' : item[key]
            item[key] = str
          }
          return item
        })
        return arr
      },
    },
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    //保存原始的字段项展示长度
    setCustomFieldsColumnsKeyWidth(){
      if(this.customFieldsColumnsKeyWidth==0){
        this.fixedColumns.forEach(item=>{
          let customFieldsColumnsKeyWidthObj = {"key":item.key,"width":item.width};
          this.customFieldsColumnsKeyWidth.push(customFieldsColumnsKeyWidthObj);
        });
      }
    },
    //修改自定列表项
    customModalOk() {
        let param = {
          allocationListFields:this.allocationListFields,
          key:this.customKey
        }
        this.$http.PostJson("/allocationlistfields/update", param).then((res) => {
        if (res.code === 1) {
          this.$Message.success({ content: this.$t('comm_success'), background: true });
          this.customModalShow= false;
          this.customModalShowLoading= false;
          this.getTask(this.query);
        }
      })
    },
    //取消修改
    customModalCancel() {
      this.customModalShow= false;
      this.customModalShowLoading= false;
    },
    //获取列表项下标并改变状态
    getFieldsColumnsIndex(row, index){
      this.customMoveIndex = index;
      // if(row.fixedField == false){
      //   if(row.showField == true){
      //   this.allocationListFields[index].showField =false;
      //   }else{
      //     this.allocationListFields[index].showField =true;
      //   }
      // }
    },
    rowClassName(row, index) {
      if (index === this.customMoveIndex) {
        return "selected-row";
      }
      return "";
    },

    //上移
    moveUp() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex > 0) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex - 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex - 1;
      }
    },
    //下移
    moveDown() {
      let checkedIndex = this.customMoveIndex;
      if (checkedIndex < this.allocationListFields.length - 1) {
        let checkedItem = this.allocationListFields.splice(checkedIndex, 1)[0];
        this.allocationListFields.splice(checkedIndex + 1, 0, checkedItem);
        this.customMoveIndex = checkedIndex + 1;
      }
    },
    // ...mapActions(["getClientList"]),
    focusFn() {
      this.getTreeOrg()
    },
    getTreeOrg(param = null) {
      let _self = this;
      _self.$http.PostJson("/org/tree", { orgId: param }).then((res) => {
        if (res.code === 1) {
          let treeNodeList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item;
          });
          _self.treeData = treeNodeList;
        }
      });
    },
    loadData(item, callback) {
      this.$http.PostJson("/org/tree", { orgId: item.id }).then((res) => {
        if (res.code === 1) {
          let childrenOrgList = res.data.map((item, index) => {
            item.title = item.name;
            item.id = item.id;
            // item.expand=false;
            item.loading = false;
            item.children = [];
            if (index === 0) {
              // item.expand=true;
            }
            return item;
          });
          callback(childrenOrgList);
        }
      });
    },
    setOrg(item) {
      this.treeValue = item[0].name
      this.query.orgId = item[0] ? item[0].id : null;
      this.orgLists = item;
      this.orgTree = false;
    },
    onClear() {
      this.query.orgId = ''
      this.treeValue = ''
    },
    choicesOrg() {
      this.orgTree = true;
    },
    getGroupList() {
      const param = {
        pageNo: 1,
        pageSize: 100000,
      };
      this.$http.wisdomPost("/group/list", param).then((res) => {
        if (res.code === 1) {
          if (res.data) {
            this.groupList = res.data.records;
          }
        }
      });
    },
    setCurrentTime() {
      this.currentTime = Date.now();
    },
    //查询
    queryClick() {
      if (this.query.type == 1) {
        this.typeName = this.$t('comm_angle_event');
      } else {
        this.typeName = this.$t('comm_angle_failure');
      }
      this.query.pageNo = 1;
      this.dateValueCopy = [];
      this.checkAll = -1;
      this.pageNo = 1;

      if (this.query.orgId == 0 || this.query.orgId == undefined) {
        this.query.orgId = "";
      }
      if (this.query.operator == 0 || this.query.operator == undefined) {
        this.query.operator = "";
      }
      if (
        this.timeRange[0] == "" ||
        this.timeRange[0] == undefined ||
        this.timeRange[1] == "" ||
        this.timeRange[1] == undefined
      ) {
        this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
        this.query.endTime = new Date(new Date().getTime() + 86400000).format("yyyy-MM-dd 00:00:00");
      }
      if (
        this.timeRange[0] &&
        this.timeRange[0] != "" &&
        this.timeRange[1] &&
        this.timeRange[1] != ""
      ) {
        this.query.startTime = new Date(this.timeRange[0]).format(
          "yyyy-MM-dd HH:mm:ss"
        );
        this.query.endTime = new Date(new Date(this.timeRange[1]).getTime() + 86400000).format("yyyy-MM-dd 00:00:00");
      }
      let startVal = moment(this.query.startTime, "YYYY-MM-DD hh:mm:ss").valueOf();
      let endVal = moment(this.query.endTime, "YYYY-MM-DD hh:mm:ss").valueOf();
      if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
        this.$Message.warning(this.$t('warning_time_not_exceed_62'));
        return;
      }
      this.dateValueCopy = [this.query.startTime, this.query.endTime];
      // this.getStatistics(this.query);
      // this.getInterruptTop(this.query);
      // this.getdegradationTop(this.query);
      this.query.keyWord = this.query.keyWord.trim();
      this.getTask(this.query);
    },
    //获取任务统计图表信息
    getStatistics(param) {
      let _self = this;
      //1.链路信息列表
      let params = {
        groupId: param.groupId,
        orgId: param.orgId,
        keyWord: param.keyWord ? param.keyWord.replace(/\s/g, "") : "",
        startTime: param.startTime,
        endTime: param.endTime,
        type: param.type,
      };
      _self.$http.PostJson("/taskanalysis/getTaskNumStatistic", params)
        .then((res) => {
          if (res.code === 1) {
            if (res.data) {
              //统计图
              let taskGraph = res.data;
              _self.pie_List[0].totalValue = taskGraph.totalTaskNum || 0;
              _self.pie_List[0].data[0].value = taskGraph.normalTaskNum || 0;
              _self.pie_List[0].data[0].Percent = taskGraph.normalPercent || 0;
              _self.pie_List[0].data[1].value = taskGraph.degradationTaskNum || 0;
              _self.pie_List[0].data[1].Percent = taskGraph.degradationPercent || 0;
              _self.pie_List[0].data[2].value = taskGraph.brokenTaskNum || 0;
              _self.pie_List[0].data[2].Percent = taskGraph.brokenPercent || 0;
            } else {
              _self.pie_List[0].totalValue = 0;
              _self.pie_List[0].data[0].value = 0;
              _self.pie_List[0].data[0].Percent = 0;
              _self.pie_List[0].data[1].value = 0;
              _self.pie_List[0].data[1].Percent = 0;
              _self.pie_List[0].data[2].value = 0;
              _self.pie_List[0].data[2].Percent = 0;
            }
          }
        });
    },
    computedHeight() {
        // 单行，两行，三行以上高度
        if (this.tabListModal.length == 1) {
          return 100;
        } else if (this.tabListModal.length == 2) {
          return 150;
        } else {
          return this.tabListModal.length == 0 ? 255 : 200
      
        }
        // // 行数乘以单行高度
        // let rowsHeight = this.tabListModalData.length * rowHeight;
        // // 返回计算后的高度，不超过最大高度
        // console.log('=====.>',Math.min(rowsHeight, this.maxHeight));
        // return Math.min(rowsHeight, this.maxHeight);
      },
    //获取{{$t('comm_interruption_top5')}}
    getInterruptTop(param) {
      this.loading2 = true;
      this.$http
        .PostJson("/taskanalysis/getBrokenTaskTop", {
          startTime: param.startTime,
          endTime: param.endTime,
          orgId: param.orgId,
          keyWord: param.keyWord ? param.keyWord.replace(/\s/g, "") : "",
          timesOrder: null,
          durationOrder: null,
          type: param.type,
          groupId: param.groupId,
        })
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if (data && Array.isArray(data)) {
              this.tabList2 = data;
            }
            this.loading2 = false;
          } else {
            this.$Message.error(res.msg);
          }
        })
        .catch((msg) => {
          this.loading2 = false;
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    //获取劣化TOP
    getdegradationTop(param) {
      this.loading3 = true;
      this.$http
        .PostJson("/taskanalysis/getDegradationTaskTop", {
          startTime: param.startTime,
          endTime: param.endTime,
          orgId: param.orgId,
          keyWord: param.keyWord ? param.keyWord.replace(/\s/g, "") : "",
          timesOrder: null,
          durationOrder: null,
          type: param.type,
          groupId: param.groupId,
        })
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if (data && Array.isArray(data)) {
              this.tabList3 = data;
            }
            this.loading3 = false;
          } else {
            this.$Message.error(res.msg);
          }
        })
        .catch((msg) => {
          this.loading3 = false;
        })
        .finally(() => {
          this.loading3 = false;
        });
    },
    sortChange({ column, key, order }) {
      if (order === 'normal') {
        this.query.sortName = 'goodRate';
        this.query.sort = 'desc';
      } else {
        this.query.sortName = key;
        this.query.sort = order;
      }
      this.getTask(this.query);
    },
    //获取自定义列表字段
    getAllocationListFieldsByKey(){
      //key值
      this.$http.PostJson("/allocationlistfields/getAllocationListFieldsByKey", { key: this.customKey }).then((res) => {
        if (res.code === 1) {
          if(res.data.allocationListFields){
            let screenWidthTemp = this.screenWidth;
            this.allocationListFields = res.data.allocationListFields;
            this.fieldsJsonObjArr = [];
           
            //中间自定义列
            res.data.allocationListFields.forEach(item=>{
                if(item.showField === true){
                  this.fieldsJsonObjArr.push(item.parameterName);
                }
            });
             this.fieldsJsonObjArr.push("action");//第一列选择列
            this.taskStatisColumns = [];
            let customColumnsWidth = 0;//回显自定义字段总宽度
            let customColumnsAvgWidth = 0;//回显自定义字段平均宽度
            //从固定列中动态回显自定义列标头并且按照顺序
            if(this.fieldsJsonObjArr.length>0){
              this.fieldsJsonObjArr.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item === item2.key){
                    //计算需要展示自定义字段项总长度
                    let customFieldsColumnsKeyWidthTemp = this.customFieldsColumnsKeyWidth.find(item3 => item3.key === item2.key);
                    if(customFieldsColumnsKeyWidthTemp){
                      customColumnsWidth = customColumnsWidth + customFieldsColumnsKeyWidthTemp.width;
                      item2.width = customFieldsColumnsKeyWidthTemp.width;
                    }
                    this.taskStatisColumns.push(item2);
                    return;
                  }
                });
              });
              //赋值标头名称
              this.allocationListFields.forEach(item=>{
                this.fixedColumns.forEach(item2=>{
                  if(item.parameterName === item2.key){
                    item.parameterTitle = item2.title;
                    return;
                  }
                });
              });
              //如果总屏幕长度大于展示字段总长度则相减得到平均值，得到的平均值再加到每个字段上(固定操作项除外)
              if(screenWidthTemp>customColumnsWidth ){
                if(this.taskStatisColumns.length>1){
                  let columnsLength = this.taskStatisColumns.length-1;
                  customColumnsAvgWidth = Math.floor((screenWidthTemp-customColumnsWidth)/columnsLength);
                }
              }
              this.taskStatisColumns.forEach(item=>{
                if(item.key != "action"){
                  item.width = item.width+customColumnsAvgWidth;
                }
              });
            }else{
              this.taskStatisColumns = this.fixedColumns;
            }
          }
        }else{
          this.taskStatisColumns = this.fixedColumns;
        }
      })
    },
    //获取任务列表
    getTask(param) {
      this.getAllocationListFieldsByKey();
      this.loading1 = true;
      this.queryLoading = true;
      var data = {
        startTime: param.startTime,
        endTime: param.endTime,
        orgId: param.orgId,
        keyWord: param.keyWord ? param.keyWord : "",
        pageNo: param.pageNo,
        pageSize: param.pageSize,
        //排序参数
        useableRateOrder: this.useableRateOrder,
        goodRateOrder: this.goodRateOrder,
        latestTimeOrder: this.latestTimeOrder,
        brokenDurationOrder: this.brokenDurationOrder,
        degradationDurationOrder: this.degradationDurationOrder,
        status: this.status,
        type: param.type,
        faultType: param.faultType,
        groupId: param.groupId,
        screening: param.screening,
        sortName: param.sortName,
        sort: param.sort
      };
      // console.log(data);
      this.$http
        .PostJson("/taskanalysis/getTaskAnalysisList", data)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            if (data) {
              const { total, records, brokenTop, degradationTop, statistic } = data;
              if (records && Array.isArray(records)) {
                this.tabList1 = records;
                this.totalCount = total;
              }
              if (brokenTop && Array.isArray(brokenTop)) {
                this.tabList2 = brokenTop;
              }
              if (degradationTop && Array.isArray(degradationTop)) {
                this.tabList3 = degradationTop;
              }
              let taskGraph = statistic;
              this.pie_List[0].totalValue = taskGraph.totalTaskNum || 0;
              this.pie_List[0].data[0].value = taskGraph.normalTaskNum || 0;
              this.pie_List[0].data[0].Percent = taskGraph.normalPercent || 0;
              this.pie_List[0].data[1].value = taskGraph.degradationTaskNum || 0;
              this.pie_List[0].data[1].Percent = taskGraph.degradationPercent || 0;
              this.pie_List[0].data[2].value = taskGraph.brokenTaskNum || 0;
              this.pie_List[0].data[2].Percent = taskGraph.brokenPercent || 0;

              if (Array.isArray(data) && data.length < 1) {
                this.tabList1 = [];
                this.totalCount = 0;
              }
            }
            this.loading1 = false;
            this.queryLoading = false;
          } else {
            this.tabList1 = [];
            this.tabList2 = [];
            this.tabList3 = [];
            this.pie_List[0].totalValue = 0;
            this.pie_List[0].data[0].value = 0;
            this.pie_List[0].data[0].Percent = 0;
            this.pie_List[0].data[1].value = 0;
            this.pie_List[0].data[1].Percent = 0;
            this.pie_List[0].data[2].value = 0;
            this.pie_List[0].data[2].Percent = 0;
            this.totalCount = 0;
            this.$Message.error(res.msg);
          }
        })
        .catch((msg) => {
          this.tabList1 = [];
          this.totalCount = 0;
          this.loading1 = false;
          this.queryLoading = false;
        })
        .finally(() => {
          this.loading1 = false;
          this.queryLoading = false;
        });
    },
    topSortChange({ column, key, order }) {
      //order值可能为asc，desc，normal（不排序）
      let k = [];
      let t = [];
      k = this.orderKey;
      t = this.orderType;
      if (k.includes(key)) {
        let i = this.printArray(k, key);
        if (order === "normal") {
          k.splice(i, 1);
          t.splice(i, 1);
        } else {
          t.splice(i, 1, order);
        }
      } else {
        k.push(key);
        t.push(order);
      }
      this.orderKey = k;
      this.orderType = t;
      this.handleTheadAddClass(column, order);
      this.getTask(this.query);
    },
    //返回某元素在数组中下标
    printArray(k, key) {
      for (let i = 0; i < k.length; i++) {
        if (k[i] == key) {
          return i;
        }
      }
    },
    // 标题行样式改变事件
    handleTheadAddClass(column, order) {
      let i = [];
      let s = [];
      i = this.sortIndex;
      s = this.sortType;
      let key = column.sortNumber;
      if (i.includes(key)) {
        let t = this.printArray(i, key);
        s.splice(t, 1, order);
      } else {
        i.push(key);
        s.push(order);
      }
      this.sortIndex = i;
      this.sortType = s;
      for (let a = 0; a < this.sortIndex.length; a++) {
        let udom = document.getElementsByClassName("ivu-icon-md-arrow-dropup")[
          this.sortIndex[a]
        ];
        let ddom = document.getElementsByClassName(
          "ivu-icon-md-arrow-dropdown"
        )[this.sortIndex[a]];
        udom.removeAttribute("id");
        udom.setAttribute("class", "ivu-icon ivu-icon-md-arrow-dropup");
        ddom.removeAttribute("id");
        ddom.setAttribute("class", "ivu-icon ivu-icon-md-arrow-dropdown");
        this.$nextTick(() => {
          if (s[a] === "asc") {
            udom.id = "select-sort" + a;
            udom.setAttribute("class", "ivu-icon ivu-icon-md-arrow-dropup on");
          }
          if (s[a] === "desc") {
            ddom.id = "select-sort" + a;
            ddom.setAttribute(
              "class",
              "ivu-icon ivu-icon-md-arrow-dropdown on"
            );
          }
        });
      }
    },
    //切换页码
    pageChange(page) {
      this.pageNo = this.query.pageNo = page;
      this.getTask(this.query);
    },
    pageChangeModal(page) {
      this.look.listParam.pageNo = this.modalPage_no = page;
      this.getModalList(this.look.listParam);
    },
    //切换每页条数
    pageSizeChange(e) {
      this.pageNo = this.query.pageNo = 1;
      this.pageSize = this.query.pageSize = e;
      this.getTask(this.query);
    },
    //切换每页条数
    tabParamPageSizeChange(e) {
      this.modalPage_no = this.look.listParam.pageNo = 1;
      this.tabParam.pageSize = this.look.listParam.pageSize = e;
      this.getModalList(this.look.listParam);
    },
    //重置趋势图
    resetData(value) {
      if (!value) {
        // 表格页码重置
       this.modelShow = false
        echarts.dispose(this.delayLossChart1)
        echarts.dispose(this.delayLossChart2)
        
        this.echartLookParama2.preNodeIp = ""
        this.echartLookParama2.nodeIp = ""
        this.usea.preNodeIp = ""
        this.usea.preNodeIp = ""
       
        this.$store.commit("updateDelayLossHistory", -1);
        sessionStorage.removeItem("delayPs")
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        this.btnValue = 1
        
        
        
      }else {
        this.modelShow = true
        // console.log('// 表格页码重置this.tabParam.pageNo = 1',this.tabParam.pageNo)
      }
    },
    //查看导出
    exportViewClick() {
      // this.saveLog();
      let fileName = this.$t('qualityreport_dialog_pt_analysis') + ".xlsx";
      // this.$Loading.start();
      this.$axios({
        url: "/taskanalysis/exportTaskEventList",
        method: "post",
        data: this.look.listParam,
        responseType: "blob", // 服务器返回的数据类型
      })
        .then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, fileName);
          } else {
            // var fileName = "";
            // fileName = "故障清单.xlsx";
            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => {
          this.selectedIds = new Set();
          this.query.ids = null;
          this.queryClick();
          this.$Loading.finish();
        });
    },
    //导出
    exportClick() {
      if (this.tabList1.length > 0) {
        let baseURL = this.$baseUrl + "/taskanalysis/exportTaskAnalysis",
          tokenId = JSON.parse(sessionStorage.getItem("accessToken")).tokenId;
        let a = document.getElementById("exportAll");
        this.$Loading.start();
        let param = {
          orgId: this.query.orgId,
          groupId: this.query.groupId,
          keyWord: this.query.keyWord,
          groupId: this.query.groupId,
          startTime: this.query.startTime,
          endTime: this.query.endTime,
          pageNo: 1,
          pageSize: this.totalCount,
          useableRateOrder: this.useableRateOrder,
          goodRateOrder: this.goodRateOrder,
          latestTimeOrder: this.latestTimeOrder,
          brokenDurationOrder: this.brokenDurationOrder,
          degradationDurationOrder: this.degradationDurationOrder,
          status: this.status,
          type: this.query.type,
          faultType: this.query.faultType,
        };
        //a标签下载
        // a.href =
        //   baseURL +
        //   (baseURL.indexOf("?") === -1 ? "?" : "&") +
        //   Qs.stringify(param);
        //blob下载
        axios({
          url: "/taskanalysis/exportTaskAnalysis",
          method: "get",
          params: param,
          responseType: "blob", // 服务器返回的数据类型
          //data: {}
        }).then((res) => {
          let data = res.data;
          const blob = new Blob([data], { type: "application/vnd.ms-excel" });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.$t('export_dial') + ".xls");
          } else {
            var fileName = "";
            fileName = this.$t('export_dial') + ".xlsx";

            const elink = document.createElement("a"); //创建一个元素
            elink.download = fileName; //设置文件下载名
            elink.style.display = "none"; //隐藏元素
            elink.href = URL.createObjectURL(blob); //元素添加href
            document.body.appendChild(elink); //元素放入body中
            elink.click(); //元素点击实现
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          }
          this.$Loading.finish();
        }).catch(error => {
          console.log(error);
          this.$Loading.finish();
        }).finally(() => {
          this.$Loading.finish();
        });
      } else {
        this.$Message.warning(this.$t('comm_no_data'));
      }
    },
    //查看详情
    actionClick(rowData, type) {
      console.log('rowData==>', rowData);
      // if (!rowData.linkIds) {
      //   this.look.show = false;
      //   this.$Message.warning(this.$t('alarm_no_data'));
      //   return false;
      // }
      let _self = this;
      this.topoShow = false;
      _self.healthLook = rowData;
      this.eventTypes = 0;
      this.queryModal = {
        keyWord: "",
        startTime: this.dateValueCopy[0],
        endTime: this.dateValueCopy[1],
        linkId: rowData.linkId,
        pageNo: this.queryModal.pageNo,
        pageSize: this.queryModal.pageSize,
      };
      _self.echartLookParama2.linkId = rowData.linkId;
      _self.echartLookParama2.devIp = rowData.devIp;
      _self.echartLookParama2.preNodeIp = rowData.sourceIp;
      _self.echartLookParama2.nodeIp = rowData.destIp;
      _self.look.show = true;
      console.log("type", type);
      if (type === "look") {
        _self.look.title = this.$t('pathrun_task_details')

      }
      if (type === "discontinue") {
        _self.look.title = this.$t('nodequality_interrupt_details')

      }
      if (type === "worsen") {
        _self.look.title = this.$t('nodequality_deteriorate_details')

      }
      _self.look.titleSourceIP = rowData.sourceIp
      _self.look.titleDestIP = rowData.destIp
      _self.look.listParam = {
        type: this.query.type,
        taskId: rowData.taskId,
        startTime: this.dateValueCopy[0],
        endTime: this.dateValueCopy[1],
        eventType: "",
        lastEventTimeOrder: _self.look.listParam.lastEventTimeOrder,
        eventDurationOrder: _self.look.listParam.eventDurationOrder,
        startTimeOrder: _self.look.listParam.startTimeOrder,
        pageNo: 1,
        pageSize: this.tabParam.pageSize,
      };
      this.taskId = rowData.taskId;
      _self.modalPage_no = 1;
      _self.getModalList(_self.look.listParam);
    },
    getModalList(param) {
      this.ModalTableLoading = true;
      this.ModalTopoLoading = true;
      this.ModalTrendLoading = true;
      this.$http
        .PostJson("/taskanalysis/getTaskEventList", param)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            const { total, records } = data;
            this.ModalTableLoading = false;
            if (records && Array.isArray(records)) {
              records.map((item, index) => {
                item.rowId = index + "" + Math.random() * 100;
                return item;
              });
              this.tabListModal = records;
              this.tabParam.totalCount = total;
              //默认第一个选中
              this.currentid = this.tabListModal[0].rowId;
              this.usea.preNodeIp = this.tabListModal[0].sourceIp;
              this.usez.nodeIp = this.tabListModal[0].destIp;
              //topo图参数设置，默认第一条
              this.topologyParam = {
                sourceIp: this.tabListModal[0].sourceIp,
                destIp: this.tabListModal[0].destIp,
                linkId: this.tabListModal[0].linkId,
                logTracertId: this.tabListModal[0].logTracertId,
                taskType: this.tabListModal[0].taskType,
                startTime: this.tabListModal[0].startTs,
                createTs: this.tabListModal[0].createTs,
              };

              if (Number(this.tabListModal[0].status) === 1) {
                this.topologyParam.endTime = new Date().format2(
                  "yyyy-MM-dd hh:mm:ss"
                );
              } else {
                this.topologyParam.endTime = new Date(
                  Number(this.tabListModal[0].lastActiveTs) * 1000
                ).format2("yyyy-MM-dd hh:mm:ss");
              }
              console.log(this.topologyParam);
              this.echartLookParama2.High =
                this.tabListModal[0].taskType == 3 ? true : false;
              //趋势图故障点定位
              this.markPoint = this.tabListModal[0].startTs;
              this.markPointGreen =
                this.tabListModal[0].status == 1
                  ? ""
                  : new Date(
                    Number(this.tabListModal[0].lastActiveTs) * 1000
                  ).format2("yyyy-MM-dd HH:mm:ss");
              //趋势图参数
              if (this.tabListModal[0].startTs) {
                const timeInterval = this.tabListModal[0].durationOfTime * 1000;
                const handradSevenTyMinutes = 170 * 60 * 1000, handradSixtyTwoHours = 162 * 60 * 60 * 1000;
                if (this.echartLookParama2.High) {
                  if (timeInterval <= handradSevenTyMinutes) {
                    this.echartLookParama2.startTime = moment(this.tabListModal[0].startTs)
                      .subtract(5, "minutes")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      new Date(Number(this.tabListModal[0].lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(5, "minutes")
                      .format("YYYY-MM-DD HH:mm:ss");
                  } else if (timeInterval > handradSevenTyMinutes && timeInterval <= handradSixtyTwoHours) {
                    this.echartLookParama2.startTime = moment(this.tabListModal[0].startTs)
                      .subtract(3, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      new Date(Number(this.tabListModal[0].lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  } else {
                    this.echartLookParama2.startTime = moment(this.tabListModal[0].startTs)
                      .subtract(7, "days")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      new Date(Number(this.tabListModal[0].lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(7, "days")
                      .format("YYYY-MM-DD HH:mm:ss");
                  }
                } else {
                  if (timeInterval > handradSixtyTwoHours) {
                    this.echartLookParama2.startTime = moment(this.tabListModal[0].startTs)
                      .subtract(7, "days")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      new Date(Number(this.tabListModal[0].lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(7, "days")
                      .format("YYYY-MM-DD HH:mm:ss");
                  } else {
                    this.echartLookParama2.startTime = moment(this.tabListModal[0].startTs)
                      .subtract(3, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                    this.echartLookParama2.endTime = moment(
                      new Date(Number(this.tabListModal[0].lastActiveTs) * 1000).format("yyyy-MM-dd HH:mm:ss"),
                      "YYYY-MM-DD HH:mm:ss"
                    )
                      .add(3, "hours")
                      .format("YYYY-MM-DD HH:mm:ss");
                  }
                }
                if (this.tabListModal[0].status == 1) {
                  this.echartLookParama2.endTime = new Date().format(
                    "yyyy-MM-dd HH:mm:ss"
                  );
                }
              }
              this.saveStartTime = this.echartLookParama2.startTime;
              this.saveEndTime = this.echartLookParama2.endTime;
              //判断是否是故障段
              this.isIpduan = this.tabListModal[0].preFurthestIp ? true : false;
              this.echartLookParama2.devIp = this.tabListModal[0].devIp == '0.0.0.0' ? null : this.tabListModal[0].devIp;
              this.echartLookParama2.preNodeIp = this.tabListModal[0].sourceIp;
              this.echartLookParama2.nodeIp = this.tabListModal[0].destIp;
              this.echartLookParama2.linkId = this.tabListModal[0].linkId;
              this.echartLookParama2.level = 2;
              this.echartLookParama2.isSpecial =
                this.tabListModal[0].isSpecial || false;
              this.echartLookParama2.queryType = 1; //1是链路，2是节点

              //获取拓扑图数据
              this.getTopology(this.topologyParam, this.tabListModal[0]);
            }
            this.ModalTableLoading = false;
          }
        })
        .catch((msg) => {
          this.ModalTableLoading = false;
          this.ModalTopoLoading = false;
          this.ModalTrendLoading = false;
          console.log(msg);
        })
        .finally(() => {
          this.ModalTableLoading = false;
          this.ModalTopoLoading = false;
          this.ModalTrendLoading = false;
        });
    },
    //详情排序
    sortModal(data) {
      console.log(data);
      this.look.listParam.lastEventTimeOrder = "";
      this.look.listParam.eventDurationOrder = "";
      this.look.listParam.startTimeOrder = "";
      if (data.key === "lastEventTime" || data.key === "lastActiveTs") {
        this.look.listParam.lastEventTimeOrder = data.order;
        // if (data.order === "normal") {
        //   this.look.listParam.lastEventTimeOrder = "desc";
        // }
      } else if (
        data.key === "eventDuration" ||
        data.key === "durationOfTime"
      ) {
        this.look.listParam.eventDurationOrder = data.order;
        // if (data.order === "normal") {
        //   this.look.listParam.eventDurationOrder = "desc";
        // }
      } else if (data.key === "startTs") {
        this.look.listParam.startTimeOrder = data.order;

      }
      this.look.listParam.pageNo = 1;
      this.look.listParam.pageNo = this.modalPage_no = 1;
      this.getModalList(this.look.listParam);
    },

    //获取拓扑图数据
    getTopology(param, datas) {
      let _self = this;
      _self.topologyList = [];
      _self.$http
        .PostJson("/taskanalysis/getRouteTopology", param)
        .then(({ code, data, msg }) => {
          if (code === 1) {
            // let dataRoute = data;
            // if (dataRoute.length > 0) {
            //   dataRoute.map(item => item.isBroken);
            //   for (let i = 0, len = dataRoute.length; i < len; i++) {
            //     if (dataRoute[i].isBroken == 1) {
            //       _self.echartLookParama2.nodeIp = dataRoute[i].ip1;
            //       break;
            //     }
            //   }
            // }
            // _self.echartLookParama2.nodeIp = datas.errorIp;
            if (datas.taskType == 3) {
              _self.echartLookParama2.nodeIp = data[data.length - 1].ip1;
            } else {
              _self.echartLookParama2.nodeIp = datas.errorIp;
            }

            // param.row.eventType
            _self.topologyHandle(
              datas.errorIp,
              data,
              datas.eventType,
              datas.extraFlag
            );
            _self.ipTitleParam.degradationType =
              datas.eventType == 1
                ? this.$t('dash_interrupt')
                : datas.eventType == 2
                  ? this.$t('common_degradation')
                  : datas.eventType == 3
                    ? this.$t('common_loss_degradation')
                    : "--";
            _self.ipTitleParam.data_source = datas.taskType == 3 ? this.$t('comm_high_freq') : "?";
            let pre_ip = "",
              array = data || [];
            // let index=array.map(item=> item.ip1).indexOf(data.broken_ip.split(',')[0]);
            let index = array
              .map((item) => item.shortIp1)
              .indexOf(_self.echartLookParama2.nodeIp);

            
            // 这一段逻辑防止后端返回的IP不是简写模式
            if(index < 0){
                index = array
                  .map((item) => item.ip1)
                  .indexOf(_self.echartLookParama2.nodeIp);
            }
            // ---------------------------------------------
            
            let findResult = array.find((item1) => item1.shortIp1 === _self.echartLookParama2.nodeIp);
            if(findResult){
              _self.echartLookParama2.nodeIp = findResult.ip1;
            }
            for (let i = index - 1; i >= 0; i--) {
              if (array[i].ip1 != "*" && array[i].suspect != true) {
                pre_ip = array[i].ip1;
                break;
              }
            }
            if (datas.taskType != 3) {
              _self.echartLookParama2.preNodeIp = pre_ip;
              _self.echartLookParama2.queryType = 2;
            }

            this.ModalTopoLoading = false;
            this.getSnmp(this.echartLookParama2);
          }
        })
        .catch((msg) => {
          console.log(msg);
          this.ModalTopoLoading = false;
          this.ModalTrendLoading = false;
        });
    },
    //处理拓扑图数据
    topologyHandle(ip, data, type, extraFla) {
      console.log(data, '拓扑图数据。。。。。')
      let ipArray = ip.split(",")[0],
        list = data;
      let number = 0,
        brokenNum = 0;
      if (list.length > 0) {
        let extraFlag = extraFla;
        let isBrokenF = false,
          isDelay = false,
          borkenIndex = 0,
          delayIndex = 0;
        list.forEach((item, index) => {
          // if (item.extraFlag === 1) {
          //   extraFlag = 1
          // }
          if (item.isBroken === 1 || item.isBroken === 3 || item.isBroken === 7) {
            isBrokenF = true;
            borkenIndex = index;
          }
          if (item.isBroken === 2) {
            isDelay = true;
            delayIndex = index;
          }
        });
        for (let i = 0, len = list.length; i < len; i++) {
          /*重新组装数据start*/
          /*nodeColor:r,y,b;lineColor:r,y,b,linkPoint:true*/
          if (list[i].isBroken != 0) {
            brokenNum = list[i].isBroken;
          }
          list[i].nodeColor =
            list[i].isBroken === 0
              ? "b"
              : list[i].isBroken === 2
                ? "y"
                : "r";
          list[i].lineColor =
            list[i].isBroken === 0
              ? "b"
              : list[i].isBroken === 2
                ? "y"
                : "r";
          list[i].linkPoint =
            list[i].isBroken == 1
              ? true
              : false;
        }
        //处理中断ip段 前的ip为*的处理
        let thisIndex = true;
        if (isBrokenF) {
          for (let index = borkenIndex; index > 0; index--) {
            if (thisIndex && list[index - 1].ip1 == "*") {
              list[index - 1].nodeColor = "r";
              list[index - 1].lineColor = "r";
              thisIndex = true
            } else if (thisIndex && list[index - 1].suspect === true) {
              list[index - 1].nodeColor = "r";
              list[index - 1].lineColor = "r";
              thisIndex = true
            }
            else {
              thisIndex = false
            }
          }
        }
        //处理时延ip段 前的ip为*的处理
        let delayFlag = true;
        if (isDelay) {
          for (let index = delayIndex; index > 0; index--) {
            if (delayFlag && list[index - 1].ip1 == "*") {
              list[index - 1].nodeColor = "y";
              list[index - 1].lineColor = "y";
              delayFlag = true
            } else if (delayFlag && list[index - 1].suspect === true) {
              list[index - 1].nodeColor = "y";
              list[index - 1].lineColor = "y";
              delayFlag = true
            }
            else {
              delayFlag = false
            }
          }
        }
        let indexBroken = list.map((item) => item.isBroken != 0).indexOf(true),
          indexX = 0;
        for (let i = indexBroken - 1; i >= 0; i--) {
          if (i === indexBroken - 1 && list[i].ip1 === "*" && i - 1 >= 0) {
            indexX = i - 1;
            list[i].lineColor =
              brokenNum === 2 ? "y" : "r";
          } else if (indexX === i && list[i].ip1 === "*" && i - 1 >= 0) {
            indexX = i - 1;
            list[i].lineColor =
              brokenNum === 2 ? "y" : "r";
          }
        }
        if (
          list.length === 2 &&
          (list[0].isBroken === 1 || list[1].isBroken === 1)
        ) {
          list[1].lineColor = "r";
          list[1].nodeColor = "r";
          list[1].linkPoint = true;
        }
        list.forEach((item, index) => {
          if (index > borkenIndex && borkenIndex != 0) {
            item.nodeColor = "g";
            item.lineColor = "g";
          }
        });
        this.topologyList = list;
        // console.log(list)
      } else {
        this.topologyList = [];
      }
    },
    //拓扑图点击事件
    topologyClick(param) {
      console.log('param==>', param);
      let _self = this;
      this.echartLookParama2.startTime = this.saveStartTime;
      this.echartLookParama2.endTime = this.saveEndTime;
      if (_self.ipTitleParam.data_source != this.$t('comm_high_freq')) {
        this.delayStart = 0;
        this.delayEnd = 100;
        if (param.suspect) {
          this.suspect = true;
          // return
        } else {
          this.suspect = false;
        }
        // if (param.index === _self.topologyList.length - 1 && param.type === 1) {
        if (param.index != 0 && param.type === 1) {
          _self.buttonVal = 1;
          _self.echartLookParama2.preNodeIp = param.data[0].ip1;
          _self.echartLookParama2.nodeIp = param.data[1].ip1;
          _self.echartLookParama2.queryType = 1;
          if (_self.echartLookParama2.nodeIp != "*") {
            this.topoShow = false;
            _self.getSnmp(_self.echartLookParama2);
          } else {
            this.topoShow = true;
          }
        } else if (param.type === 2) {
          this.topoShow = false;
          _self.buttonVal = 1;
          _self.echartLookParama2.preNodeIp = param.data[0].ip1;
          _self.echartLookParama2.nodeIp = param.data[1].ip1;
          _self.echartLookParama2.queryType = 2;
          _self.getSnmp(_self.echartLookParama2);
        }
      }
    },

    async getSnmp(params) {
      console.log('this.tabListModal===>', this.tabListModal);
      console.log('params===>', params);
      this.loading = true;
      this.noDataLoading = false;
      this.echarLoading = true;
      this.preAlias.name = "";
      this.preAlias.port = "";
      this.zAlias.name = "";
      this.zAlias.port = "";
      this.delayLossData.delay = [];
      this.delayLossData.loss = [];
      this.flowData.enter = [];
      this.flowData.issue = [];
      this.goodRateData.useRateList = [];
      this.goodRateData.goodRateList = [];
      console.log('delay===>', this.delayLossData.delay);
      console.log('loss===>', this.delayLossData.loss);
      params.level = '';
      params.taskId = this.taskId;
      await this.$http
        .PostJson("/trend/getDelayAndLostTrend", params)
        .then((res) => {
          //时延丢包趋势数据
          if (res.code === 1) {
            this.preAlias.name = res.data.preName;
            this.preAlias.port = res.data.prePort;
            this.zAlias.name = res.data.name;
            this.zAlias.port = res.data.port;
            this.delayLossLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              // this.echart1.show = false;
            }
            let lineData = [];
            if (res.data.lineOne) {
              lineData = res.data.lineOne;
            } else if (res.data.lineTwo) {
              lineData = res.data.lineTwo;
            }
            let index = this.closest(lineData, this.markPoint);
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              // this.echart1.show = true;
              this.startScale = false;
              this.delayLossScale = true;
              this.delayLossData.delay = res.data.lineOne;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.delayLossData.loss = res.data.lineTwo;
            }
            this.delayStart = 0;
            // if (res.data.lineOne && res.data.lineOne.length <= 300) {
            this.delayEnd = 100;
            this.startValue = res.data.lineOne[0][0];
            this.endValue = res.data.lineOne[res.data.lineOne.length - 1][0];
            // } else if (res.data.lineOne && res.data.lineOne.length > 300) {
            // this.delayEnd = (300 * 100) / res.data.lineOne.length;
            // this.startValue = res.data.lineOne[0][0];
            // this.endValue = res.data.lineOne[299][0];
            // if (index !== "noPoint") {
            //   if (index <= 150) {
            //     this.startValue = res.data.lineOne[0][0];
            //     if (index + 150 < res.data.lineOne.length - 1) {
            //       this.endValue = res.data.lineOne[index + 150][0];
            //     } else {
            //       this.endValue =
            //         res.data.lineOne[res.data.lineOne.length - 1][0];
            //     }
            //   } else {
            //     this.startValue = res.data.lineOne[index - 150][0];
            //     if (index + 150 < res.data.lineOne.length) {
            //       this.endValue = res.data.lineOne[index + 150][0];
            //     } else {
            //       this.endValue =
            //         res.data.lineOne[res.data.lineOne.length - 1][0];
            //     }
            //   }
            // }
            // } else {
            //   this.delayEnd = 100;
            // }

            this.$store.commit("updateDelayLossHistory", {
              level: res.data.level,
              datas: [this.delayLossData.delay, this.delayLossData.loss],
            }); //保存数据
            this.$store.commit("setdelayLTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
          } else {
            console.log(res.msg);
            this.delayLossData.delay = [];
            this.delayLossData.loss = [];
          }
        })
        .catch((msg) => {
          console.log(msg);
        });

      let trendParam = JSON.parse(JSON.stringify(params));
      trendParam = Object.assign(trendParam, { snmp: false, info: "拨测质差分析" });
      trendParam.level = '';
      await this.$http
        .PostJson("/trend/getDataTrend", trendParam)
        .then((res) => {
          //流速趋势数据
          if (res.code === 1) {
            let flowUnit = 1;
            this.flowLevel = res.data.level;
            if (!res.data.lineOne || res.data.lineOne.length < 1) {
              this.echart2.show = false;
              this.flowLevel = 99;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.echart2.show = true;
              let unitChange = this.getFlowUnit(res.data.lineTwo);
              this.flowUnit = unitChange[0];
              this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
              this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
              flowUnit = unitChange[1];
              this.flowData.enter = res.data.lineTwo.map((item) => {
                // return [
                //   item[0],
                //   item[1] == null || item[1] == ""
                //     ? null
                //     : (Number(item[1]) / flowUnit).toFixed(2)
                // ];
                return [item[0], item[1]];
              });
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.flowData.issue = res.data.lineOne.map((item) => {
                // return [
                //   item[0],
                //   item[1] == null || item[1] == ""
                //     ? null
                //     : (Number(item[1]) / flowUnit).toFixed(2)
                // ];
                return [item[0], item[1]];
              });
            }
            this.$store.commit("setflowTlevel", res.data.level); //保存首次加载的顶层数据粒度级别
            this.$store.commit("setflowUnit", {
              level: res.data.level,
              unit: this.flowUnit
            }); //保存单位
          }
        });
      this.startScale = false;
      params.level = 1;
      let paramsStr = JSON.parse(JSON.stringify(params));
      let params2 = Object.assign(paramsStr);
      params2.preNodeIp = this.usea.preNodeIp;
      params2.nodeIp = this.usez.nodeIp;

      await this.$http
        .PostJson("/trend/getGoodAndUsableRateTrend", params2)
        .then((res) => {
          //优良率趋势数据
          if (res.code === 1 && res.data) {
            this.usea.name = res.data.preName;
            this.usea.port = res.data.prePort;
            this.usez.name = res.data.name;
            this.usez.port = res.data.port;
            this.startScale2 = false;
            this.goodRateLevel = res.data.level;
            if (!res.data.lineOne && !res.data.lineTwo) {
              this.goodRateLevel = 99;
            }
            if (res.data.lineOne && res.data.lineOne.length > 0) {
              this.goodRateData.useRateList = res.data.lineOne;
            }
            if (res.data.lineTwo && res.data.lineTwo.length > 0) {
              this.goodRateData.goodRateList = res.data.lineTwo;
            }
            this.delayStart2 = 0;
            this.delayEnd2 = 100;
            if (res.data.lineOne && res.data.lineOne.length > 0) {

              this.startValue2 = res.data.lineOne[0][0];
              this.endValue2 = res.data.lineOne[res.data.lineOne.length - 1][0];
            }
            // else if (res.data.lineOne && res.data.lineOne.length > 300) {
            //   this.delayEnd2 = (300 * 100) / res.data.lineOne.length;
            //   this.startValue2 = res.data.lineOne[0][0];
            //   this.endValue2 = res.data.lineOne[299][0];
            // } else {
            //   this.delayEnd2 = 100;
            // }
          }
        });
      if (this.echart2.show) {
        this.height = 500;
      } else if (!this.echart2.show) {
        this.height = 260;
      }
      if (this.btnValue == 1) {
        this.initEchart();
      } else {
        this.initEchart2();
      }
      this.ModalTrendLoading = false;
      this.echarLoading = false;
    },
    setDelayLossTooltip() {
      let _self = this;
      return eConfig.tip("axis", function (param) {
        if (_self.btnValue == 1) {
          _self.scale = param[0].data[0];
        } else if (_self.btnValue == 2) {
          _self.scale2 = param[0].data[0];
        }
        var obj = {};
        param = param.reduce(function (item, next) {
          obj[next.seriesIndex]
            ? ""
            : (obj[next.seriesIndex] = true && item.push(next));
          return item;
        }, []);
        let src = "",
          delayTime = "",
          delayTip = "",
          flowTime = "",
          flowTip = "",
          rateTime = "",
          rateTip = "";
        for (let i = 0, len = param.length; i < len; i++) {
          if (param[i].seriesIndex === 0 || param[i].seriesIndex === 1) {
            delayTime = param[i].data[0] + "<br />";
            delayTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            delayTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (param[i].seriesIndex === 2 || param[i].seriesIndex === 3) {
            flowTime = param[i].data[0] + "<br />";
            flowTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            flowTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                ? "--"
                : _self.flowSize(param[i].value[1], true, true)) +
              // param[i].value[1]
              // _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
          if (
            param[i].seriesIndex === 4 ||
            param[i].seriesIndex === 5 ||
            param[i].seriesIndex === 6 ||
            param[i].seriesIndex === 7
          ) {
            rateTime = param[i].data[0] + "<br />";
            rateTip +=
              '<span class="tooltip-round" style="background-color:' +
              param[i].color +
              '"></span>';
            rateTip +=
              param[i].seriesName +
              "：" +
              (param[i].value[1] === undefined ||
                param[i].value[1] === null ||
                param[i].value[1] === "" ||
                param[i].value[1] == -1
                ? "--"
                : param[i].value[1]) +
              _self.delayLossUnit[param[i].seriesName] +
              "<br />";
          }
        }
        return delayTime + delayTip + flowTime + flowTip + rateTime + rateTip;
      });
    },
    Option() {
      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "2%",
              top: "40px",
              width: "96%",
              height: this.echart2.show ? "140px" : "140px",
            },
            {
              // show:this.echart2.show,
              left: "2%",
              top: "270px",
              width: "96%",
              height: this.echart2.show ? "140px" : "0px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#fff" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t('speed_delay'), this.$t('comm_packet_loss')]
            },
            {
              show: this.echart2.show,
              top: "220px",
              right: "43%",
              icon: "roundRect",
              gridIndex: 1,
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#fff" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              data: [this.$t('specquality_incoming_velocity'), this.$t('specquality_exit_velocity')],
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLabel: {
                formatter: (value, index) => {
                  let xDate = new Date(value).format("yyyy-MM-dd HH:mm:ss").split(' ');
                  return xDate[0]
                },
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                }
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
            {
              show: this.echart2.show,
              type: "time",
              gridIndex: 1,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLabel: {
                formatter: (value, index) => {
                  let xDate = new Date(value).format("yyyy-MM-dd HH:mm:ss").split(' ');
                  return xDate[0]
                },
                textStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#0e2a5f",
                }
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: this.$t('comm_delay(ms)'),
              type: "value",
              max:this.handleYcoordinate(this.delayLossData.delay).maxNum,
              min:this.handleYcoordinate(this.delayLossData.delay).minNum,
              scale: true,
              gridIndex: 0,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
            {
              name: this.$t('comm_loss_rate'),
              type: "value",
              scale: true,
              gridIndex: 0,
              position: "right",
              max: 100,
              min: 0,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
            {
              show: this.echart2.show,
              // name: "流速(" + this.flowUnit + ")",
              type: "value",
              scale: true,
              position: "left",
              gridIndex: 1,
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
              axisLabel: {
                show: true,
                formatter: (value) => {
                  return this.getUnit(value, true, true);
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0, 1],
              startValue: this.startValue,
              endValue: this.endValue,
            },
            {
              type: "slider",
              top: this.echart2.show ? "460px" : "230px",
              height: 20,
              xAxisIndex: [0, 1],
              realtime: true,
              startValue: this.startValue,
              endValue: this.endValue,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , // "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , //  top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
            },
          ],
          series: [
            {
              type: "line",
              name: this.$t('speed_delay'),
              xAxisIndex: 0,
              yAxisIndex: 0,
              smooth: true,
              symbol: this.delayLossData.delay.length > 1 ? "none" : "circle",
              // seriesLayoutBy:'row',
              color: this.delayLossColor[0],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color:eConfig.areaStyle.delayColor_0[this.currentSkin] //  "rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.delayColor_8[this.currentSkin] // "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.delay,
              markLine: {
                symbol: ["pin", "none"],
                symbolSize: 15,
                data: [
                  {
                    symbol: "image://" + pointRed,
                    symbolSize: 10,
                    xAxis: this.markPoint,
                    symbolRotate: "0",
                    // symbolOffset:[0,'0'],
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "red",
                      fontSize: 10,
                      formatter: () => {
                        return this.markPoint;
                      },
                      // backgroundColor: "#fff",
                      // padding: [0]
                    },
                  },
                  {
                    symbol: "image://" + pointGreen,
                    symbolSize: 10,
                    xAxis: this.markPointGreen,
                    symbolRotate: "0",
                    // symbolOffset:[0,'70px'],
                    lineStyle: {
                      color: "rgba(0,0,0,0)",
                      width: 0,
                      opacity: 1,
                    },
                    label: {
                      show: true,
                      position: "start",
                      color: "#33cc33",
                      fontSize: 10,
                      // backgroundColor: "#fff",
                      formatter: () => {
                        return this.markPointGreen;
                      },
                      // padding: [0]
                    },
                  },
                ],
                emphasis: {
                  lineStyle: {
                    color: "red",
                    width: 0,
                    opacity: 1,
                  },
                },
              },
            },
            {
              type: "line",
              name: this.$t('comm_packet_loss'),
              xAxisIndex: 0,
              yAxisIndex: 1,
              // seriesLayoutBy:'row',
              smooth: true,
              symbol: this.delayLossData.loss.length > 1 ? "none" : "circle",
              color: this.delayLossColor[1],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.lossColor_0[this.currentSkin] // "rgba(2, 144, 253, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.lossColor_8[this.currentSkin] // "rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
              data: this.delayLossData.loss,
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_incoming_velocity'),
              type: "line",
              smooth: true,
              symbol: this.flowData.enter.length > 1 ? "none" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 2, // 指定y轴
              color: this.flowColor[0],
              data: this.flowData.enter,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowInColor_0[this.currentSkin] //"rgba(0, 255, 238, 0.60)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowInColor_8[this.currentSkin] //"rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              show: this.echart2.show,
              name: this.$t('specquality_exit_velocity'),
              type: "line",
              smooth: true,
              symbol: this.flowData.issue.length > 1 ? "none" : "circle",
              xAxisIndex: 1,
              yAxisIndex: 2,
              color: this.flowColor[1],
              data: this.flowData.issue,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: eConfig.areaStyle.flowOutColor_0[this.currentSkin] //"rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: eConfig.areaStyle.flowOutColor_8[this.currentSkin] //"rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart() {
      let that = this;
      if (that.delayLossChart1) {
        // debugger
        // that.delayLossChart1.clear();
        that.delayLossChart1.dispose()
        
      }
      top.document.getElementById("pathquality-delayLoss").style.height = this.height + "px";
      that.delayLossChart1 = echarts.init(this.$refs["pathquality-delayLoss"]);
      that.delayLossChart1.resize();
      /*设置时延丢包率echart图*/
      console.log(this.Option())
   
      that.delayLossChart1.setOption(this.Option());

      that.delayLossChart1.getZr().on("mousewheel", (params) => {
        let getTlevel = that.$store.state.delayTlevel; //获取delay保存的顶层粒度级别
        let getflowTlevel = that.$store.state.flowTlevel; //获取delay保存的顶层粒度级别
        let getSaveData = that.$store.state.delayLossHistory; //获取保存的数据
        let getflowSaveData = that.$store.state.flowHistory; //获取保存的数据
        let getflowSaveUnit = that.$store.state.flowUnit; //获取流速单位数据

        var pointInPixel = [params.offsetX, params.offsetY];
        if (
          that.delayLossChart1.containPixel(
            { gridIndex: [0, 1, 2] },
            pointInPixel
          )
        ) {
          let startValue =
            that.delayLossChart1.getModel().option.dataZoom[0].startValue;
          let endValue =
            that.delayLossChart1.getModel().option.dataZoom[0].endValue;
          this.echartLookParama2.startTime = new Date(startValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          this.echartLookParama2.endTime = new Date(endValue).format2(
            "yyyy-MM-dd HH:mm:ss"
          );
          that.setPs(that.delayLossLevel, [
            this.timeChange(startValue),
            this.timeChange(endValue),
          ]);
          let differ = endValue - startValue;
          let original = 180 * 60 * 1000; // (0，180分钟)，原始采集粒度；
          let minute = 7 * 24 * 60 * 60 * 1000 - 180 * 60 * 1000; // (180分钟，7天)，1分钟粒度；
          let hour = 7 * 24 * 60 * 60 * 1000; // (7天，∞天)，1小时粒度；
          var levelNum = "";
          let start = that.delayLossChart1.getModel().option.dataZoom[0].start;
          let end = that.delayLossChart1.getModel().option.dataZoom[0].end;

          if (params.wheelDelta >= 0 && that.startScale == false) {
            if (this.delayLossLevel == 1) {
              // 小时粒度
              if (differ < minute) {
                if (!that.startScale) {
                  that.startScale = true;
                  levelNum = 2;
                  let delayParam = Object.assign(this.echartLookParama2, {
                    level: levelNum,
                  });
                  let flowParam = Object.assign(delayParam, {
                    snmp: this.dataSource == 1 ? true : false,
                  });
                  this.getDelayLoss(delayParam, flowParam);
                }
              }
            } else if (this.delayLossLevel == 2) {
              // 分钟粒度
              if (differ < original) {
                if (!that.startScale) {
                  if (this.echartLookParama2.High) {
                    that.startScale = true;
                    levelNum = 4;
                    let delayParam = Object.assign(this.echartLookParama2, {
                      level: levelNum,
                    });
                    let flowParam = Object.assign(delayParam, {
                      snmp: this.dataSource == 1 ? true : false,
                    });
                    this.getDelayLoss(delayParam, flowParam);
                  }
                }
              }
            }
          } else {
            // console.log("缩小");
           
            if (start == 0 && end == 100) {
              //是否处在缩放过程中
              if (that.delayLossLevel == getTlevel) {
                that.setPs(that.delayLossLevel, [
                  that.timeChange(startValue),
                  that.timeChange(endValue),
                ]);
                that.startScale = false;
              } else {
                let getSite = JSON.parse(sessionStorage.getItem("delayPs"));
                if (that.flowLevel == getflowTlevel) {
                  that.startScale = false;
                } else {
                  if (that.flowLevel == 2) {
                    that.flowLevel = 1;
                    that.startScale = true;
                    that.flowUnit = getflowSaveUnit.HoursUnit;
                    that.flowData.enter = getflowSaveData.HoursData.enter;
                    that.flowData.issue = getflowSaveData.HoursData.issue;
                  } else if (that.flowLevel == 3 || that.flowLevel == 4) {
                    that.flowLevel = 2;
                    that.startScale = true;
                    that.flowUnit = getflowSaveUnit.minuteUnit;
                    that.flowData.enter = getflowSaveData.minuteData.enter;
                    that.flowData.issue = getflowSaveData.minuteData.issue;
                  }
                }

                if (that.delayLossLevel == getTlevel) {
                  that.startScale = false;
                } else if (that.delayLossLevel == 2) {
                  that.delayLossLevel = 1;
                  that.startScale = true;
                  that.delayLossData.delay = getSaveData.HoursData.delay;
                  that.delayLossData.loss = getSaveData.HoursData.loss;
                  that.delayStart = getSite.psH.start;
                  that.startValue = getSite.psH.start;
                  that.delayEnd = getSite.psH.end;
                  that.endValue = getSite.psH.end;
                } else if (
                  that.delayLossLevel == 3 ||
                  that.delayLossLevel == 4
                ) {
                  that.delayLossLevel = 2;
                  that.startScale = true;
                  that.delayLossData.delay = getSaveData.minuteData.delay;
                  that.delayLossData.loss = getSaveData.minuteData.loss;
                  that.delayStart = getSite.psM.start;
                  that.startValue = getSite.psM.start;
                  that.delayEnd = getSite.psM.end;
                  that.endValue = getSite.psM.end;
                }
                this.$nextTick(() => {
                  that.startScale = false;
                  that.initEchart();
                })
              }
            }
          }
        }
      });
    },

    async getDelayLoss(
      delayParam,
      flowParam,
      cachF,
      hoverDelayTime,
    ) {
      let isUpdate = false;
      this.echarLoading = true;
      if (delayParam.level != 99) {
        this.delayLossData.delay = [];
        this.delayLossData.loss = [];
        isUpdate = true;
        this.delayLoading = true;
        await this.$http
          .PostJson("/trend/getDelayAndLostTrend", delayParam)
          .then((res) => {
            //时延丢包趋势数据
            if (res.code === 1) {
              this.delayLossLevel = res.data.level;
              let lineData = [];
              if (res.data.lineOne) {
                lineData = res.data.lineOne;
              } else if (res.data.lineTwo) {
                lineData = res.data.lineTwo;
              }
              // let index = this.closest(lineData, hoverDelayTime);
              let index = this.closest(lineData, this.markPoint);
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.delayStart = 0;
                // if (res.data.lineOnetotal <= 300) {
                this.delayEnd = 100;
                this.startValue = res.data.lineOne[0][0];
                this.endValue =
                  res.data.lineOne[res.data.lineOne.length - 1][0];
                // } else {
                // this.delayEnd = (300 * 100) / res.data.lineOnetotal;
                // this.endValue = res.data.lineOne[parseInt(res.data.lineOnetotal/2)][0]
                // if (index <= 150) {
                //   this.startValue = res.data.lineOne[0][0];
                //   if (index + 150 < res.data.lineOnetotal - 1) {
                //     this.endValue = res.data.lineOne[index + 150][0];
                //   } else {
                //     this.endValue =
                //       res.data.lineOne[res.data.lineOne.length - 1][0];
                //   }
                // } else {
                //   this.startValue = res.data.lineOne[index - 150][0];
                //   if (index + 150 < res.data.lineOnetotal) {
                //     this.endValue = res.data.lineOne[index + 150][0];
                //   } else {
                //     this.endValue =
                //       res.data.lineOne[res.data.lineOne.length - 1][0];
                //   }
                // }
                // }
                this.startScale = false;
                this.delayLossScale = true;
                this.delayLossData.delay = res.data.lineOne;
              }
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                this.delayLossData.loss = res.data.lineTwo;
              }
              this.$store.commit("updateDelayLossHistory", {
                level: res.data.level,
                datas: [this.delayLossData.delay, this.delayLossData.loss],
              }); //保存数据
              this.setPs(this.delayLossLevel, [this.startValue, this.endValue]);
            }
          });
      }

      if (flowParam.level != 99) {
        this.flowData.enter = [];
        this.flowData.issue = [];
        isUpdate = true;
        this.flowLoading = true;
        let trendParam = JSON.parse(JSON.stringify(flowParam));
        trendParam = Object.assign(trendParam, { snmp: true });
        await this.$http
          .wisdomPost("/specqualityTrendData/getHealthyDataTrend", trendParam)
          .then((res) => {
            //流速趋势数据
            if (res.code === 1) {
              let flowUnit = 1;
              this.flowLevel = res.data.level;
              if (res.data.lineTwo && res.data.lineTwo.length > 0) {
                let unitChange = this.getFlowUnit(res.data.lineOne);
                this.flowUnit = unitChange[0];
                this.delayLossUnit[this.$t('specquality_incoming_velocity')] = unitChange[0];
                this.delayLossUnit[this.$t('specquality_exit_velocity')] = unitChange[0];
                flowUnit = unitChange[1];
                this.flowData.enter = res.data.lineTwo.map((item) => {
                  // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                  return [item[0], item[1]];
                });
              }
              if (res.data.lineOne && res.data.lineOne.length > 0) {
                this.flowData.issue = res.data.lineOne.map((item) => {
                  // return [item[0], Number(item[1] / flowUnit).toFixed(2)];
                  return [item[0], item[1]];
                });
              }

              this.$store.commit("updateFlowHistory", {
                level: res.data.level,
                datas: [this.flowData.enter, this.flowData.issue],
              }); //保存数据
              this.$store.commit("setflowUnit", {
                level: res.data.level,
                unit: this.flowUnit,
              }); //保存单位
            }
          });
      }

      this.delayLoading = false;
      this.flowLoading = false;
      this.rateLoading = false;
      this.startScale = false;
      if (isUpdate) {
        this.initEchart();
      }
      this.echarLoading = false;
    },
    //记录滑块的位置
    setPs(level, position) {
      //记录滚动条的位置
      if (level == 0) {
        this.delayPs.psD.start = position[0];
        this.delayPs.psD.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 1) {
        this.delayPs.psH.start = position[0];
        this.delayPs.psH.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      } else if (level == 2) {
        this.delayPs.psM.start = position[0];
        this.delayPs.psM.end = position[1];
        sessionStorage.setItem("delayPs", JSON.stringify(this.delayPs));
      }
    },

    Option2() {
      let optionArr = [
        {
          tooltip: this.setDelayLossTooltip(),
          axisPointer: {
            type: "shadow",
            link: {
              xAxisIndex: "all",
            },
          },
          grid: [
            {
              left: "2%",
              top: "40px",
              width: "96%",
              height: "140px",
            },
          ],
          legend: [
            {
              show: true,
              top: "0%",
              right: "43%",
              gridIndex: 0,
              icon: "roundRect",
              itemWidth: 16,
              itemHeight: 12,
              textStyle: {
                color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#484b56",
                fontSize: 12,
                fontFamily: "MicrosoftYaHei-Bold",
              },
              color: this.delayLossColor,
              data: [this.$t('specquality_availability'), this.$t('specquality_good_rate')],
            },
          ],
          xAxis: [
            {
              type: "time",
              gridIndex: 0,
              splitLine: {
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "rgba(42, 56, 64, 1)" : "#e3e7f2",
                },
              },
              axisLine: {
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: 1,
                },
              },
            },
          ],
          yAxis: [
            {
              name: this.$t('comm_unit') + "(" + this.goodRateUnit + ")",
              type: "value",
              scale: true,
              gridIndex: 0,
              max: 100,
              position: "left",
              axisTick: {
                show: false,
              },
              axisLine: {
                symbol: ["none", "arrow"],
                symbolSize: [6, 10],
                lineStyle: {
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                  width: "1", //坐标线的宽度
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dotted",
                  color: top.window.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
            },
          ],
          dataZoom: [
            {
              type: "inside",
              xAxisIndex: [0],
              // start: this.delayStart2,
              // end: this.delayEnd2
              startValue: this.startValue2,
              endValue: this.endValue2,
            },
            {
              type: "slider",
              // left: "5%",
              // right: "5%",
              top: "230px",
              height: 20,
              xAxisIndex: [0],
              realtime: true,
              // start: this.delayStart2,
              // end: this.delayEnd2
              startValue: this.startValue2,
              endValue: this.endValue2,
              fillerColor: eConfig.dataZoom.fillerColor[this.currentSkin] , // "rgba(2, 29, 54, 1)",
              borderColor: eConfig.dataZoom.borderColor[this.currentSkin] , // "rgba(22, 67, 107, 1)",
              handleStyle: {
                color: eConfig.dataZoom.handleStyle.color[this.currentSkin] , // "rgba(2, 67, 107, 1)"
              },
              textStyle: {
                color: eConfig.dataZoom.textStyle.color[this.currentSkin] , //  top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              }
              // handleStyle: {
              //   color: "rgba(2, 67, 107, 1)"
              // },
              // textStyle: {
              //   color: top.window.isdarkSkin == 1 ? "#617ca5" : "#676f82",
              // }
            },
          ],
          series: [
            {
              name: this.$t('specquality_availability'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol:
                this.goodRateData.useRateList.length > 1 ? "none" : "circle",
              // stack: "总量",
              color: this.goodRateColor[2],
              data: this.goodRateData.useRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(241,145,73, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(241,145,73, 0.2)",
                    },
                  ],
                },
              },
            },
            {
              name: this.$t('specquality_good_rate'),
              type: "line",
              z: 2,
              smooth: true,
              xAxisIndex: 0,
              yAxisIndex: 0,
              symbol:
                this.goodRateData.goodRateList.length > 1 ? "none" : "circle",
              // stack: "总量",
              color: this.goodRateColor[3],
              data: this.goodRateData.goodRateList,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(36,195,197, 0.8)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(36,195,197, 0.2)",
                    },
                  ],
                },
              },
            },
          ],
        },
      ];
      return optionArr[0];
    },
    initEchart2() {
      let that = this;
      if (that.delayLossChart2) {
        that.delayLossChart2.clear();
        // that.delayLossChart2.dispose()
       
      }
      that.delayLossChart2 = echarts.init(this.$refs["pathquality-delayLoss2"]);

      /*设置时延丢包率echart图*/
      that.delayLossChart2.setOption(this.Option2());
    },
    //时间推算
    setTime(level, time) {
      let Interval = 0,
        start = new Date(this.echartLookParama2.startTime).getTime(),
        end = new Date(this.echartLookParama2.endTime).getTime(),
        newStart = 0,
        newEnd = 0;
      if (level == 0) {
        // Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 1) {
        Interval = 15 * 24 * 60 * 60 * 1000;
      } else if (level == 2) {
        Interval = 12 * 60 * 60 * 1000;
      }
      newStart = time - Interval;
      newEnd = time + Interval;

      if (newStart < start) {
        newStart = start;
      }
      if (newEnd > end) {
        newEnd = end;
      }
      newStart = this.timeChange(newStart);
      newEnd = this.timeChange(newEnd);
      return [newStart, newEnd];
    },
    editCancel() { },
    /*趋势图指标切换*/
    btnClick(num) {
      this.btnValue = num;
      // this.initEchart2()
      
      if (this.btnValue == 1) {
        setTimeout(() => {this.initEchart();},200)
    
       
      } else {
        // this.delayLossChart2.dispose()
        
      
        setTimeout(() => {this.initEchart2();},200)
        
      }
    },
  },
};
</script>

<style >
.table-item {
  display: flex;
  justify-content: space-between;
}

.action-btn {
  color: #05eeff;
  cursor: pointer;
}

.taskStrategy-modal .ivu-table-header th {
  background: #f1f6fe !important;
}

.fault-tab b {
  font-weight: bold !important;
}

.fault-tab .ivu-table-wrapper-with-border {
  border: 1px solid #dcdee2;
  border-bottom: 0;
  border-right: 0;
}

.tableBorder .ivu-table:after {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background-color: #dcdee2;
  z-index: 3;
}

.taskStrategy-modal .ivu-table-wrapper-with-border {
  border: 1px solid #dcdee2;
  border-right: 0;
}

.healthManage p {
  text-align: left !important;
  margin-bottom: 5px !important;
}

.pie-div {
  background-color: var(--body_conent_b_color, #061824);
  padding: 15px;
}

.echarts-pie {
  width: 208px;
  height: 208px;
  margin: 0 auto;
}

.pie-example {
  display: flex;
  justify-content: space-around;
}

.pie-example-box {
  display: flex;
}

.pie-example-box .icon-box {
  display: block;
  width: 36px;
  height: 36px;
  margin: 0 auto 13px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 36px;
}

.icon-normal {
  background-image: url("../../../assets/wisdom/icon-chart1.png");
}
.light-icon-normal {
  background-image: url("../../../assets/wisdom/light-icon-chart1.png");
}

.icon-degradation {
  background-image: url("../../../assets/wisdom/icon-chart2.png");
}

.icon-end {
  background-image: url("../../../assets/wisdom/icon-chart3.png");
}

.example-right {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 5px;
}

.example-header {
  display: flex;
  align-items: center;
}

.example-title {
  font-size: 14px;
  color: var(--th_font_color, #303748) !important;
  text-align: center;
  height: 14px;
  line-height: 14px;
}

.example-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--th_font_color, #303748) !important;
  height: 24px;
  line-height: 24px;
  margin-left: 5px;
}

.example-beliel {
  text-align: center;
  font-size: 16px;
  color: #5ca0d5;
  height: 18px;
  line-height: 18px;
}

.pie-example-hover {
  cursor: pointer;
}

.task-modal .ivu-modal-body {
  font-size: 14px !important;
}

.task-modal .ivu-modal-body .taskStrategy-modal a,
.task-modal .ivu-modal-body .taskStrategy-modal .ivu-page-total {
  font-size: 14px !important;
  vertical-align: middle;
}

.task-modal .ivu-modal-body .taskStrategy-modal .snmp-content-right {
  font-size: 14px;
}

.wisdom-fault .wisdom-fault-top .fault-top-box .ivu-picker-confirm button {
  width: auto !important;
  height: auto !important;
}

.btnChange > a {
  display: inline-block;
  vertical-align: top;
  width: 120px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  /* color: #484b56; */
  font-weight: bold;
  /* border: solid 1px #e6e8ee; */

  color: var(--btnChange_color, #5ca0d5);
  border: solid 1px var(--btnChange_border_color, #02b8fd);
}

.btnChange > a.active {
  /* color: #fff; */
  /* background-color: #4e7bff; */
  /* border-color: #4e7bff; */

  color: var(--btnChange_active_color, #060d15);
  background-color: var(--btnChange_active_bg_color, #4e7bff);
  border: 0px;
}

.my-table-fixed .ivu-table-fixed {
  /*width:auto!important*/
}

.headerSelectArea .ivu-select-selection {
  border: none;
  background-color: #f1f6fe;
}

.headerSelectArea.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
</style>
<style  lang='less'>
.topTable td {
  background-color: var(--table_td_bg_color, #061824) !important;
  // border-bottom:1px solid #06324D !important;
  height: 45px !important;
}

.lookBox .title {
  margin: 0px !important;
}
</style>

