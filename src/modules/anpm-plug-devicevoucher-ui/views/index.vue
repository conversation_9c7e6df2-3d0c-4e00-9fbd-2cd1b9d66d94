<template>
  <section class="sectionBox">
    <!--  凭证管理页面  -->
    <div class="section-top">
      <!--  查询条件    -->
      <div class="fn_box">
        <div class="fn_item">
          <label class="fn_item_label">{{ $t("comm_keywords") }}：</label>
          <div class="fn_item_box">
            <Input
              v-model.trim="name"
              :placeholder="$t('device_discovery_select_credentials')"
              :title="$t('device_discovery_select_credentials')"
              style="width: 240px"
            />
          </div>
        </div>
      </div>
      <!--面板功能按键区域     -->
      <div class="tool-btn">
        <div
          class="btn-placehoder"
          v-if="
            !permissionObj.list && !permissionObj.add && !permissionObj.delete
          "
        ></div>
        <div>
          <!-- <Button class="query-btn" type="primary" icon="ios-search" @click="queryClick(1)" :title="$t('common_query')"></Button> -->
          <Button
            class="jiaHao-btn"
            type="primary"
            v-if="permissionObj.list"
            @click="queryClick(1)"
            :title="$t('common_query')"
          >
            <i class="iconfont icon-icon-query" />
          </Button>
          <Button
            class="query-btn"
            type="primary"
            v-if="permissionObj.add"
            icon="md-add"
            @click="openModal"
            :title="$t('common_new')"
          ></Button>
          <Button
            class="delete-btn"
            type="primary"
            v-if="permissionObj.delete"
            @click="rowRemove('deleteAll')"
            :title="$t('common_delete')"
          >
            <i class="iconfont icon-icon-del" style="color: #fe5c5c" />
          </Button>
          <!-- <Dropdown @on-click="moreBtnClick">
                        <Button class="more-btn"  @click='rowRemove("deleteAll")'>
                            删除
                        </Button>
                    </Dropdown> -->
        </div>
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body" style="padding: 0">
      <Loading :loading="pageLoading"></Loading>
      <div class="section-body-content">
        <div>
          <Table
            ref="tablelist"
            stripe
            :columns="tableColumn"
            class="fixed-left-right"
            :data="tableData"
            :no-data-text="
              pageData.total > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
          </Table>
          <!-- v-if="pageData.total > 0" -->
          <div class="tab-page" style="border-top: 0">
            <Page
              v-page
              :current.sync="page.pageNo"
              :page-size="page.pageSize"
              :total="pageData.total"
              :page-size-opts="page.pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <Modal
      :title="title"
      sticky
      v-model="modalShow"
      width="900"
      draggable
      :mask="true"
      :mask-closable="false"
      @on-ok="addSubmit"
      @on-cancel="cancleForm"
    >
      <Form
        ref="modalForm"
        v-if="modalShow"
        :model="modalForm"
        :rules="addRuleFormRule"
        class="width_50_Form"
        @submit.native.prevent
        :label-width="130"
      >
        <!-- <FormItem label="凭证类型：" prop="type">
                    <Select v-model="modalForm.type" filterable clearable placeholder="请填选择凭证类型">
                        <Option value="Telnet/SSH">Telnet/SSH</Option>
                        <Option value="Windows/WMI">Windows/WMI</Option>
                        <Option value="SNMP">SNMP</Option>
                        <Option value="非标协议">非标协议</Option>
                    </Select>
                </FormItem> -->
        <!--  prop="orgId"  -->
        <FormItem
          :label="$t('comm_org') + $t('comm_colon')"
          v-if="title === this.$t('common_new')"
          prop="orgId"
        >
          <div class="fn_item_box">
            <TreeSelect
              v-model="treeValue"
              style="width: 90%"
              ref="TreeSelect"
              :data="treeData"
              :placeholder="$t('snmp_pl_man')"
              :loadData="loadOrgTreeData"
              @onSelectChange="setOrg"
              @onClear="onClear"
              @onFocus="focusFn"
            >
            </TreeSelect>
            <!-- <Select class="noIcon" v-model="modalForm.orgId" disabled style="width:calc(100% - 65px)" placeholder="请选择机构">
                            <Option v-for="item in orgLists" :value="item.id" :key="item.id">{{ item.name }}</Option>
                        </Select>
                        <Button type="info" style="margin-left: 5px;width: 60px" @click.stop="choicesOrg()">{{$t('but_choose')}}</Button>
                        <div class="orgDiv" id="selectBox" v-show="orgTreeModal" style="right: 0">
                            <div class="title" style="line-height: 32px">机构选择</div>
                            <div class="orgTreeScroll">
                                <Tree :data="treeData" :load-data="loadOrgTreeData" @on-select-change="setOrg"></Tree>
                            </div>
                        </div> -->
          </div>
        </FormItem>
        <!--  prop="orgName" -->
        <FormItem
          :label="$t('comm_org') + $t('comm_colon')"
          v-if="title === this.$t('common_update')"
          prop="orgId"
        >
          <Input
            v-model="modalForm.orgName"
            style="width: 90%"
            disabled
          ></Input>
        </FormItem>
        <FormItem
          :label="$t('device_discovery_credentials') + $t('comm_colon')"
          prop="name"
          class="width_50_item"
        >
          <Input
            v-model="modalForm.name"
            maxlength="50"
            style="width: 90%"
            :placeholder="$t('device_discovery_fill_credentials')"
          ></Input>
        </FormItem>
        <FormItem
          :label="$t('phytopo_desc') + $t('comm_colon')"
          class="width_100_item"
        >
          <Input
            type="textarea"
            style="width: 96%"
            v-model="modalForm.describe"
            maxlength="200"
          ></Input>
        </FormItem>

        <FormItem
          :label="$t('snmptask_SNMP_port') + $t('comm_colon')"
          prop="snmpPort"
          class="width_50_item"
        >
          <Input
            v-model="modalForm.snmpPort"
            style="width: 90%"
            maxlength="6"
          ></Input>
        </FormItem>
        <FormItem
          :label="$t('snmptask_SNMP') + $t('comm_colon')"
          prop="snmpVersion"
          class="width_50_item"
        >
          <Select
            style="width: 90%"
            v-model="modalForm.snmpVersion"
            :placeholder="$t('device_discovery_credentials_type')"
          >
            <Option value="0">SNMP v1</Option>
            <Option value="1">SNMP v2</Option>
            <Option value="3">SNMP v3</Option>
          </Select>
        </FormItem>

        <FormItem
          label="Community："
          prop="snmpCommunity"
          class="width_50_item"
          v-if="modalForm.snmpVersion != '3'"
        >
          <Input
            style="width: 90%"
            v-model="modalForm.snmpCommunity"
            maxlength="32"
          ></Input>
        </FormItem>

        <FormItem
          :label="$t('snmptask_security_level') + $t('comm_colon')"
          prop="securityLevel"
          class="width_50_item"
          v-if="modalForm.snmpVersion == '3'"
        >
          <Select
            style="width: 90%"
            v-model="modalForm.securityLevel"
            :placeholder="$t('device_discovery_select_security')"
            @on-change="securityLevelChange"
          >
            <Option value="1">{{ $t("device_discovery_security1") }}</Option>
            <Option value="2">{{ $t("device_discovery_security2") }}</Option>
            <Option value="3">{{ $t("device_discovery_security3") }}</Option>
          </Select>
        </FormItem>
        <FormItem
          :label="$t('backup_user_name') + $t('comm_colon')"
          prop="userName"
          class="width_50_item"
          v-if="modalForm.snmpVersion == '3'"
        >
          <Input
            style="width: 90%"
            v-model="modalForm.userName"
            maxlength="30"
          ></Input>
        </FormItem>
        <!-- 上下文名称 -->
        <div class="snmptask-context-name">
          <FormItem
            :label="$t('snmptask_context_name') + $t('comm_colon')"
            style="width: 97%"
            v-if="modalForm.snmpVersion == '3'"
          >
            <Input v-model="modalForm.contextName" maxlength="30"></Input>
          </FormItem>
        </div>

        <div>
          <FormItem
            :label="$t('device_discovery_verify_protocol') + $t('comm_colon')"
            prop="verifyProtocol"
            class="width_50_item"
            v-if="modalForm.snmpVersion == '3'"
          >
            <Select
              style="width: 90%"
              v-model="modalForm.verifyProtocol"
              :disabled="
                !modalForm.securityLevel || modalForm.securityLevel == 1
              "
              :label="$t('device_discovery_select_protocol')"
            >
              <Option value="1">MD5</Option>
              <Option value="2">SHA</Option>
            </Select>
          </FormItem>
          <FormItem
            :label="$t('device_discovery_verify_password2') + $t('comm_colon')"
            prop="password"
            :rules="password"
            class="width_50_item"
            v-if="modalForm.snmpVersion == '3'"
          >
            <Input
              style="width: 90%"
              v-model="modalForm.password"
              maxlength="30"
              :disabled="
                !modalForm.securityLevel || modalForm.securityLevel == 1
              "
            ></Input>
          </FormItem>
          <FormItem
            :label="$t('snmptask_private_protocol')"
            prop="privateProtocol"
            class="width_50_item"
            v-if="modalForm.snmpVersion == '3'"
          >
            <Select
              style="width: 90%"
              v-model="modalForm.privateProtocol"
              :disabled="
                !modalForm.securityLevel || modalForm.securityLevel != 3
              "
              :placeholder="$t('snmptask_select_private_protocol')"
            >
              <Option value="1">DES</Option>
              <Option value="2">AES-128</Option>
              <Option value="3">AES-256</Option>
            </Select>
          </FormItem>
          <FormItem
            :label="$t('snmptask_encryption_password')"
            prop="encipherPassword"
            :rules="encipherPassword"
            class="width_50_item"
            v-if="modalForm.snmpVersion == '3'"
          >
            <Input
              style="width: 90%"
              v-model="modalForm.encipherPassword"
              maxlength="30"
              :disabled="
                !modalForm.securityLevel || modalForm.securityLevel != 3
              "
            ></Input>
          </FormItem>
        </div>

        <!-- <div style="width:670px;margin-left:120px;height:300px;overflow-y:auto;">
                    <FormItem label="SNMP端口：" prop="snmpPort" class="width_100_item">
                        <Input v-model="modalForm.snmpPort" maxlength="6" style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="SNMP版本：" prop="snmpVersion" class="width_100_item">
                        <Select v-model="modalForm.snmpVersion" placeholder="请填选择凭证类型" style="width: 50%;">
                            <Option value="0">SNMP v1</Option>
                            <Option value="1">SNMP v2</Option>
                            <Option value="3">SNMP v3</Option>
                        </Select>
                    </FormItem>

                    <FormItem label="Community：" prop="snmpCommunity" class="width_100_item" v-if="modalForm.snmpVersion!='3'">
                        <Input v-model="modalForm.snmpCommunity" maxlength="32" style="width: 50%;"></Input>
                    </FormItem>

                    <FormItem label="安全级别：" prop="securityLevel" class="width_100_item" v-if="modalForm.snmpVersion=='3'">
                        <Select v-model="modalForm.securityLevel" placeholder="请填选安全级别" style="width: 50%;" @on-change="securityLevelChange">
                            <Option value="1">无验证、无加密</Option>
                            <Option value="2">验证、无加密</Option>
                            <Option value="3">验证、加密</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="用户名：" prop="userName" class="width_100_item" v-if="modalForm.snmpVersion=='3'">
                        <Input v-model="modalForm.userName" maxlength="30" style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="上下文名称：" class="width_100_item" v-if="modalForm.snmpVersion=='3'">
                        <Input v-model="modalForm.contextName" maxlength="30" style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="验证密码：" prop="password" :rules="password" class="width_100_item" v-if="modalForm.snmpVersion=='3'">
                        <Input v-model="modalForm.password" maxlength="30" :disabled="!modalForm.securityLevel  || modalForm.securityLevel == 1"
                               style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="验证协议：" prop="verifyProtocol" class="width_100_item" v-if="modalForm.snmpVersion=='3'">
                        <Select v-model="modalForm.verifyProtocol" :disabled="!modalForm.securityLevel  || modalForm.securityLevel == 1"
                                placeholder="请选择验证协议" style="width: 50%;">
                            <Option value="1">MD5</Option>
                            <Option value="2">SHA</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="加密密码：" prop="encipherPassword" :rules="encipherPassword" class="width_100_item"
                              v-if="modalForm.snmpVersion=='3'">
                        <Input v-model="modalForm.encipherPassword" maxlength="30"
                               :disabled="!modalForm.securityLevel  || modalForm.securityLevel != 3" style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="私有协议：" prop="privateProtocol" class="width_100_item" v-if="modalForm.snmpVersion=='3'">
                        <Select v-model="modalForm.privateProtocol" :disabled="!modalForm.securityLevel  || modalForm.securityLevel != 3"
                                placeholder="请选择选私有协议：" style="width: 50%;">
                            <Option value="1">DES</Option>
                            <Option value="2">AES-128</Option>
                        </Select>
                    </FormItem>

                    <FormItem label="协议：" prop="agreement" class="width_100_item">
                        <Select v-model="modalForm.agreement" filterable clearable placeholder="请填选择凭证类型" style="width: 50%;">
                            <Option value="SSH">SSH</Option>
                            <Option value="Telnet">Telnet</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="端口：" prop="port" class="width_100_item">
                        <Input v-model="modalForm.port" maxlength="6" style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="超时时间（秒）：" prop="time" class="width_100_item formTime">
                        <Input v-model="modalForm.time" style="width: 50%;"></Input>pageData
                    </FormItem>
                    <FormItem label="用户名：" prop="userName" class="width_100_item">
                        <Input v-model="modalForm.userName" maxlength="50" placeholder="请输入用户名" style="width: 50%;"></Input>
                    </FormItem>
                    <FormItem label="禁用Sudo权限：" class="width_100_item">
                        <Checkbox v-model="modalForm.interest" style="margin-top: 8px;"></Checkbox>
                    </FormItem>
                    <FormItem label="使用共有密钥进行认证：" v-if="modalForm.agreement!='Telnet'" class="width_100_item formKey">
                        <Checkbox v-model="modalForm.secretKey" style="margin-top: 8px;"></Checkbox>
                    </FormItem>
                    <FormItem label="密码：" prop="pwd" class="width_100_item">
                        <Input v-model="modalForm.pwd" style="width: 50%;"></Input>
                    </FormItem>
                </div> -->
      </Form>

      <div slot="footer">
        <Button type="error" size="large" @click="cancleForm">{{
          $t("common_cancel")
        }}</Button>
        <Button type="primary" size="large" @click="addSubmit">{{
          $t("but_confirm")
        }}</Button>
      </div>
    </Modal>
  </section>
</template>
<script>
import moment from 'moment';
import '@/config/page.js';
import global from '../../../common/global.js';
import { mapGetters, mapActions, mapState } from 'vuex';
import { addDraggable } from '@/common/drag.js';
import locationreload from '@/common/locationReload';
import TreeSelect from '@/common/treeSelect/treeSelect.vue';
export default {
    name: 'discoverRule',
    components: { TreeSelect },
    watch: {},
    data() {
        return {
                currentSkin: sessionStorage.getItem('dark') || 1,
            treeValue: '',
            // 机构
            orgLists: [],
            orgTreeModal: false,
            treeData: [],
            //----------------
            name: '',
            // 表头
            tableColumn: [
                {
                    type: 'selection',
                    width: 30,
                    align: 'center',
                    // fixed:'left'
                },
                {
                    title: this.$t('comm_org'),
                    key: 'orgName',
                    align: 'left',
                    minWidth: 200,
                    tooltip: true
                },
                {
                    title: this.$t('device_discovery_credentials'),
                    key: 'name',
                    align: 'left',
                    minWidth: 300,
                    tooltip: true
                },
                {
                    title: this.$t('comm_describe'),
                    key: 'describe',
                    align: 'left',
                    minWidth: 500,
                    tooltip: true
                },
                {
                    title: this.$t('common_controls'),
                    slot: 'action',
                    width: 120,
                    align: 'center',
                    // fixed: "right",
                    render: (h, params) => {
                        let edit=
                            h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                        transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: this.currentSkin == 1 ?'edit1-btn':'light-edit1-btn',
                                         style: {
                                            display: this.permissionObj.update
                                                ? "inline-block"
                                                : "none",
                                            marginRight: '0px',
                                        },
                                        on: {
                                            click: () => {
                                                this.openEditForm(params.row);
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('common_update'))
                                ]
                            ),
                            del = h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                        transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: 'del1-btn',
                                         style: {
                                            display: this.permissionObj.delete
                                                ? "inline-block"
                                                : "none",
                                            marginRight: '0px',
                                        },
                                        on: {
                                            click: () => {
                                                this.rowRemove('delete', params.row.id);
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('common_delete'))
                                ]
                            ),
                            array = [edit, del];
                            return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                        
                    }
                }
            ],
            pageLoading: false,
            //分页参数
            page: {
                pageNo: 1,
                pageSize: 10,
                pageSizeOpts: [10, 50, 100, 200, 500, 1000],
                pageLoading: false
            },
            /** 列表数据 */
            pageData: {
                total: 0,
                list: []
            },
            modalShow: false,
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: new Set(),
            modalForm: {
                // 默认传值
                voucherType: 3,
                // 组织
                orgId: null,
                // 组织名称
                orgName: '',
                // 凭证名称
                name: '',
                // 描述
                describe: '',
                // SNMP端口
                snmpPort: null,

                // time: '10',
                // SNMP版本
                snmpVersion: null,
                // Community
                snmpCommunity: '',
                // 安全级别
                securityLevel: '',
                // 用户名
                userName: '',
                // 上下文
                contextName: '',
                // 验证密码
                password: '',
                // 验证协议
                verifyProtocol: '1',
                // 加密密码
                encipherPassword: '',
                // 私有协议
                privateProtocol: '1'
            },
            title: '',
            // 验证
            addRuleFormRule: {
                orgId: [{ required: true, type: 'number', message: this.$t('device_discovery_org_not_null'), trigger: 'change' }],
                orgName: [{ required: true, message: this.$t('device_discovery_org_not_null'), trigger: 'blur' }],
                snmpPort: [{ required: true, message: this.$t('device_discovery_snmp_not_null'), trigger: 'blur' }],
                type: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }],
                name: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }],
                snmpVersion: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }],
                // V1 v2
                snmpCommunity: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }],
                // 3
                securityLevel: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'change' }],
                userName: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }],
                // password: [{ required: false, message: '不能为空', trigger: 'blur' }],
                verifyProtocol: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }],
                // encipherPassword: [{ required: false, message: '不能为空', trigger: 'blur' }],
                privateProtocol: [{ required: true, message: this.$t('discover_cannot_be_null'), trigger: 'blur' }]
            },
            openId: '',
            password: {},
            encipherPassword: {}
        };
    },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
          // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        this.getTreeOrg();
    },
    computed:{
        tableData:{
            get : function() {
                let arr = this.pageData.list.map(item=>{
                    for (const key in item) {
                        let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                        item[key] = str
                    }
                   return item
                })
                return arr
            },
        },
    },
    created() {
      this.$nextTick(() => {
        locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());
      })
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        const orgId = JSON.parse(sessionStorage.getItem('accessToken')).user.orgId;
        moment.locale('zh-cn');
        this.queryClick(1);
        
    },
    methods: {
         handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        moreBtnClick(val) {
            eval(`this.${val}`);
        },
        // 获取机构
        getTreeOrg() {
            let _self = this;
            _self.$http.PostJson('/org/tree', { orgId: null }).then(res => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item;
                    });
                    _self.orgLists = treeNodeList;
                    _self.treeData = treeNodeList;
                }
            });
        },
        loadOrgTreeData(item, callback) {
            this.$http.PostJson('/org/tree', { orgId: item.id }).then(res => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                        }
                        return item;
                    });
                    callback(childrenOrgList);
                }
            });
        },
        setOrg(item) {
            this.treeValue = item[0].name;
            this.modalForm.orgId = item[0] ? item[0].id : null;
            // this.orgLists = item;
            // this.orgTreeModal = false;
        },
       focusFn() {
        this.getTreeOrg()
        },
        onClear() {
            this.treeValue = '';
            this.modalForm.orgId = '';
        },
        choicesOrg() {
            this.orgTreeModal = true;
        },
        //查询事件
        queryClick(pageNo) {
            //初始化页码
            this.page.pageNo = pageNo;
            //设置查询参数
            const queryParam = {
                name: this.name,
                pageNo: this.page.pageNo,
                pageSize: this.page.pageSize
            };
            //打开加载动画
            this.pageLoading = true;
            //请求数据
            this.getTableList(queryParam);
        },
        getTableList(queryParam) {
            this.$http.wisdomPost('/deviceVoucher/list', queryParam).then(({ code, data, msg }) => {
                if (code === 1) {
                    this.pageLoading = false;
                    this.pageData.total = data.total;
                    this.pageData.list = data.records;
                } else {
                    this.pageLoading = false;
                    this.$Message.warning({content:msg,background:true});
                }
            });
        },
        // 新建
        openModal() {
            
            this.modalShow = true;
            this.$nextTick(()=>{
                    // this.onClear();
                    this.title = this.$t('common_new');
                    this.modalForm.name = '';
                    this.modalForm.describe = '';
                    this.modalForm.snmpPort = "161";
                    this.modalForm.snmpVersion = '1';
                    this.modalForm.snmpCommunity = '';
                    this.modalForm.securityLevel = '';
                    this.modalForm.userName = '';
                    this.modalForm.contextName = '';
                    this.modalForm.verifyProtocol = '1';
                    this.modalForm.privateProtocol = '1';
                    this.modalForm.password = '';
                    this.modalForm.encipherPassword = '';
                })
                // this.treeValue = item[0].name;
                // this.modalForm.orgId = item[0] ? item[0].id : null;
                // 新增 机构 默认选中
                let token = JSON.parse(sessionStorage.getItem("accessToken"));
                if (token && token.user) {
                this.treeValue = token.user.orgName;
                console.log(this.treeValue,'this.treeValue')
                this.modalForm.orgId = token.user.orgId
                
                }
                
        },
        //新建请求接口
        addSubmit() {
            this.$refs['modalForm'].validate(validate => {
                if (validate) {
                    let queryData = {};
                    if (this.modalForm.snmpVersion == 3) {
                        delete this.modalForm.snmpCommunity;
                        queryData = this.modalForm;
                    } else {
                        queryData = {
                            voucherType: this.modalForm.voucherType,
                            orgId: this.modalForm.orgId,
                            name: this.modalForm.name,
                            snmpPort: this.modalForm.snmpPort,
                            snmpVersion: this.modalForm.snmpVersion,
                            snmpCommunity: this.modalForm.snmpCommunity,
                            describe: this.modalForm.describe
                        };
                    }
                    if (!this.openId) {
                        // 新建
                        this.$http.PostJson('/deviceVoucher/add', queryData).then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success({content:this.$t('device_discovery_create_success'),background:true});
                                this.modalShow = false;
                                this.queryClick(1);
                                //重置为空
                                this.onClear();
                                this.modalForm.name = '';
                                this.modalForm.describe = '';
                                this.modalForm.snmpPort = "161";
                                this.modalForm.snmpVersion = '2';
                                this.modalForm.snmpCommunity = '';
                                this.modalForm.securityLevel = '';
                                this.modalForm.userName = '';
                                this.modalForm.contextName = '';
                                this.modalForm.password = '';
                                this.modalForm.encipherPassword = '';
                            } else {
                                this.$Message.warning({content:msg,background:true});
                            }
                        });
                    } else {
                        //修改
                        queryData['id'] = this.openId;
                        this.$http.PostJson('/deviceVoucher/update', queryData).then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success({content:this.$t('comm_changed_successful'),background:true});
                                this.modalShow = false;
                                this.queryClick(1);
                                //重置为空
                                this.onClear();
                                this.modalForm.name = '';
                                this.modalForm.describe = '';
                                this.modalForm.snmpPort = "161";
                                this.modalForm.snmpVersion = '2';
                                this.modalForm.snmpCommunity = '';
                                this.modalForm.securityLevel = '';
                                this.modalForm.userName = '';
                                this.modalForm.contextName = '';
                                this.modalForm.password = '';
                                this.modalForm.encipherPassword = '';
                                this.openId = '';
                            } else {
                                this.$Message.warning({content:msg,background:true});
                            }
                        });
                    }
                }
                //  else {
                //     this.$Message.warning('验证不通过');
                // }
            });
        },
        // 关闭
        cancleForm() {
            this.modalShow = false;
            this.openId = '';
            this.$refs['modalForm'].resetFields();
            this.onClear();
        },
        // 修改弹窗
        openEditForm(row) {
            this.getTreeOrg();
            this.title = this.$t('common_update');
            this.$http.get('/deviceVoucher/get?id=' + row.id).then(({ code, data, msg }) => {
                if (code === 1) {
                    this.$nextTick(() => {
                        const obj = JSON.parse(JSON.stringify(data));
                        this.modalForm = obj;
                        this.modalForm.orgId = data.orgId;
                        this.modalForm.orgName = data.orgName;
                        this.modalForm.snmpPort = String(data.snmpPort);
                        this.openId = row.id;
                        this.modalForm.snmpVersion = String(obj.snmpVersion);
                        if(obj.securityLevel && obj.securityLevel != 0){
                            this.modalForm.securityLevel = String(obj.securityLevel);
                        }else{
                            this.modalForm.securityLevel = '';
                        }
                        
                        if(obj.verifyProtocol){
                            this.modalForm.verifyProtocol = String(obj.verifyProtocol);
                        }else{
                            this.modalForm.verifyProtocol = '1';
                        }
                        if(obj.privateProtocol){
                            this.modalForm.privateProtocol = String(obj.privateProtocol);
                        }else{
                            this.modalForm.privateProtocol = '1';
                        }
                        this.modalShow = true;
                    });
                } else {
                    this.$Message.error({content:msg,background:true});
                }
            });
        },
        /** 删除 */
        rowRemove(type, data) {
            let ids = [];
            if (type == 'delete') {
                ids.push(data);
            } else {
                // 将对象转换成数组
                ids = Array.from(this.selectedIds);
            }

            if (ids.length == 0) {
                this.$Message.warning({content:this.$t('device_discovery_select_del'),background:true});
                return;
            }
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: this.$t('discover_msg_delete'),
                onOk: () => {
                    this.$http
                        .PostJson('/deviceVoucher/delete', ids)
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success({content:this.$t('common_controls_succ'),background:true});
                                this.queryClick(1);
                                this.selectedIds = new Set();
                            } else {
                                this.$Message.warning({content:msg,background:true});
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        });
                }
            });
        },
        // 取消一项
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
        },
        // 单独勾选
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
        },
        // 全选
        handleSelectAll(slection) {
            if (slection.length === 0) {
                this.selectedIds = new Set();
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                });
            }
        },
        //数据列表页码切换
        pageChange(pageNo) {
            this.queryClick(pageNo);
        },
        //数据列表页码大小改变
        pageSizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.queryClick(this.page.pageNo);
        },
        //snmp版本选择时
        securityLevelChange(val) {
            //验证密码：password，加密密码： encipherPassword
            let passwordSuc = {};
            let encipherPasswordSuc = {};
            if (val === 1) {
                passwordSuc = {};
                encipherPasswordSuc = {};
            } else if (val === '2') {
                passwordSuc = { required: true, message: this.$t('device_discovery_verify_pass'), trigger: 'blur' };
                encipherPasswordSuc = {};
            } else if (val === '3') {
                passwordSuc = { required: true, message: this.$t('device_discovery_verify_pass'), trigger: 'blur' };
                encipherPasswordSuc = { required: encipherPasswordSuc, message: this.$t('snmptask_encryption_empty'), trigger: 'blur' };
            }
            this.password = passwordSuc;
            this.encipherPassword = encipherPasswordSuc;
        }
    },
    destroyed() {}
};
</script>

<style lang='less'>
//  /deep/.formTime {
//     > label {
//         width: 140px !important;
//         margin-left: -22px !important;
//     }
// }
</style>
<style scoped lang="less">
</style>


