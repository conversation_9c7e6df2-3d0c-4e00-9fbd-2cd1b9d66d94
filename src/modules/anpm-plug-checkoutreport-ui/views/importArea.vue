<template>
  <section>
    <Form
      v-show="step"
      :model="importQuery"
     
      @submit.native.prevent
      ref="importForm"
      class="checkoutreport"
    >
     <!-- :label-width="110" -->
      <div style="position: relative">
        <!--PPTX模板-->
        <!-- <FormItem label="PPTX模板" style="margin-bottom: 20px">
        </FormItem> -->
        <div class="ivu-form-item" style="margin-bottom: 20px;">
        <label class="ivu-form-item-label" :style="templateStyle">{{$t('test_report_pptx_template')}}</label> 
        <div class="ivu-form-item-content" style="margin-left: 100px;"> 
        <!---->
        </div>
        </div>
        <FormItem :label="$t('test_report_current_template')" style="margin-bottom: 15px;" :label-width="labelWidth">
            <span>{{ pptxFileTemplateName }}</span>
            <div  class="downBox" v-if="pptxFileTemplateName">
              <div class="downTemplate">
                <img   :src='fileDown' alt="" />
                <span  @click="pptxTemplateDown" ref="templateDown"> {{$t('comm_download_tpl')}}</span>
              </div>
            </div>
            <span v-if="!pptxFileTemplateName" >{{$t('test_report_no_template')}}</span>

        </FormItem>
         <FormItem :label="$t('test_report_update_template')" style="margin-bottom: 5px" :label-width="labelWidth">
        <div class="templateUpdate">
            <span class="uploadText">{{ pptxFileName }}</span>
            <Upload :before-upload="handlePptxUpload" :show-upload-list="false" accept=".pptx" :action="$baseUrl + '/checkoutreport/uploadPptxFile'">
                <Button type="primary" style="margin-left:10px;" class="chooseBtn">{{$t('comm_select_file')}}</Button>
            </Upload>
            <Button type="primary" class="updateBtn" @click="uploadPptxFile" style="margin-left:10px;" >{{$t('test_report_update_template')}}
            </Button>
        </div>
        </FormItem>
        <FormItem style="margin-bottom: 5px" :label-width="labelWidth">
          <span class="tips">{{$t('test_report_prompt_pptx_size')}}</span>
        </FormItem>
        <!--{{$t('test_report_docx_template')}}-->
         <!-- <FormItem label="{{$t('test_report_docx_template')}}" style="margin-bottom: 20px">
        </FormItem> -->
        <div class="ivu-form-item" style="margin-bottom: 20px;">
        <label class="ivu-form-item-label" :style="templateStyle">{{$t('test_report_docx_template')}}</label> 
        <div class="ivu-form-item-content" style="margin-left: 100px;"> 
        <!---->
        </div>
        </div>
        <FormItem :label="$t('test_report_current_template')" style="margin-bottom: 15px" :label-width="labelWidth">
            <span>{{ docxFileTemplateName }}</span>
            <div  class="downBox"  v-if="docxFileTemplateName">
              <div class="downTemplate" >
                <img   :src='fileDown' alt="" />
                <span  @click="docxTemplateDown" ref="templateDown"> {{$t('comm_download_tpl')}}</span>
              </div>
            </div>
            <span v-if="!docxFileTemplateName" >
            {{$t('test_report_no_template')}}</span>
        </FormItem>
        <FormItem :label="$t('test_report_update_template')" style="margin-bottom: 5px" :label-width="labelWidth">
        <div class="templateUpdate">
            <span class="uploadText">{{ docxFileName }}</span>
            <Upload :before-upload="handleDocxUpload" :show-upload-list="false" accept=".docx" :action="$baseUrl + '/checkoutreport/uploadDocxFile'">
                <Button type="primary" style="margin-left:10px;" class="chooseBtn">{{$t('comm_select_file')}}</Button>
            </Upload>
            <Button type="primary" class="updateBtn" @click="uploadDocxFile" style="margin-left:10px;">{{$t('test_report_update_template')}}
            </Button>
        </div>
        </FormItem>
        <FormItem :label-width="labelWidth">
          <span class="tips">{{$t('test_report_prompt_docx_size')}}</span>
        </FormItem>
      </div>
    </Form>
  </section>
</template>

<script>
import axios from "axios";
import { fileDown } from "../../../assets/base64Img/img.js";
export default {
  name: "importArea",
  props: {
    imData: {},
    steps: {},
  },
  data() {
    return {
      fileDown:fileDown,
      butLoading:false,
      importQuery: {
        province: "",
        city: "",
      },
      stepTwo: {
        province: "",
        city: "",
      },
      uploadFiles: "",
      step: true,
      abnormal: false,
      importData: {},
      provinceList: [],
      cityList: [],
      // 中文 100px
      // 英文 150px
      labelWidth:150,
      // 中文 100px
      // 英文 130px
      templateStyle:"width: 100px;",
      

      btnSendTem: false,
      loading: false,
      loadingText: "",
      repeatExcel: [],
      single: false,
      exportExcle: {},
      allExcel: {},
      importOkFlag: false,
      pptxFile: '',
      pptxFileName: '',
      docxFile: '',
      docxFileName: '',
      loadingPptxFile: false,
      loadingDocxFile: false,
      pptxFileTemplateName:'',
      docxFileTemplateName:'',
    };
  },
  watch: {
    imData: {
      handler(val) {
        this.importData = Object.assign({}, val);
      },
      deep: true,
    },
    steps: {
      handler(val) {
        this.step = val;
      },
      deep: true,
    },
  },
  created() {
        this.getTemplateInfo();
        // 
        let locale=localStorage.getItem('locale');
        if(locale == 'zh'){
          this.labelWidth = 100;
            this.templateStyle ="width: 100px;";
        }else{
          this.labelWidth = 150;
           this.templateStyle ="width: 130px;";
        }
  },
  methods: {
    // 获取模板名称
    getTemplateInfo() {
      this.$http.post('/checkoutreport/getTemplateInfo').then(res => {
          if (res.code == 1) {
            this.pptxFileTemplateName = res.data.pptxFileName;
            this.docxFileTemplateName = res.data.docxFileName;
          } else {
        }
      });
    },
    //pptx模板下载
    pptxTemplateDown() {
      this.btnSendTem = true;
      // 加载loading
      let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;
      let params = new URLSearchParams();
      params.append("token_id", token_id);
      this.$Loading.start();
      axios({
        url: "/checkoutreport/downloadPptx",
        method: "get",
        responseType: "blob", // 服务器返回的数据类型
        params:{
          templateName:this.pptxFileTemplateName
        }
      })
        .then((res) => {
          // 关闭loading
          const content = res.data;
          // 构造一个blob对象来处理数据
          const blob = new Blob([content], {
            type: "application/vnd.ms-pptx",
          });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.pptxFileTemplateName+".pptx");
          } else {
            const fileName = this.pptxFileTemplateName+".pptx"; // 导出文件名
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ("download" in document.createElement("a")) {
              // 支持a标签download的浏览器
              const _res = res.data;
              let blob = new Blob([_res]);
              let downloadElement = document.createElement("a");
              let href = window.URL.createObjectURL(blob); //创建下载的链接
              downloadElement.href = href;
              downloadElement.download = fileName; //下载后文件名
              document.body.appendChild(downloadElement);
              downloadElement.click(); //点击下载
              document.body.removeChild(downloadElement); //下载完成移除元素
              window.URL.revokeObjectURL(href); //释放掉blob对象
            } else {
              // 其他浏览器
              navigator.msSaveBlob(blob, fileName);
            }
          }
          this.btnSendTem = false;
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
          // 关闭loading
          this.btnSendTem = false;
          this.$Loading.finish();
        }).finally(()=>{
          this.$Loading.finish();
        });
    },
    //pptx模板下载
    docxTemplateDown() {
      this.btnSendTem = true;
      // 加载loading
      let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;
      let params = new URLSearchParams();
      params.append("token_id", token_id);
      this.$Loading.start();
      axios({
        url: "/checkoutreport/downloadDocx",
        method: "get",
        responseType: "blob", // 服务器返回的数据类型
        params:{
          templateName:this.docxFileTemplateName
        }
      })
        .then((res) => {
          // 关闭loading
          const content = res.data;
          // 构造一个blob对象来处理数据
          const blob = new Blob([content], {
            type: "application/vnd.ms-docx",
          });
          if ("msSaveOrOpenBlob" in navigator) {
            window.navigator.msSaveOrOpenBlob(blob, this.docxFileTemplateName+".docx");
          } else {
            const fileName = this.docxFileTemplateName+".docx"; // 导出文件名
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ("download" in document.createElement("a")) {
              // 支持a标签download的浏览器
              const _res = res.data;
              let blob = new Blob([_res]);
              let downloadElement = document.createElement("a");
              let href = window.URL.createObjectURL(blob); //创建下载的链接
              downloadElement.href = href;
              downloadElement.download = fileName; //下载后文件名
              document.body.appendChild(downloadElement);
              downloadElement.click(); //点击下载
              document.body.removeChild(downloadElement); //下载完成移除元素
              window.URL.revokeObjectURL(href); //释放掉blob对象
            } else {
              // 其他浏览器
              navigator.msSaveBlob(blob, fileName);
            }
          }
          this.btnSendTem = false;
          this.$Loading.finish();
        })
        .catch((error) => {
          console.log(error);
          // 关闭loading
          this.btnSendTem = false;
          this.$Loading.finish();
        }).finally(()=>{
          this.$Loading.finish();
        });
    },
  
    //更新ppt模板文件
    uploadPptxFile() {
            let _this = this;
            if (this.pptxFile) {
                let fileFormData = new FormData();
                fileFormData.append('files', this.pptxFile);
                _this.loadingPptxFile = true;
                axios({
                    url: '/checkoutreport/uploadPptxFile',
                    method: 'post',
                    timeout: 300000,
                    data: fileFormData
                })
                    .then(res => {
                        let datas = res.data;
                        if (datas.code == 1) {
                            // this.getLisenceInfo();
                            this.$Message.success(this.$t('interface_update_success'));
                            this.pptxFile = null;
                            this.pptxFileName = '';
                            this.getTemplateInfo();
                        } else {
                            this.$Message.warning(datas.msg);
                        }
                        _this.loadingPptxFile = false;
                    })
                    .catch(err => {
                        _this.loadingPptxFile = false;
                    })
                    .finally(() => {
                        _this.loadingPptxFile = false;
                    });
            } else {
                this.$Message.warning(this.$t('system_prompt_select_file'));
            }
        },
  //更新docx模板文件
  uploadDocxFile() {
            let _this = this;
            if (this.docxFile) {
                let fileFormData = new FormData();
                fileFormData.append('files', this.docxFile);
                _this.loadingDocxFile = true;
                axios({
                    url: '/checkoutreport/uploadDocxFile',
                    method: 'post',
                    timeout: 300000,
                    data: fileFormData
                })
                    .then(res => {
                        let datas = res.data;
                        if (datas.code == 1) {
                            // this.getLisenceInfo();
                            this.$Message.success(this.$t('interface_update_success'));
                            this.docxFile = null;
                            this.docxFileName = '';
                            this.getTemplateInfo();
                        } else {
                            this.$Message.warning(datas.msg);
                        }
                        _this.loadingDocxFile = false;
                    })
                    .catch(err => {
                        _this.loadingDocxFile = false;
                    })
                    .finally(() => {
                        _this.loadingDocxFile = false;
                    });
            } else {
                this.$Message.warning(this.$t('system_prompt_select_file'));
            }
        },

 // 选择上传文件
    handlePptxUpload(file) {
      this.pptxFile = file;
      this.pptxFileName = file.name;
      return false;
    },   
    handleDocxUpload(file) {
      this.docxFile = file;
      this.docxFileName = file.name;
      return false;
    },  
  },
  mounted() {},
};
</script>

<style lang="less">
.formStyle.ivu-form .ivu-form-item-label {
  text-align: right !important;
}
.formStyle{
  .uploadBox{
    margin-right: 10px;
    padding: 0;
    border: none;
  }
  .uploadBut{
    span{
      color: #fff;
      display: inline-block !important;
    }
  }
}
.abnormalTop {
  display: flex;
  align-items: center;
  padding: 25px 0;
}
.abnormalTips {
  label,
  span {
    color: red;
  }
  label {
    font-weight: bold;
  }
}
</style>

