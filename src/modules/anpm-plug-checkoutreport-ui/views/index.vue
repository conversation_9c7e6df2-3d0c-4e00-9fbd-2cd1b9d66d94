<template>
  <div :class="{ 'light-no-tabs': currentSkin == 0 }">
    <section class="sectionBox">
      <!-- 报告生成策略 页面-->
      <div class="section-top">
        <Row class="fn_box">
          <Col span="4">
            <div class="fn_item" style="width: 320px">
              <label class="fn_item_label"
                >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <TreeSelect
                  v-model="treeValue"
                  ref="TreeSelect"
                  :data="treeData"
                  :placeholder="$t('comm_please_select')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg"
                  @onClear="onClear"
                  @onFocus="focusFn(1)"
                >
                </TreeSelect>
              </div>
            </div>
          </Col>
          <Col span="4" style="margin-left: 120px">
            <div class="fn_item" style="width: 320px">
              <label class="fn_item_label"
                >{{ $t("test_report_file_format")
                }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.fileFormat"
                  clearable
                  :placeholder="$t('test_report_select_file_format')"
                >
                  <Option
                    v-for="item in fileFormatList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col>
          <Col span="4" style="margin-left: 100px">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("shieldlist_fgt") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <DatePicker
                  format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  :options="timeOptionsTwo"
                  v-model="timeRange"
                  :editable="false"
                  :clearable="false"
                  style="width: 300px"
                  :confirm="false"
                  @on-change="dateChange"
                >
                </DatePicker>
              </div>
            </div>
          </Col>
          <Col span="4">
            <div class="fn_item" style="width: 320px; margin-left: 180px">
              <label class="fn_item_label"
                >{{ $t("comm_status") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.status"
                  filterable
                  :only-filter-with-text="true"
                  clearable
                  :placeholder="$t('gether_pl_status')"
                >
                  <Option
                    v-for="item in statusList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
              </div>
            </div>
          </Col>
        </Row>
        <Row>
          <Col span="7">
            <div class="fn_item" style="width: 320px">
              <label class="fn_item_label"
                >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Input
                  v-model.trim="query.keyWord"
                  :placeholder="$t('comm_report_name')"
                />
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn">
          <div>
            <Button
              class="jiaHao-btn"
              type="primary"
              v-if="permissionObj.list"
              @click="queryDialList"
              :title="$t('common_query')"
            >
              <i class="iconfont icon-icon-query" />
            </Button>

            <Button
              class="query-btn"
              type="primary"
              v-if="permissionObj.add"
              icon="md-add"
              @click="eidtClick('add')"
              :title="$t('common_new')"
            ></Button>
            <Dropdown
              @on-click="moreBtnClick"
              v-if="permissionObj.delete || permissionObj.config"
            >
              <Button class="more-btn">
                {{ $t("dashboard_more") }}
                <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                <!-- <DropdownItem name='downloadFile()' v-if="permissionObj.download">下载</DropdownItem> -->
                <DropdownItem name="deleteData(2)" v-if="permissionObj.delete"
                  >{{ $t("but_delete") }}
                </DropdownItem>
                <DropdownItem name="configData(2)" v-if="permissionObj.config"
                  >{{ $t("test_report_disposition") }}
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>

      <div class="section-body" style="padding: 0">
        <div class="section-body-content">
          <div>
            <Loading :loading="loading"></Loading>
            <Table
              ref="tableList"
              stripe
              :columns="columns"
              :data="tabList"
              @on-select="handleSelect"
              @on-select-cancel="handleCancel"
              @on-select-all="handleSelectAll"
              @on-select-all-cancel="handleSelectAll"
              :no-data-text="
                loading
                  ? ''
                  : tabList.length > 0
                  ? ''
                  : currentSkin == 1
                  ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
                  : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                    $t('common_No_data') +
                    '</p></div>'
              "
            >
              <template slot-scope="{ row }" slot="status">
                <span v-if="row.status == 0" style="color: #fc432a">{{
                  $t("test_report_build_failure")
                }}</span>
                <span v-else-if="row.status == 1" style="color: #0285bd"
                  >{{ $t("test_report_generating")
                  }}{{ row.estimatedTimeStr }}</span
                >
                <span v-else-if="row.status == 2" style="color: #07c5a3">{{
                  $t("test_report_generated")
                }}</span>
                <span v-else>--</span>

                <Tooltip
                  max-width="180"
                  :content="row.failInfo"
                  placement="right"
                >
                  <Icon
                    type="ios-information-circle"
                    v-if="row.failInfo && row.status == 0"
                    style="color: red"
                  />
                </Tooltip>
              </template>
            </Table>
          </div>
        </div>
        <div class="tab-page" style="border-top: 0" v-if="tabList.length > 0">
          <Page
            v-page
            :current.sync="query.pageNo"
            :page-size="query.pageSize"
            :total="totalCount"
            :page-size-opts="pageSizeOpts"
            :prev-text="$t('common_previous')"
            :next-text="$t('common_next_page')"
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
            show-elevator
            show-sizer
          >
          </Page>
        </div>
      </div>

      <!--新建 编辑 弹窗-->
      <Modal
        sticky
        v-model="editShow"
        :title="editTitle"
        draggable
        :mask="true"
        width="800"
        class="eidtModal"
      >
        <Form
          ref="editForm"
          :model="editFormData"
          :rules="editFormValidate"
          :label-width="120"
        >
          <Row class="fn_box">
            <Col span="12">
              <!--  prop="orgId" -->
              <FormItem
                class=""
                :label="$t('comm_org') + $t('comm_colon')"
                prop="orgId"
              >
                <TreeSelect
                  v-model="treeValue1"
                  ref="TreeSelect"
                  :data="treeData2"
                  :placeholder="$t('snmp_pl_man')"
                  :loadData="loadOrgTreeData"
                  @onSelectChange="setOrg2"
                  @onClear="onClear2"
                  @onFocus="focusFn(2)"
                  style="width: 250px"
                >
                </TreeSelect>
              </FormItem>
            </Col>
            <FormItem
              :label="$t('comm_report_name') + $t('comm_colon')"
              prop="name"
              :label-width="150"
            >
              <Input
                v-model="editFormData.name"
                :placeholder="$t('phytopo_enter_name')"
                style="width: 220px"
                maxlength="50"
              ></Input>
            </FormItem>
          </Row>
          <!-- <FormItem label="报告名称：" prop="name">
                    <Input v-model="editFormData.name" placeholder="请输入名称" style="width:50%" maxlength="50"></Input>
                </FormItem> -->
          <FormItem
            :label="$t('test_report_label_statistical_time')"
            prop="timeRange"
          >
            <DatePicker
              format="yyyy-MM-dd "
              type="daterange"
              :options="timeOptions"
              v-model="editFormData.timeRange"
              :editable="false"
              :clearable="false"
              style="width: 250px"
              :confirm="false"
            >
            </DatePicker>
          </FormItem>
          <FormItem :label="$t('test_report_label_group')">
            <Select
              ref="serviceStatusClear"
              v-model="editFormData.groupIds"
              multiple
              :max-tag-count="1"
              clearable
              :placeholder="$t('comm_select_group')"
              style="width: 250px"
            >
              <Option
                v-for="item in groupList"
                :value="item.id"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem
            :label="$t('test_report_file_format')"
            prop="strFileFormat"
            class="implication error"
          >
            <p>
              <CheckboxGroup v-model="editFormData.strFileFormat">
                <Checkbox label="0">pptx</Checkbox>
                <Checkbox label="1">docx</Checkbox>
              </CheckboxGroup>
            </p>
          </FormItem>
          <FormItem
            :label="$t('comm_report_provider') + $t('comm_colon')"
            prop="provider"
            style="width: 750px"
          >
            <Input
              v-model="editFormData.provider"
              :placeholder="$t('report_please_provider')"
              maxlength="50"
            ></Input>
          </FormItem>
          <FormItem
            :label="$t('test_report_describe') + $t('comm_colon')"
            prop="remarks"
            style="width: 750px"
          >
            <Input
              v-model="editFormData.remarks"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 5 }"
              :placeholder="$t('comm_describe')"
              maxlength="200"
              show-word-limit
            ></Input>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button
            type="error"
            style="margin-right: 20px"
            @click="editShow = false"
            >{{ $t("common_cancel") }}</Button
          >
          <Button
            class="custom-button"
            @click="submitEditForm"
            :loading="submitLoading"
            style="width: 100px"
            >{{ $t("test_report_generate_report") }}</Button
          >
        </div>
      </Modal>

      <!--导入-->
      <Modal
        v-model="importModal.show"
        width="800"
        class="import-modal"
        :title="
          step
            ? $t('test_report_disposition')
            : $t('test_report_update_disposition')
        "
        :loading="importModal.loading"
        :mask="true"
        sticky
        draggable
      >
        <div slot="footer"></div>
        <!-- <section class="taskStrategy-modal">
        <import-area
          :steps="step"
          @flagChange="flagChange"
          @loadingChange="loadingChange"
          ref="importForm"
          :imData="importModal.data"
        ></import-area>
      </section> -->
        <section class="taskStrategy-modal">
          <import-area
            ref="importForm"
            :imData="importModal.data"
          ></import-area>
        </section>
      </Modal>
    </section>
  </div>
</template>

<script>
// 质量报告 / 报告生成策略
import global from '@/common/global.js';
import TreeSelect from '@/common/treeSelect/treeSelect.vue';
import validate from '@/common/validate';
import moment from 'moment';
import '@/config/page.js';
import importArea from "./importArea";

import axios from "axios";
export default {
    components: { importArea, TreeSelect },
    watch: {
        startTime(val) {
            if (val) {
                this.editFormData.startTime = new Date(val).format('yyyy-MM-dd hh:mm:ss');
            }
        },
        endTime(val) {
            if (val) {
                this.editFormData.endTime = new Date(val).format('yyyy-MM-dd hh:mm:ss');
            }
        }
    },
    data() {
        return {
                  currentSkin: sessionStorage.getItem('dark') || 1,
            submitLoading:false,
            timerInterval: null,
            step: true,
            importModal: {
                show: false,
                loading: true,
                data: {},
            },
            hasClick: false,
            groupList: [],
            treeValue: '',
            treeValue1: '',
            //权限对象
            permissionObj: {},
            treeData: [],
            treeData2: [],

            orgLists: [],
            readonly: true,
            fileFormatList: [
                { label: 'pptx', value: 0 },
                { label: 'docx', value: 1 }
            ],
            statusList: [
                { label: this.$t('test_report_build_failure'), value: 0 },
                { label: this.$t('test_report_generating'), value: 1 },
                { label: this.$t('test_report_generated'), value: 2 }
            ],
            loading: false,
            tabList: [],
            query: {
                orgId: '',
                fileFormat: '',
                status: '',
                keyWord: '',
                pageNo: 1,
                pageSize: 10
            },
            pageSizeOpts: [10, 50, 100, 200, 500, 1000],
            totalCount: 0,
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'left',
                    fixed:'left'
                },
                {
                    title: this.$t('comm_report_name'),
                    key: 'name',
                    align: 'left',
                    minWidth: 350,
                    className: 'bgColor',
                    tooltip: true
                },
                {
                    title: this.$t('comm_org'),
                    key: 'orgName',
                    align: 'left',
                    minWidth: 240,
                    className: 'bgColor',
                    tooltip: true,
                    // render: (h, params) => {
                    //     let str = params.row.orgName;
                    //     return h('span', str === undefined || str === null || str == 'null' || str === '' ? '--' : str);
                    // }
                },
                {
                    title: this.$t('test_report_file_format'),
                    key: 'fileFormatName',
                    align: 'left',
                    width: 150,
                    className: 'bgColor',
                    render: (h, params) => {
                        let str = params.row.fileFormatName;
                        return h('span', str === undefined || str === null || str == 'null' || str === '' ? '--' : str);
                    }
                },
                {
                    title: this.$t('comm_report_time_range'),
                    key: 'statisticalTimeRange',
                    align: 'left',
                    minWidth: 300,
                    className: 'bgColor',
                    tooltip: true
                },
                {
                    title: this.$t('shieldlist_fgt'),
                    key: 'createTime',
                    align: 'left',
                    minWidth: 300,
                    className: 'bgColor',
                    tooltip: true
                },
                {
                    title: this.$t('comm_status'),
                    key: 'status',
                    width: 130,
                    align: 'left',
                    className: 'bgColor',
                    slot: 'status',
                },
                {
                    title: this.$t('common_controls'),
                    width: '120',
                    align: 'center',
                    className: 'bgColor',
                    fixed:'right',
                    render: (h, params) => {
                        let className = params.row.status == '2' ? 'dow-btn' : this.currentSkin == 1 ? 'dow-btn-dis' : 'light-dow-btn-dis'
                        let down = h(
                            'Tooltip',
                            {
                                props: {
                                    placement: 'left-end',
                                    transfer: true
                                }
                            },
                            [
                                h('span', {
                                    class: className,
                                    style: {
                                        display: this.permissionObj.download ? 'inline-block' : 'none',
                                        marginRight: '0px',

                                      },
                                    on: {
                                        click: () => {
                                            this.handledownloadFile(params.row.id, params.row.fileAccessPath)
                                        }
                                    }
                                }),
                                h('span', { slot: 'content' }, this.$t('wifi_download'))
                            ]
                        )
                        let array = [down];
                        //return h('div', array);
                        return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                    }
                }
            ],
            editShow: false,
            editTitle: '',
            selectedIds: new Set(),
            editFormData: {
                name: '',
                strFileFormat: [],
                desc: '',
                startTime: '',
                timeRange: '',
                endTime: '',
                remarks: '',
                provider: '',
                orgId: '',
                groupIds: []
                // treeValue1:'',
            },
            editFormValidate: {
                orgId: [{ required: true, trigger: 'change', type: 'number', message: this.$t('comm_select_org'), }],
                // name: [{ required: true, trigger: 'blur', minLength: 1, maxLength: 50, validator: validate.validateString, name: '报告名称' }],
                name: [{ required: true, trigger: 'blur', minLength: 1, maxLength: 50, validator: validate.validateStrContainsSpecialChars, name: this.$t('comm_report_name2') }],
                timeRange: [{ required: true, trigger: 'change', validator: validate.validateDate, }],
                strFileFormat: [{ required: true, trigger: 'change', message: this.$t('test_report_select_file_format'), type: 'array' }],
                provider: [{ required: true, trigger: 'blur', minLength: 1, maxLength: 50, validator: validate.validateStrContainsSpecialChars, name: this.$t('test_report_provider') }],
                remarks: [{ required: false, trigger: 'blur', minLength: 1, maxLength: 200, validator: validate.validateStrContainsSpecialChars, name: this.$t('test_report_describe') }],
            },
            timeOptions: {
                disabledDate(date) {
                    return date && date.valueOf() > Date.now();
                },
                shortcuts: [
                    // {
                    //     text: this.$t('comm_half_hour'),
                    //     value() {
                    //         const start = new Date();
                    //         const end = new Date();
                    //         start.setTime(start.getTime() - 30 * 60 * 1000);
                    //         return [start.format('yyyy-MM-dd HH:mm:ss'), end.format('yyyy-MM-dd HH:mm:ss')];
                    //     }
                    // },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate()
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    }
                ]
            },

            timeOptionsTwo: {
                shortcuts: [
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate()
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    }
                ]
            },

            timeRange: [],
            //  机构类型
            orgLevel: null,
            companyList: []
        };
    },
    created() {
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        this.getDialList();
        this.getTreeOrg(1);
        this.orgLevel = JSON.parse(sessionStorage.getItem('accessToken')).orgLevel;
    },
    mounted() {
                 // 监听 storage 事件
        window.addEventListener('storage', this.handleStorageChange);
        // 30秒定时更新数据
        this.timerInterval = setInterval(() => {
            this.getDialList();
        }, 1000 * 30);
    },
    beforeDestroy() {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
        window.removeEventListener('storage', this.handleStorageChange);
    },
    methods: {
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        dateChange(val, type) {
            if (val[0] === val[1]) {
                this.timeRange = [val[0], new Date(val[1]).format("yyyy-MM-dd 23:59:59")]
            }
        },
        moreBtnClick(val) {
            eval(`this.${val}`);
        },
        getTreeOrg(type) {
            let _self = this;
            _self.$http.PostJson('/org/tree', { orgId: null }).then(res => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item;
                    });
                    if (type == 1) {
                        _self.treeData = treeNodeList;
                      
                    } else {
                        _self.treeData2 = treeNodeList;
                    }
                }
            });
        },
        loadOrgTreeData(item, callback) {
            this.$http.PostJson('/org/tree', { orgId: item.id }).then(res => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item;
                    });
                    callback(childrenOrgList);
                }
            });
        },
        setOrg(item) {
            console.log('=====>',item[0]);
            this.treeValue = item[0].name;
            this.query.orgId = item[0] ? item[0].id : null;
        },
        getGroupList(orgId) {
            const param = {
                orgId: orgId
            };
            this.$http.wisdomPost("/group/groupList", param).then(res => {
                if (res.code === 1) {
                    if (res.data) {
                        this.groupList = res.data;
                    }
                }
            });
        },
         focusFn(num) {
            this.getTreeOrg(num)
            }, 
        onClear() {
            this.treeValue = '';
            this.query.orgId = '';
        },
        setOrg2(item) {
            this.treeValue1 = item[0].name;
            this.editFormData.orgId = item[0] ? item[0].id : null;
            this.$refs.serviceStatusClear.clearSingleSelect()
            this.editFormData.groupIds = '';
            console.log(item);
            if (item[0] != null && item[0].id != null && item[0].id != '') {
                this.getGroupList(item[0].id);
            }
        },
        onClear2() {
            this.treeValue1 = '';
            this.editFormData.orgId = '';
        },
        queryDialList() {
            this.query.startTime = ''
            this.query.endTime = ''
            if (this.timeRange[0]) {
                this.query.startTime = moment(this.timeRange[0]).format("YYYY-MM-DD HH:mm:ss");
            }
            if (this.timeRange[1]) {
                this.query.endTime = moment(this.timeRange[1]).format("YYYY-MM-DD HH:mm:ss");
            }
            // this.query.startTime = moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
            // this.query.endTime = moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');

            this.query.pageNo = 1;
            this.getDialList();
        },
        getDialList() {
            this.$http.PostJson('/checkoutreport/list', this.query).then(res => {
                if (res.code === 1) {
                    res.data.records.forEach(item => {
                        item.remarks = item.remarks || '--';
                    });
                    this.tabList = res.data.records;
                    this.totalCount = res.data.total;
                }
            });
        },
        // 新建、修改弹窗
        eidtClick(type, rowData) {
            this.editShow = true;
            this.getTreeOrg(2);
            this.$refs['editForm'].resetFields();
            if (type == 'add') {
                this.editFormData.id = undefined;
                this.treeValue1 = '',
                    this.editTitle = this.$t('common_new');
                this.editFormData.timeRange = [new Date(new Date().getTime() - 3600 * 1000 * 24 * 14).format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')],
                    this.getProviderName();
            } else if (type == 'modify') {
                this.editTitle = this.$t('common_update');
                const { id, name, fileFormat, remarks, provider, startTime, endTime, orgId, groupIds } = rowData;

                this.editFormData = { id, name, fileFormat, remarks, provider, startTime, endTime, orgId, groupIds: !groupIds ? [] : groupIds.split(',').map((item) => Number(item)) };
                console.log(this.editFormData);
                this.editFormData.remarks = this.editFormData.remarks == '--' ? '' : this.editFormData.remarks;
                this.treeValue1 = rowData.orgName;
                this.getGroupList(orgId);
                if (type == 4) {
                    this.timeRange = [startTime, endTime];
                }
            }
        },
        // 新建、修改提交
        submitEditForm() {
            console.log('this.editFormData', this.editFormData);
            const param = { ...this.editFormData };
            if (param.groupIds) {
                param.groupIds = param.groupIds.join(',');
            }
            this.submitLoading = true;
            param.strFileFormat = param.strFileFormat.join(',');
            const url = this.editTitle === this.$t('common_update') ? '/checkoutreport/update' : '/checkoutreport/insert';
            this.$refs['editForm'].validate(valid => {
                if (valid) {
                    let startVal = moment(new Date(this.editFormData.timeRange[0]).format("yyyy-MM-dd HH:mm:ss"), "YYYY-MM-DD hh:mm:ss").valueOf();
                    let endVal = moment(new Date(this.editFormData.timeRange[1]).format("yyyy-MM-dd HH:mm:ss"), "YYYY-MM-DD 23:59:59").valueOf();
                    if ((endVal - startVal) / 1000 / 3600 / 24 > 92) {
                        this.$Message.warning(this.$t('test_report_limit_time'));
                        return;
                    }
                    this.submitLoading = true;
                    param.startTime = moment(this.editFormData.timeRange[0]).format('YYYY-MM-DD 00:00:00');
                    param.endTime = moment(this.editFormData.timeRange[1]).format('YYYY-MM-DD 23:59:59');
                    this.$http
                        .wisdomPost(url, param)
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success(this.editTitle + this.$t('comm_success'));
                                 this.editShow = false;
                            } else {
                                this.$Message.warning(msg);
                                this.submitLoading = false;
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        })
                        .finally(() => {
                            this.submitLoading = false;
                            this.editFormData.groupIds = '';
                            this.query.pageNo = 1;
                            this.getDialList();
                        });
                } else {
                     this.submitLoading = false;
                }
            });
        },
        // 获取登录账号的默认报告提供方名称
        getProviderName() {
            let _self = this;
            _self.$http.wisdomPost('/qualityReportStrategy/getProviderName').then(res => {
                if (res.code === 1) {
                    _self.editFormData.provider = res.msg;
                }
            });
        },
        // 删除数据
        deleteData(type, row) {
            let ids;
            const idsArr = Array.from(this.selectedIds);
            if (idsArr.length < 1) {
                this.$Message.warning(this.$t('group_select_delete'));
                return;
            } else if (idsArr.length > 0) {
                ids = idsArr.join(',');
            }
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: this.$t('test_report_prompt_delete'),
                onOk: () => {
                    //调用删除接口
                    this.deleteApi(ids);
                }
            });
        },
        // 删除数据接口请求
        deleteApi(ids) {
            this.$http
                .wisdomPost('/checkoutreport/delete', { ids })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.$Message.success(this.$t('common_controls_succ'));
                        this.query.pageNo = 1;
                        this.getDialList();
                        this.selectedIds = new Set();
                    } else {
                        this.$Message.warning(msg);
                    }
                })
                .catch(err => {
                    throw new Error(err);
                });
        },

        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
            if (slection.length === 0) {
                var data = this.$refs.tableList.data;
                data.forEach(item => {
                    if (this.selectedIds.has(item.id)) {
                        this.selectedIds.delete(item.id);
                    }
                });
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                });
            }
        },
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
        },
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
        },
        pageChange(val) {
            //表格页码切换
            this.query.pageNo = val;
            this.getDialList(this.query);
        },
        pageSizeChange(e) {
            //表格每页展示多少条数据切换
            this.query.pageNo = 1;
            this.query.pageSize = e;
            this.getDialList(this.query);
        },

        //pptx模板下载
        downloadFile(row) {
            let ids;
            console.log('ssss' + row);
            const idsArr = Array.from(this.selectedIds);
            if (idsArr.length < 1) {
                this.$Message.warning(this.$t('test_report_prompt_select_data'));
                return;
            } else if (idsArr.length > 0) {
                ids = idsArr.join(',');
            }
            var data = this.$refs.tableList.data;
            var selectData = [];
            data.forEach(item => {
                if (this.selectedIds.has(item.id)) {
                    selectData.push(item);
                }
            });
            selectData.forEach(item => {
                this.handledownloadFile(item.id, item.fileAccessPath);
            });


        },
        handledownloadFile(id, fileNames) {
            if (!fileNames) {
                this.$Message.warning(this.$t('test_report_prompt_no_down_file'));
            }
            var fileType = fileNames.split(".")[1];
            this.btnSendTem = true;
            // 加载loading
            let token_id = JSON.parse(sessionStorage.getItem("accessToken")).token_id;
            let params = new URLSearchParams();
            params.append("token_id", token_id);
            this.$Loading.start();
            axios({
                url: "/checkoutreport/download",
                method: "get",
                responseType: "blob", // 服务器返回的数据类型
                params: {
                    id: id,
                }
            })
                .then((res) => {
                    // 关闭loading
                    const content = res.data;
                    // 构造一个blob对象来处理数据
                    const blob = new Blob([content], {
                        type: "application/vnd.ms-" + fileType,
                    });
                    if ("msSaveOrOpenBlob" in navigator) {
                        window.navigator.msSaveOrOpenBlob(blob, fileNames);
                    } else {
                        const fileName = fileNames; // 导出文件名
                        // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
                        // IE10以上支持blob但是依然不支持download
                        if ("download" in document.createElement("a")) {
                            // 支持a标签download的浏览器
                            const _res = res.data;
                            let blob = new Blob([_res]);
                            let downloadElement = document.createElement("a");
                            let href = window.URL.createObjectURL(blob); //创建下载的链接
                            downloadElement.href = href;
                            downloadElement.download = fileName; //下载后文件名
                            document.body.appendChild(downloadElement);
                            downloadElement.click(); //点击下载
                            document.body.removeChild(downloadElement); //下载完成移除元素
                            window.URL.revokeObjectURL(href); //释放掉blob对象
                        } else {
                            // 其他浏览器
                            navigator.msSaveBlob(blob, fileName);
                        }
                    }
                    this.btnSendTem = false;
                    this.$Loading.finish();
                })
                .catch((error) => {
                    console.log(error);
                    // 关闭loading
                    this.btnSendTem = false;
                    this.$Loading.finish();
                }).finally(() => {
                    this.$Loading.finish();
                });
        },
        configData() {
            this.importModal.show = true;
            // this.$refs.importForm.$refs.uploadFile.value = "";
            // this.$refs.importForm.repeatExcel = [];
            this.$refs["importForm"].$refs["importForm"].resetFields();
        },

    },
};
</script>
<style lang="less" scoped>
.show-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  background-image: url("../../../assets/btn-img/btn-look.png");
  background-repeat: no-repeat;
}

.daoChu-btn {
  width: 60px !important;
}

.custom-button {
  height: 36px !important;
  width: 120px !important;
  background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%) !important;
  border-radius: 4px 4px 4px 4px !important;
  opacity: 1 !important;
  color: #060d15 !important;
  font-weight: normal !important;
  border-color: #57a3f3 !important;
}
</style>
