const lan = require('../../../common/language')
export default [
  {
    path: "/",
    name: lan.getLabel("src.testingReport"),
    meta: {
      authority: true
    },
    redirect:'/checkoutreport',
  },
  {
    path: "/checkoutreport",
    name: lan.getLabel("src.testingReport"),
    meta: {
      authority: true
    },
    component: resolve =>
      require(["@/modules/anpm-plug-checkoutreport-ui/views/index.vue"], resolve)
  },
];
