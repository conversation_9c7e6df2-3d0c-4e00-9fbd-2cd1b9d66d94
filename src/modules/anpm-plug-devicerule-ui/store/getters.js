"use strict";
//获取目的管理列表
export const ProbeTargetName = state => state.ProbeTargetName;
// 人员管理
export const Person = state => state.Person;
// 获取客户列表
export const ClientList = state => state.ClientList;
// 链路类型配置列表
export const LinkTypeList = state => state.LinkTypeList;

// 获取链路类型列表
export const LinkTypeListss = state => state.LinkTypeListss;
// 获取链路配置列表
export const ListLinks = state => state.ListLinks;
// 获取告警等级
export const AramLIst = state => state.AramLIst;
// 获取告警规则列表
export const AramRoleList = state => state.AramRoleList;
// 获取公共节点对比分析
export const PublicNodeList = state => state.PublicNodeList;
// 获取公共节点对比分析承载链路总数
export const ChengzaiList = state => state.ChengzaiList;
// 获取公共节点对比分析中断链路总数
export const ZhongduanList = state => state.ZhongduanList;
// 获取公共节点对比分析劣化链路总数
export const LiehuaList = state => state.LiehuaList;
// 获取公共节点对比分析链路树图
export const linkMoreList = state => state.linkMoreList;
// 获取公共节点对比分析 单链路图
export const LinksList = state => state.LinksList;
// 获取实时告警
export const RealTimeAlarmList = state => state.RealTimeAlarmList;
export const AlarmDetailList = state => state.AlarmDetailList;

export const historyAlarmList = state => state.historyAlarmList;
// 获取链路查询列表
export const LinkSearchList = state => state.LinkSearchList;
export const LinkSearchDetailList = state => state.LinkSearchDetailList;
// 获取链路统计
export const LinkNumberList = state => state.LinkNumberList;
// 获取中断链路信息
export const ListBrokenEvent = state => state.ListBrokenEvent;
export const BrokenTimeList = state => state.BrokenTimeList;
export const BrokenCountList = state => state.BrokenCountList;
export const BrokenAllList = state => state.BrokenAllList;
export const BrokenEventList = state => state.BrokenEventList;
export const BrokenEventBList = state => state.BrokenEventBList;
// 劣化链路分析
export const Deglist = state => state.Deglist;
export const degDetailsList = state => state.degDetailsList;
//默认值
export const defaultConfigList = state => state.defaultConfigList;
export const saveDefaultValue = state => state.defaultConfigList;
