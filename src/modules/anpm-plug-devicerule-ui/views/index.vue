<template>
  <section class="sectionBox">
    <!-- 设备发现规则页面 -->
    <div class="section-top">
      <Row class="fn_box">
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 180px"
              >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <TreeSelect
                v-model="treeValue"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadOrgTreeData"
                @onSelectChange="setOrg"
                @onClear="onClear"
                @onFocus="focusFn"
              >
              </TreeSelect>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 180px"
              >{{ $t("comm_discovery_state") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="status"
                :placeholder="$t('comm_select')"
                filterable
                :only-filter-with-text="true"
                clearable
              >
                <Option
                  v-for="(item, index) in statusList"
                  :value="item.value"
                  :key="index"
                  >{{ item.label }}
                </Option>
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 180px"
              >{{ $t("comm_keywords") }}：</label
            >
            <div class="fn_item_box">
              <Input
                v-model.trim="keyWord"
                :placeholder="$t('device_discovery_rule_matching')"
                :title="$t('device_discovery_rule_matching')"
                style="width: 350px"
              />
            </div>
          </div>
        </Col>
      </Row>
      <!--面板功能按键区域-->
      <div class="tool-btn">
        <div>
          <Button
            class="jiaHao-btn"
            type="primary"
            v-if="deviceRuleDiscover.list"
            @click="queryClick(1)"
            :title="$t('common_query')"
          >
            <i class="iconfont icon-icon-query" />
          </Button>
          <!-- <Button class="query-btn" type="primary" v-if="deviceRuleDiscover.list" icon="ios-search" @click="queryClick(1)"
                            :title="$t('common_query')"></Button> -->
          <Button
            class="query-btn"
            type="primary"
            v-if="deviceRuleDiscover.add"
            icon="md-add"
            @click="openAddForm"
            :title="$t('common_new')"
          ></Button>
          <Dropdown @on-click="moreBtnClick">
            <Button class="more-btn">
              {{ $t("common_more") }}
              <Icon type="ios-arrow-down"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem
                name='rowRemove("plural")'
                v-if="deviceRuleDiscover.delete"
                >{{ $t("but_remove") }}</DropdownItem
              >
              <DropdownItem name="start()" v-if="deviceRuleDiscover.status">{{
                $t("but_enable")
              }}</DropdownItem>
              <DropdownItem name="stop()" v-if="deviceRuleDiscover.status">{{
                $t("common_disable")
              }}</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body" style="padding: 0">
      <Loading :loading="pageLoading"></Loading>
      <div class="section-body-content">
        <div>
          <!--    列表      -->
          <Table
            ref="tableList"
            stripe
            :columns="tableColumn"
            class="fixed-left-right"
            :data="tableData"
            :no-data-text="
              pageData.total > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('comm_no_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('comm_no_data') +
                  '</p></div>'
            "
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
            <!--    列表操作列功能        -->
            <!-- <template slot-scope="{ row }" slot="action" class="tableTools">
                            <span style="margin-right: 6px; color: #57a3f3; cursor: pointer" v-if="deviceRuleDiscover.update"
                                  @click="openEditForm(row)">{{$t('comm_edit')}}</span>
                            <span v-if="deviceRuleDiscover.delete" style="margin-right: 6px; color: #f16643; cursor: pointer"
                                  @click="rowRemove('singular',row)">{{$t('but_remove')}}</span>
                        </template> -->
          </Table>
          <!--   分页      -->
          <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
            <Page
              v-page
              :current.sync="page.pageNo"
              :page-size="page.pageSize"
              :total="pageData.total"
              :page-size-opts="page.pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建规则弹框  -->
    <Modal
      :title="addForm.insert ? this.$t('common_new') : this.$t('common_update')"
      sticky
      v-model="addOpen"
      width="1000"
      draggable
      :mask="true"
      :mask-closable="false"
      @on-ok="addSubmit"
      @on-cancel="cancleForm('addForm')"
    >
      <div style="margin-right: 50px">
        <Form
          ref="addForm"
          v-if="addOpen"
          :model="addForm"
          :rules="addRuleFormRule"
          class="width_50_Form"
          @submit.native.prevent
          :label-width="180"
        >
          <!-- 机构 -->
          <FormItem :label="$t('comm_org') + $t('comm_colon')" prop="orgId">
            <div class="fn_item_box">
              <TreeSelect
                v-model="treeValue1"
                ref="TreeSelect"
                :data="treeData"
                :placeholder="$t('snmp_pl_man')"
                :loadData="loadOrgTreeData"
                @onSelectChange="setEditOrg"
                @onClear="onClear2"
                @onFocus="focusFn"
              >
              </TreeSelect>
              <!-- <Input v-if="addForm.id" v-model="addForm.orgName" disabled></Input>
                        <div v-else>
                            <Select class="noIcon" v-model="addForm.orgId" disabled style="width:calc(100% - 65px)" placeholder="请选择机构">
                                <Option v-for="item in orgLists" :value="item.id" :key="item.id">{{ item.name }}</Option>
                            </Select>
                            <Button type="info" style="margin-left: 5px;width: 60px" @click.stop="choicesOrg(2)">{{$t('but_choose')}}</Button>
                        </div>
                        <div class="orgDiv" id="selectBox" v-show="orgTreeModal" style="right: 0">
                            <div class="title" style="line-height: 32px">机构选择</div>
                            <div class="orgTreeScroll">
                                <Tree :data="treeData" :load-data="loadOrgTreeData" @on-select-change="setEditOrg"></Tree>
                            </div>
                        </div> -->
            </div>
          </FormItem>
          <FormItem
            :label="$t('discover_rule_name') + $t('comm_colon')"
            prop="name"
          >
            <Input v-model="addForm.name" maxlength="50"></Input>
          </FormItem>
          <!-- 采集器 -->
          <FormItem
            :label="$t('discover_gether_name') + $t('comm_colon')"
            prop="getherCode"
          >
            <Input
              v-if="addForm.id"
              v-model="addForm.getherName"
              disabled
            ></Input>
            <Select
              v-else
              ref="getherClear"
              v-model="addForm.getherCode"
              filterable
              :only-filter-with-text="true"
              clearable
              :placeholder="$t('gether_select_collector')"
            >
              <Option
                v-for="item in gatherList"
                :value="item.code"
                :key="item.code"
                >{{ item.labelName }}
              </Option>
            </Select>
          </FormItem>
          <FormItem
            :label="$t('discover_dis_inter') + $t('comm_colon')"
            prop="intervalTime"
          >
            <Input
              v-model.number="addForm.intervalTime"
              style="width: 230px; margin-right: 5px"
            ></Input
            >{{ $t("comm_hour") }}
          </FormItem>
          <FormItem
            :label="$t('discover_ip_rang') + $t('comm_colon')"
            class="width_100_item ipBox"
            prop="ipStr"
          >
            <Input
              v-model="addForm.ipStr"
              :placeholder="$t('comm_ip_range')"
              maxlength="128"
            ></Input>
            <span>{{ $t("discover_ip_tips") }}</span>
            <!-- <div class="ipitemBox startIp">
                            <label style="padding: 0 14px 0 0">{{$t('comm_from')}}</label>
                            <Input style="width: 61px" v-model="addForm.ipOne" @input="pushIp(0, $event, 0)"></Input><span
                                class="ipitemspan">·</span>
                            <Input style="width: 61px" v-model="addForm.ipTwo" @input="pushIp(1, $event, 0)"></Input><span
                                class="ipitemspan">·</span>
                            <Input style="width: 61px" v-model="addForm.ipThree" @input="pushIp(2, $event, 0)"></Input><span
                                class="ipitemspan">·</span>
                            <Input style="width: 61px" v-model="addForm.startIpFour" @input="pushIp(3, $event, 0)"></Input>
                        </div>
                        <div class="ipitemBox endIp">
                            <label style="padding: 0 20px 0 20px">{{$t('comm_to')}}</label>
                            <Input style="width: 61px" v-model="addForm.ipOne" @input="pushIp(0, $event, 0)"></Input><span
                                class="ipitemspan">·</span>
                            <Input style="width: 61px" v-model="addForm.ipTwo" @input="pushIp(1, $event, 0)"></Input><span
                                class="ipitemspan">·</span>
                            <Input style="width: 61px" v-model="addForm.ipThree" @input="pushIp(2, $event, 0)"></Input><span
                                class="ipitemspan">·</span>
                            <Input style="width: 61px" v-model="addForm.endIpFour" @input="pushIp(4, $event, 0)"></Input>
                        </div> -->
          </FormItem>
          <FormItem
            :label="$t('device_discovery_select_credential') + $t('comm_colon')"
            prop="voucherIds"
            class="width_100_item"
          >
            <Select
              v-model="addForm.voucherIds"
              multiple
              :max-tag-count="3"
              :placeholder="$t('comm_please_select')"
            >
              <Option
                v-for="item in voucherList"
                :value="item.id"
                style="width: 680px"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem
            :label="$t('phytopo_desc') + $t('comm_colon')"
            class="width_100_item"
          >
            <Input
              type="textarea"
              maxlength="200"
              v-model="addForm.describe"
            ></Input>
          </FormItem>
          <Divider style="margin: 10px 0 30px 0" />
          <div>
            <div style="display: inline-block; width: 150px; float: left">
              <p
                style="
                  text-align: right;
                  line-height: 1;
                  padding: 10px 12px 10px 0;
                "
              >
                {{ $t("device_discovery_action_conf") }}：
              </p>
            </div>
            <div style="margin-left: 120px">
              <FormItem
                label=""
                class="width_100_item"
                style="margin-left: -120px"
              >
                <RadioGroup v-model="addForm.actionConfig">
                  <Radio label="0">
                    <span>{{ $t("device_discovery_action_conf") }}</span>
                  </Radio>
                  <Radio label="1">
                    <span>{{ $t("device_discovery_discover_only") }}</span>
                  </Radio>
                </RadioGroup>
              </FormItem>
              <div v-if="addForm.actionConfig != 1">
                <span
                  >{{ $t("comm_conditions") }}&nbsp;&nbsp;&nbsp;<label
                    style="color: #afaeae80"
                    >{{ $t("device_discovery_select") }}</label
                  ></span
                >
                <div
                  style="display: flex; align-items: center; margin-top: 10px"
                >
                  <Select
                    v-model="standard"
                    style="width: 200px; margin-right: 10px"
                    :placeholder="$t('device_discovery_select_standard')"
                  >
                    <Option
                      v-for="(item, index) in standardList"
                      :value="item.value"
                      :key="index"
                    >
                      {{ item.label }}</Option
                    >
                  </Select>
                  <Select
                    v-model="chooseCondition"
                    style="width: 200px; margin-right: 10px"
                    :placeholder="$t('device_discovery_select_standard')"
                  >
                    <Option
                      v-for="(item, index) in chooseConditionList"
                      :value="item.value"
                      :key="index"
                      >{{ item.label }}</Option
                    >
                  </Select>
                  <Input
                    v-model.trim="conditionVal"
                    maxlength="30"
                    style="width: 200px"
                    :placeholder="$t('device_discovery_match_value')"
                  />
                  <Icon
                    type="ios-add-circle"
                    style="
                      font-size: 20px;
                      color: #2b85e4;
                      cursor: pointer;
                      margin-left: 30px;
                    "
                    @click="addRow"
                  />
                </div>
                <FormItem
                  label=""
                  class="width_100_item"
                  style="margin: 10px 0 10px -120px"
                >
                  <RadioGroup v-model="addForm.conditionMatch">
                    <Radio label="0">
                      <span>{{ $t("device_discovery_meet_any") }}</span>
                    </Radio>
                    <Radio label="1">
                      <span>{{ $t("device_discovery_match_all") }}</span>
                    </Radio>
                  </RadioGroup>
                </FormItem>
                <div class="snmptask-body" style="margin-left: 0px">
                  <div class="task_indicator_header">
                    <div class="item_header" style="width: 25%">
                      <span>{{ $t("device_discovery_standard") }}</span>
                    </div>
                    <div class="item_header" style="width: 25%">
                      <span>{{ $t("comm_conditions") }}</span>
                    </div>
                    <div class="item_header" style="width: 25%">
                      <span>{{ $t("comm_value") }}</span>
                    </div>
                    <div class="item_header" style="width: 25%">
                      <span>{{ $t("common_controls") }}</span>
                    </div>
                  </div>
                  <div
                    class="task_indicator_body"
                    style="margin-bottom: 10px; width: 100%"
                  >
                    <div
                      class="indicator_row"
                      v-for="(item, index) in conditionsListName"
                      :key="'row' + index"
                      style="width: 100%"
                    >
                      <div class="task_indicator_body_item" style="width: 25%">
                        {{ item.standard }}
                      </div>
                      <div class="task_indicator_body_item" style="width: 25%">
                        {{ item.chooseCondition }}
                      </div>
                      <div class="task_indicator_body_item" style="width: 25%">
                        {{ item.conditionVal }}
                      </div>
                      <div
                        class="task_indicator_body_item"
                        @click="deleteRow(index)"
                        style="
                          color: rgb(87, 163, 243);
                          cursor: pointer;
                          width: 25%;
                        "
                      >
                        <span>{{ $t("but_remove") }}</span>
                      </div>
                    </div>
                  </div>
                  <span
                    >{{ $t("common_action") }} &nbsp;&nbsp;&nbsp;<label
                      style="color: #afaeae80"
                      >{{ $t("device_discovery_condition") }}</label
                    ></span
                  >
                  <div
                    style="display: flex; align-items: center; margin: 10px 0"
                  >
                    <!-- 动作类型 -->
                    <Select
                      v-model="actionType"
                      style="width: 150px; margin-right: 10px"
                      @on-change="actionChange"
                      :placeholder="$t('device_discovery_select_action')"
                    >
                      <Option
                        v-for="(item, index) in actionList"
                        :value="item.value"
                        :key="index"
                      >
                        {{ item.label }}
                      </Option>
                    </Select>
                    <!-- 运维等级 -->
                    <Select
                      v-model="maintainLevel"
                      style="width: 150px; margin-right: 10px"
                      :placeholder="$t('device_discovery_select_level')"
                    >
                      <Option
                        v-for="(item, index) in gradeList"
                        :value="item.value"
                        :key="index"
                      >
                        {{ item.label }}
                      </Option>
                    </Select>
                    <!-- 分组 -->
                    <Select
                      v-model="groupIds"
                      :max-tag-count="1"
                      multiple
                      style="width: 150px; margin-right: 10px"
                      :placeholder="$t('comm_select_group')"
                    >
                      <Option
                        v-for="(item, index) in getGroupList"
                        style="width: 230px"
                        :value="item.id"
                        :key="index"
                      >
                        {{ item.name }}
                      </Option>
                    </Select>
                    <!-- 探针 -->
                    <Select
                      v-model="probeIds"
                      multiple
                      :max-tag-count="1"
                      style="width: 150px; margin-right: 10px"
                      :placeholder="$t('peertopeer_select_probe')"
                    >
                      <Option
                        v-for="(item, index) in probeList"
                        :value="item.code"
                        :key="index"
                        style="width: 230px"
                        >{{ item.labelName }}</Option
                      >
                    </Select>
                    <!-- v-show="actionType!='2'"  -->
                    <Select
                      v-if="actionType != '2'"
                      v-model="deviceTarget"
                      multiple
                      :max-tag-count="1"
                      :placeholder="$t('testspeed_select_indicators')"
                      style="width: 150px; margin-right: 10px"
                    >
                      <Option
                        v-for="(item, index) in deviceTargetList"
                        :value="item.value"
                        :key="index"
                        >{{ item.label }}</Option
                      >
                    </Select>
                    <Icon
                      type="ios-add-circle"
                      style="font-size: 20px; color: #2b85e4; cursor: pointer"
                      @click="addActionRow"
                    />
                  </div>
                  <div class="snmptask-body" style="margin-left: 0px">
                    <div class="task_indicator_header">
                      <div class="item_header" style="width: 170px">
                        <span>{{ $t("common_action") }}</span>
                      </div>
                      <div class="item_header" style="width: 400px">
                        <span
                          >{{ $t("common_action_attribute")
                          }}{{ $t("common_controls") }}</span
                        >
                      </div>
                      <div class="item_header">
                        <span>{{ $t("common_controls") }}</span>
                      </div>
                    </div>
                    <div class="task_indicator_body">
                      <div
                        class="indicator_row"
                        v-for="(item, index) in tableModalList1"
                        :key="'row' + index"
                      >
                        <div class="task_indicator_body_item">
                          {{ item.action }}
                        </div>
                        <div
                          class="task_indicator_body_item"
                          style="width: 400px"
                        >
                          {{ item.attribute }}
                        </div>
                        <div
                          class="task_indicator_body_item"
                          @click="deleteActionRow(index)"
                          style="color: rgb(87, 163, 243); cursor: pointer"
                        >
                          <span>{{ $t("but_remove") }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </div>
      <div slot="footer">
        <Button type="error" size="large" @click="cancleForm('addForm')">{{
          $t("common_cancel")
        }}</Button>
        <Button type="primary" size="large" @click="addSubmit">{{
          $t("but_confirm")
        }}</Button>
      </div>
    </Modal>
  </section>
</template>
<script>
//  机构网络监测 / 自动发现 / 发现规则
import validate from '@/common/validate';
import axios from 'axios';
import moment from 'moment';
import '@/config/page.js';
import global from '../../../common/global.js';
import locationreload from '@/common/locationReload';
import TreeSelect from '@/common/treeSelect/treeSelect.vue';
import ipv6Format from "@/common/ipv6Format";

export default {
    name: 'discoverRule',
    components: { TreeSelect },
    data() {
        const valiIp = (rule, value, callback) => {
            let notEmpty = true;
            for (let i = 0; i < 5; i++) {
                const item = value[i];
                if (item == undefined || item === '' || item == null) {
                    notEmpty = false;
                    break;
                }
            }
            let startIp = '',
                endIp = '';
            if (notEmpty === false) {
                callback(new Error(this.$t('comm_ip_correct')));
            } else {
                const ipStartWith3 = value[0] + '.' + value[1] + '.' + value[2];
                startIp = ipStartWith3 + '.' + value[3];
                endIp = ipStartWith3 + '.' + value[4];
                const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                if (!reg.test(startIp) || !reg.test(endIp)) {
                    callback(new Error(this.$t('comm_ip_correct')));
                } else {
                    if (value[4] - value[3] <= 0) {
                        callback(new Error(this.$t('device_discovery_error_ip')));
                    }
                    callback();
                }
            }
        };


    // 验证 **********-200或2001:db8::1a2f:0001-1a2b 这种形式的IP
        const verifyIp = (rule, value, callback) => {

        value = this.addForm.ipStr;

        // 为空的情况
        if(value == null || value == '' || value == undefined){
            callback(new Error(this.$t('comm_ip_correct')))
            return
        }
        var indexOf = value.indexOf("-");
        if(indexOf <= 0){
            callback(new Error(this.$t('comm_ip_correct')))
            return
        }

        // 分隔判断
        var ipSegment = value.split("-");
        if(ipSegment.length != 2){
            callback(new Error(this.$t('comm_ip_correct')))
            return
        }

        // 先取出开头IP
        var startIp = ipSegment[0];
        if(startIp.indexOf(":") >=0){
            // ipV6的格式
            verifyIpV6(startIp,ipSegment[1],callback);
        }else{
            // ipv4的格式
            verifyIpV4(startIp,ipSegment[1],callback);
        }
        };


        // 验证 2001:db8::1a2f:0001-1a2b
        const verifyIpV4 = (startIp,endSegment,callback) => {

        // Ip地址验证
        const reg = validate.getIpv4Regex();
        // /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if (!reg.test(startIp)) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        // 必须为数字格式 并且小于 255 大于等于 0 
        var result = Number(endSegment);
        if (isNaN(result) || result <= 0 || result > 255) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        // 不足四位进行补位
        var startArrays = startIp.split(".");
        var endIpSegment = [];;
        for(var i = 0 ; i < 3 ; i++){
            endIpSegment.push(startArrays[i]);
        }
        var endIpStr = endIpSegment.join(".")+"."+endSegment;
        console.log("结束Ip为：", endIpStr );
        if (!reg.test(endIpStr)) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        var startEndSegment = parseInt(startArrays[startArrays.length-1]);
        // 比较最后一位的大小
        if (result - startEndSegment <= 0) {
            callback(new Error(this.$t('device_discovery_error_ip')));
            return
        }
        callback();
        };

        // 验证 **********-200
        const verifyIpV6 = (startIp,endSegment,callback) => {

        // Ip地址验证
        const ipv6Regex = validate.getIpv6Regex();
        // /\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;
        if (!ipv6Regex.test(startIp)) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        var endSegmentRegex = /^[0-9a-fA-F]{1,4}$/;
        if (!endSegmentRegex.test(endSegment)) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        // 转换成16进制数字格式 并且小于 65535（2^{16} - 1） 大于等于 0 
        var result = parseInt(endSegment , 16);
        if (isNaN(result) || result <= 0 || result > 65535) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        // 先补全地址
        startIp = validate.expandIpv6(startIp);
        // 不足进行补位
        var startArrays = startIp.split(":");
        var endIpSegment = [];;
        for(var i = 0 ; i < 7 ; i++){
            endIpSegment.push(startArrays[i]);
        }
        var endIpStr = endIpSegment.join(":")+":"+endSegment;
        console.log("结束Ip为：", endIpStr );
        if (!ipv6Regex.test(endIpStr)) {
            callback(new Error(this.$t('comm_ip_correct')));
            return;
        }
        // 比较最后一位的大小
        var startEndSegment = parseInt(startArrays[startArrays.length-1] ,16);
        if (result - startEndSegment <= 0) {
            callback(new Error(this.$t('device_discovery_error_ip')));
            return
        }
        callback();
        };






        let _this = this;
        return {
            currentSkin: sessionStorage.getItem('dark') || 1,
            treeValue: '',
            treeValue1: '',
            // 机构
            orgLists: [],
            orgId: '',
            orgTreeModal: false,
            treeData: [],
            //--------------

            pageLoading: true,
            sourceList: [],
            nodeList: [],
            nodeData: [],
            sourceData: [],
            orgIds: '',
            isdarkSkin: top.window.isdarkSkin,
            /*權限*/
            deviceRuleDiscover: {},
            /* 参数列表 */
            //规则
            status: null,
            //规则列表
            statusList: [
                { value: 0, label: this.$t('comm_enable') },
                { value: 1, label: this.$t('common_disable') }
            ],
            //关键字
            keyWord: '',
            //分页参数
            page: {
                pageNo: 1,
                pageSize: 10,
                pageSizeOpts: [10, 50, 100, 200, 500, 1000],
                pageLoading: false
            },
            /** 列表数据 */
            pageData: {
                total: 0,
                list: []
            },
            /** 列表选中数据 */
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: new Set(),
            selectedDatas: [],
            /** 新建表单 */
            addForm: {
                orgId: '',
                name: '',
                getherCode: null,
                ipArr: [null, null, null, null, null],
                ipOne: '',
                ipTwo: '',
                ipThree: '',
                startIpFour: '',
                endIpFour: '',
                intervalTime: 1,
                // 描述
                describe: '',
                // 凭证
                voucherIds: [],
                conditionMatch: '0',
                actionConfig: null,
                ipStr:"",
                insert: true
            },
            //新建加载动画
            addLoading: true,
            //采集器数据列表
            gatherList: [],
            //新建弹框
            addOpen: false,
            //新建表单验证规则
            addRuleFormRule: {
                orgId: [{ required: true, type: 'number', message: this.$t('comm_select_org'), trigger: 'change' }],
                name: [{ required: true, type: 'string', message: this.$t('discover_pd_rule_name'), trigger: 'blur' }],
                getherCode: [{ required: true, type: 'string', message: this.$t('gether_select_collector'), trigger: 'change' }],
                // ipArr: [{ trigger: 'blur', length: 32, validator: valiIp }],
                ipStr: [{ trigger: 'blur', length: 128, validator: verifyIp }],
                voucherIds: [{ required: true, type: 'array', message: this.$t('device_discovery_choose_credentials'), trigger: 'change' }],
                intervalTime: [
                    { required: true, type: 'number', message: this.$t('discover_select_inter_time'), trigger: 'blur' },
                    { validator: validate.isInteger, min: 1, max: 100, trigger: 'blur', isInteger: true },
                    { validator: validate.checkMax, min: 1, max: 100, trigger: 'blur', isInteger: true }
                ]
            },
            /** 修改表单 */
            editForm: {
                id: null,
                orgId: '',
                name: '',
                getherCode: null,
                ipArr: [null, null, null, null, null],
                ipOne: '',
                ipTwo: '',
                ipThree: '',
                startIpFour: '',
                endIpFour: '',
                intervalTime: '',
                describe: '',
                ipStr:"",
                conditionMatch: '',
                actionConfig: ''
            },
            //修改加载动画
            editLoading: true,
            // 凭证
            voucherList: [],
            //修改弹框
            editOpen: false,
            /** 表格标题列 */
            tableColumn: [
                { type: 'selection', width: 30, className: 'bgColor', align: 'center' },
                { title: this.$t('discover_rule_name'), align: 'left', width: 220, key: 'ruleName', tooltip: true },
                {
                    title: this.$t('device_discovery_ip_range'),
                    align: 'left',
                    minWidth: 150,
                    key: 'startIp',
                    render: (h, param) => {
                        var str = param.row.startIp + ' — ' + param.row.endIp
                        let maxWidth = param.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);

                    }
                },
                { title: this.$t('testspeed_org'), align: 'left', width: 100, key: 'orgName', tooltip: true },
                { title: this.$t('discover_gether_name'), align: 'left', width: 140, key: 'getherName', tooltip: true },
                {
                    title: this.$t('comm_status'),
                    align: 'left',
                    width: 80,
                    key: 'status',
                    render: (h, param) => {
                        return h('span', { style: { color: param.row.status == 1 ? '#C0C4CC' : '#19be6b' } }, param.row.status == 1 ? this.$t('comm_disable') : this.$t('comm_enable'));
                    }
                },
                { title: this.$t('device_discovery_last_scan_time'), align: 'left', width: 180, key: 'excuteTime' },
                {
                    title: this.$t('common_controls'),
                    align: 'center',
                    width: 110,
                    // fixed:'right',
                    render: (h, params) => {
                        let edit = h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                        transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: this.currentSkin == 1 ?'edit1-btn':'light-edit1-btn',
                                        style: {
                                            display: this.deviceRuleDiscover.update ? 'inline-block' : 'none',
                                            marginRight: '0px',
                                        },
                                        on: {
                                            click: () => {
                                                this.openEditForm(params.row);
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('common_update'))
                                ]
                            ),
                        del =h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                         transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: 'del1-btn',
                                        style: {
                                            display: this.deviceRuleDiscover.delete ? 'inline-block' : 'none',
                                            marginRight: '0px',
                                          },
                                        on: {
                                            click: () => {
                                                this.rowRemove('singular', params.row);
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('common_delete'))
                                ]
                            ),
                            array = [edit,del];
                        // return h('div', array);
                        return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                    }
                }
            ],
            addMolStartOpt: {
                disabledDate: date => {
                    if (_this.addForm.endDate) {
                        let end = new Date(_this.addForm.endDate);
                        return date > end;
                    } else {
                        return date > Date.now();
                    }
                }
            },
            addMolEndOpt: {
                disabledDate(date) {
                    if (_this.addForm.startDate) {
                        let end = new Date(_this.addForm.startDate);
                        return date < end;
                    } else {
                        return date > Date.now();
                    }
                }
            },
            editMolStartOpt: {
                disabledDate: date => {
                    if (_this.editForm.endDate) {
                        let end = new Date(_this.editForm.endDate);
                        return date > end;
                    } else {
                        return date > Date.now();
                    }
                }
            },
            editMolEndOpt: {
                disabledDate(date) {
                    if (_this.editForm.startDate) {
                        let end = new Date(_this.editForm.startDate);
                        return date < end;
                    } else {
                        return date > Date.now();
                    }
                }
            },
            standardList: [
                {
                    value: 1,
                    label: this.$t('snmpoid_device_merchant')
                },
                {
                    value: 2,
                    label: this.$t('snmpoid_model')
                },
                {
                    value: 3,
                    label: this.$t('discover_device_type')
                },
                {
                    value: 4,
                    label: this.$t('comm_topo_device_name')
                },
                {
                    value: 5,
                    label: this.$t('testspeed_ip_address')
                }
            ],
            chooseConditionList: [
                {
                    value: 1,
                    label: this.$t('comm_equal')
                },
                {
                    value: 2,
                    label: this.$t('comm_not_equal_to')
                },
                {
                    value: 3,
                    label: this.$t('comm_contain')
                },
                {
                    value: 4,
                    label: this.$t('comm_not_contain')
                }
            ],
            standard: '',
            chooseCondition: '',
            conditionVal: '',
            conditionsList: [],
            conditionsListName: [],
            // -----------------
            actionType: '',
            maintainLevel: '',
            groupIds: [],
            probeIds: [],
            deviceTarget: [],
            actionList: [
                {
                    value: '1',
                    label: this.$t('device_discovery_new_task')
                },
                {
                    value: '2',
                    label: this.$t('device_discovery_only_add_device')
                }
            ],
            gradeList: [
                {
                    value: '1',
                    label: this.$t('logback_first')
                },
                {
                    value: '2',
                    label: this.$t('logback_second')
                },
                {
                    value: '3',
                    label: this.$t('logback_tertiary')
                }
            ],
            getGroupList: [],
            probeList: [],
            deviceTargetList: [],
            tableModalList1: [],
            updateId: ''
        };
    },
    watch: {},
    mounted() {
      // 监听 storage 事件
     window.addEventListener('storage', this.handleStorageChange);
        //  this.$Skin.skinChange(top.window.skin)
        top.window.addEventListener('message', e => {
            if (e) {
                if (e.data.type == 'msg') {
                    return;
                } else if (typeof e.data == 'object') {
                    this.isdarkSkin = e.data.isdarkSkin;
                    this.$Skin.skinChange(top.window.skin);
                } else if (typeof e.data == 'number') {
                    this.isdarkSkin = e.data;
                    this.$Skin.skinChange(top.window.skin);
                }
            }
        });
        //机构弹框  点击外部 关闭
        top.document.addEventListener('click', e => {
            var box = top.document.getElementById('selectBox');
            if (box && box.contains(e.target)) {
            } else {
                this.orgTree = false;
                this.orgTreeModal = false;
            }
        });
    },
    beforeDestroy() {
// 移除事件监听
        window.removeEventListener('storage', this.handleStorageChange);
 },
    created() {
      this.$nextTick(() => {
         locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());

      })
       
        let permission = global.getPermission();
        this.deviceRuleDiscover = Object.assign(permission, {});
        console.log('权限---');
        console.log(this.deviceRuleDiscover);
        const orgId = JSON.parse(sessionStorage.getItem('accessToken')).user.orgId;
        moment.locale('zh-cn');
        this.queryClick(1);
        this.getTreeOrg();
        // 获取凭证
        this.getVoucher();
        // 获取指标
        this.getDeviceTargets();
        //获取分组
        this.getGroupSearchList();
        // 探针
        this.getGetDepartList();
         // 新增 机构 采集器默认选中
        let token = JSON.parse(sessionStorage.getItem("accessToken"));
        if (token && token.user) {
        this.treeValue1 = token.user.orgName;
        this.addForm.orgId = token.user.orgId
        this.getGetherList(this.addForm.orgId)
        console.log(this.gatherList,'444444')
        // this.addForm.getherCode = this.gatherList[0].code
        }

    },
    computed: {
        tableData: {
            get: function () {
                let arr = this.pageData.list.map(item => {
                    // for (const key in item) {
                    //     let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                    //     item[key] = str
                    // }
                   // return item
                    return item
                })
                return arr
            },
        },
    },
    methods: {
        handleStorageChange(event) {
        if (event.key === 'dark') {
          this.currentSkin = event.newValue; // 更新肤色
        }
      },
        moreBtnClick(val) {
            eval(`this.${val}`);
        },
        // 获取机构
        getTreeOrg() {
            let _self = this;
            _self.$http.PostJson('/org/tree', { orgId: null }).then(res => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        // item.expand=false;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                            // item.expand=true;
                        }
                        return item;
                    });
                    _self.treeData = treeNodeList;
                }
            });
        },
        loadOrgTreeData(item, callback) {
            this.$http.PostJson('/org/tree', { orgId: item.id }).then(res => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                        }
                        return item;
                    });
                    callback(childrenOrgList);
                }
            });
        },
        setOrg(item) {
            this.treeValue = item[0].name;
            this.orgId = item[0] ? item[0].id : null;
        },
       focusFn() {
        this.getTreeOrg()
        },
        onClear() {
            this.treeValue = '';
            this.orgId = '';
        },
        // 弹框选择
        setEditOrg(item) {
            this.treeValue1 = item[0].name;
            this.addForm.orgId = item[0] ? item[0].id : null;
            // this.orgLists = item;
            // this.orgTreeModal = false;
            // 选择机构后，动态更新采集器数据
            this.getGetherList(this.addForm.orgId, item[0]);
        },
        onClear2() {
            this.treeValue1 = '';
            this.addForm.orgId = '';
        },
        choicesOrg(val) {
            if (val == 1) {
                this.orgTree = true;
            } else {
                this.orgTreeModal = true;
            }
        },

        //查询事件
        queryClick(pageNo) {
            //初始化页码
            this.page.pageNo = pageNo;
            this.keyWord = this.keyWord.trim();
            // this.keyWord = this.keyWord.replace(/\s/g, '');
            //设置查询参数
            const queryParam = {
                orgId: this.orgId,
                status: this.status,
                keyword: this.keyWord,
                pageNo: this.page.pageNo,
                pageSize: this.page.pageSize
            };
            //打开加载动画
            this.pageLoading = true;

            //请求数据
            this.getTableList(queryParam);
        },
        //获取列表数据
        getTableList(queryParam) {
            this.$http
                .PostJson('/deviceDiscoverRule/list', queryParam)
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        if (data) {
                            this.pageLoading = false;
                            this.pageData.list = data.records;
                            this.pageData.total = data.total || 0;
                        } else {
                            this.pageData.list = [];
                            this.pageData.total = 0;
                            this.pageLoading = false;
                        }
                    } else {
                        this.pageData.total = 0;
                        this.pageLoading = false;
                        this.$Message.warning({ content: msg, background: true });
                        this.pageLoading = false;
                    }
                })
                .catch(error => {
                    this.pageData.total = 0;
                    this.pageLoading = false;
                });
        },
        /** 新建 */
        //打开新建表单
        openAddForm() {
            this.addOpen = true;
            this.$nextTick(() => {
                if (this.$refs.addForm !== undefined) this.$refs.addForm.resetFields()
                const orgId = JSON.parse(sessionStorage.getItem('accessToken')).user.orgId;
                // this.getGetherList(orgId); // 获取采集器数据
                this.orgLists = this.treeData;
               this.empty();
                this.updateId = '';
                // this.treeValue1 = '';
                this.addForm.insert = true;
                this.addForm.orgId = orgId
                
               
            })
            
        },
        // 得到ip地址
         getIps(valeIp){
            var startIp = "";
            var endIp = "";
            // 分隔判断
            var ipSegment = valeIp.split("-");
            if(ipSegment.length != 2){
                return [startIp,endIp];
            }
            // 先取出开头IP
            var startIp = ipSegment[0];
            var separator = ".";
            if(startIp.indexOf(":") >=0){
                // ipV6的格式
                separator = ":";
            }else{
                separator = ".";
                // ipv4的格式
            }
            // 得到最后的一个 .或者: 的位置
            var ipPrevIndex =  startIp.lastIndexOf(separator);
            endIp = startIp.substring(0,ipPrevIndex) + separator + ipSegment[1];

            console.log("原始IP = " , valeIp);
            console.log("startIp = " , startIp);
            console.log("endIp = " , endIp );

            return [startIp,endIp];
            },
        //新建请求接口
        addSubmit() {
            this.$refs['addForm'].validate(validate => {
                console.log(validate);
                if (validate) {

                     // 拼接ip
                    var ips = this.getIps(this.addForm.ipStr);
                     this.addForm.startIp = ips[0];
                     this.addForm.endIp = ips[1];

                    // this.addForm.startIp = this.addForm.ipOne + '.' + this.addForm.ipTwo + '.' + this.addForm.ipThree + '.' + this.addForm.startIpFour;
                    // this.addForm.endIp = this.addForm.ipOne + '.' + this.addForm.ipTwo + '.' + this.addForm.ipThree + '.' + this.addForm.endIpFour;
                    if (this.addForm.actionConfig == '0') {
                        if (this.conditionsList.length == 0) {
                            this.$Message.warning({ content: this.$t('device_discovery_warning_condition_add'), background: true });
                            return;
                        } else if (this.tableModalList1.length == 0) {
                            this.$Message.warning({ content: this.$t('device_discovery_warning_action_add'), background: true });
                            return;
                        }
                        this.addForm.conditionsList = this.conditionsList;
                        this.addForm.action = {
                            actionType: this.actionType,
                            maintainLevel: this.maintainLevel,
                            groupIds: this.groupIds,
                            probeIds: this.probeIds
                        };
                        if (this.actionType != 2) {
                            if (this.deviceTarget.indexOf(3) == -1 || this.deviceTarget.indexOf(7) == -1) {
                                this.$Message.warning({ content: this.$t('device_discovery_warning_device'), background: true });
                                return;
                            }
                            this.addForm.action['deviceTarget'] = this.deviceTarget;
                        } else {
                            this.addForm.action['deviceTarget'] = [];
                        }
                    }
                    //新建
                    if (this.addForm.insert) {
                        console.log('完整数据');
                        console.log(this.addForm);
                        this.$http
                            .PostJson('/deviceDiscoverRule/add', this.addForm)
                            .then(({ code, data, msg }) => {
                                if (code === 1) {
                                    this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                                    //清空选择的数据
                                    this.selectedIds = new Set();
                                    this.selectedDatas = [];
                                    this.queryClick(1);
                                    this.addOpen = false;
                                } else {
                                    this.$Message.warning({ content: msg, background: true });
                                }
                            })
                            .catch(err => {
                                throw new Error(err);
                            });
                        console.log(this.$t('common_new'));
                    } else {
                        this.addForm.id = this.updateId;
                        this.$http
                            .PostJson('/deviceDiscoverRule/update', this.addForm)
                            .then(({ code, data, msg }) => {
                                if (code === 1) {
                                    this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                                    //清空选择的数据
                                    this.selectedIds = new Set();
                                    this.selectedDatas = [];
                                    this.queryClick(1);
                                    this.addOpen = false;
                                } else {
                                    this.$Message.warning({ content: msg, background: true });
                                }
                            })
                            .catch(err => {
                                throw new Error(err);
                            });
                        console.log(this.$t('common_update'));
                    }
                } else {
                    this.$Message.warning({ content: this.$t('device_discovery_warning_validate'), background: true });
                    //取消按钮加载效果
                    this.addLoading = false;
                    this.$nextTick(() => {
                        this.addLoading = true;
                    });
                }
            });
        },
        editModalChange(val) {
            if (!val) {
                this.sourceData = [];
                this.nodeData = [];
                this.editForm.actionConfig = 0;
            }
        },
        //置空
        empty() {
            this.addForm = {
                name: '',
                getherCode: this.getherCode,
                ipArr: [null, null, null, null, null],
                ipOne: '',
                ipTwo: '',
                ipThree: '',
                orgId: '',
                startIpFour: '',
                endIpFour: '',
                intervalTime: 1,
                // 描述
                describe: '',
                ipStr:"",
                // 凭证
                voucherIds: [],
                conditionMatch: '0',
                actionConfig: '1'
            };
            this.addForm.ipStr = "";
            // 清空条件配置
            this.standard = '';
            this.chooseCondition = '';
            this.conditionVal = '';
            // 清空动作配置
            this.actionType = '';
            this.maintainLevel = '';
            this.groupIds = [];
            this.probeIds = [];
            this.deviceTarget = [3, 7];
            this.tableModalList1 = [];

            // 条件下默认显示条件
            // 设备类型  等于 交换机
            // 设备类型  等于 路由器
            this.conditionsList = [];
            this.conditionsListName = [];
            let deviceType = 3; //设备类型
            let chooseCondition = 1; //等于
            this.conditionsList = [
                { standard: deviceType, chooseCondition: chooseCondition, conditionVal: this.$t('default_manager_switchboard') },
                { standard: deviceType, chooseCondition: chooseCondition, conditionVal: this.$t('default_manager_router') }
            ];
            this.conditionsList.forEach(r => {
                let val = this.standardList.find(e => e.value == r.standard);
                let val1 = this.chooseConditionList.find(e => e.value == r.chooseCondition);
                this.conditionsListName.push({ standard: val.label, chooseCondition: val1.label, conditionVal: r.conditionVal });
            });
        },
        /** 编辑 */
        //打开编辑表单
        async openEditForm(param) {
            if (this.$refs.addForm !== undefined) this.$refs.addForm.resetFields()
            this.orgLists = this.treeData;
            this.addForm.insert = false;
            this.addOpen = true;
            this.updateId = param.id;
            this.treeValue1 = param.orgName;
            // 先置空
            this.empty();
            // 再进行
            this.$nextTick(() => {
                this.$http
                    .PostJson('/deviceDiscoverRule/getDetail', { id: param.id })
                    .then(({ code, data, msg }) => {
                        if (code === 1) {
                            console.log(data);
                            this.getGetherList(data.orgId); // 获取采集器数据
                            // let ip = data.startIp.split('.');
                            // let ipEnd = data.endIp.split('.')[3];
                            this.addForm = {
                                id: data.id,
                                orgName: data.orgName,
                                getherName: data.getherLabelName,
                                name: data.name,
                                getherCode: data.getherCode,
                                // ipArr: [ip[0], ip[1], ip[2], ip[3], ipEnd],
                                // ipOne: ip[0],
                                // ipTwo: ip[1],
                                // ipThree: ip[2],
                                // startIpFour: ip[3],
                                // endIpFour: ipEnd,
                                orgId: data.orgId,
                                intervalTime: data.intervalTime,
                                // 描述
                                describe: data.describe,
                                // 凭证
                                voucherIds: data.voucherIds,
                                conditionMatch: String(data.conditionMatch),
                                actionConfig: String(data.actionConfig)
                            };
                            this.addForm.ipStr = this.convertIps(data.startIp,data.endIp);
                            this.actionType = String(data.action.actionType);
                            this.maintainLevel = String(data.action.maintainLevel);
                            this.groupIds = data.action.groupIds;
                            this.probeIds = data.action.probeIds;
                            this.deviceTarget = data.action.deviceTarget;
                            let list = [];
                            data.conditionsList.forEach(r => {
                                let val = this.standardList.find(e => e.value == r.standard);
                                let val1 = this.chooseConditionList.find(e => e.value == r.chooseCondition);
                                list.push({ standard: val.label, chooseCondition: val1.label, conditionVal: r.conditionVal });
                            });
                            this.conditionsList = data.conditionsList;
                            this.conditionsListName = list;

                            let obj = data.action;
                            let value = '';
                            let maintainLevel = this.gradeList.find(r => r.value == obj.maintainLevel);
                            let actionType = this.actionList.find(r => r.value == obj.actionType);
                            let groupName = this.getGroupList
                                .filter(r => {
                                    return obj.groupIds.some(e => e == r.id);
                                })
                                .map(r => {
                                    return r.name;
                                })
                                .join(',');

                            let probeName = this.probeList
                                .filter(r => {
                                    return obj.probeIds.some(e => e == r.code);
                                })
                                .map(r => {
                                    return r.name;
                                })
                                .join(',');

                            if (this.actionType != 2) {
                                let deviceName = this.deviceTargetList
                                    .filter(r => {
                                        return obj.deviceTarget.some(e => e == r.value);
                                    })
                                    .map(r => {
                                        return r.label;
                                    })
                                    .join(',');
                              value = this.$t('discover_level')+ this.$t('comm_colon') + maintainLevel.label + this.$t('comm_semicolon') + this.$t('comm_grouping') + this.$t('comm_colon')  + groupName + this.$t('comm_semicolon') + this.$t('interface_mapping_dial') + this.$t('comm_colon') + probeName + this.$t('comm_semicolon') + this.$t('spec_indic')+ this.$t('comm_colon') + deviceName;
                            } else {
                                value = this.$t('discover_level')+ this.$t('comm_colon') + maintainLevel.label + this.$t('comm_semicolon') + this.$t('comm_grouping') + this.$t('comm_colon') + groupName + this.$t('comm_semicolon') + this.$t('interface_mapping_dial') + this.$t('comm_colon') + probeName;
                            }

                            this.tableModalList1.push({ action: actionType.label, attribute: value });
                        } else {
                            this.$Message.warning({ content: msg, background: true });
                        }
                    })
                    .catch(err => {
                        throw new Error(err);
                    });
            });
        },
        // 转换IPs
        convertIps(startIp,endIp){
        var startIpStr= "";
        var endIpStr= "";
        // 先取出开头IP
        var separator = ".";
        // 判断
        if(startIp.indexOf(":") >=0){
            // ipV6的格式
            separator = ":";
        }else{
            separator = ".";
            // ipv4的格式
        }
        // 得到最后的一个 .或者: 的位置
        var ipPrevIndex =  startIp.lastIndexOf(separator);
        startIpStr = startIp.substring(0,ipPrevIndex);

        // 得到结束Ip的最后一段
        ipPrevIndex =  endIp.lastIndexOf(separator);
        endIpStr = endIp.substring(ipPrevIndex+1);

        return startIp +"-"+endIpStr;
        },
        /** 删除 */
        rowRemove(type, data) {
            console.log(data);
            let checkedIds = [];
            if (type === 'plural') {
                //多选
                const idArr = Array.from(this.selectedIds);
                if (idArr.length < 1) {
                    this.$Message.warning({ content: this.$t('phytopo_select_data'), background: true });
                    return;
                } else {
                    checkedIds = idArr;
                }
            } else if (type === 'singular') {
                //单选
                checkedIds = [data.id];
            }
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: this.$t('discover_msg_delete'),
                onOk: () => {
                    this.$http
                        .PostJson('/deviceDiscoverRule/delete', { ids: checkedIds })
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                                //清空选择的数据
                                this.selectedIds = new Set();
                                this.selectedDatas = [];
                                this.queryClick(1);
                            } else {
                                this.$Message.warning({ content: msg, background: true });
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        });
                }
            });
        },
        /** 启用*/
        start() {
            const idArr = Array.from(this.selectedIds);
            console.log(idArr);
            if (idArr.length < 1) {
                this.$Message.warning({ content: this.$t('phytopo_select_data'), background: true });
                return;
            } else {
                top.window.$iviewModal.confirm({
                    title: this.$t('common_enable_prompt'),
                    content: this.$t('discover_msg_enable'),
                    onOk: () => {
                        this.$http
                            .PostJson('/deviceDiscoverRule/enable', { ids: idArr })
                            .then(({ code, data, msg }) => {
                                if (code === 1) {
                                    this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                                    //清空选择的数据
                                    this.selectedIds = new Set();
                                    this.selectedDatas = [];
                                    this.queryClick(1);
                                } else {
                                    this.$Message.warning({ content: msg, background: true });
                                }
                            })
                            .catch(error => {
                                this.$Message.warning({ content: this.$t('common_controls_fial'), background: true });
                                throw new Error(error);
                            });
                    }
                });
            }
        },
        /** 禁用*/
        stop() {
            const idArr = Array.from(this.selectedIds);
            console.log(idArr);
            if (idArr.length < 1) {
                this.$Message.warning({ content: this.$t('phytopo_select_data'), background: true });
                return;
            } else {
                top.window.$iviewModal.confirm({
                    title: this.$t('common_disable_prompt'),
                    content: this.$t('discover_msg_disable'),
                    onOk: () => {
                        this.$http
                            .PostJson('/deviceDiscoverRule/disable', { ids: idArr })
                            .then(({ code, data, msg }) => {
                                if (code === 1) {
                                    this.$Message.success({ content: this.$t('common_controls_succ'), background: true });
                                    this.queryClick(1);
                                } else {
                                    this.$Message.warning ({ content: msg, background: true });
                                }
                            })
                            .catch(error => {
                                this.$Message.warning({ content: this.$t('common_controls_fial'), background: true });
                                throw new Error(error);
                            });
                    }
                });
            }
        },
        actionChange(val) {
            console.log(val);
            this.actionType = val;
            //如果选择了采集任务（1） 需要默认选中 设备信息  端口信息  如果没有就添加
            if(this.actionType=='1'){
               if (this.deviceTarget.indexOf(3) == -1){
                this.deviceTarget.push(3)
               }
               if(this.deviceTarget.indexOf(7) == -1){
                this.deviceTarget.push(7)
                }
             }else{
                this.deviceTarget=[]
             }
        },
        /** 取消事件 */
        cancleForm(formObj) {
            if (formObj == 'editForm') {
                this.editOpen = false;
            } else {
                this.addOpen = false;
            }

            this.$refs[formObj].resetFields();
        },
        //获取采集器数据
        getGetherList(orgId, orgItem) {
            console.log(11111,orgId)
            this.gatherList = [];
            this.getherCode = '';
            this.$http
                .wisdomPost('/sys/gether/discoverList', {
                    orgId: orgId,
                    type: 2,
                    pageNo: 1,
                    pageSize: 10000
                })
                .then(({ code, data, msg }) => {
                    if (code === 1 && data) {
                        this.gatherList = data.records;
                        console.log(this.gatherList,'666666666')
                        this.getherCode = this.gatherList[0].code;
                    } else {
                        this.gatherList = [];
                    }
                })
                .catch(error => {
                    this.gatherList = [];
                    throw new Error(error);
                });
        },
        // 获取凭证
        getVoucher() {
            this.$http
                .wisdomPost('/deviceVoucher/getVoucher')
                .then(({ code, data }) => {
                    if (code === 1 && data) {
                        this.voucherList = data;
                    } else {
                        this.voucherList = [];
                    }
                })
                .catch(error => {
                    this.voucherList = [];
                    throw new Error(error);
                });
        },
        // 获取指标
        getDeviceTargets() {
            this.$http
                .wisdomPost('/deviceDiscoverRule/getDeviceTargets')
                .then(({ code, data }) => {
                    if (code === 1 && data) {
                        this.deviceTargetList = data;
                    } else {
                        this.deviceTargetList = [];
                    }
                })
                .catch(error => {
                    this.deviceTargetList = [];
                    throw new Error(error);
                });
        },
        // 分组
        getGroupSearchList() {
            const param = {
                pageNo: 1,
                pageSize: 100000
            };
            this.$http.wisdomPost('/group/list', param).then(res => {
                if (res.code === 1) {
                    if (res.data) {
                        this.getGroupList = res.data.records;
                    }
                }
            });
        },
        //获取探针列表
        getGetDepartList() {
            this.$http
                .post('/sys/gether/listByDepart', {
                    type: 1,
                    pageNo: 1,
                    pageSize: 100000
                })
                .then(({ code, data, msg }) => {
                    if (code === 1 && typeof data === 'object') {
                        this.probeList = data.records;
                    } else {
                        throw new Error(String(msg));
                    }
                });
        },
        //数据列表页码切换
        pageChange(pageNo) {
            this.queryClick(pageNo);
        },
        //数据列表页码大小改变
        pageSizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.queryClick(this.page.pageNo);
        },
        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
            if (slection.length === 0) {
                let data = this.$refs.tableList.data;
                data.forEach(item => {
                    if (this.selectedIds.has(item.id)) {
                        this.selectedIds.delete(item.id);
                    }
                });
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                });
            }
            console.log(this.selectedIds);
        },
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
            console.log(this.selectedIds);
        },
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
            console.log(this.selectedIds);
        },
        //IP操作
        pushIp(index, value, type) {
            if (type === 0) {
                this.addForm.ipArr[index] = value;
            } else if (type === 1) {
                this.editForm.ipArr[index] = value;
            }
        },
        addRow() {
            if (!this.standard) {
                this.$Message.warning({ content: this.$t('device_discovery_select_standard'), background: true });
            } else if (!this.chooseCondition) {
                this.$Message.warning({ content: this.$t('device_discovery_select_properties'), background: true });
            } else if (!this.conditionVal) {
                this.$Message.warning({ content: this.$t('device_discovery_fill_value'), background: true });
            } else {
                if (this.conditionsListName.length >= 10) {
                    this.$Message.warning({ content: this.$t('device_discovery_mac_conditions'), background: true });
                } else {
                    this.conditionsList.push({ standard: this.standard, chooseCondition: this.chooseCondition, conditionVal: this.conditionVal });

                    let val = this.standardList.find(r => r.value == this.standard);
                    let val1 = this.chooseConditionList.find(r => r.value == this.chooseCondition);
                    this.conditionsListName.push({ standard: val.label, chooseCondition: val1.label, conditionVal: this.conditionVal });
                }
            }
        },
        deleteRow(index) {
            this.conditionsList.splice(index, 1);
            this.conditionsListName.splice(index, 1);
        },
        addActionRow() {
            if (!this.actionType) {       //          this.$t('device_discovery_mac_conditions'),
                this.$Message.warning({ content: this.$t('device_discovery_select_action'), background: true });
                return;
            } else if (!this.maintainLevel) {
                this.$Message.warning({ content: this.$t('device_discovery_select_level'), background: true });
                return;
            } else if ((this.deviceTarget.indexOf(3) == -1 || this.deviceTarget.indexOf(7) == -1) && this.actionType != '2') {
                this.$Message.warning({ content: this.$t('device_discovery_warning_device'), background: true });
                return;
            } else {
                if (this.tableModalList1.length > 0) {
                    this.$Message.warning({ content: this.$t('device_discovery_mac_conditions'), background: true });
                } else {
                    let value = '';
                    let maintainLevel = this.gradeList.filter(r => {
                        return r.value == this.maintainLevel;
                    });
                    let actionType = this.actionList.filter(r => {
                        return r.value == this.actionType;
                    });
                    let groupName = this.getGroupList
                        .filter(r => {
                            return this.groupIds.some(e => e == r.id);
                        })
                        .map(r => {
                            return r.name;
                        })
                        .join(',');
                    let probeName = this.probeList
                        .filter(r => {
                            return this.probeIds.some(e => e == r.code);
                        })
                        .map(r => {
                            return r.name;
                        })
                        .join(',');

                    if (this.actionType != 2) {
                        let deviceName = this.deviceTargetList
                            .filter(r => {
                                return this.deviceTarget.some(e => e == r.value);
                            })
                            .map(r => {
                                return r.label;
                            })
                            .join(',');
                        value = this.$t('discover_level')+ this.$t('comm_colon') + maintainLevel[0].label + this.$t('comm_semicolon') + this.$t('comm_grouping') + this.$t('comm_colon')  + groupName + this.$t('comm_semicolon') + this.$t('interface_mapping_dial') + this.$t('comm_colon') + probeName + this.$t('comm_semicolon') + this.$t('spec_indic')+ this.$t('comm_colon') + deviceName;
                    } else {
                      value = this.$t('discover_level')+ this.$t('comm_colon') + maintainLevel[0].label + this.$t('comm_semicolon') + this.$t('comm_grouping') + this.$t('comm_colon') + groupName + this.$t('comm_semicolon') + this.$t('interface_mapping_dial') + this.$t('comm_colon') + probeName;
                    }

                    this.tableModalList1.push({ action: actionType[0].label, attribute: value });
                }
            }
        },
        deleteActionRow(index) {
            this.tableModalList1.splice(index, 1);
        }
    },
    destroyed() { }
};
</script>

<style lang='less'>
.ipItem {
  width: 20px;
}
</style>
<style scoped lang="less"></style>


