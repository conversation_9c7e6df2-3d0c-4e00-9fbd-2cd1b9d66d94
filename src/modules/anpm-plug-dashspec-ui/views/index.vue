<template>
  <div
    class="itemEcharts"
    :style=" 'position: relative; height: 100%;background:'+(isbigscreen?'#060D15':isdarkSkin==1?'#00000000':'#ffffff') "
  >
    <Loading :loading="loading"></Loading>
    <div class="pieBox" v-if="!loading && pie_List.length>0" style="position: relative">
      <!--<Loading :loading="pieLoading"></Loading>-->
      <draw-pie
        :node="item.node"
        :datas="item"
        :isbigscreen="isbigscreen"
        :isdarkSkin="isdarkSkin"
        :typeCode="typeCode"
        :time="{startTime:param.startTime+' 00:00:00',endTime:param.endTime+' 23:59:59'}"
        :styleProp="styleProp"
        v-for="(item, index) in pie_List"
        :key="index"
      ></draw-pie>
    </div>
    <div v-else-if="!loading && pie_List.length<1" :class="{'fillempty':isdarkSkin == 1 , 'fillempty2':isdarkSkin == 0  }">
      <p class="emptyText">{{$t('common_No_data')}}</p>
    </div>
  </div>
</template>

<script>
  function clientProperty(){
    if (document.body.offsetHeight!=0 && document.body.offsetWidth!=0) {
      return {width:document.body.offsetWidth,height:document.body.offsetHeight}
    }
  }
  import drawPie from "@/common/echarts/dashboardPie2.vue";
  import '@/timechange';
  export default {
    name: "dashSpecial",
    components: {
      drawPie
    },
    data() {
      return {
        //刷新时间间隔
        intervalTime:null,
        //刷新参数
        interrefresh:null,
        nowDate:new Date(),
        //时间参数
        startTime:null,
        endTime:null,
        loading: false,
        loading1: true,
        pieLoading: false,
        height: 260,
        param: {
          startTime:new Date().format2('yyyy-MM-dd'),
          endTime:new Date().format2('yyyy-MM-dd'),
          componentId:"",
          groupIdsStr:""
        },
        pie_List: [],
        styleProp:clientProperty(),
        typeCode:"special_statistic", //此参数由调用的时候传进来，传进来的是构件的code例（route_statistic，special_statistic，snmp_statistic）
        isbigscreen:false,
        isdarkSkin:0
      };
    },
    created() {
      this.isdarkSkin = top.window.isdarkSkin;
      const src = window.frames.frameElement.getAttribute('src'),name=window.frames.frameElement.getAttribute('name');
      if (parent.window.isEdit) {
        const params = window.frames.frameElement.getAttribute('params');
        this.intervalTime = params.split('&&topoId=')[0].split('?intervalTime=')[1];
        this.param.componentId = this.getReqParam(params,1)
        this.param.groupIdsStr = this.getReqParam(params,2)
      }else{
        this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
        this.param.componentId = this.getReqParam(window.frames.name,1)
        this.param.groupIdsStr = this.getReqParam(window.frames.name,2)
        if (window.frames.frameElement) {
          window.frames.frameElement.contentWindow.close();
        }
      }
      this.getPie(this.param);
    },
    mounted() {
         // 监听 storage 事件
      window.addEventListener('storage', this.handleStorageChange);
      // window.thisVm = this;
      this.isbigscreen = window.isbigscreen;
      if (this.intervalTime&& Number(this.intervalTime)) {
        this.interrefresh = setInterval(()=>{this.loading = true;this.getPie(this.param);},this.intervalTime * 1000);
      }
    },
    methods: {
        // 获取参数 componentId , groupIds
      getReqParam(url , type){
         var reg = /componentId=(\d+)/;
          if(type == 2){
            reg = /groupIds=([^&]*)/;
          }
          var match = url.match(reg);
          if (match) {
            return match[1];
          } else {
            return "";
          }
      },
      
         handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
      // 获取环形图数据
      getPie(param) {
        this.pie_List = [];
        let httpRequest = this.$http.wisdomPost("/home/<USER>", param)
        httpRequest.then(res => {
          let datas = res.data;
          if (res.code === 1) {
            //专线统计分析图
            if (datas && this.typeCode=='special_statistic') {
              this.pie_List.push({
                node: "pie2",
                title: this.$t('spec_line'),
                totalValue: datas.specialTotalNum,
                data: [
                  {
                    value: datas.normalNum,
                    name: this.$t('dash_normal'),
                    percent:
                      datas.normalNum == 0
                        ? '0.00'
                        : (
                          (Number(datas.normalNum) /
                            Number(datas.specialTotalNum)) *
                          100
                        ).toFixed(2)
                  },
                  {
                    value: datas.degradationNum,
                    name: this.$t('dash_deterioration'),
                    link:'specquality',
                    percent:datas.degradationRate
                  },
                  {
                    value: datas.brokenNum,
                    name: this.$t('dash_interrupt'),
                    link:'specquality',
                    percent:datas.brokenRate
                  },
                  {
                    value: datas.unTaskNum,
                    name: this.$t('but_pause'),
                    link:'specquality',
                    percent:datas.unStatusRate
                  }
                ]
              });
            }else{
              this.pie_List=[];
            }
          }else{
            this.pie_List=[];
          }
        }).catch((error)=>{
          this.loading = false;
          this.pie_List=[];
          if (parent.loading){
            parent.loading['dashspec'] = false;
          }
        }).finally(()=>{
          this.pieLoading = false;
          this.loading = false;
          if (parent.loading){
            parent.loading['dashspec'] = false;
          }
        });
      httpRequest = null
      },
    },
    beforeDestroy(){
        // 移除事件监听
      window.removeEventListener('storage', this.handleStorageChange);
      if (this.interrefresh) {
        clearInterval(this.interrefresh);
        this.interrefresh = null;
      }
    }
  };
</script>

<style scoped>
  .pieBox{
    height: 100%;
  }
</style>
