<template>
  <section class="section-special">
    <!--  设备发现页面 设备发现数据  -->
    <div class="section-top">
      <Row class="fn_box">
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 190px"
              >{{ $t("comm_time") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <DatePicker
                format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :options="timeOptions"
                v-model="timeRange"
                :editable="false"
                :clearable="false"
                style="width: 100%"
                :confirm="false"
              >
              </DatePicker>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 190px"
              >{{ $t("comm_org") }}{{ $t("comm_colon") }}</label
            >
            <TreeSelect
              v-model="treeValue"
              ref="TreeSelect"
              :data="treeData"
              :placeholder="$t('snmp_pl_man')"
              :loadData="loadOrgTreeData"
              @onSelectChange="setOrg"
              @onClear="onClear"
              @onFocus="focusFn"
            >
            </TreeSelect>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 190px"
              >{{ $t("discover_rule") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="ruleId"
                :placeholder="$t('snmp_pl_man')"
                filterable
                :only-filter-with-text="true"
                clearable
              >
                <Option
                  style="width: 200px"
                  v-for="(item, index) in ruleList"
                  :value="item.ruleId"
                  :key="index"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 300px"
              >{{ $t("device_discovery_collect_or_not")
              }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="taskCode"
                :placeholder="$t('snmp_pl_man')"
                filterable
                :only-filter-with-text="true"
                clearable
              >
                <Option
                  v-for="(item, index) in taskCodeList"
                  :value="item.id"
                  :key="index"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 190px"
              >{{ $t("discover_device_type") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Select
                v-model="deviceType"
                :placeholder="$t('snmp_pl_man')"
                clearable
                filterable
                :only-filter-with-text="true"
              >
                <Option
                  v-for="item in deviceTypeList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </div>
        </Col>
        <Col span="6">
          <div class="fn_item">
            <label class="fn_item_label" style="width: 190px"
              >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
            >
            <div class="fn_item_box">
              <Input
                v-model="keyWords"
                :placeholder="$t('device_discovery_probe_IP')"
                :title="$t('device_discovery_probe_IP')"
              />
            </div>
          </div>
        </Col>
      </Row>
      <!--面板功能按键区域-->
      <div class="tool-btn">
        <div>
          <Button
            class="query-btn"
            type="primary"
            icon="ios-search"
            v-if="permissionObj.list"
            @click="queryClick(1)"
            :title="$t('common_query')"
          ></Button>
          <Button
            class="query-btn"
            type="primary"
            icon="md-add"
            v-if="permissionObj.createtask"
            @click="openAddForm('plural', selectedIds)"
            :title="$t('common_new')"
          ></Button>
        </div>
      </div>
    </div>
    <!--  行业列表面板  -->
    <div class="section-body" style="padding: 0">
      <Loading :loading="pageLoading"></Loading>
      <div class="section-body-content">
        <div>
          <!--    列表      -->
          <Table
            ref="tableList"
            stripe
            :columns="tableColumn"
            class="fixed-left-right"
            :data="tableData"
            :no-data-text="
              pageData.total > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            size="small"
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
            <!--    列表操作列功能        -->
            <template slot-scope="{ row }" slot="taskCode">
              <span>{{ row.taskCode || $t("probetask_no") }}</span>
              <Tooltip
                max-width="700"
                :content="row.failInfo"
                placement="left"
                style="white-space: nowrap"
              >
                <Icon
                  type="ios-information-circle"
                  v-if="!row.taskCode && row.failInfo"
                  style="color: red"
                />
              </Tooltip>
            </template>

            <!-- <template slot-scope="{ row }" slot="action">
                            <span style="margin-right:6px;color: #f16643; cursor: pointer" @click="rowRemove(row)">{{$t('but_remove')}}</span>
                            <span style="color: #57a3f3; cursor: pointer" @click="openAddForm('singular',row)" v-if="!row.taskCode">{{$t('discover_create_task')}}</span>
                        </template> -->
          </Table>
          <!--   分页      -->
          <div class="tab-page" style="border-top: 0" v-if="pageData.total > 0">
            <Page
              v-page
              :current.sync="page.pageNo"
              :page-size="page.pageSize"
              :total="pageData.total"
              :page-size-opts="page.pageSizeOpts"
              :prev-text="$t('common_previous')"
              :next-text="$t('common_next_page')"
              @on-change="pageChange"
              @on-page-size-change="pageSizeChange"
              show-elevator
              show-sizer
            >
            </Page>
          </div>
        </div>
      </div>
    </div>

    <!--新建-->
    <Modal
      sticky
      v-model="addModalShow"
      width="890"
      ref="modalDrag"
      class="task-modal threshold-modal"
      :title="
        addType === 0
          ? this.$t('discover_create_task')
          : this.$t('common_update')
      "
      draggable
      :mask="true"
      @on-cancel="closeCancel"
    >
      <section class="taskStrategy-modal">
        <!-- <div v-if="addTaskType === 'plural'" style="margin-left: 55px;margin-bottom: 10px"> -->
        <div style="margin-left: 55px; margin-bottom: 10px">
          <!-- <Icon
            type="ios-information-circle"
            style="color: #fea31b; font-size: 20px"
          /> -->
          <!-- <span style="color: #ffca7b">{{
            $t("device_discovery_system_support")
          }}</span> -->
        </div>

        <Form
          :model="addForm"
          :rules="addFormRule"
          :label-width="110"
          ref="addForm"
        >
          <div class="newAddFrom" v-if="!devId">
            <FormItem
              :label="$t('comm_group') + $t('comm_colon')"
              prop="groupIds"
              class="inlineForm"
            >
              <Select
                v-model="addForm.groupIds"
                multiple
                :max-tag-count="1"
                clearable
                :placeholder="$t('comm_select_group')"
                style="width: 250px"
              >
                <Option
                  style="width: 250px"
                  v-for="item in groupList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </FormItem>
            <FormItem
              :label="$t('gether_level') + $t('comm_colon')"
              prop="maintainLevel"
            >
              <Select
                v-model="addForm.maintainLevel"
                filterable
                :only-filter-with-text="true"
                clearable
                :placeholder="$t('device_discovery_gether_level_select')"
                style="width: 250px"
              >
                <Option
                  v-for="item in maintainList"
                  :value="item.value"
                  :key="item.value"
                  >{{ item.lable }}</Option
                >
              </Select>
            </FormItem>
          </div>
        </Form>
        <div style="font-size: 14px; width: 790px">
          <div class="snmptask-title" style="text-align: right">
            <span class="tip">{{ $t("spec_indic") }}:</span>
            <span style="float: left">
              {{ $t("device_discovery_timing_indicators") }}
            </span>
            <Icon
              type="ios-add-circle"
              style="font-size: 20px; color: #2b85e4; cursor: pointer"
              @click="addRow()"
            />
          </div>
          <div class="snmptask-body">
            <div class="task_indicator_header">
              <div
                class="item_header"
                style="width: 240px"
                v-for="(item, index) in taskIndicatorHList"
                :key="item.key + index"
              >
                <span>{{ item.name }}</span>
              </div>
            </div>
            <div class="task_indicator_body">
              <div
                class="indicator_row"
                v-for="(item, index) in taskIndicatorBList"
                :key="'row' + index"
              >
                <div class="task_indicator_body_item" style="width: 240px">
                  <Select
                    v-model="item.indicatorName"
                    filterable
                    :only-filter-with-text="true"
                    :placeholder="$t('snmp_pl_man')"
                    style="width: 198px"
                    @on-change="indicatorChange($event, index)"
                  >
                    <Option
                      v-for="item in indicatorList"
                      :value="item.id"
                      :key="item.id"
                      :disabled="item.disabled"
                      :label="item.label"
                    ></Option>
                  </Select>
                </div>
                <div class="task_indicator_body_item" style="width: 240px">
                  <Input
                    v-model.trim="item.gatherPeriodValue"
                    style="width: 80px; margin-right: 10px"
                    :max="23"
                    type="number"
                    @on-change="gatherChange($event, index, 'gatherPeriod')"
                  />
                  <Select
                    class="unitHeight"
                    v-model="item.gatherPeriodUnitValue"
                    style="width: 80px"
                    :placeholder="$t('comm_unit')"
                    @on-change="gatherChange($event, index, 'periodUnit')"
                    :label-in-value="true"
                  >
                    <Option
                      v-if="
                        items.id == 2 &&
                        (item.indicatorName == 1 ||
                          item.indicatorName == 2 ||
                          item.indicatorName == 5 ||
                          item.indicatorName == 6 ||
                          item.indicatorName == 7 ||
                          item.indicatorName == 4)
                      "
                      v-for="items in deviceUnitList"
                      :value="items.id"
                      :key="items.id"
                      >{{ items.label }}</Option
                    >
                    <Option
                      v-if="
                        items.id == 3 &&
                        (item.indicatorName == 3 ||
                          item.indicatorName == 4 ||
                          item.indicatorName == 7)
                      "
                      v-for="items in deviceUnitList"
                      :value="items.id"
                      :key="items.id"
                      >{{ items.label }}</Option
                    >
                    <Option
                      v-if="items.id == 4 && item.indicatorName == 3"
                      v-for="items in deviceUnitList"
                      :value="items.id"
                      :key="items.id"
                      >{{ items.label }}</Option
                    >
                  </Select>
                </div>
                <div class="task_indicator_body_item" style="display: none">
                  <Select
                    v-model="item.operationType"
                    v-show="item.indicatorName == 1"
                    filterable
                    :only-filter-with-text="true"
                    style="width: 120px"
                    :placeholder="$t('snmp_pl_man')"
                  >
                    <Option
                      value="1"
                      v-show="addForm.factory === 1 || addForm.factory === 5"
                      >UDP-Jitter</Option
                    >
                    <Option value="2" v-show="addForm.factory === 3"
                      >ICMP-Echo</Option
                    >
                    <Option
                      value="3"
                      v-show="addForm.factory === 1 || addForm.factory === 5"
                      >ICMP-Jitter</Option
                    >
                    <Option
                      value="4"
                      v-show="addForm.factory === 2 || addForm.factory === 4"
                      >ICMP</Option
                    >
                  </Select>
                </div>
                <div class="task_indicator_body_item" style="width: 240px">
                  <Icon
                    type="md-remove-circle"
                    v-if="item.indicatorName !== 3 && item.indicatorName !== 7"
                    style="font-size: 20px; color: #2b85e4; cursor: pointer"
                    @click="deleteRow(index)"
                  />
                </div>
              </div>
            </div>
            <div style="margin-top: 10px">
              <span style="padding: 10px 0 5px 0; display: block">{{
                $t("device_discovery_map_select")
              }}</span>
              <Select
                v-model="interfaceInfo"
                multiple
                filterable
                :only-filter-with-text="true"
                :placeholder="$t('snmp_pl_man')"
              >
                <Option
                  v-for="(itme, index) in interfaceInput"
                  :key="index"
                  :value="String(itme.id)"
                  >{{ itme.labelName }}</Option
                >
              </Select>
              <span style="padding: 10px 0 5px 0; display: block">{{
                $t("device_discovery_same_IP")
              }}</span>
            </div>
          </div>
        </div>
      </section>
      <div slot="footer">
        <Button type="error" style="margin-right: 20px" @click="closeCancel">{{
          $t("common_cancel")
        }}</Button>
        <Button type="primary" @click="addSubmit()" :loading="addLoading">{{
          $t("common_verify")
        }}</Button>
      </div>
    </Modal>
    <!-- 修改弹框  -->
    <Modal
      :title="$t('common_update')"
      sticky
      v-model="editOpen"
      width="622"
      draggable
      :mask="true"
      :loading="editLoading"
      @on-ok="editSubmit"
      @on-cancel="cancleForm('editForm')"
    >
      <Form
        ref="editForm"
        :model="editForm"
        :rules="editFormRule"
        class="width_50_Form"
        @submit.native.prevent
        :label-width="120"
      >
        <FormItem :label="$t('comm_org') + $t('comm_colon')">
          <Input v-model="editForm.orgName" disabled></Input>
        </FormItem>
        <FormItem :label="$t('comm_group') + $t('comm_colon')">
          <Input v-model="editForm.groupName" disabled></Input>
        </FormItem>
        <FormItem label="IP:">
          <Input v-model="editForm.discoveryIp" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_discovery_rule') + $t('comm_colon')">
          <Input v-model="editForm.rule" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_level') + $t('comm_colon')">
          <Input v-model="editForm.maintainLevel" disabled></Input>
        </FormItem>
        <!-- 当拨测协议为icmp时，隐藏端口字段 -->
        <FormItem
          :label="$t('discover_port') + $t('comm_colon')"
          v-if="!(editForm.protocol == 'ICMP')"
        >
          <Input v-model="editForm.port" disabled></Input>
        </FormItem>
        <FormItem
          :label="$t('device_discovery_dial_test_protocol') + $t('comm_colon')"
        >
          <Input v-model="editForm.protocol" disabled></Input>
        </FormItem>
        <FormItem
          :label="$t('device_discovery_whether_monitored') + $t('comm_colon')"
        >
          <Input v-model="editForm.taskCode" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_time') + $t('comm_colon')">
          <Input v-model="editForm.startTime" disabled></Input>
        </FormItem>
        <FormItem :label="$t('discover_equipment_type') + $t('comm_colon')">
          <Select
            v-model="editForm.deviceType"
            filterable
            :only-filter-with-text="true"
            clearable
            :placeholder="$t('discover_select_device_type')"
          >
            <Option
              v-for="item in deviceTypeList"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
      </Form>
    </Modal>
  </section>
</template>
<script>
// 自动发现 / 发现数据

import moment from 'moment';
import '@/config/page.js';
import global from '../../../common/global.js';
import { mapGetters, mapActions, mapState } from 'vuex';
import locationreload from '@/common/locationReload';
import TreeSelect from '@/common/treeSelect/treeSelect.vue';
import ipv6Format from "@/common/ipv6Format";
import langFn  from '@/common/mixins/langFn'

export default {
    name: 'discoverData',
     mixins: [langFn],
    components: { TreeSelect },
    data() {
        let _this = this;
        return {
                currentSkin: sessionStorage.getItem('dark') || 1,
            treeValue: '',
            devId: '',
            rowData: null,
            anpmVersion: '1',
            pageLoading: true,
            isdarkSkin: top.window.isdarkSkin,
            addTaskType: '',
            permissionObj: {},
            //当前时间
            currentTime: Date.now(),
            /* 参数列表 */
            //时间
            timeRange: [],
            timeOptions: {
                shortcuts: [
                    {
                        text: this.$t('comm_half_hour'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 30 * 60 * 1000);
                            return [start.format('yyyy-MM-dd HH:mm:ss'), end.format('yyyy-MM-dd HH:mm:ss')];
                        }
                    },
                    {
                        text: this.$t('comm_today'),
                        value() {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime());
                            return [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_yesterday'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_7'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_last_30'),
                        value() {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_curr_month'),
                        value() {
                            let nowDate = new Date();
                            let date = {
                                year: nowDate.getFullYear(),
                                month: nowDate.getMonth(),
                                date: nowDate.getDate()
                            };
                            let end = new Date(date.year, date.month + 1, 0);
                            let start = new Date(date.year, date.month, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    },
                    {
                        text: this.$t('comm_preced_month'),
                        value() {
                            let date = new Date();
                            let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
                            let end = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
                            let start = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
                            return [start.format('yyyy-MM-dd 00:00:00'), end.format('yyyy-MM-dd 23:59:59')];
                        }
                    }
                ]
            },

            // 机构
            orgId: '',
            treeData: [],
            //规则
            ruleId: null,
            // 是否已采集
            taskCode: '',
            taskCodeList: [
                {
                    id: '1',
                    name: this.$t('device_discovery_monitored'),
                },
                {
                    id: '0',
                    name: this.$t('device_discovery_not_monitored'),
                }
            ],
            //规则列表
            ruleList: [],
            //设备类型
            deviceType: null,
            deviceTypeList: [],
            //关键字
            keyWords: '',
            //分页参数
            page: {
                pageNo: 1,
                pageSize: 10,
                pageSizeOpts: [10, 50, 100, 200, 500, 1000],
                pageLoading: false
            },
            /** 列表数据 */
            pageData: {
                total: 0,
                list: []
            },
            /** 列表选中数据 */
            //多选（为了支持翻页多选，保存的数据）
            selectedIds: new Set(),
            selectedDatas: [],
            //-------------------------------------新建表单
            addForm: {
                maintainLevel: '',
                groupIds: [],
                factory: undefined
            },
            //新建动画
            addLoading: false,
            addIds: '',
            //分组数据列表
            groupList: [],
            //运维等级数据列表
            maintainList: [],
            //新建弹框
            addType: 0,
            addModalShow: false,
            //新建表单验证规则
            addFormRule: {
                groupIds: [
                    {
                        required: false,
                        type: 'array',
                        min: 1,
                        message: this.$t('comm_select_group'),
                        trigger: 'change'
                    }
                ],
                maintainLevel: [
                    {
                        required: true,
                        message: this.$t('device_discovery_gether_level_select'),
                        trigger: 'change'
                    }
                ]
            },
            // 变更指标列表的实现
            //表头
            taskIndicatorHList: [
                { name: this.$t('comm_Index_name'), key: 'indicator_name' },
                { name: this.$t('comm_cycle'), key: 'gatherPeriod' },
                // { name: this.$t('comm_job_type'), key: 'operationType' },
                { name: this.$t('common_controls'), key: 'handleItem' }
            ],
            taskIndicatorBList: [{ indicatorName: 1, gatherPeriodValue: 1, gatherPeriodUnitValue: 1, operationType: 1, disabled: false }],
            indicatorList: [
                { id: 1, label: this.$t('device_discovery_trunk_information'), disabled: false },
                { id: 2, label: this.$t('device_discovery_port_indicators'), disabled: false },
                { id: 3, label: this.$t('comm_device'), disabled: false },
                { id: 4, label: this.$t('device_discovery_link_information'), disabled: false },
                { id: 5, label: this.$t('device_discovery_ARP_information'), disabled: false },
                { id: 6, label: this.$t('device_discovery_MAC_information'), disabled: false },
                { id: 7, label: this.$t('device_discovery_port_information'), disabled: false }
            ],
            //时延，丢包的单位
            // timeLossUnitList: [{ id: 2, label: '分' }],
            //流速的单位
            // flowUnitList: [{ id: 2, label: '分' }],
            //设备信息的单位
            deviceUnitList: [
                { id: 1, label: this.$t('comm_second') },
                { id: 2, label: this.$t('comm_minutes') },
                { id: 3, label: this.$t('comm_hour') },
                { id: 4, label: this.$t('comm_day1') },
                { id: 5, label: this.$t('comm_week') }
            ],
            operationTypeList: [
                // { id: 1, name: 'UDP-Jitter' },
                // { id: 2, name: 'ICMP-Echo' },
                // { id: 3, name: 'ICMP-Jitter' },
                // { id: 4, name: 'ICMP' }
            ],
            interfaceInput: [],
            interfaceInfo: [],
            //-------------------------------------
            /** 修改表单 */
            editForm: {
                id: null,
                ip: '',
                groupId: null,
                groupName: '',
                rule: null,
                protocol: null,
                port: '',
                startTime: null,
                taskCode: null,
                deviceType: ''
            },
            //修改验证规则
            editFormRule: {
                deviceType: [{ required: true, message: this.$t('discover_select_device_type'), trigger: 'change' }]
            },
            //修改弹框
            editOpen: false,
            //修改加载
            editLoading: true,
            /** 表格标题列 */
            tableColumn: [
                { type: 'selection', width: 30, className: 'bgColor', align: 'center' },
                // 探针ip
                {
                    title: this.$t('device_discovery_probe_IP'),
                    align: 'left',
                    width:200,
                    key: 'discoveryIp',
                    render: (h, params) => {
                        let str = params.row.discoveryIp;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        let maxWidth = params.column.width; // 获取动态传递的宽度
                        str = ipv6Format.formatIPv6Address(str,maxWidth);
                        return h('div', { style: { whiteSpace: 'pre-wrap' } }, str);
                    }
                },
                // 设备名称
                {
                    title: this.$t('phytopo_device_Name'),
                    align: 'left',
                    width: 150,
                    key: 'deviceName',
                    render: (h, params) => {
                        let str = params.row.deviceName;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        let str1 = str;
                        // if (str.length > 9) {
                        //     str1 = str.substring(0, 9) + '...';
                        // }
                        // return h(
                        //     'Tooltip',
                        //     {
                        //         props: {
                        //             placement: 'top'
                        //         }
                        //     },
                        //     [
                        //         str1,
                        //         h(
                        //             'span',
                        //             {
                        //                 slot: 'content', //slot属性
                        //                 style: {
                        //                     whiteSpace: 'normal',
                        //                     wordBreak: 'break-all'
                        //                 }
                        //             },
                        //             str
                        //         )
                        //     ]
                        // );
                         if(str !== '--') {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'left',
                  content: str,
                }

              },str)

            ])

            }else {
              return h('span',str)
            }
                    }
                },
                // 设备类型
                {
                    title: this.$t('discover_device_type'),
                    align: 'left',
                    width: 120,
                    key: 'deviceTypeName',
                    tooltip:true,
                  render: (h, params) => {
                    let str = Number(params.row.deviceType),
                        text = "--";
                    switch (str) {
                      case 1:
                        text = this.$t('snmptask_server');
                        break;
                      case 2:
                        text = this.$t('default_manager_switchboard');
                        break;
                      case 3:
                        text = this.$t('default_manager_router');
                        break;
                      case 4:
                        text = this.$t('snmptask_firewall');
                        break;
                      case 5:
                        text = this.$t('comm_unknown');
                        break;
                      default:
                        text = "--";
                        break;
                    }
                    return h("span", text);
                  },
                },
                // 设备厂商
                {   title: this.$t('snmpoid_device_merchant'),
                    align: 'left', 
                    width: this.getColumnWidth(130,150),
                    key: 'deviceFactoryName',
                  render: (h, params) => {
                    console.log(params.row);
                    let str = Number(params.row.factory),
                        text = "--";
                    switch (str) {
                      case 1:
                        text = this.$t('snmpoid_cisco');
                        break;
                      case 2:
                        text = this.$t('snmpoid_huawei');
                        break;
                      case 3:
                        text = this.$t('snmpoid_huasan');
                        break;
                      case 4:
                        text = this.$t('snmpoid_ruijie');
                        break;
                      case 5:
                        text = this.$t('snmpoid_maipu');
                        break;
                      case 30:
                        text = this.$t('snmpoid_shenxinfu');
                        break;
                      default:
                        text = "--";
                        break;
                    }
                    return h("span", text);
                    // return h('span', str === undefined || str === null || str == 'null' || str === '' ? '--' : str);
                  }
                },
                {
                    title: this.$t('snmpoid_model'),
                    align: 'left',
                    minWidth: 120,
                    key: 'showDeviceModel',
                    render: (h, params) => {
                        let str = params.row.showDeviceModel;
                        str = str === undefined || str === null || str === '' || str === 'null' ? '--' : str;
                        // let str1 = str;
                        // if (str.length > 7) {
                        //     str1 = str.substring(0, 7) + '...';
                        // }
                        // return h(
                        //     'Tooltip',
                        //     {
                        //         props: {
                        //             placement: 'top'
                        //         }
                        //     },
                        //     [
                        //         str1,
                        //         h(
                        //             'span',
                        //             {
                        //                 slot: 'content', //slot属性
                        //                 style: {
                        //                     whiteSpace: 'normal',
                        //                     wordBreak: 'break-all'
                        //                 }
                        //             },
                        //             str
                        //         )
                        //     ]
                        // );
                         if(str !== '--') {
              return h('div',{class:'table-ellipsis'},[
              h('Tooltip',{
                props: {
                  placement:'left',
                  content: str,
                }

              },str)

            ])

            }else {
              return h('span',str)
            }
                    }
                },
                { title: this.$t('comm_org'), align: 'left', minWidth: 80, key: 'orgName',tooltip:true },
                {
                    title: this.$t('discover_gether_name'),
                    align: 'left',
                    minWidth: 110,
                    key: 'getherName',
                    tooltip:true
                },
                {
                    title: this.$t('discover_rule'),
                    align: 'left',
                    minWidth: 150,
                    key: 'ruleName',
                    tooltip:true
                },
                {
                    title: this.$t('discover_discovery_time'),
                    align: 'left',
                    width: 170,
                    key: 'discoveryTime',
                    tooltip:true
                },
                // 是否采集
                {
                    title: this.$t('device_discovery_collect_or_not'),
                    align: 'left',
                    minWidth: this.getColumnWidth(130,150),
                    key: 'taskCode',
                    slot: 'taskCode'
                },
                // 操作
                {
                    title: this.$t('common_controls'),
                    align: 'center',
                    width: 110,
                    // fixed: 'right',
                    render: (h, params) => {
                      let del = h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                        transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: 'del1-btn',
                                         style: {
                                            display: this.permissionObj.delete
                                                ? "inline-block"
                                                : "none",
                                                marginRight: !params.row.taskCode && this.permissionObj.createtask ?'0px':'-20px',
                                        },
                                        on: {
                                            click: () => {
                                                this.rowRemove(params.row);
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('common_delete'))
                                ]
                            ),
                        add = h(
                                'Tooltip',
                                {
                                    props: {
                                        placement: 'left-end',
                                        transfer: true
                                    }
                                },
                                [
                                    h('span', {
                                        class: 'create1-btn',
                                        style: {
                                            display: !params.row.taskCode && this.permissionObj.createtask ? 'inline-block' : 'none',
                                            marginRight: this.permissionObj.delete ? '0px':'20px',
                                          },
                                        on: {
                                            click: () => {
                                                this.openAddForm('singular',params.row);
                                            }
                                        }
                                    }),
                                    h('span', { slot: 'content' }, this.$t('discover_create_task'))
                                ]
                            ),
                        array = [del, add];
                        // return h('div', array);
                        return h('div', {
                            style: {
                                display: 'flex', // 使用 Flexbox
                                justifyContent: 'center', // 水平居中
                                alignItems: 'center', // 垂直居中
                                gap: '20px', // 图标之间的间隔
                                height: '100%', // 确保 div 高度占满单元格
                                lineHeight: '40px', // 设置行高以帮助垂直居中
                            }
                        }, array);
                    }
                }
            ],
            molStartOpt: {
                disabledDate: date => {
                    if (_this.addForm.endDate) {
                        let end = new Date(_this.addForm.endDate);
                        return date > end;
                    } else {
                        return date > Date.now();
                    }
                }
            },
            molEndOpt: {
                disabledDate(date) {
                    if (_this.addForm.startDate) {
                        let end = new Date(_this.addForm.startDate);
                        return date < end;
                    } else {
                        return date > Date.now();
                    }
                }
            }
        };
    },
    watch: {},
    computed: {
        ...mapGetters({ defaultConfigList: 'defaultConfigList' }),
        tableData:{
            get : function() {
                let arr = this.pageData.list.map(item=>{
                    // for (const key in item) {
                    //     let str = item[key] === undefined || item[key] === null ||  item[key] === '' ||  item[key] === 'null' ? '--' :  item[key]
                    //     item[key] = str
                    // }
                   return item
                })
                return arr
            },
        },
    },
    created() {
        this.anpmVersion = JSON.parse(sessionStorage.getItem('accessToken')).anpmVersion;
        if (this.anpmVersion != '1') {
            this.indicatorList.splice(0, 1);
            this.columns.splice(11, 1);
        }
        this.$nextTick(() => {
           locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());

        })
       
        let permission = global.getPermission();
        this.permissionObj = Object.assign(permission, {});
        moment.locale('zh-cn');
        this.getRuleList();
        // this.getDeviceList();
        //获取分组下拉
        this.getGroupList();
        // 维等级数据
        this.getmaintainLevelList();
        this.queryClick(1);
        // 获取保存接口探针列表
        this.getInterfaceList();
        // 机构
        this.getTreeOrg();
        //设备类型
        this.getDeviceType();
        // this.timeRange = [new Date().format('yyyy-MM-dd 00:00:00'), new Date().format('yyyy-MM-dd 23:59:59')];
    },
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
    mounted() {
          // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
        top.window.addEventListener('message', e => {
            if (e) {
                if (e.data.type == 'msg') {
                    return;
                } else if (typeof e.data == 'object') {
                    this.isdarkSkin = e.data.isdarkSkin;
                    this.$Skin.skinChange(top.window.skin);
                } else if (typeof e.data == 'number') {
                    this.isdarkSkin = e.data;
                    this.$Skin.skinChange(top.window.skin);
                }
            }
        });
        this.getDefaultList();
    },
    methods: {
      
        ...mapActions({ getDefaultList: 'getDefaultConfigList' }),
           handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
        // 时分秒增加减少
        deleteRow(index) {
            if (this.taskIndicatorBList.length == 1) {
                this.$Message.warning(this.$t('device_discovery_at_least_one_indicator'));
                return;
            } else {
                this.taskIndicatorBList.splice(index, 1);
            }
        },
        focusFn() {
        this.getTreeOrg()
        },
        onClear() {
            this.treeValue = '';
            this.orgId = '';
        },
        addRow() {
            let objData = this.taskIndicatorBList;
            let sIds = Object.assign([], this.indicatorList);
            if (objData.length >= this.indicatorList.length) {
                this.$Message.warning(this.$t('device_discovery_most_indicator'));
                return;
            } else {
                sIds = sIds.filter(item => !objData.some(dataId => dataId.indicatorName === item.id));
                let factory = this.addForm.factory;
                //1思科、2华为、3华三
                let operationType = '3';
                if (factory == 1) {
                    operationType = '3';
                } else if (factory == 2) {
                    operationType = '4';
                } else if (factory == 3) {
                    operationType = '2';
                }
                if (sIds[0].id == 1 && this.anpmVersion == '1') {
                    //中继信息
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.relayDelayLoss,
                        gatherPeriodUnitValue: this.defaultConfigList.relayDelayLossUnit,
                        operationType: operationType,
                        disabled: false
                    });
                } else if (sIds[0].id == 2) {
                    //端口指标
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.relayFlow,
                        gatherPeriodUnitValue: this.defaultConfigList.relayFlowUnit,
                        operationType: '',
                        disabled: false
                    });
                } else if (sIds[0].id == 3) {
                    //设备信息
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.relayEqInfor,
                        gatherPeriodUnitValue: this.defaultConfigList.relayEqInforUnit,
                        operationType: '',
                        disabled: false
                    });
                } else if (sIds[0].id == 4) {
                    //链路信息
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.linkValue,
                        gatherPeriodUnitValue: this.defaultConfigList.linkUnit,
                        operationType: '',
                        disabled: false
                    });
                } else if (sIds[0].id == 5) {
                    //ARP信息
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.arpValue,
                        gatherPeriodUnitValue: this.defaultConfigList.arpUnit,
                        operationType: '',
                        disabled: false
                    });
                } else if (sIds[0].id == 6) {
                    //MAC信息
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.macValue,
                        gatherPeriodUnitValue: this.defaultConfigList.macUnit,
                        operationType: '',
                        disabled: false
                    });
                } else if (sIds[0].id == 7) {
                    //端口信息
                    this.taskIndicatorBList.push({
                        indicatorName: sIds[0].id,
                        gatherPeriodValue: this.defaultConfigList.flowInfoValue,
                        gatherPeriodUnitValue: this.defaultConfigList.flowInfoUnit,
                        operationType: '',
                        disabled: false
                    });
                }
            }
        },
        // 时分秒 方法
        indicatorChange(value, index) {
            //获取当前行数据
            let rowData = this.taskIndicatorBList[index];
            //判断个指标当前单位的最大值
            if (value == 3) {
                if (rowData.gatherPeriodUnitValue == 3 && rowData.gatherPeriodValue > 23) {
                    rowData.gatherPeriodValue = 23;
                }
            } else if (value == 1 || value == 2) {
                if (rowData.gatherPeriodValue > 59) {
                    rowData.gatherPeriodValue = 59;
                }
            }
            if(rowData.indicatorName == 1 || rowData.indicatorName == 2 || rowData.indicatorName == 5 || rowData.indicatorName == 6){
                rowData.gatherPeriodUnitValue = 2;
            }else if(rowData.indicatorName == 4 || rowData.indicatorName == 7){
                rowData.gatherPeriodUnitValue = 3;
            }else if(rowData.indicatorName == 3){
                rowData.gatherPeriodUnitValue = 4;
            }
            console.log("rowData:",rowData);
            // rowData.gatherPeriodUnitValue
        },
        gatherChange(value, index, type) {
            if (type === 'gatherPeriod') {
                const itemValue = value.target.value;
                const unitVal = this.taskIndicatorBList[index].gatherPeriodUnitValue;
                if (unitVal == 1 || unitVal == 2) {
                    //秒分 最大值
                    if (itemValue > 59) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 59;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 59;
                            value.target.value = 59;
                        });
                    }
                } else if (unitVal == 3) {
                    //小时 最大值
                    if (itemValue > 23) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 23;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 23;
                            value.target.value = 23;
                        });
                    }
                } else if (unitVal == 4) {
                    //小时 最大值
                    if (itemValue > 7) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 7;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 7;
                            value.target.value = 7;
                        });
                    }
                    if (itemValue < 1) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 1;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 1;
                            value.target.value = 1;
                        });
                    }
                }
            }
            if (type === 'periodUnit') {
                const unitVal = this.taskIndicatorBList[index].gatherPeriodUnitValue;
                const incdicatorVal = this.taskIndicatorBList[index].gatherPeriodValue;
                if (unitVal == 3) {
                    //小时 最大值
                    if (incdicatorVal > 23) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 23;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 23;
                        });
                    }
                }
                if (unitVal == 4) {
                    //天 最大值
                    if (incdicatorVal > 7) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 7;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 7;
                        });
                    }
                    if (incdicatorVal < 1) {
                        this.taskIndicatorBList[index].gatherPeriodValue = 1;
                        this.$nextTick(() => {
                            this.taskIndicatorBList[index].gatherPeriodValue = 1;
                        });
                    }
                }
            }
        },

        // 获取保存接口探针列表
        getInterfaceList() {
            let accessToken = JSON.parse(sessionStorage.getItem('accessToken'));
            let orgId = accessToken.user.orgId;
            this.$http
                .post('/sys/gether/listByDepart', {
                    type: 1,
                    orgId: null,
                    pageNo: 1,
                    pageSize: 100000
                })
                .then(({ code, data, msg }) => {
                    if (code === 1 && typeof data === 'object') {
                        this.interfaceInput = data.records;
                    } else {
                        throw new Error(String(msg));
                    }
                });
        },
        // 获取机构
        getTreeOrg(type) {
            let self = this;
            this.$http.PostJson('/org/tree', { orgId: null }).then(res => {
                if (res.code === 1) {
                    let treeNodeList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                        }
                        return item;
                    });
                    self.treeData = treeNodeList;
                }
            });
        },
        loadOrgTreeData(item, callback) {
            this.$http.PostJson('/org/tree', { orgId: item.id }).then(res => {
                if (res.code === 1) {
                    let childrenOrgList = res.data.map((item, index) => {
                        item.title = item.name;
                        item.id = item.id;
                        item.loading = false;
                        item.children = [];
                        if (index === 0) {
                        }
                        return item;
                    });
                    callback(childrenOrgList);
                }
            });
        },
        setOrg(item) {
            this.treeValue = item[0].name;
            this.orgId = item[0] ? item[0].id : null;
        },

        //查询事件
        queryClick(pageNo) {
            this.keyWords = this.keyWords.trim();
            //初始化页码
            this.page.pageNo = pageNo;
            //设置查询参数
            let start = '',
                end = '';
            if (this.timeRange[0]) {
                start = moment(this.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
            }
            if (this.timeRange[1]) {
                end = moment(this.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
            }
            if(this.timeRange[0] && this.timeRange[1]){
                let startVal = moment(this.timeRange[0],"YYYY-MM-DD hh:mm:ss").valueOf();
                let endVal = moment(this.timeRange[1],"YYYY-MM-DD hh:mm:ss").valueOf();
                if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
                    this.$Message.warning(this.$t('warning_time_not_exceed_62'));
                    return;
                }
            }
            const queryParam = {
                startTime: start,
                endTime: end,
                orgId: this.orgId,
                // 发现规则
                ruleId: this.ruleId,
                taskCode: this.taskCode,
                deviceType: this.deviceType,
                keyWords: this.keyWords,
                pageNo: this.page.pageNo,
                pageSize: this.page.pageSize
            };
            //打开加载动画
            this.pageLoading = true;
            //请求数据
            this.getTableList(queryParam);
        },
        delTrim(){
            this.keyWords = this.keyWords;
        },
        //获取列表数据
        getTableList(queryParam) {
            this.$http
                .wisdomPost('/deviceDiscoveryData/list', queryParam)
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        if (data) {
                          this.pageData.list = data.records;
                            this.pageData.total = data.total || 0;
                        } else {
                        }
                    } else {
                        this.$Message.warning(msg);
                    }
                })
                .catch(error => {
                    this.setTableListEmpty();
                })
                .finally(() => {
                    this.pageLoading = false;
                });
        },
        //设置列表数据为空列表
        setTableListEmpty() {
            this.pageData.list = [];
            this.pageData.total = 0;
        },
        /** 新建 */
        //打开新建表单
        openAddForm(type, data) {
          debugger
            this.addTaskType = type;
            // 创建单个任务
            if (type === 'singular') {
                this.rowData = { ...data };
                this.devId = data.devId;
                if (!data.factory) {
                    this.$Message.warning(this.$t('device_discovery_system_not_support'));
                    return;
                }
                this.addModalShow = true;
            }
            // 批量创建任务
            if (type === 'plural') {
                this.devId = ''
                if (this.selectedDatas.length == 0) {
                    this.$Message.error(this.$t('device_discovery_error_select'));
                    return;
                }
                let selectedDatasArr = this.selectedDatas.filter(f => !f.factory);
                if (selectedDatasArr.length > 0) {
                    this.$Message.warning(this.$t('device_discovery_warning_system_not_support'));
                    return;
                }
                // 默认取第一个值的厂商
                data = this.selectedDatas[0];
                this.addModalShow = true;
            }
            this.$nextTick(() => {
              debugger
                this.addForm.factory = data.factory;
                //1思科、2华为、3华三 , 5 迈普
                let operationType = '3';
                if (data.factory == 1) {
                    operationType = '3';
                } else if (data.factory == 2 || data.factory == 4) {
                    operationType = '4';
                } else if (data.factory == 3) {
                    operationType = '2';
                }
                this.taskIndicatorBList = [
                    { indicatorName: 1, gatherPeriodValue: this.defaultConfigList.relayDelayLoss, gatherPeriodUnitValue: this.defaultConfigList.relayDelayLossUnit, operationType: operationType, disabled: false },
                    {
                        indicatorName: 2,
                        gatherPeriodValue: this.defaultConfigList.relayFlow,
                        gatherPeriodUnitValue: this.defaultConfigList.relayFlowUnit,
                        operationType: null,
                        disabled: false
                    },
                    {
                        indicatorName: 3,
                        gatherPeriodValue: this.defaultConfigList.relayEqInfor,
                        gatherPeriodUnitValue: this.defaultConfigList.relayEqInforUnit,
                        operationType: null,
                        disabled: false
                    },
                    {
                        indicatorName: 4,
                        gatherPeriodValue: this.defaultConfigList.linkValue,
                        gatherPeriodUnitValue: this.defaultConfigList.linkUnit,
                        operationType: null,
                        disabled: false
                    },
                    {
                        indicatorName: 5,
                        gatherPeriodValue: this.defaultConfigList.arpValue,
                        gatherPeriodUnitValue: this.defaultConfigList.arpUnit,
                        operationType: null,
                        disabled: false
                    },
                    {
                        indicatorName: 6,
                        gatherPeriodValue: this.defaultConfigList.macValue,
                        gatherPeriodUnitValue: this.defaultConfigList.macUnit,
                        operationType: null,
                        disabled: false
                    },
                    {
                        indicatorName: 7,
                        gatherPeriodValue: this.defaultConfigList.flowInfoValue,
                        gatherPeriodUnitValue: this.defaultConfigList.flowInfoUnit,
                        operationType: null,
                        disabled: false
                    }
                ];
            });
        },
        //新建请求接口
        addSubmit() {
            this.$refs['addForm'].validate(async valid => {
                if (valid) {
                    this.addLoading = true;
                    let devId = await this.getDevId();
                    debugger
                    devId && (await this.saveTask(devId));
                    this.addLoading = false;
                }
            });
        },
        async getDevId() {
            let devId = '';
            let url = '';
            let param = null;
            if (this.addTaskType === 'singular') {
                url = '/device/add';
                param = {
                    deviceName: this.rowData.deviceName,
                    deviceIp: this.rowData.discoveryIp,
                    deviceType: this.rowData.deviceType,
                    factory: this.rowData.factory,
                    deviceModel: this.rowData.deviceModel,
                    showDeviceModel:this.rowData.showDeviceModel,
                    orgId: this.rowData.orgId,
                    dataId: this.rowData.id,
                    groupIds: this.addForm.groupIds.join(','),
                    getherCode: this.rowData.getherCode,
                    maintainLevel: this.addForm.maintainLevel ? this.addForm.maintainLevel : this.rowData.maintainLevel,
                    delFlag: 1,
                    voucherId: this.rowData.voucherId ? this.rowData.voucherId : null
                };
                if (this.rowData.devId) {
                    devId = this.rowData.devId;
                }
            } else {
                url = '/device/addDevs';
                let array = [];
                this.selectedDatas.forEach(item => {
                    array.push({
                        deviceName: item.deviceName,
                        deviceIp: item.discoveryIp,
                        deviceType: item.deviceType,
                        factory: item.factory,
                        deviceModel: item.deviceModel,
                        showDeviceModel:item.showDeviceModel,
                        orgId: item.orgId,
                        dataId: item.id,
                        groupIds: this.addForm.groupIds.join(','),
                        getherCode: item.getherCode,
                        maintainLevel: this.addForm.maintainLevel ? this.addForm.maintainLevel : this.rowData.maintainLevel,
                        delFlag: 1,
                        voucherId: item.voucherId ? item.voucherId : null
                    });
                    if (item.devId) {
                        devId = item.devId + ',' + devId;
                    }
                });
                param = [...array];
            }
            if (devId) {
                return devId;
            }
            await this.$http.PostJson(url, param).then(res => {
                if (res.code === 1) {
                    devId = res.data.id;
                } else {
                    this.$Message.error(res.msg);
                }
            });
            return devId;
        },
        async saveTask(devId) {
            let param = {
                devIds: devId,
                orgId: this.rowData?.orgId,
                getherId: this.rowData?.getherId,
                probeId: this.interfaceInfo.join(',')
            };
            let url = this.addTaskType === 'singular' ? '/snmptask/addTask' : '/snmptask/addSnmpDevInfo';
            let objData = this.taskIndicatorBList;
            let list = [];
            for (let i = 0, len = objData.length; i < len; i++) {
                if (len > 1) {
                    for (let j = i + 1; j < len; j++) {
                        if (objData[i].indicatorName == objData[j].indicatorName) {
                            this.$Message.warning(this.$t('device_discovery_warning_same_indicator'));
                            return;
                        }
                    }
                }
                if (objData[i].indicatorName == 1 && (objData[i].operationType == null || objData[i].operationType == '')) {
                    this.$Message.warning(this.$t('snmptask_select_type'));
                    return;
                }
                if (objData[i].gatherPeriodValue == null || objData[i].gatherPeriodValue == '' || objData[i].gatherPeriodValue == undefined) {
                    if (objData[i].indicatorName == 1) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.relayDelayLoss;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.relayDelayLossUnit;
                        // objData[i].operationType = "3";
                    } else if (objData[i].indicatorName == 2) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.relayFlow;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.relayFlowUnit;
                    } else if (objData[i].indicatorName == 3) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.relayEqInfor;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.relayEqInforUnit;
                    } else if (objData[i].indicatorName == 4) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.linkValue;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.linkUnit;
                    } else if (objData[i].indicatorName == 5) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.arpValue;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.arpUnit;
                    } else if (objData[i].indicatorName == 6) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.macValue;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.macUnit;
                    } else if (objData[i].indicatorName == 7) {
                        objData[i].gatherPeriodValue = this.defaultConfigList.flowInfoValue;
                        objData[i].gatherPeriodUnitValue = this.defaultConfigList.flowInfoUnit;
                    }
                }
                console.log('objData[i].operationType:', objData[i].operationType);
                let operationType = objData[i].operationType;
                if (!operationType) {
                    operationType = 0;
                }
                list.push({
                    rateType: objData[i].indicatorName,
                    rate: Number(objData[i].gatherPeriodValue),
                    rateUnit: objData[i].gatherPeriodUnitValue,
                    operationType: Number(operationType)
                });
            }
            param.taskList = list;
            console.log(param.taskList);
            debugger
            await this.$http.PostJson(url, param).then(res => {
                if (res.code === 1) {
                    this.selectedDatas = [];
                    this.$Message.success(res.msg);
                    this.addModalShow = false;
                    this.queryClick(1);
                } else {
                    this.$Message.error({content:res.msg,background:true});
                    if (res.code === 50) {
                        this.addModalShow = false;
                    }else{
                      this.selectedDatas = [];
                    }
                }
            });
        },

        closeCancel() {
            this.addModalShow = false;
            this.$refs['addForm'].resetFields();
            this.interfaceInfo = [];
        },
        //修改请求接口
        editSubmit() {
            this.$refs['editForm'].validate(valid => {
                if (valid) {
                    const editParam = {
                        id: this.editForm.id,
                        deviceType: this.editForm.deviceType
                    };
                    this.$http
                        .wisdomPost('/netDiscoverData/update', editParam)
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success(this.$t('comm_changed_successful'));
                                this.editOpen = false;
                            } else {
                                this.$Message.warning(msg);
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        })
                        .finally(() => {
                            //取消按钮加载效果
                            this.editLoading = false;
                            this.$nextTick(() => {
                                this.editLoading = true;
                            });
                            this.queryClick(1);
                        });
                } else {
                    //取消按钮加载效果
                    this.editLoading = false;
                    this.$nextTick(() => {
                        this.editLoading = true;
                    });
                }
            });
        },
        /** 删除 */
        rowRemove(data) {
            let ids = [data.id];
            top.window.$iviewModal.confirm({
                title: this.$t('common_delete_prompt'),
                content: this.$t('discover_msg_delete'),
                onOk: () => {
                    this.$http
                        .PostJson('/deviceDiscoveryData/delete', ids)
                        .then(({ code, data, msg }) => {
                            if (code === 1) {
                                this.$Message.success(this.$t('common_controls_succ'));
                                this.queryClick(1);
                            } else {
                                this.$Message.warning(msg);
                            }
                        })
                        .catch(err => {
                            throw new Error(err);
                        });
                }
            });
        },
        /** 取消事件 */
        cancleForm(formObj) {
            this.$refs[formObj].resetFields();
            this.addModalShow = false;
        },
        //获取规则数据
        getRuleList() {
            this.ruleList = [];
            this.$http
                .wisdomPost('/deviceDiscoveryData/getByDevRuleList')
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.ruleList = data ?? [];
                    } else {
                        this.ruleList = [];
                        this.$Message.warning(msg);
                    }
                })
                .catch(err => {
                    this.ruleList = [];
                    throw new Error(err);
                });
        },
        //获取设备数据
        // getDeviceList() {
        //     this.deviceTypeList = [];
        //     this.$http
        //         .wisdomPost('/dataTable/queryCode', { key: 'netDeviceType' })
        //         .then(({ code, data, msg }) => {
        //             if (code === 1) {
        //                 this.deviceTypeList = data ?? [];
        //             } else {
        //                 this.deviceTypeList = [];
        //                 this.$Message.warning(msg);
        //             }
        //         })
        //         .catch(err => {
        //             this.deviceTypeList = [];
        //             throw new Error(err);
        //         });
        // },
        //获取分组数据
        getGroupList() {
            this.groupList = [];
            this.$http
                .wisdomPost('/group/list', { pageNo: 1, pageSize: 10000 })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.groupList = data ? data.records : [];
                    } else {
                        this.groupList = [];
                        this.$Message.warning(msg);
                    }
                })
                .catch(err => {
                    this.groupList = [];
                    throw new Error(err);
                });
        },
        //获取运维等级数据
        getmaintainLevelList() {
            this.maintainList = [];
            this.$http
                .wisdomPost('/dataTable/queryCode', { key: 'maintainLevel' })
                .then(({ code, data, msg }) => {
                    if (code === 1) {
                        this.maintainList = data ?? [];
                    } else {
                        this.maintainList = [];
                        this.$Message.warning(msg);
                    }
                })
                .catch(err => {
                    this.maintainList = [];
                    throw new Error(err);
                });
        },
        //数据列表页码切换
        pageChange(pageNo) {
            this.queryClick(pageNo);
        },
        //数据列表页码大小改变
        pageSizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.queryClick(this.page.pageNo);
        },
        //选中table的项目
        /*全选*/
        handleSelectAll(slection) {
            if (slection.length === 0) {
                let data = this.$refs.tableList.data;
                data.forEach(item => {
                    if (this.selectedIds.has(item.id)) {
                        this.selectedIds.delete(item.id);
                        this.selectedDatas.splice(
                            this.selectedDatas.findIndex(current => item.id === current.id),
                            1
                        );
                        // this.selectedDatas.remove(JSON.stringify(item));
                    }
                });
            } else {
                slection.forEach(item => {
                    this.selectedIds.add(item.id);
                    this.selectedDatas.push(item);
                });
            }
        },
        handleSelect(slection, row) {
            this.selectedIds.add(row.id);
            this.selectedDatas.push(row);
        },
        handleCancel(slection, row) {
            this.selectedIds.delete(row.id);
            this.selectedDatas.splice(
                this.selectedDatas.findIndex(item => item.id === row.id),
                1
            );
        },
        // 获取设备类型
        getDeviceType() {
            this.$http.wisdomPost('/deviceType/queryList').then(res => {
                if (res.code === 1) {
                    this.deviceTypeList = res.data;
                } else {
                    this.$Message.error(res.msg);
                }
            });
        }
    },
    destroyed() {}
};
</script>

<style lang='less'>
.section-special {
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  padding: 0 18px;
  background: var(--contentBox_bgcolor, #f4f6f9);
  /*搜索栏*/
  .section-top {
    height: auto;
    // border-bottom: 1px solid var(--border_color,#dddddd);
    margin: -18px -18px 0 -18px;
    padding: 18px;
    padding-top: 36px;
    background: var(--contentBox_bgcolor, #f4f6f9);
    /*查询框区域*/
    .fn_box {
      margin-bottom: 10px;
      text-align: left;
      width: calc(100% - 235px);
      display: flex;
    }
    /*按钮区域*/
    .fn_tool {
      text-align: right;
      margin-bottom: 12px;
      button {
        margin-right: 8px;
      }
      button:last-child {
        margin-right: 0;
      }
    }
    .fn_item {
      display: flex;
      // flex:1;
      width: 100%;
      height: 32px;
      // margin-right: 18px;
      margin-bottom: 20px;
      padding-right: 10px;
      > label {
        display: inline-block;
        font-size: 14px;
        line-height: 1;
        padding: 9px 0;
        text-align: right;
        min-width: 75px;
      }
      .fn_item_label {
        display: inline-block;
        font-size: 14px;
        line-height: 1;
        padding: 9px 0;
        text-align: right;
        min-width: 85px;
        color: var(--search_lable_font_color, #fff);
      }
      .fn_item_box {
        display: inline-block;
        // margin-left: 8px;
        height: inherit;
        width: 100%;
      }
    }
  }
  /*分割栏*/
  /*内容栏*/
  .section-body {
    position: relative;
  }
  /*底部栏*/
}
</style>



