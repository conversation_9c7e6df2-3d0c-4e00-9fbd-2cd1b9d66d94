const lan =require('../../../common/language')
"use strict";
import { $http } from "@/server/http";
export const getDevModel = ({ commit, state }, param) => {
    $http
        .post("snmpoid/getDevModel", param)
        .then(res => {
            let data = res.data;
            var resultArr = [];
            resultArr = data.filter(item => {
                return item !== "" && item != undefined;
            });
            commit("setDevModel", resultArr);
        })
        .catch(err => {
            //console.log(err)
        });
};
// 获取客户列表
export const getClientList = ({ commit, state }, param) => {
    if (!param) {
        param = {
            pageNo: 1,
            pageSize: 10000,
            province: '',
            city: '',
            area: '',
            keyword: '',
        }
    }
    $http
        .post("org/list", param)
        .then(res => {
            let data = res.data;
            if (res.code === 1) {
                commit("setClientList", data.records);
            } else {
                throw new Error(lan.getLabel("src.EGE"))
            }
        })
        .catch(err => {
            //console.log(err)
        });
};
const getUnit = (value) => {
    switch (value) {
      case 'SECOND':
        return 1;
        break;
      case 'MINUTE':
        return 2;
        break;
      case 'HOUR':
        return 3;
        break;
      case 'DAY':
        return 4;
        break;
      case 'WEEK':
        return 5;
        break;
    }
  };
  export const getDefaultConfigList = ({ commit, state }, param) => {
    $http.PostJson('/defvalue/getCodeTableChildInfos',{parentType:"RELAY_COLLECT"}).then(({ code, data, msg }) => {
        let defaultValue = {};
        for (let i = 0, len = data.length; i < len; i++) {
          let unit = getUnit(data[i].unit);
          if (data[i].key == "DELAY_LOSS_RATE") {//中继
            defaultValue.relayDelayLoss = data[i].value;
            defaultValue.relayDelayLossUnit = unit;
          }
          if (data[i].key == "FLOW") {//端口指标
            defaultValue.relayFlow = data[i].value;
            defaultValue.relayFlowUnit = unit;
          }
          if (data[i].key == "DEVICE") {//设备信息
            defaultValue.relayEqInfor = data[i].value;
            defaultValue.relayEqInforUnit = unit;
          }
          if (data[i].key == "LINK") {//链路信息
            defaultValue.linkValue = data[i].value;
            defaultValue.linkUnit = unit;
          }
          if (data[i].key == "ARP") {//ARP信息
            defaultValue.arpValue = data[i].value;
            defaultValue.arpUnit = unit;
          }
          if (data[i].key == "MAC") {//MAC信息
            defaultValue.macValue = data[i].value;
            defaultValue.macUnit = unit;
          }
          if (data[i].key == "FLOW_INFO") {//端口信息
            defaultValue.flowInfoValue = data[i].value;
            defaultValue.flowInfoUnit = unit;
          }
        }
        commit("setDefaultConfigList", defaultValue);
      })
      .catch(err => {
        
      });
  };
