<template>
  <div>
    <!-- 深色 -->
    <div class="layout" v-if="skinValue == 1">
      <div
        ref="menuheader"
        :style="{
          position: 'fixed',
          width: '100%',
          left: '0',
          top: '0',
          'z-index': '999',
        }"
      >
        <Header class="menuHeader">
          <div class="layout-logo" id="logoHeight">
            <!-- <img
              v-if="imageBase64"
              :src="
                skinValue == 0
                  ? require('@/assets/' + shallowLogo)
                  : require('@/assets/' + deepLogo)
              "
            /> -->
            <!-- <img :src='logoImgUrl?logoImgUrl:require("@/assets/logo-title.png")' /> -->
            <img :src="logoImgUrl ? logoImgUrl : ''" v-if="imageBase64" />
            <!-- <span class="logoText">主动式网络性能透视系统</span> -->
            <span class="logoText" :style="{ 'font-size': titleSize }">{{
              systemName ? systemName : ""
            }}</span>
          </div>
          <div class="layout-nav">
            <div class="layout-nav-content">
              <li
                class="ivu-menu-item"
                v-for="(menuLev1, index) in menuNavs"
                :key="menuLev1.functionId"
                :name="menuLev1.seqNum"
                @click="menuBtn(menuLev1)"
                @mouseover="menuShow(index, menuLev1)"
                @mouseout="menuHide(index)"
                :class="[
                  parentactiveId === menuLev1.functionId ? 'active' : '',
                ]"
              >
                {{ menuLev1.functionName }}

                <div class="menuItemBox" v-if="filterMenuLev2(menuLev1)">
                  <ol class="menuItem">
                    <li
                      class="menu-title"
                      v-for="menuLev2 in menuLev1.sysFunctionList"
                      :key="menuLev2.functionId"
                      @click.self="menuBtn2(menuLev2, menuLev1.seqNum)"
                      :class="[
                        activeId === menuLev2.functionId ? 'active' : '',
                      ]"
                    >
                      {{ menuLev2.functionName }}
                      <!-- 拨测分析二级界面引导 -->
                      <div v-if="fillterTipShow(menuLev2)" :class="tipClass">
                        <div class="tip-text">{{ tipItem.text }}</div>
                        <div class="tip-btn">
                          <Button
                            class="all-btn"
                            v-if="tipActive != 12"
                            @click.stop="closeTip"
                            >{{ $t("nav_all_btn") }}</Button
                          >
                          <Button
                            class="current-btn"
                            v-if="tipActive != 2"
                            @click.stop="closeCurrent(tipItem.id)"
                            >{{ $t("nav_current_btn") }}</Button
                          >
                        </div>
                      </div>
                    </li>
                  </ol>
                  <!-- 拨测分析二级界面导航 -->
                </div>
                <!-- 界面导航 -->
                <div v-if="fillterTipShow(menuLev1)" :class="tipClass">
                  <div class="tip-text">{{ tipItem.text }}</div>
                  <div class="tip-btn">
                    <Button
                      class="all-btn"
                      v-if="tipActive != 1"
                      @click="closeTip"
                      >{{ $t("nav_all_btn") }}</Button
                    >
                    <Button
                      class="current-btn"
                      v-if="tipActive != 1"
                      @click="closeCurrent(tipItem.id)"
                      >{{ $t("nav_current_btn") }}</Button
                    >
                  </div>
                </div>
                <!-- 界面导航 -->
              </li>
            </div>
          </div>
          <div class="layout-nav-box">
            <div
              @mousemove="handleConfig"
              @mouseleave="closeConfig"
              class="layout-notice"
            >
              <Icon type="md-notifications" color="#fff" size="26" />
              <div v-show="isConfigShow" class="switchBox header-right-menu tc">
                <table>
                  <tr>
                    <th>{{ $t("comm_alarm_table_alarm_type") }}</th>
                    <th>{{ $t("comm_alarm_table_popup_prompt") }}</th>
                    <th>{{ $t("comm_alarm_table_sound_effect_prompt") }}</th>
                    <th>{{ $t("comm_alarm_table_auto_close") }}</th>
                    <th>{{ $t("comm_alarm_table_projection_screen") }}</th>
                  </tr>
                  <tr>
                    <td>{{ $t("dash_interrupt_alarm") }}</td>
                    <td>
                      <i-switch
                        v-model="statrList.breakNews"
                        @on-change="modalSwitch($event, 'break')"
                      />
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.breakSound"
                        @on-change="soundSwitch('break')"
                      />
                      <audio
                        src="../../../assets/audio/zhongduan.mp3"
                        controls
                        hidden
                        ref="audio"
                      ></audio>
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.breakClose"
                        @on-change="modalSwitch('break')"
                      />
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.breakKeep"
                        @on-change="modalSwitch('break')"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td>{{ $t("dash_deterioration_alarm") }}</td>
                    <td>
                      <i-switch
                        v-model="statrList.badNews"
                        @on-change="modalSwitch"
                      />
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.badSound"
                        @on-change="soundSwitch($event, 'bad')"
                      />
                      <audio
                        src="../../../assets/audio/liehua.mp3"
                        controls
                        hidden
                        ref="audio2"
                      ></audio>
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.badClose"
                        @on-change="modalSwitch"
                      />
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.badKeep"
                        @on-change="modalSwitch"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td>{{ $t("dash_prompt_alarm") }}</td>
                    <td>
                      <i-switch
                        v-model="statrList.otherNews"
                        @on-change="modalSwitch"
                      />
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.otherSound"
                        @on-change="soundSwitch($event, 'other')"
                      />
                      <audio
                        src="../../../assets/audio/liehua.mp3"
                        controls
                        hidden
                        ref="audio3"
                      ></audio>
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.otherClose"
                        @on-change="modalSwitch"
                      />
                    </td>
                    <td>
                      <i-switch
                        v-model="statrList.otherKeep"
                        @on-change="modalSwitch"
                      />
                    </td>
                  </tr>
                </table>
                <audio
                  src="../../../assets/audio/kaiguan.mp3"
                  controls
                  hidden
                  muted
                  ref="kaiguan"
                ></audio>
              </div>
            </div>
            <!-- 帮助中心 -->
            <div class="layout-help" v-if="helpShow">
              <Dropdown
                placement="bottom-end"
                @on-click="handleHelp"
                @on-visible-change="handlerVisible"
              >
                <img src="@/assets/help-log.png" />
                <DropdownMenu slot="list">
                  <DropdownItem
                    v-if="helpLang == 0"
                    :disabled="tipStatus == 'true'"
                    name="text"
                    >{{ $t("help_manual") }}</DropdownItem
                  >
                  <DropdownItem
                    :disabled="tipStatus == 'true'"
                    v-if="navShow"
                    :divided="helpLang == 0"
                    name="nav"
                    >{{ $t("help_nav") }}</DropdownItem
                  >
                </DropdownMenu>
              </Dropdown>
              <!-- 帮助中心界面导航 -->
              <div v-if="tipActive == 13" :class="tipClass">
                <div class="tip-text">{{ tipItem.text }}</div>
              </div>
            </div>

            <!-- /帮助中心 -->
            <div class="layout-user">
              <div
                class="header-right-user"
                style="float: left; position: relative"
                @mouseover="headerMenuShow"
                @mouseout="headerMenuHide"
              >
                <img v-if="user.userPhoto != ''" :src="userImgUrl" />
                <span v-else class="iconfont face user"></span>
                <span>{{ user.userName }}</span>
                <ul
                  class="header-right-menu toolSystem"
                  v-show="headerMenu"
                  @mouseover="headerMenuShow"
                  @mouseout="headerMenuHide"
                >
                  <li>
                    <div @click="modifyPwd" style="line-height: 28px">
                      <Icon type="key"></Icon>
                      <span>{{ $t("server_change_password") }}</span>
                    </div>
                  </li>
                  <li>
                    <div
                      class="right-language"
                      style="line-height: 28px; align-items: center"
                    >
                      <Dropdown
                        placement="left-start"
                        class="dropdown-language-menu"
                      >
                        <a href="javascript:void(0)">
                          {{ lang }}
                        </a>
                        <template #list>
                          <DropdownMenu>
                            <DropdownItem @click.native="changLanguage('zh')">{{
                              $t("comm_language_zh")
                            }}</DropdownItem>
                            <DropdownItem @click.native="changLanguage('en')">{{
                              $t("comm_language_en")
                            }}</DropdownItem>
                          </DropdownMenu>
                        </template>
                      </Dropdown>
                    </div>
                  </li>
                  <li>
                    <div @click="loginOutbtn" style="line-height: 28px">
                      <Icon type="power"></Icon>
                      <span>{{ $t("server_exit") }}</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>

            <!-- <div class="layout-skin">
              <div>
                <Switch
                  true-color="#015197"
                  false-color="#253142"
                  v-model="skinValue"
                  :true-value="1"
                  :false-value="0"
                >
                  <span slot="open">浅</span>
                  <span slot="close">深</span>
                </Switch>
              </div>
            </div> -->
          </div>
        </Header>
      </div>
      <div
        style="width: 100%; overflow: hidden; height: 100%"
        id="specialStyle"
      >
        <Content style="position: relative; height: 100%">
          <div class="contentBox">
            <div class="contentIframe">
              <iframe
                src=""
                id="sub-content-page"
                width="100%"
                height="100%"
                frameborder="0"
                scrolling="auto"
              ></iframe>
            </div>
          </div>
        </Content>
      </div>
    </div>
    <!-- /深色结束 -->
    <!-- 浅色 -->
    <LightIndex
      v-else
      @changePwd="modifyPwd"
      @loginOutbtn="loginOutbtn"
      @changLanguage="changLanguage"
      @updateStartList="updateStartList"
    />
    <!-- /浅色结束 -->
    <!-- 各种弹窗 -->
    <Modal
      sticky
      v-model="loginOutStatus"
      :title="$t('server_reminder')"
      draggable
      :mask="false"
      @on-ok="loginOutOk"
      class="serverModal"
    >
      <p>{{ $t("server_sure_exit") }}</p>
    </Modal>

    <Modal
      sticky
      :title="$t('server_change_password')"
      v-model="showUserPwd"
      :closable="true"
      width="560"
      class="serverModal"
    >
      <div slot="footer">
        <Button type="error" style="margin-right: 20px" @click="userCancel">{{
          $t("common_cancel")
        }}</Button>
        <Button type="primary" @click="submitUserPwd">{{
          $t("common_verify")
        }}</Button>
      </div>
      <Form
        ref="userPwdData"
        :model="userPwdData"
        :rules="ruleValidate"
        :label-width="150"
        class="mgB20"
      >
        <FormItem
          :label="$t('server_old_password')"
          prop="oldPassword"
          class="mgB20"
        >
          <i-input
            type="password"
            v-show="!showOldPass"
            v-model="userPwdData.oldPassword"
            :placeholder="$t('user_v_pwd_enter_old')"
            style="width: 200px"
          ></i-input>
          <i-input
            type="text"
            v-show="showOldPass"
            v-model="userPwdData.oldPassword"
            :placeholder="$t('user_v_pwd_enter_old')"
            style="width: 200px"
          ></i-input>
          <div @click="showOldPass = !showOldPass" class="pwdShow">
            {{
              showOldPass ? $t("user_hide_password") : $t("user_show_password")
            }}
          </div>
        </FormItem>
        <FormItem
          :label="$t('message_password')"
          prop="showUserPwd"
          style="margin-bottom: 10px"
        >
          <i-input
            v-show="!showPass"
            type="password"
            v-model="userPwdData.showUserPwd"
            :placeholder="$t('default_manager_enter_password')"
            style="width: 200px"
          ></i-input>
          <i-input
            v-show="showPass"
            type="text"
            v-model="userPwdData.showUserPwd"
            :placeholder="$t('default_manager_enter_password')"
            style="width: 200px"
          ></i-input>
          <div @click="showPass = !showPass" class="pwdShow">
            {{ showPass ? $t("user_hide_password") : $t("user_show_password") }}
          </div>
        </FormItem>
        <div class="input_span mgB20 mgT20" v-if="isChinese == true">
          <label style="width: 150px">{{ $t("message_Intensity") }}</label>
          <span :class="{ color1: one }" id="one"></span>
          <span :class="{ color2: two }" id="two"></span>
          <span :class="{ color3: three }" id="three"></span>
        </div>
        <div class="input_span mgB20 mgT20" v-if="isChinese != true">
          <label style="width: 150px">{{ $t("message_Intensity") }}</label>
          <span :class="{ color1: one }" id="oneUs"></span>
          <span :class="{ color2: two }" id="twoUs"></span>
          <span :class="{ color3: three }" id="threeUs"></span>
        </div>
        <FormItem
          :label="$t('message_password_confirmation')"
          prop="rePassword"
          class="mgB20"
          style="margin-top: 10px"
        >
          <i-input
            type="password"
            v-show="!showPass"
            v-model="userPwdData.rePassword"
            :placeholder="$t('message_password_again')"
            style="width: 200px"
          ></i-input>
          <i-input
            type="text"
            v-show="showPass"
            v-model="userPwdData.rePassword"
            :placeholder="$t('message_password_again')"
            style="width: 200px"
          ></i-input>
        </FormItem>
        <FormItem label="" class="mgB20">
          <span style="color: red">{{ $t("message_password_consist") }}</span>
        </FormItem>
      </Form>
    </Modal>
    <div class="tipsBox">
      <!-- 中断告警弹窗 -->
      <transition name="moveR">
        <div
          :class="
            this.skinValue == 0
              ? 'modal-backdrop light-tipsModalStyle1'
              : 'modal-backdrop tipsModalStyle1'
          "
          v-show="tipsModal"
          :style="
            this.skinValue == 0
              ? 'border-right: 1px solid rgba(254, 92, 94, 0.4)'
              : 'none'
          "
        >
          <div class="modal-header">
            <h3>{{ $t("dash_interrupt_alarm") }}</h3>
            <Icon
              type="ios-close"
              size="31"
              color="#fff"
              @click="tipsModal = false"
            />
          </div>
          <div class="modal-body">
            <ul>
              <li
                v-for="(item, index) in breakList"
                :key="index"
                class="alarmTypeColor1"
              >
                <i class="alarmSign"></i>
                <div class="tipsModalRight">
                  <div class="alarmTime">
                    <Icon type="md-time" size="20" />
                    <span>{{ item.faultStartTime || "--" }}</span>
                  </div>
                  <div class="alarmTipsTitle">
                    <div>
                      <label class="alarmType">{{ returnLabel(item) }}</label>
                      <span class="alarmIp"
                        >{{ item.errorIps | ipGet
                        }}{{ getRecoveryedLabel(item) }}
                        <!-- {{item.recoveryed == '0'? "（"+this.$t('common_recovered')+"）": "（"+this.$t('common_unrecovered')+"）"}} -->
                      </span>
                    </div>
                  </div>
                  <div>
                    <p
                      v-if="item.faultDesc != null"
                      style="word-break: break-all"
                    >
                      {{ item.faultDesc }}
                    </p>
                    <p v-else style="text-align: center">
                      {{ $t("dash_no_content") }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div
            class="modal-footer"
            :style="
              this.skinValue == 0
                ? 'border-top: 1px solid #E4E7ED;border-bottom: 1px solid rgba(254, 92, 94, 0.4)'
                : 'border-top: 1px solid #3b262f;'
            "
          >
            <Button class="btnConfirm" @click="handleFault('break')">{{
              $t("comm_go_to")
            }}</Button>
          </div>
        </div>
      </transition>

      <!-- 劣化告警弹窗 -->
      <transition name="moveR">
        <div
          :class="
            this.skinValue == 0
              ? 'modal-backdrop2 light-tipsModalStyle2'
              : 'modal-backdrop2 tipsModalStyle2'
          "
          v-show="tipsModal2"
          :style="
            this.skinValue == 0
              ? 'border-right: 1px solid rgba(254, 163, 27, 0.4)'
              : 'none'
          "
        >
          <div class="modal-header">
            <h3>{{ $t("dash_deterioration_alarm") }}</h3>
            <Icon
              type="ios-close"
              size="31"
              color="#fff"
              @click="tipsModal2 = false"
            />
          </div>
          <div class="modal-body">
            <ul>
              <li
                v-for="(item, index) in badList"
                :key="index"
                class="alarmTypeColor2"
              >
                <i class="alarmSign"></i>
                <div class="tipsModalRight">
                  <div class="alarmTime">
                    <Icon type="md-time" size="20" />
                    <span>{{ item.faultStartTime || "--" }}</span>
                  </div>
                  <div class="alarmTipsTitle">
                    <div>
                      <label class="alarmType">{{ returnLabel(item) }}</label>
                      <span class="alarmIp"
                        >{{ item.errorIps | ipGet
                        }}{{ getRecoveryedLabel(item) }}
                        <!-- {{item.recoveryed == '0'? "（"+this.$t('common_recovered')+"）": "（"+this.$t('common_unrecovered')+"）"}} -->
                      </span>
                    </div>
                  </div>
                  <div>
                    <p
                      v-if="item.faultDesc != null"
                      style="word-break: break-all"
                    >
                      {{ item.faultDesc }}
                    </p>
                    <p v-else style="text-align: center">
                      {{ $t("dash_no_content") }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div
            class="modal-footer"
            :style="
              this.skinValue == 0
                ? 'border-top: 1px solid #E4E7ED;border-bottom: 1px solid rgba(254, 163, 27, 0.4)'
                : 'border-top: 1px solid #412b0b;'
            "
          >
            <Button class="btnConfirm" @click="handleFault('bad')">{{
              $t("comm_go_to")
            }}</Button>
          </div>
        </div>
      </transition>

      <!-- 提示信息弹窗 -->
      <transition name="moveR">
        <div
          :class="
            this.skinValue == 0
              ? 'modal-backdrop3 light-tipsModalStyle3'
              : 'modal-backdrop3 tipsModalStyle3'
          "
          v-show="tipsModal3"
          :style="
            this.skinValue == 0
              ? 'border-right: 1px solid rgba(5, 212, 255, 0.52)'
              : 'none'
          "
        >
          <div class="modal-header">
            <h3>{{ $t("server_prompt_message") }}</h3>
            <Icon
              type="ios-close"
              size="31"
              color="#fff"
              @click="tipsModal3 = false"
            />
          </div>
          <div class="modal-body">
            <ul>
              <li
                v-for="(item, index) in otherList"
                :key="index"
                class="alarmTypeColor3"
              >
                <i class="alarmSign"></i>
                <div class="tipsModalRight">
                  <div class="alarmTime">
                    <Icon type="md-time" size="20" />
                    <span>{{ item.faultStartTime || "--" }}</span>
                  </div>
                  <div class="alarmTipsTitle">
                    <div>
                      <label class="alarmType">{{ returnLabel(item) }}</label>
                      <!-- 专线告警、端口告警 才显示状态 -->
                      <span
                        v-if="
                          item.faultType == 9 ||
                          item.faultType == 10 ||
                          item.faultType == 11
                        "
                        class="alarmIp"
                      >
                        {{ getRecoveryedLabel(item) }}
                        <!-- {{item.recoveryed == 0 ? "（"+this.$t('common_recovered')+"）" : "（"+this.$t('common_unrecovered')+"）" }} -->
                      </span>
                    </div>
                  </div>
                  <div>
                    <p
                      v-if="item.faultDesc != null"
                      style="word-break: break-all"
                    >
                      {{ item.faultDesc }}
                    </p>
                    <p v-else style="text-align: center">
                      {{ $t("dash_no_content") }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div
            class="modal-footer"
            :style="
              this.skinValue == 0
                ? 'border-top: 1px solid #E4E7ED;border-bottom: 1px solid rgba(5, 212, 255, 0.52)'
                : 'border-top: 1px solid #093c5b;'
            "
          >
            <Button class="btnConfirm" @click="handleFault('other')">{{
              $t("comm_go_to")
            }}</Button>
          </div>
        </div>
      </transition>
    </div>
    <!-- /各种弹窗 -->
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import ipv6Format from "@/common/ipv6Format";


//二级iframe页面改变颜色函数
function skinChange(obj) {
  const property = Object.keys(obj);
  const color = Object.keys(obj).map(function (i) {
    return obj[i];
  });
  let root = document.documentElement;
  for (let i = 0; i < property.length; i++) {
    root.style.setProperty(property[i], color[i]);
  }
}
function iframeIsLoad(iframe, callback) {
  if (iframe.attachEvent) {
    iframe.attachEvent("onload", function () {
      callback && callback();
    });
  } else {
    iframe.onload = function () {
      callback && callback();
    };
  }
}
function iframeAutoFit(
  iframeObj = document.getElementById("sub-content-page")
) {
  var bHeight = iframeObj.contentWindow.document.body.scrollHeight;
  var dHeight = iframeObj.contentWindow.document.documentElement.scrollHeight;
  setTimeout(function () {
    if (!iframeObj) return;
    iframeObj.height = iframeObj.Document
      ? iframeObj.Document.body.scrollHeight
      : iframeObj.contentDocument.body.offsetHeight;
  }, 200);
}
const resizeIframeHeight = (iframeEl, resizeRadio) => {
  const iframeWindow =
    iframeEl.contentWindow || iframeEl.contentDocument.parentWindow;
  if (iframeWindow.document.body) {
    //获取主容器高度，iframe内div容器高度会自己撑开
    if (iframeWindow.document.querySelector("#app")) {
      const height = iframeWindow.document.querySelector("#app").offsetHeight;
      //获取窗口大小
      const windowHeight = document.body.clientHeight;
      const iframeMinheight = windowHeight - 100;
      iframeEl.height = height * (1 + Math.abs(1 - resizeRadio)) + "px";
    }
  }
};
var resizeRadio = 1;
const resizeZoom = () => {
  var ratio = 1,
    iframeRatio = 1,
    screen = 1540,
    ua = navigator.userAgent.toLowerCase();
  var screentWidth = document.body.clientWidth;
  if (~ua.indexOf("msie")) {
    if (screen.deviceXDPI && screen.logicalXDPI) {
      ratio = screen.deviceXDPI / screen.logicalXDPI;
    }
  } else if (
    window.outerWidth !== undefined &&
    window.innerWidth !== undefined
  ) {
    ratio = window.innerWidth / screen;
    iframeRatio = window.innerWidth / screentWidth;
  }

  if (ratio) {
    ratio = ratio.toFixed(4);
    resizeRadio = iframeRatio;
  }
  // document.getElementsByClassName('ivu-layout-header')[0].style.height =56 * (Math.abs(1-ratio)+1) + 'px';
  document.getElementsByClassName("ivu-layout-header")[0].style.zoom = ratio;
  document.getElementsByClassName("contentBox")[0].style.paddingTop =
    ratio * 90 + "px";
  // document.getElementsByClassName('linkArea')[0].style.zoom = ratio;
  // console.log(window.frames['sub-content-page'].contentWindow.document.body)
  // window.frames['sub-content-page'].contentWindow.document.body.style.zoom = ratio;
};

window.onload = function () {
  // const timeId = setInterval(() => {
  //     //选择iframe
  //     const iframeEl = document.getElementById('sub-content-page');
  //     resizeIframeHeight(iframeEl, resizeRadio);
  // }, 200);
  let allConfirmList = document.getElementsByClassName("modalIndex");
  if (allConfirmList && allConfirmList.length > 0) {
    for (let i = 0; i < allConfirmList.length; i++) {
      allConfirmList[i].style.zIndex = 50000;
      allConfirmList[i].parentNode.firstElementChild.style.zIndex = 50000;
    }
  }
  window.addEventListener("resize", () => {
    resizeZoom();
  });
};
// import md5 from 'js-md5';
import { addDraggable } from "@/common/drag.js";
import "@/timechange";
import global from "../../../common/global.js";
import handlePassword from "@/common/handlePassword";
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import SeverEnum from "../utils/enumeration";
import urlData from "@/common/helpManul/help-cn.js";

export default {
  name: "frame",
  components: {
    LightIndex: () => import("./lightIndex.vue"),
  },
  computed: {
    // textSize() {

    //     if(this.systemName.length >12) {
    //        return true
    //     }else {
    //         return false

    //     }

    // },
    ...mapState("m_light", ["statrListLight"]),

    ...mapState("m_help", ["tipActive", "tipStatus"]),
    // sever页导航的内容
    // tipItem() {
    //    let arr = this.tipList.filter(item => item.id == this.tipActive)
    //    console.log(arr,'arr')
    //     return arr[0]

    // },
    // 导航的定位样式
    tipClass() {
      // debugger

      const obj = SeverEnum.positionClass.find(
        (item) => item.id == this.tipActive
      );
      console.log(obj, this.tipActive, SeverEnum.positionClass, "数据");
      if (obj) {
        return ["nav-tip", obj.value];
        // debugger
      } else {
        return "nav-tip";
      }
    },
    // 界面导航的显示与隐藏
    tipShow() {
      console.log(this.tipStatus, "tipStatus");
      if (this.tipStatus == "true") {
        console.log(true, "true......................");
        if (
          this.tipActive == 1 ||
          this.tipActive == 2 ||
          this.tipActive == 8 ||
          this.tipActive == 9 ||
          this.tipActive == 10 ||
          this.tipActive == 11 ||
          this.tipActive == 12 ||
          this.tipActive == 13
        ) {
          return true;
        } else {
          return false;
        }
        return true;
      } else {
        // debugger
        return false;
      }
    },
    // 没有权限的用户无法开启界面导航
    navShow() {
      if (sessionStorage.getItem("nav-per") == "true" && this.isLicense) {
        return true;
      } else {
        return false;
      }
    },
  },
  data() {
    const valiPwd = (rule, value, callback) => {
      if (value === "") {
        var _msg = this.$t("user_update_password_validation_error_tip");
        callback(new Error(_msg));
      }
      if (value === this.userPwdData.oldPassword) {
        var _msg = this.$t("user_update_password_equal_tip");
        callback(new Error(_msg));
      }
      let num = /\d/,
        sletter = /[a-z]/,
        bletter = /[A-Z]/,
        symbol = /[,\.@!#%'\+\*\-:;^_`]/;
      let hasNum = num.test(value);
      let hasSletter = sletter.test(value);
      let hasBletter = bletter.test(value);
      let hasSymbol = symbol.test(value);
      //  点亮强度条
      //符合数量   如果>=1  红色    =2 黄色  =3 绿色
      let conformNum = 0;
      if (hasNum) ++conformNum;
      if (hasSletter || hasBletter) ++conformNum;
      if (hasSymbol) ++conformNum;
      console.log(conformNum, "密码进度条。。。。。。。。。");
      if (conformNum == 1) {
        this.one = true;
        this.two = false;
        this.three = false;
      } else if (conformNum == 2) {
        this.one = true;
        this.two = true;
        this.three = false;
      } else if (conformNum == 3) {
        this.one = true;
        this.two = true;
        this.three = true;
      }
      if (
        ((hasNum && hasSletter && hasBletter) ||
          (hasNum && hasSletter && hasSymbol) ||
          (hasNum && hasBletter && hasSymbol) ||
          (hasSletter && hasBletter && hasSymbol)) &&
        value.length > 0 &&
        value.length <= 20
      ) {
        callback();
      } else {
        var _msg = this.$t("user_update_password_validation_error_tip");
        callback(new Error(_msg));
      }
    };
    const valiPwdr = (rule, value, callback) => {
      if (value === this.userPwdData.showUserPwd) {
        callback();
      } else {
        var _msg = this.$t("user_update_password_tip");
        callback(new Error(_msg));
      }
    };
    const valiPwds = (rule, value, callback) => {
      //长度验证方法
      if (value.length > rule.length) {
        return false;
      } else {
        return true;
      }
    };
    return {
      memoryTimer:null,
      titleSize: "20px",
      // 控制界面导航tip显示
      // tipActive:1,
      // 控制需要界面导航的状态
      // tipStatus:true,
      // 控制界面导航的显示内容
      menuHeated: null,
      isOver: false,
      isLicense: true,
      helpShow: true,
      tipItem: {},
      // 界面导航id与定位样式的对应关系
      // 界面导航显示名字的控制
      activeTipName: {
        en: "Probe Task",
        cn: "拨测分析",
      },
      // 控制系统配置的显示隐藏
      isConfigShow: false,

      isChinese: true,
      lang: "",
      logoImgUrl: "",
      systemName: "",
      imageBase64: "",
      //一键换肤
      skinValue: 1,
      // skinValue:1,
      shallowLogo: "logo1.png",
      deepLogo: "shenLogo.png",
      // 中断告警
      statrList: {
        breakNews: false, //中断告警弹窗提示
        breakSound: false, //中断告警音效提示
        breakClose: false, //中断告警自动关闭
        breakKeep: false, //中断告警全屏保持
        // 劣化告警
        badNews: false, //劣化告警弹窗提示
        badSound: false, //劣化告警音效提示
        badClose: false, //劣化告警自动关闭
        badKeep: false, //劣化告警全屏保持
        // 提示告警
        otherNews: false, //提示告警弹窗提示
        otherSound: false, //提示告警音效提示
        otherClose: false, //提示告警自动关闭
        otherKeep: false, //提示告警全屏保持
      },
      modalState: false,
      breakList: [],
      badList: [],
      otherList: [],
      maxId: "",
      brominId: "",
      degminId: "",
      tipsModal: false,
      tipsModal2: false,
      visitorId: "",
      tipsModal3: false,
      iframSrc: "/home",
      activeId: null,
      parentactiveId: null,
      preMenu: "",
      activeName: "",
      one: false,
      two: false,
      three: false,
      showPass: false,
      showOldPass: false,
      accessToken: {},
      loginOutStatus: false,
      editPwdStatus: false,
      headerMenu: false,
      showUserPwd: false,
      user: {
        userPhoto: "",
        userName: "admin",
      },
      userPwdData: {
        showUserPwd: "",
        oldPassword: "",
        rePassword: "",
      },
      ruleValidate: {
        showUserPwd: [
          { validator: valiPwd, trigger: "change", required: true },
        ],
        oldPassword: [
          {
            required: true,
            message: this.$t("user_v_pwd_enter_old"),
            change: "blur",
          },
          {
            message: this.$t("user_v_pwd_len_32"),
            trigger: "change",
            length: 32,
            validator: valiPwds,
          },
        ],
        rePassword: [
          { required: true, validator: valiPwdr, trigger: "change" },
        ],
      },
      /*新菜单参数*/
      menuNavs: [],
      /*新菜单参数完*/

      getTimelyData: null,
      soundTips: false,
      newsTips: false,
      //定时执行获取session是否过期
      sessionInterval: null,
      tipList: [
        { id: 1, state: false, text: this.$t("nav_step1_tip") },
        { id: 2, state: false, text: this.$t("nav_step2_tip") },
        { id: 3, state: false, text: this.$t("nav_step3_tip") },
        { id: 4, state: false, text: this.$t("nav_step4_tip") },
        { id: 5, state: false, text: this.$t("nav_step5_tip") },
        { id: 6, state: false, text: this.$t("nav_step6_tip") },
        { id: 7, state: false, text: this.$t("nav_step7_tip") },
        { id: 8, state: false, text: this.$t("nav_step8_tip") },
        { id: 9, state: false, text: this.$t("nav_step9_tip") },
        { id: 10, state: false, text: this.$t("nav_step10_tip") },
        { id: 11, state: false, text: this.$t("nav_step11_tip") },
        { id: 12, state: false, text: this.$t("nav_step12_tip") },
        { id: 13, state: false, text: this.$t("nav_step13_tip") },
      ],
      helpLang: 0,
      // memoryThreshold:4096
      memoryThreshold:1536
    };
  },
  deactivated() {
    this.$http
      .post("/loginOut", param)
      .then((res) => {
        if (res.code == 1) {
          sessionStorage.clear();
          location.href =
            window.location.hostname === "localhost"
              ? "anpm-plug-login-ui.html"
              : "/login/index.html";
        } else {
          console.log("退出失败.");
        }
      })
      .catch((err) => {
        console.log(err);
      });
  },
  async created() {
    // if(sessionStorage.getItem('nav-per') == null)
    // debugger
    // await this.isPermission()
    // 验证 lisence 是否过期
   
     this.getMenuList();
     this.skinValue = sessionStorage.getItem("dark") > 0 ? 1 : 0
    this.checkLisence();
    // 保存屏幕宽度，给构建modal使用
   

    if (this.tipStatus == "true" && this.tipActive == 2) {
      this.changeTipActive(1);
    }

    let permission = sessionStorage.getItem("nav-per");
    const isFirst = localStorage.getItem("isFirst");
    console.log(isFirst, "isFirst");
    // // 如果没有权限关闭界面导航
    // if(permission == 'false') {
    //     this.changeTipStatus(false)
    //     this.changeTipActive(null)
    // }
    if (isFirst && isFirst == "true" && permission == "true") {
      // 第一次登录 并且有权限
      this.getLisence(1);
    } else {
      this.getLisence(2);
    }
    // // 如果有权限校验lisence
    // if(this.tipStatus == 'true') {

    //     this.getLisence(1)
    // }else {
    //     if(permission == 'true') {
    //         this.getLisence(2)
    //     }
    // }

    var langStr = localStorage.getItem("locale") || "zh";
    if (langStr === "zh") {
      this.lang = this.$t("comm_language_title");
      this.$i18n.locale = langStr;
      this.isChinese = true;
    } else {
      this.lang = this.$t("comm_language_title");
      this.$i18n.locale = langStr;
      this.isChinese = false;
    }
    this.getSystemInfo();
    this.getUniqueCode();
    console.log(sessionStorage.getItem("dark"));
    // this.skinValue = sessionStorage.getItem("dark")??0;
    window.$iviewModal = this.$Modal;
    if (sessionStorage.getItem("accessToken")) {
      const accessToken = JSON.parse(sessionStorage.getItem("accessToken"));
      // this.loginUser = accessToken.user.userName;
      // if(accessToken.user.roles.length > 0){
      // let role = accessToken.user.roles[0].roleId;
      // if(role == 4){
      //   this.roleType = true;
      //   this.getAuditAlarm = setInterval(this.auditAlarm, 1000 * 60)
      // }
      // if(role == 2){
      //   if(sessionStorage.getItem('isGly') != 1){
      //     this.auditMemory();
      //   }
      // }
      // }
      accessToken.user.roles.map((r) => {
        if (r.roleName === "系统管理员") {
          // if(sessionStorage.getItem('isGly') != 1){
          //   this.auditMemory();
          // }
          this.auditMemory();
        }
      });
    } else {
      // this.loginUser = '';
    }

    localStorage.removeItem("isFirst");
  },
  watch: {
    skinValue: {
      handler(val) {
        console.log(val);
        let pie_legend_normal_color_arr = ['#03B999','#00FFEE']
        let pie_legend_deterioration_color_arr = ['#FEA31B','#FEA31B']
        let pie_legend_break_color_arr = ['#FE5C5C','#FE5C5C']
        let pie_legend_suspend_color_arr = ['#97C5E9','#4EAED2']
        let pie_count_color_arr = ['#0290FD','#ffffff']
        let point_name_bg_arr = ['#F5F7FA','rgba(46, 72, 93, 0.2)']
        // 以上仪表盘饼图
        let body_b_color_arr = ["#F5F6FA", "#060D15"];//整体背景颜色
        let sidebar_b_color_mr_arr = ["#F5F6FA", "#061824"];//左边菜单栏背景颜色
        let body_conent_b_color_arr = ["#ffffff", "#061824"];//表格内容的背景颜色
        let search_lable_font_color_arr = ["#515A6E", "#fff"];//查询条件lable标题字体颜色
        let button_background_color_arr = ["#0290FD", "#05EBEB"];//查询按钮背景颜色
        let button_background_color_arr_two = ["#0290FD", "#049DEC"];//查询按钮背景颜色
        let button_background_color_arr_three = ["#FFFFFF", "#151106"];//查询按钮背景颜色
        let reset_export_del_button_font_color_arr = ["#0290FD", "#05EEFF"];//重置，导出按钮字体颜色
        let del_button_font_color_arr = ["#FEA31B", "#fea31b"];//重置按钮字体颜色
        let reset_export_button_border_1_color_arr = ["#0290FD", "#015197"];//重置导出按钮边框颜色
        let reset_export_button_border_2_color_arr = ["#0290FD", "#31F0FE"];//重置导出按钮边框颜色
        let reset_export_button_border_3_color_arr = ["#0290FD", "#015197"];//重置导出按钮边框颜色
        let scrollbar_thumb_bg_color_arr=["#DCDFE6", "#015197"];//滚动条里面小方块背景颜色
        let scrollbar_track_bg_color_arr=["#F5F7FA", "#06324D"];//滚动条里面轨道背景颜色
        let scrollbar_thumb_shadow_color_arr=["#fff", "rgba(6, 50, 77, 1)"];//滚动条里面小方块背景颜色
        let scrollbar_track_shadow_color_arr=["#fff", "rgba(0, 0, 0, 0.2)"];//滚动条里面轨道背景颜色
        let query_btn_hover_bg_1_color_arr = ["#5CADFF","#049DEC"];//查询按钮悬浮上去后变化背景颜色
        let query_btn_hover_bg_2_color_arr = ["#5CADFF","#05EBEB"];//查询按钮悬浮上去后变化背景颜色
        let query_btn_active_bg_1_color_arr = ["rgba(6, 132, 233, 1)","rgba(4, 157, 236, 0.8)"];//查询按钮点击之后变化背景颜色
        let query_btn_active_bg_2_color_arr = ["rgba(6, 132, 233, 1)","rgba(5, 235, 235, 0.8)"];//查询按钮点击之后变化背景颜色
        let modal_b_color_arr = ["#ffffff", "#08101A"];
        let modal_input_border_color_arr = ["#dcdee2", "#35455d"];
        let header_b_color_arr = ["#1252c8", "#253142"];
        let header_font_color_arr = ["#ffffff", "#ffffff"];//最上面菜单栏名字文字颜色
        let search_check_box_background_color_arr = ["#0290FD", "#02b8fd"];//查询条件复选框背景图
        let search_check_box_font_color_arr = ["#FFFFFF", "#060d15"];//查询条件复选框文字颜色
        let search_check_box_bg_tabStyle_color_arr=["#FFFFFF", "#061824"];//故障清单故障历时复选框背景图
        let search_check_box_font_tabStyle_color_arr=["#0290FD", "#fff"];//故障清单故障历时选中后文字复选框背景图
        let search_check_box_font_tabStyle_two_color_arr=["#515A6E", "#5ca0d5"];//故障清单故障历时文字复选框背景图
        let search_ivu_select_input_boder_color_1_arr=["#DCDFE6", "#060d15"];//搜索栏查询条件输入框颜色
        let search_ivu_select_input_boder_color_2_arr=["#DCDFE6", "#015197"];//搜索栏查询条件输入框颜色
        let search_ivu_select_input_boder_color_3_arr=["#DCDFE6", "#31f0fe"];//搜索栏查询条件输入框颜色
        let search_ivu_select_input_boder_color_4_arr=["#DCDFE6", "#ffffff"];//搜索栏查询条件输入框颜色
        let button_networdFault_del_bg_color_arr=["#FEA31B","#8a5505"];//故障清单-清除按钮颜色
        let button_networdFault_del_bg_2_color_arr=["#ffffff","#061824"];//故障清单-清除按钮颜色
        let button_networdFault_del_bg_3_color_arr=["#FEA31B","#b77006"];//故障清单-清除按钮颜色
        let button_networdFault_del_bg_4_color_arr=["#FEA31B","#fea31b"];//故障清单-清除按钮颜色
        let networdFault_type_border_color_arr=["#fff","#04478e"];//故障清单-故障类型复选框边框颜色
        let networdFault_type_border_normal_color_arr=["#DCDFE6","#04478e"];//故障清单-故障类型正常复选框边框颜色
        let networdFault_type_border_normal_font_color_arr=["#515A6E","#5ca0d5"];//故障清单-故障类型正常复选框字体颜色
        let networdFault_duration_border_select_color_arr=["#0290FD","#05eeff"];//故障清单-故障类型正常复选框字体颜色
        let query_org_select_color_arr=["#808695","#05eeff"];//查询条件：组织查询方向颜色
        let more_btn_font_color_arr = ["#515A6E","#02b8fd"];//查询栏-更多按钮-文字颜色
        let more_btn_background_image_1_color_arr = ["#DCDFE6","#015197"];//查询栏-更多按钮-背景和边框颜色
        let more_btn_background_image_2_color_arr = ["#DCDFE6","#31f0fe"];//查询栏-更多按钮-背景和边框颜色
        let more_btn_ivu_dropdown_item_font_color_arr=["#515A6E","#dff1ff"];//查询栏-更多按钮-下拉列表-正常字体颜色
        let more_btn_ivu_dropdown_item_hover_bg_color_arr=["#DCDFE6","#06324d"];//查询栏-更多按钮-下拉列表-悬浮后背景颜色
        let more_btn_ivu_dropdown_item_hover_font_color_arr=["#0290FD","#00ffee"];//查询栏-更多按钮-下拉列表-悬浮后字体颜色
        let ivu_tooltip_inner_bg_color_arr = ["rgba(255, 255, 255, 1)","rgba(22, 67, 107, 1)"];//提示框背景颜色
        let ivu_tooltip_inner_font_color_arr=["#515A6E","#FFF"];//提示框字体颜色
        let table_text_click_color_arr=["#0290FD","#05EEFF"];//表格-文字-点击颜色
        let ivu_select_visible_border_color_arr=["#0290FD","#05eeff"];//查询条件-点中输入框-边框颜色
        let ivu_picker_confirm_bg_color_arr=["#FFFFFF","#06324d"];//查询条件-时间选择器-文字背景颜色
        let ivu_picker_confirm_border_color_arr=["#DCDFE6","#06324d"];//查询条件-时间选择器-边框颜色
        let ivu_picker_confirm_font_color_arr=["#515A6E","#c5c8ce"];//查询条件-时间选择器-文字颜色
        let ivu_picker_confirm_hover_bg_color_arr=["#0290FD","#06324d"];//查询条件-时间选择器-按钮悬浮背景颜色
        let ivu_picker_confirm_hover_font_color_arr=["#FFFFFF","#57a3f3"];//查询条件-时间选择器-按钮悬浮文字颜色
        let ivu_picker_confirm_select_font_color_arr=["#515A6E","#c5c8ce"];//查询条件-时间选择器-选择时间按钮文字颜色
        let inintEchart_dataZoom_filler_color_arr=["rgba(228, 231, 237, 1)","rgba(2, 29, 54, 1)"];//趋势图-时间选择-背景颜色
        let inintEchart_dataZoom_border_color_arr=["rgba(228, 231, 237, 1)","rgba(22, 67, 107, 1)"];//趋势图-时间选择-背景颜色
        let rpapth_a_link_lTitle_color_arr=["#17233D","#05eeff"];//中继线路监测-趋势图-标头文字颜色
        let quality_report_left_menu_bg_color_arr=["#FFFFFF","#011B2D"];//质量报告-详情-左边菜单栏-背景颜色
        let quality_report_left_ivu_menu_font_color_arr=["#17233D","#fff"];//质量报告--详情-左边菜单栏-字体颜色
        let defaultmanager_left_menu_bg_color_arr=["#F5F6FA","#16436b"];//默认配置-左边菜单选中后背景颜色
        let defaultmanager_left_menu_font_color_arr=["#0290FD","#05eeff"];//默认配置-左边菜单选中后字体颜色
        let dashboard_button_hover_font_color_arr=["#0290FD","#ffffff"];//仪表盘-按钮-悬浮-字体颜色
        let dashboard_component_title_font_color_arr=["#0290FD","#00ffee"];//仪表盘-构件-里面标题字体颜色
        let dashboard_bigscale_bg1_color_arr=["#0290FC","#049DEC"];//仪表盘-放大图标-背景颜色
        let dashboard_bigscale_bg2_color_arr=["#0290FC","#05EBEB"];//仪表盘-放大图标-背景颜色
        let dashboard_userDefined_btn_border1_color_arr = ["#0290FD","#015197"];//查询栏-更多按钮-背景和边框颜色
        let dashboard_userDefined_btn_border2_color_arr = ["#0290FD","#31f0fe"];//查询栏-更多按钮-背景和边框颜色
        let pathtopo_tabs_box_select_font_color_arr = ["#808695","#05eeff"];//路径拓扑-选择框-字体颜色
        let pathtopo_tabs_box_select_border_color_arr = ["#DCDFE6","#045b8e"];//路径拓扑-选择框-边框颜色
        let pathtopo_tabs_box_select_bg_color_arr = ["#fff","#061824"];//路径拓扑-选择框-背景颜色
        let pathtopo_logo_box_bg_color_arr=["#fff","#061824"];//路径拓扑-拓扑图上的log标志的背景图
        let pathtopo_logo_box_font_color_arr=["#808695","#5ca0d5"];//路径拓扑-拓扑图上的log标志的字体颜色图
        let pathtopo_fiber_routine_model_font_color_arr=["#808695","#5ca0d5"];//路径拓扑-选择展示方式没有悬浮情况下的字体颜色
        let pathtopo_fiber_routine_model_active_font_color_arr=["#0290FC","#05eeff"];//路径拓扑-选择展示方式悬浮情况下的字体颜色
        let pathtopo_icon_box_border_color_arr=["#DCDFE6","#045b8e"];//路径拓扑-图标-边框颜色
        let pathtopo_icon_special_suspend_border_color_arr=["#0290FD","#05eeff"];//路径拓扑-图标-悬浮-边框颜色
        let pathtopo_my_tooltip_bg_color_arr=["#FFFFFF","#06324d"];//路径拓扑-图标-提示框背景颜色
        let pathtopo_my_tooltip_left_box_drag_step_bg_color_arr=["#F5F7FA","#22465d"];//路径拓扑-图标-收缩右边滚动条背景颜色
        let pathtopo_my_tooltip_left_box_step_bg_color_arr=["#DCDFE6","#05eeff"];//路径拓扑-图标-收缩左边滚动条背景颜色
        let pathtopo_my_tooltip_left_box_step_circle_bg_color_arr=["#F5F7FA","#06324d"];//路径拓扑-图标-收缩节点背景颜色
        let pathtopo_my_tooltip_left_box_num_tip_bg_color_arr=["#F5F7FA","#015197"];//路径拓扑-图标-收缩节点数字颜色
        let comm_back_icon_bg_color_arr=["#0290FD","#31f0fe"];//返回按钮字体颜色
        let pathtopo_list_select_bg_color_arr=["rgba(255, 255, 255, 1)","rgba(0, 0, 0, 0)"];//路径拓扑-列表，下拉选择背景颜色
        let data_sync_primary_bg_arr = ["#FFFFFF", "transparent"];//数据同步按钮背景颜色
        let ivu_radio_inner_color_arr = ["#C0C4CC", "#ffffff"];//多个radio选择框
        let specialmonitor_checkedItem_bg_color_arr=["#fff","#06324d"];//专线监测选择专线背景图
        let probetaskModal_item_line_bg_color_arr=["#DCDFE6","#06324D"];//拨测任务管理-新增-分割线背景色
        let picker_panel_content_after_color_arr =["#F5F6FA","#154b81"];//时间选择器-分割线背景色
        let pathtopo_legend_splitline_border_color_arr=["#DCDFE6","#22465d"];//路径拓扑-图例弹框-分割线-颜色
        let dashboard_component_loading_bg_color_arr=["#FFFFFF","#000000"];//仪表盘-构件-加载中-背景颜色
        let testspeed_skinPrimary_bg_color_arr = ["#fff", "transparent"];//在线测速页面按钮背景颜色
        let testspeed_skinPrimary_font_color_arr = ["#0290FD", "#2d8cf0"];//在线测速页面按钮字体颜色
        let testspeed_restStartBut_bg_color_arr = ["#0290FD", "#169bd5"];//在线测速页面按钮字体颜色
        let header_active_bg_color_arr = [
          "rgba(125, 165, 232, 0.16)",
          "rgba(97, 124, 165, 0.16)",
        ];
        let header_list_bg_color_arr = ["#ffffff", "#253142"];
        let header_list_font_color_arr = ["#515A6E", "#5CA0D5"];
        let header_list_activefont_color_arr = ["#ffffff", "#ffffff"];
        let header_list_activebg_color_arr = [
          "#7da5e8",
          "rgba(97, 124, 165, 0.16)",
        ];
        let link_b_color_arr = ["#f4f6f9", "#1a222e"];
        let chartBak_arr = ["#303748", "#617ca5"];
        let dash_tabsActive_color_arr = ["#2d8cf0", "#1E9AFF"];
        let message_bg_arr = ["rgba(255,255,255)", "rgba(44, 37, 48, .6)"];
        let message_border_success_arr = ["#fff", "#19be6b"];
        let message_border_err_arr = ["#fff", "#ed3f13"];
        let message_border_warning_arr = ["#fff", "#f90"];
        let message_font_success_arr = ["##515a6e", "#19be6b"];
        let message_font_err_arr = ["##515a6e", "#ed3f13"];
        let message_font_warning_arr = ["##515a6e", "#f90"];
        let table_checkbox_border_color_arr = ["#e8eaec", "#04478E"];//table表格选择框的颜色
        let primary_bcg_arr = ["#2d8cf0", "transparent"];
        let primary_border_arr = ["#2d8cf0", "#2d8cf0"];
        let primary_font_arr = ["#fff", "#2d8cf0"];
        let warning_bcg_arr = ["#ffad33", "transparent"];
        let warning_border_arr = ["#ffad33", "#ffad33"];
        let warning_font_arr = ["#fff", "#ffad33"];
        let success_bcg_arr = ["#47cb89", "transparent"];
        let success_border_arr = ["#47cb89", "#47cb89"];
        let success_font_arr = ["#fff", "#47cb89"];
        let error_bcg_arr = ["#f16643", "transparent"];
        let error_border_arr = ["#f16643", "#f16643"];
        let error_font_arr = ["#fff", "#f16643"];
        let ivu_tag_border_arr = ["#e8eaec", "#06324D"];
        let ivu_select_arrow_arr = ["#808695", "#05EEFF"];
        let font_color_arr = ["#303748", "#fff"];
        let modal_header_color_arr = ["#fff", "#D1E4FF"];
        let org_btn_bg_color_arr = ["#57c5f7", "#465b7a"];
        let contentBox_bgcolor_arr = ["#fff", "#060D15"];
        let fonthandle_color_arr = ["#2d8cf0", "#1E9AFF"];
        let input_placeholder_color_arr = ["#c5c8ce", "#5CA0D5"];
        let topo_menu_region_border_arr = ["#E4E7ED", "#22465D"];
        let path_fiber_g6_color_arr = ["#17233D", "#ccc"];
        let path_tooltip_g6_color_arr = ["#17233D", "#fff"];
        let path_tooltip_g6_bg_color_arr = ["#fff", "#16436b"];

        let btnConfirm_bg1_color_arr=["#fff","#049dec"];//消息弹框背景颜色
        let btnConfirm_bg2_color_arr=["#fff","#05ebeb"];//消息弹框背景颜色
        let btnConfirm_font_color_arr=["#0290FD","#042039"];//消息弹框字体颜色
        let btnConfirm_border_color_arr=["#0290FD","none"];//消息探针边框颜色
        let model_borer_color_arr = ['#fff','#31F0FE']

        // 弹窗的标题背景色
        let modal_header_b_color_arr = [
          "#ffffff",
          "#08101A"
        ];
        // 弹窗的标题字体颜色
        let modal_header_font_color_arr = [
          "#17233D",
          "#31F0FE"

        ];
        let modal_title_name_color_arr = ["#17233D","#00FFEE"];
        let modal_title_name_color_two_arr = ["#515A6E","#fff"];
        // 弹窗的取消按钮颜色设置
        let modal_footer_but_b_color_arr = ["#515A6E", "#5CA0D5"];
        let modal_footer_but_cancel_background_color_arr = ["#FFFFFF", "#061824"];
        let modal_footer_but_cancel_background_border_color_arr = ["#C0C4CC", "#015197"];

        // 弹窗的确定按钮颜色设置
        let modal_footer_butok_b_color_arr = ["#FFFFFF", "#060d15"];
        let modal_footer_but_ok_background_color_arr = ["#0290FD", "linear-gradient(357deg, #049dec 0%, #05ebeb 100%)"];
        let modal_footer_but_ok_background_border_color_arr = ["none", "#05ebeb"];

        // 
        // 弹窗的内容背景色设置
        let modal_background_color_arr = ["#FFFFFF", "linear-gradient(to right, #08101A, #08101A), linear-gradient(80deg, #015197 37%, #31F0FE 46%, #015197 65%, #015197 100%);"];
        let modal_icon_close_btn_color_arr = ["#808695", "00ffee"];
        let modal_background_color_arr2 = ["#DCDFE6", "#31F0FE"];
        // 
        let confirmmodal_footer_cancel_b_color_arr = ["transparent", "#061824"];
        let border_color_arr = ["#C0C4CC", "#06324D"];//查询条件是复选框的边框颜色
        let icon_tree_arr = ["#e8eaec", "#04478E"];
        let th_b_color_arr = ["#F5F7FA", "#032A4D"];//table表格标题头的那行的背景颜色
        let th_b_stripe_color_arr = ["#F8F8F9", "#011B2D"];//table表格间隔单行的背景颜色
        let td_b_color_arr = ["#FFFFFF", "#060D15"];//table表格间隔双行的背景颜色
        let th_font_color_arr = ["#515A6E", "#FFFFFF"];//table表格标题文字颜色
        let td_font_color_arr = ["#17233D", "#FFFFFF"];//table表格里文字颜色
        let thd_border_color_arr = ["#e8eaec", "#2e3c51"];
        let table_content_column_link_color_arr = ["#0290FD", "#05eeff"];//table表格里文字是连接的颜色

        let tdd_border_color_arr = ["#e8eaec", "#222d3d"];
        let table_sort_color_arr = ["#c5c8ce", "#465b7a"];
        let table_sorton_color_arr = ["#2d8cf0", "#8fd4ff"];
        let input_font_color_arr = ["#515A6E", "#fff"];//列表分页：末页俩个字的字体颜色
        let input_border_color_arr = ["#dcdee2", "#2e3c51"];
        let input_b_color_arr = ["#ffffff", "#061824"];
        let input_checkbox_b_color_arr = ["#ffffff", "#1A222E"];
        // 下拉框选中的背景色颜色设置
        let selectdrop_b_color_arr = ["#E5F4FF", "#06324D"];
        // 下拉框选中的字体颜色设置
        let selectdrop_font_color_arr = ["#0290FD", "#00FFEE"];
        let confirmModal_font_color_arr = ["#17233d", "#5CA0D5"];
        let dash_h_b_color_arr = ["#f4f6f9", "#060D15"];
        let dash_h_f_color_arr = ["#303748", "#00FFEE"];
        let dash_b_b_color_arr = ["#ffffff", "#1a222e"];
        let dash_border_color_arr = ["#dddddd", "#202A39"];
        let page_b_color_arr = ["#0290FD", "#06324D"];
        let page_font_color_arr = ["#FFFFFF", "#ffffff"];
        let page_border_color_arr = ["#0290FD", "#05eeff"];
        let alarm_modal_border_color_arr = ["#d7d7d7", "#35455d"];
        let time_disabled_b_color_arr = ["#f7f7f7", "#06324D"];
        let time_hover_b_color_arr = ["#e1f0fe", "#465b7a"];
        let reset_but_b_color_arr = ["#999", "#465b7a"];
        let topo_model_head_color_arr = ["#1e97fa", "#465b7a"];
        let topo_checkbox_b_color_arr = ["#fff", "#1A222E"];
        let oid_l_m_b_color_arr = ["#fff", "#1F2A38"];
        // 质量报告颜色变量
        let menu_active_color_arr = ["#F0FAFF", "#253142"];
        let left_menu_color_arr = ["#ffffff", "#1A222E"];
        let report_bg_color_arr = ["#F4F6F9", "#0D151F"];
        let wifi_boder_color_arr = ["#dcdfe6", "#465B7A"];
        let wifi_tip_color_arr = ["#303748", "#808695"];
        let wifi_tip_content_color_arr = ["#808695", "#DFF1FF"];
        let wifi_device_color_arr = ["#303748", "#fff"];
        let monitorItem_color_arr2 = ["#17233D", "#fff"];
        let monitorItem_color_arr = ["#17233D", "#D3E4FF"];
        let monitorItem_bg_color_arr = ["#ffffff", "#060D15"];
        let monitorItem_border_color_arr = ["#DCDFE6", "#013164"];


        // 表格冻结 右边的列的颜色
        let tableFixedRight_arr = ["#ffffff" , "#011B2D"];
        let table_header_bg_color_arr = ["#ffffff" , "#032A4D"];
        let table_td_th_border_color_arr = ["#E4E7ED" , "#032A4D"];
        let table_td_bg_color_arr = ["#fff" , "#061824"];
        let page_total_color_arr=["#515A6E" , "#fff"];
        let select_dropdown_border_color_arr = ["#DCDFE6" , "#04478e"];
        let date_picker_cells_cell_range_bg_color_arr=["#F5F6FA","#06324d"];
        let sidebar_bg_color_arr = ['#F5F6FA','none']
        let body_table_content_border_color_arr=["#DCDFE6","#06254b"];
        let spectendency_chart_bg_color_arr=["#ffffff","#06121c"];
        // 拨测任务查询路径表格的颜色
        let task_path_list_table_color_arr=["#17233D","#ffffff"];
        let border_none_color_arr = ["#DCDFE6" , "#015197"];
        // switch 样式
        let switch_close_color_arr = ["#fff" , "#032a4d"];
        let switch_close_font_color_arr = ["#808695" , "#5ca0d5"];
        let switch_close_after_bg_color_arr = ["#E4E7ED" , "#015197"];
        let switch_close_border_color_arr = ["#DCDFE6" , "#015197"];
        // form ivu_tag 背景色样式
        let ivu_tag_bg_color_arr = ["#ffffff", "#06324D"];
        // checkbox 样式
         let from_checkbox_color_arr = ["#ffffff", "#06324D"];
         // 表单不可用的样式
         let from_input_disabled_color_arr = ["#515A6E", "#ccc"];
         // 选中的颜色
         let from_checkbox_check_color_arr = ["#0290FD", "#05eeff"];
         // 选中的边框颜色
         let from_checkbox_check_border_color_arr = ["#0290FD", "#06324D"];
         // 
         let from_time_picker_cells_cell_hover_color_arr = ["#E5F4FF","#06324d"];
         let from_time_picker_cells_cell_select_bg_color_arr = ["#E5F4FF","#06324d"];
         let from_time_picker_cells_cell_border_color_arr = ["#ffffff","#06324d"];
         // 高级更多按钮样式颜色
         let from_condition_btn_font_color_arr = ["#0290FD","#05eeff"];
         let from_condition_btn_border_color_arr = ["#0290FD","#05eeff"];
         let from_condition_btn_bg_color_arr = ["#fff","#06324d"];
         // 默认值配置
         let default_value_remarks_color_arr = ["#515A6E","#5CA0D5"];
         let default_value_font_color_arr = ["#515A6E","#fff"];
         let default_value_group_append_bg_color_arr = ["#ffffff","#061824"];
         let default_value_group_append_border_color_arr = ["#DCDFE6","#015197"];
         let default_value_menu_color_arr = ["#97C5E9","#5ca0d5"];//质量报告-详情-左边标题-悬浮选中的字体颜色
         let default_value_menu_select_font_color_arr = ["#64BCFF","#05eeff"];//质量报告-详情-左边标题-选中以后的字体颜色
         let default_value_menu_select_bg_color_arr = ["#64BCFF","#16436b"];
         // 默认值重置按钮样式
         let default_value_result_btn_bg_color_arr = ["#ffffff","#061824"];
         let default_value_result_btn_color_arr = ["#515A6E","#5ca0d5"];
         let default_value_result_btn_border_color_arr = ["#C0C4CC","#015197"];
         // oid 颜色配置
        let oid_left_bg_color_arr = ["#ffffff" , "#011B2D"];
        // radio 颜色配置
        let form_radio_border_color_arr = ["#C0C4CC" , "#04478e"];
        let form_radio_check_color_arr = ["#0290FD" , "#05eeff"];
        // 
        let spectendency_choose_btn_color_arr=["#ffffff","#05eeff"];
        let spectendency_choose_btn_border_color_arr=["#0290FD","#05eeff"];
        let spectendency_choose_btn_bg_color_arr=["#0290FD","#06324d"];
        
        // 选择按钮样式
        let choose_data_btn_font_color_arr=["#FFFFFF","#05EEFF"];
        let choose_data_btn_bg_color_arr=["#0290FD","#06324D"];
        let choose_data_btn_border_color_arr=["#0290FD","#05EEFF"];
        let choose_data_btn_bg_color_arr2=["#fff","#06324D"];
        let choose_data_btn_font_color_arr2=["#0290FD","#05EEFF"];

        // 删除按钮颜色
        let delete_data_btn_font_color_arr=["#FE5C5C","#FE5C5C"];
        let delete_data_btn_bg_color_arr=["#fff","#1F1414"];
        let delete_data_btn_border_color_arr=["#FE5C5C","#FE5C5C"];

        // 选择文件上传按钮
        let choose_file_btn_font_color_arr=["#FFFFFF","#060D15"];
        let choose_file_btn_bg_color_arr=["#0290FD","linear-gradient(357deg, #049DEC 0%, #05EBEB 100%)"];
        let choose_file_btn_border_color_arr=["#0290FD","#2d8cf0"];

        // 升级平台跳转颜色
        let upgrade_btn_font_color_arr=["#0290FC","#05EEFF"];
        let upgrade_btn_bg_color_arr=["#fff","#06324D"];
        let upgrade_btn_border_color_arr=["#0290FC","#05EEFF"];
        let upgrade_lisenceInfo_color_arr=["#17233D","#05EEFF"];

        // 下载模板文字按钮颜色
        let download_template_color_arr=["#0290FD","#05EBEB"];

        // 故障清单查看故障详情弹窗的文本颜色
        let fault_phenomenon_content_color_arr=["#515A6E","#05eeff"];

        // 拨测页面详情页面切换菜单颜色
        let btnChange_color_arr= ["#515A6E","#5CA0D5"];
        let btnChange_border_color_arr= ["#C0C4CC","#02B8FD"];
        let btnChange_active_color_arr= ["#fff","#060D15"];
        let btnChange_active_bg_color_arr= ["#0290FD","#4e7bff"];
        let btnChange_active_border_color_arr= ["#C0C4CC","#4e7bff"];

        // 选择显示字段颜色配置
        let field_font_color_arr = ["#515A6E" , "#fff"]
        let field_table_td_bg_color_arr = ["#E5F4FF" , "#06324D"]
        let field_table_border_color_arr = ["#DCDFE6 " , "#015197"]
        let field_table_bg_color_arr = ["#fff" , "#061824"]
        let field_table_td_hover_color_arr = ["#E5F4FF" , "#061824"]

        let field_sort_btn_hover_font_color_arr = ["#0290FD" , "#05EEFF"]
        let field_sort_btn_hover_border_color_arr = ["#0290FD" , "#0290FD"]
        let field_sort_btn_border_color_arr = ["#C1C4CC" , "#04478E"]
        let field_sort_btn_bg_color_arr = ["#fff" , "#071B31"]
        let field_sort_btn_font_color_arr = ["#808695" , "#1FA2FF"]


        // tab 样式
        let tab_nav_font_color_arr = ["#808695" , "#5CA0D5"]
        let tab_nav_margin_left_arr = ["0px" , "-20px"]
        let tab_nav_active_font_color_arr = ["#0290FD" , "#060D15"]
        let tab_nav_active_font_weight_arr = ["200" , "700"]

        // 下一步按钮样式
        let next_btn_font_color_arr = ["#FFFFFF","#060d15"];
        let next_btn_border_color_arr = ["#0290FD","#57a3f3"];
        let next_btn_bg_color_arr = ["#0290FD","#049dec"];
        let next_btn_bg_color_2_arr = ["#0290FD","#05ebeb"];


        // wifi 详情页面颜色
        let wifi_name_font_color_arr=["#0290FD" , "#00FFEE"];
        let wifi_name_border_color_arr=["#0290FD" , "#00FFEE"];
        let wifi_name_bg_color_arr=["#fff" , "#22465D"];

        let wifi_point_speed_item_process_color_arr=["#DCDFE6" , "#51636F"];

        // wifi 内网颜色设置
        let wifi_internal_bg_color_arr=["#fff" , "#061824"];
        let wifi_internal_border_color_arr=["#DCDFE6" , "#22465D"];

        let wifi_internal_title_font_color_arr=["#0290FD" , "#02B8FD"];
        let wifi_internal_title_border_color_arr=["#0290FD" , "#22465D"];
        let wifi_internal_title_bg_color_arr=["#fff" , "#061824"];

        // 帮助手册样式
        let help_font_color_arr = ["#17233D","#5ca0d5"];
        let help_font_hover_color_arr = ["#0290FD","#05eeff"];
        let help_font_hover_bg_color_arr = ["#E5F4FF","#253142"];


        // 仪表盘趋势图 tooltip 样式
        let dashboard_tooltip_bg_color_arr= ["#fff","rgba(18,55,127,.8)"];
        let dashboard_tooltip_color_arr= ["#5A5F6C","#d1e4ff"];
        let dashboard_tooltip_border_color_arr= ["#E4E7ED","rgba(18,55,127,.8)"];
        let ivu_radio_wrapper_checked_bg_arr = ['#E5F4FF','#00466e'];
        let ivu_radio_wrapper_checked_font_arr=['#0290FD','#2d8cf0'];

         let license_upgrade_btn_border_color_arr=["#0290FC","transparent"];
         let license_upgrade_btn_font_color_arr=["#0290FD","#02b8fd"];
         let license_contentBox_bgcolor_arr = ["#F5F6FA ", "#060D15"];
         let license_text_color_arr = ["#808695 ", "#5CA0D5"];
         let time_picker_suffix_color_arr = ["#C0C4CC","#5CA0D5"];
         let btn_change_active_bg_arr = ['#EBF6FF','#02B8FD'];
         let data_picker_no_active_arr=['#DCDFE6','#2C4558'];
         //专线线路报告
         let speciallinereport_progress_bg_color_arr=['#DCDFE6','#032A4D'];
         let speciallinereport_progress_font_color_arr=['#FFFFFF','#FFFFFF'];





        let body_b_color = "#f4f6f9",
          font_color = "#303748",
          border_color = "#dddddd",
          icon_tree = "#dddddd",
          th_b_color = "#f1f6fe",
          th_b_stripe_color = "#e8eaec";
        let td_b_color = "#ffffff",
          th_font_color = "#303748",
          td_font_color = "#303748",
          thd_border_color = "#e8eaec";
        let table_sort_color = "#c5c8ce",
          table_sorton_color = "#2d8cf0",
          input_font_color = "#303748",
          input_b_color = "#ffffff";
        let selectdrop_b_color = "#ffffff",
          selectdrop_font_color = "#303748",
          confirmModal_font_color = "#17233d";
        let dash_h_b_color = "#f4f6f9",
          dash_h_f_color = "#303748",
          dash_b_b_color = "#ffffff",
          dash_border_color = "#dddddd";
        let input_placeholder_color = "#c5c8ce",
          org_btn_bg_color = "#57c5f7",
          table_checkbox_border_color = "#e8eaec";
        let primary_bcg = "#2d8cf0",
          primary_border = "#2d8cf0",
          primary_font = "#fff";
        let warning_bcg = "#ffad33",
          warning_border = "#ffad33",
          warning_font = "#fff";
        let success_bcg = "#47cb89",
          success_border = "#47cb89",
          success_font = "#fff";
        let error_bcg = "#f16643",
          error_border = "#f16643",
          error_font = "#f16643";
        let ivu_tag_border = "#e8eaec",
          ivu_select_arrow = "#808695",
          modal_header_b_color =
            "radial-gradient(rgba(43, 133, 228, 0.6), rgba(43, 133, 228, 0.9))";
        let modal_b_color = "#ffffff",
          modal_input_border_color = "#dcdee2",
          contentBox_bgcolor = "none",
          confirmmodal_footer_cancel_b_color = "transparent";
        let header_b_color = "#1252c8",
          header_font_color = "#ffffff",
          header_active_bg_color = "rgba(125, 165, 232, 0.16)";
        let header_list_bg_color = "#ffffff",
          header_list_font_color = "#303748",
          header_list_activefont_color = "#ffffff";
        let header_list_activebg_color = "#7da5e8",
          message_bg = "rgba(255,255,255,.9)",
          chartBak = "#303748",
          link_b_color = "#f4f6f9";
        let input_checkbox_b_color = "#2d8cf0",
          message_success_color,
          message_err_color,
          message_warning_color = "#fff";
        let message_font_success,
          message_font_err,
          message_font_warning = "#515a6e",
          page_b_color = "#fff",
          modal_header_color = "#fff";
        let alarm_modal_border_color = "#d7d7d7",
          time_disabled_b_color = "#f7f7f7",
          time_hover_b_color = "#e1f0fe";
        let reset_but_b_color = "#999",
          topo_model_head_color = "#1e97fa",
          topo_checkbox_b_color = "#fff",
          oid_l_m_b_color = "#fff";
        let menu_active_color = "#F0FAFF";
        let left_menu_color = "#ffffff";
        let report_bg_color = "#F4F6F9";
        let wifi_boder_color = "#dcdfe6";
        let wifi_tip_color = "#303748";
        let wifi_device_color = "#303748";
        let wifi_tip_content_color = "808695";
        let monitorItem_color = "#e8eaec";
        let monitorItem_bg_color = "#ffffff";
        let search_lable_font_color = "#fff";
        let table_content_column_link_color = "#05eeff";
        let button_background_color = "#05eeff";
        let button_background_two_color = "#049DEC";
        let search_check_box_background_color = "#02b8fd";
        let search_check_box_font_color="#060d15";
        let search_check_box_bg_tabStyle_color = "#061824";
        let search_check_box_font_tabStyle_color="#fff";
        let search_check_box_font_tabStyle_two_color = "#5ca0d5";
        let search_ivu_select_input_boder_1_color = "#060d15";
        let search_ivu_select_input_boder_2_color = "#015197";
        let search_ivu_select_input_boder_3_color = "#31f0fe";
        let search_ivu_select_input_boder_4_color = "#ffffff";
        let button_networdFault_del_bg_color = "#8a5505";
        let button_networdFault_del_bg_2_color="#ffffff";
        let button_networdFault_del_bg_3_color="#b77006";
        let button_networdFault_del_bg_4_color="#fea31b";
        let networdFault_type_border_color="#04478e";
        let networdFault_type_border_normal_color="#04478e";
        let networdFault_type_border_normal_font_color="#5ca0d5";
        let networdFault_duration_border_select_color = "#05eeff";
        let query_org_select_color="#05eeff";
        let button_background_three_color = "#151106";
        let reset_export_del_button_font_color = "#05EEFF";
        let del_button_font_color="#fea31b";
        let reset_export_button_border_1_color="#015197";
        let reset_export_button_border_2_color="#31F0FE";
        let reset_export_button_border_3_color="#015197";
        let scrollbar_thumb_bg_color="#015197";
        let scrollbar_track_bg_color="#06324D";
        let scrollbar_thumb_shadow_color="rgba(6, 50, 77, 1)";
        let scrollbar_track_shadow_color="rgba(0, 0, 0, 0.2)";
        let query_btn_hover_bg_1_color="#049DEC";
        let query_btn_hover_bg_2_color="#05EBEB";
        let query_btn_active_bg_1_color="rgba(4, 157, 236, 0.8)";
        let query_btn_active_bg_2_color="rgba(5, 235, 235, 0.8)";
        let more_btn_font_color = "#02b8fd";
        let more_btn_background_image_1_color="#015197";
        let more_btn_background_image_2_color="#31f0fe";
        let more_btn_ivu_dropdown_item_hover_bg_color="#06324d";
        let more_btn_ivu_dropdown_item_hover_font_color="#00ffee";
        let more_btn_ivu_dropdown_item_font_color="#dff1ff";
        let ivu_tooltip_inner_bg_color="rgba(22, 67, 107, 1)";
        let ivu_tooltip_inner_font_color="#ffffff";
        let ivu_select_visible_border_color="#05eeff";
        let inintEchart_dataZoom_filler_color="rgba(2, 29, 54, 1)";
        let inintEchart_dataZoom_border_color="rgba(22, 67, 107, 1)";
        let ivu_picker_confirm_bg_color="#06324d";
        let ivu_picker_confirm_border_color="#06324d";
        let ivu_picker_confirm_font_color="#c5c8ce";
        let ivu_picker_confirm_hover_bg_color="#06324d";
        let ivu_picker_confirm_hover_font_color="#57a3f3";
        let ivu_picker_confirm_select_font_color="#c5c8ce";
        let rpapth_a_link_lTitle_color="#05eeff";
        let quality_report_left_menu_bg_color="#011B2D";
        let quality_report_left_ivu_menu_font_color="#fff";
        let defaultmanager_left_menu_bg_color="#16436b";
        let defaultmanager_left_menu_font_color="#05eeff";
        let dashboard_button_hover_font_color="#ffffff";
        let dashboard_component_title_font_color="#00ffee";
        let dashboard_bigscale_bg1_color="#049DEC";
        let dashboard_bigscale_bg2_color="#05EBEB";
        let dashboard_userDefined_btn_border1_color="#015197";
        let dashboard_userDefined_btn_border2_color="#31F0FE";
        let pathtopo_tabs_box_select_font_color="#05eeff";
        let pathtopo_tabs_box_select_bg_color="#fff";
        let pathtopo_tabs_box_select_border_color="#045b8e";
        let pathtopo_logo_box_bg_color="#061824";
        let pathtopo_logo_box_font_color="#5ca0d5";
        let pathtopo_fiber_routine_model_font_color="#5ca0d5";
        let pathtopo_fiber_routine_model_active_font_color="#05eeff";
        let pathtopo_icon_box_border_color="#045b8e";
        let pathtopo_icon_special_suspend_border_color="#05eeff";
        let pathtopo_my_tooltip_bg_color="#06324d";
        let pathtopo_my_tooltip_left_box_drag_step_bg_color="#22465d";
        let pathtopo_my_tooltip_left_box_step_bg_color="#05eeff";
        let pathtopo_my_tooltip_left_box_step_circle_bg_color="#06324d";
        let pathtopo_my_tooltip_left_box_num_tip_bg_color="#015197";
        let comm_back_icon_bg_color="#31f0fe";
        let pathtopo_list_select_bg_color="rgba(0, 0, 0, 0)";
        let data_sync_primary_bg="#2d8cf0";

        let ivu_radio_inner_color = "#ffffff";
        let specialmonitor_checkedItem_bg_color = "#06324d";
        let probetaskModal_item_line_bg_color="#06324D";

        let pie_count_color = pie_count_color_arr[val];
       
        let pie_legend_normal_color = pie_legend_normal_color_arr[val];
        let pie_legend_deterioration_color = pie_legend_deterioration_color_arr[val];
        let pie_legend_break_color = pie_legend_break_color_arr[val];
        let pie_legend_suspend_color = pie_legend_suspend_color_arr[val];
        let table_text_click_color = table_text_click_color_arr[val];
        let ivu_radio_wrapper_checked_bg = ivu_radio_wrapper_checked_bg_arr[val]
        let ivu_radio_wrapper_checked_font = ivu_radio_wrapper_checked_font_arr[val]
        let speciallinereport_progress_bg_color = speciallinereport_progress_bg_color_arr[val];
        let speciallinereport_progress_font_color = speciallinereport_progress_font_color_arr[val];
        body_b_color = body_b_color_arr[val];
        search_lable_font_color = search_lable_font_color_arr[val];
        table_content_column_link_color = table_content_column_link_color_arr[val];
        button_background_color = button_background_color_arr[val];
        button_background_two_color = button_background_color_arr_two[val];
        button_background_three_color = button_background_color_arr_three[val];
        search_check_box_background_color = search_check_box_background_color_arr[val];
        search_check_box_font_color = search_check_box_font_color_arr[val];
        search_check_box_bg_tabStyle_color = search_check_box_bg_tabStyle_color_arr[val];
        search_check_box_font_tabStyle_color=search_check_box_font_tabStyle_color_arr[val];
        search_check_box_font_tabStyle_two_color=search_check_box_font_tabStyle_two_color_arr[val];
        search_ivu_select_input_boder_1_color = search_ivu_select_input_boder_color_1_arr[val];
        search_ivu_select_input_boder_2_color = search_ivu_select_input_boder_color_2_arr[val];
        search_ivu_select_input_boder_3_color = search_ivu_select_input_boder_color_3_arr[val];
        search_ivu_select_input_boder_4_color = search_ivu_select_input_boder_color_4_arr[val];
        button_networdFault_del_bg_color = button_networdFault_del_bg_color_arr[val];
        button_networdFault_del_bg_2_color = button_networdFault_del_bg_2_color_arr[val];
        button_networdFault_del_bg_3_color_arr = button_networdFault_del_bg_3_color_arr[val];
        button_networdFault_del_bg_4_color_arr = button_networdFault_del_bg_4_color_arr[val];
        networdFault_type_border_color = networdFault_type_border_color_arr[val];
        networdFault_type_border_normal_color = networdFault_type_border_normal_color_arr[val];
        networdFault_type_border_normal_font_color = networdFault_type_border_normal_font_color_arr[val];
        networdFault_duration_border_select_color = networdFault_duration_border_select_color_arr[val];
        query_org_select_color= query_org_select_color_arr[val];
        reset_export_del_button_font_color = reset_export_del_button_font_color_arr[val];
        del_button_font_color = del_button_font_color_arr[val];
        reset_export_button_border_1_color = reset_export_button_border_1_color_arr[val];
        reset_export_button_border_2_color = reset_export_button_border_2_color_arr[val];
        reset_export_button_border_3_color = reset_export_button_border_3_color_arr[val];
        scrollbar_thumb_bg_color = scrollbar_thumb_bg_color_arr[val];
        scrollbar_track_bg_color = scrollbar_track_bg_color_arr[val];
        scrollbar_thumb_shadow_color = scrollbar_thumb_shadow_color_arr[val];
        scrollbar_track_shadow_color = scrollbar_track_shadow_color_arr[val];
        query_btn_hover_bg_1_color = query_btn_hover_bg_1_color_arr[val];
        query_btn_hover_bg_2_color = query_btn_hover_bg_2_color_arr[val];
        query_btn_active_bg_1_color = query_btn_active_bg_1_color_arr[val];
        query_btn_active_bg_2_color = query_btn_active_bg_2_color_arr[val];
        more_btn_font_color = more_btn_font_color_arr[val];
        more_btn_background_image_1_color = more_btn_background_image_1_color_arr[val];
        more_btn_background_image_2_color = more_btn_background_image_2_color_arr[val];
        more_btn_ivu_dropdown_item_hover_bg_color =more_btn_ivu_dropdown_item_hover_bg_color_arr[val];
        more_btn_ivu_dropdown_item_hover_font_color=more_btn_ivu_dropdown_item_hover_font_color_arr[val];
        more_btn_ivu_dropdown_item_font_color=more_btn_ivu_dropdown_item_font_color_arr[val];
        ivu_tooltip_inner_bg_color = ivu_tooltip_inner_bg_color_arr[val];
        
        ivu_tooltip_inner_font_color=ivu_tooltip_inner_font_color_arr[val];
        ivu_select_visible_border_color = ivu_select_visible_border_color_arr[val];
        inintEchart_dataZoom_filler_color = inintEchart_dataZoom_filler_color_arr[val];
        inintEchart_dataZoom_border_color = inintEchart_dataZoom_border_color_arr[val];
        ivu_picker_confirm_bg_color = ivu_picker_confirm_bg_color_arr[val];
        ivu_picker_confirm_border_color = ivu_picker_confirm_border_color_arr[val];
        ivu_picker_confirm_font_color = ivu_picker_confirm_font_color_arr[val];
        ivu_picker_confirm_hover_bg_color = ivu_picker_confirm_hover_bg_color_arr[val];
        ivu_picker_confirm_hover_font_color = ivu_picker_confirm_hover_font_color_arr[val];
        ivu_picker_confirm_select_font_color=ivu_picker_confirm_select_font_color_arr[val];
        rpapth_a_link_lTitle_color = rpapth_a_link_lTitle_color_arr[val];
        quality_report_left_menu_bg_color = quality_report_left_menu_bg_color_arr[val];
        quality_report_left_ivu_menu_font_color = quality_report_left_ivu_menu_font_color_arr[val];
        defaultmanager_left_menu_bg_color = defaultmanager_left_menu_bg_color_arr[val];
        defaultmanager_left_menu_font_color = defaultmanager_left_menu_font_color_arr[val];
        dashboard_button_hover_font_color = dashboard_button_hover_font_color_arr[val];
        dashboard_component_title_font_color = dashboard_component_title_font_color_arr[val];
        dashboard_bigscale_bg1_color = dashboard_bigscale_bg1_color_arr[val];
        dashboard_bigscale_bg2_color = dashboard_bigscale_bg2_color_arr[val];
        dashboard_userDefined_btn_border1_color = dashboard_userDefined_btn_border1_color_arr[val];
        dashboard_userDefined_btn_border2_color = dashboard_userDefined_btn_border2_color_arr[val];
        pathtopo_tabs_box_select_font_color=pathtopo_tabs_box_select_font_color_arr[val];
        pathtopo_tabs_box_select_bg_color = pathtopo_tabs_box_select_bg_color_arr[val];
        pathtopo_tabs_box_select_border_color = pathtopo_tabs_box_select_border_color_arr[val];
        pathtopo_logo_box_bg_color = pathtopo_logo_box_bg_color_arr[val];
        pathtopo_logo_box_font_color = pathtopo_logo_box_font_color_arr[val];
        pathtopo_fiber_routine_model_font_color = pathtopo_fiber_routine_model_font_color_arr[val];
        pathtopo_fiber_routine_model_active_font_color = pathtopo_fiber_routine_model_active_font_color_arr[val];
        pathtopo_icon_box_border_color = pathtopo_icon_box_border_color_arr[val];
        pathtopo_icon_special_suspend_border_color = pathtopo_icon_special_suspend_border_color_arr[val];
        pathtopo_my_tooltip_bg_color = pathtopo_my_tooltip_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_drag_step_bg_color = pathtopo_my_tooltip_left_box_drag_step_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_step_bg_color = pathtopo_my_tooltip_left_box_step_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_step_circle_bg_color = pathtopo_my_tooltip_left_box_step_circle_bg_color_arr[val];
        pathtopo_my_tooltip_left_box_num_tip_bg_color = pathtopo_my_tooltip_left_box_num_tip_bg_color_arr[val];
        comm_back_icon_bg_color = comm_back_icon_bg_color_arr[val];
        pathtopo_list_select_bg_color = pathtopo_list_select_bg_color_arr[val];
        data_sync_primary_bg = data_sync_primary_bg_arr[val];
        font_color = font_color_arr[val];
        border_color = border_color_arr[val];
        icon_tree = icon_tree_arr[val];
        th_b_color = th_b_color_arr[val];
        th_b_stripe_color = th_b_stripe_color_arr[val];

        td_b_color = td_b_color_arr[val];
        th_font_color = th_font_color_arr[val];
        td_font_color = td_font_color_arr[val];
        thd_border_color = thd_border_color_arr[val];

        table_sort_color = table_sort_color_arr[val];
        table_sorton_color = table_sorton_color_arr[val];
        input_font_color = input_font_color_arr[val];
        input_b_color = input_b_color_arr[val];
        input_checkbox_b_color = input_checkbox_b_color_arr[val];
        selectdrop_b_color = selectdrop_b_color_arr[val];
        selectdrop_font_color = selectdrop_font_color_arr[val];
        confirmModal_font_color = confirmModal_font_color_arr[val];

        dash_h_b_color = dash_h_b_color_arr[val];
        dash_h_f_color = dash_h_f_color_arr[val];
        dash_b_b_color = dash_b_b_color_arr[val];
        dash_border_color = dash_border_color_arr[val];
        input_placeholder_color = input_placeholder_color_arr[val];
        org_btn_bg_color = org_btn_bg_color_arr[val];
        table_checkbox_border_color = table_checkbox_border_color_arr[val];

        primary_bcg = primary_bcg_arr[val];
        primary_border = primary_border_arr[val];
        primary_font = primary_font_arr[val];
        warning_bcg = warning_bcg_arr[val];
        warning_border = warning_border_arr[val];
        warning_font = warning_font_arr[val];
        success_bcg = success_bcg_arr[val];
        success_border = success_border_arr[val];
        success_font = success_font_arr[val];
        error_bcg = error_bcg_arr[val];
        error_border = error_border_arr[val];
        error_font = error_font_arr[val];
        ivu_tag_border = ivu_tag_border_arr[val];
        ivu_select_arrow = ivu_select_arrow_arr[val];
        modal_header_b_color = modal_header_b_color_arr[val];
        modal_b_color = modal_b_color_arr[val];
        modal_input_border_color = modal_input_border_color_arr[val];
        contentBox_bgcolor = contentBox_bgcolor_arr[val];
        header_b_color = header_b_color_arr[val];
        header_font_color = header_font_color_arr[val];
        header_active_bg_color = header_active_bg_color_arr[val];

        header_list_bg_color = header_list_bg_color_arr[val];
        header_list_font_color = header_list_font_color_arr[val];
        header_list_activefont_color = header_list_activefont_color_arr[val];
        header_list_activebg_color = header_list_activebg_color_arr[val];
        message_bg = message_bg_arr[val];
        message_success_color = message_border_success_arr[val];
        message_err_color = message_border_err_arr[val];
        message_warning_color = message_border_warning_arr[val];
        message_font_success = message_font_success_arr[val];
        message_font_err = message_font_err_arr[val];
        message_font_warning = message_font_warning_arr[val];
        chartBak = chartBak_arr[val];
        link_b_color = link_b_color_arr[val];
        page_b_color = page_b_color_arr[val];
        modal_header_color = modal_header_color_arr[val];
        alarm_modal_border_color = alarm_modal_border_color_arr[val];
        time_disabled_b_color = time_disabled_b_color_arr[val];
        time_hover_b_color = time_hover_b_color_arr[val];
        confirmmodal_footer_cancel_b_color =
          confirmmodal_footer_cancel_b_color_arr[val];
        reset_but_b_color = reset_but_b_color_arr[val];
        topo_model_head_color = topo_model_head_color_arr[val];
        topo_checkbox_b_color = topo_checkbox_b_color_arr[val];
        oid_l_m_b_color = oid_l_m_b_color_arr[val];
        menu_active_color = menu_active_color_arr[val];
        left_menu_color = left_menu_color_arr[val];
        report_bg_color = report_bg_color_arr[val];

        wifi_boder_color = wifi_boder_color_arr[val];
        wifi_tip_color = wifi_tip_color_arr[val];
        wifi_tip_content_color = wifi_tip_content_color_arr[val];
        wifi_device_color = wifi_device_color_arr[val];
        monitorItem_color = monitorItem_color_arr[val];
        monitorItem_bg_color = monitorItem_bg_color_arr[val];
        ivu_radio_inner_color = ivu_radio_inner_color_arr[val];
        specialmonitor_checkedItem_bg_color = specialmonitor_checkedItem_bg_color_arr[val];
        probetaskModal_item_line_bg_color = probetaskModal_item_line_bg_color_arr[val];

        let modal_title_name_color = modal_title_name_color_arr[val];
        let modal_title_name_color_two = modal_title_name_color_two_arr[val];
        // 取消按钮颜色
        let modal_footer_but_cancel_color = modal_footer_but_b_color_arr[val];
        let modal_footer_but_cancel_background_color = modal_footer_but_cancel_background_color_arr[val];
        let modal_footer_but_cancel_background_border_color = modal_footer_but_cancel_background_border_color_arr[val];
        
        // 弹窗的确定按钮颜色设置
        let modal_footer_butok_b_color = modal_footer_butok_b_color_arr[val];
        let modal_footer_but_ok_background_color = modal_footer_but_ok_background_color_arr[val];
        let modal_footer_but_ok_background_border_color = modal_footer_but_ok_background_border_color_arr[val];

        let modal_background_color = modal_background_color_arr[val];
        let modal_icon_close_btn_color = modal_icon_close_btn_color_arr[val];

        //
         let table_fixed_right_color = tableFixedRight_arr[val];
         let table_header_bg_color = table_header_bg_color_arr[val];
         let table_td_th_border_color = table_td_th_border_color_arr[val];
        let monitorItem_border_color = monitorItem_border_color_arr[val];
        let monitorItem_color2 = monitorItem_color_arr2[val];

        let page_font_color = page_font_color_arr[val];
        let page_border_color = page_border_color_arr[val];
        let page_total_color = page_total_color_arr[val];
         let modal_header_font_color = modal_header_font_color_arr[val];
         let select_dropdown_border_color = select_dropdown_border_color_arr[val];
         let date_picker_cells_cell_range_bg_color = date_picker_cells_cell_range_bg_color_arr[val];
         let body_conent_b_color = body_conent_b_color_arr[val];
         let body_table_content_border_color = body_table_content_border_color_arr[val];
         let spectendency_chart_bg_color = spectendency_chart_bg_color_arr[val];
         let task_path_list_table_color = task_path_list_table_color_arr[val];
         let border_none_color = border_none_color_arr[val];
        let switch_close_color = switch_close_color_arr[val];
        let switch_close_font_color = switch_close_font_color_arr[val];
        let switch_close_after_bg_color = switch_close_after_bg_color_arr[val];
        let switch_close_border_color = switch_close_border_color_arr[val];
        let from_input_disabled_color = from_input_disabled_color_arr[val];
        let ivu_tag_bg_color = ivu_tag_bg_color_arr[val];
        let from_checkbox_check_color = from_checkbox_check_color_arr[val];
        let from_checkbox_check_border_color = from_checkbox_check_border_color_arr[val];
        let from_time_picker_cells_cell_hover_color = from_time_picker_cells_cell_hover_color_arr[val];
        let from_time_picker_cells_cell_border_color = from_time_picker_cells_cell_border_color_arr[val];
        let from_time_picker_cells_cell_select_bg_color = from_time_picker_cells_cell_select_bg_color_arr[val];
        let default_value_remarks_color = default_value_remarks_color_arr[val];
        let default_value_group_append_bg_color = default_value_group_append_bg_color_arr[val];
        let default_value_group_append_border_color = default_value_group_append_border_color_arr[val];
        let default_value_menu_color = default_value_menu_color_arr[val];
        let default_value_menu_select_font_color = default_value_menu_select_font_color_arr[val];
        let default_value_menu_select_bg_color = default_value_menu_select_bg_color_arr[val];

            let default_value_result_btn_bg_color = default_value_result_btn_bg_color_arr[val];
         let default_value_result_btn_color =default_value_result_btn_color_arr[val];
         let default_value_result_btn_border_color = default_value_result_btn_border_color_arr[val];
         let oid_left_bg_color = oid_left_bg_color_arr[val];
          let form_radio_border_color =form_radio_border_color_arr[val];
          let form_radio_check_color = form_radio_check_color_arr[val];
          let table_td_bg_color = table_td_bg_color_arr[val];
          let default_value_font_color = default_value_font_color_arr[val];

          let spectendency_choose_btn_color = spectendency_choose_btn_color_arr[val];
          let spectendency_choose_btn_border_color = spectendency_choose_btn_border_color_arr[val];
          let spectendency_choose_btn_bg_color = spectendency_choose_btn_bg_color_arr[val];


          let choose_data_btn_font_color = choose_data_btn_font_color_arr[val];
          let choose_data_btn_bg_color = choose_data_btn_bg_color_arr[val];
          let choose_data_btn_border_color = choose_data_btn_border_color_arr[val];
          let choose_data_btn_bg_color2 = choose_data_btn_bg_color_arr2[val];
          let choose_data_btn_font_color2 = choose_data_btn_font_color_arr2[val];

          let delete_data_btn_font_color = delete_data_btn_font_color_arr[val];
          let delete_data_btn_bg_color = delete_data_btn_bg_color_arr[val];
          let delete_data_btn_border_color = delete_data_btn_border_color_arr[val];


          let choose_file_btn_font_color = choose_file_btn_font_color_arr[val];
          let choose_file_btn_bg_color = choose_file_btn_bg_color_arr[val];
          let choose_file_btn_border_color = choose_file_btn_border_color_arr[val];

          let upgrade_btn_font_color = upgrade_btn_font_color_arr[val];
          let upgrade_btn_bg_color = upgrade_btn_bg_color_arr[val];
          let upgrade_btn_border_color = upgrade_btn_border_color_arr[val];
          let upgrade_lisenceInfo_color = upgrade_lisenceInfo_color_arr[val];

          let download_template_color = download_template_color_arr[val];

          let fault_phenomenon_content_color = fault_phenomenon_content_color_arr[val];

          let modal_background_color2 = modal_background_color_arr2[val];





          let btnChange_color = btnChange_color_arr[val];
          let btnChange_border_color = btnChange_border_color_arr[val];
          let btnChange_active_color = btnChange_active_color_arr[val];
          let btnChange_active_bg_color = btnChange_active_bg_color_arr[val];
          let btnChange_active_border_color = btnChange_active_border_color_arr[val];



          let field_font_color = field_font_color_arr[val];
          let field_table_td_bg_color = field_table_td_bg_color_arr[val];
          let field_table_border_color = field_table_border_color_arr[val];
          let field_table_bg_color = field_table_bg_color_arr[val];
          let field_table_td_hover_color = field_table_td_hover_color_arr[val];

          let picker_panel_content_after_color = picker_panel_content_after_color_arr[val];
          let pathtopo_legend_splitline_border_color = pathtopo_legend_splitline_border_color_arr[val];

          let dashboard_component_loading_bg_color = dashboard_component_loading_bg_color_arr[val];
          let field_sort_btn_hover_font_color = field_sort_btn_hover_font_color_arr[val];
          let field_sort_btn_hover_border_color = field_sort_btn_hover_border_color_arr[val];
          let field_sort_btn_border_color = field_sort_btn_border_color_arr[val];
          let field_sort_btn_bg_color = field_sort_btn_bg_color_arr[val];
          let field_sort_btn_font_color = field_sort_btn_font_color_arr[val];

          let testspeed_skinPrimary_bg_color = testspeed_skinPrimary_bg_color_arr[val];
          let testspeed_skinPrimary_font_color = testspeed_skinPrimary_font_color_arr[val];
          let testspeed_restStartBut_bg_color = testspeed_restStartBut_bg_color_arr[val];
          let tab_nav_font_color = tab_nav_font_color_arr[val];
          let tab_nav_margin_left = tab_nav_margin_left_arr[val];
          let tab_nav_active_font_color = tab_nav_active_font_color_arr[val];
          let tab_nav_active_font_weight = tab_nav_active_font_weight_arr[val];


          let next_btn_font_color = next_btn_font_color_arr[val];
          let next_btn_border_color = next_btn_border_color_arr[val];
          let next_btn_bg_color = next_btn_bg_color_arr[val];
          let next_btn_bg_color_2 = next_btn_bg_color_2_arr[val];


          let from_condition_btn_font_color = from_condition_btn_font_color_arr[val];
          let from_condition_btn_border_color = from_condition_btn_border_color_arr[val];
          let from_condition_btn_bg_color = from_condition_btn_bg_color_arr[val];


          let wifi_name_font_color = wifi_name_font_color_arr[val];
          let wifi_name_border_color = wifi_name_border_color_arr[val];
          let wifi_name_bg_color = wifi_name_bg_color_arr[val];
          let wifi_point_speed_item_process_color = wifi_point_speed_item_process_color_arr[val];


          let wifi_internal_bg_color = wifi_internal_bg_color_arr[val];
          let wifi_internal_border_color = wifi_internal_border_color_arr[val];
          let wifi_internal_title_font_color = wifi_internal_title_font_color_arr[val];
          let wifi_internal_title_border_color = wifi_internal_title_border_color_arr[val];
          let wifi_internal_title_bg_color = wifi_internal_title_bg_color_arr[val];


                  // 帮助手册样式
        let help_font_color = help_font_color_arr[val];
        let help_font_hover_color = help_font_hover_color_arr[val];
        let help_font_hover_bg_color = help_font_hover_bg_color_arr[val];

        // 仪表盘趋势图样式
        let dashboard_tooltip_bg_color = dashboard_tooltip_bg_color_arr[val];
        let dashboard_tooltip_color = dashboard_tooltip_color_arr[val];
        let dashboard_tooltip_border_color = dashboard_tooltip_border_color_arr[val];
        let topo_menu_region_border = topo_menu_region_border_arr[val];
        let path_fiber_g6_color = path_fiber_g6_color_arr[val];
        let path_tooltip_g6_color = path_tooltip_g6_color_arr[val];
        let path_tooltip_g6_bg_color = path_tooltip_g6_bg_color_arr[val];


         let license_upgrade_btn_border_color = license_upgrade_btn_border_color_arr[val];
         let license_upgrade_btn_font_color = license_upgrade_btn_font_color_arr[val];
         let license_contentBox_bgcolor = license_contentBox_bgcolor_arr[val];
         let license_text_color = license_text_color_arr[val];
         let sidebar_bg_color = sidebar_bg_color_arr[val];
         let sidebar_b_color_mr = sidebar_b_color_mr_arr[val];
         let  point_name_bg =  point_name_bg_arr[val];

         let btnConfirm_bg1_color = btnConfirm_bg1_color_arr[val];
         let btnConfirm_bg2_color = btnConfirm_bg2_color_arr[val];
         let btnConfirm_font_color = btnConfirm_font_color_arr[val];
         let btnConfirm_border_color = btnConfirm_border_color_arr[val];
         let time_picker_suffix_color = time_picker_suffix_color_arr[val];
         let btn_change_active_bg = btn_change_active_bg_arr[val];
         let model_borer_color = model_borer_color_arr[val]
         let data_picker_no_active=data_picker_no_active_arr[val]

        // 获取根
        let root = document.documentElement;

        // root.style.setProperty('--body_b_color', body_b_color);
        window.skin = {
       
          "--topo_menu_region_border": topo_menu_region_border,
          "--body_b_color": body_b_color,
          "--font_color": font_color,
          "--border_color": border_color,
          "--icon_tree": icon_tree,
          "--th_b_color": th_b_color,
          "--th_b_stripe_color": th_b_stripe_color,
          "--td_b_color": td_b_color,
          "--th_font_color": th_font_color,
          "--td_font_color": td_font_color,
          "--thd_border_color": thd_border_color,
          "--table_sort_color": table_sort_color,
          "--table_sorton_color": table_sorton_color,
          "--input_font_color": input_font_color,
          "--input_b_color": input_b_color,
          "--input_checkbox_b_color": input_checkbox_b_color,
          "--selectdrop_b_color": selectdrop_b_color,
          "--selectdrop_font_color": selectdrop_font_color,
          "--select_dropdown_border_color": select_dropdown_border_color,
          "--confirmModal_font_color": confirmModal_font_color,

          "--dash_h_b_color": dash_h_b_color,
          "--dash_h_f_color": dash_h_f_color,
          "--dash_b_b_color": dash_b_b_color,
          "--dash_border_color": dash_border_color,
          "--input_placeholder_color": input_placeholder_color,
          "--org_btn_bg_color": org_btn_bg_color,
          "--table_checkbox_border_color": table_checkbox_border_color,

          "--primary_bcg": primary_bcg,
          "--primary_border": primary_border,
          "--primary_font": primary_font,
          "--warning_bcg": warning_bcg,
          "--warning_border": warning_border,
          "--warning_font": warning_font,
          "--success_bcg": success_bcg,
          "--success_border": success_border,
          "--success_font": success_font,
          "--error_bcg": error_bcg,
          "--error_border": error_border,
          "--error_font": error_font,
          "--message_success_color": message_success_color,
          "--message_err_color": message_err_color,
          "--message_warning_color": message_warning_color,
          "--message_font_success": message_font_success,
          "--message_font_err": message_font_err,
          "--message_font_warning": message_font_warning,

          "--ivu_tag_border": ivu_tag_border,
          "--ivu_select_arrow": ivu_select_arrow,
          "--modal_header_b_color": modal_header_b_color,
          "--modal_footer_but_b_color": modal_footer_but_cancel_color,
          "--modal_footer_butok_b_color": modal_footer_butok_b_color,
          "--modal_footer_but_ok_background_color": modal_footer_but_ok_background_color,
          "--modal_footer_but_ok_background_border_color": modal_footer_but_ok_background_border_color,
          "--modal_b_color": modal_b_color,
          "--modal_input_border_color": modal_input_border_color,
          "--modal_footer_but_cancel_color": modal_footer_but_cancel_color,
          "--modal_footer_but_cancel_background_color": modal_footer_but_cancel_background_color,
          "--modal_footer_but_cancel_background_border_color": modal_footer_but_cancel_background_border_color,
          "--modal_icon_close_btn_color": modal_icon_close_btn_color,
          "--contentBox_bgcolor": contentBox_bgcolor,
          "--header_b_color": header_b_color,
          "--header_font_color": header_font_color,
          "--header_active_bg_color": header_active_bg_color,
          "--header_list_bg_color": header_list_bg_color,
          "--header_list_font_color": header_list_font_color,
          "--header_list_activefont_color": header_list_activefont_color,
          "--header_list_activebg_color": header_list_activebg_color,
          "--message_bg": message_bg,
          "--chartBak": chartBak,
          "--link_b_color": link_b_color,
          "--page_b_color": page_b_color,
          "--page_font_color": page_font_color,
          "--page_border_color": page_border_color,
          "--modal_header_color": modal_header_color,
          "--alarm_modal_border_color": alarm_modal_border_color,
          "--time_disabled_b_color": time_disabled_b_color,
          "--time_hover_b_color": time_hover_b_color,
          "--confirmmodal_footer_cancel_b_color":confirmmodal_footer_cancel_b_color,
          "--reset_but_b_color": reset_but_b_color,
          "--topo_model_head_color": topo_model_head_color,
          "--topo_checkbox_b_color": topo_checkbox_b_color,
          "--oid_l_m_b_color": oid_l_m_b_color,
          "--menu_active_color": menu_active_color,
          "--left_menu_color": left_menu_color,
          "--report_bg_color": report_bg_color,
          "--wifi_boder_color": wifi_boder_color,
          "--wifi_tip_color": wifi_tip_color,
          "--wifi_device_color": wifi_device_color,
          "--wifi_tip_content_color": wifi_tip_content_color,
          "--monitorItem_color": monitorItem_color,
          "--monitorItem_bg_color": monitorItem_bg_color,
          "--search_lable_font_color": search_lable_font_color,
          "--table_content_column_link_color":table_content_column_link_color,
          "--button_background_color":button_background_color,
          "--search_check_box_background_color":search_check_box_background_color,
          "--search_check_box_font_color":search_check_box_font_color,
          "--button_background_two_color":button_background_two_color,
          "--search_check_box_bg_tabStyle_color":search_check_box_bg_tabStyle_color,
          "--search_check_box_font_tabStyle_color":search_check_box_font_tabStyle_color,
          "--search_check_box_font_tabStyle_two_color":search_check_box_font_tabStyle_two_color,
          "--search_ivu_select_input_boder_1_color":search_ivu_select_input_boder_1_color,
          "--search_ivu_select_input_boder_2_color":search_ivu_select_input_boder_2_color,
          "--search_ivu_select_input_boder_3_color":search_ivu_select_input_boder_3_color,
          "--search_ivu_select_input_boder_4_color":search_ivu_select_input_boder_4_color,
          "--button_networdFault_del_bg_color":button_networdFault_del_bg_color,
          "--button_networdFault_del_bg_2_color":button_networdFault_del_bg_2_color,
          "--button_networdFault_del_bg_3_color":button_networdFault_del_bg_3_color,
          "--button_networdFault_del_bg_4_color":button_networdFault_del_bg_4_color,
          "--networdFault_type_border_color":networdFault_type_border_color,
          "--networdFault_type_border_normal_color":networdFault_type_border_normal_color,
          "--networdFault_type_border_normal_font_color":networdFault_type_border_normal_font_color,
          "--networdFault_duration_border_select_color":networdFault_duration_border_select_color,
          "--query_org_select_color":query_org_select_color,
          "--button_background_three_color":button_background_three_color,
          "--reset_export_del_button_font_color":reset_export_del_button_font_color,
          "--del_button_font_color":del_button_font_color,
          "--reset_export_button_border_1_color":reset_export_button_border_1_color,
          "--reset_export_button_border_2_color":reset_export_button_border_2_color,
          "--reset_export_button_border_3_color":reset_export_button_border_3_color,
          "--scrollbar_thumb_bg_color":scrollbar_thumb_bg_color,
          "--scrollbar_track_bg_color":scrollbar_track_bg_color,
          "--scrollbar_thumb_shadow_color":scrollbar_thumb_shadow_color,
          "--scrollbar_track_shadow_color":scrollbar_track_shadow_color,
          "--query_btn_hover_bg_1_color":query_btn_hover_bg_1_color,
          "--query_btn_hover_bg_2_color":query_btn_hover_bg_2_color,
          "--query_btn_active_bg_1_color":query_btn_active_bg_1_color,
          "--query_btn_active_bg_2_color":query_btn_active_bg_2_color,
          "--more_btn_font_color":more_btn_font_color,
          "--more_btn_background_image_1_color":more_btn_background_image_1_color,
          "--more_btn_background_image_2_color":more_btn_background_image_2_color,
          "--more_btn_ivu_dropdown_item_hover_bg_color":more_btn_ivu_dropdown_item_hover_bg_color,
          "--more_btn_ivu_dropdown_item_hover_font_color":more_btn_ivu_dropdown_item_hover_font_color,
          "--more_btn_ivu_dropdown_item_font_color":more_btn_ivu_dropdown_item_font_color,
          "--ivu_tooltip_inner_bg_color":ivu_tooltip_inner_bg_color,
          "--table_text_click_color":table_text_click_color,
          "--ivu_tooltip_inner_font_color":ivu_tooltip_inner_font_color,
          "--ivu_select_visible_border_color":ivu_select_visible_border_color,
          "--inintEchart_dataZoom_filler_color":inintEchart_dataZoom_filler_color,
          "--inintEchart_dataZoom_border_color":inintEchart_dataZoom_border_color,
          "--ivu_picker_confirm_bg_color":ivu_picker_confirm_bg_color,
          "--ivu_picker_confirm_border_color":ivu_picker_confirm_border_color,
          "--ivu_picker_confirm_font_color":ivu_picker_confirm_font_color,
          "--ivu_picker_confirm_hover_bg_color":ivu_picker_confirm_hover_bg_color,
          "--ivu_picker_confirm_hover_font_color":ivu_picker_confirm_hover_font_color,
          "--ivu_picker_confirm_select_font_color":ivu_picker_confirm_select_font_color,
          "--rpapth_a_link_lTitle_color":rpapth_a_link_lTitle_color,
          "--quality_report_left_menu_bg_color":quality_report_left_menu_bg_color,
          "--quality_report_left_ivu_menu_font_color":quality_report_left_ivu_menu_font_color,
          "--defaultmanager_left_menu_bg_color":defaultmanager_left_menu_bg_color,
          "--defaultmanager_left_menu_font_color":defaultmanager_left_menu_font_color,
          "--dashboard_button_hover_font_color":dashboard_button_hover_font_color,
          "--dashboard_component_title_font_color":dashboard_component_title_font_color,
          "--dashboard_bigscale_bg1_color":dashboard_bigscale_bg1_color,
          "--dashboard_bigscale_bg2_color":dashboard_bigscale_bg2_color,
          "--dashboard_userDefined_btn_border1_color":dashboard_userDefined_btn_border1_color,
          "--dashboard_userDefined_btn_border2_color":dashboard_userDefined_btn_border2_color,
          "--pathtopo_tabs_box_select_font_color":pathtopo_tabs_box_select_font_color,
          "--pathtopo_tabs_box_select_bg_color":pathtopo_tabs_box_select_bg_color,
          "--pathtopo_tabs_box_select_border_color":pathtopo_tabs_box_select_border_color,
          "--pathtopo_logo_box_bg_color":pathtopo_logo_box_bg_color,
          "--pathtopo_logo_box_font_color":pathtopo_logo_box_font_color,
          "--pathtopo_fiber_routine_model_font_color":pathtopo_fiber_routine_model_font_color,
          "--pathtopo_fiber_routine_model_active_font_color":pathtopo_fiber_routine_model_active_font_color,
          "--pathtopo_icon_box_border_color":pathtopo_icon_box_border_color,
          "--pathtopo_icon_special_suspend_border_color":pathtopo_icon_special_suspend_border_color,
          "--pathtopo_my_tooltip_bg_color":pathtopo_my_tooltip_bg_color,
          "--pathtopo_my_tooltip_left_box_drag_step_bg_color":pathtopo_my_tooltip_left_box_drag_step_bg_color,
          "--pathtopo_my_tooltip_left_box_step_bg_color":pathtopo_my_tooltip_left_box_step_bg_color,
          "--pathtopo_my_tooltip_left_box_step_circle_bg_color":pathtopo_my_tooltip_left_box_step_circle_bg_color,
          "--pathtopo_my_tooltip_left_box_num_tip_bg_color":pathtopo_my_tooltip_left_box_num_tip_bg_color,
          "--comm_back_icon_bg_color":comm_back_icon_bg_color,
          "--pathtopo_list_select_bg_color":pathtopo_list_select_bg_color,
          "--data_sync_primary_bg":data_sync_primary_bg,
          "--comm_strong": this.$t("comm_strong"),
          "--comm_medium": this.$t("comm_medium"),
          "--comm_weak": this.$t("comm_weak"),

          // zxq ----
          "--table_fixed_right_color":table_fixed_right_color,
          "--table_td_th_border_color":table_td_th_border_color,
          "--modal_background_color":modal_background_color,
          "--modal_header_font_color":modal_header_font_color,
          "--table_header_bg_color":table_header_bg_color,
          "--date_picker_cells_cell_range_bg_color":date_picker_cells_cell_range_bg_color,
          "--page_total_color":page_total_color,
          "--modal_title_name_color":modal_title_name_color,
          "--modal_title_name_color_two":modal_title_name_color_two,
          "--body_conent_b_color":body_conent_b_color,
          "--body_table_content_border_color":body_table_content_border_color,
          "--spectendency_chart_bg_color":spectendency_chart_bg_color,
          "--task_path_list_table_color":task_path_list_table_color,
          "--border_none_color":border_none_color,
          "--switch_close_color":switch_close_color,
          "--switch_close_font_color":switch_close_font_color,
          "--switch_close_after_bg_color":switch_close_after_bg_color,
          "--switch_close_border_color":switch_close_border_color,
          "--ivu_tag_bg_color":ivu_tag_bg_color,
          "--from_checkbox_check_color":from_checkbox_check_color,
          "--from_checkbox_check_border_color":from_checkbox_check_border_color,
          "--from_time_picker_cells_cell_hover_color":from_time_picker_cells_cell_hover_color,
          "--from_time_picker_cells_cell_select_bg_color":from_time_picker_cells_cell_select_bg_color,
          "--from_time_picker_cells_cell_border_color":from_time_picker_cells_cell_border_color,
          "--from_input_disabled_color":from_input_disabled_color,
          "--default_value_remarks_color":default_value_remarks_color,
          "--default_value_group_append_bg_color":default_value_group_append_bg_color,
          "--default_value_group_append_border_color":default_value_group_append_border_color,
          "--default_value_menu_color":default_value_menu_color,
          "--default_value_menu_select_font_color":default_value_menu_select_font_color,
          "--default_value_menu_select_bg_color":default_value_menu_select_bg_color,
          "--default_value_result_btn_bg_color":default_value_result_btn_bg_color,
          "--default_value_result_btn_color":default_value_result_btn_color,
          "--default_value_result_btn_border_color":default_value_result_btn_border_color,
          "--default_value_font_color":default_value_font_color,
          "--oid_left_bg_color":oid_left_bg_color,
          "--form_radio_check_color":form_radio_check_color,
          "--form_radio_border_color":form_radio_border_color,
          "--table_td_bg_color":table_td_bg_color,

          "--spectendency_choose_btn_color":spectendency_choose_btn_color,
          "--spectendency_choose_btn_border_color":spectendency_choose_btn_border_color,
          "--spectendency_choose_btn_bg_color":spectendency_choose_btn_bg_color,

          "--choose_data_btn_font_color":choose_data_btn_font_color,
          "--choose_data_btn_bg_color":choose_data_btn_bg_color,
          "--choose_data_btn_border_color":choose_data_btn_border_color,
          "--choose_data_btn_bg_color2":choose_data_btn_bg_color2,
          "--choose_data_btn_font_color2":choose_data_btn_font_color2,


          "--delete_data_btn_font_color":delete_data_btn_font_color,
          "--delete_data_btn_bg_color":delete_data_btn_bg_color,
          "--delete_data_btn_border_color":delete_data_btn_border_color,


          "--choose_file_btn_font_color":choose_file_btn_font_color,
          "--choose_file_btn_bg_color":choose_file_btn_bg_color,
          "--choose_file_btn_border_color":choose_file_btn_border_color,


          "--upgrade_btn_font_color":upgrade_btn_font_color,
          "--upgrade_btn_bg_color":upgrade_btn_bg_color,
          "--upgrade_btn_border_color":upgrade_btn_border_color,
          "--upgrade_lisenceInfo_color":upgrade_lisenceInfo_color,

          "--download_template_color":download_template_color,
          "--fault_phenomenon_content_color":fault_phenomenon_content_color,

          "--btnChange_color":btnChange_color,
          "--btnChange_border_color":btnChange_border_color,
          "--btnChange_active_color":btnChange_active_color,
          "--btnChange_active_bg_color":btnChange_active_bg_color,
          "--btnChange_active_border_color":btnChange_active_border_color,


          "--field_font_color":field_font_color,
          "--field_table_td_bg_color":field_table_td_bg_color,
          "--field_table_border_color":field_table_border_color,
          "--field_table_bg_color":field_table_bg_color,
          "--field_table_td_hover_color":field_table_td_hover_color,


          "--field_sort_btn_hover_font_color":field_sort_btn_hover_font_color,
          "--field_sort_btn_hover_border_color":field_sort_btn_hover_border_color,
          "--field_sort_btn_border_color":field_sort_btn_border_color,
          "--field_sort_btn_bg_color":field_sort_btn_bg_color,
          "--field_sort_btn_font_color":field_sort_btn_font_color,
          "--modal_background_color2":modal_background_color2,
          "--testspeed_skinPrimary_bg_color":testspeed_skinPrimary_bg_color,
          "--testspeed_skinPrimary_font_color":testspeed_skinPrimary_font_color,
          "--testspeed_restStartBut_bg_color":testspeed_restStartBut_bg_color,
          "--tab_nav_font_color":tab_nav_font_color,
          "--tab_nav_margin_left":tab_nav_margin_left,
          "--tab_nav_active_font_color":tab_nav_active_font_color,
          "--tab_nav_active_font_weight":tab_nav_active_font_weight,


          "--next_btn_font_color":next_btn_font_color,
          "--next_btn_border_color":next_btn_border_color,
          "--next_btn_bg_color":next_btn_bg_color,
          "--next_btn_bg_color_2":next_btn_bg_color_2,

          "--btnConfirm_bg1_color":btnConfirm_bg1_color,
          "--btnConfirm_bg2_color":btnConfirm_bg2_color,
          "--btnConfirm_font_color":btnConfirm_font_color,
          "--btnConfirm_border_color":btnConfirm_border_color,


          "--from_condition_btn_font_color":from_condition_btn_font_color,
          "--from_condition_btn_border_color":from_condition_btn_border_color,
          "--from_condition_btn_bg_color":from_condition_btn_bg_color,

          "--wifi_name_font_color":wifi_name_font_color,
          "--wifi_name_border_color":wifi_name_border_color,
          "--wifi_name_bg_color":wifi_name_bg_color,
          "--wifi_point_speed_item_process_color":wifi_point_speed_item_process_color,
          "--pathtopo_legend_splitline_border_color":pathtopo_legend_splitline_border_color,
          "--dashboard_component_loading_bg_color":dashboard_component_loading_bg_color,
          "--wifi_internal_bg_color":wifi_internal_bg_color,
          "--wifi_internal_border_color":wifi_internal_border_color,
          "--wifi_internal_title_font_color":wifi_internal_title_font_color,
          "--wifi_internal_title_border_color":wifi_internal_title_border_color,
          "--wifi_internal_title_bg_color":wifi_internal_title_bg_color,


          "--help_font_color":help_font_color,
          "--help_font_hover_color":help_font_hover_color,
          "--help_font_hover_bg_color":help_font_hover_bg_color,

          "--dashboard_tooltip_bg_color":dashboard_tooltip_bg_color,
          "--dashboard_tooltip_color":dashboard_tooltip_color,

          "--pie_legend_normal_color":pie_legend_normal_color,
          "--pie_legend_deterioration_color":pie_legend_deterioration_color,
          "--pie_legend_break_color":pie_legend_break_color,
          "--pie_legend_suspend_color":pie_legend_suspend_color,
          "--pie_count_color":pie_count_color,
          "--ivu_radio_wrapper_checked_bg":ivu_radio_wrapper_checked_bg,
          "--ivu_radio_wrapper_checked_font":ivu_radio_wrapper_checked_font,
          "--ivu_radio_inner_color":ivu_radio_inner_color,
          "--specialmonitor_checkedItem_bg_color":specialmonitor_checkedItem_bg_color,
          "--probetaskModal_item_line_bg_color":probetaskModal_item_line_bg_color,
          "--picker_panel_content_after_color":picker_panel_content_after_color,

          "--license_upgrade_btn_border_color":license_upgrade_btn_border_color,
          "--license_upgrade_btn_font_color":license_upgrade_btn_font_color,
          "--license_contentBox_bgcolor":license_contentBox_bgcolor,
          "--license_text_color":license_text_color,
          "--monitorItem_border_color":monitorItem_border_color,
          "--monitorItem_color2":monitorItem_color2,
          "--dashboard_tooltip_border_color":dashboard_tooltip_border_color,
          "--path_fiber_g6_color":path_fiber_g6_color,
          "--path_tooltip_g6_color":path_tooltip_g6_color,
          "--path_tooltip_g6_bg_color":path_tooltip_g6_bg_color,
          "--sidebar_bg_color":sidebar_bg_color,
          "--sidebar_bg_color_mr":sidebar_b_color_mr,
          "--point_name_bg_color":point_name_bg,
          "--time_picker_suffix_color":time_picker_suffix_color,
          "--btn_change_active_bg" : btn_change_active_bg,
          "--model_borer_color":model_borer_color,
          "--data_picker_no_active":data_picker_no_active,
          "--speciallinereport_progress_bg_color":speciallinereport_progress_bg_color,
          "--speciallinereport_progress_font_color":speciallinereport_progress_font_color
           // zxq -------

        };
        // let comm_strong = this.$t('comm_strong');
        // let comm_medium = this.$t('comm_medium');
        // let comm_weak = this.$t('comm_weak');
        window.isdarkSkin = val;
        // sessionStorage.setItem("dark", val);
        window.postMessage(val, window.location.origin);
        skinChange(window.skin);
        const iframe2 = document.getElementById("sub-content-page");
        iframe2.contentWindow.skinChange(window.skin);
        // iframe2.contentWindow.location.reload(true)
      },
      deep: true,
      immediate: true,
    },
    tipActive: {
      // tipItem() {
      handler(newVal) {
        console.log("监听到了");
        //    如果到了最后一步，用户一分钟没有操作将提示框关闭
        if (newVal == 13) {
          setTimeout(() => {
            this.changeTipActive(null);
          }, 6000);
        }
        let arr = this.tipList.filter((item) => item.id == this.tipActive);
        this.tipItem = arr[0];

        let arrName = [
          { id: 1, obj: { en: "Probe Task", cn: "拨测分析" } },
          { id: 2, obj: { en: "Probe Tasks", cn: "拨测任务管理" } },
          { id: 8, obj: { en: "Alerts", cn: "故障清单" } },
          { id: 9, obj: { en: "Topology", cn: "拓扑管理" } },
          { id: 10, obj: { en: "PT Analysis", cn: "拨测质差分析" } },
          { id: 11, obj: { en: "Link Analysis", cn: "链路质差分析" } },
          { id: 12, obj: { en: "PT Records", cn: "拨测健康档案" } },
        ];
        console.log(this.tipActive, "tipActive");
        const findResult = arrName.filter((item) => item.id == this.tipActive);
        console.log(findResult, findResult);
        if (findResult) {
          this.activeTipName = findResult[0].obj;
        }
        console.log(this.activeTipName, "activeTipName");
      },
      immediate: true,

      //    console.log(arr,'arr')
      //     return arr[0]

      // },
    },
    iframSrc(newVal) {
      console.log(newVal, "newVal");
      this.$Message.destroy();
    },
    // statrListLight:{
    //   handler(newVal) {
    //     // console.log(newVal,'newVal')
    //     this.setSwitchState(newVal)
    //   },
    //   deep: true
    // }
  },
  filters: {
    returnLabel2(val) {
      console.log(this);
      let faultList = global.faultList,
        _this = this;
      if (faultList.length > 0) {
        var lists = faultList.filter((item) => item.value == 10);
        if (lists.length == 0) {
          faultList.push({
            value: 10,
            label: _this.$t("dash_port_congestion"),
          });
        }
      }

      let nameObj = faultList.filter((item) => item.value == val);
      if (nameObj.length != 0) {
        return nameObj[0].label;
      } else {
        return this.$t("comm_other");
      }
    },
    ipGet(val) {
      var result = val;
      if (val != "" || val != null) {
        result = val.split(",")[0];
      } else {
        result = val;
      }
      let maxWidth = 450; // 获取动态传递的宽度
      var ips = result.split("-");
      if (ips.length > 0) {
        var ip1 = ipv6Format.formatIPv6Address(ips[0], maxWidth);
        var ip2 = ipv6Format.formatIPv6Address(ips[1], maxWidth);
        return ip1 + "-" + ip2;
      } else {
        result = ipv6Format.formatIPv6Address(result, maxWidth);
        return result;
      }
    },
  },
  mounted() {

    
     // 设置最小值，防止页面过小
    var clientWidth = document.documentElement.clientWidth * 0.98;
    if(clientWidth < 1551){
      clientWidth = 1551;
    }
    localStorage.setItem("modalWidth", clientWidth);
     this.getSwitchState();
     if(this.skinValue == 1) {
      this.menuHeated = this.$refs.menuheader.offsetHeight;
      
     }
    
    // console.log('logoHeight',)
    localStorage.setItem(
      "logoHeight",
      document.getElementById("logoHeight").clientHeight
    );
    // console.log("menuHeated", this.$refs.menuheader, this.menuHeated);
    //     window.onbeforeunload = function (e) {
    //       e = e || window.event;
    //       console.log(window.isdarkSkin);
    //       // 兼容IE8和Firefox 4以前的版本
    //       if (e) {
    //         e.returnValue = '关闭提示';
    //       }
    // return '关闭提示'
    //       // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
    //     };
    resizeZoom();
    //开始定时执行获取session过期与否
    this.sessionInterval = setInterval(() => {
      this.GET_SESSION_OVERTIME();
    }, 20 * 1000);
   

    this.$refs.audio.muted = true;
    let specialNmae = JSON.parse(sessionStorage.getItem("menu"));
    if(specialNmae.functionUrl === "/dashboard"){
      this.startMemoryMonitor()
    }
    // if (sessionStorage.getItem("menu")) {
    //   if (specialNmae.navName === "中继线路监测") {
    //     document.body.style.overflow = "visible";
    //     document.getElementById("specialStyle").style.overflow = "visible";
    //   } else {
    //     document.body.style.overflow = "hidden";
    //     document.getElementById("specialStyle").style.overflow = "hidden";
    //   }
    // }

    window.vm = this;

    window.addEventListener("message", (e) => {
      if (e && e.data) {
        if (e.data.type == "msg") {
          this.proMsg(e.data.msg, e.data.proType);
          return;
        }
      }
      this.setActive(e.data);
    });
   
    this.accessToken = JSON.parse(sessionStorage.getItem("accessToken"));
    
    this.user.userName = handlePassword.decrypt(this.accessToken.user.userName);
    var _this = this;
    if (sessionStorage.hasOwnProperty("menu")) {
      let menu = JSON.parse(sessionStorage.getItem("menu"));
      let functionUrl = menu.functionUrl;
      const threeMenu = {
        "/autodiscover": ["/discoverdata", "/discoverrule"],
        "/devicefind": ["/devicedata", "/devicerule", "/devicevoucher"],
      };
      const flatten = (arr) => {
        return [].concat(
          ...arr.map((x) => (Array.isArray(x) ? flatten(x) : x))
        );
      };
      let secondMenu = null;
      for (let key in threeMenu) {
        if (secondMenu) {
          break;
        }
        if (threeMenu[key].indexOf(functionUrl) > -1) {
          secondMenu = key;
        }
      }
      const threeMenuArr = flatten(Object.values(threeMenu));
      if (threeMenuArr.indexOf(functionUrl) > -1) {
        let functionUrl2 = secondMenu;
        this.iframSrc =
          window.location.hostname === "localhost"
            ? "/anpm-plug-" + functionUrl2.split("/")[1] + "-ui.html"
            : functionUrl2;
      } else {
        this.iframSrc =
          window.location.hostname === "localhost"
            ? "/anpm-plug-" + functionUrl.split("/")[1] + "-ui.html"
            : functionUrl;
      }
      this.parentactiveId = menu.parentactiveId;
      this.activeId = menu.activeId;
      this.preMenu = menu.parentFunctionName;
      this.activeName = menu.navName;
    } else {
      this.parentactiveId = 1;
      this.activeId = null;
      this.preMenu = "";
      this.activeName = _this.$t("server_home_page");
      this.iframSrc =
        window.location.hostname === "localhost"
          ? "/anpm-plug-home-ui.html"
          : "/home?rd" + Math.random();
    }
    // debugger
    if(this.skinValue == 0) {
      // this.setIframSrc(this.iframSrc)
      // this.$store.commit('m_lightMenu/setIframSrc',this.iframSrc)
    }else {
       document.getElementById("sub-content-page").src = this.iframSrc;


    }
   
    addDraggable();
  },
  destroyed() {
    window.onbeforeunload = function (e) {
      var e = window.event || e;
      e.returnValue = "是否确定关闭页面?";
    };
    // window.removeEventListener('beforeunload', e => this.beforeunloadHandler(e))
    // window.removeEventListener('unload', e => this.unloadHandler(e))
  },
  methods: {
    ...mapMutations("m_help", ["changeTipActive", "changeTipStatus"]),
    // 浅色版本修改配置
    updateStartList() {
      this.setSwitchState(this.statrListLight)
      
    },
    // 监测内存
       // 检查内存使用情况
  checkMemoryUsage() {
    if (window.performance && window.performance.memory) {
      const usedMemory = window.performance.memory.usedJSHeapSize / (1024 * 1024); // 转换为MB
      console.log('当前内存使用:', usedMemory.toFixed(2) + 'MB');
      
      if (usedMemory > this.memoryThreshold) {
        console.log('内存占用超过阈值，准备刷新页面');
        // 刷新页面前保存必要的状态
       
        window.location.reload();
      }
    }
  },
    startMemoryMonitor() {
    this.memoryTimer = setInterval(() => {
      this.checkMemoryUsage();
    }, 1 * 60 * 1000); // 每1分钟检查一次
  },
   measureTextWidth() {
          // this.systemName
         
          // console.log(textWidth,'文本宽度')
          let textBox = 300
        if(this.logoImgUrl) {
          textBox = 300-32-30

        }
        let span = document.createElement('span')
          
          span.style.fontSize ='20px'
          span.style.whiteSpace = 'nowrap';
          span.textContent = this.systemName
          document.body.appendChild(span)
          let textWidth = span.offsetWidth
          document.body.removeChild(span)
     
        
          console.log(textWidth,textBox,'文字宽度。。。。。。。。。。。。。。。。。。。')
       
        // console.log('textBox',textBox)
        if(textWidth > textBox) {
          let size  = 20/textWidth * textBox - 1 
          if(size <10) {
            size = 10
          } else {
            size >= 11 ? size = size - 1 : size = size
          
    }
          this.titleSize = size + 'px'

           
          
        }
       
       
          
        },
    checkLisence() {
      this.$http.wisdomPost("/lisence/check").then((resJson) => {
        if (resJson && resJson.code == 1) {
          console.log("checkLisence==>" , resJson)
        } else {
            this.$Message.warning({
              duration: 5,
              closable: true,
              content: resJson.msg,
              background: true,
            });
        }
      });
    },
    // 判断用户是否有权限开启界面导航
    async isPermission() {
      console.log("111111111");

      try {
        const res = await this.$http.get("/lisence/queryInterfaceGuidance");
        //  debbuger
        console.log(res, "res");
        // debugger
        if (res.code == 1) {
          if (res.data.showInterfaceGuidance == 1) {
            // 判断用户是否有开启用户界面的权限
            sessionStorage.setItem("nav-per", "true");
          } else {
            sessionStorage.setItem("nav-per", "false");
          }

          // debugger
        } else {
          sessionStorage.setItem("nav-per", "false");
        }
      } catch (error) {}
    },
    handleConfig() {
      if (this.tipStatus == "true") {
        return;
      }
      this.isConfigShow = true;
    },
    closeConfig() {
      this.isConfigShow = false;
    },
    // 界面导航要先看有没有liscense
    async getLisence(num) {
      // debugger
      try {
        const res = await this.$http.post("/lisence/check");
        if (res.code !== 1) {
          // debugger
          // this.changeTipStatus(false)
          // this.changeTipActive(null)
          this.isLicense = false;
        } else {
          console.log(num);
          // debugger

          if (num == 1) {
            // debugger;
            this.changeTipActive(1);
            this.changeTipStatus(true);
            this.isLicense = true;
            localStorage.removeItem("isFirst");

            location.reload();
          }
          this.isLicense = true;
        }
      } catch (error) {}
    },
    // 界面引导，点击跳过
    closeTip() {
      location.reload();

      this.changeTipStatus(false);
      this.changeTipActive(13);
    },
    handleEnter() {
      this.isOver = true;
    },

    // 关闭当前
    closeCurrent(id) {
      if (id == 12) {
        let i = Number(id) + 1;
        this.changeTipActive(i);
        if (i == 13) {
          this.changeTipStatus(false);
          // this.changeTipActive(null)
        }
        // 刷新页面
        location.reload();
      } else {
        let i = Number(id) + 1;
        this.changeTipActive(i);
        if (i == 13) {
          this.changeTipStatus(false);
          // this.changeTipActive(null)
        }
      }
    },
    // 界面引导帮助中心
    handleHelp(value) {
      console.log(value);
      if (value == "nav") {
        // 唤起界面引导
        this.changeTipStatus(true);
        this.changeTipActive(1);
        // 重新刷新页面，页面回到仪表盘
        let menu = {
          activeId: null,
          functionUrl: "/dashboard",
          navName: "仪表盘",
          node1: "",
          parentFunctionName: "",
          parentactiveId: 1,
          subMenuName: "",
        };
        sessionStorage.setItem("menu", JSON.stringify(menu));
        location.reload();
      } else {
        //打开帮助文档网页
        console.log(urlData, "123");
        let str = "";
        let url = JSON.parse(sessionStorage.getItem("menu")).functionUrl;
        let findResult = urlData.urlData.find((item) => item.fnUrl == url);
        if (findResult) {
          str = findResult.toUrl;
        } else {
          str = "1.产品简介.html#1产品简介";
        }

        const targetUrl = "/helpmenual/md/" + str;
        //    const targetUrl = 'http://localhost:4000/md/' + str;
        top.open(targetUrl, "_blank");
      }
    },
    // 界面导航最后一步，帮助中心展开最后一步提示消失
    handlerVisible(visible) {
      // console.log(str)
      if (visible) {
        // 如果在最后一步，下拉框展开提示消失
        if (this.tipActive == "13") {
          // console.log(11111)
          this.changeTipActive(null);
        }
      }
    },
    // 界面引导处理显示关系
    fillterTipShow(menu) {
      if (this.tipShow) {
        //    return true
        // console.log(this.activeTip,'有没有')
        if (
          menu.functionName == this.activeTipName.cn ||
          menu.functionName == this.activeTipName.en
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    // 处理一级标题禁止点击
    fillterDisable(menuLev1) {
      if (tipStatus) {
        if (
          menuLev1.functionName == "拨测分析" ||
          menuLev1.functionName == "Probe Task"
        ) {
          return false;
        } else {
          return true;
        }
      }
    },
    // 界面导航处理其他二级菜单不显示
    filterMenuLev2(menu) {
      if (this.tipStatus == "true") {
        // 如果在界面导航只有拨测分析的二级菜单显示
        if (
          menu.functionName == "拨测分析" ||
          menu.functionName == "Probe Task"
        ) {
          if (this.tipActive == 8 || this.tipActive == 9) {
            // debugger
            // console.log(123)
            return false;
          } else {
            return true;
          }
        } else {
          return false;
        }
        // 如果故障清单和路径topo的时候隐藏二级菜单（因为要遮挡）
      } else {
        if (menu.sysFunctionList && menu.sysFunctionList.length > 0) {
          return true;
        } else {
          return false;
        }
      }
    },

    returnLabel(item) {
      // console.log(item , this , "------------------")
      let val = item.faultType;
      let faultList = global.faultList,
        _this = this;
      if (faultList.length > 0) {
        var lists = faultList.filter((item) => item.value == 10);
        if (lists.length == 0) {
          faultList.push({
            value: 10,
            label: _this.$t("dash_port_congestion"),
          });
        }
      }

      let nameObj = faultList.filter((item) => item.value == val);
      if (nameObj.length != 0) {
        return nameObj[0].label;
      } else {
        return _this.$t("comm_other");
      }
    },
    getRecoveryedLabel(item) {
      return item.recoveryed == "0"
        ? "(" + this.$t("common_recovered") + ")"
        : "(" + this.$t("common_unrecovered") + ")";
    },
    changLanguage(value) {
      var langStr = value,
        _this = this;
      // console.log('lang=>',langStr);
      if (langStr === "zh") {
        _this.lang = this.$t("comm_language_title");
        _this.$i18n.locale = langStr;
      } else {
        _this.lang = this.$t("comm_language_title");
        _this.$i18n.locale = langStr;
      }
      localStorage.setItem("locale", langStr);

      _this.$http
        .wisdomPost("/change", { lang: langStr })
        .then((res) => {
          // console.log(res);
          // 加载菜单数据
          _this.queryMenuList();
        })
        .catch((err) => {
          // console.log(err);
          location.reload();
        });
    },
    // 加载所有的菜单信息
    queryMenuList() {
      var _this = this;
      _this.$http.post("/user/changeMenu").then((resJson) => {
        if (resJson && resJson.code == 1) {
          this.changeMenuListResult(resJson.data.funcs || []);
          setTimeout(function () {
            location.reload();
          }, 500);
        } else {
          location.reload();
        }
      });
    },
    getUniqueCode() {
      FingerprintJS.load().then((fp) => {
        fp.get().then((result) => {
          const visitorId = result.visitorId;
          // console.log('获取设备唯一标识：', visitorId);
          this.visitorId = visitorId;
        });
      });
    },
    // 获取系统配置信息
    getSystemInfo() {
      this.$http.post("/lisence/queryLogo").then((res) => {
        if (res.code == 1) {
          if (res.data) {
            this.systemName = res.data.name;
            this.logoImgUrl = res.data.path;
            this.imageBase64 = res.data.image;
            this.helpLang = res.data.defaultLanguage;
            this.$store.commit('m_light/setHelpLang', this.helpLang)
            this.$store.commit('m_light/setSystemInfor', res.data)
            // 英文版本，如果没有界面导航整个图标不显示 0中文 1英文
            if (this.helpLang == 1 && this.navShow == false) {
              this.helpShow = false;
            }
          }
          this.measureTextWidth();
          this.upateIcon();
        }
      });
    },
    // 修改浏览器导航图标。
    upateIcon() {
      let link = document.querySelector('link[rel="icon"]');
      let imageBase64 = `data:image/png;base64,${this.imageBase64}`;
      // link.href = this.imageBase64 ? imageBase64 : require('@/assets/logo-title.png');
      link.href = this.imageBase64 ? imageBase64 : "";
    },
    beforeunloadHandler() {
      this._beforeUnload_time = new Date().getTime();
    },
    unloadHandler(e) {
      this._gap_time = new Date().getTime() - this._beforeUnload_time;
      //判断是窗口关闭还是刷新
      if (this._gap_time <= 5) {
        $.ajax({
          url: "/loginOut",
          type: "post",
          async: false, //或false,是否异步
        });
      }
    },
    // 管理角色内存已满提示
    auditMemory() {
      this.$http.wisdomPost("/auditLogAlarm").then((res) => {
        // code: 10034为需要弹框的，其他的都不弹框
        if (res.code == 10034) {
          // this.$refs.glyAudio.currentTime = 0;
          // this.$refs.glyAudio.muted = false;
          // this.$refs.glyAudio.play(); //播放
          this.$Modal.warning({
            title: this.$t("comm_warn_tip_title"),
            content: res.msg,
          });
          sessionStorage.setItem("isGly", 1);
        }
      });
    }, //获取session是否过期API
    GET_SESSION_OVERTIME() {
      this.$http
        .wisdomPost("/us/invalidate", { label: this.visitorId })
        .then(({ code, data, msg }) => {
          if (data == 0) {
            sessionStorage.clear();
            window.location.href =
              window.location.hostname === "localhost"
                ? "/anpm-plug-login-ui.html"
                : "/";
            clearInterval(this.sessionInterval);
          } else if (code == 1 && data == 1) {
          } else if (code == 1) {
            var refreshMenu = data.refreshMenu || false;
            // 是否刷新菜单
            if (refreshMenu) {
              this.queryMenuList();
            }
          }
        });
    },
    // 处理故障
    handleFault(type) {
      let name = this.$t("alarm_fault_list");
      this.preMenu = this.activeName = name;
      let linkName = this.menuNavs.filter((item) => item.functionName == name);
      let data = linkName[0].sysFunctionList[0];
      if (type == "break") {
        this.tipsModal = false;
      }
      if (type == "bad") {
        this.tipsModal2 = false;
      }
      if (type == "other") {
        this.tipsModal3 = false;
      }
      this.menuBtn2(data, linkName[0].seqNum);
    },

    // 弹窗提示开关
    modalSwitch(val, type) {
      if (type == "break") {
        this.statrList.breakNews = val;
        if (val) {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
          }
        } else {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            clearTimeout(this.getTimelyData);
            this.getTimelyData = null;
            this.maxId = "";
            this.brominId = "";
            this.degminId = "";
          }
        }
      }
      if (type == "bad") {
        this.statrList.badNews = val;
        if (val) {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.otherNews
          ) {
            this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
          }
        } else {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.otherNews
          ) {
            clearTimeout(this.getTimelyData);
            this.getTimelyData = null;
            this.maxId = "";
            this.brominId = "";
            this.degminId = "";
          }
        }
      }
      if (type == "other") {
        this.statrList.otherNews = val;
        if (val) {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews
          ) {
            this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
          }
        } else {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews
          ) {
            clearTimeout(this.getTimelyData);
            this.getTimelyData = null;
            this.maxId = "";
            this.brominId = "";
            this.degminId = "";
          }
        }
      }
      this.setSwitchState(this.statrList);
      
      // 判断是否弹窗提示
      if (!this.statrList.breakNews) {
        setTimeout(() => {
          this.tipsModal = false;
          this.brominId = "";
          this.breakList = [];
        }, 100);
      }

      if (!this.statrList.badNews) {
        setTimeout(() => {
          this.tipsModal2 = false;
          this.degminId = "";
          this.badList = [];
        }, 100);
      }

      if (!this.statrList.otherNews) {
        setTimeout(() => {
          this.tipsModal3 = false;
          this.otherList = [];
        }, 100);
      }
    },
    // 1分钟获取一次数据
    getMinuteData() {
      if (
        this.statrList.breakNews ||
        this.statrList.breakSound ||
        this.statrList.badNews ||
        this.statrList.badSound ||
        this.statrList.otherNews ||
        this.statrList.otherSound
      ) {
        setTimeout(() => {
          if (!this.tipsModal) {
            this.brominId = "";
            this.breakList = [];
          }
          if (!this.tipsModal2) {
            this.degminId = "";
            this.badList = [];
          }
          if (!this.tipsModal3) {
            this.otherList = [];
          }
          let param = {
            id: this.maxId,
            brominId: this.brominId,
            degminId: this.degminId,
          };
          // param.id = 3311;
          // param.brominId = 3311;
          // param.degminId = 3311;
          // console.log(param);
          this.$http.wisdomPost("/fault/getFaultPrompt", param).then((res) => {
            if (res.code == 1) {
              let resData = res.data;
              if (resData.total >= 1) {
                // let totalArr = [...resData.broPro,...resData.degPro,...resData.hintPro]
                // totalArr = totalArr.map(e=>e.id)
                // let repeatId = Array.from(new Set(totalArr));
                // this.maxId = Math.min(...repeatId);
                // 中断数据处理
                if (resData.broPro.length >= 1) {
                  // let broProArr = []
                  // 改变新返回数据id相同的恢复状态

                  this.breakList = resData.broPro;

                  // resData.broPro.filter((item) =>{
                  //   // broProArr.push(item.id);
                  //   this.breakList.filter(e=>{
                  //     if(item.id == e.id){
                  //       if(item.recoveryed == '0'){
                  //         e.recoveryed = '0';
                  //       }else{
                  //         e.recoveryed = '1';
                  //       }
                  //     }
                  //   })
                  // })
                  // let idFrom = Array.from(new Set(broProArr))
                  // this.brominId = Math.min(...idFrom);
                  // 删除新返回的数据与弹窗数据相同id的数据重新赋值resData的数据
                  // resData.broPro = resData.broPro.filter((item) =>{
                  //   let id = this.breakList.map(v=>v.id)
                  //   return !id.includes(item.id)
                  // })
                  // resData.broPro.forEach((item) =>{
                  //   this.breakList.unshift(item)
                  // })

                  // 直接取最小的值
                  let broProAttr = resData.broPro.map((e) => e.id);
                  let broProId = Array.from(new Set(broProAttr));
                  // if(this.brominId != '' && this.brominId  > 0){
                  //    broProId.push(this.brominId);
                  // }
                  this.brominId = Math.min(...broProId);
                }

                // 劣化数据处理
                if (resData.degPro.length >= 1) {
                  this.badList = resData.degPro;

                  // let degProArr = []
                  // resData.degPro.filter((item) =>{
                  //   // degProArr.push(item.id);
                  //   this.badList.filter(e=>{
                  //     if(item.id == e.id){
                  //       if( item.recoveryed == '0'){
                  //         e.recoveryed = '0';
                  //       }else{
                  //         e.recoveryed = '1';
                  //       }
                  //     }
                  //   })
                  // })
                  // let idFrom = Array.from(new Set(degProArr))
                  // this.degminId = Math.min(...idFrom);
                  // resData.degPro = resData.degPro.filter((item) =>{
                  //   let id = this.badList.map(v=>v.id)
                  //    if( item.recoveryed == '1'){
                  //       item.recoveryed = '1';
                  //       }else{
                  //         item.recoveryed = '0';
                  //       }
                  //   return !id.includes(item.id)
                  // })
                  // resData.degPro.forEach(item=>{
                  //   this.badList.unshift(item)
                  // })

                  // this.degminId = this.badList[this.badList.length - 1].id

                  // 直接取最小的值
                  let degProAttr = resData.degPro.map((e) => e.id);
                  let degProId = Array.from(new Set(degProAttr));

                  // if(this.degminId != '' && this.degminId  > 0){
                  //    degProId.push(this.degminId);
                  // }
                  this.degminId = Math.min(...degProId);
                }

                // 提示信息
                if (resData.hintPro.length >= 1) {
                  this.otherList = resData.hintPro;

                  let hintProAttr = resData.hintPro.map((e) => e.id);
                  let hintProId = Array.from(new Set(hintProAttr));
                  // if(this.maxId != '' && this.maxId  > 0){
                  //    hintProId.push(this.maxId);
                  // }
                  // this.maxId = Math.min(...hintProId);
                  // let repeatId = Array.from(new Set(totalArr));
                  // this.maxId = Math.min(...repeatId);

                  //   resData.hintPro.forEach(item=>{

                  //     this.otherList.filter(e=>{
                  //       if(item.id == e.id){
                  //         if( item.recoveryed == '1'){
                  //           e.recoveryed = '1';
                  //         }else{
                  //           e.recoveryed = '0';
                  //         }
                  //       }
                  //     })

                  //   })

                  // resData.hintPro = resData.hintPro.filter((item) =>{
                  //     let id = this.otherList.map(v=>v.id)
                  //      if( item.recoveryed == '1'){
                  //         item.recoveryed = '1';
                  //         }else{
                  //           item.recoveryed = '0';
                  //         }
                  //     return !id.includes(item.id)
                  //   })

                  //   resData.hintPro.forEach(item=>{
                  //     this.otherList.unshift(item)
                  //   })
                }

                if (this.breakList.length > 0 && this.statrList.breakNews) {
                  let listLen = this.breakList.length;
                  if (listLen > 100) {
                    this.breakList.splice(0, 100);
                  }
                  this.tipsModal = true;
                  if (this.statrList.breakClose) {
                    setTimeout(() => {
                      this.tipsModal = false;
                    }, 30000);
                  }
                }
                if (this.badList.length > 0 && this.statrList.badNews) {
                  let listLen = this.badList.length;
                  if (listLen > 100) {
                    this.badList.splice(0, 100);
                  }
                  this.tipsModal2 = true;
                  if (this.statrList.badClose) {
                    setTimeout(() => {
                      this.tipsModal2 = false;
                    }, 30000);
                  }
                }
                if (this.otherList.length > 0 && this.statrList.otherNews) {
                  let listLen = this.otherList.length;
                  if (listLen > 100) {
                    this.otherList.splice(0, 100);
                  }
                  this.tipsModal3 = true;
                  if (this.statrList.otherClose) {
                    setTimeout(() => {
                      this.tipsModal3 = false;
                    }, 30000);
                  }
                }

                if (this.statrList.breakSound && resData.broPro.length > 0) {
                  this.$refs.audio.currentTime = 0;
                  this.$refs.audio.muted = false;
                  this.$refs.audio.play(); //播放
                } else if (
                  this.statrList.badSound &&
                  resData.degPro.length > 0
                ) {
                  this.$refs.audio2.currentTime = 0;
                  this.$refs.audio2.muted = false;
                  this.$refs.audio2.play(); //播放
                } else if (
                  this.statrList.otherSound &&
                  resData.hintPro.length > 0
                ) {
                  this.$refs.audio3.currentTime = 0;
                  this.$refs.audio3.muted = false;
                  this.$refs.audio3.play(); //播放
                }
              }

              // if (resData.length >= 1) {
              //   let newIdArr = []
              //   //合并中断和劣化告警列表
              //   let mergeArr = [...this.breakList,...this.badList];
              //   // 改变新返回数据id相同的恢复状态
              //   resData.filter((item) =>{
              //     newIdArr.push(item.id);
              //     mergeArr.filter(e=>{
              //       if(item.id == e.id && e.recoveryed == '1'){
              //         e.recoveryed = '1';
              //       }
              //     })
              //   })
              //   // 删除新返回的数据与合并数据相同id的数据重新赋值resData的数据
              //   resData = resData.filter((item) =>{
              //     let id = mergeArr.map(v=>v.id)
              //     return !id.includes(item.id)
              //   })
              //   resData.filter(item=>{
              //     if(item.faultType == 1){
              //       this.breakList.unshift(item)
              //     }
              //     if(item.faultType == 2 || item.faultType == 3){
              //       this.badList.unshift(item)
              //     }
              //     if(item.faultType == 7 || item.faultType == 8){
              //       this.otherList.unshift(item)
              //     }
              //   })

              //   if(this.breakList.length > 0 && this.statrList.breakNews){
              //     let listLen = this.breakList.length;
              //     if(listLen > 1000){
              //       this.breakList.splice(1000,listLen)
              //     }
              //     this.tipsModal = true;
              //     if(this.statrList.breakClose){
              //       setTimeout(()=>{
              //         this.tipsModal = false;
              //       },30000)
              //     }
              //   }
              //   if(this.badList.length > 0 && this.statrList.badNews){
              //     let listLen = this.badList.length;
              //     if(listLen > 1000){
              //       this.badList.splice(1000,listLen)
              //     }
              //     this.tipsModal2 = true;
              //     if(this.statrList.badClose){
              //       setTimeout(()=>{
              //         this.tipsModal2 = false;
              //       },30000)
              //     }
              //   }
              //   if(this.otherList.length > 0 && this.statrList.otherNews){
              //     let listLen = this.otherList.length;
              //     if(listLen > 1000){
              //       this.otherList.splice(1000,listLen)
              //     }
              //     this.tipsModal3 = true;
              //     if(this.statrList.otherClose){
              //       setTimeout(()=>{
              //         this.tipsModal3 = false;
              //       },30000)
              //     }
              //   }
              //   let fromIdArr = Array.from(new Set(newIdArr));
              //   this.lastId = Math.max(...fromIdArr);
              //   this.minId = Math.min(...fromIdArr);
              //   if (this.statrList.breakSound) {
              //     this.$refs.audio.currentTime = 0;
              //     this.$refs.audio.muted = false;
              //     this.$refs.audio.play(); //播放
              //   }else if(this.statrList.badSound){
              //     this.$refs.audio.currentTime = 0;
              //     this.$refs.audio.muted = false;
              //     this.$refs.audio.play(); //播放
              //   }else if(this.statrList.otherSound){
              //     this.$refs.audio.currentTime = 0;
              //     this.$refs.audio.muted = false;
              //     this.$refs.audio.play(); //播放
              //   }
              // }
            }
          });
        }, 2000);
      }
    },

    // 获取提示开关状态
    getSwitchState() {
      this.$http.post("/fault/getPrompt").then((res) => {
        if (res.code == 1) {
          if (res.data != null) {
            this.statrList.breakNews =
              res.data.pop_flag_break == 1 ? true : false;
            this.statrList.breakSound =
              res.data.sound_flag_break == 1 ? true : false;
            this.statrList.breakClose =
              res.data.self_closin_break == 1 ? true : false;
            this.statrList.breakKeep =
              res.data.large_screen_hold_break == 1 ? true : false;

            this.statrList.badNews =
              res.data.pop_flag_degradation == 1 ? true : false;
            this.statrList.badSound =
              res.data.sound_flag_degradation == 1 ? true : false;
            this.statrList.badClose =
              res.data.self_closin_degradation == 1 ? true : false;
            this.statrList.badKeep =
              res.data.large_screen_hold_degradation == 1 ? true : false;

            this.statrList.otherNews =
              res.data.pop_flag_else == 1 ? true : false;
            this.statrList.otherSound =
              res.data.sound_flag_else == 1 ? true : false;
            this.statrList.otherClose =
              res.data.self_closin_else == 1 ? true : false;
            this.statrList.otherKeep =
              res.data.large_screen_hold_else == 1 ? true : false;
             
  
            if (
              this.statrList.breakSound ||
              this.statrList.badSound ||
              this.statrList.otherSound
            ) {
              // 浏览器相关安全限制，刷新页面判断是否开启关闭声音
              this.$refs.kaiguan
                .play()
                .then((res) => {
                  this.$refs.kaiguan.muted = true;
                  // console.log('音频正常播放');
                })
                .catch((err) => {
                  this.$Modal.confirm({
                    title: this.$t("message_prompt"),
                    content: this.$t("comm_alarm_sound_tip"),
                    okText: this.$t("comm_alarm_sound_open_btn"),
                    cancelText: this.$t("comm_alarm_sound_close_btn"),
                    onOk: () => {
                      this.soundSwitch(this.statrList.breakSound, "break");
                      this.soundSwitch(this.statrList.badSound, "bad");
                      this.soundSwitch(this.statrList.otherSound, "other");
                      this.$Message.success({
                        content: this.$t("comm_alarm_sound_open_success_tip"),
                        background: true,
                      });
                    },
                    onCancel: () => {
                      this.soundSwitch(false, "break");
                      this.soundSwitch(false, "bad");
                      this.soundSwitch(false, "other");
                      this.$Message.warning({
                        content: this.$t("comm_alarm_sound_close_success_tip"),
                        background: true,
                      });
                    },
                  });
                  // console.log('音频不能播放');
                });
            }

            if (
              this.statrList.breakSound ||
              this.statrList.badSound ||
              this.statrList.otherSound ||
              this.statrList.breakNews ||
              this.statrList.badNews ||
              this.statrList.otherNews
            ) {
              this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
            }
          } else {
            this.statrList.breakNews = true;
            this.statrList.breakSound = true;
            this.statrList.breakClose = false;
            this.statrList.breakKeep = false;

            this.statrList.badNews = true;
            this.statrList.badSound = true;
            this.statrList.badClose = true;
            this.statrList.badKeep = false;

            this.statrList.otherNews = true;
            this.statrList.otherSound = true;
            this.statrList.otherClose = true;
            this.statrList.otherKeep = false;
          }
        }
        // debugger
         this.$store.commit('m_light/setStatrListLight',this.statrList)
      });
    },
    // 设置语音和弹窗开关状态
    setSwitchState(param) {
      let params = {
        popFlagBreak: param.breakNews ? 1 : 0,
        soundFlagBreak: param.breakSound ? 1 : 0,
        selfClosinBreak: param.breakClose ? 1 : 0,
        largeScreenHoldBreak: param.breakKeep ? 1 : 0,

        popFlagDegradation: param.badNews ? 1 : 0,
        soundFlagDegradation: param.badSound ? 1 : 0,
        selfClosinDegradation: param.badClose ? 1 : 0,
        largeScreenHoldDegradation: param.badKeep ? 1 : 0,

        popFlagElse: param.otherNews ? 1 : 0,
        soundFlagElse: param.otherSound ? 1 : 0,
        selfClosinElse: param.otherClose ? 1 : 0,
        largeScreenHoldElse: param.otherKeep ? 1 : 0,
      };

      this.$http.post("/fault/setPrompt", params).then((res) => {
        // console.log(res.msg);
      });
    },

    // 语言提醒开起关闭
    soundSwitch(val, type) {
      if (type == "break") {
        this.statrList.breakSound = val;
        if (val) {
          if (
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
          }
        } else {
          if (
            !this.statrList.badSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            clearTimeout(this.getTimelyData);
            this.getTimelyData = null;
            this.maxId = "";
            this.brominId = "";
            this.degminId = "";
            this.$refs.audio.pause(); //关闭
          }
        }
      }
      if (type == "bad") {
        this.statrList.badSound = val;
        if (val) {
          if (
            !this.statrList.breakSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
          }
        } else {
          if (
            !this.statrList.breakSound &&
            !this.statrList.otherSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            clearTimeout(this.getTimelyData);
            this.getTimelyData = null;
            this.maxId = "";
            this.brominId = "";
            this.degminId = "";
            this.$refs.audio.pause(); //关闭
          }
        }
      }
      if (type == "other") {
        this.statrList.otherSound = val;
        if (val) {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            this.getTimelyData = setInterval(this.getMinuteData, 1000 * 60);
          }
        } else {
          if (
            !this.statrList.breakSound &&
            !this.statrList.badSound &&
            !this.statrList.breakNews &&
            !this.statrList.badNews &&
            !this.statrList.otherNews
          ) {
            clearTimeout(this.getTimelyData);
            this.getTimelyData = null;
            this.maxId = "";
            this.brominId = "";
            this.degminId = "";
            this.$refs.audio.pause(); //关闭
          }
        }
      }
      this.$refs.audio.currentTime = 0; //从头开始播放提示音
      this.setSwitchState(this.statrList);
    },

    proMsg(msg, type) {
      if (type == "warning") {
        this.$Message.warning({ content: msg, background: true });
        return;
      }
      if (type == "success") {
        this.$Message.success({ content: msg, background: true });
        return;
      }
      if (type == "info") {
        this.$Message.info({ content: msg, background: true });
        return;
      }
      if (type == "error") {
        this.$Message.error({ content: msg, background: true });
        return;
      }
      if (type == "loading") {
        this.$Message.loading({ content: msg, background: true });
        return;
      }
    },
    setActive(param) {
      // console.log(sessionStorage.getItem('fn'));
      const menuArry = JSON.parse(sessionStorage.getItem("fn"));
      const currentName = param.parentFunctionName || param.navName;
      const index = menuArry.findIndex((item) => {
        return item.functionName == currentName;
      });
      if (index != -1) {
        this.menuShow(index);
      }

      if (!param.activeId) {
        return;
      }
      this.parentactiveId = param.parentactiveId;
      this.activeId = param.activeId;
      this.preMenu = param.parentFunctionName;
      this.activeName = param.navName;
    },
    getMenuList() {
      let _self = this;
      if(sessionStorage.hasOwnProperty('menu') && sessionStorage.getItem('dark') == 0){ 
        // debugger
        console.log('menu',sessionStorage.getItem('menu'))  
        return
      }
      _self.getMenuListResult([]);
    },
    changeMenuListResult(funListAll) {
      let _self = this;
      
      _self.getMenuListResult(funListAll);

      var accessTokenJson = JSON.parse(sessionStorage.getItem("accessToken"));
      if (accessTokenJson) {
        var funcArray = accessTokenJson.funcs;

        for (let index = 0, size = funListAll.length; index < size; index++) {
          const item = funListAll[index];

          //模块名
          const moduleName = item.routeUrl
            ? item.routeUrl.split("/")[1].toLowerCase()
            : [];
          if (
            item.routeUrl &&
            item.routeUrl != null &&
            item.routeUrl != undefined &&
            moduleName &&
            moduleName != undefined
          ) {
            var moduleNameKey = "btn-" + moduleName,
              btnJsonData = {};

            var funListElement = funcArray.filter((element) => {
              return element.fnId == item.fnId;
            });

            if (funListElement && funListElement.length > 0) {
              btnJsonData = funListElement[0][moduleNameKey];
            }

            if (btnJsonData) {
              item[moduleNameKey] = btnJsonData;
            }
          }
        }
        accessTokenJson.funcs = funListAll;
        sessionStorage.setItem("accessToken", JSON.stringify(accessTokenJson));
      }
    },
    getMenuListResult(funListAll) {
      let _self = this;
      let funList = [];
      if (funListAll && funListAll.length > 0) {
        funList = funListAll;
      } else {
        funList = JSON.parse(sessionStorage.getItem("accessToken")).funcs;
      }
      let newFunList = funList.filter((item) => item.fnLevel < 4);
      console.log(newFunList, "所有的菜单");
      newFunList = newFunList.filter(
        (item) =>
          item.fnName != "采集器版本" &&
          item.fnName != "管理后台" &&
          item.fnName != "Collector Version" &&
          item.fnName != "Management Backend"
      );
      // console.log('nweFunLIst',newFunList)

      /*新菜单处理*/
      this.menuNavs = [];
      if (funList) {
        let menuNavLev1 = [];
        for (let i in newFunList) {
          if (newFunList[i].fnLevel === 1) {
            let item = {};
            item.functionName = newFunList[i].fnName;
            item.parentFunctionId = newFunList[i].parentFnId;
            item.functionFlag = null;
            item.state = 1;
            item.functionLeafNode = null;
            item.functionType = 2;
            item.functionUrl = newFunList[i].routeUrl;
            item.seqNum = i;
            item.functionDesc = null;
            item.parentFunctionName = null;
            item.index = 1;
            item.sysFunctionList = [];
            item.functionIds = null;
            item.icoName = newFunList[i].ico;
            item.functionId = newFunList[i].fnId;
            menuNavLev1.push(item);
          }
        }
        for (let j in menuNavLev1) {
          for (let k in newFunList) {
            if (newFunList[k].parentFnId === menuNavLev1[j].functionId) {
              let items = {};
              items.functionId = newFunList[k].fnId;
              items.functionCode = k;
              items.functionName = newFunList[k].fnName;
              items.parentFunctionId = menuNavLev1[j].functionId;
              items.functionFlag = null;
              items.state = 1;
              items.functionLeafNode = null;
              items.functionType = 2;
              items.functionUrl = newFunList[k].routeUrl;
              items.seqNum = k;
              items.functionDesc = null;
              items.parentFunctionName = menuNavLev1[j].functionName;
              items.index = 2;
              items.sysFunctionList2 = [];
              items.functionIds = newFunList[k].fnId;
              items.icoName = newFunList[k].ico;
              menuNavLev1[j].sysFunctionList.push(items);
            }
          }
        }
        for (let value of menuNavLev1) {
          let level2_menu = value.sysFunctionList;
          if (level2_menu.length > 0) {
            for (let l of level2_menu) {
              for (let k of newFunList) {
                if (k.parentFnId === l.functionId) {
                  let items = {};
                  items.functionId = k.fnId;
                  items.functionCode = k;
                  items.functionName = k.fnName;
                  items.parentFunctionId = l.functionId;
                  items.functionFlag = null;
                  items.state = 1;
                  items.functionLeafNode = null;
                  items.functionType = 2;
                  items.functionUrl = k.routeUrl;
                  items.seqNum = k;
                  items.functionDesc = null;
                  items.parentFunctionName = l.functionName;
                  items.index = 3;
                  items.sysFunctionList2 = [];
                  items.functionIds = k.fnId;
                  items.icoName = k.ico;
                  l.sysFunctionList2.push(items);
                }
              }
            }
            value.sysFunctionList = level2_menu;
          }
        }

        this.menuNavs = menuNavLev1;
        console.log(this.menuNavs, "菜单");

        sessionStorage.setItem("fn", JSON.stringify(menuNavLev1));
        this.$store.commit('m_lightMenu/setMenuList', menuNavLev1)
        if (!sessionStorage.hasOwnProperty("menu")) {
          const firstMenu = this.menuNavs[0];
          if (firstMenu.sysFunctionList.length < 1) {
            this.parentactiveId = firstMenu.functionId;
            this.activeId = null;
            this.preMenu = firstMenu.parentFunctionName;
            this.activeName = firstMenu.functionName;
          } else {
            this.parentactiveId = firstMenu.parentFunctionId;
            this.activeId = firstMenu.sysFunctionList[0].functionId;
            this.preMenu = firstMenu.sysFunctionList[0].parentFunctionName;
            this.activeName = firstMenu.sysFunctionList[0].functionName;
          }
          let navData = {
            navName:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.functionName
                : firstMenu.sysFunctionList[0].functionName,
            functionCode:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.functionCode
                : firstMenu.sysFunctionList[0].functionCode,
            subMenuName:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.seqNum
                : firstMenu.sysFunctionList[0].seqNum,
            functionUrl:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.functionUrl
                : firstMenu.sysFunctionList[0].functionUrl,
            node1:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.seqNum
                : firstMenu.sysFunctionList[0].seqNum,
            parentactiveId:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.functionId
                : firstMenu.sysFunctionList[0].parentFunctionId,
            activeId:
              firstMenu.sysFunctionList.length < 1
                ? null
                : firstMenu.sysFunctionList[0].functionId,
            parentFunctionName:
              firstMenu.sysFunctionList.length < 1
                ? firstMenu.parentFunctionName
                : firstMenu.sysFunctionList[0].parentFunctionName,
          };
          sessionStorage.setItem("menu", JSON.stringify(navData));
          this.$store.commit('m_lightMenu/setMenuItem', navData)
        }
      } else {
        this.$Message.warning({
          content: this.$t("user_no_find_permissions"),
          background: true,
        });
        setTimeout(() => {
          window.location.href =
            window.location.hostname === "localhost"
              ? "/anpm-plug-login-ui.html"
              : "/server/index.html";
        }, 2000);
      }
    },
    menuShow(index, menuLev1) {
      // console.log(e)

      let nodeList = document.querySelectorAll(".ivu-menu-item");
      let currentNode = nodeList[index]; // 当前鼠标悬浮的节点
      const nodeArr = Array.from(nodeList);
      // console.log(nodeArr,'nodeArr')
      // 如果界面导航开启,鼠标移上去只有有提示的二级显示，鼠标移开不消失
      if (this.tipStatus == "true") {
        // 拨测任务
        if (
          menuLev1.functionName == "拨测分析" ||
          menuLev1.functionName == "Probe Task"
        ) {
          if (this.tipActive == 1) {
            // this.tipActive = 2
            // debugger
            if (this.isOver) {
            } else {
              this.changeTipActive(2);
            }
          }
          nodeArr[index].children[0].style.display = "block";
        } else {
        }
      } else {
        // debugger

        // 鼠标悬浮以外的节点
        const otherNode = nodeArr.filter((item, i) => {
          return index != i;
        });

        otherNode.forEach((item) => {
          if (item.children.length > 0) {
            // console.log(item.children,'item.children')
            // console.log(item,'item.....')
            item.children[0].style.display = "none";
          }
        });
        if (currentNode.children.length > 0) {
          currentNode.children[0].style.display = "block";
        }
      }
    },
    menuHide(index) {
      // 总节点
      let nodeList = document.querySelectorAll(".ivu-menu-item");
      const nodeArr = Array.from(nodeList);
      // 激活的节点
      // debugger
      const activeNode = nodeArr.filter((item) => {
        return item.getAttribute("class").includes("active");
      });
      // 未激活的节点
      const otherNode = nodeArr.filter((item) => {
        return !item.getAttribute("class").includes("active");
      });
      // 如果在界面导航 鼠标移出不隐藏否则没法点
      if (this.tipStatus == "true") {
        return;
      }
      // 鼠标移出时，未激活的节点隐藏
      otherNode.forEach((item) => {
        if (item.children.length > 0) {
          item.children[0].style.display = "none";
        }
      });
      // 鼠标移出时，激活的节点需要显示出来
      if (activeNode[0].children.length > 0) {
        activeNode[0].children[0].style.display = "block";
      }
    },
    //个人中心隐藏显示
    headerMenuShow() {
      // 如果界面导航开启不允许操作个人中心修改密码等等
      if (this.tipStatus == "true") {
        return;
      }
      this.headerMenu = true;
    },
    headerMenuHide() {
      this.headerMenu = false;
    },
    modifyPwd() {
      this.showUserPwd = true;
    },
    loginOutbtn() {
      this.loginOutStatus = true;
    },
    loginOutOk() {
      var param = {};
      this.$http
        .post("/loginOut", param)
        .then((res) => {
          if (res.code == 1) {
            // sessionStorage.clear();
            sessionStorage.removeItem("menu");
            sessionStorage.removeItem("accessToken");
            sessionStorage.removeItem("nav-per");
            sessionStorage.removeItem("dark");
            sessionStorage.removeItem("fn");

            // 退出界面导航关闭
            location.href =
              window.location.hostname === "localhost"
                ? "anpm-plug-login-ui.html"
                : "/login/index.html";
          } else {
            // console.log('退出失败.');
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    userCancel() {
      this.$refs["userPwdData"].resetFields();
      this.showUserPwd = false;
    },

    submitUserPwd() {
      this.$refs["userPwdData"].validate((valid) => {
        if (this.userPwdData.showUserPwd.length >= 8) {
          if (valid) {
            let param = {
              passwd: handlePassword.encryption(this.userPwdData.showUserPwd),
              id: this.accessToken.user.id,
              passwdOld: handlePassword.encryption(
                this.userPwdData.oldPassword
              ),
            };
            this.$http
              .post("/user/updateUserPwd", param)
              .then((res) => {
                if (res.code == 1) {
                  this.$Message.success({
                    content: this.$t("comm_success"),
                    background: true,
                  });
                  setTimeout(() => {
                    this.$http
                      .post("/loginOut", {})
                      .then((res) => {
                        if (res.code == 1) {
                          sessionStorage.clear();
                          location.href =
                            window.location.hostname === "localhost"
                              ? "anpm-plug-login-ui.html"
                              : "/login/index.html";
                        } else {
                          // console.log('退出失败.');
                        }
                      })
                      .catch((err) => {
                        // console.log(err);
                      });
                  }, 500);
                  this.showUserPwd = false;
                } else {
                  this.$Message.warning({ content: res.msg, background: true });
                  this.showUserPwd = true;
                }
              })
              .catch((err) => {
                // console.log(err);
              });
          } else {
            this.showUserPwd = true;
          }
        } else {
          this.$Message.warning({
            content: this.$t("server_password_8"),
            background: true,
          });
          this.showUserPwd = true;
        }
      });
    },
    menuBtn(data) {
     
      // debugger
      // document.querySelector('.contentIframe').style.overflow ='auto'
      // 判断如果在界面导航，菜单禁止点击
      if (this.tipStatus == "true") {
        return;
      }
      let _self = this,
        urlNmame = data.functionName;
      if (data.sysFunctionList && data.sysFunctionList.length < 1) {
        this.parentactiveId = data.functionId;
        this.activeId = null;
        this.preMenu = data.parentFunctionName;
        this.activeName = data.functionName;
      }
      if (data.sysFunctionList.length === 0) {
        let navData = {
          navName: data.functionName,
          functionCode: data.functionCode,
          subMenuName: data.seqNum,
          functionUrl: data.functionUrl,
          node1: data.seqNum,
          parentactiveId: data.functionId,
          activeId: null,
          parentFunctionName: data.parentFunctionName,
        };
        sessionStorage.setItem("menu", JSON.stringify(navData));
        this.$store.commit('m_lightMenu/setMenuItem', navData)
        this.removeModalDome();
        let functionUrl =
          data.functionUrl.indexOf("?") > -1
            ? data.functionUrl.split("?")[0]
            : data.functionUrl;
        let src = functionUrl.substring(1, functionUrl.length);
        if (sessionStorage.getItem("currentRoute")) {
          sessionStorage.removeItem("currentRoute");
        }
        this.iframSrc =
          window.location.hostname === "localhost"
            ? "/anpm-plug-" + src + "-ui.html"
            : data.functionUrl;
        document.getElementById("sub-content-page").src = this.iframSrc;
        const iframe2 = document.getElementById("sub-content-page");
        iframeIsLoad(iframe2, function () {
          iframe2.contentWindow.skinChange(window.skin);
        });
      }

       
     
      if(data.functionUrl == '/dashboard') {
        if(!this.memoryTimer) {
          this.startMemoryMonitor()
        }
      }else {
        if(this.memoryTimer) {
          clearInterval(this.memoryTimer);
          this.memoryTimer = null;
        }
        if(sessionStorage.getItem('tabActive')) {
          sessionStorage.removeItem('tabActive')
        }
      }
      },
    // 二级菜单点击逻辑
    menuBtnLv2(data, node1) {
      
   
      let _self = this,
        urlNmame = data.functionName;
      this.parentactiveId = data.parentFunctionId;
      this.activeId = data.functionId;
      this.preMenu = data.parentFunctionName;
      this.activeName = data.functionName;
      if (data.sysFunctionList2.length >= 0) {
        let navData = {
          navName: data.functionName,
          functionCode: data.functionCode,
          subMenuName: data.seqNum,
          functionUrl: data.functionUrl,
          node1: node1,
          parentactiveId: data.parentFunctionId,
          activeId: data.functionId,
          parentFunctionName: data.parentFunctionName,
          index: data.sysFunctionList2.length > 0 ? 3 : 2,
        };
        sessionStorage.setItem("menu", JSON.stringify(navData));
        this.removeModalDome();
        // if (navData.navName === "中继线路监测") {
        //   document.body.style.overflow = "visible";
        //   document.getElementById("specialStyle").style.overflow = "visible";
        // } else {
        //   document.body.style.overflow = "hidden";
        //   document.getElementById("specialStyle").style.overflow = "hidden";
        // }
        let functionUrl =
          data.functionUrl.indexOf("?") > -1
            ? data.functionUrl.split("?")[0]
            : data.functionUrl;
        let src = functionUrl.substring(1, functionUrl.length);
        if (sessionStorage.getItem("currentRoute")) {
          sessionStorage.removeItem("currentRoute");
        }
        this.iframSrc =
          window.location.hostname === "localhost"
            ? "/anpm-plug-" +
              src +
              "-ui.html" +
              (src == "alarmlist" ? "?checkedTab=networkFault" : "")
            : data.functionUrl +
              (src == "alarmlist" ? "?checkedTab=networkFault" : "");
        document.getElementById("sub-content-page").src = this.iframSrc;
        const iframe2 = document.getElementById("sub-content-page");
        iframeIsLoad(iframe2, function () {
          iframe2.contentWindow.skinChange(window.skin);
        });
      }
    },
    menuBtn2(data, node1) {
     
      console.log(data, node1);
      // document.getElementsByClassName('menuItemBox')[index].style.display='block'
      // document.querySelector('.contentIframe').style.overflow ='auto'
      // 界面导航
      // this.tipActive = this.tipActive += 1
      // let id = Number(this.tipActive) + 1
      if (this.tipStatus == "true") {
        if (this.tipActive == "2" && data.functionUrl == "/probetask") {
          this.changeTipActive(3);
        }
        // 界面导航状态二级菜单只有拨测任务管理才能点击
        if (data.functionUrl == "/probetask") {
          // console.log(data.functionCode)
          this.menuBtnLv2(data, node1);
        } else {
        }
      } else {
        this.menuBtnLv2(data, node1);
      }

      // if( this.tipStatus == 'true' && this.tipActive == '2') {
      //      this.changeTipActive(3)
      //     //  3 只能点击拨测任务管理 functionCode '45'
      //     // 二级菜单只能点击拨测任务管理
      //     if(data.functionCode == '45') {
      //          this.menuBtnLv2(data,node1)

      //     }else {

      //     }

      // }else {
      //     this.menuBtnLv2(data,node1)

      // }
     
         if(this.memoryTimer) {
        clearInterval(this.memoryTimer);
        this.memoryTimer = null;
      }
       if(sessionStorage.getItem('tabActive')) {
          sessionStorage.removeItem('tabActive')
        }
    },
    parentSrcSet(param) {
      this.iframSrc = param;
    },
    //清除菜单跳转前 生成的模态框
    removeModalDome() {
      let domeArr = document.getElementsByClassName("v-transfer-dom");
      let msgdomeArr = document.getElementsByClassName("ivu-message");
      for (let i = domeArr.length - 1; i >= 0; i--) {
        //删除模态框
        if (
          domeArr[i] != null &&
          domeArr[i].className.indexOf("serverModal") < 0
        ) {
          // domeArr[i].parentNode.removeChild(domeArr[i]);
          domeArr[i].remove();
        }
      }
      // for (let index = 0 ;index<=msgdomeArr.length-1;index++){//删除消息框
      //   if (msgdomeArr[index]!=null){
      //     msgdomeArr[index].remove();
      //   }
      // }
    },
  },
  directives: {},
  beforeDestory() {
    if(this.memoryTimer) {
        clearInterval(this.memoryTimer);
      }
    clearTimeout(this.getTimelyData);
    this.getTimelyData = null;
    // window.addEventListener('message'
    window.addEventListener("message", function (e) {
      console.log("移除监听");
    });
    if (this.sessionInterval) {
      clearInterval(this.sessionInterval);
      this.sessionInterval = null;
    }
    if (this.getTimelyData) {
      clearInterval(this.getTimelyData);
      this.getTimelyData = null;
    }
  },
};
</script>
<style scoped lang="less">
.menu-title {
  position: relative;
}
.menu-title:hover {
  .tip-text {
    font-weight: 400 !important;
  }
}

.modalIndex {
  z-index: 33333 !important;
}
.modalIndex + div {
  z-index: 33333 !important;
}
.input_span span {
  display: inline-block;
  width: 88px;
  height: 10px;
  background: #eee;
  line-height: 20px;
  position: relative;
}
.input_span label {
  display: inline-block;
  width: 120px;
  text-align: right;
  padding-right: 12px;
}
#one {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-right: 0px solid;
  margin-right: 3px;
  color: red;
}
#one:before {
  content: "弱";
  position: absolute;
  top: 10px;
  left: 36px;
}
#oneUs {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-right: 0px solid;
  margin-right: 3px;
  color: red;
}
#oneUs:before {
  content: "W";
  position: absolute;
  top: 10px;
  left: 36px;
}
#two {
  border-left: 0px solid;
  border-right: 0px solid;
  margin-left: -5px;
  margin-right: 3px;
  color: orange;
}
#two:before {
  content: "中";
  position: absolute;
  top: 10px;
  left: 36px;
}
#twoUs {
  border-left: 0px solid;
  border-right: 0px solid;
  margin-left: -5px;
  margin-right: 3px;
  color: orange;
}
#twoUs:before {
  content: "M";
  position: absolute;
  top: 10px;
  left: 36px;
}
#three {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left: 0px solid;
  margin-left: -5px;
  color: #19be6b;
}
#three:before {
  content: "强";
  position: absolute;
  top: 10px;
  left: 36px;
}

#threeUs {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left: 0px solid;
  margin-left: -5px;
  color: #19be6b;
}
#threeUs:before {
  content: "S";
  position: absolute;
  top: 10px;
  left: 36px;
}

.color1 {
  color: red !important;
  background: red !important;
}

.color2 {
  color: orange !important;
  background: orange !important;
}

.color3 {
  color: #19be6b !important;
  background: #19be6b !important;
}

#font span:nth-child(1) {
  color: red;
  margin-left: 80px;
}

#font span:nth-child(2) {
  color: orange;
  margin: 0 60px;
}

#font span:nth-child(3) {
  color: #19be6b;
}

.pwdShow {
  display: inline-block;
  vertical-align: top;
  height: 32px;
  line-height: 32px;
  cursor: pointer;
  margin-left: 10px;
  font-size: 12px;
}
.layout-skin .ivu-switch {
  margin-left: 5px;
}
</style>
<style scoped lang="less">
// 菜单调整
.menuHeader {
  display: flex;
  height: 90px;
  background-image: url("../../../assets/menu/img-menu.png");
  background-size: 100% 100%;
  z-index: 999;
  .layout-logo {
    width: 300px;
    display: flex;
    align-items: center;
    img {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 10px;
    }
    .logoText {
      display: inline-block;
      color: white;
      font-size: 20px;
      font-weight: bold;
      vertical-align: middle;
      // line-height: 56px;
      cursor: default;
      // white-space: nowrap;
    }
    .logoText-small {
      display: inline-block;
      color: white;
      font-size: 12px;
      font-weight: bold;
      vertical-align: middle;
      // line-height: 56px;
      cursor: default;
    }
  }
  .layout-nav {
    flex: 1;
    .layout-nav-content {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      li {
        // width: 80px;
        // flex: 1;
        width: 90px;
      }
    }
  }
  .layout-nav-box {
    // width: 150px;
    display: flex;
    // align-items: center;
    .layout-skin {
      div {
        height: 45px;
        display: flex;
        align-items: center;
      }
    }
  }
}
.header-right-user {
  color: #ade0eb;
}
/deep/ .ivu-switch-inner {
  color: #ade0eb;
}
/deep/ .ivu-switch:after {
  background-color: #05eeff;
}
.ivu-icon-md-notifications:before {
  color: #05eeff;
}
.layout-nav .layout-nav-content > li {
  position: relative;
}
.layout-nav .layout-nav-content > li .menuItemBox ol {
  list-style-type: none;
  display: flex;
  justify-content: end;
}
.layout-nav .layout-nav-content > li .menuItemBox ol.menuItem > li {
  min-width: 110px;
}
.layout-nav .layout-nav-content > li .menuItemBox {
  background: transparent;
  right: 0;
}
.nav-disabled {
  cursor: not-allowed;
}
.layout-nav .layout-nav-content > li .menuItemBox ol.menuItem > li:hover,
.layout-nav .layout-nav-content > li .menuItemBox ol.menuItem > li.active {
  color: #31f0fe;
  font-weight: bold;
  font-size: 14px;
  border-bottom: 7px solid;
  border-image: url("../../../assets/img-menu-02.png") 6 stretch;
}
.moveR-enter-active,
.moveR-leave-active {
  transition: all 0.3s linear;
  transform: translateX(0);
}
.moveR-enter,
.moveR-leave {
  transform: translateX(100%);
}
.moveR-leave-to {
  transform: translateX(100%);
}
.tipsBox {
  position: fixed;
  z-index: 998;
  right: 10px;
  // top:45px;
  bottom: 5px;
}
@media screen and (max-width: 1600px) {
  .tipsBox {
    .modal-body {
      max-height: 120px;
    }
  }
}
@media screen and (max-width: 1440px) {
  .tipsBox {
    .modal-body {
      max-height: 100px;
    }
  }
}
.tipsModalStyle1 {
  margin-top: 5px;
  width: 450px;
  box-shadow: inset 0px 4px 10px 1px rgba(254, 92, 94, 0.4);
  text-align: left;
  background-color: var(--modal_b_color, #ffffff);
}
.light-tipsModalStyle1 {
  margin-top: 5px;
  width: 450px;
  // box-shadow: inset 0px 4px 10px 1px rgba(254, 92, 94, 0.4);
  box-shadow: inset 0px 1px 1px 1px rgba(254, 92, 94, 0.4);
  text-align: left;
  background-color: var(--modal_b_color, #ffffff);
}

.tipsModalStyle2 {
  margin-top: 5px;
  width: 450px;
  box-shadow: inset 0px 4px 10px 1px rgba(254, 163, 27, 0.4);
  text-align: left;
  background-color: var(--modal_b_color, #ffffff);
}
.light-tipsModalStyle2 {
  margin-top: 5px;
  width: 450px;
  // box-shadow: inset 0px 4px 10px 1px rgba(254, 163, 27, 0.4);
  box-shadow: inset 0px 1px 1px 1px rgba(254, 163, 27, 0.4);
  text-align: left;
  background-color: var(--modal_b_color, #ffffff);
}
.tipsModalStyle3 {
  margin-top: 5px;
  width: 450px;
  box-shadow: inset 0px 4px 10px 1px rgba(5, 212, 255, 0.52);
  text-align: left;
  background-color: var(--modal_b_color, #ffffff);
}
.light-tipsModalStyle3 {
  margin-top: 5px;
  width: 450px;
  // box-shadow: inset 0px 4px 10px 1px rgba(5, 212, 255, 0.52);
  box-shadow: inset 0px 1px 1px 1px rgba(5, 212, 255, 0.52);
  text-align: left;
  background-color: var(--modal_b_color, #ffffff);
}
.modal-backdrop {
  .modal-header {
    background: linear-gradient(272deg, #ff7474 0%, #fe5c5e 100%);
  }
}
.modal-backdrop2 {
  .modal-header {
    background: linear-gradient(272deg, #ffbe5e 0%, #fea31b 100%);
  }
}
.modal-backdrop3 {
  .modal-header {
    background: linear-gradient(272deg, #05eeff 0%, #0290fd 100%);
  }
}

.modal-header {
  color: #fff;
  // border-bottom: 1px solid #e8eaec;
  padding: 8px 15px;
  position: relative;
  h3 {
    font-weight: 500;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
  }
  .ivu-icon {
    position: absolute;
    right: 8px;
    top: 4px;
    overflow: hidden;
    cursor: pointer;
    color: #fff !important;
  }
}
.modal-footer {
  // border-top: 1px solid var(--modal_input_border_color, #dcdee2) !important;
  justify-content: flex-end;
  padding: 8px 15px;
  display: flex;
}
.modal-body {
  position: relative;
  max-height: 140px;
  overflow-y: auto;
  padding: 10px;
  ul {
    li {
      display: flex;
      align-items: flex-start;
      margin: 0 10px 0 10px;
      padding: 0 0 10px 10px;
      // border-left: 3px solid var(--alarm_modal_border_color, #d7d7d7);
      position: relative;
    }
  }
  .tipsModalRight {
    margin-left: 10px;
    // width: calc(100% - 30px);
    width: 100%;
  }
  .alarmSign {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    position: absolute;
    left: -12px;
  }
  .alarmType {
    padding: 0 3px;
    border-radius: 4px;
  }
  .alarmTypeColor1 {
    .alarmType {
      border: 1px solid #b94647;
      color: #fe5c5e;
    }
    .alarmSign {
      background: #fe5c5e;
    }
  }
  .lightalarmTypeColor1 {
    .alarmType {
      // border: 1px solid #b94647;
      color: #fe5c5e;
    }
    .alarmSign {
      background: #fe5c5e;
    }
  }
  .alarmTypeColor2 {
    .alarmType {
      border: 1px solid #fea31b;
      color: #fea31b;
    }
    .alarmSign {
      background: #fea31b;
    }
  }
  .lightalarmTypeColor2 {
    .alarmType {
      // border: 1px solid #fea31b;
      color: #fea31b;
    }
    .alarmSign {
      background: #fea31b;
    }
  }
  .alarmTypeColor3 {
    .alarmType {
      border: 1px solid #028dc3;
      color: #05eeff;
    }
    .alarmSign {
      background: #05eeff;
    }
  }
  .lightalarmTypeColor3 {
    .alarmType {
      // border: 1px solid #028dc3;
      color: #05eeff;
    }
    .alarmSign {
      background: #05eeff;
    }
  }
  .alarmIp {
    font-weight: 600;
    margin-left: 10px;
    // whiteSpace: pre-wrap;
  }
  .alarmTime {
    margin-bottom: 10px;
    .ivu-icon {
      margin-right: 10px;
    }
  }
  .alarmTipsTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }
}
.btnConfirm {
  width: 74px;
  height: 32px;
  // background: linear-gradient(357deg, #049dec 0%, #05ebeb 100%);
  background: linear-gradient(
    357deg,
    var(--btnConfirm_bg1_color, #049dec) 0%,
    var(--btnConfirm_bg2_color, #05ebeb) 100%
  );
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  // color: #042039;
  color: var(--btnConfirm_font_color, #042039);
  // border: none;
  border: 1px solid var(--btnConfirm_border_color, none);
  cursor: pointer;
  // padding: 0 20px;
  // margin-left: 40px;
  // height: 32px;
  // color: #fff;
  // background-color: #2d8cf0;
  // border-radius: 0;
}

.layout-notice {
  float: right;
  height: 50%;
  padding: 0 10px;
  // margin-right: 5px;
  position: relative;
  .ivu-icon {
    line-height: 45px;
    cursor: pointer;
  }
  .switchBox {
    right: 5px;
    // display: none;
    width: 500px;
    table {
      width: 100%;
      text-align: center;
      padding-top: 10px;
      font-size: 14px;
      th {
        padding: 10px 0;
      }
      td {
        padding: 5px 0 10px 0;
      }
    }
    li {
      div {
        padding: 8px;
      }
      span {
        margin-right: 10px;
      }
    }
    li:hover div {
      background-color: #f5f5f5;
    }
  }
}
// .layout-notice:hover .switchBox {
//     display: block;
// }
/deep/ .ivu-switch-checked:after {
  left: 37px;
}
.layout {
  //background: #f4f6f9;
  .ivu-layout {
    //background: #f4f6f9;
  }
  .ivu-layout-header {
    line-height: 25px;
    // background: var(--header_b_color,#1252c8);
    padding: 0 10px;
    .ivu-menu-primary {
      background: transparent;
      height: inherit;
      line-height: 1;
    }
  }
  .ivu-layout-content {
    .linkArea {
      height: 44px;
      text-align: left;
      cursor: default;
      padding: 0 20px;
      .linkBox {
        line-height: 44px;
        color: var(--font_color, #303748);
        .linkTitle {
          color: var(--font_color, #303748);
          cursor: pointer;
        }
      }
    }
    .contentBox {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: visible;
      .contentIframe {
        // height: calc(100% - 90px);
        height: 100%;
        // overflow-y: auto;
      }
    }
  }
}

// .layout-logo {
//   width: 270px;
//   height: inherit;
//   float: left;
//   position: relative;
//   text-align: left;
//   img {
//     width: 32px;
//     height: 32px;
//     vertical-align: middle;
//     margin-right: 10px;
//   }
//   .logoText {
//     display: inline-block;
//     color: white;
//     font-size: 20px;
//     font-weight: bold;
//     vertical-align: middle;
//     line-height: 56px;
//     cursor: default;
//   }
// }

// .layout-nav-box {
//   height: inherit;
//   margin-left: 270px;
// }

.layout-nav {
  height: inherit;
  .layout-nav-content {
    display: inline-block;
    // margin-right: 100px;
    // float: right;
    > li {
      // padding: 0 20px;
      line-height: 45px;
      position: relative;
      color: var(--header_font_color, #ffffff);
      .menuItemBox {
        display: none;
        position: absolute;
        min-width: 100%;
        left: 0;
        // background: var(--header_list_bg_color,#f4f6f9);
        color: var(--header_list_font_color, #303748);
        box-shadow: 0 0 8px var(--header_list_activebg_color, "#7da5e8");
        ol {
          list-style-type: none;
        }
        ol.menuItem > li {
          height: 45px;
          line-height: 45px;
        }
        ol.menuItem > li:hover,
        ol.menuItem > li.active {
          // background: var(--header_list_activebg_color,'#7da5e8');
          color: #31f0fe;
        }
      }
    }
    > li:hover {
      //background: rgba(125, 165, 232, 0.2);
      // background: var(--header_active_bg_color,'rgba(125, 165, 232, 0.16)');
      background-image: url("../../../assets/menu/img-menu-01.png");
      background-size: 100% 100%;
      color: #ffffff;
      .menuItemBox {
        display: block;
      }
    }
    > li.active {
      // background: var(--header_active_bg_color,'rgba(125, 165, 232, 0.16)');
      background-image: url("../../../assets/menu/img-menu-01.png");
      background-size: 100% 100%;
      color: #ffffff;
      .menuItemBox {
        display: block;
      }
    }
  }
}

@media screen and (min-width: 1200px) and (max-width: 1600px) {
  .layout-nav {
    padding-left: 0;
    .layout-nav-content {
      margin-right: 0px;
    }
  }
}
@media screen and (max-width: 1200px) {
  .layout-nav {
    padding-left: 0;
    .layout-nav-content {
      margin-right: 0;
    }
  }
}

.layout-user,
.layout-skin {
  height: 100%;
  margin-right: 0;
  float: right;
}
/deep/.layout-help {
  height: 50%;
  display: flex;
  align-items: center;
  position: relative;
  .ivu-select-dropdown:after {
    content: "";
    position: absolute;
    top: -6px;
    right: 5px;
    display: block;
    border-right: 6px solid transparent;
    border-bottom: 6px solid rgb(37, 49, 66);
    border-left: 6px solid transparent;
  }
  .ivu-select-dropdown {
    margin-top: 13px !important;
    border: unset;
    background-color: var(--body_conent_b_color, #253142) !important;
    .ivu-dropdown-item {
      color: var(--help_font_color, #5ca0d5) !important;
      padding: 7px 26px;
      margin: 7px 16px;
    }
    .ivu-dropdown-item-divided {
      border-top: 1px solid var(--border_color, #060d15);
    }
    .ivu-dropdown-item-divided:before {
      height: unset;
    }

    // #5ca0d5
    .ivu-dropdown-item:hover {
      background-color: var(--help_font_hover_bg_color, #253142) !important;
      color: var(--help_font_hover_color, #05eeff) !important;
    }
  }
}
.layout-footer-center {
  text-align: center;
}

/deep/ .ivu-menu-horizontal .ivu-menu-item {
  padding: 0;
  position: relative;
}
.linkArea {
  background-color: var(--link_b_color, #f4f6f9);
  border-bottom: 1px solid var(--border_color, #dddddd);
}
.header-right-menu {
  background: var(--header_list_bg_color, #f4f6f9);
}
.toolSystem {
  color: var(--header_list_font_color, #303748);
}

/deep/ .dropdown-language-menu {
  background: var(--header_list_bg_color, #f4f6f9);
  color: var(--header_list_font_color, #303748);
  display: block;
  width: 100%;
  padding: 0 0 0 0;
}

/deep/ .dropdown-language-menu a {
  color: var(--header_list_font_color, #303748);
  display: block;
  width: 100%;
}

/deep/ .dropdown-language-menu .ivu-dropdown-rel {
  background: var(--header_list_bg_color, #f4f6f9);
  display: block;
  padding: 0 0 0 0;
}

/deep/ .dropdown-language-menu .ivu-select-dropdown {
  display: block;
  padding: 0 0 0 0;
  top: 53px !important;
}

/deep/ .dropdown-language-menu .ivu-dropdown-rel:hover a {
  background: var(--link_b_color, #2c384a);
}

/deep/ .dropdown-language-menu a:hover {
  background: var(--link_b_color, #2c384a);
}

/deep/ .right-language {
  padding: 0 0 0 0;
}

.header-right-menu li:hover div {
  background: var(--link_b_color, #2c384a);
}
</style>