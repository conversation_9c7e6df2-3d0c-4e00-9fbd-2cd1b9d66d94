<template>
  <section
    class=""
    :style="
      'position: relative; height: 100%;background:' +
      (isbigscreen ? '#060D15' : isdarkSkin == 1 ? '#060D15' : '#060D15')
    "
  >
    <Loading :loading="loading"></Loading>
    <!-- :class="isbigscreen?'bigColor':'defaultColor'" -->
    <p
      class="gisreturnBtn"
      v-show="returnBtn"
      :class="isbigscreen ? '' : 'defaultColor'"
    >
      <Icon type="ios-undo" @click="returnClick" />
    </p>
    <div
      class="gis"
      id="china-map"
      ref="gis"
      style="height: 100%; width: 100%"
    ></div>
    <img
      class="southMap"
      :src="
        this.isdarkSkin == 1
          ? require('../../../assets/img-map.png')
          : require('../../../assets/img-map-light.png')
      "
      alt=""
      v-show="mapShow"
      :style="'width:' + imgWidth + 'px'"
    />
    <!--<div class="gisTable">-->
    <!--<div class="header">-->
    <!--<span>专线名称</span>-->
    <!--<span>平均时延ms</span>-->
    <!--<span>平均丢包率</span>-->
    <!--<span>入流速</span>-->
    <!--<span>入带宽利用率</span>-->
    <!--<span>出流速</span>-->
    <!--<span>出带宽利用率</span>-->
    <!--</div>-->
    <!--<div class="marquee" :style="'height:'+tabHeight+'px'" @mouseover="gisTableStop" @mouseout="gisTableOn">-->
    <!--<div class="marquee_box">-->
    <!--<loading></loading>-->
    <!--<ul class="marquee_list" v-if="marqueeList.length>0" :class="{marquee_top:animate}">-->
    <!--<li-->
    <!--v-for="(item, index) in marqueeList"-->
    <!--:key="index"-->
    <!--:style="{background: item.background,display: 'table', width:'100%'}"-->
    <!--&gt;-->
    <!--<span :title="item.name || '&#45;&#45;'">{{item.name || '&#45;&#45;'}}</span>-->
    <!--<span>{{item.avgDelay || '&#45;&#45;'}}</span>-->
    <!--<span> {{item.avgLoss || '&#45;&#45;'}}</span>-->
    <!--<span>{{item.avgFlowIntoSpeed || '&#45;&#45;'}}</span>-->
    <!--<span> {{item.flowIntoUse || '&#45;&#45;'}}</span>-->
    <!--<span>{{item.avgFlowOutSpeed || '&#45;&#45;'}}</span>-->
    <!--<span>{{item.flowOutUse || '&#45;&#45;'}}</span>-->
    <!--</li>-->
    <!--</ul>-->
    <!--<div v-else style="line-height: 60px">'+$t('common_No_data')+'</div>-->
    <!--</div>-->
    <!--</div>-->
    <!--</div>-->
  </section>
</template>

<script>
  import gisJson from "../assets/gis"
  import Loading from "@/common/loading/loading";
  let echarts = require('echarts');
  import {columnarBlue,columnarYellow,columnarRed,columnarGrey} from '../../../assets/base64Img/img.js'
  export default {
    name: "index",
    components: {
      Loading

    },
    created() {
      this.isdarkSkin = sessionStorage.getItem('dark') || 1;
      const src = window.frames.frameElement.getAttribute('src'),name=window.frames.frameElement.getAttribute('name');

      if (parent.window.isEdit) {
        let params = JSON.parse(unescape(src.split('param=')[1]));
        this.intervalTime = params.intervalTime;
        this.provinceCode = params.provinceCode||100000;
        this.selectedCode = params.provinceCode||100000;
        this.saveData = params;
        // 当前不是全国地图，不显示右下角南海图标
        if (this.selectedCode !=100000) {
          this.mapShow = false
        }
        // console.log('编辑：'+parent.window.dashgis)
        console.log(JSON.parse(unescape(src.split('param=')[1])))
        
        console.log('编辑：'+window.frames.name)
      }else{
        let params = JSON.parse(unescape(src.split('param=')[1]));
        // this.intervalTime = window.frames.name.split('&&topoId=')[0].split('?intervalTime=')[1];
        // this.provinceCode = window.frames.name.split('&&provinceCode=')[1].split('&&timeType=')[0]||100000;
        // this.selectedCode = window.frames.name.split('&&provinceCode=')[1].split('&&timeType=')[0]||100000;
        this.intervalTime = params.intervalTime;
        this.provinceCode = params.provinceCode||100000;
        this.selectedCode = params.provinceCode||100000;
        // 当前不是全国地图，不显示右下角南海图标
        if (this.selectedCode !=100000) {
          this.mapShow = false
        }
        this.saveData = params;
        // console.log('主页面：'+window.frames.name)
        if (window.frames.frameElement) {
          window.frames.frameElement.contentWindow.close();
        }
      }
      window.provinceCode = this.provinceCode;
      this.tabHeight = document.body.clientHeight / 2;
      // this.getProvinceList();
      console.log(this.selectedCode,this.saveData.provinceMunicipalityCode,'11111111111111')
      let str = ''
      if(this.selectedCode === 100000) {
        str = this.selectedCode
      }else {
        str = this.saveData.provinceMunicipalityCode
      }
      this.$http.wisdomPost('/specialIndex/getSpecDataList',{provinceCode:str}).then(({code,data,msg})=>{
        if (code === 1) {
          // data = [{"id":95,"specialNum":null,"linkIds":"500788","name":"","specialAIp":"*********","specialZIp":"*********","specialIp":null,"clientName":null,"devAIp":null,"devZIp":null,"upstreamBandwidth":"","downstreamBandwidth":"","operator":null,"orgName":null,"state":null,"avgDelay":null,"maxDelay":null,"minDelay":null,"avgLoseRate":null,"maxLoseRate":null,"minLoseRate":null,"specialFault":{"specialDataId":null,"brokenTime":null,"degradationTime":null,"lrDegradationTime":null,"totalDegradationTime":null,"brokenCount":null,"degradationCount":null,"loseDeCount":null,"sumDegrCount":null,"cutTime":null,"powerDownTime":null,"busyTime":null,"runTime":null,"dataSource":null,"dataTime":null,"eventStatusSnmp":null,"brokenAvgTime":null,"degradationAvgTime":null,"linkIds":null},"goodRate":null,"useRate":null,"nrGoodRate":null,"nrUseRate":null,"specialZIpName":null,"displayId":"IPing202201280003","snmpRepeatId":null,"dataSource":5,"maxFlowIntoSpeed":null,"avgFlowIntoSpeed":null,"minFlowIntoSpeed":null,"maxFlowOutSpeed":null,"avgFlowOutSpeed":null,"minFlowOutSpeed":null,"devIp":"","interfaceIp":null,"flowIntoUse":null,"flowOutUse":null,"provinceA":null,"cityA":null,"areaA":null,"provinceZ":null,"cityZ":null,"areaZ":null,"aLongLatValue":"116.408-39.904","zLongLatValue":"113.544-22.202","healthyStatus":3},{"id":98,"specialNum":null,"linkIds":"500876","name":"","specialAIp":"*********","specialZIp":"*********","specialIp":null,"clientName":null,"devAIp":null,"devZIp":null,"upstreamBandwidth":"","downstreamBandwidth":"","operator":null,"orgName":null,"state":null,"avgDelay":null,"maxDelay":null,"minDelay":null,"avgLoseRate":null,"maxLoseRate":null,"minLoseRate":null,"specialFault":{"specialDataId":null,"brokenTime":null,"degradationTime":null,"lrDegradationTime":null,"totalDegradationTime":null,"brokenCount":null,"degradationCount":null,"loseDeCount":null,"sumDegrCount":null,"cutTime":null,"powerDownTime":null,"busyTime":null,"runTime":null,"dataSource":null,"dataTime":null,"eventStatusSnmp":null,"brokenAvgTime":null,"degradationAvgTime":null,"linkIds":null},"goodRate":null,"useRate":null,"nrGoodRate":null,"nrUseRate":null,"specialZIpName":null,"displayId":"ITra202201280003","snmpRepeatId":null,"dataSource":5,"maxFlowIntoSpeed":null,"avgFlowIntoSpeed":null,"minFlowIntoSpeed":null,"maxFlowOutSpeed":null,"avgFlowOutSpeed":null,"minFlowOutSpeed":null,"devIp":"","interfaceIp":null,"flowIntoUse":null,"flowOutUse":null,"provinceA":null,"cityA":null,"areaA":null,"provinceZ":null,"cityZ":null,"areaZ":null,"aLongLatValue":"116.408-39.904","zLongLatValue":"104.071-30.67","healthyStatus":3},{"id":99,"specialNum":null,"linkIds":"500866","name":"测试专线","specialAIp":"**********","specialZIp":"**********","specialIp":null,"clientName":null,"devAIp":null,"devZIp":null,"upstreamBandwidth":"1m","downstreamBandwidth":"1m","operator":null,"orgName":null,"state":null,"avgDelay":1.3,"maxDelay":null,"minDelay":null,"avgLoseRate":0.0,"maxLoseRate":null,"minLoseRate":null,"specialFault":{"specialDataId":null,"brokenTime":null,"degradationTime":null,"lrDegradationTime":null,"totalDegradationTime":null,"brokenCount":null,"degradationCount":null,"loseDeCount":null,"sumDegrCount":null,"cutTime":null,"powerDownTime":null,"busyTime":null,"runTime":null,"dataSource":null,"dataTime":null,"eventStatusSnmp":null,"brokenAvgTime":null,"degradationAvgTime":null,"linkIds":null},"goodRate":null,"useRate":null,"nrGoodRate":null,"nrUseRate":null,"specialZIpName":null,"displayId":"","snmpRepeatId":8392,"dataSource":5,"maxFlowIntoSpeed":null,"avgFlowIntoSpeed":null,"minFlowIntoSpeed":null,"maxFlowOutSpeed":null,"avgFlowOutSpeed":null,"minFlowOutSpeed":null,"devIp":"**********","interfaceIp":null,"flowIntoUse":null,"flowOutUse":null,"provinceA":null,"cityA":null,"areaA":null,"provinceZ":null,"cityZ":null,"areaZ":null,"aLongLatValue":"116.408-39.904","zLongLatValue":"112.551-37.893","healthyStatus":0},{"id":100,"specialNum":null,"linkIds":"500880","name":"","specialAIp":"*********","specialZIp":"*********","specialIp":null,"clientName":null,"devAIp":null,"devZIp":null,"upstreamBandwidth":"10M","downstreamBandwidth":"10M","operator":null,"orgName":null,"state":null,"avgDelay":0.5,"maxDelay":null,"minDelay":null,"avgLoseRate":0.0,"maxLoseRate":null,"minLoseRate":null,"specialFault":{"specialDataId":null,"brokenTime":null,"degradationTime":null,"lrDegradationTime":null,"totalDegradationTime":null,"brokenCount":null,"degradationCount":null,"loseDeCount":null,"sumDegrCount":null,"cutTime":null,"powerDownTime":null,"busyTime":null,"runTime":null,"dataSource":null,"dataTime":null,"eventStatusSnmp":null,"brokenAvgTime":null,"degradationAvgTime":null,"linkIds":null},"goodRate":null,"useRate":null,"nrGoodRate":null,"nrUseRate":null,"specialZIpName":null,"displayId":"TTra202201280002","snmpRepeatId":null,"dataSource":5,"maxFlowIntoSpeed":null,"avgFlowIntoSpeed":null,"minFlowIntoSpeed":null,"maxFlowOutSpeed":null,"avgFlowOutSpeed":null,"minFlowOutSpeed":null,"devIp":"","interfaceIp":null,"flowIntoUse":null,"flowOutUse":null,"provinceA":null,"cityA":null,"areaA":null,"provinceZ":null,"cityZ":null,"areaZ":null,"aLongLatValue":"116.408-39.904","zLongLatValue":"126.645-45.758","healthyStatus":0},{"id":106,"specialNum":null,"linkIds":null,"name":null,"specialAIp":"**********","specialZIp":"**********","specialIp":null,"clientName":null,"devAIp":null,"devZIp":null,"upstreamBandwidth":null,"downstreamBandwidth":null,"operator":null,"orgName":null,"state":null,"avgDelay":null,"maxDelay":null,"minDelay":null,"avgLoseRate":null,"maxLoseRate":null,"minLoseRate":null,"specialFault":{"specialDataId":null,"brokenTime":null,"degradationTime":null,"lrDegradationTime":null,"totalDegradationTime":null,"brokenCount":null,"degradationCount":null,"loseDeCount":null,"sumDegrCount":null,"cutTime":null,"powerDownTime":null,"busyTime":null,"runTime":null,"dataSource":null,"dataTime":null,"eventStatusSnmp":null,"brokenAvgTime":null,"degradationAvgTime":null,"linkIds":null},"goodRate":null,"useRate":null,"nrGoodRate":null,"nrUseRate":null,"specialZIpName":null,"displayId":null,"snmpRepeatId":null,"dataSource":null,"maxFlowIntoSpeed":null,"avgFlowIntoSpeed":null,"minFlowIntoSpeed":null,"maxFlowOutSpeed":null,"avgFlowOutSpeed":null,"minFlowOutSpeed":null,"devIp":"","interfaceIp":null,"flowIntoUse":null,"flowOutUse":null,"provinceA":null,"cityA":null,"areaA":null,"provinceZ":null,"cityZ":null,"areaZ":null,"aLongLatValue":"114.498-38.042","zLongLatValue":"118.361-31.341","healthyStatus":3}]

          if (data &&  (data instanceof  Array)) {
            const apiData = data;
            let handleData = [];
            handleData = apiData.map((item)=>{
              let aLongLatArr = item.aLongLatValue && item.aLongLatValue != '' ? item.aLongLatValue.split('-') :null;
              let zLongLatArr = item.zLongLatValue && item.zLongLatValue != '' ? item.zLongLatValue.split('-') :null;
              return {
                id:item.id,
                isSource:null,
                name:item.name||'--',
                aLongLatValue:item.aLongLatValue||'--',
                zLongLatValue:item.zLongLatValue||'--',
                sourceAddress:aLongLatArr ? [aLongLatArr[0], aLongLatArr[1]] :null,
                destAddress:zLongLatArr ? [zLongLatArr[0], zLongLatArr[1]] : null,
                source:item.specialAIp||'--',
                dest:item.specialZIp||'--',
                avgLoss:item.avgLoseRate!=null ? item.avgLoseRate:'--',
                avgDelay:item.avgDelay!=null ? item.avgDelay:'--',
                avgFlowIntoSpeed:item.avgFlowIntoSpeed!=null ? item.avgFlowIntoSpeed:'--',
                avgFlowOutSpeed:item.avgFlowOutSpeed!=null ? item.avgFlowOutSpeed:'--',
                flowIntoUse:item.flowIntoUse!=null ? item.flowIntoUse:'--',
                flowOutUse:item.flowOutUse!=null ? item.flowOutUse:'--',
                faultType:item.healthyStatus==9? 5 : item.healthyStatus,
                matchingType:item.matchingType|| 0
              }
              if (item.aLongLatValue && item.aLongLatValue != '' && item.zLongLatValue && item.zLongLatValue != '') {
              }else{
                console.log('存在无效数据，经纬度缺失！')
              }
            });
            //去除无效数据（如：undefined，null）
            let gisData = handleData.filter((item)=>{
              return item && item.aLongLatValue != item.zLongLatValue
            });
            //设置源数组及 起始点数组
            let sourceArr = [];
            for (let i = 0, len = gisData.length; i < len; i++) {
              sourceArr.push(
                {
                  isSource:true,
                  sourceAddress:gisData[i].sourceAddress,
                  destAddress:gisData[i].sourceAddress,
                  sourceId:gisData[ i ].aLongLatValue,
                  aLongLatValue:gisData[i].aLongLatValue,
                  source:gisData[ i ].source,
                  faultType:gisData[ i ].faultType,
                }
              )
            }
            //去重
            let hash = {};
            let newsourceArr = sourceArr.reduceRight((item, next) => {
              hash[next.sourceId] ? '' : hash[next.sourceId] = true && item.push(next);
              return item
            }, []);
            this.gisdata = gisData.concat(newsourceArr);
            // console.log(this.gisdata,'this.gisdata')/
            this.marqueeList = gisData;
            for (let i =0,len=gisData.length;i<len;i++ ){
              if (i % 2 !== 0) {
                this.marqueeList[i]["background"] ="rgba(4,29,70,.2)";
              }else{
                this.marqueeList[i]["background"] ="rgba(0,0,0,.2)";
              }
            }
          }
        }
        else{
          console.log('最终进入else')
          this.gisdata= [];
        }
      }).catch(error=>{this.gisdata=[];
        if (parent.loading){
          parent.loading['dashgis'] = false;
        }
      })
        .finally(()=>{
          if (parent.loading){
            parent.loading['dashgis'] = false;
          }
        })
    },
    data() {
      return {
        // 多省报错
        registerInfo : {},
        gisDome:null,
        columnarBlue:columnarBlue,
        columnarYellow:columnarYellow,
        columnarRed:columnarRed,
        columnarGrey:columnarGrey,
        mapShow:true,
        //saveData
        saveData:'',
        //是否显示地图切换按钮
        provinceCode:100000,
        //地图切换显示返回按钮
        returnBtn:false,
        //当前选择的code
        selectedCode:100000,
        //刷新时间间隔
        intervalTime:null,
        //刷新参数
        interrefresh:null,
        loading:false,
        //正常，中断,劣化，颜色取值
        pathColor:['#038079','#702A2A','#7E541E','#646474','#702A2A','#7E541E'],// blue ,red,yellow,grey,red,yellow
        // bigpathColor:['#17C7B0','#DB2C2C','#ED921C','#d0d0d0','#f33004','#f79232'],
        bigpathColor:['#07C5A3','#FF9A9A','#FFCB7E','#C0C4CC','#FF9A9A','#FFCB7E'],

        // 飞线颜色
        flyColor:['#00FFEE','#FE5C5C','#FEA31B','#9D9DB1','#FE5C5C','#FEA31B'],
        flyColor2:['#00FFD1','#FE5C5C','#FEA31B','#808695','#FE5C5C','#FEA31B'],

        gisdata:[],
        //轮播列表数据
        marqueeList:[],
        //轮播区域高度
        tabHeight:150,
        animate: false,
        marquee:null,
        isbigscreen:false,
        isdarkSkin:0,
        gisData:[
          {name:'全国',code:100000},
          {name:'北京',code:110000},
          {name:'天津',code:120000},
          {name:'河北',code:130000},
          {name:'山西',code:140000},
          {name:'内蒙古',code:150000},
          {name:'辽宁',code:210000},
          {name:'吉林',code:220000},
          {name:'黑龙江',code:230000},
          {name:'上海',code:310000},
          {name:'江苏',code:320000},
          {name:'浙江',code:330000},
          {name:'安徽',code:340000},
          {name:'福建',code:350000},
          {name:'江西',code:360000},
          {name:'山东',code:370000},
          {name:'河南',code:410000},
          {name:'湖北',code:420000},
          {name:'湖南',code:430000},
          {name:'广东',code:440000},
          {name:'广西',code:450000},
          {name:'海南',code:460000},
          {name:'重庆',code:500000},
          {name:'四川',code:510000},
          {name:'贵州',code:520000},
          {name:'云南',code:530000},
          {name:'西藏',code:540000},
          {name:'陕西',code:610000},
          {name:'甘肃',code:620000},
          {name:'青海',code:630000},
          {name:'宁夏',code:640000},
          {name:'新疆',code:650000},
          {name:'台湾',code:710000},
          {name:'香港',code:810000},
          {name:'澳门',code:820000},
        ]
      }
    },
    computed:{
      imgWidth (){
        let screenW  = document.body.clientWidth
        let rate = screenW/1920
        return 88*rate
      },
     
    },
    watch:{
      gisdata(newValue){
       
        this.$nextTick(()=>{
          if(this.provinceCode === 100000) {
            let address = this.gisData.length === 0 ? this.saveData.name : this.gisData.filter((item)=>item.code==this.selectedCode)[0].name
              this.getFloatingInfo(this.selectedCode,address);
             
          }else {
            console.log(this.selectedCode,this.saveData.provinceMunicipalityCode)
            // debugger
            this.getProvinceInfo()
          }
        
          // this.mapInit()
        })
      }
    },
     mounted() {
        window.thisVm = this;
        window.parent.parent.addEventListener("message", (e) => {
          if (e ) {
            if (e.data.type == 'msg') {
              return;
            }else if(typeof e.data == 'object'){
              // this.isdarkSkin = e.data.isdarkSkin;
              
      
             this.provinceCode === 100000 ? this.getFloatingInfo(this.provinceCode,this.gisData.filter((item)=>item.code==this.provinceCode)[0].name) :   this.getProvinceInfo()
            }else if(typeof e.data == 'number'){
              // this.isdarkSkin = e.data;
   
             this.provinceCode === 100000 ? this.getFloatingInfo(this.provinceCode,this.gisData.filter((item)=>item.code==this.provinceCode)[0].name) :   this.getProvinceInfo()
            }

          }
        });
        this.isbigscreen = window.isbigscreen;
        // this.mapInit();
        //  console.log(this.provinceCode,'6666666666666666666666666')
         if(this.provinceCode === 100000) {
          let address = this.gisData.length === 0 ? this.saveData.name : this.gisData.filter((item)=>item.code==this.selectedCode)[0].name
           this.getFloatingInfo(this.provinceCode,this.gisData.filter((item)=>item.code==this.provinceCode)[0].name);

         }else {
          this.getProvinceInfo()
         }
       

        if (this.intervalTime&& Number(this.intervalTime)) {
          this.interrefresh = setInterval(()=>{this.loading = true;this.getList(this.selectedCode)},this.intervalTime * 1000);
        }
    },
    methods: {
      //获取数据
      getList(code,drawMapType,num){
        console.log(code,22222222)
        this.loading = true;
        this.gisdata= [];
        let str = ''
        if(code === 100001) {
          str = this.saveData.provinceMunicipalityCode

        }else {
          str = code
        }
        let httpRequest = this.$http.wisdomPost('/specialIndex/getSpecDataList',{provinceCode:str})
        httpRequest.then(({code,data,msg})=>{
          if (code === 1) {
            if (data &&  (data instanceof  Array)) {
              const apiData = data;
              let handleData = [];
              handleData = apiData.map((item)=>{
                let aLongLatArr = item.aLongLatValue && item.aLongLatValue != '' ? item.aLongLatValue.split('-') :null;
                let zLongLatArr = item.zLongLatValue && item.zLongLatValue != '' ? item.zLongLatValue.split('-') :null;
                return {
                  id:item.id,
                  isSource:null,
                  name:item.name||'--',
                  aLongLatValue:item.aLongLatValue||'--',
                  zLongLatValue:item.zLongLatValue||'--',
                  sourceAddress:aLongLatArr ? [aLongLatArr[0], aLongLatArr[1]] :null,
                  destAddress:zLongLatArr ? [zLongLatArr[0], zLongLatArr[1]] : null,
                  source:item.specialAIp||'--',
                  dest:item.specialZIp||'--',
                  avgLoss:item.avgLoseRate!=null ? item.avgLoseRate:'--',
                  avgDelay:item.avgDelay!=null ? item.avgDelay:'--',
                  avgFlowIntoSpeed:item.avgFlowIntoSpeed!=null ? item.avgFlowIntoSpeed:'--',
                  avgFlowOutSpeed:item.avgFlowOutSpeed!=null ? item.avgFlowOutSpeed:'--',
                  flowIntoUse:item.flowIntoUse!=null ? item.flowIntoUse:'--',
                  flowOutUse:item.flowOutUse!=null ? item.flowOutUse:'--',
                  faultType:item.healthyStatus==9? 5 : item.healthyStatus,
                  matchingType:item.matchingType|| 0
                }
                if (item.aLongLatValue && item.aLongLatValue != '' && item.zLongLatValue && item.zLongLatValue != '') {
                }else{
                  console.log('存在无效数据，经纬度缺失！')
                }
              });
              //去除无效数据（如：undefined，null）
              let gisData = handleData.filter((item)=>{
                // return item && item.aLongLatValue != item.zLongLatValue
                return item
              });
              //设置源数组及 起始点数组
              let sourceArr = [];
              for (let i = 0, len = gisData.length; i < len; i++) {
                sourceArr.push(
                  {
                    isSource:true,
                    sourceAddress:gisData[i].sourceAddress,
                    destAddress:gisData[i].sourceAddress,
                    sourceId:gisData[ i ].aLongLatValue,
                    aLongLatValue:gisData[i].aLongLatValue,
                    source:gisData[ i ].source,
                    faultType:gisData[ i ].faultType,
                  }
                )
              }
              //去重
              let hash = {};
              let newsourceArr = sourceArr.reduceRight((item, next) => {
                hash[next.sourceId] ? '' : hash[next.sourceId] = true && item.push(next);
                return item
              }, []);
              this.gisdata = gisData.concat(newsourceArr);
              this.marqueeList = gisData;
              for (let i =0,len=gisData.length;i<len;i++ ){
                if (i % 2 !== 0) {
                  this.marqueeList[i]["background"] ="rgba(4,29,70,.2)";
                }else{
                  this.marqueeList[i]["background"] ="rgba(0,0,0,.2)";
                }
              }
            }
          }
          else{
            this.gisdata= [];
          }
        }).catch(error=>{console.log(error);this.gisdata=[];this.loading = false;})
          .finally(()=>{
            this.loading = false;
            if(this.provinceCode === 100000 || num === 1) {
                this.getFloatingInfo(code,drawMapType)
                console.log('进入到了这里')

            }else {
              console.log('进入到这里来了，应该对了')
              this.getProvinceInfo()
            }
          
          })
        httpRequest = null
      },
      //数据分组，根据起点分组，相同起点分在同一组
      groupData(oldArray = []){
     
     
        let resultArray = [];
        for (let item of oldArray) {
          let source = item.source, lcmc = item.lcmc, ldmc = item.ldmc,aLongLatValue = item.aLongLatValue,faultType = item.faultType;
          let idx = resultArray.findIndex(val => {
            return val.aLongLatValue === aLongLatValue
          });
          let resultMap = idx !== -1 ? resultArray[idx] : {};
          if (Object.keys(resultMap).length === 0) {
            let datas = [];
            datas.push(item);
            resultMap['source'] = source;
            resultMap['sourceAdrees'] = aLongLatValue;
            resultMap['aLongLatValue'] = aLongLatValue;
            resultMap['lcmc'] = lcmc;
            resultMap['ldmc'] = ldmc;
            resultMap['datas'] = datas;
            resultMap['faultType'] = faultType;
            resultArray.push(resultMap);
          } else {
            let resultObj = resultArray[idx] || {};
            let array = resultObj['datas'];
            for (let i = 0; i < array.length; i++) {
              if (array[i].zLongLatValue === item.zLongLatValue) {
                item.curveness = 0.3+((array.length>10 ? 0.01 : 0.03)*(array.length+1))
                break
              }
            }
            array.push(item);
          }
        }
        return resultArray;
      },
      //起点，终点坐标
      convertData(data){
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = dataItem.sourceAddress;
          var toCoord = dataItem.destAddress;
          console.log('dataItem.faultType',dataItem.faultType);
          console.log('color',this.isbigscreen ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType]);
          if (fromCoord && toCoord) {
            res.push([{
              coord:fromCoord,
            },{
              coord:toCoord,
              lineStyle:{
                color:this.isdarkSkin==0 ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType],
                curveness:dataItem.curveness
              },
              effect:{
                color:this.isdarkSkin==0 ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType]
              },
              name: dataItem.name,
              itemStyle:{textStyle:{color:'red'}},
              avgLoss:dataItem.avgLoss,
              avgDelay:dataItem.avgDelay,
              avgFlowIntoSpeed:dataItem.avgFlowIntoSpeed,
              avgFlowOutSpeed:dataItem.avgFlowOutSpeed,
              flowIntoUse:dataItem.flowIntoUse,
              flowOutUse:dataItem.flowOutUse,
              source:dataItem.source,
              dest:dataItem.dest,
              faultType:dataItem.faultType,
              matchingType:dataItem.matchingType,
            }]);
          }
        }
        console.log(res,'res.........')
        return res;
      },
      
      flyData(data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var dataItem = data[i];
          var fromCoord = dataItem.sourceAddress;
          var toCoord = dataItem.destAddress;
          console.log('dataItem.faultType',dataItem.faultType);
          console.log('color',this.isbigscreen ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType]);
          if (fromCoord && toCoord) {
            res.push([{
              coord:fromCoord,
            },{
              coord:toCoord,
              lineStyle:{
                color:this.isdarkSkin == 0 ? this.flyColor2[dataItem.faultType] : this.flyColor[dataItem.faultType],
                curveness:dataItem.curveness
              },
              effect:{
                color:this.isdarkSkin == 0? this.flyColor2[dataItem.faultType] : this.flyColor[dataItem.faultType]
              },
              name: dataItem.name,
              itemStyle:{textStyle:{color:'red'}},
              avgLoss:dataItem.avgLoss,
              avgDelay:dataItem.avgDelay,
              avgFlowIntoSpeed:dataItem.avgFlowIntoSpeed,
              avgFlowOutSpeed:dataItem.avgFlowOutSpeed,
              flowIntoUse:dataItem.flowIntoUse,
              flowOutUse:dataItem.flowOutUse,
              source:dataItem.source,
              dest:dataItem.dest,
              faultType:dataItem.faultType,
              matchingType:dataItem.matchingType,
            }]);
          }
        }
        console.log(res,'res.........')
        return res;

      },
      //轮播列表
      showMarquee: function() {
        if (this.marqueeList.length > 5) {
          this.animate = true;
          setTimeout(() => {
            if (this.marqueeList.length % 2 != 0) {
              this.marqueeList[0]["background"] =
                this.marqueeList[0]["background"] === "rgba(4,29,70,.2)"
                  ? "rgba(0,0,0,.2)"
                  : "rgba(4,29,70,.2)";
            }
            this.marqueeList.push(this.marqueeList[0]);
            this.marqueeList.shift();
            this.animate = false;
          }, 500);
        }
      },
      //移入
      gisTableStop(){
        clearInterval(this.marquee);
        this.marquee = null;
      },
      //移除
      gisTableOn(){
        this.marquee = setInterval(this.showMarquee, 2000);
      },

      // 获取提示信息
    getFlowLegendOption(params) {
      console.log('调用了这个方法的。。。。。')
      // matchingType => 0：Z端匹配，1：A端匹配
      // 出流速=>下行流速 , 入流速 => 上行流速
      let specialLine = this.$t('dash_Special_line');
      let specialLineName = this.$t('dash_Special_line_name');
      let averageDelay = this.$t('dash_average_delay');
      let averagePacketLoss = this.$t('dash_average_packet_loss');
      let avgFlowIntoSpeedStr = this.$t('dashboard_upstream');
      let flowIntoUseStr = this.$t('dashboard_uplink');
      let avgFlowOutSpeedStr = this.$t('dashboard_down') ;
      let flowOutUseStr = this.$t('dashboard_downstream');
        return "<div style='text-align: left;'><span>"+specialLine+"："+params.data.source+"-"+params.data.dest+"</span><br>"+
                    "<span>"+specialLineName+"："+params.data.name+"</span><br>"+
                    "<span>"+averageDelay+"："+params.data.avgDelay+'ms'+"</span><br>"+
                    "<span>"+averagePacketLoss+"："+params.data.avgLoss+'%'+"</span><br>"+
                    "<span>"+avgFlowIntoSpeedStr+"："+params.data.avgFlowIntoSpeed+"</span><br>"+
                    "<span>"+flowIntoUseStr+"："+params.data.flowIntoUse+'%'+"</span><br>"+
                    "<span>"+avgFlowOutSpeedStr+"："+params.data.avgFlowOutSpeed+"</span><br>"+
                    "<span>"+flowOutUseStr+"："+params.data.flowOutUse+'%'+"</span></div>"

    },
     getProvinceInfo(){
      // console.log('绘制省地图la.................',this.provinceCode)
      let address = ''
      // console.log('多选省22222222')
      if(this.saveData.provinceMunicipalityCode.indexOf(',') === -1) {
        // 一个省
        this.drawOneMap(address,null,this.saveData.provinceMunicipalityCode)
      }else {
        // 多个省
        this.drawProvnceMap(address)

}
  
      
      
      
     },
      //重写地图实现
      getFloatingInfo(Area,address){  

        this.drawMap(address,null,Area)
     
        
       
      },
      drawProvnceMap(drawMapType) {
         this.gisDome = null
         let str = this.saveData.provinceMunicipalityCode
         let Jsons = str.split(",")
        // console.log('绘制省地图.......',this.provinceCode)
         let Json = gisJson.json_100000
        let arr = []
        Json.features.forEach(item => {
          Jsons.forEach(item2 => {
            if(item.id == item2) {
              arr.push(item)

            }
          })
    }) 
    if (!this.registerInfo[name]) {
      const obj = {
        UTF8Encoding:true,
        type: "FeatureCollection",
        features:arr
      }
      this.registerInfo[name] = obj
    }
    // let obj = {
    //   UTF8Encoding:true,
    //   type: "FeatureCollection",
    //   features:arr

    // }
    let gisData = this.groupData(this.gisdata);
        console.log('gisData',gisData);
        var series = [];
        gisData.forEach((item, i)=> {
          series.push(
            // 飞线
              {
                name: item.source,
                type: 'lines',
                zlevel: 1,
                effect: {
                  show: true,
                  period: 6,
                  trailLength: 0.03,
                  color: 'red',
                  symbolSize: 4
                },
                lineStyle: {
                  normal: {
                    color: '#17C7B0',
                    width: 0,
                    curveness: 0.5
                  }
                },
                data: this.flyData(item.datas)
              },
              // 连线
              {
                name: item.source,
                type: 'lines',
                // effect: {
                //   show: true,
                //   period: 6,
                //   trailLength: 0.2,
                //   symbol: 'arrow',
                //   symbolSize: 3
                // },
                lineStyle: {
                  normal: {
                    color: '#17C7B0',
                    width: 2,
                    opacity: 0.7,
                    curveness: 0.5
                  }
                },
                data: this.convertData(item.datas)
              },
              {
                name: item.source,
                type: 'effectScatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                rippleEffect: {
                  brushType: 'stroke'
                },
                label: {
                  normal: {
                    show: false,
                    position: 'right',
                    formatter: (params)=>{
                    }
                  }
                },
                // symbol: 'image://'+this.columnarBlue,
                symbol:(value, params) => {
                  let faultType  = params.data.faultType
                  let img = ''
                  if (faultType == 0) {
                    img = columnarBlue
                  }else if (faultType == 1|| faultType == 4) {
                    img = columnarRed
                  }else if (faultType == 2 || faultType == 5) {
                    img = columnarYellow
                  }else{
                    img = columnarGrey
                  }
                  return 'image://'+img
                },
                symbolSize: 16,
                itemStyle: {
                  normal: {
                    color: '#17C7B0'
                  }
                },
                data: item.datas.map((dataItem)=> {
                  return {
                    name: dataItem.source,
                    value:dataItem.destAddress,
                    symbolSize:dataItem.isSource ? 16 :12,
                    itemStyle:{color:this.isbigscreen ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType]},
                    avgLoss:dataItem.avgLoseRate,
                    avgDelay:dataItem.avgDelay,
                    source:dataItem.source,
                    dest:dataItem.dest,
                    avgFlowIntoSpeed:dataItem.avgFlowIntoSpeed,
                    avgFlowOutSpeed:dataItem.avgFlowOutSpeed,
                    flowIntoUse:dataItem.flowIntoUse,
                    flowOutUse:dataItem.flowOutUse,
                    faultType:dataItem.faultType,
                    matchingType:dataItem.matchingType,
                  };
                })
              },
              {
                type:'map',
                map:drawMapType,
                geoIndex:0,
                aspectScale:0.75,
                showLegendSymbol:false,
                data:this.gisData,
              }
          );
        });
      
      this.gisDome = document.getElementById('china-map');
       echarts.dispose(this.gisDome);
      let _that = this,echartInit = echarts.init(this.gisDome);
      let option = {
          backgroundColor: this.isbigscreen ? "transparent" : this.isdarkSkin==1 ? "transparent":"white",
          tooltip: {
           backgroundColor: this.isdarkSkin==1 ? 'rgba(18,55,127, 0.82)' : 'rgba(255, 255, 255, 0.9)',
            borderColor:this.isdarkSkin==1 ? 'rgb(18,55,127)' : 'unset',
            borderWidth:this.isdarkSkin==1 ? 1 : 0,
            textStyle:{color: this.isdarkSkin==1 ? 'white' : '#515A6E'},
            extraCssText: this.isdarkSkin == 1
            ? ''
            : 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);', // 添加阴影效果
            formatter:(params)=>{
              console.log(params,'这又是什么东西啊。。。。。')
                            
              if (params.componentSubType === 'lines') {

                 return _that.getFlowLegendOption(params);
                
                // return "<div style='text-align: left;'><span>专线："+params.data.source+"-"+params.data.dest+"</span><br>"+
                //     "<span>专线名称："+params.data.name+"</span><br>"+
                //     "<span>平均时延："+params.data.avgDelay+'ms'+"</span><br>"+
                //     "<span>平均丢包率："+params.data.avgLoss+'%'+"</span><br>"+
                //     "<span>入流速："+params.data.avgFlowIntoSpeed+"</span><br>"+
                //     "<span>入带宽利用率："+params.data.flowIntoUse+'%'+"</span><br>"+
                //     "<span>出流速："+params.data.avgFlowOutSpeed+"</span><br>"+
                //     "<span>出带宽利用率："+params.data.flowOutUse+'%'+"</span></div>"
              }
            }
          }, // 鼠标移到图里面的浮动提示框
          
           geo:{
            show:true,
            map:drawMapType,
            roam: 'move',
            zoom:1.0,
            scaleLimit: {  //控制滚轮缩放大小
              max: 1.3,
              min: 1
            },
            label: {
              normal: {
                show: true, // 是否显示对应地名
                textStyle: {
                  color: this.isbigscreen||this.isdarkSkin==1 ? '#8FD4FF' :'#333333',
                  fontWeight:'normal'
                },
                color: this.isbigscreen||this.isdarkSkin==1 ? '#8FD4FF' :'#333333',
              },
              emphasis: {
                textStyle: {
                  color: this.isdarkSkin==1 ? '#8FD4FF' :'#808695',
                  fontWeight:'normal'
                }
              }
            },
            itemStyle: {
              normal: {
                borderColor: this.isdarkSkin==1 ? '#185481' : '#98C8E5',
                 borderType:'dotted',
                borderWidth: 1,
                areaColor: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [{
                    "offset": 0,
                    "color": this.isdarkSkin==1 ? "#002233" :"#F6FBFF"
                  },
                    {
                      "offset": 1,
                      "color": this.isbigscreen||this.isdarkSkin==1 ? "#002233" :"white"
                    }],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: '#029395',
                shadowOffsetX:this.isbigscreen||this.isdarkSkin==1 ?  -2 : 0,
                shadowOffsetY:this.isbigscreen||this.isdarkSkin==1 ?  2 : 0,
                shadowBlur: this.isbigscreen||this.isdarkSkin==1 ? 6 : 0
              },
              emphasis: {
                borderColor: '#185481',
                borderWidth: 1,
                areaColor: this.isbigscreen||this.isdarkSkin==1 ? {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [{
                    "offset": 0,
                    "color": "#002233"
                  },
                    {
                      "offset": 1,
                      "color": "#002233"
                    }],
                } : "#A9DAFF",
                shadowColor: this.isbigscreen||this.isdarkSkin==1 ? '#029395' :'#D9F4FE',
                shadowOffsetX: -2,
                shadowOffsetY: 2,
                shadowBlur: 6
              }
            },
          },
          series: series
        };
       echarts.registerMap(drawMapType,this.registerInfo[name])
     
     
       echartInit.setOption(option,true);
     
       
    echartInit.on('click',(param)=>{
     console.log(param,'param')
     
          // if (param.data && param.data.code){
          //   console.log('if')
          //   let Area = param.data.code;
          //   let drawMapType = param.name;
          //   this.returnBtn = true;
          //   this.mapShow = false
          //   this.selectedCode = Area;
          //   this.gisdata = [];
          //   this.getList(Area,drawMapType)
          //   // this.getFloatingInfo(Area,drawMapType)
          // }else if (param.name){
            console.log('else')
            let clickRegin = this.gisData.filter(item=>item.name==param.name);
            if (clickRegin && clickRegin.length>0){
              let Area =clickRegin[0].code;
              let drawMapType = param.name;
              this.returnBtn = true;
              this.mapShow = false
              this.selectedCode = Area;
              this.gisdata = [];
              this.getList(Area,drawMapType,1)
              // this.getFloatingInfo(Area,drawMapType)
            }
          // }
        })


      },
      drawOneMap(drawMapType,data,Area) {
         var Json = {};
        Json =  gisJson['json_'+Area];
        console.log(this.gisdata);
        let gisData = this.groupData(this.gisdata);
        console.log('gisData',gisData);
        var series = [];
        gisData.forEach((item, i)=> {
          series.push(
            // 飞线
              {
                name: item.source,
                type: 'lines',
                zlevel: 1,
                effect: {
                  show: true,
                  period: 6,
                  trailLength: 0.20,
                  color: 'red',
                  symbolSize: 3
                },
                lineStyle: {
                  normal: {
                    color: '#17C7B0',
                    width: 0,
                    curveness: 0.5
                  }
                },
                data: this.flyData(item.datas)
              },
              // 底线
              {
                name: item.source,
                type: 'lines',
                // effect: {
                //   show: true,
                //   period: 6,
                //   trailLength: 0.07,
                //   symbol: 'arrow',
                //   symbolSize: 3
                // },
                lineStyle: {
                  normal: {
                    color: '#17C7B0',
                    width: 2,
                    opacity: 0.7,
                    curveness: 0.5
                  }
                },
                data: this.convertData(item.datas)
              },
              // 涟漪散点     
              {
                name: item.source,
                type: 'effectScatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                rippleEffect: {
                  brushType: 'stroke'
                },
                label: {
                  normal: {
                    show: false,
                    position: 'right',
                    formatter: (params)=>{
                    }
                  }
                },
                // symbol: 'image://'+this.columnarBlue,
                symbol:(value, params) => {
                  let faultType  = params.data.faultType
                  let img = ''
                  if (faultType == 0) {
                    img = columnarBlue
                  }else if (faultType == 1|| faultType == 4) {
                    img = columnarRed
                  }else if (faultType == 2 || faultType == 5) {
                    img = columnarYellow
                  }else{
                    img = columnarGrey
                  }
                  return 'image://'+img
                },
                symbolSize: 16,
                itemStyle: {
                  normal: {
                    color: '#17C7B0'
                  }
                },
                data: item.datas.map((dataItem)=> {
                  return {
                    name: dataItem.source,
                    value:dataItem.destAddress,
                    symbolSize:dataItem.isSource ? 16 :12,
                    itemStyle:{color:this.isbigscreen ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType]},
                    avgLoss:dataItem.avgLoseRate,
                    avgDelay:dataItem.avgDelay,
                    source:dataItem.source,
                    dest:dataItem.dest,
                    avgFlowIntoSpeed:dataItem.avgFlowIntoSpeed,
                    avgFlowOutSpeed:dataItem.avgFlowOutSpeed,
                    flowIntoUse:dataItem.flowIntoUse,
                    flowOutUse:dataItem.flowOutUse,
                    faultType:dataItem.faultType,
                    matchingType:dataItem.matchingType,
                  };
                })
              },
              {
                type:'map',
                map:drawMapType,
                geoIndex:0,
                aspectScale:0.75,
                showLegendSymbol:false,
                data:this.gisData,
              }
          );
        });
        this.gisDome = document.getElementById('china-map');
        echarts.dispose(this.gisDome);
        let _that = this,echartInit = echarts.init(this.gisDome);
        let option = {
          backgroundColor: this.isbigscreen ? "transparent" : this.isdarkSkin==1 ? "transparent":"white",
          tooltip: {
           backgroundColor: this.isdarkSkin==1 ? 'rgba(18,55,127, 0.82)' : 'rgba(255, 255, 255, 0.9)',
            borderColor:this.isdarkSkin==1 ? 'rgb(18,55,127)' : 'unset',
            borderWidth:this.isdarkSkin==1 ? 1 : 0,
            textStyle:{color: this.isdarkSkin==1 ? 'white' : '#515A6E'},
            extraCssText: this.isdarkSkin == 1
            ? ''
            : 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);', // 添加阴影效果
            formatter:(params)=>{
                            
              if (params.componentSubType === 'lines') {

                 return _that.getFlowLegendOption(params);
                
                // return "<div style='text-align: left;'><span>专线："+params.data.source+"-"+params.data.dest+"</span><br>"+
                //     "<span>专线名称："+params.data.name+"</span><br>"+
                //     "<span>平均时延："+params.data.avgDelay+'ms'+"</span><br>"+
                //     "<span>平均丢包率："+params.data.avgLoss+'%'+"</span><br>"+
                //     "<span>入流速："+params.data.avgFlowIntoSpeed+"</span><br>"+
                //     "<span>入带宽利用率："+params.data.flowIntoUse+'%'+"</span><br>"+
                //     "<span>出流速："+params.data.avgFlowOutSpeed+"</span><br>"+
                //     "<span>出带宽利用率："+params.data.flowOutUse+'%'+"</span></div>"
              }
            }
          }, // 鼠标移到图里面的浮动提示框
          geo:{
            show:true,
            map:drawMapType,
            roam: 'move',
            zoom:1.0,
            scaleLimit: {  //控制滚轮缩放大小
              max: 1.3,
              min: 1
            },
            label: {
              normal: {
                show: true, // 是否显示对应地名
                textStyle: {
                  color: this.isbigscreen||this.isdarkSkin==1 ? '#8FD4FF' :'#333333',
                  fontWeight:'normal'
                },
                color: this.isbigscreen||this.isdarkSkin==1 ? '#8FD4FF' :'#333333',
              },
              emphasis: {
                textStyle: {
                  color: this.isdarkSkin==1 ? '#8FD4FF' :'#808695',
                  fontWeight:'normal'
                }
              }
            },
            itemStyle: {
              normal: {
                borderColor: this.isdarkSkin==1 ? '#185481' : '#98C8E5',
                 borderType:'dotted',
                borderWidth: 1,
                areaColor: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [{
                    "offset": 0,
                    "color": this.isdarkSkin==1 ? "#002233" :"#F6FBFF"
                  },
                    {
                      "offset": 1,
                      "color": this.isbigscreen||this.isdarkSkin==1 ? "#002233" :"white"
                    }],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: '#029395',
                shadowOffsetX:this.isbigscreen||this.isdarkSkin==1 ?  -2 : 0,
                shadowOffsetY:this.isbigscreen||this.isdarkSkin==1 ?  2 : 0,
                shadowBlur: this.isbigscreen||this.isdarkSkin==1 ? 6 : 0
              },
              emphasis: {
                borderColor: '#185481',
                borderWidth: 1,
                areaColor: this.isbigscreen||this.isdarkSkin==1 ? {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [{
                    "offset": 0,
                    "color": "#002233"
                  },
                    {
                      "offset": 1,
                      "color": "#002233"
                    }],
                } : "#A9DAFF",
                shadowColor: this.isbigscreen||this.isdarkSkin==1 ? '#029395' :'#D9F4FE',
                shadowOffsetX: -2,
                shadowOffsetY: 2,
                shadowBlur: 6
              }
            },
          },
          series: series
        };
        echarts.registerMap(drawMapType,Json);
        echartInit.setOption(option);

      },
    async drawMap(drawMapType,data,Area){ 
      console.log(drawMapType,data,Area)
        var Json = {};
        Json =  gisJson['json_'+Area];
        console.log(this.gisdata);
        let gisData = this.groupData(this.gisdata);
        console.log('gisData',gisData);
        var series = [];
        gisData.forEach((item, i)=> {
          series.push(
            // 飞线
              {
                name: item.source,
                type: 'lines',
                zlevel: 1,
                effect: {
                  show: true,
                  period: 6,
                  trailLength: 0.20,
                  color: 'red',
                  symbolSize: 3
                },
                lineStyle: {
                  normal: {
                    color: '#17C7B0',
                    width: 0,
                    curveness: 0.5
                  }
                },
                data: this.flyData(item.datas)
              },
              // 底线
              {
                name: item.source,
                type: 'lines',
                // effect: {
                //   show: true,
                //   period: 6,
                //   trailLength: 0.07,
                //   symbol: 'arrow',
                //   symbolSize: 3
                // },
                lineStyle: {
                  normal: {
                    color: '#17C7B0',
                    width: 2,
                    opacity: 0.7,
                    curveness: 0.5
                  }
                },
                data: this.convertData(item.datas)
              },
              // 涟漪散点     
              {
                name: item.source,
                type: 'effectScatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                rippleEffect: {
                  brushType: 'stroke'
                },
                label: {
                  normal: {
                    show: false,
                    position: 'right',
                    formatter: (params)=>{
                    }
                  }
                },
                // symbol: 'image://'+this.columnarBlue,
                symbol:(value, params) => {
                  let faultType  = params.data.faultType
                  let img = ''
                  if (faultType == 0) {
                    img = columnarBlue
                  }else if (faultType == 1|| faultType == 4) {
                    img = columnarRed
                  }else if (faultType == 2 || faultType == 5) {
                    img = columnarYellow
                  }else{
                    img = columnarGrey
                  }
                  return 'image://'+img
                },
                symbolSize: 16,
                itemStyle: {
                  normal: {
                    color: '#17C7B0'
                  }
                },
                data: item.datas.map((dataItem)=> {
                  return {
                    name: dataItem.source,
                    value:dataItem.destAddress,
                    symbolSize:dataItem.isSource ? 16 :12,
                    itemStyle:{color:this.isbigscreen ? this.bigpathColor[dataItem.faultType] : this.pathColor[dataItem.faultType]},
                    avgLoss:dataItem.avgLoseRate,
                    avgDelay:dataItem.avgDelay,
                    source:dataItem.source,
                    dest:dataItem.dest,
                    avgFlowIntoSpeed:dataItem.avgFlowIntoSpeed,
                    avgFlowOutSpeed:dataItem.avgFlowOutSpeed,
                    flowIntoUse:dataItem.flowIntoUse,
                    flowOutUse:dataItem.flowOutUse,
                    faultType:dataItem.faultType,
                    matchingType:dataItem.matchingType,
                  };
                })
              },
              {
                type:'map',
                map:drawMapType,
                geoIndex:0,
                aspectScale:0.75,
                showLegendSymbol:false,
                data:this.gisData,
              }
          );
        });
        this.gisDome = document.getElementById('china-map');
        echarts.dispose(this.gisDome);
        let _that = this,echartInit = echarts.init(this.gisDome);
        let option = {
          backgroundColor: this.isbigscreen ? "transparent" : this.isdarkSkin==1 ? "transparent":"white",
          tooltip: {
            backgroundColor: this.isdarkSkin==1 ? 'rgba(18,55,127, 0.82)' : 'rgba(255, 255, 255, 0.9)',
            borderColor:this.isdarkSkin==1 ? 'rgb(18,55,127)' : 'unset',
            borderWidth:this.isdarkSkin==1 ? 1 : 0,
            textStyle:{color: this.isdarkSkin==1 ? 'white' : '#515A6E'},
            extraCssText: this.isdarkSkin == 1
            ? ''
            : 'box-shadow: 0px 0px 8px 1px rgba(0,0,0,0.16);', // 添加阴影效果
            formatter:(params)=>{
                            
              if (params.componentSubType === 'lines') {

                 return _that.getFlowLegendOption(params);
                
                // return "<div style='text-align: left;'><span>专线："+params.data.source+"-"+params.data.dest+"</span><br>"+
                //     "<span>专线名称："+params.data.name+"</span><br>"+
                //     "<span>平均时延："+params.data.avgDelay+'ms'+"</span><br>"+
                //     "<span>平均丢包率："+params.data.avgLoss+'%'+"</span><br>"+
                //     "<span>入流速："+params.data.avgFlowIntoSpeed+"</span><br>"+
                //     "<span>入带宽利用率："+params.data.flowIntoUse+'%'+"</span><br>"+
                //     "<span>出流速："+params.data.avgFlowOutSpeed+"</span><br>"+
                //     "<span>出带宽利用率："+params.data.flowOutUse+'%'+"</span></div>"
              }
            }
          }, // 鼠标移到图里面的浮动提示框
          geo:{
            show:true,
            map:drawMapType,
            roam: 'move',
            zoom:1.0,
            scaleLimit: {  //控制滚轮缩放大小
              max: 1.3,
              min: 1
            },
            label: {
              normal: {
                show: true, // 是否显示对应地名
                textStyle: {
                  color: this.isbigscreen||this.isdarkSkin==1 ? '#8FD4FF' :'#333333',
                  fontWeight:'normal'
                },
                color: this.isbigscreen||this.isdarkSkin==1 ? '#8FD4FF' :'#333333',
              },
              emphasis: {
                textStyle: {
                  color: this.isdarkSkin==1 ? '#8FD4FF' :'#808695',
                  fontWeight:'normal'
                }
              }
            },
            itemStyle: {
              normal: {
                borderColor: this.isdarkSkin==1 ? '#185481' : '#98C8E5',
                 borderType:'dotted',
                borderWidth: 1,
                areaColor: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [{
                    "offset": 0,
                    "color": this.isdarkSkin==1 ? "#002233" :"#F6FBFF"
                  },
                    {
                      "offset": 1,
                      "color": this.isbigscreen||this.isdarkSkin==1 ? "#002233" :"white"
                    }],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: '#029395',
                shadowOffsetX:this.isbigscreen||this.isdarkSkin==1 ?  -2 : 0,
                shadowOffsetY:this.isbigscreen||this.isdarkSkin==1 ?  2 : 0,
                shadowBlur: this.isbigscreen||this.isdarkSkin==1 ? 6 : 0
              },
              emphasis: {
                borderColor: '#185481',
                borderWidth: 1,
                areaColor: this.isbigscreen||this.isdarkSkin==1 ? {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.8,
                  colorStops: [{
                    "offset": 0,
                    "color": "#002233"
                  },
                    {
                      "offset": 1,
                      "color": "#002233"
                    }],
                } : "#A9DAFF",
                shadowColor: this.isbigscreen||this.isdarkSkin==1 ? '#029395' :'#D9F4FE',
                shadowOffsetX: -2,
                shadowOffsetY: 2,
                shadowBlur: 6
              }
            },
          },
          series: series
        };
        echarts.registerMap(drawMapType,Json);
        echartInit.setOption(option);
        echartInit.on('click',(param)=>{
          console.log('点记了')
         
          if (param.data && param.data.code){
            console.log('进入if')
            let Area = param.data.code;
            let drawMapType = param.name;
            this.returnBtn = true;
            this.mapShow = false
            this.selectedCode = Area;
            this.gisdata = [];
            this.getList(Area,drawMapType)
            // this.getFloatingInfo(Area,drawMapType)
          }else if (param.name){
            console.log('进入elif')
            let clickRegin = this.gisData.filter(item=>item.name==param.name);
            if (clickRegin && clickRegin.length>0){
              let Area =clickRegin[0].code;
              let drawMapType = param.name;
              this.returnBtn = true;
              this.mapShow = false
              this.selectedCode = Area;
              this.gisdata = [];
              this.getList(Area,drawMapType)
              // this.getFloatingInfo(Area,drawMapType)
            }
          }
        })
      },
      //重写地图实现end
      //返回全地图
      returnClick(){
        console.log(this.provinceCode,'回到全国状态。。。。。')
        this.returnBtn = false;
        this.provinceCode = this.saveData.provinceCode
        this.selectedCode = this.saveData.provinceCode||100000;
        if(this.provinceCode === 100000) {
          this.mapShow = true
        }
        this.$nextTick(() => {
           this.getList(this.provinceCode)

        })
       
        if(this.provinceCode === 100000) {
          this.mapShow = true
          let address = this.gisData.length === 0 ? this.saveData.name : this.gisData.filter((item)=>item.code==this.selectedCode)[0].name
           this.getFloatingInfo(this.provinceCode,address)

        }else {
          console.log('回到多选省状态。。。。。')
           
          this.getProvinceInfo()
        }
       
      }
    },
    beforeDestroy(){
      console.log("销毁了")
      // 销毁Echarts实例
      window.parent.parent.removeEventListener('message')
    
     echarts.dispose(this.gisDome);
     this.gisDome = null
      if (this.interrefresh) {
        clearInterval(this.interrefresh);
        this.interrefresh = null;
      }
      if (this.marquee) {
        clearInterval(this.marquee);
        this.marquee = null;
      }
    },
  };
</script>

<style scoped>
.gis {
  height: 100%;
}
.gisTable {
  position: absolute;
  left: 20px;
  bottom: 20px;
  color: #8fd4ff;
  text-align: center;
}
.marquee {
  width: 100%;
  box-sizing: border-box;
}
.header {
  display: table;
  width: 100%;
}
.header span {
  display: inline-block;
  line-height: 30px;
}
.marquee_box {
  display: block;
  position: relative;
  width: 100%;
  /* 占四分之三的高度  */
  height: 100%;
  overflow: hidden;
  margin: 0 auto;
}

.marquee_list {
  display: block;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.marquee_top {
  transition: all 0.5s ease-out;
  margin-top: -40px;
}

.marquee_list li {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
}

.marquee_list li span {
  display: inline-block;
  overflow: hidden;
  cursor: default;
  white-space: nowrap;
  word-wrap: break-word;
  text-overflow: ellipsis;
}
.marquee_list li span:first-child,
.header span:first-child {
  width: 80px;
}
.marquee_list li span:nth-child(2),
.header span:nth-child(2) {
  width: 80px;
}
.marquee_list li span:nth-child(3),
.header span:nth-child(3) {
  width: 80px;
}
.marquee_list li span:nth-child(4),
.header span:nth-child(4) {
  width: 80px;
}
.marquee_list li span:nth-child(5),
.header span:nth-child(5) {
  width: 90px;
}
.marquee_list li span:nth-child(6),
.header span:nth-child(6) {
  width: 80px;
}
.marquee_list li span:nth-child(7),
.header span:nth-child(7) {
  width: 90px;
}
.gisreturnBtn.defaultColor {
  /* color: #333333;
  background: #ffffff; */
  background: var(--dash_h_b_color, #f4f6f9);
  color: var(--dash_h_f_color, #303748);
}
.gisreturnBtn.bigColor {
  color: #8fd4ff;
  background: #020933;
}
</style>
<style scoped lang="less">
.gisreturnBtn {
  height: 30px;
  font-size: 24px;
  padding-left: 20px;
  text-align: left;
  color: #d1e4ff;
  i {
    cursor: pointer;
  }
}
.southMap {
  position: absolute;
  right: 25%;
  bottom: 10%;
}
</style>


