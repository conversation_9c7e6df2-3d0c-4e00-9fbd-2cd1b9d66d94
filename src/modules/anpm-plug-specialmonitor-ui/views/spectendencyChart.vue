<template>
  <div :id="tendencyChartId" style="height: 100%"></div>
</template>

<script>
let echarts = require("echarts/lib/echarts");
require("echarts/lib/chart/line");
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/dataZoom");
import global from "@/common/global.js";
import moment from "moment";
import echartFn from "@/common/mixins/echartFun";
export default {
  name: "tendencyChart",
  mixins: [echartFn],
  props: {
    indicatordata: {
      type: Object,
      default: function () {
        return {};
      },
    },
    tendencyChartColor: {
      type: Array,
      default: function () {
        return ["#4e7bff", "#ffa212"];
      },
    },
    isdarkSkin: {
      type: Number,
      default: 0,
    },
    useMaxMin: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      currentSkin: sessionStorage.getItem('dark') || 1,
      // 0：Z端匹配，1：A端匹配
      matchingType: 0,
      tendencyChartId: "",
      tendencyChartType: "",
      //第一次的粒度
      oldLevel: null,
      //echar是否t加载完成
      ecahrtLoaded: false,
      tempW: 0, // 存储底部滑块宽度,
    
    };
  },
  watch: {
    indicatordata: {
      handler(val) {
        // console.log("indicatordata", val);
        this.tendencyChartId = "tendencyChartId" + val.id;
        this.tendencyChartType = val.type;
        this.inintEchart(this.tendencyChartId);
      },
      deep: true,
      immediate: true,
    },
    isdarkSkin: {
      handler(val) {
        this.inintEchart(this.tendencyChartId);
      },
      deep: true,
      // immediate:true
    },
  },
  created() {
    // 不能删除
    // this.matchingType = this.indicatordata.matchingType;
    // let delayLossUnitTemp =  {};
    // // 时延
    // delayLossUnitTemp[this.$t("comm_time_delay")] =  "ms";
    // // 丢包率
    // delayLossUnitTemp[this.$t('comm_packet_loss')] =  "%";
    // // 入流速
    // delayLossUnitTemp[this.$t("dashboard_upstream")] =  "bps";
    // // 出流速
    // delayLossUnitTemp[this.$t("dashboard_down")] =  "bps";
    // // 上行带宽利用率
    // delayLossUnitTemp[this.$t("dashboard_uplink")] =  "%";
    // // 下行带宽利用率
    // delayLossUnitTemp[this.$t("dashboard_downstream")] = "%";
    // // 可用率（免责）
    // delayLossUnitTemp[this.$t('specquality_availability_exemption')] =  "%";
    // // 优良率（免责）
    // delayLossUnitTemp[this.$t('specquality_good_rate_exemption')] =  "%";
    // // 可用率
    // delayLossUnitTemp[this.$t('specquality_availability')] =  "%";
    // // 优良率
    // delayLossUnitTemp[this.$t('specquality_good_rate')] =  "%";
    // this.delayLossUnit = Object.assign(this.delayLossUnit,delayLossUnitTemp);
  },
  computed: {
    indicatorArr() {
      let flowArray = this.getFlowLegendOption();
      return {
        1: {
          // 时延
          arr: [this.$t("comm_time_delay"), this.$t("comm_packet_loss")],
          unit: ["ms", "%"],
          yaxis: [
            {
              name: this.$t("comm_time_delay") + "：（ms）",
              id: 1,
              nameLocation: "end",
              nameTextStyle: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
             min:this.handleYspectendency(this.indicatordata.delay).minNum,
              max:this.handleYspectendency(this.indicatordata.delay).maxNum,
              type: "value",
              scale: true,
              axisLine: { show: false },
              splitLine: {
                show: this.indicatordata.time.length < 1 ? false : true,

                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
              axisTick: { show: false },
              axisLabel: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
            },
            // 丢包率
            {
              name: this.$t("comm_packet_loss") + "：（%）",
              id: 2,
              nameLocation: "end",
              nameTextStyle: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
              },
              max: 100,
              min: 0,
              type: "value",
              scale: true,
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: false,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: this.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
                },
              },
              axisLabel: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#e3e7f2",
              },
            },
          ],
          series: [
            // 时延
            {
              name: this.$t("comm_time_delay"),
              type: "line",
              symbol: "circle",
              symbolSize: 1,
              smooth: true,
              showAllSymbol: true,
              z: 1,
              yAxisIndex: 0,

              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              color: "#00FFEE",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(0, 255, 238,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },

              data: this.indicatordata.delay,
            },
            {
              name: this.$t("comm_packet_loss"),
              type: "line",
              z: 2,
              yAxisIndex: 1,
              smooth: true,
              symbol: "circle",
              showAllSymbol: true,
              symbolSize: 1,
              color: "#0290FD",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(2, 144, 253,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.loss,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
          ],
        },
        // 上行下行
        2: {
          // arr:[this.$t('dashboard_upstream'),this.$t('dashboard_down'),this.$t('dashboard_uplink'),this.$t('dashboard_downstream')],
          arr: flowArray,
          unit: ["bps", "bps", "%", "%"],
          yaxis: [
            {
              name: this.$t("server_flow_rate") + "：（bps）",
              id: 3,
              scale: true,
              nameLocation: "end",
              nameTextStyle: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
              type: "value",
              axisLine: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
              axisTick: { show: false },
              axisLabel: {
                show: true,
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                formatter: (value) => {
                  return this.getUnit(value, true, true);
                },
              },
            },
            {
              name: this.$t("specquality_utilization"),
              id: 4,
              nameLocation: "end",
              max:this.useMaxMin.max,
              min:this.useMaxMin.min,
              nameTextStyle: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
              type: "value",
              scale: true,
              axisLine: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
              axisTick: { show: false },
              axisLabel: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
            },
          ],
          series: [
            {
              // name: this.$t('specquality_incoming_velocity'),
              name: flowArray[0],
              type: "line",
              symbol: "circle",
              symbolSize: 1,
              smooth: true,
              showAllSymbol: true,
              z: 1,
              yAxisIndex: 0,
              color: "#81C8FF",
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(129, 200, 255,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(129, 200, 255, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.inflow,
            },
            {
              // name: this.$t('specquality_exit_velocity'),
              name: flowArray[1],
              type: "line",
              z: 2,
              yAxisIndex: 0,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              showAllSymbol: true,
              color: "#9BFBAE",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(155, 251, 174,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(155, 251, 174, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.outflow,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
            {
              // name: "入利用率",
              name: flowArray[2],
              type: "line",
              z: 2,
              yAxisIndex: 1,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              showAllSymbol: true,
              color: "#0290FD",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(2, 144, 253,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },

              data: this.indicatordata.inuse,

              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
            {
              // name: "出利用率",
              name: flowArray[3],
              type: "line",
              z: 2,
              yAxisIndex: 1,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              showAllSymbol: true,
              color: "#00FFEE",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(0, 255, 238,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },

              data: this.indicatordata.outuse,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
          ],
        },
        3: {
          arr: [
            this.$t("specquality_availability"),
            this.$t("specquality_good_rate"),
            this.$t("specquality_availability_exemption"),
            this.$t("specquality_good_rate_exemption"),
          ],
          unit: ["%", "%", "%", "%"],
          yaxis: [
            {
              name: this.$t("specquality_availability") + "：（%）",
              id: 1,
              nameLocation: "end",
              nameTextStyle: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
              type: "value",
              scale: true,
              axisLine: { show: false },
              splitLine: {
                show: this.indicatordata.time.length < 1 ? false : true,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
              max: 100,
              min: 0,
              axisTick: { show: false },
              axisLabel: { color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82" },
            },
            {
              name: this.$t("dashboard_excellent_rate") + "：（%）",
              id: 2,
              nameLocation: "end",
              nameTextStyle: {
                color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
              },
              type: "value",
              scale: true,
              axisLine: { show: false },
              splitLine: {
                show: this.indicatordata.time.length < 1 ? false : true,
                lineStyle: {
                  type: "dashed",
                  width: 0.5,
                  color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82",
                },
              },
              max: 100,
              min: 0,
              axisTick: { show: false },
              axisLabel: { color: this.isdarkSkin == 1 ? "#5CA0D5" : "#676f82" },
            },
          ],
          series: [
            // 可用率

            {
              name: this.$t("specquality_availability"),
              type: "line",
              symbol: "circle",
              symbolSize: 1,
              smooth: true,
              showAllSymbol: true,
              z: 1,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
              color: "#0290FD",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(2, 144, 253,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(2, 144, 253, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.useable,
            },
            // 优良率
            {
              name: this.$t("specquality_good_rate"),
              type: "line",
              z: 2,
              yAxisIndex: 0,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              showAllSymbol: true,
              color: "#00FFEE",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(0, 255, 238,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(0, 255, 238, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.goodrate,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
            // 可用率（免责）
            {
              name: this.$t("specquality_availability_exemption"),
              type: "line",
              z: 2,
              yAxisIndex: 1,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              showAllSymbol: true,
              color: "#81C8FF",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(129, 200, 255,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(129, 200, 255, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.useableexemption,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
            // 优良率（免责）
            {
              name: this.$t("specquality_good_rate_exemption"),
              type: "line",
              z: 2,
              yAxisIndex: 1,
              smooth: true,
              symbol: "circle",
              symbolSize: 1,
              showAllSymbol: true,
              color: "#9BFBAE",
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgb(155, 251, 174,0.6)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(155, 251, 174, 0.2)",
                    },
                  ],
                },
              },
              data: this.indicatordata.goodrateexemption,
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1,
                  },
                },
              },
            },
          ],
        },
      };
    },
  },
  mounted() {
    this.inintEchart(this.tendencyChartId);
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
  // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
    // 获取流速信息
    getFlowLegendOption() {
      let flowLegendArray = [
        this.$t("dashboard_upstream"),
        this.$t("dashboard_down"),
        this.$t("dashboard_uplink"),
        this.$t("dashboard_downstream"),
      ];
      // matchingType => 0：Z端匹配，1：A端匹配
      // if(this.matchingType == 0){
      //   // 出流速=>上行流速 , 入流速 => 下行流速
      //   flowLegendArray = [this.$t('dashboard_down'), this.$t('dashboard_upstream'), this.$t('dashboard_downstream'), this.$t('dashboard_uplink')];
      // }else if(this.matchingType == 1){
      //   // 出流速=>下行流速 , 入流速 => 上行流速
      //   flowLegendArray = [this.$t('dashboard_upstream'), this.$t('dashboard_down'), this.$t('dashboard_uplink'), this.$t('dashboard_downstream')];
      // }
      return flowLegendArray;
    },
    inintEchart(id) {
      this.myChart1 = echarts.init(document.getElementById(id));
      console.log(this.myChart1);
      this.myChart1.setOption({
        color: this.tendencyChartColor,
        title: {
          show: this.indicatordata.time.length < 1,
          text: this.$t("alarm_no_data"),
          top: "46%",
          left: "center",
          textStyle: {
            color: this.isdarkSkin == 1 ? "#617ca5" : "#333",
            fontFamily: "serif",
            fontWeigth: "400",
            fontSize: 18,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
            snap: true,
            shadowStyle: {
              color: this.isdarkSkin == 1 ? "rgba(0,0,0,1)" : "rgba(0,0,0,1)",
              width: 30,
            },
            lineStyle: {},
          },
          borderWidth: 1,
          borderColor: this.isdarkSkin == 1 ? "rgba(18,55,127, 0.82)" : "#DCDFE6",
          backgroundColor:
            this.isdarkSkin == 1 ? "rgba(18,55,127, 0.82)" : "rgba(255, 255, 255, 0.82)",
          padding: [10, 18],
          textStyle: {
            align: "left",
            color: this.isdarkSkin == 1 ? "#617ca5" : "#515A6E",
            fontFamily: "MicrosoftYaHei-Bold",
          },
          formatter: (param) => {
            // console.log(param,'什么东西')
            let unit = this.indicatorArr[this.tendencyChartType].unit;
            let divDome = "<div style=line-height:16px>";
            divDome += "<span>" + param[0].axisValue + "</span><br/>";
            param.forEach((element, index) => {
              if (this.tendencyChartType != 2) {
                if (element.seriesName == this.$t("comm_packet_loss")) {
                  divDome +=
                    "<span class=tooltips><span style=width:12px;height:12px;display:inline-block;border-radius:100%;margin-right:3px;vertical-align:middle;background:" +
                    element.color +
                    "></span>" +
                    element.seriesName +
                    ":" +
                    (element.value >= 0 ? element.value + "%" : "--") +
                    "</span><br/>";
                } else {
                  divDome +=
                    "<span class=tooltips><span style=width:12px;height:12px;display:inline-block;border-radius:100%;margin-right:3px;vertical-align:middle;background:" +
                    element.color +
                    "></span>" +
                    element.seriesName +
                    ":" +
                    (element.value >= 0 ? element.value + unit[index] : "--") +
                    "</span><br/>";
                }
              } else {
                if (index < 2) {
                  divDome +=
                    "<span><span style=width:12px;height:12px;display:inline-block;border-radius:100%;margin-right:3px;vertical-align:middle;background:" +
                    element.color +
                    "></span>" +
                    element.seriesName +
                    ":" +
                    this.flowSize(element.value) +
                    "</span><br/>";
                } else {
                  divDome +=
                    "<span><span style=width:12px;height:12px;display:inline-block;border-radius:100%;margin-right:3px;vertical-align:middle;background:" +
                    element.color +
                    "></span>" +
                    element.seriesName +
                    ":" +
                    (element.value >= 0 ? element.value + unit[index] : "--") +
                    "</span><br/>";
                }
              }
            });
            divDome += "</div>";
            return divDome;
          },
        },
        legend: {
          top: "10%",
          show: false,
          right: "center",
          icon: "rect",
          itemHeight: 15,
          textStyle: {
            color: this.isdarkSkin == 1 ? "#617ca5" : "#484b56",
            fontSize: 14,
            fontFamily: "MicrosoftYaHei-Bold",
          },
          data: this.indicatorArr[this.tendencyChartType].arr,
        },
        grid: {
          top: "25%",
          left: "5%",
          right: "5%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: true,
              lineStyle: {
                type: "dotted",
                color: this.currentSkin == 1 ? "#5CA0D5" : "#808695",
                width: 1,
              },
            },
            axisLabel: {},
            data: this.indicatordata.time,
          },
        ],
        yAxis: this.indicatorArr[this.tendencyChartType].yaxis,
        dataZoom: [
          {
            type: "inside",
            xAxisIndex: [0],
          },
          {
            type: "slider",
            left: "5%",
            right: "5%",
            bottom: "4%",
            height: "15",
            xAxisIndex: [0],
            realtime: true,
            fillerColor: this.currentSkin == 1 ? "rgba(2, 29, 54, 1)": "rgba(228, 231, 237, 1)",
            borderColor: this.currentSkin == 1 ? "rgba(22, 67, 107, 1)":"rgba(228, 231, 237, 1)",
            handleStyle: {
              color: this.currentSkin == 1 ? "rgba(2, 67, 107, 1)":"#C0C4CC",
            },
            textStyle: {
              color: this.currentSkin == 1 ? "#617ca5":"#808695",
            },
          },
        ],
        series: this.indicatorArr[this.tendencyChartType].series,
      });
      this.ecahrtLoaded = true;
      this.myChart1.getZr().on("mousewheel", (params) => {
        if (this.ecahrtLoaded) {
          const wheel = params.wheelDelta;

          let start = this.myChart1.getModel().option.dataZoom[0].start;
          let end = this.myChart1.getModel().option.dataZoom[0].end;
          let doomW = end - start; // 底部滑块宽度
          const { id, type, dataSource, level, time } = this.indicatordata;
          if (type == 3) {
            //可用率优良率直接返回,无需切换粒度，无论放大还是缩小
            return;
          }
          if (wheel < 0 && doomW > this.tempW) {
            //缩小
            if (level == 3) {
              //时延  高频粒度为秒时无需切换/普通任务粒度为分钟时无需切换
              return;
            }
            //获取保存的参数
            let idSession = JSON.parse(sessionStorage.getItem("tendencyChart"));
            const sessionItem = idSession
              ? idSession.filter((item) => {
                  return item.id == id;
                })[0]
              : {};
            const oldLevel = sessionItem.oldLevel;
            if (
              type == 1 &&
              ((dataSource == 4 && oldLevel == 1) || (dataSource != 4 && oldLevel == 2))
            ) {
              return;
            } else if (type == 2 && oldLevel == 2) {
              return;
            }
            //设置新的请求参数
            let newParams = {
              specialIds: id,
              type: type,
              startTime: "",
              endTime: "",
              pageNo: 1,
              pageSize: 1,
              level: null,
            };

            if (type == 2 && oldLevel == 3) {
              if (level == 2) {
                newParams.startTime = sessionItem["1"].startTime;
                newParams.endTime = sessionItem["1"].endTime;
                newParams.level = sessionItem["1"].level;
                this.ecahrtLoaded = false;
                this.myChart1.showLoading({
                  text: this.$t("specialmonitor_loading"),
                  color: "#57a3f3",
                  textColor: "#fff",
                  maskColor: "rgba(18,55,127,.5)",
                });
                this.$emit("whellScale", newParams);
              }
            }
            if (type == 1) {
              if (dataSource == 4) {
                if (oldLevel == 3) {
                  if (level == 1) {
                    newParams.startTime = sessionItem["2"].startTime;
                    newParams.endTime = sessionItem["2"].endTime;
                    newParams.level = sessionItem["2"].level;
                    this.ecahrtLoaded = false;
                    this.myChart1.showLoading({
                      text: this.$t("specialmonitor_loading"),
                      color: "#57a3f3",
                      textColor: "#fff",
                      maskColor: "rgba(18,55,127,.5)",
                    });
                    this.$emit("whellScale", newParams);
                  } else if (level == 2) {
                    newParams.startTime = sessionItem["1"].startTime;
                    newParams.endTime = sessionItem["1"].endTime;
                    newParams.level = sessionItem["1"].level;
                    this.ecahrtLoaded = false;
                    this.myChart1.showLoading({
                      text: this.$t("specialmonitor_loading"),
                      color: "#57a3f3",
                      textColor: "#fff",
                      maskColor: "rgba(18,55,127,.5)",
                    });
                    this.$emit("whellScale", newParams);
                  }
                } else if (oldLevel == 2) {
                  if (level == 1) {
                    newParams.startTime = idSession["1"].startTime;
                    newParams.endTime = idSession["1"].endTime;
                    newParams.level = idSession["1"].level;
                    this.ecahrtLoaded = false;
                    this.myChart1.showLoading({
                      text: this.$t("specialmonitor_loading"),
                      color: "#57a3f3",
                      textColor: "#fff",
                      maskColor: "rgba(18,55,127,.5)",
                    });
                    this.$emit("whellScale", newParams);
                  }
                }
              } else {
                if (oldLevel == 3 && level == 2) {
                  newParams.startTime = sessionItem["1"].startTime;
                  newParams.endTime = sessionItem["1"].endTime;
                  newParams.level = sessionItem["1"].level;
                  this.ecahrtLoaded = false;
                  this.myChart1.showLoading({
                    text: this.$t("specialmonitor_loading"),
                    color: "#57a3f3",
                    textColor: "#fff",
                    maskColor: "rgba(18,55,127,.5)",
                  });
                  this.$emit("whellScale", newParams);
                }
              }
            }
          } else {
            //放大

            if (
              type == 1 &&
              ((dataSource == 4 && level == 1) || (dataSource != 4 && level == 2))
            ) {
              //时延  高频粒度为秒时无需切换/普通任务粒度为分钟时无需切换
              return;
            }
            if (type == 2 && level == 2) {
              //流速  粒度为分钟时无需切换,无论高频/普通任务
              return;
            }
            this.tempW = doomW; // 记录开始放大时，底部滑块的宽度
            //获取当前滚动后的开始结束时间下标
            var startValue = this.myChart1.getModel().option.dataZoom[0].startValue;
            var endValue = this.myChart1.getModel().option.dataZoom[0].endValue;
            let startTime = time[startValue],
              endTime = time[endValue];
            //计算前后时间差
            let diffTime = new Date(endTime).getTime() - new Date(startTime).getTime();
            //level变化的时间规则
            const level1 = 10800000,
              level2 = 604800 * 1000;
            //根据时间差  和当前level 设置新的level和时间
            let newLevel = level,
              newStartTime = startTime,
              newEndTime = endTime;
            //设置新的请求参数
            let newParams = {
              specialIds: id,
              type: type,
              startTime: newStartTime,
              endTime: newEndTime,
              pageNo: 1,
              pageSize: 1,
              level: level,
            };
            //设置session
            console.log(this.indicatordata.scaleLevel);
            if (this.indicatordata.scaleLevel == 0) {
              let idSession = JSON.parse(sessionStorage.getItem("tendencyChart"));
              console.log(idSession);
              if (idSession) {
                const isFind = idSession.findIndex((item) => {
                  return item.id == id;
                });
                console.log(isFind);

                if (isFind > -1) {
                } else {
                  idSession.push({
                    id: id,
                    oldLevel: this.indicatordata.level,
                    1: {
                      level: null,
                      startTime: this.indicatordata.startTime,
                      endTime: this.indicatordata.endTime,
                    },
                  });
                  sessionStorage.setItem("tendencyChart", JSON.stringify(idSession));
                }
              } else {
                sessionStorage.setItem(
                  "tendencyChart",
                  JSON.stringify([
                    {
                      id: id,
                      oldLevel: this.indicatordata.level,
                      1: {
                        level: null,
                        startTime: this.indicatordata.startTime,
                        endTime: this.indicatordata.endTime,
                      },
                    },
                  ])
                );
              }
            } else if (
              this.indicatordata.scaleLevel == 2 &&
              this.indicatordata.level == 2 &&
              this.indicatordata.dataSource == 4
            ) {
              let idSession = JSON.parse(sessionStorage.getItem("tendencyChart"));
              if (idSession) {
                idSession.map((item) => {
                  if (item.id == id) {
                    item["2"] = {
                      level: this.indicatordata.level,
                      startTime: this.indicatordata.startTime,
                      endTime: this.indicatordata.endTime,
                    };
                  }
                  return item;
                });
              }
              sessionStorage.setItem(id, JSON.stringify(idSession));
            }
            if (level == 3 && diffTime <= level2 && diffTime > level1) {
              newParams.level = 2;
              this.ecahrtLoaded = false;
              this.myChart1.showLoading({
                text: this.$t("specialmonitor_loading"),
                color: "#57a3f3",
                textColor: "#fff",
                maskColor: "rgba(18,55,127,.5)",
              });
              this.$emit("whellScale", newParams);
            }
            if (level == 2 && diffTime <= level1 && dataSource == 4) {
              newParams.level = 1;
              this.ecahrtLoaded = false;
              this.myChart1.showLoading({
                text: this.$t("specialmonitor_loading"),
                color: "#57a3f3",
                textColor: "#fff",
                maskColor: "rgba(18,55,127,.5)",
              });
              this.$emit("whellScale", newParams);
            }
          }
        }
      });
      window.addEventListener("resize", () => {
        if (this.myChart1 != null) {
          this.myChart1.resize();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
