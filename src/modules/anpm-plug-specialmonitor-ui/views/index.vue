<template>
  <div class="scroll-container" v-on:scroll="handleScroll">
    <section
      class="sectionBox"
      style="
        display: flex;
        box-orient: vertical;
        flex-direction: column;
        height: 100%;
      "
    >
      <div class="section-top" id="scrollHeader">
        <Row class="fn_box" style="width: calc(100% - 100px)">
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("spec_indic") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box">
                <Select
                  v-model="query.indicator"
                  filterable
                  :placeholder="$t('testspeed_select_indicators')"
                >
                  <Option
                    v-for="item in indicatorList"
                    :value="item.id"
                    :key="item.name"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </Col>
          <Col span="12">
            <div class="fn_item">
              <label class="fn_item_label"
                >{{ $t("spec_time_granularity") }}{{ $t("comm_colon") }}</label
              >

              <div class="fn_item_box">
                <div class="report-left-item" style="display: inline-block">
                  <RadioGroup
                    v-model="dateType"
                    @on-change="dateTypeChange"
                    style="margin-right: 0px"
                  >
                    <Radio label="1">
                      <span
                        >{{ $t("spec_date_type_1")
                        }}{{ $t("comm_colon") }}</span
                      >
                    </Radio>
                    <Radio label="2">
                      <span
                        >{{ $t("spec_date_type_2")
                        }}{{ $t("comm_colon") }}</span
                      >
                    </Radio>
                    <Radio label="3">
                      <span
                        >{{ $t("spec_date_type_3")
                        }}{{ $t("comm_colon") }}</span
                      >
                    </Radio>
                  </RadioGroup>
                </div>
                <div class="report-left-box" style="display: inline-block">
                  <DatePicker
                    type="month"
                    v-if="dateType === '1'"
                    :value="monthValue"
                    :options="monthOptions"
                    :placeholder="$t('comm_select_month')"
                    :format="$t('comm_format_year')"
                    style="width: 120px"
                    :clearable="false"
                    :editable="false"
                    @on-change="monthChange"
                  >
                  </DatePicker>
                  <div class="report-left-time" v-else-if="dateType === '2'">
                    <div class="left-time-year" style="display: inline-block">
                      <DatePicker
                        type="year"
                        :value="yearValue"
                        :options="yearOptions"
                        :format="$t('comm_format_year2')"
                        :placeholder="$t('comm_year')"
                        style="width: 90px"
                        :clearable="false"
                        :editable="false"
                        @on-change="yearChange"
                      >
                      </DatePicker>
                    </div>
                    <div
                      class="left-time-box checked-time"
                      style="
                        width: 305px;
                        display: inline-block;
                        margin-left: 10px;
                        vertical-align: middle;
                      "
                    >
                      <time-item
                        ref="ref_week"
                        :setYear="yearValue"
                        :setDay="dayValue"
                        @on-change="weekChange"
                      ></time-item>
                    </div>
                  </div>
                  <div
                    class="report-left-time disabledTime"
                    v-show="dateType === '3'"
                  >
                    <DatePicker
                      type="date"
                      v-model="startTime"
                      :placeholder="$t('comm_select_time')"
                      format="yyyy-MM-dd"
                      style="width: 120px"
                      :clearable="false"
                      :editable="false"
                      :options="startOptions"
                    >
                    </DatePicker>
                    <span style="padding: 0 15px"
                      >{{ $t("comm_to") }}{{ $t("comm_colon") }}</span
                    >
                    <DatePicker
                      type="date"
                      v-model="endTime"
                      :placeholder="$t('comm_select_time')"
                      format="yyyy-MM-dd"
                      style="width: 120px"
                      :clearable="false"
                      :editable="false"
                      :options="endOptions"
                    >
                    </DatePicker>
                  </div>
                </div>
              </div>
            </div>
          </Col>
          <Col span="6">
            <div class="fn_item">
              <label class="fn_item_label" style="width: 30%"
                >{{ $t("dash_Special_line") }}{{ $t("comm_colon") }}</label
              >
              <div class="fn_item_box" style="width: 70%">
                <div
                  class="specListBox noIcon"
                  ref="specListBox"
                  id="specListBox"
                >
                  <span
                    v-if="checkedSpecList.length < 1"
                    style="line-height: 32px"
                    class="placherText"
                    >{{ $t("spec_select_line") }}</span
                  >
                  <div
                    v-else
                    class="checkedItem"
                    v-for="(item, index) in checkedSpecList.slice(
                      0,
                      checkedSpecList.length - moreNum
                    )"
                    :key="index"
                  >
                    <span class="ivu-tag-text" :title="item.name">{{
                      item.name
                    }}</span>
                    <!-- <Icon type="ios-close" class="ivu-tag-exit" @click="deleteItem(index)" /> -->
                  </div>
                  <div class="checkedMoreItem" v-show="moreNum > 0">
                    <span class="ivu-tag-text">+{{ moreNum }}...</span>
                  </div>
                  <Icon
                    v-if="checkedSpecList.length > 0"
                    type="ios-close"
                    class="ivu-tag-exit"
                    @click="deleteItem(0)"
                  />
                </div>
                <Button class="choose-btn" @click.stop="choiceSpec">{{
                  $t("but_choose")
                }}</Button>
              </div>
            </div>
          </Col>
        </Row>
        <div class="tool-btn" style="margin-top: -68px">
          <div>
            <Button
              v-if="permissionObj.list"
              class="query-btn"
              icon="ios-search"
              type="primary"
              @click="searchClick"
              :title="$t('common_query')"
            ></Button>
            <div v-else class="query-placeholder"></div>
          </div>
        </div>
      </div>

      <div class="section-body contentBox_bg" style="margin-top: 5px">
        <div class="section-body-content" id="scrollBody">
          <div class="specBox">
            <Loading :loading="pageLoading"></Loading>
            <div
              v-if="indicatorData.length < 1 && !pageLoading"
              :class="{
                fillempty: currentSkin == 1,
                fillempty2: currentSkin == 0,
              }"
            >
              <p class="emptyText">{{ $t("common_No_data") }}</p>
            </div>
            <div
              class="specItem"
              v-else
              v-for="(item, index) in indicatorData"
              :key="item.id"
            >
              <indicator-item
                ref="indicatorItem"
                :indicatorItemData="item"
                :isdarkSkin="isdarkSkin"
                @scale="
                  (...arg) => {
                    getSCaleData(...arg, index);
                  }
                "
              ></indicator-item>
            </div>
            <div v-if="totalCount > 1" class="totalCountClass">
              {{ $t("lisence_total") }} {{ totalCount }}
              {{ $t("comm_records") }}
            </div>
            <div v-show="ismore && !loading" class="dataTips">
              {{ $t("specialmonitor_data") }}...
            </div>
          </div>
        </div>
      </div>

      <!-- 专线选择模态框 -->
      <Modal
        class="specialmonitor-modal border-table"
        width="1000"
        v-model="specModal.show"
        :label="$t('specialmonitor_select_line')"
        :title="$t('spec_select_line')"
        :mask="true"
        sticky
        draggable
      >
        <div class="conditionSlCont sectionBox">
          <div class="section-top">
            <Row>
              <Col span="12">
                <div class="fn_item">
                  <label class="fn_item_label"
                    >{{ $t("comm_keywords") }}{{ $t("comm_colon") }}</label
                  >
                  <div class="fn_item_box">
                    <Input
                      v-model="specModal.query.keyword"
                      :title="$t('specialmonitor_matching')"
                      :placeholder="$t('specialmonitor_matching')"
                      style="width: 100%"
                    />
                  </div>
                </div>
              </Col>
              <Col span="6">
                <div class="fn_item">
                  <label class="fn_item_label"
                    >{{ $t("comm_group") }}{{ $t("comm_colon") }}</label
                  >
                  <div class="fn_item_box">
                    <Select
                      v-model="specModal.query.groupIds"
                      multiple
                      :max-tag-count="1"
                      clearable
                      filterable
                      :only-filter-with-text="true"
                      style="width: 200px"
                    >
                      <Option
                        v-for="item in groupList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option
                      >
                    </Select>
                  </div>
                </div>
              </Col>
              <Col span="3">
                <div class="fn_item">
                  <Button
                    type="primary"
                    class="query-btn"
                    icon="ios-search"
                    style="margin-left: 100px"
                    @click="
                      getSpecList({
                        datas: specModal.query.keyword,
                        pageNo: 1,
                        pageSize: specModal.query.pageSize,
                      })
                    "
                  >
                  </Button>
                </div>
              </Col>
            </Row>
          </div>
        </div>
        <div class="conditionBoxCont border-table">
          <Table
            :columns="speccolumns"
            :data="specModal.data"
            size="small"
            ref="specTable"
            class="mgB15"
            :no-data-text="
              specloading
                ? ''
                : specModal.data.length > 0
                ? ''
                : currentSkin == 1
                ? '<div class=\'table_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
                : '<div class=\'table2_empty\'><p class=\'emptyText\' >' +
                  $t('common_No_data') +
                  '</p></div>'
            "
            @on-select="handleSelect"
            @on-select-cancel="handleCancel"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
            :disabled-hover="true"
          ></Table>
          <div class="tab-page" v-if="parseInt(specModal.total) > 0">
            <Page
              ref="pages"
              :total="specModal.total"
              v-if="parseInt(specModal.total) > 0"
              show-total
              show-sizer
              show-elevator
              :current="specModal.query.pageNo"
              :page-size="specModal.query.pageSize"
              :page-size-opts="specModal.pageOpts"
              @on-change="pageChange($event)"
              @on-page-size-change="pageSizeChange($event)"
            ></Page>
          </div>
        </div>
        <div slot="footer">
          <Button type="error" style="margin-right: 20px" @click="closeModal">{{
            $t("common_cancel")
          }}</Button>
          <Button type="primary" @click="specSave">{{
            $t("common_verify")
          }}</Button>
        </div>
      </Modal>
    </section>
  </div>
</template>

<script>
let echarts = require('echarts/lib/echarts')
import locationreload from "@/common/locationReload";

var finished = true;
import loading from "@/common/loading/loading"
import global from "@/common/global.js";
import timeItem from "@/common/time/index.vue";
import indicatorItem from "./specItem.vue";
import echartFn from "@/common/mixins/echartFun";
import moment from 'moment';
import ipv6Format from "@/common/ipv6Format";

export default {
  name: "specpath",
  components: {
    timeItem,
    loading,
    indicatorItem
  },
  mixins: [echartFn],
  data() {
    return {
            currentSkin: sessionStorage.getItem('dark') || 1,
      groupList:[],
      pageLoading: false,
      isScroll: false,
      ismore: false,
      isLastPage: false,
      totalCount: 0,
      isdarkSkin: sessionStorage.getItem('dark') || 1,
      //权限对象
      permissionObj: {},
      //统计指标
      indicatorList: [
        { id: 1, name: this.$t('specialmonitor_delay') },
        { id: 2, name: this.$t('specialmonitor_delay2') },
        { id: 3, name: this.$t('specialmonitor_delay3') },
      ],
      //时间类型
      dateType: "3",
      monthOptions: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now() - 24 * 60 * 60 * 1000;
        }
      },
      yearOptions: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      monthValue: new Date().format(this.$t('comm_format_year')),
      yearValue: new Date().format("yyyy"),
      //搜索字段
      query: {
        indicator: 2,//指标
        startTime: new Date().format("yyyy-MM") + "-01",//开始时间
        endTime: "",//结束时间
        checkedSpecList: "",
      },
      chartPage: 1,
      chartPageSaved: 1,
      startTime: new Date().format2('yyyy-MM-dd 00:00:00'),
      endTime: new Date().format2('yyyy-MM-dd 23:59:59'),
      currentTime: Date.now(),
      startOptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const start = new Date();
              return start;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              return start;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let start = new Date(date.year, date.month, 1);
              return start;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();

              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              return start;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
        ],
        disabledDate: (date) => {
          let endTime = this.endTime == '' ? Date.now() : this.endTime
          return date > Date.now() || date > new Date(endTime).valueOf()
        },
      },
      endOptions: {
        shortcuts: [
          {
            text: this.$t('comm_today'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              this.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.endTime = new Date().format("yyyy-MM-dd 23:59:59");
              this.query.startTime = new Date().format("yyyy-MM-dd 00:00:00");
              this.query.endTime = new Date().format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_yesterday'),
            value() {
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              const end = new Date();
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_7'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_last_30'),
            value() {
              const end = new Date();
              return end;
            },
            onClick: (picker) => {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              const end = new Date();
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_curr_month'),
            value() {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              return end;
            },
            onClick: (picker) => {
              let nowDate = new Date();
              let date = {
                year: nowDate.getFullYear(),
                month: nowDate.getMonth(),
                date: nowDate.getDate(),
              };
              let end = new Date(date.year, date.month + 1, 0);
              let start = new Date(date.year, date.month, 1);
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
          {
            text: this.$t('comm_preced_month'),
            value() {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              return end;
            },
            onClick: (picker) => {
              let date = new Date();
              let day = new Date(
                date.getFullYear(),
                date.getMonth(),
                0
              ).getDate();
              let end = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                day
              );
              let start = new Date(
                new Date().getFullYear(),
                new Date().getMonth() - 1,
                1
              );
              this.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.endTime = end.format("yyyy-MM-dd 23:59:59");
              this.query.startTime = start.format("yyyy-MM-dd 00:00:00");
              this.query.endTime = end.format("yyyy-MM-dd 23:59:59");
            },
          },
        ],
        disabledDate: (date) => {
          let startTime = this.startTime == '' ? '' : this.startTime
          return date > Date.now() || date < new Date(startTime).valueOf() - 8 * 3600 * 1000
        },
      },
      dayValue: "",
      week: {},
      //专线已选择
      checkedSpecList: [],
      moreNum: 0,
      //多选保存参数
      selectedIds: new Set(),
      selectedDatas: [],
      specloading: false,
      specModal: {
        show: false,
        query: {
          keyword: "",
          pageNo: 1,
          groupIds:"",
          pageSize: 10,
        },
        total: 0,
        pageOpts: [10, 50, 100, 200, 500, 1000],
        data: [],
      },
      //专线列表表头
      speccolumns: [
        {
          //多选
          type: "selection",
          width: 60,
          className: "bgColor",
          align: "center",
        },
        {
          title: this.$t('dash_Special_line_name'),
          key: "name",
          align: "center",
          render: (h, params) => {
            const data = params.row.name || '--';
            return h("div", {
              style: {
                overflow: 'hidden', //超出的文本隐藏
                textOverflow: 'ellipsis', //溢出用省略号显示
                whiteSpace: 'nowrap', //溢出不换行
              }, attrs: {
                title: data
              },
            }, data)
          }
        },
           //分组
        {
          title: this.$t('comm_grouping'),
          key: "groupNames",
          align: "center",
          width: 200,
          render: (h, params) => {
            let str = params.row.groupNames;
            console.log(params.row , "--------------");
            if (str === undefined || str === null) {
              str = "--";
            }
            let v = "";
            if (str.length > 20) {
              v = str.substring(0, 15) + "...";
               return h('Tooltip',{
                      props: {
                        placement:'top-start',
                        "max-width":500,
                      }
                     },
                     [v,h(
                      'span',{
                        slot:'content',
                      },str
                     )]);
            } else {
              v = str;
               return h('span',v)
            }
          },
        },
        {
          title: this.$t('dash_A'),
          key: "specAIp",
          align: "center",
          width:200,
          render: (h, params) => {
            var data = params.row.specAIp || '--';
            // return h("div", data)
            let maxWidth = params.column.width; // 获取动态传递的宽度
              data = ipv6Format.formatIPv6Address(data,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, data);
          }
        },
        {
          title: this.$t('dash_Z'),
          key: "specZIp",
          align: "center",
            width:200,
          render: (h, params) => {
            var data = params.row.specZIp || '--';
            // return h("div", data)
            let maxWidth = params.column.width; // 获取动态传递的宽度
              data = ipv6Format.formatIPv6Address(data,maxWidth);
              return h('div', { style: { whiteSpace: 'pre-wrap' } }, data);
          }
        },
      ],
      //每次点击时保存指标类型
      indicatorType: 1,
      //loading状态
      loading: true,
      //监测指标数据
      indicatorData: [],
      savedData: [],
      //监测指标总条数
      indicatorDataTotal: 0,
      //记录上次滚动位置
      beforescrollTop: 0,
      //判断当前是否处于滚动加载数据中
      scrolling: false,
      //当前数据已经请求到第几页了
    }
  },
  created() {


  //  按周搜索先初始化时间
        let date = new Date().getTime();
        this.dayValue = new Date(date).format('yyyy-MM-dd');
      this.$nextTick(() => {
            locationreload.loactionReload(this.$route.path.split('/')[1].toLowerCase());

      })

    let permission = global.getPermission();
    this.permissionObj = Object.assign(permission, {});

    this.getGroupList();

  },
  computed: {},
    beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('storage', this.handleStorageChange);
  },
  mounted() {
    // 监听 storage 事件
    window.addEventListener('storage', this.handleStorageChange);
    // window.parent.addEventListener("message", (e) => {
    //   if (e) {
    //     if (e.data.type == 'msg') {
    //       return;
    //     } else if (typeof e.data == 'object') {
    //       this.isdarkSkin = e.data.isdarkSkin;
    //     } else if (typeof e.data == 'number') {
    //       this.isdarkSkin = e.data;
    //     }

    //   }
    // });
    this.searchClick();
    this.$nextTick(function () {
      // 解决视图渲染，数据未更新
      // top.window.addEventListener("scroll", this.onScroll, true);
      // window.addEventListener("scroll", this.onScroll, true);
    });
  },
  methods: {
       handleStorageChange(event) {
      if (event.key === 'dark') {
        this.currentSkin = event.newValue; // 更新肤色
      }
    },
      // 处理到底部
    handleScroll(event) {
      console.log("滚动了");
      console.log(event.target.scrollTop);
      console.log(event.target.scrollHeight);
      console.log(event.target.offsetHeight);

      const container = document.querySelector(".scroll-container");
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight - 400) {
        console.log("滚动到底部了");
        this.onScroll();
      }
    },
    getGroupList(orgId) {
      const param = {
        orgId: orgId,
      };
      this.groupCreate = true;
      this.$http.wisdomPost("/group/groupList", param).then((res) => {
        if (res.code === 1) {
          if (res.data) {
            this.groupList = res.data;
          }
        } else {
          this.groupList = [];
        }
      });
    },
    //时间粒度单选事件
    dateTypeChange(val) {
      this.query.dateType = val;
      if (val === "2") {
        // let date = new Date().setDate(1);
        let date = new Date().getTime();
        this.dayValue = new Date(date).format('yyyy-MM-dd');
      } else if (val === "1") {
        // if (JSON.stringify(this.week) != "{}") {
        //   this.monthValue = this.week.title;
        //   this.query.startTime = this.week.value[0];
        // }
        let year = this.monthValue.split(this.$t('comm_year1'))[0],
          month = this.monthValue.split(this.$t('comm_year1'))[1].split(this.$t('comm_month'))[0];

        let time = year + "-" + month;
        this.query.startTime = new Date(time).format('yyyy-MM-dd');
      }
    },
    //时间事件
    monthChange(val) {
      let day = val.split(this.$t('comm_year1')),
        year = day[0],
        month = day[1].split(this.$t('comm_month'))[0];
      this.query.startTime = year + "-" + month + "-" + "01";
      this.query.endTime = year + "-" + month + "-" + new Date(year, month, 0).getDate();
      this.monthValue = val;
    },
    yearChange(val) {
      let month = this.monthValue.split(this.$t('comm_year1')),
        startTime = this.query.startTime.split("-");
      this.week = {};
      this.monthValue = val + month[1];
      this.query.startTime =
        val.split(this.$t('comm_year1'))[0] + "-" + startTime[1] + "-" + startTime[2];
      this.yearValue = val.split(this.$t('comm_year1'))[0];
    },
    weekChange(value) {
      this.yearValue = value.title.split(this.$t('comm_year1'))[0];
      this.dayValue = value.value[0];
      this.week = Object.assign({}, value);
    },

    //时间事件
    startTimeChange(val) {
      if (val == "") {
        this.endTime = "";
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
        this.endOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        var now = new Date(val),
          y = now.getFullYear(),
          m = now.getMonth() + 1,
          h = now.getHours(),
          min = now.getMinutes(),
          s = now.getSeconds(),
          d = now.getDate();
        let ss =
          y +
          "-" +
          (m < 10 ? "0" + m : m) +
          "-" +
          (d < 10 ? "0" + d : d) +
          " " +
          now.toTimeString().substr(0, 8);
        this.endOptions.disabledDate = (date) => {
          let checkedDay = new Date(
            y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let checkedTime = new Date(this.startTime).valueOf();
          let interTime = checkedTime - checkedDay;
          let startTime = this.startTime
            ? new Date(this.startTime).valueOf()
            : "";
          let endTime = val
            ? new Date(val).valueOf() + 62 * 24 * 3600 * 1000
            : "";
          return (
            (date && date.valueOf() < startTime - interTime) ||
            (date &&
              date.valueOf() >
              (endTime > this.currentTime ? this.currentTime : endTime))
          );
        };
      }
    },
    endTimeChange(val) {
      if (val == "") {
        this.startOptions.disabledDate = (date) => {
          return date && date.valueOf() > this.currentTime;
        };
      } else {
        if (this.startTime == "" || this.startTime == undefined) {
          this.endTime = "";
          this.$Message.warning(this.$t('specquality_strat'));
        } else {
          var now = new Date(val),
            y = now.getFullYear(),
            m = now.getMonth() + 1,
            h = now.getHours(),
            min = now.getMinutes(),
            s = now.getSeconds(),
            d = now.getDate();
          let ss =
            y +
            "-" +
            (m < 10 ? "0" + m : m) +
            "-" +
            (d < 10 ? "0" + d : d) +
            " " +
            now.toTimeString().substr(0, 8);
          let checkedDayTimes = new Date(
            y + "-" + m + "-" + d + " 00:00:00"
          ).valueOf();
          let nowDayTimes = moment().startOf("day").valueOf();
          if (ss.slice(-8) == "00:00:00") {
            if (checkedDayTimes == nowDayTimes) {
              this.endTime = new Date(this.currentTime).format(
                "yyyy-MM-dd HH:mm:ss"
              );
            } else {
              this.endTime = new Date(val).format("yyyy-MM-dd 23:59:59");
            }
          }
          this.startOptions.disabledDate = (date) => {
            let endTime = this.endTime ? new Date(this.endTime).valueOf() : "";
            let startTime = val
              ? new Date(val).valueOf() - 62 * 24 * 3600 * 1000
              : "";
            return (
              (date && date.valueOf() < startTime) ||
              (date &&
                date.valueOf() >
                (endTime > this.currentTime ? this.currentTime : endTime))
            );
          };
        }
      }
    },

    getSpecChartList(param, page, scrollDistance) {//获取专线数据API
      this.loading = true;
      this.pageLoading = true
      if (this.chartPageSaved === page) {
        this.$http.PostJson("/specialmonitor/getTrend", Object.assign({ pageNo: page, pageSize: 10 }, param)).then((res) => {

          if (res.code === 1) {
            if (res.data) {
              this.totalCount = res.data.total;
              let newData = this.handleData(res.data.records, this.indicatorType, this.specModal.data, 0, param);
              this.isLastPage = Number(page) * 3 >= res.data.total ? true : false;
              this.isScroll = false;

              if (page === 1) {
                this.indicatorData = [];
                this.indicatorDataTotal = res.data.total || 0;
              }
              this.indicatorData.push.apply(this.indicatorData, newData);
            
            
            }
            else {
              this.indicatorData = [];
            }

          } else {
            this.indicatorData = [];
            this.$Message.error(res.msg)
          }
        }).catch((err) => {
          this.indicatorData = [];
          this.loading = false;
        }).finally(() => {
          this.loading = false;
          this.pageLoading = false
          let _this = this;
          setTimeout(function () { _this.scrolling = false, finished = true }, 1000)

        });

      }

    },
    //处理返回的数据
    handleData(data, type, specList, scaleLevel, param) {
      const dataArr = data, _that = this;
      let handleDatas = [], indicatorType = _that.indicatorList.filter(items => { return items.id === type })[0].name;
      if (!Array.isArray) {
        Array.isArray = function (arg) {
          return Object.prototype.toString.call(arg) === '[object Array]';
        };
      }
      if (data && Array.isArray(dataArr)) {
        handleDatas = dataArr.map(item => {
          let itemData = {};
          let specData = item.specialData;
          itemData.id = specData.id;
          itemData.scaleLevel = scaleLevel;
          itemData.startTime = param.startTime;
          itemData.endTime = param.endTime;
          itemData.dataSource = specData.dataSource;
          itemData.level = item.level;
          itemData.type = type;
          itemData.specAName = (specData.provinceA || "") + (specData.areaA || "") + (specData.cityA || "");
          itemData.specZName = (specData.provinceZ || "") + (specData.areaZ || "") + (specData.cityZ || "");
          itemData.specName = specData.name == null || specData.name == undefined ? "" : specData.name;
          itemData.specAIp = specData.specialAIp;
          itemData.specZIp = specData.specialZIp;
          itemData.indicatorType = indicatorType;
          itemData.matchingType = specData.matchingType;
          let targetData = item.target;
          if (type === 1) {
            itemData.delayDetails = targetData ? { now: targetData.delay, min: targetData.delayMin, max: targetData.delayMax, avg: targetData.delayAvg } : { now: "", min: "", max: "", avg: "" };
            itemData.lossDetails = targetData ? { now: targetData.lose, min: targetData.loseMin, max: targetData.loseMax, avg: targetData.loseAvg } : { now: "", min: "", max: "", avg: "" };
          } else if (type === 2) {
            itemData.inflowDetails = targetData ? { now: targetData.flowIntoSpeed ? this.getUnit(targetData.flowIntoSpeed, true, true) : "", min: targetData.flowIntoSpeedMin ? this.getUnit(targetData.flowIntoSpeedMin, true, true) : "", max: targetData.flowIntoSpeedMax ? this.getUnit(targetData.flowIntoSpeedMax, true, true) : "", avg: targetData.flowIntoSpeedAvg ? this.getUnit(targetData.flowIntoSpeedAvg, true, true) : "" } : { now: "", min: "", max: "", avg: "" };
            itemData.outflowDetails = targetData ? { now: targetData.flowOutSpeed ? this.getUnit(targetData.flowOutSpeed, true, true) : "", min: targetData.flowOutSpeedMin ? this.getUnit(targetData.flowOutSpeedMin, true, true) : "", max: targetData.flowOutSpeedMax ? this.getUnit(targetData.flowOutSpeedMax, true, true) : "", avg: targetData.flowOutSpeedAvg ? this.getUnit(targetData.flowOutSpeedAvg, true, true) : "" } : { now: "", min: "", max: "", avg: "" };
            itemData.inuseDetails = targetData ? { now: targetData.useFlowIntoSpeed, min: targetData.useFlowIntoSpeedMin, max: targetData.useFlowIntoSpeedMax, avg: targetData.useFlowIntoSpeedAvg } : { now: "", min: "", max: "", avg: "" };
            itemData.outuseDetails = targetData ? { now: targetData.useFlowOutSpeed, min: targetData.useFlowOutSpeedMin, max: targetData.useFlowOutSpeedMax, avg: targetData.useFlowOutSpeedAvg } : { now: "", min: "", max: "", avg: "" };
          } else if (type === 3) {
            itemData.useableDetails = targetData ? { now: targetData.useRate, min: targetData.useRateMin, max: targetData.useRateMax, avg: targetData.useRateAvg } : { now: "", min: "", max: "", avg: "" };
            itemData.goodrateDetails = targetData ? { now: targetData.fineRate, min: targetData.fineRateMin, max: targetData.fineRateMax, avg: targetData.fineRateAvg } : { now: "", min: "", max: "", avg: "" };
            itemData.useableexemptionDetails = targetData ? { now: targetData.useRateRelief, min: targetData.useRateReliefMin, max: targetData.useRateReliefMax, avg: targetData.useRateReliefAvg } : { now: "", min: "", max: "", avg: "" };
            itemData.goodrateexemptionDetails = targetData ? { now: targetData.fineRateRelief, min: targetData.fineRateReliefMin, max: targetData.fineRateReliefMax, avg: targetData.fineRateReliefAvg } : { now: "", min: "", max: "", avg: "" };
          }




          let trendData = item.trend;
          let delayData = [], lossData = [], inflowData = [], outflowData = [], inuseData = [], outuseData = [], useableData = [], goodrateData = [], useableexemptionData = [], goodrateexemptionData = [], times = [];
          if (trendData) {
            for (let index = 0; index < trendData.length; index++) {
              const element = trendData[index];

              if (type === 1) {
                delayData.push(element.delay);
                lossData.push(element.lose);
              } else if (type === 2) {
                inflowData.push(element.flowIntoSpeed);
                outflowData.push(element.flowOutSpeed);
                inuseData.push(element.useFlowIntoSpeed);
                outuseData.push(element.useFlowOutSpeed);
              } else if (type === 3) {
                useableData.push(element.useRate);
                goodrateData.push(element.fineRate);
                useableexemptionData.push(element.useRateRelief);
                goodrateexemptionData.push(element.fineRateRelief);
              }
              times.push(element.dateTime);
            }
          } else {
            delayData = [];
            lossData = [];
            inflowData = [];
            outflowData = [];
            inuseData = [];
            outuseData = [];
            useableData = [];
            goodrateData = [];
            useableexemptionData = [];
            goodrateexemptionData = [];
            times = [];
          }
          itemData.delay = delayData;
          itemData.loss = lossData;

          itemData.inflow = inflowData;
          itemData.outflow = outflowData;
          itemData.inuse = inuseData;
          itemData.outuse = outuseData;

          itemData.useable = useableData;
          itemData.goodrate = goodrateData;
          itemData.useableexemption = useableexemptionData;
          itemData.goodrateexemption = goodrateexemptionData;

          itemData.time = times;
          return itemData
        })
      }
      return handleDatas
    },
    //查询点击
    searchClick() {


      if (this.dateType === "3") {
        var startTimeTemp = new Date(this.startTime).format("yyyy-MM-dd");
        var endTimeTemp = new Date(new Date(this.endTime).getTime() + 86400000).format('yyyy-MM-dd');
        let startVal = moment(startTimeTemp, "YYYY-MM-DD hh:mm:ss").valueOf();
        let endVal = moment(endTimeTemp, "YYYY-MM-DD hh:mm:ss").valueOf();
        if ((endVal - startVal) / 1000 / 3600 / 24 > 62) {
          this.$Message.warning(this.$t('warning_time_not_exceed_62'));
          return;
        }
      }
      this.indicatorData = [];
      this.loading = true;
      this.indicatorType = this.query.indicator;
      this.query.endTime = null;
      this.indicatorDataTotal = 0;
      this.totalCount = 0;
      let idSession = sessionStorage.getItem('tendencyChart');
      if (idSession) {
        sessionStorage.removeItem('tendencyChart')
      }
      if (this.dateType === "1") {

        let day = this.monthValue.split(this.$t('comm_year1')),
          year = day[0],
          month = day[1].split(this.$t('comm_month'))[0];
        let currentM = new Date().getMonth() + 1;
        if (Number(month) == currentM) {
          this.query.endTime = new Date(new Date().getTime() + 86400000).format('yyyy-MM-dd 00:00:00')
        } else {
          let mEndTime = year + "-" + month + "-" + new Date(year, month, 0).getDate();
          this.query.endTime = new Date(new Date(mEndTime).getTime() + 86400000).format('yyyy-MM-dd 00:00:00');
        }
        // this.query.endTime = "";
      } else if (this.dateType === "2") {
        let param = this.$refs["ref_week"].getParams();
        this.week = Object.assign({}, param);
        this.query.startTime = param.value[0];
        this.query.endTime = new Date(new Date(param.value[1]).getTime() + 86400000).format('yyyy-MM-dd');

      } else if (this.dateType === "3") {
        this.query.startTime = new Date(this.startTime).format("yyyy-MM-dd");
        this.query.endTime = new Date(new Date(this.endTime).getTime() + 86400000).format('yyyy-MM-dd');
      }
      this.query.checkedSpecList = this.checkedSpecList;
      //查询参数
      let param = {
        type: this.query.indicator,
        startTime: new Date(this.query.startTime).format('yyyy-MM-dd 00:00:00'),
        endTime: new Date(this.query.endTime).format('yyyy-MM-dd 00:00:00'),
        // startTime:new Date(this.query.startTime+" 00:00:00").valueOf()/1000,
        // endTime:new Date(this.query.endTime+" 23:59:59").valueOf()/1000,
        specialIds: this.query.checkedSpecList.length < 1 ? "" : this.query.checkedSpecList.map(item => { return item.id }).join(',')
      }

      this.chartPage = 1;
      this.chartPageSaved = 1;
      //调用查询接口
      this.getSpecChartList(param, this.chartPage, 0);
    },
    //专线列表获取
    getSpecList(param) {
      this.specModal.query.keyword = this.specModal.query.keyword.trim();
      param.groupIds = this.specModal.query.groupIds;
      this.specloading = true;
      this.$http.wisdomPost("/specialmonitor/list", param).then((res) => {
        if (res.code === 1) {
          if (res.data.records) {
            this.specModal.data = res.data.records.map(({ id, areaA, areaZ, cityA, cityZ, specialNum2, specialNum, provinceA, provinceZ, name, specialAIp, specialZIp,groupNames }) => {
              return { id, name: `${name || ""}(${specialNum2 || specialNum})`, areaA, areaZ, cityA, cityZ, provinceA, provinceZ, specAIp: specialAIp, specZIp: specialZIp,groupNames }
            });
            this.specModal.total = res.data.total;
            //拿到table的所有行的引用，_isChecked属性  实现页面切换选中状态不变
            let _that = this;
            setTimeout(function () {
              let objData = _that.$refs.specTable.$refs.tbody.objData;
              for (let key in objData) {
                if (_that.selectedIds.has(objData[key].id)) {
                  objData[key]._isChecked = true;
                }
              }
            }, 0);
          }
          else {
            this.specModal.data = [];
            this.specModal.total = 0;
          }
        } else {
          this.specModal.data = [];
          this.specModal.total = 0;
          this.$Message.error(res.msg)
        }
      }).catch((err) => {
        this.specModal.data = [];
        this.specModal.total = 0;
        this.specloading = false;
      }).finally(() => {
        this.specloading = false;
      });

    },
    //表格选择事件
    handleSelect(slection, row) {
      this.selectedIds.add(row.id);
      this.selectedDatas.push(row);
    },
    //缩放请求数据
    getSCaleData(param, index) {
      this.loading = true;
      this.$http.PostJson("/specialmonitor/getCutGranularityTrend", Object.assign(param, {})).then(({ code, data, msg }) => {
        if (code === 1) {
          let chartDom = document.getElementById('tendencyChartId' + this.$refs.indicatorItem[index].indicatorItemData.id)
          echarts.init(chartDom).hideLoading()
          if (data) {
            const findeIndex = this.indicatorData.findIndex(item => {
              return item.id == param.specialIds;
            })
            let newData = this.handleData(data.records, this.indicatorType, this.specModal.data, param.level, param);
            this.indicatorData[findeIndex] = newData[0];
            this.$set(this.indicatorData, findeIndex, newData[0])
          }
        }
      })
    },
    handleCancel(slection, row) {
      this.selectedIds.delete(row.id);
      this.selectedDatas.splice(this.selectedDatas.findIndex(item => item.id == row.id), 1);
    },
    handleSelectAll(slection) {
      if (slection.length === 0) {
        const tableType = 'specTable';
        let data = this.$refs[tableType].data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
        this.selectedDatas = [];
      } else {
        this.selectedDatas = [];
        slection.forEach((item) => {
          this.selectedIds.add(item.id);
          this.selectedDatas.push(item);
        });
      }
    },
    //分页事件
    pageChange(page, typeModal) {
      this.specModal.query.pageNo = page;
      this.getSpecList({ datas: this.specModal.query.keyword, pageNo: this.specModal.query.pageNo, pageSize: this.specModal.query.pageSize });
    },
    pageSizeChange(pageSize, typeModal) {
      this.specModal.query.pageNo = 1;
      this.specModal.query.pageSize = pageSize;
      this.getSpecList({ keywords: this.specModal.query.keyword, pageNo: this.specModal.query.pageNo, pageSize: this.specModal.query.pageSize });
    },
    //选择专线按钮
    choiceSpec() {
      this.specModal.show = true;
      this.specModal.query.pageNo = 1;
      this.specModal.query.keyword = '';
      this.getSpecList({ datas: this.specModal.query.keyword, pageNo: 1, pageSize: this.specModal.query.pageSize })
    },
    //删除选中的专线
    deleteItem(index) {
      this.checkedSpecList.splice(index, 1);
      this.handleWidth()
    },
    //关闭专线模态框
    closeModal() {
      this.selectedIds = new Set();
      this.selectedDatas = [];
      this.specModal.show = false;
    },
    //中继选择保存事件
    specSave() {
      this.save();

    },
    //保存操作
    save() {
      this.moreNum = 0;
      let selectArr = this.selectedDatas;
      selectArr.forEach((item) => {
        item.name = item.name;
      });
      const checkedArr = this.checkedSpecList;
      var newAlarmTaskArr = checkedArr;
      selectArr.forEach(items => {
        var newTaskArr = newAlarmTaskArr.filter(item => {
          return item.id != items.id;
        });
        newAlarmTaskArr = newTaskArr;
      });

      // this.checkedSpecList = newAlarmTaskArr.concat(selectArr);
      this.checkedSpecList = selectArr;
      this.handleWidth();
      this.selectedIds = new Set();
      this.selectedDatas = [];
      this.specModal.show = false;

    },
    //计算当前专线选择框剩余多少未显示
    handleWidth() {
      let specListBoxW = this.$refs['specListBox'].clientWidth;
      let w = 0, indexs = 0;
      for (let index = 0; index < this.checkedSpecList.length; index++) {
        if (w + 55 >= specListBoxW && indexs < this.checkedSpecList.length - 1) {
          this.checkedShowSpecList = this.checkedSpecList.slice(0, indexs);
          this.moreNum = this.checkedSpecList.length - indexs;
          break
        }
        let textWidth = this.checkedSpecList[index].name.length * 12;
        w += 35 + (textWidth > 120 ? 120 : textWidth);
        indexs = index;
        this.moreNum = 0;
      }
    },


    // 获取滚动条当前的位置
    getScrollTop() {
      let scrollTop = 0;
      scrollTop = top.document.getElementsByClassName("contentIframe")[0].scrollTop
      return scrollTop;
    },
    // 获取当前可视范围的高度
    getClientHeight() {
      let clientHeight = 0;
      if (top.document.body.clientHeight && top.document.documentElement.clientHeight) {
        clientHeight = Math.min(
          top.document.body.clientHeight,
          top.document.documentElement.clientHeight
        );
      } else {
        clientHeight = Math.max(
          top.document.body.clientHeight,
          top.document.documentElement.clientHeight
        );
      }
      return clientHeight;
    },
    // 获取文档完整的高度
    getScrollHeight() {
      return Math.max(
        top.document.body.scrollHeight,
        top.document.documentElement.scrollHeight
      );
    },
    // 滚动事件触发下拉加载
    onScroll() {
      if (!this.isScroll) {
        if (
          this.getScrollHeight() -
          this.getClientHeight() -
          this.getScrollTop() <= 0
        ) {
          this.isScroll = true;
          if (this.isLastPage) {
            this.ismore = true;
          } else if (!this.isLastPage) {
            if (this.isScroll === true) {

              let param = {
                type: this.query.indicator,
                // startTime:new Date(this.query.startTime+" 00:00:00").valueOf()/1000,
                // endTime:new Date(this.query.endTime+" 23:59:59").valueOf()/1000,
                startTime: new Date(this.query.startTime).format('yyyy-MM-dd 00:00:00'),
                endTime: new Date(this.query.endTime).format('yyyy-MM-dd 00:00:00'),
                specialIds: this.query.checkedSpecList.length < 1 ? "" : this.query.checkedSpecList.map(item => { return item.id }).join(',')
              }
              this.chartPage += 1;
              this.chartPageSaved = this.chartPage;
              this.getSpecChartList(param, this.chartPage);
              //  this.getLine(this.query, 1);
            }
          }
        } else {
          this.ismore = false;
        }
      } else {
      }
    },
  },
  beforeDestroy() {
    let idSession = sessionStorage.getItem('tendencyChart');
    if (idSession) {
      sessionStorage.removeItem('tendencyChart')
    }
  }
}
</script>

<style>
.searchTop .ivu-select-input {
  height: 32px;
  line-height: 32px !important;
}

.fault-tab .dialTest-tab-content .ivu-table th.bgColor {
  /*background: #f1f6fe !important*/
}
</style>
<style lang="less">
.checked-time {
  .time-input {
    input {
      height: 32px;
      cursor: default;
    }
  }
}
</style>
<style lang="less" scoped>
.scroll-container {
  width: 100%;
  overflow: scroll;
  height: 100vh;
}
.query-placeholder {
  width: 60px;
  height: 35px;
}
.choose-btn {
  width: 56px;
  height: 32px;
  background: var(--spectendency_choose_btn_bg_color, #06324d);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid var(--spectendency_choose_btn_border_color, #05eeff);
  color: var(--spectendency_choose_btn_color, #05eeff);
  margin-left: 4px;
  justify-content: center;
  align-items: center;
  text-align: center;
  display: inline-flex;
}

.fix-middle {
  position: fixed;
  top: 30%;
}

.totalCountClass {
  // position: absolute;
  bottom: -40px;
  right: 20px;
  text-align: right;
  line-height: 30px;
  font-size: 18px;
  margin-top: 10px;
}

.dataTips {
  font-size: 16px;
  width: 100%;
  height: 100%;
  text-align: center;
  text-indent: 5px;
  line-height: 100px;
}

.specListBox {
  display: inline-block;
  min-width: 150px;
  border-radius: 4px;
  box-sizing: border-box;
  height: 32px;
  border: 1px solid var(--border_color, #dddddd);
  padding: 0 8px;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;

  .placherText {
    color: var(--input_placeholder_color, #c5c8ce);
  }

  .checkedItem {
    display: inline-block;
    margin: 4px 0 4px 3px;
    padding: 0 8px;
    line-height: 22px;
    border-radius: 3px;
    background: var(--specialmonitor_checkedItem_bg_color, #06324d);
    font-size: 12px;
    vertical-align: middle;
    opacity: 1;
    overflow: hidden;
    position: relative;

    .ivu-tag-text {
      margin-right: 14px;
      display: block;
      max-width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: var(--font_color, #303748) !important;
    }

    .ivu-tag-exit {
      display: block;
      position: absolute;
      right: 4px;
      top: 4px;
      transform: scale(1.42857143) rotate(0);
      cursor: pointer;
    }
  }

  .checkedMoreItem {
    display: inline-block;
    margin: 4px 0 4px 3px;
    padding: 0 8px;
    line-height: 22px;
    border-radius: 3px;
    background: #f7f7f7;
    font-size: 12px;
    vertical-align: middle;
    opacity: 1;
    overflow: hidden;

    .ivu-tag-text {
      margin-right: 14px;
      display: block;
    }
  }
}

.specListBtn {
  display: inline-block;
  height: 32px;
  border: 1px solid #dcdee2;
  padding: 2px 10px;
  line-height: 28px;
  border-radius: 4px;
  margin-left: 6px;
  cursor: pointer;
}

.sectionBox .section-body {
  flex: 1;
  //background: #ffffff;
  position: relative;
}

.specBox {
  .specItem {
    margin-bottom: 20px;
  }

  .specItem:last-child {
    margin-bottom: 0;
  }
}

.conditionSlCont {
  padding: 8px;

  .headerInput {
    display: inline-block;

    .section-text {
      display: inline-block;
    }
  }
}

.conditionBoxCont {
  padding: 8px;

  .ivu-page {
    padding: 0;
  }
}

.fillempty {
  width: 100%;
  height: 260px;
  background-position: center 45%;
  background-size: 200px 138.5px;
  background-image: url("../../../assets/dashboard/fileempty.png");
  background-repeat: no-repeat;
  position: relative;

  .emptyText {
    position: absolute;
    display: block;
    width: 100%;
    text-align: center;
    top: calc(45% + 90px);
  }
}
/deep/ .ivu-radio-inner {
  border-color: var(--ivu_radio_inner_color, #ffffff) !important;
}
</style>