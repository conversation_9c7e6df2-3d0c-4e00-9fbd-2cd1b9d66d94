<template>
  <div class="specContent">
    <div :class="[currentSkin == 1 ? 'specItemTitle' : 'specItemTitle-light']">
      {{ specItemTitle }}
    </div>
    <div class="spectendencyChart">
      <spectendency-chart
        :tendencyChartColor="tendencyChartColor"
        :indicatordata="indicatorItemData"
        :useMaxMin="useMaxMin"
        :isdarkSkin="isdarkSkin"
        @whellScale="itemScale"
      ></spectendency-chart>
    </div>
    <div class="specItemdetails">
      <div class="detailsBox">
        <div class="detailsLeft">
          <div class="specMsg"></div>
          <div
            class="specMsg"
            v-for="specItem in specMsgList[indicatorItemData.type]"
            :key="'specMsg' + specItem.type + specItem.id"
          >
            <span
              class="specLengd"
              :style="
                'background:' +
                specItem.color +
                ';border-color:' +
                specItem.color
              "
            ></span>
            {{ specMsgList["specDetails"] + specItem.indicator }}
          </div>
        </div>
        <div class="detailsRight" :style="isdarkSkin == 1 ? '#617ca5' : ''">
          <div
            class="detailsColumn"
            style="text-align: left"
            v-for="(tableColumn, index) in detailsColumn"
            :key="indicatorItemData.id + '-' + index"
          >
            {{ tableColumn.headerName }}
          </div>
          <div
            class="detailsTable"
            v-for="specItem in specMsgList[indicatorItemData.type]"
            :key="'data' + specItem.type + specItem.id"
          >
            <span
              class="detailsTableItem"
              style="text-align: left"
              v-for="(dataItem, index) in specItem.data"
              :key="index + '-' + dataItem"
              >{{
                0 > dataItem ||
                dataItem == undefined ||
                dataItem == null ||
                dataItem === ""
                  ? "--"
                  : dataItem + specItem.unit
              }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import loading from "@/common/loading/loading"
  import spectendencyChart from "./spectendencyChart"
  import global from "@/common/global.js";
  import moment from 'moment';
  export default {
    name: "indicatorItem",
    components: {
      spectendencyChart
    },
    props:{
      indicatorItemData:{
        type: Object,
        default: function() {
          return {};
        }
      },
      isdarkSkin:{
        type: Number,
        default: 0
      },
    },
    data() {
      return {
        currentSkin:sessionStorage.getItem('dark') || 1,
        //loading状态
        loading: false,
        // 0：Z端匹配，1：A端匹配 
        matchingType:0,
        // 利用率最大最小值
        useMaxMin:{},
        //监测指标数据
        indicatorData:[],
        //趋势图颜色配置
        tendencyChartColor:["#4e7bff","#ffa212","#24C3C5","#478EE9"],
        //专线标题
        specItemTitle:"",
        //专线详情
        detailsColumn:[
        {index:2,headerName:this.$t('specialmonitor_up_data')},
          {index:3,headerName:this.$t('specialmonitor_minimum')},
          {index:4,headerName:this.$t('specialmonitor_average')},
          {index:5,headerName:this.$t('specialmonitor_maximum')},
        ],
        //详情数据
        detailsData:[
          "1"
        ],
        specMsgList:{
          "specDetails":"",
          "1":[
            // 时延
            {id:1,type:'delay',indicator:this.$t('comm_time_delay')+"：",unit:"ms",color:"#00FFEE",data:[]},
            // 丢包率
            {id:2,type:'loss',indicator:this.$t('comm_packet_loss')+"：",unit:"%",color:"#0290FD",data:[]},
          ],
           "3":[
            // 可用率
            {id:1,type:'useable',indicator:this.$t('specquality_availability')+"：",unit:"%",color:"#0290FD",data:[]},
            // 优良率
            {id:2,type:'goodrate',indicator:this.$t('specquality_good_rate')+"：",unit:"%",color:"#00FFEE",data:[]},
            // 可用率（免责）
            {id:3,type:'useableexemption',indicator:this.$t('specquality_availability_exemption')+"：",unit:"%",color:"#81C8FF",data:[]},
            // 优良率（免责）
            {id:4,type:'goodrateexemption',indicator:this.$t('specquality_good_rate_exemption')+"：",unit:"%",color:"#9BFBAE",data:[]},
          ]
        },
      }
    },
    watch:{
      indicatorItemData: {
        handler(val) {
          
        this.matchingType  = this.indicatorItemData.matchingType;
        
        this.getSpecMsgList();
      // 加载指标
          this.specItemTitle = `${val.specName} （${val.specAIp}-${val.specZIp}）：${val.indicatorType}`;
          this.specMsgList['specDetails'] = `${val.specName} （${val.specAIp}-${val.specZIp}）`;
          this.specMsgList[val.type].forEach(element => {
            let {now,min,avg,max} = val[element.type+'Details'];
            let arrayDetails = [now,min,avg,max];
            element.data = arrayDetails;
          });
        },
        deep: true,
        immediate:true
      },
    },
    created() {
        this.matchingType  = this.indicatorItemData.matchingType;
    },
    computed: {},
    mounted() {
   
    },
    methods: {
      getSpecMsgList(){
        let flowArrays = this.getFlowLegendOption();
        this.specMsgList["2"] = [
            {id:1,type:'inflow',indicator:flowArrays[0]+"：",unit:"",color:"#81C8FF",data:[]},
            {id:2,type:'outflow',indicator:flowArrays[1]+"：",unit:"",color:"#9BFBAE",data:[]},
            {id:3,type:'inuse',indicator:flowArrays[2]+"：",unit:"%",color:"#0290FD",data:[]},
            {id:4,type:'outuse',indicator:flowArrays[3]+"：",unit:"%",color:"#00FFEE",data:[]},
          ]
          let upUse=this.specMsgList["2"][2].data
          let downUse=this.specMsgList["3"][3].data

         this.useMaxMin.max=upUse[3]>downUse[3]?upUse[3]:downUse[3]
         this.useMaxMin.min=upUse[1]<downUse[1]?upUse[1]:downUse[1]

        },
    // 获取流速信息
    getFlowLegendOption(){
      let flowLegendArray = [this.$t('dashboard_upstream'), this.$t('dashboard_down'), this.$t('dashboard_uplink'), this.$t('dashboard_downstream')];
      // matchingType => 0：Z端匹配，1：A端匹配 
      // if(this.matchingType == 0){
      //   // 出流速=>上行流速 , 入流速 => 下行流速
      //   flowLegendArray = [this.$t('dashboard_down'), this.$t('dashboard_upstream'), this.$t('dashboard_downstream'), this.$t('dashboard_uplink')];
      // }else if(this.matchingType == 1){
      //   // 出流速=>下行流速 , 入流速 => 上行流速
      //   flowLegendArray = [this.$t('dashboard_upstream'), this.$t('dashboard_down'), this.$t('dashboard_uplink'), this.$t('dashboard_downstream')];
      // }
      return flowLegendArray;

    },
      itemScale(param){
        this.$emit('scale',param)
      },
    }
  }
</script>

<style lang="less" scoped>
.specContent {
  // border: 1px solid var(--border_color,#ddd);
  // border-radius: 6px;
  border: 1px solid var(--border_color, #06254b);
  .specItemTitle {
    height: 46px;
    width: 100%;
    background-image: url("../../../assets/wisdom/img-bg-zj.png");
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    padding-left: 10px;
    font-weight: 700;
    color: #ffffff;
    // cursor: pointer;
    // border-bottom:1px solid var(--border_color,#ddd);
    // height: 42px;
    // font-weight: 600;
    font-size: 14px;
    // line-height: 42px;
    // text-align: left;
    // text-indent: 20px;
    // cursor: default;
  }
  .specItemTitle-light {
    height: 46px;
    width: 100%;
    background-image: url("../../../assets/wisdom/img-bg-zj2.png");
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    padding-left: 10px;
    font-weight: 700;
    color: #17233d;
    // cursor: pointer;
    // border-bottom:1px solid var(--border_color,#ddd);
    // height: 42px;
    // font-weight: 600;
    font-size: 14px;
    // line-height: 42px;
    // text-align: left;
    // text-indent: 20px;
    // cursor: default;
  }
  .spectendencyChart {
    height: 258px;
    background-color: var(--spectendency_chart_bg_color, #06121c);
  }
  .specItemdetails {
    display: flex;
    box-orient: vertical;
    flex-direction: column;
    background-color: var(--spectendency_chart_bg_color, #06121c);

    .detailsBox {
      margin: 8px 5%;
      flex: 1;
      text-align: left;
      .detailsLeft {
        display: inline-block;
        .specMsg {
          height: 28px;
          line-height: 28px;
          display: flex;
          align-items: center;
          cursor: default;
          .specLengd {
            display: inline-block;
            width: 18px;
            height: 10px;
            border-width: 1px;
            border-style: solid;
            border-radius: 3px;
            margin: 0px 5px 0px 10px;
          }
        }
      }
      .detailsRight {
        display: inline-block;
        margin-left: 80px;
        .detailsTable {
          height: 28px;
          .detailsTableItem {
            display: inline-block;
            height: 28px;
            line-height: 28px;
            text-align: center;
            width: 120px;
          }
        }
      }
      .detailsColumn {
        display: inline-block;
        height: 28px;
        line-height: 28px;
        text-align: center;
        width: 120px;
        font-weight: 545;
      }
    }
  }
}
</style>